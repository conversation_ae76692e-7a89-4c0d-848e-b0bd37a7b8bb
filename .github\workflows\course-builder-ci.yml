name: Course Builder CI/CD

on:
  push:
    branches: [main, develop]
    paths:
      - 'apps/frontend/src/components/admin/course-builder/**'
      - 'apps/frontend/src/stores/admin/**'
      - 'apps/frontend/src/app/admin/course-builder/**'
      - 'apps/api/src/endpoints/admin/**'
      - 'database/**'
  pull_request:
    branches: [main, develop]
    paths:
      - 'apps/frontend/src/components/admin/course-builder/**'
      - 'apps/frontend/src/stores/admin/**'
      - 'apps/frontend/src/app/admin/course-builder/**'
      - 'apps/api/src/endpoints/admin/**'
      - 'database/**'

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Frontend Tests
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/frontend

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run TypeScript check
        run: pnpm type-check

      - name: Run ESLint
        run: pnpm lint

      - name: Run Prettier check
        run: pnpm format:check

      - name: Run unit tests
        run: pnpm test:ci
        env:
          CI: true

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: frontend-test-results
          path: apps/frontend/test-results/

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: apps/frontend/coverage/lcov.info
          flags: frontend
          name: course-builder-frontend

  # Backend Tests
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/api

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_USER: test
          POSTGRES_DB: course_builder_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run database migrations
        run: pnpm db:migrate
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/course_builder_test

      - name: Run TypeScript check
        run: pnpm type-check

      - name: Run ESLint
        run: pnpm lint

      - name: Run unit tests
        run: pnpm test:ci
        env:
          CI: true
          DATABASE_URL: postgresql://test:test@localhost:5432/course_builder_test

      - name: Run integration tests
        run: pnpm test:integration
        env:
          CI: true
          DATABASE_URL: postgresql://test:test@localhost:5432/course_builder_test

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: backend-test-results
          path: apps/api/test-results/

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: apps/api/coverage/lcov.info
          flags: backend
          name: course-builder-backend

  # E2E Tests
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [frontend-tests, backend-tests]

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_USER: test
          POSTGRES_DB: course_builder_e2e
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install Playwright browsers
        run: pnpm exec playwright install --with-deps
        working-directory: apps/frontend

      - name: Setup test database
        run: pnpm db:migrate
        working-directory: apps/api
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/course_builder_e2e

      - name: Start backend server
        run: pnpm dev &
        working-directory: apps/api
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/course_builder_e2e
          PORT: 3001

      - name: Start frontend server
        run: pnpm dev &
        working-directory: apps/frontend
        env:
          NEXT_PUBLIC_API_URL: http://localhost:3001

      - name: Wait for servers
        run: |
          npx wait-on http://localhost:3000 http://localhost:3001 --timeout 60000

      - name: Run Playwright tests
        run: pnpm test:e2e
        working-directory: apps/frontend
        env:
          CI: true

      - name: Upload Playwright report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: apps/frontend/playwright-report/

  # Security Scan
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # Build and Deploy (only on main branch)
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: [frontend-tests, backend-tests, e2e-tests, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build frontend
        run: pnpm build
        working-directory: apps/frontend

      - name: Build backend
        run: pnpm build
        working-directory: apps/api

      - name: Deploy to staging
        run: echo "Deploy to staging environment"
        # Add your deployment logic here

      - name: Run smoke tests
        run: echo "Run smoke tests against staging"
        # Add smoke test logic here

      - name: Deploy to production
        run: echo "Deploy to production environment"
        # Add production deployment logic here
