# Course List Page - Institute Admin Panel PRD

## Project Overview
Create a course list page for the institute admin panel that displays courses in a card view with creation and management capabilities. This is specifically for the institute admin section of the LMS SaaS platform.

## Location
- **Frontend**: `apps/frontend/src/app/admin/courses/page.tsx` (Institute Admin Panel)
- **Backend**: `apps/api/src/endpoints/institute-admin/courses.ts`

## Core Features

### 1. Course List Page
**Primary Interface**: Display all courses in a card-based layout
- **Layout**: Card view with responsive design
- **Course Cards**: Each card displays course thumbnail, title, description preview, pricing info, and action buttons
- **Branch Filtering**: Automatically filter courses by logged-in user's branch_id (no UI selection needed)

### 2. Create Course Button & Modal
**Location**: "Create Course" button on the course list page
**Functionality**: Opens a modal with course creation form

### 3. Course Creation Form
**Form Fields**:
- **Title**: Required text field
- **Description**: Text area for course description
- **Pricing Configuration**:
  - **Free Plan**: Radio button option
  - **One-Time Plan**: Radio button option with:
    - Total Price: Numeric input
    - Discount Percentage: Numeric input (0-100%)
    - Final Price: Auto-calculated and displayed

### 4. Course Card Actions
Each course card includes action buttons:
- **Course Builder**: Navigate to course content creation
- **Course Preview**: Preview course as students see it
- **Information**: View/edit course details
- **Learners**: View enrolled students
- **Course Discussions**: Manage course discussions

## Technical Requirements

### Frontend Implementation
- **Location**: `apps/frontend/src/app/admin/courses/page.tsx`
- **Framework**: Next.js with TypeScript
- **UI Library**: Shadcn UI components with Tailwind CSS
- **Form Management**: Formik + Yup validation
- **State Management**: Zustand for course data

### Backend API Requirements
- **Location**: `apps/api/src/endpoints/institute-admin/courses.ts`
- **Course CRUD Operations**: Create, read courses with automatic branch_id assignment
- **Branch-Based Filtering**: Automatically filter courses by user's branch_id
- **Validation**: Server-side validation for course data
- **Permissions**: Institute Admin and Staff access only

### Database Schema
```sql
courses:
- id (primary key)
- title (varchar)
- description (text)
- pricing_type (enum: 'free', 'one_time')
- price_amount (decimal, nullable)
- discount_percentage (integer, nullable)
- final_price (decimal, calculated)
- institute_id (foreign key)
- branch_id (foreign key to branches)
- created_by (foreign key to users)
- created_at (timestamp)
- updated_at (timestamp)
```

### Branch-Based Course Management
- **Automatic Branch Assignment**: Courses assigned to logged-in user's branch
- **No Branch Selection UI**: Handled automatically based on authentication
- **Branch Filtering**: Course lists filtered by user's branch_id

## User Experience Flow

### Course List Page Flow
1. Institute admin lands on `/admin/courses` page
2. Sees card view of courses from their branch
3. Clicks "Create Course" to add new course

### Course Creation Flow
1. Click "Create Course" button
2. Modal opens with creation form
3. Fill required fields (title, description)
4. Configure pricing (free or one-time with discount)
5. Submit form with validation
6. Success: Modal closes, course appears in list
7. Error: Show validation messages

### Course Card Interaction Flow
1. Each course card shows action buttons
2. Click specific action button:
   - Course Builder: Navigate to content creation
   - Preview: Open course preview
   - Information: View course details
   - Learners: View enrolled students
   - Discussions: Manage discussions

## Implementation Scope
**Focus**: Only course list page with card view and create course modal
**Location**: Institute admin panel (`/admin/courses`)
**Features**:
- Display courses in card layout
- Create course button and modal
- Course card action buttons
- Branch-based filtering (automatic)
