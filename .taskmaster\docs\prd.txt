# Platform Settings System - Product Requirements Document

## Project Overview
Create a comprehensive platform settings system for the LMS using an options table with key-value pairs, complete with backend endpoints, Zustand store, and frontend integration.

## Current System Architecture
- **Backend**: Payload CMS (Node.js/Express) on port 3001
- **Frontend**: Next.js 15 with App Router on port 3000
- **Database**: PostgreSQL with Payload adapter
- **State Management**: Zustand stores
- **UI**: TailwindCSS + Shadcn/UI + Radix components
- **Forms**: Formik + Yup validation

## Core Requirements

### 1. Database Schema - Options Table
Create a simple options table with key-value structure:
- **Table Name**: `options`
- **Fields**:
  - `id` (Primary Key)
  - `key` (String, Unique) - Setting name (e.g., "platform_name", "support_email")
  - `value` (Text) - Setting value (e.g., "KISS LMS", "<EMAIL>")
  - `description` (Text, Optional) - Human-readable description
  - `category` (String) - Group settings (e.g., "platform", "email", "security")
  - `type` (String) - Data type hint (e.g., "string", "boolean", "number", "json")
  - `is_public` (Boolean) - Whether setting can be accessed publicly
  - `created_at` (Timestamp)
  - `updated_at` (Timestamp)

### 2. Backend API Endpoints
Create comprehensive CRUD endpoints for platform settings:
- `GET /api/settings` - Get all settings (with filtering)
- `GET /api/settings/:key` - Get specific setting by key
- `POST /api/settings` - Create new setting
- `PUT /api/settings/:key` - Update setting by key
- `DELETE /api/settings/:key` - Delete setting by key
- `POST /api/settings/bulk` - Bulk update multiple settings
- `GET /api/settings/category/:category` - Get settings by category

### 3. Payload CMS Collection
Create a Payload CMS collection for the options table:
- Admin interface for managing settings
- Validation rules for different setting types
- Access control for super admin only
- Search and filtering capabilities

### 4. Zustand Store Implementation
Create a dedicated settings store with:
- State management for all platform settings
- Actions for CRUD operations
- Loading states and error handling
- Caching and optimistic updates
- Type-safe setting access

### 5. Frontend Integration
Update the platform settings page to:
- Use the new Zustand store
- Dynamic form generation based on setting types
- Real-time validation and updates
- Toast notifications for success/error states
- Proper error handling and loading states

### 6. Perfect Folder Structure
Organize code with clean architecture:
```
apps/api/src/
├── collections/
│   └── Options.ts
├── endpoints/
│   └── settings.ts
└── types/
    └── settings.ts

apps/frontend/src/
├── stores/
│   └── settings/
│       └── useSettingsStore.ts
├── lib/
│   └── api/
│       └── settings.ts
├── types/
│   └── settings.ts
└── app/super-admin/settings/platform/
    └── page.tsx (updated)
```

### 7. Default Platform Settings
Implement default settings for:
- `platform_name` - "KISS LMS"
- `platform_url` - "https://groups-exam.com"
- `support_email` - "<EMAIL>"
- `maintenance_mode` - false
- `allow_registration` - true
- `require_email_verification` - true
- `max_institutes_per_plan` - 1000
- `session_timeout` - 60
- `max_login_attempts` - 5

## Technical Implementation Details

### Database Migration
- Create migration script for options table
- Seed default platform settings
- Add proper indexes for performance

### API Security
- Super admin authentication required
- Input validation and sanitization
- Rate limiting for settings endpoints
- Audit logging for setting changes

### Frontend Features
- Type-safe setting access with TypeScript
- Form validation with Yup schemas
- Optimistic updates for better UX
- Setting categories and grouping
- Search and filter capabilities

### Performance Considerations
- Cache frequently accessed settings
- Minimize database queries
- Efficient state updates
- Lazy loading for large setting lists

## Success Criteria
- Options table created and functional
- All CRUD endpoints working correctly
- Zustand store properly managing state
- Platform settings page fully integrated
- Type-safe throughout the application
- Proper error handling and validation
- Clean, maintainable code structure

## Priority Features
1. **High Priority**: Database schema, basic CRUD endpoints, Zustand store
2. **Medium Priority**: Advanced filtering, bulk operations, caching
3. **Low Priority**: Advanced admin features, setting history, import/export

## Timeline Expectations
Complete implementation with proper testing and documentation, ensuring all components work together seamlessly.
