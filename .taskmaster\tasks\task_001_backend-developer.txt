# Task ID: 1
# Title: Implement Flexible File Upload Middleware System
# Status: pending
# Dependencies: None
# Priority: high
# Description: Create a flexible file upload middleware system that can switch between local storage and S3 based on storage_provider setting from platform settings
# Details:
Build a comprehensive file upload system that supports both local storage and S3 storage based on platform settings. Include proper validation, file processing, and URL generation for logo and favicon uploads.

# Test Strategy:


# Subtasks:
## 1. Create Storage Adapter Interface and Implementations [pending]
### Dependencies: None
### Description: Design and implement storage adapter interface with local storage and S3 implementations
### Details:
Create a unified interface for file storage operations (upload, delete, getUrl) with separate implementations for local filesystem and AWS S3. Include proper error handling and configuration management.

## 2. Implement File Upload Middleware [pending]
### Dependencies: None
### Description: Create Express middleware for handling multipart file uploads with validation
### Details:
Build middleware using multer for file upload handling, including file type validation, size limits, and proper error handling. Support both single and multiple file uploads.

## 3. Create File Upload API Endpoints [pending]
### Dependencies: None
### Description: Implement Payload CMS endpoints for file upload, delete, and serve operations
### Details:
Create custom Payload endpoints: POST /api/platform/upload (upload files), DELETE /api/platform/files/:id (delete files), GET /api/platform/files/:filename (serve files). Include proper authentication and authorization.

## 4. Implement Platform Settings Integration [pending]
### Dependencies: None
### Description: Create endpoints to update platform settings with uploaded file references
### Details:
Build endpoints to update platform logo and favicon settings with uploaded file references. Include validation for file types and automatic cleanup of old files when new ones are uploaded.

## 5. Add File Processing and Optimization [pending]
### Dependencies: None
### Description: Implement image processing for logo and favicon optimization
### Details:
Use Sharp library to process uploaded images - resize, optimize, and generate different formats/sizes for logos and favicons. Include automatic format conversion and compression.

## 6. Configure Storage Provider Settings [pending]
### Dependencies: None
### Description: Implement dynamic storage provider configuration based on platform settings
### Details:
Create configuration system that reads storage_provider setting from platform options and initializes the appropriate storage adapter. Include environment variable management and fallback mechanisms.

