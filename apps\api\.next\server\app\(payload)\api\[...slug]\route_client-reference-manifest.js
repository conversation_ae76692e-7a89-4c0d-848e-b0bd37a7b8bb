globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(payload)/api/[...slug]/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{},"edgeSSRModuleMapping":{},"clientModules":{"C:\\projects\\lms\\node_modules\\.pnpm\\@payloadcms+next@3.43.0_@types+react@19.1.0_graphql@16.11.0_monaco-editor@0.52.2_next@15.3.0__5kipoy5xwbqm355o7gona4yn6a\\node_modules\\@payloadcms\\next\\dist\\prod\\styles.css":{"id":"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+next@3.43.0_@types+react@19.1.0_graphql@16.11.0_monaco-editor@0.52.2_next@15.3.0__5kipoy5xwbqm355o7gona4yn6a/node_modules/@payloadcms/next/dist/prod/styles.css","name":"*","chunks":["app/(payload)/api/[...slug]/route","static/chunks/app/(payload)/api/%5B...slug%5D/route.js"],"async":false},"C:\\projects\\lms\\node_modules\\.pnpm\\@payloadcms+next@3.43.0_@types+react@19.1.0_graphql@16.11.0_monaco-editor@0.52.2_next@15.3.0__5kipoy5xwbqm355o7gona4yn6a\\node_modules\\@payloadcms\\next\\dist\\esm\\prod\\styles.css":{"id":"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+next@3.43.0_@types+react@19.1.0_graphql@16.11.0_monaco-editor@0.52.2_next@15.3.0__5kipoy5xwbqm355o7gona4yn6a/node_modules/@payloadcms/next/dist/prod/styles.css","name":"*","chunks":["app/(payload)/api/[...slug]/route","static/chunks/app/(payload)/api/%5B...slug%5D/route.js"],"async":false}},"entryCSSFiles":{"C:\\projects\\lms\\apps\\api\\src\\":[],"C:\\projects\\lms\\apps\\api\\src\\app\\(payload)\\api\\[...slug]\\route":[{"inlined":false,"path":"static/css/app/(payload)/api/[...slug]/route.css"}]},"rscModuleMapping":{"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+next@3.43.0_@types+react@19.1.0_graphql@16.11.0_monaco-editor@0.52.2_next@15.3.0__5kipoy5xwbqm355o7gona4yn6a/node_modules/@payloadcms/next/dist/prod/styles.css":{"*":{"id":"(rsc)/../../node_modules/.pnpm/@payloadcms+next@3.43.0_@types+react@19.1.0_graphql@16.11.0_monaco-editor@0.52.2_next@15.3.0__5kipoy5xwbqm355o7gona4yn6a/node_modules/@payloadcms/next/dist/prod/styles.css","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}