// Check all columns in users table to see if role_id exists
const { Client } = require('pg')

async function checkAllColumns() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('🔌 Connecting to PostgreSQL database...')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Check ALL columns in users table
    const allColumns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'users'
      ORDER BY ordinal_position;
    `)

    console.log('\n📊 ALL columns in users table:')
    console.table(allColumns.rows)

    // Specifically look for any role-related columns
    const roleColumns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name LIKE '%role%'
      ORDER BY column_name;
    `)

    if (roleColumns.rows.length > 0) {
      console.log('\n🔍 Role-related columns found:')
      console.table(roleColumns.rows)
    } else {
      console.log('\n⚠️  No role-related columns found.')
    }

    // Check foreign key constraints for any role columns
    const fkCheck = await client.query(`
      SELECT
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name='users';
    `)

    if (fkCheck.rows.length > 0) {
      console.log('\n🔗 ALL foreign key constraints:')
      console.table(fkCheck.rows)
    } else {
      console.log('\n⚠️  No foreign key constraints found.')
    }

  } catch (error) {
    console.error('❌ Database connection error:', error.message)
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed.')
  }
}

// Run the check
checkAllColumns().catch(console.error)
