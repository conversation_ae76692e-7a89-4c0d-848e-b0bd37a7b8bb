// Check all users in database
const { Client } = require('pg')

async function checkAllUsers() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('👥 Checking all users in database...\n')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Check for user ID 6 specifically
    console.log('🔍 Checking for user ID 6...')
    const userSix = await client.query(`
      SELECT
        id,
        email,
        first_name,
        last_name,
        legacy_role,
        is_active,
        created_at
      FROM users
      WHERE id = 6
    `)

    if (userSix.rows.length > 0) {
      console.log('✅ User ID 6 found:')
      console.log(userSix.rows[0])
    } else {
      console.log('❌ User ID 6 NOT found!')
    }

    // Get all users with their details
    const allUsers = await client.query(`
      SELECT
        id,
        email,
        first_name,
        last_name,
        legacy_role,
        is_active,
        created_at
      FROM users
      ORDER BY id
    `)

    console.log(`\n📊 Total users found: ${allUsers.rows.length}\n`)

    if (allUsers.rows.length === 0) {
      console.log('❌ No users found in database!')
      console.log('💡 You need to create users through Payload admin interface')
      console.log('🌐 Go to: http://localhost:3002/admin')
      return
    }

    console.log('📋 All Users in Database:')
    console.log('=' .repeat(80))

    allUsers.rows.forEach((user, index) => {
      console.log(`\n${index + 1}. User Details:`)
      console.log(`   🆔 ID: ${user.id}`)
      console.log(`   📧 Email: ${user.email}`)
      console.log(`   👤 Name: ${user.first_name} ${user.last_name}`)
      console.log(`   🎭 Role: ${user.role}`)
      console.log(`   ✅ Active: ${user.is_active}`)
      console.log(`   🔢 Login Attempts: ${user.login_attempts}`)
      console.log(`   🔒 Locked Until: ${user.lock_until || 'Not locked'}`)
      console.log(`   📅 Created: ${user.created_at}`)
      console.log('   ' + '-'.repeat(50))
    })

    console.log('\n🔐 Login Credentials for Testing:')
    console.log('=' .repeat(80))

    allUsers.rows.forEach((user, index) => {
      console.log(`\n${index + 1}. ${user.first_name} ${user.last_name} (${user.role}):`)
      console.log(`   📧 Email: ${user.email}`)
      
      // Suggest passwords based on role and email
      let suggestedPassword = 'Unknown'
      if (user.email === '<EMAIL>') {
        suggestedPassword = 'SuperAdmin@123'
      } else if (user.email === '<EMAIL>') {
        suggestedPassword = 'demo123'
      } else if (user.email === '<EMAIL>') {
        suggestedPassword = 'student123'
      } else if (user.email === '<EMAIL>') {
        suggestedPassword = 'admin123 (or check with admin)'
      } else if (user.email === '<EMAIL>') {
        suggestedPassword = 'student123 (or check with admin)'
      }
      
      console.log(`   🔑 Password: ${suggestedPassword}`)
      
      // Suggest login URL based on role
      let loginUrl = 'http://localhost:3002/auth/user-login'
      if (user.role === 'super_admin') {
        loginUrl = 'http://localhost:3002/auth/admin/login'
      } else if (user.role === 'institute_admin') {
        loginUrl = 'http://localhost:3002/auth/login'
      }
      
      console.log(`   🌐 Login URL: ${loginUrl}`)
    })

    console.log('\n💡 Notes:')
    console.log('- If password is "Unknown", the user was created manually')
    console.log('- You can reset passwords through Payload admin interface')
    console.log('- Admin Panel: http://localhost:3002/admin')

  } catch (error) {
    console.error('❌ Error checking users:', error.message)
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed')
  }
}

console.log('👥 Database Users Checker\n')
checkAllUsers()
  .then(() => {
    console.log('\n✅ Check completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Check failed:', error.message)
    process.exit(1)
  })
