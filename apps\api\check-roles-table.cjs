// Check if roles table exists and what's in it
const { Client } = require('pg')

async function checkRolesTable() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('🔌 Connecting to PostgreSQL database...')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Check if roles table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'roles'
      );
    `)

    if (!tableCheck.rows[0].exists) {
      console.log('❌ Roles table does not exist.')
      console.log('💡 This might be why the role_id column is not being created.')
      
      // Check what tables do exist
      const allTables = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name;
      `)
      
      console.log('\n📋 Available tables:')
      console.table(allTables.rows)
      
      return
    }

    console.log('✅ Roles table exists!')

    // Check roles table structure
    const rolesColumns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'roles'
      ORDER BY ordinal_position;
    `)

    console.log('\n📊 Roles table structure:')
    console.table(rolesColumns.rows)

    // Check roles data
    const rolesData = await client.query('SELECT * FROM roles LIMIT 10;')
    
    if (rolesData.rows.length > 0) {
      console.log('\n📋 Roles data (first 10 rows):')
      console.table(rolesData.rows)
    } else {
      console.log('\n⚠️  Roles table is empty.')
    }

  } catch (error) {
    console.error('❌ Database connection error:', error.message)
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed.')
  }
}

// Run the check
checkRolesTable().catch(console.error)
