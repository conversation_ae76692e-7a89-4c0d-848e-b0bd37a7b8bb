// Check database schema for users table
const { Client } = require('pg')

async function checkSchema() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('🔌 Connecting to PostgreSQL database...')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Check if users table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `)

    if (!tableCheck.rows[0].exists) {
      console.log('❌ Users table does not exist.')
      console.log('💡 Please run the API server first to create the database schema.')
      return
    }

    console.log('📋 Users table found! Checking columns...')

    // Get table structure
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'users'
      ORDER BY ordinal_position;
    `)

    console.log('\n📊 Users table structure:')
    console.log('┌─────────────────────┬─────────────────┬─────────────┬─────────────────┐')
    console.log('│ Column Name         │ Data Type       │ Nullable    │ Default         │')
    console.log('├─────────────────────┼─────────────────┼─────────────┼─────────────────┤')
    
    columns.rows.forEach(col => {
      const name = col.column_name.padEnd(19)
      const type = col.data_type.padEnd(15)
      const nullable = col.is_nullable.padEnd(11)
      const defaultVal = (col.column_default || 'NULL').padEnd(15)
      console.log(`│ ${name} │ ${type} │ ${nullable} │ ${defaultVal} │`)
    })
    
    console.log('└─────────────────────┴─────────────────┴─────────────┴─────────────────┘')

    // Check if any users exist
    const userCount = await client.query('SELECT COUNT(*) as count FROM users')
    console.log(`\n👥 Total users in database: ${userCount.rows[0].count}`)

    // Check for existing super admin
    const superAdminCheck = await client.query(`
      SELECT id, email, "firstName", "lastName", role 
      FROM users 
      WHERE email = '<EMAIL>' OR role = 'super_admin'
    `)

    if (superAdminCheck.rows.length > 0) {
      console.log('\n🔍 Existing super admin users:')
      superAdminCheck.rows.forEach(user => {
        console.log(`   🆔 ID: ${user.id}`)
        console.log(`   📧 Email: ${user.email}`)
        console.log(`   👤 Name: ${user.firstName} ${user.lastName}`)
        console.log(`   🎭 Role: ${user.role}`)
        console.log('   ─────────────────────────────────')
      })
    } else {
      console.log('\n⚠️  No super admin users found')
    }

  } catch (error) {
    console.error('❌ Error checking schema:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Database connection failed. Please check:')
      console.log('   - PostgreSQL is running')
      console.log('   - Database "lms_new" exists')
      console.log('   - Credentials are correct (postgres:1234)')
    }
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed')
  }
}

console.log('🔍 Database Schema Checker\n')
checkSchema()
  .then(() => {
    console.log('\n✅ Schema check completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Schema check failed:', error.message)
    process.exit(1)
  })
