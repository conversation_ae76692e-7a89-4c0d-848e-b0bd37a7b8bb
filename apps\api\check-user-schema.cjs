// Check the current Users table schema to verify column names
const { Client } = require('pg')

async function checkUserSchema() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('🔌 Connecting to PostgreSQL database...')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Check if users table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `)

    if (!tableCheck.rows[0].exists) {
      console.log('❌ Users table does not exist.')
      console.log('💡 Please run the API server first to create the database schema.')
      return
    }

    console.log('✅ Users table exists!')
    console.log('\n📋 Checking column structure...')

    // Check specific columns related to branch and role
    const columnCheck = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name IN ('branch_id', 'branch_id_id', 'role_id', 'role_id_id', 'branch')
      ORDER BY column_name;
    `)

    if (columnCheck.rows.length === 0) {
      console.log('⚠️  No branch or role related columns found.')
    } else {
      console.log('\n🔍 Found the following branch/role columns:')
      console.table(columnCheck.rows)
    }

    // Check all columns in users table
    const allColumns = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'users'
      ORDER BY ordinal_position;
    `)

    console.log('\n📊 All columns in users table:')
    console.table(allColumns.rows)

    // Check for any foreign key constraints
    const fkCheck = await client.query(`
      SELECT
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name='users'
        AND kcu.column_name IN ('branch_id', 'branch_id_id', 'role_id', 'role_id_id', 'branch');
    `)

    if (fkCheck.rows.length > 0) {
      console.log('\n🔗 Foreign key constraints found:')
      console.table(fkCheck.rows)
    } else {
      console.log('\n⚠️  No foreign key constraints found for branch/role columns.')
    }

  } catch (error) {
    console.error('❌ Database connection error:', error.message)
    console.log('\n💡 Make sure PostgreSQL is running and the database exists.')
    console.log('💡 Check your database connection settings in the script.')
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed.')
  }
}

// Run the check
checkUserSchema().catch(console.error)
