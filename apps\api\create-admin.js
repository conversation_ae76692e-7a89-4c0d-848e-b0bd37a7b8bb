// Simple script to create admin user via API
const API_BASE = 'http://localhost:3002/api'

async function createSuperAdmin() {
  console.log('🔧 Creating Super Admin User...\n')

  try {
    // Create super admin user directly in database
    const response = await fetch(`${API_BASE}/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'SuperAdmin@123',
        firstName: 'Super',
        lastName: 'Admin',
        role: 'super_admin'
      }),
    })

    if (response.ok) {
      const data = await response.json()
      console.log('✅ Super Admin created successfully!')
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Password: SuperAdmin@123')
      console.log('🚀 Login URL: http://localhost:3002/admin')
    } else {
      const errorText = await response.text()
      console.log('❌ Failed to create super admin. Status:', response.status)
      console.log('❌ Response:', errorText)
      console.log('\n💡 Try visiting http://localhost:3002/admin to create the first user manually')
    }

  } catch (error) {
    console.error('❌ Error:', error.message)
    console.log('\n💡 Make sure the API server is running on http://localhost:3002')
    console.log('💡 Then visit http://localhost:3002/admin to create the first user manually')
  }
}

createSuperAdmin()
