// Create super admin with correct database schema
const { Client } = require('pg')
const bcrypt = require('bcrypt')

async function createSuperAdmin() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('🌱 Creating Super Admin User...\n')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Check if super admin already exists
    const existingUser = await client.query(
      'SELECT id, email, role, first_name, last_name FROM users WHERE email = $1',
      ['<EMAIL>']
    )

    if (existingUser.rows.length > 0) {
      console.log('⚠️  Super Admin already exists!')
      const user = existingUser.rows[0]
      console.log('🆔 ID:', user.id)
      console.log('📧 Email:', user.email)
      console.log('👤 Name:', `${user.first_name} ${user.last_name}`)
      console.log('🎭 Role:', user.role)
      console.log('\n🔐 Login Credentials:')
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Password: SuperAdmin@123')
      console.log('🌐 Admin Panel: http://localhost:3002/admin')
      console.log('🌐 Frontend Login: http://localhost:3002/auth/admin/login')
      return
    }

    // Generate password hash
    console.log('🔐 Hashing password...')
    const hashedPassword = await bcrypt.hash('SuperAdmin@123', 10)

    // Insert super admin user with correct column names
    console.log('👤 Creating Super Admin user...')
    const insertQuery = `
      INSERT INTO users (
        email, 
        hash, 
        first_name, 
        last_name, 
        role, 
        is_active,
        email_verified,
        created_at, 
        updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING id, email, first_name, last_name, role
    `

    const now = new Date()
    const result = await client.query(insertQuery, [
      '<EMAIL>',
      hashedPassword,
      'Super',
      'Admin',
      'super_admin',
      true,        // is_active
      true,        // email_verified
      now,         // created_at
      now          // updated_at
    ])

    const createdUser = result.rows[0]

    console.log('\n🎉 Super Admin created successfully!')
    console.log('🆔 User ID:', createdUser.id)
    console.log('📧 Email:', createdUser.email)
    console.log('👤 Name:', `${createdUser.first_name} ${createdUser.last_name}`)
    console.log('🎭 Role:', createdUser.role)
    
    console.log('\n🔐 Login Credentials:')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: SuperAdmin@123')
    console.log('🌐 Admin Panel: http://localhost:3002/admin')
    console.log('🌐 Frontend Login: http://localhost:3002/auth/admin/login')

    // Verify by checking all users
    const allUsers = await client.query('SELECT id, email, role, first_name, last_name FROM users ORDER BY id')
    console.log('\n📋 All users in database:')
    allUsers.rows.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.id} | Email: ${user.email} | Role: ${user.role} | Name: ${user.first_name} ${user.last_name}`)
    })

  } catch (error) {
    console.error('❌ Error creating super admin:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Database connection failed. Please check:')
      console.log('   - PostgreSQL is running')
      console.log('   - Database "lms_new" exists')
      console.log('   - Credentials are correct (postgres:1234)')
    } else if (error.code === '23505') {
      console.log('\n💡 User with this email already exists')
    }
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed')
  }
}

console.log('🌱 Super Admin Creation Tool\n')
createSuperAdmin()
  .then(() => {
    console.log('\n✅ Process completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Process failed:', error.message)
    process.exit(1)
  })
