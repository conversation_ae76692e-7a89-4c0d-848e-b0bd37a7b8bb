// Simple script to create super admin user with proper bcrypt hash
const bcrypt = require('bcrypt')

async function generateHashAndSQL() {
  console.log('🔐 Generating bcrypt hash for SuperAdmin@123...\n')
  
  try {
    const password = 'SuperAdmin@123'
    const saltRounds = 10
    const hashedPassword = await bcrypt.hash(password, saltRounds)
    
    console.log('✅ Password hash generated successfully!')
    console.log('🔑 Password:', password)
    console.log('🔒 Hash:', hashedPassword)
    
    console.log('\n📋 SQL Script to create Super Admin:')
    console.log('=' .repeat(80))
    
    const sqlScript = `
-- Create Super Admin User
INSERT INTO users (
    email, 
    password, 
    "firstName", 
    "lastName", 
    role, 
    "isActive",
    "createdAt", 
    "updatedAt"
) 
SELECT 
    '<EMAIL>',
    '${hashedPassword}',
    'Super',
    'Admin',
    'super_admin',
    true,
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM users WHERE email = '<EMAIL>'
);

-- Verify the user was created
SELECT id, email, "firstName", "lastName", role, "isActive", "createdAt" 
FROM users 
WHERE email = '<EMAIL>';
`
    
    console.log(sqlScript)
    console.log('=' .repeat(80))
    
    console.log('\n🚀 Instructions:')
    console.log('1. Copy the SQL script above')
    console.log('2. Connect to PostgreSQL database: lms_new')
    console.log('3. Run the SQL script')
    console.log('4. Super Admin will be created with these credentials:')
    console.log('   📧 Email: <EMAIL>')
    console.log('   🔑 Password: SuperAdmin@123')
    console.log('   🌐 Admin Panel: http://localhost:3002/admin')
    console.log('   🌐 Frontend Login: http://localhost:3002/auth/admin/login')
    
    console.log('\n💡 Alternative: Run this command in PostgreSQL:')
    console.log(`psql -d lms_new -c "INSERT INTO users (email, password, \\"firstName\\", \\"lastName\\", role, \\"isActive\\", \\"createdAt\\", \\"updatedAt\\") SELECT '<EMAIL>', '${hashedPassword}', 'Super', 'Admin', 'super_admin', true, NOW(), NOW() WHERE NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>');"`)
    
  } catch (error) {
    console.error('❌ Error generating hash:', error.message)
  }
}

// Also provide a direct database insertion function
async function createUserDirectly() {
  console.log('\n🔄 Attempting direct database insertion...')
  
  try {
    // This would require pg module to be installed
    const { Client } = require('pg')
    
    const client = new Client({
      host: '127.0.0.1',
      port: 5432,
      database: 'lms_new',
      user: 'postgres',
      password: '1234'
    })
    
    await client.connect()
    console.log('✅ Connected to database')
    
    // Check if user exists
    const existingUser = await client.query(
      'SELECT id, email FROM users WHERE email = $1',
      ['<EMAIL>']
    )
    
    if (existingUser.rows.length > 0) {
      console.log('⚠️  Super Admin already exists!')
      console.log('📧 Email:', existingUser.rows[0].email)
      console.log('🆔 ID:', existingUser.rows[0].id)
    } else {
      // Create the user
      const hashedPassword = await bcrypt.hash('SuperAdmin@123', 10)
      
      const result = await client.query(`
        INSERT INTO users (
          email, password, "firstName", "lastName", role, "isActive", "createdAt", "updatedAt"
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id, email, "firstName", "lastName", role
      `, [
        '<EMAIL>',
        hashedPassword,
        'Super',
        'Admin',
        'super_admin',
        true,
        new Date(),
        new Date()
      ])
      
      console.log('🎉 Super Admin created successfully!')
      console.log('🆔 User ID:', result.rows[0].id)
      console.log('📧 Email:', result.rows[0].email)
      console.log('👤 Name:', `${result.rows[0].firstName} ${result.rows[0].lastName}`)
      console.log('🎭 Role:', result.rows[0].role)
    }
    
    await client.end()
    
    console.log('\n🔐 Login Credentials:')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: SuperAdmin@123')
    console.log('🌐 Admin Panel: http://localhost:3002/admin')
    console.log('🌐 Frontend Login: http://localhost:3002/auth/admin/login')
    
  } catch (error) {
    console.log('❌ Direct database connection failed:', error.message)
    console.log('💡 Using SQL script method instead...')
  }
}

// Run both methods
console.log('🌱 Super Admin Creation Tool\n')

generateHashAndSQL()
  .then(() => createUserDirectly())
  .then(() => {
    console.log('\n✅ Process completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Process failed:', error.message)
    process.exit(1)
  })
