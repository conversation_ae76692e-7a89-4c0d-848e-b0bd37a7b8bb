// Create super admin user via Payload API
async function createUserViaPayload() {
  console.log('👑 Creating Super Admin via Payload API...\n')

  try {
    // Test 1: Create user via Payload's users endpoint
    console.log('1. Testing user creation via /api/users')
    const createResponse = await fetch('http://localhost:3002/api/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'SuperAdmin@123',
        firstName: 'Super',
        lastName: 'Admin',
        role: 'super_admin',
        isActive: true
      }),
    })

    console.log('Create Status:', createResponse.status)
    const createText = await createResponse.text()
    console.log('Create Response:', createText)

    if (createResponse.ok) {
      console.log('✅ User created successfully via Payload API!')
      
      // Now test login
      console.log('\n2. Testing login with created user')
      const loginResponse = await fetch('http://localhost:3002/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'SuperAdmin@123'
        }),
      })

      console.log('Login Status:', loginResponse.status)
      const loginText = await loginResponse.text()
      console.log('Login Response:', loginText)

      if (loginResponse.ok) {
        console.log('🎉 Login successful!')
      } else {
        console.log('❌ Login failed')
      }
    } else {
      console.log('❌ User creation failed')
      
      // Check if user already exists
      console.log('\n3. Checking existing users')
      const usersResponse = await fetch('http://localhost:3002/api/users', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      console.log('Users Status:', usersResponse.status)
      const usersText = await usersResponse.text()
      console.log('Users Response:', usersText.substring(0, 500) + '...')
    }

  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

// Also test direct login with existing user
async function testDirectLogin() {
  console.log('\n4. Testing direct login with existing database user')
  
  try {
    const loginResponse = await fetch('http://localhost:3002/api/users/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'SuperAdmin@123'
      }),
    })

    console.log('Direct Login Status:', loginResponse.status)
    const loginText = await loginResponse.text()
    console.log('Direct Login Response:', loginText)

  } catch (error) {
    console.error('❌ Direct login error:', error.message)
  }
}

console.log('🧪 Payload User Creation Test\n')
createUserViaPayload()
  .then(() => testDirectLogin())
  .then(() => {
    console.log('\n✅ Test completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Test failed:', error.message)
    process.exit(1)
  })
