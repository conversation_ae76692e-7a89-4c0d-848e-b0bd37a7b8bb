const { Client } = require('pg')
require('dotenv').config()

async function dropStaffTables() {
  const client = new Client({
    connectionString: process.env.DATABASE_URI
  })

  try {
    await client.connect()
    console.log('Connected to database')

    // Drop staff-related tables and indexes
    console.log('Dropping staff-related tables...')
    
    await client.query('DROP TABLE IF EXISTS "staff" CASCADE;')
    console.log('✅ Dropped staff table')

    await client.query('DROP TABLE IF EXISTS "staff_roles" CASCADE;')
    console.log('✅ Dropped staff_roles table')

    // Try to drop any staff-related permissions (may not exist)
    try {
      await client.query(`
        DELETE FROM "permissions"
        WHERE "resource" = 'staff'
        OR "name" LIKE '%Staff%';
      `)
      console.log('✅ Removed staff-related permissions')
    } catch (permError) {
      console.log('ℹ️ No staff permissions found to remove (this is normal)')
    }

    console.log('✅ Staff management system completely removed from database!')

  } catch (error) {
    console.error('❌ Error dropping staff tables:', error.message)
  } finally {
    await client.end()
  }
}

dropStaffTables()
