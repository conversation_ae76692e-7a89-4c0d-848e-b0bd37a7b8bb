-- Fix migration issues for LMS database
-- Run this SQL script to resolve the migration conflicts

-- 1. First, let's check if the course_purchases table exists and what constraints it has
-- If the table exists but has issues, we'll recreate it

-- Drop the course_purchases table if it exists (this will remove all constraints)
DROP TABLE IF EXISTS "course_purchases" CASCADE;

-- 2. Add missing columns to courses table if they don't exist
-- These are safe to run multiple times

-- Add discount_percentage column
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'courses' AND column_name = 'discount_percentage') THEN
        ALTER TABLE "courses" ADD COLUMN "discount_percentage" INTEGER;
    END IF;
END $$;

-- Add final_price column
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'courses' AND column_name = 'final_price') THEN
        ALTER TABLE "courses" ADD COLUMN "final_price" NUMERIC;
    END IF;
END $$;

-- Add institute_id column
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'courses' AND column_name = 'institute_id') THEN
        ALTER TABLE "courses" ADD COLUMN "institute_id" INTEGER;
    END IF;
END $$;

-- Add branch_id column
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'courses' AND column_name = 'branch_id') THEN
        ALTER TABLE "courses" ADD COLUMN "branch_id" INTEGER;
    END IF;
END $$;

-- Add created_by_id column
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'courses' AND column_name = 'created_by_id') THEN
        ALTER TABLE "courses" ADD COLUMN "created_by_id" INTEGER;
    END IF;
END $$;

-- 3. Add foreign key constraints for the new columns
-- Add foreign key for institute_id
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'courses_institute_id_institutes_id_fk') THEN
        ALTER TABLE "courses" ADD CONSTRAINT "courses_institute_id_institutes_id_fk" 
        FOREIGN KEY ("institute_id") REFERENCES "institutes"("id") ON DELETE SET NULL;
    END IF;
END $$;

-- Add foreign key for branch_id
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'courses_branch_id_branches_id_fk') THEN
        ALTER TABLE "courses" ADD CONSTRAINT "courses_branch_id_branches_id_fk" 
        FOREIGN KEY ("branch_id") REFERENCES "branches"("id") ON DELETE SET NULL;
    END IF;
END $$;

-- Add foreign key for created_by_id
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'courses_created_by_id_users_id_fk') THEN
        ALTER TABLE "courses" ADD CONSTRAINT "courses_created_by_id_users_id_fk" 
        FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE SET NULL;
    END IF;
END $$;

-- 4. The course_purchases table will be recreated by Payload CMS when the server starts
-- This script ensures the courses table has the correct schema

COMMIT;
