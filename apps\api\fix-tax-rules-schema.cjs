const { Client } = require('pg')
require('dotenv').config()

async function fixTaxRulesSchema() {
  const client = new Client({
    connectionString: process.env.DATABASE_URI
  })

  try {
    await client.connect()
    console.log('Connected to database')

    // Drop the problematic tax-rules table and related indexes
    console.log('Dropping tax-rules table and related indexes...')
    
    await client.query('DROP TABLE IF EXISTS "tax_rules" CASCADE;')
    console.log('✅ Dropped tax_rules table')

    // Also drop related tables that might have foreign key constraints
    await client.query('DROP TABLE IF EXISTS "tax_groups" CASCADE;')
    console.log('✅ Dropped tax_groups table')

    await client.query('DROP TABLE IF EXISTS "tax_components" CASCADE;')
    console.log('✅ Dropped tax_components table')

    console.log('✅ Database schema reset complete!')
    console.log('Now restart the API server to recreate tables with the new schema.')

  } catch (error) {
    console.error('❌ Error fixing schema:', error.message)
  } finally {
    await client.end()
  }
}

fixTaxRulesSchema()
