-- Migration script to fix Users table schema
-- This fixes the column naming issues and removes unwanted columns

BEGIN;

-- Step 1: Check current state
SELECT 'Current branch/role columns:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('branch_id', 'branch_id_id', 'role_id', 'role_id_id', 'branch')
ORDER BY column_name;

-- Step 2: Create the correct branch_id column if it doesn't exist
-- This should be an integer foreign key to branches table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'branch_id'
    ) THEN
        ALTER TABLE users ADD COLUMN branch_id INTEGER;
        ALTER TABLE users ADD CONSTRAINT fk_users_branch_id 
            FOREIGN KEY (branch_id) REFERENCES branches(id);
        CREATE INDEX IF NOT EXISTS idx_users_branch_id ON users(branch_id);
    END IF;
END $$;

-- Step 3: Migrate data from incorrect columns to correct column
-- Migrate from branch_id_id to branch_id (if data exists)
UPDATE users 
SET branch_id = branch_id_id 
WHERE branch_id_id IS NOT NULL AND branch_id IS NULL;

-- Step 4: Drop the old text branch column (if it exists and is text)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'branch' 
        AND data_type = 'character varying'
    ) THEN
        ALTER TABLE users DROP COLUMN branch;
    END IF;
END $$;

-- Step 5: Drop the incorrect columns with double _id suffix
-- Drop branch_id_id column
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'branch_id_id'
    ) THEN
        ALTER TABLE users DROP COLUMN branch_id_id;
    END IF;
END $$;

-- Step 6: Drop all role-related columns (we don't need them anymore)
-- Drop role_id column
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'role_id'
    ) THEN
        ALTER TABLE users DROP COLUMN role_id;
    END IF;
END $$;

-- Drop role_id_id column
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'role_id_id'
    ) THEN
        ALTER TABLE users DROP COLUMN role_id_id;
    END IF;
END $$;

-- Step 7: Verify the final structure
SELECT 'Final branch/role columns after migration:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('branch_id', 'branch_id_id', 'role_id', 'role_id_id', 'branch')
ORDER BY column_name;

-- Step 8: Check foreign key constraints
SELECT 'Foreign key constraints:' as info;
SELECT
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name='users'
    AND kcu.column_name IN ('branch_id', 'branch_id_id', 'role_id', 'role_id_id', 'branch');

COMMIT;

-- Success message
SELECT 'Migration completed successfully! Users table now has proper branch_id column.' as result;
