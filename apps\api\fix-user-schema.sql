-- Fix Users table schema - Remove incorrect columns and add correct ones
-- This script fixes the column naming issue where Payload created branch_id_id and role_id_id

-- Step 1: Check current table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('branch_id', 'branch_id_id', 'role_id', 'role_id_id', 'branch')
ORDER BY column_name;

-- Step 2: Add correct branch_id column if it doesn't exist
-- (Payload will create this automatically when we restart the server)

-- Step 3: Migrate data from incorrect columns to correct ones (if needed)
-- Only run these if you have data in the incorrect columns

-- Migrate branch data from branch_id_id to branch_id (if branch_id_id exists and has data)
-- UPDATE users SET branch_id = branch_id_id WHERE branch_id_id IS NOT NULL AND branch_id IS NULL;

-- Step 4: Drop the incorrect columns (run these after confirming data migration)
-- WARNING: This will permanently delete data in these columns!

-- Drop role_id related columns (we don't need roles anymore)
-- ALTER TABLE users DROP COLUMN IF EXISTS role_id_id;
-- ALTER TABLE users DROP COLUMN IF EXISTS role_id;

-- Drop incorrect branch column (only after confirming branch_id is working)
-- ALTER TABLE users DROP COLUMN IF EXISTS branch_id_id;

-- Step 5: Verify the final structure
-- SELECT column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'users' 
-- AND column_name IN ('branch_id', 'branch')
-- ORDER BY column_name;

-- Step 6: Create index for performance (if not already exists)
-- CREATE INDEX IF NOT EXISTS idx_users_branch_id ON users(branch_id);

-- Instructions:
-- 1. First, restart your API server to let Payload create the correct schema
-- 2. Run Step 1 to check current columns
-- 3. If you have data to migrate, uncomment and run Step 3
-- 4. After confirming everything works, uncomment and run Step 4 to clean up
-- 5. Run Step 5 to verify the final structure
-- 6. Run Step 6 to add performance index
