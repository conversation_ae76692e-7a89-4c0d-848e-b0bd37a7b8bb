// Insert super admin with proper hash and salt for Payload authentication
const { Client } = require('pg')
const bcrypt = require('bcrypt')
const crypto = require('crypto')

async function insertSuperAdminProperly() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('👑 Creating Super Admin with proper Payload authentication...\n')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    const email = '<EMAIL>'
    const password = 'SuperAdmin@123'

    // Check if super admin already exists
    const existingUser = await client.query(
      'SELECT id, email, role FROM users WHERE email = $1',
      [email]
    )

    if (existingUser.rows.length > 0) {
      console.log('⚠️  Super Admin already exists! Updating with proper authentication...')
      
      // Delete existing user to recreate properly
      await client.query('DELETE FROM users WHERE email = $1', [email])
      console.log('🗑️  Deleted existing user')
    }

    // Generate salt (Payload uses random salt)
    const salt = crypto.randomBytes(32).toString('hex')
    console.log('🧂 Generated salt:', salt.substring(0, 20) + '...')

    // Generate hash using bcrypt (Payload's method)
    const saltRounds = 10
    const hash = await bcrypt.hash(password, saltRounds)
    console.log('🔒 Generated hash:', hash.substring(0, 30) + '...')

    // Insert super admin user with proper Payload fields
    console.log('👤 Creating Super Admin user...')
    const insertQuery = `
      INSERT INTO users (
        email, 
        hash, 
        salt,
        first_name, 
        last_name, 
        role, 
        is_active,
        email_verified,
        login_attempts,
        created_at, 
        updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING id, email, first_name, last_name, role
    `

    const now = new Date()
    const result = await client.query(insertQuery, [
      email,           // email
      hash,            // hash (bcrypt)
      salt,            // salt
      'Super',         // first_name
      'Admin',         // last_name
      'super_admin',   // role
      true,            // is_active
      true,            // email_verified
      0,               // login_attempts
      now,             // created_at
      now              // updated_at
    ])

    const createdUser = result.rows[0]

    console.log('\n🎉 Super Admin created successfully with proper authentication!')
    console.log('🆔 User ID:', createdUser.id)
    console.log('📧 Email:', createdUser.email)
    console.log('👤 Name:', `${createdUser.first_name} ${createdUser.last_name}`)
    console.log('🎭 Role:', createdUser.role)
    
    console.log('\n🔐 Login Credentials:')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: SuperAdmin@123')
    console.log('🌐 Admin Panel: http://localhost:3002/admin')
    console.log('🌐 Frontend Login: http://localhost:3002/auth/admin/login')

    // Verify the user can be found
    const verifyUser = await client.query(
      'SELECT id, email, role, hash, salt FROM users WHERE email = $1',
      [email]
    )

    if (verifyUser.rows.length > 0) {
      console.log('\n✅ Verification successful!')
      console.log('🔍 User found in database with proper hash and salt')
      
      // Test password verification
      const storedHash = verifyUser.rows[0].hash
      const isPasswordValid = await bcrypt.compare(password, storedHash)
      console.log('🔐 Password verification:', isPasswordValid ? '✅ Valid' : '❌ Invalid')
    }

  } catch (error) {
    console.error('❌ Error creating super admin:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Database connection failed. Please check:')
      console.log('   - PostgreSQL is running')
      console.log('   - Database "lms_new" exists')
      console.log('   - Credentials are correct (postgres:1234)')
    } else if (error.code === '23505') {
      console.log('\n💡 User with this email already exists')
    }
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed')
  }
}

console.log('👑 Super Admin Proper Creation Tool\n')
insertSuperAdminProperly()
  .then(() => {
    console.log('\n✅ Process completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Process failed:', error.message)
    process.exit(1)
  })
