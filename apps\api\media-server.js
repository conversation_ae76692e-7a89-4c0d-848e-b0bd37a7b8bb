const express = require('express')
const path = require('path')
const fs = require('fs')

// Try to load cors, but don't fail if it's not available
let cors
try {
  cors = require('cors')
} catch (error) {
  console.log('⚠️ CORS module not available, using manual CORS headers')
  cors = null
}

console.log('🚀 Starting standalone media server...')

const app = express()
const PORT = 3002 // Different port to avoid conflicts

// Enable CORS for all origins
if (cors) {
  app.use(cors({
    origin: '*',
    methods: ['GET', 'HEAD', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  }))
} else {
  // Manual CORS middleware
  app.use((req, res, next) => {
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS')
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    if (req.method === 'OPTIONS') {
      res.sendStatus(200)
    } else {
      next()
    }
  })
}

// Media directory path
const mediaDir = path.resolve(__dirname, 'media')
console.log('📁 Media directory:', mediaDir)

// Check if media directory exists
if (!fs.existsSync(mediaDir)) {
  console.error('❌ Media directory does not exist:', mediaDir)
  process.exit(1)
}

// Serve static files from media directory
app.use('/media', express.static(mediaDir, {
  maxAge: '1d', // Cache for 1 day
  etag: true,
  lastModified: true,
  setHeaders: (res, filePath) => {
    // Set proper MIME types
    if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
      res.setHeader('Content-Type', 'image/jpeg')
    } else if (filePath.endsWith('.png')) {
      res.setHeader('Content-Type', 'image/png')
    } else if (filePath.endsWith('.gif')) {
      res.setHeader('Content-Type', 'image/gif')
    } else if (filePath.endsWith('.webp')) {
      res.setHeader('Content-Type', 'image/webp')
    } else if (filePath.endsWith('.svg')) {
      res.setHeader('Content-Type', 'image/svg+xml')
    } else if (filePath.endsWith('.pdf')) {
      res.setHeader('Content-Type', 'application/pdf')
    }
    
    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS')
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type')
  }
}))

// Debug endpoint to list files
app.get('/debug/media', (req, res) => {
  console.log('🔍 Debug media endpoint called')
  
  try {
    const files = []
    
    function scanDirectory(dirPath, relativePath = '') {
      const items = fs.readdirSync(dirPath)
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item)
        const itemRelativePath = relativePath ? `${relativePath}/${item}` : item
        
        try {
          const stats = fs.statSync(itemPath)
          
          if (stats.isDirectory()) {
            scanDirectory(itemPath, itemRelativePath)
          } else {
            files.push({
              name: item,
              path: itemRelativePath,
              fullPath: itemPath,
              size: stats.size,
              modified: stats.mtime,
              url: `/media/${itemRelativePath}`
            })
          }
        } catch (error) {
          console.log(`⚠️ Could not stat ${itemPath}:`, error.message)
        }
      }
    }
    
    scanDirectory(mediaDir)
    
    // Sort by modification date (newest first)
    files.sort((a, b) => new Date(b.modified).getTime() - new Date(a.modified).getTime())
    
    console.log(`📊 Found ${files.length} files`)
    
    res.json({
      success: true,
      mediaDir,
      totalFiles: files.length,
      files: files.slice(0, 20), // Return first 20 files
      serverPort: PORT,
      message: `Found ${files.length} files in media directory`
    })
  } catch (error) {
    console.error('❌ Debug error:', error)
    res.status(500).json({
      success: false,
      message: `Debug error: ${error.message}`
    })
  }
})

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Media server is running',
    mediaDir,
    port: PORT,
    timestamp: new Date().toISOString()
  })
})

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Media Server is running',
    endpoints: {
      media: '/media/*',
      debug: '/debug/media',
      health: '/health'
    },
    mediaDir,
    port: PORT
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`✅ Media server running on http://localhost:${PORT}`)
  console.log(`📁 Serving files from: ${mediaDir}`)
  console.log(`🌐 Test URL: http://localhost:${PORT}/media/avatars/`)
  console.log(`🔍 Debug URL: http://localhost:${PORT}/debug/media`)
  console.log(`❤️ Health check: http://localhost:${PORT}/health`)
})

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down media server...')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down media server...')
  process.exit(0)
})
