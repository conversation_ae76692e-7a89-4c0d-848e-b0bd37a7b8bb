{"name": "api", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation PORT=3001 next dev -p 3001", "devsafe": "rimraf .next && cross-env NODE_OPTIONS=--no-deprecation PORT=3001 next dev -p 3001", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "seed": "cross-env NODE_OPTIONS=--no-deprecation tsx src/seed/index.ts", "seed:roles": "cross-env NODE_OPTIONS=--no-deprecation tsx src/seed/seedRolesOnly.ts", "assign:admin": "cross-env NODE_OPTIONS=--no-deprecation tsx src/seed/assignSuperAdminRole.ts", "test:permissions": "cross-env NODE_OPTIONS=--no-deprecation tsx src/seed/testRolePermissions.ts", "test:login": "cross-env NODE_OPTIONS=--no-deprecation tsx src/seed/testLogin.ts", "start": "cross-env NODE_OPTIONS=--no-deprecation PORT=3001 next start -p 3001"}, "dependencies": {"@payloadcms/db-postgres": "3.43.0", "@payloadcms/next": "3.43.0", "@payloadcms/payload-cloud": "3.43.0", "@payloadcms/richtext-lexical": "3.43.0", "@payloadcms/ui": "3.43.0", "bcrypt": "^6.0.0", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "graphql": "^16.8.1", "jsonwebtoken": "^9.0.2", "next": "15.3.0", "payload": "3.43.0", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "0.32.6"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.5.4", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "eslint": "^9.16.0", "eslint-config-next": "15.3.0", "prettier": "^3.4.2", "tsx": "^4.20.3", "typescript": "5.7.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^8 || ^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}