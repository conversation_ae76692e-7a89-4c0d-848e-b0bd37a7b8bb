// Reset all users to start fresh
const { Client } = require('pg')

async function resetUsers() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('🔄 Resetting all users to start fresh...\n')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Show current users
    const currentUsers = await client.query('SELECT id, email, role FROM users ORDER BY id')
    console.log(`\n📊 Current users (${currentUsers.rows.length}):`)
    currentUsers.rows.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.id} | Email: ${user.email} | Role: ${user.role}`)
    })

    // Delete all users
    console.log('\n🗑️  Deleting all users...')
    const deleteResult = await client.query('DELETE FROM users')
    console.log(`✅ Deleted ${deleteResult.rowCount} users`)

    // Reset the sequence
    console.log('🔄 Resetting user ID sequence...')
    await client.query('ALTER SEQUENCE users_id_seq RESTART WITH 1')
    console.log('✅ Sequence reset to start from ID 1')

    // Verify deletion
    const verifyUsers = await client.query('SELECT COUNT(*) as count FROM users')
    console.log(`\n📊 Users remaining: ${verifyUsers.rows[0].count}`)

    console.log('\n🎯 Next Steps:')
    console.log('1. Go to: http://localhost:3002/admin')
    console.log('2. You will see "Create First User" form')
    console.log('3. Fill in:')
    console.log('   📧 Email: <EMAIL>')
    console.log('   🔑 Password: SuperAdmin@123')
    console.log('   👤 First Name: Super')
    console.log('   👤 Last Name: Admin')
    console.log('   🎭 Role: Super Admin')
    console.log('4. Click "Create"')
    console.log('\n✅ This will create the user properly through Payload!')

  } catch (error) {
    console.error('❌ Error resetting users:', error.message)
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed')
  }
}

console.log('🔄 User Reset Tool\n')
resetUsers()
  .then(() => {
    console.log('\n✅ Reset completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Reset failed:', error.message)
    process.exit(1)
  })
