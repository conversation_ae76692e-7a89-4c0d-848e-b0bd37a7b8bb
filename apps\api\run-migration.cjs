// Run the database migration to fix Users table schema
const fs = require('fs')
const path = require('path')

// Try to use the pg client from the postgres adapter
let Client;
try {
  // Try to import from the postgres adapter
  const { Client: PgClient } = require('pg')
  Client = PgClient
} catch (error) {
  console.error('❌ Could not import pg client:', error.message)
  console.log('💡 Please install pg package: npm install pg')
  process.exit(1)
}

async function runMigration() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('🔌 Connecting to PostgreSQL database...')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'fix-user-schema-migration.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')

    console.log('📄 Running migration script...')
    
    // Split the SQL into individual statements and execute them
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          const result = await client.query(statement)
          if (result.rows && result.rows.length > 0) {
            console.table(result.rows)
          }
        } catch (error) {
          console.error('⚠️  Error executing statement:', error.message)
          console.log('Statement:', statement.substring(0, 100) + '...')
        }
      }
    }

    console.log('✅ Migration completed!')

    // Verify the final state
    console.log('\n🔍 Verifying final schema...')
    const finalCheck = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name IN ('branch_id', 'branch_id_id', 'role_id', 'role_id_id', 'branch')
      ORDER BY column_name;
    `)

    if (finalCheck.rows.length === 0) {
      console.log('⚠️  No branch/role columns found after migration.')
    } else {
      console.log('\n📊 Final branch/role columns:')
      console.table(finalCheck.rows)
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    console.log('\n💡 Make sure PostgreSQL is running and the database exists.')
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed.')
  }
}

// Run the migration
runMigration().catch(console.error)
