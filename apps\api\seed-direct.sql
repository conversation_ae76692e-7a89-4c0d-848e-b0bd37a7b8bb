-- Direct SQL script to create Super Admin user
-- Run this script in PostgreSQL to create the super admin user

-- First, check if the users table exists
-- If not, you need to run the API server first to create the schema

-- Check if super admin already exists
SELECT id, email, role FROM users WHERE email = '<EMAIL>';

-- If no results, insert the super admin user
-- Password hash for 'SuperAdmin@123' using bcrypt with salt rounds 10
-- You can generate this hash using: bcrypt.hash('SuperAdmin@123', 10)

INSERT INTO users (
    email, 
    password, 
    "firstName", 
    "lastName", 
    role, 
    "isActive",
    "createdAt", 
    "updatedAt"
) 
SELECT 
    '<EMAIL>',
    '$2b$10$rKJ8VQx5Zx5Zx5Zx5Zx5ZOQx5Zx5Zx5Zx5Zx5Zx5Zx5Zx5Zx5Zx5Z', -- This is a placeholder hash
    'Super',
    'Admin',
    'super_admin',
    true,
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM users WHERE email = '<EMAIL>'
);

-- Verify the user was created
SELECT id, email, "firstName", "lastName", role, "isActive", "createdAt" 
FROM users 
WHERE email = '<EMAIL>';

-- Display login credentials
SELECT 
    'Super Admin user created successfully!' as message,
    '<EMAIL>' as email,
    'SuperAdmin@123' as password,
    'http://localhost:3002/admin' as admin_url,
    'http://localhost:3002/auth/admin/login' as frontend_url;
