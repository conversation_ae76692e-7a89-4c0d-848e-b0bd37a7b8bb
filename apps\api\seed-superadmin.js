const { Client } = require('pg')
const bcrypt = require('bcrypt')

// Database configuration
const dbConfig = {
  host: '127.0.0.1',
  port: 5432,
  database: 'lms_new',
  user: 'postgres',
  password: '1234'
}

// Super Admin data
const superAdminData = {
  email: '<EMAIL>',
  password: 'SuperAdmin@123',
  firstName: 'Super',
  lastName: 'Admin',
  role: 'super_admin'
}

async function seedSuperAdmin() {
  const client = new Client(dbConfig)
  
  try {
    console.log('🔌 Connecting to PostgreSQL database...')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Check if users table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `)

    if (!tableCheck.rows[0].exists) {
      console.log('❌ Users table does not exist. Please run the API server first to create tables.')
      return
    }

    console.log('📋 Users table found!')

    // Check if super admin already exists
    const existingUser = await client.query(
      'SELECT id, email, role FROM users WHERE email = $1',
      [superAdminData.email]
    )

    if (existingUser.rows.length > 0) {
      console.log('⚠️  Super Admin already exists!')
      console.log('📧 Email:', existingUser.rows[0].email)
      console.log('🎭 Role:', existingUser.rows[0].role)
      console.log('🆔 ID:', existingUser.rows[0].id)
      console.log('\n🔐 Login Credentials:')
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Password: SuperAdmin@123')
      console.log('🌐 Login URL: http://localhost:3002/admin')
      return
    }

    // Hash the password
    console.log('🔐 Hashing password...')
    const saltRounds = 10
    const hashedPassword = await bcrypt.hash(superAdminData.password, saltRounds)

    // Insert super admin user
    console.log('👤 Creating Super Admin user...')
    const insertQuery = `
      INSERT INTO users (
        email, 
        password, 
        "firstName", 
        "lastName", 
        role, 
        "isActive",
        "createdAt", 
        "updatedAt"
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, email, "firstName", "lastName", role
    `

    const now = new Date().toISOString()
    const result = await client.query(insertQuery, [
      superAdminData.email,
      hashedPassword,
      superAdminData.firstName,
      superAdminData.lastName,
      superAdminData.role,
      true, // isActive
      now,  // createdAt
      now   // updatedAt
    ])

    const createdUser = result.rows[0]

    console.log('\n🎉 Super Admin created successfully!')
    console.log('🆔 User ID:', createdUser.id)
    console.log('📧 Email:', createdUser.email)
    console.log('👤 Name:', `${createdUser.firstName} ${createdUser.lastName}`)
    console.log('🎭 Role:', createdUser.role)
    
    console.log('\n🔐 Login Credentials:')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: SuperAdmin@123')
    console.log('🌐 Admin Panel: http://localhost:3002/admin')
    console.log('🌐 Frontend Login: http://localhost:3002/auth/admin/login')

  } catch (error) {
    console.error('❌ Error seeding super admin:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Database connection failed. Please check:')
      console.log('   - PostgreSQL is running')
      console.log('   - Database "lms_new" exists')
      console.log('   - Credentials are correct (postgres:1234)')
    } else if (error.code === '23505') {
      console.log('\n💡 User with this email already exists')
    } else {
      console.log('\n💡 Make sure the API server has been run at least once to create the database schema')
    }
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed')
  }
}

// Run the seeding function
console.log('🌱 Starting Super Admin seeding process...\n')
seedSuperAdmin()
  .then(() => {
    console.log('\n✅ Seeding process completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Seeding process failed:', error.message)
    process.exit(1)
  })
