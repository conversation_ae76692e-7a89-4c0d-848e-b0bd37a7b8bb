const { getPayload } = require('payload')
const config = require('./src/payload.config.ts')

async function seedThemes() {
  try {
    console.log('🌱 Starting theme seeding...')
    
    const payload = await getPayload({ config })
    
    // Check if themes already exist
    const existingThemes = await payload.find({
      collection: 'themes',
      where: {
        type: { equals: 'institute' }
      },
      limit: 1
    })

    if (existingThemes.totalDocs > 0) {
      console.log('✅ Institute themes already exist, skipping seed')
      process.exit(0)
    }

    // Institute themes to create
    const instituteThemes = [
      {
        name: 'Education Modern',
        slug: 'education-modern',
        type: 'institute',
        category: 'Education',
        description: 'A clean, modern theme perfect for educational institutions',
        isActive: true,
        colors: {
          primary: '#059669',
          secondary: '#6b7280',
          accent: '#f59e0b',
          background: '#ffffff',
          text: '#111827'
        },
        fonts: {
          heading: 'Poppins',
          body: 'Inter'
        },
        features: [
          'Responsive Design',
          'Course Catalog',
          'Student Portal',
          'Online Enrollment'
        ],
        usageCount: 0,
        rating: 4.8
      },
      {
        name: 'Business Professional',
        slug: 'business-professional',
        type: 'institute',
        category: 'Business',
        description: 'A sophisticated theme for business schools',
        isActive: true,
        colors: {
          primary: '#1e40af',
          secondary: '#374151',
          accent: '#dc2626',
          background: '#f9fafb',
          text: '#1f2937'
        },
        fonts: {
          heading: 'Roboto',
          body: 'Open Sans'
        },
        features: [
          'Professional Layout',
          'Corporate Branding',
          'Executive Programs',
          'Certification Tracking'
        ],
        usageCount: 0,
        rating: 4.6
      },
      {
        name: 'Creative Arts',
        slug: 'creative-arts',
        type: 'institute',
        category: 'Creative',
        description: 'A vibrant theme for art schools and creative academies',
        isActive: true,
        colors: {
          primary: '#7c3aed',
          secondary: '#ec4899',
          accent: '#f59e0b',
          background: '#fefefe',
          text: '#1f2937'
        },
        fonts: {
          heading: 'Playfair Display',
          body: 'Source Sans Pro'
        },
        features: [
          'Portfolio Showcase',
          'Gallery Integration',
          'Creative Workshops',
          'Artist Profiles'
        ],
        usageCount: 0,
        rating: 4.9
      },
      {
        name: 'Tech Academy',
        slug: 'tech-academy',
        type: 'institute',
        category: 'Technology',
        description: 'A cutting-edge theme for technology institutes',
        isActive: true,
        colors: {
          primary: '#0891b2',
          secondary: '#475569',
          accent: '#10b981',
          background: '#ffffff',
          text: '#0f172a'
        },
        fonts: {
          heading: 'JetBrains Mono',
          body: 'Fira Sans'
        },
        features: [
          'Code Playground',
          'Project Showcase',
          'Tech Stack Display',
          'Bootcamp Programs'
        ],
        usageCount: 0,
        rating: 4.7
      }
    ]

    // Create institute themes
    for (const themeData of instituteThemes) {
      try {
        const theme = await payload.create({
          collection: 'themes',
          data: themeData
        })
        console.log(`✅ Created institute theme: ${themeData.name}`)
      } catch (error) {
        console.error(`❌ Failed to create theme ${themeData.name}:`, error.message)
      }
    }

    console.log('🎉 Theme seeding completed!')
    process.exit(0)

  } catch (error) {
    console.error('❌ Error seeding themes:', error)
    process.exit(1)
  }
}

seedThemes()
