#!/usr/bin/env python3
import http.server
import socketserver
import os
import json
from urllib.parse import unquote

class MediaHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.join(os.getcwd(), 'media'), **kwargs)
    
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        # Handle debug endpoint
        if self.path == '/debug/media':
            self.handle_debug()
            return
        
        # Handle health check
        if self.path == '/health':
            self.handle_health()
            return
        
        # Handle media files
        if self.path.startswith('/media/'):
            # Remove /media prefix and serve from media directory
            file_path = self.path[7:]  # Remove '/media/' prefix
            self.path = '/' + file_path
        
        # Call parent handler
        super().do_GET()
    
    def handle_debug(self):
        try:
            media_dir = os.path.join(os.getcwd(), 'media')
            files = []
            
            for root, dirs, filenames in os.walk(media_dir):
                for filename in filenames:
                    full_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(full_path, media_dir)
                    stat = os.stat(full_path)
                    
                    files.append({
                        'name': filename,
                        'path': relative_path.replace('\\', '/'),
                        'fullPath': full_path,
                        'size': stat.st_size,
                        'modified': stat.st_mtime,
                        'url': f'/media/{relative_path.replace(os.sep, "/")}'
                    })
            
            # Sort by modification time (newest first)
            files.sort(key=lambda x: x['modified'], reverse=True)
            
            response = {
                'success': True,
                'mediaDir': media_dir,
                'totalFiles': len(files),
                'files': files[:20],  # Return first 20 files
                'serverPort': 3002,
                'message': f'Found {len(files)} files in media directory'
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response, indent=2).encode())
            
        except Exception as e:
            error_response = {
                'success': False,
                'message': f'Debug error: {str(e)}'
            }
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(error_response).encode())
    
    def handle_health(self):
        response = {
            'status': 'ok',
            'message': 'Media server is running',
            'mediaDir': os.path.join(os.getcwd(), 'media'),
            'port': 3002,
            'timestamp': str(os.times())
        }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response, indent=2).encode())

def main():
    PORT = 3002
    media_dir = os.path.join(os.getcwd(), 'media')
    
    print(f"🚀 Starting Python media server...")
    print(f"📁 Media directory: {media_dir}")
    
    if not os.path.exists(media_dir):
        print(f"❌ Media directory does not exist: {media_dir}")
        return
    
    print(f"✅ Media server running on http://localhost:{PORT}")
    print(f"📁 Serving files from: {media_dir}")
    print(f"🌐 Test URL: http://localhost:{PORT}/media/avatars/")
    print(f"🔍 Debug URL: http://localhost:{PORT}/debug/media")
    print(f"❤️ Health check: http://localhost:{PORT}/health")
    print(f"🛑 Press Ctrl+C to stop")
    
    try:
        with socketserver.TCPServer(("", PORT), MediaHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down media server...")

if __name__ == "__main__":
    main()
