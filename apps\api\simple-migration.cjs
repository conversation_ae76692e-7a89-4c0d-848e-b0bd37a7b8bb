// Simple migration to fix Users table schema
const { Client } = require('pg')

async function runSimpleMigration() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('🔌 Connecting to PostgreSQL database...')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Step 1: Add the correct branch_id column
    console.log('\n📝 Step 1: Adding branch_id column...')
    try {
      await client.query('ALTER TABLE users ADD COLUMN branch_id INTEGER;')
      console.log('✅ Added branch_id column')
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  branch_id column already exists')
      } else {
        console.error('❌ Error adding branch_id column:', error.message)
      }
    }

    // Step 2: Add foreign key constraint
    console.log('\n📝 Step 2: Adding foreign key constraint...')
    try {
      await client.query('ALTER TABLE users ADD CONSTRAINT fk_users_branch_id FOREIGN KEY (branch_id) REFERENCES branches(id);')
      console.log('✅ Added foreign key constraint')
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  Foreign key constraint already exists')
      } else {
        console.error('❌ Error adding foreign key constraint:', error.message)
      }
    }

    // Step 3: Create index
    console.log('\n📝 Step 3: Creating index...')
    try {
      await client.query('CREATE INDEX idx_users_branch_id ON users(branch_id);')
      console.log('✅ Created index')
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  Index already exists')
      } else {
        console.error('❌ Error creating index:', error.message)
      }
    }

    // Step 4: Migrate data from branch_id_id to branch_id
    console.log('\n📝 Step 4: Migrating data from branch_id_id to branch_id...')
    try {
      const result = await client.query('UPDATE users SET branch_id = branch_id_id WHERE branch_id_id IS NOT NULL AND branch_id IS NULL;')
      console.log(`✅ Migrated ${result.rowCount} rows`)
    } catch (error) {
      console.error('❌ Error migrating data:', error.message)
    }

    // Step 5: Drop the old text branch column
    console.log('\n📝 Step 5: Dropping old text branch column...')
    try {
      await client.query('ALTER TABLE users DROP COLUMN branch;')
      console.log('✅ Dropped old branch column')
    } catch (error) {
      if (error.message.includes('does not exist')) {
        console.log('ℹ️  Branch column does not exist')
      } else {
        console.error('❌ Error dropping branch column:', error.message)
      }
    }

    // Step 6: Drop branch_id_id column
    console.log('\n📝 Step 6: Dropping branch_id_id column...')
    try {
      await client.query('ALTER TABLE users DROP COLUMN branch_id_id;')
      console.log('✅ Dropped branch_id_id column')
    } catch (error) {
      if (error.message.includes('does not exist')) {
        console.log('ℹ️  branch_id_id column does not exist')
      } else {
        console.error('❌ Error dropping branch_id_id column:', error.message)
      }
    }

    // Step 7: Drop role_id column
    console.log('\n📝 Step 7: Dropping role_id column...')
    try {
      await client.query('ALTER TABLE users DROP COLUMN role_id;')
      console.log('✅ Dropped role_id column')
    } catch (error) {
      if (error.message.includes('does not exist')) {
        console.log('ℹ️  role_id column does not exist')
      } else {
        console.error('❌ Error dropping role_id column:', error.message)
      }
    }

    // Step 8: Drop role_id_id column
    console.log('\n📝 Step 8: Dropping role_id_id column...')
    try {
      await client.query('ALTER TABLE users DROP COLUMN role_id_id;')
      console.log('✅ Dropped role_id_id column')
    } catch (error) {
      if (error.message.includes('does not exist')) {
        console.log('ℹ️  role_id_id column does not exist')
      } else {
        console.error('❌ Error dropping role_id_id column:', error.message)
      }
    }

    // Step 9: Verify final state
    console.log('\n🔍 Verifying final schema...')
    const finalCheck = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name IN ('branch_id', 'branch_id_id', 'role_id', 'role_id_id', 'branch')
      ORDER BY column_name;
    `)

    if (finalCheck.rows.length === 0) {
      console.log('⚠️  No branch/role columns found after migration.')
    } else {
      console.log('\n📊 Final branch/role columns:')
      console.table(finalCheck.rows)
    }

    // Check foreign keys
    const fkCheck = await client.query(`
      SELECT
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name='users'
        AND kcu.column_name IN ('branch_id', 'branch_id_id', 'role_id', 'role_id_id', 'branch');
    `)

    if (fkCheck.rows.length > 0) {
      console.log('\n🔗 Foreign key constraints:')
      console.table(fkCheck.rows)
    } else {
      console.log('\n⚠️  No foreign key constraints found.')
    }

    console.log('\n🎉 Migration completed successfully!')

  } catch (error) {
    console.error('❌ Migration failed:', error.message)
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed.')
  }
}

// Run the migration
runSimpleMigration().catch(console.error)
