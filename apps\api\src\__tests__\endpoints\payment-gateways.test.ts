import request from 'supertest'
import { Express } from 'express'

// Mock payload instance
const mockPayload = {
  find: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  findByID: jest.fn(),
}

// Mock request object
const createMockRequest = (user: any = null) => ({
  user,
  payload: mockPayload,
  params: {},
  body: {},
  query: {}
})

// Mock response object
const createMockResponse = () => {
  const res: any = {}
  res.status = jest.fn().mockReturnValue(res)
  res.json = jest.fn().mockReturnValue(res)
  return res
}

// Import the endpoint handlers
import paymentGatewayEndpoints from '../../endpoints/super-admin/payment-gateways'
import institutePaymentConfigEndpoints from '../../endpoints/institute-admin/payment-configs'

describe('Payment Gateway Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Super Admin Payment Gateway Endpoints', () => {
    const superAdminUser = { role: 'super_admin', id: 'admin1' }

    describe('GET /super-admin/payment-gateways', () => {
      it('should fetch payment gateways for super admin', async () => {
        const mockGateways = {
          docs: [
            {
              id: '1',
              name: 'Razorpay',
              provider: 'razorpay',
              isActive: true
            }
          ],
          totalDocs: 1
        }

        mockPayload.find.mockResolvedValueOnce(mockGateways)

        const req = createMockRequest(superAdminUser)
        const res = createMockResponse()

        const endpoint = paymentGatewayEndpoints.find(e => e.path === '/super-admin/payment-gateways' && e.method === 'get')
        await endpoint?.handler(req, res)

        expect(mockPayload.find).toHaveBeenCalledWith({
          collection: 'payment-gateways',
          sort: 'displayOrder',
          limit: 100
        })
        expect(res.json).toHaveBeenCalledWith({
          success: true,
          gateways: mockGateways.docs,
          total: mockGateways.totalDocs
        })
      })

      it('should deny access for non-super admin', async () => {
        const req = createMockRequest({ role: 'institute_admin' })
        const res = createMockResponse()

        const endpoint = paymentGatewayEndpoints.find(e => e.path === '/super-admin/payment-gateways' && e.method === 'get')
        await endpoint?.handler(req, res)

        expect(res.status).toHaveBeenCalledWith(403)
        expect(res.json).toHaveBeenCalledWith({ error: 'Access denied' })
      })
    })

    describe('POST /super-admin/payment-gateways', () => {
      it('should create payment gateway for super admin', async () => {
        const gatewayData = {
          name: 'Stripe',
          provider: 'stripe',
          supportedCurrencies: [{ currency: 'USD' }],
          isActive: true
        }

        const createdGateway = { id: '2', ...gatewayData }
        mockPayload.create.mockResolvedValueOnce(createdGateway)

        const req = createMockRequest(superAdminUser)
        req.body = gatewayData
        const res = createMockResponse()

        const endpoint = paymentGatewayEndpoints.find(e => e.path === '/super-admin/payment-gateways' && e.method === 'post')
        await endpoint?.handler(req, res)

        expect(mockPayload.create).toHaveBeenCalledWith({
          collection: 'payment-gateways',
          data: gatewayData
        })
        expect(res.json).toHaveBeenCalledWith({
          success: true,
          gateway: createdGateway,
          message: 'Payment gateway created successfully'
        })
      })
    })

    describe('DELETE /super-admin/payment-gateways/:id', () => {
      it('should delete payment gateway if not in use', async () => {
        mockPayload.find.mockResolvedValueOnce({ totalDocs: 0 })
        mockPayload.delete.mockResolvedValueOnce({})

        const req = createMockRequest(superAdminUser)
        req.params = { id: '1' }
        const res = createMockResponse()

        const endpoint = paymentGatewayEndpoints.find(e => e.path === '/super-admin/payment-gateways/:id' && e.method === 'delete')
        await endpoint?.handler(req, res)

        expect(mockPayload.find).toHaveBeenCalledWith({
          collection: 'institute-payment-configs',
          where: { gateway: { equals: '1' } },
          limit: 1
        })
        expect(mockPayload.delete).toHaveBeenCalledWith({
          collection: 'payment-gateways',
          id: '1'
        })
        expect(res.json).toHaveBeenCalledWith({
          success: true,
          message: 'Payment gateway deleted successfully'
        })
      })

      it('should prevent deletion if gateway is in use', async () => {
        mockPayload.find.mockResolvedValueOnce({ totalDocs: 1 })

        const req = createMockRequest(superAdminUser)
        req.params = { id: '1' }
        const res = createMockResponse()

        const endpoint = paymentGatewayEndpoints.find(e => e.path === '/super-admin/payment-gateways/:id' && e.method === 'delete')
        await endpoint?.handler(req, res)

        expect(res.status).toHaveBeenCalledWith(400)
        expect(res.json).toHaveBeenCalledWith({
          error: 'Cannot delete gateway that is being used by institutes'
        })
      })
    })
  })

  describe('Institute Admin Payment Config Endpoints', () => {
    const instituteAdminUser = { role: 'institute_admin', id: 'admin1', institute: 'inst1' }

    describe('GET /institute-admin/payment-gateways/available', () => {
      it('should fetch available gateways for institute admin', async () => {
        const mockGateways = {
          docs: [
            {
              id: '1',
              name: 'Razorpay',
              provider: 'razorpay',
              isActive: true
            }
          ]
        }

        mockPayload.find.mockResolvedValueOnce(mockGateways)

        const req = createMockRequest(instituteAdminUser)
        const res = createMockResponse()

        const endpoint = institutePaymentConfigEndpoints.find(e => e.path === '/institute-admin/payment-gateways/available')
        await endpoint?.handler(req, res)

        expect(mockPayload.find).toHaveBeenCalledWith({
          collection: 'payment-gateways',
          where: { isActive: { equals: true } },
          sort: 'displayOrder'
        })
        expect(res.json).toHaveBeenCalledWith({
          success: true,
          gateways: mockGateways.docs
        })
      })
    })

    describe('POST /institute-admin/payment-configs', () => {
      it('should configure payment gateway for institute', async () => {
        const gatewayData = {
          gatewayId: '1',
          configData: { key_id: 'test_key', key_secret: 'test_secret' },
          isTestMode: true
        }

        const mockGateway = { id: '1', name: 'Razorpay', isActive: true }
        const mockConfig = { id: 'config1', ...gatewayData }

        mockPayload.findByID.mockResolvedValueOnce(mockGateway)
        mockPayload.find.mockResolvedValueOnce({ totalDocs: 0 })
        mockPayload.create.mockResolvedValueOnce(mockConfig)

        const req = createMockRequest(instituteAdminUser)
        req.body = gatewayData
        const res = createMockResponse()

        const endpoint = institutePaymentConfigEndpoints.find(e => e.path === '/institute-admin/payment-configs' && e.method === 'post')
        await endpoint?.handler(req, res)

        expect(mockPayload.findByID).toHaveBeenCalledWith({
          collection: 'payment-gateways',
          id: '1'
        })
        expect(mockPayload.create).toHaveBeenCalledWith({
          collection: 'institute-payment-configs',
          data: {
            institute: 'inst1',
            gateway: '1',
            configData: gatewayData.configData,
            isTestMode: true,
            status: 'testing',
            configuredBy: 'admin1'
          }
        })
        expect(res.json).toHaveBeenCalledWith({
          success: true,
          config: mockConfig,
          message: 'Payment gateway configured successfully'
        })
      })

      it('should update existing configuration', async () => {
        const gatewayData = {
          gatewayId: '1',
          configData: { key_id: 'updated_key', key_secret: 'updated_secret' },
          isTestMode: false
        }

        const mockGateway = { id: '1', name: 'Razorpay', isActive: true }
        const existingConfig = { id: 'config1', institute: 'inst1', gateway: '1' }
        const updatedConfig = { id: 'config1', ...gatewayData }

        mockPayload.findByID.mockResolvedValueOnce(mockGateway)
        mockPayload.find.mockResolvedValueOnce({ totalDocs: 1, docs: [existingConfig] })
        mockPayload.update.mockResolvedValueOnce(updatedConfig)

        const req = createMockRequest(instituteAdminUser)
        req.body = gatewayData
        const res = createMockResponse()

        const endpoint = institutePaymentConfigEndpoints.find(e => e.path === '/institute-admin/payment-configs' && e.method === 'post')
        await endpoint?.handler(req, res)

        expect(mockPayload.update).toHaveBeenCalledWith({
          collection: 'institute-payment-configs',
          id: 'config1',
          data: {
            configData: gatewayData.configData,
            isTestMode: false,
            status: 'testing',
            configuredBy: 'admin1'
          }
        })
      })
    })
  })
})
