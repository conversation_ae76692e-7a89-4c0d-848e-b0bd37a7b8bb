import { Access } from 'payload/config'

// Check if user is authenticated
export const isAuthenticated: Access = ({ req: { user } }) => {
  return Boolean(user)
}

// Check if user is admin (super admin or institute admin)
export const isAdmin: Access = ({ req: { user } }) => {
  if (!user) return false
  return user.legacyRole === 'super_admin' || user.legacyRole === 'institute_admin'
}

// Check if user is super admin
export const isSuperAdmin: Access = ({ req: { user } }) => {
  if (!user) return false
  return user.legacyRole === 'super_admin'
}

// Check if user is institute admin
export const isInstituteAdmin: Access = ({ req: { user } }) => {
  if (!user) return false
  return user.legacyRole === 'institute_admin'
}

// Check if user is student
export const isStudent: Access = ({ req: { user } }) => {
  if (!user) return false
  return user.role === 'student'
}

// Check if user owns the resource or is admin
export const isOwnerOrAdmin: Access = ({ req: { user } }) => {
  if (!user) return false
  
  // Super admin can access everything
  if (user.role === 'super_admin') return true
  
  // Institute admin can access their institute's resources
  if (user.role === 'institute_admin') {
    return {
      institute: {
        equals: user.institute
      }
    }
  }
  
  // Students can only access their own resources
  if (user.role === 'student') {
    return {
      user: {
        equals: user.id
      }
    }
  }
  
  return false
}

// Check if user can access institute resources
export const canAccessInstitute: Access = ({ req: { user } }) => {
  if (!user) return false
  
  // Super admin can access all institutes
  if (user.role === 'super_admin') return true
  
  // Institute admin can only access their own institute
  if (user.role === 'institute_admin') {
    return {
      id: {
        equals: user.institute
      }
    }
  }
  
  return false
}

// Check if user can manage courses
export const canManageCourses: Access = ({ req: { user } }) => {
  if (!user) return false

  // Super admin can manage all courses
  if (user.legacyRole === 'super_admin') return true

  // Institute admin can manage their institute's courses
  if (user.legacyRole === 'institute_admin') {
    return {
      institute_id: {
        equals: user.institute
      }
    }
  }

  // Institute staff (branch managers, trainers) can manage courses in their branch
  if (['branch_manager', 'trainer'].includes(user.role)) {
    return {
      and: [
        {
          institute_id: {
            equals: user.institute
          }
        },
        {
          or: [
            {
              branch_id: {
                equals: user.branch
              }
            },
            {
              visibility: {
                equals: 'public'
              }
            }
          ]
        }
      ]
    }
  }

  return false
}

// Check if user can view courses (including students)
export const canViewCourses: Access = ({ req: { user } }) => {
  if (!user) {
    // Allow public access to published courses
    return {
      status: {
        equals: 'published'
      }
    }
  }

  // Super admin can view all courses
  if (user.legacyRole === 'super_admin') return true

  // Institute admin can view their institute's courses
  if (user.legacyRole === 'institute_admin') {
    return {
      institute_id: {
        equals: user.institute
      }
    }
  }

  // Institute staff can view courses in their branch + public courses in institute
  if (['branch_manager', 'trainer'].includes(user.role)) {
    return {
      and: [
        {
          institute_id: {
            equals: user.institute
          }
        },
        {
          or: [
            {
              branch_id: {
                equals: user.branch
              }
            },
            {
              visibility: {
                equals: 'public'
              }
            }
          ]
        }
      ]
    }
  }

  // Students can view published courses
  if (user.role === 'student') {
    return {
      status: {
        equals: 'published'
      }
    }
  }

  return false
}

// Check if user can manage themes
export const canManageThemes: Access = ({ req: { user } }) => {
  if (!user) return false
  
  // Super admin can manage all themes
  if (user.role === 'super_admin') return true
  
  // Institute admin can manage institute themes
  if (user.role === 'institute_admin') {
    return {
      or: [
        {
          type: {
            equals: 'institute'
          }
        },
        {
          and: [
            {
              type: {
                equals: 'platform'
              }
            },
            {
              isActive: {
                equals: true
              }
            }
          ]
        }
      ]
    }
  }
  
  return false
}

// Check if user can view themes
export const canViewThemes: Access = ({ req: { user } }) => {
  // Allow public access to active themes
  if (!user) {
    return {
      isActive: {
        equals: true
      }
    }
  }
  
  // Super admin can view all themes
  if (user.role === 'super_admin') return true
  
  // Institute admin can view active themes
  if (user.role === 'institute_admin') {
    return {
      isActive: {
        equals: true
      }
    }
  }
  
  // Students can view active themes
  return {
    isActive: {
      equals: true
    }
  }
}

// Check if user can manage media
export const canManageMedia: Access = ({ req: { user } }) => {
  if (!user) return false
  
  // Super admin can manage all media
  if (user.role === 'super_admin') return true
  
  // Institute admin can manage their institute's media
  if (user.role === 'institute_admin') {
    return {
      or: [
        {
          uploadedBy: {
            equals: user.id
          }
        },
        {
          institute: {
            equals: user.institute
          }
        }
      ]
    }
  }
  
  return false
}

// Check if user can view media
export const canViewMedia: Access = ({ req: { user } }) => {
  // Allow public access to public media
  if (!user) {
    return {
      isPublic: {
        equals: true
      }
    }
  }

  // Super admin can view all media
  if (user.role === 'super_admin') return true

  // Institute admin can view their institute's media and public media
  if (user.role === 'institute_admin') {
    return {
      or: [
        {
          institute: {
            equals: user.institute
          }
        },
        {
          isPublic: {
            equals: true
          }
        }
      ]
    }
  }

  // Students can view public media
  return {
    isPublic: {
      equals: true
    }
  }
}

// Check if user is admin or accessing their own resource
export const isAdminOrSelf: Access = ({ req: { user } }) => {
  if (!user) return false

  // Super admin can access everything
  if (user.role === 'super_admin') return true

  // Institute admin can access their institute's resources
  if (user.role === 'institute_admin') {
    return {
      or: [
        {
          user: {
            equals: user.id
          }
        },
        {
          institute: {
            equals: user.institute
          }
        }
      ]
    }
  }

  // Students can only access their own resources
  return {
    user: {
      equals: user.id
    }
  }
}
