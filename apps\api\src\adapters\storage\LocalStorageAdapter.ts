import fs from 'fs/promises'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'
import {
  StorageAdapter,
  StorageConfig,
  UploadOptions,
  UploadResult,
  DeleteResult,
  FileInfo
} from './StorageAdapter'

/**
 * Local Storage Adapter Implementation
 * Handles file storage on the local filesystem
 */
export class LocalStorageAdapter extends StorageAdapter {
  private uploadDir: string
  private baseUrl: string
  private publicPath: string

  constructor(config: StorageConfig) {
    super(config)
    
    if (!config.local) {
      throw new Error('Local storage configuration is required')
    }

    this.uploadDir = config.local.uploadDir
    this.baseUrl = config.local.baseUrl
    this.publicPath = config.local.publicPath || '/media'

    console.log('📁 Local Storage Adapter initialized:', {
      uploadDir: this.uploadDir,
      baseUrl: this.baseUrl,
      publicPath: this.publicPath
    })

    // Ensure upload directory exists
    this.ensureUploadDir()
  }

  /**
   * Upload file to local storage
   */
  async uploadFile(
    buffer: Buffer,
    originalName: string,
    mimeType: string,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    console.log('📤 Local storage upload started:', {
      originalName,
      mimeType,
      size: buffer.length,
      folder: options.folder
    })

    // Validate file
    const validation = this.validateFile(buffer, mimeType, options)
    if (!validation.valid) {
      throw new Error(validation.message)
    }

    // Generate unique filename
    const filename = options.filename || this.generateFilename(originalName, options.folder)
    const fullPath = path.join(this.uploadDir, filename)
    const dir = path.dirname(fullPath)

    try {
      // Ensure directory exists
      await fs.mkdir(dir, { recursive: true })

      // Write file to disk
      await fs.writeFile(fullPath, buffer)

      // Get file stats
      const stats = await fs.stat(fullPath)

      // Generate result
      const fileId = uuidv4()
      const url = this.getPublicUrl(filename)

      const result: UploadResult = {
        id: fileId,
        filename: path.basename(filename),
        originalName,
        mimeType,
        size: stats.size,
        url,
        path: filename,
        metadata: {
          format: this.getExtensionFromMimeType(mimeType)
        }
      }

      // Process image sizes if requested
      if (mimeType.startsWith('image/') && options.generateSizes) {
        result.sizes = await this.generateImageSizes(buffer, filename, mimeType, options.generateSizes)
      }

      console.log('✅ Local storage upload completed:', {
        filename: result.filename,
        url: result.url,
        size: result.size,
        sizes: result.sizes ? Object.keys(result.sizes) : []
      })

      return result

    } catch (error) {
      console.error('❌ Local storage upload failed:', error)
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Delete file from local storage
   */
  async deleteFile(filePath: string): Promise<DeleteResult> {
    console.log('🗑️ Deleting file from local storage:', filePath)

    try {
      const fullPath = path.join(this.uploadDir, filePath)
      
      // Check if file exists
      try {
        await fs.access(fullPath)
      } catch {
        return {
          success: true,
          message: 'File does not exist'
        }
      }

      // Delete the file
      await fs.unlink(fullPath)

      console.log('✅ File deleted successfully:', filePath)
      return {
        success: true,
        message: 'File deleted successfully'
      }

    } catch (error) {
      console.error('❌ Failed to delete file:', error)
      return {
        success: false,
        message: `Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Get file information
   */
  async getFileInfo(filePath: string): Promise<FileInfo> {
    try {
      const fullPath = path.join(this.uploadDir, filePath)
      const stats = await fs.stat(fullPath)

      return {
        exists: true,
        size: stats.size,
        lastModified: stats.mtime,
        contentType: this.getMimeTypeFromExtension(path.extname(filePath))
      }

    } catch {
      return {
        exists: false
      }
    }
  }

  /**
   * Get public URL for file
   */
  getPublicUrl(filePath: string): string {
    // Remove leading slash if present
    const cleanPath = filePath.startsWith('/') ? filePath.substring(1) : filePath
    return `${this.publicPath}/${cleanPath}`
  }

  /**
   * Health check for local storage
   */
  async healthCheck(): Promise<{ healthy: boolean; message?: string }> {
    try {
      // Check if upload directory exists and is writable
      await fs.access(this.uploadDir, fs.constants.W_OK)
      
      // Try to write a test file
      const testFile = path.join(this.uploadDir, '.health-check')
      await fs.writeFile(testFile, 'health-check')
      await fs.unlink(testFile)

      return {
        healthy: true,
        message: 'Local storage is healthy'
      }

    } catch (error) {
      return {
        healthy: false,
        message: `Local storage health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Ensure upload directory exists
   */
  private async ensureUploadDir(): Promise<void> {
    try {
      await fs.mkdir(this.uploadDir, { recursive: true })
      console.log('📁 Upload directory ensured:', this.uploadDir)
    } catch (error) {
      console.error('❌ Failed to create upload directory:', error)
      throw new Error(`Failed to create upload directory: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate image sizes (placeholder - requires Sharp library)
   */
  private async generateImageSizes(
    buffer: Buffer,
    filename: string,
    mimeType: string,
    sizes: any[]
  ): Promise<Record<string, UploadResult>> {
    console.log('⚠️ Image resizing not implemented yet - requires Sharp library')
    // TODO: Implement image resizing with Sharp
    return {}
  }

  /**
   * Get MIME type from file extension
   */
  private getMimeTypeFromExtension(ext: string): string {
    const extToMime: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      '.pdf': 'application/pdf',
      '.txt': 'text/plain'
    }

    return extToMime[ext.toLowerCase()] || 'application/octet-stream'
  }
}
