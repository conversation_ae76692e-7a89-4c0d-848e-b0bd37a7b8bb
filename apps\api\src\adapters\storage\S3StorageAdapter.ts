import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  HeadObjectCommand,
  GetObjectCommand
} from '@aws-sdk/client-s3'
import { v4 as uuidv4 } from 'uuid'
import {
  StorageAdapter,
  StorageConfig,
  UploadOptions,
  UploadResult,
  DeleteResult,
  FileInfo
} from './StorageAdapter'

/**
 * S3 Storage Adapter Implementation
 * Handles file storage on AWS S3 or S3-compatible services
 */
export class S3StorageAdapter extends StorageAdapter {
  private s3Client: S3Client
  private bucket: string
  private region: string
  private publicUrl?: string
  private cdnUrl?: string

  constructor(config: StorageConfig) {
    super(config)
    
    if (!config.s3) {
      throw new Error('S3 storage configuration is required')
    }

    this.bucket = config.s3.bucket
    this.region = config.s3.region
    this.publicUrl = config.s3.publicUrl
    this.cdnUrl = config.s3.cdnUrl

    // Initialize S3 client
    this.s3Client = new S3Client({
      region: this.region,
      credentials: {
        accessKeyId: config.s3.accessKeyId,
        secretAccessKey: config.s3.secretAccessKey
      },
      endpoint: config.s3.endpoint,
      forcePathStyle: !!config.s3.endpoint // Required for custom endpoints
    })

    console.log('☁️ S3 Storage Adapter initialized:', {
      bucket: this.bucket,
      region: this.region,
      hasPublicUrl: !!this.publicUrl,
      hasCdnUrl: !!this.cdnUrl,
      hasCustomEndpoint: !!config.s3.endpoint
    })
  }

  /**
   * Upload file to S3
   */
  async uploadFile(
    buffer: Buffer,
    originalName: string,
    mimeType: string,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    console.log('☁️ S3 upload started:', {
      originalName,
      mimeType,
      size: buffer.length,
      folder: options.folder
    })

    // Validate file
    const validation = this.validateFile(buffer, mimeType, options)
    if (!validation.valid) {
      throw new Error(validation.message)
    }

    // Generate unique filename
    const filename = options.filename || this.generateFilename(originalName, options.folder)
    const key = filename

    try {
      // Prepare upload parameters
      const uploadParams = {
        Bucket: this.bucket,
        Key: key,
        Body: buffer,
        ContentType: mimeType,
        Metadata: options.metadata || {},
        // Make object publicly readable
        ACL: 'public-read' as const
      }

      // Upload to S3
      const command = new PutObjectCommand(uploadParams)
      await this.s3Client.send(command)

      // Generate result
      const fileId = uuidv4()
      const url = this.getPublicUrl(key)
      const cdnUrl = this.getCdnUrl(key)

      const result: UploadResult = {
        id: fileId,
        filename: key.split('/').pop() || key,
        originalName,
        mimeType,
        size: buffer.length,
        url,
        cdnUrl,
        path: key,
        metadata: {
          format: this.getExtensionFromMimeType(mimeType)
        }
      }

      // Process image sizes if requested
      if (mimeType.startsWith('image/') && options.generateSizes) {
        result.sizes = await this.generateImageSizes(buffer, key, mimeType, options.generateSizes)
      }

      console.log('✅ S3 upload completed:', {
        key,
        url: result.url,
        cdnUrl: result.cdnUrl,
        size: result.size,
        sizes: result.sizes ? Object.keys(result.sizes) : []
      })

      return result

    } catch (error) {
      console.error('❌ S3 upload failed:', error)
      throw new Error(`Failed to upload file to S3: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Delete file from S3
   */
  async deleteFile(filePath: string): Promise<DeleteResult> {
    console.log('🗑️ Deleting file from S3:', filePath)

    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: filePath
      })

      await this.s3Client.send(command)

      console.log('✅ File deleted successfully from S3:', filePath)
      return {
        success: true,
        message: 'File deleted successfully'
      }

    } catch (error) {
      console.error('❌ Failed to delete file from S3:', error)
      return {
        success: false,
        message: `Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Get file information from S3
   */
  async getFileInfo(filePath: string): Promise<FileInfo> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucket,
        Key: filePath
      })

      const response = await this.s3Client.send(command)

      return {
        exists: true,
        size: response.ContentLength,
        lastModified: response.LastModified,
        contentType: response.ContentType
      }

    } catch (error: any) {
      if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
        return {
          exists: false
        }
      }

      throw error
    }
  }

  /**
   * Get public URL for S3 object
   */
  getPublicUrl(filePath: string): string {
    if (this.publicUrl) {
      return `${this.publicUrl}/${filePath}`
    }

    // Default S3 public URL format
    return `https://${this.bucket}.s3.${this.region}.amazonaws.com/${filePath}`
  }

  /**
   * Get CDN URL for S3 object
   */
  getCdnUrl(filePath: string): string | undefined {
    if (this.cdnUrl) {
      return `${this.cdnUrl}/${filePath}`
    }
    return undefined
  }

  /**
   * Health check for S3 storage
   */
  async healthCheck(): Promise<{ healthy: boolean; message?: string }> {
    try {
      // Try to list objects in the bucket (with limit 1)
      const { ListObjectsV2Command } = await import('@aws-sdk/client-s3')
      const command = new ListObjectsV2Command({
        Bucket: this.bucket,
        MaxKeys: 1
      })

      await this.s3Client.send(command)

      return {
        healthy: true,
        message: 'S3 storage is healthy'
      }

    } catch (error) {
      return {
        healthy: false,
        message: `S3 storage health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Generate image sizes using Sharp and upload to S3
   */
  private async generateImageSizes(
    buffer: Buffer,
    key: string,
    mimeType: string,
    sizes: any[]
  ): Promise<Record<string, UploadResult>> {
    console.log('🔄 Generating image sizes for S3:', sizes.length)

    try {
      const { ImageProcessingService } = await import('../../services/image-processing-service')
      const processedSizes = await ImageProcessingService.generateImageSizes(buffer, sizes)

      const results: Record<string, UploadResult> = {}

      // Upload each processed size to S3
      for (const [sizeName, processedImage] of Object.entries(processedSizes)) {
        const sizeKey = this.generateSizeKey(key, sizeName, processedImage.format)

        // Upload to S3
        const uploadParams = {
          Bucket: this.bucket,
          Key: sizeKey,
          Body: processedImage.buffer,
          ContentType: `image/${processedImage.format}`,
          ACL: 'public-read' as const
        }

        const command = new PutObjectCommand(uploadParams)
        await this.s3Client.send(command)

        // Create result
        results[sizeName] = {
          id: `${sizeName}-${Date.now()}`,
          filename: sizeKey.split('/').pop() || sizeKey,
          originalName: `${sizeName}_${key}`,
          mimeType: `image/${processedImage.format}`,
          size: processedImage.size,
          url: this.getPublicUrl(sizeKey),
          cdnUrl: this.getCdnUrl(sizeKey),
          path: sizeKey,
          metadata: {
            width: processedImage.width,
            height: processedImage.height,
            format: processedImage.format
          }
        }
      }

      console.log('✅ Image sizes generated for S3:', Object.keys(results))
      return results

    } catch (error) {
      console.error('❌ Failed to generate image sizes for S3:', error)
      return {}
    }
  }

  /**
   * Generate S3 key for image size
   */
  private generateSizeKey(originalKey: string, sizeName: string, format: string): string {
    const ext = originalKey.split('.').pop()
    const baseName = originalKey.replace(`.${ext}`, '')
    return `${baseName}_${sizeName}.${format}`
  }
}
