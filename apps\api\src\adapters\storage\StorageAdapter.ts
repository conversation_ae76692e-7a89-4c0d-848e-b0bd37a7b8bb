/**
 * Storage Adapter Interface for Platform File Upload System
 * Provides a unified interface for different storage providers (Local, S3, etc.)
 */

export interface StorageConfig {
  provider: 'local' | 's3' | 'cloudflare-r2'
  local?: LocalStorageConfig
  s3?: S3StorageConfig
  cloudflareR2?: CloudflareR2Config
}

export interface LocalStorageConfig {
  uploadDir: string
  baseUrl: string
  publicPath: string
}

export interface S3StorageConfig {
  bucket: string
  region: string
  accessKeyId: string
  secretAccessKey: string
  endpoint?: string
  publicUrl?: string
  cdnUrl?: string
}

export interface CloudflareR2Config {
  bucket: string
  accountId: string
  accessKeyId: string
  secretAccessKey: string
  publicUrl?: string
  cdnUrl?: string
}

export interface UploadOptions {
  folder?: string
  filename?: string
  contentType?: string
  metadata?: Record<string, string>
  generateSizes?: ImageSize[]
  mediaType?: string
}

export interface ImageSize {
  name: string
  width: number
  height: number
  quality?: number
  format?: 'webp' | 'jpeg' | 'png'
}

export interface UploadResult {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  cdnUrl?: string
  path: string
  metadata: {
    width?: number
    height?: number
    format?: string
    [key: string]: any
  }
  sizes?: Record<string, UploadResult>
}

export interface DeleteResult {
  success: boolean
  message?: string
}

export interface FileInfo {
  exists: boolean
  size?: number
  lastModified?: Date
  contentType?: string
}

/**
 * Abstract Storage Adapter Interface
 * All storage providers must implement this interface
 */
export abstract class StorageAdapter {
  protected config: StorageConfig

  constructor(config: StorageConfig) {
    this.config = config
  }

  /**
   * Upload a file to storage
   */
  abstract uploadFile(
    buffer: Buffer,
    originalName: string,
    mimeType: string,
    options?: UploadOptions
  ): Promise<UploadResult>

  /**
   * Delete a file from storage
   */
  abstract deleteFile(filePath: string): Promise<DeleteResult>

  /**
   * Get file information
   */
  abstract getFileInfo(filePath: string): Promise<FileInfo>

  /**
   * Get public URL for a file
   */
  abstract getPublicUrl(filePath: string): string

  /**
   * Get CDN URL for a file (if available)
   */
  abstract getCdnUrl?(filePath: string): string | undefined

  /**
   * Check if storage is healthy and accessible
   */
  abstract healthCheck(): Promise<{ healthy: boolean; message?: string }>

  /**
   * Generate a unique filename
   */
  protected generateFilename(originalName: string, folder?: string): string {
    const timestamp = Date.now()
    const randomId = Math.random().toString(36).substring(2, 15)
    const ext = originalName.split('.').pop()
    const baseName = originalName.split('.').slice(0, -1).join('.')
    const cleanBaseName = baseName.replace(/[^a-zA-Z0-9-_]/g, '-')
    
    const filename = `${cleanBaseName}-${timestamp}-${randomId}.${ext}`
    return folder ? `${folder}/${filename}` : filename
  }

  /**
   * Validate file before upload
   */
  protected validateFile(
    buffer: Buffer,
    mimeType: string,
    options?: UploadOptions
  ): { valid: boolean; message?: string } {
    // Check file size (default 10MB limit)
    const maxSize = 10 * 1024 * 1024
    if (buffer.length > maxSize) {
      return {
        valid: false,
        message: `File size exceeds maximum limit of ${maxSize / 1024 / 1024}MB`
      }
    }

    // Basic MIME type validation
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'application/pdf',
      'text/plain'
    ]

    if (!allowedTypes.includes(mimeType)) {
      return {
        valid: false,
        message: `File type ${mimeType} is not allowed`
      }
    }

    return { valid: true }
  }

  /**
   * Get file extension from MIME type
   */
  protected getExtensionFromMimeType(mimeType: string): string {
    const mimeToExt: Record<string, string> = {
      'image/jpeg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp',
      'image/svg+xml': 'svg',
      'application/pdf': 'pdf',
      'text/plain': 'txt'
    }

    return mimeToExt[mimeType] || 'bin'
  }
}

/**
 * Storage Factory to create appropriate storage adapter
 */
export class StorageFactory {
  static create(config: StorageConfig): StorageAdapter {
    switch (config.provider) {
      case 'local':
        const { LocalStorageAdapter } = require('./LocalStorageAdapter')
        return new LocalStorageAdapter(config)
      
      case 's3':
        const { S3StorageAdapter } = require('./S3StorageAdapter')
        return new S3StorageAdapter(config)
      
      // case 'cloudflare-r2':
      //   const { CloudflareR2Adapter } = require('./CloudflareR2Adapter')
      //   return new CloudflareR2Adapter(config)
      
      default:
        throw new Error(`Unsupported storage provider: ${config.provider}`)
    }
  }
}
