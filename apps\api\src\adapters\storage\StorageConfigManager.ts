import path from 'path'
import { StorageConfig, LocalStorageConfig, S3StorageConfig } from './StorageAdapter'

/**
 * Storage Configuration Manager
 * Manages storage configuration from platform settings and environment variables
 */
export class StorageConfigManager {
  private static instance: StorageConfigManager
  private cachedConfig: StorageConfig | null = null
  private cacheExpiry: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  private constructor() {}

  static getInstance(): StorageConfigManager {
    if (!StorageConfigManager.instance) {
      StorageConfigManager.instance = new StorageConfigManager()
    }
    return StorageConfigManager.instance
  }

  /**
   * Get storage configuration from platform settings
   */
  async getStorageConfig(payload: any): Promise<StorageConfig> {
    // Return cached config if still valid
    if (this.cachedConfig && Date.now() < this.cacheExpiry) {
      console.log('📋 Using cached storage configuration')
      return this.cachedConfig
    }

    console.log('🔍 Fetching storage configuration from platform settings...')

    try {
      // Get storage provider setting
      const providerOption = await payload.find({
        collection: 'options',
        where: {
          key: { equals: 'storage_provider' }
        },
        limit: 1
      })

      const provider = providerOption.docs[0]?.value || 'local'
      console.log(`📋 Storage provider: ${provider}`)

      let config: StorageConfig

      switch (provider) {
        case 'local':
          config = await this.getLocalConfig(payload)
          break
        
        case 's3':
          config = await this.getS3Config(payload)
          break
        
        default:
          console.warn(`⚠️ Unknown storage provider: ${provider}, falling back to local`)
          config = await this.getLocalConfig(payload)
      }

      // Cache the configuration
      this.cachedConfig = config
      this.cacheExpiry = Date.now() + this.CACHE_DURATION

      console.log('✅ Storage configuration loaded successfully:', {
        provider: config.provider,
        cached: true
      })

      return config

    } catch (error) {
      console.error('❌ Failed to get storage configuration:', error)
      
      // Fallback to local storage with default settings
      console.log('🔄 Falling back to default local storage configuration')
      const fallbackConfig = this.getDefaultLocalConfig()
      
      // Cache the fallback config for a shorter duration
      this.cachedConfig = fallbackConfig
      this.cacheExpiry = Date.now() + (1 * 60 * 1000) // 1 minute for fallback
      
      return fallbackConfig
    }
  }

  /**
   * Get local storage configuration
   */
  private async getLocalConfig(payload: any): Promise<StorageConfig> {
    console.log('📁 Loading local storage configuration...')

    try {
      // Get local storage settings
      const localOptions = await payload.find({
        collection: 'options',
        where: {
          key: {
            in: ['local_upload_dir', 'local_base_url', 'local_public_path']
          }
        }
      })

      const settings: Record<string, string> = {}
      localOptions.docs.forEach((option: any) => {
        settings[option.key] = option.value
      })

      const localConfig: LocalStorageConfig = {
        uploadDir: settings.local_upload_dir || path.resolve(process.cwd(), 'media'),
        baseUrl: settings.local_base_url || process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3001',
        publicPath: settings.local_public_path || '/media'
      }

      console.log('📁 Local storage configuration:', {
        uploadDir: localConfig.uploadDir,
        baseUrl: localConfig.baseUrl,
        publicPath: localConfig.publicPath
      })

      return {
        provider: 'local',
        local: localConfig
      }

    } catch (error) {
      console.error('❌ Failed to load local storage configuration:', error)
      return this.getDefaultLocalConfig()
    }
  }

  /**
   * Get S3 storage configuration
   */
  private async getS3Config(payload: any): Promise<StorageConfig> {
    console.log('☁️ Loading S3 storage configuration...')

    try {
      // Get S3 settings from platform options
      const s3Options = await payload.find({
        collection: 'options',
        where: {
          key: {
            in: [
              's3_bucket',
              's3_region',
              's3_access_key_id',
              's3_secret_access_key',
              's3_endpoint',
              's3_public_url',
              's3_cdn_url'
            ]
          }
        }
      })

      const settings: Record<string, string> = {}
      s3Options.docs.forEach((option: any) => {
        settings[option.key] = option.value
      })

      // Validate required S3 settings
      const requiredSettings = ['s3_bucket', 's3_region', 's3_access_key_id', 's3_secret_access_key']
      const missingSettings = requiredSettings.filter(key => !settings[key])

      if (missingSettings.length > 0) {
        throw new Error(`Missing required S3 settings: ${missingSettings.join(', ')}`)
      }

      const s3Config: S3StorageConfig = {
        bucket: settings.s3_bucket,
        region: settings.s3_region,
        accessKeyId: settings.s3_access_key_id,
        secretAccessKey: settings.s3_secret_access_key,
        endpoint: settings.s3_endpoint,
        publicUrl: settings.s3_public_url,
        cdnUrl: settings.s3_cdn_url
      }

      console.log('☁️ S3 storage configuration loaded:', {
        bucket: s3Config.bucket,
        region: s3Config.region,
        hasAccessKey: !!s3Config.accessKeyId,
        hasSecretKey: !!s3Config.secretAccessKey,
        hasEndpoint: !!s3Config.endpoint,
        hasPublicUrl: !!s3Config.publicUrl,
        hasCdnUrl: !!s3Config.cdnUrl
      })

      return {
        provider: 's3',
        s3: s3Config
      }

    } catch (error) {
      console.error('❌ Failed to load S3 storage configuration:', error)
      throw error
    }
  }

  /**
   * Get default local storage configuration
   */
  private getDefaultLocalConfig(): StorageConfig {
    return {
      provider: 'local',
      local: {
        uploadDir: path.resolve(process.cwd(), 'media'),
        baseUrl: process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3001',
        publicPath: '/media'
      }
    }
  }

  /**
   * Clear cached configuration (useful for testing or when settings change)
   */
  clearCache(): void {
    console.log('🗑️ Clearing storage configuration cache')
    this.cachedConfig = null
    this.cacheExpiry = 0
  }

  /**
   * Get current cached configuration (if any)
   */
  getCachedConfig(): StorageConfig | null {
    if (this.cachedConfig && Date.now() < this.cacheExpiry) {
      return this.cachedConfig
    }
    return null
  }

  /**
   * Validate storage configuration
   */
  async validateConfig(config: StorageConfig): Promise<{ valid: boolean; message?: string }> {
    try {
      switch (config.provider) {
        case 'local':
          if (!config.local) {
            return { valid: false, message: 'Local storage configuration is missing' }
          }
          if (!config.local.uploadDir || !config.local.baseUrl) {
            return { valid: false, message: 'Local storage requires uploadDir and baseUrl' }
          }
          break

        case 's3':
          if (!config.s3) {
            return { valid: false, message: 'S3 storage configuration is missing' }
          }
          if (!config.s3.bucket || !config.s3.region || !config.s3.accessKeyId || !config.s3.secretAccessKey) {
            return { valid: false, message: 'S3 storage requires bucket, region, accessKeyId, and secretAccessKey' }
          }
          break

        default:
          return { valid: false, message: `Unsupported storage provider: ${config.provider}` }
      }

      return { valid: true }

    } catch (error) {
      return {
        valid: false,
        message: `Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }
}
