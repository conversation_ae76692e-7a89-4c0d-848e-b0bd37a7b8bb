/**
 * Storage Adapters Export Module
 * Provides unified access to all storage adapters and utilities
 */

// Core interfaces and base classes
export {
  StorageAdapter,
  StorageFactory,
  type StorageConfig,
  type LocalStorageConfig,
  type S3StorageConfig,
  type CloudflareR2Config,
  type UploadOptions,
  type UploadResult,
  type DeleteResult,
  type FileInfo,
  type ImageSize
} from './StorageAdapter'

// Storage adapter implementations
export { LocalStorageAdapter } from './LocalStorageAdapter'
export { S3StorageAdapter } from './S3StorageAdapter'

// Configuration manager
export { StorageConfigManager } from './StorageConfigManager'

// Utility functions
export * from './utils'
