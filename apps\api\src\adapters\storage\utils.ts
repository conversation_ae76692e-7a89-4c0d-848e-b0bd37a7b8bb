/**
 * Storage Utility Functions
 * Common utilities for file handling and validation
 */

import { ImageSize } from './StorageAdapter'

/**
 * Validate file type against allowed MIME types
 */
export function validateFileType(mimeType: string, allowedTypes: string[]): boolean {
  return allowedTypes.some(type => {
    if (type.endsWith('/*')) {
      return mimeType.startsWith(type.slice(0, -1))
    }
    return mimeType === type
  })
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  const lastDot = filename.lastIndexOf('.')
  return lastDot !== -1 ? filename.substring(lastDot + 1).toLowerCase() : ''
}

/**
 * Get MIME type from file extension
 */
export function getMimeTypeFromExtension(extension: string): string {
  const ext = extension.toLowerCase().replace('.', '')
  
  const mimeTypes: Record<string, string> = {
    // Images
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    'bmp': 'image/bmp',
    'ico': 'image/x-icon',
    
    // Documents
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    
    // Text
    'txt': 'text/plain',
    'csv': 'text/csv',
    'json': 'application/json',
    'xml': 'application/xml',
    
    // Archives
    'zip': 'application/zip',
    'rar': 'application/x-rar-compressed',
    '7z': 'application/x-7z-compressed',
    
    // Audio
    'mp3': 'audio/mpeg',
    'wav': 'audio/wav',
    'ogg': 'audio/ogg',
    
    // Video
    'mp4': 'video/mp4',
    'avi': 'video/x-msvideo',
    'mov': 'video/quicktime',
    'wmv': 'video/x-ms-wmv'
  }

  return mimeTypes[ext] || 'application/octet-stream'
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Generate a safe filename by removing/replacing invalid characters
 */
export function sanitizeFilename(filename: string): string {
  // Remove or replace invalid characters
  return filename
    .replace(/[<>:"/\\|?*]/g, '-') // Replace invalid characters with dash
    .replace(/\s+/g, '-') // Replace spaces with dash
    .replace(/-+/g, '-') // Replace multiple dashes with single dash
    .replace(/^-|-$/g, '') // Remove leading/trailing dashes
    .toLowerCase()
}

/**
 * Check if file is an image based on MIME type
 */
export function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/')
}

/**
 * Check if file is a document based on MIME type
 */
export function isDocumentFile(mimeType: string): boolean {
  const documentTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv'
  ]
  
  return documentTypes.includes(mimeType)
}

/**
 * Get default image sizes for different media types
 */
export function getDefaultImageSizes(mediaType?: string): ImageSize[] {
  switch (mediaType) {
    case 'logo':
    case 'platform_logo':
      return [
        { name: 'thumbnail', width: 100, height: 100, quality: 90 },
        { name: 'small', width: 200, height: 200, quality: 90 },
        { name: 'medium', width: 400, height: 400, quality: 85 },
        { name: 'large', width: 800, height: 800, quality: 85 }
      ]
    
    case 'favicon':
      return [
        { name: '16x16', width: 16, height: 16, quality: 100, format: 'png' },
        { name: '32x32', width: 32, height: 32, quality: 100, format: 'png' },
        { name: '48x48', width: 48, height: 48, quality: 100, format: 'png' },
        { name: '64x64', width: 64, height: 64, quality: 100, format: 'png' },
        { name: '128x128', width: 128, height: 128, quality: 100, format: 'png' },
        { name: '256x256', width: 256, height: 256, quality: 100, format: 'png' }
      ]
    
    case 'avatar':
    case 'user_avatar':
      return [
        { name: 'thumbnail', width: 50, height: 50, quality: 90 },
        { name: 'small', width: 100, height: 100, quality: 90 },
        { name: 'medium', width: 200, height: 200, quality: 85 },
        { name: 'large', width: 400, height: 400, quality: 85 }
      ]
    
    case 'course_thumbnail':
      return [
        { name: 'thumbnail', width: 150, height: 100, quality: 90 },
        { name: 'small', width: 300, height: 200, quality: 90 },
        { name: 'medium', width: 600, height: 400, quality: 85 },
        { name: 'large', width: 1200, height: 800, quality: 80 }
      ]
    
    default:
      return [
        { name: 'thumbnail', width: 150, height: 150, quality: 90 },
        { name: 'small', width: 300, height: 300, quality: 90 },
        { name: 'medium', width: 600, height: 600, quality: 85 },
        { name: 'large', width: 1200, height: 1200, quality: 80 }
      ]
  }
}

/**
 * Validate image dimensions
 */
export function validateImageDimensions(
  width: number,
  height: number,
  requirements?: {
    minWidth?: number
    maxWidth?: number
    minHeight?: number
    maxHeight?: number
    aspectRatio?: number
  }
): { valid: boolean; message?: string } {
  if (!requirements) {
    return { valid: true }
  }

  if (requirements.minWidth && width < requirements.minWidth) {
    return { valid: false, message: `Image width must be at least ${requirements.minWidth}px` }
  }

  if (requirements.maxWidth && width > requirements.maxWidth) {
    return { valid: false, message: `Image width must not exceed ${requirements.maxWidth}px` }
  }

  if (requirements.minHeight && height < requirements.minHeight) {
    return { valid: false, message: `Image height must be at least ${requirements.minHeight}px` }
  }

  if (requirements.maxHeight && height > requirements.maxHeight) {
    return { valid: false, message: `Image height must not exceed ${requirements.maxHeight}px` }
  }

  if (requirements.aspectRatio) {
    const actualRatio = width / height
    const tolerance = 0.1 // 10% tolerance
    if (Math.abs(actualRatio - requirements.aspectRatio) > tolerance) {
      return { valid: false, message: `Image aspect ratio must be approximately ${requirements.aspectRatio}` }
    }
  }

  return { valid: true }
}

/**
 * Generate unique filename with timestamp and random string
 */
export function generateUniqueFilename(originalName: string, prefix?: string): string {
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 15)
  const extension = getFileExtension(originalName)
  const baseName = originalName.replace(/\.[^/.]+$/, '') // Remove extension
  const sanitizedBaseName = sanitizeFilename(baseName)
  
  const parts = [
    prefix,
    sanitizedBaseName,
    timestamp.toString(),
    randomString
  ].filter(Boolean)
  
  return `${parts.join('-')}.${extension}`
}
