import { NextRequest, NextResponse } from 'next/server'
import path from 'path'
import fs from 'fs/promises'
import { lookup } from 'mime-types'

console.log('🔥 Media route handler loaded - /media/[...path]/route.ts')

// GET handler for serving media files
export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  console.log('📁📁📁 MEDIA ROUTE HANDLER CALLED! 📁📁📁')
  
  try {
    // Extract the file path from the URL parameters
    const filePath = params.path.join('/')
    
    console.log('📋 Media file request:', {
      url: request.url,
      filePath: filePath,
      params: params
    })

    if (!filePath) {
      console.log('❌ No file path provided')
      return new NextResponse('File path required', { status: 400 })
    }

    // Construct the full file path
    const mediaDir = path.resolve(process.cwd(), 'media')
    const fullPath = path.join(mediaDir, filePath)
    
    console.log('📁 File paths:', {
      mediaDir,
      filePath,
      fullPath
    })

    // Security check: ensure the path is within the media directory
    const normalizedPath = path.normalize(fullPath)
    const normalizedMediaDir = path.normalize(mediaDir)
    
    if (!normalizedPath.startsWith(normalizedMediaDir)) {
      console.log('❌ Security violation: path outside media directory')
      return new NextResponse('Access denied', { status: 403 })
    }

    // Check if file exists and get stats
    let stats
    try {
      stats = await fs.stat(fullPath)

      if (!stats.isFile()) {
        console.log('❌ Path is not a file:', fullPath)
        return new NextResponse('Not a file', { status: 404 })
      }

      console.log('✅ File found:', {
        path: fullPath,
        size: stats.size,
        modified: stats.mtime
      })
    } catch (error) {
      console.log('❌ File not found:', fullPath)
      return new NextResponse('File not found', { status: 404 })
    }

    // Read the file
    const fileBuffer = await fs.readFile(fullPath)

    // Determine MIME type
    const mimeType = lookup(fullPath) || 'application/octet-stream'

    console.log('📦 Serving file:', {
      path: fullPath,
      size: fileBuffer.length,
      mimeType
    })

    // Create response with proper headers
    const response = new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': mimeType,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'public, max-age=86400', // 1 day cache
        'ETag': `"${stats.mtime.getTime()}-${stats.size}"`,
        'Last-Modified': stats.mtime.toUTCString(),
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      }
    })

    console.log('✅ File served successfully:', {
      path: filePath,
      size: fileBuffer.length,
      mimeType
    })

    return response

  } catch (error) {
    console.error('💥 Media route error:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    
    return new NextResponse(`Media route error: ${errorMessage}`, { 
      status: 500,
      headers: {
        'Content-Type': 'text/plain'
      }
    })
  }
}

// HEAD handler for checking if file exists
export async function HEAD(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  console.log('📁 HEAD request for media file')
  
  try {
    const filePath = params.path.join('/')
    const mediaDir = path.resolve(process.cwd(), 'media')
    const fullPath = path.join(mediaDir, filePath)
    
    // Security check
    const normalizedPath = path.normalize(fullPath)
    const normalizedMediaDir = path.normalize(mediaDir)
    
    if (!normalizedPath.startsWith(normalizedMediaDir)) {
      return new NextResponse(null, { status: 403 })
    }

    // Check if file exists
    const stats = await fs.stat(fullPath)
    
    if (!stats.isFile()) {
      return new NextResponse(null, { status: 404 })
    }

    const mimeType = lookup(fullPath) || 'application/octet-stream'

    return new NextResponse(null, {
      status: 200,
      headers: {
        'Content-Type': mimeType,
        'Content-Length': stats.size.toString(),
        'Cache-Control': 'public, max-age=86400',
        'ETag': `"${stats.mtime.getTime()}-${stats.size}"`,
        'Last-Modified': stats.mtime.toUTCString(),
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      }
    })

  } catch (error) {
    return new NextResponse(null, { status: 404 })
  }
}

// OPTIONS handler for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    }
  })
}
