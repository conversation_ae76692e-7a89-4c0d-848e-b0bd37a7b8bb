import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { lookup } from 'mime-types'

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    console.log('📁 Media file request:', params.path)

    // Join the path segments
    const filePath = params.path.join('/')
    
    // Construct the full path to the media file
    const mediaDir = path.resolve(process.cwd(), 'media')
    const fullPath = path.join(mediaDir, filePath)
    
    console.log('🔍 Looking for file:', {
      requestedPath: filePath,
      fullPath: fullPath,
      mediaDir: mediaDir
    })

    // Security check: ensure the path is within the media directory
    if (!fullPath.startsWith(mediaDir)) {
      console.log('❌ Security violation: Path outside media directory')
      return new NextResponse('Access Denied', { status: 403 })
    }

    // Check if file exists and get stats
    let stats
    try {
      stats = await fs.stat(fullPath)
      
      if (!stats.isFile()) {
        console.log('❌ Path is not a file:', fullPath)
        return new NextResponse('Not a file', { status: 404 })
      }
      
      console.log('✅ File found:', {
        path: fullPath,
        size: stats.size,
        modified: stats.mtime
      })
    } catch (error) {
      console.log('❌ File not found:', fullPath)
      return new NextResponse('File not found', { status: 404 })
    }

    // Read the file
    const fileBuffer = await fs.readFile(fullPath)
    
    // Determine MIME type
    const mimeType = lookup(fullPath) || 'application/octet-stream'
    
    console.log('📦 Serving file:', {
      path: fullPath,
      size: fileBuffer.length,
      mimeType
    })

    // Create response with proper headers
    const response = new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': mimeType,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'public, max-age=86400', // 1 day cache
        'ETag': `"${stats.mtime.getTime()}-${stats.size}"`,
        'Last-Modified': stats.mtime.toUTCString(),
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      }
    })

    return response

  } catch (error) {
    console.error('❌ Media file route error:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

export async function HEAD(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Join the path segments
    const filePath = params.path.join('/')
    
    // Construct the full path to the media file
    const mediaDir = path.resolve(process.cwd(), 'media')
    const fullPath = path.join(mediaDir, filePath)
    
    // Security check
    if (!fullPath.startsWith(mediaDir)) {
      return new NextResponse('Access Denied', { status: 403 })
    }

    // Check if file exists
    try {
      const stats = await fs.stat(fullPath)
      
      if (!stats.isFile()) {
        return new NextResponse('Not a file', { status: 404 })
      }
      
      // Determine MIME type
      const mimeType = lookup(fullPath) || 'application/octet-stream'
      
      // Return headers only
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Content-Type': mimeType,
          'Content-Length': stats.size.toString(),
          'Cache-Control': 'public, max-age=86400',
          'ETag': `"${stats.mtime.getTime()}-${stats.size}"`,
          'Last-Modified': stats.mtime.toUTCString(),
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        }
      })
    } catch (error) {
      return new NextResponse('File not found', { status: 404 })
    }
  } catch (error) {
    console.error('❌ Media file HEAD error:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    }
  })
}
