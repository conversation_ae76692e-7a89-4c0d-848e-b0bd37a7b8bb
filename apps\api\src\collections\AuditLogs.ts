import { CollectionConfig } from 'payload/types'

const AuditLogs: CollectionConfig = {
  slug: 'audit_logs',
  admin: {
    useAsTitle: 'action',
    defaultColumns: ['action', 'userId', 'targetType', 'targetId', 'createdAt'],
    group: 'System',
  },
  access: {
    read: ({ req: { user } }) => {
      // Only super admins and institute admins can read audit logs
      if (user?.legacyRole === 'super_admin') return true
      if (user?.legacyRole === 'institute_admin') {
        // Institute admins can only see logs for their institute
        return {
          'metadata.institute': {
            equals: user.institute,
          },
        }
      }
      return false
    },
    create: () => true, // System can create audit logs
    update: () => false, // Audit logs should not be updated
    delete: ({ req: { user } }) => {
      // Only super admins can delete audit logs
      return user?.legacyRole === 'super_admin'
    },
  },
  fields: [
    {
      name: 'userId',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        description: 'User who performed the action',
      },
    },
    {
      name: 'action',
      type: 'select',
      required: true,
      options: [
        { label: 'Student Created', value: 'STUDENT_CREATED' },
        { label: 'Student Updated', value: 'STUDENT_UPDATED' },
        { label: 'Student Activated', value: 'STUDENT_ACTIVATED' },
        { label: 'Student Deactivated', value: 'STUDENT_DEACTIVATED' },
        { label: 'Student Deleted', value: 'STUDENT_DELETED' },
        { label: 'Student Status Changed', value: 'STUDENT_STATUS_CHANGED' },
        { label: 'Bulk Student Update', value: 'BULK_STUDENT_UPDATE' },
        { label: 'Migration Completed', value: 'MIGRATION_COMPLETED' },
        { label: 'System Action', value: 'SYSTEM_ACTION' },
      ],
      admin: {
        description: 'Type of action performed',
      },
    },
    {
      name: 'targetType',
      type: 'select',
      required: true,
      options: [
        { label: 'Student', value: 'student' },
        { label: 'User', value: 'user' },
        { label: 'Institute', value: 'institute' },
        { label: 'Branch', value: 'branch' },
        { label: 'Role', value: 'role' },
        { label: 'System', value: 'system' },
      ],
      admin: {
        description: 'Type of target entity',
      },
    },
    {
      name: 'targetId',
      type: 'text',
      required: true,
      admin: {
        description: 'ID of the target entity',
      },
    },
    {
      name: 'reason',
      type: 'textarea',
      admin: {
        description: 'Reason for the action (optional)',
      },
    },
    {
      name: 'metadata',
      type: 'json',
      admin: {
        description: 'Additional metadata about the action',
      },
    },
    {
      name: 'ipAddress',
      type: 'text',
      admin: {
        description: 'IP address of the user who performed the action',
      },
    },
    {
      name: 'userAgent',
      type: 'text',
      admin: {
        description: 'User agent of the client',
      },
    },
  ],
  timestamps: true,
  hooks: {
    beforeChange: [
      ({ req, data }) => {
        // Automatically capture IP address and user agent
        if (req) {
          data.ipAddress = req.ip || req.connection?.remoteAddress
          data.userAgent = req.headers?.['user-agent']
        }
        return data
      },
    ],
  },
}

export default AuditLogs
