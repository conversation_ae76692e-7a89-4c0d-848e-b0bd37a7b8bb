import { CollectionConfig } from 'payload/types'
import { isAdmin, isInstituteAdmin } from '../access/index'

const Bills: CollectionConfig = {
  slug: 'bills',
  admin: {
    useAsTitle: 'billNumber',
    defaultColumns: ['billNumber', 'branch', 'totalAmount', 'status', 'dueDate'],
    group: 'Billing Management',
  },
  access: {
    read: ({ req: { user } }) => {
      if (!user) return false
      
      if (user.role === 'super_admin') return true
      
      if (user.role === 'institute_admin') {
        return { 'branch.institute': { equals: user.institute } }
      }
      
      if (user.role === 'branch_admin') {
        return { branch: { equals: user.branch } }
      }
      
      return false
    },
    create: isAdmin,
    update: ({ req: { user } }) => {
      if (!user) return false
      
      if (user.role === 'super_admin') return true
      
      if (user.role === 'institute_admin') {
        return { 'branch.institute': { equals: user.institute } }
      }
      
      return false
    },
    delete: isAdmin,
  },
  fields: [
    {
      name: 'billNumber',
      type: 'text',
      required: true,
      unique: true,
      index: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'branch',
      type: 'relationship',
      relationTo: 'branches',
      required: true,
      index: true,
    },
    {
      name: 'billingPeriod',
      type: 'group',
      fields: [
        {
          name: 'startDate',
          type: 'date',
          required: true,
        },
        {
          name: 'endDate',
          type: 'date',
          required: true,
        },
        {
          name: 'month',
          type: 'number',
          required: true,
          min: 1,
          max: 12,
        },
        {
          name: 'year',
          type: 'number',
          required: true,
        },
      ],
    },
    {
      name: 'amounts',
      type: 'group',
      fields: [
        {
          name: 'baseFee',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            description: 'Monthly base subscription fee',
          },
        },
        {
          name: 'commissionAmount',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            description: 'Total commission from student purchases',
          },
        },
        {
          name: 'subtotal',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            readOnly: true,
            description: 'Base fee + Commission amount',
          },
        },
        {
          name: 'taxAmount',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            description: 'Tax amount based on location',
          },
        },
        {
          name: 'totalAmount',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            readOnly: true,
            description: 'Final amount including tax',
          },
        },
        {
          name: 'currency',
          type: 'select',
          required: true,
          defaultValue: 'INR',
          options: [
            { label: 'Indian Rupee (₹)', value: 'INR' },
            { label: 'US Dollar ($)', value: 'USD' },
            { label: 'Euro (€)', value: 'EUR' },
            { label: 'British Pound (£)', value: 'GBP' },
          ],
        },
      ],
    },
    {
      name: 'taxDetails',
      type: 'group',
      fields: [
        {
          name: 'taxScenario',
          type: 'select',
          options: [
            { label: 'Intra-State', value: 'intra_state' },
            { label: 'Inter-State', value: 'inter_state' },
            { label: 'International', value: 'international' },
          ],
        },
        {
          name: 'taxComponents',
          type: 'array',
          fields: [
            {
              name: 'componentName',
              type: 'text',
              required: true,
            },
            {
              name: 'componentCode',
              type: 'text',
              required: true,
            },
            {
              name: 'rate',
              type: 'number',
              required: true,
            },
            {
              name: 'amount',
              type: 'number',
              required: true,
            },
          ],
        },
      ],
    },
    {
      name: 'commissionDetails',
      type: 'array',
      fields: [
        // Temporarily commented out until CoursePurchases collection is implemented
        // {
        //   name: 'studentPurchase',
        //   type: 'relationship',
        //   relationTo: 'course-purchases',
        // },
        {
          name: 'courseTitle',
          type: 'text',
          required: true,
        },
        {
          name: 'studentName',
          type: 'text',
          required: true,
        },
        {
          name: 'purchaseAmount',
          type: 'number',
          required: true,
        },
        {
          name: 'commissionRate',
          type: 'number',
          required: true,
          admin: {
            description: 'Commission rate as percentage',
          },
        },
        {
          name: 'commissionAmount',
          type: 'number',
          required: true,
        },
        {
          name: 'purchaseDate',
          type: 'date',
          required: true,
        },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'pending',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Sent', value: 'sent' },
        { label: 'Viewed', value: 'viewed' },
        { label: 'Paid', value: 'paid' },
        { label: 'Overdue', value: 'overdue' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
      index: true,
    },
    {
      name: 'dates',
      type: 'group',
      fields: [
        {
          name: 'generatedDate',
          type: 'date',
          required: true,
          defaultValue: () => new Date(),
        },
        {
          name: 'sentDate',
          type: 'date',
        },
        {
          name: 'dueDate',
          type: 'date',
          required: true,
        },
        {
          name: 'paidDate',
          type: 'date',
        },
        {
          name: 'viewedDate',
          type: 'date',
        },
      ],
    },
    {
      name: 'paymentDetails',
      type: 'group',
      fields: [
        {
          name: 'paymentMethod',
          type: 'select',
          options: [
            { label: 'Razorpay', value: 'razorpay' },
            { label: 'Stripe', value: 'stripe' },
            { label: 'PayPal', value: 'paypal' },
            { label: 'Bank Transfer', value: 'bank_transfer' },
            { label: 'UPI', value: 'upi' },
          ],
        },
        {
          name: 'transactionId',
          type: 'text',
        },
        {
          name: 'paymentGatewayResponse',
          type: 'json',
          admin: {
            description: 'Payment gateway response data',
          },
        },
        {
          name: 'paidBy',
          type: 'relationship',
          relationTo: 'users',
        },
      ],
    },
    {
      name: 'notes',
      type: 'textarea',
      maxLength: 1000,
    },
    {
      name: 'attachments',
      type: 'array',
      fields: [
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
        },
        {
          name: 'description',
          type: 'text',
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Generate bill number
          const date = new Date()
          const year = date.getFullYear()
          const month = String(date.getMonth() + 1).padStart(2, '0')
          const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
          data.billNumber = `BILL-${year}${month}-${random}`

          // Calculate totals
          if (data.amounts) {
            data.amounts.subtotal = (data.amounts.baseFee || 0) + (data.amounts.commissionAmount || 0)
            data.amounts.totalAmount = data.amounts.subtotal + (data.amounts.taxAmount || 0)
          }

          // Set due date (30 days from generation)
          if (!data.dates?.dueDate) {
            const dueDate = new Date()
            dueDate.setDate(dueDate.getDate() + 30)
            if (!data.dates) data.dates = {}
            data.dates.dueDate = dueDate
          }
        }

        if (operation === 'update') {
          // Update status based on payment
          if (data.paymentDetails?.transactionId && data.status === 'pending') {
            data.status = 'paid'
            if (!data.dates) data.dates = {}
            data.dates.paidDate = new Date()
          }

          // Recalculate totals if amounts change
          if (data.amounts) {
            data.amounts.subtotal = (data.amounts.baseFee || 0) + (data.amounts.commissionAmount || 0)
            data.amounts.totalAmount = data.amounts.subtotal + (data.amounts.taxAmount || 0)
          }
        }

        return data
      },
    ],
  },
  timestamps: true,
}

export default Bills
