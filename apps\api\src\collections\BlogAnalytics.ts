import { CollectionConfig } from 'payload/types'

const BlogAnalytics: CollectionConfig = {
  slug: 'blog-analytics',
  admin: {
    useAsTitle: 'date',
    defaultColumns: ['post', 'date', 'views', 'uniqueViews', 'likes'],
    group: 'Blog Management',
  },
  access: {
    read: ({ req: { user } }) => {
      // Institute staff can read analytics from their institute's posts
      if (user?.institute && ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
        return {
          'post.institute': {
            equals: user.institute
          }
        }
      }
      
      return false
    },
    create: ({ req: { user } }) => {
      // Only system can create analytics records
      return user?.legacyRole === 'super_admin'
    },
    update: ({ req: { user } }) => {
      // Only system can update analytics records
      return user?.legacyRole === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      // Only super admin can delete analytics
      return user?.legacyRole === 'super_admin'
    },
  },
  fields: [
    {
      name: 'post',
      type: 'relationship',
      relationTo: 'blog-posts',
      required: true,
    },
    {
      name: 'date',
      type: 'date',
      required: true,
      admin: {
        description: 'Date for this analytics record (YYYY-MM-DD)'
      }
    },
    {
      name: 'views',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Total page views for this date'
      }
    },
    {
      name: 'uniqueViews',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Unique visitors for this date'
      }
    },
    {
      name: 'likes',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Total likes received on this date'
      }
    },
    {
      name: 'comments',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Total comments received on this date'
      }
    },
    {
      name: 'shares',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Total shares on this date'
      }
    },
    {
      name: 'trafficSources',
      type: 'group',
      fields: [
        {
          name: 'direct',
          type: 'number',
          defaultValue: 0,
          admin: {
            description: 'Direct traffic (typed URL, bookmarks)'
          }
        },
        {
          name: 'search',
          type: 'number',
          defaultValue: 0,
          admin: {
            description: 'Search engine traffic'
          }
        },
        {
          name: 'social',
          type: 'number',
          defaultValue: 0,
          admin: {
            description: 'Social media traffic'
          }
        },
        {
          name: 'referral',
          type: 'number',
          defaultValue: 0,
          admin: {
            description: 'Referral traffic from other websites'
          }
        }
      ]
    },
    {
      name: 'engagement',
      type: 'group',
      fields: [
        {
          name: 'avgTimeOnPage',
          type: 'number',
          admin: {
            description: 'Average time spent on page (in seconds)'
          }
        },
        {
          name: 'bounceRate',
          type: 'number',
          admin: {
            description: 'Bounce rate percentage (0-100)'
          }
        }
      ]
    }
  ],
  indexes: [
    {
      fields: ['post', 'date'],
      unique: true
    }
  ]
}

export default BlogAnalytics
