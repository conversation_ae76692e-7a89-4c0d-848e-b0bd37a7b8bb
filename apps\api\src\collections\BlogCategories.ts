import { CollectionConfig } from 'payload/types'

const BlogCategories: CollectionConfig = {
  slug: 'blog-categories',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'slug', 'isActive', 'createdAt'],
    group: 'Blog Management',
  },
  access: {
    read: ({ req: { user } }) => {
      // Students can read active categories from their institute
      if (user?.legacyRole === 'student') {
        return {
          and: [
            {
              institute: {
                equals: user.institute
              }
            },
            {
              isActive: {
                equals: true
              }
            }
          ]
        }
      }
      
      // Institute staff can read all categories from their institute
      if (user?.institute && ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
        return {
          institute: {
            equals: user.institute
          }
        }
      }
      
      return false
    },
    create: ({ req: { user } }) => {
      return user?.institute && ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)
    },
    update: ({ req: { user } }) => {
      if (user?.legacyRole === 'institute_admin') {
        return {
          institute: {
            equals: user.institute
          }
        }
      }
      
      // Other staff can only update categories they created
      if (user?.institute && ['branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
        return {
          and: [
            {
              institute: {
                equals: user.institute
              }
            },
            {
              createdBy: {
                equals: user.id
              }
            }
          ]
        }
      }
      
      return false
    },
    delete: ({ req: { user } }) => {
      return user?.legacyRole === 'institute_admin' && user?.institute
    },
  },
  fields: [
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      admin: {
        condition: (data, siblingData, { user }) => user?.legacyRole === 'super_admin'
      }
    },
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      admin: {
        description: 'URL-friendly version of the name'
      }
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'color',
      type: 'text',
      admin: {
        description: 'Hex color code for category display (e.g., #3B82F6)'
      },
      validate: (val) => {
        if (val && !/^#[0-9A-F]{6}$/i.test(val)) {
          return 'Color must be a valid hex code (e.g., #3B82F6)'
        }
        return true
      }
    },
    {
      name: 'icon',
      type: 'text',
      admin: {
        description: 'Icon class name (e.g., lucide icon names like "book", "graduation-cap")'
      }
    },
    {
      name: 'parentCategory',
      type: 'relationship',
      relationTo: 'blog-categories',
      admin: {
        description: 'Parent category for hierarchical organization'
      },
      filterOptions: ({ user }) => {
        // Handle invalid institute IDs
        const instituteId = user?.institute
        if (!instituteId || instituteId === 'NaN' || isNaN(parseInt(instituteId))) {
          return {
            id: {
              equals: -1 // Return no results for invalid institute
            }
          }
        }

        return {
          institute: {
            equals: parseInt(instituteId)
          }
        }
      }
    },
    {
      name: 'displayOrder',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Order for displaying categories (lower numbers first)'
      }
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'seo',
      type: 'group',
      fields: [
        {
          name: 'title',
          type: 'text',
          maxLength: 150,
          admin: {
            description: 'SEO title for category pages'
          }
        },
        {
          name: 'description',
          type: 'textarea',
          maxLength: 300,
          admin: {
            description: 'SEO description for category pages'
          }
        }
      ]
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        readOnly: true
      }
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        // Set institute and createdBy from authenticated user
        if (req.user?.legacyRole !== 'super_admin') {
          // Ensure institute ID is a number
          const instituteId = parseInt(req.user?.institute)
          if (!isNaN(instituteId)) {
            data.institute = instituteId
          }
        }

        if (operation === 'create' && !data.createdBy) {
          data.createdBy = req.user?.id
        }

        // Auto-generate slug from name if not provided or if name changed
        if (data.name && (!data.slug || data.slug.trim() === '')) {
          data.slug = data.name
            .toLowerCase()
            .trim()
            .replace(/[^a-z0-9\s]+/g, '') // Remove special characters but keep spaces
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/(^-|-$)/g, '') // Remove leading/trailing hyphens
        }

        console.log('BlogCategories hook - Final data:', {
          institute: data.institute,
          instituteType: typeof data.institute,
          name: data.name,
          slug: data.slug,
          createdBy: data.createdBy
        })

        return data
      }
    ]
  }
}

export default BlogCategories
