import { CollectionConfig } from 'payload/types'

const BlogComments: CollectionConfig = {
  slug: 'blog-comments',
  admin: {
    useAsTitle: 'content',
    defaultColumns: ['content', 'authorName', 'status', 'createdAt'],
    group: 'Blog Management',
  },
  access: {
    read: ({ req: { user } }) => {
      // Students can read approved comments from their institute's posts
      if (user?.legacyRole === 'student') {
        return {
          and: [
            {
              'post.institute': {
                equals: user.institute
              }
            },
            {
              status: {
                equals: 'approved'
              }
            }
          ]
        }
      }
      
      // Institute staff can read all comments from their institute's posts
      if (user?.institute && ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
        return {
          'post.institute': {
            equals: user.institute
          }
        }
      }
      
      return false
    },
    create: ({ req: { user } }) => {
      // Any authenticated user can create comments
      return !!user
    },
    update: ({ req: { user } }) => {
      if (user?.legacyRole === 'institute_admin') {
        return {
          'post.institute': {
            equals: user.institute
          }
        }
      }
      
      // Users can only update their own comments
      if (user) {
        return {
          author: {
            equals: user.id
          }
        }
      }
      
      return false
    },
    delete: ({ req: { user } }) => {
      if (user?.legacyRole === 'institute_admin') {
        return {
          'post.institute': {
            equals: user.institute
          }
        }
      }
      
      // Users can delete their own comments
      if (user) {
        return {
          author: {
            equals: user.id
          }
        }
      }
      
      return false
    },
  },
  fields: [
    {
      name: 'post',
      type: 'relationship',
      relationTo: 'blog-posts',
      required: true,
    },
    {
      name: 'parentComment',
      type: 'relationship',
      relationTo: 'blog-comments',
      admin: {
        description: 'For nested/reply comments'
      }
    },
    {
      name: 'content',
      type: 'textarea',
      required: true,
      maxLength: 1000,
    },
    {
      name: 'authorName',
      type: 'text',
      required: true,
      maxLength: 100,
    },
    {
      name: 'authorEmail',
      type: 'email',
      admin: {
        description: 'Email for notifications (not displayed publicly)'
      }
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: 'If logged in user'
      }
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'pending',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' },
        { label: 'Spam', value: 'spam' },
      ]
    },
    {
      name: 'moderatedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true
      }
    },
    {
      name: 'moderatedAt',
      type: 'date',
      admin: {
        readOnly: true
      }
    },
    {
      name: 'likeCount',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true
      }
    },
    {
      name: 'ipAddress',
      type: 'text',
      admin: {
        readOnly: true,
        description: 'IP address for spam prevention'
      }
    },
    {
      name: 'userAgent',
      type: 'textarea',
      admin: {
        readOnly: true,
        description: 'User agent for spam prevention'
      }
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        // Set author info from authenticated user
        if (req.user && operation === 'create') {
          data.author = req.user.id
          data.authorName = `${req.user.firstName} ${req.user.lastName}`.trim()
          data.authorEmail = req.user.email
        }
        
        // Capture IP and user agent for spam prevention
        if (operation === 'create') {
          data.ipAddress = req.ip || req.connection?.remoteAddress
          data.userAgent = req.headers?.['user-agent']
        }
        
        // Set moderation info when status changes
        if (data.status && data.status !== 'pending' && req.user) {
          data.moderatedBy = req.user.id
          data.moderatedAt = new Date()
        }
        
        return data
      }
    ]
  }
}

export default BlogComments
