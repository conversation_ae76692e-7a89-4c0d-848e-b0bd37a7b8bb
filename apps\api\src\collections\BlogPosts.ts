import { CollectionConfig } from 'payload/types'

const BlogPosts: CollectionConfig = {
  slug: 'blog-posts',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'status', 'category', 'author', 'publishedAt'],
    group: 'Blog Management',
  },
  access: {
    read: ({ req: { user } }) => {
      // Students can only read published posts from their institute
      if (user?.legacyRole === 'student') {
        return {
          and: [
            {
              institute: {
                equals: user.institute
              }
            },
            {
              status: {
                equals: 'published'
              }
            }
          ]
        }
      }
      
      // Institute staff can read all posts from their institute
      if (user?.institute && ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
        return {
          institute: {
            equals: user.institute
          }
        }
      }
      
      return false
    },
    create: ({ req: { user } }) => {
      return user?.institute && ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)
    },
    update: ({ req: { user } }) => {
      if (user?.legacyRole === 'institute_admin') {
        return {
          institute: {
            equals: user.institute
          }
        }
      }
      
      // Staff can only update their own posts
      if (user?.institute && ['branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
        return {
          and: [
            {
              institute: {
                equals: user.institute
              }
            },
            {
              author: {
                equals: user.id
              }
            }
          ]
        }
      }
      
      return false
    },
    delete: ({ req: { user } }) => {
      return user?.legacyRole === 'institute_admin' && user?.institute
    },
  },
  fields: [
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      admin: {
        condition: (data, siblingData, { user }) => user?.legacyRole === 'super_admin'
      }
    },
    {
      name: 'title',
      type: 'text',
      required: true,
      maxLength: 200,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      admin: {
        description: 'URL-friendly version of the title'
      }
    },
    {
      name: 'excerpt',
      type: 'textarea',
      maxLength: 300,
      admin: {
        description: 'Brief summary of the post (used in previews)'
      }
    },
    {
      name: 'content',
      type: 'richText',
      required: true,
      admin: {
        elements: [
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
          'blockquote', 'ul', 'ol', 'li',
          'link', 'upload', 'indent'
        ],
        leaves: [
          'bold', 'italic', 'underline', 'strikethrough', 'code'
        ]
      }
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Main image for the blog post'
      }
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Scheduled', value: 'scheduled' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ]
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        condition: (data) => data.status === 'published'
      }
    },
    {
      name: 'scheduledFor',
      type: 'date',
      admin: {
        condition: (data) => data.status === 'scheduled'
      }
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'blog-categories',
      required: false,
      filterOptions: ({ user }) => {
        try {
          // Handle invalid institute IDs
          const instituteId = user?.institute
          console.log('BlogPosts category filterOptions - user institute:', instituteId, typeof instituteId)

          if (!instituteId || instituteId === 'NaN' || isNaN(parseInt(instituteId))) {
            console.log('Invalid institute ID, allowing all categories temporarily')
            // Temporarily allow all categories instead of blocking
            return {}
          }

          const parsedInstituteId = parseInt(instituteId)
          console.log('BlogPosts category filterOptions - parsed institute ID:', parsedInstituteId)

          return {
            institute: {
              equals: parsedInstituteId
            }
          }
        } catch (error) {
          console.error('Error in BlogPosts category filterOptions:', error)
          // Allow all categories on error instead of blocking
          return {}
        }
      }
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true
        }
      ]
    },
    {
      name: 'seo',
      type: 'group',
      fields: [
        {
          name: 'title',
          type: 'text',
          maxLength: 150,
          admin: {
            description: 'SEO title (max 150 characters)'
          }
        },
        {
          name: 'description',
          type: 'textarea',
          maxLength: 300,
          admin: {
            description: 'SEO description (max 300 characters)'
          }
        },
        {
          name: 'keywords',
          type: 'array',
          fields: [
            {
              name: 'keyword',
              type: 'text'
            }
          ]
        }
      ]
    },
    {
      name: 'settings',
      type: 'group',
      fields: [
        {
          name: 'allowComments',
          type: 'checkbox',
          defaultValue: true
        },
        {
          name: 'isFeatured',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Show in featured posts section'
          }
        },
        {
          name: 'isSticky',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Pin to top of blog list'
          }
        }
      ]
    },
    {
      name: 'analytics',
      type: 'group',
      admin: {
        readOnly: true
      },
      fields: [
        {
          name: 'viewCount',
          type: 'number',
          defaultValue: 0
        },
        {
          name: 'likeCount',
          type: 'number',
          defaultValue: 0
        },
        {
          name: 'commentCount',
          type: 'number',
          defaultValue: 0
        },
        {
          name: 'readingTime',
          type: 'number',
          admin: {
            description: 'Estimated reading time in minutes'
          }
        }
      ]
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        readOnly: true
      }
    },
    {
      name: 'lastEditedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true
      }
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, req, operation }) => {
        // Set institute and author from authenticated user
        if (req.user?.legacyRole !== 'super_admin') {
          // Ensure institute ID is a number
          const instituteId = parseInt(req.user?.institute)
          if (!isNaN(instituteId)) {
            data.institute = instituteId
          }
        }

        if (operation === 'create' && !data.author) {
          data.author = req.user?.id
        }

        if (operation === 'update') {
          data.lastEditedBy = req.user?.id
        }

        console.log('BlogPosts hook - Final data:', {
          institute: data.institute,
          instituteType: typeof data.institute,
          title: data.title,
          author: data.author,
          category: data.category,
          categoryType: typeof data.category,
          operation
        })

        // Auto-generate slug from title if not provided
        if (!data.slug && data.title) {
          data.slug = data.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }

        // Calculate reading time
        if (data.content) {
          const textContent = JSON.stringify(data.content).replace(/<[^>]*>/g, '')
          const wordCount = textContent.split(/\s+/).length
          data.analytics = {
            ...data.analytics,
            readingTime: Math.ceil(wordCount / 200) // Average reading speed
          }
        }

        // Set published date when status changes to published
        if (data.status === 'published' && !data.publishedAt) {
          data.publishedAt = new Date()
        }

        return data
      }
    ]
  }
}

export default BlogPosts
