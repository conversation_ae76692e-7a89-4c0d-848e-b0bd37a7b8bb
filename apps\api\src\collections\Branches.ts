import { CollectionConfig } from 'payload/types'
import { isAdmin, isAdminOrSelf } from '../access/index'

const Branches: CollectionConfig = {
  slug: 'branches',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'institute', 'location', 'isActive', 'createdAt'],
    group: 'Institute Management',
  },
  access: {
    read: ({ req: { user } }) => {
      if (!user) return false
      
      // Super admin can read all branches
      if (user.role === 'super_admin') return true
      
      // Institute admin can read their institute's branches
      if (user.role === 'institute_admin') {
        return { institute: { equals: user.institute } }
      }
      
      return false
    },
    create: ({ req: { user } }) => {
      return user?.role === 'institute_admin' || user?.role === 'super_admin'
    },
    update: ({ req: { user } }) => {
      if (!user) return false
      
      if (user.role === 'super_admin') return true
      
      if (user.role === 'institute_admin') {
        return { institute: { equals: user.institute } }
      }
      
      return false
    },
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      maxLength: 20,
      index: true,
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      index: true,
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'address',
          type: 'textarea',
          required: true,
        },
        {
          name: 'country',
          type: 'relationship',
          relationTo: 'countries',
          required: true,
        },
        {
          name: 'state',
          type: 'relationship',
          relationTo: 'states',
          required: true,
        },
        {
          name: 'district',
          type: 'relationship',
          relationTo: 'districts',
          required: true,
        },
        {
          name: 'pincode',
          type: 'text',
          maxLength: 10,
        },
        {
          name: 'coordinates',
          type: 'group',
          fields: [
            {
              name: 'latitude',
              type: 'number',
            },
            {
              name: 'longitude',
              type: 'number',
            },
          ],
        },
      ],
    },
    {
      name: 'contact',
      type: 'group',
      fields: [
        {
          name: 'phone',
          type: 'text',
          maxLength: 20,
        },
        {
          name: 'email',
          type: 'email',
        },
        {
          name: 'website',
          type: 'text',
        },
      ],
    },
    {
      name: 'taxInformation',
      type: 'group',
      fields: [
        {
          name: 'gstNumber',
          type: 'text',
          maxLength: 15,
          validate: (val) => {
            if (val && !/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(val)) {
              return 'Invalid GST number format'
            }
            return true
          },
        },
        {
          name: 'panNumber',
          type: 'text',
          maxLength: 10,
          validate: (val) => {
            if (val && !/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(val)) {
              return 'Invalid PAN number format'
            }
            return true
          },
        },
        {
          name: 'taxRegistrationNumber',
          type: 'text',
          maxLength: 50,
        },
        {
          name: 'isGstRegistered',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
    },

    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'isHeadOffice',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Mark as head office/main branch',
      },
    },
    {
      name: 'workingDays',
      type: 'group',
      fields: [
        {
          name: 'monday',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'tuesday',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'wednesday',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'thursday',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'friday',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'saturday',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'sunday',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
      admin: {
        description: 'Select the working days for this branch',
      },
    },
    {
      name: 'operatingHours',
      type: 'group',
      fields: [
        {
          name: 'openTime',
          type: 'text',
          admin: {
            description: 'Opening time (e.g., 09:00)',
          },
        },
        {
          name: 'closeTime',
          type: 'text',
          admin: {
            description: 'Closing time (e.g., 18:00)',
          },
        },
      ],
      admin: {
        description: 'Operating hours for this branch',
      },
    },
    {
      name: 'isDeleted',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Soft delete flag - marks branch as deleted without removing from database',
      },
    },
    {
      name: 'deletedAt',
      type: 'date',
      admin: {
        description: 'Timestamp when the branch was soft deleted',
        condition: (data) => data.isDeleted,
      },
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: 'User who created this branch',
        readOnly: true,
      },
    },
    {
      name: 'updatedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: 'User who last updated this branch',
        readOnly: true,
      },
    },
    {
      name: 'deletedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: 'User who deleted this branch',
        readOnly: true,
        condition: (data) => data.isDeleted,
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Set institute from user context if not provided
          if (!data.institute && req.user?.institute) {
            data.institute = req.user.institute
          }
          
          // Generate branch code if not provided
          if (!data.code && data.name) {
            data.code = data.name.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 10)
          }
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default Branches
