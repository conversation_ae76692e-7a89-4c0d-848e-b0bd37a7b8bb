import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const Countries: CollectionConfig = {
  slug: 'countries',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'code', 'currency', 'isActive', 'createdAt'],
    group: 'Location Management',
  },
  access: {
    read: () => true, // All users can read countries
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      maxLength: 3,
      index: true,
      validate: (val) => {
        if (!/^[A-Z]{2,3}$/.test(val)) {
          return 'Country code must be 2-3 uppercase letters'
        }
        return true
      },
    },
    {
      name: 'flag',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'details',
      type: 'group',
      fields: [
        {
          name: 'capital',
          type: 'text',
          maxLength: 100,
        },
        {
          name: 'currency',
          type: 'text',
          maxLength: 50,
        },
        {
          name: 'currencyCode',
          type: 'text',
          maxLength: 3,
        },
        {
          name: 'language',
          type: 'text',
          maxLength: 100,
        },
        {
          name: 'timezone',
          type: 'text',
          maxLength: 50,
        },
        {
          name: 'population',
          type: 'number',
        },
        {
          name: 'area',
          type: 'number',
          admin: {
            description: 'Area in square kilometers',
          },
        },
      ],
    },
    {
      name: 'coordinates',
      type: 'group',
      fields: [
        {
          name: 'latitude',
          type: 'number',
        },
        {
          name: 'longitude',
          type: 'number',
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'priority',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Higher priority countries appear first',
      },
    },
    {
      name: 'metadata',
      type: 'json',
      admin: {
        description: 'Additional country metadata',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create' || operation === 'update') {
          // Ensure country code is uppercase
          if (data.code) {
            data.code = data.code.toUpperCase()
          }
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default Countries
