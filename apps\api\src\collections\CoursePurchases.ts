import { CollectionConfig } from 'payload'

const CoursePurchases: CollectionConfig = {
  slug: 'course-purchases',
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['student', 'course', 'purchaseDate'],
    group: 'Billing Management',
    hidden: true, // Hide from admin panel for now
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: [
    {
      name: 'student',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        description: 'Student who made the purchase',
      },
    },
    {
      name: 'course',
      type: 'relationship',
      relationTo: 'courses',
      required: true,
      admin: {
        description: 'Course that was purchased',
      },
    },
    {
      name: 'branch',
      type: 'relationship',
      relationTo: 'branches',
      required: true,
      admin: {
        description: 'Branch where the purchase was made',
      },
    },
    {
      name: 'purchaseDetails',
      type: 'group',
      fields: [
        {
          name: 'originalPrice',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            description: 'Original course price',
          },
        },
        {
          name: 'discountAmount',
          type: 'number',
          min: 0,
          admin: {
            description: 'Discount amount applied',
          },
        },
        {
          name: 'finalAmount',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            description: 'Final amount paid',
          },
        },
        {
          name: 'currency',
          type: 'select',
          required: true,
          options: [
            { label: 'INR', value: 'INR' },
            { label: 'USD', value: 'USD' },
            { label: 'EUR', value: 'EUR' },
          ],
          defaultValue: 'INR',
        },
      ],
    },
    {
      name: 'purchaseDate',
      type: 'date',
      required: true,
      admin: {
        description: 'Date of purchase',
      },
    },
  ],
  timestamps: true,
}

export default CoursePurchases
