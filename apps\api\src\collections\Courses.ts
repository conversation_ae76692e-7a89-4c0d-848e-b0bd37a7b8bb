import { CollectionConfig } from 'payload'

export const Courses: CollectionConfig = {
  slug: 'courses',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'instructor', 'status', 'createdAt'],
    group: 'Content',
    description: 'Manage courses in the LMS',
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => {
      // Allow admins and instructors to create courses
      return user && (user.legacyRole === 'institute_admin' || user.legacyRole === 'trainer')
    },
    update: ({ req: { user } }) => {
      // Allow admins and instructors to update courses
      return user && (user.legacyRole === 'institute_admin' || user.legacyRole === 'trainer')
    },
    delete: ({ req: { user } }) => {
      // Only admins can delete courses
      return user && user.legacyRole === 'institute_admin'
    },
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Course Title',
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Course Description',
    },
    {
      name: 'instructor',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      label: 'Instructor',
      filterOptions: ({ relationTo, siblingData }) => {
        // Filter for users with the 'instructor' role
        return {
          legacyRole: {
            equals: 'trainer',
          },
        }
      },
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
      defaultValue: 'draft',
      label: 'Status',
    },
    {
      name: 'thumbnail',
      type: 'upload',
      relationTo: 'media',
      label: 'Course Thumbnail',
    },
  ],
}

export default Courses
