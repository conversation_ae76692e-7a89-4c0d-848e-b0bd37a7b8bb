import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const Districts: CollectionConfig = {
  slug: 'districts',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'state', 'type', 'isActive', 'createdAt'],
    group: 'Location Management',
  },
  access: {
    read: () => true, // All users can read districts
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'code',
      type: 'text',
      maxLength: 10,
      index: true,
    },
    {
      name: 'state',
      type: 'relationship',
      relationTo: 'states',
      required: true,
      index: true,
    },
    {
      name: 'details',
      type: 'group',
      fields: [
        {
          name: 'type',
          type: 'select',
          required: true,
          options: [
            { label: 'District', value: 'district' },
            { label: 'City', value: 'city' },
            { label: 'Municipality', value: 'municipality' },
            { label: 'Town', value: 'town' },
            { label: 'Village', value: 'village' },
          ],
          defaultValue: 'district',
          index: true,
        },
        {
          name: 'population',
          type: 'number',
        },
        {
          name: 'area',
          type: 'number',
        },
        {
          name: 'pincode',
          type: 'text',
          maxLength: 10,
        },
      ],
    },
    {
      name: 'coordinates',
      type: 'group',
      fields: [
        {
          name: 'latitude',
          type: 'number',
        },
        {
          name: 'longitude',
          type: 'number',
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'priority',
      type: 'number',
      defaultValue: 0,
    },
    {
      name: 'metadata',
      type: 'json',
    },
  ],
  timestamps: true,
}

export default Districts
