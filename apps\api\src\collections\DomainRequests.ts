import { CollectionConfig } from 'payload/types'
import { isAdmin, isAdminOrSelf } from '../access/index'

const DomainRequests: CollectionConfig = {
  slug: 'domain-requests',
  labels: {
    singular: 'Domain Request',
    plural: 'Domain Requests',
  },
  admin: {
    useAsTitle: 'requestedDomain',
    defaultColumns: ['requestedDomain', 'institute', 'status', 'requestedAt'],
    group: 'Institute Management',
  },
  access: {
    read: ({ req: { user } }) => {
      if (!user) return false
      
      // Super admin can read all domain requests
      if (user.role === 'super_admin') return true
      
      // Institute admin can read their own requests
      if (user.role === 'institute_admin') {
        return { institute: { equals: user.institute } }
      }
      
      return false
    },
    create: ({ req: { user } }) => {
      // Only institute admins can create domain requests
      return user?.role === 'institute_admin'
    },
    update: isAdmin, // Only super admin can update status
    delete: isAdmin,
  },
  fields: [
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      index: true,
    },
    {
      name: 'requestedDomain',
      type: 'text',
      required: true,
      unique: true,
      validate: (val) => {
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/
        if (!domainRegex.test(val)) {
          return 'Invalid domain format (e.g., myinstitute.com)'
        }
        return true
      },
    },
    {
      name: 'currentDomain',
      type: 'text',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Active', value: 'active' },
        { label: 'Rejected', value: 'rejected' },
      ],
      defaultValue: 'pending',
      index: true,
    },
    {
      name: 'purpose',
      type: 'text',
      required: true,
    },
    {
      name: 'notes',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'requestedBy',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'requestedAt',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'reviewedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        condition: (data) => data.status !== 'pending',
      },
    },
    {
      name: 'reviewedAt',
      type: 'date',
      admin: {
        condition: (data) => data.status !== 'pending',
      },
    },
    {
      name: 'rejectionReason',
      type: 'textarea',
      admin: {
        condition: (data) => data.status === 'rejected',
      },
    },
    {
      name: 'sslStatus',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Active', value: 'active' },
        { label: 'Failed', value: 'failed' },
      ],
      admin: {
        condition: (data) => data.status === 'approved' || data.status === 'active',
      },
    },
    {
      name: 'dnsRecords',
      type: 'array',
      fields: [
        {
          name: 'type',
          type: 'select',
          required: true,
          options: [
            { label: 'A', value: 'A' },
            { label: 'CNAME', value: 'CNAME' },
            { label: 'TXT', value: 'TXT' },
          ],
        },
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'value',
          type: 'text',
          required: true,
        },
        {
          name: 'ttl',
          type: 'number',
          required: true,
          defaultValue: 300,
        },
      ],
      admin: {
        condition: (data) => data.status === 'approved',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Only set requestedBy if not already provided
          if (!data.requestedBy) {
            data.requestedBy = req.user?.id
          }

          // Only set requestedAt if not already provided
          if (!data.requestedAt) {
            data.requestedAt = new Date()
          }

          // Set current domain based on institute
          if (req.user?.institute) {
            // This would be populated from institute data
            data.currentDomain = `${req.user.institute}.groups-exam.com`
          }
        }
        
        if (operation === 'update' && data.status !== 'pending') {
          data.reviewedBy = req.user?.id
          data.reviewedAt = new Date()
        }
        
        return data
      },
    ],
    afterChange: [
      async ({ doc, req, operation }) => {
        // Update institute when domain request status changes
        if (operation === 'update' && doc.status === 'approved') {
          try {
            await req.payload.update({
              collection: 'institutes',
              id: doc.institute,
              data: {
                customDomain: doc.requestedDomain,
                domainVerified: true,
              },
            })
            console.log(`✅ Institute ${doc.institute} domain updated to ${doc.requestedDomain}`)
          } catch (error) {
            console.error('Failed to update institute domain:', error)
          }
        }

        // Remove domain from institute if rejected
        if (operation === 'update' && doc.status === 'rejected') {
          try {
            await req.payload.update({
              collection: 'institutes',
              id: doc.institute,
              data: {
                customDomain: null,
                domainVerified: false,
              },
            })
            console.log(`❌ Institute ${doc.institute} domain request rejected`)
          } catch (error) {
            console.error('Failed to update institute domain:', error)
          }
        }
      },
    ],
  },
  timestamps: true,
}

export default DomainRequests
