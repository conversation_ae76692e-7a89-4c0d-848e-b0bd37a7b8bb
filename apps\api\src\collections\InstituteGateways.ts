import { CollectionConfig } from 'payload/types'

const InstituteGateways: CollectionConfig = {
  slug: 'institute-gateways',
  admin: {
    useAsTitle: 'gateway',
    defaultColumns: ['institute', 'gateway', 'isActive', 'updatedAt'],
    description: 'Institute-specific payment gateway configurations'
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.legacyRole === 'super_admin') return true
      if (user?.institute) {
        return {
          institute: { equals: user.institute }
        }
      }
      return false
    },
    create: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin' || user?.institute
    },
    update: ({ req: { user } }) => {
      if (user?.legacyRole === 'super_admin') return true
      if (user?.institute) {
        return {
          institute: { equals: user.institute }
        }
      }
      return false
    },
    delete: ({ req: { user } }) => {
      if (user?.legacyRole === 'super_admin') return true
      if (user?.institute) {
        return {
          institute: { equals: user.institute }
        }
      }
      return false
    }
  },
  fields: [
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      admin: {
        description: 'Institute that owns this gateway configuration'
      }
    },
    {
      name: 'gateway',
      type: 'relationship',
      relationTo: 'payment-gateways',
      required: true,
      admin: {
        description: 'Payment gateway being configured'
      }
    },
    {
      name: 'configuration',
      type: 'json',
      required: true,
      admin: {
        description: 'Gateway-specific configuration values (API keys, secrets, etc.)'
      },
      validate: (value: any) => {
        if (!value || typeof value !== 'object') {
          return 'Configuration must be a valid object'
        }
        return true
      }
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Whether this gateway is active for the institute'
      }
    },
    {
      name: 'testMode',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Whether this gateway is in test/sandbox mode'
      }
    },
    {
      name: 'configuredBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: 'User who configured this gateway'
      }
    },
    {
      name: 'lastTestedAt',
      type: 'date',
      admin: {
        description: 'Last time this gateway configuration was tested'
      }
    },
    {
      name: 'isPrimary',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Set as primary/default payment gateway for this institute'
      }
    },
    {
      name: 'priorityOrder',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Priority for gateway selection (lower numbers = higher priority)'
      }
    },
    {
      name: 'notes',
      type: 'textarea',
      maxLength: 1000,
      admin: {
        description: 'Internal notes about this gateway configuration'
      }
    }
  ],
  indexes: [
    {
      fields: ['institute', 'gateway'],
      unique: true
    },
    {
      fields: ['institute', 'isActive']
    }
  ],
  hooks: {
    beforeChange: [
      async ({ data, req, operation, originalDoc }: any) => {
        // Set institute from authenticated user if not provided
        if (operation === 'create' && !data.institute && req.user?.institute) {
          data.institute = req.user.institute
        }

        // Set configuredBy for new records
        if (operation === 'create' && req.user) {
          data.configuredBy = req.user.id
        }

        // Handle primary gateway logic
        if (data.isPrimary === true) {
          // If setting this as primary, unset all other primary gateways for this institute
          const instituteId = data.institute || req.user?.institute
          if (instituteId) {
            await req.payload.update({
              collection: 'institute-gateways',
              where: {
                and: [
                  { institute: { equals: instituteId } },
                  { id: { not_equals: originalDoc?.id } }
                ]
              },
              data: {
                isPrimary: false
              }
            })
          }
        }

        return data
      }
    ],
    beforeValidate: [
      ({ data, req }: any) => {
        // Ensure institute admins can only configure their own institute's gateways
        if (req.user?.legacyRole !== 'super_admin' && req.user?.institute) {
          data.institute = req.user.institute
        }
        return data
      }
    ]
  },
  timestamps: true,
}

export default InstituteGateways
