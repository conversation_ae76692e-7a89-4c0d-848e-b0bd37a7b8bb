import { CollectionConfig } from 'payload/types'

const InstituteThemes: CollectionConfig = {
  slug: 'institute-themes',
  labels: {
    singular: 'Institute Theme',
    plural: 'Institute Themes',
  },
  admin: {
    useAsTitle: 'id',
    description: 'Manages theme assignments for institutes',
    defaultColumns: ['institute', 'theme', 'isActive', 'appliedAt'],
    group: 'Institute Management',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: [
    // Institute Reference
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      label: 'Institute',
      admin: {
        description: 'The institute this theme is assigned to',
      },
    },
    
    // Theme Reference
    {
      name: 'theme',
      type: 'relationship',
      relationTo: 'themes',
      required: true,
      label: 'Theme',
      admin: {
        description: 'The theme assigned to the institute',
      },
    },
    
    // Active Status
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Is Active',
      defaultValue: true,
      admin: {
        description: 'Whether this theme assignment is currently active',
      },
    },
    
    // Theme Customizations
    {
      name: 'customizations',
      type: 'json',
      label: 'Theme Customizations',
      admin: {
        description: 'Custom colors, fonts, and content overrides for this theme',
      },
    },
    
    // Application Details
    {
      name: 'appliedAt',
      type: 'date',
      label: 'Applied At',
      defaultValue: () => new Date(),
      admin: {
        description: 'When this theme was applied to the institute',
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    
    {
      name: 'appliedBy',
      type: 'relationship',
      relationTo: 'users',
      label: 'Applied By',
      admin: {
        description: 'User who applied this theme',
      },
    },
    
    // Previous Theme (for history tracking)
    {
      name: 'previousTheme',
      type: 'relationship',
      relationTo: 'themes',
      label: 'Previous Theme',
      admin: {
        description: 'The theme that was active before this one',
      },
    },
    
    // Notes
    {
      name: 'notes',
      type: 'textarea',
      label: 'Notes',
      admin: {
        description: 'Optional notes about this theme assignment',
      },
    },
    
    // Audit Fields
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true,
        position: 'sidebar',
      },
    },
    {
      name: 'updatedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true,
        position: 'sidebar',
      },
    },
  ],
  
  // Hooks
  hooks: {
    beforeChange: [
      async ({ data, req, operation }) => {
        // Set audit fields
        if (operation === 'create') {
          data.createdBy = req.user?.id
          data.appliedBy = req.user?.id
        }
        data.updatedBy = req.user?.id
        
        // Simplified hook - only handle audit fields
        // Theme deactivation is now handled in the API endpoint
        console.log('🎨 InstituteThemes hook:', {
          operation,
          institute: data.institute,
          theme: data.theme,
          isActive: data.isActive
        })
        
        return data
      },
    ],
    
    afterChange: [
      async ({ doc, req, operation }) => {
        // Log theme changes
        console.log(`🎨 Institute theme ${operation}:`, {
          institute: doc.institute,
          theme: doc.theme,
          isActive: doc.isActive,
          appliedAt: doc.appliedAt
        })
        
        // Update theme usage count
        if (operation === 'create' && doc.isActive) {
          try {
            const theme = await req.payload.findByID({
              collection: 'themes',
              id: doc.theme
            })
            
            if (theme) {
              await req.payload.update({
                collection: 'themes',
                id: doc.theme,
                data: {
                  usageCount: (theme.usageCount || 0) + 1
                }
              })
            }
          } catch (error) {
            console.error('Error updating theme usage count:', error)
          }
        }
      },
    ],
  },
  
  // Indexes for better performance
  indexes: [
    {
      fields: ['institute', 'isActive'],
      unique: false,
    },
    {
      fields: ['theme'],
      unique: false,
    },
    {
      fields: ['appliedAt'],
      unique: false,
    },
  ],
}

export default InstituteThemes
