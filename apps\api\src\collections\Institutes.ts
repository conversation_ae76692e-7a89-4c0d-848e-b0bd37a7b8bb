import type { CollectionConfig } from 'payload'
import { isSuperAdmin, canAccessInstitute } from '../access/index'

export const Institutes: CollectionConfig = {
  slug: 'institutes',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'email', 'isActive', 'domainVerified', 'createdAt'],
  },
  fields: [
    // Basic Information
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Institute Name',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'Slug',
      admin: {
        readOnly: true,
      },
      hooks: {
        beforeValidate: [
          ({ data }) => {
            if (data?.name && !data?.slug) {
              data.slug = data.name
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '')
            }
          },
        ],
      },
    },
    {
      name: 'email',
      type: 'email',
      label: 'Contact Email',
    },
    {
      name: 'phone',
      type: 'text',
      label: 'Contact Phone',
    },
    {
      name: 'website',
      type: 'text',
      label: 'Website URL',
      validate: (val: string | null | undefined) => {
        if (val && typeof val === 'string' && !/^https?:\/\/.+/.test(val)) {
          return 'Please enter a valid URL starting with http:// or https://'
        }
        return true
      },
    },
    {
      name: 'tagline',
      type: 'text',
      label: 'Tagline',
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      label: 'Institute Logo',
    },
    {
      name: 'favicon',
      type: 'upload',
      relationTo: 'media',
      label: 'Institute Favicon',
      admin: {
        description: 'Upload a favicon for your institute (recommended: 32x32 or 16x16 pixels, .ico, .png, or .svg format)',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
    },

    // Address Information with Location References
    {
      name: 'addressStreet',
      type: 'text',
      label: 'Street Address',
    },
    {
      name: 'cityId',
      type: 'text',
      label: 'City ID',
      admin: {
        description: 'Reference to city in location management system',
      },
    },
    {
      name: 'stateId',
      type: 'text',
      label: 'State ID',
      admin: {
        description: 'Reference to state in location management system',
      },
    },
    {
      name: 'countryId',
      type: 'text',
      label: 'Country ID',
      admin: {
        description: 'Reference to country in location management system',
      },
    },
    {
      name: 'districtId',
      type: 'text',
      label: 'District ID',
      admin: {
        description: 'Reference to district in location management system',
      },
    },
    {
      name: 'zipCode',
      type: 'text',
      label: 'ZIP/Postal Code',
    },
    // Domain Management
    {
      name: 'customDomain',
      type: 'text',
      label: 'Custom Domain',
      admin: {
        description: 'Custom domain for the institute (e.g., abc-academy.com)',
      },
    },
    {
      name: 'domainVerified',
      type: 'checkbox',
      label: 'Domain Verified',
      defaultValue: false,
    },



    // Status
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active',
      defaultValue: true,
    },

    // Legacy subscription fields (for backward compatibility)
    {
      name: 'subscriptionPlan',
      type: 'select',
      options: [
        { label: 'Free Trial', value: 'free_trial' },
        { label: 'Basic', value: 'basic' },
        { label: 'Professional', value: 'professional' },
        { label: 'Enterprise', value: 'enterprise' },
      ],
      defaultValue: 'free_trial',
    },
    {
      name: 'subscriptionStatus',
      type: 'select',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
        { label: 'Suspended', value: 'suspended' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
      defaultValue: 'active',
    },
    {
      name: 'subscriptionExpiry',
      type: 'date',
    },
    {
      name: 'maxStudents',
      type: 'number',
      defaultValue: 100,
    },
    {
      name: 'maxCourses',
      type: 'number',
      defaultValue: 10,
    },
    {
      name: 'maxBranches',
      type: 'number',
      defaultValue: 1,
    },
    {
      name: 'features',
      type: 'group',
      fields: [
        {
          name: 'marketplace',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'liveClasses',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'exams',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'blogs',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'analytics',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
    },

    // Soft Delete
    {
      name: 'deletedAt',
      type: 'date',
      admin: {
        hidden: true,
      },
    },

  ],
  timestamps: true,
  access: {
    read: () => true,
    create: ({ req: { user } }) => user?.legacyRole === 'super_admin',
    update: ({ req: { user } }) => user?.legacyRole === 'super_admin' || user?.legacyRole === 'institute_admin',
    delete: ({ req: { user } }) => user?.legacyRole === 'super_admin',
  },
}
