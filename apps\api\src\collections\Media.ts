import type { CollectionConfig } from 'payload'
import path from 'path'
import { fileURLToPath } from 'url'
import { canManageMedia, canViewMedia, isAuthenticated } from '../access/index'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export const Media: CollectionConfig = {
  slug: 'media',
  admin: {
    useAsTitle: 'filename',
    defaultColumns: ['filename', 'alt', 'filesize', 'mimeType', 'createdAt'],
    group: 'Media Management',
  },
  access: {
    read: canViewMedia, // Public and authenticated access to media
    create: isAuthenticated, // Any authenticated user can upload
    update: canManageMedia, // Only admins and owners can update
    delete: canManageMedia, // Only admins and owners can delete
  },
  upload: {
    staticDir: path.resolve(dirname, '../../media'),
    adminThumbnail: 'thumbnail',
    mimeTypes: [
      'image/*',
      'video/*',
      'audio/*',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'application/zip',
      'application/x-rar-compressed',
    ],

    imageSizes: [
      {
        name: 'thumbnail',
        width: 400,
        height: 300,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 80,
          },
        },
      },
      {
        name: 'card',
        width: 768,
        height: 432,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 85,
          },
        },
      },
      {
        name: 'hero',
        width: 1200,
        height: 675,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 90,
          },
        },
      },
      // Profile/Avatar specific sizes
      {
        name: 'avatar_small',
        width: 40,
        height: 40,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 85,
          },
        },
      },
      {
        name: 'avatar_medium',
        width: 80,
        height: 80,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 85,
          },
        },
      },
      {
        name: 'avatar_large',
        width: 150,
        height: 150,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 90,
          },
        },
      },
      {
        name: 'profile',
        width: 300,
        height: 300,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 90,
          },
        },
      },
    ],
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      required: true,
      admin: {
        description: 'Alternative text for accessibility and SEO',
      },
    },
    {
      name: 'caption',
      type: 'text',
      admin: {
        description: 'Caption or description for the media',
      },
    },
    {
      name: 'mediaType',
      type: 'select',
      options: [
        { label: 'Course Thumbnail', value: 'course_thumbnail' },
        { label: 'Course Content', value: 'course_content' },
        { label: 'Theme Asset', value: 'theme_asset' },
        { label: 'Institute Logo', value: 'institute_logo' },
        { label: 'Platform Logo', value: 'platform_logo' },
        { label: 'Platform Favicon', value: 'platform_favicon' },
        { label: 'User Avatar', value: 'user_avatar' },
        { label: 'Blog Image', value: 'blog_image' },
        { label: 'Marketing Material', value: 'marketing' },
        { label: 'Document', value: 'document' },
        { label: 'Other', value: 'other' },
      ],
      admin: {
        description: 'Type of media for organization and filtering',
      },
    },
    {
      name: 'uploadedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true,
        description: 'User who uploaded this media',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, data, operation }) => {
        // Fix URLs - remove domain and /api/ prefix
        if (data.url) {
          // Handle full URLs like http://localhost:3001/api/media/file/filename.jpg
          if (data.url.includes('://')) {
            data.url = data.url.replace(/^https?:\/\/[^\/]+\/api\//, '/')
            console.log('🔧 beforeChange: Fixed full media URL:', data.url)
          } else if (data.url.includes('/api/')) {
            data.url = data.url.replace(/^\/api\//, '/')
            console.log('🔧 beforeChange: Removed /api/ from media URL:', data.url)
          }
        }

        // Fix thumbnailURL if it exists
        if (data.thumbnailURL) {
          if (data.thumbnailURL.includes('://')) {
            data.thumbnailURL = data.thumbnailURL.replace(/^https?:\/\/[^\/]+\/api\//, '/')
            console.log('🔧 beforeChange: Fixed full thumbnail URL:', data.thumbnailURL)
          } else if (data.thumbnailURL.includes('/api/')) {
            data.thumbnailURL = data.thumbnailURL.replace(/^\/api\//, '/')
            console.log('🔧 beforeChange: Removed /api/ from thumbnail URL:', data.thumbnailURL)
          }
        }

        // Fix sizes URLs if they exist
        if (data.sizes) {
          Object.keys(data.sizes).forEach(sizeKey => {
            const size = data.sizes[sizeKey]
            if (size && size.url) {
              if (size.url.includes('://')) {
                size.url = size.url.replace(/^https?:\/\/[^\/]+\/api\//, '/')
                console.log(`🔧 beforeChange: Fixed full size URL (${sizeKey}):`, size.url)
              } else if (size.url.includes('/api/')) {
                size.url = size.url.replace(/^\/api\//, '/')
                console.log(`🔧 beforeChange: Removed /api/ from size URL (${sizeKey}):`, size.url)
              }
            }
          })
        }

        if (operation === 'create') {
          // Set uploaded by
          if (req.user?.id) {
            data.uploadedBy = req.user.id
          }

          // Auto-generate alt text if not provided
          if (!data.alt && data.filename) {
            data.alt = data.filename.replace(/\.[^/.]+$/, '').replace(/[-_]/g, ' ')
          }
        }
        return data
      },
    ],
    afterChange: [
      async ({ req, doc }) => {
        // Fix URLs that Payload generates after saving (including full URLs)
        let needsUpdate = false
        const updateData: any = {}

        // Fix main URL
        if (doc.url) {
          let fixedUrl = doc.url
          if (doc.url.includes('://')) {
            fixedUrl = doc.url.replace(/^https?:\/\/[^\/]+\/api\//, '/')
            needsUpdate = true
            console.log('🔧 afterChange: Fixed full media URL:', fixedUrl)
          } else if (doc.url.includes('/api/')) {
            fixedUrl = doc.url.replace(/^\/api\//, '/')
            needsUpdate = true
            console.log('🔧 afterChange: Removed /api/ from media URL:', fixedUrl)
          }
          if (needsUpdate) {
            updateData.url = fixedUrl
          }
        }

        // Fix thumbnailURL
        if (doc.thumbnailURL) {
          let fixedThumbUrl = doc.thumbnailURL
          if (doc.thumbnailURL.includes('://')) {
            fixedThumbUrl = doc.thumbnailURL.replace(/^https?:\/\/[^\/]+\/api\//, '/')
            needsUpdate = true
            console.log('🔧 afterChange: Fixed full thumbnail URL:', fixedThumbUrl)
          } else if (doc.thumbnailURL.includes('/api/')) {
            fixedThumbUrl = doc.thumbnailURL.replace(/^\/api\//, '/')
            needsUpdate = true
            console.log('🔧 afterChange: Removed /api/ from thumbnail URL:', fixedThumbUrl)
          }
          if (fixedThumbUrl !== doc.thumbnailURL) {
            updateData.thumbnailURL = fixedThumbUrl
          }
        }

        // Fix sizes URLs
        if (doc.sizes) {
          const fixedSizes = { ...doc.sizes }
          let sizesChanged = false
          Object.keys(fixedSizes).forEach(sizeKey => {
            const size = fixedSizes[sizeKey]
            if (size && size.url) {
              if (size.url.includes('://')) {
                size.url = size.url.replace(/^https?:\/\/[^\/]+\/api\//, '/')
                sizesChanged = true
                console.log(`🔧 afterChange: Fixed full size URL (${sizeKey}):`, size.url)
              } else if (size.url.includes('/api/')) {
                size.url = size.url.replace(/^\/api\//, '/')
                sizesChanged = true
                console.log(`🔧 afterChange: Removed /api/ from size URL (${sizeKey}):`, size.url)
              }
            }
          })
          if (sizesChanged) {
            updateData.sizes = fixedSizes
            needsUpdate = true
          }
        }

        // Update the record if any URLs were fixed
        if (needsUpdate) {
          console.log('🔄 afterChange: Updating record with fixed URLs...')
          try {
            await req.payload.update({
              collection: 'media',
              id: doc.id,
              data: updateData,
              depth: 0, // Prevent infinite recursion
            })
            console.log('✅ afterChange: URLs updated successfully')
          } catch (error) {
            console.error('❌ afterChange: Failed to update URLs:', error)
          }
        }
      },
    ],
  },
  timestamps: true,
}
