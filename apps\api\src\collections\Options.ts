import { CollectionConfig } from 'payload'

export const Options: CollectionConfig = {
  slug: 'options',
  admin: {
    useAsTitle: 'key',
    defaultColumns: ['key', 'value', 'category', 'type', 'is_public', 'updatedAt'],
    group: 'Platform Settings',
    description: 'Manage platform-wide configuration settings',
  },
  access: {
    // Only super admin can access settings
    read: ({ req: { user } }) => {
      if (!user) return false
      return user.legacyRole === 'super_admin'
    },
    create: ({ req: { user } }) => {
      if (!user) return false
      return user.legacyRole === 'super_admin'
    },
    update: ({ req: { user } }) => {
      if (!user) return false
      return user.legacyRole === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      if (!user) return false
      return user.legacyRole === 'super_admin'
    },
  },
  fields: [
    {
      name: 'key',
      type: 'text',
      required: true,
      unique: true,
      index: true,
      admin: {
        description: 'Unique setting identifier (e.g., platform_name, support_email)',
        placeholder: 'platform_name',
      },
      validate: (value: any) => {
        if (!value) return 'Key is required'
        if (!/^[a-z0-9_]+$/.test(value)) {
          return 'Key must contain only lowercase letters, numbers, and underscores'
        }
        return true
      }
    },
    {
      name: 'value',
      type: 'text', // Use text instead of textarea for better compatibility
      required: true,
      admin: {
        description: 'Setting value (can be string, number, boolean, JSON, or media ID)',
        placeholder: 'KISS LMS',
      },
    },
    {
      name: 'description',
      type: 'text',
      admin: {
        description: 'Human-readable description of this setting',
        placeholder: 'The name of the platform displayed to users',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      defaultValue: 'platform',
      index: true,
      options: [
        { label: 'Platform', value: 'platform' },
        { label: 'Email', value: 'email' },
        { label: 'Security', value: 'security' },
        { label: 'Storage', value: 'storage' },
        { label: 'Payment', value: 'payment' },
        { label: 'Notification', value: 'notification' },
        { label: 'Integration', value: 'integration' },
        { label: 'Feature', value: 'feature' },
      ],
      admin: {
        description: 'Category to group related settings',
      },
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      defaultValue: 'string',
      options: [
        { label: 'String', value: 'string' },
        { label: 'Number', value: 'number' },
        { label: 'Boolean', value: 'boolean' },
        { label: 'JSON', value: 'json' },
        { label: 'URL', value: 'url' },
        { label: 'Email', value: 'email' },
        { label: 'Textarea', value: 'textarea' },
        { label: 'File Upload', value: 'upload' },
        { label: 'Media', value: 'media' },
      ],
      admin: {
        description: 'Data type for validation and UI rendering',
      },
    },
    {
      name: 'is_public',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Whether this setting can be accessed by non-admin users',
      },
    },
    {
      name: 'is_required',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Whether this setting is required for platform operation',
      },
    },
    {
      name: 'upload',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Upload file for this setting (only used when type is "upload")',
        condition: (data) => data?.type === 'upload',
      },
    },
    {
      name: 'validation_rules',
      type: 'group',
      fields: [
        {
          name: 'min_length',
          type: 'number',
          admin: {
            description: 'Minimum length for string values',
            condition: (data) => {
              return data?.type === 'string'
            },
          },
        },
        {
          name: 'max_length',
          type: 'number',
          admin: {
            description: 'Maximum length for string values',
            condition: (data) => {
              return data?.type === 'string'
            },
          },
        },
        {
          name: 'min_value',
          type: 'number',
          admin: {
            description: 'Minimum value for number types',
            condition: (data) => {
              return data?.type === 'number'
            },
          },
        },
        {
          name: 'max_value',
          type: 'number',
          admin: {
            description: 'Maximum value for number types',
            condition: (data) => {
              return data?.type === 'number'
            },
          },
        },
        {
          name: 'pattern',
          type: 'text',
          admin: {
            description: 'Regex pattern for validation',
            placeholder: '^[a-zA-Z0-9]+$',
          },
        },
      ],
      admin: {
        description: 'Validation rules for this setting',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        // Auto-generate description if not provided
        if (operation === 'create' && !data.description && data.key) {
          data.description = data.key.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())
        }
        
        // Validate value based on type
        if (data.type && data.value) {
          switch (data.type) {
            case 'number':
              if (isNaN(Number(data.value))) {
                throw new Error('Value must be a valid number')
              }
              break
            case 'boolean':
              if (!['true', 'false', '1', '0'].includes(data.value.toLowerCase())) {
                throw new Error('Value must be true/false or 1/0')
              }
              break
            case 'email':
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
              if (!emailRegex.test(data.value)) {
                throw new Error('Value must be a valid email address')
              }
              break
            case 'url':
              // Allow any string for URL type - no validation
              break
            case 'json':
              try {
                JSON.parse(data.value)
              } catch {
                throw new Error('Value must be valid JSON')
              }
              break
          }
        }
        
        return data
      },
    ],
    afterRead: [
      async ({ req, doc }) => {
        // For media type settings, populate the media reference
        if (doc.type === 'media' && doc.value) {
          try {
            const mediaRecord = await req.payload.findByID({
              collection: 'media',
              id: doc.value,
            })

            if (mediaRecord) {
              // Fix URL to remove /api/ prefix and domain
             

              // Add only essential media data to avoid bloated response
              doc.mediaData = {
                id: mediaRecord.id,
                filename: mediaRecord.filename,
                url:mediaRecord.url,
                alt: mediaRecord.alt,
                mediaType: mediaRecord.mediaType,
                mimeType: mediaRecord.mimeType,
                filesize: mediaRecord.filesize,
                width: mediaRecord.width,
                height: mediaRecord.height,
              }
            }
          } catch (error) {
            console.error(`Failed to populate media for setting ${doc.key}:`, error)
          }
        }

        return doc
      },
    ],
    afterChange: [
      ({ req, operation, doc }) => {
        // Log setting changes for audit
        console.log(`Setting ${operation}: ${doc.key} = ${doc.value} by ${req.user?.email}`)
      },
    ],
  },
  timestamps: true,
}

export default Options
