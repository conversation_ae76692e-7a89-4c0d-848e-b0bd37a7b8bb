import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const PaymentGateways: CollectionConfig = {
  slug: 'payment-gateways',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'supportedCurrencies', 'supportedMethods', 'isActive', 'isFeatured'],
    group: 'Payment Management',
  },
  access: {
    read: () => true, // All users can read available gateways
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      admin: {
        description: 'Payment gateway name (e.g., Razorpay, Stripe, PayPal)'
      }
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        description: 'Description of the payment gateway'
      }
    },
    {
      name: 'supportedCurrencies',
      type: 'json',
      required: true,
      admin: {
        description: 'List of supported currency codes (e.g., ["INR", "USD", "EUR"])'
      },
      validate: (value: any) => {
        if (!value) return 'Supported currencies are required'
        if (!Array.isArray(value)) return 'Supported currencies must be an array'
        if (value.length === 0) return 'At least one currency is required'

        for (const currency of value) {
          if (typeof currency !== 'string') return 'Each currency must be a string'
          if (currency.length !== 3) return 'Currency codes must be exactly 3 characters'
          if (!/^[A-Z]{3}$/.test(currency)) return 'Currency codes must be 3 uppercase letters'
        }
        return true
      }
    },
    {
      name: 'supportedMethods',
      type: 'json',
      admin: {
        description: 'List of supported payment methods (e.g., ["credit_card", "debit_card", "upi"])'
      },
      validate: (value: any) => {
        if (!value) return true // Optional field
        if (!Array.isArray(value)) return 'Supported methods must be an array'

        for (const method of value) {
          if (typeof method !== 'string') return 'Each payment method must be a string'
          if (method.length > 50) return 'Payment method names must be 50 characters or less'
          if (!/^[a-zA-Z0-9_-]+$/.test(method)) return 'Payment methods can only contain letters, numbers, underscore, and hyphen'
        }
        return true
      }
    },
    {
      name: 'supportedCountries',
      type: 'json',
      admin: {
        description: 'List of supported country codes (e.g., ["IN", "US", "GB"]) - optional'
      },
      validate: (value: any) => {
        if (!value) return true // Optional field
        if (!Array.isArray(value)) return 'Supported countries must be an array'

        for (const country of value) {
          if (typeof country !== 'string') return 'Each country must be a string'
          if (country.length !== 2) return 'Country codes must be exactly 2 characters'
          if (!/^[A-Z]{2}$/.test(country)) return 'Country codes must be 2 uppercase letters'
        }
        return true
      }
    },
    {
      name: 'requiredConfigFields',
      type: 'array',
      admin: {
        description: 'Required configuration fields that institutes must provide'
      },
      fields: [
        {
          name: 'key',
          type: 'text',
          required: true,
          maxLength: 50,
          admin: {
            description: 'Field identifier (e.g., "api_key", "secret_key")'
          },
          validate: (value: any) => {
            if (!value) return 'Field key is required'
            if (!/^[a-zA-Z0-9_-]+$/.test(value)) return 'Field key can only contain letters, numbers, underscore, and hyphen'
            return true
          }
        },
        {
          name: 'label',
          type: 'text',
          required: true,
          maxLength: 100,
          admin: {
            description: 'Human-readable label (e.g., "API Key", "Secret Key")'
          }
        },
        {
          name: 'type',
          type: 'select',
          required: true,
          options: [
            { label: 'Text', value: 'text' },
            { label: 'Password', value: 'password' },
            { label: 'URL', value: 'url' },
            { label: 'Email', value: 'email' },
            { label: 'Number', value: 'number' },
            { label: 'Boolean', value: 'boolean' }
          ],
          admin: {
            description: 'Input type for the field'
          }
        },
        {
          name: 'placeholder',
          type: 'text',
          maxLength: 200,
          admin: {
            description: 'Example text for the field'
          }
        },
        {
          name: 'description',
          type: 'textarea',
          maxLength: 500,
          admin: {
            description: 'Help text explaining the field purpose'
          }
        },
        {
          name: 'isRequired',
          type: 'checkbox',
          defaultValue: true,
          admin: {
            description: 'Whether this field is mandatory for institutes'
          }
        }
      ]
    },
    {
      name: 'optionalConfigFields',
      type: 'array',
      admin: {
        description: 'Optional configuration fields that institutes can provide'
      },
      fields: [
        {
          name: 'key',
          type: 'text',
          required: true,
          maxLength: 50,
          admin: {
            description: 'Field identifier (e.g., "theme_color", "webhook_url")'
          },
          validate: (value: any) => {
            if (!value) return 'Field key is required'
            if (!/^[a-zA-Z0-9_-]+$/.test(value)) return 'Field key can only contain letters, numbers, underscore, and hyphen'
            return true
          }
        },
        {
          name: 'label',
          type: 'text',
          required: true,
          maxLength: 100,
          admin: {
            description: 'Human-readable label (e.g., "Theme Color", "Webhook URL")'
          }
        },
        {
          name: 'type',
          type: 'select',
          required: true,
          options: [
            { label: 'Text', value: 'text' },
            { label: 'Password', value: 'password' },
            { label: 'URL', value: 'url' },
            { label: 'Email', value: 'email' },
            { label: 'Number', value: 'number' },
            { label: 'Boolean', value: 'boolean' }
          ],
          admin: {
            description: 'Input type for the field'
          }
        },
        {
          name: 'placeholder',
          type: 'text',
          maxLength: 200,
          admin: {
            description: 'Example text for the field'
          }
        },
        {
          name: 'description',
          type: 'textarea',
          maxLength: 500,
          admin: {
            description: 'Help text explaining the field purpose'
          }
        },
        {
          name: 'isRequired',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Whether this field is mandatory for institutes'
          }
        }
      ]
    },
    {
      name: 'documentationUrl',
      type: 'text',
      admin: {
        description: 'Link to gateway integration documentation'
      }
    },
    {
      name: 'apiVersion',
      type: 'text',
      maxLength: 20,
      admin: {
        description: 'API version supported by this gateway'
      }
    },
    {
      name: 'webhookSupport',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Whether this gateway supports webhooks'
      }
    },

    {
      name: 'logoUrl',
      type: 'text',
      admin: {
        description: 'URL to gateway logo image'
      }
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Show as featured gateway in institute selection'
      }
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: 'Super Admin who added this gateway'
      }
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, req, operation }: any) => {
        // Initialize empty configuration fields if not provided
        if (!data.requiredConfigFields) {
          data.requiredConfigFields = []
        }
        if (!data.optionalConfigFields) {
          data.optionalConfigFields = []
        }

        // Set createdBy for new records
        if (operation === 'create' && req.user) {
          data.createdBy = req.user.id
        }

        return data
      }
    ]
  },
  timestamps: true,
}

export default PaymentGateways
