import { CollectionConfig } from 'payload'

export const Permissions: CollectionConfig = {
  slug: 'permissions',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'description', 'createdAt'],
    group: 'Users',
    description: 'Manage individual permissions for the system',
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    update: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      unique: true,
      label: 'Permission Name',
      admin: {
        description: 'A unique identifier for the permission (e.g., create:courses, delete:users)',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Permission Description',
    },
  ],
}

export default Permissions
