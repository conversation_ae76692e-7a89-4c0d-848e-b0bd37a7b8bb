import { CollectionConfig } from 'payload/types'

const PlatformBlogCategories: CollectionConfig = {
  slug: 'platform-blog-categories',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'description', 'isActive'],
    group: 'Platform Content',
  },
  access: {
    read: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    create: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    update: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'description',
      type: 'textarea',
    },
    {
      name: 'color',
      type: 'text',
      admin: {
        description: 'Hex color code for category display'
      }
    },
    {
      name: 'icon',
      type: 'text',
      admin: {
        description: 'Icon name for category display'
      }
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate slug from name if not provided
        if (!data.slug && data.name) {
          data.slug = data.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }
        
        return data
      }
    ]
  }
}

export default PlatformBlogCategories
