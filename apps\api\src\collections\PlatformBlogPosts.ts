import { CollectionConfig } from 'payload/types'

const PlatformBlogPosts: CollectionConfig = {
  slug: 'platform-blog-posts',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'status', 'category', 'author', 'publishedAt'],
    group: 'Platform Content',
  },
  access: {
    read: ({ req: { user } }) => {
      // Only super admins can read platform blog posts
      return user?.legacyRole === 'super_admin'
    },
    create: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    update: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
  },
  fields: [
    // Basic Information
    {
      name: 'title',
      type: 'text',
      required: true,
      maxLength: 200,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'URL-friendly version of the title'
      }
    },
    {
      name: 'excerpt',
      type: 'textarea',
      maxLength: 300,
      admin: {
        description: 'Brief summary for previews'
      }
    },
    {
      name: 'content',
      type: 'richText',
      required: true,
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
    },

    // Publishing
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Scheduled', value: 'scheduled' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'scheduledFor',
      type: 'date',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        condition: (data) => data.status === 'scheduled',
      },
    },

    // Organization
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'platform-blog-categories',
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
    },

    // Announcement System
    {
      name: 'isAnnouncement',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Mark as platform announcement'
      }
    },
    {
      name: 'announcementPriority',
      type: 'select',
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
        { label: 'Critical', value: 'critical' },
      ],
      admin: {
        condition: (data) => data.isAnnouncement,
      },
    },
    {
      name: 'targetAudience',
      type: 'select',
      hasMany: true,
      options: [
        { label: 'Institute Admins', value: 'institute_admin' },
        { label: 'Students', value: 'student' },
        { label: 'Staff', value: 'staff' },
        { label: 'Public', value: 'public' },
      ],
      defaultValue: ['public'],
    },

    // SEO
    {
      name: 'seo',
      type: 'group',
      fields: [
        {
          name: 'title',
          type: 'text',
          maxLength: 150,
        },
        {
          name: 'description',
          type: 'textarea',
          maxLength: 300,
        },
        {
          name: 'keywords',
          type: 'array',
          fields: [
            {
              name: 'keyword',
              type: 'text',
            },
          ],
        },
        {
          name: 'canonicalUrl',
          type: 'text',
        },
      ],
    },

    // Analytics
    {
      name: 'analytics',
      type: 'group',
      fields: [
        {
          name: 'viewCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'uniqueViewCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'likeCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'shareCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'readingTime',
          type: 'number',
          admin: {
            readOnly: true,
            description: 'Estimated reading time in minutes',
          },
        },
        // Audience-specific analytics
        {
          name: 'instituteAdminViews',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'studentViews',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'staffViews',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'publicViews',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
      ],
    },

    // Settings
    {
      name: 'settings',
      type: 'group',
      fields: [
        {
          name: 'allowComments',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'isFeatured',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'isSticky',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Pin to top of blog list',
          },
        },
        {
          name: 'showOnDashboard',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Show on platform dashboard',
          },
        },
      ],
    },

    // Authoring
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        readOnly: true
      }
    },
    {
      name: 'lastEditedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true
      }
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, req }) => {
        // Set author from authenticated user
        if (!data.author) {
          data.author = req.user?.id
        }
        
        // Set lastEditedBy
        data.lastEditedBy = req.user?.id
        
        // Auto-generate slug from title if not provided
        if (!data.slug && data.title) {
          data.slug = data.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }
        
        // Calculate reading time
        if (data.content) {
          const wordCount = data.content.replace(/<[^>]*>/g, '').split(/\s+/).length
          data.analytics = {
            ...data.analytics,
            readingTime: Math.ceil(wordCount / 200) // Average reading speed
          }
        }
        
        // Set published date when status changes to published
        if (data.status === 'published' && !data.publishedAt) {
          data.publishedAt = new Date()
        }
        
        return data
      }
    ]
  }
}

export default PlatformBlogPosts
