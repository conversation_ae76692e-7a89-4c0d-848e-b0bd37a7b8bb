import { CollectionConfig } from 'payload'

export const RolePermissions: CollectionConfig = {
  slug: 'role-permissions',
  labels: {
    singular: 'Role Permission',
    plural: 'Role Permissions',
  },
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['role', 'permission', 'createdAt'],
    group: 'User Management',
  },
  access: {
    create: ({ req: { user } }) => {
      // Use legacyRole field for access control (consistent with other collections)
      return user?.legacyRole === 'super_admin'
    },
    read: ({ req: { user } }) => {
      // Use legacyRole field for access control (consistent with other collections)
      return user?.legacyRole === 'super_admin'
    },
    update: ({ req: { user } }) => {
      // Use legacyRole field for access control (consistent with other collections)
      return user?.legacyRole === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      // Use legacyRole field for access control (consistent with other collections)
      return user?.legacyRole === 'super_admin'
    },
  },
  fields: [
    {
      name: 'role',
      type: 'relationship',
      relationTo: 'roles',
      required: true,
      admin: {
        description: 'The role to assign permissions to',
      },
    },
    {
      name: 'permission',
      type: 'relationship',
      relationTo: 'permissions',
      required: true,
      admin: {
        description: 'The permission to assign to the role',
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Whether this role-permission assignment is active',
      },
    },
    {
      name: 'assignedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: 'User who assigned this permission to the role',
        readOnly: true,
      },
    },
    {
      name: 'assignedAt',
      type: 'date',
      defaultValue: () => new Date(),
      admin: {
        description: 'When this permission was assigned to the role',
        readOnly: true,
      },
    },
    {
      name: 'notes',
      type: 'textarea',
      admin: {
        description: 'Optional notes about this role-permission assignment',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          if (req.user) {
            data.assignedBy = req.user.id
          }
          data.assignedAt = new Date()
        }
        return data
      },
    ],
  },
  indexes: [
    {
      fields: ['role', 'permission'],
      unique: true,
    },
    {
      fields: ['role'],
    },
    {
      fields: ['permission'],
    },
    {
      fields: ['isActive'],
    },
  ],
}

export default RolePermissions
