import { CollectionConfig } from 'payload'

export const Roles: CollectionConfig = {
  slug: 'roles',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'description', 'createdAt'],
    group: 'Users',
    description: 'Manage user roles and their associated permissions',
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    update: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      unique: true,
      label: 'Role Name',
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Role Description',
    },
    {
      name: 'permissions',
      type: 'relationship',
      relationTo: 'permissions',
      hasMany: true,
      label: 'Permissions',
    },
  ],
}

export default Roles
