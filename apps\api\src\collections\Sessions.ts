import { CollectionConfig } from 'payload/types'
import { isAdminOrSelf } from '../access/index'

const Sessions: CollectionConfig = {
  slug: 'sessions',
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['user', 'deviceType', 'ipAddress', 'isActive', 'createdAt'],
  },
  access: {
    read: isAdminOrSelf,
    create: () => true, // Sessions are created automatically
    update: isAdminOrSelf,
    delete: isAdminOrSelf,
  },
  fields: [
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      index: true,
    },
    {
      name: 'sessionToken',
      type: 'text',
      required: true,
      unique: true,
      index: true,
      admin: { hidden: true },
    },
    {
      name: 'deviceInfo',
      type: 'group',
      fields: [
        {
          name: 'deviceType',
          type: 'select',
          required: true,
          options: [
            { label: 'Desktop', value: 'desktop' },
            { label: 'Mobile', value: 'mobile' },
            { label: 'Tablet', value: 'tablet' },
          ],
        },
        {
          name: 'deviceName',
          type: 'text',
          required: true,
        },
        {
          name: 'browser',
          type: 'text',
          required: true,
        },
        {
          name: 'operatingSystem',
          type: 'text',
          required: true,
        },
        {
          name: 'userAgent',
          type: 'textarea',
          admin: { hidden: true },
        },
      ],
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'ipAddress',
          type: 'text',
          required: true,
          index: true,
        },
        {
          name: 'city',
          type: 'text',
        },
        {
          name: 'region',
          type: 'text',
        },
        {
          name: 'country',
          type: 'text',
        },
        {
          name: 'latitude',
          type: 'number',
        },
        {
          name: 'longitude',
          type: 'number',
        },
      ],
    },
    {
      name: 'security',
      type: 'group',
      fields: [
        {
          name: 'isSecure',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'isCurrent',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'loginMethod',
          type: 'select',
          options: [
            { label: 'Password', value: 'password' },
            { label: 'Two Factor', value: '2fa' },
            { label: 'Social Login', value: 'social' },
            { label: 'SSO', value: 'sso' },
          ],
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'lastActivity',
      type: 'date',
      required: true,
      index: true,
    },
    {
      name: 'expiresAt',
      type: 'date',
      required: true,
      index: true,
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Set expiration time (2 hours from now)
          const expirationTime = new Date()
          expirationTime.setHours(expirationTime.getHours() + 2)
          data.expiresAt = expirationTime
          data.lastActivity = new Date()
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default Sessions
