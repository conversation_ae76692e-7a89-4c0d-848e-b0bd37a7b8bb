import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const Settings: CollectionConfig = {
  slug: 'settings',
  admin: {
    useAsTitle: 'key',
    defaultColumns: ['key', 'category', 'scope', 'institute', 'updatedAt'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (!user) return false
      
      // Super admin can read all settings
      if (user.role === 'super_admin') return true
      
      // Institute admin can read their own settings
      if (user.role === 'institute_admin') {
        return {
          or: [
            { scope: { equals: 'global' } },
            { 
              and: [
                { scope: { equals: 'institute' } },
                { institute: { equals: user.institute } }
              ]
            }
          ]
        }
      }
      
      // Students can only read global settings
      return { scope: { equals: 'global' } }
    },
    create: isAdmin,
    update: ({ req: { user } }) => {
      if (!user) return false
      
      // Super admin can update all settings
      if (user.role === 'super_admin') return true
      
      // Institute admin can update their own settings
      if (user.role === 'institute_admin') {
        return {
          and: [
            { scope: { equals: 'institute' } },
            { institute: { equals: user.institute } }
          ]
        }
      }
      
      return false
    },
    delete: isAdmin,
  },
  fields: [
    {
      name: 'key',
      type: 'text',
      required: true,
      unique: true,
      index: true,
    },
    {
      name: 'value',
      type: 'json',
      required: true,
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Platform', value: 'platform' },
        { label: 'Security', value: 'security' },
        { label: 'Email', value: 'email' },
        { label: 'Storage', value: 'storage' },
        { label: 'Billing', value: 'billing' },
        { label: 'Theme', value: 'theme' },
        { label: 'Institute', value: 'institute' },
        { label: 'Course', value: 'course' },
        { label: 'User', value: 'user' },
        { label: 'Notification', value: 'notification' },
      ],
      index: true,
    },
    {
      name: 'scope',
      type: 'select',
      required: true,
      options: [
        { label: 'Global', value: 'global' },
        { label: 'Institute', value: 'institute' },
        { label: 'Branch', value: 'branch' },
      ],
      defaultValue: 'global',
      index: true,
    },
    {
      name: 'dataType',
      type: 'select',
      required: true,
      options: [
        { label: 'String', value: 'string' },
        { label: 'Number', value: 'number' },
        { label: 'Boolean', value: 'boolean' },
        { label: 'JSON', value: 'json' },
        { label: 'Array', value: 'array' },
      ],
      defaultValue: 'string',
    },
    {
      name: 'description',
      type: 'textarea',
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      admin: {
        condition: (data) => data.scope === 'institute' || data.scope === 'branch',
      },
      index: true,
    },
    {
      name: 'isPublic',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Whether this setting can be accessed by non-authenticated users',
      },
    },
    {
      name: 'isEditable',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Whether this setting can be modified through the UI',
      },
    },
  ],
  timestamps: true,
}

export default Settings
