import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const States: CollectionConfig = {
  slug: 'states',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'code', 'country', 'isActive', 'createdAt'],
    group: 'Location Management',
  },
  access: {
    read: () => true, // All users can read states
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'code',
      type: 'text',
      maxLength: 10,
      index: true,
    },
    {
      name: 'country',
      type: 'relationship',
      relationTo: 'countries',
      required: true,
      index: true,
    },
    {
      name: 'details',
      type: 'group',
      fields: [
        {
          name: 'capital',
          type: 'text',
          maxLength: 100,
        },
        {
          name: 'population',
          type: 'number',
        },
        {
          name: 'area',
          type: 'number',
          admin: {
            description: 'Area in square kilometers',
          },
        },
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'State', value: 'state' },
            { label: 'Province', value: 'province' },
            { label: 'Territory', value: 'territory' },
            { label: 'Region', value: 'region' },
          ],
          defaultValue: 'state',
        },
      ],
    },
    {
      name: 'coordinates',
      type: 'group',
      fields: [
        {
          name: 'latitude',
          type: 'number',
        },
        {
          name: 'longitude',
          type: 'number',
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'priority',
      type: 'number',
      defaultValue: 0,
    },
    {
      name: 'metadata',
      type: 'json',
    },
  ],
  timestamps: true,
}

export default States
