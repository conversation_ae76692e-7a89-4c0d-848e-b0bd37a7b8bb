import { CollectionConfig } from 'payload'

const StudentDetails: CollectionConfig = {
  slug: 'student-details',
  admin: {
    useAsTitle: 'student',
    description: 'Additional details for students including location, education, and personal information',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: [
    // Student reference (required)
    {
      name: 'student',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      unique: true,
      admin: {
        description: 'Reference to the student user',
      },
      filterOptions: {
        legacyRole: {
          equals: 'student'
        }
      }
    },
    
    // Location Information (optional)
    {
      name: 'country',
      type: 'relationship',
      relationTo: 'countries',
      required: false,
      admin: {
        description: 'Country of residence',
      },
    },
    {
      name: 'state',
      type: 'relationship',
      relationTo: 'states',
      required: false,
      admin: {
        description: 'State/Province of residence',
      },
    },
    {
      name: 'district',
      type: 'relationship',
      relationTo: 'districts',
      required: false,
      admin: {
        description: 'District/City of residence',
      },
    },
    
    // Education Information (optional)
    {
      name: 'education',
      type: 'group',
      fields: [
        {
          name: 'highestQualification',
          type: 'select',
          options: [
            { label: 'High School', value: 'high_school' },
            { label: 'Diploma', value: 'diploma' },
            { label: 'Bachelor\'s Degree', value: 'bachelors' },
            { label: 'Master\'s Degree', value: 'masters' },
            { label: 'PhD', value: 'phd' },
            { label: 'Other', value: 'other' },
          ],
          admin: {
            description: 'Highest educational qualification',
          },
        },
        {
          name: 'institution',
          type: 'text',
          admin: {
            description: 'Name of the educational institution',
          },
        },
        {
          name: 'fieldOfStudy',
          type: 'text',
          admin: {
            description: 'Field of study or specialization',
          },
        },
        {
          name: 'graduationYear',
          type: 'number',
          admin: {
            description: 'Year of graduation',
          },
        },
        {
          name: 'percentage',
          type: 'number',
          admin: {
            description: 'Percentage or CGPA',
          },
        },
      ],
    },
    
    // Personal Information (optional)
    {
      name: 'personalInfo',
      type: 'group',
      fields: [
        {
          name: 'fatherName',
          type: 'text',
          admin: {
            description: 'Father\'s name',
          },
        },
        {
          name: 'motherName',
          type: 'text',
          admin: {
            description: 'Mother\'s name',
          },
        },
        {
          name: 'guardianName',
          type: 'text',
          admin: {
            description: 'Guardian\'s name (if different from parents)',
          },
        },
        {
          name: 'emergencyContact',
          type: 'text',
          admin: {
            description: 'Emergency contact number',
          },
        },
        {
          name: 'bloodGroup',
          type: 'select',
          options: [
            { label: 'A+', value: 'a_positive' },
            { label: 'A-', value: 'a_negative' },
            { label: 'B+', value: 'b_positive' },
            { label: 'B-', value: 'b_negative' },
            { label: 'AB+', value: 'ab_positive' },
            { label: 'AB-', value: 'ab_negative' },
            { label: 'O+', value: 'o_positive' },
            { label: 'O-', value: 'o_negative' },
          ],
          admin: {
            description: 'Blood group',
          },
        },
        {
          name: 'nationality',
          type: 'text',
          admin: {
            description: 'Nationality',
          },
        },
        {
          name: 'religion',
          type: 'text',
          admin: {
            description: 'Religion',
          },
        },
        {
          name: 'caste',
          type: 'text',
          admin: {
            description: 'Caste/Category',
          },
        },
      ],
    },
    
    // Identification Documents (optional)
    {
      name: 'documents',
      type: 'group',
      fields: [
        {
          name: 'aadharNumber',
          type: 'text',
          admin: {
            description: 'Aadhar card number',
          },
        },
        {
          name: 'panNumber',
          type: 'text',
          admin: {
            description: 'PAN card number',
          },
        },
        {
          name: 'passportNumber',
          type: 'text',
          admin: {
            description: 'Passport number',
          },
        },
        {
          name: 'drivingLicense',
          type: 'text',
          admin: {
            description: 'Driving license number',
          },
        },
      ],
    },
    
    // Additional Information (optional)
    {
      name: 'additionalInfo',
      type: 'group',
      fields: [
        {
          name: 'hobbies',
          type: 'textarea',
          admin: {
            description: 'Hobbies and interests',
          },
        },
        {
          name: 'skills',
          type: 'textarea',
          admin: {
            description: 'Skills and competencies',
          },
        },
        {
          name: 'experience',
          type: 'textarea',
          admin: {
            description: 'Work experience or internships',
          },
        },
        {
          name: 'goals',
          type: 'textarea',
          admin: {
            description: 'Career goals and aspirations',
          },
        },
        {
          name: 'notes',
          type: 'textarea',
          admin: {
            description: 'Additional notes',
          },
        },
      ],
    },
    
    // System fields
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Whether this student detail record is active',
      },
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: 'User who created this record',
      },
    },
    {
      name: 'updatedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: 'User who last updated this record',
      },
    },
  ],
  timestamps: true,
}

export default StudentDetails
