import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const TaxComponents: CollectionConfig = {
  slug: 'tax-components',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'code', 'rate', 'type', 'isActive', 'createdAt'],
    group: 'Tax Management',
  },
  access: {
    read: () => true, // All users can read tax components
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      maxLength: 20,
      index: true,
      validate: (val) => {
        if (!/^[A-Z0-9_]+$/.test(val)) {
          return 'Tax code must contain only uppercase letters, numbers, and underscores'
        }
        return true
      },
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'SGST (State GST)', value: 'sgst' },
        { label: 'CGST (Central GST)', value: 'cgst' },
        { label: 'IGST (Integrated GST)', value: 'igst' },
        { label: 'VAT (Value Added Tax)', value: 'vat' },
        { label: 'Sales Tax', value: 'sales_tax' },
        { label: 'Income Tax', value: 'income_tax' },
        { label: 'Service Tax', value: 'service_tax' },
        { label: 'Custom Tax', value: 'custom' },
      ],
      index: true,
    },
    {
      name: 'rate',
      type: 'number',
      required: true,
      min: 0,
      max: 100,
      admin: {
        description: 'Tax rate as percentage (e.g., 9 for 9%)',
      },
    },
    {
      name: 'applicableRegions',
      type: 'array',
      fields: [
        {
          name: 'country',
          type: 'relationship',
          relationTo: 'countries',
          required: true,
        },
        {
          name: 'states',
          type: 'relationship',
          relationTo: 'states',
          hasMany: true,
        },
        {
          name: 'isDefault',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Default tax for this region',
          },
        },
      ],
    },
    {
      name: 'calculationMethod',
      type: 'select',
      required: true,
      defaultValue: 'percentage',
      options: [
        { label: 'Percentage', value: 'percentage' },
        { label: 'Fixed Amount', value: 'fixed' },
        { label: 'Tiered', value: 'tiered' },
      ],
    },
    {
      name: 'tieredRates',
      type: 'array',
      admin: {
        condition: (data) => data.calculationMethod === 'tiered',
      },
      fields: [
        {
          name: 'minAmount',
          type: 'number',
          required: true,
        },
        {
          name: 'maxAmount',
          type: 'number',
        },
        {
          name: 'rate',
          type: 'number',
          required: true,
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'effectiveFrom',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
    },
    {
      name: 'effectiveTo',
      type: 'date',
    },
    {
      name: 'priority',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Higher priority taxes are applied first',
      },
    },
    {
      name: 'metadata',
      type: 'json',
      admin: {
        description: 'Additional tax metadata and configuration',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create' || operation === 'update') {
          // Ensure tax code is uppercase
          if (data.code) {
            data.code = data.code.toUpperCase()
          }
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default TaxComponents
