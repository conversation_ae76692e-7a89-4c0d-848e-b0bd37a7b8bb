import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const TaxGroups: CollectionConfig = {
  slug: 'tax-groups',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'code', 'totalRate', 'componentsCount', 'isActive'],
    group: 'Tax Management',
  },
  access: {
    read: () => true,
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      maxLength: 20,
      index: true,
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'taxComponents',
      type: 'array',
      required: true,
      minRows: 1,
      fields: [
        {
          name: 'component',
          type: 'relationship',
          relationTo: 'tax-components',
          required: true,
        },
        {
          name: 'customRate',
          type: 'number',
          admin: {
            description: 'Override component rate (optional)',
          },
        },
        {
          name: 'isOptional',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
    },
    {
      name: 'totalRate',
      type: 'number',
      admin: {
        readOnly: true,
        description: 'Calculated total rate of all components',
      },
    },
    {
      name: 'applicableScenarios',
      type: 'array',
      fields: [
        {
          name: 'scenario',
          type: 'select',
          required: true,
          options: [
            { label: 'Intra-State (Same State)', value: 'intra_state' },
            { label: 'Inter-State (Different States)', value: 'inter_state' },
            { label: 'International', value: 'international' },
            { label: 'B2B Transaction', value: 'b2b' },
            { label: 'B2C Transaction', value: 'b2c' },
            { label: 'Export', value: 'export' },
            { label: 'Import', value: 'import' },
          ],
        },
        {
          name: 'fromCountry',
          type: 'relationship',
          relationTo: 'countries',
        },
        {
          name: 'toCountry',
          type: 'relationship',
          relationTo: 'countries',
        },
        {
          name: 'fromState',
          type: 'relationship',
          relationTo: 'states',
        },
        {
          name: 'toState',
          type: 'relationship',
          relationTo: 'states',
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'isDefault',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Default tax group for applicable scenarios',
      },
    },
    {
      name: 'effectiveFrom',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
    },
    {
      name: 'effectiveTo',
      type: 'date',
    },
    {
      name: 'priority',
      type: 'number',
      defaultValue: 0,
    },
  ],
  hooks: {
    beforeChange: [
      async ({ req, operation, data }) => {
        if (operation === 'create' || operation === 'update') {
          // Calculate total rate from components
          if (data.taxComponents && data.taxComponents.length > 0) {
            let totalRate = 0
            
            for (const componentData of data.taxComponents) {
              if (componentData.customRate) {
                totalRate += componentData.customRate
              } else if (componentData.component) {
                // Fetch component rate from database
                const component = await req.payload.findByID({
                  collection: 'tax-components',
                  id: componentData.component
                })
                if (component) {
                  totalRate += component.rate
                }
              }
            }
            
            data.totalRate = totalRate
          }
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default TaxGroups
