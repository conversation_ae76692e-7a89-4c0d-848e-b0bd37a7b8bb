import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const TaxRules: CollectionConfig = {
  slug: 'tax-rules',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'taxGroup', 'priority', 'isActive', 'createdAt'],
    group: 'Tax Management',
  },
  access: {
    read: () => true,
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'taxGroup',
      type: 'relationship',
      relationTo: 'tax-groups',
      required: true,
      index: true,
    },
    {
      name: 'conditions',
      type: 'group',
      fields: [
        {
          name: 'transactionType',
          type: 'select',
          options: [
            { label: 'Course Purchase', value: 'course_purchase' },
            { label: 'Subscription', value: 'subscription' },
            { label: 'Certification', value: 'certification' },
            { label: 'Live Class', value: 'live_class' },
            { label: 'Exam Fee', value: 'exam_fee' },
            { label: 'Material Purchase', value: 'material_purchase' },
          ],
        },
        {
          name: 'customerType',
          type: 'select',
          options: [
            { label: 'Individual', value: 'individual' },
            { label: 'Business', value: 'business' },
            { label: 'Educational Institution', value: 'educational' },
            { label: 'Government', value: 'government' },
          ],
        },
        {
          name: 'minAmount',
          type: 'number',
          min: 0,
          admin: {
            description: 'Minimum transaction amount for this rule',
          },
        },
        {
          name: 'maxAmount',
          type: 'number',
          admin: {
            description: 'Maximum transaction amount for this rule',
          },
        },
        {
          name: 'customerCountry',
          type: 'relationship',
          relationTo: 'countries',
          admin: {
            description: 'Customer location country',
          },
        },
        {
          name: 'customerState',
          type: 'relationship',
          relationTo: 'states',
          admin: {
            description: 'Customer location state',
          },
        },
        {
          name: 'instituteCountry',
          type: 'relationship',
          relationTo: 'countries',
          admin: {
            description: 'Institute location country',
          },
        },
        {
          name: 'instituteState',
          type: 'relationship',
          relationTo: 'states',
          admin: {
            description: 'Institute location state',
          },
        },
        {
          name: 'locationScenario',
          type: 'select',
          options: [
            { label: 'Same State', value: 'same_state' },
            { label: 'Different State (Same Country)', value: 'different_state' },
            { label: 'Different Country', value: 'different_country' },
          ],
          admin: {
            description: 'Location-based tax scenario',
          },
        },
        {
          name: 'conditionEffectiveFrom',
          type: 'date',
          admin: {
            description: 'Date from which this condition is effective',
          },
        },
        {
          name: 'conditionEffectiveTo',
          type: 'date',
          admin: {
            description: 'Date until which this condition is effective',
          },
        },
        {
          name: 'applicableDays',
          type: 'select',
          hasMany: true,
          options: [
            { label: 'Monday', value: '1' },
            { label: 'Tuesday', value: '2' },
            { label: 'Wednesday', value: '3' },
            { label: 'Thursday', value: '4' },
            { label: 'Friday', value: '5' },
            { label: 'Saturday', value: '6' },
            { label: 'Sunday', value: '0' },
          ],
          admin: {
            description: 'Days of week when this rule applies',
          },
        },
      ],
    },
    {
      name: 'exemptions',
      type: 'array',
      fields: [
        {
          name: 'condition',
          type: 'select',
          required: true,
          options: [
            { label: 'Student Discount', value: 'student_discount' },
            { label: 'Educational Institution', value: 'educational_exemption' },
            { label: 'Government Entity', value: 'government_exemption' },
            { label: 'Export Transaction', value: 'export_exemption' },
            { label: 'Amount Below Threshold', value: 'threshold_exemption' },
            { label: 'Special Category', value: 'special_category' },
          ],
        },
        {
          name: 'exemptionPercentage',
          type: 'number',
          min: 0,
          max: 100,
          defaultValue: 100,
          admin: {
            description: 'Percentage of tax to exempt (100 = full exemption)',
          },
        },
        {
          name: 'thresholdAmount',
          type: 'number',
          admin: {
            condition: (data, siblingData) => siblingData.condition === 'threshold_exemption',
          },
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'priority',
      type: 'number',
      required: true,
      defaultValue: 0,
      admin: {
        description: 'Higher priority rules are evaluated first',
      },
    },
    {
      name: 'effectiveFrom',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
    },
    {
      name: 'effectiveTo',
      type: 'date',
    },
  ],
  timestamps: true,
}

export default TaxRules
