import { CollectionConfig } from 'payload/types'
import { canManageThemes, canViewThemes } from '../access/index'

const Themes: CollectionConfig = {
  slug: 'themes',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'type', 'category', 'isActive', 'usageCount'],
    group: 'Theme Management',
  },
  access: {
    read: canViewThemes, // All users can view active themes
    create: canManageThemes, // Only admins can create themes
    update: canManageThemes, // Only admins can update themes
    delete: canManageThemes, // Only admins can delete themes
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      admin: {
        description: 'Display name of the theme',
      },
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      index: true,
      admin: {
        description: 'URL-friendly identifier for the theme',
      },
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Platform Theme', value: 'platform' },
        { label: 'Institute Theme', value: 'institute' },
      ],
      index: true,
      admin: {
        description: 'Type of theme - Platform for main site, Institute for institute websites',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        // Platform categories
        { label: 'SaaS Modern', value: 'saas_modern' },
        { label: 'SaaS Corporate', value: 'saas_corporate' },
        { label: 'SaaS Startup', value: 'saas_startup' },
        { label: 'SaaS Minimal', value: 'saas_minimal' },
        // Institute categories
        { label: 'Education Modern', value: 'education_modern' },
        { label: 'Education Classic', value: 'education_classic' },
        { label: 'Coaching Professional', value: 'coaching_professional' },
        { label: 'University Classic', value: 'university_classic' },
        { label: 'Online Academy', value: 'online_academy' },
        { label: 'Training Center', value: 'training_center' },
      ],
      index: true,
      admin: {
        description: 'Theme category for organization and filtering',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      maxLength: 500,
      admin: {
        description: 'Brief description of the theme and its features',
      },
    },
    {
      name: 'version',
      type: 'text',
      required: true,
      defaultValue: '1.0.0',
      admin: {
        description: 'Theme version number',
      },
    },
    {
      name: 'previewImage',
      type: 'upload',
      relationTo: 'media',
      required: false,
      admin: {
        description: 'Preview thumbnail (400x300px recommended)',
      },
    },
    {
      name: 'demoImage',
      type: 'upload',
      relationTo: 'media',
      required: false,
      admin: {
        description: 'Full demo screenshot (1200x800px recommended)',
      },
    },
    {
      name: 'screenshots',
      type: 'array',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'text',
        },
      ],
      admin: {
        description: 'Additional screenshots showcasing theme features',
      },
    },
    {
      name: 'colors',
      type: 'group',
      fields: [
        {
          name: 'primary',
          type: 'text',
          required: true,
          defaultValue: '#3b82f6',
          admin: {
            description: 'Primary brand color (hex code)',
          },
        },
        {
          name: 'secondary',
          type: 'text',
          required: true,
          defaultValue: '#64748b',
          admin: {
            description: 'Secondary color (hex code)',
          },
        },
        {
          name: 'accent',
          type: 'text',
          required: true,
          defaultValue: '#10b981',
          admin: {
            description: 'Accent color for highlights (hex code)',
          },
        },
        {
          name: 'background',
          type: 'text',
          required: true,
          defaultValue: '#ffffff',
          admin: {
            description: 'Background color (hex code)',
          },
        },
        {
          name: 'text',
          type: 'text',
          required: true,
          defaultValue: '#1f2937',
          admin: {
            description: 'Primary text color (hex code)',
          },
        },
        {
          name: 'muted',
          type: 'text',
          defaultValue: '#6b7280',
          admin: {
            description: 'Muted text color (hex code)',
          },
        },
        {
          name: 'border',
          type: 'text',
          defaultValue: '#e5e7eb',
          admin: {
            description: 'Border color (hex code)',
          },
        },
      ],
      admin: {
        description: 'Default color scheme for the theme',
      },
    },
    {
      name: 'fonts',
      type: 'group',
      fields: [
        {
          name: 'heading',
          type: 'text',
          required: true,
          defaultValue: 'Inter',
          admin: {
            description: 'Font family for headings',
          },
        },
        {
          name: 'body',
          type: 'text',
          required: true,
          defaultValue: 'Inter',
          admin: {
            description: 'Font family for body text',
          },
        },
        {
          name: 'mono',
          type: 'text',
          defaultValue: 'Fira Code',
          admin: {
            description: 'Monospace font for code',
          },
        },
      ],
      admin: {
        description: 'Typography settings for the theme',
      },
    },
    {
      name: 'features',
      type: 'array',
      fields: [
        {
          name: 'feature',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'List of theme features and capabilities',
      },
    },
    {
      name: 'suitableFor',
      type: 'array',
      fields: [
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'Coaching Centers', value: 'coaching_centers' },
            { label: 'Online Academies', value: 'online_academies' },
            { label: 'Universities', value: 'universities' },
            { label: 'Training Institutes', value: 'training_institutes' },
            { label: 'Skill Development', value: 'skill_development' },
            { label: 'Professional Courses', value: 'professional_courses' },
            { label: 'Exam Preparation', value: 'exam_preparation' },
            { label: 'Corporate Training', value: 'corporate_training' },
          ],
        },
      ],
      admin: {
        description: 'Types of institutes this theme is suitable for',
        condition: (data) => data.type === 'institute',
      },
    },
    {
      name: 'customizableElements',
      type: 'json',
      admin: {
        description: 'JSON configuration for customizable theme elements',
      },
    },
    {
      name: 'usageCount',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
        description: 'Number of institutes using this theme',
      },
    },
    {
      name: 'rating',
      type: 'group',
      fields: [
        {
          name: 'average',
          type: 'number',
          defaultValue: 0,
          min: 0,
          max: 5,
          admin: { 
            readOnly: true,
            description: 'Average rating from users',
          },
        },
        {
          name: 'count',
          type: 'number',
          defaultValue: 0,
          admin: { 
            readOnly: true,
            description: 'Total number of ratings',
          },
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Whether this theme is available for selection',
      },
    },
    {
      name: 'isDefault',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Set as default theme for new institutes',
      },
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this theme in the theme gallery',
      },
    },
    {
      name: 'demoUrl',
      type: 'text',
      admin: {
        description: 'URL to live demo of the theme',
      },
    },
    {
      name: 'documentationUrl',
      type: 'text',
      admin: {
        description: 'URL to theme documentation',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Auto-generate slug from name if not provided
          if (!data.slug && data.name) {
            data.slug = data.name
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/(^-|-$)/g, '')
          }
        }
        
        // Ensure only one default theme per type
        if (data.isDefault && data.type) {
          // This would need to be implemented to check existing defaults
          // and update them when a new default is set
        }
        
        return data
      },
    ],
  },
  timestamps: true,
}

export default Themes
