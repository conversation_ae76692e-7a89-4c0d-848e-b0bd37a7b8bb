import { CollectionConfig } from 'payload/types'
import { isAdmin, isInstituteAdmin } from '../access/index'

const UserPermissions: CollectionConfig = {
  slug: 'user-permissions',
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['user', 'permission', 'type', 'status', 'createdAt'],
    group: 'Access Control',
  },
  access: {
    read: ({ req: { user } }) => {
      if (!user) return false

      // Use legacyRole field for access control
      if (user.legacyRole === 'super_admin') return true

      if (user.legacyRole === 'institute_admin') {
        return {
          or: [
            { 'user.institute': { equals: user.institute } },
            { user: { equals: user.id } }
          ]
        }
      }

      // Users can read their own permissions
      return { user: { equals: user.id } }
    },
    create: ({ req: { user } }) => {
      if (!user) return false
      return user.legacyRole === 'super_admin' || user.legacyRole === 'institute_admin'
    },
    update: ({ req: { user } }) => {
      if (!user) return false
      return user.legacyRole === 'super_admin' || user.legacyRole === 'institute_admin'
    },
    delete: ({ req: { user } }) => {
      if (!user) return false
      return user.legacyRole === 'super_admin' || user.legacyRole === 'institute_admin'
    }
  },
  fields: [
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      index: true,
    },
    {
      name: 'permission',
      type: 'relationship',
      relationTo: 'permissions',
      required: true,
      index: true,
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Grant Permission', value: 'grant' },
        { label: 'Revoke Permission', value: 'revoke' },
        { label: 'Override Permission', value: 'override' }
      ],
      defaultValue: 'grant'
    },
    {
      name: 'scope',
      type: 'select',
      required: true,
      options: [
        { label: 'Platform', value: 'platform' },
        { label: 'Institute', value: 'institute' },
        { label: 'Branch', value: 'branch' },
        { label: 'Self', value: 'self' }
      ],
      defaultValue: 'self'
    },
    {
      name: 'scopeTarget',
      type: 'group',
      fields: [
        {
          name: 'institute',
          type: 'relationship',
          relationTo: 'institutes',
          admin: {
            condition: (data) => data.scope === 'institute' || data.scope === 'branch'
          }
        },
        {
          name: 'branch',
          type: 'relationship',
          relationTo: 'branches',
          admin: {
            condition: (data) => data.scope === 'branch'
          }
        }
      ]
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        { label: 'Pending Approval', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' },
        { label: 'Revoked', value: 'revoked' }
      ],
      defaultValue: 'pending'
    },
    {
      name: 'approvalWorkflow',
      type: 'group',
      fields: [
        {
          name: 'requestedBy',
          type: 'relationship',
          relationTo: 'users',
          required: true,
        },
        {
          name: 'approvedBy',
          type: 'relationship',
          relationTo: 'users',
          admin: {
            condition: (data) => data.status === 'approved'
          }
        },
        {
          name: 'rejectedBy',
          type: 'relationship',
          relationTo: 'users',
          admin: {
            condition: (data) => data.status === 'rejected'
          }
        },
        {
          name: 'approvalDate',
          type: 'date',
          admin: {
            condition: (data) => data.status === 'approved'
          }
        },
        {
          name: 'rejectionDate',
          type: 'date',
          admin: {
            condition: (data) => data.status === 'rejected'
          }
        },
        {
          name: 'reason',
          type: 'textarea',
          maxLength: 500,
          admin: {
            description: 'Reason for approval/rejection'
          }
        }
      ]
    },
    {
      name: 'effectivePeriod',
      type: 'group',
      fields: [
        {
          name: 'startDate',
          type: 'date',
          defaultValue: () => new Date(),
        },
        {
          name: 'endDate',
          type: 'date',
          admin: {
            description: 'Leave empty for permanent permission'
          }
        }
      ]
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'metadata',
      type: 'group',
      fields: [
        {
          name: 'autoApproved',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Permission was automatically approved'
          }
        },
        {
          name: 'inheritedFromRole',
          type: 'relationship',
          relationTo: 'roles',
          admin: {
            description: 'Role this permission was inherited from'
          }
        },
        {
          name: 'notes',
          type: 'textarea',
          maxLength: 1000,
        }
      ]
    }
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Set requestedBy to current user
          if (!data.approvalWorkflow?.requestedBy) {
            data.approvalWorkflow = {
              ...data.approvalWorkflow,
              requestedBy: req.user?.id
            }
          }

          // Auto-approve for super admin
          if (req.user?.role === 'super_admin') {
            data.status = 'approved'
            data.approvalWorkflow = {
              ...data.approvalWorkflow,
              approvedBy: req.user.id,
              approvalDate: new Date()
            }
            data.metadata = {
              ...data.metadata,
              autoApproved: true
            }
          }
        }

        if (operation === 'update') {
          // Set approval/rejection details
          if (data.status === 'approved' && !data.approvalWorkflow?.approvedBy) {
            data.approvalWorkflow = {
              ...data.approvalWorkflow,
              approvedBy: req.user?.id,
              approvalDate: new Date()
            }
          }

          if (data.status === 'rejected' && !data.approvalWorkflow?.rejectedBy) {
            data.approvalWorkflow = {
              ...data.approvalWorkflow,
              rejectedBy: req.user?.id,
              rejectionDate: new Date()
            }
          }
        }

        return data
      }
    ]
  },
  timestamps: true,
}

export default UserPermissions
