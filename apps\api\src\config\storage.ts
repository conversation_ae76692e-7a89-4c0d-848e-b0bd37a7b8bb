import { S3Client, S3ClientConfig } from '@aws-sdk/client-s3'
import { CloudflareR2Config } from '../types/storage'

/**
 * Cloud Storage Configuration for Course Builder System
 * Supports both AWS S3 and Cloudflare R2 with automatic failover
 */

export interface StorageConfig {
  provider: 'aws-s3' | 'cloudflare-r2' | 'local'
  bucket: string
  region: string
  endpoint?: string
  accessKeyId: string
  secretAccessKey: string
  publicUrl?: string
  cdnUrl?: string
  maxFileSize: number
  allowedMimeTypes: string[]
  enableVersioning: boolean
  enableEncryption: boolean
  lifecyclePolicies: {
    deleteAfterDays?: number
    transitionToIA?: number
    transitionToGlacier?: number
  }
}

// Default storage configuration
const defaultConfig: Partial<StorageConfig> = {
  maxFileSize: 100 * 1024 * 1024, // 100MB
  allowedMimeTypes: [
    // Images
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    
    // Videos
    'video/mp4',
    'video/webm',
    'video/quicktime',
    'video/x-msvideo',
    
    // Audio
    'audio/mpeg',
    'audio/wav',
    'audio/ogg',
    'audio/mp4',
    
    // Documents
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
    
    // Archives
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed'
  ],
  enableVersioning: true,
  enableEncryption: true,
  lifecyclePolicies: {
    deleteAfterDays: 365,
    transitionToIA: 30,
    transitionToGlacier: 90
  }
}

// AWS S3 Configuration
export const createS3Config = (): StorageConfig => {
  return {
    ...defaultConfig,
    provider: 'aws-s3',
    bucket: process.env.AWS_S3_BUCKET || 'course-builder-files',
    region: process.env.AWS_REGION || 'us-east-1',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
    publicUrl: process.env.AWS_S3_PUBLIC_URL,
    cdnUrl: process.env.AWS_CLOUDFRONT_URL
  } as StorageConfig
}

// Cloudflare R2 Configuration
export const createR2Config = (): StorageConfig => {
  return {
    ...defaultConfig,
    provider: 'cloudflare-r2',
    bucket: process.env.R2_BUCKET || 'course-builder-files',
    region: process.env.R2_REGION || 'auto',
    endpoint: process.env.R2_ENDPOINT || `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    accessKeyId: process.env.R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '',
    publicUrl: process.env.R2_PUBLIC_URL,
    cdnUrl: process.env.R2_CDN_URL
  } as StorageConfig
}

// Local Storage Configuration (for development)
export const createLocalConfig = (): StorageConfig => {
  return {
    ...defaultConfig,
    provider: 'local',
    bucket: 'uploads',
    region: 'local',
    accessKeyId: 'local',
    secretAccessKey: 'local',
    publicUrl: process.env.LOCAL_STORAGE_URL || 'http://localhost:3001/uploads'
  } as StorageConfig
}

// Get active storage configuration
export const getStorageConfig = (): StorageConfig => {
  const provider = process.env.STORAGE_PROVIDER as StorageConfig['provider'] || 'local'
  
  switch (provider) {
    case 'aws-s3':
      return createS3Config()
    case 'cloudflare-r2':
      return createR2Config()
    case 'local':
      return createLocalConfig()
    default:
      console.warn(`Unknown storage provider: ${provider}, falling back to local`)
      return createLocalConfig()
  }
}

// Create S3 Client for AWS S3 or Cloudflare R2
export const createS3Client = (config: StorageConfig): S3Client => {
  const clientConfig: S3ClientConfig = {
    region: config.region,
    credentials: {
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.secretAccessKey
    }
  }

  // Add endpoint for Cloudflare R2
  if (config.provider === 'cloudflare-r2' && config.endpoint) {
    clientConfig.endpoint = config.endpoint
  }

  return new S3Client(clientConfig)
}

// Validate storage configuration
export const validateStorageConfig = (config: StorageConfig): { valid: boolean; errors: string[] } => {
  const errors: string[] = []

  if (!config.bucket) {
    errors.push('Bucket name is required')
  }

  if (!config.accessKeyId) {
    errors.push('Access Key ID is required')
  }

  if (!config.secretAccessKey) {
    errors.push('Secret Access Key is required')
  }

  if (config.provider === 'cloudflare-r2' && !config.endpoint) {
    errors.push('Endpoint is required for Cloudflare R2')
  }

  if (config.maxFileSize <= 0) {
    errors.push('Max file size must be greater than 0')
  }

  if (!config.allowedMimeTypes || config.allowedMimeTypes.length === 0) {
    errors.push('At least one allowed MIME type is required')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// Storage health check
export const checkStorageHealth = async (config: StorageConfig): Promise<{ healthy: boolean; error?: string }> => {
  try {
    if (config.provider === 'local') {
      // For local storage, just check if directory exists
      const fs = await import('fs/promises')
      const path = await import('path')
      const uploadDir = path.join(process.cwd(), 'uploads')
      
      try {
        await fs.access(uploadDir)
        return { healthy: true }
      } catch {
        await fs.mkdir(uploadDir, { recursive: true })
        return { healthy: true }
      }
    }

    // For cloud storage, test connection
    const s3Client = createS3Client(config)
    const { HeadBucketCommand } = await import('@aws-sdk/client-s3')
    
    await s3Client.send(new HeadBucketCommand({ Bucket: config.bucket }))
    return { healthy: true }
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown storage error'
    }
  }
}

// File type validation
export const validateFileType = (filename: string, mimeType: string, config: StorageConfig): boolean => {
  // Check MIME type
  if (!config.allowedMimeTypes.includes(mimeType)) {
    return false
  }

  // Additional file extension validation
  const ext = filename.toLowerCase().split('.').pop()
  const allowedExtensions = {
    'image/jpeg': ['jpg', 'jpeg'],
    'image/png': ['png'],
    'image/gif': ['gif'],
    'image/webp': ['webp'],
    'image/svg+xml': ['svg'],
    'video/mp4': ['mp4'],
    'video/webm': ['webm'],
    'video/quicktime': ['mov'],
    'video/x-msvideo': ['avi'],
    'audio/mpeg': ['mp3'],
    'audio/wav': ['wav'],
    'audio/ogg': ['ogg'],
    'audio/mp4': ['m4a'],
    'application/pdf': ['pdf'],
    'application/msword': ['doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['docx'],
    'application/vnd.ms-excel': ['xls'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['xlsx'],
    'application/vnd.ms-powerpoint': ['ppt'],
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['pptx'],
    'text/plain': ['txt'],
    'text/csv': ['csv'],
    'application/zip': ['zip'],
    'application/x-rar-compressed': ['rar'],
    'application/x-7z-compressed': ['7z']
  }

  const validExtensions = allowedExtensions[mimeType as keyof typeof allowedExtensions]
  return validExtensions ? validExtensions.includes(ext || '') : false
}

// Get file category based on MIME type
export const getFileCategory = (mimeType: string): 'image' | 'video' | 'audio' | 'document' | 'archive' | 'other' => {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType.startsWith('video/')) return 'video'
  if (mimeType.startsWith('audio/')) return 'audio'
  if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('sheet') || mimeType.includes('presentation') || mimeType.startsWith('text/')) return 'document'
  if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('7z')) return 'archive'
  return 'other'
}

export default {
  getStorageConfig,
  createS3Client,
  validateStorageConfig,
  checkStorageHealth,
  validateFileType,
  getFileCategory,
  createS3Config,
  createR2Config,
  createLocalConfig
}
