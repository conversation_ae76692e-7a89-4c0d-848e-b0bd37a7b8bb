import { requireAuth } from '../../middleware/auth'
import { requirePermission, Permission } from '../../middleware/rbac'
import { tenantContextMiddleware } from '../../middleware/tenant-context'
import { logPermissionCheck } from '../../middleware/permission-audit'
import { documentProcessingService } from '../../services/document-processing'
import type { Endpoint } from 'payload'

/**
 * Document Processing API Endpoints
 * Handles document upload, processing, security scanning, and preview generation
 */

export const documentProcessingEndpoints: Endpoint[] = [
  // Process Document
  {
    path: '/admin/documents/process',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_CREATE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_CREATE,
            'document',
            true,
            { action: 'process-document', req }
          )

          // Check if file was uploaded
          if (!req.files || !req.files.document) {
            return res.status(400).json({
              success: false,
              error: 'No document file provided'
            })
          }

          const documentFile = Array.isArray(req.files.document) 
            ? req.files.document[0] 
            : req.files.document

          if (!documentFile.data || !documentFile.name) {
            return res.status(400).json({
              success: false,
              error: 'Invalid document file'
            })
          }

          // Parse processing options from request body
          const options = {
            generatePreview: req.body.generatePreview === 'true',
            generateThumbnail: req.body.generateThumbnail !== 'false',
            extractText: req.body.extractText === 'true',
            validateSecurity: req.body.validateSecurity !== 'false',
            extractMetadata: req.body.extractMetadata !== 'false',
            maxPreviewPages: parseInt(req.body.maxPreviewPages) || 5
          }

          // Start document processing
          const result = await documentProcessingService.processDocument(
            req.user!,
            documentFile.data,
            documentFile.name,
            options
          )

          if (!result.success) {
            return res.status(500).json({
              success: false,
              error: result.error || 'Document processing failed'
            })
          }

          res.json({
            success: true,
            jobId: result.jobId,
            message: 'Document processing started'
          })
        } catch (error) {
          console.error('Error processing document:', error)
          res.status(500).json({
            success: false,
            error: 'Internal server error'
          })
        }
      }]
  },

  // Get Processing Status
  {
    path: '/admin/documents/process/:jobId/status',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_READ),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { jobId } = req.params

          logPermissionCheck(
            req.user!,
            Permission.CONTENT_READ,
            'document',
            true,
            { action: 'get-processing-status', jobId, req }
          )

          const job = documentProcessingService.getJobStatus(jobId)

          if (!job) {
            return res.status(404).json({
              success: false,
              error: 'Processing job not found'
            })
          }

          res.json({
            success: true,
            job: {
              id: job.id,
              status: job.status,
              progress: job.progress,
              startTime: job.startTime,
              endTime: job.endTime,
              error: job.error
            }
          })
        } catch (error) {
          console.error('Error getting processing status:', error)
          res.status(500).json({
            success: false,
            error: 'Internal server error'
          })
        }
      }]
  },

  // Get Processing Result
  {
    path: '/admin/documents/process/:jobId/result',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_READ),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { jobId } = req.params

          logPermissionCheck(
            req.user!,
            Permission.CONTENT_READ,
            'document',
            true,
            { action: 'get-processing-result', jobId, req }
          )

          const job = documentProcessingService.getJobStatus(jobId)

          if (!job) {
            return res.status(404).json({
              success: false,
              error: 'Processing job not found'
            })
          }

          if (job.status !== 'completed') {
            return res.status(400).json({
              success: false,
              error: `Processing not completed. Current status: ${job.status}`
            })
          }

          const result = documentProcessingService.getJobResult(jobId)

          if (!result) {
            return res.status(404).json({
              success: false,
              error: 'Processing result not found'
            })
          }

          res.json({
            success: true,
            result: {
              id: result.id,
              originalFile: result.originalFile,
              metadata: result.metadata,
              thumbnail: result.thumbnail,
              preview: result.preview,
              extractedText: result.extractedText,
              securityScan: result.securityScan,
              processingTime: result.processingTime,
              status: result.status
            }
          })
        } catch (error) {
          console.error('Error getting processing result:', error)
          res.status(500).json({
            success: false,
            error: 'Internal server error'
          })
        }
      }]
  },

  // Validate Document Security
  {
    path: '/admin/documents/validate-security',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_CREATE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_CREATE,
            'document',
            true,
            { action: 'validate-document-security', req }
          )

          // Check if file was uploaded
          if (!req.files || !req.files.document) {
            return res.status(400).json({
              success: false,
              error: 'No document file provided'
            })
          }

          const documentFile = Array.isArray(req.files.document) 
            ? req.files.document[0] 
            : req.files.document

          if (!documentFile.data || !documentFile.name) {
            return res.status(400).json({
              success: false,
              error: 'Invalid document file'
            })
          }

          // Quick security validation only
          const result = await documentProcessingService.processDocument(
            req.user!,
            documentFile.data,
            documentFile.name,
            {
              generatePreview: false,
              generateThumbnail: false,
              extractText: false,
              validateSecurity: true,
              extractMetadata: false
            }
          )

          if (!result.success) {
            return res.status(500).json({
              success: false,
              error: result.error || 'Security validation failed'
            })
          }

          // Wait for quick processing to complete
          let attempts = 0
          const maxAttempts = 30 // 30 seconds max wait
          
          while (attempts < maxAttempts) {
            const job = documentProcessingService.getJobStatus(result.jobId)
            
            if (job?.status === 'completed') {
              const processResult = documentProcessingService.getJobResult(result.jobId)
              
              return res.json({
                success: true,
                securityScan: processResult?.securityScan || {
                  isClean: false,
                  threats: ['Scan incomplete'],
                  scanDate: new Date()
                }
              })
            }
            
            if (job?.status === 'failed') {
              return res.status(500).json({
                success: false,
                error: job.error || 'Security validation failed'
              })
            }

            await new Promise(resolve => setTimeout(resolve, 1000))
            attempts++
          }

          res.status(408).json({
            success: false,
            error: 'Security validation timeout'
          })
        } catch (error) {
          console.error('Error validating document security:', error)
          res.status(500).json({
            success: false,
            error: 'Internal server error'
          })
        }
      }]
  },

  // Get Supported Document Types
  {
    path: '/admin/documents/supported-types',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_READ),
      async (req, res) => {
        try {
          const supportedTypes = [
            {
              extension: '.pdf',
              mimeType: 'application/pdf',
              description: 'Portable Document Format',
              maxSize: '100MB',
              features: ['thumbnail', 'preview', 'text_extraction', 'security_scan']
            },
            {
              extension: '.doc',
              mimeType: 'application/msword',
              description: 'Microsoft Word Document (Legacy)',
              maxSize: '50MB',
              features: ['thumbnail', 'security_scan']
            },
            {
              extension: '.docx',
              mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              description: 'Microsoft Word Document',
              maxSize: '50MB',
              features: ['thumbnail', 'text_extraction', 'security_scan']
            },
            {
              extension: '.xls',
              mimeType: 'application/vnd.ms-excel',
              description: 'Microsoft Excel Spreadsheet (Legacy)',
              maxSize: '50MB',
              features: ['thumbnail', 'security_scan']
            },
            {
              extension: '.xlsx',
              mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              description: 'Microsoft Excel Spreadsheet',
              maxSize: '50MB',
              features: ['thumbnail', 'security_scan']
            },
            {
              extension: '.ppt',
              mimeType: 'application/vnd.ms-powerpoint',
              description: 'Microsoft PowerPoint Presentation (Legacy)',
              maxSize: '100MB',
              features: ['thumbnail', 'security_scan']
            },
            {
              extension: '.pptx',
              mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
              description: 'Microsoft PowerPoint Presentation',
              maxSize: '100MB',
              features: ['thumbnail', 'preview', 'security_scan']
            },
            {
              extension: '.txt',
              mimeType: 'text/plain',
              description: 'Plain Text Document',
              maxSize: '10MB',
              features: ['text_extraction', 'security_scan']
            },
            {
              extension: '.rtf',
              mimeType: 'application/rtf',
              description: 'Rich Text Format',
              maxSize: '25MB',
              features: ['text_extraction', 'security_scan']
            }
          ]

          res.json({
            success: true,
            supportedTypes,
            totalTypes: supportedTypes.length
          })
        } catch (error) {
          console.error('Error getting supported types:', error)
          res.status(500).json({
            success: false,
            error: 'Internal server error'
          })
        }
      }]
  }
]
