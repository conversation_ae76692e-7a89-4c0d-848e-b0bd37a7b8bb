import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'
import { requirePermission, Permission } from '../../middleware/rbac'
import { tenantContextMiddleware } from '../../middleware/tenant-context'
import { fileUploadService } from '../../services/file-upload'
import { logPermissionCheck } from '../../middleware/permission-audit'
import multer from 'multer'

/**
 * File Upload API Endpoints for Course Builder System
 * Handles secure file uploads with tenant isolation and permission control
 */

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
    files: 10 // Maximum 10 files per request
  },
  fileFilter: (req, file, cb) => {
    // Basic file type validation (detailed validation in service)
    const allowedMimes = [
      'image/', 'video/', 'audio/', 'application/pdf', 
      'application/msword', 'application/vnd.', 'text/'
    ]
    
    const isAllowed = allowedMimes.some(mime => file.mimetype.startsWith(mime))
    cb(null, isAllowed)
  }
})

const adminFileUploadEndpoints: Endpoint[] = [
  // Generate upload URL for direct client uploads
  {
    path: '/admin/files/upload-url',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_UPLOAD),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { filename, mimeType, size, options = {} } = await req.json()

          if (!filename || !mimeType || !size) {
            return res.status(400).json({
              success: false,
              error: 'Filename, MIME type, and size are required'
            })
          }

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_UPLOAD,
            'file-upload',
            true,
            {
              action: 'generate-upload-url',
              metadata: { filename, mimeType, size },
              req
            }
          )

          const result = await fileUploadService.generateUploadUrl(
            req.user!,
            filename,
            mimeType,
            size,
            options
          )

          if (!result.success) {
            return res.status(400).json(result)
          }

          res.json({
            success: true,
            data: {
              uploadUrl: result.uploadUrl,
              file: result.file
            }
          })
        } catch (error) {
          console.error('Error generating upload URL:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to generate upload URL'
          })
        }
      }]
  },

  // Direct file upload endpoint
  {
    path: '/admin/files/upload',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_UPLOAD),
      tenantContextMiddleware,
      upload.array('files', 10),
      async (req, res) => {
        try {
          const files = req.files as Express.Multer.File[]
          
          if (!files || files.length === 0) {
            return res.status(400).json({
              success: false,
              error: 'No files provided'
            })
          }

          const uploadPromises = files.map(async (file) => {
            // Log permission check for each file
            logPermissionCheck(
              req.user!,
              Permission.CONTENT_UPLOAD,
              'file-upload',
              true,
              {
                action: 'direct-upload',
                metadata: { 
                  filename: file.originalname, 
                  mimeType: file.mimetype, 
                  size: file.size 
                },
                req
              }
            )

            return await fileUploadService.uploadFile(
              req.user!,
              file.buffer,
              file.originalname,
              file.mimetype,
              JSON.parse(req.body.options || '{}')
            )
          })

          const results = await Promise.all(uploadPromises)
          const successful = results.filter(r => r.success)
          const failed = results.filter(r => !r.success)

          res.json({
            success: failed.length === 0,
            data: {
              uploaded: successful.map(r => r.file),
              failed: failed.map(r => ({ error: r.error })),
              summary: {
                total: files.length,
                successful: successful.length,
                failed: failed.length
              }
            }
          })
        } catch (error) {
          console.error('Error uploading files:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to upload files'
          })
        }
      }]
  },

  // Generate download URL
  {
    path: '/admin/files/download-url',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff', 'student']),
      requirePermission(Permission.CONTENT_READ),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { filePath, expiresIn = 3600 } = await req.json()

          if (!filePath) {
            return res.status(400).json({
              success: false,
              error: 'File path is required'
            })
          }

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_READ,
            'file-download',
            true,
            {
              action: 'generate-download-url',
              resourceId: filePath,
              metadata: { expiresIn },
              req
            }
          )

          const result = await fileUploadService.generateDownloadUrl(
            req.user!,
            filePath,
            expiresIn
          )

          if (!result.success) {
            return res.status(403).json(result)
          }

          res.json({
            success: true,
            data: {
              downloadUrl: result.url,
              expiresIn
            }
          })
        } catch (error) {
          console.error('Error generating download URL:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to generate download URL'
          })
        }
      }]
  },

  // Delete file
  {
    path: '/admin/files/:filePath',
    method: 'delete',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_DELETE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { filePath } = req.params

          if (!filePath) {
            return res.status(400).json({
              success: false,
              error: 'File path is required'
            })
          }

          // Decode file path
          const decodedPath = decodeURIComponent(filePath)

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_DELETE,
            'file-delete',
            true,
            {
              action: 'delete-file',
              resourceId: decodedPath,
              req
            }
          )

          const result = await fileUploadService.deleteFile(req.user!, decodedPath)

          if (!result.success) {
            return res.status(403).json(result)
          }

          res.json({
            success: true,
            message: 'File deleted successfully'
          })
        } catch (error) {
          console.error('Error deleting file:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to delete file'
          })
        }
      }]
  },

  // Get file metadata
  {
    path: '/admin/files/metadata',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff', 'student']),
      requirePermission(Permission.CONTENT_READ),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { filePath } = await req.json()

          if (!filePath) {
            return res.status(400).json({
              success: false,
              error: 'File path is required'
            })
          }

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_READ,
            'file-metadata',
            true,
            {
              action: 'get-metadata',
              resourceId: filePath,
              req
            }
          )

          const result = await fileUploadService.getFileMetadata(req.user!, filePath)

          if (!result.success) {
            return res.status(403).json(result)
          }

          res.json({
            success: true,
            data: result.metadata
          })
        } catch (error) {
          console.error('Error getting file metadata:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to get file metadata'
          })
        }
      }]
  },

  // Bulk file operations
  {
    path: '/admin/files/bulk',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_DELETE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { operation, filePaths } = await req.json()

          if (!operation || !filePaths || !Array.isArray(filePaths)) {
            return res.status(400).json({
              success: false,
              error: 'Operation and file paths array are required'
            })
          }

          if (operation === 'delete') {
            const deletePromises = filePaths.map(async (filePath: string) => {
              // Log permission check for each file
              logPermissionCheck(
                req.user!,
                Permission.CONTENT_DELETE,
                'file-bulk-delete',
                true,
                {
                  action: 'bulk-delete',
                  resourceId: filePath,
                  req
                }
              )

              return await fileUploadService.deleteFile(req.user!, filePath)
            })

            const results = await Promise.all(deletePromises)
            const successful = results.filter(r => r.success).length
            const failed = results.filter(r => !r.success).length

            res.json({
              success: failed === 0,
              data: {
                summary: {
                  total: filePaths.length,
                  successful,
                  failed
                }
              }
            })
          } else {
            res.status(400).json({
              success: false,
              error: 'Unsupported bulk operation'
            })
          }
        } catch (error) {
          console.error('Error performing bulk operation:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to perform bulk operation'
          })
        }
      }]
  },

  // Storage health check
  {
    path: '/admin/files/health',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager']),
      requirePermission(Permission.SYSTEM_ADMIN),
      async (req, res) => {
        try {
          const { checkStorageHealth, getStorageConfig } = await import('../../config/storage')
          const config = getStorageConfig()
          const health = await checkStorageHealth(config)

          res.json({
            success: true,
            data: {
              provider: config.provider,
              healthy: health.healthy,
              error: health.error,
              config: {
                bucket: config.bucket,
                region: config.region,
                maxFileSize: config.maxFileSize,
                allowedMimeTypes: config.allowedMimeTypes.length
              }
            }
          })
        } catch (error) {
          console.error('Error checking storage health:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to check storage health'
          })
        }
      }]
  }
]

export default adminFileUploadEndpoints
