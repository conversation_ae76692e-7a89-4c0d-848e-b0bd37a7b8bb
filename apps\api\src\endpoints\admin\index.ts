// Admin API Endpoints
// These endpoints are for institute administrators

// File Upload endpoints (temporarily disabled due to multer dependency and Payload 3.x compatibility)
// import adminFileUploadEndpoints from './file-upload'
// export { adminFileUploadEndpoints }

// Video Processing endpoints (temporarily disabled due to multer dependency)
// import adminVideoProcessingEndpoints from './video-processing'
// export { adminVideoProcessingEndpoints }

// Media Processing endpoints (temporarily disabled due to multer dependency)
// import adminMediaProcessingEndpoints from './media-processing'
// export { adminMediaProcessingEndpoints }

// Document Processing endpoints (temporarily disabled due to Payload 3.x compatibility)
// import adminDocumentProcessingEndpoints from './document-processing'
// export { adminDocumentProcessingEndpoints }

// Media Dashboard endpoints (temporarily disabled due to potential compatibility issues)
// import mediaDashboardEndpoints from './media-dashboard'
// export { mediaDashboardEndpoints }

// Simple working endpoints for testing
import simpleWorkingEndpoints from './simple-working-endpoints'
export { simpleWorkingEndpoints }

// Export all admin endpoints as a combined array
export const allAdminEndpoints = [
  ...simpleWorkingEndpoints,
]
