import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'
import { requirePermission, Permission } from '../../middleware/rbac'
import { tenantContextMiddleware } from '../../middleware/tenant-context'
import { logPermissionCheck } from '../../middleware/permission-audit'
import { videoProcessingService } from '../../services/video-processing'
import { processingQueue } from '../../services/processing-queue'

/**
 * Media Processing Dashboard API Endpoints
 * Provides comprehensive monitoring and management for video and document processing operations
 */

const mediaDashboardEndpoints: Endpoint[] = [
  // Get dashboard overview statistics
  {
    path: '/admin/media-dashboard/overview',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_VIEW),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_VIEW,
            'media-dashboard',
            true,
            { action: 'get-dashboard-overview', req }
          )

          // Get processing queue statistics
          const queueStats = processingQueue.getStats()
          
          // Get system health metrics
          const systemHealth = {
            videoProcessingService: {
              status: 'healthy', // This would be determined by actual health checks
              activeJobs: processingQueue.getActiveJobsCount(),
              queueLength: queueStats.pending,
              averageProcessingTime: queueStats.averageProcessingTime
            },
            documentProcessingService: {
              status: 'healthy',
              activeJobs: 0, // Would be implemented when document processing is added
              queueLength: 0,
              averageProcessingTime: 0
            }
          }

          // Calculate performance metrics
          const performanceMetrics = {
            totalJobsToday: queueStats.total, // This would be filtered by date in real implementation
            completedJobsToday: queueStats.completed,
            failedJobsToday: queueStats.failed,
            averageProcessingTime: queueStats.averageProcessingTime,
            throughput: queueStats.throughput,
            successRate: queueStats.total > 0 ? (queueStats.completed / queueStats.total) * 100 : 0
          }

          // Get recent activity (last 24 hours)
          const recentJobs = processingQueue.getRecentJobs(24) // Get jobs from last 24 hours
          
          res.json({
            success: true,
            data: {
              queueStats,
              systemHealth,
              performanceMetrics,
              recentActivity: recentJobs.slice(0, 10) // Latest 10 jobs for overview
            }
          })
        } catch (error) {
          console.error('Error fetching dashboard overview:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to fetch dashboard overview'
          })
        }
      }]
  },

  // Get detailed processing jobs with filtering and pagination
  {
    path: '/admin/media-dashboard/jobs',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_VIEW),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const url = new URL(req.url || '')
          const page = parseInt(url.searchParams.get('page') || '1')
          const limit = parseInt(url.searchParams.get('limit') || '20')
          const status = url.searchParams.get('status')
          const type = url.searchParams.get('type')
          const priority = url.searchParams.get('priority')
          const sortBy = url.searchParams.get('sortBy') || 'createdAt'
          const sortOrder = url.searchParams.get('sortOrder') || 'desc'
          const search = url.searchParams.get('search')

          logPermissionCheck(
            req.user!,
            Permission.CONTENT_VIEW,
            'media-dashboard',
            true,
            { action: 'get-processing-jobs', req }
          )

          // Get filtered jobs
          const jobs = processingQueue.getJobs({
            status: status as any,
            type: type as any,
            priority: priority as any,
            search,
            page,
            limit,
            sortBy,
            sortOrder: sortOrder as 'asc' | 'desc',
            userId: req.user!.legacyRole === 'super_admin' ? undefined : req.user!.id,
            instituteId: req.user!.legacyRole === 'super_admin' ? undefined : req.user!.institute
          })

          res.json({
            success: true,
            data: jobs
          })
        } catch (error) {
          console.error('Error fetching processing jobs:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to fetch processing jobs'
          })
        }
      }]
  },

  // Get detailed job information
  {
    path: '/admin/media-dashboard/jobs/:jobId',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_VIEW),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { jobId } = req.params

          logPermissionCheck(
            req.user!,
            Permission.CONTENT_VIEW,
            'media-dashboard',
            true,
            { action: 'get-job-details', resourceId: jobId, req }
          )

          const job = processingQueue.getJob(jobId)

          if (!job) {
            return res.status(404).json({
              success: false,
              error: 'Job not found'
            })
          }

          // Check if user has access to this job
          if (req.user!.legacyRole !== 'super_admin' && job.userId !== req.user!.id) {
            return res.status(403).json({
              success: false,
              error: 'Access denied'
            })
          }

          // Get additional details for video processing jobs
          let additionalDetails = null
          if (job.type === 'video') {
            const videoJob = videoProcessingService.getJobStatus(jobId)
            const videoResult = videoProcessingService.getJobResult(jobId)
            additionalDetails = {
              videoProcessing: {
                status: videoJob?.status,
                progress: videoJob?.progress,
                result: videoResult
              }
            }
          }

          res.json({
            success: true,
            data: {
              ...job,
              additionalDetails
            }
          })
        } catch (error) {
          console.error('Error fetching job details:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to fetch job details'
          })
        }
      }]
  },

  // Retry failed job
  {
    path: '/admin/media-dashboard/jobs/:jobId/retry',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_MANAGE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { jobId } = req.params

          logPermissionCheck(
            req.user!,
            Permission.CONTENT_MANAGE,
            'media-dashboard',
            true,
            { action: 'retry-job', resourceId: jobId, req }
          )

          const job = processingQueue.getJob(jobId)

          if (!job) {
            return res.status(404).json({
              success: false,
              error: 'Job not found'
            })
          }

          // Check if user has access to this job
          if (req.user!.legacyRole !== 'super_admin' && job.userId !== req.user!.id) {
            return res.status(403).json({
              success: false,
              error: 'Access denied'
            })
          }

          if (job.status !== 'failed') {
            return res.status(400).json({
              success: false,
              error: 'Only failed jobs can be retried'
            })
          }

          const success = await processingQueue.retryJob(jobId)

          if (success) {
            res.json({
              success: true,
              message: 'Job retry initiated'
            })
          } else {
            res.status(500).json({
              success: false,
              error: 'Failed to retry job'
            })
          }
        } catch (error) {
          console.error('Error retrying job:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to retry job'
          })
        }
      }]
  },

  // Cancel pending job
  {
    path: '/admin/media-dashboard/jobs/:jobId/cancel',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_MANAGE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { jobId } = req.params

          logPermissionCheck(
            req.user!,
            Permission.CONTENT_MANAGE,
            'media-dashboard',
            true,
            { action: 'cancel-job', resourceId: jobId, req }
          )

          const job = processingQueue.getJob(jobId)

          if (!job) {
            return res.status(404).json({
              success: false,
              error: 'Job not found'
            })
          }

          // Check if user has access to this job
          if (req.user!.legacyRole !== 'super_admin' && job.userId !== req.user!.id) {
            return res.status(403).json({
              success: false,
              error: 'Access denied'
            })
          }

          if (!['pending', 'processing'].includes(job.status)) {
            return res.status(400).json({
              success: false,
              error: 'Only pending or processing jobs can be cancelled'
            })
          }

          const success = await processingQueue.cancelJob(jobId)

          if (success) {
            res.json({
              success: true,
              message: 'Job cancelled successfully'
            })
          } else {
            res.status(500).json({
              success: false,
              error: 'Failed to cancel job'
            })
          }
        } catch (error) {
          console.error('Error cancelling job:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to cancel job'
          })
        }
      }]
  },

  // Update job priority
  {
    path: '/admin/media-dashboard/jobs/:jobId/priority',
    method: 'put',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_MANAGE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { jobId } = req.params
          const { priority } = await req.json()

          if (!['low', 'normal', 'high', 'urgent'].includes(priority)) {
            return res.status(400).json({
              success: false,
              error: 'Invalid priority level'
            })
          }

          logPermissionCheck(
            req.user!,
            Permission.CONTENT_MANAGE,
            'media-dashboard',
            true,
            { action: 'update-job-priority', resourceId: jobId, req }
          )

          const job = processingQueue.getJob(jobId)

          if (!job) {
            return res.status(404).json({
              success: false,
              error: 'Job not found'
            })
          }

          // Check if user has access to this job
          if (req.user!.legacyRole !== 'super_admin' && job.userId !== req.user!.id) {
            return res.status(403).json({
              success: false,
              error: 'Access denied'
            })
          }

          if (job.status !== 'pending') {
            return res.status(400).json({
              success: false,
              error: 'Only pending jobs can have their priority updated'
            })
          }

          const success = await processingQueue.updateJobPriority(jobId, priority)

          if (success) {
            res.json({
              success: true,
              message: 'Job priority updated successfully'
            })
          } else {
            res.status(500).json({
              success: false,
              error: 'Failed to update job priority'
            })
          }
        } catch (error) {
          console.error('Error updating job priority:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to update job priority'
          })
        }
      }]
  }
]

export default mediaDashboardEndpoints
