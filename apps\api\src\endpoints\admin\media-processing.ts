import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'
import { requirePermission, Permission } from '../../middleware/rbac'
import { tenantContextMiddleware } from '../../middleware/tenant-context'
import { mediaProcessingService } from '../../services/media-processing'
import { logPermissionCheck } from '../../middleware/permission-audit'

/**
 * Media Processing API Endpoints for Course Builder System
 * Handles image optimization, video transcoding, and thumbnail generation
 */

const adminMediaProcessingEndpoints: Endpoint[] = [
  // Process uploaded media file
  {
    path: '/admin/media/process',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_CREATE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { filePath, mimeType, options = {} } = await req.json()

          if (!filePath || !mimeType) {
            return res.status(400).json({
              success: false,
              error: 'File path and MIME type are required'
            })
          }

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_CREATE,
            'media-processing',
            true,
            {
              action: 'process-media',
              resourceId: filePath,
              metadata: { mimeType, options },
              req
            }
          )

          const result = await mediaProcessingService.processMedia(
            req.user!,
            filePath,
            mimeType,
            options
          )

          if (!result.success) {
            return res.status(400).json(result)
          }

          res.json({
            success: true,
            data: {
              jobId: result.jobId,
              processed: result.processed
            }
          })
        } catch (error) {
          console.error('Error processing media:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to process media'
          })
        }
      }]
  },

  // Get processing job status
  {
    path: '/admin/media/job/:jobId',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_READ),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { jobId } = req.params

          if (!jobId) {
            return res.status(400).json({
              success: false,
              error: 'Job ID is required'
            })
          }

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_READ,
            'media-processing-status',
            true,
            {
              action: 'get-job-status',
              resourceId: jobId,
              req
            }
          )

          const status = mediaProcessingService.getJobStatus(jobId)

          if (!status) {
            return res.status(404).json({
              success: false,
              error: 'Job not found'
            })
          }

          res.json({
            success: true,
            data: {
              jobId,
              status: status.status,
              progress: status.progress
            }
          })
        } catch (error) {
          console.error('Error getting job status:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to get job status'
          })
        }
      }]
  },

  // Batch process multiple media files
  {
    path: '/admin/media/batch-process',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_CREATE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { files, options = {} } = await req.json()

          if (!files || !Array.isArray(files) || files.length === 0) {
            return res.status(400).json({
              success: false,
              error: 'Files array is required'
            })
          }

          // Validate files array
          for (const file of files) {
            if (!file.filePath || !file.mimeType) {
              return res.status(400).json({
                success: false,
                error: 'Each file must have filePath and mimeType'
              })
            }
          }

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_CREATE,
            'media-batch-processing',
            true,
            {
              action: 'batch-process-media',
              metadata: { fileCount: files.length, options },
              req
            }
          )

          const processingPromises = files.map(async (file: any) => {
            try {
              const result = await mediaProcessingService.processMedia(
                req.user!,
                file.filePath,
                file.mimeType,
                { ...options, ...file.options }
              )
              return {
                filePath: file.filePath,
                success: result.success,
                jobId: result.jobId,
                processed: result.processed,
                error: result.error
              }
            } catch (error) {
              return {
                filePath: file.filePath,
                success: false,
                error: error instanceof Error ? error.message : 'Processing failed'
              }
            }
          })

          const results = await Promise.all(processingPromises)
          const successful = results.filter(r => r.success)
          const failed = results.filter(r => !r.success)

          res.json({
            success: failed.length === 0,
            data: {
              results,
              summary: {
                total: files.length,
                successful: successful.length,
                failed: failed.length
              }
            }
          })
        } catch (error) {
          console.error('Error batch processing media:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to batch process media'
          })
        }
      }]
  },

  // Generate thumbnail for existing media
  {
    path: '/admin/media/thumbnail',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_CREATE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { filePath, mimeType, options = {} } = await req.json()

          if (!filePath || !mimeType) {
            return res.status(400).json({
              success: false,
              error: 'File path and MIME type are required'
            })
          }

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_CREATE,
            'thumbnail-generation',
            true,
            {
              action: 'generate-thumbnail',
              resourceId: filePath,
              metadata: { mimeType, options },
              req
            }
          )

          const result = await mediaProcessingService.processMedia(
            req.user!,
            filePath,
            mimeType,
            { ...options, generateThumbnail: true }
          )

          if (!result.success) {
            return res.status(400).json(result)
          }

          res.json({
            success: true,
            data: {
              jobId: result.jobId,
              thumbnail: result.processed?.thumbnail
            }
          })
        } catch (error) {
          console.error('Error generating thumbnail:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to generate thumbnail'
          })
        }
      }]
  },

  // Optimize image
  {
    path: '/admin/media/optimize-image',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_CREATE),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { filePath, options = {} } = await req.json()

          if (!filePath) {
            return res.status(400).json({
              success: false,
              error: 'File path is required'
            })
          }

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_CREATE,
            'image-optimization',
            true,
            {
              action: 'optimize-image',
              resourceId: filePath,
              metadata: { options },
              req
            }
          )

          const result = await mediaProcessingService.processMedia(
            req.user!,
            filePath,
            'image/jpeg', // Assume image for optimization
            { ...options, optimizeImage: true }
          )

          if (!result.success) {
            return res.status(400).json(result)
          }

          res.json({
            success: true,
            data: {
              jobId: result.jobId,
              optimized: result.processed?.optimized
            }
          })
        } catch (error) {
          console.error('Error optimizing image:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to optimize image'
          })
        }
      }]
  },

  // Get media metadata
  {
    path: '/admin/media/metadata',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff', 'student']),
      requirePermission(Permission.CONTENT_READ),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { filePath, mimeType } = await req.json()

          if (!filePath || !mimeType) {
            return res.status(400).json({
              success: false,
              error: 'File path and MIME type are required'
            })
          }

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_READ,
            'media-metadata',
            true,
            {
              action: 'get-media-metadata',
              resourceId: filePath,
              metadata: { mimeType },
              req
            }
          )

          // For now, return basic metadata
          // In a full implementation, this would extract detailed metadata
          res.json({
            success: true,
            data: {
              filePath,
              mimeType,
              category: mimeType.split('/')[0],
              // Additional metadata would be extracted here
            }
          })
        } catch (error) {
          console.error('Error getting media metadata:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to get media metadata'
          })
        }
      }]
  },

  // Media processing health check
  {
    path: '/admin/media/health',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager']),
      requirePermission(Permission.SYSTEM_ADMIN),
      async (req, res) => {
        try {
          // Check if required dependencies are available
          const health = {
            sharp: true,
            ffmpeg: true,
            tempDirectory: true,
            processingJobs: 0
          }

          try {
            const sharp = require('sharp')
            await sharp({ create: { width: 1, height: 1, channels: 3, background: 'red' } })
              .png()
              .toBuffer()
          } catch (error) {
            health.sharp = false
          }

          try {
            const ffmpeg = require('fluent-ffmpeg')
            // Basic ffmpeg availability check would go here
          } catch (error) {
            health.ffmpeg = false
          }

          try {
            const fs = require('fs/promises')
            const path = require('path')
            const tempDir = path.join(process.cwd(), 'temp')
            await fs.access(tempDir)
          } catch (error) {
            health.tempDirectory = false
          }

          res.json({
            success: true,
            data: {
              healthy: health.sharp && health.ffmpeg && health.tempDirectory,
              components: health
            }
          })
        } catch (error) {
          console.error('Error checking media processing health:', error)
          res.status(500).json({
            success: false,
            error: 'Failed to check media processing health'
          })
        }
      }]
  }
]

export default adminMediaProcessingEndpoints
