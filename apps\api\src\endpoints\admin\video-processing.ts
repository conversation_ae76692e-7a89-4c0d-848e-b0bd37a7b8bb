import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'
import { requirePermission, Permission } from '../../middleware/rbac'
import { tenantContextMiddleware } from '../../middleware/tenant-context'
import { videoProcessingService } from '../../services/video-processing'
import { logPermissionCheck } from '../../middleware/permission-audit'
import multer from 'multer'

/**
 * Video Processing API Endpoints for Course Builder System
 * Handles video upload, processing, transcoding, and streaming optimization
 */

// Configure multer for video uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB for videos
    files: 1 // Single video file
  },
  fileFilter: (req, file, cb) => {
    // Only allow video files
    const allowedMimes = [
      'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 
      'video/flv', 'video/webm', 'video/mkv', 'video/m4v'
    ]
    
    const isAllowed = allowedMimes.includes(file.mimetype)
    cb(null, isAllowed)
  }
})

const videoProcessingEndpoints: Endpoint[] = [
  // Upload and process video
  {
    path: '/admin/video/upload',
    method: 'post',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_UPLOAD),
      tenantContextMiddleware,
      upload.single('video'),
      async (req, res) => {
        try {
          const file = req.file as Express.Multer.File
          
          if (!file) {
            return res.status(400).json({
              success: false,
              error: 'No video file provided'
            })
          }

          // Parse processing options from request body
          const options = {
            generateThumbnails: req.body.generateThumbnails !== 'false',
            transcodeToMultipleFormats: req.body.transcodeToMultipleFormats !== 'false',
            optimizeForStreaming: req.body.optimizeForStreaming === 'true',
            generatePreview: req.body.generatePreview === 'true',
            extractSubtitles: req.body.extractSubtitles === 'true',
            addWatermark: req.body.addWatermark === 'true',
            quality: req.body.quality || 'medium',
            thumbnailCount: parseInt(req.body.thumbnailCount) || 5,
            previewDuration: parseInt(req.body.previewDuration) || 30,
            watermarkText: req.body.watermarkText || '',
            watermarkPosition: req.body.watermarkPosition || 'bottom-right'
          }

          // Log permission check
          logPermissionCheck(
            req.user!,
            Permission.CONTENT_UPLOAD,
            'video-processing',
            true,
            {
              action: 'upload-video',
              metadata: { 
                filename: file.originalname, 
                size: file.size,
                mimeType: file.mimetype,
                options 
              },
              req
            }
          )

          // Start video processing
          const result = await videoProcessingService.processVideo(
            req.user!,
            file.buffer,
            file.originalname,
            options
          )

          if (result.success) {
            res.status(202).json({
              success: true,
              message: 'Video processing started',
              jobId: result.jobId,
              statusUrl: `/admin/video/status/${result.jobId}`
            })
          } else {
            res.status(500).json({
              success: false,
              error: result.error || 'Failed to start video processing'
            })
          }
        } catch (error) {
          console.error('Video upload error:', error)
          res.status(500).json({
            success: false,
            error: 'Internal server error'
          })
        }
      }
    ]
  },

  // Get video processing status
  {
    path: '/admin/video/status/:jobId',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_VIEW),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { jobId } = req.params

          if (!jobId) {
            return res.status(400).json({
              success: false,
              error: 'Job ID is required'
            })
          }

          const job = videoProcessingService.getJobStatus(jobId)

          if (!job) {
            return res.status(404).json({
              success: false,
              error: 'Processing job not found'
            })
          }

          res.json({
            success: true,
            job: {
              id: job.id,
              status: job.status,
              progress: job.progress,
              startTime: job.startTime,
              endTime: job.endTime,
              error: job.error
            }
          })
        } catch (error) {
          console.error('Error getting video processing status:', error)
          res.status(500).json({
            success: false,
            error: 'Internal server error'
          })
        }
      }
    ]
  },

  // Get video processing result
  {
    path: '/admin/video/result/:jobId',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_VIEW),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          const { jobId } = req.params

          if (!jobId) {
            return res.status(400).json({
              success: false,
              error: 'Job ID is required'
            })
          }

          const job = videoProcessingService.getJobStatus(jobId)
          const result = videoProcessingService.getJobResult(jobId)

          if (!job) {
            return res.status(404).json({
              success: false,
              error: 'Processing job not found'
            })
          }

          if (job.status !== 'completed') {
            return res.status(202).json({
              success: false,
              error: 'Processing not completed yet',
              status: job.status,
              progress: job.progress
            })
          }

          if (!result) {
            return res.status(404).json({
              success: false,
              error: 'Processing result not found'
            })
          }

          res.json({
            success: true,
            result: {
              id: result.id,
              originalFile: result.originalFile,
              metadata: result.metadata,
              thumbnails: result.thumbnails,
              transcodedVersions: result.transcodedVersions,
              streamingManifest: result.streamingManifest,
              dashManifest: result.dashManifest,
              previewClip: result.previewClip,
              subtitles: result.subtitles,
              watermarkedVideo: result.watermarkedVideo,
              processingTime: result.processingTime,
              status: result.status
            }
          })
        } catch (error) {
          console.error('Error getting video processing result:', error)
          res.status(500).json({
            success: false,
            error: 'Internal server error'
          })
        }
      }
    ]
  },

  // List all video processing jobs for user
  {
    path: '/admin/video/jobs',
    method: 'get',
    handler: [
      requireAuth(['institute_admin', 'branch_manager', 'trainer', 'staff']),
      requirePermission(Permission.CONTENT_VIEW),
      tenantContextMiddleware,
      async (req, res) => {
        try {
          // This would typically query a database for user's jobs
          // For now, return empty array as jobs are stored in memory
          res.json({
            success: true,
            jobs: [],
            message: 'Job history feature coming soon'
          })
        } catch (error) {
          console.error('Error listing video processing jobs:', error)
          res.status(500).json({
            success: false,
            error: 'Internal server error'
          })
        }
      }
    ]
  }
]

export default videoProcessingEndpoints
