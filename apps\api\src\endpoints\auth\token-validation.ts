import { Endpoint } from 'payload/config'
import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'

// POST /api/auth/verify-token
export const verifyTokenEndpoint: Endpoint = {
  path: '/auth/verify-token',
  method: 'post',
  handler: async (req: NextRequest) => {
    try {
      const body = await req.json()
      const { token } = body

      if (!token) {
        return NextResponse.json({ 
          valid: false, 
          error: 'Token is required' 
        }, { status: 400 })
      }

      // Verify JWT token
      const secret = process.env.PAYLOAD_SECRET
      if (!secret) {
        return NextResponse.json({ 
          valid: false, 
          error: 'Server configuration error' 
        }, { status: 500 })
      }

      try {
        const decoded = jwt.verify(token, secret) as any
        
        // Check if token is expired
        const currentTime = Math.floor(Date.now() / 1000)
        if (decoded.exp && currentTime >= decoded.exp) {
          return NextResponse.json({ 
            valid: false, 
            error: 'Token expired',
            expired: true
          }, { status: 401 })
        }

        // Verify user still exists and is active
        const { payload } = req as any
        if (payload) {
          try {
            const user = await payload.findByID({
              collection: 'users',
              id: decoded.id
            })

            if (!user || !user.isActive) {
              return NextResponse.json({ 
                valid: false, 
                error: 'User not found or inactive' 
              }, { status: 401 })
            }

            return NextResponse.json({ 
              valid: true,
              user: {
                id: user.id,
                email: user.email,
                role: user.legacyRole,
                institute: user.institute,
                branch: user.branch
              },
              expiresAt: new Date(decoded.exp * 1000).toISOString()
            })

          } catch (userError) {
            console.error('Error fetching user:', userError)
            return NextResponse.json({ 
              valid: false, 
              error: 'User verification failed' 
            }, { status: 401 })
          }
        }

        // If no payload context, just verify token structure
        return NextResponse.json({ 
          valid: true,
          expiresAt: decoded.exp ? new Date(decoded.exp * 1000).toISOString() : null
        })

      } catch (jwtError) {
        console.error('JWT verification failed:', jwtError)
        return NextResponse.json({ 
          valid: false, 
          error: 'Invalid token' 
        }, { status: 401 })
      }

    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ 
        valid: false, 
        error: 'Internal server error' 
      }, { status: 500 })
    }
  }
}

// POST /api/auth/refresh-token
export const refreshTokenEndpoint: Endpoint = {
  path: '/auth/refresh-token',
  method: 'post',
  handler: async (req: NextRequest) => {
    try {
      const authHeader = req.headers.get('authorization')
      const token = authHeader?.replace('Bearer ', '') || 
                   (await req.json().catch(() => ({})))?.token

      if (!token) {
        return NextResponse.json({ 
          error: 'Token is required' 
        }, { status: 400 })
      }

      const secret = process.env.PAYLOAD_SECRET
      if (!secret) {
        return NextResponse.json({ 
          error: 'Server configuration error' 
        }, { status: 500 })
      }

      try {
        // Verify current token (allow expired tokens for refresh)
        const decoded = jwt.verify(token, secret, { ignoreExpiration: true }) as any
        
        // Check if token is too old to refresh (e.g., more than 7 days expired)
        const currentTime = Math.floor(Date.now() / 1000)
        const maxRefreshTime = decoded.exp + (7 * 24 * 60 * 60) // 7 days after expiration
        
        if (currentTime > maxRefreshTime) {
          return NextResponse.json({ 
            error: 'Token too old to refresh' 
          }, { status: 401 })
        }

        // Verify user still exists and is active
        const { payload } = req as any
        if (payload) {
          const user = await payload.findByID({
            collection: 'users',
            id: decoded.id
          })

          if (!user || !user.isActive) {
            return NextResponse.json({ 
              error: 'User not found or inactive' 
            }, { status: 401 })
          }

          // Generate new token
          const newToken = jwt.sign(
            {
              id: user.id,
              email: user.email,
              role: user.legacyRole,
              institute: user.institute,
              branch: user.branch
            },
            secret,
            { expiresIn: '24h' }
          )

          return NextResponse.json({ 
            token: newToken,
            user: {
              id: user.id,
              email: user.email,
              role: user.legacyRole,
              institute: user.institute,
              branch: user.branch
            },
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          })
        }

        return NextResponse.json({ 
          error: 'Unable to refresh token' 
        }, { status: 500 })

      } catch (jwtError) {
        console.error('JWT refresh failed:', jwtError)
        return NextResponse.json({ 
          error: 'Invalid token' 
        }, { status: 401 })
      }

    } catch (error) {
      console.error('Token refresh error:', error)
      return NextResponse.json({ 
        error: 'Internal server error' 
      }, { status: 500 })
    }
  }
}

// Course functionality has been removed from the system

      const { payload } = req

      // Get course details
      const course = await payload.findByID({
        collection: 'courses',
        id: courseId,
        depth: 2
      })

      if (!course) {
        return NextResponse.json({ 
          hasAccess: false,
          accessLevel: 'none',
          restrictions: ['Course not found'],
          branchAccess: false,
          instituteAccess: false
        }, { status: 404 })
      }

      // Super admin has full access
      if (req.user.legacyRole === 'super_admin') {
        return NextResponse.json({ 
          hasAccess: true,
          accessLevel: 'full',
          restrictions: [],
          branchAccess: true,
          instituteAccess: true
        })
      }

      // Check institute access
      const hasInstituteAccess = course.institute === req.user.institute
      if (!hasInstituteAccess) {
        // Check if course is shared with user's institute
        const isSharedWithInstitute = course.shareSettings?.shareWithMarketplace ||
          course.sharedBranches?.some((shared: any) => 
            shared.branch?.institute === req.user.institute && shared.isActive
          )

        if (!isSharedWithInstitute) {
          return NextResponse.json({ 
            hasAccess: false,
            accessLevel: 'none',
            restrictions: ['Course not available to your institute'],
            branchAccess: false,
            instituteAccess: false
          })
        }
      }

      // Check branch access
      const hasBranchAccess = course.branch === req.user.branch ||
        course.sharedBranches?.some((shared: any) => 
          shared.branch === req.user.branch && shared.isActive
        )

      // Determine access level based on role and sharing settings
      let accessLevel = 'view'
      const restrictions: string[] = []

      if (req.user.legacyRole === 'institute_admin' && hasInstituteAccess) {
        accessLevel = 'full'
      } else if (hasBranchAccess) {
        const sharedBranch = course.sharedBranches?.find((shared: any) => 
          shared.branch === req.user.branch
        )
        accessLevel = sharedBranch?.accessLevel || 'view'
      } else if (hasInstituteAccess) {
        accessLevel = 'view'
        restrictions.push('Limited to view access')
      }

      // Check if course is published
      if (course.status !== 'published') {
        if (accessLevel !== 'full') {
          restrictions.push('Course is not published')
        }
      }

      return NextResponse.json({ 
        hasAccess: true,
        accessLevel,
        restrictions,
        branchAccess: hasBranchAccess,
        instituteAccess: hasInstituteAccess
      })

    } catch (error) {
      console.error('Error checking course access:', error)
      return NextResponse.json({ 
        hasAccess: false,
        accessLevel: 'none',
        restrictions: ['Access check failed'],
        branchAccess: false,
        instituteAccess: false
      }, { status: 500 })
    }
  }
}
