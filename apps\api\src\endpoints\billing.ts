import { Endpoint } from 'payload/config'
import { BillingService } from '../services/billingService'

// Get billing dashboard data
export const getBillingDashboardEndpoint: Endpoint = {
  path: '/billing/dashboard',
  method: 'get',
  handler: async (req) => {
    const { user } = req
    
    if (!user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const billingService = new BillingService(req.payload)
      let dashboardData

      if (user.role === 'super_admin') {
        dashboardData = await billingService.getBillingDashboardData()
      } else if (user.role === 'institute_admin') {
        dashboardData = await billingService.getBillingDashboardData(undefined, user.institute)
      } else if (user.role === 'branch_admin') {
        dashboardData = await billingService.getBillingDashboardData(user.branch)
      } else {
        return Response.json({ message: 'Forbidden' }, { status: 403 })
      }

      return Response.json({
        success: true,
        data: dashboardData
      })

    } catch (error) {
      console.error('Billing dashboard error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Get bills with filtering
export const getBillsEndpoint: Endpoint = {
  path: '/billing/bills',
  method: 'get',
  handler: async (req) => {
    const { user } = req
    
    if (!user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const { searchParams } = new URL(req.url!)
      const status = searchParams.get('status')
      const month = searchParams.get('month')
      const year = searchParams.get('year')
      const branchId = searchParams.get('branchId')
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')

      let where: any = {}

      // Role-based access control
      if (user.role === 'institute_admin') {
        where['branch.institute'] = { equals: user.institute }
      } else if (user.role === 'branch_admin') {
        where.branch = { equals: user.branch }
      } else if (user.role !== 'super_admin') {
        return Response.json({ message: 'Forbidden' }, { status: 403 })
      }

      // Apply filters
      if (status) {
        where.status = { equals: status }
      }

      if (month && year) {
        where['billingPeriod.month'] = { equals: parseInt(month) }
        where['billingPeriod.year'] = { equals: parseInt(year) }
      }

      if (branchId && user.role === 'super_admin') {
        where.branch = { equals: branchId }
      }

      const bills = await req.payload.find({
        collection: 'bills',
        where,
        page,
        limit,
        sort: '-createdAt',
        populate: ['branch', 'branch.institute']
      })

      return Response.json({
        success: true,
        bills: bills.docs,
        pagination: {
          page: bills.page,
          limit: bills.limit,
          totalPages: bills.totalPages,
          totalDocs: bills.totalDocs,
          hasNextPage: bills.hasNextPage,
          hasPrevPage: bills.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Bills fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Generate monthly bill
export const generateBillEndpoint: Endpoint = {
  path: '/billing/generate',
  method: 'post',
  handler: async (req) => {
    const { user } = req
    
    if (!user || (user.role !== 'super_admin' && user.role !== 'institute_admin')) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const body = await req.json()
      const { branchId, month, year, baseFee } = body

      if (!branchId || !month || !year) {
        return Response.json(
          { success: false, error: 'Missing required fields' },
          { status: 400 }
        )
      }

      // Check access permissions
      if (user.role === 'institute_admin') {
        const branch = await req.payload.findByID({
          collection: 'branches',
          id: branchId
        })

        if (!branch || branch.institute !== user.institute) {
          return Response.json({ message: 'Forbidden' }, { status: 403 })
        }
      }

      const billingService = new BillingService(req.payload)
      const billingPeriod = {
        month: parseInt(month),
        year: parseInt(year),
        startDate: new Date(parseInt(year), parseInt(month) - 1, 1),
        endDate: new Date(parseInt(year), parseInt(month), 0)
      }

      const result = await billingService.generateMonthlyBill({
        branchId,
        billingPeriod,
        baseFee: baseFee || 0
      })

      return Response.json({
        success: true,
        bill: result
      })

    } catch (error) {
      console.error('Bill generation error:', error)
      return Response.json(
        { success: false, error: error.message || 'Bill generation failed' },
        { status: 500 }
      )
    }
  }
}

// Generate bulk bills
export const generateBulkBillsEndpoint: Endpoint = {
  path: '/billing/generate-bulk',
  method: 'post',
  handler: async (req) => {
    const { user } = req
    
    if (!user || (user.role !== 'super_admin' && user.role !== 'institute_admin')) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const body = await req.json()
      const { month, year } = body

      if (!month || !year) {
        return Response.json(
          { success: false, error: 'Missing required fields' },
          { status: 400 }
        )
      }

      const billingService = new BillingService(req.payload)
      const instituteId = user.role === 'institute_admin' ? user.institute : undefined

      const results = await billingService.generateBulkBills(
        parseInt(month),
        parseInt(year),
        instituteId
      )

      return Response.json({
        success: true,
        results,
        generated: results.length
      })

    } catch (error) {
      console.error('Bulk bill generation error:', error)
      return Response.json(
        { success: false, error: 'Bulk bill generation failed' },
        { status: 500 }
      )
    }
  }
}

// Get commission details
export const getCommissionDetailsEndpoint: Endpoint = {
  path: '/billing/commission',
  method: 'get',
  handler: async (req) => {
    const { user } = req
    
    if (!user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const { searchParams } = new URL(req.url!)
      const branchId = searchParams.get('branchId')
      const month = searchParams.get('month')
      const year = searchParams.get('year')
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '50')

      let where: any = {
        'paymentDetails.paymentStatus': { equals: 'completed' }
      }

      // Role-based access control
      if (user.role === 'institute_admin') {
        where['course.institute'] = { equals: user.institute }
      } else if (user.role === 'branch_admin') {
        where.branch = { equals: user.branch }
      } else if (user.role !== 'super_admin') {
        return Response.json({ message: 'Forbidden' }, { status: 403 })
      }

      // Apply filters
      if (branchId && user.role === 'super_admin') {
        where.branch = { equals: branchId }
      }

      if (month && year) {
        where['billingInfo.billingMonth'] = { equals: parseInt(month) }
        where['billingInfo.billingYear'] = { equals: parseInt(year) }
      }

      const purchases = await req.payload.find({
        collection: 'course-purchases',
        where,
        page,
        limit,
        sort: '-purchaseDate',
        populate: ['course', 'student', 'branch']
      })

      // Calculate summary
      const summary = {
        totalRevenue: purchases.docs.reduce((sum, p) => sum + p.purchaseDetails.finalAmount, 0),
        totalCommission: purchases.docs.reduce((sum, p) => sum + p.commissionDetails.commissionAmount, 0),
        purchaseCount: purchases.totalDocs,
        averageOrderValue: purchases.docs.length > 0 
          ? purchases.docs.reduce((sum, p) => sum + p.purchaseDetails.finalAmount, 0) / purchases.docs.length 
          : 0
      }

      return Response.json({
        success: true,
        purchases: purchases.docs,
        summary,
        pagination: {
          page: purchases.page,
          limit: purchases.limit,
          totalPages: purchases.totalPages,
          totalDocs: purchases.totalDocs,
          hasNextPage: purchases.hasNextPage,
          hasPrevPage: purchases.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Commission details error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Update bill status
export const updateBillStatusEndpoint: Endpoint = {
  path: '/billing/bills/:id/status',
  method: 'patch',
  handler: async (req) => {
    const { user } = req
    
    if (!user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const billId = req.url!.split('/').slice(-2)[0]
      const body = await req.json()
      const { status, paymentDetails } = body

      if (!status) {
        return Response.json(
          { success: false, error: 'Status is required' },
          { status: 400 }
        )
      }

      // Get bill and check permissions
      const bill = await req.payload.findByID({
        collection: 'bills',
        id: billId,
        populate: ['branch']
      })

      if (!bill) {
        return Response.json(
          { success: false, error: 'Bill not found' },
          { status: 404 }
        )
      }

      // Check access permissions
      if (user.role === 'institute_admin' && bill.branch.institute !== user.institute) {
        return Response.json({ message: 'Forbidden' }, { status: 403 })
      }

      if (user.role === 'branch_admin' && bill.branch.id !== user.branch) {
        return Response.json({ message: 'Forbidden' }, { status: 403 })
      }

      const updateData: any = { status }

      // Add payment details if status is paid
      if (status === 'paid' && paymentDetails) {
        updateData.paymentDetails = paymentDetails
        updateData['dates.paidDate'] = new Date()
      }

      // Add viewed date if status is viewed
      if (status === 'viewed') {
        updateData['dates.viewedDate'] = new Date()
      }

      // Add sent date if status is sent
      if (status === 'sent') {
        updateData['dates.sentDate'] = new Date()
      }

      const updatedBill = await req.payload.update({
        collection: 'bills',
        id: billId,
        data: updateData
      })

      return Response.json({
        success: true,
        bill: updatedBill
      })

    } catch (error) {
      console.error('Bill status update error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

export const billingEndpoints = [
  getBillingDashboardEndpoint,
  getBillsEndpoint,
  generateBillEndpoint,
  generateBulkBillsEndpoint,
  getCommissionDetailsEndpoint,
  updateBillStatusEndpoint,
]
