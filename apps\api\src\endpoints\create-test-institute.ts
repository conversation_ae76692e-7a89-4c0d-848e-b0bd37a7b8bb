import { Endpoint } from 'payload'

// Endpoint to create a test institute for domain testing
export const createTestInstituteEndpoint: Endpoint = {
  path: '/test/create-institute',
  method: 'post',
  handler: async (req: any) => {
    try {
      console.log('🏫 Creating test institute...')
      
      // Check if institute already exists
      const existingInstitutes = await req.payload.find({
        collection: 'institutes',
        where: {
          customDomain: { equals: 'hello.local:3000' }
        },
        limit: 1
      })

      if (existingInstitutes.totalDocs > 0) {
        return Response.json({
          success: true,
          message: 'Test institute already exists',
          institute: existingInstitutes.docs[0]
        })
      }

      // Create test institute
      const testInstitute = await req.payload.create({
        collection: 'institutes',
        data: {
          name: 'Hello Institute',
          slug: 'hello-institute',
          email: '<EMAIL>',
          phone: '+****************',
          website: 'https://hello.local',
          tagline: 'Excellence in Education',
          description: 'A premier educational institution providing quality education and training programs.',
          customDomain: 'hello.local:3000',
          domainVerified: true,
          isActive: true,
          subscriptionPlan: 'basic',
          subscriptionStatus: 'active',
          maxStudents: 1000,
          maxCourses: 100,
          maxBranches: 5,
          featuresMarketplace: true,
          featuresLiveClasses: true,
          featuresExams: true,
          featuresBlogs: true,
          featuresAnalytics: true
        }
      })
      
      console.log('✅ Test institute created:', testInstitute.id)
      
      return Response.json({
        success: true,
        message: 'Test institute created successfully!',
        institute: {
          id: testInstitute.id,
          name: testInstitute.name,
          slug: testInstitute.slug,
          customDomain: testInstitute.customDomain,
          domainVerified: testInstitute.domainVerified,
          isActive: testInstitute.isActive
        }
      })
      
    } catch (error) {
      console.error('❌ Create test institute error:', error)
      return Response.json({
        success: false,
        error: 'Failed to create test institute: ' + (error as any)?.message
      }, { status: 500 })
    }
  }
}
