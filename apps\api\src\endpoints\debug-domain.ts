import { Endpoint } from 'payload'

// Debug endpoint to check domain resolution (PUBLIC)
export const debugDomainEndpoint: Endpoint = {
  path: '/public/debug/domain-resolve',
  method: 'get',
  handler: async (req: any) => {
    try {
      const url = new URL(req.url)
      const domain = url.searchParams.get('domain')

      console.log('🔍 Debug domain lookup for:', domain)

      // Get all institutes to see what domains exist
      const allInstitutes = await req.payload.find({
        collection: 'institutes',
        limit: 100
      })

      console.log('📋 All institutes:', allInstitutes.docs.map(inst => ({
        id: inst.id,
        name: inst.name,
        customDomain: (inst as any).custom_domain || (inst as any).customDomain,
        domainVerified: (inst as any).domain_verified || (inst as any).domainVerified,
        isActive: (inst as any).is_active || (inst as any).isActive
      })))

      // Try to find institute by domain
      let institute = null
      if (domain) {
        const institutes = await req.payload.find({
          collection: 'institutes',
          where: {
            customDomain: { equals: domain },
            domainVerified: { equals: true },
            isActive: { equals: true }
          },
          limit: 1
        })

        if (institutes.totalDocs > 0) {
          institute = institutes.docs[0]
        }
      }

      return Response.json({
        success: true,
        debug: {
          requestedDomain: domain,
          foundInstitute: institute ? {
            id: institute.id,
            name: institute.name,
            customDomain: (institute as any).custom_domain || (institute as any).customDomain,
            domainVerified: (institute as any).domain_verified || (institute as any).domainVerified,
            isActive: (institute as any).is_active || (institute as any).isActive
          } : null,
          allInstitutes: allInstitutes.docs.map(inst => ({
            id: inst.id,
            name: inst.name,
            customDomain: (inst as any).custom_domain || (inst as any).customDomain,
            domainVerified: (inst as any).domain_verified || (inst as any).domainVerified,
            isActive: (inst as any).is_active || (inst as any).isActive
          })),
          totalInstitutes: allInstitutes.totalDocs
        }
      })

    } catch (error) {
      console.error('❌ Debug domain error:', error)
      return Response.json({
        success: false,
        error: 'Debug failed: ' + (error as any)?.message
      }, { status: 500 })
    }
  }
}
