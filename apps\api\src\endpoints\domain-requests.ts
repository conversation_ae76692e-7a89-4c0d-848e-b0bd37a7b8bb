import { Endpoint } from 'payload/config'

// Get domain requests (Super Admin view)
export const getDomainRequestsEndpoint: Endpoint = {
  path: '/admin/domain-requests',
  method: 'get',
  handler: async (req) => {
    const { user } = req
    
    if (!user || user.legacyRole !== 'super_admin') {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const { searchParams } = new URL(req.url!)
      const status = searchParams.get('status')
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '10')

      let where: any = {}
      if (status) {
        where.status = { equals: status }
      }

      const requests = await req.payload.find({
        collection: 'domain-requests',
        where,
        page,
        limit,
        sort: '-requestedAt',
        populate: ['institute', 'requestedBy', 'reviewedBy'],
      })

      return Response.json({
        success: true,
        requests: requests.docs,
        pagination: {
          page: requests.page,
          limit: requests.limit,
          totalPages: requests.totalPages,
          totalDocs: requests.totalDocs,
        },
      })
    } catch (error) {
      console.error('Domain requests fetch error:', error)
      return Response.json(
        { message: 'Failed to fetch domain requests' },
        { status: 500 }
      )
    }
  },
}

// Approve domain request
export const approveDomainRequestEndpoint: Endpoint = {
  path: '/admin/domain-requests/:requestId/approve',
  method: 'post',
  handler: async (req) => {
    const { user } = req
    
    if (!user || user.role !== 'super_admin') {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const requestId = req.routeParams?.requestId

      if (!requestId) {
        return Response.json({ message: 'Request ID is required' }, { status: 400 })
      }

      // Get the domain request
      const domainRequest = await req.payload.findByID({
        collection: 'domain-requests',
        id: requestId,
      })

      if (domainRequest.status !== 'pending') {
        return Response.json(
          { message: 'Only pending requests can be approved' },
          { status: 400 }
        )
      }

      // Generate DNS records for the domain
      const dnsRecords = [
        {
          type: 'A',
          name: '@',
          value: '*************', // Replace with actual server IP
          ttl: 300,
        },
        {
          type: 'CNAME',
          name: 'www',
          value: domainRequest.requestedDomain,
          ttl: 300,
        },
        {
          type: 'TXT',
          name: '@',
          value: `v=spf1 include:groups-exam.com ~all`,
          ttl: 300,
        },
      ]

      // Update domain request status
      const updatedRequest = await req.payload.update({
        collection: 'domain-requests',
        id: requestId,
        data: {
          status: 'approved',
          reviewedBy: user.id,
          reviewedAt: new Date(),
          sslStatus: 'pending',
          dnsRecords,
        },
      })

      // TODO: Initiate SSL certificate provisioning
      // TODO: Send notification email to institute

      return Response.json({
        success: true,
        request: updatedRequest,
        message: 'Domain request approved successfully',
      })
    } catch (error) {
      console.error('Domain approval error:', error)
      return Response.json(
        { message: 'Failed to approve domain request' },
        { status: 500 }
      )
    }
  },
}

// Reject domain request
export const rejectDomainRequestEndpoint: Endpoint = {
  path: '/admin/domain-requests/:requestId/reject',
  method: 'post',
  handler: async (req) => {
    const { user } = req
    
    if (!user || user.role !== 'super_admin') {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const requestId = req.routeParams?.requestId
      const body = await req.json()
      const { reason } = body

      if (!requestId) {
        return Response.json({ message: 'Request ID is required' }, { status: 400 })
      }

      if (!reason) {
        return Response.json({ message: 'Rejection reason is required' }, { status: 400 })
      }

      // Get the domain request
      const domainRequest = await req.payload.findByID({
        collection: 'domain-requests',
        id: requestId,
      })

      if (domainRequest.status !== 'pending') {
        return Response.json(
          { message: 'Only pending requests can be rejected' },
          { status: 400 }
        )
      }

      // Update domain request status
      const updatedRequest = await req.payload.update({
        collection: 'domain-requests',
        id: requestId,
        data: {
          status: 'rejected',
          reviewedBy: user.id,
          reviewedAt: new Date(),
          rejectionReason: reason,
        },
      })

      // TODO: Send notification email to institute

      return Response.json({
        success: true,
        request: updatedRequest,
        message: 'Domain request rejected',
      })
    } catch (error) {
      console.error('Domain rejection error:', error)
      return Response.json(
        { message: 'Failed to reject domain request' },
        { status: 500 }
      )
    }
  },
}

// Submit domain request (Institute Admin)
export const submitDomainRequestEndpoint: Endpoint = {
  path: '/institute/domain-request',
  method: 'post',
  handler: async (req) => {
    const { user } = req
    
    if (!user || user.role !== 'institute_admin') {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const body = await req.json()
      const { domainName, purpose, notes } = body

      if (!domainName || !purpose) {
        return Response.json(
          { message: 'Domain name and purpose are required' },
          { status: 400 }
        )
      }

      // Check if domain already requested
      const existingRequest = await req.payload.find({
        collection: 'domain-requests',
        where: {
          requestedDomain: { equals: domainName }
        },
        limit: 1,
      })

      if (existingRequest.docs.length > 0) {
        return Response.json(
          { message: 'Domain has already been requested' },
          { status: 400 }
        )
      }

      // Create domain request
      const domainRequest = await req.payload.create({
        collection: 'domain-requests',
        data: {
          institute: user.institute,
          requestedDomain: domainName,
          currentDomain: `${user.institute}.groups-exam.com`, // Assuming this format
          purpose,
          notes,
          requestedBy: user.id,
          status: 'pending',
        },
      })

      return Response.json({
        success: true,
        request: domainRequest,
        message: 'Domain request submitted successfully',
      })
    } catch (error) {
      console.error('Domain request submission error:', error)
      return Response.json(
        { message: 'Failed to submit domain request' },
        { status: 500 }
      )
    }
  },
}

// Get institute's domain status
export const getInstituteDomainStatusEndpoint: Endpoint = {
  path: '/institute/domain-status',
  method: 'get',
  handler: async (req) => {
    const { user } = req
    
    if (!user || user.role !== 'institute_admin') {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Get latest domain request for institute
      const domainRequests = await req.payload.find({
        collection: 'domain-requests',
        where: {
          institute: { equals: user.institute }
        },
        sort: '-requestedAt',
        limit: 1,
      })

      const domainStatus = {
        currentDomain: `${user.institute}.groups-exam.com`,
        customDomain: null,
        status: 'none',
        requestedAt: null,
        approvedAt: null,
        rejectionReason: null,
        sslStatus: null,
        dnsRecords: null,
      }

      if (domainRequests.docs.length > 0) {
        const request = domainRequests.docs[0]
        domainStatus.customDomain = request.requestedDomain
        domainStatus.status = request.status
        domainStatus.requestedAt = request.requestedAt
        domainStatus.approvedAt = request.reviewedAt
        domainStatus.rejectionReason = request.rejectionReason
        domainStatus.sslStatus = request.sslStatus
        domainStatus.dnsRecords = request.dnsRecords
      }

      return Response.json({
        success: true,
        domain: domainStatus,
      })
    } catch (error) {
      console.error('Domain status fetch error:', error)
      return Response.json(
        { message: 'Failed to fetch domain status' },
        { status: 500 }
      )
    }
  },
}

export const domainRequestEndpoints = [
  getDomainRequestsEndpoint,
  approveDomainRequestEndpoint,
  rejectDomainRequestEndpoint,
  submitDomainRequestEndpoint,
  getInstituteDomainStatusEndpoint,
]
