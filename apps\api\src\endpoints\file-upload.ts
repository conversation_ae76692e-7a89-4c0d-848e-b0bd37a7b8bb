import type { Endpoint } from 'payload'
import { requireAuth } from '../middleware/auth'
import { createUploadMiddleware, AVATAR_SIZES, COURSE_THUMBNAIL_SIZES, LOGO_SIZES, FAVICON_SIZES } from '../middleware/upload-middleware'

console.log('🔥 file-upload.ts file loaded - common file upload API for all file types!')

// Common file upload endpoint that handles all file types
export const commonFileUploadEndpoint: Endpoint = {
  path: '/upload',
  method: 'post',
  handler: async (req) => {
    console.log('🚀🚀🚀 COMMON FILE UPLOAD ENDPOINT CALLED! 🚀🚀🚀')
    console.log('📝 Request URL:', req.url)
    console.log('📝 Request method:', req.method)
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }
    
    console.log('✅ Auth check passed, user:', {
      id: req.user?.id,
      email: req.user?.email,
      legacyRole: req.user?.legacyRole
    })

    try {
      // Parse form data to get upload type and other parameters
      const formData = await req.formData()
      const file = formData.get('file') as File
      const uploadType = formData.get('uploadType') as string || 'general'
      const updateUserField = formData.get('updateUserField') as string // e.g., 'avatar'
      const folder = formData.get('folder') as string
      
      console.log('📋 Upload parameters:', {
        uploadType,
        updateUserField,
        folder,
        fileName: file?.name,
        fileSize: file?.size,
        fileType: file?.type
      })

      if (!file) {
        console.log('❌ No file provided')
        return Response.json(
          { success: false, message: 'No file provided' },
          { status: 400 }
        )
      }

      // Get upload configuration based on type
      const uploadConfig = getUploadConfig(uploadType)
      
      console.log('🔧 Upload config:', {
        mediaType: uploadConfig.mediaType,
        maxFileSize: uploadConfig.maxFileSize,
        generateSizes: uploadConfig.generateSizes?.length || 0,
        folder: folder || uploadConfig.folder
      })

      // Create upload middleware instance with current storage configuration
      console.log('🔧 Creating upload middleware...')
      const uploadMiddleware = await createUploadMiddleware(req.payload)

      // Use uploadConfig.folder as default, only override if folder is explicitly provided and not empty
      const finalFolder = (folder && folder.trim()) ? folder : uploadConfig.folder

      console.log('📂 Folder selection:', {
        formDataFolder: folder,
        configFolder: uploadConfig.folder,
        finalFolder: finalFolder
      })

      // Handle file upload with type-specific settings (pass the already parsed file)
      const uploadResult = await uploadMiddleware.handleFileUpload(req, {
        mediaType: uploadConfig.mediaType,
        folder: finalFolder,
        generateSizes: uploadConfig.generateSizes,
        maxFileSize: uploadConfig.maxFileSize,
        allowedMimeTypes: uploadConfig.allowedMimeTypes
      }, file) // Pass the file to avoid double parsing

      if (!uploadResult.success) {
        console.log('❌ Upload failed:', uploadResult.message)
        return Response.json(
          { success: false, message: uploadResult.message },
          { status: 400 }
        )
      }

      console.log('📊 File uploaded successfully:', {
        id: uploadResult.media?.id,
        filename: uploadResult.media?.filename,
        url: uploadResult.media?.url,
        sizes: Object.keys(uploadResult.media?.sizes || {})
      })

      // Update user field if specified (e.g., avatar)
      let updatedUser = null
      if (updateUserField && uploadResult.media) {
        console.log('🔄 Updating user field:', updateUserField)
        
        try {
          updatedUser = await req.payload.update({
            collection: 'users',
            id: req.user!.id,
            data: {
              [updateUserField]: uploadResult.media.id,
            },
          })

          // Remove password from response
          const { password: _, ...userWithoutPassword } = updatedUser
          updatedUser = userWithoutPassword

          console.log('✅ User field updated successfully')
        } catch (error) {
          console.warn('⚠️ Failed to update user field:', error)
          // Don't fail the entire upload if user update fails
        }
      }

      return Response.json({
        success: true,
        message: `${uploadConfig.displayName} uploaded successfully`,
        media: {
          id: uploadResult.media!.id,
          filename: uploadResult.media!.filename,
          url: uploadResult.media!.url,
          sizes: uploadResult.media!.sizes,
          alt: uploadResult.media!.alt,
          mediaType: uploadResult.media!.mediaType,
        },
        user: updatedUser,
        uploadType,
      })

    } catch (error) {
      console.error('💥 File upload error:', error)
      return Response.json(
        { success: false, message: 'Failed to upload file' },
        { status: 500 }
      )
    }
  },
}

// Get current user's uploaded files
export const getUserFilesEndpoint: Endpoint = {
  path: '/api/upload/my-files',
  method: 'get',
  handler: async (req) => {
    console.log('🚀🚀🚀 GET USER FILES ENDPOINT CALLED! 🚀🚀🚀')
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }

    try {
      const url = new URL(req.url || 'http://localhost')
      const mediaType = url.searchParams.get('mediaType')
      const page = parseInt(url.searchParams.get('page') || '1')
      const limit = parseInt(url.searchParams.get('limit') || '10')

      console.log('📋 Query parameters:', { mediaType, page, limit })

      const where: any = {
        uploadedBy: { equals: req.user!.id }
      }

      if (mediaType) {
        where.mediaType = { equals: mediaType }
      }

      const files = await req.payload.find({
        collection: 'media',
        where,
        page,
        limit,
        sort: '-createdAt',
      })

      console.log('📊 Found files:', {
        count: files.docs.length,
        totalDocs: files.totalDocs
      })

      return Response.json({
        success: true,
        files: files.docs,
        totalDocs: files.totalDocs,
        totalPages: files.totalPages,
        page: files.page,
        limit: files.limit,
        hasNextPage: files.hasNextPage,
        hasPrevPage: files.hasPrevPage,
      })

    } catch (error) {
      console.error('💥 Get user files error:', error)
      return Response.json(
        { success: false, message: 'Failed to get files' },
        { status: 500 }
      )
    }
  },
}

// Delete uploaded file
export const deleteFileEndpoint: Endpoint = {
  path: '/api/upload/:id',
  method: 'delete',
  handler: async (req) => {
    console.log('🚀🚀🚀 DELETE FILE ENDPOINT CALLED! 🚀🚀🚀')
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }

    try {
      const url = new URL(req.url || 'http://localhost')
      const pathParts = url.pathname.split('/')
      const fileId = pathParts[pathParts.length - 1]

      if (!fileId) {
        return Response.json(
          { success: false, message: 'File ID is required' },
          { status: 400 }
        )
      }

      console.log('🗑️ Deleting file:', fileId)

      // Get file details first
      const file = await req.payload.findByID({
        collection: 'media',
        id: fileId,
      })

      if (!file) {
        return Response.json(
          { success: false, message: 'File not found' },
          { status: 404 }
        )
      }

      // Check if user owns the file or is admin
      if (file.uploadedBy !== req.user!.id && req.user!.legacyRole !== 'super_admin') {
        return Response.json(
          { success: false, message: 'Permission denied' },
          { status: 403 }
        )
      }

      // Delete file using upload middleware
      const uploadMiddleware = await createUploadMiddleware(req.payload)
      await uploadMiddleware.deleteFile(file)

      // Delete from database
      await req.payload.delete({
        collection: 'media',
        id: fileId,
      })

      console.log('✅ File deleted successfully')

      return Response.json({
        success: true,
        message: 'File deleted successfully',
      })

    } catch (error) {
      console.error('💥 Delete file error:', error)
      return Response.json(
        { success: false, message: 'Failed to delete file' },
        { status: 500 }
      )
    }
  },
}

// Helper function to get upload configuration based on type
function getUploadConfig(uploadType: string) {
  const configs = {
    avatar: {
      mediaType: 'user_avatar',
      folder: 'avatars',
      generateSizes: AVATAR_SIZES,
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedMimeTypes: ['image/*'],
      displayName: 'Avatar'
    },
    course_thumbnail: {
      mediaType: 'course_thumbnail',
      folder: 'courses',
      generateSizes: COURSE_THUMBNAIL_SIZES,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: ['image/*'],
      displayName: 'Course Thumbnail'
    },
    institute_logo: {
      mediaType: 'institute_logo',
      folder: 'institutes',
      generateSizes: LOGO_SIZES,
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedMimeTypes: ['image/*'],
      displayName: 'Institute Logo'
    },
    platform_logo: {
      mediaType: 'platform_logo',
      folder: 'platform/file',
      generateSizes: LOGO_SIZES,
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedMimeTypes: ['image/*'],
      displayName: 'Platform Logo'
    },
    platform_favicon: {
      mediaType: 'platform_favicon',
      folder: 'platform/file',
      generateSizes: FAVICON_SIZES,
      maxFileSize: 2 * 1024 * 1024, // 2MB
      allowedMimeTypes: ['image/*'],
      displayName: 'Platform Favicon'
    },
    document: {
      mediaType: 'document',
      folder: 'documents',
      generateSizes: undefined, // No resizing for documents
      maxFileSize: 50 * 1024 * 1024, // 50MB
      allowedMimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      displayName: 'Document'
    },
    general: {
      mediaType: 'general',
      folder: 'uploads',
      generateSizes: undefined,
      maxFileSize: 25 * 1024 * 1024, // 25MB
      allowedMimeTypes: ['image/*', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      displayName: 'File'
    }
  }

  return configs[uploadType as keyof typeof configs] || configs.general
}

// Remove avatar endpoint that deletes files and cleans up database
export const removeAvatarEndpoint: Endpoint = {
  path: '/remove-avatar',
  method: 'delete',
  handler: async (req) => {
    console.log('🗑️🗑️🗑️ REMOVE AVATAR ENDPOINT CALLED! 🗑️🗑️🗑️')
    console.log('📝 Request URL:', req.url)
    console.log('📝 Request method:', req.method)

    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }

    console.log('✅ Auth check passed, user:', {
      id: req.user?.id,
      email: req.user?.email,
      legacyRole: req.user?.legacyRole
    })

    try {
      // Parse request body to get media ID or user ID
      let body: any = {}
      try {
        if (req.json) {
          body = await req.json()
        }
      } catch (error) {
        console.log('⚠️ No JSON body provided, using URL parameters')
      }

      const { mediaId, userId, removeFromUser } = body

      console.log('📋 Remove avatar parameters:', {
        mediaId,
        userId,
        removeFromUser,
        currentUserId: req.user?.id
      })

      // If no mediaId provided, try to get it from user's avatar field
      let targetMediaId = mediaId
      let targetUserId = userId || req.user?.id

      if (!targetMediaId && targetUserId) {
        console.log('🔍 Looking up user avatar...')
        const user = await req.payload.findByID({
          collection: 'users',
          id: targetUserId
        })

        if (user && user.avatar) {
          targetMediaId = typeof user.avatar === 'object' ? user.avatar.id : user.avatar
          console.log('📋 Found user avatar media ID:', targetMediaId)
        }
      }

      if (!targetMediaId) {
        console.log('❌ No media ID provided or found')
        return Response.json(
          { success: false, message: 'No media ID provided or found' },
          { status: 400 }
        )
      }

      // Get media object from database
      console.log('🔍 Fetching media object from database...')
      const media = await req.payload.findByID({
        collection: 'media',
        id: targetMediaId
      })

      if (!media) {
        console.log('❌ Media not found in database')
        return Response.json(
          { success: false, message: 'Media not found' },
          { status: 404 }
        )
      }

      console.log('📋 Media object found:', {
        id: media.id,
        filename: media.filename,
        url: media.url,
        sizesCount: Object.keys(media.sizes || {}).length
      })

      // Check permissions - user can only delete their own avatar unless they're admin
      if (media.uploadedBy !== req.user!.id && req.user!.legacyRole !== 'super_admin') {
        console.log('❌ Permission denied')
        return Response.json(
          { success: false, message: 'Permission denied' },
          { status: 403 }
        )
      }

      // Create upload middleware to handle file deletion
      console.log('🔧 Creating upload middleware for file deletion...')
      const uploadMiddleware = await createUploadMiddleware(req.payload)

      // Delete files using upload middleware
      console.log('🗑️ Deleting files...')
      await uploadMiddleware.deleteFile(media)

      // Remove from user's avatar field if requested
      if (removeFromUser && targetUserId) {
        console.log('👤 Removing avatar from user profile...')
        await req.payload.update({
          collection: 'users',
          id: targetUserId,
          data: {
            avatar: null
          }
        })
        console.log('✅ Avatar removed from user profile')
      }

      // Delete media record from database
      console.log('🗑️ Deleting media record from database...')
      await req.payload.delete({
        collection: 'media',
        id: targetMediaId
      })

      console.log('🎉 Avatar removal completed successfully!')

      return Response.json({
        success: true,
        message: 'Avatar removed successfully',
        deletedMedia: {
          id: media.id,
          filename: media.filename,
          url: media.url
        },
        userUpdated: removeFromUser && targetUserId
      })

    } catch (error) {
      console.error('❌ Avatar removal error:', error)
      return Response.json(
        { success: false, message: error instanceof Error ? error.message : 'Avatar removal failed' },
        { status: 500 }
      )
    }
  }
}
