import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Debug logging
      console.log('Blog advanced endpoint user info:', {
        userId: user.id,
        institute: user.institute,
        instituteType: typeof user.institute,
        legacyRole: user.legacyRole
      })

      if (!user.institute || user.institute === 'NaN' || isNaN(parseInt(user.institute))) {
        console.error('Invalid institute ID:', user.institute)
        return Response.json({
          success: false,
          error: `No valid institute assigned to user. Institute ID: ${user.institute}`
        }, { status: 403 })
      }

      return handler(req)
    }
  }
}

// AI-powered blog search
export const blogSearchEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/search',
  'get',
  async (req) => {
    try {
      const { user } = req
      const { q, category, tags, status, limit = 10, page = 1 } = req.query

      // Build search query
      const searchQuery: any = {
        institute: {
          equals: user.institute
        }
      }

      // Add text search if query provided
      if (q) {
        searchQuery.or = [
          {
            title: {
              contains: q
            }
          },
          {
            excerpt: {
              contains: q
            }
          },
          {
            content: {
              contains: q
            }
          },
          {
            'tags.tag': {
              contains: q
            }
          }
        ]
      }

      // Add filters
      if (category) {
        searchQuery.category = { equals: category }
      }

      if (status) {
        searchQuery.status = { equals: status }
      }

      if (tags) {
        const tagArray = Array.isArray(tags) ? tags : [tags]
        searchQuery['tags.tag'] = {
          in: tagArray
        }
      }

      // Execute search
      const posts = await req.payload.find({
        collection: 'blog-posts',
        where: searchQuery,
        limit: parseInt(limit as string),
        page: parseInt(page as string),
        sort: '-createdAt',
        populate: ['category', 'author', 'featuredImage']
      })

      // AI-powered content recommendations (if query provided)
      let recommendations = []
      if (q && posts.docs.length > 0) {
        recommendations = await generateContentRecommendations(q, posts.docs, req.payload)
      }

      return Response.json({
        success: true,
        posts: posts.docs,
        pagination: {
          page: posts.page,
          limit: posts.limit,
          totalPages: posts.totalPages,
          totalDocs: posts.totalDocs,
          hasNextPage: posts.hasNextPage,
          hasPrevPage: posts.hasPrevPage
        },
        recommendations,
        searchQuery: q
      })
    } catch (error) {
      console.error('Blog search error:', error)
      return Response.json({
        success: false,
        error: 'Search failed'
      }, { status: 500 })
    }
  }
)

// Get trending/popular posts
export const getTrendingPostsEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/trending',
  'get',
  async (req) => {
    try {
      const { user } = req
      const { period = '7d', limit = 5 } = req.query

      // Calculate date range based on period
      const now = new Date()
      const startDate = new Date()

      switch (period) {
        case '24h':
          startDate.setDate(now.getDate() - 1)
          break
        case '7d':
          startDate.setDate(now.getDate() - 7)
          break
        case '30d':
          startDate.setDate(now.getDate() - 30)
          break
        default:
          startDate.setDate(now.getDate() - 7)
      }

      // Get posts with high engagement
      const trendingPosts = await req.payload.find({
        collection: 'blog-posts',
        where: {
          and: [
            {
              institute: {
                equals: user.institute
              }
            },
            {
              status: {
                equals: 'published'
              }
            },
            {
              publishedAt: {
                greater_than: startDate.toISOString()
              }
            }
          ]
        },
        limit: parseInt(limit as string),
        sort: '-createdAt', // Fallback to creation date since analytics might not exist
        populate: ['category', 'author', 'featuredImage']
      })

      return Response.json({
        success: true,
        posts: trendingPosts.docs || [],
        period
      })
    } catch (error) {
      console.error('Trending posts error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch trending posts'
      }, { status: 500 })
    }
  }
)

// Get blog analytics
export const getBlogAnalyticsEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/analytics',
  'get',
  async (req) => {
    try {
      const { user } = req
      const { period = '30d', postId } = req.query

      // Calculate date range
      const now = new Date()
      const startDate = new Date()

      switch (period) {
        case '7d':
          startDate.setDate(now.getDate() - 7)
          break
        case '30d':
          startDate.setDate(now.getDate() - 30)
          break
        case '90d':
          startDate.setDate(now.getDate() - 90)
          break
        default:
          startDate.setDate(now.getDate() - 30)
      }

      let analyticsQuery: any = {
        date: {
          greater_than_equal: startDate.toISOString().split('T')[0]
        }
      }

      // If specific post requested
      if (postId) {
        analyticsQuery.post = { equals: postId }
      } else {
        // Filter by institute posts
        analyticsQuery['post.institute'] = { equals: user.institute }
      }

      // Get analytics data - handle case where collection might be empty
      let analytics
      try {
        analytics = await req.payload.find({
          collection: 'blog-analytics',
          where: analyticsQuery,
          limit: 1000,
          sort: 'date',
          populate: ['post']
        })
      } catch (error) {
        console.log('Analytics collection might not exist or be empty, returning default data')
        analytics = { docs: [] }
      }

      // Aggregate data
      const aggregated = (analytics.docs || []).reduce((acc, curr) => {
        acc.totalViews += curr.views || 0
        acc.totalUniqueViews += curr.uniqueViews || 0
        acc.totalLikes += curr.likes || 0
        acc.totalComments += curr.comments || 0
        acc.totalShares += curr.shares || 0
        return acc
      }, {
        totalViews: 0,
        totalUniqueViews: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0
      })

      return Response.json({
        success: true,
        analytics: aggregated,
        dailyData: analytics.docs || [],
        period
      })
    } catch (error) {
      console.error('Blog analytics error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch analytics'
      }, { status: 500 })
    }
  }
)

// AI-powered content recommendation function
const generateContentRecommendations = async (query: string, posts: any[], payload: any) => {
  try {
    // Simple keyword-based recommendations
    // In production, this could use OpenAI or other AI services
    const keywords = query.toLowerCase().split(' ')

    const recommendations = posts
      .map(post => {
        let score = 0

        // Score based on title matches
        keywords.forEach(keyword => {
          if (post.title.toLowerCase().includes(keyword)) score += 3
          if (post.excerpt?.toLowerCase().includes(keyword)) score += 2
          if (post.tags?.some((tag: any) => tag.tag.toLowerCase().includes(keyword))) score += 1
        })

        return { ...post, relevanceScore: score }
      })
      .filter(post => post.relevanceScore > 0)
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 3)

    return recommendations
  } catch (error) {
    console.error('Recommendation generation error:', error)
    return []
  }
}
