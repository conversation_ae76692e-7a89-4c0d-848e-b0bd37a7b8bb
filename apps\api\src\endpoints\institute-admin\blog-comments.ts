import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      if (!user.institute) {
        return Response.json({
          success: false,
          error: 'No institute assigned to user'
        }, { status: 403 })
      }

      return handler(req)
    }
  }
}

// Get all comments for institute posts
export const getBlogCommentsEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/comments',
  'get',
  async (req) => {
    try {
      const { user } = req
      const { page = 1, limit = 10, status, search } = req.query

      // Build query
      const query: any = {
        'post.institute': {
          equals: user.institute
        }
      }

      // Add filters
      if (status) {
        query.status = { equals: status }
      }

      if (search) {
        query.or = [
          {
            content: {
              contains: search
            }
          },
          {
            authorName: {
              contains: search
            }
          }
        ]
      }

      const comments = await req.payload.find({
        collection: 'blog-comments',
        where: query,
        limit: parseInt(limit as string),
        page: parseInt(page as string),
        sort: '-createdAt',
        populate: ['post', 'author', 'parentComment', 'moderatedBy']
      })

      return Response.json({
        success: true,
        comments: comments.docs,
        pagination: {
          page: comments.page,
          limit: comments.limit,
          totalPages: comments.totalPages,
          totalDocs: comments.totalDocs,
          hasNextPage: comments.hasNextPage,
          hasPrevPage: comments.hasPrevPage
        }
      })
    } catch (error) {
      console.error('Get blog comments error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch blog comments'
      }, { status: 500 })
    }
  }
)

// Update comment status (moderate)
export const updateBlogCommentEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/comments/:id',
  'patch',
  async (req) => {
    try {
      const { user } = req
      const { id } = req.params
      const updateData = await req.json()

      // Check if comment exists and belongs to user's institute posts
      const existingComment = await req.payload.findByID({
        collection: 'blog-comments',
        id,
        populate: ['post']
      })

      if (existingComment.post.institute !== user.institute) {
        return Response.json({
          success: false,
          error: 'Access denied'
        }, { status: 403 })
      }

      // Set moderation info
      updateData.moderatedBy = user.id
      updateData.moderatedAt = new Date()

      const comment = await req.payload.update({
        collection: 'blog-comments',
        id,
        data: updateData
      })

      return Response.json({
        success: true,
        comment,
        message: 'Comment updated successfully'
      })
    } catch (error) {
      console.error('Update blog comment error:', error)
      return Response.json({
        success: false,
        error: 'Failed to update blog comment'
      }, { status: 500 })
    }
  }
)

// Delete comment
export const deleteBlogCommentEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/comments/:id',
  'delete',
  async (req) => {
    try {
      const { user } = req
      const { id } = req.params

      // Only institute admin can delete comments
      if (user.legacyRole !== 'institute_admin') {
        return Response.json({
          success: false,
          error: 'Only institute admin can delete comments'
        }, { status: 403 })
      }

      // Check if comment exists and belongs to user's institute posts
      const existingComment = await req.payload.findByID({
        collection: 'blog-comments',
        id,
        populate: ['post']
      })

      if (existingComment.post.institute !== user.institute) {
        return Response.json({
          success: false,
          error: 'Access denied'
        }, { status: 403 })
      }

      await req.payload.delete({
        collection: 'blog-comments',
        id
      })

      return Response.json({
        success: true,
        message: 'Comment deleted successfully'
      })
    } catch (error) {
      console.error('Delete blog comment error:', error)
      return Response.json({
        success: false,
        error: 'Failed to delete blog comment'
      }, { status: 500 })
    }
  }
)
