import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Debug logging
      console.log('Blog endpoint user info:', {
        userId: user.id,
        institute: user.institute,
        instituteType: typeof user.institute,
        legacyRole: user.legacyRole
      })

      if (!user.institute || user.institute === 'NaN' || isNaN(parseInt(user.institute))) {
        console.error('Invalid institute ID:', user.institute)
        return Response.json({
          success: false,
          error: `No valid institute assigned to user. Institute ID: ${user.institute}`
        }, { status: 403 })
      }

      return handler(req)
    }
  }
}

// Get all blog posts for institute
export const getBlogPostsEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/posts',
  'get',
  async (req) => {
    try {
      const { user } = req
      const { page = 1, limit = 10, status, category, search } = req.query

      // Build query
      const userInstituteId = parseInt(user.institute)
      console.log('List posts - User institute ID:', userInstituteId, typeof userInstituteId)

      const query: any = {
        institute: {
          equals: userInstituteId
        }
      }

      // Add filters
      if (status) {
        query.status = { equals: status }
      }

      if (category) {
        query.category = { equals: category }
      }

      if (search) {
        query.or = [
          {
            title: {
              contains: search
            }
          },
          {
            excerpt: {
              contains: search
            }
          },
          {
            content: {
              contains: search
            }
          }
        ]
      }

      const posts = await req.payload.find({
        collection: 'blog-posts',
        where: query,
        limit: parseInt(limit as string),
        page: parseInt(page as string),
        sort: '-createdAt',
        populate: ['category', 'author', 'featuredImage']
      })

      return Response.json({
        success: true,
        posts: posts.docs,
        pagination: {
          page: posts.page,
          limit: posts.limit,
          totalPages: posts.totalPages,
          totalDocs: posts.totalDocs,
          hasNextPage: posts.hasNextPage,
          hasPrevPage: posts.hasPrevPage
        }
      })
    } catch (error) {
      console.error('Get blog posts error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch blog posts'
      }, { status: 500 })
    }
  }
)

// Get single blog post
export const getBlogPostEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/posts/:id',
  'get',
  async (req) => {
    try {
      const { user } = req
      // Extract ID from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const id = pathParts[pathParts.length - 1]

      if (!id || id === 'undefined') {
        return Response.json({
          success: false,
          error: 'Post ID is required'
        }, { status: 400 })
      }

      const post = await req.payload.findByID({
        collection: 'blog-posts',
        id,
        populate: ['category', 'author', 'featuredImage', 'lastEditedBy']
      })

      // Check if post belongs to user's institute
      console.log('Get post - Access check:', {
        postId: id,
        postInstitute: post.institute,
        postInstituteType: typeof post.institute,
        userInstitute: user.institute,
        userInstituteType: typeof user.institute
      })

      // Compare institute IDs (handle both string and number types)
      const postInstituteId = typeof post.institute === 'object'
        ? post.institute?.id
        : post.institute
      const userInstituteId = parseInt(user.institute)

      if (parseInt(postInstituteId) !== userInstituteId) {
        return Response.json({
          success: false,
          error: `Access denied. Post belongs to institute ${postInstituteId}, user belongs to institute ${userInstituteId}`
        }, { status: 403 })
      }

      return Response.json({
        success: true,
        post
      })
    } catch (error) {
      console.error('Get blog post error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch blog post'
      }, { status: 500 })
    }
  }
)

// Create blog post
export const createBlogPostEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/posts',
  'post',
  async (req) => {
    try {
      const { user } = req
      const postData = await req.json()

      console.log('Create blog post - User info:', {
        userId: user.id,
        institute: user.institute,
        instituteType: typeof user.institute,
        legacyRole: user.legacyRole
      })
      console.log('Create blog post - Post data before processing:', postData)

      // Validate institute ID
      const instituteId = parseInt(user.institute)
      if (isNaN(instituteId)) {
        throw new Error(`Invalid institute ID: ${user.institute}`)
      }

      // Ensure institute is set correctly (though hooks should handle this)
      postData.institute = instituteId
      postData.author = user.id

      // Handle category field - convert to proper format if needed
      if (postData.category && typeof postData.category === 'string') {
        // If category is sent as string ID, convert to number
        const categoryId = parseInt(postData.category)
        if (!isNaN(categoryId)) {
          postData.category = categoryId
        } else {
          // If category ID is invalid, remove it
          delete postData.category
        }
      }

      console.log('Create blog post - Final data before creation:', {
        title: postData.title,
        slug: postData.slug,
        category: postData.category,
        categoryType: typeof postData.category,
        institute: postData.institute,
        instituteType: typeof postData.institute,
        author: postData.author
      })

      console.log('Create blog post - Post data after processing:', {
        ...postData,
        institute: postData.institute,
        instituteType: typeof postData.institute,
        content: '[CONTENT_HIDDEN]' // Don't log full content
      })

      // Handle richText content field - use Payload CMS Lexical format
      if (!postData.content) {
        // Provide default empty richText structure
        postData.content = {
          root: {
            type: 'root',
            children: [{
              type: 'paragraph',
              children: [{
                type: 'text',
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: '',
                version: 1
              }],
              direction: null,
              format: '',
              indent: 0,
              textFormat: 0,
              textStyle: '',
              version: 1
            }],
            direction: null,
            format: '',
            indent: 0,
            version: 1
          }
        }
      } else if (typeof postData.content === 'string') {
        // Convert string to richText format
        postData.content = {
          root: {
            type: 'root',
            children: [{
              type: 'paragraph',
              children: [{
                type: 'text',
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: postData.content,
                version: 1
              }],
              direction: null,
              format: '',
              indent: 0,
              textFormat: 0,
              textStyle: '',
              version: 1
            }],
            direction: null,
            format: '',
            indent: 0,
            version: 1
          }
        }
      }

      // Check if category exists and belongs to user's institute before creating post
      if (postData.category) {
        try {
          console.log('Validating category:', postData.category)

          // Skip validation if user institute is invalid - let Payload handle it
          if (user.institute === 'NaN' || isNaN(parseInt(user.institute))) {
            console.log('User institute invalid, skipping category validation but keeping category')
          } else {
            const categoryExists = await req.payload.findByID({
              collection: 'blog-categories',
              id: postData.category
            })

            console.log('Category found:', {
              id: categoryExists.id,
              name: categoryExists.name,
              institute: categoryExists.institute,
              instituteType: typeof categoryExists.institute
            })

            // Check if category belongs to user's institute
            const categoryInstituteId = typeof categoryExists.institute === 'object'
              ? categoryExists.institute?.id
              : categoryExists.institute
            const userInstituteId = parseInt(user.institute)

            if (parseInt(categoryInstituteId) !== userInstituteId) {
              console.log('Category does not belong to user institute, but keeping it for now')
              // Don't remove category - let Payload CMS filterOptions handle it
            }
          }
        } catch (error: any) {
          console.log('Category validation failed, but keeping category:', error.message)
          // Don't remove category - let Payload CMS handle validation
        }
      }

      console.log('Final postData before creation:', {
        title: postData.title,
        slug: postData.slug,
        category: postData.category,
        institute: postData.institute,
        author: postData.author
      })

      let post
      try {
        post = await req.payload.create({
          collection: 'blog-posts',
          data: postData
        })
      } catch (categoryError: any) {
        // If creation fails and we have a category, try without category
        if (postData.category && categoryError.message?.includes('Category')) {
          console.log('Post creation failed with category, retrying without category')
          const postDataWithoutCategory = { ...postData }
          delete postDataWithoutCategory.category

          post = await req.payload.create({
            collection: 'blog-posts',
            data: postDataWithoutCategory
          })

          console.log('Post created successfully without category')
        } else {
          throw categoryError
        }
      }

      console.log('Blog post created successfully:', post.id)

      return Response.json({
        success: true,
        post,
        message: 'Blog post created successfully'
      })
    } catch (error: any) {
      console.error('Create blog post error:', error)
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      return Response.json({
        success: false,
        error: `Failed to create blog post: ${error.message || error}`
      }, { status: 500 })
    }
  }
)

// Update blog post
export const updateBlogPostEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/posts/:id',
  'patch',
  async (req) => {
    try {
      const { user } = req
      // Extract ID from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const id = pathParts[pathParts.length - 1]

      if (!id || id === 'undefined') {
        return Response.json({
          success: false,
          error: 'Post ID is required'
        }, { status: 400 })
      }

      const updateData = await req.json()

      // Check if post exists and belongs to user's institute
      const existingPost = await req.payload.findByID({
        collection: 'blog-posts',
        id
      })

      console.log('Update post - Access check:', {
        postId: id,
        postInstitute: existingPost.institute,
        postInstituteType: typeof existingPost.institute,
        userInstitute: user.institute,
        userInstituteType: typeof user.institute,
        match: existingPost.institute == user.institute
      })

      // Compare institute IDs (handle both string and number types)
      const postInstituteId = typeof existingPost.institute === 'object'
        ? existingPost.institute?.id
        : existingPost.institute
      const userInstituteId = parseInt(user.institute)

      if (parseInt(postInstituteId) !== userInstituteId) {
        return Response.json({
          success: false,
          error: `Access denied. Post belongs to institute ${postInstituteId}, user belongs to institute ${userInstituteId}`
        }, { status: 403 })
      }

      // Check if user can edit this post
      if (user.legacyRole !== 'institute_admin' && existingPost.author !== user.id) {
        return Response.json({
          success: false,
          error: 'You can only edit your own posts'
        }, { status: 403 })
      }

      updateData.lastEditedBy = user.id

      // Handle category field - convert to proper format if needed
      if (updateData.category && typeof updateData.category === 'string') {
        // If category is sent as string ID, convert to number
        const categoryId = parseInt(updateData.category)
        if (!isNaN(categoryId)) {
          updateData.category = categoryId
        }
      }

      // Handle richText content field - use Payload CMS Lexical format
      if (updateData.content !== undefined) {
        if (!updateData.content) {
          // Provide default empty richText structure
          updateData.content = {
            root: {
              type: 'root',
              children: [{
                type: 'paragraph',
                children: [{
                  type: 'text',
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: '',
                  version: 1
                }],
                direction: null,
                format: '',
                indent: 0,
                textFormat: 0,
                textStyle: '',
                version: 1
              }],
              direction: null,
              format: '',
              indent: 0,
              version: 1
            }
          }
        } else if (typeof updateData.content === 'string') {
          // Convert string to richText format
          updateData.content = {
            root: {
              type: 'root',
              children: [{
                type: 'paragraph',
                children: [{
                  type: 'text',
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: updateData.content,
                  version: 1
                }],
                direction: null,
                format: '',
                indent: 0,
                textFormat: 0,
                textStyle: '',
                version: 1
              }],
              direction: null,
              format: '',
              indent: 0,
              version: 1
            }
          }
        }
      }

      const post = await req.payload.update({
        collection: 'blog-posts',
        id,
        data: updateData
      })

      return Response.json({
        success: true,
        post,
        message: 'Blog post updated successfully'
      })
    } catch (error) {
      console.error('Update blog post error:', error)
      return Response.json({
        success: false,
        error: 'Failed to update blog post'
      }, { status: 500 })
    }
  }
)

// Delete blog post
export const deleteBlogPostEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/posts/:id',
  'delete',
  async (req) => {
    try {
      const { user } = req
      // Extract ID from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const id = pathParts[pathParts.length - 1]

      if (!id || id === 'undefined') {
        return Response.json({
          success: false,
          error: 'Post ID is required'
        }, { status: 400 })
      }

      // Only institute admin can delete posts
      if (user.legacyRole !== 'institute_admin') {
        return Response.json({
          success: false,
          error: 'Only institute admin can delete posts'
        }, { status: 403 })
      }

      // Check if post exists and belongs to user's institute
      const existingPost = await req.payload.findByID({
        collection: 'blog-posts',
        id
      })

      console.log('Delete post - Access check:', {
        postId: id,
        postInstitute: existingPost.institute,
        postInstituteType: typeof existingPost.institute,
        userInstitute: user.institute,
        userInstituteType: typeof user.institute
      })

      // Compare institute IDs (handle both string and number types)
      const postInstituteId = typeof existingPost.institute === 'object'
        ? existingPost.institute?.id
        : existingPost.institute
      const userInstituteId = parseInt(user.institute)

      if (parseInt(postInstituteId) !== userInstituteId) {
        return Response.json({
          success: false,
          error: `Access denied. Post belongs to institute ${postInstituteId}, user belongs to institute ${userInstituteId}`
        }, { status: 403 })
      }

      await req.payload.delete({
        collection: 'blog-posts',
        id
      })

      return Response.json({
        success: true,
        message: 'Blog post deleted successfully'
      })
    } catch (error) {
      console.error('Delete blog post error:', error)
      return Response.json({
        success: false,
        error: 'Failed to delete blog post'
      }, { status: 500 })
    }
  }
)

// Get all blog categories for institute
export const getBlogCategoriesEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/categories',
  'get',
  async (req) => {
    try {
      const { user } = req
      const { page = 1, limit = 50 } = req.query

      const categories = await req.payload.find({
        collection: 'blog-categories',
        where: {
          institute: {
            equals: user.institute
          }
        },
        limit: parseInt(limit as string),
        page: parseInt(page as string),
        sort: 'displayOrder',
        populate: ['parentCategory', 'createdBy']
      })

      return Response.json({
        success: true,
        categories: categories.docs,
        pagination: {
          page: categories.page,
          limit: categories.limit,
          totalPages: categories.totalPages,
          totalDocs: categories.totalDocs,
          hasNextPage: categories.hasNextPage,
          hasPrevPage: categories.hasPrevPage
        }
      })
    } catch (error) {
      console.error('Get blog categories error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch blog categories'
      }, { status: 500 })
    }
  }
)

// Create blog category
export const createBlogCategoryEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/categories',
  'post',
  async (req) => {
    try {
      const { user } = req
      const categoryData = await req.json()

      console.log('Create blog category - User info:', {
        userId: user.id,
        institute: user.institute,
        instituteType: typeof user.institute,
        legacyRole: user.legacyRole
      })
      console.log('Create blog category - Category data before processing:', categoryData)

      // Validate institute ID
      const instituteId = parseInt(user.institute)
      if (isNaN(instituteId)) {
        throw new Error(`Invalid institute ID: ${user.institute}`)
      }

      // Auto-generate slug if name is provided but slug is empty
      if (categoryData.name && (!categoryData.slug || categoryData.slug.trim() === '')) {
        categoryData.slug = categoryData.name
          .toLowerCase()
          .trim()
          .replace(/[^a-z0-9\s]+/g, '') // Remove special characters but keep spaces
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/(^-|-$)/g, '') // Remove leading/trailing hyphens
      }

      // Ensure institute is set correctly (though hooks should handle this)
      categoryData.institute = instituteId
      categoryData.createdBy = user.id

      console.log('Create blog category - Category data after processing:', {
        ...categoryData,
        institute: categoryData.institute,
        instituteType: typeof categoryData.institute
      })

      const category = await req.payload.create({
        collection: 'blog-categories',
        data: categoryData
      })

      console.log('Blog category created successfully:', {
        id: category.id,
        institute: category.institute,
        name: category.name
      })

      return Response.json({
        success: true,
        category,
        message: 'Blog category created successfully'
      })
    } catch (error: any) {
      console.error('Create blog category error:', error)
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      return Response.json({
        success: false,
        error: `Failed to create blog category: ${error.message || error}`
      }, { status: 500 })
    }
  }
)

// Update blog category
export const updateBlogCategoryEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/categories/:id',
  'patch',
  async (req) => {
    try {
      const { user } = req
      // Extract ID from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const id = pathParts[pathParts.length - 1]

      if (!id || id === 'undefined') {
        return Response.json({
          success: false,
          error: 'Category ID is required'
        }, { status: 400 })
      }

      const updateData = await req.json()

      // Auto-generate slug if name is provided but slug is empty
      if (updateData.name && (!updateData.slug || updateData.slug.trim() === '')) {
        updateData.slug = updateData.name
          .toLowerCase()
          .trim()
          .replace(/[^a-z0-9\s]+/g, '') // Remove special characters but keep spaces
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/(^-|-$)/g, '') // Remove leading/trailing hyphens
      }

      console.log('Update blog category - Data after slug generation:', {
        name: updateData.name,
        slug: updateData.slug,
        id: id
      })

      // Check if category exists and belongs to user's institute
      const existingCategory = await req.payload.findByID({
        collection: 'blog-categories',
        id
      })

      console.log('Update category - Access check:', {
        categoryId: id,
        categoryInstitute: existingCategory.institute,
        categoryInstituteType: typeof existingCategory.institute,
        userInstitute: user.institute,
        userInstituteType: typeof user.institute,
        match: existingCategory.institute == user.institute
      })

      // Compare institute IDs (handle both string and number types)
      const categoryInstituteId = typeof existingCategory.institute === 'object'
        ? existingCategory.institute?.id
        : existingCategory.institute
      const userInstituteId = parseInt(user.institute)

      if (parseInt(categoryInstituteId) !== userInstituteId) {
        return Response.json({
          success: false,
          error: `Access denied. Category belongs to institute ${categoryInstituteId}, user belongs to institute ${userInstituteId}`
        }, { status: 403 })
      }

      // Check if user can edit this category
      if (user.legacyRole !== 'institute_admin' && existingCategory.createdBy !== user.id) {
        return Response.json({
          success: false,
          error: 'You can only edit categories you created'
        }, { status: 403 })
      }

      const category = await req.payload.update({
        collection: 'blog-categories',
        id,
        data: updateData
      })

      return Response.json({
        success: true,
        category,
        message: 'Blog category updated successfully'
      })
    } catch (error) {
      console.error('Update blog category error:', error)
      return Response.json({
        success: false,
        error: 'Failed to update blog category'
      }, { status: 500 })
    }
  }
)

// Delete blog category
export const deleteBlogCategoryEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/blog/categories/:id',
  'delete',
  async (req) => {
    try {
      const { user } = req
      // Extract ID from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const id = pathParts[pathParts.length - 1]

      if (!id || id === 'undefined') {
        return Response.json({
          success: false,
          error: 'Category ID is required'
        }, { status: 400 })
      }

      // Only institute admin can delete categories
      if (user.legacyRole !== 'institute_admin') {
        return Response.json({
          success: false,
          error: 'Only institute admin can delete categories'
        }, { status: 403 })
      }

      // Check if category exists and belongs to user's institute
      const existingCategory = await req.payload.findByID({
        collection: 'blog-categories',
        id
      })

      if (existingCategory.institute !== user.institute) {
        return Response.json({
          success: false,
          error: 'Access denied'
        }, { status: 403 })
      }

      // Check if category has posts
      const postsWithCategory = await req.payload.find({
        collection: 'blog-posts',
        where: {
          category: {
            equals: id
          }
        },
        limit: 1
      })

      if (postsWithCategory.totalDocs > 0) {
        return Response.json({
          success: false,
          error: 'Cannot delete category that has posts. Please reassign posts first.'
        }, { status: 400 })
      }

      await req.payload.delete({
        collection: 'blog-categories',
        id
      })

      return Response.json({
        success: true,
        message: 'Blog category deleted successfully'
      })
    } catch (error) {
      console.error('Delete blog category error:', error)
      return Response.json({
        success: false,
        error: 'Failed to delete blog category'
      }, { status: 500 })
    }
  }
)
