import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Get institute ID from user
      const instituteId = typeof user.institute === 'object' ? user.institute.id : user.institute

      if (!instituteId) {
        return Response.json({
          success: false,
          error: 'No institute assigned to user'
        }, { status: 403 })
      }

      // Add user and institute information to request for convenience
      req.userId = user.id
      req.userEmail = user.email
      req.userName = `${user.firstName || ''} ${user.lastName || ''}`.trim()
      req.instituteId = instituteId
      req.userRole = user.legacyRole || user.role

      return handler(req)
    }
  }
}

// Helper function to parse URL parameters
const parseUrlParams = (req: any) => {
  try {
    const url = new URL(req.url)
    return {
      page: parseInt(url.searchParams.get('page') || '1'),
      limit: parseInt(url.searchParams.get('limit') || '20'),
      search: url.searchParams.get('search') || '',
      status: url.searchParams.get('status') || '',
      branch: url.searchParams.get('branch') || ''
    }
  } catch (error) {
    return {
      page: 1,
      limit: 20,
      search: '',
      status: '',
      branch: ''
    }
  }
}

// Helper function to parse request body
const parseRequestBody = async (req: any) => {
  try {
    return req.json ? await req.json() : req.body
  } catch (error) {
    return {}
  }
}

// Get institute branches
export const getInstituteBranchesEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/branches',
  'get',
  async (req: any) => {
    try {
      const { page, limit, search } = parseUrlParams(req)
      const instituteId = req.instituteId

      // Build search query
      const whereClause: any = {
        institute: { equals: instituteId }
      }

      if (search) {
        whereClause.or = [
          { name: { contains: search } },
          { code: { contains: search } },
          { address: { contains: search } }
        ]
      }

      // Fetch branches for this institute
      const branches = await req.payload.find({
        collection: 'branches',
        where: whereClause,
        depth: 2,
        limit,
        page,
        sort: '-createdAt'
      })

      return Response.json({
        success: true,
        data: branches.docs,
        pagination: {
          page: branches.page,
          limit: branches.limit,
          totalPages: branches.totalPages,
          totalDocs: branches.totalDocs,
          hasNextPage: branches.hasNextPage,
          hasPrevPage: branches.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Get institute branches error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch institute branches'
      }, { status: 500 })
    }
  }
)

// Create new branch
export const createBranchEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/branches',
  'post',
  async (req) => {
    try {
      const instituteId = req.instituteId
      const requestBody = await parseRequestBody(req)

      console.log('🔍 Branch Creation Request:', {
        instituteId,
        userId: req.userId,
        requestBody: JSON.stringify(requestBody, null, 2)
      })

      const {
        name,
        code,
        location,
        contact,
        taxInformation,
        isHeadOffice,
        workingDays
      } = requestBody

      // Validate required fields
      if (!name) {
        return Response.json({
          success: false,
          error: 'Branch name is required'
        }, { status: 400 })
      }

      if (!location?.address || !location?.country || !location?.state || !location?.district) {
        return Response.json({
          success: false,
          error: 'Complete location information is required'
        }, { status: 400 })
      }

      // Validate that institute and location IDs exist
      console.log('🔍 Validating IDs:', {
        instituteId,
        countryId: location.country,
        stateId: location.state,
        districtId: location.district
      })

      try {
        // Convert string IDs to integers for validation
        const countryId = parseInt(location.country)
        const stateId = parseInt(location.state)
        const districtId = parseInt(location.district)

        console.log('🔍 Converted IDs:', { countryId, stateId, districtId })

        // Validate institute exists
        const institute = await req.payload.findByID({
          collection: 'institutes',
          id: instituteId
        })

        // Validate location IDs exist
        const country = await req.payload.findByID({
          collection: 'countries',
          id: countryId
        })

        const state = await req.payload.findByID({
          collection: 'states',
          id: stateId
        })

        const district = await req.payload.findByID({
          collection: 'districts',
          id: districtId
        })

        console.log('✅ Validation passed:', {
          institute: institute.name,
          country: country.name,
          state: state.name,
          district: district.name
        })

      } catch (validationError: any) {
        console.error('❌ Validation failed:', {
          error: validationError.message,
          instituteId,
          locationIds: {
            country: location.country,
            state: location.state,
            district: location.district
          }
        })
        return Response.json({
          success: false,
          error: `Invalid location data: ${validationError.message}`
        }, { status: 400 })
      }

      // Generate code if not provided
      const branchCode = code || name.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 10)

      // Check if branch code already exists for this institute
      const existingBranch = await req.payload.find({
        collection: 'branches',
        where: {
          institute: { equals: instituteId },
          code: { equals: branchCode }
        },
        limit: 1
      })

      if (existingBranch.docs.length > 0) {
        return Response.json({
          success: false,
          error: 'Branch code already exists for this institute'
        }, { status: 400 })
      }

      // Prepare branch data to match exact database columns
      const branchData: any = {
        // Basic fields
        name,
        code: branchCode,
        institute: instituteId,

        // Location fields (matching database structure)
        location: {
          address: location.address,
          country: parseInt(location.country), // Convert string to integer
          state: parseInt(location.state),     // Convert string to integer
          district: parseInt(location.district), // Convert string to integer
          pincode: location.pincode || null,
          coordinates: {
            latitude: location.coordinates?.latitude || null,
            longitude: location.coordinates?.longitude || null
          }
        },

        // Contact fields
        contact: {
          phone: contact?.phone || null,
          email: contact?.email || null,
          website: contact?.website || null
        },

        // Tax information fields
        taxInformation: {
          gstNumber: taxInformation?.gstNumber || null,
          panNumber: taxInformation?.panNumber || null,
          taxRegistrationNumber: taxInformation?.taxRegistrationNumber || null,
          isGstRegistered: taxInformation?.isGstRegistered || false
        },

        // Status fields
        isActive: true,
        isHeadOffice: isHeadOffice || false,
        isDeleted: false,

        // Working days (matching database columns)
        workingDays: {
          monday: workingDays?.monday ?? true,
          tuesday: workingDays?.tuesday ?? true,
          wednesday: workingDays?.wednesday ?? true,
          thursday: workingDays?.thursday ?? true,
          friday: workingDays?.friday ?? true,
          saturday: workingDays?.saturday ?? true,
          sunday: workingDays?.sunday ?? false
        },

        // Operating hours (matching database columns)
        operatingHours: {
          openTime: requestBody.operatingHours?.openTime || '09:00',
          closeTime: requestBody.operatingHours?.closeTime || '18:00'
        },

        // Audit fields
        createdBy: req.userId
      }

      console.log('🚀 Creating branch with data:', JSON.stringify(branchData, null, 2))

      // Create branch with comprehensive data
      const branch = await req.payload.create({
        collection: 'branches',
        data: branchData
      })

      return Response.json({
        success: true,
        data: branch,
        message: 'Branch created successfully'
      })

    } catch (error: any) {
      console.error('❌ Create branch error:', {
        error: error.message,
        stack: error.stack,
        details: error.details || error.data || error
      })

      // Return more specific error message if available
      const errorMessage = error.message || 'Failed to create branch'
      return Response.json({
        success: false,
        error: errorMessage
      }, { status: 500 })
    }
  }
)

// Update branch
export const updateBranchEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/branches/:id',
  'patch',
  async (req) => {
    try {
      const instituteId = req.instituteId
      const branchId = req.params.id
      const updateData = await parseRequestBody(req)

      // Verify branch belongs to this institute
      const existingBranch = await req.payload.findByID({
        collection: 'branches',
        id: branchId
      })

      if (!existingBranch) {
        return Response.json({
          success: false,
          error: 'Branch not found'
        }, { status: 404 })
      }

      const branchInstituteId = typeof existingBranch.institute === 'object'
        ? existingBranch.institute.id
        : existingBranch.institute

      if (branchInstituteId !== instituteId) {
        return Response.json({
          success: false,
          error: 'You can only update branches from your institute'
        }, { status: 403 })
      }

      // Check if new code conflicts with existing branches (if code is being changed)
      if (updateData.code && updateData.code !== existingBranch.code) {
        const codeConflict = await req.payload.find({
          collection: 'branches',
          where: {
            institute: { equals: instituteId },
            code: { equals: updateData.code },
            id: { not_equals: branchId }
          },
          limit: 1
        })

        if (codeConflict.docs.length > 0) {
          return Response.json({
            success: false,
            error: 'Branch code already exists for this institute'
          }, { status: 400 })
        }
      }

      // Update branch with comprehensive data
      const updatedBranch = await req.payload.update({
        collection: 'branches',
        id: branchId,
        data: {
          ...updateData,
          updatedBy: req.userId
        }
      })

      return Response.json({
        success: true,
        data: updatedBranch,
        message: 'Branch updated successfully'
      })

    } catch (error) {
      console.error('Update branch error:', error)
      return Response.json({
        success: false,
        error: 'Failed to update branch'
      }, { status: 500 })
    }
  }
)

// Delete branch
export const deleteBranchEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/branches/:id',
  'delete',
  async (req) => {
    try {
      const instituteId = req.instituteId
      const branchId = req.params.id

      // Verify branch belongs to this institute
      const existingBranch = await req.payload.findByID({
        collection: 'branches',
        id: branchId
      })

      if (!existingBranch) {
        return Response.json({
          success: false,
          error: 'Branch not found'
        }, { status: 404 })
      }

      const branchInstituteId = typeof existingBranch.institute === 'object'
        ? existingBranch.institute.id
        : existingBranch.institute

      if (branchInstituteId !== instituteId) {
        return Response.json({
          success: false,
          error: 'You can only delete branches from your institute'
        }, { status: 403 })
      }

      // Check if branch has students
      const studentsInBranch = await req.payload.find({
        collection: 'users',
        where: {
          branch: { equals: branchId },
          legacyRole: { equals: 'student' }
        },
        limit: 1
      })

      if (studentsInBranch.docs.length > 0) {
        return Response.json({
          success: false,
          error: 'Cannot delete branch with enrolled students'
        }, { status: 400 })
      }

      // Soft delete branch
      await req.payload.update({
        collection: 'branches',
        id: branchId,
        data: {
          isActive: false,
          isDeleted: true,
          deletedAt: new Date(),
          deletedBy: req.userId
        }
      })

      return Response.json({
        success: true,
        message: 'Branch deleted successfully'
      })

    } catch (error) {
      console.error('Delete branch error:', error)
      return Response.json({
        success: false,
        error: 'Failed to delete branch'
      }, { status: 500 })
    }
  }
)

// Debug endpoint to check location data (no auth required for debugging)
export const debugLocationDataEndpoint: Endpoint = {
  path: '/debug/location-data',
  method: 'get',
  handler: async (req: any) => {
    try {
      // Get all countries
      const countries = await req.payload.find({
        collection: 'countries',
        limit: 20
      })

      // Get all states
      const states = await req.payload.find({
        collection: 'states',
        limit: 50
      })

      // Get all districts
      const districts = await req.payload.find({
        collection: 'districts',
        limit: 100
      })

      return Response.json({
        success: true,
        data: {
          countries: countries.docs.map((c: any) => ({
            id: c.id,
            name: c.name,
            code: c.code
          })),
          states: states.docs.map((s: any) => ({
            id: s.id,
            name: s.name,
            code: s.code,
            country: typeof s.country === 'object' ? s.country.id : s.country
          })),
          districts: districts.docs.map((d: any) => ({
            id: d.id,
            name: d.name,
            code: d.code,
            state: typeof d.state === 'object' ? d.state.id : d.state
          }))
        }
      })

    } catch (error: any) {
      console.error('Debug location data error:', error)
      return Response.json({
        success: false,
        error: error.message
      }, { status: 500 })
    }
  }
}
