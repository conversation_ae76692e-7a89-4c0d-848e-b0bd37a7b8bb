import { Endpoint } from 'payload'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Get institute ID from user
      const instituteId = typeof user.institute === 'object' ? user.institute.id : user.institute

      if (!instituteId) {
        return Response.json({
          success: false,
          error: 'No institute assigned to user'
        }, { status: 403 })
      }

      // Get branch ID from user
      const branchId = typeof user.branch === 'object' ? user.branch.id : user.branch

      if (!branchId) {
        return Response.json({
          success: false,
          error: 'No branch assigned to user'
        }, { status: 403 })
      }

      // Add user and institute information to request for convenience
      req.userId = user.id
      req.userEmail = user.email
      req.userName = `${user.firstName || ''} ${user.lastName || ''}`.trim()
      req.instituteId = instituteId
      req.branchId = branchId
      req.userRole = user.legacyRole || user.role

      return handler(req)
    }
  }
}

// GET /api/institute-admin/courses - List courses with pagination
export const getCoursesEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/courses',
  'get',
  async (req: any) => {
    try {
      const { payload } = req
      const { searchParams } = new URL(req.url)
      
      // Get pagination parameters
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '10')
      const offset = (page - 1) * limit

      // Get search parameters
      const search = searchParams.get('search') || ''
      const status = searchParams.get('status') || ''
      const pricing_type = searchParams.get('pricing_type') || ''

      // Build where clause for filtering
      const where: any = {
        and: [
          {
            institute: {
              equals: req.instituteId
            }
          },
          {
            branch: {
              equals: req.branchId
            }
          }
        ]
      }

      // Add search filter
      if (search) {
        where.and.push({
          or: [
            {
              title: {
                contains: search
              }
            },
            {
              description: {
                contains: search
              }
            }
          ]
        })
      }

      // Add status filter
      if (status) {
        where.and.push({
          status: {
            equals: status
          }
        })
      }

      // Add pricing type filter
      if (pricing_type) {
        where.and.push({
          pricing_type: {
            equals: pricing_type
          }
        })
      }

      // Get courses with pagination
      const result = await payload.find({
        collection: 'courses',
        where,
        limit,
        page,
        sort: '-createdAt',
        populate: {
          thumbnail: true,
          created_by: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      return Response.json({
        success: true,
        data: {
          courses: result.docs,
          pagination: {
            page: result.page,
            limit: result.limit,
            totalPages: result.totalPages,
            totalDocs: result.totalDocs,
            hasNextPage: result.hasNextPage,
            hasPrevPage: result.hasPrevPage
          }
        }
      })

    } catch (error) {
      console.error('Error fetching courses:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch courses'
      }, { status: 500 })
    }
  }
)

// POST /api/institute-admin/courses - Create new course
export const createCourseEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/courses',
  'post',
  async (req: any) => {
    try {
      const { payload } = req
      const body = await req.json()
      
      const { title, description, pricing_type, price_amount, discount_percentage, thumbnail } = body

      // Validation
      if (!title || title.trim().length === 0) {
        return Response.json({
          success: false,
          error: 'Title is required'
        }, { status: 400 })
      }

      if (title.length > 100) {
        return Response.json({
          success: false,
          error: 'Title must be 100 characters or less'
        }, { status: 400 })
      }

      if (!description || description.trim().length < 50) {
        return Response.json({
          success: false,
          error: 'Description must be at least 50 characters'
        }, { status: 400 })
      }

      if (!pricing_type || !['free', 'one_time'].includes(pricing_type)) {
        return Response.json({
          success: false,
          error: 'Invalid pricing type'
        }, { status: 400 })
      }

      // Validate pricing for one-time courses
      if (pricing_type === 'one_time') {
        if (!price_amount || price_amount <= 0) {
          return Response.json({
            success: false,
            error: 'Price amount is required for paid courses'
          }, { status: 400 })
        }

        if (discount_percentage && (discount_percentage < 0 || discount_percentage > 100)) {
          return Response.json({
            success: false,
            error: 'Discount percentage must be between 0 and 100'
          }, { status: 400 })
        }
      }

      // Calculate final price
      let final_price = null
      if (pricing_type === 'one_time' && price_amount) {
        const discount = discount_percentage || 0
        final_price = price_amount * (1 - discount / 100)
      }

      // Create course
      const course = await payload.create({
        collection: 'courses',
        data: {
          title: title.trim(),
          description: description.trim(),
          pricing_type,
          price_amount: pricing_type === 'one_time' ? price_amount : null,
          discount_percentage: pricing_type === 'one_time' ? (discount_percentage || 0) : null,
          final_price,
          status: 'draft',
          institute: req.instituteId,
          branch: req.branchId,
          created_by: req.userId,
          thumbnail: thumbnail || null
        }
      })

      return Response.json({
        success: true,
        data: course,
        message: 'Course created successfully'
      }, { status: 201 })

    } catch (error) {
      console.error('Error creating course:', error)
      return Response.json({
        success: false,
        error: 'Failed to create course'
      }, { status: 500 })
    }
  }
)
