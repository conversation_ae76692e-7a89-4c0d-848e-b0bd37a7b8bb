import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'
import { request } from 'http'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Get institute ID from user
      const instituteId = typeof user.institute === 'object' ? user.institute.id : user.institute

      if (!instituteId) {
        return Response.json({
          success: false,
          error: 'No institute assigned to user'
        }, { status: 403 })
      }

      // Add user and institute information to request for convenience
      req.userId = user.id
      req.userEmail = user.email
      req.userName = `${user.firstName || ''} ${user.lastName || ''}`.trim()
      req.instituteId = instituteId
      req.userRole = user.legacyRole || user.role

      return handler(req)
    }
  }
}

// Helper function to parse request body
const parseRequestBody = async (req: any) => {
  try {
    return req.json ? await req.json() : req.body
  } catch (error) {
    return {}
  }
}

// Create domain request
export const createDomainRequestEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/domain-request',
  'post',
  async (req: any) => {
    try {
      // Institute ID is automatically provided by the helper
      const instituteId = req.instituteId
      const { domainName, purpose } = await parseRequestBody(req)

      if (!domainName) {
        return Response.json({
          success: false,
          error: 'Domain name is required'
        }, { status: 400 })
      }

      // Validate domain format
      const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/
      if (!domainRegex.test(domainName)) {
        return Response.json({
          success: false,
          error: 'Please enter a valid domain name (e.g., academy.com)'
        }, { status: 400 })
      }

      // Check if domain already exists
      const existingDomain = await req.payload.find({
        collection: 'domain-requests',
        where: {
          requestedDomain: { equals: domainName.toLowerCase().trim() }
        },
        limit: 1
      })

      if (existingDomain.docs.length > 0) {
        return Response.json({
          success: false,
          error: 'This domain has already been requested'
        }, { status: 400 })
      }

      // Check if institute already has a domain request
      const existingRequest = await req.payload.find({
        collection: 'domain-requests',
        where: {
          institute: { equals: instituteId }
        },
        limit: 1
      })

      if (existingRequest.docs.length > 0) {
        return Response.json({
          success: false,
          error: 'Your institute already has a domain request. Only one domain request is allowed per institute.'
        }, { status: 400 })
      }


      // Verify user exists before creating domain request
      const user = await req.payload.findByID({
        collection: 'users',
        id: req.userId
      })

      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 404 })
      }

    

      // Create domain request with explicit requestedBy field
      const domainRequest = await req.payload.create({
        collection: 'domain-requests',
        data: {
          institute: instituteId,
          requestedDomain: domainName.toLowerCase().trim(),
          status: 'pending',
          purpose: purpose || 'Custom domain for institute website', // Required field
          requestedBy: user.id, // Use verified user ID
          notes: 'Domain request submitted via institute admin panel'
        }
      })

      // Update institute with pending domain
      await req.payload.update({
        collection: 'institutes',
        id: instituteId,
        data: {
          customDomain: domainName.toLowerCase().trim(),
          domainVerified: false
        }
      })

      return Response.json({
        success: true,
        message: 'Domain request submitted successfully! It will be reviewed by our admin team.'
      })

    } catch (error) {
      console.error('Create domain request error:', error)
      return Response.json({
        success: false,
        error: 'Failed to submit domain request'
      }, { status: 500 })
    }
  }
)

// Get domain request status
export const getDomainRequestStatusEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/domain-request/status',
  'get',
  async (req) => {
    try {
      // Institute ID is automatically provided by the helper
      const instituteId = req.instituteId

      // Get domain request for this institute
      const domainRequest = await req.payload.find({
        collection: 'domain-requests',
        where: {
          institute: { equals: instituteId }
        },
        limit: 1,
        depth: 2
      })

      if (domainRequest.docs.length === 0) {
        return Response.json({
          success: true,
          data: null,
          message: 'No domain request found'
        })
      }

      const request = domainRequest.docs[0]

      return Response.json({
        success: true,
        data: {
          id: request.id,
          domainName: request.requestedDomain,
          status: request.status,
          requestedAt: request.requestedAt,
          reviewedAt: request.reviewedAt,
          reviewedBy: request.reviewedBy,
          rejectionReason: request.rejectionReason,
          notes: request.notes,
          dnsConfiguration: request.dnsConfiguration,
          sslStatus: request.sslStatus
        }
      })

    } catch (error) {
      console.error('Get domain request status error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch domain request status'
      }, { status: 500 })
    }
  }
)
