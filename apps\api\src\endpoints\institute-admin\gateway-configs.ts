import { Endpoint } from 'payload'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Debug logging
      console.log('Gateway endpoint user info:', {
        userId: user.id,
        institute: user.institute,
        instituteType: typeof user.institute,
        legacyRole: user.legacyRole
      })

      if (!user.institute || user.institute === 'NaN' || isNaN(parseInt(user.institute))) {
        console.error('Invalid institute ID:', user.institute)
        return Response.json({
          success: false,
          error: `No valid institute assigned to user. Institute ID: ${user.institute}`
        }, { status: 403 })
      }

      return handler(req)
    }
  }
}

// Get available payment gateways for institute
export const getAvailableGatewaysEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/available-gateways',
  'get',
  async (req: any) => {
    try {
      const { payload } = req

      // Fetch all active payment gateways
      const gateways = await payload.find({
        collection: 'payment-gateways',
        where: {
          isActive: { equals: true }
        },
        limit: 100,
        sort: 'name'
      })

      return Response.json({
        success: true,
        gateways: gateways.docs
      })
    } catch (error: any) {
      console.error('Error fetching available gateways:', error)
      return Response.json({
        error: 'Failed to fetch available gateways',
        details: error.message
      }, { status: 500 })
    }
  }
)

// Get institute's configured gateways
export const getInstituteGatewayConfigsEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/gateway-configs',
  'get',
  async (req: any) => {
    try {
      const { user, payload } = req

      // Fetch institute's gateway configurations
      const configs = await payload.find({
        collection: 'institute-gateways',
        where: {
          institute: { equals: user.institute }
        },
        depth: 2,
        limit: 100,
        sort: '-updatedAt'
      })

      return Response.json({
        success: true,
        configs: configs.docs
      })
    } catch (error: any) {
      console.error('Error fetching gateway configs:', error)
      return Response.json({
        error: 'Failed to fetch gateway configurations',
        details: error.message
      }, { status: 500 })
    }
  }
)

// Get specific gateway configuration
export const getGatewayConfigEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/gateway-configs/:gatewayId',
  'get',
  async (req: any) => {
    try {
      const { user, payload } = req
      const gatewayId = req.routeParams?.gatewayId || req.params?.gatewayId

      // Find existing configuration
      const existingConfig = await payload.find({
        collection: 'institute-gateways',
        where: {
          and: [
            { institute: { equals: user.institute } },
            { gateway: { equals: gatewayId } }
          ]
        },
        depth: 2,
        limit: 1
      })

      if (existingConfig.docs.length === 0) {
        return Response.json({ error: 'Gateway configuration not found' }, { status: 404 })
      }

      return Response.json({
        success: true,
        config: existingConfig.docs[0]
      })
    } catch (error: any) {
      console.error('Error fetching gateway config:', error)
      return Response.json({
        error: 'Failed to fetch gateway configuration',
        details: error.message
      }, { status: 500 })
    }
  }
)

// Create or update gateway configuration
export const saveGatewayConfigEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/gateway-configs',
  'post',
  async (req: any) => {
    try {
      const { user, payload } = req
      const body = await req.json()
      const { gatewayId, configuration, isActive = false, testMode = true, notes = '', isPrimary = false } = body

      // Validate required fields
      if (!gatewayId) {
        return Response.json({ error: 'Gateway ID is required' }, { status: 400 })
      }

      if (!configuration || typeof configuration !== 'object') {
        return Response.json({ error: 'Configuration object is required' }, { status: 400 })
      }

      // Check if gateway exists and is active
      const gateway = await payload.findByID({
        collection: 'payment-gateways',
        id: gatewayId
      })

      if (!gateway || !gateway.isActive) {
        return Response.json({ error: 'Gateway not found or not active' }, { status: 400 })
      }

      // Validate configuration against gateway's required fields
      const missingFields = []
      if (gateway.requiredConfigFields) {
        for (const field of gateway.requiredConfigFields) {
          if (field.isRequired && (!configuration[field.key] || configuration[field.key].trim() === '')) {
            missingFields.push(field.label)
          }
        }
      }

      if (missingFields.length > 0) {
        return Response.json({
          error: 'Missing required configuration fields',
          missingFields
        }, { status: 400 })
      }

      // Check if configuration already exists
      const existingConfig = await payload.find({
        collection: 'institute-gateways',
        where: {
          and: [
            { institute: { equals: user.institute } },
            { gateway: { equals: gatewayId } }
          ]
        },
        limit: 1
      })

      let savedConfig

      if (existingConfig.docs.length > 0) {
        // Update existing configuration
        savedConfig = await payload.update({
          collection: 'institute-gateways',
          id: existingConfig.docs[0].id,
          data: {
            configuration,
            isActive,
            testMode,
            notes,
            isPrimary,
            configuredBy: user.id
          }
        })
      } else {
        // Create new configuration
        savedConfig = await payload.create({
          collection: 'institute-gateways',
          data: {
            institute: user.institute,
            gateway: gatewayId,
            configuration,
            isActive,
            testMode,
            notes,
            isPrimary,
            configuredBy: user.id
          }
        })
      }

      // Populate the response
      const populatedConfig = await payload.findByID({
        collection: 'institute-gateways',
        id: savedConfig.id,
        depth: 2
      })

      return Response.json({
        success: true,
        config: populatedConfig,
        message: existingConfig.docs.length > 0 ? 'Gateway configuration updated successfully' : 'Gateway configuration created successfully'
      })
    } catch (error: any) {
      console.error('Error saving gateway config:', error)
      return Response.json({
        error: 'Failed to save gateway configuration',
        details: error.message
      }, { status: 500 })
    }
  }
)

// Delete gateway configuration
export const deleteGatewayConfigEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/gateway-configs/:configId',
  'delete',
  async (req: any) => {
    try {
      const { user, payload } = req
      const configId = req.routeParams?.configId || req.params?.configId

      // Verify the configuration belongs to the user's institute
      const config = await payload.findByID({
        collection: 'institute-gateways',
        id: configId
      })

      if (!config) {
        return Response.json({ error: 'Gateway configuration not found' }, { status: 404 })
      }

      // Compare institute IDs (handle both string and number types)
      const configInstituteId = typeof config.institute === 'object'
        ? config.institute?.id
        : config.institute
      const userInstituteId = parseInt(user.institute)

      console.log('Delete config - Access check:', {
        configId,
        configInstitute: config.institute,
        configInstituteId,
        userInstitute: user.institute,
        userInstituteId,
        match: parseInt(configInstituteId) === userInstituteId
      })

      if (parseInt(configInstituteId) !== userInstituteId) {
        return Response.json({
          error: `Access denied. Config belongs to institute ${configInstituteId}, user belongs to institute ${userInstituteId}`
        }, { status: 403 })
      }

      // Delete the configuration
      await payload.delete({
        collection: 'institute-gateways',
        id: configId
      })

      return Response.json({
        success: true,
        message: 'Gateway configuration deleted successfully'
      })
    } catch (error: any) {
      console.error('Error deleting gateway config:', error)
      return Response.json({
        error: 'Failed to delete gateway configuration',
        details: error.message
      }, { status: 500 })
    }
  }
)

// Test gateway configuration
export const testGatewayConfigEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/gateway-configs/:configId/test',
  'post',
  async (req: any) => {
    try {
      const { user, payload } = req
      const configId = req.routeParams?.configId || req.params?.configId

      // Verify the configuration belongs to the user's institute
      const config = await payload.findByID({
        collection: 'institute-gateways',
        id: configId,
        depth: 2
      })

      if (!config) {
        return Response.json({ error: 'Gateway configuration not found' }, { status: 404 })
      }

      // Compare institute IDs (handle both string and number types)
      const configInstituteId = typeof config.institute === 'object'
        ? config.institute?.id
        : config.institute
      const userInstituteId = parseInt(user.institute)

      console.log('Test config - Access check:', {
        configId,
        configInstitute: config.institute,
        configInstituteId,
        userInstitute: user.institute,
        userInstituteId,
        match: parseInt(configInstituteId) === userInstituteId
      })

      if (parseInt(configInstituteId) !== userInstituteId) {
        return Response.json({
          error: `Access denied. Config belongs to institute ${configInstituteId}, user belongs to institute ${userInstituteId}`
        }, { status: 403 })
      }

      // For now, we'll just simulate a test
      // In a real implementation, you would test the actual gateway connection
      const gatewayName = typeof config.gateway === 'object' ? config.gateway.name : 'Gateway'
      const testResult = {
        success: true,
        message: `${gatewayName} configuration test successful`,
        timestamp: new Date().toISOString()
      }

      // Update the lastTestedAt timestamp
      await payload.update({
        collection: 'institute-gateways',
        id: configId,
        data: {
          lastTestedAt: new Date().toISOString()
        }
      })

      return Response.json(testResult)
    } catch (error: any) {
      console.error('Error testing gateway config:', error)
      return Response.json({
        error: 'Failed to test gateway configuration',
        details: error.message
      }, { status: 500 })
    }
  }
)
