// Institute Admin API Endpoints
// These endpoints are specifically for institute administrators to manage their own institute data

// Institute management endpoints
export {
  getInstituteDetailsEndpoint,
  updateInstituteDetailsEndpoint,
  getInstituteStatsEndpoint
} from './institute'

// Branch management endpoints
export {
  getInstituteBranchesEndpoint,
  createBranchEndpoint,
  updateBranchEndpoint,
  deleteBranchEndpoint
} from './branches'

// Comprehensive Student management endpoints
export {
  getStudentsEndpoint,
  createStudentEndpoint,
  updateStudentEndpoint,
  getStudentByIdEndpoint,
  deleteStudentEndpoint,
  getBranchesEndpoint,
  getRolesEndpoint
} from './students'

// Staff management endpoints
export {
  getStaffEndpoint,
  createStaffEndpoint,
  updateStaffEndpoint,
  getStaffByIdEndpoint,
  deleteStaffEndpoint,
  toggleStaffStatusEndpoint
} from './staff'

// Course management endpoints
export {
  getCoursesEndpoint,
  createCourseEndpoint
} from './courses'









// Domain request endpoints
export {
  createDomainRequestEndpoint,
  getDomainRequestStatusEndpoint
} from './domain-request'

// Theme management endpoints
export {
  getInstituteThemesEndpoint,
  getCurrentInstituteThemeEndpoint,
  applyThemeToInstituteEndpoint,
  previewThemeEndpoint,
  getInstituteThemeHistoryEndpoint,
  getThemesListEndpoint
} from './themes'

// Blog management endpoints
export {
  getBlogPostsEndpoint,
  getBlogPostEndpoint,
  createBlogPostEndpoint,
  updateBlogPostEndpoint,
  deleteBlogPostEndpoint,
  getBlogCategoriesEndpoint,
  createBlogCategoryEndpoint,
  updateBlogCategoryEndpoint,
  deleteBlogCategoryEndpoint
} from './blog'

// Advanced blog features endpoints
export {
  blogSearchEndpoint,
  getTrendingPostsEndpoint,
  getBlogAnalyticsEndpoint
} from './blog-advanced'

// Blog comment management endpoints
export {
  getBlogCommentsEndpoint,
  updateBlogCommentEndpoint,
  deleteBlogCommentEndpoint
} from './blog-comments'

// Payment Gateway Configuration endpoints
export {
  getAvailableGatewaysEndpoint,
  getInstituteGatewayConfigsEndpoint,
  getGatewayConfigEndpoint,
  saveGatewayConfigEndpoint,
  deleteGatewayConfigEndpoint,
  testGatewayConfigEndpoint
} from './gateway-configs'
