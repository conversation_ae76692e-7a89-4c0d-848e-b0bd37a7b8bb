import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Get institute ID from user
      const instituteId = typeof user.institute === 'object' ? user.institute.id : user.institute

      if (!instituteId) {
        return Response.json({
          success: false,
          error: 'No institute assigned to user'
        }, { status: 403 })
      }

      // Add user and institute information to request for convenience
      req.userId = user.id
      req.userEmail = user.email
      req.userName = `${user.firstName || ''} ${user.lastName || ''}`.trim()
      req.instituteId = instituteId
      req.userRole = user.legacyRole || user.role

      return handler(req)
    }
  }
}

// Get institute details for the logged-in institute admin
export const getInstituteDetailsEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/institute',
  'get',
  async (req: any) => {

    try {
      // Get institute ID from user
      const instituteId = typeof req.user.institute === 'object' ? req.user.institute.id : req.user.institute

      if (!instituteId) {
        return Response.json({
          success: false,
          error: 'No institute assigned to user'
        }, { status: 403 })
      }

      // Fetch institute details with related data
      const institute = await req.payload.findByID({
        collection: 'institutes',
        id: instituteId,
        depth: 2
      })

      if (!institute) {
        return Response.json({
          success: false,
          error: 'Institute not found'
        }, { status: 404 })
      }

      return Response.json({
        success: true,
        data: institute
      })

    } catch (error) {
      console.error('Get institute details error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch institute details'
      }, { status: 500 })
    }
  }
)

// Update institute details (limited fields)
export const updateInstituteDetailsEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/institute',
  'patch',
  async (req: any) => {
    try {
      const instituteId = req.instituteId

      const body = req.json ? await req.json() : req.body
      const {
        name,
        email,
        phone,
        website,
        tagline,
        description,
        addressStreet,
        zipCode,
        customDomain
      } = body || {}

      // Only allow updating specific fields
      const updateData: any = {}
      if (name) updateData.name = name
      if (email) updateData.email = email
      if (phone) updateData.phone = phone
      if (website) updateData.website = website
      if (tagline) updateData.tagline = tagline
      if (description) updateData.description = description
      if (addressStreet) updateData.addressStreet = addressStreet
      if (zipCode) updateData.zipCode = zipCode

      // Handle custom domain request - create domain request instead of direct update
      if (customDomain) {
        // Check if domain request already exists
        const existingRequest = await req.payload.find({
          collection: 'domain-requests',
          where: {
            institute: { equals: instituteId }
          },
          limit: 1
        })

        if (existingRequest.docs.length > 0) {
          return Response.json({
            success: false,
            error: 'Domain request already exists for this institute'
          }, { status: 400 })
        }

        // Create new domain request
        await req.payload.create({
          collection: 'domain-requests',
          data: {
            institute: instituteId,
            requestedDomain: customDomain.toLowerCase().trim(),
            status: 'pending',
            requestedAt: new Date().toISOString(),
            notes: 'Domain request submitted via institute settings'
          }
        })

        // Update institute with pending domain
        updateData.customDomain = customDomain.toLowerCase().trim()
        updateData.domainVerified = false
      }

      const updatedInstitute = await req.payload.update({
        collection: 'institutes',
        id: instituteId,
        data: updateData
      })

      return Response.json({
        success: true,
        data: updatedInstitute,
        message: 'Institute details updated successfully'
      })

    } catch (error) {
      console.error('Update institute details error:', error)
      return Response.json({
        success: false,
        error: 'Failed to update institute details'
      }, { status: 500 })
    }
  }
)

// Get institute statistics
export const getInstituteStatsEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/institute/stats',
  'get',
  async (req: any) => {
    try {
      const instituteId = req.instituteId

      const url = new URL(req.url)
      const branch = url.searchParams.get('branch')

      // Base query for students
      const studentQuery: any = {
        institute: { equals: instituteId },
        legacyRole: { equals: 'student' }
      }

      // Add branch filter if specified
      if (branch && branch !== 'all') {
        studentQuery.branch = { equals: branch }
      }

      // Get total students count
      const studentsCount = await req.payload.find({
        collection: 'users',
        where: studentQuery,
        limit: 0
      })

      // Get total branches count
      const branchesCount = await req.payload.find({
        collection: 'branches',
        where: {
          institute: { equals: instituteId }
        },
        limit: 0
      })

      // Get total courses count (if courses collection exists)
      let coursesCount = { totalDocs: 0 }
      try {
        coursesCount = await req.payload.find({
          collection: 'courses',
          where: {
            institute: { equals: instituteId }
          },
          limit: 0
        })
      } catch (error) {
        // Courses collection might not exist yet
        console.log('Courses collection not found, defaulting to 0')
      }

      // Get recent students (last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const recentStudents = await req.payload.find({
        collection: 'users',
        where: {
          ...studentQuery,
          createdAt: { greater_than: thirtyDaysAgo.toISOString() }
        },
        limit: 0
      })

      return Response.json({
        success: true,
        data: {
          totalStudents: studentsCount.totalDocs,
          totalBranches: branchesCount.totalDocs,
          totalCourses: coursesCount.totalDocs,
          recentStudents: recentStudents.totalDocs,
          growthRate: studentsCount.totalDocs > 0 ?
            Math.round((recentStudents.totalDocs / studentsCount.totalDocs) * 100) : 0
        }
      })

    } catch (error) {
      console.error('Get institute stats error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch institute statistics'
      }, { status: 500 })
    }
  }
)
