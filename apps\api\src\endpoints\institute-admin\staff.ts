import { Endpoint } from 'payload'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Get institute ID from user
      const instituteId = typeof user.institute === 'object' ? user.institute.id : user.institute

      if (!instituteId) {
        return Response.json({
          success: false,
          error: 'No institute assigned to user'
        }, { status: 403 })
      }

      // Add user and institute information to request for convenience
      req.userId = user.id
      req.userEmail = user.email
      req.userName = `${user.firstName || ''} ${user.lastName || ''}`.trim()
      req.instituteId = instituteId
      req.userRole = user.legacyRole || user.role

      return handler(req)
    }
  }
}

// Helper function to parse request body
const parseRequestBody = async (req: any) => {
  try {
    return await req.json()
  } catch (error) {
    throw new Error('Invalid JSON in request body')
  }
}

// GET staff endpoint - List all staff members for the institute
const getStaffEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/staff',
  'get',
  async (req) => {
    try {
      console.log('=== GET STAFF DEBUG ===')
      
      const {
        page = 1,
        limit = 20,
        search = '',
        role = '',
        branch_id = '',
        status = 'all',
        sort = 'firstName',
        order = 'asc'
      } = req.query

      // Build base query - filter by institute and exclude students
      const whereClause: any = {
        institute: { equals: req.instituteId },
        legacyRole: { not_equals: 'student' }
      }

      // Search functionality
      if (search) {
        whereClause.or = [
          { firstName: { contains: search, options: 'i' } },
          { lastName: { contains: search, options: 'i' } },
          { email: { contains: search, options: 'i' } }
        ]
      }

      // Role filtering
      if (role && role !== 'all') {
        whereClause.legacyRole = { equals: role }
      }

      // Branch filtering
      if (branch_id && branch_id !== 'all') {
        whereClause.branch = { equals: branch_id }
      }

      // Status filtering
      if (status !== 'all') {
        whereClause.isActive = status === 'active'
      }

      console.log('Staff query where clause:', JSON.stringify(whereClause, null, 2))

      // Execute query with pagination
      const staff = await req.payload.find({
        collection: 'users',
        where: whereClause,
        page: parseInt(page),
        limit: parseInt(limit),
        sort: order === 'desc' ? `-${sort}` : sort,
        depth: 2
      })

      console.log(`Found ${staff.docs.length} staff members`)

      return Response.json({
        success: true,
        data: staff.docs,
        pagination: {
          page: staff.page,
          limit: staff.limit,
          totalPages: staff.totalPages,
          totalDocs: staff.totalDocs,
          hasNextPage: staff.hasNextPage,
          hasPrevPage: staff.hasPrevPage
        }
      })

    } catch (error: any) {
      console.error('Get staff error:', error)

      return Response.json({
        success: false,
        error: 'Failed to fetch staff members',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// POST staff endpoint - Create new staff member
const createStaffEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/staff',
  'post',
  async (req) => {
    try {
      console.log('=== STAFF CREATION DEBUG ===')
      
      // Parse request body
      const body = await parseRequestBody(req)
      console.log('Request body:', body)

      const {
        firstName,
        lastName,
        email,
        phone,
        password,
        legacyRole,
        branch_id,
        isActive = true
      } = body

      // Validate required fields
      if (!firstName || !lastName || !email || !password || !legacyRole) {
        return Response.json({
          success: false,
          error: 'Missing required fields: firstName, lastName, email, password, legacyRole'
        }, { status: 400 })
      }

      // Check if email already exists
      const existingUser = await req.payload.find({
        collection: 'users',
        where: {
          email: { equals: email }
        },
        limit: 1
      })

      if (existingUser.docs.length > 0) {
        return Response.json({
          success: false,
          error: 'Email already exists'
        }, { status: 400 })
      }

      // Validate role exists and is level 3 (staff role)
      const roleCheck = await req.payload.find({
        collection: 'roles',
        where: {
          name: { equals: legacyRole },
          level: { equals: '3' }
        },
        limit: 1
      })

      if (roleCheck.docs.length === 0) {
        return Response.json({
          success: false,
          error: 'Invalid role specified'
        }, { status: 400 })
      }

      // Validate branch if provided
      if (branch_id) {
        const branchCheck = await req.payload.findByID({
          collection: 'branches',
          id: branch_id
        })

        if (!branchCheck) {
          return Response.json({
            success: false,
            error: 'Invalid branch specified'
          }, { status: 400 })
        }
      }

      // Create staff member
      const staffData: any = {
        firstName,
        lastName,
        email,
        phone,
        password,
        legacyRole,
        institute: req.instituteId,
        isActive,
        emailVerified: false
      }

      // Add branch if provided
      if (branch_id) {
        staffData.branch = branch_id
      }

      console.log('Creating staff with data:', staffData)

      const newStaff = await req.payload.create({
        collection: 'users',
        data: staffData
      })

      console.log('Staff created successfully:', newStaff.id)

      return Response.json({
        success: true,
        data: newStaff,
        message: 'Staff member created successfully'
      })

    } catch (error: any) {
      console.error('Staff creation error:', error)

      return Response.json({
        success: false,
        error: 'Failed to create staff member',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// GET staff by ID endpoint
const getStaffByIdEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/staff/:id',
  'get',
  async (req) => {
    try {
      const { id } = req.params

      const staff = await req.payload.findByID({
        collection: 'users',
        id,
        depth: 2
      })

      // Verify staff belongs to the same institute
      const staffInstituteId = typeof staff.institute === 'object' ? staff.institute.id : staff.institute
      if (staffInstituteId !== req.instituteId) {
        return Response.json({
          success: false,
          error: 'Staff member not found'
        }, { status: 404 })
      }

      // Verify it's not a student
      if (staff.legacyRole === 'student') {
        return Response.json({
          success: false,
          error: 'Staff member not found'
        }, { status: 404 })
      }

      return Response.json({
        success: true,
        data: staff
      })

    } catch (error: any) {
      console.error('Get staff by ID error:', error)

      return Response.json({
        success: false,
        error: 'Failed to fetch staff member',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// PATCH staff endpoint - Update staff member
const updateStaffEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/staff/:id',
  'patch',
  async (req) => {
    try {
      const { id } = req.params
      const body = await parseRequestBody(req)
      console.log('Updating staff:', id, body)

      // Get existing staff member
      const existingStaff = await req.payload.findByID({
        collection: 'users',
        id
      })

      // Verify staff belongs to the same institute
      const staffInstituteId = typeof existingStaff.institute === 'object' ? existingStaff.institute.id : existingStaff.institute
      if (staffInstituteId !== req.instituteId) {
        return Response.json({
          success: false,
          error: 'Staff member not found'
        }, { status: 404 })
      }

      // Verify it's not a student
      if (existingStaff.legacyRole === 'student') {
        return Response.json({
          success: false,
          error: 'Staff member not found'
        }, { status: 404 })
      }

      const {
        firstName,
        lastName,
        email,
        phone,
        legacyRole,
        branch_id,
        isActive
      } = body

      // Validate email uniqueness if email is being changed
      if (email && email !== existingStaff.email) {
        const existingUser = await req.payload.find({
          collection: 'users',
          where: {
            email: { equals: email },
            id: { not_equals: id }
          },
          limit: 1
        })

        if (existingUser.docs.length > 0) {
          return Response.json({
            success: false,
            error: 'Email already exists'
          }, { status: 400 })
        }
      }

      // Validate role if provided
      if (legacyRole) {
        const roleCheck = await req.payload.find({
          collection: 'roles',
          where: {
            name: { equals: legacyRole },
            level: { equals: '3' }
          },
          limit: 1
        })

        if (roleCheck.docs.length === 0) {
          return Response.json({
            success: false,
            error: 'Invalid role specified'
          }, { status: 400 })
        }
      }

      // Validate branch if provided
      if (branch_id) {
        const branchCheck = await req.payload.findByID({
          collection: 'branches',
          id: branch_id
        })

        if (!branchCheck) {
          return Response.json({
            success: false,
            error: 'Invalid branch specified'
          }, { status: 400 })
        }
      }

      // Build update data
      const updateData: any = {}
      if (firstName !== undefined) updateData.firstName = firstName
      if (lastName !== undefined) updateData.lastName = lastName
      if (email !== undefined) updateData.email = email
      if (phone !== undefined) updateData.phone = phone
      if (legacyRole !== undefined) updateData.legacyRole = legacyRole
      if (branch_id !== undefined) updateData.branch = branch_id
      if (isActive !== undefined) updateData.isActive = isActive

      const updatedStaff = await req.payload.update({
        collection: 'users',
        id,
        data: updateData
      })

      return Response.json({
        success: true,
        data: updatedStaff,
        message: 'Staff member updated successfully'
      })

    } catch (error: any) {
      console.error('Update staff error:', error)

      return Response.json({
        success: false,
        error: 'Failed to update staff member',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// DELETE staff endpoint - Soft delete staff member
const deleteStaffEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/staff/:id',
  'delete',
  async (req) => {
    try {
      const { id } = req.params

      // Get existing staff member
      const existingStaff = await req.payload.findByID({
        collection: 'users',
        id
      })

      // Verify staff belongs to the same institute
      const staffInstituteId = typeof existingStaff.institute === 'object' ? existingStaff.institute.id : existingStaff.institute
      if (staffInstituteId !== req.instituteId) {
        return Response.json({
          success: false,
          error: 'Staff member not found'
        }, { status: 404 })
      }

      // Verify it's not a student
      if (existingStaff.legacyRole === 'student') {
        return Response.json({
          success: false,
          error: 'Staff member not found'
        }, { status: 404 })
      }

      // Soft delete by setting isActive to false
      await req.payload.update({
        collection: 'users',
        id,
        data: {
          isActive: false
        }
      })

      return Response.json({
        success: true,
        message: 'Staff member deleted successfully'
      })

    } catch (error: any) {
      console.error('Delete staff error:', error)

      return Response.json({
        success: false,
        error: 'Failed to delete staff member',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// PATCH staff status endpoint - Toggle staff status
const toggleStaffStatusEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/staff/:id/status',
  'patch',
  async (req) => {
    try {
      const { id } = req.params

      // Get existing staff member
      const existingStaff = await req.payload.findByID({
        collection: 'users',
        id
      })

      // Verify staff belongs to the same institute
      const staffInstituteId = typeof existingStaff.institute === 'object' ? existingStaff.institute.id : existingStaff.institute
      if (staffInstituteId !== req.instituteId) {
        return Response.json({
          success: false,
          error: 'Staff member not found'
        }, { status: 404 })
      }

      // Verify it's not a student
      if (existingStaff.legacyRole === 'student') {
        return Response.json({
          success: false,
          error: 'Staff member not found'
        }, { status: 404 })
      }

      // Toggle status
      const newStatus = !existingStaff.isActive

      const updatedStaff = await req.payload.update({
        collection: 'users',
        id,
        data: {
          isActive: newStatus
        }
      })

      return Response.json({
        success: true,
        data: updatedStaff,
        message: `Staff member ${newStatus ? 'activated' : 'deactivated'} successfully`
      })

    } catch (error: any) {
      console.error('Toggle staff status error:', error)

      return Response.json({
        success: false,
        error: 'Failed to toggle staff status',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// Export all endpoints
export {
  getStaffEndpoint,
  createStaffEndpoint,
  updateStaffEndpoint,
  getStaffByIdEndpoint,
  deleteStaffEndpoint,
  toggleStaffStatusEndpoint
}
