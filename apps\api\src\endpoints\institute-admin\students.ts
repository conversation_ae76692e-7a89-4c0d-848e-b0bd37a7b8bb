import { Endpoint } from 'payload'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Get institute ID from user
      const instituteId = typeof user.institute === 'object' ? user.institute.id : user.institute

      if (!instituteId) {
        return Response.json({
          success: false,
          error: 'No institute assigned to user'
        }, { status: 403 })
      }

      // Add user and institute information to request for convenience
      req.userId = user.id
      req.userEmail = user.email
      req.userName = `${user.firstName || ''} ${user.lastName || ''}`.trim()
      req.instituteId = instituteId
      req.userRole = user.legacyRole || user.role

      return handler(req)
    }
  }
}

// Helper function to parse request body
const parseRequestBody = async (req: any) => {
  try {
    return req.json ? await req.json() : req.body
  } catch (error) {
    return {}
  }
}

// GET students endpoint
const getStudentsEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/students',
  'get',
  async (req) => {
    try {
      console.log('=== GET STUDENTS DEBUG ===')
      
      const {
        page = 1,
        limit = 20,
        search = '',
        status = 'all'
      } = req.query

      // Build base query - filter by institute and student role
      const whereClause: any = {
        legacyRole: { equals: 'student' },
        institute: { equals: req.instituteId }
      }

      // Search functionality
      if (search) {
        whereClause.or = [
          { firstName: { contains: search, options: 'i' } },
          { lastName: { contains: search, options: 'i' } },
          { email: { contains: search, options: 'i' } }
        ]
      }

      // Status filtering
      if (status !== 'all') {
        whereClause.isActive = status === 'active'
      }

      console.log('Query where clause:', whereClause)

      // Fetch students with minimal depth for better performance
      const students = await req.payload.find({
        collection: 'users',
        where: whereClause,
        depth: 1, // Reduced depth for better performance
        limit: parseInt(limit as string),
        page: parseInt(page as string),
        sort: '-createdAt'
      })

      // Get StudentDetails for all students
      const studentIds = students.docs.map((student: any) => student.id)
      let studentDetailsMap = new Map()

      if (studentIds.length > 0) {
        try {
          const studentDetails = await req.payload.find({
            collection: 'student-details',
            where: {
              student: {
                in: studentIds
              }
            },
            depth: 2, // Include location relationships
            limit: studentIds.length
          })

          // Create a map for quick lookup
          studentDetails.docs.forEach((detail: any) => {
            studentDetailsMap.set(detail.student.id || detail.student, detail)
          })
        } catch (error) {
          console.error('Failed to fetch student details:', error)
        }
      }

      // Transform response to include only essential fields
      const transformedStudents = students.docs.map((student: any) => {
        const studentDetail = studentDetailsMap.get(student.id)

        return {
          // Basic student info
          id: student.id,
          firstName: student.firstName,
          lastName: student.lastName,
          email: student.email,
          phone: student.phone,

          // Branch info (only name and code)
          branch: student.branch ? {
            id: student.branch.id,
            name: student.branch.name,
            code: student.branch.code
          } : null,

          // Role info (only name and code)
          role: student.role ? {
            id: student.role.id,
            name: student.role.name,
            code: student.role.code
          } : null,

          // Status and timestamps
          is_active: student.isActive,
          createdAt: student.createdAt,

          // Optional additional fields you might need
          address: student.address,
          dateOfBirth: student.dateOfBirth,
          gender: student.gender,

          // StudentDetails (if available)
          studentDetails: studentDetail ? {
            id: studentDetail.id,
            // Location info
            country: studentDetail.country ? {
              id: studentDetail.country.id,
              name: studentDetail.country.name,
              code: studentDetail.country.code
            } : null,
            state: studentDetail.state ? {
              id: studentDetail.state.id,
              name: studentDetail.state.name,
              code: studentDetail.state.code
            } : null,
            district: studentDetail.district ? {
              id: studentDetail.district.id,
              name: studentDetail.district.name,
              code: studentDetail.district.code
            } : null,
            // Other details
            education: studentDetail.education,
            personalInfo: studentDetail.personalInfo,
            documents: studentDetail.documents,
            additionalInfo: studentDetail.additionalInfo
          } : null
        }
      })

      return Response.json({
        success: true,
        data: transformedStudents,
        pagination: {
          page: students.page,
          limit: students.limit,
          totalPages: students.totalPages,
          totalDocs: students.totalDocs,
          hasNextPage: students.hasNextPage,
          hasPrevPage: students.hasPrevPage
        }
      })

    } catch (error: any) {
      console.error('=== GET STUDENTS ERROR ===')
      console.error('Error details:', error)
      
      return Response.json({
        success: false,
        error: 'Failed to fetch students',
        details: error?.message || 'Unknown error'
      }, { status: 500 })
    }
  }
)

// CREATE student endpoint
const createStudentEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/students',
  'post',
  async (req) => {
    try {
      console.log('=== STUDENT CREATION DEBUG ===')
      
      // Parse request body
      const body = await parseRequestBody(req)
      console.log('Request body:', body)

      const {
        firstName,
        lastName,
        email,
        phone,
        password,
        branch_id,
        branch,    // Preferred field name (maps to branch_id in database)
        address,
        dateOfBirth,
        gender,
        // Location fields (will be stored in StudentDetails)
        country,
        state,
        district,
        // StudentDetails fields (optional)
        studentDetails,
        is_active = true
      } = body

      // Use branch if provided (preferred), otherwise use branch_id for backward compatibility
      const branchId = branch || branch_id
      // Convert to number for Payload relationship field
      const branchIdNumber = branchId ? parseInt(branchId, 10) : null

      // Convert location IDs to numbers for Payload relationship fields
      const countryIdNumber = country ? parseInt(country, 10) : null
      const stateIdNumber = state ? parseInt(state, 10) : null
      const districtIdNumber = district ? parseInt(district, 10) : null

      // Automatically assign student role (ID 7)
      const roleId = 7

      console.log('Extracted fields:', {
        firstName,
        lastName,
        email,
        password: !!password,
        branchId: branchId,
        branchIdNumber: branchIdNumber,
        countryIdNumber,
        stateIdNumber,
        districtIdNumber,
        roleId: 'auto-assigned (student)'
      })

      // Validate required fields
      if (!firstName || !lastName || !email || !password) {
        return Response.json({
          success: false,
          error: 'First name, last name, email, and password are required',
          debug: {
            firstName: !!firstName,
            lastName: !!lastName,
            email: !!email,
            password: !!password,
            branchId,
            roleId: 'auto-assigned (student)'
          }
        }, { status: 400 })
      }

      // Check if email already exists
      const existingUser = await req.payload.find({
        collection: 'users',
        where: {
          email: { equals: email }
        }
      })

      if (existingUser.docs.length > 0) {
        return Response.json({
          success: false,
          error: 'Email already exists'
        }, { status: 400 })
      }

      // Create student user
      const student = await req.payload.create({
        collection: 'users',
        data: {
          firstName,
          lastName,
          email,
          phone: phone || '',
          password,
          legacyRole: 'student',
          institute: req.instituteId, // Assign to current user's institute
          branch: branchIdNumber, // This maps to branch_id column in database
          role: roleId, // This maps to role_id column in database (auto-assigned student role)
          address: address || '',
          dateOfBirth: dateOfBirth || null,
          gender: gender || '',
          isActive: is_active
        }
      })

      // Create StudentDetails if any additional data is provided
      let studentDetailsRecord = null
      if (studentDetails || countryIdNumber || stateIdNumber || districtIdNumber) {
        try {
          const studentDetailsData: any = {
            student: student.id,
            createdBy: req.user.id
          }

          // Add location data if provided
          if (countryIdNumber) studentDetailsData.country = countryIdNumber
          if (stateIdNumber) studentDetailsData.state = stateIdNumber
          if (districtIdNumber) studentDetailsData.district = districtIdNumber

          // Add additional student details if provided
          if (studentDetails) {
            if (studentDetails.education) studentDetailsData.education = studentDetails.education
            if (studentDetails.personalInfo) studentDetailsData.personalInfo = studentDetails.personalInfo
            if (studentDetails.documents) studentDetailsData.documents = studentDetails.documents
            if (studentDetails.additionalInfo) studentDetailsData.additionalInfo = studentDetails.additionalInfo
          }

          console.log('Creating StudentDetails with data:', JSON.stringify(studentDetailsData, null, 2))

          studentDetailsRecord = await req.payload.create({
            collection: 'student-details',
            data: studentDetailsData
          })

          console.log('StudentDetails created successfully:', studentDetailsRecord.id)
        } catch (detailsError) {
          console.error('Failed to create StudentDetails:', detailsError)
          // Don't fail the entire operation if StudentDetails creation fails
        }
      }

      console.log('Student created successfully:', student.id)
      if (studentDetailsRecord) {
        console.log('Created StudentDetails ID:', studentDetailsRecord.id)
      }

      return Response.json({
        success: true,
        data: {
          student,
          studentDetails: studentDetailsRecord
        },
        message: 'Student created successfully'
      })

    } catch (error: any) {
      console.error('=== STUDENT CREATION ERROR ===')
      console.error('Error details:', error)
      
      if (error.name === 'ValidationError') {
        return Response.json({
          success: false,
          error: 'Validation failed',
          details: error?.message || 'Unknown validation error',
          validationErrors: error?.data || error?.details || {}
        }, { status: 400 })
      }

      return Response.json({
        success: false,
        error: 'Failed to create student',
        details: error?.message || 'Unknown error'
      }, { status: 500 })
    }
  }
)

// GET branches endpoint
const getBranchesEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/branches',
  'get',
  async (req) => {
    try {
      const branches = await req.payload.find({
        collection: 'branches',
        limit: 100,
        sort: 'name'
      })

      return Response.json({
        success: true,
        data: branches.docs
      })

    } catch (error: any) {
      console.error('Get branches error:', error)
      
      return Response.json({
        success: false,
        error: 'Failed to fetch branches',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// GET roles endpoint - Returns all available roles for staff management
const getRolesEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/roles',
  'get',
  async (req) => {
    try {
      // Get all roles and filter for Level 4 roles on the backend
      const roles = await req.payload.find({
        collection: 'roles',
        where: {
          level: { equals: '3' }
        },
        limit: 100,
        sort: 'name'
      })

      return Response.json({
        success: true,
        data: roles.docs
      })

    } catch (error: any) {
      console.error('Get roles error:', error)

      return Response.json({
        success: false,
        error: 'Failed to fetch roles',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// UPDATE student endpoint
const updateStudentEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/students/:id',
  'put',
  async (req) => {
    try {
      const studentId = req.routeParams?.id

      if (!studentId) {
        return Response.json(
          { success: false, error: 'Student ID is required' },
          { status: 400 }
        )
      }

      const body = await req.json()
      console.log('=== STUDENT UPDATE DEBUG ===')
      console.log('Student ID:', studentId)
      console.log('Request body:', JSON.stringify(body, null, 2))

      const {
        firstName,
        lastName,
        email,
        phone,
        password,
        branch_id,
        branch,    // Preferred field name (maps to branch_id in database)
        address,
        dateOfBirth,
        gender,
        // Location fields (will be stored in StudentDetails)
        country,
        state,
        district,
        // StudentDetails fields (optional)
        studentDetails,
        is_active = true
      } = body

      // Use branch if provided (preferred), otherwise use branch_id for backward compatibility
      const branchId = branch || branch_id
      // Convert to number for Payload relationship field
      const branchIdNumber = branchId ? parseInt(branchId, 10) : null

      // Convert location IDs to numbers for Payload relationship fields
      const countryIdNumber = country ? parseInt(country, 10) : null
      const stateIdNumber = state ? parseInt(state, 10) : null
      const districtIdNumber = district ? parseInt(district, 10) : null

      console.log('Extracted fields:', {
        firstName,
        lastName,
        email,
        password: !!password,
        branchId: branchId,
        branchIdNumber: branchIdNumber,
        countryIdNumber,
        stateIdNumber,
        districtIdNumber
      })

      // Prepare update data (location fields removed - now stored in StudentDetails)
      const updateData: any = {
        firstName,
        lastName,
        email,
        phone: phone || '',
        address: address || '',
        dateOfBirth: dateOfBirth || null,
        gender: gender || '',
        isActive: is_active
      }

      // Add branch if provided
      if (branchIdNumber) {
        updateData.branch = branchIdNumber
      }

      // Add password if provided (for password updates)
      if (password && password.trim() !== '') {
        updateData.password = password
      }

      console.log('Update data:', JSON.stringify(updateData, null, 2))

      // Update student
      const student = await req.payload.update({
        collection: 'users',
        id: studentId,
        data: updateData
      })

      // Handle StudentDetails update/creation
      let studentDetailsRecord = null
      if (studentDetails || countryIdNumber || stateIdNumber || districtIdNumber) {
        try {
          // Check if StudentDetails already exists for this student
          const existingDetails = await req.payload.find({
            collection: 'student-details',
            where: {
              student: {
                equals: studentId
              }
            },
            limit: 1
          })

          const studentDetailsData: any = {
            student: studentId,
            updatedBy: req.user.id
          }

          // Add location data if provided
          if (countryIdNumber) studentDetailsData.country = countryIdNumber
          if (stateIdNumber) studentDetailsData.state = stateIdNumber
          if (districtIdNumber) studentDetailsData.district = districtIdNumber

          // Add additional student details if provided (filter out empty values)
          if (studentDetails) {
            // Education details
            if (studentDetails.education) {
              const education: any = {}
              if (studentDetails.education.highestQualification && studentDetails.education.highestQualification !== 'select-qualification') {
                education.highestQualification = studentDetails.education.highestQualification
              }
              if (studentDetails.education.institution && studentDetails.education.institution.trim()) {
                education.institution = studentDetails.education.institution
              }
              if (studentDetails.education.fieldOfStudy && studentDetails.education.fieldOfStudy.trim()) {
                education.fieldOfStudy = studentDetails.education.fieldOfStudy
              }
              if (studentDetails.education.graduationYear) {
                education.graduationYear = studentDetails.education.graduationYear
              }
              if (studentDetails.education.percentage) {
                education.percentage = studentDetails.education.percentage
              }
              if (Object.keys(education).length > 0) {
                studentDetailsData.education = education
              }
            }

            // Personal info details
            if (studentDetails.personalInfo) {
              const personalInfo: any = {}
              if (studentDetails.personalInfo.fatherName && studentDetails.personalInfo.fatherName.trim()) {
                personalInfo.fatherName = studentDetails.personalInfo.fatherName
              }
              if (studentDetails.personalInfo.motherName && studentDetails.personalInfo.motherName.trim()) {
                personalInfo.motherName = studentDetails.personalInfo.motherName
              }
              if (studentDetails.personalInfo.guardianName && studentDetails.personalInfo.guardianName.trim()) {
                personalInfo.guardianName = studentDetails.personalInfo.guardianName
              }
              if (studentDetails.personalInfo.emergencyContact && studentDetails.personalInfo.emergencyContact.trim()) {
                personalInfo.emergencyContact = studentDetails.personalInfo.emergencyContact
              }
              if (studentDetails.personalInfo.bloodGroup && studentDetails.personalInfo.bloodGroup !== 'select-blood-group') {
                personalInfo.bloodGroup = studentDetails.personalInfo.bloodGroup
              }
              if (studentDetails.personalInfo.nationality && studentDetails.personalInfo.nationality.trim()) {
                personalInfo.nationality = studentDetails.personalInfo.nationality
              }
              if (studentDetails.personalInfo.religion && studentDetails.personalInfo.religion.trim()) {
                personalInfo.religion = studentDetails.personalInfo.religion
              }
              if (studentDetails.personalInfo.caste && studentDetails.personalInfo.caste.trim()) {
                personalInfo.caste = studentDetails.personalInfo.caste
              }
              if (Object.keys(personalInfo).length > 0) {
                studentDetailsData.personalInfo = personalInfo
              }
            }

            // Documents
            if (studentDetails.documents) {
              const documents: any = {}
              if (studentDetails.documents.aadharNumber && studentDetails.documents.aadharNumber.trim()) {
                documents.aadharNumber = studentDetails.documents.aadharNumber
              }
              if (studentDetails.documents.panNumber && studentDetails.documents.panNumber.trim()) {
                documents.panNumber = studentDetails.documents.panNumber
              }
              if (studentDetails.documents.passportNumber && studentDetails.documents.passportNumber.trim()) {
                documents.passportNumber = studentDetails.documents.passportNumber
              }
              if (studentDetails.documents.drivingLicense && studentDetails.documents.drivingLicense.trim()) {
                documents.drivingLicense = studentDetails.documents.drivingLicense
              }
              if (Object.keys(documents).length > 0) {
                studentDetailsData.documents = documents
              }
            }

            // Additional info
            if (studentDetails.additionalInfo) {
              const additionalInfo: any = {}
              if (studentDetails.additionalInfo.hobbies && studentDetails.additionalInfo.hobbies.trim()) {
                additionalInfo.hobbies = studentDetails.additionalInfo.hobbies
              }
              if (studentDetails.additionalInfo.skills && studentDetails.additionalInfo.skills.trim()) {
                additionalInfo.skills = studentDetails.additionalInfo.skills
              }
              if (studentDetails.additionalInfo.experience && studentDetails.additionalInfo.experience.trim()) {
                additionalInfo.experience = studentDetails.additionalInfo.experience
              }
              if (studentDetails.additionalInfo.goals && studentDetails.additionalInfo.goals.trim()) {
                additionalInfo.goals = studentDetails.additionalInfo.goals
              }
              if (studentDetails.additionalInfo.notes && studentDetails.additionalInfo.notes.trim()) {
                additionalInfo.notes = studentDetails.additionalInfo.notes
              }
              if (Object.keys(additionalInfo).length > 0) {
                studentDetailsData.additionalInfo = additionalInfo
              }
            }
          }

          console.log('StudentDetails data:', JSON.stringify(studentDetailsData, null, 2))

          if (existingDetails.docs.length > 0) {
            // Update existing StudentDetails
            studentDetailsRecord = await req.payload.update({
              collection: 'student-details',
              id: existingDetails.docs[0].id,
              data: studentDetailsData
            })
            console.log('StudentDetails updated successfully:', studentDetailsRecord.id)
          } else {
            // Create new StudentDetails
            studentDetailsData.createdBy = req.user.id
            studentDetailsRecord = await req.payload.create({
              collection: 'student-details',
              data: studentDetailsData
            })
            console.log('StudentDetails created successfully:', studentDetailsRecord.id)
          }
        } catch (detailsError) {
          console.error('Failed to update/create StudentDetails:', detailsError)
          // Don't fail the entire operation if StudentDetails update fails
        }
      }

      console.log('=== STUDENT UPDATE SUCCESS ===')
      console.log('Updated student ID:', student.id)

      return Response.json({
        success: true,
        data: {
          student,
          studentDetails: studentDetailsRecord
        },
        message: 'Student updated successfully'
      })

    } catch (error: any) {
      console.log('=== STUDENT UPDATE ERROR ===')
      console.log('Error details:', {
        message: error?.message,
        data: error?.data,
        isOperational: error?.isOperational,
        status: error?.status,
        cause: error?.cause
      })

      return Response.json({
        success: false,
        error: 'Failed to update student',
        details: error?.message || 'Unknown error'
      }, { status: 500 })
    }
  }
)

// GET /api/institute-admin/students/:id - Get single student with complete details
const getStudentByIdEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/students/:id',
  'get',
  async (req: any) => {
    try {
      console.log('=== GET SINGLE STUDENT REQUEST ===')
      console.log('Request params:', req.params)
      console.log('Request routeParams:', req.routeParams)
      console.log('Request URL:', req.url)

      // Try both params and routeParams for compatibility
      const studentId = req.routeParams?.id || req.params?.id
      console.log('Extracted Student ID:', studentId)
      console.log('User:', req.user?.id, req.user?.legacyRole)
      console.log('Institute ID:', req.instituteId)

      if (!studentId) {
        return Response.json({
          success: false,
          message: 'Student ID is required'
        }, { status: 400 })
      }

      // Get student with relationships
      const student = await req.payload.findByID({
        collection: 'users',
        id: studentId,
        depth: 2, // Include branch and role relationships
        where: {
          and: [
            {
              institute: {
                equals: req.instituteId
              }
            },
            {
              legacyRole: {
                equals: 'student'
              }
            }
          ]
        }
      })

      if (!student) {
        return Response.json({
          success: false,
          message: 'Student not found or access denied'
        }, { status: 404 })
      }

      // Get StudentDetails for this student
      let studentDetails = null
      try {
        const studentDetailsResult = await req.payload.find({
          collection: 'student-details',
          where: {
            student: {
              equals: studentId
            }
          },
          depth: 2, // Include location relationships
          limit: 1
        })

        if (studentDetailsResult.docs.length > 0) {
          studentDetails = studentDetailsResult.docs[0]
        }
      } catch (detailsError) {
        console.error('Failed to fetch StudentDetails:', detailsError)
        // Continue without StudentDetails if fetch fails
      }

      // Transform response to include only essential fields
      const transformedStudent = {
        // Basic student info
        id: student.id,
        firstName: student.firstName,
        lastName: student.lastName,
        email: student.email,
        phone: student.phone,

        // Branch info
        branch: student.branch ? {
          id: student.branch.id,
          name: student.branch.name,
          code: student.branch.code
        } : null,

        // Role info
        role: student.role ? {
          id: student.role.id,
          name: student.role.name,
          code: student.role.code
        } : null,

        // Status and timestamps
        is_active: student.isActive,
        createdAt: student.createdAt,
        updatedAt: student.updatedAt,

        // Additional fields
        address: student.address,
        dateOfBirth: student.dateOfBirth,
        gender: student.gender,

        // StudentDetails (if available)
        studentDetails: studentDetails ? {
          id: studentDetails.id,
          // Location info
          country: studentDetails.country ? {
            id: studentDetails.country.id,
            name: studentDetails.country.name,
            code: studentDetails.country.code
          } : null,
          state: studentDetails.state ? {
            id: studentDetails.state.id,
            name: studentDetails.state.name,
            code: studentDetails.state.code
          } : null,
          district: studentDetails.district ? {
            id: studentDetails.district.id,
            name: studentDetails.district.name,
            code: studentDetails.district.code
          } : null,
          // Other details
          education: studentDetails.education,
          personalInfo: studentDetails.personalInfo,
          documents: studentDetails.documents,
          additionalInfo: studentDetails.additionalInfo,
          isActive: studentDetails.isActive,
          createdAt: studentDetails.createdAt,
          updatedAt: studentDetails.updatedAt
        } : null
      }

      console.log('=== STUDENT FETCH SUCCESS ===')
      console.log('Student ID:', transformedStudent.id)
      console.log('Has StudentDetails:', !!transformedStudent.studentDetails)

      return Response.json({
        success: true,
        data: transformedStudent,
        message: 'Student retrieved successfully'
      })

    } catch (error) {
      console.error('=== GET SINGLE STUDENT ERROR ===')
      console.error('Error:', error)

      return Response.json({
        success: false,
        message: 'Failed to retrieve student',
        error: process.env.NODE_ENV === 'development' ? (error as Error)?.message : 'Internal server error'
      }, { status: 500 })
    }
  }
)

// DELETE /api/institute-admin/students/:id - Delete student with cascade deletion
const deleteStudentEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/students/:id',
  'delete',
  async (req: any) => {
    try {
      console.log('=== DELETE STUDENT REQUEST ===')
      console.log('Request params:', req.params)
      console.log('Request routeParams:', req.routeParams)
      console.log('User:', req.user?.id, req.user?.legacyRole)
      console.log('Institute ID:', req.instituteId)

      // Try both params and routeParams for compatibility
      const studentId = req.routeParams?.id || req.params?.id
      console.log('Extracted Student ID:', studentId)

      if (!studentId) {
        return Response.json({
          success: false,
          message: 'Student ID is required'
        }, { status: 400 })
      }

      // First, verify the student exists and belongs to the institute
      const student = await req.payload.findByID({
        collection: 'users',
        id: studentId,
        where: {
          and: [
            {
              institute: {
                equals: req.instituteId
              }
            },
            {
              legacyRole: {
                equals: 'student'
              }
            }
          ]
        }
      })

      if (!student) {
        return Response.json({
          success: false,
          message: 'Student not found or access denied'
        }, { status: 404 })
      }

      console.log('Student found:', student.firstName, student.lastName)

      // Step 1: Delete associated StudentDetails (if exists)
      let deletedStudentDetails = null
      try {
        const studentDetailsResult = await req.payload.find({
          collection: 'student-details',
          where: {
            student: {
              equals: studentId
            }
          },
          limit: 1
        })

        if (studentDetailsResult.docs.length > 0) {
          const studentDetailsId = studentDetailsResult.docs[0].id
          deletedStudentDetails = await req.payload.delete({
            collection: 'student-details',
            id: studentDetailsId
          })
          console.log('StudentDetails deleted:', studentDetailsId)
        } else {
          console.log('No StudentDetails found for student')
        }
      } catch (detailsError) {
        console.error('Failed to delete StudentDetails:', detailsError)
        // Continue with user deletion even if StudentDetails deletion fails
      }

      // Step 2: Delete the user record
      const deletedStudent = await req.payload.delete({
        collection: 'users',
        id: studentId
      })

      console.log('=== STUDENT DELETION SUCCESS ===')
      console.log('Deleted student ID:', deletedStudent.id)
      console.log('Deleted StudentDetails:', !!deletedStudentDetails)

      return Response.json({
        success: true,
        data: {
          deletedStudent: {
            id: deletedStudent.id,
            firstName: deletedStudent.firstName,
            lastName: deletedStudent.lastName,
            email: deletedStudent.email
          },
          deletedStudentDetails: !!deletedStudentDetails
        },
        message: 'Student deleted successfully'
      })

    } catch (error) {
      console.error('=== DELETE STUDENT ERROR ===')
      console.error('Error:', error)

      return Response.json({
        success: false,
        message: 'Failed to delete student',
        error: process.env.NODE_ENV === 'development' ? (error as Error)?.message : 'Internal server error'
      }, { status: 500 })
    }
  }
)

export { getStudentsEndpoint, createStudentEndpoint, updateStudentEndpoint, getStudentByIdEndpoint, deleteStudentEndpoint, getBranchesEndpoint, getRolesEndpoint }
