import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated institute admin endpoints
const createInstituteAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Get institute ID from user
      const instituteId = typeof user.institute === 'object' ? user.institute.id : user.institute

      if (!instituteId) {
        return Response.json({
          success: false,
          error: 'No institute assigned to user'
        }, { status: 403 })
      }

      // Add user and institute information to request for convenience
      req.userId = user.id
      req.userEmail = user.email
      req.userName = `${user.firstName || ''} ${user.lastName || ''}`.trim()
      req.instituteId = instituteId
      req.userRole = user.legacyRole || user.role

      return handler(req)
    }
  }
}

// Get all available institute themes
export const getInstituteThemesEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/themes',
  'get',
  async (req: any) => {
    try {
      console.log('🎨 Fetching institute themes for institute:', req.instituteId)

      // First, let's check all themes to debug
      const allThemes = await req.payload.find({
        collection: 'themes',
        limit: 100
      })

      console.log('🔍 Debug - All themes in database:', {
        total: allThemes.totalDocs,
        themes: allThemes.docs.map((t: any) => ({
          id: t.id,
          name: t.name,
          type: t.type,
          isActive: t.isActive
        }))
      })

      // Get all active institute themes
      const themes = await req.payload.find({
        collection: 'themes',
        where: {
          type: { equals: 'institute' },
          isActive: { equals: true }
        },
        sort: 'name',
        limit: 100
      })

      console.log(`✅ Found ${themes.totalDocs} institute themes`)

      // If no themes found, let's also check inactive ones
      if (themes.totalDocs === 0) {
        const inactiveThemes = await req.payload.find({
          collection: 'themes',
          where: {
            type: { equals: 'institute' }
          },
          limit: 100
        })
        console.log(`🔍 Found ${inactiveThemes.totalDocs} total institute themes (including inactive)`)
      }

      return Response.json({
        success: true,
        themes: themes.docs.map((theme: any) => ({
          id: theme.id,
          name: theme.name,
          slug: theme.slug,
          description: theme.description,
          category: theme.category,
          type: theme.type,
          version: theme.version,
          colors: theme.colors,
          fonts: theme.fonts,
          features: theme.features || [],
          preview: theme.preview,
          screenshots: theme.screenshots || [],
          customizableElements: theme.customizableElements,
          usageCount: theme.usageCount || 0,
          rating: theme.rating || { average: 0, count: 0 },
          isActive: theme.isActive,
          isDefault: theme.isDefault || false,
          isPremium: theme.isPremium || false,
          createdAt: theme.createdAt,
          updatedAt: theme.updatedAt
        })),
        pagination: {
          totalDocs: themes.totalDocs,
          page: themes.page,
          totalPages: themes.totalPages,
          hasNextPage: themes.hasNextPage,
          hasPrevPage: themes.hasPrevPage
        },
        debug: {
          totalThemesInDB: allThemes.totalDocs,
          instituteThemesFound: themes.totalDocs,
          queryUsed: {
            type: 'institute',
            isActive: true
          }
        }
      })

    } catch (error) {
      console.error('❌ Get institute themes error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch themes'
      }, { status: 500 })
    }
  }
)

// Get current institute's selected theme
export const getCurrentInstituteThemeEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/themes/current',
  'get',
  async (req: any) => {
    try {
      const instituteId = req.instituteId

      if (!instituteId) {
        return Response.json({
          success: false,
          error: 'Institute ID not found'
        }, { status: 401 })
      }

      console.log('🎨 Getting current theme for institute:', instituteId)

      // Get current active theme assignment for this institute
      const instituteThemes = await req.payload.find({
        collection: 'institute-themes',
        where: {
          institute: { equals: instituteId },
          isActive: { equals: true }
        },
        depth: 2, // Include theme and institute data
        limit: 1
      })

      let currentTheme = null
      let themeCustomizations = {}

      if (instituteThemes.totalDocs > 0) {
        const instituteTheme = instituteThemes.docs[0]
        currentTheme = instituteTheme.theme
        themeCustomizations = instituteTheme.customizations || {}
      }

      console.log('✅ Current theme retrieved:', currentTheme?.name || 'None selected')

      return Response.json({
        success: true,
        currentTheme: currentTheme ? {
          id: currentTheme.id,
          name: currentTheme.name,
          slug: currentTheme.slug,
          description: currentTheme.description,
          colors: currentTheme.colors,
          fonts: currentTheme.fonts,
          features: currentTheme.features,
          preview: currentTheme.preview,
          customizableElements: currentTheme.customizableElements
        } : null,
        themeCustomizations,
        appliedAt: instituteThemes.docs[0]?.appliedAt || null
      })

    } catch (error) {
      console.error('❌ Get current institute theme error:', error)
      return Response.json({
        success: false,
        error: 'Failed to get current theme'
      }, { status: 500 })
    }
  }
)

// Apply theme to institute
export const applyThemeToInstituteEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/themes/apply',
  'post',
  async (req: any) => {
    try {
      const instituteId = req.instituteId // This is now set by the auth middleware
      const requestBody = await req.json()
      const { themeId, customizations } = requestBody

      if (!themeId) {
        return Response.json({
          success: false,
          error: 'Theme ID is required'
        }, { status: 400 })
      }

      console.log('🎨 Applying theme to institute:', {
        instituteId,
        themeId,
        hasCustomizations: !!customizations
      })

      // Verify theme exists and is active
      const theme = await req.payload.findByID({
        collection: 'themes',
        id: themeId
      })

      if (!theme || !theme.isActive || theme.type !== 'institute') {
        return Response.json({
          success: false,
          error: 'Invalid or inactive theme'
        }, { status: 400 })
      }

      // Check if institute already has a theme assignment
      console.log('🔍 Checking for existing theme assignment for institute:', instituteId)

      const existingTheme = await req.payload.find({
        collection: 'institute-themes',
        where: {
          institute: { equals: instituteId }
        },
        limit: 1
      })

      console.log('📊 Existing theme query result:', {
        totalDocs: existingTheme.totalDocs,
        hasExisting: existingTheme.totalDocs > 0,
        existingId: existingTheme.docs[0]?.id,
        currentTheme: existingTheme.docs[0]?.theme
      })

      // Check if we're trying to apply the same theme that's already active
      if (existingTheme.totalDocs > 0) {
        const currentThemeId = existingTheme.docs[0]?.theme
        if (currentThemeId === themeId) {
          console.log('⚠️ Theme is already active for this institute')
          // Still proceed with the update to refresh customizations if provided
        }
      }

      let instituteTheme

      if (existingTheme.totalDocs > 0) {
        // Update existing theme assignment
        const existingId = existingTheme.docs[0].id
        console.log('� Replacing existing theme assignment:', {
          existingId,
          instituteId,
          newThemeId: themeId,
          userId: req.userId
        })

        try {
          // Update the existing assignment (no deletion)
          const updateData = {
            theme: themeId,
            customizations: customizations || {},
            appliedBy: req.userId,
            appliedAt: new Date()
            // Don't update: isActive, institute, previousTheme
          }

          console.log('📝 Update data:', updateData)

          instituteTheme = await req.payload.update({
            collection: 'institute-themes',
            id: existingId,
            data: updateData
          })

          console.log('✅ Theme assignment updated successfully:', {
            id: instituteTheme.id,
            theme: themeId,
            isActive: instituteTheme.isActive
          })
        } catch (updateError) {
          console.error('❌ Failed to update theme assignment:', {
            error: updateError,
            existingId,
            themeId,
            instituteId
          })
          throw new Error(`Failed to update theme assignment: ${(updateError as any)?.message || updateError}`)
        }
      } else {
        // Create new institute theme assignment
        console.log('🆕 Creating new theme assignment for institute:', {
          instituteId,
          themeId,
          userId: req.userId
        })

        try {
          instituteTheme = await req.payload.create({
            collection: 'institute-themes',
            data: {
              institute: instituteId,
              theme: themeId,
              isActive: true,
              customizations: customizations || {},
              appliedBy: req.userId,
              appliedAt: new Date()
            }
          })

          console.log('✅ Theme assignment created successfully:', {
            id: instituteTheme.id,
            theme: themeId
          })
        } catch (createError) {
          console.error('❌ Failed to create theme assignment:', {
            error: createError,
            themeId,
            instituteId
          })
          throw new Error(`Failed to create theme assignment: ${(createError as any)?.message || createError}`)
        }
      }

    

      return Response.json({
        success: true,
        message: 'Theme applied successfully'        
      })

    } catch (error: any) {
      console.error('❌ Apply theme error:', {
        error: error.message || error,
        stack: error.stack,
        instituteId: req.instituteId,
        userId: req.userId
      })

      return Response.json({
        success: false,
        error: error.message || 'Failed to apply theme',
        details: {
          timestamp: new Date().toISOString()
        }
      }, { status: 500 })
    }
  }
)

// Preview theme (without applying)
export const previewThemeEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/themes/:id/preview',
  'get',
  async (req: any) => {
    try {
      const { id } = req.params

      if (!id) {
        return Response.json({
          success: false,
          error: 'Theme ID is required'
        }, { status: 400 })
      }

      console.log('🎨 Previewing theme:', id)

      // Get theme details
      const theme = await req.payload.findByID({
        collection: 'themes',
        id
      })

      if (!theme || !theme.isActive || theme.type !== 'institute') {
        return Response.json({
          success: false,
          error: 'Theme not found or inactive'
        }, { status: 404 })
      }

      return Response.json({
        success: true,
        theme: {
          id: theme.id,
          name: theme.name,
          slug: theme.slug,
          description: theme.description,
          category: theme.category,
          colors: theme.colors,
          fonts: theme.fonts,
          features: theme.features,
          preview: theme.preview,
          screenshots: theme.screenshots,
          customizableElements: theme.customizableElements,
          demoUrl: theme.demoUrl
        }
      })

    } catch (error) {
      console.error('❌ Preview theme error:', error)
      return Response.json({
        success: false,
        error: 'Failed to preview theme'
      }, { status: 500 })
    }
  }
)

// Get institute theme history
export const getInstituteThemeHistoryEndpoint: Endpoint = createInstituteAdminEndpoint(
  '/institute-admin/themes/history',
  'get',
  async (req: any) => {
    try {
      const instituteId = req.instituteId // This is now set by the auth middleware

      console.log('🎨 Getting theme history for institute:', instituteId)

      // Get all theme assignments for this institute
      const instituteThemes = await req.payload.find({
        collection: 'institute-themes',
        where: {
          institute: { equals: instituteId }
        },
        depth: 2, // Include theme and user data
        sort: '-appliedAt', // Most recent first
        limit: 50
      })

      console.log(`✅ Found ${instituteThemes.totalDocs} theme assignments`)

      return Response.json({
        success: true,
        themeHistory: instituteThemes.docs.map((assignment: any) => ({
          id: assignment.id,
          theme: {
            id: assignment.theme.id,
            name: assignment.theme.name,
            slug: assignment.theme.slug,
            preview: assignment.theme.preview
          },
          isActive: assignment.isActive,
          appliedAt: assignment.appliedAt,
          appliedBy: assignment.appliedBy ? {
            id: assignment.appliedBy.id,
            name: assignment.appliedBy.name || assignment.appliedBy.email
          } : null,
          customizations: assignment.customizations,
          notes: assignment.notes
        })),
        pagination: {
          totalDocs: instituteThemes.totalDocs,
          page: instituteThemes.page,
          totalPages: instituteThemes.totalPages
        }
      })

    } catch (error) {
      console.error('❌ Get theme history error:', error)
      return Response.json({
        success: false,
        error: 'Failed to get theme history'
      }, { status: 500 })
    }
  }
)

// Public endpoint to get institute themes (no auth required)
export const getPublicInstituteThemesEndpoint: Endpoint = {
  path: '/public/themes/institute',
  method: 'get',
  handler: async (req: any) => {
    try {
      console.log('🎨 Fetching public institute themes')

      // Get all active institute themes
      const themes = await req.payload.find({
        collection: 'themes',
        where: {
          type: { equals: 'institute' },
          isActive: { equals: true }
        },
        sort: 'name',
        limit: 100
      })

      console.log(`✅ Found ${themes.totalDocs} public institute themes`)

      return Response.json({
        success: true,
        themes: themes.docs.map((theme: any) => ({
          id: theme.id,
          name: theme.name,
          slug: theme.slug,
          description: theme.description,
          category: theme.category,
          type: theme.type,
          version: theme.version,
          colors: theme.colors,
          fonts: theme.fonts,
          features: theme.features || [],
          preview: theme.preview,
          screenshots: theme.screenshots || [],
          usageCount: theme.usageCount || 0,
          rating: theme.rating || { average: 0, count: 0 },
          isActive: theme.isActive,
          isDefault: theme.isDefault || false,
          isPremium: theme.isPremium || false,
          createdAt: theme.createdAt,
          updatedAt: theme.updatedAt
        })),
        pagination: {
          totalDocs: themes.totalDocs,
          page: themes.page,
          totalPages: themes.totalPages,
          hasNextPage: themes.hasNextPage,
          hasPrevPage: themes.hasPrevPage
        }
      })

    } catch (error) {
      console.error('❌ Get public institute themes error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch institute themes',
        themes: [],
        pagination: {
          totalDocs: 0,
          page: 1,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      }, { status: 500 })
    }
  }
}

// Simple public endpoint for themes-list (no auth required)
export const getThemesListEndpoint: Endpoint = {
  path: '/themes-list',
  method: 'get',
  handler: async (req: any) => {
    try {
      const url = new URL(req.url || 'http://localhost:3001')
      const type = url.searchParams.get('type') || 'institute'

      console.log('🎨 Themes-list endpoint called with type:', type)

      // Get themes from database
      let whereClause: any = {}

      if (type) {
        whereClause.type = { equals: type }
      }

      const themes = await req.payload.find({
        collection: 'themes',
        where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
        sort: 'name',
        limit: 100
      })

      console.log(`✅ Found ${themes.totalDocs} themes from database`)

      // Format themes data consistently
      const formattedThemes = themes.docs.map((theme: any) => ({
        id: theme.id,
        name: theme.name,
        slug: theme.slug,
        description: theme.description,
        category: theme.category,
        type: theme.type,
        version: theme.version,
        colors: theme.colors || {
          primary: '#10B981',
          secondary: '#3B82F6',
          accent: '#F59E0B',
          background: '#FFFFFF',
          text: '#1F2937'
        },
        fonts: theme.fonts || {
          heading: 'Inter, sans-serif',
          body: 'Inter, sans-serif'
        },
        features: theme.features || [],
        preview: theme.preview,
        screenshots: theme.screenshots || [],
        usageCount: theme.usageCount || 0,
        rating: theme.rating || { average: 0, count: 0 },
        isActive: theme.isActive,
        isDefault: theme.isDefault || false,
        isPremium: theme.isPremium || false,
        createdAt: theme.createdAt,
        updatedAt: theme.updatedAt
      }))

      return Response.json({
        success: true,
        themes: formattedThemes,
        totalDocs: themes.totalDocs,
        page: themes.page,
        totalPages: themes.totalPages
      })

    } catch (error) {
      console.error('Error fetching themes:', error)
      return Response.json(
        { success: false, error: 'Failed to fetch themes' },
        { status: 500 }
      )
    }
  }
}

// Export all institute theme endpoints
export const instituteThemeEndpoints = [
  getInstituteThemesEndpoint,
  getCurrentInstituteThemeEndpoint,
  applyThemeToInstituteEndpoint,
  previewThemeEndpoint,
  getInstituteThemeHistoryEndpoint,
  getPublicInstituteThemesEndpoint,
  getThemesListEndpoint
]
