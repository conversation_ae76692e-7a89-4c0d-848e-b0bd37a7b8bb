import { Endpoint } from 'payload/config'
import { createInstituteAdminEndpoint, parseRequestBody, parseUrlParams } from './utils'

// List tutors
export const getTutorsEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/tutors',
  'get',
  async (req: any) => {
    try {
      const { page, limit, search, sort, order } = parseUrlParams(req)
      const { searchParams } = new URL(req.url!)
      const branchId = searchParams.get('branch_id')
      const specialization = searchParams.get('specialization')
      const status = searchParams.get('status') || 'all'
      const experience = searchParams.get('experience')
      
      const instituteId = req.instituteId

      // Build search query
      const whereClause: any = {
        institute: { equals: instituteId },
        legacyRole: { equals: 'tutor' }
      }

      // Status filter
      if (status !== 'all') {
        whereClause.isActive = { equals: status === 'active' }
      }

      // Branch filter (tutors can be assigned to multiple branches)
      if (branchId && branchId !== 'all') {
        whereClause.branch_id = { contains: branchId }
      }

      // Search filter
      if (search) {
        whereClause.or = [
          { firstName: { contains: search } },
          { lastName: { contains: search } },
          { email: { contains: search } },
          { phone: { contains: search } }
        ]
      }

      // Fetch tutors
      const tutors = await req.payload.find({
        collection: 'users',
        where: whereClause,
        depth: 2,
        limit,
        page,
        sort: sort ? `${order === 'desc' ? '-' : ''}${sort}` : '-createdAt'
      })

      // Transform data to include additional tutor information
      const transformedData = tutors.docs.map((tutor: any) => ({
        id: tutor.id,
        firstName: tutor.firstName,
        lastName: tutor.lastName,
        email: tutor.email,
        phone: tutor.phone,
        specialization: tutor.specialization || ['General'],
        experience: tutor.experience || 'Not specified',
        qualifications: tutor.qualifications || '',
        bio: tutor.bio || '',
        branch_ids: tutor.branch_id ? [tutor.branch_id] : [],
        branches: tutor.branch_id ? [{
          id: tutor.branch_id,
          name: `Branch ${tutor.branch_id}`,
          code: tutor.branch_id
        }] : [],
        role: {
          id: tutor.role_id || 'tutor',
          name: 'Tutor'
        },
        isActive: tutor.isActive,
        emailVerified: tutor.emailVerified,
        lastLogin: tutor.lastLogin,
        coursesCreated: 0, // This would be calculated from actual course data
        studentsEnrolled: 0, // This would be calculated from actual enrollment data
        createdAt: tutor.createdAt,
        updatedAt: tutor.updatedAt
      }))

      return Response.json({
        success: true,
        data: transformedData,
        pagination: {
          page: tutors.page,
          limit: tutors.limit,
          totalPages: tutors.totalPages,
          totalDocs: tutors.totalDocs,
          hasNextPage: tutors.hasNextPage,
          hasPrevPage: tutors.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Error fetching tutors:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch tutors'
      }, { status: 500 })
    }
  }
)

// Create tutor
export const createTutorEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/tutors',
  'post',
  async (req: any) => {
    try {
      const instituteId = req.instituteId
      const requestBody = await parseRequestBody(req)

      const {
        firstName,
        lastName,
        email,
        phone,
        specialization = [],
        experience,
        qualifications,
        branch_ids = [],
        role_id,
        password,
        bio,
        isActive = true
      } = requestBody

      // Validate required fields
      if (!firstName || !lastName || !email || !password || !specialization.length || !branch_ids.length) {
        return Response.json({
          success: false,
          error: 'Required fields are missing',
          details: {
            required: ['firstName', 'lastName', 'email', 'password', 'specialization', 'branch_ids']
          }
        }, { status: 400 })
      }

      // Check if email already exists
      const existingUser = await req.payload.find({
        collection: 'users',
        where: {
          email: { equals: email }
        }
      })

      if (existingUser.docs.length > 0) {
        return Response.json({
          success: false,
          error: 'Email already exists',
          code: 'DUPLICATE_EMAIL',
          field: 'email'
        }, { status: 400 })
      }

      // For now, store the first branch_id in branch_id field
      // In a full implementation, you'd have a separate table for tutor-branch relationships
      const primaryBranchId = branch_ids[0]

      // Create tutor
      const newTutor = await req.payload.create({
        collection: 'users',
        data: {
          firstName,
          lastName,
          email,
          phone,
          password,
          legacyRole: 'tutor',
          role_id: role_id || 'tutor',
          branch_id: primaryBranchId,
          institute: instituteId,
          specialization,
          experience,
          qualifications,
          bio,
          isActive,
          emailVerified: false
        }
      })

      return Response.json({
        success: true,
        data: {
          id: newTutor.id,
          firstName: newTutor.firstName,
          lastName: newTutor.lastName,
          email: newTutor.email,
          phone: newTutor.phone,
          specialization: newTutor.specialization,
          experience: newTutor.experience,
          branch_ids: [primaryBranchId],
          isActive: newTutor.isActive,
          createdAt: newTutor.createdAt
        },
        message: 'Tutor created successfully'
      })

    } catch (error) {
      console.error('Error creating tutor:', error)
      return Response.json({
        success: false,
        error: 'Failed to create tutor'
      }, { status: 500 })
    }
  }
)

// Get tutor details
export const getTutorDetailsEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/tutors/:id',
  'get',
  async (req: any) => {
    try {
      const { id } = req.params
      const instituteId = req.instituteId

      const tutor = await req.payload.findByID({
        collection: 'users',
        id,
        depth: 2
      })

      if (!tutor) {
        return Response.json({
          success: false,
          error: 'Tutor not found'
        }, { status: 404 })
      }

      // Verify tutor belongs to this institute
      const tutorInstituteId = typeof tutor.institute === 'object'
        ? tutor.institute.id
        : tutor.institute

      if (tutorInstituteId !== instituteId) {
        return Response.json({
          success: false,
          error: 'Tutor not found'
        }, { status: 404 })
      }

      // Verify it's actually a tutor
      if (tutor.legacyRole !== 'tutor') {
        return Response.json({
          success: false,
          error: 'User is not a tutor'
        }, { status: 400 })
      }

      return Response.json({
        success: true,
        data: {
          id: tutor.id,
          firstName: tutor.firstName,
          lastName: tutor.lastName,
          email: tutor.email,
          phone: tutor.phone,
          specialization: tutor.specialization || [],
          experience: tutor.experience,
          qualifications: tutor.qualifications,
          bio: tutor.bio,
          branch_ids: tutor.branch_id ? [tutor.branch_id] : [],
          role_id: tutor.role_id,
          isActive: tutor.isActive,
          emailVerified: tutor.emailVerified,
          lastLogin: tutor.lastLogin,
          coursesCreated: 0, // Would be calculated from actual data
          studentsEnrolled: 0, // Would be calculated from actual data
          createdAt: tutor.createdAt,
          updatedAt: tutor.updatedAt
        }
      })

    } catch (error) {
      console.error('Error fetching tutor details:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch tutor details'
      }, { status: 500 })
    }
  }
)

// Update tutor
export const updateTutorEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/tutors/:id',
  'patch',
  async (req: any) => {
    try {
      const { id } = req.params
      const instituteId = req.instituteId
      const updateData = await parseRequestBody(req)

      // Verify tutor exists and belongs to institute
      const existingTutor = await req.payload.findByID({
        collection: 'users',
        id
      })

      if (!existingTutor) {
        return Response.json({
          success: false,
          error: 'Tutor not found'
        }, { status: 404 })
      }

      const tutorInstituteId = typeof existingTutor.institute === 'object'
        ? existingTutor.institute.id
        : existingTutor.institute

      if (tutorInstituteId !== instituteId || existingTutor.legacyRole !== 'tutor') {
        return Response.json({
          success: false,
          error: 'Tutor not found'
        }, { status: 404 })
      }

      // If updating branch assignments, use the first branch as primary
      if (updateData.branch_ids && updateData.branch_ids.length > 0) {
        updateData.branch_id = updateData.branch_ids[0]
        delete updateData.branch_ids
      }

      // Update tutor
      const updatedTutor = await req.payload.update({
        collection: 'users',
        id,
        data: updateData
      })

      return Response.json({
        success: true,
        data: {
          id: updatedTutor.id,
          firstName: updatedTutor.firstName,
          lastName: updatedTutor.lastName,
          email: updatedTutor.email,
          phone: updatedTutor.phone,
          specialization: updatedTutor.specialization,
          experience: updatedTutor.experience,
          branch_ids: updatedTutor.branch_id ? [updatedTutor.branch_id] : [],
          isActive: updatedTutor.isActive,
          updatedAt: updatedTutor.updatedAt
        },
        message: 'Tutor updated successfully'
      })

    } catch (error) {
      console.error('Error updating tutor:', error)
      return Response.json({
        success: false,
        error: 'Failed to update tutor'
      }, { status: 500 })
    }
  }
)

// Toggle tutor status
export const toggleTutorStatusEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/tutors/:id/status',
  'patch',
  async (req: any) => {
    try {
      const { id } = req.params
      const { isActive, reason } = await parseRequestBody(req)

      const updatedTutor = await req.payload.update({
        collection: 'users',
        id,
        data: { isActive }
      })

      return Response.json({
        success: true,
        data: {
          id: updatedTutor.id,
          isActive: updatedTutor.isActive
        },
        message: `Tutor ${isActive ? 'activated' : 'deactivated'} successfully`
      })

    } catch (error) {
      console.error('Error toggling tutor status:', error)
      return Response.json({
        success: false,
        error: 'Failed to update tutor status'
      }, { status: 500 })
    }
  }
)

// Assign tutor to branches
export const assignTutorToBranchesEndpoint = createInstituteAdminEndpoint(
  '/institute-admin/tutors/:id/branches',
  'patch',
  async (req: any) => {
    try {
      const { id } = req.params
      const { branch_ids, action = 'replace' } = await parseRequestBody(req)

      // For now, just update the primary branch
      // In a full implementation, you'd manage a separate tutor-branch relationship table
      const primaryBranchId = branch_ids && branch_ids.length > 0 ? branch_ids[0] : null

      const updatedTutor = await req.payload.update({
        collection: 'users',
        id,
        data: { branch_id: primaryBranchId }
      })

      return Response.json({
        success: true,
        data: {
          id: updatedTutor.id,
          branch_ids: primaryBranchId ? [primaryBranchId] : []
        },
        message: 'Tutor branch assignment updated successfully'
      })

    } catch (error) {
      console.error('Error assigning tutor to branches:', error)
      return Response.json({
        success: false,
        error: 'Failed to assign tutor to branches'
      }, { status: 500 })
    }
  }
)
