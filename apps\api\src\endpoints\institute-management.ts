import { Endpoint } from 'payload/config'
import { requireAuth, requireSuperAdmin } from '../middleware/auth'
import bcrypt from 'bcrypt'

// Helper function for authenticated endpoints
const createAuthenticatedEndpoint = (
  path: string,
  method: 'get' | 'post' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>,
  allowedRoles?: string[]
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = allowedRoles ? requireAuth(allowedRoles) : requireAuth()
      const authResult = await authMiddleware(req)
      
      if (authResult) {
        return authResult
      }
      
      return handler(req)
    }
  }
}

// Get institutes with filtering and pagination
export const getInstitutesEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes',
  'get',
  async (req: any) => {
    try {
      const { searchParams } = new URL(req.url!)
      const search = searchParams.get('search') || ''
      const isActive = searchParams.get('isActive') || 'true'
      const domainVerified = searchParams.get('domainVerified')
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')

      const where: any = {
        deletedAt: { exists: false } // Exclude soft deleted
      }

      // Active filter
      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      // Domain verified filter
      if (domainVerified !== null && domainVerified !== undefined) {
        where.domainVerified = { equals: domainVerified === 'true' }
      }

      // Search filter
      if (search) {
        where.or = [
          { name: { contains: search } },
          { email: { contains: search } },
          { slug: { contains: search } },
          { website: { contains: search } }
        ]
      }

      const institutes = await req.payload.find({
        collection: 'institutes',
        where,
        page,
        limit,
        sort: '-createdAt'
      })

      return Response.json({
        success: true,
        docs: institutes.docs,
        page: institutes.page,
        limit: institutes.limit,
        totalPages: institutes.totalPages,
        totalDocs: institutes.totalDocs,
        hasNextPage: institutes.hasNextPage,
        hasPrevPage: institutes.hasPrevPage
      })

    } catch (error) {
      console.error('Institutes fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
  ['super_admin'] // Only super admin can view all institutes
)

// Create institute with admin user
export const createInstituteEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes',
  'post',
  async (req: any) => {
    try {
      const {
        // Institute data
        name,
        email,
        phone,
        website,
        tagline,
        logo,
        addressStreet,
        cityId,
        stateId,
        countryId,
        districtId,
        zipCode,
        customDomain,
        // Admin user data
        adminFirstName,
        adminLastName,
        adminEmail,
        adminPassword
      } = await req.json()

      // Validate required fields
      if (!name) {
        return Response.json(
          { success: false, error: 'Institute name is required' },
          { status: 400 }
        )
      }

      if (!adminFirstName || !adminLastName || !adminEmail || !adminPassword) {
        return Response.json(
          { success: false, error: 'Admin user details are required' },
          { status: 400 }
        )
      }

      // Auto-generate slug from institute name
      const generateSlug = (name: string): string => {
        return name
          .toLowerCase()
          .trim()
          .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
          .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      }

      let slug = generateSlug(name)

      // Check if slug already exists and make it unique
      let slugCounter = 1
      let originalSlug = slug

      while (true) {
        const existingInstitute = await req.payload.find({
          collection: 'institutes',
          where: {
            slug: { equals: slug }
          },
          limit: 1
        })

        if (existingInstitute.docs.length === 0) {
          break // Slug is unique
        }

        slug = `${originalSlug}-${slugCounter}`
        slugCounter++
      }

      // Create institute
      const institute = await req.payload.create({
        collection: 'institutes',
        data: {
          name,
          slug,
          email,
          phone,
          website,
          tagline,
          logo,
          addressStreet,
          cityId,
          stateId,
          countryId,
          districtId,
          zipCode,
          customDomain,
          domainVerified: false,
          isActive: true
        }
      })

      // Get institute_admin role by code (more reliable than name)
      const instituteAdminRole = await req.payload.find({
        collection: 'roles',
        where: {
          code: { equals: 'institute_admin' }
        },
        limit: 1
      })

      if (!instituteAdminRole.docs.length) {
        throw new Error('Institute admin role not found')
      }

      // PayloadCMS will automatically hash the password when auth: true

      // Check if user already exists with similar email (to handle typos)
      const existingUser = await req.payload.find({
        collection: 'users',
        where: {
          or: [
            { email: { equals: adminEmail.toLowerCase().trim() } },
            { email: { contains: adminEmail.split('@')[0] } } // Check for similar emails
          ]
        },
        limit: 1
      })

      let adminUser
      if (existingUser.docs.length > 0) {
        // Update existing user with correct email and password
        adminUser = await req.payload.update({
          collection: 'users',
          id: existingUser.docs[0].id,
          data: {
            firstName: adminFirstName,
            lastName: adminLastName,
            email: adminEmail.toLowerCase().trim(),
            password: adminPassword,
            role: instituteAdminRole.docs[0].id,
            legacyRole: 'institute_admin',
            institute: institute.id,
            isActive: true
          }
        })
      } else {
        // Create new institute admin user
        adminUser = await req.payload.create({
          collection: 'users',
          data: {
            firstName: adminFirstName,
            lastName: adminLastName,
            email: adminEmail.toLowerCase().trim(),
            password: adminPassword,
            role: instituteAdminRole.docs[0].id,
            legacyRole: 'institute_admin',
            institute: institute.id,
            isActive: true
          }
        })
      }

      // Create domain request if custom domain is provided
      let domainRequest = null
      if (customDomain && customDomain.trim()) {
        try {
          domainRequest = await req.payload.create({
            collection: 'domain-requests',
            data: {
              institute: institute.id,
              requestedDomain: customDomain.trim(),
              status: 'pending',
              purpose: 'Institute custom domain setup',
              requestedBy: adminUser.id
            }
          })
        } catch (domainError) {
          console.error('Domain request creation error:', domainError)
          // Don't fail the entire operation if domain request fails
        }
      }

      return Response.json({
        success: true,
        institute,
        adminUser: {
          id: adminUser.id,
          email: adminUser.email,
          firstName: adminUser.firstName,
          lastName: adminUser.lastName
        },
        domainRequest: domainRequest ? {
          id: domainRequest.id,
          requestedDomain: domainRequest.requestedDomain,
          status: domainRequest.status
        } : null,
        message: 'Institute and admin user created successfully'
      })

    } catch (error) {
      console.error('Create institute error:', error)
      return Response.json(
        { success: false, error: error.message || 'Failed to create institute' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)

// Update institute
export const updateInstituteEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes/:id',
  'put',
  async (req: any) => {
    try {
      const { id } = req.params
      const updateData = await req.json()

      // Remove fields that shouldn't be updated directly
      delete updateData.slug // Slug is auto-generated
      delete updateData.createdAt
      delete updateData.deletedAt

      const institute = await req.payload.update({
        collection: 'institutes',
        id,
        data: updateData
      })

      return Response.json({
        success: true,
        institute,
        message: 'Institute updated successfully'
      })

    } catch (error) {
      console.error('Update institute error:', error)
      return Response.json(
        { success: false, error: error.message || 'Failed to update institute' },
        { status: 500 }
      )
    }
  },
  ['super_admin', 'institute_admin']
)

// Soft delete institute
export const deleteInstituteEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes/:id',
  'delete',
  async (req: any) => {
    try {
      const { id } = req.params

      // Soft delete by setting deletedAt timestamp
      const institute = await req.payload.update({
        collection: 'institutes',
        id,
        data: {
          deletedAt: new Date(),
          isActive: false
        }
      })

      return Response.json({
        success: true,
        message: 'Institute deleted successfully'
      })

    } catch (error) {
      console.error('Delete institute error:', error)
      return Response.json(
        { success: false, error: error.message || 'Failed to delete institute' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)

// Get institute statistics
export const getInstituteStatisticsEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/statistics',
  'get',
  async (req: any) => {
    try {
      // Total institutes
      const totalInstitutes = await req.payload.find({
        collection: 'institutes',
        where: { deletedAt: { exists: false } },
        limit: 0
      })

      // Active institutes
      const activeInstitutes = await req.payload.find({
        collection: 'institutes',
        where: {
          and: [
            { deletedAt: { exists: false } },
            { isActive: { equals: true } }
          ]
        },
        limit: 0
      })

      // Verified domains
      const verifiedDomains = await req.payload.find({
        collection: 'institutes',
        where: {
          and: [
            { deletedAt: { exists: false } },
            { domainVerified: { equals: true } }
          ]
        },
        limit: 0
      })

      // Recent institutes (last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const recentInstitutes = await req.payload.find({
        collection: 'institutes',
        where: {
          and: [
            { deletedAt: { exists: false } },
            { createdAt: { greater_than: thirtyDaysAgo } }
          ]
        },
        limit: 0
      })

      return Response.json({
        success: true,
        data: {
          total: totalInstitutes.totalDocs,
          active: activeInstitutes.totalDocs,
          inactive: totalInstitutes.totalDocs - activeInstitutes.totalDocs,
          verifiedDomains: verifiedDomains.totalDocs,
          recentlyCreated: recentInstitutes.totalDocs
        }
      })

    } catch (error) {
      console.error('Get institute statistics error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)

// Verify custom domain
export const verifyDomainEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes/:id/verify-domain',
  'post',
  async (req: any) => {
    try {
      const { id } = req.params

      // Get institute
      const institute = await req.payload.findByID({
        collection: 'institutes',
        id
      })

      if (!institute || !institute.customDomain) {
        return Response.json({
          success: false,
          error: 'Institute or custom domain not found'
        }, { status: 404 })
      }

      // TODO: Implement actual domain verification logic
      // For now, just mark as verified
      const updatedInstitute = await req.payload.update({
        collection: 'institutes',
        id,
        data: {
          domainVerified: true
        }
      })

      return Response.json({
        success: true,
        institute: updatedInstitute,
        message: 'Domain verified successfully'
      })

    } catch (error) {
      console.error('Verify domain error:', error)
      return Response.json(
        { success: false, error: 'Failed to verify domain' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)
