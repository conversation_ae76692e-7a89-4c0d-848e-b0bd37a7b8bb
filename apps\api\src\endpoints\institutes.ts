import type { Endpoint } from 'payload'
import { requireAuth, requireSuperAdmin, requireInstituteAdmin } from '../middleware/auth'

// Get all institutes (Super Admin only)
export const getAllInstitutesEndpoint: Endpoint = {
  path: '/institutes',
  method: 'get',
  handler: async (req) => {
    const authCheck = await requireSuperAdmin(req)
    if (authCheck) return authCheck

    const url = new URL(req.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const status = url.searchParams.get('status')
    const plan = url.searchParams.get('plan')
    const search = url.searchParams.get('search')

    try {
      const where: any = {}

      if (status) {
        where.subscriptionStatus = { equals: status }
      }

      if (plan) {
        where.subscriptionPlan = { equals: plan }
      }

      if (search) {
        where.or = [
          { name: { contains: search } },
          { email: { contains: search } },
          { slug: { contains: search } },
        ]
      }

      const institutes = await req.payload.find({
        collection: 'institutes',
        where,
        page,
        limit,
        sort: '-createdAt',
      })

      return Response.json({
        institutes: institutes.docs,
        totalDocs: institutes.totalDocs,
        totalPages: institutes.totalPages,
        page: institutes.page,
        limit: institutes.limit,
        hasNextPage: institutes.hasNextPage,
        hasPrevPage: institutes.hasPrevPage,
      })

    } catch (error) {
      console.error('Get institutes error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Get current user's institute
export const getCurrentInstituteEndpoint: Endpoint = {
  path: '/institutes/me',
  method: 'get',
  handler: async (req) => {
    const authCheck = await requireInstituteAdmin(req)
    if (authCheck) return authCheck

    try {
      const currentUser = await req.payload.findByID({
        collection: 'users',
        id: req.user.id,
      })

      if (!currentUser.institute) {
        return Response.json(
          { message: 'User not associated with any institute' },
          { status: 400 }
        )
      }

      const institute = await req.payload.findByID({
        collection: 'institutes',
        id: currentUser.institute,
      })

      return Response.json({
        institute,
      })

    } catch (error) {
      console.error('Get current institute error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Update current user's institute
export const updateCurrentInstituteEndpoint: Endpoint = {
  path: '/institutes/me',
  method: 'patch',
  handler: async (req) => {
    const authCheck = await requireAuth(['institute_admin'])(req)
    if (authCheck) return authCheck

    const {
      name,
      email,
      phone,
      website,
      description,
      address,
      customDomain,
    } = req.body

    try {
      const currentUser = await req.payload.findByID({
        collection: 'users',
        id: req.user.id,
      })

      if (!currentUser.institute) {
        return Response.json(
          { message: 'User not associated with any institute' },
          { status: 400 }
        )
      }

      const updateData: any = {}
      
      if (name) updateData.name = name
      if (email) updateData.email = email
      if (phone) updateData.phone = phone
      if (website) updateData.website = website
      if (description) updateData.description = description
      if (address) updateData.address = address
      if (customDomain) {
        updateData.customDomain = customDomain
        updateData.domainVerified = false // Reset verification when domain changes
      }

      const updatedInstitute = await req.payload.update({
        collection: 'institutes',
        id: currentUser.institute,
        data: updateData,
      })

      return Response.json({
        message: 'Institute updated successfully',
        institute: updatedInstitute,
      })

    } catch (error) {
      console.error('Update institute error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Create institute (Super Admin only)
export const createInstituteEndpoint: Endpoint = {
  path: '/institutes',
  method: 'post',
  handler: async (req) => {
    const authCheck = await requireSuperAdmin(req)
    if (authCheck) return authCheck

    const {
      name,
      slug,
      email,
      phone,
      website,
      description,
      address,
      subscriptionPlan = 'free_trial',
      maxStudents = 100,
      maxCourses = 10,
      maxBranches = 1,
    } = req.body

    if (!name || !slug || !email) {
      return Response.json(
        { message: 'Name, slug, and email are required' },
        { status: 400 }
      )
    }

    try {
      // Check if slug already exists
      const existingInstitutes = await req.payload.find({
        collection: 'institutes',
        where: {
          slug: { equals: slug },
        },
      })

      if (existingInstitutes.docs.length > 0) {
        return Response.json(
          { message: 'Institute slug already exists' },
          { status: 400 }
        )
      }

      const institute = await req.payload.create({
        collection: 'institutes',
        data: {
          name,
          slug,
          email,
          phone,
          website,
          description,
          address,
          subscriptionPlan,
          subscriptionStatus: 'active',
          subscriptionExpiry: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          maxStudents,
          maxCourses,
          maxBranches,
          features: {
            marketplace: subscriptionPlan !== 'free_trial',
            liveClasses: ['professional', 'enterprise'].includes(subscriptionPlan),
            exams: true,
            blogs: ['professional', 'enterprise'].includes(subscriptionPlan),
            analytics: true,
          },
          isActive: true,
          createdBy: req.user.id,
        },
      })

      return Response.json({
        message: 'Institute created successfully',
        institute,
      })

    } catch (error) {
      console.error('Create institute error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Update institute (Super Admin only)
export const updateInstituteEndpoint: Endpoint = {
  path: '/institutes/:id',
  method: 'patch',
  handler: async (req) => {
    const authCheck = await requireSuperAdmin(req)
    if (authCheck) return authCheck

    const { id } = req.params
    const updateData = req.body

    try {
      const updatedInstitute = await req.payload.update({
        collection: 'institutes',
        id,
        data: updateData,
      })

      return Response.json({
        message: 'Institute updated successfully',
        institute: updatedInstitute,
      })

    } catch (error) {
      console.error('Update institute error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Delete institute (Super Admin only)
export const deleteInstituteEndpoint: Endpoint = {
  path: '/institutes/:id',
  method: 'delete',
  handler: async (req) => {
    const authCheck = await requireSuperAdmin(req)
    if (authCheck) return authCheck

    const { id } = req.params

    try {
      await req.payload.delete({
        collection: 'institutes',
        id,
      })

      return Response.json({
        message: 'Institute deleted successfully',
      })

    } catch (error) {
      console.error('Delete institute error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Get institute profile (Institute Admin)
export const getInstituteProfileEndpoint: Endpoint = {
  path: '/institutes/profile',
  method: 'get',
  handler: async (req) => {
    const authCheck = await requireAuth(req)
    if (authCheck) return authCheck

    const { user } = req

    if (!['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.role)) {
      return Response.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    try {
      // Get institute from user's institute relationship
      const institute = await req.payload.findByID({
        collection: 'institutes',
        id: user.institute.id
      })

      return Response.json({
        success: true,
        institute
      })
    } catch (error) {
      console.error('Get institute profile error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Update institute profile (Institute Admin only)
export const updateInstituteProfileEndpoint: Endpoint = {
  path: '/institutes/profile',
  method: 'patch',
  handler: async (req) => {
    const authCheck = await requireAuth(req)
    if (authCheck) return authCheck

    const { user } = req

    if (user.role !== 'institute_admin') {
      return Response.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    try {
      const updateData = await req.json()

      const updatedInstitute = await req.payload.update({
        collection: 'institutes',
        id: user.institute.id,
        data: updateData
      })

      return Response.json({
        success: true,
        doc: updatedInstitute,
        message: 'Institute profile updated successfully'
      })
    } catch (error) {
      console.error('Update institute profile error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Get institute statistics
export const getInstituteStatsEndpoint: Endpoint = {
  path: '/institutes/stats',
  method: 'get',
  handler: async (req) => {
    const authCheck = await requireAuth(req)
    if (authCheck) return authCheck

    const { user } = req

    if (!['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.role)) {
      return Response.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    try {
      // In a real implementation, calculate these stats from the database
      const stats = {
        totalStudents: 1250,
        activeStudents: 1100,
        totalCourses: 45,
        publishedCourses: 38,
        totalBranches: 5,
        activeBranches: 5,
        monthlyRevenue: 125000,
        totalRevenue: 1500000,
        enrollmentsThisMonth: 85,
        completionRate: 78.5
      }

      return Response.json({ success: true, stats })
    } catch (error) {
      console.error('Get institute stats error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Get institute by custom domain (PUBLIC - for middleware)
export const getInstituteByDomainEndpoint: Endpoint = {
  path: '/public/domain/resolve',
  method: 'get',
  handler: async (req) => {
    try {
      console.log('🔍 Domain endpoint called with URL:', req.url)

      const url = new URL(req.url || 'http://localhost:3001')
      const domain = url.searchParams.get('domain')

      console.log('🔍 Extracted domain parameter:', domain)
      console.log('🔍 All URL search params:', Object.fromEntries(url.searchParams.entries()))

      if (!domain) {
        console.log('❌ No domain parameter provided')
        return Response.json({
          success: false,
          error: 'Domain parameter is required'
        }, { status: 400 })
      }

      console.log('🔍 Looking up institute by domain:', domain)

      // Find institute by custom domain
      const institutes = await req.payload.find({
        collection: 'institutes',
        where: {
          customDomain: { equals: domain },
          domainVerified: { equals: true },
          isActive: { equals: true }
        },
        limit: 1
      })

      if (institutes.totalDocs === 0) {
        console.log('❌ No institute found for domain:', domain)
        return Response.json({
          success: false,
          error: 'Institute not found'
        }, { status: 404 })
      }

      const institute = institutes.docs[0]

      // Get current active theme for this institute
      let currentTheme = null

      // Only try to fetch theme if institute has a valid ID
      if (institute && institute.id) {
        try {
          console.log('🎨 Fetching theme for institute ID:', institute.id)

          const instituteThemes = await req.payload.find({
            collection: 'institute-themes',
            where: {
              institute: { equals: institute.id },
              isActive: { equals: true }
            },
            depth: 2, // Include theme data
            limit: 1
          })

          console.log(`🎨 Found ${instituteThemes.totalDocs} active themes for institute`)

          if (instituteThemes.totalDocs > 0) {
            const instituteTheme = instituteThemes.docs[0]
            const themeData = instituteTheme.theme

            if (themeData && typeof themeData === 'object') {
              console.log('🎨 Active theme found:', (themeData as any).name || 'Unknown')

              currentTheme = {
                ...(themeData as any),
                customizations: instituteTheme.customizations || {}
              }
            }
          } else {
            console.log('🎨 No active theme found for institute')
          }
        } catch (themeError) {
          console.error('❌ Error fetching theme for institute:', institute.id, themeError)
        }
      } else {
        console.log('⚠️ Invalid institute ID, skipping theme fetch')
      }

      console.log('✅ Institute found:', {
        id: institute.id,
        name: institute.name,
        slug: institute.slug,
        domain: institute.customDomain,
        theme: currentTheme?.name || 'None'
      })

      return Response.json({
        success: true,
        institute: {
          id: institute.id,
          name: institute.name,
          slug: institute.slug,
          custom_domain: institute.customDomain,
          domain_verified: institute.domainVerified,
          is_active: institute.isActive,
          theme_id: currentTheme?.id || null,
          theme: currentTheme,
          logo: institute.logo
        }
      })

    } catch (error) {
      console.error('❌ Get institute by domain error:', error)
      return Response.json({
        success: false,
        error: 'Internal server error'
      }, { status: 500 })
    }
  }
}
