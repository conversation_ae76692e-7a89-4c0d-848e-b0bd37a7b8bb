import { Endpoint } from 'payload/config'

// Get countries with filtering and pagination
export const getCountriesEndpoint: Endpoint = {
  path: '/locations/countries',
  method: 'get',
  handler: async (req) => {
    try {
      const { searchParams } = new URL(req.url!)
      const search = searchParams.get('search') || ''
      const isActive = searchParams.get('isActive') || 'true'
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')
      const sort = searchParams.get('sort') || 'name'

      const where: any = {}

      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      if (search) {
        where.or = [
          { name: { contains: search } },
          { code: { contains: search } },
          { 'details.capital': { contains: search } }
        ]
      }

      const countries = await req.payload.find({
        collection: 'countries',
        where,
        page,
        limit,
        sort: sort as string
      })

      return Response.json({
        success: true,
        countries: countries.docs,
        pagination: {
          page: countries.page,
          limit: countries.limit,
          totalPages: countries.totalPages,
          totalDocs: countries.totalDocs,
          hasNextPage: countries.hasNextPage,
          hasPrevPage: countries.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Countries fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Get states by country with filtering
export const getStatesEndpoint: Endpoint = {
  path: '/locations/states',
  method: 'get',
  handler: async (req) => {
    try {
      const { searchParams } = new URL(req.url!)
      const countryId = searchParams.get('countryId')
      const search = searchParams.get('search') || ''
      const isActive = searchParams.get('isActive') || 'true'
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '50')
      const sort = searchParams.get('sort') || 'name'

      const where: any = {}

      if (countryId) {
        where.country = { equals: countryId }
      }

      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      if (search) {
        where.or = [
          { name: { contains: search } },
          { code: { contains: search } },
          { 'details.capital': { contains: search } }
        ]
      }

      const states = await req.payload.find({
        collection: 'states',
        where,
        page,
        limit,
        sort: sort as string,
        populate: ['country']
      })

      return Response.json({
        success: true,
        states: states.docs,
        pagination: {
          page: states.page,
          limit: states.limit,
          totalPages: states.totalPages,
          totalDocs: states.totalDocs,
          hasNextPage: states.hasNextPage,
          hasPrevPage: states.hasPrevPage
        }
      })

    } catch (error) {
      console.error('States fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Get districts by state with filtering
export const getDistrictsEndpoint: Endpoint = {
  path: '/locations/districts',
  method: 'get',
  handler: async (req) => {
    try {
      const { searchParams } = new URL(req.url!)
      const stateId = searchParams.get('stateId')
      const countryId = searchParams.get('countryId')
      const search = searchParams.get('search') || ''
      const type = searchParams.get('type')
      const isActive = searchParams.get('isActive') || 'true'
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '100')
      const sort = searchParams.get('sort') || 'name'

      const where: any = {}

      if (stateId) {
        where.state = { equals: stateId }
      }

      if (type) {
        where['details.type'] = { equals: type }
      }

      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      if (search) {
        where.or = [
          { name: { contains: search } },
          { code: { contains: search } },
          { 'details.pincode': { contains: search } }
        ]
      }

      // If countryId is provided but not stateId, get all districts in that country
      if (countryId && !stateId) {
        const statesInCountry = await req.payload.find({
          collection: 'states',
          where: { country: { equals: countryId } },
          limit: 1000
        })

        const stateIds = statesInCountry.docs.map((state: any) => state.id)
        where.state = { in: stateIds }
      }

      const districts = await req.payload.find({
        collection: 'districts',
        where,
        page,
        limit,
        sort: sort as string,
        populate: ['state']
      })

      return Response.json({
        success: true,
        districts: districts.docs,
        pagination: {
          page: districts.page,
          limit: districts.limit,
          totalPages: districts.totalPages,
          totalDocs: districts.totalDocs,
          hasNextPage: districts.hasNextPage,
          hasPrevPage: districts.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Districts fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Get cities by state with filtering (cities are districts with type 'city')
export const getCitiesEndpoint: Endpoint = {
  path: '/locations/cities',
  method: 'get',
  handler: async (req) => {
    try {
      const { searchParams } = new URL(req.url!)
      const stateId = searchParams.get('stateId')
      const countryId = searchParams.get('countryId')
      const search = searchParams.get('search') || ''
      const isActive = searchParams.get('isActive') || 'true'
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '100')
      const sort = searchParams.get('sort') || 'name'

      const where: any = {
        'details.type': { in: ['city', 'municipality', 'town'] } // Only get cities and urban areas
      }

      if (stateId) {
        where.state = { equals: stateId }
      }

      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      if (search) {
        where.or = [
          { name: { contains: search } },
          { code: { contains: search } }
        ]
      }

      // If countryId is provided but not stateId, get all cities in that country
      if (countryId && !stateId) {
        const statesInCountry = await req.payload.find({
          collection: 'states',
          where: { country: { equals: countryId } },
          limit: 1000
        })

        const stateIds = statesInCountry.docs.map((state: any) => state.id)
        where.state = { in: stateIds }
      }

      const cities = await req.payload.find({
        collection: 'districts', // Cities are stored as districts
        where,
        page,
        limit,
        sort: sort as string,
        populate: ['state']
      })

      return Response.json({
        success: true,
        cities: cities.docs,
        pagination: {
          page: cities.page,
          limit: cities.limit,
          totalPages: cities.totalPages,
          totalDocs: cities.totalDocs,
          hasNextPage: cities.hasNextPage,
          hasPrevPage: cities.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Cities fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Get location hierarchy (country -> states -> districts)
export const getLocationHierarchyEndpoint: Endpoint = {
  path: '/locations/hierarchy/:countryId',
  method: 'get',
  handler: async (req) => {
    try {
      const countryId = req.routeParams?.countryId

      if (!countryId) {
        return Response.json(
          { success: false, error: 'Country ID is required' },
          { status: 400 }
        )
      }

      // Get country details
      const country = await req.payload.findByID({
        collection: 'countries',
        id: countryId
      })

      if (!country) {
        return Response.json(
          { success: false, error: 'Country not found' },
          { status: 404 }
        )
      }

      // Get states in this country
      const states = await req.payload.find({
        collection: 'states',
        where: {
          and: [
            { country: { equals: countryId } },
            { isActive: { equals: true } }
          ]
        },
        limit: 1000,
        sort: 'name'
      })

      // Get districts for each state
      const statesWithDistricts = await Promise.all(
        states.docs.map(async (state: any) => {
          const districts = await req.payload.find({
            collection: 'districts',
            where: {
              and: [
                { state: { equals: state.id } },
                { isActive: { equals: true } }
              ]
            },
            limit: 1000,
            sort: 'name'
          })

          return {
            ...state,
            districts: districts.docs
          }
        })
      )

      return Response.json({
        success: true,
        country,
        states: statesWithDistricts,
        totalStates: states.totalDocs,
        totalDistricts: statesWithDistricts.reduce(
          (total, state) => total + state.districts.length,
          0
        )
      })

    } catch (error) {
      console.error('Location hierarchy fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Create country endpoint
export const createCountryEndpoint: Endpoint = {
  path: '/locations/countries',
  method: 'post',
  handler: async (req) => {
    try {
      const body = await req.json()

      const country = await req.payload.create({
        collection: 'countries',
        data: body
      })

      return Response.json({
        success: true,
        country
      })

    } catch (error) {
      console.error('Country creation error:', error)
      return Response.json(
        { success: false, error: 'Failed to create country' },
        { status: 500 }
      )
    }
  }
}

// Update country endpoint
export const updateCountryEndpoint: Endpoint = {
  path: '/locations/countries/:id',
  method: 'put',
  handler: async (req) => {
    try {
      const countryId = req.routeParams?.id
      const body = await req.json()

      if (!countryId) {
        return Response.json(
          { success: false, error: 'Country ID is required' },
          { status: 400 }
        )
      }

      const country = await req.payload.update({
        collection: 'countries',
        id: countryId,
        data: body
      })

      return Response.json({
        success: true,
        country
      })

    } catch (error) {
      console.error('Country update error:', error)
      return Response.json(
        { success: false, error: 'Failed to update country' },
        { status: 500 }
      )
    }
  }
}

// Delete country endpoint
export const deleteCountryEndpoint: Endpoint = {
  path: '/locations/countries/:id',
  method: 'delete',
  handler: async (req) => {
    try {
      const countryId = req.routeParams?.id

      if (!countryId) {
        return Response.json(
          { success: false, error: 'Country ID is required' },
          { status: 400 }
        )
      }

      await req.payload.delete({
        collection: 'countries',
        id: countryId
      })

      return Response.json({
        success: true,
        message: 'Country deleted successfully'
      })

    } catch (error) {
      console.error('Country deletion error:', error)
      return Response.json(
        { success: false, error: 'Failed to delete country' },
        { status: 500 }
      )
    }
  }
}

// Create state endpoint
export const createStateEndpoint: Endpoint = {
  path: '/locations/states',
  method: 'post',
  handler: async (req) => {
    try {
      const body = await req.json()
      console.log('Creating state with data:', JSON.stringify(body, null, 2))

      // Convert country ID to number if it's a string (Payload expects numeric IDs for relationships)
      if (body.country && typeof body.country === 'string') {
        body.country = parseInt(body.country)
      }

      // Ensure the country exists before creating the state
      if (body.country) {
        const countryExists = await req.payload.findByID({
          collection: 'countries',
          id: body.country
        })

        if (!countryExists) {
          return Response.json(
            { success: false, error: 'Invalid country ID' },
            { status: 400 }
          )
        }
      }

      console.log('Processed data for state creation:', JSON.stringify(body, null, 2))

      const state = await req.payload.create({
        collection: 'states',
        data: body
      })

      return Response.json({
        success: true,
        state
      })

    } catch (error) {
      console.error('State creation error:', error)
      return Response.json(
        { success: false, error: 'Failed to create state' },
        { status: 500 }
      )
    }
  }
}

// Update state endpoint
export const updateStateEndpoint: Endpoint = {
  path: '/locations/states/:id',
  method: 'put',
  handler: async (req) => {
    try {
      const stateId = req.routeParams?.id
      const body = await req.json()

      if (!stateId) {
        return Response.json(
          { success: false, error: 'State ID is required' },
          { status: 400 }
        )
      }

      const state = await req.payload.update({
        collection: 'states',
        id: stateId,
        data: body
      })

      return Response.json({
        success: true,
        state
      })

    } catch (error) {
      console.error('State update error:', error)
      return Response.json(
        { success: false, error: 'Failed to update state' },
        { status: 500 }
      )
    }
  }
}

// Delete state endpoint
export const deleteStateEndpoint: Endpoint = {
  path: '/locations/states/:id',
  method: 'delete',
  handler: async (req) => {
    try {
      const stateId = req.routeParams?.id

      if (!stateId) {
        return Response.json(
          { success: false, error: 'State ID is required' },
          { status: 400 }
        )
      }

      await req.payload.delete({
        collection: 'states',
        id: stateId
      })

      return Response.json({
        success: true,
        message: 'State deleted successfully'
      })

    } catch (error) {
      console.error('State deletion error:', error)
      return Response.json(
        { success: false, error: 'Failed to delete state' },
        { status: 500 }
      )
    }
  }
}

// Create district endpoint
export const createDistrictEndpoint: Endpoint = {
  path: '/locations/districts',
  method: 'post',
  handler: async (req) => {
    try {
      const body = await req.json()
      console.log('Creating district with data:', JSON.stringify(body, null, 2))

      // Convert state ID to number if it's a string (Payload expects numeric IDs for relationships)
      if (body.state && typeof body.state === 'string') {
        body.state = parseInt(body.state)
      }

      // Remove country field if present (districts are linked to states, not directly to countries)
      if (body.country) {
        delete body.country
      }

      // Ensure the state exists before creating the district
      if (body.state) {
        const stateExists = await req.payload.findByID({
          collection: 'states',
          id: body.state
        })

        if (!stateExists) {
          return Response.json(
            { success: false, error: 'Invalid state ID' },
            { status: 400 }
          )
        }
      }

      console.log('Processed data for district creation:', JSON.stringify(body, null, 2))

      const district = await req.payload.create({
        collection: 'districts',
        data: body
      })

      return Response.json({
        success: true,
        district
      })

    } catch (error) {
      console.error('District creation error:', error)
      return Response.json(
        { success: false, error: 'Failed to create district' },
        { status: 500 }
      )
    }
  }
}

// Update district endpoint
export const updateDistrictEndpoint: Endpoint = {
  path: '/locations/districts/:id',
  method: 'put',
  handler: async (req) => {
    try {
      const districtId = req.routeParams?.id
      const body = await req.json()

      if (!districtId) {
        return Response.json(
          { success: false, error: 'District ID is required' },
          { status: 400 }
        )
      }

      const district = await req.payload.update({
        collection: 'districts',
        id: districtId,
        data: body
      })

      return Response.json({
        success: true,
        district
      })

    } catch (error) {
      console.error('District update error:', error)
      return Response.json(
        { success: false, error: 'Failed to update district' },
        { status: 500 }
      )
    }
  }
}

// Delete district endpoint
export const deleteDistrictEndpoint: Endpoint = {
  path: '/locations/districts/:id',
  method: 'delete',
  handler: async (req) => {
    try {
      const districtId = req.routeParams?.id

      if (!districtId) {
        return Response.json(
          { success: false, error: 'District ID is required' },
          { status: 400 }
        )
      }

      await req.payload.delete({
        collection: 'districts',
        id: districtId
      })

      return Response.json({
        success: true,
        message: 'District deleted successfully'
      })

    } catch (error) {
      console.error('District deletion error:', error)
      return Response.json(
        { success: false, error: 'Failed to delete district' },
        { status: 500 }
      )
    }
  }
}

export const locationEndpoints = [
  getCountriesEndpoint,
  getStatesEndpoint,
  getDistrictsEndpoint,
  getCitiesEndpoint,
  getLocationHierarchyEndpoint,
  createCountryEndpoint,
  updateCountryEndpoint,
  deleteCountryEndpoint,
  createStateEndpoint,
  updateStateEndpoint,
  deleteStateEndpoint,
  createDistrictEndpoint,
  updateDistrictEndpoint,
  deleteDistrictEndpoint,
]
