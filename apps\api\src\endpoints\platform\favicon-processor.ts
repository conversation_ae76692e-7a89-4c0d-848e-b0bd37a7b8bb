import type { Endpoint } from 'payload'
import { requireAuth } from '../../middleware/auth'
import { PlatformUploadService } from '../../services/platform-upload-service'
import { ImageProcessingService } from '../../services/image-processing-service'

console.log('🔥 Favicon processor endpoint loaded!')

/**
 * Favicon Processing Endpoint
 * Processes uploaded images into multiple favicon formats and sizes
 */

export const processFaviconEndpoint: Endpoint = {
  path: '/platform/favicon/process',
  method: 'post',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('🔖 Process favicon endpoint called')

      try {
        // Parse form data
        const formData = await req.formData()
        const file = formData.get('file') as File

        if (!file) {
          return res.status(400).json({
            success: false,
            message: 'No file provided'
          })
        }

        console.log('📁 Favicon file details:', {
          name: file.name,
          size: file.size,
          type: file.type
        })

        // Convert File to <PERSON>uffer
        const arrayBuffer = await file.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)

        // Validate image
        const validation = await ImageProcessingService.validateAndGetMetadata(buffer)
        if (!validation.valid) {
          return res.status(400).json({
            success: false,
            message: validation.message || 'Invalid image file'
          })
        }

        console.log('✅ Image validated:', {
          width: validation.metadata?.width,
          height: validation.metadata?.height,
          format: validation.metadata?.format
        })

        // Generate favicon sizes
        const faviconSizes = await ImageProcessingService.createFavicon(buffer)

        console.log('🔖 Favicon sizes generated:', Object.keys(faviconSizes))

        // Create upload service instance
        const uploadService = new PlatformUploadService(req.payload)

        // Upload each favicon size
        const uploadedFavicons: any = {}
        
        for (const [sizeName, processedImage] of Object.entries(faviconSizes)) {
          try {
            const { uploadResult, mediaRecord } = await uploadService.uploadFile(
              processedImage.buffer,
              `favicon-${sizeName}.png`,
              'image/png',
              {
                folder: 'platform/favicons',
                mediaType: 'favicon',
                alt: `Platform Favicon ${sizeName}`
              }
            )

            uploadedFavicons[sizeName] = {
              upload: uploadResult,
              media: mediaRecord
            }

            console.log(`✅ Favicon ${sizeName} uploaded:`, mediaRecord.url)

          } catch (error) {
            console.error(`❌ Failed to upload favicon ${sizeName}:`, error)
          }
        }

        // Get current favicon settings to clean up old files
        const currentFaviconSettings = await req.payload.find({
          collection: 'options',
          where: {
            key: {
              in: [
                'platform_favicon_16x16',
                'platform_favicon_32x32',
                'platform_favicon_48x48',
                'platform_favicon_64x64',
                'platform_favicon_128x128',
                'platform_favicon_256x256'
              ]
            }
          }
        })

        // Clean up old favicon files
        for (const setting of currentFaviconSettings.docs) {
          if (setting.value) {
            try {
              await uploadService.deleteFile(setting.value)
              console.log('🗑️ Old favicon file cleaned up:', setting.key)
            } catch (error) {
              console.warn('⚠️ Failed to clean up old favicon file:', error)
            }
          }
        }

        // Update favicon settings
        const updatedSettings: any = {}

        for (const [sizeName, faviconData] of Object.entries(uploadedFavicons)) {
          const settingKey = `platform_favicon_${sizeName}`
          const mediaId = (faviconData as any).media.id

          // Check if setting exists
          const existingSetting = currentFaviconSettings.docs.find(s => s.key === settingKey)

          if (existingSetting) {
            // Update existing setting
            await req.payload.update({
              collection: 'options',
              id: existingSetting.id,
              data: {
                value: mediaId,
                description: `Platform favicon ${sizeName} media ID`
              }
            })
          } else {
            // Create new setting
            await req.payload.create({
              collection: 'options',
              data: {
                key: settingKey,
                value: mediaId,
                description: `Platform favicon ${sizeName} media ID`,
                category: 'platform',
                type: 'media'
              }
            })
          }

          updatedSettings[settingKey] = mediaId
        }

        console.log('✅ All favicon settings updated successfully')

        return res.json({
          success: true,
          message: 'Favicon processed and updated successfully',
          data: {
            originalImage: {
              width: validation.metadata?.width,
              height: validation.metadata?.height,
              format: validation.metadata?.format,
              size: buffer.length
            },
            generatedSizes: Object.keys(faviconSizes),
            uploadedFavicons,
            updatedSettings
          }
        })

      } catch (error) {
        console.error('❌ Process favicon error:', error)
        return res.status(500).json({
          success: false,
          message: `Favicon processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Get current favicon settings endpoint
export const getFaviconSettingsEndpoint: Endpoint = {
  path: '/platform/favicon/settings',
  method: 'get',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('📋 Get favicon settings endpoint called')

      try {
        // Get all favicon settings
        const faviconSettings = await req.payload.find({
          collection: 'options',
          where: {
            key: {
              in: [
                'platform_favicon_16x16',
                'platform_favicon_32x32',
                'platform_favicon_48x48',
                'platform_favicon_64x64',
                'platform_favicon_128x128',
                'platform_favicon_256x256'
              ]
            }
          }
        })

        const favicons: any = {}

        // Load media records for each favicon
        for (const setting of faviconSettings.docs) {
          if (setting.value) {
            try {
              const mediaRecord = await req.payload.findByID({
                collection: 'media',
                id: setting.value
              })

              const sizeName = setting.key.replace('platform_favicon_', '')
              favicons[sizeName] = mediaRecord

            } catch (error) {
              console.warn(`⚠️ Failed to load favicon media for ${setting.key}:`, error)
            }
          }
        }

        console.log('✅ Favicon settings retrieved:', Object.keys(favicons))

        return res.json({
          success: true,
          data: {
            favicons,
            availableSizes: Object.keys(favicons)
          }
        })

      } catch (error) {
        console.error('❌ Get favicon settings error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to get favicon settings: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Export favicon processing endpoints
export const faviconProcessorEndpoints = [
  processFaviconEndpoint,
  getFaviconSettingsEndpoint
]
