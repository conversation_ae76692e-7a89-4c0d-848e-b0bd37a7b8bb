import type { Endpoint } from 'payload'
import { requireAuth } from '../../middleware/auth'
import {
  createLogoUploadMiddleware,
  createFaviconUploadMiddleware,
  validateUploadResult,
  formatUploadResponse,
  handleUploadError,
  type UploadRequest
} from '../../middleware/platform-upload-middleware'
import { PlatformUploadService } from '../../services/platform-upload-service'

console.log('🔥 Platform file upload endpoints loaded!')

/**
 * Platform File Upload Endpoints
 * Handles logo, favicon, and other platform asset uploads
 */

// Logo upload endpoint
export const logoUploadEndpoint: Endpoint = {
  path: '/platform/upload/logo',
  method: 'post',
  handler: [
    requireAuth(['super_admin']), // Only super admin can upload platform assets
    ...createLogoUploadMiddleware(),
    validateUploadResult,
    async (req: UploadRequest, res) => {
      console.log('🖼️ Logo upload endpoint called')

      try {
        if (!req.uploadResult) {
          return res.status(400).json({
            success: false,
            message: 'Upload failed - no result'
          })
        }

        // Create upload service instance
        const uploadService = new PlatformUploadService(req.payload)

        // Create media record
        const { mediaRecord } = await uploadService.uploadFile(
          Buffer.from([]), // Buffer already processed by middleware
          req.uploadResult.originalName,
          req.uploadResult.mimeType,
          {
            folder: 'platform/logos',
            mediaType: 'platform_logo',
            alt: 'Platform Logo'
          }
        )

        console.log('✅ Logo uploaded and media record created:', {
          mediaId: mediaRecord.id,
          filename: mediaRecord.filename,
          url: mediaRecord.url
        })

        return res.json({
          success: true,
          message: 'Logo uploaded successfully',
          data: {
            upload: req.uploadResult,
            media: mediaRecord
          }
        })

      } catch (error) {
        console.error('❌ Logo upload error:', error)
        return res.status(500).json({
          success: false,
          message: `Logo upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    },
    handleUploadError
  ]
}

// Favicon upload endpoint
export const faviconUploadEndpoint: Endpoint = {
  path: '/platform/upload/favicon',
  method: 'post',
  handler: [
    requireAuth(['super_admin']), // Only super admin can upload platform assets
    ...createFaviconUploadMiddleware(),
    validateUploadResult,
    async (req: UploadRequest, res) => {
      console.log('🔖 Favicon upload endpoint called')

      try {
        if (!req.uploadResult) {
          return res.status(400).json({
            success: false,
            message: 'Upload failed - no result'
          })
        }

        // Create upload service instance
        const uploadService = new PlatformUploadService(req.payload)

        // Create media record
        const { mediaRecord } = await uploadService.uploadFile(
          Buffer.from([]), // Buffer already processed by middleware
          req.uploadResult.originalName,
          req.uploadResult.mimeType,
          {
            folder: 'platform/favicons',
            mediaType: 'favicon',
            alt: 'Platform Favicon'
          }
        )

        console.log('✅ Favicon uploaded and media record created:', {
          mediaId: mediaRecord.id,
          filename: mediaRecord.filename,
          url: mediaRecord.url
        })

        return res.json({
          success: true,
          message: 'Favicon uploaded successfully',
          data: {
            upload: req.uploadResult,
            media: mediaRecord
          }
        })

      } catch (error) {
        console.error('❌ Favicon upload error:', error)
        return res.status(500).json({
          success: false,
          message: `Favicon upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    },
    handleUploadError
  ]
}

// Generic platform file upload endpoint
export const platformFileUploadEndpoint: Endpoint = {
  path: '/platform/upload',
  method: 'post',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('📤 Generic platform upload endpoint called')

      try {
        // Parse form data
        const formData = await req.formData()
        const file = formData.get('file') as File
        const uploadType = formData.get('type') as string

        if (!file) {
          return res.status(400).json({
            success: false,
            message: 'No file provided'
          })
        }

        if (!uploadType) {
          return res.status(400).json({
            success: false,
            message: 'Upload type is required'
          })
        }

        console.log('📁 File details:', {
          name: file.name,
          size: file.size,
          type: file.type,
          uploadType
        })

        // Convert File to Buffer
        const arrayBuffer = await file.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)

        // Create upload service instance
        const uploadService = new PlatformUploadService(req.payload)

        // Determine folder and media type based on upload type
        let folder = 'platform/general'
        let mediaType = 'platform_file'
        let alt = file.name

        switch (uploadType) {
          case 'logo':
            folder = 'platform/logos'
            mediaType = 'platform_logo'
            alt = 'Platform Logo'
            break
          case 'favicon':
            folder = 'platform/favicons'
            mediaType = 'favicon'
            alt = 'Platform Favicon'
            break
          case 'banner':
            folder = 'platform/banners'
            mediaType = 'platform_banner'
            alt = 'Platform Banner'
            break
        }

        // Upload file and create media record
        const { uploadResult, mediaRecord } = await uploadService.uploadFile(
          buffer,
          file.name,
          file.type,
          {
            folder,
            mediaType,
            alt
          }
        )

        console.log('✅ Platform file uploaded successfully:', {
          uploadType,
          mediaId: mediaRecord.id,
          filename: mediaRecord.filename,
          url: mediaRecord.url
        })

        return res.json({
          success: true,
          message: `${uploadType} uploaded successfully`,
          data: {
            upload: uploadResult,
            media: mediaRecord
          }
        })

      } catch (error) {
        console.error('❌ Platform upload error:', error)
        return res.status(500).json({
          success: false,
          message: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Delete platform file endpoint
export const deletePlatformFileEndpoint: Endpoint = {
  path: '/platform/files/:id',
  method: 'delete',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('🗑️ Delete platform file endpoint called')

      try {
        const { id } = req.params

        if (!id) {
          return res.status(400).json({
            success: false,
            message: 'File ID is required'
          })
        }

        // Create upload service instance
        const uploadService = new PlatformUploadService(req.payload)

        // Delete file and media record
        const result = await uploadService.deleteFile(id)

        if (!result.success) {
          return res.status(400).json({
            success: false,
            message: result.message || 'Delete failed'
          })
        }

        console.log('✅ Platform file deleted successfully:', id)

        return res.json({
          success: true,
          message: 'File deleted successfully'
        })

      } catch (error) {
        console.error('❌ Delete platform file error:', error)
        return res.status(500).json({
          success: false,
          message: `Delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Get platform file info endpoint
export const getPlatformFileEndpoint: Endpoint = {
  path: '/platform/files/:id',
  method: 'get',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('📋 Get platform file endpoint called')

      try {
        const { id } = req.params

        if (!id) {
          return res.status(400).json({
            success: false,
            message: 'File ID is required'
          })
        }

        // Create upload service instance
        const uploadService = new PlatformUploadService(req.payload)

        // Get file info
        const fileInfo = await uploadService.getFileInfo(id)

        if (!fileInfo) {
          return res.status(404).json({
            success: false,
            message: 'File not found'
          })
        }

        console.log('✅ Platform file info retrieved:', {
          id: fileInfo.id,
          filename: fileInfo.filename
        })

        return res.json({
          success: true,
          data: fileInfo
        })

      } catch (error) {
        console.error('❌ Get platform file error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to get file info: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Health check endpoint
export const uploadHealthCheckEndpoint: Endpoint = {
  path: '/platform/upload/health',
  method: 'get',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('🏥 Upload health check endpoint called')

      try {
        // Create upload service instance
        const uploadService = new PlatformUploadService(req.payload)

        // Perform health check
        const healthResult = await uploadService.healthCheck()

        const statusCode = healthResult.healthy ? 200 : 503

        return res.status(statusCode).json({
          success: healthResult.healthy,
          message: healthResult.message,
          data: healthResult.details
        })

      } catch (error) {
        console.error('❌ Upload health check error:', error)
        return res.status(500).json({
          success: false,
          message: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Export all endpoints
export const platformUploadEndpoints = [
  logoUploadEndpoint,
  faviconUploadEndpoint,
  platformFileUploadEndpoint,
  deletePlatformFileEndpoint,
  getPlatformFileEndpoint,
  uploadHealthCheckEndpoint
]
