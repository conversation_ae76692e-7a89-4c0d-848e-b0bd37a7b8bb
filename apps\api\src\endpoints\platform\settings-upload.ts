import type { Endpoint } from 'payload'
import { requireAuth } from '../../middleware/auth'
import {
  createLogoUploadMiddleware,
  createFaviconUploadMiddleware,
  validateUploadResult,
  type UploadRequest
} from '../../middleware/platform-upload-middleware'
import { PlatformUploadService } from '../../services/platform-upload-service'

console.log('🔥 Platform settings upload endpoints loaded!')

/**
 * Platform Settings Upload Endpoints
 * Handles logo and favicon uploads with automatic platform settings updates
 */

// Update platform logo endpoint
export const updatePlatformLogoEndpoint: Endpoint = {
  path: '/platform/settings/logo',
  method: 'post',
  handler: [
    requireAuth(['super_admin']),
    ...createLogoUploadMiddleware(),
    validateUploadResult,
    async (req: UploadRequest, res) => {
      console.log('🖼️ Update platform logo endpoint called')

      try {
        if (!req.uploadResult) {
          return res.status(400).json({
            success: false,
            message: 'Upload failed - no result'
          })
        }

        // Create upload service instance
        const uploadService = new PlatformUploadService(req.payload)

        // Create media record
        const { mediaRecord } = await uploadService.uploadFile(
          Buffer.from([]), // Buffer already processed by middleware
          req.uploadResult.originalName,
          req.uploadResult.mimeType,
          {
            folder: 'platform/logos',
            mediaType: 'platform_logo',
            alt: 'Platform Logo'
          }
        )

        console.log('✅ Logo uploaded, updating platform settings...')

        // Get current logo setting to clean up old file
        const currentLogoSetting = await req.payload.find({
          collection: 'options',
          where: {
            key: { equals: 'platform_logo' }
          },
          limit: 1
        })

        // Clean up old logo file if exists
        if (currentLogoSetting.docs.length > 0 && currentLogoSetting.docs[0].value) {
          try {
            const oldMediaId = currentLogoSetting.docs[0].value
            await uploadService.deleteFile(oldMediaId)
            console.log('🗑️ Old logo file cleaned up:', oldMediaId)
          } catch (error) {
            console.warn('⚠️ Failed to clean up old logo file:', error)
          }
        }

        // Update platform logo setting
        if (currentLogoSetting.docs.length > 0) {
          // Update existing setting
          await req.payload.update({
            collection: 'options',
            id: currentLogoSetting.docs[0].id,
            data: {
              value: mediaRecord.id,
              description: 'Platform logo media ID'
            }
          })
        } else {
          // Create new setting
          await req.payload.create({
            collection: 'options',
            data: {
              key: 'platform_logo',
              value: mediaRecord.id,
              description: 'Platform logo media ID',
              category: 'platform',
              type: 'media'
            }
          })
        }

        console.log('✅ Platform logo setting updated successfully')

        return res.json({
          success: true,
          message: 'Platform logo updated successfully',
          data: {
            upload: req.uploadResult,
            media: mediaRecord,
            setting: {
              key: 'platform_logo',
              value: mediaRecord.id
            }
          }
        })

      } catch (error) {
        console.error('❌ Update platform logo error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to update platform logo: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Update platform favicon endpoint
export const updatePlatformFaviconEndpoint: Endpoint = {
  path: '/platform/settings/favicon',
  method: 'post',
  handler: [
    requireAuth(['super_admin']),
    ...createFaviconUploadMiddleware(),
    validateUploadResult,
    async (req: UploadRequest, res) => {
      console.log('🔖 Update platform favicon endpoint called')

      try {
        if (!req.uploadResult) {
          return res.status(400).json({
            success: false,
            message: 'Upload failed - no result'
          })
        }

        // Create upload service instance
        const uploadService = new PlatformUploadService(req.payload)

        // Create media record
        const { mediaRecord } = await uploadService.uploadFile(
          Buffer.from([]), // Buffer already processed by middleware
          req.uploadResult.originalName,
          req.uploadResult.mimeType,
          {
            folder: 'platform/favicons',
            mediaType: 'favicon',
            alt: 'Platform Favicon'
          }
        )

        console.log('✅ Favicon uploaded, updating platform settings...')

        // Get current favicon setting to clean up old file
        const currentFaviconSetting = await req.payload.find({
          collection: 'options',
          where: {
            key: { equals: 'platform_favicon' }
          },
          limit: 1
        })

        // Clean up old favicon file if exists
        if (currentFaviconSetting.docs.length > 0 && currentFaviconSetting.docs[0].value) {
          try {
            const oldMediaId = currentFaviconSetting.docs[0].value
            await uploadService.deleteFile(oldMediaId)
            console.log('🗑️ Old favicon file cleaned up:', oldMediaId)
          } catch (error) {
            console.warn('⚠️ Failed to clean up old favicon file:', error)
          }
        }

        // Update platform favicon setting
        if (currentFaviconSetting.docs.length > 0) {
          // Update existing setting
          await req.payload.update({
            collection: 'options',
            id: currentFaviconSetting.docs[0].id,
            data: {
              value: mediaRecord.id,
              description: 'Platform favicon media ID'
            }
          })
        } else {
          // Create new setting
          await req.payload.create({
            collection: 'options',
            data: {
              key: 'platform_favicon',
              value: mediaRecord.id,
              description: 'Platform favicon media ID',
              category: 'platform',
              type: 'media'
            }
          })
        }

        console.log('✅ Platform favicon setting updated successfully')

        return res.json({
          success: true,
          message: 'Platform favicon updated successfully',
          data: {
            upload: req.uploadResult,
            media: mediaRecord,
            setting: {
              key: 'platform_favicon',
              value: mediaRecord.id
            }
          }
        })

      } catch (error) {
        console.error('❌ Update platform favicon error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to update platform favicon: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Get platform branding assets endpoint
export const getPlatformBrandingEndpoint: Endpoint = {
  path: '/platform/settings/branding',
  method: 'get',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('🎨 Get platform branding endpoint called')

      try {
        // Get logo and favicon settings
        const brandingSettings = await req.payload.find({
          collection: 'options',
          where: {
            key: {
              in: ['platform_logo', 'platform_favicon']
            }
          }
        })

        const branding: any = {
          logo: null,
          favicon: null
        }

        // Process each setting
        for (const setting of brandingSettings.docs) {
          if (setting.key === 'platform_logo' && setting.value) {
            try {
              const logoMedia = await req.payload.findByID({
                collection: 'media',
                id: setting.value
              })
              branding.logo = logoMedia
            } catch (error) {
              console.warn('⚠️ Failed to load logo media:', error)
            }
          }

          if (setting.key === 'platform_favicon' && setting.value) {
            try {
              const faviconMedia = await req.payload.findByID({
                collection: 'media',
                id: setting.value
              })
              branding.favicon = faviconMedia
            } catch (error) {
              console.warn('⚠️ Failed to load favicon media:', error)
            }
          }
        }

        console.log('✅ Platform branding retrieved:', {
          hasLogo: !!branding.logo,
          hasFavicon: !!branding.favicon
        })

        return res.json({
          success: true,
          data: branding
        })

      } catch (error) {
        console.error('❌ Get platform branding error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to get platform branding: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Remove platform logo endpoint
export const removePlatformLogoEndpoint: Endpoint = {
  path: '/platform/settings/logo',
  method: 'delete',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('🗑️ Remove platform logo endpoint called')

      try {
        // Get current logo setting
        const logoSetting = await req.payload.find({
          collection: 'options',
          where: {
            key: { equals: 'platform_logo' }
          },
          limit: 1
        })

        if (logoSetting.docs.length === 0 || !logoSetting.docs[0].value) {
          return res.json({
            success: true,
            message: 'No logo to remove'
          })
        }

        // Delete the media file
        const uploadService = new PlatformUploadService(req.payload)
        await uploadService.deleteFile(logoSetting.docs[0].value)

        // Clear the setting
        await req.payload.update({
          collection: 'options',
          id: logoSetting.docs[0].id,
          data: {
            value: '',
            description: 'Platform logo media ID (removed)'
          }
        })

        console.log('✅ Platform logo removed successfully')

        return res.json({
          success: true,
          message: 'Platform logo removed successfully'
        })

      } catch (error) {
        console.error('❌ Remove platform logo error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to remove platform logo: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Remove platform favicon endpoint
export const removePlatformFaviconEndpoint: Endpoint = {
  path: '/platform/settings/favicon',
  method: 'delete',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('🗑️ Remove platform favicon endpoint called')

      try {
        // Get current favicon setting
        const faviconSetting = await req.payload.find({
          collection: 'options',
          where: {
            key: { equals: 'platform_favicon' }
          },
          limit: 1
        })

        if (faviconSetting.docs.length === 0 || !faviconSetting.docs[0].value) {
          return res.json({
            success: true,
            message: 'No favicon to remove'
          })
        }

        // Delete the media file
        const uploadService = new PlatformUploadService(req.payload)
        await uploadService.deleteFile(faviconSetting.docs[0].value)

        // Clear the setting
        await req.payload.update({
          collection: 'options',
          id: faviconSetting.docs[0].id,
          data: {
            value: '',
            description: 'Platform favicon media ID (removed)'
          }
        })

        console.log('✅ Platform favicon removed successfully')

        return res.json({
          success: true,
          message: 'Platform favicon removed successfully'
        })

      } catch (error) {
        console.error('❌ Remove platform favicon error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to remove platform favicon: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Export all endpoints
export const platformSettingsUploadEndpoints = [
  updatePlatformLogoEndpoint,
  updatePlatformFaviconEndpoint,
  getPlatformBrandingEndpoint,
  removePlatformLogoEndpoint,
  removePlatformFaviconEndpoint
]
