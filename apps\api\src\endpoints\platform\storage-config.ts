import type { Endpoint } from 'payload'
import { requireAuth } from '../../middleware/auth'
import { StorageConfigManager, StorageFactory } from '../../adapters/storage'
import { initializeStorageSettings, validateStorageSettings, getStorageSettingsSummary } from '../../scripts/init-storage-settings'

console.log('🔥 Storage configuration endpoints loaded!')

/**
 * Storage Configuration Management Endpoints
 * Handles dynamic storage provider configuration
 */

// Get current storage configuration endpoint
export const getStorageConfigEndpoint: Endpoint = {
  path: '/platform/storage/config',
  method: 'get',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('📋 Get storage configuration endpoint called')

      try {
        const configManager = StorageConfigManager.getInstance()
        const config = await configManager.getStorageConfig(req.payload)

        // Remove sensitive information from response
        const safeConfig = {
          provider: config.provider,
          local: config.local ? {
            uploadDir: config.local.uploadDir,
            baseUrl: config.local.baseUrl,
            publicPath: config.local.publicPath
          } : undefined,
          s3: config.s3 ? {
            bucket: config.s3.bucket,
            region: config.s3.region,
            endpoint: config.s3.endpoint,
            publicUrl: config.s3.publicUrl,
            cdnUrl: config.s3.cdnUrl,
            hasAccessKey: !!config.s3.accessKeyId,
            hasSecretKey: !!config.s3.secretAccessKey
          } : undefined
        }

        console.log('✅ Storage configuration retrieved:', {
          provider: safeConfig.provider,
          hasLocal: !!safeConfig.local,
          hasS3: !!safeConfig.s3
        })

        return res.json({
          success: true,
          data: safeConfig
        })

      } catch (error) {
        console.error('❌ Get storage configuration error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to get storage configuration: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Update storage provider endpoint
export const updateStorageProviderEndpoint: Endpoint = {
  path: '/platform/storage/provider',
  method: 'post',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('🔧 Update storage provider endpoint called')

      try {
        const { provider } = await req.json()

        if (!provider || !['local', 's3'].includes(provider)) {
          return res.status(400).json({
            success: false,
            message: 'Invalid storage provider. Must be "local" or "s3"'
          })
        }

        console.log('📝 Updating storage provider to:', provider)

        // Check if setting exists
        const existingSetting = await req.payload.find({
          collection: 'options',
          where: {
            key: { equals: 'storage_provider' }
          },
          limit: 1
        })

        if (existingSetting.docs.length > 0) {
          // Update existing setting
          await req.payload.update({
            collection: 'options',
            id: existingSetting.docs[0].id,
            data: {
              value: provider,
              description: 'Active storage provider (local or s3)'
            }
          })
        } else {
          // Create new setting
          await req.payload.create({
            collection: 'options',
            data: {
              key: 'storage_provider',
              value: provider,
              description: 'Active storage provider (local or s3)',
              category: 'storage',
              type: 'string'
            }
          })
        }

        // Clear configuration cache
        const configManager = StorageConfigManager.getInstance()
        configManager.clearCache()

        console.log('✅ Storage provider updated successfully')

        return res.json({
          success: true,
          message: `Storage provider updated to ${provider}`,
          data: { provider }
        })

      } catch (error) {
        console.error('❌ Update storage provider error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to update storage provider: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Update S3 configuration endpoint
export const updateS3ConfigEndpoint: Endpoint = {
  path: '/platform/storage/s3',
  method: 'post',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('☁️ Update S3 configuration endpoint called')

      try {
        const {
          bucket,
          region,
          accessKeyId,
          secretAccessKey,
          endpoint,
          publicUrl,
          cdnUrl
        } = await req.json()

        // Validate required fields
        if (!bucket || !region || !accessKeyId || !secretAccessKey) {
          return res.status(400).json({
            success: false,
            message: 'Missing required S3 configuration: bucket, region, accessKeyId, secretAccessKey'
          })
        }

        console.log('📝 Updating S3 configuration:', {
          bucket,
          region,
          hasAccessKey: !!accessKeyId,
          hasSecretKey: !!secretAccessKey,
          endpoint,
          publicUrl,
          cdnUrl
        })

        // S3 configuration settings
        const s3Settings = [
          { key: 's3_bucket', value: bucket, description: 'S3 bucket name' },
          { key: 's3_region', value: region, description: 'S3 region' },
          { key: 's3_access_key_id', value: accessKeyId, description: 'S3 access key ID' },
          { key: 's3_secret_access_key', value: secretAccessKey, description: 'S3 secret access key' },
          { key: 's3_endpoint', value: endpoint || '', description: 'S3 custom endpoint (optional)' },
          { key: 's3_public_url', value: publicUrl || '', description: 'S3 public URL (optional)' },
          { key: 's3_cdn_url', value: cdnUrl || '', description: 'S3 CDN URL (optional)' }
        ]

        // Get existing S3 settings
        const existingSettings = await req.payload.find({
          collection: 'options',
          where: {
            key: {
              in: s3Settings.map(s => s.key)
            }
          }
        })

        // Update or create each setting
        for (const setting of s3Settings) {
          const existing = existingSettings.docs.find(s => s.key === setting.key)

          if (existing) {
            // Update existing setting
            await req.payload.update({
              collection: 'options',
              id: existing.id,
              data: {
                value: setting.value,
                description: setting.description
              }
            })
          } else {
            // Create new setting
            await req.payload.create({
              collection: 'options',
              data: {
                key: setting.key,
                value: setting.value,
                description: setting.description,
                category: 'storage',
                type: 'string'
              }
            })
          }
        }

        // Clear configuration cache
        const configManager = StorageConfigManager.getInstance()
        configManager.clearCache()

        console.log('✅ S3 configuration updated successfully')

        return res.json({
          success: true,
          message: 'S3 configuration updated successfully',
          data: {
            bucket,
            region,
            endpoint,
            publicUrl,
            cdnUrl
          }
        })

      } catch (error) {
        console.error('❌ Update S3 configuration error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to update S3 configuration: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Test storage configuration endpoint
export const testStorageConfigEndpoint: Endpoint = {
  path: '/platform/storage/test',
  method: 'post',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('🧪 Test storage configuration endpoint called')

      try {
        const configManager = StorageConfigManager.getInstance()
        const config = await configManager.getStorageConfig(req.payload)

        // Validate configuration
        const validation = await configManager.validateConfig(config)
        if (!validation.valid) {
          return res.status(400).json({
            success: false,
            message: validation.message || 'Invalid storage configuration'
          })
        }

        // Create storage adapter and test
        const storageAdapter = StorageFactory.create(config)
        const healthCheck = await storageAdapter.healthCheck()

        console.log('🏥 Storage health check result:', healthCheck)

        return res.json({
          success: healthCheck.healthy,
          message: healthCheck.message || (healthCheck.healthy ? 'Storage is healthy' : 'Storage has issues'),
          data: {
            provider: config.provider,
            healthy: healthCheck.healthy,
            validation: validation.valid
          }
        })

      } catch (error) {
        console.error('❌ Test storage configuration error:', error)
        return res.status(500).json({
          success: false,
          message: `Storage test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Clear storage configuration cache endpoint
export const clearStorageCacheEndpoint: Endpoint = {
  path: '/platform/storage/cache/clear',
  method: 'post',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('🗑️ Clear storage cache endpoint called')

      try {
        const configManager = StorageConfigManager.getInstance()
        configManager.clearCache()

        console.log('✅ Storage configuration cache cleared')

        return res.json({
          success: true,
          message: 'Storage configuration cache cleared successfully'
        })

      } catch (error) {
        console.error('❌ Clear storage cache error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to clear storage cache: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Initialize storage settings endpoint
export const initStorageSettingsEndpoint: Endpoint = {
  path: '/platform/storage/init',
  method: 'post',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('🚀 Initialize storage settings endpoint called')

      try {
        await initializeStorageSettings({ payload: req.payload })

        const summary = await getStorageSettingsSummary({ payload: req.payload })
        const validation = await validateStorageSettings({ payload: req.payload })

        console.log('✅ Storage settings initialized successfully')

        return res.json({
          success: true,
          message: 'Storage settings initialized successfully',
          data: {
            summary,
            validation
          }
        })

      } catch (error) {
        console.error('❌ Initialize storage settings error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to initialize storage settings: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Get storage settings summary endpoint
export const getStorageSummaryEndpoint: Endpoint = {
  path: '/platform/storage/summary',
  method: 'get',
  handler: [
    requireAuth(['super_admin']),
    async (req, res) => {
      console.log('📊 Get storage summary endpoint called')

      try {
        const summary = await getStorageSettingsSummary({ payload: req.payload })
        const validation = await validateStorageSettings({ payload: req.payload })

        console.log('✅ Storage summary retrieved:', summary)

        return res.json({
          success: true,
          data: {
            summary,
            validation
          }
        })

      } catch (error) {
        console.error('❌ Get storage summary error:', error)
        return res.status(500).json({
          success: false,
          message: `Failed to get storage summary: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  ]
}

// Export storage configuration endpoints
export const storageConfigEndpoints = [
  getStorageConfigEndpoint,
  updateStorageProviderEndpoint,
  updateS3ConfigEndpoint,
  testStorageConfigEndpoint,
  clearStorageCacheEndpoint,
  initStorageSettingsEndpoint,
  getStorageSummaryEndpoint
]
