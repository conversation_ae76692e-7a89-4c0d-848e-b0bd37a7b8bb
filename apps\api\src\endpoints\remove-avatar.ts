import type { Endpoint } from 'payload'
import { requireAuth } from '../middleware/auth'
import { createUploadMiddleware } from '../middleware/upload-middleware'
import fs from 'fs/promises'
import path from 'path'

console.log('🔥 remove-avatar.ts file loaded - avatar removal API!')

// Remove avatar endpoint that deletes files and cleans up database
export const removeAvatarEndpoint: Endpoint = {
  path: '/remove-avatar',
  method: 'delete',
  handler: async (req) => {
    console.log('🗑️🗑️🗑️ REMOVE AVATAR ENDPOINT CALLED! 🗑️🗑️🗑️')
    console.log('📝 Request URL:', req.url)
    console.log('📝 Request method:', req.method)
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }
    
    console.log('✅ Auth check passed, user:', {
      id: req.user?.id,
      email: req.user?.email,
      legacyRole: req.user?.legacyRole
    })

    try {
      // Parse request body to get media ID or user ID
      const body = await req.json()
      const { mediaId, userId, removeFromUser } = body
      
      console.log('📋 Remove avatar parameters:', {
        mediaId,
        userId,
        removeFromUser,
        currentUserId: req.user?.id
      })

      // If no mediaId provided, try to get it from user's avatar field
      let targetMediaId = mediaId
      let targetUserId = userId || req.user?.id

      if (!targetMediaId && targetUserId) {
        console.log('🔍 Looking up user avatar...')
        const user = await req.payload.findByID({
          collection: 'users',
          id: targetUserId
        })
        
        if (user && user.avatar) {
          targetMediaId = typeof user.avatar === 'object' ? user.avatar.id : user.avatar
          console.log('📋 Found user avatar media ID:', targetMediaId)
        }
      }

      if (!targetMediaId) {
        console.log('❌ No media ID provided or found')
        return Response.json(
          { success: false, message: 'No media ID provided or found' },
          { status: 400 }
        )
      }

      // Get media object from database
      console.log('🔍 Fetching media object from database...')
      const media = await req.payload.findByID({
        collection: 'media',
        id: targetMediaId
      })

      if (!media) {
        console.log('❌ Media not found in database')
        return Response.json(
          { success: false, message: 'Media not found' },
          { status: 404 }
        )
      }

      console.log('📋 Media object found:', {
        id: media.id,
        filename: media.filename,
        url: media.url,
        sizesCount: Object.keys(media.sizes || {}).length
      })

      // Create upload middleware to handle file deletion
      console.log('🔧 Creating upload middleware for file deletion...')
      const uploadMiddleware = await createUploadMiddleware(req.payload)

      // Delete files using upload middleware
      console.log('🗑️ Deleting files...')
      await uploadMiddleware.deleteFile(media)

      // Remove from user's avatar field if requested
      if (removeFromUser && targetUserId) {
        console.log('👤 Removing avatar from user profile...')
        await req.payload.update({
          collection: 'users',
          id: targetUserId,
          data: {
            avatar: null
          }
        })
        console.log('✅ Avatar removed from user profile')
      }

      // Delete media record from database
      console.log('🗑️ Deleting media record from database...')
      await req.payload.delete({
        collection: 'media',
        id: targetMediaId
      })

      console.log('🎉 Avatar removal completed successfully!')

      return Response.json({
        success: true,
        message: 'Avatar removed successfully',
        deletedMedia: {
          id: media.id,
          filename: media.filename,
          url: media.url
        },
        userUpdated: removeFromUser && targetUserId
      })

    } catch (error) {
      console.error('❌ Avatar removal error:', error)
      return Response.json(
        { success: false, message: error instanceof Error ? error.message : 'Avatar removal failed' },
        { status: 500 }
      )
    }
  }
}

// Remove multiple avatar files endpoint
export const removeMultipleAvatarsEndpoint: Endpoint = {
  path: '/remove-avatars',
  method: 'delete',
  handler: async (req) => {
    console.log('🗑️🗑️🗑️ REMOVE MULTIPLE AVATARS ENDPOINT CALLED! 🗑️🗑️🗑️')
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }

    try {
      // Parse request body to get media IDs
      const body = await req.json()
      const { mediaIds, removeFromUsers } = body
      
      console.log('📋 Remove multiple avatars parameters:', {
        mediaIds: mediaIds?.length || 0,
        removeFromUsers
      })

      if (!mediaIds || !Array.isArray(mediaIds) || mediaIds.length === 0) {
        console.log('❌ No media IDs provided')
        return Response.json(
          { success: false, message: 'No media IDs provided' },
          { status: 400 }
        )
      }

      const results = []
      const uploadMiddleware = await createUploadMiddleware(req.payload)

      for (const mediaId of mediaIds) {
        try {
          console.log(`🔍 Processing media ID: ${mediaId}`)
          
          // Get media object
          const media = await req.payload.findByID({
            collection: 'media',
            id: mediaId
          })

          if (!media) {
            console.log(`❌ Media ${mediaId} not found`)
            results.push({
              mediaId,
              success: false,
              message: 'Media not found'
            })
            continue
          }

          // Delete files
          await uploadMiddleware.deleteFile(media)

          // Remove from users if requested
          if (removeFromUsers) {
            await req.payload.update({
              collection: 'users',
              where: {
                avatar: {
                  equals: mediaId
                }
              },
              data: {
                avatar: null
              }
            })
          }

          // Delete media record
          await req.payload.delete({
            collection: 'media',
            id: mediaId
          })

          results.push({
            mediaId,
            success: true,
            filename: media.filename
          })

          console.log(`✅ Successfully removed media: ${mediaId}`)

        } catch (error) {
          console.error(`❌ Error removing media ${mediaId}:`, error)
          results.push({
            mediaId,
            success: false,
            message: error instanceof Error ? error.message : 'Removal failed'
          })
        }
      }

      const successCount = results.filter(r => r.success).length
      const failureCount = results.filter(r => !r.success).length

      console.log(`🎉 Bulk removal completed: ${successCount} success, ${failureCount} failures`)

      return Response.json({
        success: true,
        message: `Removed ${successCount} avatars, ${failureCount} failures`,
        results,
        summary: {
          total: mediaIds.length,
          success: successCount,
          failures: failureCount
        }
      })

    } catch (error) {
      console.error('❌ Bulk avatar removal error:', error)
      return Response.json(
        { success: false, message: error instanceof Error ? error.message : 'Bulk removal failed' },
        { status: 500 }
      )
    }
  }
}
