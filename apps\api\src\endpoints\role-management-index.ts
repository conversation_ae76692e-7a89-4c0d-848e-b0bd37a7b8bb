import { Endpoint } from 'payload/config'
import {
  getRolesEndpoint,
  getPermissionsEndpoint,
  getRolePermissionsEndpoint,
  getRolesWithPermissionsEndpoint,
  getPermissionsByCategoryEndpoint,
  assignPermissionsToRoleEndpoint,
  removePermissionFromRoleEndpoint,
  getRoleStatisticsEndpoint,
  getSpecificRolePermissionsEndpoint,
  bulkRoleOperationsEndpoint
} from './role-management'

// Export all role management endpoints
export const roleManagementEndpoints: Endpoint[] = [
  // Basic CRUD operations
  getRolesEndpoint,
  getPermissionsEndpoint,
  getRolePermissionsEndpoint,
  
  // Enhanced endpoints
  getRolesWithPermissionsEndpoint,
  getPermissionsByCategoryEndpoint,
  getSpecificRolePermissionsEndpoint,
  
  // Role-Permission management
  assignPermissionsToRoleEndpoint,
  removePermissionFromRoleEndpoint,
  
  // Statistics and analytics
  getRoleStatisticsEndpoint,
  
  // Bulk operations
  bulkRoleOperationsEndpoint
]

// Export individual endpoints for selective use
export {
  getRolesEndpoint,
  getPermissionsEndpoint,
  getRolePermissionsEndpoint,
  getRolesWithPermissionsEndpoint,
  getPermissionsByCategoryEndpoint,
  assignPermissionsToRoleEndpoint,
  removePermissionFromRoleEndpoint,
  getRoleStatisticsEndpoint,
  getSpecificRolePermissionsEndpoint,
  bulkRoleOperationsEndpoint
}

// Default export for easy import
export default roleManagementEndpoints
