import { Endpoint } from 'payload'
import { seedThemes } from '../seed/themes'

// Endpoint to seed all themes (for development/testing)
export const seedThemesEndpoint: Endpoint = {
  path: '/seed/themes',
  method: 'get',
  handler: async (req: any) => {
    try {
      console.log('🌱 Seeding themes...')

      // Use the comprehensive themes seed function
      await seedThemes(req.payload)

      return Response.json({
        success: true,
        message: 'Themes seeded successfully!'
      })

    } catch (error) {
      console.error('❌ Seed themes error:', error)
      return Response.json({
        success: false,
        error: 'Failed to seed themes: ' + (error as any)?.message || 'Unknown error'
      }, { status: 500 })
    }
  }
}
