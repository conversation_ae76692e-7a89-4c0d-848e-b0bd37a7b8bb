import { Endpoint } from 'payload'
import payload from 'payload'
import { seedThemes } from '../seed/themes'

/**
 * Seed Database Endpoint
 * POST /api/seed - Creates super admin user directly in database
 */
const seedEndpoint: Endpoint = {
  path: '/seed',
  method: 'post',
  handler: async (req, res) => {
    try {
      console.log('🌱 Seeding database with super admin...')

      // Super Admin user data
      const superAdminData = {
        firstName: 'Super',
        lastName: 'Admin',
        email: '<EMAIL>',
        password: 'SuperAdmin@123',
        role: 'super_admin',
        isActive: true
      }

      // Check if super admin already exists
      const existingAdmin = await payload.find({
        collection: 'users',
        where: {
          email: {
            equals: superAdminData.email
          }
        }
      })

      if (existingAdmin.docs.length > 0) {
        return res.status(200).json({
          success: true,
          message: 'Super Admin already exists',
          data: {
            email: superAdminData.email,
            password: superAdminData.password,
            role: superAdminData.role,
            id: existingAdmin.docs[0].id
          }
        })
      }

      // Create the super admin user
      const superAdmin = await payload.create({
        collection: 'users',
        data: {
          ...superAdminData,
          role: 'super_admin' as const
        }
      })

      console.log('✅ Super Admin created successfully!')

      // Seed themes
      console.log('🎨 Seeding themes...')
      await seedThemes(payload)
      console.log('✅ Themes seeded successfully!')

      return res.status(201).json({
        success: true,
        message: 'Database seeded successfully (Super Admin + Themes)',
        data: {
          email: superAdminData.email,
          password: superAdminData.password,
          role: superAdminData.role,
          id: superAdmin.id,
          firstName: superAdmin.firstName,
          lastName: superAdmin.lastName
        }
      })

    } catch (error) {
      console.error('❌ Error seeding database:', error)
      return res.status(500).json({
        success: false,
        message: 'Failed to seed database',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
}

/**
 * Get Super Admin Info Endpoint
 * GET /api/seed - Returns super admin credentials
 */
const getSuperAdminEndpoint: Endpoint = {
  path: '/seed',
  method: 'get',
  handler: async (req, res) => {
    try {
      // Find super admin user
      const superAdmin = await payload.find({
        collection: 'users',
        where: {
          and: [
            {
              email: {
                equals: '<EMAIL>'
              }
            },
            {
              role: {
                equals: 'super_admin'
              }
            }
          ]
        }
      })

      if (superAdmin.docs.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Super Admin not found',
          credentials: {
            email: '<EMAIL>',
            password: 'SuperAdmin@123',
            note: 'Use POST /api/seed to create super admin'
          }
        })
      }

      return res.status(200).json({
        success: true,
        message: 'Super Admin found',
        data: {
          id: superAdmin.docs[0].id,
          email: superAdmin.docs[0].email,
          firstName: superAdmin.docs[0].firstName,
          lastName: superAdmin.docs[0].lastName,
          role: superAdmin.docs[0].role,
          isActive: superAdmin.docs[0].isActive
        },
        credentials: {
          email: '<EMAIL>',
          password: 'SuperAdmin@123'
        }
      })

    } catch (error) {
      console.error('❌ Error getting super admin info:', error)
      return res.status(500).json({
        success: false,
        message: 'Failed to get super admin info',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
}

export { seedEndpoint, getSuperAdminEndpoint }
