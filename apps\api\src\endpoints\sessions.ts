import { Endpoint } from 'payload/config'
import jwt from 'jsonwebtoken'

// Get user's active sessions
export const getActiveSessionsEndpoint: Endpoint = {
  path: '/auth/sessions',
  method: 'get',
  handler: async (req) => {
    const { user } = req
    
    if (!user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const sessions = await req.payload.find({
        collection: 'sessions',
        where: {
          and: [
            { user: { equals: user.id } },
            { isActive: { equals: true } },
            { expiresAt: { greater_than: new Date() } }
          ]
        },
        sort: '-lastActivity',
        limit: 50,
      })

      // Format sessions for frontend
      const formattedSessions = sessions.docs.map((session: any) => ({
        id: session.id,
        deviceType: session.deviceInfo?.deviceType,
        deviceName: session.deviceInfo?.deviceName,
        browser: session.deviceInfo?.browser,
        operatingSystem: session.deviceInfo?.operatingSystem,
        ipAddress: session.location?.ipAddress,
        location: {
          city: session.location?.city,
          country: session.location?.country,
          region: session.location?.region,
        },
        loginTime: session.createdAt,
        lastActivity: session.lastActivity,
        isCurrent: session.security?.isCurrent,
        isSecure: session.security?.isSecure,
      }))

      return Response.json({
        success: true,
        sessions: formattedSessions,
      })
    } catch (error) {
      console.error('Sessions fetch error:', error)
      return Response.json(
        { message: 'Failed to fetch sessions' },
        { status: 500 }
      )
    }
  },
}

// Terminate specific session
export const terminateSessionEndpoint: Endpoint = {
  path: '/auth/sessions/:sessionId',
  method: 'delete',
  handler: async (req) => {
    const { user } = req
    
    if (!user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const sessionId = req.routeParams?.sessionId

      if (!sessionId) {
        return Response.json({ message: 'Session ID is required' }, { status: 400 })
      }

      // Verify session belongs to user
      const session = await req.payload.findByID({
        collection: 'sessions',
        id: sessionId,
      })

      if (session.user !== user.id) {
        return Response.json({ message: 'Unauthorized' }, { status: 403 })
      }

      // Deactivate session
      await req.payload.update({
        collection: 'sessions',
        id: sessionId,
        data: {
          isActive: false,
        },
      })

      return Response.json({
        success: true,
        message: 'Session terminated successfully',
      })
    } catch (error) {
      console.error('Session termination error:', error)
      return Response.json(
        { message: 'Failed to terminate session' },
        { status: 500 }
      )
    }
  },
}

// Terminate all other sessions (keep current)
export const terminateOtherSessionsEndpoint: Endpoint = {
  path: '/auth/sessions/terminate-others',
  method: 'post',
  handler: async (req) => {
    const { user } = req
    
    if (!user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Get current session token from Authorization header
      const authHeader = req.headers.get('authorization')
      let currentSessionToken = null
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        currentSessionToken = authHeader.substring(7)
      }

      // Find all active sessions for user
      const sessions = await req.payload.find({
        collection: 'sessions',
        where: {
          and: [
            { user: { equals: user.id } },
            { isActive: { equals: true } }
          ]
        },
      })

      // Deactivate all sessions except current one
      const updatePromises = sessions.docs
        .filter((session: any) => session.sessionToken !== currentSessionToken)
        .map((session: any) => 
          req.payload.update({
            collection: 'sessions',
            id: session.id,
            data: { isActive: false },
          })
        )

      await Promise.all(updatePromises)

      return Response.json({
        success: true,
        message: `Terminated ${updatePromises.length} other sessions`,
      })
    } catch (error) {
      console.error('Bulk session termination error:', error)
      return Response.json(
        { message: 'Failed to terminate other sessions' },
        { status: 500 }
      )
    }
  },
}

// Get session limits/policies
export const getSessionLimitsEndpoint: Endpoint = {
  path: '/auth/session-limits',
  method: 'get',
  handler: async (req) => {
    const { user } = req
    
    if (!user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Get session limits from settings
      const settings = await req.payload.find({
        collection: 'settings',
        where: {
          key: { in: [
            'max_desktop_sessions',
            'max_mobile_sessions', 
            'max_total_sessions',
            'session_timeout',
            'require_secure_connection',
            'allow_concurrent_sessions'
          ]}
        },
      })

      const settingsMap = settings.docs.reduce((acc: any, setting: any) => {
        acc[setting.key] = setting.value
        return acc
      }, {})

      const limits = {
        maxDesktopSessions: settingsMap.max_desktop_sessions || 3,
        maxMobileSessions: settingsMap.max_mobile_sessions || 2,
        maxTotalSessions: settingsMap.max_total_sessions || 5,
        sessionTimeout: settingsMap.session_timeout || 120, // minutes
        requireSecureConnection: settingsMap.require_secure_connection || false,
        allowConcurrentSessions: settingsMap.allow_concurrent_sessions || true,
      }

      return Response.json({
        success: true,
        limits,
      })
    } catch (error) {
      console.error('Session limits fetch error:', error)
      return Response.json(
        { message: 'Failed to fetch session limits' },
        { status: 500 }
      )
    }
  },
}

// Create session (called during login)
export const createSessionEndpoint: Endpoint = {
  path: '/auth/sessions/create',
  method: 'post',
  handler: async (req) => {
    try {
      const body = await req.json()
      const { userId, sessionToken, deviceInfo, location, security } = body

      if (!userId || !sessionToken) {
        return Response.json(
          { message: 'User ID and session token are required' },
          { status: 400 }
        )
      }

      // Mark all other sessions as not current
      await req.payload.update({
        collection: 'sessions',
        where: { user: { equals: userId } },
        data: { 'security.isCurrent': false },
      })

      // Create new session
      const session = await req.payload.create({
        collection: 'sessions',
        data: {
          user: userId,
          sessionToken,
          deviceInfo: deviceInfo || {},
          location: location || {},
          security: {
            ...security,
            isCurrent: true,
          },
          isActive: true,
          lastActivity: new Date(),
        },
      })

      return Response.json({
        success: true,
        session,
      })
    } catch (error) {
      console.error('Session creation error:', error)
      return Response.json(
        { message: 'Failed to create session' },
        { status: 500 }
      )
    }
  },
}

export const sessionEndpoints = [
  getActiveSessionsEndpoint,
  terminateSessionEndpoint,
  terminateOtherSessionsEndpoint,
  getSessionLimitsEndpoint,
  createSessionEndpoint,
]
