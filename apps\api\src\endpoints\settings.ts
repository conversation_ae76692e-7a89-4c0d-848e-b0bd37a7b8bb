import { Endpoint } from 'payload'
import { requireAuth } from '../middleware/auth'
import { populate } from 'dotenv'

// Type definitions
interface Setting {
  id: string
  key: string
  value: string
  description?: string
  category: string
  type: 'string' | 'number' | 'boolean' | 'json' | 'url' | 'email' | 'textarea' | 'upload'
  is_public: boolean
  is_required?: boolean
  validation_rules?: {
    min_length?: number
    max_length?: number
    min_value?: number
    max_value?: number
    pattern?: string
  }
  upload?: string | { id: string; url: string; filename: string } // For file uploads
  createdAt: string
  updatedAt: string
}

// Helper function for authenticated super admin endpoints
const createSuperAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['super_admin'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Verify super admin role
      if (user.legacyRole !== 'super_admin') {
        return Response.json({
          success: false,
          error: 'Super admin access required'
        }, { status: 403 })
      }

      return handler(req)
    }
  }
}

// Helper function to validate setting value based on type
const validateSettingValue = (value: any, type: string): boolean => {
  try {
    // Allow empty strings, null, and undefined for optional fields - this is important for removing values
    if (value === '' || value === null || value === undefined) {
      return true
    }

    switch (type) {
      case 'number':
        return !isNaN(Number(value))
      case 'boolean':
        return ['true', 'false', '1', '0'].includes(value.toLowerCase())
      case 'json':
        JSON.parse(value)
        return true
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
      case 'url':
        return true // Allow any string for URL type
      case 'media':
        // Allow any value for media type - including empty string for removal
        return true
      case 'textarea':
        return true // Any string is valid for textarea
      case 'upload':
        return true // File uploads are handled separately
      case 'string':
        return true // Any string is valid
      default:
        return true
    }
  } catch (error) {
    return false
  }
}

// GET all settings (with filtering)
export const getSettings = createSuperAdminEndpoint(
  '/platform/settings',
  'get',
  async (req) => {
    try {
      // Extract query parameters
      const url = new URL(req.url)
      const category = url.searchParams.get('category')
      const type = url.searchParams.get('type')
      const is_public = url.searchParams.get('is_public')
      const search = url.searchParams.get('search')

      // Build query
      const query: any = {}

      if (category) query.category = { equals: category }
      if (type) query.type = { equals: type }
      if (is_public !== null) query.is_public = { equals: is_public === 'true' }
      if (search) {
        query.or = [
          { key: { contains: search } },
          { description: { contains: search } },
          { value: { contains: search } },
        ]
      }

      // Fetch settings from database (get all without pagination)
      const settings = await req.payload.find({
        collection: 'options',
        where: query,
        sort: 'key',
        limit: 0, // Get all results without pagination
        populate:{
          value: true
        }
      }) 

      // Process settings to resolve media IDs to URLs
      
      

      return Response.json({
        success: true,
        settings: settings,
        totalDocs: settings.totalDocs,
        totalPages: settings.totalPages,
        page: settings.page,
        limit: settings.limit
      })
    } catch (error) {
      console.error('Error fetching settings:', error)
      return Response.json({
        success: false,
        message: 'Error fetching settings',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, { status: 500 })
    }
  }
)

// GET setting by key
export const getSettingByKey = createSuperAdminEndpoint(
  '/platform/settings/:key',
  'get',
  async (req) => {
    try {
      // Extract key from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const key = pathParts[pathParts.length - 1]

      if (!key || key === 'undefined') {
        return Response.json({
          success: false,
          error: 'Setting key is required'
        }, { status: 400 })
      }

      // Find setting by key
      const setting = await req.payload.find({
        collection: 'options',
        where: { key: { equals: key } },
      })

      if (!setting.docs || setting.docs.length === 0) {
        return Response.json({
          success: false,
          message: `Setting with key '${key}' not found`,
        }, { status: 404 })
      }

      return Response.json({
        success: true,
        setting: setting.docs[0]
      })
    } catch (error) {
      console.error(`Error fetching setting by key:`, error)
      return Response.json({
        success: false,
        message: 'Error fetching setting',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, { status: 500 })
    }
  }
)

// POST create new setting
export const createSetting = createSuperAdminEndpoint(
  '/platform/settings',
  'post',
  async (req) => {
    try {
      const body = await req.json()
      const { key, value, description, category, type, is_public, is_required, validation_rules } = body

      // Validate required fields
      if (!key || value === undefined || !category || !type) {
        return Response.json({
          success: false,
          message: 'Missing required fields: key, value, category, and type are required',
        }, { status: 400 })
      }

      // Validate key format
      if (!/^[a-z0-9_]+$/.test(key)) {
        return Response.json({
          success: false,
          message: 'Key must contain only lowercase letters, numbers, and underscores',
        }, { status: 400 })
      }

      // Validate value based on type
      if (!validateSettingValue(value, type)) {
        return Response.json({
          success: false,
          message: `Value is not valid for type '${type}'`,
        }, { status: 400 })
      }

      // Check if setting already exists
      const existingSetting = await req.payload.find({
        collection: 'options',
        where: { key: { equals: key } },
      })

      if (existingSetting.docs && existingSetting.docs.length > 0) {
        return Response.json({
          success: false,
          message: `Setting with key '${key}' already exists`,
        }, { status: 409 })
      }

      // Create new setting
      const newSetting = await req.payload.create({
        collection: 'options',
        data: {
          key,
          value,
          description,
          category,
          type,
          is_public: is_public || false,
          is_required: is_required || false,
          validation_rules,
        },
      })

      return Response.json({
        success: true,
        setting: newSetting
      }, { status: 201 })
    } catch (error) {
      console.error('Error creating setting:', error)
      return Response.json({
        success: false,
        message: 'Error creating setting',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, { status: 500 })
    }
  }
)

// PUT update setting by key
export const updateSetting = createSuperAdminEndpoint(
  '/platform/settings/:key',
  'put',
  async (req) => {
    try {
      // Extract key from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const key = pathParts[pathParts.length - 1]

      if (!key || key === 'undefined') {
        return Response.json({
          success: false,
          error: 'Setting key is required'
        }, { status: 400 })
      }

      const body = await req.json()
      const { value, description, category, type, is_public, is_required, validation_rules } = body

      // Find setting by key
      const existingSetting = await req.payload.find({
        collection: 'options',
        where: { key: { equals: key } },
      })

      if (!existingSetting.docs || existingSetting.docs.length === 0) {
        return Response.json({
          success: false,
          message: `Setting with key '${key}' not found`,
        }, { status: 404 })
      }

      const settingId = existingSetting.docs[0].id

      // Validate value based on type if type is provided or use existing type
      const settingType = type || existingSetting.docs[0].type
      if (value !== undefined && value !== null && !validateSettingValue(value, settingType)) {
        return Response.json({
          success: false,
          message: `Value is not valid for type '${settingType}'`,
        }, { status: 400 })
      }

      // Update setting
      const updatedSetting = await req.payload.update({
        collection: 'options',
        id: settingId,
        data: {
          value: value !== undefined ? value : existingSetting.docs[0].value,
          description: description !== undefined ? description : existingSetting.docs[0].description,
          category: category || existingSetting.docs[0].category,
          type: type || existingSetting.docs[0].type,
          is_public: is_public !== undefined ? is_public : existingSetting.docs[0].is_public,
          is_required: is_required !== undefined ? is_required : existingSetting.docs[0].is_required,
          validation_rules: validation_rules || existingSetting.docs[0].validation_rules,
        },
      })

      return Response.json({
        success: true,
        setting: updatedSetting
      })
    } catch (error) {
      console.error('Error updating setting:', error)
      return Response.json({
        success: false,
        message: 'Error updating setting',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, { status: 500 })
    }
  }
)

// DELETE setting by key
export const deleteSetting = createSuperAdminEndpoint(
  '/platform/settings/:key',
  'delete',
  async (req) => {
    try {
      // Extract key from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const key = pathParts[pathParts.length - 1]

      if (!key || key === 'undefined') {
        return Response.json({
          success: false,
          error: 'Setting key is required'
        }, { status: 400 })
      }

      // Find setting by key
      const existingSetting = await req.payload.find({
        collection: 'options',
        where: { key: { equals: key } },
      })

      if (!existingSetting.docs || existingSetting.docs.length === 0) {
        return Response.json({
          success: false,
          message: `Setting with key '${key}' not found`,
        }, { status: 404 })
      }

      const settingId = existingSetting.docs[0].id

      // Check if setting is required
      if (existingSetting.docs[0].is_required) {
        return Response.json({
          success: false,
          message: `Cannot delete required setting '${key}'`,
        }, { status: 400 })
      }

      // Delete setting
      await req.payload.delete({
        collection: 'options',
        id: settingId,
      })

      return Response.json({
        success: true,
        message: `Setting '${key}' deleted successfully`,
      })
    } catch (error) {
      console.error('Error deleting setting:', error)
      return Response.json({
        success: false,
        message: 'Error deleting setting',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, { status: 500 })
    }
  }
)


// POST bulk update settings
export const bulkUpdateSettings = createSuperAdminEndpoint(
  '/platform/settings/bulk',
  'post',
  async (req) => {
    try {
      const body = await req.json()
      const { settings } = body

      if (!Array.isArray(settings)) {
        return Response.json({
          success: false,
          message: 'Settings must be an array',
        }, { status: 400 })
      }

      const results = []
      const errors = []

      // Process each setting
      for (const setting of settings) {
        try {
          const { key, value, type, ...rest } = setting

          if (!key) {
            errors.push({ key, error: 'Key is required' })
            continue
          }

          // Validate value based on type if provided
          // Note: We explicitly allow null, undefined, and empty string values for all types
          // This is useful for removing/clearing settings like logos, favicons, etc.
          console.log(`🔍 Processing setting: ${key} = "${value}" (type: ${type}, typeof: ${typeof value})`)

          if (type) {
            // For URL type, explicitly allow null, undefined, and empty string
            if (type === 'url' && (value === null || value === undefined || value === '')) {
              console.log(`✅ Allowing empty/null URL value for ${key}`)
              // Skip validation for empty URL values - they are allowed
            } else if (type === 'media') {
              console.log(`✅ Skipping validation for media type ${key}: "${value}" (any value allowed)`)
              // Skip validation entirely for media type - any value is allowed (media ID or empty for removal)
            } else {
              console.log(`🔍 Validating ${key} with validateSettingValue...`)
              if (!validateSettingValue(value, type)) {
                console.log(`❌ Validation failed for ${key}: "${value}" (type: ${type})`)
                errors.push({ key, error: `The following field is invalid: Value` })
                continue
              } else {
                console.log(`✅ Validation passed for ${key}`)
              }
            }
          }

          // Find existing setting
          const existingSetting = await req.payload.find({
            collection: 'options',
            where: { key: { equals: key } },
          })

          if (!existingSetting.docs || existingSetting.docs.length === 0) {
            // Create new setting - convert null to empty string for storage
            const newSetting = await req.payload.create({
              collection: 'options',
              data: {
                key,
                value: value === null ? '' : value,
                type,
                ...rest,
              },
            })
            results.push({ key, operation: 'created', id: newSetting.id })
          } else {
            // Update existing setting - convert null to empty string for storage
            const settingId = existingSetting.docs[0].id
            const updatedSetting = await req.payload.update({
              collection: 'options',
              id: settingId,
              data: {
                value: value === null ? '' : value,
                type,
                ...rest,
              },
            })
            results.push({ key, operation: 'updated', id: updatedSetting.id })
          }
        } catch (error) {
          errors.push({ key: setting.key, error: error instanceof Error ? error.message : 'Unknown error' })
        }
      }

      return Response.json({
        success: true,
        results,
        errors,
        updated: results.length,
        failed: errors.length,
      })
    } catch (error) {
      console.error('Error bulk updating settings:', error)
      return Response.json({
        success: false,
        message: 'Error bulk updating settings',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, { status: 500 })
    }
  }
)

// GET settings by category
export const getSettingsByCategory = createSuperAdminEndpoint(
  '/platform/settings/category/:category',
  'get',
  async (req) => {
    try {
      // Extract category from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const category = pathParts[pathParts.length - 1]

      if (!category || category === 'undefined') {
        return Response.json({
          success: false,
          error: 'Category is required'
        }, { status: 400 })
      }

      // Build query
      const query: any = { category: { equals: category } }

      // Fetch settings from database (get all without pagination)
      const settings = await req.payload.find({
        collection: 'options',
        where: query,
        sort: 'key',
        limit: 0, // Get all results without pagination
      })

      return Response.json({
        success: true,
        settings: settings.docs,
        totalDocs: settings.totalDocs,
        category
      })
    } catch (error) {
      console.error(`Error fetching settings by category:`, error)
      return Response.json({
        success: false,
        message: 'Error fetching settings',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, { status: 500 })
    }
  }
)

// Export all endpoints
export const settingsEndpoints = [
  getSettings,
  getSettingByKey,
  createSetting,
  updateSetting,
  deleteSetting,
  bulkUpdateSettings,
  getSettingsByCategory,
]

export default settingsEndpoints
