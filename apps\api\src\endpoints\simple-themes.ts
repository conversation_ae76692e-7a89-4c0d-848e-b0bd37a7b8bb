import { Endpoint } from 'payload'

// Simple working endpoint to get themes by type
export const getThemesByTypeSimple: Endpoint = {
  path: '/api/themes-by-type',
  method: 'get',
  handler: async (req) => {
    try {
      console.log('🎨 Simple themes endpoint called')
      
      // Parse query parameters
      const url = new URL(req.url || 'http://localhost:3001')
      const type = url.searchParams.get('type') || 'institute'
      
      console.log('📋 Requested type:', type)

      // If no database connection, return mock data
      if (!req.payload) {
        console.log('⚠️ No payload connection, returning mock data')
        return Response.json({
          success: true,
          themes: getMockInstituteThemes(),
          total: getMockInstituteThemes().length,
          source: 'mock'
        })
      }

      // Try to get from database
      try {
        const themes = await req.payload.find({
          collection: 'themes',
          where: {
            type: { equals: type },
            isActive: { equals: true }
          },
          sort: 'name',
          limit: 50
        })

        console.log(`✅ Found ${themes.totalDocs} themes in database`)

        if (themes.totalDocs === 0) {
          console.log('📋 No themes in database, returning mock data')
          return Response.json({
            success: true,
            themes: getMockInstituteThemes(),
            total: getMockInstituteThemes().length,
            source: 'mock'
          })
        }

        return Response.json({
          success: true,
          themes: themes.docs.map((theme: any) => ({
            id: theme.id,
            name: theme.name,
            slug: theme.slug,
            description: theme.description,
            category: theme.category,
            type: theme.type,
            colors: theme.colors,
            fonts: theme.fonts,
            features: theme.features || [],
            isActive: theme.isActive,
            usageCount: theme.usageCount || 0,
            rating: theme.rating || { average: 0, count: 0 }
          })),
          total: themes.totalDocs,
          source: 'database'
        })

      } catch (dbError) {
        console.log('⚠️ Database error, returning mock data:', dbError)
        return Response.json({
          success: true,
          themes: getMockInstituteThemes(),
          total: getMockInstituteThemes().length,
          source: 'mock'
        })
      }

    } catch (error) {
      console.error('❌ Themes endpoint error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch themes',
        themes: getMockInstituteThemes(),
        total: getMockInstituteThemes().length,
        source: 'fallback'
      }, { status: 500 })
    }
  }
}

// Mock institute themes data
function getMockInstituteThemes() {
  return [
    {
      id: '1',
      name: 'Education Modern',
      slug: 'education-modern',
      description: 'Modern education theme with course marketplace functionality',
      category: 'education_modern',
      type: 'institute',
      colors: {
        primary: '#10B981',
        secondary: '#3B82F6',
        accent: '#F59E0B',
        background: '#FFFFFF',
        text: '#1F2937'
      },
      fonts: {
        heading: 'Inter, sans-serif',
        body: 'Inter, sans-serif'
      },
      features: ['Course Marketplace', 'Search Filters', 'Shopping Cart', 'Student Portal'],
      isActive: true,
      usageCount: 125,
      rating: { average: 4.8, count: 156 }
    },
    {
      id: '2',
      name: 'Professional Academy',
      slug: 'professional-academy',
      description: 'Professional theme for coaching centers and academies',
      category: 'coaching_professional',
      type: 'institute',
      colors: {
        primary: '#3B82F6',
        secondary: '#10B981',
        accent: '#F59E0B',
        background: '#FFFFFF',
        text: '#1F2937'
      },
      fonts: {
        heading: 'Poppins, sans-serif',
        body: 'Inter, sans-serif'
      },
      features: ['Course Catalog', 'Student Portal', 'Online Enrollment', 'Progress Tracking'],
      isActive: true,
      usageCount: 89,
      rating: { average: 4.5, count: 89 }
    },
    {
      id: '3',
      name: 'Learning Hub',
      slug: 'learning-hub',
      description: 'Community-focused learning theme with social features',
      category: 'online_academy',
      type: 'institute',
      colors: {
        primary: '#EC4899',
        secondary: '#8B5CF6',
        accent: '#F59E0B',
        background: '#FFFFFF',
        text: '#1F2937'
      },
      fonts: {
        heading: 'Montserrat, sans-serif',
        body: 'Nunito, sans-serif'
      },
      features: ['Social Learning', 'Peer Reviews', 'Study Groups', 'Gamification'],
      isActive: true,
      usageCount: 67,
      rating: { average: 4.2, count: 67 }
    },
    {
      id: '4',
      name: 'Classic Education',
      slug: 'classic-education',
      description: 'Traditional education theme with formal design',
      category: 'education_classic',
      type: 'institute',
      colors: {
        primary: '#1F2937',
        secondary: '#6B7280',
        accent: '#F59E0B',
        background: '#FFFFFF',
        text: '#111827'
      },
      fonts: {
        heading: 'Georgia, serif',
        body: 'Times New Roman, serif'
      },
      features: ['Traditional Layout', 'Course Catalog', 'Student Records', 'Announcements'],
      isActive: true,
      usageCount: 45,
      rating: { average: 4.0, count: 32 }
    },
    {
      id: '5',
      name: 'Training Center Pro',
      slug: 'training-center-pro',
      description: 'Professional training center theme with certification features',
      category: 'training_center',
      type: 'institute',
      colors: {
        primary: '#059669',
        secondary: '#0D9488',
        accent: '#F59E0B',
        background: '#FFFFFF',
        text: '#1F2937'
      },
      fonts: {
        heading: 'Roboto, sans-serif',
        body: 'Open Sans, sans-serif'
      },
      features: ['Certification Management', 'Skills Assessment', 'Progress Tracking', 'Instructor Portal'],
      isActive: true,
      usageCount: 78,
      rating: { average: 4.6, count: 94 }
    }
  ]
}

export default getThemesByTypeSimple
