import type { Endpoint } from 'payload'
import { requireAuth } from '../middleware/auth'
import path from 'path'
import fs from 'fs/promises'
import { v4 as uuidv4 } from 'uuid'

console.log('🔥 simple-upload.ts file loaded - minimal upload endpoint!')

// Simple file upload endpoint that bypasses all Payload complexity
export const simpleUploadEndpoint: Endpoint = {
  path: '/simple-upload',
  method: 'post',
  handler: async (req) => {
    console.log('🚀🚀🚀 SIMPLE UPLOAD ENDPOINT CALLED! 🚀🚀🚀')
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }
    
    console.log('✅ Auth check passed, user:', {
      id: req.user?.id,
      email: req.user?.email
    })

    try {
      // Parse form data
      const formData = await req.formData()
      const file = formData.get('file') as File
      const uploadType = formData.get('uploadType') as string || 'avatar'
      
      console.log('📋 Simple upload details:', {
        fileName: file?.name,
        fileSize: file?.size,
        fileType: file?.type,
        uploadType
      })

      if (!file) {
        console.log('❌ No file provided')
        return Response.json(
          { success: false, message: 'No file provided' },
          { status: 400 }
        )
      }

      // Determine folder based on upload type
      const folderMap: Record<string, string> = {
        avatar: 'avatars',
        course_thumbnail: 'courses',
        institute_logo: 'institutes',
        document: 'documents',
        general: 'uploads'
      }
      
      const folder = folderMap[uploadType] || 'uploads'
      
      // Generate unique filename
      const fileExtension = path.extname(file.name)
      const baseName = path.basename(file.name, fileExtension)
      const uniqueId = uuidv4()
      const timestamp = Date.now()
      const filename = `${baseName}-${timestamp}-${uniqueId}${fileExtension}`
      
      // Create full path
      const uploadDir = path.resolve(process.cwd(), 'media')
      const folderPath = path.join(uploadDir, folder)
      const fullPath = path.join(folderPath, filename)
      
      console.log('📁 File paths:', {
        uploadDir,
        folder,
        folderPath,
        filename,
        fullPath
      })

      // Convert File to Buffer
      const arrayBuffer = await file.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      
      console.log('📦 File buffer created:', {
        bufferSize: buffer.length,
        originalSize: file.size
      })

      // Ensure directory exists
      console.log('📂 Creating directory:', folderPath)
      await fs.mkdir(folderPath, { recursive: true })
      console.log('✅ Directory created/verified')

      // Write file
      console.log('💾 Writing file to:', fullPath)
      await fs.writeFile(fullPath, buffer)
      console.log('✅ File written successfully')

      // Generate URL
      const url = `/media/${folder}/${filename}`
      
      console.log('🌐 File URL generated:', url)

      // Create simple response
      const result = {
        success: true,
        message: 'File uploaded successfully',
        file: {
          id: uniqueId,
          filename: filename,
          originalName: file.name,
          url: url,
          size: file.size,
          type: file.type,
          folder: folder,
          uploadType: uploadType
        }
      }

      console.log('✅ Simple upload completed successfully:', {
        filename,
        url,
        size: file.size
      })

      return Response.json(result)

    } catch (error) {
      console.error('💥 Simple upload error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      return Response.json(
        { 
          success: false, 
          message: `Simple upload failed: ${errorMessage}`,
          error: errorMessage
        },
        { status: 500 }
      )
    }
  },
}

// Get uploaded files (simple version)
export const getSimpleFilesEndpoint: Endpoint = {
  path: '/simple-upload/files',
  method: 'get',
  handler: async (req) => {
    console.log('🚀🚀🚀 GET SIMPLE FILES ENDPOINT CALLED! 🚀🚀🚀')
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      return authCheck
    }

    try {
      const uploadDir = path.resolve(process.cwd(), 'media')
      const folders = ['avatars', 'courses', 'institutes', 'documents', 'uploads']
      
      const files: any[] = []
      
      for (const folder of folders) {
        const folderPath = path.join(uploadDir, folder)
        
        try {
          const folderFiles = await fs.readdir(folderPath)
          
          for (const filename of folderFiles) {
            const filePath = path.join(folderPath, filename)
            const stats = await fs.stat(filePath)
            
            files.push({
              filename,
              folder,
              url: `/media/${folder}/${filename}`,
              size: stats.size,
              createdAt: stats.birthtime,
              modifiedAt: stats.mtime
            })
          }
        } catch (error) {
          // Folder doesn't exist or can't be read, skip it
          console.log(`⚠️ Could not read folder ${folder}:`, error)
        }
      }
      
      // Sort by creation date (newest first)
      files.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      
      return Response.json({
        success: true,
        files,
        count: files.length
      })

    } catch (error) {
      console.error('💥 Get simple files error:', error)
      return Response.json(
        { success: false, message: 'Failed to get files' },
        { status: 500 }
      )
    }
  },
}
