import type { Endpoint } from 'payload'
import path from 'path'
import fs from 'fs/promises'
import { lookup } from 'mime-types'

console.log('🔥 static-files.ts file loaded - static file serving endpoint!')

// Static file serving endpoint that works immediately without server restart
export const staticFileEndpoint: Endpoint = {
  path: '/media/:path*',
  method: 'get',
  handler: async (req) => {
    console.log('📁📁📁 STATIC FILE ENDPOINT CALLED! 📁📁📁')
    
    try {
      // Extract the file path from the URL
      const url = new URL(req.url || '', 'http://localhost')
      const requestedPath = url.pathname.replace('/media/', '')
      
      console.log('📋 Static file request:', {
        originalUrl: req.url,
        pathname: url.pathname,
        requestedPath: requestedPath
      })

      if (!requestedPath) {
        console.log('❌ No file path provided')
        return new Response('File path required', { status: 400 })
      }

      // Construct the full file path
      const mediaDir = path.resolve(process.cwd(), 'media')
      const fullPath = path.join(mediaDir, requestedPath)
      
      console.log('📁 File paths:', {
        mediaDir,
        requestedPath,
        fullPath
      })

      // Security check: ensure the path is within the media directory
      const normalizedPath = path.normalize(fullPath)
      const normalizedMediaDir = path.normalize(mediaDir)
      
      if (!normalizedPath.startsWith(normalizedMediaDir)) {
        console.log('❌ Security violation: path outside media directory')
        return new Response('Access denied', { status: 403 })
      }

      // Check if file exists
      try {
        const stats = await fs.stat(fullPath)
        
        if (!stats.isFile()) {
          console.log('❌ Path is not a file:', fullPath)
          return new Response('Not a file', { status: 404 })
        }
        
        console.log('✅ File found:', {
          path: fullPath,
          size: stats.size,
          modified: stats.mtime
        })
      } catch (error) {
        console.log('❌ File not found:', fullPath)
        return new Response('File not found', { status: 404 })
      }

      // Read the file
      const fileBuffer = await fs.readFile(fullPath)
      
      // Determine MIME type
      const mimeType = lookup(fullPath) || 'application/octet-stream'
      
      console.log('📦 Serving file:', {
        path: fullPath,
        size: fileBuffer.length,
        mimeType
      })

      // Create response with proper headers
      const response = new Response(fileBuffer, {
        status: 200,
        headers: {
          'Content-Type': mimeType,
          'Content-Length': fileBuffer.length.toString(),
          'Cache-Control': 'public, max-age=86400', // 1 day cache
          'ETag': `"${stats.mtime.getTime()}-${stats.size}"`,
          'Last-Modified': stats.mtime.toUTCString(),
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
          'Access-Control-Allow-Headers': 'Content-Type',
        }
      })

      console.log('✅ File served successfully:', {
        path: requestedPath,
        size: fileBuffer.length,
        mimeType
      })

      return response

    } catch (error) {
      console.error('💥 Static file serving error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      return new Response(`Static file error: ${errorMessage}`, { 
        status: 500,
        headers: {
          'Content-Type': 'text/plain'
        }
      })
    }
  },
}

// Debug endpoint to check media directory structure
export const debugMediaEndpoint: Endpoint = {
  path: '/debug/media',
  method: 'get',
  handler: async (req) => {
    console.log('🔍🔍🔍 DEBUG MEDIA ENDPOINT CALLED! 🔍🔍🔍')
    
    try {
      const mediaDir = path.resolve(process.cwd(), 'media')
      
      console.log('📁 Checking media directory:', mediaDir)
      
      // Check if media directory exists
      try {
        const mediaDirStats = await fs.stat(mediaDir)
        console.log('✅ Media directory exists:', mediaDirStats.isDirectory())
      } catch (error) {
        console.log('❌ Media directory does not exist')
        return Response.json({
          success: false,
          message: 'Media directory does not exist',
          mediaDir
        })
      }

      // List all files in media directory and subdirectories
      const files: any[] = []
      
      async function scanDirectory(dirPath: string, relativePath: string = '') {
        try {
          const items = await fs.readdir(dirPath)
          
          for (const item of items) {
            const itemPath = path.join(dirPath, item)
            const itemRelativePath = relativePath ? `${relativePath}/${item}` : item
            
            try {
              const stats = await fs.stat(itemPath)
              
              if (stats.isDirectory()) {
                // Recursively scan subdirectory
                await scanDirectory(itemPath, itemRelativePath)
              } else {
                // Add file to list
                files.push({
                  name: item,
                  path: itemRelativePath,
                  fullPath: itemPath,
                  size: stats.size,
                  modified: stats.mtime,
                  url: `/media/${itemRelativePath}`
                })
              }
            } catch (error) {
              console.log(`⚠️ Could not stat ${itemPath}:`, error)
            }
          }
        } catch (error) {
          console.log(`⚠️ Could not read directory ${dirPath}:`, error)
        }
      }
      
      await scanDirectory(mediaDir)
      
      // Sort files by modification date (newest first)
      files.sort((a, b) => new Date(b.modified).getTime() - new Date(a.modified).getTime())
      
      console.log(`📊 Found ${files.length} files in media directory`)
      
      return Response.json({
        success: true,
        mediaDir,
        totalFiles: files.length,
        files: files.slice(0, 20), // Return first 20 files
        allFiles: files.length <= 20 ? files : undefined,
        message: `Found ${files.length} files in media directory`
      })

    } catch (error) {
      console.error('💥 Debug media error:', error)
      return Response.json({
        success: false,
        message: `Debug error: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  },
}
