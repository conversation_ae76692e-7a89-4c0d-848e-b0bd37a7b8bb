import type { Endpoint } from 'payload'
import { requireAuth } from '../../middleware/auth'
import { createUploadMiddleware, AVATAR_SIZES } from '../../middleware/upload-middleware'

console.log('🔥 super-admin/avatar-upload.ts file loaded - avatar upload endpoint with flexible storage!')

// Upload avatar for super admin
export const uploadAvatarEndpoint: Endpoint = {
  path: '/api/super-admin/avatar/upload',
  method: 'post',
  handler: async (req) => {
    console.log('🚀🚀🚀 SUPER ADMIN AVATAR UPLOAD ENDPOINT CALLED! 🚀🚀🚀')
    console.log('📝 Request URL:', req.url)
    console.log('📝 Request method:', req.method)
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }
    
    console.log('✅ Auth check passed, user:', {
      id: req.user?.id,
      email: req.user?.email,
      legacyRole: req.user?.legacyRole
    })

    try {
      // Create upload middleware instance with current storage configuration
      console.log('🔧 Creating upload middleware...')
      const uploadMiddleware = await createUploadMiddleware(req.payload)

      // Handle file upload with avatar-specific settings
      const uploadResult = await uploadMiddleware.handleFileUpload(req, {
        mediaType: 'user_avatar',
        folder: 'avatars',
        generateSizes: AVATAR_SIZES,
        maxFileSize: 5 * 1024 * 1024, // 5MB
        allowedMimeTypes: ['image/*']
      })

      if (!uploadResult.success) {
        console.log('❌ Upload failed:', uploadResult.message)
        return Response.json(
          { success: false, message: uploadResult.message },
          { status: 400 }
        )
      }

      console.log('📊 Avatar uploaded successfully:', {
        id: uploadResult.media?.id,
        filename: uploadResult.media?.filename,
        url: uploadResult.media?.url,
        sizes: Object.keys(uploadResult.media?.sizes || {})
      })

      // Update user's avatar field
      console.log('🔄 Updating user avatar...')

      const updatedUser = await req.payload.update({
        collection: 'users',
        id: req.user!.id,
        data: {
          avatar: uploadResult.media!.id,
        },
      })

      console.log('✅ User avatar updated successfully')

      // Remove password from response
      const { password: _, ...userWithoutPassword } = updatedUser

      return Response.json({
        success: true,
        message: 'Avatar uploaded successfully',
        media: {
          id: uploadResult.media!.id,
          filename: uploadResult.media!.filename,
          url: uploadResult.media!.url,
          sizes: uploadResult.media!.sizes,
          alt: uploadResult.media!.alt,
          mediaType: uploadResult.media!.mediaType,
        },
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('💥 Avatar upload error:', error)
      return Response.json(
        { success: false, message: 'Failed to upload avatar' },
        { status: 500 }
      )
    }
  },
}

// Get current user avatar
export const getCurrentAvatarEndpoint: Endpoint = {
  path: '/api/super-admin/avatar/me',
  method: 'get',
  handler: async (req) => {
    console.log('🚀🚀🚀 GET CURRENT AVATAR ENDPOINT CALLED! 🚀🚀🚀')
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }

    try {
      console.log('🔍 Fetching user with avatar...')
      
      const user = await req.payload.findByID({
        collection: 'users',
        id: req.user!.id,
        depth: 2, // Include avatar media details
      })

      if (!user) {
        console.log('❌ User not found')
        return Response.json(
          { success: false, message: 'User not found' },
          { status: 404 }
        )
      }

      console.log('📊 User avatar info:', {
        hasAvatar: !!user.avatar,
        avatarId: user.avatar?.id,
        avatarUrl: user.avatar?.url
      })

      return Response.json({
        success: true,
        avatar: user.avatar || null,
      })

    } catch (error) {
      console.error('💥 Get avatar error:', error)
      return Response.json(
        { success: false, message: 'Failed to get avatar' },
        { status: 500 }
      )
    }
  },
}

// Remove current user avatar
export const removeAvatarEndpoint: Endpoint = {
  path: '/api/super-admin/avatar/me',
  method: 'delete',
  handler: async (req) => {
    console.log('🚀🚀🚀 REMOVE AVATAR ENDPOINT CALLED! 🚀🚀🚀')
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }

    try {
      console.log('🔍 Getting current user avatar...')
      
      const user = await req.payload.findByID({
        collection: 'users',
        id: req.user!.id,
        depth: 1,
      })

      if (!user || !user.avatar) {
        console.log('❌ No avatar to remove')
        return Response.json(
          { success: false, message: 'No avatar to remove' },
          { status: 400 }
        )
      }

      const avatarId = typeof user.avatar === 'object' ? user.avatar.id : user.avatar

      console.log('🗑️ Removing avatar from user...')
      
      // Remove avatar from user
      const updatedUser = await req.payload.update({
        collection: 'users',
        id: req.user!.id,
        data: {
          avatar: null,
        },
      })

      console.log('🗑️ Deleting media file...')
      
      // Delete the media file
      await req.payload.delete({
        collection: 'media',
        id: avatarId,
      })

      console.log('✅ Avatar removed successfully')

      // Remove password from response
      const { password: _, ...userWithoutPassword } = updatedUser

      return Response.json({
        success: true,
        message: 'Avatar removed successfully',
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('💥 Remove avatar error:', error)
      return Response.json(
        { success: false, message: 'Failed to remove avatar' },
        { status: 500 }
      )
    }
  },
}
