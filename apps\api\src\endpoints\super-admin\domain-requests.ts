import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'

// Middleware to check if user is super admin
const requireSuperAdmin = async (req: any, res: any, next: any) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'Authentication required' })
    }

    if (req.user.legacyRole !== 'super_admin') {
      return res.status(403).json({ success: false, error: 'Super admin access required' })
    }

    next()
  } catch (error) {
    console.error('Super admin middleware error:', error)
    return res.status(500).json({ success: false, error: 'Internal server error' })
  }
}

// Get all domain requests
export const getDomainRequestsEndpoint: Endpoint = {
  path: '/super-admin/domain-requests',
  method: 'get',
  handler: [requireAuth, requireSuperAdmin, async (req, res) => {
    try {
      const { page = 1, limit = 20, status = '', search = '' } = req.query

      // Build search query
      const whereClause: any = {}

      if (status) {
        whereClause.status = { equals: status }
      }

      if (search) {
        whereClause.or = [
          { requestedDomain: { contains: search } },
          { 'institute.name': { contains: search } }
        ]
      }

      // Fetch domain requests
      const domainRequests = await req.payload.find({
        collection: 'domain-requests',
        where: whereClause,
        depth: 2,
        limit: parseInt(limit as string),
        page: parseInt(page as string),
        sort: '-requestedAt'
      })

      return res.json({
        success: true,
        data: domainRequests.docs,
        pagination: {
          page: domainRequests.page,
          limit: domainRequests.limit,
          totalPages: domainRequests.totalPages,
          totalDocs: domainRequests.totalDocs,
          hasNextPage: domainRequests.hasNextPage,
          hasPrevPage: domainRequests.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Get domain requests error:', error)
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch domain requests'
      })
    }
  }]
}

// Approve domain request
export const approveDomainRequestEndpoint: Endpoint = {
  path: '/super-admin/domain-requests/:id/approve',
  method: 'patch',
  handler: [requireAuth, requireSuperAdmin, async (req, res) => {
    try {
      const requestId = req.params.id
      const { dnsConfiguration, notes } = req.body

      // Update domain request status
      const updatedRequest = await req.payload.update({
        collection: 'domain-requests',
        id: requestId,
        data: {
          status: 'approved',
          reviewedAt: new Date().toISOString(),
          reviewedBy: req.user.id,
          dnsConfiguration,
          notes,
          sslStatus: 'pending'
        }
      })

      return res.json({
        success: true,
        data: updatedRequest,
        message: 'Domain request approved successfully'
      })

    } catch (error) {
      console.error('Approve domain request error:', error)
      return res.status(500).json({
        success: false,
        error: 'Failed to approve domain request'
      })
    }
  }]
}

// Reject domain request
export const rejectDomainRequestEndpoint: Endpoint = {
  path: '/super-admin/domain-requests/:id/reject',
  method: 'patch',
  handler: [requireAuth, requireSuperAdmin, async (req, res) => {
    try {
      const requestId = req.params.id
      const { rejectionReason, notes } = req.body

      if (!rejectionReason) {
        return res.status(400).json({
          success: false,
          error: 'Rejection reason is required'
        })
      }

      // Update domain request status
      const updatedRequest = await req.payload.update({
        collection: 'domain-requests',
        id: requestId,
        data: {
          status: 'rejected',
          reviewedAt: new Date().toISOString(),
          reviewedBy: req.user.id,
          rejectionReason,
          notes
        }
      })

      return res.json({
        success: true,
        data: updatedRequest,
        message: 'Domain request rejected'
      })

    } catch (error) {
      console.error('Reject domain request error:', error)
      return res.status(500).json({
        success: false,
        error: 'Failed to reject domain request'
      })
    }
  }]
}

// Activate domain (mark as live)
export const activateDomainEndpoint: Endpoint = {
  path: '/super-admin/domain-requests/:id/activate',
  method: 'patch',
  handler: [requireAuth, requireSuperAdmin, async (req, res) => {
    try {
      const requestId = req.params.id
      const { sslStatus = 'active', notes } = req.body

      // Update domain request status
      const updatedRequest = await req.payload.update({
        collection: 'domain-requests',
        id: requestId,
        data: {
          status: 'active',
          activatedAt: new Date().toISOString(),
          sslStatus,
          notes
        }
      })

      return res.json({
        success: true,
        data: updatedRequest,
        message: 'Domain activated successfully'
      })

    } catch (error) {
      console.error('Activate domain error:', error)
      return res.status(500).json({
        success: false,
        error: 'Failed to activate domain'
      })
    }
  }]
}

// Get domain request details
export const getDomainRequestDetailsEndpoint: Endpoint = {
  path: '/super-admin/domain-requests/:id',
  method: 'get',
  handler: [requireAuth, requireSuperAdmin, async (req, res) => {
    try {
      const requestId = req.params.id

      const domainRequest = await req.payload.findByID({
        collection: 'domain-requests',
        id: requestId,
        depth: 3
      })

      return res.json({
        success: true,
        data: domainRequest
      })

    } catch (error) {
      console.error('Get domain request details error:', error)
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch domain request details'
      })
    }
  }]
}
