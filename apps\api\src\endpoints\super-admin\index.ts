// Super Admin API Endpoints
// These endpoints are specifically for super administrators

// Domain request management endpoints
export {
  getDomainRequestsEndpoint,
  approveDomainRequestEndpoint,
  rejectDomainRequestEndpoint,
  activateDomainEndpoint,
  getDomainRequestDetailsEndpoint
} from './domain-requests'

// Platform blog management endpoints
export {
  getPlatformBlogPostsEndpoint,
  getPlatformBlogPostEndpoint,
  createPlatformBlogPostEndpoint,
  updatePlatformBlogPostEndpoint,
  deletePlatformBlogPostEndpoint,
  publishPlatformBlogPostEndpoint,
  schedulePlatformBlogPostEndpoint,
  getPlatformBlogCategoriesEndpoint,
  createPlatformBlogCategoryEndpoint,
  updatePlatformBlogCategoryEndpoint,
  deletePlatformBlogCategoryEndpoint,
  getPlatformBlogAnalyticsEndpoint
} from './platform-blogs'

// User management endpoints
export {
  getCurrentUserEndpoint as superAdminGetCurrentUserEndpoint,
  updateCurrentUserEndpoint as superAdminUpdateCurrentUserEndpoint,
  getAll<PERSON>sersEndpoint as superAdminGetAllUsersEndpoint,
  createUserEndpoint as superAdminCreateUserEndpoint
} from './users'

// Avatar upload endpoints
export {
  uploadAvatarEndpoint as superAdminUploadAvatarEndpoint,
  getCurrentAvatarEndpoint as superAdminGetCurrentAvatarEndpoint,
  removeAvatarEndpoint as superAdminRemoveAvatarEndpoint
} from './avatar-upload'
