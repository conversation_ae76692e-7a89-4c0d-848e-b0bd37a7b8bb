import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'

// Shared data transformation function for consistent data structure
function transformGatewayData(gatewayData: any, isUpdate = false) {
  const transformedData: any = {}

  // Required fields for create, optional for update
  if (gatewayData.name || !isUpdate) {
    transformedData.name = gatewayData.name?.trim() || ''
  }

  if (gatewayData.description !== undefined) {
    transformedData.description = gatewayData.description?.trim() ||
      (gatewayData.name ? `${gatewayData.name.trim()} payment gateway` : '')
  }

  // Transform arrays to simple string arrays (consistent format)
  if (gatewayData.supportedCurrencies) {
    transformedData.supportedCurrencies = gatewayData.supportedCurrencies.map((curr: any) =>
      typeof curr === 'string' ? curr.toUpperCase() : curr.currency?.toUpperCase()
    ).filter(Boolean)
  } else if (!isUpdate) {
    transformedData.supportedCurrencies = ['INR'] // Default for create
  }

  if (gatewayData.supportedMethods) {
    transformedData.supportedMethods = gatewayData.supportedMethods.map((method: any) =>
      typeof method === 'string' ? method : method.method
    ).filter(Boolean)
  } else if (!isUpdate) {
    transformedData.supportedMethods = [] // Default for create
  }

  if (gatewayData.supportedCountries) {
    transformedData.supportedCountries = gatewayData.supportedCountries.map((country: any) =>
      typeof country === 'string' ? country.toUpperCase() : country.country?.toUpperCase()
    ).filter(Boolean)
  } else if (!isUpdate) {
    transformedData.supportedCountries = ['IN'] // Default for create
  }

  // Optional fields
  if (gatewayData.documentationUrl !== undefined) {
    transformedData.documentationUrl = gatewayData.documentationUrl?.trim() || ''
  }

  if (gatewayData.apiVersion !== undefined) {
    transformedData.apiVersion = gatewayData.apiVersion?.trim() || '1.0'
  } else if (!isUpdate) {
    transformedData.apiVersion = '1.0'
  }

  if (gatewayData.webhookSupport !== undefined) {
    transformedData.webhookSupport = gatewayData.webhookSupport
  } else if (!isUpdate) {
    transformedData.webhookSupport = true
  }

  if (gatewayData.logoUrl !== undefined) {
    transformedData.logoUrl = gatewayData.logoUrl?.trim() || ''
  }

  if (gatewayData.isActive !== undefined) {
    transformedData.isActive = gatewayData.isActive
  } else if (!isUpdate) {
    transformedData.isActive = true
  }

  if (gatewayData.isFeatured !== undefined) {
    transformedData.isFeatured = gatewayData.isFeatured
  } else if (!isUpdate) {
    transformedData.isFeatured = false
  }

  return transformedData
}

// Helper function for authenticated super admin endpoints
const createSuperAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['super_admin', 'platform_staff'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      // Debug logging
      console.log('Payment gateway endpoint user info:', {
        userId: user.id,
        legacyRole: user.legacyRole
      })

      // Verify super admin access
      if (user.legacyRole !== 'super_admin' && user.legacyRole !== 'platform_staff') {
        console.error('Insufficient permissions:', user.legacyRole)
        return Response.json({
          success: false,
          error: 'Super admin access required'
        }, { status: 403 })
      }

      return handler(req)
    }
  }
}

// Get all payment gateways (Super Admin)
export const getPaymentGatewaysEndpoint = createSuperAdminEndpoint(
  '/super-admin/payment-gateways',
  'get',
  async (req) => {
    try {
      const gateways = await req.payload.find({
        collection: 'payment-gateways',
        sort: 'displayOrder',
        limit: 100
      })

      return Response.json({
        success: true,
        gateways: gateways.docs,
        total: gateways.totalDocs
      })
    } catch (error) {
      console.error('Gateway fetch error:', error)
      return Response.json({ error: 'Internal server error' }, { status: 500 })
    }
  }
)

// Create payment gateway (Super Admin)
export const createPaymentGatewayEndpoint = createSuperAdminEndpoint(
  '/super-admin/payment-gateways',
  'post',
  async (req) => {
    try {
      const gatewayData = await req.json()

      // Basic validation
      if (!gatewayData.name?.trim()) {
        return Response.json({
          error: 'Gateway name is required',
          details: 'Please provide a valid gateway name'
        }, { status: 400 })
      }

      if (!gatewayData.supportedCurrencies || !Array.isArray(gatewayData.supportedCurrencies) || gatewayData.supportedCurrencies.length === 0) {
        return Response.json({
          error: 'Supported currencies are required',
          details: 'Please provide at least one supported currency'
        }, { status: 400 })
      }

      // Transform data using shared function for consistency
      const transformedData = transformGatewayData(gatewayData, false)

      // Validate transformed data structure
      console.log('Final transformed data for CREATE:', JSON.stringify(transformedData, null, 2))
      console.log('Data types:', {
        supportedCurrencies: Array.isArray(transformedData.supportedCurrencies) ? 'array' : typeof transformedData.supportedCurrencies,
        supportedMethods: Array.isArray(transformedData.supportedMethods) ? 'array' : typeof transformedData.supportedMethods,
        supportedCountries: Array.isArray(transformedData.supportedCountries) ? 'array' : typeof transformedData.supportedCountries
      })

      const gateway = await req.payload.create({
        collection: 'payment-gateways',
        data: transformedData
      })

      return Response.json({
        success: true,
        gateway,
        message: 'Payment gateway created successfully'
      })
    } catch (error: any) {
      console.error('Gateway creation error:', error)
      return Response.json({
        error: 'Failed to create payment gateway',
        details: error.message,
        validationErrors: error.data
      }, { status: 500 })
    }
  }
)

// Update payment gateway (Super Admin)
export const updatePaymentGatewayEndpoint = createSuperAdminEndpoint(
  '/super-admin/payment-gateways/:id',
  'patch',
  async (req) => {
    try {
      // Extract ID from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const id = pathParts[pathParts.length - 1]

      const gatewayData = await req.json()

      // Log the incoming data for debugging
      console.log('Updating gateway with data:', JSON.stringify(gatewayData, null, 2))

      // Transform data using shared function for consistency (same as create)
      const transformedData = transformGatewayData(gatewayData, true)

      // Validate transformed data structure
      console.log('Final transformed data for UPDATE:', JSON.stringify(transformedData, null, 2))
      console.log('Data types:', {
        supportedCurrencies: transformedData.supportedCurrencies ? (Array.isArray(transformedData.supportedCurrencies) ? 'array' : typeof transformedData.supportedCurrencies) : 'undefined',
        supportedMethods: transformedData.supportedMethods ? (Array.isArray(transformedData.supportedMethods) ? 'array' : typeof transformedData.supportedMethods) : 'undefined',
        supportedCountries: transformedData.supportedCountries ? (Array.isArray(transformedData.supportedCountries) ? 'array' : typeof transformedData.supportedCountries) : 'undefined'
      })

      const gateway = await req.payload.update({
        collection: 'payment-gateways',
        id,
        data: transformedData
      })

      return Response.json({
        success: true,
        gateway,
        message: 'Payment gateway updated successfully'
      })
    } catch (error: any) {
      console.error('Gateway update error:', error)
      return Response.json({
        error: 'Failed to update payment gateway',
        details: error.message,
        validationErrors: error.data
      }, { status: 500 })
    }
  }
)

// Delete payment gateway (Super Admin)
export const deletePaymentGatewayEndpoint = createSuperAdminEndpoint(
  '/super-admin/payment-gateways/:id',
  'delete',
  async (req) => {
    try {
      // Extract ID from URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const id = pathParts[pathParts.length - 1]

      // Check if gateway is being used by any institute
      const configs = await req.payload.find({
        collection: 'institute-gateways',
        where: {
          gateway: {
            equals: id
          }
        },
        limit: 1
      })

      if (configs.totalDocs > 0) {
        return Response.json({
          error: 'Cannot delete gateway that is being used by institutes'
        }, { status: 400 })
      }

      await req.payload.delete({
        collection: 'payment-gateways',
        id
      })

      return Response.json({
        success: true,
        message: 'Payment gateway deleted successfully'
      })
    } catch (error) {
      console.error('Gateway deletion error:', error)
      return Response.json({ error: 'Failed to delete payment gateway' }, { status: 500 })
    }
  }
)

// Export all endpoints
const paymentGatewayEndpoints = [
  getPaymentGatewaysEndpoint,
  createPaymentGatewayEndpoint,
  updatePaymentGatewayEndpoint,
  deletePaymentGatewayEndpoint
]

export default paymentGatewayEndpoints
