import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'

// Helper function for authenticated super admin endpoints
const createSuperAdminEndpoint = (
  path: string,
  method: 'get' | 'post' | 'patch' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = requireAuth(['super_admin'])
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult
      }

      // Get user information
      const user = req.user
      if (!user) {
        return Response.json({
          success: false,
          error: 'User not found'
        }, { status: 401 })
      }

      return handler(req)
    }
  }
}

// Utility function to parse URL parameters
const parseUrlParams = (req: any) => {
  const url = new URL(req.url)
  const searchParams = url.searchParams
  
  return {
    page: parseInt(searchParams.get('page') || '1'),
    limit: parseInt(searchParams.get('limit') || '20'),
    search: searchParams.get('search') || '',
    status: searchParams.get('status') || '',
    category: searchParams.get('category') || '',
    targetAudience: searchParams.get('targetAudience') || '',
    isAnnouncement: searchParams.get('isAnnouncement') || '',
    sortBy: searchParams.get('sortBy') || 'createdAt',
    sortOrder: searchParams.get('sortOrder') || 'desc',
  }
}

// GET /api/super-admin/platform-blogs/posts - Get all platform blog posts
export const getPlatformBlogPostsEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/posts',
  'get',
  async (req: any) => {
    try {
      const { page, limit, search, status, category, targetAudience, isAnnouncement, sortBy, sortOrder } = parseUrlParams(req)

      // Build search query
      const whereClause: any = {}

      if (search) {
        whereClause.or = [
          { title: { contains: search } },
          { excerpt: { contains: search } },
          { content: { contains: search } }
        ]
      }

      if (status) {
        whereClause.status = { equals: status }
      }

      if (category) {
        whereClause.category = { equals: category }
      }

      if (targetAudience) {
        whereClause.targetAudience = { contains: targetAudience }
      }

      if (isAnnouncement === 'true') {
        whereClause.isAnnouncement = { equals: true }
      }

      // Fetch posts
      const posts = await req.payload.find({
        collection: 'platform-blog-posts',
        where: whereClause,
        depth: 2,
        limit,
        page,
        sort: `${sortOrder === 'desc' ? '-' : ''}${sortBy}`
      })

      return Response.json({
        success: true,
        posts: posts.docs,
        pagination: {
          page: posts.page,
          limit: posts.limit,
          totalDocs: posts.totalDocs,
          totalPages: posts.totalPages,
          hasNextPage: posts.hasNextPage,
          hasPrevPage: posts.hasPrevPage
        }
      })

    } catch (error: any) {
      console.error('Get platform blog posts error:', error)
      
      return Response.json({
        success: false,
        error: 'Failed to fetch platform blog posts',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// GET /api/super-admin/platform-blogs/posts/:id - Get single platform blog post
export const getPlatformBlogPostEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/posts/:id',
  'get',
  async (req: any) => {
    try {
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const postId = pathParts[pathParts.length - 1]

      const post = await req.payload.findByID({
        collection: 'platform-blog-posts',
        id: postId,
        depth: 2
      })

      return Response.json({
        success: true,
        post
      })

    } catch (error: any) {
      console.error('Get platform blog post error:', error)
      
      return Response.json({
        success: false,
        error: 'Failed to fetch platform blog post',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// POST /api/super-admin/platform-blogs/posts - Create platform blog post
export const createPlatformBlogPostEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/posts',
  'post',
  async (req: any) => {
    try {
      const body = await req.json()
      
      // Set author to current user
      body.author = req.user.id

      const post = await req.payload.create({
        collection: 'platform-blog-posts',
        data: body
      })

      return Response.json({
        success: true,
        message: 'Platform blog post created successfully',
        post
      })

    } catch (error: any) {
      console.error('Create platform blog post error:', error)
      
      return Response.json({
        success: false,
        error: 'Failed to create platform blog post',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// PUT /api/super-admin/platform-blogs/posts/:id - Update platform blog post
export const updatePlatformBlogPostEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/posts/:id',
  'put',
  async (req: any) => {
    try {
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const postId = pathParts[pathParts.length - 1]
      
      const body = await req.json()
      
      // Set lastEditedBy to current user
      body.lastEditedBy = req.user.id

      const post = await req.payload.update({
        collection: 'platform-blog-posts',
        id: postId,
        data: body
      })

      return Response.json({
        success: true,
        message: 'Platform blog post updated successfully',
        post
      })

    } catch (error: any) {
      console.error('Update platform blog post error:', error)
      
      return Response.json({
        success: false,
        error: 'Failed to update platform blog post',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// DELETE /api/super-admin/platform-blogs/posts/:id - Delete platform blog post
export const deletePlatformBlogPostEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/posts/:id',
  'delete',
  async (req: any) => {
    try {
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const postId = pathParts[pathParts.length - 1]

      await req.payload.delete({
        collection: 'platform-blog-posts',
        id: postId
      })

      return Response.json({
        success: true,
        message: 'Platform blog post deleted successfully'
      })

    } catch (error: any) {
      console.error('Delete platform blog post error:', error)
      
      return Response.json({
        success: false,
        error: 'Failed to delete platform blog post',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// POST /api/super-admin/platform-blogs/posts/:id/publish - Publish platform blog post
export const publishPlatformBlogPostEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/posts/:id/publish',
  'post',
  async (req: any) => {
    try {
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const postId = pathParts[pathParts.indexOf('posts') + 1]

      const post = await req.payload.update({
        collection: 'platform-blog-posts',
        id: postId,
        data: {
          status: 'published',
          publishedAt: new Date(),
          lastEditedBy: req.user.id
        }
      })

      return Response.json({
        success: true,
        message: 'Platform blog post published successfully',
        post
      })

    } catch (error: any) {
      console.error('Publish platform blog post error:', error)
      
      return Response.json({
        success: false,
        error: 'Failed to publish platform blog post',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// POST /api/super-admin/platform-blogs/posts/:id/schedule - Schedule platform blog post
export const schedulePlatformBlogPostEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/posts/:id/schedule',
  'post',
  async (req: any) => {
    try {
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const postId = pathParts[pathParts.indexOf('posts') + 1]
      
      const body = await req.json()
      const { scheduledFor } = body

      if (!scheduledFor) {
        return Response.json({
          success: false,
          error: 'Scheduled date is required'
        }, { status: 400 })
      }

      const post = await req.payload.update({
        collection: 'platform-blog-posts',
        id: postId,
        data: {
          status: 'scheduled',
          scheduledFor: new Date(scheduledFor),
          lastEditedBy: req.user.id
        }
      })

      return Response.json({
        success: true,
        message: 'Platform blog post scheduled successfully',
        post
      })

    } catch (error: any) {
      console.error('Schedule platform blog post error:', error)
      
      return Response.json({
        success: false,
        error: 'Failed to schedule platform blog post',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// GET /api/super-admin/platform-blogs/categories - Get all platform blog categories
export const getPlatformBlogCategoriesEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/categories',
  'get',
  async (req: any) => {
    try {
      const categories = await req.payload.find({
        collection: 'platform-blog-categories',
        limit: 100,
        sort: 'name'
      })

      return Response.json({
        success: true,
        categories: categories.docs
      })

    } catch (error: any) {
      console.error('Get platform blog categories error:', error)

      return Response.json({
        success: false,
        error: 'Failed to fetch platform blog categories',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// POST /api/super-admin/platform-blogs/categories - Create platform blog category
export const createPlatformBlogCategoryEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/categories',
  'post',
  async (req: any) => {
    try {
      const body = await req.json()

      const category = await req.payload.create({
        collection: 'platform-blog-categories',
        data: body
      })

      return Response.json({
        success: true,
        message: 'Platform blog category created successfully',
        category
      })

    } catch (error: any) {
      console.error('Create platform blog category error:', error)

      return Response.json({
        success: false,
        error: 'Failed to create platform blog category',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// PUT /api/super-admin/platform-blogs/categories/:id - Update platform blog category
export const updatePlatformBlogCategoryEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/categories/:id',
  'put',
  async (req: any) => {
    try {
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const categoryId = pathParts[pathParts.length - 1]

      const body = await req.json()

      const category = await req.payload.update({
        collection: 'platform-blog-categories',
        id: categoryId,
        data: body
      })

      return Response.json({
        success: true,
        message: 'Platform blog category updated successfully',
        category
      })

    } catch (error: any) {
      console.error('Update platform blog category error:', error)

      return Response.json({
        success: false,
        error: 'Failed to update platform blog category',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// DELETE /api/super-admin/platform-blogs/categories/:id - Delete platform blog category
export const deletePlatformBlogCategoryEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/categories/:id',
  'delete',
  async (req: any) => {
    try {
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const categoryId = pathParts[pathParts.length - 1]

      await req.payload.delete({
        collection: 'platform-blog-categories',
        id: categoryId
      })

      return Response.json({
        success: true,
        message: 'Platform blog category deleted successfully'
      })

    } catch (error: any) {
      console.error('Delete platform blog category error:', error)

      return Response.json({
        success: false,
        error: 'Failed to delete platform blog category',
        details: error?.message
      }, { status: 500 })
    }
  }
)

// GET /api/super-admin/platform-blogs/analytics - Get platform blog analytics
export const getPlatformBlogAnalyticsEndpoint = createSuperAdminEndpoint(
  '/super-admin/platform-blogs/analytics',
  'get',
  async (req: any) => {
    try {
      const url = new URL(req.url)
      const period = url.searchParams.get('period') || '30d'

      // Calculate date range based on period
      const now = new Date()
      let startDate = new Date()

      switch (period) {
        case '7d':
          startDate.setDate(now.getDate() - 7)
          break
        case '30d':
          startDate.setDate(now.getDate() - 30)
          break
        case '90d':
          startDate.setDate(now.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(now.getFullYear() - 1)
          break
        default:
          startDate.setDate(now.getDate() - 30)
      }

      // Get all published posts
      const posts = await req.payload.find({
        collection: 'platform-blog-posts',
        where: {
          status: { equals: 'published' },
          publishedAt: { greater_than: startDate }
        },
        limit: 1000
      })

      // Calculate analytics
      const analytics = posts.docs.reduce((acc, post) => {
        const postAnalytics = post.analytics || {}
        return {
          totalViews: acc.totalViews + (postAnalytics.viewCount || 0),
          totalUniqueViews: acc.totalUniqueViews + (postAnalytics.uniqueViewCount || 0),
          totalLikes: acc.totalLikes + (postAnalytics.likeCount || 0),
          totalShares: acc.totalShares + (postAnalytics.shareCount || 0),
          instituteAdminViews: acc.instituteAdminViews + (postAnalytics.instituteAdminViews || 0),
          studentViews: acc.studentViews + (postAnalytics.studentViews || 0),
          staffViews: acc.staffViews + (postAnalytics.staffViews || 0),
          publicViews: acc.publicViews + (postAnalytics.publicViews || 0),
        }
      }, {
        totalViews: 0,
        totalUniqueViews: 0,
        totalLikes: 0,
        totalShares: 0,
        instituteAdminViews: 0,
        studentViews: 0,
        staffViews: 0,
        publicViews: 0,
      })

      // Get post counts by status
      const statusCounts = await Promise.all([
        req.payload.count({ collection: 'platform-blog-posts', where: { status: { equals: 'draft' } } }),
        req.payload.count({ collection: 'platform-blog-posts', where: { status: { equals: 'published' } } }),
        req.payload.count({ collection: 'platform-blog-posts', where: { status: { equals: 'scheduled' } } }),
        req.payload.count({ collection: 'platform-blog-posts', where: { isAnnouncement: { equals: true } } }),
      ])

      return Response.json({
        success: true,
        analytics: {
          ...analytics,
          totalPosts: posts.totalDocs,
          draftPosts: statusCounts[0].totalDocs,
          publishedPosts: statusCounts[1].totalDocs,
          scheduledPosts: statusCounts[2].totalDocs,
          announcementPosts: statusCounts[3].totalDocs,
          period
        }
      })

    } catch (error: any) {
      console.error('Get platform blog analytics error:', error)

      return Response.json({
        success: false,
        error: 'Failed to fetch platform blog analytics',
        details: error?.message
      }, { status: 500 })
    }
  }
)
