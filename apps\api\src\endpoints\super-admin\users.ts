import type { Endpoint } from 'payload'
import { requireAuth, requireSuperAdmin } from '../../middleware/auth'
import bcrypt from 'bcrypt'

console.log('🔥 super-admin/users.ts file loaded - user endpoints are being registered!')

// Get current user profile (Super Admin)
export const getCurrentUserEndpoint: Endpoint = {
  path: '/super-admin/user/me',
  method: 'get',
  handler: async (req) => {
    console.log('🚀🚀🚀 SUPER ADMIN /api/super-admin/user/me ENDPOINT CALLED! 🚀🚀🚀')
    console.log('🚀 === Super Admin Get Current User ===')
    console.log('📝 Request URL:', req.url)
    console.log('📝 Request method:', req.method)
    console.log('📝 Request headers:', {
      authorization: req.headers.get('authorization') ? 'Bearer token present' : 'No token',
      contentType: req.headers.get('content-type'),
      userAgent: req.headers.get('user-agent')
    })
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }
    
    console.log('✅ Auth check passed, user:', {
      id: req.user?.id,
      email: req.user?.email,
      legacyRole: req.user?.legacyRole
    })

    try {
      console.log('🔍 Attempting to find user by ID:', req.user!.id)
      
      const user = await req.payload.findByID({
        collection: 'users',
        id: req.user!.id,
      })

      console.log('📊 Database lookup result:', {
        found: !!user,
        userId: user?.id,
        userEmail: user?.email,
        hasPassword: !!user?.password
      })

      if (!user) {
        console.log('❌ User not found in database')
        return Response.json(
          { message: 'User not found' },
          { status: 404 }
        )
      }

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user

      console.log('✅ Returning user data successfully')
      return Response.json({
        success: true,
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('💥 Get current user error:', error)
      return Response.json(
        { success: false, message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Update current user profile (Super Admin)
export const updateCurrentUserEndpoint: Endpoint = {
  path: '/super-admin/user/me',
  method: 'put',
  handler: async (req) => {
    console.log('🚀🚀🚀 SUPER ADMIN UPDATE USER ENDPOINT CALLED! 🚀🚀🚀')
    console.log('🚀 === Super Admin Update Current User ===')
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }

    const body = req.json ? await req.json() : req.body
    const { firstName, lastName, phone, email, password, avatar } = body

    console.log('📝 Update data received:', {
      firstName: !!firstName,
      lastName: !!lastName,
      phone: !!phone,
      email: !!email,
      password: !!password,
      avatar: !!avatar
    })

    try {
      const updateData: any = {
        firstName,
        lastName,
        phone,
      }

      // Handle avatar update if provided
      if (avatar) {
        if (typeof avatar === 'string' && avatar.startsWith('http')) {
          // If avatar is a URL, we need to find the corresponding media ID
          console.log('🖼️ Avatar URL provided, need to find media ID:', avatar)

          // Extract filename from URL to find the media record
          const urlParts = avatar.split('/')
          const filename = urlParts[urlParts.length - 1]

          try {
            // Search for media record with this filename
            const mediaRecords = await req.payload.find({
              collection: 'media',
              where: {
                filename: {
                  equals: filename
                }
              },
              limit: 1
            })

            if (mediaRecords.docs.length > 0) {
              updateData.avatar = mediaRecords.docs[0].id
              console.log('✅ Found media ID for avatar:', mediaRecords.docs[0].id)
            } else {
              console.log('⚠️ No media record found for filename:', filename)
              // Don't update avatar if we can't find the media record
            }
          } catch (mediaError) {
            console.error('❌ Error finding media record:', mediaError)
            // Don't update avatar if there's an error
          }
        } else if (typeof avatar === 'number' || typeof avatar === 'string') {
          // If avatar is already an ID, use it directly
          updateData.avatar = avatar
          console.log('🖼️ Avatar ID provided:', avatar)
        }
      }

      // Handle email update if provided
      if (email && req.user && email !== req.user.email) {
        updateData.email = email
        console.log('📧 Email will be updated')
      }

      // Handle password update if provided
      if (password && password.trim() !== '') {
        updateData.password = password
        console.log('🔒 Password will be updated')
      }

      console.log('🔄 Updating user with ID:', req.user!.id)

      const updatedUser = await req.payload.update({
        collection: 'users',
        id: req.user!.id,
        data: updateData,
      })

      // Remove password from response
      const { password: _, ...userWithoutPassword } = updatedUser

      console.log('✅ User updated successfully')
      return Response.json({
        success: true,
        message: 'Profile updated successfully',
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('💥 Update user error:', error)
      return Response.json(
        { success: false, message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Get all users (Super Admin only)
export const getAllUsersEndpoint: Endpoint = {
  path: '/api/super-admin/users',
  method: 'get',
  handler: async (req) => {
    console.log('🚀🚀🚀 SUPER ADMIN GET ALL USERS ENDPOINT CALLED! 🚀🚀🚀')
    
    const authCheck = await requireSuperAdmin(req)
    if (authCheck) {
      console.log('❌ Super admin auth check failed')
      return authCheck
    }

    const url = new URL(req.url || 'http://localhost')
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const role = url.searchParams.get('role')
    const search = url.searchParams.get('search')

    console.log('📊 Query parameters:', { page, limit, role, search })

    try {
      const where: any = {}

      if (role) {
        where.role = { equals: role }
      }

      if (search) {
        where.or = [
          { firstName: { contains: search } },
          { lastName: { contains: search } },
          { email: { contains: search } },
        ]
      }

      const users = await req.payload.find({
        collection: 'users',
        where,
        page,
        limit,
        sort: '-createdAt',
      })

      // Remove passwords from response
      const usersWithoutPasswords = users.docs.map((user: any) => {
        const { password: _, ...userWithoutPassword } = user
        return userWithoutPassword
      })

      console.log('✅ Retrieved users successfully:', {
        count: usersWithoutPasswords.length,
        totalDocs: users.totalDocs
      })

      return Response.json({
        success: true,
        users: usersWithoutPasswords,
        totalDocs: users.totalDocs,
        totalPages: users.totalPages,
        page: users.page,
        limit: users.limit,
        hasNextPage: users.hasNextPage,
        hasPrevPage: users.hasPrevPage,
      })

    } catch (error) {
      console.error('💥 Get all users error:', error)
      return Response.json(
        { success: false, message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Create new user (Super Admin only)
export const createUserEndpoint: Endpoint = {
  path: '/api/super-admin/users',
  method: 'post',
  handler: async (req) => {
    console.log('🚀🚀🚀 SUPER ADMIN CREATE USER ENDPOINT CALLED! 🚀🚀🚀')
    
    const authCheck = await requireSuperAdmin(req)
    if (authCheck) {
      console.log('❌ Super admin auth check failed')
      return authCheck
    }

    const body = req.json ? await req.json() : req.body
    const { email, password, firstName, lastName, phone, role, institute, branch } = body

    console.log('📝 Create user data received:', {
      email: !!email,
      password: !!password,
      firstName: !!firstName,
      lastName: !!lastName,
      phone: !!phone,
      role,
      institute: !!institute,
      branch: !!branch
    })

    if (!email || !password || !firstName || !lastName || !role) {
      console.log('❌ Required fields missing')
      return Response.json(
        { success: false, message: 'Required fields are missing' },
        { status: 400 }
      )
    }

    try {
      // Check if email already exists
      const existingUsers = await req.payload.find({
        collection: 'users',
        where: {
          email: { equals: email.toLowerCase() },
        },
      })

      if (existingUsers.docs.length > 0) {
        console.log('❌ Email already exists')
        return Response.json(
          { success: false, message: 'Email already exists' },
          { status: 400 }
        )
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10)

      const userData: any = {
        email: email.toLowerCase(),
        password: hashedPassword,
        firstName,
        lastName,
        phone,
        role,
        isActive: true,
        emailVerified: false,
      }

      if (institute) {
        userData.institute = institute
      }

      if (branch) {
        userData.branch = branch
      }

      console.log('🔄 Creating new user...')

      const newUser = await req.payload.create({
        collection: 'users',
        data: userData,
      })

      // Remove password from response
      const { password: _, ...userWithoutPassword } = newUser

      console.log('✅ User created successfully:', {
        id: newUser.id,
        email: newUser.email
      })

      return Response.json({
        success: true,
        message: 'User created successfully',
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('💥 Create user error:', error)
      return Response.json(
        { success: false, message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}
