import { Endpoint } from 'payload/config'
import { TaxCalculationService } from '../services/taxCalculationService'
import { authenticateToken } from '../middleware/auth'

// Get tax components with filtering
export const getTaxComponentsEndpoint: Endpoint = {
  path: '/tax/components',
  method: 'get',
  handler: async (req) => {
    try {
      const { searchParams } = new URL(req.url!)
      const search = searchParams.get('search') || ''
      const type = searchParams.get('type')
      const isActive = searchParams.get('isActive') || 'true'
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')

      const where: any = {}

      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      if (type) {
        where.type = { equals: type }
      }

      if (search) {
        where.or = [
          { name: { contains: search } },
          { code: { contains: search } },
          { description: { contains: search } }
        ]
      }

      const components = await req.payload.find({
        collection: 'tax-components',
        where,
        page,
        limit,
        sort: '-createdAt',
        populate: ['applicableRegions.country', 'applicableRegions.states']
      })

      return Response.json({
        success: true,
        components: components.docs,
        pagination: {
          page: components.page,
          limit: components.limit,
          totalPages: components.totalPages,
          totalDocs: components.totalDocs,
          hasNextPage: components.hasNextPage,
          hasPrevPage: components.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Tax components fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Get tax groups with filtering
export const getTaxGroupsEndpoint: Endpoint = {
  path: '/tax/groups',
  method: 'get',
  handler: async (req) => {
    try {
      const { searchParams } = new URL(req.url!)
      const search = searchParams.get('search') || ''
      const isActive = searchParams.get('isActive') || 'true'
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')

      const where: any = {}

      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      if (search) {
        where.or = [
          { name: { contains: search } },
          { code: { contains: search } },
          { description: { contains: search } }
        ]
      }

      const groups = await req.payload.find({
        collection: 'tax-groups',
        where,
        page,
        limit,
        sort: '-createdAt',
        populate: ['taxComponents.component']
      })

      return Response.json({
        success: true,
        groups: groups.docs,
        pagination: {
          page: groups.page,
          limit: groups.limit,
          totalPages: groups.totalPages,
          totalDocs: groups.totalDocs,
          hasNextPage: groups.hasNextPage,
          hasPrevPage: groups.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Tax groups fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Get tax rules with filtering
export const getTaxRulesEndpoint: Endpoint = {
  path: '/tax/rules',
  method: 'get',
  handler: async (req) => {
    try {
      const { searchParams } = new URL(req.url!)
      const search = searchParams.get('search') || ''
      const isActive = searchParams.get('isActive') || 'true'
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')

      const where: any = {}

      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      if (search) {
        where.or = [
          { name: { contains: search } },
          { description: { contains: search } }
        ]
      }

      const rules = await req.payload.find({
        collection: 'tax-rules',
        where,
        page,
        limit,
        sort: '-priority',
        populate: ['taxGroup']
      })

      return Response.json({
        success: true,
        rules: rules.docs,
        pagination: {
          page: rules.page,
          limit: rules.limit,
          totalPages: rules.totalPages,
          totalDocs: rules.totalDocs,
          hasNextPage: rules.hasNextPage,
          hasPrevPage: rules.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Tax rules fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Calculate tax for a transaction
export const calculateTaxEndpoint: Endpoint = {
  path: '/tax/calculate',
  method: 'post',
  handler: async (req) => {
    try {
      const body = await req.json()
      const {
        amount,
        transactionType,
        customerType,
        customerLocation,
        instituteLocation,
        branchId,
        transactionDate
      } = body

      if (!amount || !customerLocation || !instituteLocation) {
        return Response.json(
          { success: false, error: 'Missing required fields' },
          { status: 400 }
        )
      }

      const taxService = new TaxCalculationService(req.payload)
      const result = await taxService.calculateTax({
        amount,
        transactionType: transactionType || 'course_purchase',
        customerType: customerType || 'individual',
        customerLocation,
        instituteLocation,
        branchId,
        transactionDate: transactionDate ? new Date(transactionDate) : new Date()
      })

      return Response.json({
        success: true,
        calculation: result
      })

    } catch (error) {
      console.error('Tax calculation error:', error)
      return Response.json(
        { success: false, error: 'Tax calculation failed' },
        { status: 500 }
      )
    }
  }
}

// Preview tax calculation for different scenarios
export const previewTaxCalculationEndpoint: Endpoint = {
  path: '/tax/preview',
  method: 'post',
  handler: async (req) => {
    try {
      const body = await req.json()
      const {
        amount,
        transactionType,
        customerType,
        customerLocation,
        instituteLocation,
        branchId
      } = body

      if (!amount || !customerLocation || !instituteLocation) {
        return Response.json(
          { success: false, error: 'Missing required fields' },
          { status: 400 }
        )
      }

      const taxService = new TaxCalculationService(req.payload)
      const result = await taxService.previewTaxCalculation({
        amount,
        transactionType: transactionType || 'course_purchase',
        customerType: customerType || 'individual',
        customerLocation,
        instituteLocation,
        branchId,
        transactionDate: new Date()
      })

      return Response.json({
        success: true,
        preview: result
      })

    } catch (error) {
      console.error('Tax preview error:', error)
      return Response.json(
        { success: false, error: 'Tax preview failed' },
        { status: 500 }
      )
    }
  }
}

// Get branches with tax information
export const getBranchesEndpoint: Endpoint = {
  path: '/tax/branches',
  method: 'get',
  handler: async (req) => {
    const { user } = req
    
    if (!user) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const { searchParams } = new URL(req.url!)
      const instituteId = searchParams.get('instituteId')

      let where: any = { isActive: { equals: true } }

      if (user.role === 'super_admin') {
        if (instituteId) {
          where.institute = { equals: instituteId }
        }
      } else if (user.role === 'institute_admin') {
        where.institute = { equals: user.institute }
      } else {
        return Response.json({ message: 'Forbidden' }, { status: 403 })
      }

      const branches = await req.payload.find({
        collection: 'branches',
        where,
        limit: 100,
        sort: 'name',
        populate: ['institute', 'location.country', 'location.state', 'location.district']
      })

      return Response.json({
        success: true,
        branches: branches.docs
      })

    } catch (error) {
      console.error('Branches fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Create tax component
export const createTaxComponentEndpoint: Endpoint = {
  path: '/tax/components',
  method: 'post',
  handler: async (req: any) => {
    // Authenticate the user
    const user = await authenticateToken(req)

    console.log('Create tax component - User:', user)
    console.log('Create tax component - User role:', user?.role)
    console.log('Create tax component - Headers:', req.headers)

    if (!user) {
      console.log('No user found in request')
      return Response.json({ message: 'No user authenticated' }, { status: 401 })
    }

    if (user.role !== 'super_admin' && user.role !== 'platform_staff') {
      console.log('User role not authorized:', user.role)
      return Response.json({ message: 'Insufficient permissions' }, { status: 403 })
    }

    try {
      const body = await req.json()

      const component = await req.payload.create({
        collection: 'tax-components',
        data: body
      })

      return Response.json({
        success: true,
        data: component
      })

    } catch (error) {
      console.error('Tax component creation error:', error)
      return Response.json(
        { success: false, error: 'Failed to create tax component' },
        { status: 500 }
      )
    }
  }
}

// Update tax component
export const updateTaxComponentEndpoint: Endpoint = {
  path: '/tax/components/:id',
  method: 'patch',
  handler: async (req: any) => {
    // Authenticate the user
    const user = await authenticateToken(req)

    if (!user || (user.role !== 'super_admin' && user.role !== 'platform_staff')) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const url = new URL(req.url!)
      const id = url.pathname.split('/').pop()
      const body = await req.json()

      const component = await req.payload.update({
        collection: 'tax-components',
        id: id!,
        data: body
      })

      return Response.json({
        success: true,
        data: component
      })

    } catch (error) {
      console.error('Tax component update error:', error)
      return Response.json(
        { success: false, error: 'Failed to update tax component' },
        { status: 500 }
      )
    }
  }
}

// Delete tax component
export const deleteTaxComponentEndpoint: Endpoint = {
  path: '/tax/components/:id',
  method: 'delete',
  handler: async (req: any) => {
    // Authenticate the user
    const user = await authenticateToken(req)

    if (!user || (user.role !== 'super_admin' && user.role !== 'platform_staff')) {
      return Response.json({ message: 'Unauthorized' }, { status: 401 })
    }

    try {
      const url = new URL(req.url!)
      const id = url.pathname.split('/').pop()

      await req.payload.delete({
        collection: 'tax-components',
        id: id!
      })

      return Response.json({
        success: true,
        message: 'Tax component deleted successfully'
      })

    } catch (error) {
      console.error('Tax component deletion error:', error)
      return Response.json(
        { success: false, error: 'Failed to delete tax component' },
        { status: 500 }
      )
    }
  }
}

export const taxManagementEndpoints = [
  getTaxComponentsEndpoint,
  getTaxGroupsEndpoint,
  getTaxRulesEndpoint,
  calculateTaxEndpoint,
  previewTaxCalculationEndpoint,
  getBranchesEndpoint,
  createTaxComponentEndpoint,
  updateTaxComponentEndpoint,
  deleteTaxComponentEndpoint,
]
