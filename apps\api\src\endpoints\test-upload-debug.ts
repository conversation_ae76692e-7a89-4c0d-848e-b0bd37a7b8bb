import type { Endpoint } from 'payload'
import { requireAuth } from '../middleware/auth'
import { createUploadMiddleware, AVATAR_SIZES } from '../middleware/upload-middleware'

console.log('🔥 test-upload-debug.ts file loaded - debug upload endpoint!')

// Debug upload endpoint to test our middleware
export const testUploadDebugEndpoint: Endpoint = {
  path: '/test-upload-debug',
  method: 'post',
  handler: async (req) => {
    console.log('🚀🚀🚀 TEST UPLOAD DEBUG ENDPOINT CALLED! 🚀🚀🚀')
    console.log('📝 Request URL:', req.url)
    console.log('📝 Request method:', req.method)
    
    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }
    
    console.log('✅ Auth check passed, user:', {
      id: req.user?.id,
      email: req.user?.email,
      legacyRole: req.user?.legacyRole
    })

    try {
      // Parse form data
      const formData = await req.formData()
      const file = formData.get('file') as File
      
      console.log('📋 Debug upload parameters:', {
        fileName: file?.name,
        fileSize: file?.size,
        fileType: file?.type
      })

      if (!file) {
        console.log('❌ No file provided')
        return Response.json(
          { success: false, message: 'No file provided' },
          { status: 400 }
        )
      }

      // Create upload middleware instance
      console.log('🔧 Creating upload middleware for debug test...')
      const uploadMiddleware = await createUploadMiddleware(req.payload)

      // Handle file upload with debug settings
      console.log('🔄 Calling upload middleware with debug settings...')
      const uploadResult = await uploadMiddleware.handleFileUpload(req, {
        mediaType: 'user_avatar',
        folder: 'debug-test',
        generateSizes: AVATAR_SIZES,
        maxFileSize: 5 * 1024 * 1024, // 5MB
        allowedMimeTypes: ['image/*']
      }, file)

      console.log('📊 Debug upload result:', {
        success: uploadResult.success,
        hasMedia: !!uploadResult.media,
        mediaUrl: uploadResult.media?.url,
        mediaUrlHasDomain: uploadResult.media?.url?.includes('://'),
        mediaUrlHasApi: uploadResult.media?.url?.includes('/api/'),
        sizesCount: Object.keys(uploadResult.media?.sizes || {}).length
      })

      if (!uploadResult.success) {
        console.log('❌ Debug upload failed:', uploadResult.message)
        return Response.json(
          { success: false, message: uploadResult.message },
          { status: 400 }
        )
      }

      console.log('🎉 Debug upload successful!')
      console.log('🔍 Final URL analysis:', {
        url: uploadResult.media?.url,
        isClean: !uploadResult.media?.url?.includes('://') && !uploadResult.media?.url?.includes('/api/'),
        thumbnailURL: uploadResult.media?.thumbnailURL,
        sizes: uploadResult.media?.sizes
      })

      return Response.json({
        success: true,
        debug: true,
        message: 'Debug upload completed',
        media: uploadResult.media,
        analysis: {
          urlHasDomain: uploadResult.media?.url?.includes('://'),
          urlHasApiPrefix: uploadResult.media?.url?.includes('/api/'),
          isCleanUrl: !uploadResult.media?.url?.includes('://') && !uploadResult.media?.url?.includes('/api/'),
          sizesCount: Object.keys(uploadResult.media?.sizes || {}).length
        }
      })

    } catch (error) {
      console.error('❌ Debug upload error:', error)
      return Response.json(
        { success: false, message: error instanceof Error ? error.message : 'Debug upload failed' },
        { status: 500 }
      )
    }
  }
}
