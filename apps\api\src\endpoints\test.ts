import type { Endpoint } from 'payload'

// Simple test endpoint to verify custom endpoints are working
export const testEndpoint: Endpoint = {
  path: '/test',
  method: 'get',
  handler: async () => {
    return Response.json({
      success: true,
      message: 'Test endpoint is working!',
      timestamp: new Date().toISOString(),
      port: process.env.PORT || '3001'
    })
  }
}

// Test theme endpoint with simpler structure
export const testThemeEndpoint: Endpoint = {
  path: '/test-theme',
  method: 'get',
  handler: async () => {
    return Response.json({
      success: true,
      currentTheme: {
        id: 'test-theme',
        name: 'Test Theme',
        type: 'platform',
        colors: {
          primary: '#3B82F6',
          secondary: '#10B981'
        }
      }
    })
  }
}

// Test with flat path first
export const debugThemeEndpoint: Endpoint = {
  path: '/platform-theme',
  method: 'get',
  handler: async () => {
    return Response.json({
      success: true,
      message: 'Debug theme endpoint working!',
      currentTheme: {
        id: 'debug-platform',
        name: 'Debug SaaS Modern',
        type: 'platform',
        version: '1.0.0',
        isActive: true,
        colors: {
          primary: '#3B82F6',
          secondary: '#10B981',
          accent: '#F59E0B',
          background: '#FFFFFF',
          text: '#1F2937',
          muted: '#6B7280',
          border: '#E5E7EB'
        },
        fonts: {
          heading: 'Inter, sans-serif',
          body: 'Inter, sans-serif',
          mono: 'JetBrains Mono, monospace'
        },
        customizations: {}
      }
    })
  }
}

// Test nested path similar to auth
export const debugThemeNestedEndpoint: Endpoint = {
  path: '/themes/current',
  method: 'get',
  handler: async () => {
    return Response.json({
      success: true,
      message: 'Debug nested theme endpoint working!',
      path: '/themes/current'
    })
  }
}

export const testEndpoints = [testEndpoint, testThemeEndpoint, debugThemeEndpoint, debugThemeNestedEndpoint]
