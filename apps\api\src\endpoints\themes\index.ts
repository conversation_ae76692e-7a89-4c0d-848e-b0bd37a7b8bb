import { Endpoint } from 'payload/config'

const themeEndpoints: Endpoint[] = [
  // Get themes by type
  {
    path: '/themes',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { type, category, featured, active = 'true' } = req.query

        const where: any = {}

        if (active === 'true') {
          where.isActive = { equals: true }
        }

        if (type) {
          where.type = { equals: type }
        }

        if (category) {
          where.category = { equals: category }
        }

        if (featured === 'true') {
          where.isFeatured = { equals: true }
        }

        const themes = await req.payload.find({
          collection: 'themes',
          where,
          sort: '-usageCount',
          depth: 1,
        })

        res.json({
          success: true,
          themes: themes.docs
        })

      } catch (error) {
        console.error('Themes fetch error:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to fetch themes'
        })
      }
    }
  },

  // Get theme by slug
  {
    path: '/themes/:slug',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { slug } = req.params

        const themes = await req.payload.find({
          collection: 'themes',
          where: {
            slug: { equals: slug }
          },
          limit: 1,
          depth: 2,
        })

        if (themes.docs.length === 0) {
          return res.status(404).json({
            success: false,
            error: 'Theme not found'
          })
        }

        const theme = themes.docs[0]

        // Check if theme is active
        if (!theme.isActive) {
          return res.status(403).json({
            success: false,
            error: 'Theme is not available'
          })
        }

        res.json({
          success: true,
          theme
        })

      } catch (error) {
        console.error('Theme fetch error:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to fetch theme'
        })
      }
    }
  },

  // Apply theme to institute
  {
    path: '/themes/:id/apply',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const userId = req.user?.id
        const userRole = req.user?.role
        const instituteId = req.user?.institute

        if (!userId || userRole !== 'institute_admin' || !instituteId) {
          return res.status(401).json({
            success: false,
            error: 'Access denied. Only institute admins can apply themes.'
          })
        }

        // Verify theme exists and is active
        const theme = await req.payload.findByID({
          collection: 'themes',
          id
        })

        if (!theme || !theme.isActive) {
          return res.status(404).json({
            success: false,
            error: 'Theme not found or inactive'
          })
        }

        // Only allow institute themes for institutes
        if (theme.type !== 'institute') {
          return res.status(400).json({
            success: false,
            error: 'Only institute themes can be applied to institutes'
          })
        }

        const { customizations = {} } = req.body

        // Update institute theme
        await req.payload.update({
          collection: 'institutes',
          id: instituteId,
          data: {
            theme: id,
            themeCustomizations: customizations
          }
        })

        // Increment theme usage count
        await req.payload.update({
          collection: 'themes',
          id,
          data: {
            usageCount: (theme.usageCount || 0) + 1
          }
        })

        res.json({
          success: true,
          message: 'Theme applied successfully',
          theme: {
            id: theme.id,
            name: theme.name,
            slug: theme.slug
          }
        })

      } catch (error) {
        console.error('Theme application error:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to apply theme'
        })
      }
    }
  },

  // Get current institute theme
  {
    path: '/themes/current',
    method: 'get',
    handler: async (req, res) => {
      try {
        const userId = req.user?.id
        const userRole = req.user?.role
        const instituteId = req.user?.institute

        if (!userId || userRole !== 'institute_admin' || !instituteId) {
          return res.status(401).json({
            success: false,
            error: 'Access denied'
          })
        }

        // Get institute with current theme
        const institute = await req.payload.findByID({
          collection: 'institutes',
          id: instituteId,
          depth: 2,
        })

        if (!institute) {
          return res.status(404).json({
            success: false,
            error: 'Institute not found'
          })
        }

        let currentTheme = null
        if (institute.theme) {
          currentTheme = institute.theme
        } else {
          // Get default institute theme
          const defaultThemes = await req.payload.find({
            collection: 'themes',
            where: {
              and: [
                { type: { equals: 'institute' } },
                { isDefault: { equals: true } },
                { isActive: { equals: true } }
              ]
            },
            limit: 1,
          })

          if (defaultThemes.docs.length > 0) {
            currentTheme = defaultThemes.docs[0]
          }
        }

        res.json({
          success: true,
          currentTheme,
          customizations: institute.themeCustomizations || {}
        })

      } catch (error) {
        console.error('Current theme fetch error:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to fetch current theme'
        })
      }
    }
  },

  // Update theme customizations
  {
    path: '/themes/customizations',
    method: 'put',
    handler: async (req, res) => {
      try {
        const userId = req.user?.id
        const userRole = req.user?.role
        const instituteId = req.user?.institute

        if (!userId || userRole !== 'institute_admin' || !instituteId) {
          return res.status(401).json({
            success: false,
            error: 'Access denied'
          })
        }

        const { customizations } = req.body

        if (!customizations || typeof customizations !== 'object') {
          return res.status(400).json({
            success: false,
            error: 'Invalid customizations data'
          })
        }

        // Update institute theme customizations
        await req.payload.update({
          collection: 'institutes',
          id: instituteId,
          data: {
            themeCustomizations: customizations
          }
        })

        res.json({
          success: true,
          message: 'Theme customizations updated successfully',
          customizations
        })

      } catch (error) {
        console.error('Theme customization error:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to update theme customizations'
        })
      }
    }
  },

  // Get platform theme (for super admin)
  {
    path: '/themes/platform/current',
    method: 'get',
    handler: async (req, res) => {
      try {
        // Get current platform theme from settings or default
        const platformThemes = await req.payload.find({
          collection: 'themes',
          where: {
            and: [
              { type: { equals: 'platform' } },
              { isDefault: { equals: true } },
              { isActive: { equals: true } }
            ]
          },
          limit: 1,
        })

        let currentTheme = null
        if (platformThemes.docs.length > 0) {
          currentTheme = platformThemes.docs[0]
        }

        res.json({
          success: true,
          currentTheme
        })

      } catch (error) {
        console.error('Platform theme fetch error:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to fetch platform theme'
        })
      }
    }
  },

  // Set platform theme (super admin only)
  {
    path: '/themes/platform/:id/apply',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const userId = req.user?.id
        const userRole = req.user?.role

        if (!userId || userRole !== 'super_admin') {
          return res.status(401).json({
            success: false,
            error: 'Access denied. Only super admins can set platform themes.'
          })
        }

        // Verify theme exists and is active
        const theme = await req.payload.findByID({
          collection: 'themes',
          id
        })

        if (!theme || !theme.isActive) {
          return res.status(404).json({
            success: false,
            error: 'Theme not found or inactive'
          })
        }

        // Only allow platform themes
        if (theme.type !== 'platform') {
          return res.status(400).json({
            success: false,
            error: 'Only platform themes can be applied to the platform'
          })
        }

        // Remove default flag from other platform themes
        await req.payload.update({
          collection: 'themes',
          where: {
            and: [
              { type: { equals: 'platform' } },
              { isDefault: { equals: true } }
            ]
          },
          data: {
            isDefault: false
          }
        })

        // Set this theme as default
        await req.payload.update({
          collection: 'themes',
          id,
          data: {
            isDefault: true,
            usageCount: (theme.usageCount || 0) + 1
          }
        })

        res.json({
          success: true,
          message: 'Platform theme applied successfully',
          theme: {
            id: theme.id,
            name: theme.name,
            slug: theme.slug
          }
        })

      } catch (error) {
        console.error('Platform theme application error:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to apply platform theme'
        })
      }
    }
  },

  // Get theme by ID (for middleware)
  {
    path: '/themes/:id',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { id } = req.params

        if (!id) {
          return res.status(400).json({
            success: false,
            error: 'Theme ID is required'
          })
        }

        const theme = await req.payload.findByID({
          collection: 'themes',
          id,
          depth: 1
        })

        if (!theme || !theme.isActive) {
          return res.status(404).json({
            success: false,
            error: 'Theme not found or inactive'
          })
        }

        res.json({
          success: true,
          theme: {
            id: theme.id,
            name: theme.name,
            slug: theme.slug,
            type: theme.type,
            colors: theme.colors,
            fonts: theme.fonts,
            features: theme.features,
            customizableElements: theme.customizableElements
          }
        })

      } catch (error) {
        console.error('Get theme by ID error:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to fetch theme'
        })
      }
    }
  }
]

export default themeEndpoints
