import type { Endpoint } from 'payload'
import { requireAuth, requireSuperAdmin, requireInstituteAdmin } from '../middleware/auth'
import bcrypt from 'bcrypt'

console.log('🔥 users.ts file loaded - endpoints are being registered!')
console.log('📍 getCurrentUserEndpoint path: /api/custom/user/me')
console.log('⏰ Timestamp:', new Date().toISOString())

// Get current user profile
export const getCurrentUserEndpoint: Endpoint = {
  path: '/users/me',
  method: 'get',
  handler: async (req) => {
    console.log('🚀🚀🚀 /api/auth/user/me ENDPOINT CALLED - NEW CODE IS WORKING! 🚀🚀🚀')
    console.log('🚀 === /api/auth/user/me GET endpoint called ===')
    console.log('📝 Request URL:', req.url)
    console.log('📝 Request method:', req.method)
    console.log('📝 Request headers:', {
      authorization: req.headers.get('authorization') ? 'Bearer token present' : 'No token',
      contentType: req.headers.get('content-type'),
      userAgent: req.headers.get('user-agent')
    })

    const authCheck = await requireAuth()(req)
    if (authCheck) {
      console.log('❌ Auth check failed')
      return authCheck
    }

    console.log('✅ Auth check passed, user:', {
      id: req.user?.id,
      email: req.user?.email,
      legacyRole: req.user?.legacyRole
    })

    try {
      console.log('🔍 Attempting to find user by ID:', req.user!.id)

      const user = await req.payload.findByID({
        collection: 'users',
        id: req.user!.id,
      })

      console.log('📊 Database lookup result:', {
        found: !!user,
        userId: user?.id,
        userEmail: user?.email,
        hasPassword: !!user?.password
      })

      if (!user) {
        console.log('❌ User not found in database')
        return Response.json(
          { message: 'User not found' },
          { status: 404 }
        )
      }

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user

      console.log('✅ Returning user data successfully')
      return Response.json({
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('💥 Get current user error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Update current user profile (PATCH)


// Update current user profile (PUT) - for frontend compatibility
export const putUpdateCurrentUserEndpoint: Endpoint = {
  path: '/api/users/me',
  method: 'put',
  handler: async (req) => {
    const authCheck = await requireAuth()(req)
    if (authCheck) return authCheck

    const body = req.json ? await req.json() : req.body
    const { firstName, lastName, phone, email, password, avatar } = body

    try {
      const updateData: any = {
        firstName,
        lastName,
        phone,
      }

      // Handle avatar update if provided
      if (avatar !== undefined) {
        if (typeof avatar === 'string' && avatar.startsWith('http')) {
          // If avatar is a URL, we need to find the corresponding media ID
          console.log('🖼️ Avatar URL provided, need to find media ID:', avatar)

          // Extract filename from URL to find the media record
          const urlParts = avatar.split('/')
          const filename = urlParts[urlParts.length - 1]

          try {
            // Search for media record with this filename
            const mediaRecords = await req.payload.find({
              collection: 'media',
              where: {
                filename: {
                  equals: filename
                }
              },
              limit: 1
            })

            if (mediaRecords.docs.length > 0) {
              updateData.avatar = mediaRecords.docs[0].id
              console.log('✅ Found media ID for avatar:', mediaRecords.docs[0].id)
            } else {
              console.log('⚠️ No media record found for filename:', filename)
              // Don't update avatar if we can't find the media record
            }
          } catch (mediaError) {
            console.error('❌ Error finding media record:', mediaError)
            // Don't update avatar if there's an error
          }
        } else if (typeof avatar === 'number' || (typeof avatar === 'string' && !avatar.startsWith('http')) || avatar === null) {
          // If avatar is already an ID, a non-URL string, or null, use it directly
          updateData.avatar = avatar
          console.log('🖼️ Avatar ID/null provided:', avatar)
        }
      }

      // Handle email update if provided
      if (email && req.user && email !== req.user.email) {
        updateData.email = email
      }

      // Handle password update if provided
      if (password && password.trim() !== '') {
        updateData.password = password
      }

      const updatedUser = await req.payload.update({
        collection: 'users',
        id: req.user!.id,
        data: updateData,
      })

      // Remove password from response
      const { password: _, ...userWithoutPassword } = updatedUser

      return Response.json({
        success: true,
        message: 'Profile updated successfully',
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('Update user error:', error)
      return Response.json(
        { success: false, message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Get all users (Super Admin only)
export const getAllUsersEndpoint: Endpoint = {
  path: '/api/users',
  method: 'get',
  handler: async (req) => {
    const authCheck = await requireSuperAdmin(req)
    if (authCheck) return authCheck

    const url = new URL(req.url || 'http://localhost')
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const role = url.searchParams.get('role')
    const search = url.searchParams.get('search')

    try {
      const where: any = {}

      if (role) {
        where.role = { equals: role }
      }

      if (search) {
        where.or = [
          { firstName: { contains: search } },
          { lastName: { contains: search } },
          { email: { contains: search } },
        ]
      }

      const users = await req.payload.find({
        collection: 'users',
        where,
        page,
        limit,
        sort: '-createdAt',
      })

      // Remove passwords from response
      const usersWithoutPasswords = users.docs.map((user: any) => {
        const { password: _, ...userWithoutPassword } = user
        return userWithoutPassword
      })

      return Response.json({
        users: usersWithoutPasswords,
        totalDocs: users.totalDocs,
        totalPages: users.totalPages,
        page: users.page,
        limit: users.limit,
        hasNextPage: users.hasNextPage,
        hasPrevPage: users.hasPrevPage,
      })

    } catch (error) {
      console.error('Get users error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Get institute users (Institute Admin only)
export const getInstituteUsersEndpoint: Endpoint = {
  path: '/api/users/institute',
  method: 'get',
  handler: async (req) => {
    const authCheck = await requireInstituteAdmin(req)
    if (authCheck) return authCheck

    const url = new URL(req.url || 'http://localhost')
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const role = url.searchParams.get('role')
    const search = url.searchParams.get('search')

    try {
      // Get current user's institute
      const currentUser = await req.payload.findByID({
        collection: 'users',
        id: req.user!.id,
      })

      if (!currentUser.institute) {
        return Response.json(
          { message: 'User not associated with any institute' },
          { status: 400 }
        )
      }

      const where: any = {
        institute: { equals: currentUser.institute },
      }

      if (role) {
        where.role = { equals: role }
      }

      if (search) {
        where.or = [
          { firstName: { contains: search } },
          { lastName: { contains: search } },
          { email: { contains: search } },
        ]
      }

      const users = await req.payload.find({
        collection: 'users',
        where,
        page,
        limit,
        sort: '-createdAt',
      })

      // Remove passwords from response
      const usersWithoutPasswords = users.docs.map((user: any) => {
        const { password: _, ...userWithoutPassword } = user
        return userWithoutPassword
      })

      return Response.json({
        users: usersWithoutPasswords,
        totalDocs: users.totalDocs,
        totalPages: users.totalPages,
        page: users.page,
        limit: users.limit,
        hasNextPage: users.hasNextPage,
        hasPrevPage: users.hasPrevPage,
      })

    } catch (error) {
      console.error('Get institute users error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Create user (Super Admin or Institute Admin)
export const createUserEndpoint: Endpoint = {
  path: '/api/users',
  method: 'post',
  handler: async (req) => {
    const authCheck = await requireAuth(['super_admin', 'platform_staff', 'institute_admin'])(req)
    if (authCheck) return authCheck

    const body = req.json ? await req.json() : req.body
    const { email, password, firstName, lastName, phone, role, institute, branch } = body

    if (!email || !password || !firstName || !lastName || !role) {
      return Response.json(
        { message: 'Required fields are missing' },
        { status: 400 }
      )
    }

    try {
      // Check permissions based on user role
      const currentUser = await req.payload.findByID({
        collection: 'users',
        id: req.user!.id,
      })

      // Super admins can create any user
      // Institute admins can only create users for their institute
      if (currentUser.legacyRole === 'institute_admin') {
        if (!currentUser.institute) {
          return Response.json(
            { message: 'Institute admin must be associated with an institute' },
            { status: 400 }
          )
        }

        // Force institute to be the same as current user's institute
        if (institute && institute !== currentUser.institute) {
          return Response.json(
            { message: 'Cannot create users for other institutes' },
            { status: 403 }
          )
        }
      }

      // Check if email already exists
      const existingUsers = await req.payload.find({
        collection: 'users',
        where: {
          email: { equals: email.toLowerCase() },
        },
      })

      if (existingUsers.docs.length > 0) {
        return Response.json(
          { message: 'Email already exists' },
          { status: 400 }
        )
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10)

      const userData: any = {
        email: email.toLowerCase(),
        password: hashedPassword,
        firstName,
        lastName,
        phone,
        role,
        isActive: true,
        emailVerified: false,
      }

      if (institute) {
        userData.institute = institute
      } else if (currentUser.legacyRole === 'institute_admin') {
        userData.institute = currentUser.institute
      }

      if (branch) {
        userData.branch = branch
      }

      const newUser = await req.payload.create({
        collection: 'users',
        data: userData,
      })

      // Remove password from response
      const { password: _, ...userWithoutPassword } = newUser

      return Response.json({
        message: 'User created successfully',
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('Create user error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}
