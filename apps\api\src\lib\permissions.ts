import type { PayloadRequest } from 'payload'
import jwt from 'jsonwebtoken'

export interface UserWithRole {
  id: string
  email: string
  role?: any
  legacyRole?: string
  permissions?: string[]
}

export interface Permission {
  id: string
  name: string
  code: string
  resource: string
  action: string
}

export interface Role {
  id: string
  name: string
  code: string
  level: string
  permissions: Permission[]
}

/**
 * Authenticate user from request
 */
export const authenticateUserFromRequest = async (req: any, payload: any): Promise<UserWithRole | null> => {
  try {
    // First, try to get user from the request (Payload should populate this)
    if (req.user) {
      console.log('User found in req.user:', req.user.email)
      return req.user
    }

    // Extract token from Authorization header
    const authHeader = req.headers.authorization || req.headers.Authorization
    let token = null
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7)
    }
    
    // Also check cookies for token
    if (!token && req.headers.cookie) {
      const cookies = req.headers.cookie.split(';').reduce((acc: any, cookie: string) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {})
      
      token = cookies['payload-token'] || cookies['token'] || cookies['auth-token']
    }

    if (token) {
      // Verify the token
      const secret = process.env.PAYLOAD_SECRET || 'your-secret-here'
      const decoded = jwt.verify(token, secret) as any
      
      if (decoded && decoded.id) {
        // Fetch the user with role relationship
        const user = await payload.findByID({
          collection: 'users',
          id: decoded.id,
          depth: 3, // Include role and permissions
        })
        
        if (user) {
          console.log('User authenticated via token:', user.email)
          return user
        }
      }
    }

    return null
  } catch (error) {
    console.error('Authentication error:', error)
    return null
  }
}

/**
 * Get user permissions from role
 */
export const getUserPermissions = (user: UserWithRole): string[] => {
  const permissions: string[] = []
  
  // Get permissions from role relationship
  if (user.role && typeof user.role === 'object' && user.role.permissions) {
    user.role.permissions.forEach((permissionItem: any) => {
      if (permissionItem.permission && typeof permissionItem.permission === 'object') {
        permissions.push(permissionItem.permission.code)
      }
    })
  }
  
  // Add legacy role permissions
  if (user.legacyRole) {
    const legacyPermissions = getLegacyRolePermissions(user.legacyRole)
    permissions.push(...legacyPermissions)
  }
  
  return [...new Set(permissions)] // Remove duplicates
}

/**
 * Get permissions for legacy roles
 */
export const getLegacyRolePermissions = (role: string): string[] => {
  const rolePermissions: Record<string, string[]> = {
    'super_admin': [
      'manage_users', 'manage_roles', 'manage_permissions', 'manage_institutes',
      'manage_courses', 'manage_settings', 'view_analytics', 'manage_billing'
    ],
    'platform_staff': [
      'manage_institutes', 'manage_courses', 'view_analytics', 'manage_settings'
    ],
    'institute_admin': [
      'manage_institute_users', 'manage_institute_courses', 'manage_branches',
      'view_institute_analytics', 'manage_institute_settings'
    ],
    'branch_manager': [
      'manage_branch_users', 'manage_branch_courses', 'view_branch_analytics'
    ],
    'trainer': [
      'manage_assigned_courses', 'view_student_progress', 'create_content'
    ],
    'institute_staff': [
      'view_institute_data', 'assist_students'
    ],
    'student': [
      'view_courses', 'submit_assignments', 'view_progress'
    ]
  }
  
  return rolePermissions[role] || []
}

/**
 * Check if user has specific permission
 */
export const hasPermission = (user: UserWithRole, permission: string): boolean => {
  const userPermissions = getUserPermissions(user)
  return userPermissions.includes(permission)
}

/**
 * Check if user has any of the specified permissions
 */
export const hasAnyPermission = (user: UserWithRole, permissions: string[]): boolean => {
  const userPermissions = getUserPermissions(user)
  return permissions.some(permission => userPermissions.includes(permission))
}

/**
 * Check if user has all of the specified permissions
 */
export const hasAllPermissions = (user: UserWithRole, permissions: string[]): boolean => {
  const userPermissions = getUserPermissions(user)
  return permissions.every(permission => userPermissions.includes(permission))
}

/**
 * Check if user is super admin
 */
export const isSuperAdmin = (user: UserWithRole): boolean => {
  // Check legacy role
  if (user.legacyRole === 'super_admin') {
    return true
  }
  
  // Check new role relationship
  if (user.role && typeof user.role === 'object') {
    return user.role.code === 'super_admin' || user.role.level === '1'
  }
  
  return false
}

/**
 * Middleware for checking permissions in endpoints
 */
export const requirePermission = (permission: string) => {
  return async (req: any, payload: any) => {
    const user = await authenticateUserFromRequest(req, payload)
    
    if (!user) {
      return {
        error: Response.json({
          success: false,
          message: 'Authentication required',
        }, { status: 401 })
      }
    }
    
    if (!hasPermission(user, permission)) {
      return {
        error: Response.json({
          success: false,
          message: `Permission '${permission}' required`,
        }, { status: 403 })
      }
    }
    
    return { user }
  }
}

/**
 * Middleware for checking super admin access
 */
export const requireSuperAdmin = async (req: any, payload: any) => {
  const user = await authenticateUserFromRequest(req, payload)
  
  if (!user) {
    return {
      error: Response.json({
        success: false,
        message: 'Authentication required',
      }, { status: 401 })
    }
  }
  
  if (!isSuperAdmin(user)) {
    return {
      error: Response.json({
        success: false,
        message: 'Super Admin access required',
      }, { status: 403 })
    }
  }
  
  return { user }
}
