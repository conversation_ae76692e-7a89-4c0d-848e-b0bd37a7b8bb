import jwt from 'jsonwebtoken'
import type { PayloadRequest } from 'payload'

export interface AuthenticatedUser {
  id: string
  email: string
  role: string
  legacyRole: string
  firstName?: string
  lastName?: string
  institute?: any
  branch?: any
  permissions?: string[]
  isActive: boolean
}

export interface InstituteAdminUser extends AuthenticatedUser {
  instituteId: string
  instituteName: string
  instituteSlug: string
  branchId?: string
  branchName?: string
}

export const authenticateToken = async (req: PayloadRequest): Promise<AuthenticatedUser | null> => {
  console.log('=== authenticateToken called ===')

  const authHeader = req.headers.get('authorization')
  console.log('Auth header present:', !!authHeader)

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    console.log('No valid auth header found')
    return null
  }

  const token = authHeader.substring(7)
  console.log('Token extracted, length:', token.length)

  try {
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || process.env.PAYLOAD_SECRET
    ) as any

    console.log('JWT decoded successfully:', {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
      exp: decoded.exp,
      iat: decoded.iat
    })

    // Verify user still exists and is active
    console.log('Looking up user in database with ID:', decoded.id)
    const user = await req.payload.findByID({
      collection: 'users',
      id: decoded.id,
      depth: 3 // Include related data
    })

    console.log('Database user lookup result:', {
      found: !!user,
      isActive: user?.isActive,
      email: user?.email,
      legacyRole: user?.legacyRole
    })

    if (!user || !user.isActive) {
      console.log('User not found or inactive')
      return null
    }

    // Extract institute ID from relationship
    const instituteId = typeof user.institute === 'object' && user.institute?.id
      ? user.institute.id
      : user.institute

    // Extract branch ID from relationship
    const branchId = typeof user.branch === 'object' && user.branch?.id
      ? user.branch.id
      : user.branch_id || user.branch

    console.log('Auth middleware user data:', {
      userId: user.id,
      rawInstitute: user.institute,
      extractedInstituteId: instituteId,
      rawBranch: user.branch,
      extractedBranchId: branchId,
      legacyRole: user.legacyRole
    })

    return {
      id: user.id,
      email: user.email,
      role: user.legacyRole || user.role,
      legacyRole: user.legacyRole || user.role,
      firstName: user.firstName,
      lastName: user.lastName,
      institute: instituteId,
      branch: branchId,
      permissions: user.permissions || [],
      isActive: user.isActive
    }
  } catch (error) {
    console.error('Token verification failed:', error)
    return null
  }
}

export const requireAuth = (allowedRoles?: string[]) => {
  return async (req: PayloadRequest) => {
    const user = await authenticateToken(req)

    if (!user) {
      return Response.json(
        { message: 'Authentication required' },
        { status: 401 }
      )
    }

    if (allowedRoles && !allowedRoles.includes(user.legacyRole)) {
      return Response.json(
        { message: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Add user to request for use in handlers
    req.user = user
    return null // Continue to handler
  }
}

export const requireSuperAdmin = requireAuth(['super_admin', 'platform_staff'])
export const requireInstituteAdmin = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
export const requireStudent = requireAuth(['student'])
export const requireAnyAuth = requireAuth() // Any authenticated user
