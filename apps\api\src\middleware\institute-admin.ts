// Middleware to check if user is institute admin and get their institute ID
export const requireInstituteAdmin = async (req: any, res: any, next: any) => {
  try {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'Authentication required' })
    }

    // Check if user is institute admin
    if (!['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(req.user.legacyRole)) {
      return res.status(403).json({ success: false, error: 'Institute admin access required' })
    }

    // Check if user has institute assigned
    if (!req.user.institute) {
      return res.status(403).json({ success: false, error: 'No institute assigned to user' })
    }

    // Add institute ID to request for easy access
    req.instituteId = typeof req.user.institute === 'object' ? req.user.institute.id : req.user.institute

    next()
  } catch (error) {
    console.error('Institute admin middleware error:', error)
    return res.status(500).json({ success: false, error: 'Internal server error' })
  }
}
