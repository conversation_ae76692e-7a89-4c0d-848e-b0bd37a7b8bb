import jwt from 'jsonwebtoken'
import { AuthenticatedUser } from './auth'

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || process.env.PAYLOAD_SECRET || 'your-secret-key'
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret'

// Token blacklist for logout functionality (use Redis in production)
const tokenBlacklist = new Set<string>()

/**
 * Enhanced JWT utilities for Course Builder System
 */

/**
 * Generate JWT Access Token with Course Builder specific claims
 */
export const generateAccessToken = (user: AuthenticatedUser): string => {
  const payload = {
    id: user.id,
    email: user.email,
    role: user.role,
    legacyRole: user.legacyRole,
    institute: user.institute,
    institute_id: user.institute,
    branch: user.branch,
    branch_id: user.branch,
    permissions: user.permissions || [],
    firstName: user.firstName,
    lastName: user.lastName,
    isActive: user.isActive
  }

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '1h',
    issuer: 'course-builder-api',
    audience: 'course-builder-frontend',
    subject: user.id
  })
}

/**
 * Generate JWT Refresh Token
 */
export const generateRefreshToken = (user: AuthenticatedUser): string => {
  const payload = {
    id: user.id,
    email: user.email,
    type: 'refresh',
    institute: user.institute
  }

  return jwt.sign(payload, JWT_REFRESH_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    issuer: 'course-builder-api',
    audience: 'course-builder-frontend',
    subject: user.id
  })
}

/**
 * Verify and decode JWT token
 */
export const verifyAccessToken = (token: string): any => {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'course-builder-api',
      audience: 'course-builder-frontend'
    })
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('TOKEN_EXPIRED')
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('TOKEN_INVALID')
    }
    throw new Error('TOKEN_VERIFICATION_FAILED')
  }
}

/**
 * Verify Refresh Token
 */
export const verifyRefreshToken = (token: string): any => {
  try {
    const decoded = jwt.verify(token, JWT_REFRESH_SECRET, {
      issuer: 'course-builder-api',
      audience: 'course-builder-frontend'
    }) as any

    if (decoded.type !== 'refresh') {
      throw new Error('INVALID_TOKEN_TYPE')
    }

    return decoded
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('REFRESH_TOKEN_EXPIRED')
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('REFRESH_TOKEN_INVALID')
    }
    throw new Error('REFRESH_TOKEN_VERIFICATION_FAILED')
  }
}

/**
 * Extract token from Authorization header
 */
export const extractTokenFromHeader = (authHeader: string | null): string | null => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }
  return authHeader.substring(7)
}

/**
 * Check if token is blacklisted
 */
export const isTokenBlacklisted = (token: string): boolean => {
  return tokenBlacklist.has(token)
}

/**
 * Blacklist token (for logout)
 */
export const blacklistToken = (token: string): void => {
  tokenBlacklist.add(token)
  
  // In production, store in Redis with TTL equal to token expiration
  // Example: redis.setex(`blacklist:${token}`, tokenTTL, '1')
  console.log(`Token blacklisted: ${token.substring(0, 20)}...`)
}

/**
 * Get token expiration time
 */
export const getTokenExpiration = (token: string): number | null => {
  try {
    const decoded = jwt.decode(token) as any
    return decoded?.exp ? decoded.exp * 1000 : null
  } catch (error) {
    return null
  }
}

/**
 * Check if token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  const expiration = getTokenExpiration(token)
  return expiration ? Date.now() >= expiration : true
}

/**
 * Generate token pair (access + refresh)
 */
export const generateTokenPair = (user: AuthenticatedUser) => {
  return {
    accessToken: generateAccessToken(user),
    refreshToken: generateRefreshToken(user),
    expiresIn: process.env.JWT_EXPIRES_IN || '1h',
    tokenType: 'Bearer'
  }
}

/**
 * Validate token structure for Course Builder requirements
 */
export const validateTokenStructure = (decoded: any): boolean => {
  const requiredFields = ['id', 'email', 'role', 'institute']
  return requiredFields.every(field => decoded[field] !== undefined)
}

/**
 * Create JWT payload for Course Builder
 */
export const createJWTPayload = (user: AuthenticatedUser) => {
  return {
    id: user.id,
    email: user.email,
    role: user.role,
    legacyRole: user.legacyRole,
    institute: user.institute,
    institute_id: user.institute,
    branch: user.branch,
    branch_id: user.branch,
    permissions: user.permissions || [],
    firstName: user.firstName,
    lastName: user.lastName,
    isActive: user.isActive,
    iat: Math.floor(Date.now() / 1000)
  }
}

/**
 * Cleanup expired blacklisted tokens (should be run periodically)
 */
export const cleanupExpiredTokens = (): void => {
  // In production, this would be handled by Redis TTL
  // For in-memory implementation, we'd need to track token expiration
  const expiredTokens: string[] = []
  
  for (const token of tokenBlacklist) {
    if (isTokenExpired(token)) {
      expiredTokens.push(token)
    }
  }
  
  expiredTokens.forEach(token => tokenBlacklist.delete(token))
  
  if (expiredTokens.length > 0) {
    console.log(`Cleaned up ${expiredTokens.length} expired blacklisted tokens`)
  }
}

/**
 * Rate limiting for authentication attempts
 */
export class AuthRateLimiter {
  private attempts = new Map<string, { count: number; resetTime: number }>()
  
  constructor(
    private maxAttempts: number = 5,
    private windowMs: number = 15 * 60 * 1000 // 15 minutes
  ) {}

  isAllowed(clientId: string): boolean {
    const now = Date.now()
    const clientAttempts = this.attempts.get(clientId)

    if (!clientAttempts || now > clientAttempts.resetTime) {
      // Reset or initialize attempts
      this.attempts.set(clientId, { count: 1, resetTime: now + this.windowMs })
      return true
    }

    if (clientAttempts.count >= this.maxAttempts) {
      return false
    }

    clientAttempts.count++
    return true
  }

  getRemainingTime(clientId: string): number {
    const clientAttempts = this.attempts.get(clientId)
    if (!clientAttempts) return 0
    
    const remaining = clientAttempts.resetTime - Date.now()
    return Math.max(0, Math.ceil(remaining / 1000))
  }

  reset(clientId: string): void {
    this.attempts.delete(clientId)
  }
}

// Global rate limiter instance
export const authRateLimiter = new AuthRateLimiter()

/**
 * JWT Error types for better error handling
 */
export enum JWTErrorType {
  TOKEN_MISSING = 'TOKEN_MISSING',
  TOKEN_INVALID = 'TOKEN_INVALID',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_REVOKED = 'TOKEN_REVOKED',
  TOKEN_MALFORMED = 'TOKEN_MALFORMED',
  REFRESH_TOKEN_EXPIRED = 'REFRESH_TOKEN_EXPIRED',
  REFRESH_TOKEN_INVALID = 'REFRESH_TOKEN_INVALID',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED'
}

/**
 * Create standardized JWT error response
 */
export const createJWTErrorResponse = (type: JWTErrorType, message?: string) => {
  const errorMessages = {
    [JWTErrorType.TOKEN_MISSING]: 'Access token required',
    [JWTErrorType.TOKEN_INVALID]: 'Invalid access token',
    [JWTErrorType.TOKEN_EXPIRED]: 'Access token has expired',
    [JWTErrorType.TOKEN_REVOKED]: 'Access token has been revoked',
    [JWTErrorType.TOKEN_MALFORMED]: 'Malformed access token',
    [JWTErrorType.REFRESH_TOKEN_EXPIRED]: 'Refresh token has expired',
    [JWTErrorType.REFRESH_TOKEN_INVALID]: 'Invalid refresh token',
    [JWTErrorType.INSUFFICIENT_PERMISSIONS]: 'Insufficient permissions',
    [JWTErrorType.RATE_LIMIT_EXCEEDED]: 'Too many authentication attempts'
  }

  return {
    success: false,
    error: message || errorMessages[type],
    code: type,
    timestamp: new Date().toISOString()
  }
}

export default {
  generateAccessToken,
  generateRefreshToken,
  verifyAccessToken,
  verifyRefreshToken,
  extractTokenFromHeader,
  isTokenBlacklisted,
  blacklistToken,
  getTokenExpiration,
  isTokenExpired,
  generateTokenPair,
  validateTokenStructure,
  createJWTPayload,
  cleanupExpiredTokens,
  AuthRateLimiter,
  authRateLimiter,
  JWTErrorType,
  createJWTErrorResponse
}
