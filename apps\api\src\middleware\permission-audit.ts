import type { PayloadRequest } from 'payload'
import { AuthenticatedUser } from './auth'
import { Permission } from './rbac'

/**
 * Permission Audit Logging System for Course Builder
 * Tracks all permission checks and access attempts for security and compliance
 */

export interface PermissionAuditLog {
  id: string
  timestamp: Date
  userId: string
  userEmail: string
  userRole: string
  institute: string
  branch?: string
  action: string
  resource: string
  resourceId?: string
  permission: Permission
  granted: boolean
  reason?: string
  ipAddress?: string
  userAgent?: string
  sessionId?: string
  metadata?: Record<string, any>
}

export interface AccessAttemptLog {
  id: string
  timestamp: Date
  userId?: string
  ipAddress?: string
  userAgent?: string
  endpoint: string
  method: string
  success: boolean
  errorCode?: string
  errorMessage?: string
  responseTime: number
  metadata?: Record<string, any>
}

// In-memory storage (use database in production)
const auditLogs: PermissionAuditLog[] = []
const accessLogs: AccessAttemptLog[] = []

// Configuration
const MAX_LOGS = 10000 // Maximum logs to keep in memory
const LOG_RETENTION_DAYS = 30

/**
 * Generate unique ID for audit log
 */
const generateLogId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Extract client information from request
 */
const extractClientInfo = (req: PayloadRequest) => {
  return {
    ipAddress: req.headers.get('x-forwarded-for') || 
               req.headers.get('x-real-ip') || 
               'unknown',
    userAgent: req.headers.get('user-agent') || 'unknown',
    sessionId: req.headers.get('x-session-id') || undefined
  }
}

/**
 * Log permission check
 */
export const logPermissionCheck = (
  user: AuthenticatedUser,
  permission: Permission,
  resource: string,
  granted: boolean,
  options: {
    resourceId?: string
    reason?: string
    action?: string
    metadata?: Record<string, any>
    req?: PayloadRequest
  } = {}
): void => {
  const clientInfo = options.req ? extractClientInfo(options.req) : {}
  
  const log: PermissionAuditLog = {
    id: generateLogId(),
    timestamp: new Date(),
    userId: user.id,
    userEmail: user.email,
    userRole: user.legacyRole || user.role,
    institute: user.institute,
    branch: user.branch,
    action: options.action || 'access',
    resource,
    resourceId: options.resourceId,
    permission,
    granted,
    reason: options.reason,
    ...clientInfo,
    metadata: options.metadata
  }
  
  auditLogs.push(log)
  
  // Cleanup old logs if needed
  if (auditLogs.length > MAX_LOGS) {
    auditLogs.splice(0, auditLogs.length - MAX_LOGS)
  }
  
  // Log to console for development
  if (process.env.NODE_ENV === 'development') {
    console.log('Permission Check:', {
      user: user.email,
      permission,
      resource,
      granted,
      reason: options.reason
    })
  }
}

/**
 * Log access attempt
 */
export const logAccessAttempt = (
  req: PayloadRequest,
  success: boolean,
  options: {
    errorCode?: string
    errorMessage?: string
    responseTime?: number
    metadata?: Record<string, any>
  } = {}
): void => {
  const clientInfo = extractClientInfo(req)
  const url = new URL(req.url)
  
  const log: AccessAttemptLog = {
    id: generateLogId(),
    timestamp: new Date(),
    userId: req.user?.id,
    endpoint: url.pathname,
    method: req.method,
    success,
    errorCode: options.errorCode,
    errorMessage: options.errorMessage,
    responseTime: options.responseTime || 0,
    ...clientInfo,
    metadata: options.metadata
  }
  
  accessLogs.push(log)
  
  // Cleanup old logs if needed
  if (accessLogs.length > MAX_LOGS) {
    accessLogs.splice(0, accessLogs.length - MAX_LOGS)
  }
}

/**
 * Get audit logs with filtering
 */
export const getAuditLogs = (filters: {
  userId?: string
  institute?: string
  permission?: Permission
  resource?: string
  granted?: boolean
  startDate?: Date
  endDate?: Date
  limit?: number
  offset?: number
} = {}): PermissionAuditLog[] => {
  let filtered = auditLogs

  if (filters.userId) {
    filtered = filtered.filter(log => log.userId === filters.userId)
  }

  if (filters.institute) {
    filtered = filtered.filter(log => log.institute === filters.institute)
  }

  if (filters.permission) {
    filtered = filtered.filter(log => log.permission === filters.permission)
  }

  if (filters.resource) {
    filtered = filtered.filter(log => log.resource === filters.resource)
  }

  if (filters.granted !== undefined) {
    filtered = filtered.filter(log => log.granted === filters.granted)
  }

  if (filters.startDate) {
    filtered = filtered.filter(log => log.timestamp >= filters.startDate!)
  }

  if (filters.endDate) {
    filtered = filtered.filter(log => log.timestamp <= filters.endDate!)
  }

  // Sort by timestamp (newest first)
  filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

  // Apply pagination
  const offset = filters.offset || 0
  const limit = filters.limit || 100
  
  return filtered.slice(offset, offset + limit)
}

/**
 * Get access logs with filtering
 */
export const getAccessLogs = (filters: {
  userId?: string
  endpoint?: string
  method?: string
  success?: boolean
  startDate?: Date
  endDate?: Date
  limit?: number
  offset?: number
} = {}): AccessAttemptLog[] => {
  let filtered = accessLogs

  if (filters.userId) {
    filtered = filtered.filter(log => log.userId === filters.userId)
  }

  if (filters.endpoint) {
    filtered = filtered.filter(log => log.endpoint.includes(filters.endpoint!))
  }

  if (filters.method) {
    filtered = filtered.filter(log => log.method === filters.method)
  }

  if (filters.success !== undefined) {
    filtered = filtered.filter(log => log.success === filters.success)
  }

  if (filters.startDate) {
    filtered = filtered.filter(log => log.timestamp >= filters.startDate!)
  }

  if (filters.endDate) {
    filtered = filtered.filter(log => log.timestamp <= filters.endDate!)
  }

  // Sort by timestamp (newest first)
  filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

  // Apply pagination
  const offset = filters.offset || 0
  const limit = filters.limit || 100
  
  return filtered.slice(offset, offset + limit)
}

/**
 * Get audit statistics
 */
export const getAuditStats = (timeframe: 'hour' | 'day' | 'week' | 'month' = 'day') => {
  const now = new Date()
  let startDate: Date

  switch (timeframe) {
    case 'hour':
      startDate = new Date(now.getTime() - 60 * 60 * 1000)
      break
    case 'day':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      break
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case 'month':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      break
  }

  const recentAuditLogs = auditLogs.filter(log => log.timestamp >= startDate)
  const recentAccessLogs = accessLogs.filter(log => log.timestamp >= startDate)

  return {
    timeframe,
    period: {
      start: startDate,
      end: now
    },
    permissions: {
      total: recentAuditLogs.length,
      granted: recentAuditLogs.filter(log => log.granted).length,
      denied: recentAuditLogs.filter(log => !log.granted).length,
      byResource: recentAuditLogs.reduce((acc, log) => {
        acc[log.resource] = (acc[log.resource] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      byUser: recentAuditLogs.reduce((acc, log) => {
        acc[log.userEmail] = (acc[log.userEmail] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    },
    access: {
      total: recentAccessLogs.length,
      successful: recentAccessLogs.filter(log => log.success).length,
      failed: recentAccessLogs.filter(log => !log.success).length,
      byEndpoint: recentAccessLogs.reduce((acc, log) => {
        acc[log.endpoint] = (acc[log.endpoint] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      averageResponseTime: recentAccessLogs.reduce((sum, log) => sum + log.responseTime, 0) / recentAccessLogs.length || 0
    }
  }
}

/**
 * Clean old audit logs
 */
export const cleanOldLogs = (): void => {
  const cutoffDate = new Date(Date.now() - LOG_RETENTION_DAYS * 24 * 60 * 60 * 1000)
  
  const initialAuditCount = auditLogs.length
  const initialAccessCount = accessLogs.length
  
  // Remove old audit logs
  for (let i = auditLogs.length - 1; i >= 0; i--) {
    if (auditLogs[i].timestamp < cutoffDate) {
      auditLogs.splice(i, 1)
    }
  }
  
  // Remove old access logs
  for (let i = accessLogs.length - 1; i >= 0; i--) {
    if (accessLogs[i].timestamp < cutoffDate) {
      accessLogs.splice(i, 1)
    }
  }
  
  const removedAudit = initialAuditCount - auditLogs.length
  const removedAccess = initialAccessCount - accessLogs.length
  
  if (removedAudit > 0 || removedAccess > 0) {
    console.log(`Cleaned ${removedAudit} audit logs and ${removedAccess} access logs older than ${LOG_RETENTION_DAYS} days`)
  }
}

/**
 * Export audit logs to JSON
 */
export const exportAuditLogs = (filters: Parameters<typeof getAuditLogs>[0] = {}) => {
  const logs = getAuditLogs(filters)
  return {
    exportDate: new Date(),
    filters,
    count: logs.length,
    logs
  }
}

/**
 * Middleware for automatic access logging
 */
export const auditMiddleware = (req: PayloadRequest, res: any, next: any) => {
  const startTime = Date.now()
  
  // Log the access attempt
  res.on('finish', () => {
    const responseTime = Date.now() - startTime
    const success = res.statusCode < 400
    
    logAccessAttempt(req, success, {
      responseTime,
      errorCode: success ? undefined : res.statusCode.toString(),
      metadata: {
        statusCode: res.statusCode,
        contentLength: res.get('content-length')
      }
    })
  })
  
  next()
}

export default {
  logPermissionCheck,
  logAccessAttempt,
  getAuditLogs,
  getAccessLogs,
  getAuditStats,
  cleanOldLogs,
  exportAuditLogs,
  auditMiddleware
}
