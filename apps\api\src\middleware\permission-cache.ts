import { AuthenticatedUser } from './auth'
import { Permission, getUserPermissions } from './rbac'

/**
 * Permission Caching System for Course Builder
 * Improves performance by caching user permissions and role checks
 */

// In-memory cache (use Redis in production)
const permissionCache = new Map<string, {
  permissions: Permission[]
  timestamp: number
  ttl: number
}>()

const roleCache = new Map<string, {
  role: string
  capabilities: string[]
  timestamp: number
  ttl: number
}>()

// Cache configuration
const CACHE_TTL = 15 * 60 * 1000 // 15 minutes
const MAX_CACHE_SIZE = 10000 // Maximum number of cached entries

/**
 * Generate cache key for user permissions
 */
const generatePermissionCacheKey = (userId: string, instituteId: string): string => {
  return `permissions:${userId}:${instituteId}`
}

/**
 * Generate cache key for user role
 */
const generateRoleCacheKey = (userId: string): string => {
  return `role:${userId}`
}

/**
 * Check if cache entry is valid
 */
const isCacheValid = (entry: { timestamp: number; ttl: number }): boolean => {
  return Date.now() - entry.timestamp < entry.ttl
}

/**
 * Clean expired cache entries
 */
const cleanExpiredEntries = () => {
  const now = Date.now()
  
  // Clean permission cache
  for (const [key, entry] of permissionCache.entries()) {
    if (now - entry.timestamp >= entry.ttl) {
      permissionCache.delete(key)
    }
  }
  
  // Clean role cache
  for (const [key, entry] of roleCache.entries()) {
    if (now - entry.timestamp >= entry.ttl) {
      roleCache.delete(key)
    }
  }
}

/**
 * Ensure cache doesn't exceed maximum size
 */
const enforceMaxCacheSize = () => {
  // Remove oldest entries if cache is too large
  if (permissionCache.size > MAX_CACHE_SIZE) {
    const entries = Array.from(permissionCache.entries())
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
    
    const toRemove = entries.slice(0, entries.length - MAX_CACHE_SIZE)
    toRemove.forEach(([key]) => permissionCache.delete(key))
  }
  
  if (roleCache.size > MAX_CACHE_SIZE) {
    const entries = Array.from(roleCache.entries())
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
    
    const toRemove = entries.slice(0, entries.length - MAX_CACHE_SIZE)
    toRemove.forEach(([key]) => roleCache.delete(key))
  }
}

/**
 * Get cached user permissions
 */
export const getCachedPermissions = (user: AuthenticatedUser): Permission[] | null => {
  const cacheKey = generatePermissionCacheKey(user.id, user.institute)
  const cached = permissionCache.get(cacheKey)
  
  if (cached && isCacheValid(cached)) {
    return cached.permissions
  }
  
  return null
}

/**
 * Cache user permissions
 */
export const cachePermissions = (user: AuthenticatedUser, permissions: Permission[]): void => {
  const cacheKey = generatePermissionCacheKey(user.id, user.institute)
  
  permissionCache.set(cacheKey, {
    permissions,
    timestamp: Date.now(),
    ttl: CACHE_TTL
  })
  
  // Cleanup if needed
  if (permissionCache.size % 100 === 0) {
    cleanExpiredEntries()
    enforceMaxCacheSize()
  }
}

/**
 * Get user permissions with caching
 */
export const getUserPermissionsWithCache = (user: AuthenticatedUser): Permission[] => {
  // Try to get from cache first
  const cached = getCachedPermissions(user)
  if (cached) {
    return cached
  }
  
  // Calculate permissions and cache them
  const permissions = getUserPermissions(user)
  cachePermissions(user, permissions)
  
  return permissions
}

/**
 * Get cached user role capabilities
 */
export const getCachedRoleCapabilities = (userId: string): string[] | null => {
  const cacheKey = generateRoleCacheKey(userId)
  const cached = roleCache.get(cacheKey)
  
  if (cached && isCacheValid(cached)) {
    return cached.capabilities
  }
  
  return null
}

/**
 * Cache user role capabilities
 */
export const cacheRoleCapabilities = (userId: string, role: string, capabilities: string[]): void => {
  const cacheKey = generateRoleCacheKey(userId)
  
  roleCache.set(cacheKey, {
    role,
    capabilities,
    timestamp: Date.now(),
    ttl: CACHE_TTL
  })
}

/**
 * Invalidate user permission cache
 */
export const invalidateUserPermissions = (userId: string, instituteId?: string): void => {
  if (instituteId) {
    const cacheKey = generatePermissionCacheKey(userId, instituteId)
    permissionCache.delete(cacheKey)
  } else {
    // Remove all permission entries for this user
    for (const key of permissionCache.keys()) {
      if (key.startsWith(`permissions:${userId}:`)) {
        permissionCache.delete(key)
      }
    }
  }
  
  // Also invalidate role cache
  const roleCacheKey = generateRoleCacheKey(userId)
  roleCache.delete(roleCacheKey)
}

/**
 * Invalidate all permissions for an institute
 */
export const invalidateInstitutePermissions = (instituteId: string): void => {
  for (const key of permissionCache.keys()) {
    if (key.endsWith(`:${instituteId}`)) {
      permissionCache.delete(key)
    }
  }
}

/**
 * Clear all permission caches
 */
export const clearAllCaches = (): void => {
  permissionCache.clear()
  roleCache.clear()
}

/**
 * Get cache statistics
 */
export const getCacheStats = () => {
  const now = Date.now()
  
  let validPermissionEntries = 0
  let expiredPermissionEntries = 0
  
  for (const entry of permissionCache.values()) {
    if (isCacheValid(entry)) {
      validPermissionEntries++
    } else {
      expiredPermissionEntries++
    }
  }
  
  let validRoleEntries = 0
  let expiredRoleEntries = 0
  
  for (const entry of roleCache.values()) {
    if (isCacheValid(entry)) {
      validRoleEntries++
    } else {
      expiredRoleEntries++
    }
  }
  
  return {
    permissions: {
      total: permissionCache.size,
      valid: validPermissionEntries,
      expired: expiredPermissionEntries
    },
    roles: {
      total: roleCache.size,
      valid: validRoleEntries,
      expired: expiredRoleEntries
    },
    config: {
      ttl: CACHE_TTL,
      maxSize: MAX_CACHE_SIZE
    }
  }
}

/**
 * Middleware to preload user permissions into cache
 */
export const preloadPermissions = async (req: any, res: any, next: any) => {
  if (req.user) {
    // Preload permissions into cache
    getUserPermissionsWithCache(req.user)
  }
  next()
}

/**
 * Scheduled cleanup job (should be run periodically)
 */
export const scheduledCleanup = (): void => {
  cleanExpiredEntries()
  enforceMaxCacheSize()
  
  const stats = getCacheStats()
  console.log('Permission cache cleanup completed:', stats)
}

/**
 * Permission cache warming for frequently accessed users
 */
export const warmCache = async (users: AuthenticatedUser[]): Promise<void> => {
  const promises = users.map(user => {
    return new Promise<void>((resolve) => {
      try {
        getUserPermissionsWithCache(user)
        resolve()
      } catch (error) {
        console.error('Error warming cache for user:', user.id, error)
        resolve()
      }
    })
  })
  
  await Promise.all(promises)
  console.log(`Warmed permission cache for ${users.length} users`)
}

/**
 * Export cache management functions
 */
export const cacheManager = {
  getCachedPermissions,
  cachePermissions,
  getUserPermissionsWithCache,
  getCachedRoleCapabilities,
  cacheRoleCapabilities,
  invalidateUserPermissions,
  invalidateInstitutePermissions,
  clearAllCaches,
  getCacheStats,
  preloadPermissions,
  scheduledCleanup,
  warmCache
}

export default cacheManager
