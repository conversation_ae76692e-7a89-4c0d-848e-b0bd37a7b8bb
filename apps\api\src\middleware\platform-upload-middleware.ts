import multer from 'multer'
import { Request, Response, NextFunction } from 'express'
import { StorageFactory, StorageConfigManager, type UploadResult, type ImageSize } from '../adapters/storage'
import { getDefaultImageSizes, validateFileType, formatFileSize } from '../adapters/storage/utils'

/**
 * Platform File Upload Middleware
 * Enhanced middleware for handling file uploads with flexible storage backend
 */

export interface PlatformUploadOptions {
  mediaType?: string
  folder?: string
  maxFileSize?: number
  allowedMimeTypes?: string[]
  generateSizes?: ImageSize[]
  requireAuth?: boolean
}

export interface UploadRequest extends Request {
  uploadResult?: UploadResult
  uploadError?: string
}

/**
 * Default upload configurations for different file types
 */
export const UPLOAD_CONFIGS = {
  logo: {
    mediaType: 'platform_logo',
    folder: 'platform/logos',
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
    generateSizes: getDefaultImageSizes('logo')
  },
  favicon: {
    mediaType: 'favicon',
    folder: 'platform/favicons',
    maxFileSize: 1 * 1024 * 1024, // 1MB
    allowedMimeTypes: ['image/x-icon', 'image/png', 'image/gif'],
    generateSizes: getDefaultImageSizes('favicon')
  },
  avatar: {
    mediaType: 'user_avatar',
    folder: 'avatars',
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    generateSizes: getDefaultImageSizes('avatar')
  },
  course_thumbnail: {
    mediaType: 'course_thumbnail',
    folder: 'courses/thumbnails',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    generateSizes: getDefaultImageSizes('course_thumbnail')
  }
}

/**
 * Create platform upload middleware
 */
export function createPlatformUploadMiddleware(options: PlatformUploadOptions = {}) {
  // Configure multer for memory storage
  const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: options.maxFileSize || 10 * 1024 * 1024, // Default 10MB
      files: 1 // Single file upload
    },
    fileFilter: (req, file, cb) => {
      console.log('🔍 File filter check:', {
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size
      })

      // Check MIME type
      const allowedTypes = options.allowedMimeTypes || ['image/*']
      const isAllowed = validateFileType(file.mimetype, allowedTypes)

      if (!isAllowed) {
        console.log('❌ File type not allowed:', file.mimetype)
        return cb(new Error(`File type ${file.mimetype} is not allowed`))
      }

      console.log('✅ File type allowed:', file.mimetype)
      cb(null, true)
    }
  })

  return [
    upload.single('file'),
    async (req: UploadRequest, res: Response, next: NextFunction) => {
      console.log('🚀 Platform upload middleware started')

      try {
        // Check if file was uploaded
        if (!req.file) {
          console.log('❌ No file provided in request')
          req.uploadError = 'No file provided'
          return next()
        }

        console.log('📁 File received:', {
          originalname: req.file.originalname,
          mimetype: req.file.mimetype,
          size: formatFileSize(req.file.size),
          buffer: !!req.file.buffer
        })

        // Get storage configuration and create adapter
        const configManager = StorageConfigManager.getInstance()
        const storageConfig = await configManager.getStorageConfig(req.payload)
        const storageAdapter = StorageFactory.create(storageConfig)

        console.log('📦 Storage adapter created:', {
          provider: storageConfig.provider
        })

        // Prepare upload options
        const uploadOptions = {
          folder: options.folder,
          mediaType: options.mediaType,
          generateSizes: options.generateSizes
        }

        console.log('⚙️ Upload options:', uploadOptions)

        // Upload file using storage adapter
        const uploadResult = await storageAdapter.uploadFile(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype,
          uploadOptions
        )

        console.log('✅ File uploaded successfully:', {
          id: uploadResult.id,
          filename: uploadResult.filename,
          url: uploadResult.url,
          size: formatFileSize(uploadResult.size),
          sizes: uploadResult.sizes ? Object.keys(uploadResult.sizes) : []
        })

        // Attach result to request
        req.uploadResult = uploadResult

        next()

      } catch (error) {
        console.error('❌ Upload middleware error:', error)
        req.uploadError = error instanceof Error ? error.message : 'Upload failed'
        next()
      }
    }
  ]
}

/**
 * Create middleware for specific upload types
 */
export function createLogoUploadMiddleware() {
  return createPlatformUploadMiddleware(UPLOAD_CONFIGS.logo)
}

export function createFaviconUploadMiddleware() {
  return createPlatformUploadMiddleware(UPLOAD_CONFIGS.favicon)
}

export function createAvatarUploadMiddleware() {
  return createPlatformUploadMiddleware(UPLOAD_CONFIGS.avatar)
}

export function createCourseThumbnailUploadMiddleware() {
  return createPlatformUploadMiddleware(UPLOAD_CONFIGS.course_thumbnail)
}

/**
 * Validation middleware for upload results
 */
export function validateUploadResult(req: UploadRequest, res: Response, next: NextFunction) {
  if (req.uploadError) {
    console.log('❌ Upload validation failed:', req.uploadError)
    return res.status(400).json({
      success: false,
      message: req.uploadError
    })
  }

  if (!req.uploadResult) {
    console.log('❌ No upload result found')
    return res.status(400).json({
      success: false,
      message: 'Upload failed - no result'
    })
  }

  console.log('✅ Upload validation passed')
  next()
}

/**
 * Response formatter middleware
 */
export function formatUploadResponse(req: UploadRequest, res: Response, next: NextFunction) {
  if (req.uploadResult) {
    const response = {
      success: true,
      message: 'File uploaded successfully',
      data: {
        id: req.uploadResult.id,
        filename: req.uploadResult.filename,
        originalName: req.uploadResult.originalName,
        mimeType: req.uploadResult.mimeType,
        size: req.uploadResult.size,
        url: req.uploadResult.url,
        cdnUrl: req.uploadResult.cdnUrl,
        metadata: req.uploadResult.metadata,
        sizes: req.uploadResult.sizes
      }
    }

    console.log('📤 Sending upload response:', {
      success: response.success,
      filename: response.data.filename,
      url: response.data.url,
      sizes: response.data.sizes ? Object.keys(response.data.sizes) : []
    })

    return res.json(response)
  }

  next()
}

/**
 * Error handling middleware for uploads
 */
export function handleUploadError(error: any, req: Request, res: Response, next: NextFunction) {
  console.error('❌ Upload error handler:', error)

  if (error instanceof multer.MulterError) {
    let message = 'Upload failed'
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        message = 'File size too large'
        break
      case 'LIMIT_FILE_COUNT':
        message = 'Too many files'
        break
      case 'LIMIT_UNEXPECTED_FILE':
        message = 'Unexpected file field'
        break
      default:
        message = error.message
    }

    return res.status(400).json({
      success: false,
      message,
      code: error.code
    })
  }

  if (error.message) {
    return res.status(400).json({
      success: false,
      message: error.message
    })
  }

  res.status(500).json({
    success: false,
    message: 'Internal server error during upload'
  })
}
