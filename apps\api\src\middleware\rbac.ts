import type { PayloadRequest } from 'payload'
import { AuthenticatedUser } from './auth'

/**
 * Role-Based Access Control (RBAC) Middleware for Course Builder System
 * Implements comprehensive permission checking and role-based access control
 */

// Define user roles hierarchy
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  INSTITUTE_ADMIN = 'institute_admin',
  BRANCH_MANAGER = 'branch_manager',
  TRAINER = 'trainer',
  STAFF = 'staff',
  STUDENT = 'student',
  LEVEL_4 = 'level_4'
}

// Define permissions for Course Builder System
export enum Permission {
  // Course Management
  COURSES_CREATE = 'courses:create',
  COURSES_READ = 'courses:read',
  COURSES_UPDATE = 'courses:update',
  COURSES_DELETE = 'courses:delete',
  COURSES_PUBLISH = 'courses:publish',
  COURSES_ARCHIVE = 'courses:archive',

  // Lesson Management
  LESSONS_CREATE = 'lessons:create',
  LESSONS_READ = 'lessons:read',
  LESSONS_UPDATE = 'lessons:update',
  LESSONS_DELETE = 'lessons:delete',
  LESSONS_REORDER = 'lessons:reorder',

  // Content Management
  CONTENT_CREATE = 'content:create',
  CONTENT_READ = 'content:read',
  CONTENT_UPDATE = 'content:update',
  CONTENT_DELETE = 'content:delete',
  CONTENT_UPLOAD = 'content:upload',

  // Assessment Management
  ASSESSMENTS_CREATE = 'assessments:create',
  ASSESSMENTS_READ = 'assessments:read',
  ASSESSMENTS_UPDATE = 'assessments:update',
  ASSESSMENTS_DELETE = 'assessments:delete',
  ASSESSMENTS_GRADE = 'assessments:grade',

  // Analytics & Reports
  ANALYTICS_VIEW = 'analytics:view',
  ANALYTICS_EXPORT = 'analytics:export',
  REPORTS_GENERATE = 'reports:generate',

  // User Management
  USERS_CREATE = 'users:create',
  USERS_READ = 'users:read',
  USERS_UPDATE = 'users:update',
  USERS_DELETE = 'users:delete',

  // Institute Management
  INSTITUTE_MANAGE = 'institute:manage',
  BRANCH_MANAGE = 'branch:manage',

  // System Administration
  SYSTEM_ADMIN = 'system:admin',
  SYSTEM_CONFIG = 'system:config'
}

// Role-Permission Matrix
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.SUPER_ADMIN]: [
    // Super admin has all permissions
    ...Object.values(Permission)
  ],

  [UserRole.INSTITUTE_ADMIN]: [
    Permission.COURSES_CREATE,
    Permission.COURSES_READ,
    Permission.COURSES_UPDATE,
    Permission.COURSES_DELETE,
    Permission.COURSES_PUBLISH,
    Permission.COURSES_ARCHIVE,
    Permission.LESSONS_CREATE,
    Permission.LESSONS_READ,
    Permission.LESSONS_UPDATE,
    Permission.LESSONS_DELETE,
    Permission.LESSONS_REORDER,
    Permission.CONTENT_CREATE,
    Permission.CONTENT_READ,
    Permission.CONTENT_UPDATE,
    Permission.CONTENT_DELETE,
    Permission.CONTENT_UPLOAD,
    Permission.ASSESSMENTS_CREATE,
    Permission.ASSESSMENTS_READ,
    Permission.ASSESSMENTS_UPDATE,
    Permission.ASSESSMENTS_DELETE,
    Permission.ASSESSMENTS_GRADE,
    Permission.ANALYTICS_VIEW,
    Permission.ANALYTICS_EXPORT,
    Permission.REPORTS_GENERATE,
    Permission.USERS_CREATE,
    Permission.USERS_READ,
    Permission.USERS_UPDATE,
    Permission.USERS_DELETE,
    Permission.INSTITUTE_MANAGE,
    Permission.BRANCH_MANAGE
  ],

  [UserRole.BRANCH_MANAGER]: [
    Permission.COURSES_CREATE,
    Permission.COURSES_READ,
    Permission.COURSES_UPDATE,
    Permission.COURSES_DELETE,
    Permission.COURSES_PUBLISH,
    Permission.LESSONS_CREATE,
    Permission.LESSONS_READ,
    Permission.LESSONS_UPDATE,
    Permission.LESSONS_DELETE,
    Permission.LESSONS_REORDER,
    Permission.CONTENT_CREATE,
    Permission.CONTENT_READ,
    Permission.CONTENT_UPDATE,
    Permission.CONTENT_DELETE,
    Permission.CONTENT_UPLOAD,
    Permission.ASSESSMENTS_CREATE,
    Permission.ASSESSMENTS_READ,
    Permission.ASSESSMENTS_UPDATE,
    Permission.ASSESSMENTS_DELETE,
    Permission.ASSESSMENTS_GRADE,
    Permission.ANALYTICS_VIEW,
    Permission.REPORTS_GENERATE,
    Permission.USERS_READ,
    Permission.USERS_UPDATE
  ],

  [UserRole.TRAINER]: [
    Permission.COURSES_CREATE,
    Permission.COURSES_READ,
    Permission.COURSES_UPDATE,
    Permission.LESSONS_CREATE,
    Permission.LESSONS_READ,
    Permission.LESSONS_UPDATE,
    Permission.LESSONS_DELETE,
    Permission.LESSONS_REORDER,
    Permission.CONTENT_CREATE,
    Permission.CONTENT_READ,
    Permission.CONTENT_UPDATE,
    Permission.CONTENT_DELETE,
    Permission.CONTENT_UPLOAD,
    Permission.ASSESSMENTS_CREATE,
    Permission.ASSESSMENTS_READ,
    Permission.ASSESSMENTS_UPDATE,
    Permission.ASSESSMENTS_DELETE,
    Permission.ASSESSMENTS_GRADE,
    Permission.ANALYTICS_VIEW
  ],

  [UserRole.STAFF]: [
    Permission.COURSES_READ,
    Permission.LESSONS_READ,
    Permission.CONTENT_READ,
    Permission.ASSESSMENTS_READ,
    Permission.ASSESSMENTS_GRADE,
    Permission.ANALYTICS_VIEW
  ],

  [UserRole.STUDENT]: [
    Permission.COURSES_READ,
    Permission.LESSONS_READ,
    Permission.CONTENT_READ,
    Permission.ASSESSMENTS_READ
  ],

  [UserRole.LEVEL_4]: [
    Permission.COURSES_READ,
    Permission.LESSONS_READ,
    Permission.CONTENT_READ,
    Permission.ASSESSMENTS_READ
  ]
}

/**
 * Check if user has specific permission
 */
export const hasPermission = (user: AuthenticatedUser, permission: Permission): boolean => {
  // Super admin has all permissions
  if (user.legacyRole === UserRole.SUPER_ADMIN || user.role === UserRole.SUPER_ADMIN) {
    return true
  }

  // Check role-based permissions
  const userRole = (user.legacyRole || user.role) as UserRole
  const rolePermissions = ROLE_PERMISSIONS[userRole] || []
  
  if (rolePermissions.includes(permission)) {
    return true
  }

  // Check user-specific permissions
  if (user.permissions && user.permissions.includes(permission)) {
    return true
  }

  return false
}

/**
 * Check if user has any of the specified permissions
 */
export const hasAnyPermission = (user: AuthenticatedUser, permissions: Permission[]): boolean => {
  return permissions.some(permission => hasPermission(user, permission))
}

/**
 * Check if user has all of the specified permissions
 */
export const hasAllPermissions = (user: AuthenticatedUser, permissions: Permission[]): boolean => {
  return permissions.every(permission => hasPermission(user, permission))
}

/**
 * Get all permissions for a user
 */
export const getUserPermissions = (user: AuthenticatedUser): Permission[] => {
  // Super admin has all permissions
  if (user.legacyRole === UserRole.SUPER_ADMIN || user.role === UserRole.SUPER_ADMIN) {
    return Object.values(Permission)
  }

  const userRole = (user.legacyRole || user.role) as UserRole
  const rolePermissions = ROLE_PERMISSIONS[userRole] || []
  const userSpecificPermissions = user.permissions || []

  // Combine role permissions with user-specific permissions
  const allPermissions = [...rolePermissions, ...userSpecificPermissions]
  
  // Remove duplicates
  return [...new Set(allPermissions)] as Permission[]
}

/**
 * Check if user can access institute data
 */
export const canAccessInstitute = (user: AuthenticatedUser, instituteId: string): boolean => {
  // Super admin can access all institutes
  if (user.legacyRole === UserRole.SUPER_ADMIN || user.role === UserRole.SUPER_ADMIN) {
    return true
  }

  // Check if user belongs to the institute
  return user.institute === instituteId
}

/**
 * Check if user can access branch data
 */
export const canAccessBranch = (user: AuthenticatedUser, branchId: string): boolean => {
  // Super admin and institute admin can access all branches in their institute
  if (user.legacyRole === UserRole.SUPER_ADMIN || 
      user.role === UserRole.SUPER_ADMIN ||
      user.legacyRole === UserRole.INSTITUTE_ADMIN ||
      user.role === UserRole.INSTITUTE_ADMIN) {
    return true
  }

  // Check if user belongs to the branch
  return user.branch === branchId
}

/**
 * RBAC Middleware Factory
 */
export const requirePermission = (permission: Permission) => {
  return async (req: PayloadRequest) => {
    if (!req.user) {
      return Response.json(
        { 
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      )
    }

    if (!hasPermission(req.user, permission)) {
      return Response.json(
        { 
          success: false,
          error: 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSIONS',
          required: permission
        },
        { status: 403 }
      )
    }

    return null // Continue to handler
  }
}

/**
 * Require any of the specified permissions
 */
export const requireAnyPermission = (permissions: Permission[]) => {
  return async (req: PayloadRequest) => {
    if (!req.user) {
      return Response.json(
        { 
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      )
    }

    if (!hasAnyPermission(req.user, permissions)) {
      return Response.json(
        { 
          success: false,
          error: 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSIONS',
          required: permissions
        },
        { status: 403 }
      )
    }

    return null // Continue to handler
  }
}

/**
 * Require all of the specified permissions
 */
export const requireAllPermissions = (permissions: Permission[]) => {
  return async (req: PayloadRequest) => {
    if (!req.user) {
      return Response.json(
        { 
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      )
    }

    if (!hasAllPermissions(req.user, permissions)) {
      return Response.json(
        { 
          success: false,
          error: 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSIONS',
          required: permissions
        },
        { status: 403 }
      )
    }

    return null // Continue to handler
  }
}

/**
 * Require specific role
 */
export const requireRole = (roles: UserRole | UserRole[]) => {
  const allowedRoles = Array.isArray(roles) ? roles : [roles]
  
  return async (req: PayloadRequest) => {
    if (!req.user) {
      return Response.json(
        { 
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      )
    }

    const userRole = req.user.legacyRole || req.user.role
    if (!allowedRoles.includes(userRole as UserRole)) {
      return Response.json(
        { 
          success: false,
          error: 'Insufficient role permissions',
          code: 'INSUFFICIENT_ROLE',
          required: allowedRoles,
          current: userRole
        },
        { status: 403 }
      )
    }

    return null // Continue to handler
  }
}

/**
 * Institute access control middleware
 */
export const requireInstituteAccess = (instituteIdParam: string = 'instituteId') => {
  return async (req: PayloadRequest) => {
    if (!req.user) {
      return Response.json(
        { 
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      )
    }

    const url = new URL(req.url || '')
    const instituteId = url.searchParams.get(instituteIdParam) || 
                       req.headers.get('X-Institute-ID')

    if (!instituteId) {
      return Response.json(
        { 
          success: false,
          error: 'Institute ID required',
          code: 'INSTITUTE_ID_REQUIRED'
        },
        { status: 400 }
      )
    }

    if (!canAccessInstitute(req.user, instituteId)) {
      return Response.json(
        { 
          success: false,
          error: 'Access denied to institute',
          code: 'INSTITUTE_ACCESS_DENIED'
        },
        { status: 403 }
      )
    }

    return null // Continue to handler
  }
}

/**
 * Branch access control middleware
 */
export const requireBranchAccess = (branchIdParam: string = 'branchId') => {
  return async (req: PayloadRequest) => {
    if (!req.user) {
      return Response.json(
        { 
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      )
    }

    const url = new URL(req.url || '')
    const branchId = url.searchParams.get(branchIdParam) ||
                     req.headers.get('X-Branch-ID')

    if (!branchId) {
      return Response.json(
        { 
          success: false,
          error: 'Branch ID required',
          code: 'BRANCH_ID_REQUIRED'
        },
        { status: 400 }
      )
    }

    if (!canAccessBranch(req.user, branchId)) {
      return Response.json(
        { 
          success: false,
          error: 'Access denied to branch',
          code: 'BRANCH_ACCESS_DENIED'
        },
        { status: 403 }
      )
    }

    return null // Continue to handler
  }
}

// Convenience middleware combinations for Course Builder
export const requireCourseManagement = requireAnyPermission([
  Permission.COURSES_CREATE,
  Permission.COURSES_UPDATE,
  Permission.COURSES_DELETE
])

export const requireLessonManagement = requireAnyPermission([
  Permission.LESSONS_CREATE,
  Permission.LESSONS_UPDATE,
  Permission.LESSONS_DELETE
])

export const requireContentManagement = requireAnyPermission([
  Permission.CONTENT_CREATE,
  Permission.CONTENT_UPDATE,
  Permission.CONTENT_DELETE
])

export const requireAssessmentManagement = requireAnyPermission([
  Permission.ASSESSMENTS_CREATE,
  Permission.ASSESSMENTS_UPDATE,
  Permission.ASSESSMENTS_DELETE
])

export default {
  UserRole,
  Permission,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  getUserPermissions,
  canAccessInstitute,
  canAccessBranch,
  requirePermission,
  requireAnyPermission,
  requireAllPermissions,
  requireRole,
  requireInstituteAccess,
  requireBranchAccess,
  requireCourseManagement,
  requireLessonManagement,
  requireContentManagement,
  requireAssessmentManagement
}
