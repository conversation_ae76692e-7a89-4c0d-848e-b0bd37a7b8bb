import type { PayloadRequest } from 'payload'
import { AuthenticatedUser } from './auth'

/**
 * Tenant Context Manager for Course Builder System
 * Manages database-level tenant isolation using PostgreSQL RLS
 */

export interface TenantContext {
  instituteId: string
  branchId?: string
  userId: string
  userRole: string
  isSuperAdmin: boolean
  isInstituteAdmin: boolean
}

/**
 * Set tenant context in database session
 */
export const setDatabaseTenantContext = async (
  payload: any,
  context: TenantContext
): Promise<void> => {
  try {
    // Use raw SQL to set session variables for RLS
    await payload.db.drizzle.execute(`
      SELECT set_tenant_context(
        '${context.instituteId}'::UUID,
        ${context.branchId ? `'${context.branchId}'::UUID` : 'NULL'},
        ${context.isSuperAdmin},
        ${context.isInstituteAdmin}
      )
    `)
  } catch (error) {
    console.error('Error setting database tenant context:', error)
    throw new Error('Failed to set tenant context')
  }
}

/**
 * Clear tenant context from database session
 */
export const clearDatabaseTenantContext = async (payload: any): Promise<void> => {
  try {
    await payload.db.drizzle.execute('SELECT clear_tenant_context()')
  } catch (error) {
    console.error('Error clearing database tenant context:', error)
    // Don't throw error for cleanup operations
  }
}

/**
 * Extract tenant context from authenticated user
 */
export const extractTenantContext = (user: AuthenticatedUser): TenantContext => {
  const isSuperAdmin = user.legacyRole === 'super_admin' || user.role === 'super_admin'
  const isInstituteAdmin = isSuperAdmin || 
                          user.legacyRole === 'institute_admin' || 
                          user.role === 'institute_admin'

  return {
    instituteId: user.institute,
    branchId: user.branch,
    userId: user.id,
    userRole: user.legacyRole || user.role,
    isSuperAdmin,
    isInstituteAdmin
  }
}

/**
 * Middleware to automatically set tenant context for database operations
 */
export const tenantContextMiddleware = async (req: PayloadRequest, res: any, next: any) => {
  if (!req.user) {
    return next()
  }

  try {
    const context = extractTenantContext(req.user)
    
    // Set tenant context in database session
    await setDatabaseTenantContext(req.payload, context)
    
    // Store context in request for later use
    req.tenantContext = context
    
    // Ensure context is cleared after request
    res.on('finish', async () => {
      try {
        await clearDatabaseTenantContext(req.payload)
      } catch (error) {
        console.error('Error clearing tenant context on response finish:', error)
      }
    })
    
    next()
  } catch (error) {
    console.error('Error in tenant context middleware:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to establish tenant context',
      code: 'TENANT_CONTEXT_ERROR'
    })
  }
}

/**
 * Validate that a resource belongs to the current tenant
 */
export const validateResourceTenancy = async (
  payload: any,
  collection: string,
  resourceId: string,
  context: TenantContext
): Promise<boolean> => {
  try {
    // Super admin can access everything
    if (context.isSuperAdmin) {
      return true
    }

    const resource = await payload.findByID({
      collection,
      id: resourceId,
      depth: 0
    })

    if (!resource) {
      return false
    }

    // Check institute ownership
    if (resource.institute_id && resource.institute_id !== context.instituteId) {
      return false
    }

    // Check branch ownership for non-institute-admin users
    if (!context.isInstituteAdmin && resource.branch_id && resource.branch_id !== context.branchId) {
      return false
    }

    return true
  } catch (error) {
    console.error('Error validating resource tenancy:', error)
    return false
  }
}

/**
 * Create tenant-aware query filter
 */
export const createTenantFilter = (context: TenantContext, isolationType: 'institute' | 'branch' = 'institute') => {
  // Super admin sees everything
  if (context.isSuperAdmin) {
    return {}
  }

  if (isolationType === 'institute') {
    return {
      institute_id: {
        equals: context.instituteId
      }
    }
  }

  if (isolationType === 'branch') {
    // Institute admin sees all branches in their institute
    if (context.isInstituteAdmin) {
      return {
        institute_id: {
          equals: context.instituteId
        }
      }
    }

    // Other roles see only their branch
    return {
      and: [
        {
          institute_id: {
            equals: context.instituteId
          }
        },
        {
          or: [
            {
              branch_id: {
                equals: context.branchId
              }
            },
            {
              branch_id: {
                equals: null
              }
            }
          ]
        }
      ]
    }
  }

  return {}
}

/**
 * Auto-populate tenant fields on document creation
 */
export const autoPopulateTenantFields = (data: any, context: TenantContext) => {
  const populatedData = { ...data }

  // Don't override if super admin explicitly sets different values
  if (context.isSuperAdmin && (data.institute_id || data.branch_id)) {
    return populatedData
  }

  // Auto-populate institute_id
  if (!populatedData.institute_id) {
    populatedData.institute_id = context.instituteId
  }

  // Auto-populate branch_id for non-institute-admin users
  if (!populatedData.branch_id && context.branchId && !context.isInstituteAdmin) {
    populatedData.branch_id = context.branchId
  }

  // Auto-populate created_by
  if (!populatedData.created_by) {
    populatedData.created_by = context.userId
  }

  return populatedData
}

/**
 * Validate tenant consistency on updates
 */
export const validateTenantUpdate = (
  existingData: any,
  updateData: any,
  context: TenantContext
): { valid: boolean; error?: string } => {
  // Super admin can modify tenant fields
  if (context.isSuperAdmin) {
    return { valid: true }
  }

  // Prevent changing institute_id
  if (updateData.institute_id && updateData.institute_id !== existingData.institute_id) {
    return {
      valid: false,
      error: 'Cannot change institute assignment'
    }
  }

  // Prevent non-institute-admin from changing branch_id to different institute
  if (updateData.branch_id && !context.isInstituteAdmin) {
    if (updateData.branch_id !== context.branchId) {
      return {
        valid: false,
        error: 'Cannot assign resource to different branch'
      }
    }
  }

  return { valid: true }
}

/**
 * File storage tenant isolation
 */
export const getTenantStoragePath = (context: TenantContext, filename: string): string => {
  const basePath = `institutes/${context.instituteId}`
  
  if (context.branchId) {
    return `${basePath}/branches/${context.branchId}/${filename}`
  }
  
  return `${basePath}/${filename}`
}

/**
 * Validate file access based on tenant context
 */
export const validateFileAccess = (filePath: string, context: TenantContext): boolean => {
  // Super admin can access all files
  if (context.isSuperAdmin) {
    return true
  }

  // Check if file belongs to user's institute
  if (!filePath.includes(`institutes/${context.instituteId}`)) {
    return false
  }

  // For non-institute-admin users, check branch access
  if (!context.isInstituteAdmin && context.branchId) {
    if (!filePath.includes(`branches/${context.branchId}`)) {
      return false
    }
  }

  return true
}

/**
 * Create tenant-aware pagination query
 */
export const createTenantPaginationQuery = (
  baseQuery: any,
  context: TenantContext,
  isolationType: 'institute' | 'branch' = 'institute'
) => {
  const tenantFilter = createTenantFilter(context, isolationType)

  if (Object.keys(tenantFilter).length === 0) {
    return baseQuery // No tenant filter for super admin
  }

  if (!baseQuery || Object.keys(baseQuery).length === 0) {
    return tenantFilter // Only tenant filter
  }

  // Combine both filters
  return {
    and: [
      tenantFilter,
      baseQuery
    ]
  }
}

/**
 * Tenant isolation hook for Payload collections
 */
export const createTenantHook = (isolationType: 'institute' | 'branch' = 'institute') => {
  return {
    beforeRead: [
      async ({ req }: any) => {
        if (req.user && req.tenantContext) {
          const tenantFilter = createTenantFilter(req.tenantContext, isolationType)
          if (Object.keys(tenantFilter).length > 0) {
            req.query = createTenantPaginationQuery(req.query, req.tenantContext, isolationType)
          }
        }
      }
    ],
    beforeChange: [
      async ({ req, data, operation }: any) => {
        if (req.user && req.tenantContext) {
          if (operation === 'create') {
            return autoPopulateTenantFields(data, req.tenantContext)
          }
          
          if (operation === 'update') {
            const validation = validateTenantUpdate(req.originalDoc, data, req.tenantContext)
            if (!validation.valid) {
              throw new Error(validation.error)
            }
          }
        }
        return data
      }
    ]
  }
}

// Extend PayloadRequest interface
declare global {
  namespace Express {
    interface Request {
      tenantContext?: TenantContext
    }
  }
}

export default {
  setDatabaseTenantContext,
  clearDatabaseTenantContext,
  extractTenantContext,
  tenantContextMiddleware,
  validateResourceTenancy,
  createTenantFilter,
  autoPopulateTenantFields,
  validateTenantUpdate,
  getTenantStoragePath,
  validateFileAccess,
  createTenantPaginationQuery,
  createTenantHook
}
