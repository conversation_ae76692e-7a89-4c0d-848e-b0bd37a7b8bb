import type { PayloadRequest } from 'payload'
import { AuthenticatedUser } from './auth'

/**
 * Multi-Tenant Data Isolation Middleware for Course Builder System
 * Ensures complete data separation between institutes
 */

/**
 * Tenant context interface
 */
export interface TenantContext {
  instituteId: string
  branchId?: string
  userId: string
  userRole: string
}

/**
 * Extract tenant context from authenticated user
 */
export const extractTenantContext = (user: AuthenticatedUser): TenantContext => {
  return {
    instituteId: user.institute,
    branchId: user.branch,
    userId: user.id,
    userRole: user.legacyRole || user.role
  }
}

/**
 * Create institute-based query filter
 */
export const createInstituteFilter = (user: AuthenticatedUser) => {
  // Super admin can access all data
  if (user.legacyRole === 'super_admin' || user.role === 'super_admin') {
    return {} // No filter for super admin
  }

  return {
    institute_id: {
      equals: user.institute
    }
  }
}

/**
 * Create branch-based query filter
 */
export const createBranchFilter = (user: AuthenticatedUser) => {
  // Super admin can access all data
  if (user.legacyRole === 'super_admin' || user.role === 'super_admin') {
    return {} // No filter for super admin
  }

  // Institute admin can access all branches in their institute
  if (user.legacyRole === 'institute_admin' || user.role === 'institute_admin') {
    return {
      institute_id: {
        equals: user.institute
      }
    }
  }

  // Other roles are restricted to their branch
  return {
    and: [
      {
        institute_id: {
          equals: user.institute
        }
      },
      {
        branch_id: {
          equals: user.branch
        }
      }
    ]
  }
}

/**
 * Create user-based query filter (for user's own data)
 */
export const createUserFilter = (user: AuthenticatedUser) => {
  // Super admin can access all data
  if (user.legacyRole === 'super_admin' || user.role === 'super_admin') {
    return {} // No filter for super admin
  }

  // Institute admin can access all users in their institute
  if (user.legacyRole === 'institute_admin' || user.role === 'institute_admin') {
    return {
      institute_id: {
        equals: user.institute
      }
    }
  }

  // Branch manager can access users in their branch
  if (user.legacyRole === 'branch_manager' || user.role === 'branch_manager') {
    return {
      and: [
        {
          institute_id: {
            equals: user.institute
          }
        },
        {
          branch_id: {
            equals: user.branch
          }
        }
      ]
    }
  }

  // Other roles can only access their own data
  return {
    id: {
      equals: user.id
    }
  }
}

/**
 * Tenant isolation middleware for API endpoints
 */
export const tenantIsolation = (isolationType: 'institute' | 'branch' | 'user' = 'institute') => {
  return async (req: PayloadRequest) => {
    if (!req.user) {
      return Response.json(
        { 
          success: false,
          error: 'Authentication required for tenant isolation',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      )
    }

    // Add tenant context to request
    req.tenantContext = extractTenantContext(req.user)

    // Add appropriate filter to request for use in handlers
    switch (isolationType) {
      case 'institute':
        req.tenantFilter = createInstituteFilter(req.user)
        break
      case 'branch':
        req.tenantFilter = createBranchFilter(req.user)
        break
      case 'user':
        req.tenantFilter = createUserFilter(req.user)
        break
    }

    return null // Continue to handler
  }
}

/**
 * Validate that data belongs to user's tenant
 */
export const validateTenantOwnership = async (
  req: PayloadRequest,
  collection: string,
  documentId: string
): Promise<boolean> => {
  if (!req.user) {
    return false
  }

  // Super admin can access everything
  if (req.user.legacyRole === 'super_admin' || req.user.role === 'super_admin') {
    return true
  }

  try {
    const document = await req.payload.findByID({
      collection,
      id: documentId,
      depth: 0
    })

    if (!document) {
      return false
    }

    // Check institute ownership
    if (document.institute_id && document.institute_id !== req.user.institute) {
      return false
    }

    // Check branch ownership for non-institute-admin users
    if (req.user.legacyRole !== 'institute_admin' && req.user.role !== 'institute_admin') {
      if (document.branch_id && document.branch_id !== req.user.branch) {
        return false
      }
    }

    return true
  } catch (error) {
    console.error('Error validating tenant ownership:', error)
    return false
  }
}

/**
 * Middleware to validate tenant ownership of a resource
 */
export const requireTenantOwnership = (collection: string, idParam: string = 'id') => {
  return async (req: PayloadRequest) => {
    if (!req.user) {
      return Response.json(
        { 
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      )
    }

    const url = new URL(req.url)
    const pathParts = url.pathname.split('/')
    const documentId = pathParts[pathParts.length - 1] // Get last part of path

    if (!documentId) {
      return Response.json(
        { 
          success: false,
          error: 'Resource ID required',
          code: 'RESOURCE_ID_REQUIRED'
        },
        { status: 400 }
      )
    }

    const hasAccess = await validateTenantOwnership(req, collection, documentId)

    if (!hasAccess) {
      return Response.json(
        { 
          success: false,
          error: 'Access denied to resource',
          code: 'TENANT_ACCESS_DENIED'
        },
        { status: 403 }
      )
    }

    return null // Continue to handler
  }
}

/**
 * Auto-populate tenant fields on document creation
 */
export const autoPopulateTenantFields = (user: AuthenticatedUser, data: any) => {
  // Don't override if super admin explicitly sets different values
  if (user.legacyRole === 'super_admin' || user.role === 'super_admin') {
    return data
  }

  const populatedData = { ...data }

  // Auto-populate institute_id if not set
  if (!populatedData.institute_id && user.institute) {
    populatedData.institute_id = user.institute
  }

  // Auto-populate branch_id for non-institute-admin users
  if (!populatedData.branch_id && user.branch && 
      user.legacyRole !== 'institute_admin' && user.role !== 'institute_admin') {
    populatedData.branch_id = user.branch
  }

  // Auto-populate created_by if not set
  if (!populatedData.created_by && user.id) {
    populatedData.created_by = user.id
  }

  return populatedData
}

/**
 * Validate tenant consistency on document updates
 */
export const validateTenantConsistency = (user: AuthenticatedUser, existingData: any, updateData: any): boolean => {
  // Super admin can modify tenant fields
  if (user.legacyRole === 'super_admin' || user.role === 'super_admin') {
    return true
  }

  // Prevent changing institute_id
  if (updateData.institute_id && updateData.institute_id !== existingData.institute_id) {
    return false
  }

  // Prevent non-institute-admin from changing branch_id to different branch
  if (updateData.branch_id && 
      user.legacyRole !== 'institute_admin' && user.role !== 'institute_admin' &&
      updateData.branch_id !== user.branch) {
    return false
  }

  return true
}

/**
 * Create tenant-aware pagination query
 */
export const createTenantPaginationQuery = (
  user: AuthenticatedUser,
  baseQuery: any = {},
  isolationType: 'institute' | 'branch' | 'user' = 'institute'
) => {
  let tenantFilter = {}

  switch (isolationType) {
    case 'institute':
      tenantFilter = createInstituteFilter(user)
      break
    case 'branch':
      tenantFilter = createBranchFilter(user)
      break
    case 'user':
      tenantFilter = createUserFilter(user)
      break
  }

  // Combine base query with tenant filter
  if (Object.keys(tenantFilter).length === 0) {
    return baseQuery // No tenant filter for super admin
  }

  if (Object.keys(baseQuery).length === 0) {
    return tenantFilter // Only tenant filter
  }

  // Combine both filters
  return {
    and: [
      tenantFilter,
      baseQuery
    ]
  }
}

/**
 * File storage tenant isolation
 */
export const getTenantStoragePath = (user: AuthenticatedUser, filename: string): string => {
  const instituteId = user.institute
  const branchId = user.branch
  
  if (branchId) {
    return `institutes/${instituteId}/branches/${branchId}/${filename}`
  }
  
  return `institutes/${instituteId}/${filename}`
}

/**
 * Validate file access based on tenant
 */
export const validateFileAccess = (user: AuthenticatedUser, filePath: string): boolean => {
  // Super admin can access all files
  if (user.legacyRole === 'super_admin' || user.role === 'super_admin') {
    return true
  }

  const instituteId = user.institute
  const branchId = user.branch

  // Check if file belongs to user's institute
  if (!filePath.includes(`institutes/${instituteId}`)) {
    return false
  }

  // For non-institute-admin users, check branch access
  if (user.legacyRole !== 'institute_admin' && user.role !== 'institute_admin') {
    if (branchId && !filePath.includes(`branches/${branchId}`)) {
      return false
    }
  }

  return true
}

// Extend PayloadRequest interface to include tenant context
declare global {
  namespace Express {
    interface Request {
      tenantContext?: TenantContext
      tenantFilter?: any
    }
  }
}

export default {
  extractTenantContext,
  createInstituteFilter,
  createBranchFilter,
  createUserFilter,
  tenantIsolation,
  validateTenantOwnership,
  requireTenantOwnership,
  autoPopulateTenantFields,
  validateTenantConsistency,
  createTenantPaginationQuery,
  getTenantStoragePath,
  validateFileAccess
}
