import { StorageService, ImageSize } from '../services/storage-service'

console.log('🔥 Upload Middleware loaded - handles file uploads with flexible storage!')

// Avatar specific image sizes
export const AVATAR_SIZES: ImageSize[] = [
  { name: 'avatar_small', width: 40, height: 40, quality: 85 },
  { name: 'avatar_medium', width: 80, height: 80, quality: 85 },
  { name: 'avatar_large', width: 150, height: 150, quality: 90 },
  { name: 'profile', width: 300, height: 300, quality: 90 },
]

// Course thumbnail sizes
export const COURSE_THUMBNAIL_SIZES: ImageSize[] = [
  { name: 'thumbnail', width: 400, height: 300, quality: 80 },
  { name: 'card', width: 768, height: 432, quality: 85 },
  { name: 'hero', width: 1200, height: 675, quality: 90 },
]

// Institute logo sizes
export const LOGO_SIZES: ImageSize[] = [
  { name: 'logo_small', width: 100, height: 100, quality: 90 },
  { name: 'logo_medium', width: 200, height: 200, quality: 90 },
  { name: 'logo_large', width: 400, height: 400, quality: 95 },
]

// Favicon sizes
export const FAVICON_SIZES: ImageSize[] = [
  { name: 'favicon_16', width: 16, height: 16, quality: 95 },
  { name: 'favicon_32', width: 32, height: 32, quality: 95 },
  { name: 'favicon_48', width: 48, height: 48, quality: 95 },
]

export interface UploadOptions {
  mediaType?: string
  folder?: string
  generateSizes?: ImageSize[]
  maxFileSize?: number
  allowedMimeTypes?: string[]
}

export class UploadMiddleware {
  private storageService: StorageService

  constructor(storageService: StorageService) {
    this.storageService = storageService
    console.log('📤 Upload Middleware initialized')
  }

  // Create upload middleware instance
  static async create(payload: any): Promise<UploadMiddleware> {
    console.log('🔧 Creating Upload Middleware instance...')
    
    const storageConfig = await StorageService.getStorageConfig(payload)
    const storageService = new StorageService(storageConfig)
    
    return new UploadMiddleware(storageService)
  }

  // Handle file upload from request
  async handleFileUpload(
    req: any,
    options: UploadOptions = {},
    file?: File // Accept pre-parsed file to avoid double parsing
  ): Promise<{
    success: boolean
    media?: any
    message?: string
  }> {
    console.log('🚀🚀🚀 UPLOAD MIDDLEWARE CALLED! 🚀🚀🚀')
    console.log('📥 Handling file upload...', {
      mediaType: options.mediaType,
      folder: options.folder,
      generateSizes: options.generateSizes?.length || 0
    })
    console.log('🔧 This middleware should return clean URLs without domain!')

    try {
      // Use provided file or parse from request
      let uploadFile = file

      if (!uploadFile) {
        // Parse multipart form data only if file not provided
        const formData = await req.formData()
        uploadFile = formData.get('file') as File
      }

      if (!uploadFile) {
        console.log('❌ No file provided in request')
        return {
          success: false,
          message: 'No file provided'
        }
      }

      console.log('📁 File received:', {
        name: uploadFile.name,
        size: uploadFile.size,
        type: uploadFile.type
      })

      // Validate file
      const validation = this.validateFile(uploadFile, options)
      if (!validation.valid) {
        console.log('❌ File validation failed:', validation.message)
        return {
          success: false,
          message: validation.message
        }
      }

      // Convert File to Buffer
      const arrayBuffer = await uploadFile.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)

      // Determine image sizes based on media type
      let generateSizes = options.generateSizes
      if (!generateSizes && uploadFile.type.startsWith('image/')) {
        generateSizes = this.getImageSizesForMediaType(options.mediaType)
      }

      console.log('🔄 Uploading file with storage service...', {
        generateSizes: generateSizes?.map(s => s.name) || []
      })

      // Upload file using storage service
      const uploadResult = await this.storageService.uploadFile(
        buffer,
        uploadFile.name,
        uploadFile.type,
        {
          folder: options.folder,
          generateSizes,
          mediaType: options.mediaType
        }
      )

      console.log('📊 Upload result:', {
        id: uploadResult.id,
        url: uploadResult.url,
        sizes: Object.keys(uploadResult.sizes || {})
      })

      // Create media record in database
      console.log('💾 Creating media record in database...')

      let media: any

      // Prepare media data outside try block so it's accessible in catch
      const mediaData = {
        filename: uploadResult.filename,
        url: uploadResult.url, // Storage service will provide correct path
        alt: this.generateAltText(uploadFile.name, options.mediaType),
        caption: `Uploaded ${uploadFile.name}`,
        mediaType: options.mediaType || 'other',
        mimeType: uploadResult.mimeType,
        filesize: uploadResult.size,
        width: uploadResult.metadata.width || null,
        height: uploadResult.metadata.height || null,
        uploadedBy: req.user?.id,
      }

      try {
        // Create actual media record in database
        console.log('💾 Creating media record in database...')

        console.log('📝 Media data to create:', mediaData)
        console.log('📝 Payload instance available:', !!req.payload)
        console.log('📝 User context:', req.user?.id)

        // Create media record directly in database bypassing Payload's file handling
        // Since we already handled file upload through storage service
        const createdMedia = await req.payload.db.create({
          collection: 'media',
          data: mediaData,
        })

        console.log('✅ Media record created in database:', {
          id: createdMedia.id,
          filename: createdMedia.filename,
          url: createdMedia.url,
          mediaType: createdMedia.mediaType
        })

        console.log('🔍 Full created media object:', createdMedia)

        // Use the created media record
        media = createdMedia

      } catch (dbError: any) {
        console.error('❌ Failed to create media record via db.create:', dbError)

        // Try alternative approach - direct database insertion
        try {
          console.log('🔄 Trying alternative database insertion...')

          // Create media record with a simple approach
          const mediaRecord = {
            ...mediaData,
            id: uploadResult.id, // Use storage service ID
            createdAt: new Date(),
            updatedAt: new Date(),
          }

          // Use raw database query if available
          if (req.payload.db.drizzle && req.payload.db.tables?.media) {
            const [insertedMedia] = await req.payload.db.drizzle
              .insert(req.payload.db.tables.media)
              .values(mediaRecord)
              .returning()

            media = insertedMedia
            console.log('✅ Media record created via raw query:', media.id)
          } else {
            // Final fallback - but mark it clearly
            console.log('⚠️ Using fallback media object - database insertion failed')
            media = mediaRecord
          }

        } catch (fallbackError: any) {
          console.error('❌ All database creation methods failed:', fallbackError)

          // Use temporary object but log the failure
          media = {
            id: uploadResult.id,
            filename: uploadResult.filename,
            url: uploadResult.url,
            alt: this.generateAltText(uploadFile.name, options.mediaType),
            mediaType: options.mediaType || 'other',
            mimeType: uploadResult.mimeType,
            filesize: uploadResult.size,
            width: uploadResult.metadata.width,
            height: uploadResult.metadata.height,
            uploadedBy: req.user?.id,
          }
          console.log('⚠️ Using temporary media object due to database errors')
        }
      }

      // Update the URL to remove /api/ prefix
      const cleanUrl = uploadResult.url // This is already /media/... from storage service

      // Since we're using a fallback media object, we don't need to update the database
      // The file is already saved and we have all the information we need
      console.log('💾 File upload completed, using fallback media object with clean URL:', cleanUrl)

      // Note: uploadResult.sizes should already have clean URLs from storage service

      console.log('✅ File upload completed successfully:', {
        mediaId: media.id,
        filename: media.filename,
        payloadUrl: media.url,
        cleanUrl: cleanUrl
      })

      // Create the final media object with ALL URLs cleaned
      // Use uploadResult URLs (which are clean) instead of Payload's URLs
      const finalMedia: any = {
        ...media,
        url: uploadResult.url, // Use clean URL from storage service
        sizes: uploadResult.sizes || {} // Use clean sizes from storage service
      }

      // Add thumbnailURL if it exists in uploadResult
      if ((uploadResult as any).thumbnailURL) {
        finalMedia.thumbnailURL = (uploadResult as any).thumbnailURL
      }

      console.log('🔧 Using clean URLs from storage service:', {
        storageUrl: uploadResult.url,
        payloadUrl: media.url,
        storageSizes: Object.keys(uploadResult.sizes || {}).length,
        hasPayloadSizes: !!(media as any).sizes
      })

      // Enhanced URL cleaning: Handle both full URLs and relative URLs
      const cleanUrlFunction = (url: string): string => {
        if (!url) return url

        // Handle full URLs like http://localhost:3001/api/media/file/filename.jpg
        if (url.includes('://')) {
          const cleaned = url.replace(/^https?:\/\/[^\/]+\/api\//, '/')
          console.log('🔧 Cleaned full URL:', url, '→', cleaned)
          return cleaned
        }

        // Handle relative URLs like /api/media/file/filename.jpg
        if (url.includes('/api/')) {
          const cleaned = url.replace(/^\/api\//, '/')
          console.log('🔧 Cleaned relative URL:', url, '→', cleaned)
          return cleaned
        }

        return url
      }

      // Clean main URL
      if (finalMedia.url) {
        finalMedia.url = cleanUrlFunction(finalMedia.url)
      }

      // Clean thumbnail URL
      if (finalMedia.thumbnailURL) {
        finalMedia.thumbnailURL = cleanUrlFunction(finalMedia.thumbnailURL)
      }

      // Clean sizes URLs
      if (finalMedia.sizes) {
        Object.keys(finalMedia.sizes).forEach(sizeKey => {
          const size = finalMedia.sizes[sizeKey]
          if (size && size.url) {
            size.url = cleanUrlFunction(size.url)
          }
        })
      }

      console.log('🎯 Final media object URLs:', {
        url: finalMedia.url,
        thumbnailURL: finalMedia.thumbnailURL || 'none',
        sizes: finalMedia.sizes ? Object.keys(finalMedia.sizes).map(key => ({
          [key]: finalMedia.sizes[key]?.url || 'none'
        })) : 'none'
      })

      console.log('🎯🎯🎯 FINAL RESPONSE FROM UPLOAD MIDDLEWARE:')
      console.log('📋 Final media object being returned:', JSON.stringify(finalMedia, null, 2))
      console.log('🔍 URL check:', {
        url: finalMedia.url,
        hasApiPrefix: finalMedia.url?.includes('/api/'),
        hasDomain: finalMedia.url?.includes('://'),
        thumbnailURL: finalMedia.thumbnailURL,
        sizesCount: Object.keys(finalMedia.sizes || {}).length
      })

      return {
        success: true,
        media: finalMedia
      }

    } catch (error) {
      console.error('💥 File upload error:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  // Validate uploaded file
  private validateFile(file: File, options: UploadOptions): { valid: boolean; message?: string } {
    // Check file size
    const maxSize = options.maxFileSize || 5 * 1024 * 1024 // 5MB default
    if (file.size > maxSize) {
      return {
        valid: false,
        message: `File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`
      }
    }

    // Check MIME type
    const allowedTypes = options.allowedMimeTypes || [
      'image/jpeg',
      'image/png', 
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]

    const isAllowed = allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        return file.type.startsWith(type.slice(0, -1))
      }
      return file.type === type
    })

    if (!isAllowed) {
      return {
        valid: false,
        message: `File type ${file.type} is not allowed`
      }
    }

    return { valid: true }
  }

  // Get appropriate image sizes based on media type
  private getImageSizesForMediaType(mediaType?: string): ImageSize[] {
    switch (mediaType) {
      case 'user_avatar':
        return AVATAR_SIZES
      case 'course_thumbnail':
        return COURSE_THUMBNAIL_SIZES
      case 'institute_logo':
        return LOGO_SIZES
      default:
        return COURSE_THUMBNAIL_SIZES // Default to course thumbnail sizes
    }
  }

  // Generate alt text for accessibility
  private generateAltText(filename: string, mediaType?: string): string {
    const baseName = filename.replace(/\.[^/.]+$/, '').replace(/[-_]/g, ' ')
    
    switch (mediaType) {
      case 'user_avatar':
        return `Avatar: ${baseName}`
      case 'course_thumbnail':
        return `Course thumbnail: ${baseName}`
      case 'institute_logo':
        return `Institute logo: ${baseName}`
      case 'blog_image':
        return `Blog image: ${baseName}`
      default:
        return baseName
    }
  }

  // Delete file and cleanup
  async deleteFile(media: any): Promise<void> {
    console.log('🗑️ Deleting file and cleanup...', {
      mediaId: media.id,
      filename: media.filename
    })

    try {
      // Delete main file
      if (media.url) {
        const filePath = media.url.replace('/media/', '')
        await this.storageService.deleteFile(filePath)
      }

      // Delete size variants if they exist
      if (media.customSizes) {
        for (const [sizeName, sizeData] of Object.entries(media.customSizes)) {
          try {
            const sizeFilePath = (sizeData as any).url?.replace('/media/', '')
            if (sizeFilePath) {
              await this.storageService.deleteFile(sizeFilePath)
            }
          } catch (error) {
            console.warn(`⚠️ Failed to delete size ${sizeName}:`, error)
          }
        }
      }

      console.log('✅ File cleanup completed')
    } catch (error) {
      console.error('❌ File cleanup error:', error)
      throw error
    }
  }
}

// Helper function to create upload middleware for endpoints
export async function createUploadMiddleware(payload: any): Promise<UploadMiddleware> {
  return await UploadMiddleware.create(payload)
}
