/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    institutes: Institute;
    themes: Theme;
    courses: Course;
    permissions: Permission;
    roles: Role;
    'user-permissions': UserPermission;
    'role-permissions': RolePermission;
    sessions: Session;
    settings: Setting;
    options: Option;
    'domain-requests': DomainRequest;
    countries: Country;
    states: State;
    districts: District;
    'tax-components': TaxComponent;
    'tax-groups': TaxGroup;
    'tax-rules': TaxRule;
    branches: Branch;
    'course-purchases': CoursePurchase;
    'payment-gateways': PaymentGateway;
    'institute-gateways': InstituteGateway;
    'institute-themes': InstituteTheme;
    audit_logs: AuditLog;
    'student-details': StudentDetail;
    'platform-blog-posts': PlatformBlogPost;
    'platform-blog-categories': PlatformBlogCategory;
    'blog-posts': BlogPost;
    'blog-categories': BlogCategory;
    'blog-comments': BlogComment;
    'blog-analytics': BlogAnalytic;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    institutes: InstitutesSelect<false> | InstitutesSelect<true>;
    themes: ThemesSelect<false> | ThemesSelect<true>;
    courses: CoursesSelect<false> | CoursesSelect<true>;
    permissions: PermissionsSelect<false> | PermissionsSelect<true>;
    roles: RolesSelect<false> | RolesSelect<true>;
    'user-permissions': UserPermissionsSelect<false> | UserPermissionsSelect<true>;
    'role-permissions': RolePermissionsSelect<false> | RolePermissionsSelect<true>;
    sessions: SessionsSelect<false> | SessionsSelect<true>;
    settings: SettingsSelect<false> | SettingsSelect<true>;
    options: OptionsSelect<false> | OptionsSelect<true>;
    'domain-requests': DomainRequestsSelect<false> | DomainRequestsSelect<true>;
    countries: CountriesSelect<false> | CountriesSelect<true>;
    states: StatesSelect<false> | StatesSelect<true>;
    districts: DistrictsSelect<false> | DistrictsSelect<true>;
    'tax-components': TaxComponentsSelect<false> | TaxComponentsSelect<true>;
    'tax-groups': TaxGroupsSelect<false> | TaxGroupsSelect<true>;
    'tax-rules': TaxRulesSelect<false> | TaxRulesSelect<true>;
    branches: BranchesSelect<false> | BranchesSelect<true>;
    'course-purchases': CoursePurchasesSelect<false> | CoursePurchasesSelect<true>;
    'payment-gateways': PaymentGatewaysSelect<false> | PaymentGatewaysSelect<true>;
    'institute-gateways': InstituteGatewaysSelect<false> | InstituteGatewaysSelect<true>;
    'institute-themes': InstituteThemesSelect<false> | InstituteThemesSelect<true>;
    audit_logs: AuditLogsSelect<false> | AuditLogsSelect<true>;
    'student-details': StudentDetailsSelect<false> | StudentDetailsSelect<true>;
    'platform-blog-posts': PlatformBlogPostsSelect<false> | PlatformBlogPostsSelect<true>;
    'platform-blog-categories': PlatformBlogCategoriesSelect<false> | PlatformBlogCategoriesSelect<true>;
    'blog-posts': BlogPostsSelect<false> | BlogPostsSelect<true>;
    'blog-categories': BlogCategoriesSelect<false> | BlogCategoriesSelect<true>;
    'blog-comments': BlogCommentsSelect<false> | BlogCommentsSelect<true>;
    'blog-analytics': BlogAnalyticsSelect<false> | BlogAnalyticsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  /**
   * Select a role for this user
   */
  role?: (number | null) | Role;
  /**
   * Legacy role field - currently used for access control
   */
  legacyRole:
    | 'super_admin'
    | 'platform_staff'
    | 'institute_admin'
    | 'branch_manager'
    | 'trainer'
    | 'institute_staff'
    | 'student';
  firstName: string;
  lastName: string;
  phone?: string | null;
  avatar?: (number | null) | Media;
  institute?: (number | null) | Institute;
  /**
   * Branch assignment for this user
   */
  branch?: (number | null) | Branch;
  /**
   * User active status - controls access to system
   */
  isActive?: boolean | null;
  emailVerified?: boolean | null;
  lastLogin?: string | null;
  lockedUntil?: string | null;
  /**
   * Physical address
   */
  address?: string | null;
  /**
   * Date of birth
   */
  dateOfBirth?: string | null;
  /**
   * Gender
   */
  gender?: ('male' | 'female' | 'other') | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "roles".
 */
export interface Role {
  id: number;
  name: string;
  code: string;
  description?: string | null;
  level: '1' | '2' | '3' | '4';
  permissions?:
    | {
        permission: number | Permission;
        scope?: ('platform' | 'institute' | 'branch' | 'self') | null;
        id?: string | null;
      }[]
    | null;
  scope?: {
    institute?: (number | null) | Institute;
    branch?: (number | null) | Branch;
  };
  /**
   * System roles cannot be deleted
   */
  isSystemRole?: boolean | null;
  isActive?: boolean | null;
  metadata?: {
    /**
     * Maximum number of users that can have this role (0 = unlimited)
     */
    maxUsers?: number | null;
    /**
     * Automatically assign this role to new users
     */
    autoAssign?: boolean | null;
    /**
     * Role assignment requires approval
     */
    requiresApproval?: boolean | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "permissions".
 */
export interface Permission {
  id: number;
  name: string;
  description?: string | null;
  code: string;
  category: 'platform' | 'institute' | 'branch' | 'users' | 'courses' | 'students' | 'billing' | 'reports' | 'settings';
  level: '1' | '2' | '3';
  /**
   * The resource this permission applies to (e.g., users, courses, institutes)
   */
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'manage' | 'view' | 'execute' | 'approve' | 'export' | 'import';
  /**
   * System permissions cannot be deleted and are required for core functionality
   */
  isSystemPermission?: boolean | null;
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "institutes".
 */
export interface Institute {
  id: number;
  name: string;
  slug: string;
  email?: string | null;
  phone?: string | null;
  website?: string | null;
  tagline?: string | null;
  logo?: (number | null) | Media;
  /**
   * Upload a favicon for your institute (recommended: 32x32 or 16x16 pixels, .ico, .png, or .svg format)
   */
  favicon?: (number | null) | Media;
  description?: string | null;
  addressStreet?: string | null;
  /**
   * Reference to city in location management system
   */
  cityId?: string | null;
  /**
   * Reference to state in location management system
   */
  stateId?: string | null;
  /**
   * Reference to country in location management system
   */
  countryId?: string | null;
  /**
   * Reference to district in location management system
   */
  districtId?: string | null;
  zipCode?: string | null;
  /**
   * Custom domain for the institute (e.g., abc-academy.com)
   */
  customDomain?: string | null;
  domainVerified?: boolean | null;
  isActive?: boolean | null;
  subscriptionPlan?: ('free_trial' | 'basic' | 'professional' | 'enterprise') | null;
  subscriptionStatus?: ('active' | 'inactive' | 'suspended' | 'cancelled') | null;
  subscriptionExpiry?: string | null;
  maxStudents?: number | null;
  maxCourses?: number | null;
  maxBranches?: number | null;
  features?: {
    marketplace?: boolean | null;
    liveClasses?: boolean | null;
    exams?: boolean | null;
    blogs?: boolean | null;
    analytics?: boolean | null;
  };
  createdBy?: (number | null) | User;
  deletedAt?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  /**
   * Alternative text for accessibility and SEO
   */
  alt: string;
  /**
   * Caption or description for the media
   */
  caption?: string | null;
  /**
   * Type of media for organization and filtering
   */
  mediaType?:
    | (
        | 'course_thumbnail'
        | 'course_content'
        | 'theme_asset'
        | 'institute_logo'
        | 'user_avatar'
        | 'blog_image'
        | 'marketing'
        | 'document'
        | 'other'
      )
    | null;
  /**
   * User who uploaded this media
   */
  uploadedBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    card?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    hero?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    avatar_small?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    avatar_medium?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    avatar_large?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    profile?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "branches".
 */
export interface Branch {
  id: number;
  name: string;
  code: string;
  institute: number | Institute;
  location: {
    address: string;
    country: number | Country;
    state: number | State;
    district: number | District;
    pincode?: string | null;
    coordinates?: {
      latitude?: number | null;
      longitude?: number | null;
    };
  };
  contact?: {
    phone?: string | null;
    email?: string | null;
    website?: string | null;
  };
  taxInformation?: {
    gstNumber?: string | null;
    panNumber?: string | null;
    taxRegistrationNumber?: string | null;
    isGstRegistered?: boolean | null;
  };
  isActive?: boolean | null;
  /**
   * Mark as head office/main branch
   */
  isHeadOffice?: boolean | null;
  /**
   * Select the working days for this branch
   */
  workingDays?: {
    monday?: boolean | null;
    tuesday?: boolean | null;
    wednesday?: boolean | null;
    thursday?: boolean | null;
    friday?: boolean | null;
    saturday?: boolean | null;
    sunday?: boolean | null;
  };
  /**
   * Operating hours for this branch
   */
  operatingHours?: {
    /**
     * Opening time (e.g., 09:00)
     */
    openTime?: string | null;
    /**
     * Closing time (e.g., 18:00)
     */
    closeTime?: string | null;
  };
  /**
   * Soft delete flag - marks branch as deleted without removing from database
   */
  isDeleted?: boolean | null;
  /**
   * Timestamp when the branch was soft deleted
   */
  deletedAt?: string | null;
  /**
   * User who created this branch
   */
  createdBy?: (number | null) | User;
  /**
   * User who last updated this branch
   */
  updatedBy?: (number | null) | User;
  /**
   * User who deleted this branch
   */
  deletedBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "countries".
 */
export interface Country {
  id: number;
  name: string;
  code: string;
  flag?: (number | null) | Media;
  details?: {
    capital?: string | null;
    currency?: string | null;
    currencyCode?: string | null;
    language?: string | null;
    timezone?: string | null;
    population?: number | null;
    /**
     * Area in square kilometers
     */
    area?: number | null;
  };
  coordinates?: {
    latitude?: number | null;
    longitude?: number | null;
  };
  isActive?: boolean | null;
  /**
   * Higher priority countries appear first
   */
  priority?: number | null;
  /**
   * Additional country metadata
   */
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "states".
 */
export interface State {
  id: number;
  name: string;
  code?: string | null;
  country: number | Country;
  details?: {
    capital?: string | null;
    population?: number | null;
    /**
     * Area in square kilometers
     */
    area?: number | null;
    type?: ('state' | 'province' | 'territory' | 'region') | null;
  };
  coordinates?: {
    latitude?: number | null;
    longitude?: number | null;
  };
  isActive?: boolean | null;
  priority?: number | null;
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "districts".
 */
export interface District {
  id: number;
  name: string;
  code?: string | null;
  state: number | State;
  details: {
    type: 'district' | 'city' | 'municipality' | 'town' | 'village';
    population?: number | null;
    area?: number | null;
    pincode?: string | null;
  };
  coordinates?: {
    latitude?: number | null;
    longitude?: number | null;
  };
  isActive?: boolean | null;
  priority?: number | null;
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "themes".
 */
export interface Theme {
  id: number;
  /**
   * Display name of the theme
   */
  name: string;
  /**
   * URL-friendly identifier for the theme
   */
  slug: string;
  /**
   * Type of theme - Platform for main site, Institute for institute websites
   */
  type: 'platform' | 'institute';
  /**
   * Theme category for organization and filtering
   */
  category:
    | 'saas_modern'
    | 'saas_corporate'
    | 'saas_startup'
    | 'saas_minimal'
    | 'education_modern'
    | 'education_classic'
    | 'coaching_professional'
    | 'university_classic'
    | 'online_academy'
    | 'training_center';
  /**
   * Brief description of the theme and its features
   */
  description: string;
  /**
   * Theme version number
   */
  version: string;
  /**
   * Preview thumbnail (400x300px recommended)
   */
  previewImage?: (number | null) | Media;
  /**
   * Full demo screenshot (1200x800px recommended)
   */
  demoImage?: (number | null) | Media;
  /**
   * Additional screenshots showcasing theme features
   */
  screenshots?:
    | {
        image: number | Media;
        title: string;
        description?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Default color scheme for the theme
   */
  colors: {
    /**
     * Primary brand color (hex code)
     */
    primary: string;
    /**
     * Secondary color (hex code)
     */
    secondary: string;
    /**
     * Accent color for highlights (hex code)
     */
    accent: string;
    /**
     * Background color (hex code)
     */
    background: string;
    /**
     * Primary text color (hex code)
     */
    text: string;
    /**
     * Muted text color (hex code)
     */
    muted?: string | null;
    /**
     * Border color (hex code)
     */
    border?: string | null;
  };
  /**
   * Typography settings for the theme
   */
  fonts: {
    /**
     * Font family for headings
     */
    heading: string;
    /**
     * Font family for body text
     */
    body: string;
    /**
     * Monospace font for code
     */
    mono?: string | null;
  };
  /**
   * List of theme features and capabilities
   */
  features?:
    | {
        feature: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Types of institutes this theme is suitable for
   */
  suitableFor?:
    | {
        type?:
          | (
              | 'coaching_centers'
              | 'online_academies'
              | 'universities'
              | 'training_institutes'
              | 'skill_development'
              | 'professional_courses'
              | 'exam_preparation'
              | 'corporate_training'
            )
          | null;
        id?: string | null;
      }[]
    | null;
  /**
   * JSON configuration for customizable theme elements
   */
  customizableElements?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Number of institutes using this theme
   */
  usageCount?: number | null;
  rating?: {
    /**
     * Average rating from users
     */
    average?: number | null;
    /**
     * Total number of ratings
     */
    count?: number | null;
  };
  /**
   * Whether this theme is available for selection
   */
  isActive?: boolean | null;
  /**
   * Set as default theme for new institutes
   */
  isDefault?: boolean | null;
  /**
   * Feature this theme in the theme gallery
   */
  isFeatured?: boolean | null;
  /**
   * URL to live demo of the theme
   */
  demoUrl?: string | null;
  /**
   * URL to theme documentation
   */
  documentationUrl?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "courses".
 */
export interface Course {
  id: number;
  /**
   * Course title (max 100 characters)
   */
  title: string;
  /**
   * Course description (minimum 50 characters)
   */
  description: string;
  /**
   * Course thumbnail image
   */
  thumbnail?: (number | null) | Media;
  /**
   * Course pricing model
   */
  pricing_type: 'free' | 'one_time';
  /**
   * Course price (only for paid courses)
   */
  price_amount?: number | null;
  /**
   * Discount percentage (0-100%)
   */
  discount_percentage?: number | null;
  /**
   * Final price after discount (calculated automatically)
   */
  final_price?: number | null;
  /**
   * Course publication status
   */
  status: 'draft' | 'published' | 'archived';
  /**
   * Institute this course belongs to
   */
  institute: number | Institute;
  /**
   * Branch this course belongs to
   */
  branch: number | Branch;
  /**
   * User who created this course
   */
  created_by: number | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-permissions".
 */
export interface UserPermission {
  id: number;
  user: number | User;
  permission: number | Permission;
  type: 'grant' | 'revoke' | 'override';
  scope: 'platform' | 'institute' | 'branch' | 'self';
  scopeTarget?: {
    institute?: (number | null) | Institute;
    branch?: (number | null) | Branch;
  };
  status: 'pending' | 'approved' | 'rejected' | 'revoked';
  approvalWorkflow: {
    requestedBy: number | User;
    approvedBy?: (number | null) | User;
    rejectedBy?: (number | null) | User;
    approvalDate?: string | null;
    rejectionDate?: string | null;
    /**
     * Reason for approval/rejection
     */
    reason?: string | null;
  };
  effectivePeriod?: {
    startDate?: string | null;
    /**
     * Leave empty for permanent permission
     */
    endDate?: string | null;
  };
  isActive?: boolean | null;
  metadata?: {
    /**
     * Permission was automatically approved
     */
    autoApproved?: boolean | null;
    /**
     * Role this permission was inherited from
     */
    inheritedFromRole?: (number | null) | Role;
    notes?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "role-permissions".
 */
export interface RolePermission {
  id: number;
  /**
   * The role to assign permissions to
   */
  role: number | Role;
  /**
   * The permission to assign to the role
   */
  permission: number | Permission;
  /**
   * Whether this role-permission assignment is active
   */
  isActive?: boolean | null;
  /**
   * User who assigned this permission to the role
   */
  assignedBy?: (number | null) | User;
  /**
   * When this permission was assigned to the role
   */
  assignedAt?: string | null;
  /**
   * Optional notes about this role-permission assignment
   */
  notes?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sessions".
 */
export interface Session {
  id: number;
  user: number | User;
  sessionToken: string;
  deviceInfo: {
    deviceType: 'desktop' | 'mobile' | 'tablet';
    deviceName: string;
    browser: string;
    operatingSystem: string;
    userAgent?: string | null;
  };
  location: {
    ipAddress: string;
    city?: string | null;
    region?: string | null;
    country?: string | null;
    latitude?: number | null;
    longitude?: number | null;
  };
  security?: {
    isSecure?: boolean | null;
    isCurrent?: boolean | null;
    loginMethod?: ('password' | '2fa' | 'social' | 'sso') | null;
  };
  isActive?: boolean | null;
  lastActivity: string;
  expiresAt: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "settings".
 */
export interface Setting {
  id: number;
  key: string;
  value:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  category:
    | 'platform'
    | 'security'
    | 'email'
    | 'storage'
    | 'billing'
    | 'theme'
    | 'institute'
    | 'course'
    | 'user'
    | 'notification';
  scope: 'global' | 'institute' | 'branch';
  dataType: 'string' | 'number' | 'boolean' | 'json' | 'array';
  description?: string | null;
  institute?: (number | null) | Institute;
  /**
   * Whether this setting can be accessed by non-authenticated users
   */
  isPublic?: boolean | null;
  /**
   * Whether this setting can be modified through the UI
   */
  isEditable?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Manage platform-wide configuration settings
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "options".
 */
export interface Option {
  id: number;
  /**
   * Unique setting identifier (e.g., platform_name, support_email)
   */
  key: string;
  /**
   * Setting value (can be string, number, boolean, or JSON)
   */
  value: string;
  /**
   * Human-readable description of this setting
   */
  description?: string | null;
  /**
   * Category to group related settings
   */
  category: 'platform' | 'email' | 'security' | 'storage' | 'payment' | 'notification' | 'integration' | 'feature';
  /**
   * Data type for validation and UI rendering
   */
  type: 'string' | 'number' | 'boolean' | 'json' | 'url' | 'email' | 'textarea' | 'upload';
  /**
   * Whether this setting can be accessed by non-admin users
   */
  is_public?: boolean | null;
  /**
   * Whether this setting is required for platform operation
   */
  is_required?: boolean | null;
  /**
   * Upload file for this setting (only used when type is "upload")
   */
  upload?: (number | null) | Media;
  /**
   * Validation rules for this setting
   */
  validation_rules?: {
    /**
     * Minimum length for string values
     */
    min_length?: number | null;
    /**
     * Maximum length for string values
     */
    max_length?: number | null;
    /**
     * Minimum value for number types
     */
    min_value?: number | null;
    /**
     * Maximum value for number types
     */
    max_value?: number | null;
    /**
     * Regex pattern for validation
     */
    pattern?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "domain-requests".
 */
export interface DomainRequest {
  id: number;
  institute: number | Institute;
  requestedDomain: string;
  currentDomain?: string | null;
  status: 'pending' | 'approved' | 'active' | 'rejected';
  purpose: string;
  notes?: string | null;
  requestedBy: number | User;
  requestedAt: string;
  reviewedBy?: (number | null) | User;
  reviewedAt?: string | null;
  rejectionReason?: string | null;
  sslStatus?: ('pending' | 'active' | 'failed') | null;
  dnsRecords?:
    | {
        type: 'A' | 'CNAME' | 'TXT';
        name: string;
        value: string;
        ttl: number;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-components".
 */
export interface TaxComponent {
  id: number;
  name: string;
  code: string;
  description?: string | null;
  type: 'sgst' | 'cgst' | 'igst' | 'vat' | 'sales_tax' | 'income_tax' | 'service_tax' | 'custom';
  /**
   * Tax rate as percentage (e.g., 9 for 9%)
   */
  rate: number;
  applicableRegions?:
    | {
        country: number | Country;
        states?: (number | State)[] | null;
        /**
         * Default tax for this region
         */
        isDefault?: boolean | null;
        id?: string | null;
      }[]
    | null;
  calculationMethod: 'percentage' | 'fixed' | 'tiered';
  tieredRates?:
    | {
        minAmount: number;
        maxAmount?: number | null;
        rate: number;
        id?: string | null;
      }[]
    | null;
  isActive?: boolean | null;
  effectiveFrom: string;
  effectiveTo?: string | null;
  /**
   * Higher priority taxes are applied first
   */
  priority?: number | null;
  /**
   * Additional tax metadata and configuration
   */
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-groups".
 */
export interface TaxGroup {
  id: number;
  name: string;
  code: string;
  description?: string | null;
  taxComponents: {
    component: number | TaxComponent;
    /**
     * Override component rate (optional)
     */
    customRate?: number | null;
    isOptional?: boolean | null;
    id?: string | null;
  }[];
  /**
   * Calculated total rate of all components
   */
  totalRate?: number | null;
  applicableScenarios?:
    | {
        scenario: 'intra_state' | 'inter_state' | 'international' | 'b2b' | 'b2c' | 'export' | 'import';
        fromCountry?: (number | null) | Country;
        toCountry?: (number | null) | Country;
        fromState?: (number | null) | State;
        toState?: (number | null) | State;
        id?: string | null;
      }[]
    | null;
  isActive?: boolean | null;
  /**
   * Default tax group for applicable scenarios
   */
  isDefault?: boolean | null;
  effectiveFrom: string;
  effectiveTo?: string | null;
  priority?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-rules".
 */
export interface TaxRule {
  id: number;
  name: string;
  description?: string | null;
  taxGroup: number | TaxGroup;
  conditions?: {
    transactionType?:
      | ('course_purchase' | 'subscription' | 'certification' | 'live_class' | 'exam_fee' | 'material_purchase')
      | null;
    customerType?: ('individual' | 'business' | 'educational' | 'government') | null;
    /**
     * Minimum transaction amount for this rule
     */
    minAmount?: number | null;
    /**
     * Maximum transaction amount for this rule
     */
    maxAmount?: number | null;
    /**
     * Customer location country
     */
    customerCountry?: (number | null) | Country;
    /**
     * Customer location state
     */
    customerState?: (number | null) | State;
    /**
     * Institute location country
     */
    instituteCountry?: (number | null) | Country;
    /**
     * Institute location state
     */
    instituteState?: (number | null) | State;
    /**
     * Location-based tax scenario
     */
    locationScenario?: ('same_state' | 'different_state' | 'different_country') | null;
    /**
     * Date from which this condition is effective
     */
    conditionEffectiveFrom?: string | null;
    /**
     * Date until which this condition is effective
     */
    conditionEffectiveTo?: string | null;
    /**
     * Days of week when this rule applies
     */
    applicableDays?: ('1' | '2' | '3' | '4' | '5' | '6' | '0')[] | null;
  };
  exemptions?:
    | {
        condition:
          | 'student_discount'
          | 'educational_exemption'
          | 'government_exemption'
          | 'export_exemption'
          | 'threshold_exemption'
          | 'special_category';
        /**
         * Percentage of tax to exempt (100 = full exemption)
         */
        exemptionPercentage?: number | null;
        thresholdAmount?: number | null;
        id?: string | null;
      }[]
    | null;
  isActive?: boolean | null;
  /**
   * Higher priority rules are evaluated first
   */
  priority: number;
  effectiveFrom: string;
  effectiveTo?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "course-purchases".
 */
export interface CoursePurchase {
  id: number;
  /**
   * Student who made the purchase
   */
  student: number | User;
  /**
   * Course that was purchased
   */
  course: number | Course;
  /**
   * Branch where the purchase was made
   */
  branch: number | Branch;
  purchaseDetails: {
    /**
     * Original course price
     */
    originalPrice: number;
    /**
     * Discount amount applied
     */
    discountAmount?: number | null;
    /**
     * Final amount paid
     */
    finalAmount: number;
    currency: 'INR' | 'USD' | 'EUR';
  };
  /**
   * Date of purchase
   */
  purchaseDate: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payment-gateways".
 */
export interface PaymentGateway {
  id: number;
  /**
   * Payment gateway name (e.g., Razorpay, Stripe, PayPal)
   */
  name: string;
  /**
   * Description of the payment gateway
   */
  description?: string | null;
  /**
   * List of supported currency codes (e.g., ["INR", "USD", "EUR"])
   */
  supportedCurrencies:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * List of supported payment methods (e.g., ["credit_card", "debit_card", "upi"])
   */
  supportedMethods?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * List of supported country codes (e.g., ["IN", "US", "GB"]) - optional
   */
  supportedCountries?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Required configuration fields that institutes must provide
   */
  requiredConfigFields?:
    | {
        /**
         * Field identifier (e.g., "api_key", "secret_key")
         */
        key: string;
        /**
         * Human-readable label (e.g., "API Key", "Secret Key")
         */
        label: string;
        /**
         * Input type for the field
         */
        type: 'text' | 'password' | 'url' | 'email' | 'number' | 'boolean';
        /**
         * Example text for the field
         */
        placeholder?: string | null;
        /**
         * Help text explaining the field purpose
         */
        description?: string | null;
        /**
         * Whether this field is mandatory for institutes
         */
        isRequired?: boolean | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Optional configuration fields that institutes can provide
   */
  optionalConfigFields?:
    | {
        /**
         * Field identifier (e.g., "theme_color", "webhook_url")
         */
        key: string;
        /**
         * Human-readable label (e.g., "Theme Color", "Webhook URL")
         */
        label: string;
        /**
         * Input type for the field
         */
        type: 'text' | 'password' | 'url' | 'email' | 'number' | 'boolean';
        /**
         * Example text for the field
         */
        placeholder?: string | null;
        /**
         * Help text explaining the field purpose
         */
        description?: string | null;
        /**
         * Whether this field is mandatory for institutes
         */
        isRequired?: boolean | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Link to gateway integration documentation
   */
  documentationUrl?: string | null;
  /**
   * API version supported by this gateway
   */
  apiVersion?: string | null;
  /**
   * Whether this gateway supports webhooks
   */
  webhookSupport?: boolean | null;
  /**
   * URL to gateway logo image
   */
  logoUrl?: string | null;
  isActive?: boolean | null;
  /**
   * Show as featured gateway in institute selection
   */
  isFeatured?: boolean | null;
  /**
   * Super Admin who added this gateway
   */
  createdBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * Institute-specific payment gateway configurations
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "institute-gateways".
 */
export interface InstituteGateway {
  id: number;
  /**
   * Institute that owns this gateway configuration
   */
  institute: number | Institute;
  /**
   * Payment gateway being configured
   */
  gateway: number | PaymentGateway;
  /**
   * Gateway-specific configuration values (API keys, secrets, etc.)
   */
  configuration:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Whether this gateway is active for the institute
   */
  isActive?: boolean | null;
  /**
   * Whether this gateway is in test/sandbox mode
   */
  testMode?: boolean | null;
  /**
   * User who configured this gateway
   */
  configuredBy?: (number | null) | User;
  /**
   * Last time this gateway configuration was tested
   */
  lastTestedAt?: string | null;
  /**
   * Set as primary/default payment gateway for this institute
   */
  isPrimary?: boolean | null;
  /**
   * Priority for gateway selection (lower numbers = higher priority)
   */
  priorityOrder?: number | null;
  /**
   * Internal notes about this gateway configuration
   */
  notes?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Manages theme assignments for institutes
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "institute-themes".
 */
export interface InstituteTheme {
  id: number;
  /**
   * The institute this theme is assigned to
   */
  institute: number | Institute;
  /**
   * The theme assigned to the institute
   */
  theme: number | Theme;
  /**
   * Whether this theme assignment is currently active
   */
  isActive?: boolean | null;
  /**
   * Custom colors, fonts, and content overrides for this theme
   */
  customizations?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * When this theme was applied to the institute
   */
  appliedAt?: string | null;
  /**
   * User who applied this theme
   */
  appliedBy?: (number | null) | User;
  /**
   * The theme that was active before this one
   */
  previousTheme?: (number | null) | Theme;
  /**
   * Optional notes about this theme assignment
   */
  notes?: string | null;
  createdBy?: (number | null) | User;
  updatedBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "audit_logs".
 */
export interface AuditLog {
  id: number;
  /**
   * User who performed the action
   */
  userId: number | User;
  /**
   * Type of action performed
   */
  action:
    | 'STUDENT_CREATED'
    | 'STUDENT_UPDATED'
    | 'STUDENT_ACTIVATED'
    | 'STUDENT_DEACTIVATED'
    | 'STUDENT_DELETED'
    | 'STUDENT_STATUS_CHANGED'
    | 'BULK_STUDENT_UPDATE'
    | 'MIGRATION_COMPLETED'
    | 'SYSTEM_ACTION';
  /**
   * Type of target entity
   */
  targetType: 'student' | 'user' | 'institute' | 'branch' | 'role' | 'system';
  /**
   * ID of the target entity
   */
  targetId: string;
  /**
   * Reason for the action (optional)
   */
  reason?: string | null;
  /**
   * Additional metadata about the action
   */
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * IP address of the user who performed the action
   */
  ipAddress?: string | null;
  /**
   * User agent of the client
   */
  userAgent?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Additional details for students including location, education, and personal information
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "student-details".
 */
export interface StudentDetail {
  id: number;
  /**
   * Reference to the student user
   */
  student: number | User;
  /**
   * Country of residence
   */
  country?: (number | null) | Country;
  /**
   * State/Province of residence
   */
  state?: (number | null) | State;
  /**
   * District/City of residence
   */
  district?: (number | null) | District;
  education?: {
    /**
     * Highest educational qualification
     */
    highestQualification?: ('high_school' | 'diploma' | 'bachelors' | 'masters' | 'phd' | 'other') | null;
    /**
     * Name of the educational institution
     */
    institution?: string | null;
    /**
     * Field of study or specialization
     */
    fieldOfStudy?: string | null;
    /**
     * Year of graduation
     */
    graduationYear?: number | null;
    /**
     * Percentage or CGPA
     */
    percentage?: number | null;
  };
  personalInfo?: {
    /**
     * Father's name
     */
    fatherName?: string | null;
    /**
     * Mother's name
     */
    motherName?: string | null;
    /**
     * Guardian's name (if different from parents)
     */
    guardianName?: string | null;
    /**
     * Emergency contact number
     */
    emergencyContact?: string | null;
    /**
     * Blood group
     */
    bloodGroup?:
      | (
          | 'a_positive'
          | 'a_negative'
          | 'b_positive'
          | 'b_negative'
          | 'ab_positive'
          | 'ab_negative'
          | 'o_positive'
          | 'o_negative'
        )
      | null;
    /**
     * Nationality
     */
    nationality?: string | null;
    /**
     * Religion
     */
    religion?: string | null;
    /**
     * Caste/Category
     */
    caste?: string | null;
  };
  documents?: {
    /**
     * Aadhar card number
     */
    aadharNumber?: string | null;
    /**
     * PAN card number
     */
    panNumber?: string | null;
    /**
     * Passport number
     */
    passportNumber?: string | null;
    /**
     * Driving license number
     */
    drivingLicense?: string | null;
  };
  additionalInfo?: {
    /**
     * Hobbies and interests
     */
    hobbies?: string | null;
    /**
     * Skills and competencies
     */
    skills?: string | null;
    /**
     * Work experience or internships
     */
    experience?: string | null;
    /**
     * Career goals and aspirations
     */
    goals?: string | null;
    /**
     * Additional notes
     */
    notes?: string | null;
  };
  /**
   * Whether this student detail record is active
   */
  isActive?: boolean | null;
  /**
   * User who created this record
   */
  createdBy?: (number | null) | User;
  /**
   * User who last updated this record
   */
  updatedBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "platform-blog-posts".
 */
export interface PlatformBlogPost {
  id: number;
  title: string;
  /**
   * URL-friendly version of the title
   */
  slug: string;
  /**
   * Brief summary for previews
   */
  excerpt?: string | null;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  featuredImage?: (number | null) | Media;
  status: 'draft' | 'scheduled' | 'published' | 'archived';
  publishedAt?: string | null;
  scheduledFor?: string | null;
  category?: (number | null) | PlatformBlogCategory;
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Mark as platform announcement
   */
  isAnnouncement?: boolean | null;
  announcementPriority?: ('low' | 'medium' | 'high' | 'critical') | null;
  targetAudience?: ('institute_admin' | 'student' | 'staff' | 'public')[] | null;
  seo?: {
    title?: string | null;
    description?: string | null;
    keywords?:
      | {
          keyword?: string | null;
          id?: string | null;
        }[]
      | null;
    canonicalUrl?: string | null;
  };
  analytics?: {
    viewCount?: number | null;
    uniqueViewCount?: number | null;
    likeCount?: number | null;
    shareCount?: number | null;
    /**
     * Estimated reading time in minutes
     */
    readingTime?: number | null;
    instituteAdminViews?: number | null;
    studentViews?: number | null;
    staffViews?: number | null;
    publicViews?: number | null;
  };
  settings?: {
    allowComments?: boolean | null;
    isFeatured?: boolean | null;
    /**
     * Pin to top of blog list
     */
    isSticky?: boolean | null;
    /**
     * Show on platform dashboard
     */
    showOnDashboard?: boolean | null;
  };
  author: number | User;
  lastEditedBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "platform-blog-categories".
 */
export interface PlatformBlogCategory {
  id: number;
  name: string;
  slug: string;
  description?: string | null;
  /**
   * Hex color code for category display
   */
  color?: string | null;
  /**
   * Icon name for category display
   */
  icon?: string | null;
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-posts".
 */
export interface BlogPost {
  id: number;
  institute?: (number | null) | Institute;
  title: string;
  /**
   * URL-friendly version of the title
   */
  slug: string;
  /**
   * Brief summary of the post (used in previews)
   */
  excerpt?: string | null;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Main image for the blog post
   */
  featuredImage?: (number | null) | Media;
  status: 'draft' | 'scheduled' | 'published' | 'archived';
  publishedAt?: string | null;
  scheduledFor?: string | null;
  category?: (number | null) | BlogCategory;
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  seo?: {
    /**
     * SEO title (max 150 characters)
     */
    title?: string | null;
    /**
     * SEO description (max 300 characters)
     */
    description?: string | null;
    keywords?:
      | {
          keyword?: string | null;
          id?: string | null;
        }[]
      | null;
  };
  settings?: {
    allowComments?: boolean | null;
    /**
     * Show in featured posts section
     */
    isFeatured?: boolean | null;
    /**
     * Pin to top of blog list
     */
    isSticky?: boolean | null;
  };
  analytics?: {
    viewCount?: number | null;
    likeCount?: number | null;
    commentCount?: number | null;
    /**
     * Estimated reading time in minutes
     */
    readingTime?: number | null;
  };
  author: number | User;
  lastEditedBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-categories".
 */
export interface BlogCategory {
  id: number;
  institute?: (number | null) | Institute;
  name: string;
  /**
   * URL-friendly version of the name
   */
  slug: string;
  description?: string | null;
  /**
   * Hex color code for category display (e.g., #3B82F6)
   */
  color?: string | null;
  /**
   * Icon class name (e.g., lucide icon names like "book", "graduation-cap")
   */
  icon?: string | null;
  /**
   * Parent category for hierarchical organization
   */
  parentCategory?: (number | null) | BlogCategory;
  /**
   * Order for displaying categories (lower numbers first)
   */
  displayOrder?: number | null;
  isActive?: boolean | null;
  seo?: {
    /**
     * SEO title for category pages
     */
    title?: string | null;
    /**
     * SEO description for category pages
     */
    description?: string | null;
  };
  createdBy: number | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-comments".
 */
export interface BlogComment {
  id: number;
  post: number | BlogPost;
  /**
   * For nested/reply comments
   */
  parentComment?: (number | null) | BlogComment;
  content: string;
  authorName: string;
  /**
   * Email for notifications (not displayed publicly)
   */
  authorEmail?: string | null;
  /**
   * If logged in user
   */
  author?: (number | null) | User;
  status: 'pending' | 'approved' | 'rejected' | 'spam';
  moderatedBy?: (number | null) | User;
  moderatedAt?: string | null;
  likeCount?: number | null;
  /**
   * IP address for spam prevention
   */
  ipAddress?: string | null;
  /**
   * User agent for spam prevention
   */
  userAgent?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-analytics".
 */
export interface BlogAnalytic {
  id: number;
  post: number | BlogPost;
  /**
   * Date for this analytics record (YYYY-MM-DD)
   */
  date: string;
  /**
   * Total page views for this date
   */
  views?: number | null;
  /**
   * Unique visitors for this date
   */
  uniqueViews?: number | null;
  /**
   * Total likes received on this date
   */
  likes?: number | null;
  /**
   * Total comments received on this date
   */
  comments?: number | null;
  /**
   * Total shares on this date
   */
  shares?: number | null;
  trafficSources?: {
    /**
     * Direct traffic (typed URL, bookmarks)
     */
    direct?: number | null;
    /**
     * Search engine traffic
     */
    search?: number | null;
    /**
     * Social media traffic
     */
    social?: number | null;
    /**
     * Referral traffic from other websites
     */
    referral?: number | null;
  };
  engagement?: {
    /**
     * Average time spent on page (in seconds)
     */
    avgTimeOnPage?: number | null;
    /**
     * Bounce rate percentage (0-100)
     */
    bounceRate?: number | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'institutes';
        value: number | Institute;
      } | null)
    | ({
        relationTo: 'themes';
        value: number | Theme;
      } | null)
    | ({
        relationTo: 'courses';
        value: number | Course;
      } | null)
    | ({
        relationTo: 'permissions';
        value: number | Permission;
      } | null)
    | ({
        relationTo: 'roles';
        value: number | Role;
      } | null)
    | ({
        relationTo: 'user-permissions';
        value: number | UserPermission;
      } | null)
    | ({
        relationTo: 'role-permissions';
        value: number | RolePermission;
      } | null)
    | ({
        relationTo: 'sessions';
        value: number | Session;
      } | null)
    | ({
        relationTo: 'settings';
        value: number | Setting;
      } | null)
    | ({
        relationTo: 'options';
        value: number | Option;
      } | null)
    | ({
        relationTo: 'domain-requests';
        value: number | DomainRequest;
      } | null)
    | ({
        relationTo: 'countries';
        value: number | Country;
      } | null)
    | ({
        relationTo: 'states';
        value: number | State;
      } | null)
    | ({
        relationTo: 'districts';
        value: number | District;
      } | null)
    | ({
        relationTo: 'tax-components';
        value: number | TaxComponent;
      } | null)
    | ({
        relationTo: 'tax-groups';
        value: number | TaxGroup;
      } | null)
    | ({
        relationTo: 'tax-rules';
        value: number | TaxRule;
      } | null)
    | ({
        relationTo: 'branches';
        value: number | Branch;
      } | null)
    | ({
        relationTo: 'course-purchases';
        value: number | CoursePurchase;
      } | null)
    | ({
        relationTo: 'payment-gateways';
        value: number | PaymentGateway;
      } | null)
    | ({
        relationTo: 'institute-gateways';
        value: number | InstituteGateway;
      } | null)
    | ({
        relationTo: 'institute-themes';
        value: number | InstituteTheme;
      } | null)
    | ({
        relationTo: 'audit_logs';
        value: number | AuditLog;
      } | null)
    | ({
        relationTo: 'student-details';
        value: number | StudentDetail;
      } | null)
    | ({
        relationTo: 'platform-blog-posts';
        value: number | PlatformBlogPost;
      } | null)
    | ({
        relationTo: 'platform-blog-categories';
        value: number | PlatformBlogCategory;
      } | null)
    | ({
        relationTo: 'blog-posts';
        value: number | BlogPost;
      } | null)
    | ({
        relationTo: 'blog-categories';
        value: number | BlogCategory;
      } | null)
    | ({
        relationTo: 'blog-comments';
        value: number | BlogComment;
      } | null)
    | ({
        relationTo: 'blog-analytics';
        value: number | BlogAnalytic;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  role?: T;
  legacyRole?: T;
  firstName?: T;
  lastName?: T;
  phone?: T;
  avatar?: T;
  institute?: T;
  branch?: T;
  isActive?: T;
  emailVerified?: T;
  lastLogin?: T;
  lockedUntil?: T;
  address?: T;
  dateOfBirth?: T;
  gender?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  mediaType?: T;
  uploadedBy?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        card?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        hero?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        avatar_small?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        avatar_medium?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        avatar_large?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        profile?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "institutes_select".
 */
export interface InstitutesSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  email?: T;
  phone?: T;
  website?: T;
  tagline?: T;
  logo?: T;
  favicon?: T;
  description?: T;
  addressStreet?: T;
  cityId?: T;
  stateId?: T;
  countryId?: T;
  districtId?: T;
  zipCode?: T;
  customDomain?: T;
  domainVerified?: T;
  isActive?: T;
  subscriptionPlan?: T;
  subscriptionStatus?: T;
  subscriptionExpiry?: T;
  maxStudents?: T;
  maxCourses?: T;
  maxBranches?: T;
  features?:
    | T
    | {
        marketplace?: T;
        liveClasses?: T;
        exams?: T;
        blogs?: T;
        analytics?: T;
      };
  createdBy?: T;
  deletedAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "themes_select".
 */
export interface ThemesSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  type?: T;
  category?: T;
  description?: T;
  version?: T;
  previewImage?: T;
  demoImage?: T;
  screenshots?:
    | T
    | {
        image?: T;
        title?: T;
        description?: T;
        id?: T;
      };
  colors?:
    | T
    | {
        primary?: T;
        secondary?: T;
        accent?: T;
        background?: T;
        text?: T;
        muted?: T;
        border?: T;
      };
  fonts?:
    | T
    | {
        heading?: T;
        body?: T;
        mono?: T;
      };
  features?:
    | T
    | {
        feature?: T;
        id?: T;
      };
  suitableFor?:
    | T
    | {
        type?: T;
        id?: T;
      };
  customizableElements?: T;
  usageCount?: T;
  rating?:
    | T
    | {
        average?: T;
        count?: T;
      };
  isActive?: T;
  isDefault?: T;
  isFeatured?: T;
  demoUrl?: T;
  documentationUrl?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "courses_select".
 */
export interface CoursesSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  thumbnail?: T;
  pricing_type?: T;
  price_amount?: T;
  discount_percentage?: T;
  final_price?: T;
  status?: T;
  institute?: T;
  branch?: T;
  created_by?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "permissions_select".
 */
export interface PermissionsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  code?: T;
  category?: T;
  level?: T;
  resource?: T;
  action?: T;
  isSystemPermission?: T;
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "roles_select".
 */
export interface RolesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  description?: T;
  level?: T;
  permissions?:
    | T
    | {
        permission?: T;
        scope?: T;
        id?: T;
      };
  scope?:
    | T
    | {
        institute?: T;
        branch?: T;
      };
  isSystemRole?: T;
  isActive?: T;
  metadata?:
    | T
    | {
        maxUsers?: T;
        autoAssign?: T;
        requiresApproval?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-permissions_select".
 */
export interface UserPermissionsSelect<T extends boolean = true> {
  user?: T;
  permission?: T;
  type?: T;
  scope?: T;
  scopeTarget?:
    | T
    | {
        institute?: T;
        branch?: T;
      };
  status?: T;
  approvalWorkflow?:
    | T
    | {
        requestedBy?: T;
        approvedBy?: T;
        rejectedBy?: T;
        approvalDate?: T;
        rejectionDate?: T;
        reason?: T;
      };
  effectivePeriod?:
    | T
    | {
        startDate?: T;
        endDate?: T;
      };
  isActive?: T;
  metadata?:
    | T
    | {
        autoApproved?: T;
        inheritedFromRole?: T;
        notes?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "role-permissions_select".
 */
export interface RolePermissionsSelect<T extends boolean = true> {
  role?: T;
  permission?: T;
  isActive?: T;
  assignedBy?: T;
  assignedAt?: T;
  notes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sessions_select".
 */
export interface SessionsSelect<T extends boolean = true> {
  user?: T;
  sessionToken?: T;
  deviceInfo?:
    | T
    | {
        deviceType?: T;
        deviceName?: T;
        browser?: T;
        operatingSystem?: T;
        userAgent?: T;
      };
  location?:
    | T
    | {
        ipAddress?: T;
        city?: T;
        region?: T;
        country?: T;
        latitude?: T;
        longitude?: T;
      };
  security?:
    | T
    | {
        isSecure?: T;
        isCurrent?: T;
        loginMethod?: T;
      };
  isActive?: T;
  lastActivity?: T;
  expiresAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "settings_select".
 */
export interface SettingsSelect<T extends boolean = true> {
  key?: T;
  value?: T;
  category?: T;
  scope?: T;
  dataType?: T;
  description?: T;
  institute?: T;
  isPublic?: T;
  isEditable?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "options_select".
 */
export interface OptionsSelect<T extends boolean = true> {
  key?: T;
  value?: T;
  description?: T;
  category?: T;
  type?: T;
  is_public?: T;
  is_required?: T;
  upload?: T;
  validation_rules?:
    | T
    | {
        min_length?: T;
        max_length?: T;
        min_value?: T;
        max_value?: T;
        pattern?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "domain-requests_select".
 */
export interface DomainRequestsSelect<T extends boolean = true> {
  institute?: T;
  requestedDomain?: T;
  currentDomain?: T;
  status?: T;
  purpose?: T;
  notes?: T;
  requestedBy?: T;
  requestedAt?: T;
  reviewedBy?: T;
  reviewedAt?: T;
  rejectionReason?: T;
  sslStatus?: T;
  dnsRecords?:
    | T
    | {
        type?: T;
        name?: T;
        value?: T;
        ttl?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "countries_select".
 */
export interface CountriesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  flag?: T;
  details?:
    | T
    | {
        capital?: T;
        currency?: T;
        currencyCode?: T;
        language?: T;
        timezone?: T;
        population?: T;
        area?: T;
      };
  coordinates?:
    | T
    | {
        latitude?: T;
        longitude?: T;
      };
  isActive?: T;
  priority?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "states_select".
 */
export interface StatesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  country?: T;
  details?:
    | T
    | {
        capital?: T;
        population?: T;
        area?: T;
        type?: T;
      };
  coordinates?:
    | T
    | {
        latitude?: T;
        longitude?: T;
      };
  isActive?: T;
  priority?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "districts_select".
 */
export interface DistrictsSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  state?: T;
  details?:
    | T
    | {
        type?: T;
        population?: T;
        area?: T;
        pincode?: T;
      };
  coordinates?:
    | T
    | {
        latitude?: T;
        longitude?: T;
      };
  isActive?: T;
  priority?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-components_select".
 */
export interface TaxComponentsSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  description?: T;
  type?: T;
  rate?: T;
  applicableRegions?:
    | T
    | {
        country?: T;
        states?: T;
        isDefault?: T;
        id?: T;
      };
  calculationMethod?: T;
  tieredRates?:
    | T
    | {
        minAmount?: T;
        maxAmount?: T;
        rate?: T;
        id?: T;
      };
  isActive?: T;
  effectiveFrom?: T;
  effectiveTo?: T;
  priority?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-groups_select".
 */
export interface TaxGroupsSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  description?: T;
  taxComponents?:
    | T
    | {
        component?: T;
        customRate?: T;
        isOptional?: T;
        id?: T;
      };
  totalRate?: T;
  applicableScenarios?:
    | T
    | {
        scenario?: T;
        fromCountry?: T;
        toCountry?: T;
        fromState?: T;
        toState?: T;
        id?: T;
      };
  isActive?: T;
  isDefault?: T;
  effectiveFrom?: T;
  effectiveTo?: T;
  priority?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-rules_select".
 */
export interface TaxRulesSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  taxGroup?: T;
  conditions?:
    | T
    | {
        transactionType?: T;
        customerType?: T;
        minAmount?: T;
        maxAmount?: T;
        customerCountry?: T;
        customerState?: T;
        instituteCountry?: T;
        instituteState?: T;
        locationScenario?: T;
        conditionEffectiveFrom?: T;
        conditionEffectiveTo?: T;
        applicableDays?: T;
      };
  exemptions?:
    | T
    | {
        condition?: T;
        exemptionPercentage?: T;
        thresholdAmount?: T;
        id?: T;
      };
  isActive?: T;
  priority?: T;
  effectiveFrom?: T;
  effectiveTo?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "branches_select".
 */
export interface BranchesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  institute?: T;
  location?:
    | T
    | {
        address?: T;
        country?: T;
        state?: T;
        district?: T;
        pincode?: T;
        coordinates?:
          | T
          | {
              latitude?: T;
              longitude?: T;
            };
      };
  contact?:
    | T
    | {
        phone?: T;
        email?: T;
        website?: T;
      };
  taxInformation?:
    | T
    | {
        gstNumber?: T;
        panNumber?: T;
        taxRegistrationNumber?: T;
        isGstRegistered?: T;
      };
  isActive?: T;
  isHeadOffice?: T;
  workingDays?:
    | T
    | {
        monday?: T;
        tuesday?: T;
        wednesday?: T;
        thursday?: T;
        friday?: T;
        saturday?: T;
        sunday?: T;
      };
  operatingHours?:
    | T
    | {
        openTime?: T;
        closeTime?: T;
      };
  isDeleted?: T;
  deletedAt?: T;
  createdBy?: T;
  updatedBy?: T;
  deletedBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "course-purchases_select".
 */
export interface CoursePurchasesSelect<T extends boolean = true> {
  student?: T;
  course?: T;
  branch?: T;
  purchaseDetails?:
    | T
    | {
        originalPrice?: T;
        discountAmount?: T;
        finalAmount?: T;
        currency?: T;
      };
  purchaseDate?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payment-gateways_select".
 */
export interface PaymentGatewaysSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  supportedCurrencies?: T;
  supportedMethods?: T;
  supportedCountries?: T;
  requiredConfigFields?:
    | T
    | {
        key?: T;
        label?: T;
        type?: T;
        placeholder?: T;
        description?: T;
        isRequired?: T;
        id?: T;
      };
  optionalConfigFields?:
    | T
    | {
        key?: T;
        label?: T;
        type?: T;
        placeholder?: T;
        description?: T;
        isRequired?: T;
        id?: T;
      };
  documentationUrl?: T;
  apiVersion?: T;
  webhookSupport?: T;
  logoUrl?: T;
  isActive?: T;
  isFeatured?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "institute-gateways_select".
 */
export interface InstituteGatewaysSelect<T extends boolean = true> {
  institute?: T;
  gateway?: T;
  configuration?: T;
  isActive?: T;
  testMode?: T;
  configuredBy?: T;
  lastTestedAt?: T;
  isPrimary?: T;
  priorityOrder?: T;
  notes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "institute-themes_select".
 */
export interface InstituteThemesSelect<T extends boolean = true> {
  institute?: T;
  theme?: T;
  isActive?: T;
  customizations?: T;
  appliedAt?: T;
  appliedBy?: T;
  previousTheme?: T;
  notes?: T;
  createdBy?: T;
  updatedBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "audit_logs_select".
 */
export interface AuditLogsSelect<T extends boolean = true> {
  userId?: T;
  action?: T;
  targetType?: T;
  targetId?: T;
  reason?: T;
  metadata?: T;
  ipAddress?: T;
  userAgent?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "student-details_select".
 */
export interface StudentDetailsSelect<T extends boolean = true> {
  student?: T;
  country?: T;
  state?: T;
  district?: T;
  education?:
    | T
    | {
        highestQualification?: T;
        institution?: T;
        fieldOfStudy?: T;
        graduationYear?: T;
        percentage?: T;
      };
  personalInfo?:
    | T
    | {
        fatherName?: T;
        motherName?: T;
        guardianName?: T;
        emergencyContact?: T;
        bloodGroup?: T;
        nationality?: T;
        religion?: T;
        caste?: T;
      };
  documents?:
    | T
    | {
        aadharNumber?: T;
        panNumber?: T;
        passportNumber?: T;
        drivingLicense?: T;
      };
  additionalInfo?:
    | T
    | {
        hobbies?: T;
        skills?: T;
        experience?: T;
        goals?: T;
        notes?: T;
      };
  isActive?: T;
  createdBy?: T;
  updatedBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "platform-blog-posts_select".
 */
export interface PlatformBlogPostsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  excerpt?: T;
  content?: T;
  featuredImage?: T;
  status?: T;
  publishedAt?: T;
  scheduledFor?: T;
  category?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  isAnnouncement?: T;
  announcementPriority?: T;
  targetAudience?: T;
  seo?:
    | T
    | {
        title?: T;
        description?: T;
        keywords?:
          | T
          | {
              keyword?: T;
              id?: T;
            };
        canonicalUrl?: T;
      };
  analytics?:
    | T
    | {
        viewCount?: T;
        uniqueViewCount?: T;
        likeCount?: T;
        shareCount?: T;
        readingTime?: T;
        instituteAdminViews?: T;
        studentViews?: T;
        staffViews?: T;
        publicViews?: T;
      };
  settings?:
    | T
    | {
        allowComments?: T;
        isFeatured?: T;
        isSticky?: T;
        showOnDashboard?: T;
      };
  author?: T;
  lastEditedBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "platform-blog-categories_select".
 */
export interface PlatformBlogCategoriesSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  description?: T;
  color?: T;
  icon?: T;
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-posts_select".
 */
export interface BlogPostsSelect<T extends boolean = true> {
  institute?: T;
  title?: T;
  slug?: T;
  excerpt?: T;
  content?: T;
  featuredImage?: T;
  status?: T;
  publishedAt?: T;
  scheduledFor?: T;
  category?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  seo?:
    | T
    | {
        title?: T;
        description?: T;
        keywords?:
          | T
          | {
              keyword?: T;
              id?: T;
            };
      };
  settings?:
    | T
    | {
        allowComments?: T;
        isFeatured?: T;
        isSticky?: T;
      };
  analytics?:
    | T
    | {
        viewCount?: T;
        likeCount?: T;
        commentCount?: T;
        readingTime?: T;
      };
  author?: T;
  lastEditedBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-categories_select".
 */
export interface BlogCategoriesSelect<T extends boolean = true> {
  institute?: T;
  name?: T;
  slug?: T;
  description?: T;
  color?: T;
  icon?: T;
  parentCategory?: T;
  displayOrder?: T;
  isActive?: T;
  seo?:
    | T
    | {
        title?: T;
        description?: T;
      };
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-comments_select".
 */
export interface BlogCommentsSelect<T extends boolean = true> {
  post?: T;
  parentComment?: T;
  content?: T;
  authorName?: T;
  authorEmail?: T;
  author?: T;
  status?: T;
  moderatedBy?: T;
  moderatedAt?: T;
  likeCount?: T;
  ipAddress?: T;
  userAgent?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-analytics_select".
 */
export interface BlogAnalyticsSelect<T extends boolean = true> {
  post?: T;
  date?: T;
  views?: T;
  uniqueViews?: T;
  likes?: T;
  comments?: T;
  shares?: T;
  trafficSources?:
    | T
    | {
        direct?: T;
        search?: T;
        social?: T;
        referral?: T;
      };
  engagement?:
    | T
    | {
        avgTimeOnPage?: T;
        bounceRate?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}