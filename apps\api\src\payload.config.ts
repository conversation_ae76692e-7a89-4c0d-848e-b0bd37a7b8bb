// storage-adapter-import-placeholder
import { postgresAdapter } from '@payloadcms/db-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import path from 'path'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'
import sharp from 'sharp'

import { Users } from './collections/Users'
import { Media } from './collections/Media'
import { Institutes } from './collections/Institutes'
import Themes from './collections/Themes'
import { Courses } from './collections/Courses'


import { Permissions } from './collections/Permissions'
import Roles from './collections/Roles'
import UserPermissions from './collections/UserPermissions'
import RolePermissions from './collections/RolePermissions'
import Sessions from './collections/Sessions'
import Settings from './collections/Settings'
import { Options } from './collections/Options'
import DomainRequests from './collections/DomainRequests'
import Countries from './collections/Countries'
import States from './collections/States'
import Districts from './collections/Districts'
import TaxComponents from './collections/TaxComponents'
import TaxGroups from './collections/TaxGroups'
import TaxRules from './collections/TaxRules'
import Branches from './collections/Branches'
// import Bills from './collections/Bills' // Temporarily disabled due to course-purchases dependency
import CoursePurchases from './collections/CoursePurchases'

import PaymentGateways from './collections/PaymentGateways'
import InstituteGateways from './collections/InstituteGateways'
import InstituteThemes from './collections/InstituteThemes'
import AuditLogs from './collections/AuditLogs'
import StudentDetails from './collections/StudentDetails'

import PlatformBlogPosts from './collections/PlatformBlogPosts'
import PlatformBlogCategories from './collections/PlatformBlogCategories'
import BlogPosts from './collections/BlogPosts'
import BlogCategories from './collections/BlogCategories'
import BlogComments from './collections/BlogComments'
import BlogAnalytics from './collections/BlogAnalytics'
import { loginEndpoint, registerInstituteEndpoint, registerStudentEndpoint, refreshTokenEndpoint, verifyTokenEndpoint } from './endpoints/auth'
import { getCurrentUserEndpoint, putUpdateCurrentUserEndpoint, getAllUsersEndpoint, getInstituteUsersEndpoint, createUserEndpoint } from './endpoints/users'
import { commonFileUploadEndpoint, getUserFilesEndpoint, deleteFileEndpoint } from './endpoints/file-upload'
import { simpleUploadEndpoint, getSimpleFilesEndpoint } from './endpoints/simple-upload'
import { staticFileEndpoint, debugMediaEndpoint } from './endpoints/static-files'
import { getAllInstitutesEndpoint, getCurrentInstituteEndpoint, updateCurrentInstituteEndpoint, createInstituteEndpoint, updateInstituteEndpoint, deleteInstituteEndpoint, getInstituteByDomainEndpoint } from './endpoints/institutes'


import { testEndpoints } from './endpoints/test'

import { settingsEndpoints } from './endpoints/settings'
import { sessionEndpoints } from './endpoints/sessions'
import { domainRequestEndpoints } from './endpoints/domain-requests'
import { locationEndpoints } from './endpoints/locations'
import { taxManagementEndpoints } from './endpoints/tax-management'
import { roleManagementEndpoints } from './endpoints/role-management-index'
import { notificationEndpoints } from './endpoints/notifications'
import { reportEndpoints } from './endpoints/reports'
import { studentEndpoints } from './endpoints/student'
import { allAdminEndpoints } from './endpoints/admin'

import { getInstituteProfileEndpoint, updateInstituteProfileEndpoint, getInstituteStatsEndpoint } from './endpoints/institutes'
import { seedThemesEndpoint } from './endpoints/seed-themes'
import { debugDomainEndpoint } from './endpoints/debug-domain'
import { createTestInstituteEndpoint } from './endpoints/create-test-institute'
import { seedEndpoint, getSuperAdminEndpoint } from './endpoints/seed'
import healthCheckEndpoints from './endpoints/health-check'
import { testUploadDebugEndpoint } from './endpoints/test-upload-debug'
import { removeAvatarEndpoint } from './endpoints/file-upload'

// Super Admin endpoints
import {
  superAdminGetCurrentUserEndpoint,
  superAdminUpdateCurrentUserEndpoint,
  superAdminGetAllUsersEndpoint,
  superAdminCreateUserEndpoint,
  superAdminUploadAvatarEndpoint,
  superAdminGetCurrentAvatarEndpoint,
  superAdminRemoveAvatarEndpoint
} from './endpoints/super-admin'

// Institute Admin endpoints
import {
  getInstituteDetailsEndpoint,
  updateInstituteDetailsEndpoint,
  getInstituteStatsEndpoint as getInstituteAdminStatsEndpoint,
  getInstituteBranchesEndpoint,
  createBranchEndpoint,
  updateBranchEndpoint,
  deleteBranchEndpoint,
  getStudentsEndpoint as getInstituteStudentsEndpoint,
  createStudentEndpoint as createInstituteStudentEndpoint,
  updateStudentEndpoint as updateInstituteStudentEndpoint,
  getStudentByIdEndpoint as getInstituteStudentByIdEndpoint,
  deleteStudentEndpoint as deleteInstituteStudentEndpoint,
  getBranchesEndpoint as getInstituteBranchesForStudentsEndpoint,
  getRolesEndpoint as getInstituteRolesEndpoint,
  getStaffEndpoint,
  createStaffEndpoint,
  updateStaffEndpoint,
  getStaffByIdEndpoint,
  deleteStaffEndpoint,
  toggleStaffStatusEndpoint,
  getCoursesEndpoint,
  createCourseEndpoint,
  createDomainRequestEndpoint,
  getDomainRequestStatusEndpoint,
  getInstituteThemesEndpoint,
  getCurrentInstituteThemeEndpoint,
  applyThemeToInstituteEndpoint,
  previewThemeEndpoint,
  getInstituteThemeHistoryEndpoint,
  getThemesListEndpoint,
  getBlogPostsEndpoint,
  getBlogPostEndpoint,
  createBlogPostEndpoint,
  updateBlogPostEndpoint,
  deleteBlogPostEndpoint,
  getBlogCategoriesEndpoint,
  createBlogCategoryEndpoint,
  updateBlogCategoryEndpoint,
  deleteBlogCategoryEndpoint,
  blogSearchEndpoint,
  getTrendingPostsEndpoint,
  getBlogAnalyticsEndpoint,
  getBlogCommentsEndpoint,
  updateBlogCommentEndpoint,
  deleteBlogCommentEndpoint,
  getAvailableGatewaysEndpoint,
  getInstituteGatewayConfigsEndpoint,
  getGatewayConfigEndpoint,
  saveGatewayConfigEndpoint,
  deleteGatewayConfigEndpoint,
  testGatewayConfigEndpoint
} from './endpoints/institute-admin'
import { billingEndpoints } from './endpoints/billing'
import paymentGatewayEndpoints from './endpoints/super-admin/payment-gateways'
import {
  getDomainRequestsEndpoint,
  approveDomainRequestEndpoint,
  rejectDomainRequestEndpoint,
  activateDomainEndpoint,
  getDomainRequestDetailsEndpoint,
  getPlatformBlogPostsEndpoint,
  getPlatformBlogPostEndpoint,
  createPlatformBlogPostEndpoint,
  updatePlatformBlogPostEndpoint,
  deletePlatformBlogPostEndpoint,
  publishPlatformBlogPostEndpoint,
  schedulePlatformBlogPostEndpoint,
  getPlatformBlogCategoriesEndpoint,
  createPlatformBlogCategoryEndpoint,
  updatePlatformBlogCategoryEndpoint,
  deletePlatformBlogCategoryEndpoint,
  getPlatformBlogAnalyticsEndpoint
} from './endpoints/super-admin'
import {
  getInstitutesEndpoint as getInstitutesManagementEndpoint,
  createInstituteEndpoint as createInstituteManagementEndpoint,
  updateInstituteEndpoint as updateInstituteManagementEndpoint,
  deleteInstituteEndpoint as deleteInstituteManagementEndpoint,
  getInstituteStatisticsEndpoint,
  verifyDomainEndpoint
} from './endpoints/institute-management'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  serverURL: process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3001',
  routes: {
    api: '/api', // Restore /api/ prefix for URLs
    admin: '/admin',
    graphQL: '/graphql'
  },
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  cors: [
    'http://localhost:3000', // Frontend development server
    'http://localhost:3001', // API server
    'http://localhost:3002', // Frontend fallback port
    'http://hello.local:3000',
    'http://127.0.0.1:3000', // Alternative localhost
    'http://127.0.0.1:3001', // Alternative localhost
    'http://127.0.0.1:3002', // Alternative localhost
    process.env.FRONTEND_URL || 'http://localhost:3000',
    // Allow all origins in development
    ...(process.env.NODE_ENV === 'development' ? ['*'] : [])
  ],
  collections: [Users, Media, Institutes, Themes, Courses, Permissions, Roles, UserPermissions, RolePermissions, Sessions, Settings, Options, DomainRequests, Countries, States, Districts, TaxComponents, TaxGroups, TaxRules, Branches, /* Bills, */ CoursePurchases, PaymentGateways, InstituteGateways, InstituteThemes, AuditLogs, StudentDetails, PlatformBlogPosts, PlatformBlogCategories, BlogPosts, BlogCategories, BlogComments, BlogAnalytics],
  endpoints: [
    // Authentication endpoints
    loginEndpoint,
    registerInstituteEndpoint,
    registerStudentEndpoint,
    refreshTokenEndpoint,
    verifyTokenEndpoint,

    // User management endpoints
    getCurrentUserEndpoint,
    putUpdateCurrentUserEndpoint,
    getAllUsersEndpoint,
    getInstituteUsersEndpoint,
    createUserEndpoint,

    // Super Admin user management endpoints
    superAdminGetCurrentUserEndpoint,
    superAdminUpdateCurrentUserEndpoint,
    superAdminGetAllUsersEndpoint,
    superAdminCreateUserEndpoint,

    // Super Admin avatar management endpoints
    superAdminUploadAvatarEndpoint,
    superAdminGetCurrentAvatarEndpoint,
    superAdminRemoveAvatarEndpoint,

    // Common file upload endpoints
    commonFileUploadEndpoint,
    getUserFilesEndpoint,
    deleteFileEndpoint,
    removeAvatarEndpoint,

    // Simple upload endpoints (bypass Payload complexity)
    simpleUploadEndpoint,
    getSimpleFilesEndpoint,

    // Static file serving endpoints (immediate fix for 404 issues)
    staticFileEndpoint,
    debugMediaEndpoint,

    // Institute management endpoints (legacy)
    getAllInstitutesEndpoint,
    getCurrentInstituteEndpoint,
    updateCurrentInstituteEndpoint,
    createInstituteEndpoint,
    updateInstituteEndpoint,
    deleteInstituteEndpoint,
    getInstituteByDomainEndpoint, // For custom domain resolution

    // Institute management endpoints (Phase 10)
    getInstitutesManagementEndpoint,
    createInstituteManagementEndpoint,
    updateInstituteManagementEndpoint,
    deleteInstituteManagementEndpoint,
    getInstituteStatisticsEndpoint,
    verifyDomainEndpoint,

    // Institute Admin endpoints (for institute administrators)
    getInstituteDetailsEndpoint,
    updateInstituteDetailsEndpoint,
    getInstituteAdminStatsEndpoint,
    getInstituteBranchesEndpoint,
    createBranchEndpoint,
    updateBranchEndpoint,
    deleteBranchEndpoint,
    getInstituteStudentsEndpoint,
    createInstituteStudentEndpoint,
    updateInstituteStudentEndpoint,
    getInstituteStudentByIdEndpoint,
    deleteInstituteStudentEndpoint,
    getInstituteBranchesForStudentsEndpoint,
    getInstituteRolesEndpoint,
    getStaffEndpoint,
    createStaffEndpoint,
    updateStaffEndpoint,
    getStaffByIdEndpoint,
    deleteStaffEndpoint,
    toggleStaffStatusEndpoint,

    // Institute Admin Course endpoints
    getCoursesEndpoint,
    createCourseEndpoint,

    createDomainRequestEndpoint,
    getDomainRequestStatusEndpoint,

    // Institute Admin Theme endpoints
    getInstituteThemesEndpoint,
    getCurrentInstituteThemeEndpoint,
    applyThemeToInstituteEndpoint,
    previewThemeEndpoint,
    getInstituteThemeHistoryEndpoint,
    getThemesListEndpoint,

    // Institute Admin Blog endpoints (Phase 14)
    getBlogPostsEndpoint,
    getBlogPostEndpoint,
    createBlogPostEndpoint,
    updateBlogPostEndpoint,
    deleteBlogPostEndpoint,
    getBlogCategoriesEndpoint,
    createBlogCategoryEndpoint,
    updateBlogCategoryEndpoint,
    deleteBlogCategoryEndpoint,
    blogSearchEndpoint,
    getTrendingPostsEndpoint,
    getBlogAnalyticsEndpoint,
    getBlogCommentsEndpoint,
    updateBlogCommentEndpoint,
    deleteBlogCommentEndpoint,

    // Super Admin Domain Management endpoints
    getDomainRequestsEndpoint,
    approveDomainRequestEndpoint,
    rejectDomainRequestEndpoint,
    activateDomainEndpoint,
    getDomainRequestDetailsEndpoint,

    // Platform Blog Management endpoints
    getPlatformBlogPostsEndpoint,
    getPlatformBlogPostEndpoint,
    createPlatformBlogPostEndpoint,
    updatePlatformBlogPostEndpoint,
    deletePlatformBlogPostEndpoint,
    publishPlatformBlogPostEndpoint,
    schedulePlatformBlogPostEndpoint,
    getPlatformBlogCategoriesEndpoint,
    createPlatformBlogCategoryEndpoint,
    updatePlatformBlogCategoryEndpoint,
    deletePlatformBlogCategoryEndpoint,
    getPlatformBlogAnalyticsEndpoint,



    // Theme management endpoints (using institute-admin themes)

    // Test endpoints
    ...testEndpoints,



    // Settings management endpoints
    ...settingsEndpoints,

    // Session management endpoints
    ...sessionEndpoints,

    // Domain request endpoints
    ...domainRequestEndpoints,

    // Location management endpoints
    ...locationEndpoints,

    // Tax management endpoints
    ...taxManagementEndpoints,

    // Role management endpoints (enhanced - tax management style)
    ...roleManagementEndpoints,

    // Billing endpoints
    ...billingEndpoints,

    // Payment Gateway Management endpoints
    ...paymentGatewayEndpoints,

    // Institute Gateway Configuration endpoints
    getAvailableGatewaysEndpoint,
    getInstituteGatewayConfigsEndpoint,
    getGatewayConfigEndpoint,
    saveGatewayConfigEndpoint,
    deleteGatewayConfigEndpoint,
    testGatewayConfigEndpoint,

    // Notification endpoints
    ...notificationEndpoints,

    // Report endpoints
    ...reportEndpoints,

    // Student endpoints
    ...studentEndpoints,

    // Admin endpoints (Course Builder System)
    ...allAdminEndpoints,

    // Institute profile endpoints
    getInstituteProfileEndpoint,
    updateInstituteProfileEndpoint,
    getInstituteStatsEndpoint,

    // Development endpoints
    seedThemesEndpoint,
    debugDomainEndpoint,
    createTestInstituteEndpoint,

    // Seed endpoints
    seedEndpoint,
    getSuperAdminEndpoint,

    // Health check and status endpoints
    ...healthCheckEndpoints,

    // Debug endpoints
    testUploadDebugEndpoint,

  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
  }),
  sharp,
  plugins: [
    payloadCloudPlugin(),
    // storage-adapter-placeholder
  ],
})
