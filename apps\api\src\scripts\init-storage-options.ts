import payload from 'payload'

console.log('🔧 Initializing storage configuration options...')

// Default storage options to create
const storageOptions = [
  {
    key: 'storage_provider',
    value: 'local',
    description: 'Storage provider for file uploads (local or s3)',
    category: 'storage',
    type: 'string',
    is_required: true,
    is_public: false,
  },
  {
    key: 's3_bucket',
    value: '',
    description: 'S3 bucket name for file storage',
    category: 'storage',
    type: 'string',
    is_required: false,
    is_public: false,
  },
  {
    key: 's3_access_key',
    value: '',
    description: 'S3 access key ID',
    category: 'storage',
    type: 'string',
    is_required: false,
    is_public: false,
  },
  {
    key: 's3_secret_key',
    value: '',
    description: 'S3 secret access key',
    category: 'storage',
    type: 'string',
    is_required: false,
    is_public: false,
  },
  {
    key: 's3_region',
    value: 'us-east-1',
    description: 'S3 region (e.g., us-east-1, eu-west-1)',
    category: 'storage',
    type: 'string',
    is_required: false,
    is_public: false,
  },
  {
    key: 's3_endpoint',
    value: '',
    description: 'Custom S3 endpoint URL (for S3-compatible services like DigitalOcean Spaces)',
    category: 'storage',
    type: 'url',
    is_required: false,
    is_public: false,
  },
  {
    key: 's3_cdn_url',
    value: '',
    description: 'CDN URL for S3 files (optional, for faster delivery)',
    category: 'storage',
    type: 'url',
    is_required: false,
    is_public: false,
  },
  {
    key: 'max_file_size',
    value: '5242880',
    description: 'Maximum file size in bytes (default: 5MB)',
    category: 'storage',
    type: 'number',
    is_required: true,
    is_public: true,
  },
  {
    key: 'allowed_file_types',
    value: 'image/jpeg,image/png,image/gif,image/webp,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    description: 'Comma-separated list of allowed MIME types',
    category: 'storage',
    type: 'textarea',
    is_required: true,
    is_public: true,
  },
  {
    key: 'image_quality',
    value: '85',
    description: 'Default image compression quality (1-100)',
    category: 'storage',
    type: 'number',
    is_required: true,
    is_public: false,
  },
]

export async function initStorageOptions() {
  try {
    console.log('📋 Creating storage configuration options...')

    for (const option of storageOptions) {
      // Check if option already exists
      const existing = await payload.find({
        collection: 'options',
        where: {
          key: { equals: option.key }
        },
        limit: 1
      })

      if (existing.docs.length > 0) {
        console.log(`⏭️  Option ${option.key} already exists, skipping...`)
        continue
      }

      // Create new option
      await payload.create({
        collection: 'options',
        data: option
      })

      console.log(`✅ Created option: ${option.key}`)
    }

    console.log('🎉 Storage options initialization completed!')

    // Display current storage configuration
    console.log('\n📊 Current Storage Configuration:')
    const currentOptions = await payload.find({
      collection: 'options',
      where: {
        category: { equals: 'storage' }
      },
      sort: 'key'
    })

    currentOptions.docs.forEach((option: any) => {
      const displayValue = option.key.includes('secret') || option.key.includes('key') 
        ? '[HIDDEN]' 
        : option.value || '[NOT SET]'
      
      console.log(`  ${option.key}: ${displayValue}`)
    })

  } catch (error) {
    console.error('❌ Failed to initialize storage options:', error)
    throw error
  }
}

// Helper function to get storage configuration
export async function getStorageConfiguration() {
  try {
    const options = await payload.find({
      collection: 'options',
      where: {
        category: { equals: 'storage' }
      }
    })

    const config: any = {}
    options.docs.forEach((option: any) => {
      config[option.key] = option.value
    })

    return config
  } catch (error) {
    console.error('❌ Failed to get storage configuration:', error)
    return {}
  }
}

// Helper function to update storage provider
export async function updateStorageProvider(provider: 'local' | 's3') {
  try {
    const existing = await payload.find({
      collection: 'options',
      where: {
        key: { equals: 'storage_provider' }
      },
      limit: 1
    })

    if (existing.docs.length > 0) {
      await payload.update({
        collection: 'options',
        id: existing.docs[0].id,
        data: {
          value: provider
        }
      })
    } else {
      await payload.create({
        collection: 'options',
        data: {
          key: 'storage_provider',
          value: provider,
          description: 'Storage provider for file uploads (local or s3)',
          category: 'storage',
          type: 'string',
          is_required: true,
          is_public: false,
        }
      })
    }

    console.log(`✅ Storage provider updated to: ${provider}`)
  } catch (error) {
    console.error('❌ Failed to update storage provider:', error)
    throw error
  }
}

// Helper function to update S3 configuration
export async function updateS3Configuration(s3Config: {
  bucket: string
  accessKey: string
  secretKey: string
  region?: string
  endpoint?: string
  cdnUrl?: string
}) {
  try {
    const updates = [
      { key: 's3_bucket', value: s3Config.bucket },
      { key: 's3_access_key', value: s3Config.accessKey },
      { key: 's3_secret_key', value: s3Config.secretKey },
      { key: 's3_region', value: s3Config.region || 'us-east-1' },
      { key: 's3_endpoint', value: s3Config.endpoint || '' },
      { key: 's3_cdn_url', value: s3Config.cdnUrl || '' },
    ]

    for (const update of updates) {
      const existing = await payload.find({
        collection: 'options',
        where: {
          key: { equals: update.key }
        },
        limit: 1
      })

      if (existing.docs.length > 0) {
        await payload.update({
          collection: 'options',
          id: existing.docs[0].id,
          data: {
            value: update.value
          }
        })
      } else {
        await payload.create({
          collection: 'options',
          data: {
            key: update.key,
            value: update.value,
            description: `S3 ${update.key.replace('s3_', '')} configuration`,
            category: 'storage',
            type: update.key.includes('url') ? 'url' : 'string',
            is_required: false,
            is_public: false,
          }
        })
      }
    }

    console.log('✅ S3 configuration updated successfully')
  } catch (error) {
    console.error('❌ Failed to update S3 configuration:', error)
    throw error
  }
}

// Run initialization if this file is executed directly
if (require.main === module) {
  initStorageOptions()
    .then(() => {
      console.log('✅ Storage options initialization completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Storage options initialization failed:', error)
      process.exit(1)
    })
}
