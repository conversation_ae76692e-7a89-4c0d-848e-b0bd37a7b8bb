import { Payload } from 'payload'
import path from 'path'

/**
 * Initialize Storage Settings Script
 * Sets up default storage configuration in the options table
 */

export interface StorageSettingsInit {
  payload: Payload
}

export async function initializeStorageSettings({ payload }: StorageSettingsInit): Promise<void> {
  console.log('🚀 Initializing storage settings...')

  try {
    // Default storage settings
    const defaultSettings = [
      // Storage provider
      {
        key: 'storage_provider',
        value: 'local',
        description: 'Active storage provider (local or s3)',
        category: 'storage',
        type: 'string'
      },

      // Local storage settings
      {
        key: 'local_upload_dir',
        value: path.resolve(process.cwd(), 'media'),
        description: 'Local storage upload directory',
        category: 'storage',
        type: 'string'
      },
      {
        key: 'local_base_url',
        value: process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3001',
        description: 'Local storage base URL',
        category: 'storage',
        type: 'string'
      },
      {
        key: 'local_public_path',
        value: '/media',
        description: 'Local storage public path',
        category: 'storage',
        type: 'string'
      },

      // S3 storage settings (empty by default)
      {
        key: 's3_bucket',
        value: '',
        description: 'S3 bucket name',
        category: 'storage',
        type: 'string'
      },
      {
        key: 's3_region',
        value: 'us-east-1',
        description: 'S3 region',
        category: 'storage',
        type: 'string'
      },
      {
        key: 's3_access_key_id',
        value: '',
        description: 'S3 access key ID',
        category: 'storage',
        type: 'string'
      },
      {
        key: 's3_secret_access_key',
        value: '',
        description: 'S3 secret access key',
        category: 'storage',
        type: 'string'
      },
      {
        key: 's3_endpoint',
        value: '',
        description: 'S3 custom endpoint (optional)',
        category: 'storage',
        type: 'string'
      },
      {
        key: 's3_public_url',
        value: '',
        description: 'S3 public URL (optional)',
        category: 'storage',
        type: 'string'
      },
      {
        key: 's3_cdn_url',
        value: '',
        description: 'S3 CDN URL (optional)',
        category: 'storage',
        type: 'string'
      },

      // Platform branding settings (empty by default)
      {
        key: 'platform_logo',
        value: '',
        description: 'Platform logo media ID',
        category: 'platform',
        type: 'media'
      },
      {
        key: 'platform_favicon',
        value: '',
        description: 'Platform favicon media ID',
        category: 'platform',
        type: 'media'
      },

      // Favicon size settings (empty by default)
      {
        key: 'platform_favicon_16x16',
        value: '',
        description: 'Platform favicon 16x16 media ID',
        category: 'platform',
        type: 'media'
      },
      {
        key: 'platform_favicon_32x32',
        value: '',
        description: 'Platform favicon 32x32 media ID',
        category: 'platform',
        type: 'media'
      },
      {
        key: 'platform_favicon_48x48',
        value: '',
        description: 'Platform favicon 48x48 media ID',
        category: 'platform',
        type: 'media'
      },
      {
        key: 'platform_favicon_64x64',
        value: '',
        description: 'Platform favicon 64x64 media ID',
        category: 'platform',
        type: 'media'
      },
      {
        key: 'platform_favicon_128x128',
        value: '',
        description: 'Platform favicon 128x128 media ID',
        category: 'platform',
        type: 'media'
      },
      {
        key: 'platform_favicon_256x256',
        value: '',
        description: 'Platform favicon 256x256 media ID',
        category: 'platform',
        type: 'media'
      }
    ]

    // Get existing settings
    const existingSettings = await payload.find({
      collection: 'options',
      where: {
        key: {
          in: defaultSettings.map(s => s.key)
        }
      },
      limit: 1000
    })

    const existingKeys = existingSettings.docs.map(s => s.key)

    // Create missing settings
    let createdCount = 0
    let skippedCount = 0

    for (const setting of defaultSettings) {
      if (!existingKeys.includes(setting.key)) {
        try {
          await payload.create({
            collection: 'options',
            data: setting
          })
          createdCount++
          console.log(`✅ Created setting: ${setting.key}`)
        } catch (error) {
          console.error(`❌ Failed to create setting ${setting.key}:`, error)
        }
      } else {
        skippedCount++
        console.log(`⏭️ Skipped existing setting: ${setting.key}`)
      }
    }

    console.log('✅ Storage settings initialization completed:', {
      total: defaultSettings.length,
      created: createdCount,
      skipped: skippedCount
    })

    // Ensure media directory exists for local storage
    if (process.env.NODE_ENV !== 'production') {
      try {
        const fs = await import('fs/promises')
        const mediaDir = path.resolve(process.cwd(), 'media')
        await fs.mkdir(mediaDir, { recursive: true })
        console.log('📁 Media directory ensured:', mediaDir)
      } catch (error) {
        console.warn('⚠️ Failed to create media directory:', error)
      }
    }

  } catch (error) {
    console.error('❌ Storage settings initialization failed:', error)
    throw error
  }
}

/**
 * Validate storage settings
 */
export async function validateStorageSettings({ payload }: StorageSettingsInit): Promise<{
  valid: boolean
  issues: string[]
}> {
  console.log('🔍 Validating storage settings...')

  const issues: string[] = []

  try {
    // Check if storage provider is set
    const providerSetting = await payload.find({
      collection: 'options',
      where: {
        key: { equals: 'storage_provider' }
      },
      limit: 1
    })

    if (providerSetting.docs.length === 0) {
      issues.push('Storage provider not configured')
    } else {
      const provider = providerSetting.docs[0].value

      if (provider === 's3') {
        // Validate S3 settings
        const s3Settings = await payload.find({
          collection: 'options',
          where: {
            key: {
              in: ['s3_bucket', 's3_region', 's3_access_key_id', 's3_secret_access_key']
            }
          }
        })

        const s3Keys = s3Settings.docs.map(s => s.key)
        const requiredS3Keys = ['s3_bucket', 's3_region', 's3_access_key_id', 's3_secret_access_key']

        for (const key of requiredS3Keys) {
          if (!s3Keys.includes(key)) {
            issues.push(`Missing S3 setting: ${key}`)
          } else {
            const setting = s3Settings.docs.find(s => s.key === key)
            if (!setting?.value) {
              issues.push(`Empty S3 setting: ${key}`)
            }
          }
        }
      }

      if (provider === 'local') {
        // Validate local settings
        const localSettings = await payload.find({
          collection: 'options',
          where: {
            key: {
              in: ['local_upload_dir', 'local_base_url']
            }
          }
        })

        const localKeys = localSettings.docs.map(s => s.key)
        const requiredLocalKeys = ['local_upload_dir', 'local_base_url']

        for (const key of requiredLocalKeys) {
          if (!localKeys.includes(key)) {
            issues.push(`Missing local setting: ${key}`)
          } else {
            const setting = localSettings.docs.find(s => s.key === key)
            if (!setting?.value) {
              issues.push(`Empty local setting: ${key}`)
            }
          }
        }
      }
    }

    const valid = issues.length === 0

    console.log('🔍 Storage settings validation completed:', {
      valid,
      issues: issues.length
    })

    return { valid, issues }

  } catch (error) {
    console.error('❌ Storage settings validation failed:', error)
    return {
      valid: false,
      issues: [`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`]
    }
  }
}

/**
 * Get storage settings summary
 */
export async function getStorageSettingsSummary({ payload }: StorageSettingsInit): Promise<{
  provider: string
  localConfigured: boolean
  s3Configured: boolean
  brandingConfigured: boolean
}> {
  try {
    // Get provider
    const providerSetting = await payload.find({
      collection: 'options',
      where: {
        key: { equals: 'storage_provider' }
      },
      limit: 1
    })

    const provider = providerSetting.docs[0]?.value || 'local'

    // Check local configuration
    const localSettings = await payload.find({
      collection: 'options',
      where: {
        key: {
          in: ['local_upload_dir', 'local_base_url']
        }
      }
    })

    const localConfigured = localSettings.docs.length >= 2 && 
                           localSettings.docs.every(s => s.value)

    // Check S3 configuration
    const s3Settings = await payload.find({
      collection: 'options',
      where: {
        key: {
          in: ['s3_bucket', 's3_region', 's3_access_key_id', 's3_secret_access_key']
        }
      }
    })

    const s3Configured = s3Settings.docs.length >= 4 && 
                        s3Settings.docs.every(s => s.value)

    // Check branding configuration
    const brandingSettings = await payload.find({
      collection: 'options',
      where: {
        key: {
          in: ['platform_logo', 'platform_favicon']
        }
      }
    })

    const brandingConfigured = brandingSettings.docs.some(s => s.value)

    return {
      provider,
      localConfigured,
      s3Configured,
      brandingConfigured
    }

  } catch (error) {
    console.error('❌ Failed to get storage settings summary:', error)
    return {
      provider: 'local',
      localConfigured: false,
      s3Configured: false,
      brandingConfigured: false
    }
  }
}
