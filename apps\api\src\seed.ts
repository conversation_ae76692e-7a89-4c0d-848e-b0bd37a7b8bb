import { getPayload } from 'payload'
import config from './payload.config'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const seed = async (): Promise<void> => {
  const payload = await getPayload({ config })

  try {
    // Check if any super admin users exist
    const existingAdmins = await payload.find({
      collection: 'users',
      where: {
        role: {
          equals: 'super_admin',
        },
      },
    })

    if (existingAdmins.docs.length === 0) {
      // Create default super admin user
      const superAdmin = await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'SuperAdmin@123',
          firstName: 'Super',
          lastName: 'Admin',
          role: 'super_admin',
          isActive: true,
          emailVerified: true,
        },
      })

      console.log('✅ Default Super Admin created successfully!')
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Password: SuperAdmin@123')
      console.log('🚀 You can now login to Payload Admin at: http://localhost:3002/admin')
    } else {
      console.log('ℹ️  Super Admin user already exists')
    }

    // Create a sample institute for testing
    const existingInstitutes = await payload.find({
      collection: 'institutes',
      where: {
        slug: {
          equals: 'demo-institute',
        },
      },
    })

    if (existingInstitutes.docs.length === 0) {
      const institute = await payload.create({
        collection: 'institutes',
        data: {
          name: 'Demo Institute',
          slug: 'demo-institute',
          email: '<EMAIL>',
          phone: '+91 9876543210',
          website: 'https://demo-institute.com',
          description: 'A demo institute for testing purposes',
          address: {
            city: 'Mumbai',
            state: 'Maharashtra',
            country: 'India',
          },
          subscriptionPlan: 'professional',
          subscriptionStatus: 'active',
          subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year
          maxStudents: 1000,
          maxCourses: 50,
          maxBranches: 5,
          features: {
            marketplace: true,
            liveClasses: true,
            exams: true,
            blogs: true,
            analytics: true,
          },
          isActive: true,
        },
      })

      // Create institute admin for the demo institute
      const instituteAdmin = await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'demo123',
          firstName: 'Institute',
          lastName: 'Admin',
          role: 'institute_admin',
          institute: institute.id,
          isActive: true,
          emailVerified: true,
        },
      })

      console.log('✅ Demo Institute created successfully!')
      console.log('🏢 Institute: Demo Institute')
      console.log('📧 Admin Email: <EMAIL>')
      console.log('🔑 Admin Password: demo123')
    }

    // Create a sample student for testing
    const existingStudents = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: '<EMAIL>',
        },
      },
    })

    if (existingStudents.docs.length === 0) {
      const student = await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'student123',
          firstName: 'Demo',
          lastName: 'Student',
          role: 'student',
          isActive: true,
          emailVerified: true,
        },
      })

      console.log('✅ Demo Student created successfully!')
      console.log('📧 Student Email: <EMAIL>')
      console.log('🔑 Student Password: student123')
    }

    console.log('\n🎉 Database seeding completed!')
    console.log('\n📋 Login Credentials Summary:')
    console.log('┌─────────────────────────────────────────────────────────┐')
    console.log('│ SUPER ADMIN (Payload CMS Admin)                        │')
    console.log('│ URL: http://localhost:3002/admin                       │')
    console.log('│ Email: <EMAIL>                            │')
    console.log('│ Password: SuperAdmin@123                               │')
    console.log('├─────────────────────────────────────────────────────────┤')
    console.log('│ SUPER ADMIN (Frontend)                                 │')
    console.log('│ URL: http://localhost:3002/auth/admin/login            │')
    console.log('│ Email: <EMAIL>                            │')
    console.log('│ Password: SuperAdmin@123                               │')
    console.log('├─────────────────────────────────────────────────────────┤')
    console.log('│ INSTITUTE ADMIN (Frontend)                             │')
    console.log('│ URL: http://localhost:3002/auth/login                  │')
    console.log('│ Email: <EMAIL>                        │')
    console.log('│ Password: demo123                                      │')
    console.log('├─────────────────────────────────────────────────────────┤')
    console.log('│ STUDENT (Frontend)                                     │')
    console.log('│ URL: http://localhost:3002/auth/user-login             │')
    console.log('│ Email: <EMAIL>                                │')
    console.log('│ Password: student123                                   │')
    console.log('└─────────────────────────────────────────────────────────┘')

  } catch (error) {
    console.error('❌ Error seeding database:', error)
  }

  process.exit(0)
}

seed()
