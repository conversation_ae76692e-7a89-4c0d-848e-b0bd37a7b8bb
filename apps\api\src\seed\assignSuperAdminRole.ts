import 'dotenv/config'
import { getPayload } from 'payload'
import config from '../payload.config'

async function assignSuperAdminRole() {
  console.log('🔧 Assigning Super Admin role to existing user...')
  
  try {
    // Initialize Payload
    const payload = await getPayload({ config })
    console.log('✅ Payload initialized successfully')

    // Find the super admin role
    const superAdminRole = await payload.find({
      collection: 'roles',
      where: { code: { equals: 'super_admin' } }
    })

    if (superAdminRole.docs.length === 0) {
      console.error('❌ Super Admin role not found. Please run seed:roles first.')
      process.exit(1)
    }

    const roleId = superAdminRole.docs[0].id
    console.log(`✅ Found Super Admin role with ID: ${roleId}`)

    // Find the super admin user (you can change this email)
    const adminEmail = '<EMAIL>'
    const adminUser = await payload.find({
      collection: 'users',
      where: { email: { equals: adminEmail } }
    })

    if (adminUser.docs.length === 0) {
      console.error(`❌ User with email ${adminEmail} not found.`)
      console.log('Available users:')
      const allUsers = await payload.find({
        collection: 'users',
        limit: 10
      })
      allUsers.docs.forEach(user => {
        console.log(`  - ${user.email} (legacyRole: ${user.legacyRole})`)
      })
      process.exit(1)
    }

    const userId = adminUser.docs[0].id
    console.log(`✅ Found user: ${adminEmail} with ID: ${userId}`)

    // Update the user to assign the super admin role
    await payload.update({
      collection: 'users',
      id: userId,
      data: {
        role: roleId,
        legacyRole: 'super_admin' // Ensure legacyRole is also set
      }
    })

    console.log(`🎉 Successfully assigned Super Admin role to ${adminEmail}`)
    console.log('✅ User can now login and access all features!')
    
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Role assignment failed:', error)
    process.exit(1)
  }
}

// Run the assignment
assignSuperAdminRole()

export default assignSuperAdminRole
