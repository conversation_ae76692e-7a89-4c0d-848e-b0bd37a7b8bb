import 'dotenv/config'
import { getPayload } from 'payload'
import config from '../payload.config'
import { seedUsers } from './users'
import { seedThemes } from './themes'
import { seedLocations } from './locations'
import { seedTaxData } from './tax'
import seedRolesAndPermissions from './seedRolesPermissions'

const seed = async (): Promise<void> => {
  try {
    console.log('Starting database seeding...')
    
    const payload = await getPayload({ config })
    
    // Seed roles and permissions first (needed for users)
    await seedRolesAndPermissions(payload)

    // Seed users
    await seedUsers(payload)

    // Seed themes
    await seedThemes(payload)

    // Seed locations
    await seedLocations(payload)

    // Seed tax data
    await seedTaxData(payload)
    
    console.log('✅ Database seeding completed successfully!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Error during seeding:', error)
    process.exit(1)
  }
}

// Run the seed function
seed()
