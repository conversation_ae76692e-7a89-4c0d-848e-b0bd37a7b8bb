import type { Payload } from 'payload'

export const seedLocations = async (payload: Payload): Promise<void> => {
  try {
    console.log('Seeding locations...')

    // Check if countries already exist
    const existingCountries = await payload.find({
      collection: 'countries',
      limit: 1
    })

    if (existingCountries.docs.length === 0) {
      // Create India
      const india = await payload.create({
        collection: 'countries',
        data: {
          name: 'India',
          code: 'IN',
          isActive: true,
          details: {
            capital: 'New Delhi',
            currency: 'INR',
            timezone: 'Asia/Kolkata',
            phoneCode: '+91'
          }
        }
      })
      console.log('✓ Country created: India')

      // Create states for India
      const maharashtra = await payload.create({
        collection: 'states',
        data: {
          name: 'Maharashtra',
          code: 'MH',
          country: india.id,
          isActive: true,
          details: {
            capital: 'Mumbai',
            type: 'state'
          }
        }
      })

      const karnataka = await payload.create({
        collection: 'states',
        data: {
          name: 'Karnataka',
          code: 'KA',
          country: india.id,
          isActive: true,
          details: {
            capital: 'Bangalore',
            type: 'state'
          }
        }
      })

      const tamilnadu = await payload.create({
        collection: 'states',
        data: {
          name: 'Tamil Nadu',
          code: 'TN',
          country: india.id,
          isActive: true,
          details: {
            capital: 'Chennai',
            type: 'state'
          }
        }
      })
      console.log('✓ States created: Maharashtra, Karnataka, Tamil Nadu')

      // Create districts for Maharashtra
      await payload.create({
        collection: 'districts',
        data: {
          name: 'Mumbai',
          code: 'MUM',
          state: maharashtra.id,
          isActive: true,
          details: {
            type: 'metropolitan',
            pincode: '400001'
          }
        }
      })

      await payload.create({
        collection: 'districts',
        data: {
          name: 'Pune',
          code: 'PUN',
          state: maharashtra.id,
          isActive: true,
          details: {
            type: 'city',
            pincode: '411001'
          }
        }
      })

      // Create districts for Karnataka
      await payload.create({
        collection: 'districts',
        data: {
          name: 'Bangalore Urban',
          code: 'BLR',
          state: karnataka.id,
          isActive: true,
          details: {
            type: 'metropolitan',
            pincode: '560001'
          }
        }
      })

      // Create districts for Tamil Nadu
      await payload.create({
        collection: 'districts',
        data: {
          name: 'Chennai',
          code: 'CHN',
          state: tamilnadu.id,
          isActive: true,
          details: {
            type: 'metropolitan',
            pincode: '600001'
          }
        }
      })

      console.log('✓ Districts created: Mumbai, Pune, Bangalore Urban, Chennai')

      // Create USA for international testing
      const usa = await payload.create({
        collection: 'countries',
        data: {
          name: 'United States',
          code: 'US',
          isActive: true,
          details: {
            capital: 'Washington D.C.',
            currency: 'USD',
            timezone: 'America/New_York',
            phoneCode: '+1'
          }
        }
      })

      const california = await payload.create({
        collection: 'states',
        data: {
          name: 'California',
          code: 'CA',
          country: usa.id,
          isActive: true,
          details: {
            capital: 'Sacramento',
            type: 'state'
          }
        }
      })

      await payload.create({
        collection: 'districts',
        data: {
          name: 'Los Angeles',
          code: 'LA',
          state: california.id,
          isActive: true,
          details: {
            type: 'city',
            pincode: '90001'
          }
        }
      })

      console.log('✓ International locations created: USA, California, Los Angeles')

    } else {
      console.log('✓ Locations already exist')
    }

    console.log('Locations seeding completed!')

  } catch (error) {
    console.error('Error seeding locations:', error)
    throw error
  }
}
