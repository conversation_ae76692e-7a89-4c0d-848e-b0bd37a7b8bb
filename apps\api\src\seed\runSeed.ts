import 'dotenv/config'
import { getPayload } from 'payload'
import config from '../payload.config'
import seedRolesAndPermissions from './seedRolesPermissions'

async function runSeed() {
  console.log('🚀 Starting LMS seeding process...')

  try {
    // Initialize Payload
    const payload = await getPayload({ config })
    console.log('✅ Payload initialized successfully')

    // Run roles and permissions seeding
    await seedRolesAndPermissions(payload)

    console.log('🎉 All seeding completed successfully!')
    process.exit(0)

  } catch (error) {
    console.error('❌ Seeding failed:', error)
    process.exit(1)
  }
}

// Run the seeding - always execute when this file is run
runSeed()

export default runSeed
