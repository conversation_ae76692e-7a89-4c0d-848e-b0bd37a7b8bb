import 'dotenv/config'
import { getPayload } from 'payload'
import config from '../payload.config'
import seedRolesAndPermissions from './seedRolesPermissions'

async function seedRolesOnly() {
  console.log('🚀 Starting roles and permissions seeding only...')
  
  try {
    // Initialize Payload
    const payload = await getPayload({ config })
    console.log('✅ Payload initialized successfully')

    // Run roles and permissions seeding
    await seedRolesAndPermissions(payload)

    console.log('🎉 Roles and permissions seeding completed successfully!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Roles seeding failed:', error)
    process.exit(1)
  }
}

// Run the seeding
seedRolesOnly()

export default seedRolesOnly
