import type { Payload } from 'payload'

// Helper function to generate permissions
const createPermission = (name: string, code: string, description: string, resource: string, action: string, category: string, level: string = '1') => ({
  name,
  code,
  description,
  resource,
  action,
  category,
  level,
  isActive: true
})

// Define essential permissions for the LMS system
const PERMISSIONS = [
  // User Management
  createPermission('View Users', 'view_users', 'View user list and details', 'users', 'view', 'users', '1'),
  createPermission('Create Users', 'create_users', 'Create new users', 'users', 'create', 'users', '1'),
  createPermission('Update Users', 'update_users', 'Edit user information', 'users', 'update', 'users', '1'),
  createPermission('Delete Users', 'delete_users', 'Delete users from system', 'users', 'delete', 'users', '1'),
  createPermission('Manage Users', 'manage_users', 'Full user management access', 'users', 'manage', 'users', '1'),

  // Staff Management (Institute Level)
  createPermission('View Staff', 'view_staff', 'View staff list and details', 'staff', 'view', 'users', '2'),
  createPermission('Create Staff', 'create_staff', 'Create new staff accounts', 'staff', 'create', 'users', '2'),
  createPermission('Update Staff', 'update_staff', 'Edit staff information', 'staff', 'update', 'users', '2'),
  createPermission('Delete Staff', 'delete_staff', 'Delete staff accounts', 'staff', 'delete', 'users', '2'),
  createPermission('Manage Staff', 'manage_staff', 'Full staff management', 'staff', 'manage', 'users', '2'),
  createPermission('Assign Staff Roles', 'assign_staff_roles', 'Assign roles to staff members', 'staff', 'execute', 'users', '2'),
  createPermission('Assign Staff Branches', 'assign_staff_branches', 'Assign staff to branches', 'staff', 'execute', 'users', '2'),

  // Trainer Management
  createPermission('View Trainers', 'view_trainers', 'View trainer list and details', 'trainers', 'view', 'users', '2'),
  createPermission('Create Trainers', 'create_trainers', 'Create new trainer accounts', 'trainers', 'create', 'users', '2'),
  createPermission('Update Trainers', 'update_trainers', 'Edit trainer information', 'trainers', 'update', 'users', '2'),
  createPermission('Delete Trainers', 'delete_trainers', 'Delete trainer accounts', 'trainers', 'delete', 'users', '2'),
  createPermission('Manage Trainers', 'manage_trainers', 'Full trainer management', 'trainers', 'manage', 'users', '2'),

  // Role & Permission Management
  createPermission('View Roles', 'view_roles', 'View roles and their details', 'roles', 'view', 'users', '1'),
  createPermission('Create Roles', 'create_roles', 'Create new roles', 'roles', 'create', 'users', '1'),
  createPermission('Update Roles', 'update_roles', 'Edit role information', 'roles', 'update', 'users', '1'),
  createPermission('Delete Roles', 'delete_roles', 'Delete roles from system', 'roles', 'delete', 'users', '1'),
  createPermission('Manage Roles', 'manage_roles', 'Full role management access', 'roles', 'manage', 'users', '1'),

  createPermission('View Permissions', 'view_permissions', 'View permissions list', 'permissions', 'view', 'users', '1'),
  createPermission('Create Permissions', 'create_permissions', 'Create new permissions', 'permissions', 'create', 'users', '1'),
  createPermission('Update Permissions', 'update_permissions', 'Edit permission details', 'permissions', 'update', 'users', '1'),
  createPermission('Delete Permissions', 'delete_permissions', 'Delete permissions', 'permissions', 'delete', 'users', '1'),
  createPermission('Manage Permissions', 'manage_permissions', 'Full permission management', 'permissions', 'manage', 'users', '1'),

  // Institute Management
  createPermission('View Institutes', 'view_institutes', 'View institute list and details', 'institutes', 'view', 'institute', '1'),
  createPermission('Create Institutes', 'create_institutes', 'Create new institutes', 'institutes', 'create', 'institute', '1'),
  createPermission('Update Institutes', 'update_institutes', 'Edit institute information', 'institutes', 'update', 'institute', '1'),
  createPermission('Delete Institutes', 'delete_institutes', 'Delete institutes', 'institutes', 'delete', 'institute', '1'),
  createPermission('Manage Institutes', 'manage_institutes', 'Full institute management', 'institutes', 'manage', 'institute', '1'),

  // Course Management
  createPermission('View Courses', 'view_courses', 'View course list and details', 'courses', 'view', 'courses', '2'),
  createPermission('Create Courses', 'create_courses', 'Create new courses', 'courses', 'create', 'courses', '2'),
  createPermission('Update Courses', 'update_courses', 'Edit course information', 'courses', 'update', 'courses', '2'),
  createPermission('Delete Courses', 'delete_courses', 'Delete courses', 'courses', 'delete', 'courses', '2'),
  createPermission('Manage Courses', 'manage_courses', 'Full course management', 'courses', 'manage', 'courses', '2'),

  // Student Management
  createPermission('View Students', 'view_students', 'View student list and details', 'students', 'view', 'students', '2'),
  createPermission('Create Students', 'create_students', 'Create new student accounts', 'students', 'create', 'students', '2'),
  createPermission('Update Students', 'update_students', 'Edit student information', 'students', 'update', 'students', '2'),
  createPermission('Delete Students', 'delete_students', 'Delete student accounts', 'students', 'delete', 'students', '2'),
  createPermission('Manage Students', 'manage_students', 'Full student management', 'students', 'manage', 'students', '2'),
  createPermission('Activate Students', 'activate_students', 'Activate/deactivate student accounts', 'students', 'execute', 'students', '2'),
  createPermission('View Student Profile', 'view_student_profile', 'View detailed student profiles', 'students', 'view', 'students', '2'),
  createPermission('Update Student Profile', 'update_student_profile', 'Edit student profile information', 'students', 'update', 'students', '2'),
  createPermission('Reset Student Password', 'reset_student_password', 'Reset student passwords', 'students', 'execute', 'students', '2'),
  createPermission('Assign Student Branch', 'assign_student_branch', 'Assign students to branches', 'students', 'execute', 'students', '2'),
  createPermission('Assign Student Role', 'assign_student_role', 'Assign roles to students', 'students', 'execute', 'students', '2'),
  createPermission('View Student Analytics', 'view_student_analytics', 'View student performance analytics', 'students', 'view', 'students', '2'),
  createPermission('Export Student Data', 'export_student_data', 'Export student information and reports', 'students', 'export', 'students', '2'),
  createPermission('Import Student Data', 'import_student_data', 'Import student data in bulk', 'students', 'create', 'students', '2'),

  // Branch Management
  createPermission('View Branches', 'view_branches', 'View branch list and details', 'branches', 'view', 'branch', '2'),
  createPermission('Create Branches', 'create_branches', 'Create new branches', 'branches', 'create', 'branch', '2'),
  createPermission('Update Branches', 'update_branches', 'Edit branch information', 'branches', 'update', 'branch', '2'),
  createPermission('Delete Branches', 'delete_branches', 'Delete branches', 'branches', 'delete', 'branch', '2'),
  createPermission('Manage Branches', 'manage_branches', 'Full branch management', 'branches', 'manage', 'branch', '2'),

  // System Settings
  createPermission('View Settings', 'view_settings', 'View system settings', 'settings', 'view', 'settings', '1'),
  createPermission('Update Settings', 'update_settings', 'Modify system settings', 'settings', 'update', 'settings', '1'),
  createPermission('Manage Settings', 'manage_settings', 'Full settings management', 'settings', 'manage', 'settings', '1'),

  // Analytics & Reports
  createPermission('View Analytics', 'view_analytics', 'View system analytics', 'analytics', 'view', 'reports', '1'),
  createPermission('View Reports', 'view_reports', 'View system reports', 'reports', 'view', 'reports', '1'),
  createPermission('Export Reports', 'export_reports', 'Export reports and data', 'reports', 'export', 'reports', '1'),

  // Billing & Finance
  createPermission('View Billing', 'view_billing', 'View billing information', 'billing', 'view', 'billing', '1'),
  createPermission('Manage Billing', 'manage_billing', 'Manage billing and payments', 'billing', 'manage', 'billing', '1'),

  // Dashboard Management
  createPermission('View Dashboard', 'view_dashboard', 'Access to dashboard overview', 'dashboard', 'view', 'platform', '1'),
  createPermission('Manage Dashboard', 'manage_dashboard', 'Full dashboard management', 'dashboard', 'manage', 'platform', '1'),

  // Location Management (Countries, States, Districts)
  createPermission('View Countries', 'view_countries', 'View countries list', 'countries', 'view', 'platform', '1'),
  createPermission('Create Countries', 'create_countries', 'Create new countries', 'countries', 'create', 'platform', '1'),
  createPermission('Update Countries', 'update_countries', 'Edit country information', 'countries', 'update', 'platform', '1'),
  createPermission('Delete Countries', 'delete_countries', 'Delete countries', 'countries', 'delete', 'platform', '1'),
  createPermission('Manage Countries', 'manage_countries', 'Full country management', 'countries', 'manage', 'platform', '1'),

  createPermission('View States', 'view_states', 'View states list', 'states', 'view', 'platform', '1'),
  createPermission('Create States', 'create_states', 'Create new states', 'states', 'create', 'platform', '1'),
  createPermission('Update States', 'update_states', 'Edit state information', 'states', 'update', 'platform', '1'),
  createPermission('Delete States', 'delete_states', 'Delete states', 'states', 'delete', 'platform', '1'),
  createPermission('Manage States', 'manage_states', 'Full state management', 'states', 'manage', 'platform', '1'),

  createPermission('View Districts', 'view_districts', 'View districts list', 'districts', 'view', 'platform', '1'),
  createPermission('Create Districts', 'create_districts', 'Create new districts', 'districts', 'create', 'platform', '1'),
  createPermission('Update Districts', 'update_districts', 'Edit district information', 'districts', 'update', 'platform', '1'),
  createPermission('Delete Districts', 'delete_districts', 'Delete districts', 'districts', 'delete', 'platform', '1'),
  createPermission('Manage Districts', 'manage_districts', 'Full district management', 'districts', 'manage', 'platform', '1'),

  createPermission('View Locations', 'view_locations', 'View all location data', 'locations', 'view', 'platform', '1'),
  createPermission('Manage Locations', 'manage_locations', 'Full location management', 'locations', 'manage', 'platform', '1'),

  // Tax Management
  createPermission('View Taxes', 'view_taxes', 'View tax configurations', 'taxes', 'view', 'billing', '1'),
  createPermission('Create Taxes', 'create_taxes', 'Create new tax configurations', 'taxes', 'create', 'billing', '1'),
  createPermission('Update Taxes', 'update_taxes', 'Edit tax configurations', 'taxes', 'update', 'billing', '1'),
  createPermission('Delete Taxes', 'delete_taxes', 'Delete tax configurations', 'taxes', 'delete', 'billing', '1'),
  createPermission('Manage Taxes', 'manage_taxes', 'Full tax management', 'taxes', 'manage', 'billing', '1'),
  createPermission('Calculate Taxes', 'calculate_taxes', 'Calculate tax amounts', 'taxes', 'execute', 'billing', '1'),

  // Theme Management
  createPermission('View Themes', 'view_themes', 'View available themes', 'themes', 'view', 'platform', '1'),
  createPermission('Create Themes', 'create_themes', 'Create new themes', 'themes', 'create', 'platform', '1'),
  createPermission('Update Themes', 'update_themes', 'Edit theme configurations', 'themes', 'update', 'platform', '1'),
  createPermission('Delete Themes', 'delete_themes', 'Delete themes', 'themes', 'delete', 'platform', '1'),
  createPermission('Manage Themes', 'manage_themes', 'Full theme management', 'themes', 'manage', 'platform', '1'),
  createPermission('Apply Themes', 'apply_themes', 'Apply themes to institutes', 'themes', 'execute', 'platform', '1'),

  // Content Management
  createPermission('View Content', 'view_content', 'View content and materials', 'content', 'view', 'courses', '2'),
  createPermission('Create Content', 'create_content', 'Create new content', 'content', 'create', 'courses', '2'),
  createPermission('Update Content', 'update_content', 'Edit content and materials', 'content', 'update', 'courses', '2'),
  createPermission('Delete Content', 'delete_content', 'Delete content', 'content', 'delete', 'courses', '2'),
  createPermission('Manage Content', 'manage_content', 'Full content management', 'content', 'manage', 'courses', '2'),

  // Live Classes
  createPermission('View Live Classes', 'view_live_classes', 'View live class schedules', 'live_classes', 'view', 'courses', '2'),
  createPermission('Create Live Classes', 'create_live_classes', 'Schedule live classes', 'live_classes', 'create', 'courses', '2'),
  createPermission('Update Live Classes', 'update_live_classes', 'Edit live class details', 'live_classes', 'update', 'courses', '2'),
  createPermission('Delete Live Classes', 'delete_live_classes', 'Delete live classes', 'live_classes', 'delete', 'courses', '2'),
  createPermission('Manage Live Classes', 'manage_live_classes', 'Full live class management', 'live_classes', 'manage', 'courses', '2'),
  createPermission('Join Live Classes', 'join_live_classes', 'Join live classes as participant', 'live_classes', 'execute', 'courses', '3'),

  // Assignments & Exams
  createPermission('View Assignments', 'view_assignments', 'View assignments and exams', 'assignments', 'view', 'courses', '2'),
  createPermission('Create Assignments', 'create_assignments', 'Create assignments and exams', 'assignments', 'create', 'courses', '2'),
  createPermission('Update Assignments', 'update_assignments', 'Edit assignment details', 'assignments', 'update', 'courses', '2'),
  createPermission('Delete Assignments', 'delete_assignments', 'Delete assignments', 'assignments', 'delete', 'courses', '2'),
  createPermission('Grade Assignments', 'grade_assignments', 'Grade student submissions', 'assignments', 'execute', 'courses', '2'),
  createPermission('Submit Assignments', 'submit_assignments', 'Submit assignment solutions', 'assignments', 'execute', 'courses', '3'),

  // Communication & Notifications
  createPermission('Send Notifications', 'send_notifications', 'Send notifications to users', 'notifications', 'create', 'platform', '2'),
  createPermission('Manage Communication', 'manage_communication', 'Manage all communication features', 'communication', 'manage', 'platform', '1'),

  // Website Management
  createPermission('View Website', 'view_website', 'View website settings', 'website', 'view', 'platform', '2'),
  createPermission('Update Website', 'update_website', 'Edit website content', 'website', 'update', 'platform', '2'),
  createPermission('Manage Website', 'manage_website', 'Full website management', 'website', 'manage', 'platform', '2'),

  // System Administration
  createPermission('System Admin', 'system_admin', 'Full system administration access', 'system', 'manage', 'platform', '1'),
  createPermission('Access All', 'access_all', 'Access to all system features', 'all', 'manage', 'platform', '1'),


]

// Define roles with their basic information
const ROLES = [
  {
    name: 'Super Admin',
    code: 'super_admin',
    description: 'Full system access with all permissions',
    level: '1',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 1,
    metadata: {
      maxUsers: null,
      autoAssign: false,
      requiresApproval: false
    }
  },
  {
    name: 'Platform Staff',
    code: 'platform_staff',
    description: 'Platform staff with administrative access',
    level: '1',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 2,
    metadata: {
      maxUsers: null,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Institute Admin',
    code: 'institute_admin',
    description: 'Institute administrator with full institute access',
    level: '2',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 3,
    metadata: {
      maxUsers: null,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Branch Manager',
    code: 'branch_manager',
    description: 'Branch manager with branch-level access',
    level: '3',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 4,
    metadata: {
      maxUsers: 50,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Trainer',
    code: 'trainer',
    description: 'Course trainer with teaching permissions',
    level: '3',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 5,
    metadata: {
      maxUsers: 100,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Institute Staff',
    code: 'institute_staff',
    description: 'Institute staff with limited administrative access',
    level: '3',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 6,
    metadata: {
      maxUsers: 20,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Student',
    code: 'student',
    description: 'Student with learning access',
    level: '3',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 7,
    metadata: {
      maxUsers: null,
      autoAssign: true,
      requiresApproval: false
    }
  }
]

// Define which permissions each role should have
const ROLE_PERMISSIONS = {
  super_admin: [
    // Super Admin gets ALL permissions
    ...PERMISSIONS.map(p => p.code)
  ],
  platform_staff: [
    'view_dashboard', 'view_users', 'view_institutes', 'view_analytics', 'view_reports', 'view_settings',
    'manage_institutes', 'view_billing', 'export_reports', 'view_themes', 'manage_themes',
    'view_locations', 'view_countries', 'view_states', 'view_districts'
  ],
  institute_admin: [
    // Dashboard & User Management
    'view_dashboard', 'view_users', 'create_users', 'update_users', 'manage_users',

    // Staff & Trainer Management
    'view_staff', 'create_staff', 'update_staff', 'delete_staff', 'manage_staff',
    'assign_staff_roles', 'assign_staff_branches',
    'view_trainers', 'create_trainers', 'update_trainers', 'delete_trainers', 'manage_trainers',

    // Course Management
    'view_courses', 'create_courses', 'update_courses', 'delete_courses', 'manage_courses',

    // Comprehensive Student Management
    'view_students', 'create_students', 'update_students', 'delete_students', 'manage_students',
    'activate_students', 'view_student_profile', 'update_student_profile', 'reset_student_password',
    'assign_student_branch', 'assign_student_role', 'view_student_analytics', 'export_student_data', 'import_student_data',

    // Branch Management
    'view_branches', 'create_branches', 'update_branches', 'delete_branches', 'manage_branches',

    // Content & Learning Management
    'view_content', 'create_content', 'update_content', 'delete_content', 'manage_content',
    'view_live_classes', 'create_live_classes', 'update_live_classes', 'delete_live_classes', 'manage_live_classes',
    'view_assignments', 'create_assignments', 'update_assignments', 'delete_assignments', 'grade_assignments',

    // Analytics & Reports
    'view_analytics', 'view_reports', 'export_reports', 'view_billing', 'view_taxes',

    // Communication & Website
    'send_notifications', 'view_website', 'update_website', 'manage_website'
  ],
  branch_manager: [
    // Dashboard & User Management
    'view_dashboard', 'view_users', 'create_users', 'update_users',

    // Course Management
    'view_courses', 'create_courses', 'update_courses', 'manage_courses',

    // Student Management (Branch Level)
    'view_students', 'create_students', 'update_students', 'manage_students',
    'activate_students', 'view_student_profile', 'update_student_profile', 'reset_student_password',
    'assign_student_role', 'view_student_analytics', 'export_student_data',

    // Branch Management
    'view_branches', 'update_branches',

    // Content & Learning Management
    'view_content', 'create_content', 'update_content', 'manage_content',
    'view_live_classes', 'create_live_classes', 'update_live_classes', 'manage_live_classes',
    'view_assignments', 'create_assignments', 'update_assignments', 'grade_assignments',

    // Analytics & Communication
    'view_analytics', 'view_reports', 'send_notifications'
  ],
  trainer: [
    'view_dashboard', 'view_courses', 'update_courses',
    'view_students', 'update_students',
    'view_content', 'create_content', 'update_content',
    'view_live_classes', 'create_live_classes', 'update_live_classes', 'manage_live_classes',
    'view_assignments', 'create_assignments', 'update_assignments', 'grade_assignments',
    'join_live_classes', 'send_notifications'
  ],
  institute_staff: [
    'view_dashboard', 'view_users', 'view_courses', 'view_students', 'update_students',
    'view_content', 'view_live_classes', 'view_assignments', 'send_notifications'
  ],
  student: [
    'view_dashboard', 'view_courses', 'view_students', 'view_content',
    'view_live_classes', 'join_live_classes', 'view_assignments', 'submit_assignments'
  ]
}

export async function seedRolesAndPermissions(payload: Payload) {
  console.log('🌱 Starting roles and permissions seeding...')

  try {
    // 1. Create Permissions
    console.log('📝 Creating permissions...')
    const createdPermissions: any[] = []
    
    for (const permission of PERMISSIONS) {
      try {
        // Check if permission already exists
        const existing = await payload.find({
          collection: 'permissions',
          where: { code: { equals: permission.code } }
        })

        if (existing.docs.length === 0) {
          const created = await payload.create({
            collection: 'permissions',
            data: permission as any
          })
          createdPermissions.push(created)
          console.log(`  ✅ Created permission: ${permission.name}`)
        } else {
          createdPermissions.push(existing.docs[0])
          console.log(`  ⏭️  Permission already exists: ${permission.name}`)
        }
      } catch (error) {
        console.error(`  ❌ Error creating permission ${permission.name}:`, error)
      }
    }

    // 2. Create Roles
    console.log('👥 Creating roles...')
    const createdRoles: any[] = []
    
    for (const role of ROLES) {
      try {
        // Check if role already exists
        const existing = await payload.find({
          collection: 'roles',
          where: { code: { equals: role.code } }
        })

        if (existing.docs.length === 0) {
          const created = await payload.create({
            collection: 'roles',
            data: role as any
          })
          createdRoles.push(created)
          console.log(`  ✅ Created role: ${role.name}`)
        } else {
          createdRoles.push(existing.docs[0])
          console.log(`  ⏭️  Role already exists: ${role.name}`)
        }
      } catch (error) {
        console.error(`  ❌ Error creating role ${role.name}:`, error)
      }
    }

    // 3. Create Role-Permission relationships
    console.log('🔗 Creating role-permission relationships...')
    
    for (const [roleCode, permissionCodes] of Object.entries(ROLE_PERMISSIONS)) {
      const role = createdRoles.find(r => r.code === roleCode)
      if (!role) {
        console.log(`  ⚠️  Role not found: ${roleCode}`)
        continue
      }

      console.log(`  🔗 Assigning permissions to ${role.name}...`)
      
      for (const permissionCode of permissionCodes) {
        const permission = createdPermissions.find(p => p.code === permissionCode)
        if (!permission) {
          console.log(`    ⚠️  Permission not found: ${permissionCode}`)
          continue
        }

        try {
          // Check if relationship already exists
          const existing = await payload.find({
            collection: 'role-permissions',
            where: {
              and: [
                { role: { equals: role.id } },
                { permission: { equals: permission.id } }
              ]
            }
          })

          if (existing.docs.length === 0) {
            await payload.create({
              collection: 'role-permissions',
              data: {
                role: role.id,
                permission: permission.id,
                scope: 'global',
                isActive: true,
                assignedAt: new Date().toISOString()
              } as any
            })
            console.log(`    ✅ Assigned ${permission.name} to ${role.name}`)
          } else {
            console.log(`    ⏭️  Permission already assigned: ${permission.name} to ${role.name}`)
          }
        } catch (error) {
          console.error(`    ❌ Error assigning ${permission.name} to ${role.name}:`, error)
        }
      }
    }

    console.log('🎉 Roles and permissions seeding completed successfully!')
    
    // Summary
    console.log('\n📊 Seeding Summary:')
    console.log(`  Permissions: ${createdPermissions.length}`)
    console.log(`  Roles: ${createdRoles.length}`)
    console.log(`  Super Admin permissions: ${ROLE_PERMISSIONS.super_admin.length}`)
    
    return {
      permissions: createdPermissions,
      roles: createdRoles,
      success: true
    }

  } catch (error) {
    console.error('❌ Error during roles and permissions seeding:', error)
    throw error
  }
}

export default seedRolesAndPermissions
