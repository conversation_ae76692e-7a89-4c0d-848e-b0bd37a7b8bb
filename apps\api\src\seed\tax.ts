import type { Payload } from 'payload'

export const seedTaxData = async (payload: Payload): Promise<void> => {
  try {
    console.log('Seeding tax data...')

    // Check if tax components already exist
    const existingComponents = await payload.find({
      collection: 'tax-components',
      limit: 1
    })

    if (existingComponents.docs.length === 0) {
      // Get India for location reference
      const india = await payload.find({
        collection: 'countries',
        where: { code: { equals: 'IN' } },
        limit: 1
      })

      const indiaId = india.docs[0]?.id

      // Create CGST component
      const cgst = await payload.create({
        collection: 'tax-components',
        data: {
          name: 'Central Goods and Services Tax',
          code: 'CGST',
          type: 'gst',
          description: 'Central GST component - 9% of transaction value',
          rate: 9,
          calculationMethod: 'percentage',
          isActive: true,
          effectiveFrom: new Date('2017-07-01'),
          priority: 1,
          applicableRegions: indiaId ? [{
            country: indiaId,
            states: []
          }] : [],
          metadata: {
            gstType: 'central',
            hsn: 'applicable_to_all'
          }
        }
      })

      // Create SGST component
      const sgst = await payload.create({
        collection: 'tax-components',
        data: {
          name: 'State Goods and Services Tax',
          code: 'SGST',
          type: 'gst',
          description: 'State GST component - 9% of transaction value',
          rate: 9,
          calculationMethod: 'percentage',
          isActive: true,
          effectiveFrom: new Date('2017-07-01'),
          priority: 2,
          applicableRegions: indiaId ? [{
            country: indiaId,
            states: []
          }] : [],
          metadata: {
            gstType: 'state',
            hsn: 'applicable_to_all'
          }
        }
      })

      // Create IGST component (for inter-state transactions)
      const igst = await payload.create({
        collection: 'tax-components',
        data: {
          name: 'Integrated Goods and Services Tax',
          code: 'IGST',
          type: 'gst',
          description: 'Integrated GST for inter-state transactions - 18% of transaction value',
          rate: 18,
          calculationMethod: 'percentage',
          isActive: true,
          effectiveFrom: new Date('2017-07-01'),
          priority: 3,
          applicableRegions: indiaId ? [{
            country: indiaId,
            states: []
          }] : [],
          metadata: {
            gstType: 'integrated',
            hsn: 'applicable_to_all'
          }
        }
      })

      console.log('✓ Tax components created: CGST, SGST, IGST')

      // Create tax groups
      const intraStateGroup = await payload.create({
        collection: 'tax-groups',
        data: {
          name: 'Intra-State GST',
          code: 'INTRA_GST',
          description: 'Tax group for transactions within the same state (CGST + SGST)',
          components: [cgst.id, sgst.id],
          isActive: true,
          applicableScenarios: ['same_state_transaction'],
          totalRate: 18,
          metadata: {
            transactionType: 'intra_state',
            applicableFor: ['course_purchase', 'subscription']
          }
        }
      })

      const interStateGroup = await payload.create({
        collection: 'tax-groups',
        data: {
          name: 'Inter-State GST',
          code: 'INTER_GST',
          description: 'Tax group for transactions between different states (IGST)',
          components: [igst.id],
          isActive: true,
          applicableScenarios: ['different_state_transaction'],
          totalRate: 18,
          metadata: {
            transactionType: 'inter_state',
            applicableFor: ['course_purchase', 'subscription']
          }
        }
      })

      console.log('✓ Tax groups created: Intra-State GST, Inter-State GST')

      // Create tax rules
      await payload.create({
        collection: 'tax-rules',
        data: {
          name: 'Same State Course Purchase',
          description: 'Apply CGST + SGST for course purchases within the same state',
          taxGroup: intraStateGroup.id,
          priority: 10,
          isActive: true,
          conditions: {
            transactionType: 'course_purchase',
            customerLocation: 'same_state_as_institute',
            minAmount: 0
          },
          applicableFrom: new Date('2017-07-01'),
          metadata: {
            ruleType: 'location_based',
            autoApply: true
          }
        }
      })

      await payload.create({
        collection: 'tax-rules',
        data: {
          name: 'Different State Course Purchase',
          description: 'Apply IGST for course purchases between different states',
          taxGroup: interStateGroup.id,
          priority: 9,
          isActive: true,
          conditions: {
            transactionType: 'course_purchase',
            customerLocation: 'different_state_from_institute',
            minAmount: 0
          },
          applicableFrom: new Date('2017-07-01'),
          metadata: {
            ruleType: 'location_based',
            autoApply: true
          }
        }
      })

      await payload.create({
        collection: 'tax-rules',
        data: {
          name: 'High Value Transaction',
          description: 'Special handling for transactions above ₹50,000',
          taxGroup: intraStateGroup.id,
          priority: 15,
          isActive: true,
          conditions: {
            transactionType: 'course_purchase',
            minAmount: 50000
          },
          applicableFrom: new Date('2017-07-01'),
          metadata: {
            ruleType: 'amount_based',
            autoApply: true,
            requiresApproval: true
          }
        }
      })

      console.log('✓ Tax rules created: Same State, Different State, High Value')

    } else {
      console.log('✓ Tax data already exists')
    }

    console.log('Tax data seeding completed!')

  } catch (error) {
    console.error('Error seeding tax data:', error)
    throw error
  }
}
