import 'dotenv/config'

async function testLogin() {
  console.log('🔍 Testing login endpoint...')
  
  try {
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '123456',
        userType: 'super_admin'
      })
    })

    if (!response.ok) {
      console.error(`❌ Login failed with status: ${response.status}`)
      const errorText = await response.text()
      console.error('Error response:', errorText)
      process.exit(1)
    }

    const data = await response.json()
    console.log('✅ Login successful!')
    
    console.log('\n📊 Response Analysis:')
    console.log(`User ID: ${data.user.id}`)
    console.log(`User Email: ${data.user.email}`)
    console.log(`Legacy Role: ${data.user.legacyRole}`)
    console.log(`Role Name: ${data.user.role?.name}`)
    console.log(`Role Code: ${data.user.role?.code}`)
    console.log(`Role Permissions Count: ${data.user.role?.permissions?.length || 0}`)
    
    if (data.user.role?.permissions?.length > 0) {
      console.log('\n✅ SUCCESS: Permissions found in login response!')
      console.log('\n📝 Sample permissions:')
      data.user.role.permissions.slice(0, 5).forEach((permission: any, index: number) => {
        console.log(`  ${index + 1}. ${permission.name} (${permission.code})`)
      })
      if (data.user.role.permissions.length > 5) {
        console.log(`  ... and ${data.user.role.permissions.length - 5} more permissions`)
      }
    } else {
      console.log('\n❌ PROBLEM: No permissions found in login response!')
      console.log('Role object:', JSON.stringify(data.user.role, null, 2))
    }

    // Test token verification
    console.log('\n🔍 Testing token verification...')
    const verifyResponse = await fetch('http://localhost:3001/api/auth/verify', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${data.token}`,
        'Content-Type': 'application/json',
      }
    })

    if (!verifyResponse.ok) {
      console.error(`❌ Token verification failed with status: ${verifyResponse.status}`)
      const errorText = await verifyResponse.text()
      console.error('Error response:', errorText)
    } else {
      const verifyData = await verifyResponse.json()
      console.log('✅ Token verification successful!')
      console.log(`Verify Permissions Count: ${verifyData.user.role?.permissions?.length || 0}`)
      
      if (verifyData.user.role?.permissions?.length > 0) {
        console.log('✅ SUCCESS: Permissions found in verify response!')
      } else {
        console.log('❌ PROBLEM: No permissions found in verify response!')
      }
    }

    console.log('\n🎉 Login test completed!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    process.exit(1)
  }
}

// Run the test
testLogin()

export default testLogin
