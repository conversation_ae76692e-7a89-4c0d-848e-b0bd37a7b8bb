import 'dotenv/config'
import { getPayload } from 'payload'
import config from '../payload.config'

async function testRolePermissions() {
  console.log('🔍 Testing role-permission relationships...')
  
  try {
    // Initialize Payload
    const payload = await getPayload({ config })
    console.log('✅ Payload initialized successfully')

    // Find the super admin role
    const superAdminRole = await payload.find({
      collection: 'roles',
      where: { code: { equals: 'super_admin' } }
    })

    if (superAdminRole.docs.length === 0) {
      console.error('❌ Super Admin role not found')
      process.exit(1)
    }

    const role = superAdminRole.docs[0]
    console.log(`✅ Found Super Admin role: ${role.name} (ID: ${role.id})`)

    // Find role-permission relationships for super admin
    const rolePermissions = await payload.find({
      collection: 'role-permissions',
      where: {
        and: [
          { role: { equals: role.id } },
          { isActive: { equals: true } }
        ]
      },
      depth: 2, // Include permission details
      limit: 1000
    })

    console.log(`📊 Found ${rolePermissions.docs.length} role-permission relationships`)

    // Extract and display permissions
    const permissions = rolePermissions.docs
      .map((rp: any) => rp.permission)
      .filter((permission: any) => permission && permission.isActive)

    console.log(`✅ Active permissions: ${permissions.length}`)

    // Display first 10 permissions as sample
    console.log('\n📝 Sample permissions:')
    permissions.slice(0, 10).forEach((permission: any, index: number) => {
      console.log(`  ${index + 1}. ${permission.name} (${permission.code})`)
    })

    if (permissions.length > 10) {
      console.log(`  ... and ${permissions.length - 10} more permissions`)
    }

    // Test the user with super admin role
    console.log('\n👤 Testing user with super admin role...')
    const adminUser = await payload.find({
      collection: 'users',
      where: { email: { equals: '<EMAIL>' } }
    })

    if (adminUser.docs.length === 0) {
      console.error('❌ Admin user not found')
      process.exit(1)
    }

    const user = adminUser.docs[0]
    console.log(`✅ Found user: ${user.email}`)
    console.log(`   Role: ${JSON.stringify(user.role)}`)
    console.log(`   Role Type: ${typeof user.role}`)
    console.log(`   Legacy Role: ${user.legacyRole}`)

    // Determine role ID
    let roleId = null
    if (user.role) {
      if (typeof user.role === 'number') {
        roleId = user.role
        console.log(`   Role ID (number): ${roleId}`)
      } else if (typeof user.role === 'object' && user.role.id) {
        roleId = user.role.id
        console.log(`   Role ID (from object): ${roleId}`)
      } else {
        console.log(`   Role structure: ${JSON.stringify(user.role)}`)
      }
    }

    // Simulate the authentication logic
    if (roleId) {
      console.log('\n🔄 Simulating authentication logic...')
      
      // Get role details
      const roleDetails = await payload.findByID({
        collection: 'roles',
        id: roleId,
        depth: 1
      })

      console.log(`✅ Role details: ${roleDetails.name}`)

      // Get role-permission relationships
      const userRolePermissions = await payload.find({
        collection: 'role-permissions',
        where: {
          and: [
            { role: { equals: roleDetails.id } },
            { isActive: { equals: true } }
          ]
        },
        depth: 2,
        limit: 1000
      })

      console.log(`✅ User role-permission relationships: ${userRolePermissions.docs.length}`)

      // Extract permissions
      const userPermissions = userRolePermissions.docs
        .map((rp: any) => rp.permission)
        .filter((permission: any) => permission && permission.isActive)

      console.log(`✅ User active permissions: ${userPermissions.length}`)

      // Create the response object that would be sent to frontend
      const responseUser = {
        ...user,
        role: {
          ...roleDetails,
          permissions: userPermissions
        }
      }

      console.log('\n📤 Response structure:')
      console.log(`   User ID: ${responseUser.id}`)
      console.log(`   User Email: ${responseUser.email}`)
      console.log(`   Role Name: ${responseUser.role.name}`)
      console.log(`   Role Permissions: ${responseUser.role.permissions.length}`)

      if (responseUser.role.permissions.length === 0) {
        console.error('❌ PROBLEM: No permissions found in response!')
        
        // Debug the role-permission relationships
        console.log('\n🔍 Debugging role-permission relationships...')
        const debugRolePermissions = await payload.find({
          collection: 'role-permissions',
          where: { role: { equals: roleDetails.id } },
          depth: 0,
          limit: 10
        })
        
        console.log(`Debug: Found ${debugRolePermissions.docs.length} raw role-permission records`)
        debugRolePermissions.docs.forEach((rp: any, index: number) => {
          console.log(`  ${index + 1}. Role: ${rp.role}, Permission: ${rp.permission}, Active: ${rp.isActive}`)
        })
      } else {
        console.log('✅ SUCCESS: Permissions found in response!')
      }
    }

    console.log('\n🎉 Role-permission test completed!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    process.exit(1)
  }
}

// Run the test
testRolePermissions()

export default testRolePermissions
