import type { Payload } from 'payload'

export const seedThemes = async (payload: Payload): Promise<void> => {
  try {
    console.log('Seeding themes...')

    // Check if themes already exist
    const existingThemes = await payload.find({
      collection: 'themes',
      limit: 1
    })

    if (existingThemes.totalDocs > 0) {
      console.log('Themes already exist, skipping seed')
      return
    }

    // Platform themes
    const platformThemes = [
      {
        name: 'SaaS Modern',
        slug: 'saas-modern',
        type: 'platform',
        version: '1.0.0',
        description: 'Modern SaaS landing page theme with clean design and professional layout',
        category: 'saas_modern',
        isActive: true,
        isDefault: true,
        isFeatured: true,
        colors: {
          primary: '#3B82F6',
          secondary: '#10B981',
          accent: '#F59E0B',
          background: '#FFFFFF',
          text: '#1F2937',
          muted: '#6B7280',
          border: '#E5E7EB'
        },
        fonts: {
          heading: 'Inter, sans-serif',
          body: 'Inter, sans-serif',
          mono: 'JetBrains Mono, monospace'
        },
        features: [
          { feature: 'Responsive Design' },
          { feature: 'Hero Section' },
          { feature: 'Features Showcase' },
          { feature: 'Pricing Table' },
          { feature: 'Contact Form' }
        ],
        rating: {
          average: 4.8,
          count: 156
        }
      },
      {
        name: 'Corporate Clean',
        slug: 'corporate-clean',
        type: 'platform',
        version: '1.0.0',
        description: 'Professional corporate theme with emphasis on trust and reliability',
        category: 'saas_corporate',
        isActive: false,
        isDefault: false,
        isFeatured: false,
        colors: {
          primary: '#1E40AF',
          secondary: '#059669',
          accent: '#DC2626',
          background: '#F9FAFB',
          text: '#111827',
          muted: '#6B7280',
          border: '#D1D5DB'
        },
        fonts: {
          heading: 'Roboto, sans-serif',
          body: 'Open Sans, sans-serif',
          mono: 'Fira Code, monospace'
        },
        features: [
          { feature: 'Responsive Design' },
          { feature: 'Hero Section' },
          { feature: 'Testimonials' },
          { feature: 'Team Section' },
          { feature: 'Blog Integration' }
        ],
        rating: {
          average: 4.6,
          count: 89
        }
      }
    ]

    // Institute themes
    const instituteThemes = [
      {
        name: 'Education Modern',
        slug: 'education-modern',
        type: 'institute',
        version: '1.0.0',
        description: 'Modern education theme with course marketplace functionality',
        category: 'education_modern',
        isActive: true,
        isDefault: true,
        isFeatured: true,
        colors: {
          primary: '#10B981',
          secondary: '#3B82F6',
          accent: '#F59E0B',
          background: '#FFFFFF',
          text: '#1F2937',
          muted: '#6B7280',
          border: '#E5E7EB'
        },
        fonts: {
          heading: 'Inter, sans-serif',
          body: 'Inter, sans-serif',
          mono: 'JetBrains Mono, monospace'
        },
        features: [
          { feature: 'Course Marketplace' },
          { feature: 'Search Filters' },
          { feature: 'Shopping Cart' },
          { feature: 'Wishlist' },
          { feature: 'Course Comparison' },
          { feature: 'Student Dashboard' }
        ],
        suitableFor: [
          { type: 'coaching_centers' },
          { type: 'online_academies' },
          { type: 'universities' }
        ],
        rating: {
          average: 4.9,
          count: 234
        }
      },
      {
        name: 'Academic Pro',
        slug: 'academic-pro',
        type: 'institute',
        version: '1.0.0',
        description: 'Professional academic theme for universities and colleges',
        category: 'university_classic',
        isActive: false,
        isDefault: false,
        isFeatured: true,
        colors: {
          primary: '#7C3AED',
          secondary: '#059669',
          accent: '#DC2626',
          background: '#FFFFFF',
          text: '#1F2937',
          muted: '#6B7280',
          border: '#E5E7EB'
        },
        fonts: {
          heading: 'Poppins, sans-serif',
          body: 'Lato, sans-serif',
          mono: 'Source Code Pro, monospace'
        },
        features: [
          { feature: 'Course Marketplace' },
          { feature: 'Live Classes' },
          { feature: 'Exam System' },
          { feature: 'Certificate Generation' },
          { feature: 'Discussion Forums' }
        ],
        suitableFor: [
          { type: 'universities' },
          { type: 'professional_courses' },
          { type: 'exam_preparation' }
        ],
        rating: {
          average: 4.7,
          count: 167
        }
      },
      {
        name: 'Learning Hub',
        slug: 'learning-hub',
        type: 'institute',
        version: '1.0.0',
        description: 'Community-focused learning theme with social features',
        category: 'online_academy',
        isActive: false,
        isDefault: false,
        isFeatured: false,
        colors: {
          primary: '#EC4899',
          secondary: '#8B5CF6',
          accent: '#F59E0B',
          background: '#FFFFFF',
          text: '#1F2937',
          muted: '#6B7280',
          border: '#E5E7EB'
        },
        fonts: {
          heading: 'Montserrat, sans-serif',
          body: 'Nunito, sans-serif',
          mono: 'JetBrains Mono, monospace'
        },
        features: [
          { feature: 'Course Marketplace' },
          { feature: 'Social Learning' },
          { feature: 'Peer Reviews' },
          { feature: 'Study Groups' },
          { feature: 'Gamification' }
        ],
        suitableFor: [
          { type: 'online_academies' },
          { type: 'skill_development' },
          { type: 'corporate_training' }
        ],
        rating: {
          average: 4.5,
          count: 98
        }
      }
    ]

    // Create platform themes
    for (const themeData of platformThemes) {
      await payload.create({
        collection: 'themes',
        data: themeData
      })
      console.log(`Created platform theme: ${themeData.name}`)
    }

    // Create institute themes
    for (const themeData of instituteThemes) {
      await payload.create({
        collection: 'themes',
        data: themeData
      })
      console.log(`Created institute theme: ${themeData.name}`)
    }

    console.log('Themes seeded successfully!')

  } catch (error) {
    console.error('Error seeding themes:', error)
    throw error
  }
}
