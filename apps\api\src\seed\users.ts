import type { Payload } from 'payload'

export const seedUsers = async (payload: Payload): Promise<void> => {
  try {
    console.log('Seeding users...')

    // Check if super admin already exists
    const existingAdmin = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: '<EMAIL>'
        }
      }
    })

    if (existingAdmin.docs.length === 0) {
      // Create super admin user
      const superAdmin = await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'admin123',
          role: 'super_admin',
          firstName: 'Super',
          lastName: 'Admin',
          isActive: true,
          isEmailVerified: true
        }
      })
      console.log('✓ Super Admin created:', superAdmin.email)
    } else {
      console.log('✓ Super Admin already exists')
    }

    // Check if institute admin already exists
    const existingInstituteAdmin = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: '<EMAIL>'
        }
      }
    })

    if (existingInstituteAdmin.docs.length === 0) {
      // Create institute admin user
      const instituteAdmin = await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'institute123',
          role: 'institute_admin',
          firstName: 'Institute',
          lastName: 'Admin',
          isActive: true,
          isEmailVerified: true
        }
      })
      console.log('✓ Institute Admin created:', instituteAdmin.email)
    } else {
      console.log('✓ Institute Admin already exists')
    }

    // Check if student already exists
    const existingStudent = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: '<EMAIL>'
        }
      }
    })

    if (existingStudent.docs.length === 0) {
      // Create student user
      const student = await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'student123',
          role: 'student',
          firstName: 'Test',
          lastName: 'Student',
          isActive: true,
          isEmailVerified: true
        }
      })
      console.log('✓ Student created:', student.email)
    } else {
      console.log('✓ Student already exists')
    }

    console.log('Users seeding completed!')

  } catch (error) {
    console.error('Error seeding users:', error)
    throw error
  }
}
