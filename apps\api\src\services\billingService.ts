import { Payload } from 'payload'
import { TaxCalculationService } from './taxCalculationService'

export interface BillingPeriod {
  month: number
  year: number
  startDate: Date
  endDate: Date
}

export interface CommissionSummary {
  totalPurchases: number
  totalRevenue: number
  totalCommission: number
  purchaseCount: number
  averageOrderValue: number
}

export interface BillGenerationInput {
  branchId: string
  billingPeriod: BillingPeriod
  baseFee?: number
}

export interface BillGenerationResult {
  billId: string
  billNumber: string
  totalAmount: number
  commissionSummary: CommissionSummary
  taxDetails: any
}

export class BillingService {
  private payload: Payload
  private taxService: TaxCalculationService

  constructor(payload: Payload) {
    this.payload = payload
    this.taxService = new TaxCalculationService(payload)
  }

  async generateMonthlyBill(input: BillGenerationInput): Promise<BillGenerationResult> {
    try {
      const { branchId, billingPeriod, baseFee = 0 } = input

      // Get branch details
      const branch = await this.payload.findByID({
        collection: 'branches',
        id: branchId,
        populate: ['institute', 'location.country', 'location.state']
      })

      if (!branch) {
        throw new Error('Branch not found')
      }

      // Calculate commission for the billing period
      const commissionSummary = await this.calculateCommissionForPeriod(branchId, billingPeriod)

      // Calculate tax
      const taxCalculation = await this.calculateBillTax(
        baseFee + commissionSummary.totalCommission,
        branch
      )

      // Create bill
      const bill = await this.payload.create({
        collection: 'bills',
        data: {
          branch: branchId,
          billingPeriod: {
            startDate: billingPeriod.startDate,
            endDate: billingPeriod.endDate,
            month: billingPeriod.month,
            year: billingPeriod.year
          },
          amounts: {
            baseFee,
            commissionAmount: commissionSummary.totalCommission,
            subtotal: baseFee + commissionSummary.totalCommission,
            taxAmount: taxCalculation.totalTax,
            totalAmount: baseFee + commissionSummary.totalCommission + taxCalculation.totalTax,
            currency: branch.billingSettings?.currency || 'INR'
          },
          taxDetails: {
            taxScenario: taxCalculation.scenario,
            taxComponents: taxCalculation.taxComponents.map(component => ({
              componentName: component.name,
              componentCode: component.code,
              rate: component.rate,
              amount: component.amount
            }))
          },
          commissionDetails: await this.getCommissionDetails(branchId, billingPeriod),
          status: 'pending'
        }
      })

      // Mark purchases as added to bill
      await this.markPurchasesAsBilled(branchId, billingPeriod, bill.id)

      return {
        billId: bill.id,
        billNumber: bill.billNumber,
        totalAmount: bill.amounts.totalAmount,
        commissionSummary,
        taxDetails: taxCalculation
      }

    } catch (error) {
      console.error('Bill generation error:', error)
      throw new Error('Failed to generate bill')
    }
  }

  async calculateCommissionForPeriod(branchId: string, period: BillingPeriod): Promise<CommissionSummary> {
    const purchases = await this.payload.find({
      collection: 'course-purchases',
      where: {
        and: [
          { branch: { equals: branchId } },
          { 'paymentDetails.paymentStatus': { equals: 'completed' } },
          { 'billingInfo.addedToBill': { equals: false } },
          { purchaseDate: { greater_than_equal: period.startDate } },
          { purchaseDate: { less_than_equal: period.endDate } }
        ]
      },
      limit: 1000,
      populate: ['course', 'student']
    })

    let totalRevenue = 0
    let totalCommission = 0
    let purchaseCount = 0

    for (const purchase of purchases.docs) {
      totalRevenue += purchase.purchaseDetails.finalAmount
      totalCommission += purchase.commissionDetails.commissionAmount
      purchaseCount++
    }

    return {
      totalPurchases: purchases.totalDocs,
      totalRevenue,
      totalCommission,
      purchaseCount,
      averageOrderValue: purchaseCount > 0 ? totalRevenue / purchaseCount : 0
    }
  }

  async calculateBillTax(amount: number, branch: any): Promise<any> {
    // Get super admin location (platform location)
    const platformLocation = {
      countryId: 'platform_country_id', // This should be configured
      stateId: 'platform_state_id'
    }

    const branchLocation = {
      countryId: branch.location.country.id,
      stateId: branch.location.state.id
    }

    return await this.taxService.calculateTax({
      amount,
      transactionType: 'subscription',
      customerType: 'business',
      customerLocation: branchLocation,
      instituteLocation: platformLocation
    })
  }

  async getCommissionDetails(branchId: string, period: BillingPeriod): Promise<any[]> {
    const purchases = await this.payload.find({
      collection: 'course-purchases',
      where: {
        and: [
          { branch: { equals: branchId } },
          { 'paymentDetails.paymentStatus': { equals: 'completed' } },
          { 'billingInfo.addedToBill': { equals: false } },
          { purchaseDate: { greater_than_equal: period.startDate } },
          { purchaseDate: { less_than_equal: period.endDate } }
        ]
      },
      limit: 1000,
      populate: ['course', 'student']
    })

    return purchases.docs.map(purchase => ({
      studentPurchase: purchase.id,
      courseTitle: purchase.course.title,
      studentName: `${purchase.student.firstName} ${purchase.student.lastName}`,
      purchaseAmount: purchase.purchaseDetails.finalAmount,
      commissionRate: purchase.commissionDetails.commissionRate,
      commissionAmount: purchase.commissionDetails.commissionAmount,
      purchaseDate: purchase.purchaseDate
    }))
  }

  async markPurchasesAsBilled(branchId: string, period: BillingPeriod, billId: string): Promise<void> {
    const purchases = await this.payload.find({
      collection: 'course-purchases',
      where: {
        and: [
          { branch: { equals: branchId } },
          { 'paymentDetails.paymentStatus': { equals: 'completed' } },
          { 'billingInfo.addedToBill': { equals: false } },
          { purchaseDate: { greater_than_equal: period.startDate } },
          { purchaseDate: { less_than_equal: period.endDate } }
        ]
      },
      limit: 1000
    })

    for (const purchase of purchases.docs) {
      await this.payload.update({
        collection: 'course-purchases',
        id: purchase.id,
        data: {
          billingInfo: {
            ...purchase.billingInfo,
            addedToBill: true,
            billId
          }
        }
      })
    }
  }

  async getBillingDashboardData(branchId?: string, instituteId?: string): Promise<any> {
    const currentDate = new Date()
    const currentMonth = currentDate.getMonth() + 1
    const currentYear = currentDate.getFullYear()

    // Build where clause based on access level
    let whereClause: any = {}
    
    if (branchId) {
      whereClause.branch = { equals: branchId }
    } else if (instituteId) {
      whereClause['branch.institute'] = { equals: instituteId }
    }

    // Get current month bills
    const currentMonthBills = await this.payload.find({
      collection: 'bills',
      where: {
        and: [
          whereClause,
          { 'billingPeriod.month': { equals: currentMonth } },
          { 'billingPeriod.year': { equals: currentYear } }
        ]
      },
      populate: ['branch']
    })

    // Get pending bills
    const pendingBills = await this.payload.find({
      collection: 'bills',
      where: {
        and: [
          whereClause,
          { status: { in: ['pending', 'sent', 'viewed'] } }
        ]
      },
      populate: ['branch']
    })

    // Get overdue bills
    const overdueBills = await this.payload.find({
      collection: 'bills',
      where: {
        and: [
          whereClause,
          { status: { equals: 'overdue' } }
        ]
      },
      populate: ['branch']
    })

    // Calculate totals
    const totalCurrentMonth = currentMonthBills.docs.reduce((sum, bill) => sum + bill.amounts.totalAmount, 0)
    const totalPending = pendingBills.docs.reduce((sum, bill) => sum + bill.amounts.totalAmount, 0)
    const totalOverdue = overdueBills.docs.reduce((sum, bill) => sum + bill.amounts.totalAmount, 0)

    // Get recent purchases for commission tracking
    const recentPurchases = await this.payload.find({
      collection: 'course-purchases',
      where: {
        and: [
          branchId ? { branch: { equals: branchId } } : instituteId ? { 'course.institute': { equals: instituteId } } : {},
          { 'paymentDetails.paymentStatus': { equals: 'completed' } },
          { purchaseDate: { greater_than_equal: new Date(currentYear, currentMonth - 1, 1) } }
        ]
      },
      limit: 100,
      sort: '-purchaseDate',
      populate: ['course', 'student', 'branch']
    })

    return {
      summary: {
        currentMonthTotal: totalCurrentMonth,
        pendingTotal: totalPending,
        overdueTotal: totalOverdue,
        billsCount: {
          current: currentMonthBills.totalDocs,
          pending: pendingBills.totalDocs,
          overdue: overdueBills.totalDocs
        }
      },
      currentMonthBills: currentMonthBills.docs,
      pendingBills: pendingBills.docs,
      overdueBills: overdueBills.docs,
      recentPurchases: recentPurchases.docs,
      commissionSummary: {
        totalCommission: recentPurchases.docs.reduce((sum, purchase) => sum + purchase.commissionDetails.commissionAmount, 0),
        totalRevenue: recentPurchases.docs.reduce((sum, purchase) => sum + purchase.purchaseDetails.finalAmount, 0),
        purchaseCount: recentPurchases.totalDocs
      }
    }
  }

  async generateBulkBills(month: number, year: number, instituteId?: string): Promise<BillGenerationResult[]> {
    // Get all active branches
    let whereClause: any = { isActive: { equals: true } }
    if (instituteId) {
      whereClause.institute = { equals: instituteId }
    }

    const branches = await this.payload.find({
      collection: 'branches',
      where: whereClause,
      limit: 1000
    })

    const results: BillGenerationResult[] = []
    const billingPeriod: BillingPeriod = {
      month,
      year,
      startDate: new Date(year, month - 1, 1),
      endDate: new Date(year, month, 0)
    }

    for (const branch of branches.docs) {
      try {
        // Check if bill already exists for this period
        const existingBill = await this.payload.find({
          collection: 'bills',
          where: {
            and: [
              { branch: { equals: branch.id } },
              { 'billingPeriod.month': { equals: month } },
              { 'billingPeriod.year': { equals: year } }
            ]
          }
        })

        if (existingBill.totalDocs === 0) {
          const result = await this.generateMonthlyBill({
            branchId: branch.id,
            billingPeriod,
            baseFee: branch.billingSettings?.baseFee || 0
          })
          results.push(result)
        }
      } catch (error) {
        console.error(`Failed to generate bill for branch ${branch.id}:`, error)
      }
    }

    return results
  }
}
