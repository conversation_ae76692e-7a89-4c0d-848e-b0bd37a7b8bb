import { v4 as uuidv4 } from 'uuid'
import { promises as fs } from 'fs'
import path from 'path'
import sharp from 'sharp'
import { fileUploadService } from './file-upload'
import type { AuthenticatedUser } from '../middleware/auth'

/**
 * Document Processing & Security Service for Course Builder System
 * Handles document processing, virus scanning, file validation, and preview generation
 */

export interface DocumentProcessingOptions {
  generatePreview?: boolean
  generateThumbnail?: boolean
  extractText?: boolean
  validateSecurity?: boolean
  extractMetadata?: boolean
  maxPreviewPages?: number
}

export interface DocumentMetadata {
  title?: string
  author?: string
  subject?: string
  creator?: string
  producer?: string
  creationDate?: string
  modificationDate?: string
  pageCount?: number
  fileSize: number
  mimeType: string
  language?: string
}

export interface ProcessedDocument {
  id: string
  originalFile: string
  metadata: DocumentMetadata
  thumbnail?: string
  preview?: string[]
  extractedText?: string
  securityScan: {
    isClean: boolean
    threats: string[]
    scanDate: Date
  }
  processingTime: number
  status: 'completed' | 'failed' | 'processing'
  error?: string
}

export interface DocumentProcessingJob {
  id: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  progress: number
  startTime: Date
  endTime?: Date
  error?: string
  result?: ProcessedDocument
}

class DocumentProcessingService {
  private processingJobs = new Map<string, DocumentProcessingJob>()
  private readonly tempDir = path.join(process.cwd(), 'temp', 'document-processing')

  constructor() {
    this.ensureTempDirectory()
  }

  /**
   * Process document file with comprehensive pipeline
   */
  async processDocument(
    user: AuthenticatedUser,
    documentFile: Buffer,
    filename: string,
    options: DocumentProcessingOptions = {}
  ): Promise<{ success: boolean; jobId: string; error?: string }> {
    const jobId = uuidv4()
    const job: DocumentProcessingJob = {
      id: jobId,
      status: 'queued',
      progress: 0,
      startTime: new Date()
    }

    this.processingJobs.set(jobId, job)

    try {
      // Start processing in background
      this.processDocumentAsync(user, documentFile, filename, options, jobId)
      
      return {
        success: true,
        jobId
      }
    } catch (error) {
      console.error('Error starting document processing:', error)
      job.status = 'failed'
      job.error = 'Failed to start document processing'
      
      return {
        success: false,
        jobId,
        error: 'Failed to start document processing'
      }
    }
  }

  /**
   * Async document processing pipeline
   */
  private async processDocumentAsync(
    user: AuthenticatedUser,
    documentFile: Buffer,
    filename: string,
    options: DocumentProcessingOptions,
    jobId: string
  ): Promise<void> {
    const job = this.processingJobs.get(jobId)!
    
    try {
      job.status = 'processing'
      job.progress = 10

      // Save temp file
      const tempFilePath = path.join(this.tempDir, `${jobId}_${filename}`)
      await fs.writeFile(tempFilePath, documentFile)
      
      job.progress = 20

      // Security scan
      const securityScan = await this.performSecurityScan(documentFile, filename)
      if (!securityScan.isClean) {
        throw new Error(`Security threat detected: ${securityScan.threats.join(', ')}`)
      }
      job.progress = 40

      // Extract metadata
      const metadata = await this.extractDocumentMetadata(tempFilePath, filename)
      job.progress = 50

      // Generate thumbnail
      let thumbnail: string | undefined
      if (options.generateThumbnail !== false) {
        thumbnail = await this.generateDocumentThumbnail(user, tempFilePath, filename)
      }
      job.progress = 70

      // Generate preview pages
      let preview: string[] | undefined
      if (options.generatePreview) {
        preview = await this.generateDocumentPreview(
          user, tempFilePath, filename, options.maxPreviewPages || 5
        )
      }
      job.progress = 80

      // Extract text content
      let extractedText: string | undefined
      if (options.extractText) {
        extractedText = await this.extractTextContent(tempFilePath, filename)
      }
      job.progress = 90

      const result: ProcessedDocument = {
        id: jobId,
        originalFile: filename,
        metadata,
        thumbnail,
        preview,
        extractedText,
        securityScan,
        processingTime: Date.now() - job.startTime.getTime(),
        status: 'completed'
      }

      job.status = 'completed'
      job.progress = 100
      job.endTime = new Date()
      job.result = result

      // Cleanup temp files
      await this.cleanupTempFiles([tempFilePath])

    } catch (error) {
      console.error('Document processing error:', error)
      job.status = 'failed'
      job.error = error instanceof Error ? error.message : 'Unknown error'
      job.progress = 0
      job.endTime = new Date()
    }
  }

  /**
   * Perform security scan on document
   */
  private async performSecurityScan(
    documentFile: Buffer,
    filename: string
  ): Promise<ProcessedDocument['securityScan']> {
    const threats: string[] = []
    
    try {
      // File type validation using magic numbers
      const fileSignature = documentFile.slice(0, 8).toString('hex')
      const isValidFileType = this.validateFileType(fileSignature, filename)
      
      if (!isValidFileType) {
        threats.push('Invalid file type or corrupted file')
      }

      // Check for suspicious patterns
      const fileContent = documentFile.toString('binary')
      
      // Check for embedded executables
      if (fileContent.includes('MZ') || fileContent.includes('PE')) {
        threats.push('Embedded executable detected')
      }

      // Check for suspicious scripts
      const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /vbscript:/i,
        /onload=/i,
        /onerror=/i
      ]

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(fileContent)) {
          threats.push('Suspicious script content detected')
          break
        }
      }

      // Check file size limits
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (documentFile.length > maxSize) {
        threats.push('File size exceeds maximum allowed limit')
      }

      return {
        isClean: threats.length === 0,
        threats,
        scanDate: new Date()
      }
    } catch (error) {
      console.error('Security scan error:', error)
      return {
        isClean: false,
        threats: ['Security scan failed'],
        scanDate: new Date()
      }
    }
  }

  /**
   * Validate file type using magic numbers
   */
  private validateFileType(signature: string, filename: string): boolean {
    const extension = path.extname(filename).toLowerCase()
    
    const validSignatures: Record<string, string[]> = {
      '.pdf': ['25504446'],
      '.doc': ['d0cf11e0', 'ec000000'],
      '.docx': ['504b0304', '504b0506'],
      '.xls': ['d0cf11e0'],
      '.xlsx': ['504b0304', '504b0506'],
      '.ppt': ['d0cf11e0'],
      '.pptx': ['504b0304', '504b0506'],
      '.txt': [], // Text files don't have consistent magic numbers
      '.rtf': ['7b5c7274']
    }

    const expectedSignatures = validSignatures[extension]
    if (!expectedSignatures) {
      return false // Unsupported file type
    }

    if (expectedSignatures.length === 0) {
      return true // Text files or types without magic numbers
    }

    return expectedSignatures.some(sig => 
      signature.toLowerCase().startsWith(sig.toLowerCase())
    )
  }

  /**
   * Extract document metadata
   */
  private async extractDocumentMetadata(
    filePath: string,
    filename: string
  ): Promise<DocumentMetadata> {
    try {
      const stats = await fs.stat(filePath)
      const extension = path.extname(filename).toLowerCase()
      
      const metadata: DocumentMetadata = {
        fileSize: stats.size,
        mimeType: this.getMimeType(extension)
      }

      // For PDF files, we could use pdf-parse or similar library
      // For Office documents, we could use officegen or similar
      // For now, return basic metadata
      
      return metadata
    } catch (error) {
      console.error('Error extracting metadata:', error)
      return {
        fileSize: 0,
        mimeType: 'application/octet-stream'
      }
    }
  }

  /**
   * Generate document thumbnail
   */
  private async generateDocumentThumbnail(
    user: AuthenticatedUser,
    filePath: string,
    filename: string
  ): Promise<string | undefined> {
    try {
      const extension = path.extname(filename).toLowerCase()
      
      if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].includes(extension)) {
        // For image files, generate thumbnail directly
        const thumbnailBuffer = await sharp(filePath)
          .resize(300, 300, {
            fit: 'cover',
            position: 'center'
          })
          .jpeg({ quality: 80 })
          .toBuffer()

        const uploadResult = await fileUploadService.uploadFile(
          user,
          thumbnailBuffer,
          `thumbnail_${Date.now()}.jpg`,
          'image/jpeg'
        )

        return uploadResult.success ? uploadResult.file?.url : undefined
      }

      // For other document types, we would need specialized libraries
      // For now, return a placeholder or generate a generic thumbnail
      return undefined
    } catch (error) {
      console.error('Error generating thumbnail:', error)
      return undefined
    }
  }

  /**
   * Generate document preview pages
   */
  private async generateDocumentPreview(
    user: AuthenticatedUser,
    filePath: string,
    filename: string,
    maxPages: number
  ): Promise<string[]> {
    try {
      // This would require specialized libraries like pdf2pic for PDFs
      // or converting Office documents to images
      // For now, return empty array
      return []
    } catch (error) {
      console.error('Error generating preview:', error)
      return []
    }
  }

  /**
   * Extract text content from document
   */
  private async extractTextContent(
    filePath: string,
    filename: string
  ): Promise<string | undefined> {
    try {
      const extension = path.extname(filename).toLowerCase()
      
      if (extension === '.txt') {
        return await fs.readFile(filePath, 'utf8')
      }

      // For other document types, we would need specialized libraries
      // like pdf-parse for PDFs, mammoth for Word documents, etc.
      return undefined
    } catch (error) {
      console.error('Error extracting text:', error)
      return undefined
    }
  }

  /**
   * Get MIME type from file extension
   */
  private getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.txt': 'text/plain',
      '.rtf': 'application/rtf'
    }

    return mimeTypes[extension] || 'application/octet-stream'
  }

  /**
   * Get processing job status
   */
  getJobStatus(jobId: string): DocumentProcessingJob | null {
    return this.processingJobs.get(jobId) || null
  }

  /**
   * Get processing job result
   */
  getJobResult(jobId: string): ProcessedDocument | null {
    const job = this.processingJobs.get(jobId)
    return job?.result || null
  }

  /**
   * Cleanup temporary files
   */
  private async cleanupTempFiles(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        await fs.unlink(filePath)
      } catch (error) {
        console.error('Error cleaning up temp file:', filePath, error)
      }
    }
  }

  /**
   * Ensure temp directory exists
   */
  private async ensureTempDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.tempDir, { recursive: true })
    } catch (error) {
      console.error('Error creating temp directory:', error)
    }
  }
}

export const documentProcessingService = new DocumentProcessingService()
