import { 
  S3<PERSON><PERSON>, 
  PutObjectCommand, 
  GetO<PERSON>Command, 
  DeleteObjectCommand,
  HeadObjectCommand,
  CopyObjectCommand
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { v4 as uuidv4 } from 'uuid'
import { createHash } from 'crypto'
import { getStorageConfig, createS3Client, validateFileType, getFileCategory } from '../config/storage'
import { getTenantStoragePath } from '../middleware/tenant-context'
import type { AuthenticatedUser } from '../middleware/auth'

/**
 * File Upload Service for Course Builder System
 * Handles secure file uploads with tenant isolation and media processing
 */

export interface UploadedFile {
  id: string
  originalName: string
  filename: string
  mimeType: string
  size: number
  category: string
  url: string
  cdnUrl?: string
  path: string
  hash: string
  metadata: {
    width?: number
    height?: number
    duration?: number
    bitrate?: number
    codec?: string
    thumbnailUrl?: string
  }
  uploadedBy: string
  uploadedAt: Date
  instituteId: string
  branchId?: string
}

export interface UploadOptions {
  generateThumbnail?: boolean
  compressImage?: boolean
  maxWidth?: number
  maxHeight?: number
  quality?: number
  preserveOriginal?: boolean
}

export interface UploadResult {
  success: boolean
  file?: UploadedFile
  error?: string
  uploadUrl?: string
}

class FileUploadService {
  private config = getStorageConfig()
  private s3Client: S3Client | null = null

  constructor() {
    if (this.config.provider !== 'local') {
      this.s3Client = createS3Client(this.config)
    }
  }

  /**
   * Generate secure upload URL for direct client uploads
   */
  async generateUploadUrl(
    user: AuthenticatedUser,
    filename: string,
    mimeType: string,
    size: number,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    try {
      // Validate file type and size
      if (!validateFileType(filename, mimeType, this.config)) {
        return {
          success: false,
          error: 'File type not allowed'
        }
      }

      if (size > this.config.maxFileSize) {
        return {
          success: false,
          error: `File size exceeds maximum allowed size of ${this.config.maxFileSize / 1024 / 1024}MB`
        }
      }

      // Generate unique filename
      const fileId = uuidv4()
      const ext = filename.split('.').pop()
      const uniqueFilename = `${fileId}.${ext}`

      // Create tenant-aware path
      const tenantPath = getTenantStoragePath(
        { 
          instituteId: user.institute, 
          branchId: user.branch,
          userId: user.id,
          userRole: user.legacyRole || user.role,
          isSuperAdmin: user.legacyRole === 'super_admin',
          isInstituteAdmin: user.legacyRole === 'institute_admin'
        },
        uniqueFilename
      )

      if (this.config.provider === 'local') {
        return {
          success: true,
          uploadUrl: `/api/upload/local/${fileId}`,
          file: {
            id: fileId,
            originalName: filename,
            filename: uniqueFilename,
            mimeType,
            size,
            category: getFileCategory(mimeType),
            url: `${this.config.publicUrl}/${tenantPath}`,
            path: tenantPath,
            hash: '',
            metadata: {},
            uploadedBy: user.id,
            uploadedAt: new Date(),
            instituteId: user.institute,
            branchId: user.branch
          }
        }
      }

      // Generate signed URL for cloud storage
      if (!this.s3Client) {
        return {
          success: false,
          error: 'Storage client not initialized'
        }
      }

      const command = new PutObjectCommand({
        Bucket: this.config.bucket,
        Key: tenantPath,
        ContentType: mimeType,
        ContentLength: size,
        Metadata: {
          originalName: filename,
          uploadedBy: user.id,
          instituteId: user.institute,
          branchId: user.branch || '',
          fileId: fileId
        }
      })

      const uploadUrl = await getSignedUrl(this.s3Client, command, { expiresIn: 3600 })

      return {
        success: true,
        uploadUrl,
        file: {
          id: fileId,
          originalName: filename,
          filename: uniqueFilename,
          mimeType,
          size,
          category: getFileCategory(mimeType),
          url: this.config.cdnUrl 
            ? `${this.config.cdnUrl}/${tenantPath}`
            : `${this.config.publicUrl}/${tenantPath}`,
          cdnUrl: this.config.cdnUrl ? `${this.config.cdnUrl}/${tenantPath}` : undefined,
          path: tenantPath,
          hash: '',
          metadata: {},
          uploadedBy: user.id,
          uploadedAt: new Date(),
          instituteId: user.institute,
          branchId: user.branch
        }
      }
    } catch (error) {
      console.error('Error generating upload URL:', error)
      return {
        success: false,
        error: 'Failed to generate upload URL'
      }
    }
  }

  /**
   * Handle direct file upload (for local storage or server-side uploads)
   */
  async uploadFile(
    user: AuthenticatedUser,
    file: Buffer,
    filename: string,
    mimeType: string,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    try {
      // Validate file
      if (!validateFileType(filename, mimeType, this.config)) {
        return {
          success: false,
          error: 'File type not allowed'
        }
      }

      if (file.length > this.config.maxFileSize) {
        return {
          success: false,
          error: `File size exceeds maximum allowed size of ${this.config.maxFileSize / 1024 / 1024}MB`
        }
      }

      // Generate file hash for deduplication
      const hash = createHash('sha256').update(file).digest('hex')

      // Generate unique filename
      const fileId = uuidv4()
      const ext = filename.split('.').pop()
      const uniqueFilename = `${fileId}.${ext}`

      // Create tenant-aware path
      const tenantPath = getTenantStoragePath(
        { 
          instituteId: user.institute, 
          branchId: user.branch,
          userId: user.id,
          userRole: user.legacyRole || user.role,
          isSuperAdmin: user.legacyRole === 'super_admin',
          isInstituteAdmin: user.legacyRole === 'institute_admin'
        },
        uniqueFilename
      )

      if (this.config.provider === 'local') {
        // Handle local file storage
        const fs = await import('fs/promises')
        const path = await import('path')
        
        const uploadDir = path.join(process.cwd(), 'uploads', path.dirname(tenantPath))
        const filePath = path.join(process.cwd(), 'uploads', tenantPath)

        // Ensure directory exists
        await fs.mkdir(uploadDir, { recursive: true })

        // Write file
        await fs.writeFile(filePath, file)

        return {
          success: true,
          file: {
            id: fileId,
            originalName: filename,
            filename: uniqueFilename,
            mimeType,
            size: file.length,
            category: getFileCategory(mimeType),
            url: `${this.config.publicUrl}/${tenantPath}`,
            path: tenantPath,
            hash,
            metadata: {},
            uploadedBy: user.id,
            uploadedAt: new Date(),
            instituteId: user.institute,
            branchId: user.branch
          }
        }
      }

      // Handle cloud storage upload
      if (!this.s3Client) {
        return {
          success: false,
          error: 'Storage client not initialized'
        }
      }

      const command = new PutObjectCommand({
        Bucket: this.config.bucket,
        Key: tenantPath,
        Body: file,
        ContentType: mimeType,
        Metadata: {
          originalName: filename,
          uploadedBy: user.id,
          instituteId: user.institute,
          branchId: user.branch || '',
          fileId: fileId,
          hash: hash
        }
      })

      await this.s3Client.send(command)

      return {
        success: true,
        file: {
          id: fileId,
          originalName: filename,
          filename: uniqueFilename,
          mimeType,
          size: file.length,
          category: getFileCategory(mimeType),
          url: this.config.cdnUrl 
            ? `${this.config.cdnUrl}/${tenantPath}`
            : `${this.config.publicUrl}/${tenantPath}`,
          cdnUrl: this.config.cdnUrl ? `${this.config.cdnUrl}/${tenantPath}` : undefined,
          path: tenantPath,
          hash,
          metadata: {},
          uploadedBy: user.id,
          uploadedAt: new Date(),
          instituteId: user.institute,
          branchId: user.branch
        }
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      return {
        success: false,
        error: 'Failed to upload file'
      }
    }
  }

  /**
   * Generate signed URL for file download
   */
  async generateDownloadUrl(
    user: AuthenticatedUser,
    filePath: string,
    expiresIn: number = 3600
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      // Validate tenant access to file
      const { validateFileAccess } = await import('../middleware/tenant-context')
      const context = {
        instituteId: user.institute,
        branchId: user.branch,
        userId: user.id,
        userRole: user.legacyRole || user.role,
        isSuperAdmin: user.legacyRole === 'super_admin',
        isInstituteAdmin: user.legacyRole === 'institute_admin'
      }

      if (!validateFileAccess(filePath, context)) {
        return {
          success: false,
          error: 'Access denied to file'
        }
      }

      if (this.config.provider === 'local') {
        return {
          success: true,
          url: `${this.config.publicUrl}/${filePath}`
        }
      }

      if (!this.s3Client) {
        return {
          success: false,
          error: 'Storage client not initialized'
        }
      }

      const command = new GetObjectCommand({
        Bucket: this.config.bucket,
        Key: filePath
      })

      const url = await getSignedUrl(this.s3Client, command, { expiresIn })

      return {
        success: true,
        url
      }
    } catch (error) {
      console.error('Error generating download URL:', error)
      return {
        success: false,
        error: 'Failed to generate download URL'
      }
    }
  }

  /**
   * Delete file
   */
  async deleteFile(
    user: AuthenticatedUser,
    filePath: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Validate tenant access to file
      const { validateFileAccess } = await import('../middleware/tenant-context')
      const context = {
        instituteId: user.institute,
        branchId: user.branch,
        userId: user.id,
        userRole: user.legacyRole || user.role,
        isSuperAdmin: user.legacyRole === 'super_admin',
        isInstituteAdmin: user.legacyRole === 'institute_admin'
      }

      if (!validateFileAccess(filePath, context)) {
        return {
          success: false,
          error: 'Access denied to file'
        }
      }

      if (this.config.provider === 'local') {
        const fs = await import('fs/promises')
        const path = await import('path')
        const fullPath = path.join(process.cwd(), 'uploads', filePath)

        try {
          await fs.unlink(fullPath)
        } catch (error) {
          // File might not exist, which is okay
        }

        return { success: true }
      }

      if (!this.s3Client) {
        return {
          success: false,
          error: 'Storage client not initialized'
        }
      }

      const command = new DeleteObjectCommand({
        Bucket: this.config.bucket,
        Key: filePath
      })

      await this.s3Client.send(command)

      return { success: true }
    } catch (error) {
      console.error('Error deleting file:', error)
      return {
        success: false,
        error: 'Failed to delete file'
      }
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(
    user: AuthenticatedUser,
    filePath: string
  ): Promise<{ success: boolean; metadata?: any; error?: string }> {
    try {
      // Validate tenant access to file
      const { validateFileAccess } = await import('../middleware/tenant-context')
      const context = {
        instituteId: user.institute,
        branchId: user.branch,
        userId: user.id,
        userRole: user.legacyRole || user.role,
        isSuperAdmin: user.legacyRole === 'super_admin',
        isInstituteAdmin: user.legacyRole === 'institute_admin'
      }

      if (!validateFileAccess(filePath, context)) {
        return {
          success: false,
          error: 'Access denied to file'
        }
      }

      if (this.config.provider === 'local') {
        const fs = await import('fs/promises')
        const path = await import('path')
        const fullPath = path.join(process.cwd(), 'uploads', filePath)

        try {
          const stats = await fs.stat(fullPath)
          return {
            success: true,
            metadata: {
              size: stats.size,
              lastModified: stats.mtime,
              created: stats.birthtime
            }
          }
        } catch (error) {
          return {
            success: false,
            error: 'File not found'
          }
        }
      }

      if (!this.s3Client) {
        return {
          success: false,
          error: 'Storage client not initialized'
        }
      }

      const command = new HeadObjectCommand({
        Bucket: this.config.bucket,
        Key: filePath
      })

      const response = await this.s3Client.send(command)

      return {
        success: true,
        metadata: {
          size: response.ContentLength,
          lastModified: response.LastModified,
          contentType: response.ContentType,
          metadata: response.Metadata
        }
      }
    } catch (error) {
      console.error('Error getting file metadata:', error)
      return {
        success: false,
        error: 'Failed to get file metadata'
      }
    }
  }
}

// Export singleton instance
export const fileUploadService = new FileUploadService()

export default fileUploadService
