import sharp from 'sharp'
import { ImageSize } from '../adapters/storage/StorageAdapter'

/**
 * Image Processing Service
 * Handles image optimization, resizing, and format conversion using Sharp
 */

export interface ProcessedImage {
  buffer: Buffer
  width: number
  height: number
  format: string
  size: number
  quality?: number
}

export interface ImageProcessingOptions {
  quality?: number
  format?: 'jpeg' | 'png' | 'webp' | 'avif'
  progressive?: boolean
  optimize?: boolean
  background?: string
}

export class ImageProcessingService {
  /**
   * Process and optimize an image
   */
  static async processImage(
    inputBuffer: Buffer,
    options: ImageProcessingOptions = {}
  ): Promise<ProcessedImage> {
    console.log('🖼️ Processing image with options:', options)

    try {
      let sharpInstance = sharp(inputBuffer)

      // Get original image metadata
      const metadata = await sharpInstance.metadata()
      console.log('📊 Original image metadata:', {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        size: inputBuffer.length
      })

      // Apply background if specified (useful for transparent images)
      if (options.background) {
        sharpInstance = sharpInstance.flatten({ background: options.background })
      }

      // Set format and quality
      const format = options.format || (metadata.format as any) || 'jpeg'
      const quality = options.quality || 85

      switch (format) {
        case 'jpeg':
          sharpInstance = sharpInstance.jpeg({
            quality,
            progressive: options.progressive !== false,
            mozjpeg: true
          })
          break

        case 'png':
          sharpInstance = sharpInstance.png({
            quality,
            progressive: options.progressive !== false,
            compressionLevel: 9,
            adaptiveFiltering: true
          })
          break

        case 'webp':
          sharpInstance = sharpInstance.webp({
            quality,
            effort: 6
          })
          break

        case 'avif':
          sharpInstance = sharpInstance.avif({
            quality,
            effort: 4
          })
          break
      }

      // Process the image
      const processedBuffer = await sharpInstance.toBuffer()
      const processedMetadata = await sharp(processedBuffer).metadata()

      const result: ProcessedImage = {
        buffer: processedBuffer,
        width: processedMetadata.width || 0,
        height: processedMetadata.height || 0,
        format,
        size: processedBuffer.length,
        quality
      }

      console.log('✅ Image processed successfully:', {
        originalSize: inputBuffer.length,
        processedSize: result.size,
        compression: `${((1 - result.size / inputBuffer.length) * 100).toFixed(1)}%`,
        dimensions: `${result.width}x${result.height}`,
        format: result.format
      })

      return result

    } catch (error) {
      console.error('❌ Image processing failed:', error)
      throw new Error(`Image processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Resize image to specific dimensions
   */
  static async resizeImage(
    inputBuffer: Buffer,
    width: number,
    height: number,
    options: ImageProcessingOptions & {
      fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside'
      position?: string
    } = {}
  ): Promise<ProcessedImage> {
    console.log('📏 Resizing image:', { width, height, fit: options.fit })

    try {
      let sharpInstance = sharp(inputBuffer)

      // Apply resize with fit options
      sharpInstance = sharpInstance.resize(width, height, {
        fit: options.fit || 'cover',
        position: options.position || 'center',
        background: options.background || { r: 255, g: 255, b: 255, alpha: 1 }
      })

      // Apply background if specified
      if (options.background) {
        sharpInstance = sharpInstance.flatten({ background: options.background })
      }

      // Set format and quality
      const format = options.format || 'jpeg'
      const quality = options.quality || 85

      switch (format) {
        case 'jpeg':
          sharpInstance = sharpInstance.jpeg({
            quality,
            progressive: options.progressive !== false,
            mozjpeg: true
          })
          break

        case 'png':
          sharpInstance = sharpInstance.png({
            quality,
            progressive: options.progressive !== false,
            compressionLevel: 9
          })
          break

        case 'webp':
          sharpInstance = sharpInstance.webp({
            quality,
            effort: 6
          })
          break
      }

      const resizedBuffer = await sharpInstance.toBuffer()
      const metadata = await sharp(resizedBuffer).metadata()

      const result: ProcessedImage = {
        buffer: resizedBuffer,
        width: metadata.width || width,
        height: metadata.height || height,
        format,
        size: resizedBuffer.length,
        quality
      }

      console.log('✅ Image resized successfully:', {
        dimensions: `${result.width}x${result.height}`,
        size: result.size,
        format: result.format
      })

      return result

    } catch (error) {
      console.error('❌ Image resize failed:', error)
      throw new Error(`Image resize failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate multiple image sizes
   */
  static async generateImageSizes(
    inputBuffer: Buffer,
    sizes: ImageSize[],
    baseOptions: ImageProcessingOptions = {}
  ): Promise<Record<string, ProcessedImage>> {
    console.log('🔄 Generating multiple image sizes:', sizes.map(s => `${s.name}:${s.width}x${s.height}`))

    const results: Record<string, ProcessedImage> = {}

    try {
      // Process each size
      for (const size of sizes) {
        console.log(`📐 Processing size: ${size.name} (${size.width}x${size.height})`)

        const sizeOptions: ImageProcessingOptions & { fit?: any } = {
          ...baseOptions,
          quality: size.quality || baseOptions.quality || 85,
          format: size.format || baseOptions.format || 'jpeg',
          fit: 'cover'
        }

        const processedImage = await this.resizeImage(
          inputBuffer,
          size.width,
          size.height,
          sizeOptions
        )

        results[size.name] = processedImage
      }

      console.log('✅ All image sizes generated successfully:', Object.keys(results))
      return results

    } catch (error) {
      console.error('❌ Generate image sizes failed:', error)
      throw new Error(`Generate image sizes failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Optimize image for web (automatic format selection and compression)
   */
  static async optimizeForWeb(
    inputBuffer: Buffer,
    options: {
      maxWidth?: number
      maxHeight?: number
      quality?: number
      preferWebP?: boolean
    } = {}
  ): Promise<ProcessedImage> {
    console.log('🌐 Optimizing image for web:', options)

    try {
      const metadata = await sharp(inputBuffer).metadata()
      let sharpInstance = sharp(inputBuffer)

      // Resize if dimensions exceed maximum
      const maxWidth = options.maxWidth || 1920
      const maxHeight = options.maxHeight || 1080

      if (metadata.width && metadata.height) {
        if (metadata.width > maxWidth || metadata.height > maxHeight) {
          sharpInstance = sharpInstance.resize(maxWidth, maxHeight, {
            fit: 'inside',
            withoutEnlargement: true
          })
        }
      }

      // Choose optimal format
      const format = options.preferWebP ? 'webp' : 'jpeg'
      const quality = options.quality || 80

      const processedImage = await this.processImage(await sharpInstance.toBuffer(), {
        format: format as any,
        quality,
        progressive: true,
        optimize: true
      })

      console.log('✅ Image optimized for web successfully')
      return processedImage

    } catch (error) {
      console.error('❌ Web optimization failed:', error)
      throw new Error(`Web optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Create favicon from image
   */
  static async createFavicon(inputBuffer: Buffer): Promise<Record<string, ProcessedImage>> {
    console.log('🔖 Creating favicon sizes')

    const faviconSizes: ImageSize[] = [
      { name: '16x16', width: 16, height: 16, quality: 100, format: 'png' },
      { name: '32x32', width: 32, height: 32, quality: 100, format: 'png' },
      { name: '48x48', width: 48, height: 48, quality: 100, format: 'png' },
      { name: '64x64', width: 64, height: 64, quality: 100, format: 'png' },
      { name: '128x128', width: 128, height: 128, quality: 100, format: 'png' },
      { name: '256x256', width: 256, height: 256, quality: 100, format: 'png' }
    ]

    return this.generateImageSizes(inputBuffer, faviconSizes, {
      format: 'png',
      background: 'transparent'
    })
  }

  /**
   * Validate image and get metadata
   */
  static async validateAndGetMetadata(inputBuffer: Buffer): Promise<{
    valid: boolean
    metadata?: sharp.Metadata
    message?: string
  }> {
    try {
      const metadata = await sharp(inputBuffer).metadata()

      // Basic validation
      if (!metadata.width || !metadata.height) {
        return {
          valid: false,
          message: 'Invalid image: missing dimensions'
        }
      }

      if (!metadata.format) {
        return {
          valid: false,
          message: 'Invalid image: unknown format'
        }
      }

      // Check for reasonable dimensions
      if (metadata.width > 10000 || metadata.height > 10000) {
        return {
          valid: false,
          message: 'Image dimensions too large (max 10000x10000)'
        }
      }

      return {
        valid: true,
        metadata
      }

    } catch (error) {
      return {
        valid: false,
        message: `Invalid image: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }
}
