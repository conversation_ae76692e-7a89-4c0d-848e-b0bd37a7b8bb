import sharp from 'sharp'
import ffmpeg from 'fluent-ffmpeg'
import { v4 as uuidv4 } from 'uuid'
import { promises as fs } from 'fs'
import path from 'path'
import { fileUploadService } from './file-upload'
import { getTenantStoragePath } from '../middleware/tenant-context'
import type { AuthenticatedUser } from '../middleware/auth'

/**
 * Media Processing Service for Course Builder System
 * Handles image optimization, video transcoding, thumbnail generation, and audio processing
 */

export interface ProcessingOptions {
  generateThumbnail?: boolean
  optimizeImage?: boolean
  transcodeVideo?: boolean
  extractAudio?: boolean
  generatePreview?: boolean
  quality?: number
  maxWidth?: number
  maxHeight?: number
  videoFormats?: string[]
  audioFormats?: string[]
}

export interface ProcessedMedia {
  original: {
    url: string
    path: string
    size: number
    dimensions?: { width: number; height: number }
    duration?: number
  }
  optimized?: {
    url: string
    path: string
    size: number
    dimensions?: { width: number; height: number }
    quality: number
  }
  thumbnail?: {
    url: string
    path: string
    size: number
    dimensions: { width: number; height: number }
  }
  transcoded?: Array<{
    format: string
    url: string
    path: string
    size: number
    bitrate?: number
    resolution?: string
  }>
  preview?: {
    url: string
    path: string
    duration: number
  }
  metadata: {
    format: string
    codec?: string
    bitrate?: number
    sampleRate?: number
    channels?: number
    duration?: number
    frameRate?: number
  }
}

export interface ProcessingResult {
  success: boolean
  processed?: ProcessedMedia
  error?: string
  jobId?: string
}

class MediaProcessingService {
  private tempDir = path.join(process.cwd(), 'temp')
  private processingJobs = new Map<string, { status: string; progress: number }>()

  constructor() {
    this.ensureTempDir()
  }

  private async ensureTempDir() {
    try {
      await fs.access(this.tempDir)
    } catch {
      await fs.mkdir(this.tempDir, { recursive: true })
    }
  }

  /**
   * Process uploaded media file
   */
  async processMedia(
    user: AuthenticatedUser,
    filePath: string,
    mimeType: string,
    options: ProcessingOptions = {}
  ): Promise<ProcessingResult> {
    const jobId = uuidv4()
    
    try {
      this.processingJobs.set(jobId, { status: 'processing', progress: 0 })

      if (mimeType.startsWith('image/')) {
        return await this.processImage(user, filePath, options, jobId)
      } else if (mimeType.startsWith('video/')) {
        return await this.processVideo(user, filePath, options, jobId)
      } else if (mimeType.startsWith('audio/')) {
        return await this.processAudio(user, filePath, options, jobId)
      } else {
        return {
          success: false,
          error: 'Unsupported media type for processing'
        }
      }
    } catch (error) {
      console.error('Error processing media:', error)
      this.processingJobs.set(jobId, { status: 'error', progress: 0 })
      return {
        success: false,
        error: 'Media processing failed',
        jobId
      }
    }
  }

  /**
   * Process image files
   */
  private async processImage(
    user: AuthenticatedUser,
    filePath: string,
    options: ProcessingOptions,
    jobId: string
  ): Promise<ProcessingResult> {
    try {
      // Download original file to temp directory
      const tempFilePath = path.join(this.tempDir, `${jobId}_original`)
      const downloadResult = await fileUploadService.generateDownloadUrl(user, filePath)
      
      if (!downloadResult.success || !downloadResult.url) {
        throw new Error('Failed to download original file')
      }

      // Download file
      const response = await fetch(downloadResult.url)
      const buffer = await response.arrayBuffer()
      await fs.writeFile(tempFilePath, Buffer.from(buffer))

      this.processingJobs.set(jobId, { status: 'processing', progress: 20 })

      // Get image metadata
      const image = sharp(tempFilePath)
      const metadata = await image.metadata()
      
      const processed: ProcessedMedia = {
        original: {
          url: downloadResult.url,
          path: filePath,
          size: buffer.byteLength,
          dimensions: metadata.width && metadata.height ? {
            width: metadata.width,
            height: metadata.height
          } : undefined
        },
        metadata: {
          format: metadata.format || 'unknown',
          codec: metadata.format
        }
      }

      this.processingJobs.set(jobId, { status: 'processing', progress: 40 })

      // Generate optimized version
      if (options.optimizeImage !== false) {
        const optimizedPath = await this.optimizeImage(
          user, tempFilePath, metadata, options, jobId
        )
        if (optimizedPath) {
          processed.optimized = optimizedPath
        }
      }

      this.processingJobs.set(jobId, { status: 'processing', progress: 70 })

      // Generate thumbnail
      if (options.generateThumbnail !== false) {
        const thumbnailPath = await this.generateImageThumbnail(
          user, tempFilePath, options, jobId
        )
        if (thumbnailPath) {
          processed.thumbnail = thumbnailPath
        }
      }

      this.processingJobs.set(jobId, { status: 'completed', progress: 100 })

      // Cleanup temp files
      await this.cleanupTempFiles([tempFilePath])

      return {
        success: true,
        processed,
        jobId
      }
    } catch (error) {
      console.error('Error processing image:', error)
      return {
        success: false,
        error: 'Image processing failed',
        jobId
      }
    }
  }

  /**
   * Process video files
   */
  private async processVideo(
    user: AuthenticatedUser,
    filePath: string,
    options: ProcessingOptions,
    jobId: string
  ): Promise<ProcessingResult> {
    try {
      // Download original file
      const tempFilePath = path.join(this.tempDir, `${jobId}_original.mp4`)
      const downloadResult = await fileUploadService.generateDownloadUrl(user, filePath)
      
      if (!downloadResult.success || !downloadResult.url) {
        throw new Error('Failed to download original file')
      }

      const response = await fetch(downloadResult.url)
      const buffer = await response.arrayBuffer()
      await fs.writeFile(tempFilePath, Buffer.from(buffer))

      this.processingJobs.set(jobId, { status: 'processing', progress: 20 })

      // Get video metadata
      const metadata = await this.getVideoMetadata(tempFilePath)
      
      const processed: ProcessedMedia = {
        original: {
          url: downloadResult.url,
          path: filePath,
          size: buffer.byteLength,
          duration: metadata.duration
        },
        metadata: {
          format: metadata.format,
          codec: metadata.codec,
          bitrate: metadata.bitrate,
          duration: metadata.duration,
          frameRate: metadata.frameRate
        }
      }

      this.processingJobs.set(jobId, { status: 'processing', progress: 40 })

      // Generate thumbnail
      if (options.generateThumbnail !== false) {
        const thumbnailPath = await this.generateVideoThumbnail(
          user, tempFilePath, options, jobId
        )
        if (thumbnailPath) {
          processed.thumbnail = thumbnailPath
        }
      }

      this.processingJobs.set(jobId, { status: 'processing', progress: 60 })

      // Transcode video
      if (options.transcodeVideo && options.videoFormats) {
        const transcodedFiles = await this.transcodeVideo(
          user, tempFilePath, options, jobId
        )
        if (transcodedFiles.length > 0) {
          processed.transcoded = transcodedFiles
        }
      }

      this.processingJobs.set(jobId, { status: 'processing', progress: 90 })

      // Generate preview
      if (options.generatePreview) {
        const previewPath = await this.generateVideoPreview(
          user, tempFilePath, options, jobId
        )
        if (previewPath) {
          processed.preview = previewPath
        }
      }

      this.processingJobs.set(jobId, { status: 'completed', progress: 100 })

      // Cleanup temp files
      await this.cleanupTempFiles([tempFilePath])

      return {
        success: true,
        processed,
        jobId
      }
    } catch (error) {
      console.error('Error processing video:', error)
      return {
        success: false,
        error: 'Video processing failed',
        jobId
      }
    }
  }

  /**
   * Process audio files
   */
  private async processAudio(
    user: AuthenticatedUser,
    filePath: string,
    options: ProcessingOptions,
    jobId: string
  ): Promise<ProcessingResult> {
    try {
      // Download original file
      const tempFilePath = path.join(this.tempDir, `${jobId}_original.mp3`)
      const downloadResult = await fileUploadService.generateDownloadUrl(user, filePath)
      
      if (!downloadResult.success || !downloadResult.url) {
        throw new Error('Failed to download original file')
      }

      const response = await fetch(downloadResult.url)
      const buffer = await response.arrayBuffer()
      await fs.writeFile(tempFilePath, Buffer.from(buffer))

      this.processingJobs.set(jobId, { status: 'processing', progress: 50 })

      // Get audio metadata
      const metadata = await this.getAudioMetadata(tempFilePath)
      
      const processed: ProcessedMedia = {
        original: {
          url: downloadResult.url,
          path: filePath,
          size: buffer.byteLength,
          duration: metadata.duration
        },
        metadata: {
          format: metadata.format,
          codec: metadata.codec,
          bitrate: metadata.bitrate,
          sampleRate: metadata.sampleRate,
          channels: metadata.channels,
          duration: metadata.duration
        }
      }

      this.processingJobs.set(jobId, { status: 'completed', progress: 100 })

      // Cleanup temp files
      await this.cleanupTempFiles([tempFilePath])

      return {
        success: true,
        processed,
        jobId
      }
    } catch (error) {
      console.error('Error processing audio:', error)
      return {
        success: false,
        error: 'Audio processing failed',
        jobId
      }
    }
  }

  /**
   * Optimize image
   */
  private async optimizeImage(
    user: AuthenticatedUser,
    tempFilePath: string,
    metadata: sharp.Metadata,
    options: ProcessingOptions,
    jobId: string
  ): Promise<ProcessedMedia['optimized']> {
    try {
      const optimizedFilePath = path.join(this.tempDir, `${jobId}_optimized.webp`)
      
      let pipeline = sharp(tempFilePath)

      // Resize if needed
      if (options.maxWidth || options.maxHeight) {
        pipeline = pipeline.resize(options.maxWidth, options.maxHeight, {
          fit: 'inside',
          withoutEnlargement: true
        })
      }

      // Convert to WebP for better compression
      pipeline = pipeline.webp({ 
        quality: options.quality || 85,
        effort: 6
      })

      await pipeline.toFile(optimizedFilePath)

      // Upload optimized file
      const optimizedBuffer = await fs.readFile(optimizedFilePath)
      const optimizedMetadata = await sharp(optimizedFilePath).metadata()
      
      const tenantPath = getTenantStoragePath(
        {
          instituteId: user.institute,
          branchId: user.branch,
          userId: user.id,
          userRole: user.legacyRole || user.role,
          isSuperAdmin: user.legacyRole === 'super_admin',
          isInstituteAdmin: user.legacyRole === 'institute_admin'
        },
        `optimized_${uuidv4()}.webp`
      )

      const uploadResult = await fileUploadService.uploadFile(
        user,
        optimizedBuffer,
        `optimized_${jobId}.webp`,
        'image/webp'
      )

      if (uploadResult.success && uploadResult.file) {
        return {
          url: uploadResult.file.url,
          path: uploadResult.file.path,
          size: optimizedBuffer.length,
          dimensions: optimizedMetadata.width && optimizedMetadata.height ? {
            width: optimizedMetadata.width,
            height: optimizedMetadata.height
          } : undefined,
          quality: options.quality || 85
        }
      }

      return undefined
    } catch (error) {
      console.error('Error optimizing image:', error)
      return undefined
    }
  }

  /**
   * Generate image thumbnail
   */
  private async generateImageThumbnail(
    user: AuthenticatedUser,
    tempFilePath: string,
    options: ProcessingOptions,
    jobId: string
  ): Promise<ProcessedMedia['thumbnail']> {
    try {
      const thumbnailFilePath = path.join(this.tempDir, `${jobId}_thumbnail.webp`)
      
      await sharp(tempFilePath)
        .resize(300, 300, {
          fit: 'cover',
          position: 'center'
        })
        .webp({ quality: 80 })
        .toFile(thumbnailFilePath)

      const thumbnailBuffer = await fs.readFile(thumbnailFilePath)
      
      const uploadResult = await fileUploadService.uploadFile(
        user,
        thumbnailBuffer,
        `thumbnail_${jobId}.webp`,
        'image/webp'
      )

      if (uploadResult.success && uploadResult.file) {
        return {
          url: uploadResult.file.url,
          path: uploadResult.file.path,
          size: thumbnailBuffer.length,
          dimensions: { width: 300, height: 300 }
        }
      }

      return undefined
    } catch (error) {
      console.error('Error generating image thumbnail:', error)
      return undefined
    }
  }

  /**
   * Generate video thumbnail
   */
  private async generateVideoThumbnail(
    user: AuthenticatedUser,
    tempFilePath: string,
    options: ProcessingOptions,
    jobId: string
  ): Promise<ProcessedMedia['thumbnail']> {
    return new Promise((resolve) => {
      const thumbnailFilePath = path.join(this.tempDir, `${jobId}_thumbnail.jpg`)
      
      ffmpeg(tempFilePath)
        .screenshots({
          timestamps: ['10%'],
          filename: `${jobId}_thumbnail.jpg`,
          folder: this.tempDir,
          size: '300x300'
        })
        .on('end', async () => {
          try {
            const thumbnailBuffer = await fs.readFile(thumbnailFilePath)
            
            const uploadResult = await fileUploadService.uploadFile(
              user,
              thumbnailBuffer,
              `thumbnail_${jobId}.jpg`,
              'image/jpeg'
            )

            if (uploadResult.success && uploadResult.file) {
              resolve({
                url: uploadResult.file.url,
                path: uploadResult.file.path,
                size: thumbnailBuffer.length,
                dimensions: { width: 300, height: 300 }
              })
            } else {
              resolve(undefined)
            }
          } catch (error) {
            console.error('Error uploading video thumbnail:', error)
            resolve(undefined)
          }
        })
        .on('error', (error) => {
          console.error('Error generating video thumbnail:', error)
          resolve(undefined)
        })
    })
  }

  /**
   * Get video metadata
   */
  private async getVideoMetadata(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err)
        } else {
          const videoStream = metadata.streams.find(s => s.codec_type === 'video')
          resolve({
            format: metadata.format.format_name,
            duration: metadata.format.duration,
            bitrate: metadata.format.bit_rate,
            codec: videoStream?.codec_name,
            frameRate: videoStream?.r_frame_rate
          })
        }
      })
    })
  }

  /**
   * Get audio metadata
   */
  private async getAudioMetadata(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err)
        } else {
          const audioStream = metadata.streams.find(s => s.codec_type === 'audio')
          resolve({
            format: metadata.format.format_name,
            duration: metadata.format.duration,
            bitrate: metadata.format.bit_rate,
            codec: audioStream?.codec_name,
            sampleRate: audioStream?.sample_rate,
            channels: audioStream?.channels
          })
        }
      })
    })
  }

  /**
   * Transcode video to multiple formats
   */
  private async transcodeVideo(
    user: AuthenticatedUser,
    tempFilePath: string,
    options: ProcessingOptions,
    jobId: string
  ): Promise<ProcessedMedia['transcoded']> {
    // Implementation for video transcoding
    // This would involve ffmpeg transcoding to different formats/resolutions
    return []
  }

  /**
   * Generate video preview
   */
  private async generateVideoPreview(
    user: AuthenticatedUser,
    tempFilePath: string,
    options: ProcessingOptions,
    jobId: string
  ): Promise<ProcessedMedia['preview']> {
    // Implementation for video preview generation
    return undefined
  }

  /**
   * Get processing job status
   */
  getJobStatus(jobId: string): { status: string; progress: number } | null {
    return this.processingJobs.get(jobId) || null
  }

  /**
   * Cleanup temporary files
   */
  private async cleanupTempFiles(filePaths: string[]) {
    for (const filePath of filePaths) {
      try {
        await fs.unlink(filePath)
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }
}

// Export singleton instance
export const mediaProcessingService = new MediaProcessingService()

export default mediaProcessingService
