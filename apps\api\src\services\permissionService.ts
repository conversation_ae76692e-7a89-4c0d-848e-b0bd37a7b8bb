import { Payload } from 'payload'

export interface PermissionCheck {
  userId: string
  permission: string
  scope?: 'platform' | 'institute' | 'branch' | 'self'
  targetId?: string
}

export interface UserPermissionSummary {
  userId: string
  rolePermissions: Array<{
    permission: string
    scope: string
    source: 'role'
    roleId: string
    roleName: string
  }>
  customPermissions: Array<{
    permission: string
    scope: string
    type: 'grant' | 'revoke' | 'override'
    source: 'custom'
    status: string
  }>
  effectivePermissions: Array<{
    permission: string
    scope: string
    hasAccess: boolean
    source: 'role' | 'custom'
  }>
}

export class PermissionService {
  private payload: Payload

  constructor(payload: Payload) {
    this.payload = payload
  }

  async checkPermission(check: PermissionCheck): Promise<boolean> {
    try {
      const { userId, permission, scope = 'self', targetId } = check

      // Get user with role information
      const user = await this.payload.findByID({
        collection: 'users',
        id: userId,
        populate: ['role', 'institute', 'branch']
      })

      if (!user || !user.isActive) {
        return false
      }

      // Check role-based permissions
      const rolePermissions = await this.getRolePermissions(user.role, scope, targetId)
      const hasRolePermission = rolePermissions.some(p => p.code === permission)

      // Check custom user permissions
      const customPermissions = await this.getUserCustomPermissions(userId, permission, scope, targetId)
      
      // Calculate effective permission
      return this.calculateEffectivePermission(hasRolePermission, customPermissions)

    } catch (error) {
      console.error('Permission check error:', error)
      return false
    }
  }

  async getUserPermissionSummary(userId: string): Promise<UserPermissionSummary> {
    try {
      // Get user with role
      const user = await this.payload.findByID({
        collection: 'users',
        id: userId,
        populate: ['role']
      })

      if (!user) {
        throw new Error('User not found')
      }

      // Get role permissions
      const rolePermissions = await this.getAllRolePermissions(user.role)

      // Get custom permissions
      const customPermissions = await this.getAllUserCustomPermissions(userId)

      // Calculate effective permissions
      const effectivePermissions = this.calculateAllEffectivePermissions(rolePermissions, customPermissions)

      return {
        userId,
        rolePermissions,
        customPermissions,
        effectivePermissions
      }

    } catch (error) {
      console.error('Get user permission summary error:', error)
      throw error
    }
  }

  private async getRolePermissions(roleId: string, scope: string, targetId?: string): Promise<any[]> {
    if (!roleId) return []

    const role = await this.payload.findByID({
      collection: 'roles',
      id: roleId,
      populate: ['permissions.permission']
    })

    if (!role || !role.isActive) return []

    // Filter permissions by scope
    return role.permissions
      .filter((rp: any) => this.isScopeMatch(rp.scope, scope, role, targetId))
      .map((rp: any) => rp.permission)
      .filter((p: any) => p && p.isActive)
  }

  private async getAllRolePermissions(roleId: string): Promise<any[]> {
    if (!roleId) return []

    const role = await this.payload.findByID({
      collection: 'roles',
      id: roleId,
      populate: ['permissions.permission']
    })

    if (!role || !role.isActive) return []

    return role.permissions
      .map((rp: any) => ({
        permission: rp.permission.code,
        scope: rp.scope,
        source: 'role',
        roleId: role.id,
        roleName: role.name
      }))
      .filter((p: any) => p.permission)
  }

  private async getUserCustomPermissions(userId: string, permission: string, scope: string, targetId?: string): Promise<any[]> {
    const userPermissions = await this.payload.find({
      collection: 'user-permissions',
      where: {
        and: [
          { user: { equals: userId } },
          { 'permission.code': { equals: permission } },
          { scope: { equals: scope } },
          { status: { equals: 'approved' } },
          { isActive: { equals: true } },
          // Check effective period
          { 'effectivePeriod.startDate': { less_than_equal: new Date() } },
          {
            or: [
              { 'effectivePeriod.endDate': { greater_than_equal: new Date() } },
              { 'effectivePeriod.endDate': { exists: false } }
            ]
          }
        ]
      },
      populate: ['permission']
    })

    return userPermissions.docs.filter(up => 
      this.isScopeTargetMatch(up.scopeTarget, scope, targetId)
    )
  }

  private async getAllUserCustomPermissions(userId: string): Promise<any[]> {
    const userPermissions = await this.payload.find({
      collection: 'user-permissions',
      where: {
        and: [
          { user: { equals: userId } },
          { status: { equals: 'approved' } },
          { isActive: { equals: true } },
          { 'effectivePeriod.startDate': { less_than_equal: new Date() } },
          {
            or: [
              { 'effectivePeriod.endDate': { greater_than_equal: new Date() } },
              { 'effectivePeriod.endDate': { exists: false } }
            ]
          }
        ]
      },
      populate: ['permission']
    })

    return userPermissions.docs.map(up => ({
      permission: up.permission.code,
      scope: up.scope,
      type: up.type,
      source: 'custom',
      status: up.status
    }))
  }

  private calculateEffectivePermission(hasRolePermission: boolean, customPermissions: any[]): boolean {
    // Start with role permission
    let hasPermission = hasRolePermission

    // Apply custom permissions in order
    for (const customPerm of customPermissions) {
      switch (customPerm.type) {
        case 'grant':
          hasPermission = true
          break
        case 'revoke':
          hasPermission = false
          break
        case 'override':
          hasPermission = true
          break
      }
    }

    return hasPermission
  }

  private calculateAllEffectivePermissions(rolePermissions: any[], customPermissions: any[]): any[] {
    const permissionMap = new Map()

    // Add role permissions
    rolePermissions.forEach(rp => {
      const key = `${rp.permission}:${rp.scope}`
      permissionMap.set(key, {
        permission: rp.permission,
        scope: rp.scope,
        hasAccess: true,
        source: 'role'
      })
    })

    // Apply custom permissions
    customPermissions.forEach(cp => {
      const key = `${cp.permission}:${cp.scope}`
      const existing = permissionMap.get(key)

      let hasAccess = existing?.hasAccess || false

      switch (cp.type) {
        case 'grant':
          hasAccess = true
          break
        case 'revoke':
          hasAccess = false
          break
        case 'override':
          hasAccess = true
          break
      }

      permissionMap.set(key, {
        permission: cp.permission,
        scope: cp.scope,
        hasAccess,
        source: 'custom'
      })
    })

    return Array.from(permissionMap.values())
  }

  private isScopeMatch(permissionScope: string, requestedScope: string, role: any, targetId?: string): boolean {
    // Platform scope matches everything
    if (permissionScope === 'platform') return true

    // Exact scope match
    if (permissionScope === requestedScope) {
      // Check if role has access to the target
      switch (requestedScope) {
        case 'institute':
          return !targetId || role.scope?.institute === targetId
        case 'branch':
          return !targetId || role.scope?.branch === targetId
        default:
          return true
      }
    }

    // Higher scope includes lower scope
    const scopeHierarchy = ['platform', 'institute', 'branch', 'self']
    const permissionLevel = scopeHierarchy.indexOf(permissionScope)
    const requestedLevel = scopeHierarchy.indexOf(requestedScope)

    return permissionLevel <= requestedLevel
  }

  private isScopeTargetMatch(scopeTarget: any, scope: string, targetId?: string): boolean {
    if (!targetId) return true

    switch (scope) {
      case 'institute':
        return scopeTarget?.institute === targetId
      case 'branch':
        return scopeTarget?.branch === targetId
      default:
        return true
    }
  }

  async assignRoleToUser(userId: string, roleId: string, requestedBy: string): Promise<void> {
    try {
      await this.payload.update({
        collection: 'users',
        id: userId,
        data: {
          role: roleId
        }
      })

      // Log the role assignment
      console.log(`Role ${roleId} assigned to user ${userId} by ${requestedBy}`)

    } catch (error) {
      console.error('Role assignment error:', error)
      throw error
    }
  }

  async requestCustomPermission(
    userId: string, 
    permissionId: string, 
    type: 'grant' | 'revoke' | 'override',
    scope: string,
    scopeTarget: any,
    requestedBy: string,
    reason?: string
  ): Promise<string> {
    try {
      const userPermission = await this.payload.create({
        collection: 'user-permissions',
        data: {
          user: userId,
          permission: permissionId,
          type,
          scope,
          scopeTarget,
          status: 'pending',
          approvalWorkflow: {
            requestedBy,
            reason
          }
        }
      })

      return userPermission.id

    } catch (error) {
      console.error('Custom permission request error:', error)
      throw error
    }
  }

  async approveCustomPermission(permissionId: string, approvedBy: string, reason?: string): Promise<void> {
    try {
      await this.payload.update({
        collection: 'user-permissions',
        id: permissionId,
        data: {
          status: 'approved',
          'approvalWorkflow.approvedBy': approvedBy,
          'approvalWorkflow.approvalDate': new Date(),
          'approvalWorkflow.reason': reason
        }
      })

    } catch (error) {
      console.error('Permission approval error:', error)
      throw error
    }
  }

  async rejectCustomPermission(permissionId: string, rejectedBy: string, reason?: string): Promise<void> {
    try {
      await this.payload.update({
        collection: 'user-permissions',
        id: permissionId,
        data: {
          status: 'rejected',
          'approvalWorkflow.rejectedBy': rejectedBy,
          'approvalWorkflow.rejectionDate': new Date(),
          'approvalWorkflow.reason': reason
        }
      })

    } catch (error) {
      console.error('Permission rejection error:', error)
      throw error
    }
  }
}
