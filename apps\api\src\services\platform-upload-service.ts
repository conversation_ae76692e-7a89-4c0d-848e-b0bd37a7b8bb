import { Payload } from 'payload'
import { StorageFactory, StorageConfigManager, type UploadResult } from '../adapters/storage'
import { ImageProcessingService } from './image-processing-service'

/**
 * Platform Upload Service
 * Handles file uploads and integrates with Payload CMS Media collection
 */

export interface MediaRecord {
  id: string
  filename: string
  mimeType: string
  filesize: number
  width?: number
  height?: number
  url: string
  sizes?: Record<string, any>
  alt?: string
  focalX?: number
  focalY?: number
}

export class PlatformUploadService {
  private payload: Payload

  constructor(payload: Payload) {
    this.payload = payload
  }

  /**
   * Upload file and create media record
   */
  async uploadFile(
    buffer: Buffer,
    originalName: string,
    mimeType: string,
    options: {
      folder?: string
      mediaType?: string
      alt?: string
      generateSizes?: any[]
    } = {}
  ): Promise<{ uploadResult: UploadResult; mediaRecord: MediaRecord }> {
    console.log('🚀 Platform upload service started:', {
      originalName,
      mimeType,
      size: buffer.length,
      options
    })

    try {
      // Validate and optimize image if it's an image file
      let processedBuffer = buffer
      let processedMimeType = mimeType
      let imageMetadata: any = {}

      if (mimeType.startsWith('image/')) {
        console.log('🖼️ Processing image file...')

        // Validate image
        const validation = await ImageProcessingService.validateAndGetMetadata(buffer)
        if (!validation.valid) {
          throw new Error(validation.message || 'Invalid image file')
        }

        imageMetadata = {
          width: validation.metadata?.width,
          height: validation.metadata?.height,
          format: validation.metadata?.format
        }

        // Optimize image for web
        const optimized = await ImageProcessingService.optimizeForWeb(buffer, {
          maxWidth: 2048,
          maxHeight: 2048,
          quality: 85,
          preferWebP: false // Keep original format for compatibility
        })

        processedBuffer = optimized.buffer
        processedMimeType = `image/${optimized.format}`

        console.log('✅ Image optimized:', {
          originalSize: buffer.length,
          optimizedSize: optimized.size,
          compression: `${((1 - optimized.size / buffer.length) * 100).toFixed(1)}%`
        })
      }

      // Get storage configuration and create adapter
      const configManager = StorageConfigManager.getInstance()
      const storageConfig = await configManager.getStorageConfig(this.payload)
      const storageAdapter = StorageFactory.create(storageConfig)

      console.log('📦 Storage adapter initialized:', {
        provider: storageConfig.provider
      })

      // Upload file using storage adapter
      const uploadResult = await storageAdapter.uploadFile(
        processedBuffer,
        originalName,
        processedMimeType,
        {
          folder: options.folder,
          mediaType: options.mediaType,
          generateSizes: options.generateSizes
        }
      )

      // Merge image metadata into upload result
      if (Object.keys(imageMetadata).length > 0) {
        uploadResult.metadata = {
          ...uploadResult.metadata,
          ...imageMetadata
        }
      }

      console.log('✅ File uploaded to storage:', {
        id: uploadResult.id,
        filename: uploadResult.filename,
        url: uploadResult.url
      })

      // Create media record in Payload CMS
      const mediaRecord = await this.createMediaRecord(uploadResult, options.alt)

      console.log('✅ Media record created:', {
        id: mediaRecord.id,
        filename: mediaRecord.filename,
        url: mediaRecord.url
      })

      return {
        uploadResult,
        mediaRecord
      }

    } catch (error) {
      console.error('❌ Platform upload service error:', error)
      throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Delete file and media record
   */
  async deleteFile(mediaId: string): Promise<{ success: boolean; message?: string }> {
    console.log('🗑️ Deleting file and media record:', mediaId)

    try {
      // Get media record
      const mediaRecord = await this.payload.findByID({
        collection: 'media',
        id: mediaId
      })

      if (!mediaRecord) {
        return {
          success: false,
          message: 'Media record not found'
        }
      }

      // Get storage adapter
      const configManager = StorageConfigManager.getInstance()
      const storageConfig = await configManager.getStorageConfig(this.payload)
      const storageAdapter = StorageFactory.create(storageConfig)

      // Delete file from storage
      const deleteResult = await storageAdapter.deleteFile(mediaRecord.filename)

      if (!deleteResult.success) {
        console.warn('⚠️ Failed to delete file from storage:', deleteResult.message)
      }

      // Delete media record
      await this.payload.delete({
        collection: 'media',
        id: mediaId
      })

      console.log('✅ File and media record deleted successfully')

      return {
        success: true,
        message: 'File deleted successfully'
      }

    } catch (error) {
      console.error('❌ Delete file error:', error)
      return {
        success: false,
        message: `Delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Get file information
   */
  async getFileInfo(mediaId: string): Promise<MediaRecord | null> {
    try {
      const mediaRecord = await this.payload.findByID({
        collection: 'media',
        id: mediaId
      })

      return mediaRecord as MediaRecord

    } catch (error) {
      console.error('❌ Get file info error:', error)
      return null
    }
  }

  /**
   * Update media record
   */
  async updateMediaRecord(
    mediaId: string,
    updates: Partial<MediaRecord>
  ): Promise<MediaRecord | null> {
    try {
      const updatedRecord = await this.payload.update({
        collection: 'media',
        id: mediaId,
        data: updates
      })

      console.log('✅ Media record updated:', {
        id: updatedRecord.id,
        updates: Object.keys(updates)
      })

      return updatedRecord as MediaRecord

    } catch (error) {
      console.error('❌ Update media record error:', error)
      return null
    }
  }

  /**
   * Create media record in Payload CMS
   */
  private async createMediaRecord(
    uploadResult: UploadResult,
    alt?: string
  ): Promise<MediaRecord> {
    console.log('📝 Creating media record:', {
      filename: uploadResult.filename,
      mimeType: uploadResult.mimeType,
      size: uploadResult.size
    })

    try {
      const mediaData = {
        filename: uploadResult.filename,
        mimeType: uploadResult.mimeType,
        filesize: uploadResult.size,
        width: uploadResult.metadata.width,
        height: uploadResult.metadata.height,
        url: uploadResult.url,
        alt: alt || uploadResult.originalName,
        sizes: this.formatSizesForPayload(uploadResult.sizes)
      }

      const mediaRecord = await this.payload.create({
        collection: 'media',
        data: mediaData
      })

      console.log('✅ Media record created in Payload:', {
        id: mediaRecord.id,
        filename: mediaRecord.filename
      })

      return mediaRecord as MediaRecord

    } catch (error) {
      console.error('❌ Failed to create media record:', error)
      throw new Error(`Failed to create media record: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Format sizes for Payload CMS
   */
  private formatSizesForPayload(sizes?: Record<string, UploadResult>): Record<string, any> | undefined {
    if (!sizes) return undefined

    const formattedSizes: Record<string, any> = {}

    Object.entries(sizes).forEach(([sizeName, sizeResult]) => {
      formattedSizes[sizeName] = {
        filename: sizeResult.filename,
        width: sizeResult.metadata.width,
        height: sizeResult.metadata.height,
        mimeType: sizeResult.mimeType,
        filesize: sizeResult.size,
        url: sizeResult.url
      }
    })

    return formattedSizes
  }

  /**
   * Health check for upload service
   */
  async healthCheck(): Promise<{ healthy: boolean; message?: string; details?: any }> {
    try {
      // Check storage adapter health
      const configManager = StorageConfigManager.getInstance()
      const storageConfig = await configManager.getStorageConfig(this.payload)
      const storageAdapter = StorageFactory.create(storageConfig)
      
      const storageHealth = await storageAdapter.healthCheck()

      // Check Payload CMS connection
      const mediaCount = await this.payload.count({
        collection: 'media'
      })

      const healthy = storageHealth.healthy && typeof mediaCount.totalDocs === 'number'

      return {
        healthy,
        message: healthy ? 'Upload service is healthy' : 'Upload service has issues',
        details: {
          storage: storageHealth,
          payloadConnection: typeof mediaCount.totalDocs === 'number',
          mediaCount: mediaCount.totalDocs
        }
      }

    } catch (error) {
      return {
        healthy: false,
        message: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }
}
