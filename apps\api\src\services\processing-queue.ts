import { EventEmitter } from 'events'
import { v4 as uuidv4 } from 'uuid'
import { mediaProcessingService } from './media-processing'
import type { AuthenticatedUser } from '../middleware/auth'

/**
 * Media Processing Queue Service for Course Builder System
 * Manages background processing jobs with priority queuing and retry logic
 */

export interface ProcessingJob {
  id: string
  userId: string
  instituteId: string
  branchId?: string
  type: 'image' | 'video' | 'audio' | 'batch'
  filePath: string
  mimeType: string
  options: any
  priority: 'low' | 'normal' | 'high' | 'urgent'
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  result?: any
  error?: string
  createdAt: Date
  startedAt?: Date
  completedAt?: Date
  retryCount: number
  maxRetries: number
  metadata?: Record<string, any>
}

export interface QueueStats {
  total: number
  pending: number
  processing: number
  completed: number
  failed: number
  cancelled: number
  averageProcessingTime: number
  throughput: number
}

class ProcessingQueueService extends EventEmitter {
  private jobs = new Map<string, ProcessingJob>()
  private processingJobs = new Set<string>()
  private maxConcurrentJobs = 3
  private retryDelay = 5000 // 5 seconds
  private cleanupInterval = 60000 // 1 minute
  private maxJobAge = 24 * 60 * 60 * 1000 // 24 hours

  constructor() {
    super()
    this.startQueueProcessor()
    this.startCleanupWorker()
  }

  /**
   * Add job to processing queue
   */
  async addJob(
    user: AuthenticatedUser,
    type: ProcessingJob['type'],
    filePath: string,
    mimeType: string,
    options: any = {},
    priority: ProcessingJob['priority'] = 'normal'
  ): Promise<string> {
    const jobId = uuidv4()
    
    const job: ProcessingJob = {
      id: jobId,
      userId: user.id,
      instituteId: user.institute,
      branchId: user.branch,
      type,
      filePath,
      mimeType,
      options,
      priority,
      status: 'pending',
      progress: 0,
      createdAt: new Date(),
      retryCount: 0,
      maxRetries: 3,
      metadata: {
        userEmail: user.email,
        userRole: user.legacyRole || user.role
      }
    }

    this.jobs.set(jobId, job)
    
    // Emit job added event
    this.emit('jobAdded', job)
    
    // Start processing if slots available
    this.processNextJob()
    
    return jobId
  }

  /**
   * Get job by ID
   */
  getJob(jobId: string): ProcessingJob | null {
    return this.jobs.get(jobId) || null
  }

  /**
   * Get jobs for user
   */
  getUserJobs(userId: string, limit: number = 50): ProcessingJob[] {
    const userJobs = Array.from(this.jobs.values())
      .filter(job => job.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit)
    
    return userJobs
  }

  /**
   * Get jobs for institute
   */
  getInstituteJobs(instituteId: string, limit: number = 100): ProcessingJob[] {
    const instituteJobs = Array.from(this.jobs.values())
      .filter(job => job.instituteId === instituteId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit)
    
    return instituteJobs
  }

  /**
   * Cancel job
   */
  cancelJob(jobId: string, userId?: string): boolean {
    const job = this.jobs.get(jobId)
    
    if (!job) {
      return false
    }

    // Check if user can cancel this job
    if (userId && job.userId !== userId) {
      return false
    }

    if (job.status === 'pending' || job.status === 'processing') {
      job.status = 'cancelled'
      job.completedAt = new Date()
      
      // Remove from processing set if it was being processed
      this.processingJobs.delete(jobId)
      
      // Emit job cancelled event
      this.emit('jobCancelled', job)
      
      // Start next job
      this.processNextJob()
      
      return true
    }

    return false
  }

  /**
   * Retry failed job
   */
  retryJob(jobId: string): boolean {
    const job = this.jobs.get(jobId)
    
    if (!job || job.status !== 'failed') {
      return false
    }

    if (job.retryCount >= job.maxRetries) {
      return false
    }

    job.status = 'pending'
    job.progress = 0
    job.error = undefined
    job.retryCount++
    
    // Emit job retried event
    this.emit('jobRetried', job)
    
    // Start processing
    this.processNextJob()
    
    return true
  }

  /**
   * Get queue statistics
   */
  getQueueStats(): QueueStats {
    const jobs = Array.from(this.jobs.values())
    
    const stats: QueueStats = {
      total: jobs.length,
      pending: jobs.filter(j => j.status === 'pending').length,
      processing: jobs.filter(j => j.status === 'processing').length,
      completed: jobs.filter(j => j.status === 'completed').length,
      failed: jobs.filter(j => j.status === 'failed').length,
      cancelled: jobs.filter(j => j.status === 'cancelled').length,
      averageProcessingTime: 0,
      throughput: 0
    }

    // Calculate average processing time
    const completedJobs = jobs.filter(j => j.status === 'completed' && j.startedAt && j.completedAt)
    if (completedJobs.length > 0) {
      const totalTime = completedJobs.reduce((sum, job) => {
        return sum + (job.completedAt!.getTime() - job.startedAt!.getTime())
      }, 0)
      stats.averageProcessingTime = totalTime / completedJobs.length
    }

    // Calculate throughput (jobs per hour in last 24 hours)
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000)
    const recentCompleted = jobs.filter(j => 
      j.status === 'completed' && 
      j.completedAt && 
      j.completedAt > last24Hours
    )
    stats.throughput = recentCompleted.length

    return stats
  }

  /**
   * Start queue processor
   */
  private startQueueProcessor() {
    setInterval(() => {
      this.processNextJob()
    }, 1000) // Check every second
  }

  /**
   * Process next job in queue
   */
  private async processNextJob() {
    // Check if we have available slots
    if (this.processingJobs.size >= this.maxConcurrentJobs) {
      return
    }

    // Get next pending job with highest priority
    const pendingJobs = Array.from(this.jobs.values())
      .filter(job => job.status === 'pending')
      .sort((a, b) => {
        // Sort by priority first, then by creation time
        const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 }
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
        if (priorityDiff !== 0) return priorityDiff
        return a.createdAt.getTime() - b.createdAt.getTime()
      })

    if (pendingJobs.length === 0) {
      return
    }

    const job = pendingJobs[0]
    await this.processJob(job)
  }

  /**
   * Process individual job
   */
  private async processJob(job: ProcessingJob) {
    try {
      // Mark job as processing
      job.status = 'processing'
      job.startedAt = new Date()
      job.progress = 0
      this.processingJobs.add(job.id)

      // Emit job started event
      this.emit('jobStarted', job)

      // Create user object for processing
      const user: AuthenticatedUser = {
        id: job.userId,
        email: job.metadata?.userEmail || '',
        role: job.metadata?.userRole || 'student',
        legacyRole: job.metadata?.userRole || 'student',
        institute: job.instituteId,
        branch: job.branchId,
        permissions: [],
        isActive: true,
        firstName: '',
        lastName: ''
      }

      // Process the media
      const result = await mediaProcessingService.processMedia(
        user,
        job.filePath,
        job.mimeType,
        job.options
      )

      if (result.success) {
        // Job completed successfully
        job.status = 'completed'
        job.progress = 100
        job.result = result.processed
        job.completedAt = new Date()
        
        // Emit job completed event
        this.emit('jobCompleted', job)
      } else {
        // Job failed
        throw new Error(result.error || 'Processing failed')
      }

    } catch (error) {
      // Job failed
      job.status = 'failed'
      job.error = error instanceof Error ? error.message : 'Unknown error'
      job.completedAt = new Date()
      
      // Emit job failed event
      this.emit('jobFailed', job)

      // Schedule retry if retries available
      if (job.retryCount < job.maxRetries) {
        setTimeout(() => {
          this.retryJob(job.id)
        }, this.retryDelay * (job.retryCount + 1)) // Exponential backoff
      }
    } finally {
      // Remove from processing set
      this.processingJobs.delete(job.id)
      
      // Process next job
      setTimeout(() => this.processNextJob(), 100)
    }
  }

  /**
   * Get job by ID
   */
  getJob(jobId: string): ProcessingJob | null {
    return this.jobs.get(jobId) || null
  }

  /**
   * Get jobs with filtering and pagination
   */
  getJobs(options: {
    status?: ProcessingJob['status']
    type?: ProcessingJob['type']
    priority?: ProcessingJob['priority']
    search?: string
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    userId?: string
    instituteId?: string
  } = {}) {
    let jobs = Array.from(this.jobs.values())

    // Apply filters
    if (options.status) {
      jobs = jobs.filter(job => job.status === options.status)
    }

    if (options.type) {
      jobs = jobs.filter(job => job.type === options.type)
    }

    if (options.priority) {
      jobs = jobs.filter(job => job.priority === options.priority)
    }

    if (options.userId) {
      jobs = jobs.filter(job => job.userId === options.userId)
    }

    if (options.instituteId) {
      jobs = jobs.filter(job => job.instituteId === options.instituteId)
    }

    if (options.search) {
      const searchLower = options.search.toLowerCase()
      jobs = jobs.filter(job =>
        job.id.toLowerCase().includes(searchLower) ||
        job.filePath.toLowerCase().includes(searchLower) ||
        job.mimeType.toLowerCase().includes(searchLower) ||
        (job.error && job.error.toLowerCase().includes(searchLower))
      )
    }

    // Sort jobs
    const sortBy = options.sortBy || 'createdAt'
    const sortOrder = options.sortOrder || 'desc'

    jobs.sort((a, b) => {
      let aValue: any = a[sortBy as keyof ProcessingJob]
      let bValue: any = b[sortBy as keyof ProcessingJob]

      // Handle date sorting
      if (aValue instanceof Date) {
        aValue = aValue.getTime()
      }
      if (bValue instanceof Date) {
        bValue = bValue.getTime()
      }

      // Handle priority sorting
      if (sortBy === 'priority') {
        const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 }
        aValue = priorityOrder[aValue as keyof typeof priorityOrder]
        bValue = priorityOrder[bValue as keyof typeof priorityOrder]
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      }
    })

    // Apply pagination
    const page = options.page || 1
    const limit = options.limit || 20
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit

    const paginatedJobs = jobs.slice(startIndex, endIndex)

    return {
      jobs: paginatedJobs,
      pagination: {
        page,
        limit,
        total: jobs.length,
        totalPages: Math.ceil(jobs.length / limit),
        hasNextPage: endIndex < jobs.length,
        hasPrevPage: page > 1
      }
    }
  }

  /**
   * Get recent jobs within specified hours
   */
  getRecentJobs(hours: number = 24): ProcessingJob[] {
    const cutoffTime = new Date(Date.now() - (hours * 60 * 60 * 1000))

    return Array.from(this.jobs.values())
      .filter(job => job.createdAt >= cutoffTime)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
  }

  /**
   * Get active jobs count
   */
  getActiveJobsCount(): number {
    return this.processingJobs.size
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.jobs.get(jobId)

    if (!job) {
      return false
    }

    if (job.status === 'pending') {
      job.status = 'cancelled'
      job.completedAt = new Date()
      this.emit('jobCancelled', job)
      return true
    }

    if (job.status === 'processing') {
      // Mark for cancellation - the processing logic should check this
      job.status = 'cancelled'
      job.completedAt = new Date()
      this.processingJobs.delete(jobId)
      this.emit('jobCancelled', job)
      return true
    }

    return false
  }

  /**
   * Update job priority
   */
  async updateJobPriority(jobId: string, priority: ProcessingJob['priority']): Promise<boolean> {
    const job = this.jobs.get(jobId)

    if (!job || job.status !== 'pending') {
      return false
    }

    job.priority = priority
    this.emit('jobPriorityUpdated', job)
    return true
  }

  /**
   * Start cleanup worker
   */
  private startCleanupWorker() {
    setInterval(() => {
      this.cleanupOldJobs()
    }, this.cleanupInterval)
  }

  /**
   * Cleanup old completed/failed jobs
   */
  private cleanupOldJobs() {
    const cutoffTime = new Date(Date.now() - this.maxJobAge)
    const jobsToDelete: string[] = []

    for (const [jobId, job] of this.jobs.entries()) {
      if (
        (job.status === 'completed' || job.status === 'failed' || job.status === 'cancelled') &&
        job.completedAt &&
        job.completedAt < cutoffTime
      ) {
        jobsToDelete.push(jobId)
      }
    }

    for (const jobId of jobsToDelete) {
      this.jobs.delete(jobId)
    }

    if (jobsToDelete.length > 0) {
      console.log(`Cleaned up ${jobsToDelete.length} old processing jobs`)
    }
  }

  /**
   * Set maximum concurrent jobs
   */
  setMaxConcurrentJobs(max: number) {
    this.maxConcurrentJobs = Math.max(1, Math.min(10, max))
  }

  /**
   * Get current queue status
   */
  getQueueStatus() {
    return {
      maxConcurrentJobs: this.maxConcurrentJobs,
      currentlyProcessing: this.processingJobs.size,
      availableSlots: this.maxConcurrentJobs - this.processingJobs.size,
      totalJobs: this.jobs.size,
      stats: this.getQueueStats()
    }
  }
}

// Export singleton instance
export const processingQueueService = new ProcessingQueueService()

export default processingQueueService
