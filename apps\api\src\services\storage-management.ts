import { S3Client, ListObjectsV2Command, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3'
import { promises as fs } from 'fs'
import path from 'path'
import { getStorageConfig, createS3Client } from '../config/storage'
import { fileUploadService } from './file-upload'
import type { AuthenticatedUser } from '../middleware/auth'

/**
 * Storage Management Service for Course Builder System
 * Handles file organization, cleanup, analytics, and storage optimization
 */

export interface StorageUsage {
  totalFiles: number
  totalSize: number
  sizeByCategory: Record<string, number>
  filesByCategory: Record<string, number>
  sizeByInstitute: Record<string, number>
  filesOlderThan30Days: number
  filesOlderThan90Days: number
  duplicateFiles: number
  orphanedFiles: number
}

export interface FileInfo {
  path: string
  size: number
  lastModified: Date
  category: string
  mimeType?: string
  instituteId?: string
  branchId?: string
  uploadedBy?: string
  isDuplicate?: boolean
  isOrphaned?: boolean
}

export interface CleanupResult {
  deletedFiles: number
  freedSpace: number
  errors: string[]
}

export interface StorageQuota {
  instituteId: string
  totalQuota: number
  usedSpace: number
  fileCount: number
  quotaPercentage: number
  isOverQuota: boolean
  warningThreshold: number
}

class StorageManagementService {
  private config = getStorageConfig()
  private s3Client: S3Client | null = null

  constructor() {
    if (this.config.provider !== 'local') {
      this.s3Client = createS3Client(this.config)
    }
  }

  /**
   * Get storage usage analytics
   */
  async getStorageUsage(instituteId?: string): Promise<StorageUsage> {
    try {
      const files = await this.listAllFiles(instituteId)
      
      const usage: StorageUsage = {
        totalFiles: files.length,
        totalSize: 0,
        sizeByCategory: {},
        filesByCategory: {},
        sizeByInstitute: {},
        filesOlderThan30Days: 0,
        filesOlderThan90Days: 0,
        duplicateFiles: 0,
        orphanedFiles: 0
      }

      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)

      for (const file of files) {
        usage.totalSize += file.size

        // Category breakdown
        const category = file.category || 'other'
        usage.sizeByCategory[category] = (usage.sizeByCategory[category] || 0) + file.size
        usage.filesByCategory[category] = (usage.filesByCategory[category] || 0) + 1

        // Institute breakdown
        if (file.instituteId) {
          usage.sizeByInstitute[file.instituteId] = (usage.sizeByInstitute[file.instituteId] || 0) + file.size
        }

        // Age analysis
        if (file.lastModified < thirtyDaysAgo) {
          usage.filesOlderThan30Days++
        }
        if (file.lastModified < ninetyDaysAgo) {
          usage.filesOlderThan90Days++
        }

        // Duplicate and orphaned files
        if (file.isDuplicate) {
          usage.duplicateFiles++
        }
        if (file.isOrphaned) {
          usage.orphanedFiles++
        }
      }

      return usage
    } catch (error) {
      console.error('Error getting storage usage:', error)
      throw new Error('Failed to get storage usage')
    }
  }

  /**
   * List all files in storage
   */
  private async listAllFiles(instituteId?: string): Promise<FileInfo[]> {
    if (this.config.provider === 'local') {
      return await this.listLocalFiles(instituteId)
    } else {
      return await this.listCloudFiles(instituteId)
    }
  }

  /**
   * List local files
   */
  private async listLocalFiles(instituteId?: string): Promise<FileInfo[]> {
    const files: FileInfo[] = []
    const uploadsDir = path.join(process.cwd(), 'uploads')

    try {
      await fs.access(uploadsDir)
    } catch {
      return files
    }

    const scanDirectory = async (dirPath: string, relativePath: string = '') => {
      try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true })

        for (const entry of entries) {
          const fullPath = path.join(dirPath, entry.name)
          const relativeFilePath = path.join(relativePath, entry.name)

          if (entry.isDirectory()) {
            await scanDirectory(fullPath, relativeFilePath)
          } else {
            const stats = await fs.stat(fullPath)
            const pathParts = relativeFilePath.split(path.sep)
            
            // Extract institute and branch from path
            let fileInstituteId: string | undefined
            let branchId: string | undefined
            
            if (pathParts[0] === 'institutes' && pathParts[1]) {
              fileInstituteId = pathParts[1]
              if (pathParts[2] === 'branches' && pathParts[3]) {
                branchId = pathParts[3]
              }
            }

            // Skip if filtering by institute and doesn't match
            if (instituteId && fileInstituteId !== instituteId) {
              continue
            }

            const category = this.getCategoryFromPath(relativeFilePath)
            
            files.push({
              path: relativeFilePath.replace(/\\/g, '/'),
              size: stats.size,
              lastModified: stats.mtime,
              category,
              instituteId: fileInstituteId,
              branchId,
              isDuplicate: false,
              isOrphaned: false
            })
          }
        }
      } catch (error) {
        console.error(`Error scanning directory ${dirPath}:`, error)
      }
    }

    await scanDirectory(uploadsDir)
    return files
  }

  /**
   * List cloud files
   */
  private async listCloudFiles(instituteId?: string): Promise<FileInfo[]> {
    if (!this.s3Client) {
      throw new Error('S3 client not initialized')
    }

    const files: FileInfo[] = []
    let continuationToken: string | undefined

    try {
      do {
        const command = new ListObjectsV2Command({
          Bucket: this.config.bucket,
          Prefix: instituteId ? `institutes/${instituteId}/` : undefined,
          ContinuationToken: continuationToken,
          MaxKeys: 1000
        })

        const response = await this.s3Client.send(command)

        if (response.Contents) {
          for (const object of response.Contents) {
            if (object.Key && object.Size && object.LastModified) {
              const pathParts = object.Key.split('/')
              let fileInstituteId: string | undefined
              let branchId: string | undefined
              
              if (pathParts[0] === 'institutes' && pathParts[1]) {
                fileInstituteId = pathParts[1]
                if (pathParts[2] === 'branches' && pathParts[3]) {
                  branchId = pathParts[3]
                }
              }

              const category = this.getCategoryFromPath(object.Key)
              
              files.push({
                path: object.Key,
                size: object.Size,
                lastModified: object.LastModified,
                category,
                instituteId: fileInstituteId,
                branchId,
                isDuplicate: false,
                isOrphaned: false
              })
            }
          }
        }

        continuationToken = response.NextContinuationToken
      } while (continuationToken)

      return files
    } catch (error) {
      console.error('Error listing cloud files:', error)
      throw new Error('Failed to list cloud files')
    }
  }

  /**
   * Get file category from path
   */
  private getCategoryFromPath(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase()
    
    const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
    const videoExts = ['.mp4', '.webm', '.mov', '.avi']
    const audioExts = ['.mp3', '.wav', '.ogg', '.m4a']
    const documentExts = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.csv']
    
    if (imageExts.includes(ext)) return 'image'
    if (videoExts.includes(ext)) return 'video'
    if (audioExts.includes(ext)) return 'audio'
    if (documentExts.includes(ext)) return 'document'
    
    return 'other'
  }

  /**
   * Cleanup old and unused files
   */
  async cleanupFiles(
    user: AuthenticatedUser,
    options: {
      deleteOlderThan?: number // days
      deleteDuplicates?: boolean
      deleteOrphaned?: boolean
      dryRun?: boolean
    } = {}
  ): Promise<CleanupResult> {
    const result: CleanupResult = {
      deletedFiles: 0,
      freedSpace: 0,
      errors: []
    }

    try {
      const files = await this.listAllFiles(
        user.legacyRole === 'super_admin' ? undefined : user.institute
      )

      const filesToDelete: FileInfo[] = []

      // Filter files for deletion
      for (const file of files) {
        let shouldDelete = false

        // Delete old files
        if (options.deleteOlderThan) {
          const cutoffDate = new Date(Date.now() - options.deleteOlderThan * 24 * 60 * 60 * 1000)
          if (file.lastModified < cutoffDate) {
            shouldDelete = true
          }
        }

        // Delete duplicates
        if (options.deleteDuplicates && file.isDuplicate) {
          shouldDelete = true
        }

        // Delete orphaned files
        if (options.deleteOrphaned && file.isOrphaned) {
          shouldDelete = true
        }

        if (shouldDelete) {
          filesToDelete.push(file)
        }
      }

      // Delete files
      if (!options.dryRun) {
        for (const file of filesToDelete) {
          try {
            const deleteResult = await fileUploadService.deleteFile(user, file.path)
            
            if (deleteResult.success) {
              result.deletedFiles++
              result.freedSpace += file.size
            } else {
              result.errors.push(`Failed to delete ${file.path}: ${deleteResult.error}`)
            }
          } catch (error) {
            result.errors.push(`Error deleting ${file.path}: ${error instanceof Error ? error.message : 'Unknown error'}`)
          }
        }
      } else {
        // Dry run - just count what would be deleted
        result.deletedFiles = filesToDelete.length
        result.freedSpace = filesToDelete.reduce((sum, file) => sum + file.size, 0)
      }

      return result
    } catch (error) {
      console.error('Error during cleanup:', error)
      throw new Error('Cleanup operation failed')
    }
  }

  /**
   * Get storage quota information for institutes
   */
  async getStorageQuotas(): Promise<StorageQuota[]> {
    try {
      const usage = await this.getStorageUsage()
      const quotas: StorageQuota[] = []

      // Default quota per institute (can be configured per institute)
      const defaultQuota = 10 * 1024 * 1024 * 1024 // 10GB

      for (const [instituteId, usedSpace] of Object.entries(usage.sizeByInstitute)) {
        const quota: StorageQuota = {
          instituteId,
          totalQuota: defaultQuota,
          usedSpace,
          fileCount: 0, // Would need to calculate from file list
          quotaPercentage: (usedSpace / defaultQuota) * 100,
          isOverQuota: usedSpace > defaultQuota,
          warningThreshold: 80
        }

        quotas.push(quota)
      }

      return quotas.sort((a, b) => b.quotaPercentage - a.quotaPercentage)
    } catch (error) {
      console.error('Error getting storage quotas:', error)
      throw new Error('Failed to get storage quotas')
    }
  }

  /**
   * Optimize storage by moving old files to cheaper storage tiers
   */
  async optimizeStorage(
    user: AuthenticatedUser,
    options: {
      moveToIA?: number // days after which to move to Infrequent Access
      moveToGlacier?: number // days after which to move to Glacier
      dryRun?: boolean
    } = {}
  ): Promise<{
    movedToIA: number
    movedToGlacier: number
    savedCosts: number
  }> {
    // This would implement storage class transitions for cloud storage
    // For now, return placeholder data
    return {
      movedToIA: 0,
      movedToGlacier: 0,
      savedCosts: 0
    }
  }

  /**
   * Generate storage report
   */
  async generateStorageReport(instituteId?: string): Promise<{
    usage: StorageUsage
    quotas: StorageQuota[]
    recommendations: string[]
    trends: any
  }> {
    try {
      const usage = await this.getStorageUsage(instituteId)
      const quotas = await this.getStorageQuotas()
      
      const recommendations: string[] = []

      // Generate recommendations
      if (usage.duplicateFiles > 0) {
        recommendations.push(`Remove ${usage.duplicateFiles} duplicate files to free up space`)
      }

      if (usage.orphanedFiles > 0) {
        recommendations.push(`Clean up ${usage.orphanedFiles} orphaned files`)
      }

      if (usage.filesOlderThan90Days > 0) {
        recommendations.push(`Consider archiving ${usage.filesOlderThan90Days} files older than 90 days`)
      }

      const overQuotaInstitutes = quotas.filter(q => q.isOverQuota)
      if (overQuotaInstitutes.length > 0) {
        recommendations.push(`${overQuotaInstitutes.length} institutes are over their storage quota`)
      }

      return {
        usage,
        quotas: instituteId ? quotas.filter(q => q.instituteId === instituteId) : quotas,
        recommendations,
        trends: {} // Would include historical data
      }
    } catch (error) {
      console.error('Error generating storage report:', error)
      throw new Error('Failed to generate storage report')
    }
  }

  /**
   * Check storage health
   */
  async checkStorageHealth(): Promise<{
    healthy: boolean
    issues: string[]
    metrics: {
      totalFiles: number
      totalSize: number
      averageFileSize: number
      largestFile: number
      oldestFile: Date
    }
  }> {
    try {
      const files = await this.listAllFiles()
      const issues: string[] = []

      if (files.length === 0) {
        issues.push('No files found in storage')
      }

      const totalSize = files.reduce((sum, file) => sum + file.size, 0)
      const averageFileSize = files.length > 0 ? totalSize / files.length : 0
      const largestFile = files.length > 0 ? Math.max(...files.map(f => f.size)) : 0
      const oldestFile = files.length > 0 ? new Date(Math.min(...files.map(f => f.lastModified.getTime()))) : new Date()

      // Check for issues
      const veryLargeFiles = files.filter(f => f.size > 100 * 1024 * 1024) // > 100MB
      if (veryLargeFiles.length > 0) {
        issues.push(`${veryLargeFiles.length} files are larger than 100MB`)
      }

      const veryOldFiles = files.filter(f => {
        const oneYearAgo = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
        return f.lastModified < oneYearAgo
      })
      if (veryOldFiles.length > 0) {
        issues.push(`${veryOldFiles.length} files are older than 1 year`)
      }

      return {
        healthy: issues.length === 0,
        issues,
        metrics: {
          totalFiles: files.length,
          totalSize,
          averageFileSize,
          largestFile,
          oldestFile
        }
      }
    } catch (error) {
      console.error('Error checking storage health:', error)
      return {
        healthy: false,
        issues: ['Failed to check storage health'],
        metrics: {
          totalFiles: 0,
          totalSize: 0,
          averageFileSize: 0,
          largestFile: 0,
          oldestFile: new Date()
        }
      }
    }
  }
}

// Export singleton instance
export const storageManagementService = new StorageManagementService()

export default storageManagementService
