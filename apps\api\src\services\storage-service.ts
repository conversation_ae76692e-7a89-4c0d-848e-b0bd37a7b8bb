import path from 'path'
import fs from 'fs/promises'
import { v4 as uuidv4 } from 'uuid'

console.log('🔥 Storage Service loaded - flexible file storage system!')

// Storage configuration interface
export interface StorageConfig {
  provider: 'local' | 's3'
  local?: {
    uploadDir: string
    baseUrl: string
  }
  s3?: {
    bucket: string
    accessKey: string
    secretKey: string
    region: string
    endpoint?: string
    cdnUrl?: string
  }
}

// Upload result interface
export interface UploadResult {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  cdnUrl?: string
  path: string
  metadata: {
    width?: number
    height?: number
    format?: string
  }
}

// Image size configuration
export interface ImageSize {
  name: string
  width: number
  height: number
  quality?: number
  format?: 'webp' | 'jpeg' | 'png'
}

export class StorageService {
  private config: StorageConfig

  constructor(config: StorageConfig) {
    this.config = config
    console.log(`📦 Storage Service initialized with provider: ${config.provider}`)

    if (config.provider === 's3') {
      console.log('⚠️ S3 support will be added in future update. Using local storage for now.')
      // For now, fallback to local storage even if S3 is configured
      this.config.provider = 'local'
    }
  }

  // Get storage configuration from options table
  static async getStorageConfig(payload: any): Promise<StorageConfig> {
    console.log('🔍 Fetching storage configuration from options table...')
    
    try {
      // Get storage provider setting
      const providerOption = await payload.find({
        collection: 'options',
        where: {
          key: { equals: 'storage_provider' }
        },
        limit: 1
      })

      const provider = providerOption.docs[0]?.value || 'local'
      console.log(`📋 Storage provider: ${provider}`)

      if (provider === 'local') {
        return {
          provider: 'local',
          local: {
            uploadDir: path.resolve(process.cwd(), 'media'),
            baseUrl: process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3001'
          }
        }
      }

      if (provider === 's3') {
        // Get S3 configuration from options table
        const s3Options = await payload.find({
          collection: 'options',
          where: {
            key: {
              in: ['s3_bucket', 's3_access_key', 's3_secret_key', 's3_region', 's3_endpoint', 's3_cdn_url']
            }
          }
        })

        const s3Config: any = {}
        s3Options.docs.forEach((option: any) => {
          const key = option.key.replace('s3_', '')
          s3Config[key] = option.value
        })

        console.log('🔧 S3 configuration loaded:', {
          bucket: s3Config.bucket,
          region: s3Config.region,
          hasAccessKey: !!s3Config.access_key,
          hasSecretKey: !!s3Config.secret_key,
          endpoint: s3Config.endpoint,
          cdnUrl: s3Config.cdn_url
        })

        return {
          provider: 's3',
          s3: {
            bucket: s3Config.bucket,
            accessKey: s3Config.access_key,
            secretKey: s3Config.secret_key,
            region: s3Config.region || 'us-east-1',
            endpoint: s3Config.endpoint,
            cdnUrl: s3Config.cdn_url
          }
        }
      }

      throw new Error(`Unsupported storage provider: ${provider}`)
    } catch (error) {
      console.error('❌ Failed to get storage configuration:', error)
      // Fallback to local storage
      console.log('🔄 Falling back to local storage')
      return {
        provider: 'local',
        local: {
          uploadDir: path.resolve(process.cwd(), 'media'),
          baseUrl: process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3001'
        }
      }
    }
  }

  // Upload file with automatic resizing
  async uploadFile(
    file: Buffer,
    originalName: string,
    mimeType: string,
    options: {
      folder?: string
      generateSizes?: ImageSize[]
      mediaType?: string
    } = {}
  ): Promise<UploadResult & { sizes?: Record<string, UploadResult> }> {
    console.log('📤 Starting file upload...', {
      originalName,
      mimeType,
      size: file.length,
      provider: this.config.provider,
      folder: options.folder
    })

    const fileId = uuidv4()
    const ext = path.extname(originalName)
    const baseName = path.basename(originalName, ext)
    const filename = `${baseName}-${Date.now()}-${fileId}${ext}`
    
    // Get basic metadata
    let metadata: any = {}
    if (mimeType.startsWith('image/')) {
      // For now, we'll skip image processing and just store basic info
      metadata = {
        width: null,
        height: null,
        format: mimeType.split('/')[1]
      }
      console.log('🖼️ Basic image metadata:', metadata)
    }

    // Upload original file
    console.log('🔄 Uploading original file with folder:', options.folder)
    console.log('📋 Upload details:', {
      filename,
      mimeType,
      folder: options.folder,
      fileSize: file.length
    })

    const originalResult = await this.uploadSingleFile(file, filename, mimeType, options.folder)
    console.log('✅ Original file upload completed:', originalResult)
    
    const result: UploadResult & { sizes?: Record<string, UploadResult> } = {
      id: fileId,
      filename,
      originalName,
      mimeType,
      size: file.length,
      url: originalResult.url,
      cdnUrl: originalResult.cdnUrl,
      path: originalResult.path,
      metadata
    }

    // For now, skip image resizing - we'll add this back when we install sharp
    if (mimeType.startsWith('image/') && options.generateSizes && options.generateSizes.length > 0) {
      console.log('⚠️ Image resizing temporarily disabled. Will be added when sharp is installed.')
      // TODO: Add image resizing with sharp
    }

    console.log('✅ File upload completed:', {
      id: result.id,
      url: result.url,
      sizes: Object.keys(result.sizes || {})
    })

    return result
  }

  // Upload single file (used internally)
  private async uploadSingleFile(
    file: Buffer,
    filename: string,
    mimeType: string,
    folder?: string
  ): Promise<{ url: string; cdnUrl?: string; path: string }> {
    const filePath = folder ? `${folder}/${filename}` : filename

    console.log('📂 Upload single file details:', {
      filename,
      folder,
      filePath,
      hasFolder: !!folder
    })

    // For now, only support local storage
    if (this.config.provider === 'local') {
      return this.uploadToLocal(file, filePath, mimeType)
    } else {
      console.log('⚠️ S3 support temporarily disabled. Using local storage.')
      return this.uploadToLocal(file, filePath, mimeType)
    }
  }

  // Upload to local storage
  private async uploadToLocal(file: Buffer, filePath: string, mimeType: string) {
    if (!this.config.local) {
      throw new Error('Local storage configuration is missing')
    }

    const fullPath = path.join(this.config.local.uploadDir, filePath)
    const dir = path.dirname(fullPath)

    console.log('📁 Local storage upload details:', {
      uploadDir: this.config.local.uploadDir,
      filePath,
      fullPath,
      dir,
      cwd: process.cwd()
    })

    try {
      // Ensure directory exists
      console.log('📂 Creating directory:', dir)
      await fs.mkdir(dir, { recursive: true })
      console.log('✅ Directory created/verified:', dir)

      // Write file
      console.log('💾 Writing file:', fullPath)
      await fs.writeFile(fullPath, file)
      console.log('✅ File written successfully:', fullPath)

      const url = `/media/${filePath}`

      console.log('💾 File uploaded to local storage:', { path: fullPath, url })

      return {
        url,
        path: fullPath
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      const errorCode = error instanceof Error && 'code' in error ? (error as any).code : 'UNKNOWN'

      console.error('❌ Local storage upload error:', {
        error: errorMessage,
        code: errorCode,
        path: fullPath,
        dir,
        uploadDir: this.config.local.uploadDir
      })
      throw new Error(`Failed to upload file to local storage: ${errorMessage}`)
    }
  }

  // TODO: Add S3 upload support when AWS SDK is installed

  // Delete file
  async deleteFile(filePath: string): Promise<void> {
    console.log('🗑️ Deleting file:', filePath)

    // For now, only support local storage
    await this.deleteFromLocal(filePath)
  }

  private async deleteFromLocal(filePath: string) {
    if (!this.config.local) return

    const fullPath = path.join(this.config.local.uploadDir, filePath)

    try {
      await fs.unlink(fullPath)
      console.log('✅ File deleted from local storage:', fullPath)
    } catch (error) {
      console.warn('⚠️ Failed to delete local file:', error)
    }
  }
}
