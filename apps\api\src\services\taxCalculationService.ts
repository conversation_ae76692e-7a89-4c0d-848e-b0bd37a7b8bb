import { Payload } from 'payload'

export interface TaxCalculationInput {
  amount: number
  transactionType: string
  customerType: string
  customerLocation: {
    countryId: string
    stateId: string
    districtId?: string
  }
  instituteLocation: {
    countryId: string
    stateId: string
    districtId?: string
  }
  branchId?: string
  transactionDate?: Date
}

export interface TaxComponent {
  id: string
  name: string
  code: string
  type: string
  rate: number
  amount: number
}

export interface TaxCalculationResult {
  subtotal: number
  totalTax: number
  grandTotal: number
  taxComponents: TaxComponent[]
  appliedRules: string[]
  exemptions: Array<{
    condition: string
    exemptionPercentage: number
    exemptedAmount: number
  }>
  scenario: string
  breakdown: {
    sgst?: number
    cgst?: number
    igst?: number
    vat?: number
    other?: number
  }
}

export class TaxCalculationService {
  private payload: Payload

  constructor(payload: Payload) {
    this.payload = payload
  }

  async calculateTax(input: TaxCalculationInput): Promise<TaxCalculationResult> {
    try {
      // Determine tax scenario
      const scenario = this.determineTaxScenario(input)
      
      // Find applicable tax rules
      const applicableRules = await this.findApplicableTaxRules(input, scenario)
      
      // Calculate base tax
      let result: TaxCalculationResult = {
        subtotal: input.amount,
        totalTax: 0,
        grandTotal: input.amount,
        taxComponents: [],
        appliedRules: [],
        exemptions: [],
        scenario,
        breakdown: {}
      }

      // Apply each rule
      for (const rule of applicableRules) {
        const ruleResult = await this.applyTaxRule(rule, input, result)
        result = this.mergeTaxResults(result, ruleResult)
      }

      // Apply exemptions
      result = await this.applyExemptions(result, input, applicableRules)

      // Calculate final totals
      result.totalTax = result.taxComponents.reduce((sum, component) => sum + component.amount, 0)
      result.grandTotal = result.subtotal + result.totalTax

      // Create breakdown
      result.breakdown = this.createTaxBreakdown(result.taxComponents)

      return result

    } catch (error) {
      console.error('Tax calculation error:', error)
      throw new Error('Failed to calculate tax')
    }
  }

  private determineTaxScenario(input: TaxCalculationInput): string {
    const { customerLocation, instituteLocation } = input

    if (customerLocation.countryId !== instituteLocation.countryId) {
      return 'international'
    }

    if (customerLocation.stateId !== instituteLocation.stateId) {
      return 'inter_state'
    }

    return 'intra_state'
  }

  private async findApplicableTaxRules(input: TaxCalculationInput, scenario: string): Promise<any[]> {
    const currentDate = input.transactionDate || new Date()

    const rules = await this.payload.find({
      collection: 'tax-rules',
      where: {
        and: [
          { isActive: { equals: true } },
          { effectiveFrom: { less_than_equal: currentDate } },
          {
            or: [
              { effectiveTo: { greater_than_equal: currentDate } },
              { effectiveTo: { exists: false } }
            ]
          },
          // Transaction type condition
          {
            or: [
              { 'conditions.transactionType': { equals: input.transactionType } },
              { 'conditions.transactionType': { exists: false } }
            ]
          },
          // Customer type condition
          {
            or: [
              { 'conditions.customerType': { equals: input.customerType } },
              { 'conditions.customerType': { exists: false } }
            ]
          },
          // Amount range condition
          {
            or: [
              { 'conditions.amountRange.min': { less_than_equal: input.amount } },
              { 'conditions.amountRange.min': { exists: false } }
            ]
          },
          {
            or: [
              { 'conditions.amountRange.max': { greater_than_equal: input.amount } },
              { 'conditions.amountRange.max': { exists: false } }
            ]
          },
          // Location conditions based on scenario
          this.buildLocationConditions(input, scenario)
        ]
      },
      sort: '-priority',
      populate: ['taxGroup']
    })

    return rules.docs
  }

  private buildLocationConditions(input: TaxCalculationInput, scenario: string): any {
    const conditions: any[] = []

    // Customer location conditions
    conditions.push({
      or: [
        { 'conditions.locationConditions.customerCountry': { equals: input.customerLocation.countryId } },
        { 'conditions.locationConditions.customerCountry': { exists: false } }
      ]
    })

    conditions.push({
      or: [
        { 'conditions.locationConditions.customerState': { equals: input.customerLocation.stateId } },
        { 'conditions.locationConditions.customerState': { exists: false } }
      ]
    })

    // Institute location conditions
    conditions.push({
      or: [
        { 'conditions.locationConditions.instituteCountry': { equals: input.instituteLocation.countryId } },
        { 'conditions.locationConditions.instituteCountry': { exists: false } }
      ]
    })

    conditions.push({
      or: [
        { 'conditions.locationConditions.instituteState': { equals: input.instituteLocation.stateId } },
        { 'conditions.locationConditions.instituteState': { exists: false } }
      ]
    })

    // Scenario condition
    conditions.push({
      or: [
        { 'conditions.locationConditions.scenario': { equals: scenario } },
        { 'conditions.locationConditions.scenario': { exists: false } }
      ]
    })

    return { and: conditions }
  }

  private async applyTaxRule(rule: any, input: TaxCalculationInput, currentResult: TaxCalculationResult): Promise<TaxCalculationResult> {
    const taxGroup = rule.taxGroup

    if (!taxGroup || !taxGroup.taxComponents) {
      return currentResult
    }

    const result = { ...currentResult }
    result.appliedRules.push(rule.name)

    // Get tax components from the group
    for (const componentData of taxGroup.taxComponents) {
      const component = componentData.component
      const customRate = componentData.customRate

      if (!component || componentData.isOptional) continue

      const taxRate = customRate || component.rate
      const taxAmount = this.calculateComponentTax(input.amount, taxRate, component.calculationMethod, component.tieredRates)

      const taxComponent: TaxComponent = {
        id: component.id,
        name: component.name,
        code: component.code,
        type: component.type,
        rate: taxRate,
        amount: taxAmount
      }

      result.taxComponents.push(taxComponent)
    }

    return result
  }

  private calculateComponentTax(amount: number, rate: number, method: string = 'percentage', tieredRates?: any[]): number {
    switch (method) {
      case 'fixed':
        return rate
      
      case 'tiered':
        if (!tieredRates || tieredRates.length === 0) return 0
        
        let tax = 0
        for (const tier of tieredRates) {
          const applicableAmount = Math.min(
            amount - tier.minAmount,
            tier.maxAmount ? tier.maxAmount - tier.minAmount : amount - tier.minAmount
          )
          
          if (applicableAmount > 0) {
            tax += (applicableAmount * tier.rate) / 100
          }
        }
        return tax
      
      case 'percentage':
      default:
        return (amount * rate) / 100
    }
  }

  private mergeTaxResults(result1: TaxCalculationResult, result2: TaxCalculationResult): TaxCalculationResult {
    return {
      ...result1,
      taxComponents: [...result1.taxComponents, ...result2.taxComponents],
      appliedRules: [...result1.appliedRules, ...result2.appliedRules]
    }
  }

  private async applyExemptions(result: TaxCalculationResult, input: TaxCalculationInput, rules: any[]): Promise<TaxCalculationResult> {
    const exemptions: any[] = []

    // Collect all exemptions from applicable rules
    for (const rule of rules) {
      if (rule.exemptions && rule.exemptions.length > 0) {
        exemptions.push(...rule.exemptions)
      }
    }

    // Apply exemptions
    for (const exemption of exemptions) {
      if (this.isExemptionApplicable(exemption, input)) {
        const exemptionAmount = this.calculateExemption(result.totalTax, exemption.exemptionPercentage)
        
        result.exemptions.push({
          condition: exemption.condition,
          exemptionPercentage: exemption.exemptionPercentage,
          exemptedAmount: exemptionAmount
        })

        // Reduce tax components proportionally
        const exemptionRatio = exemptionAmount / result.totalTax
        result.taxComponents = result.taxComponents.map(component => ({
          ...component,
          amount: component.amount * (1 - exemptionRatio)
        }))
      }
    }

    return result
  }

  private isExemptionApplicable(exemption: any, input: TaxCalculationInput): boolean {
    switch (exemption.condition) {
      case 'threshold_exemption':
        return input.amount < (exemption.thresholdAmount || 0)
      
      case 'student_discount':
        return input.customerType === 'individual' // Simplified logic
      
      case 'educational_exemption':
        return input.customerType === 'educational'
      
      case 'government_exemption':
        return input.customerType === 'government'
      
      case 'export_exemption':
        return input.customerLocation.countryId !== input.instituteLocation.countryId
      
      default:
        return false
    }
  }

  private calculateExemption(taxAmount: number, exemptionPercentage: number): number {
    return (taxAmount * exemptionPercentage) / 100
  }

  private createTaxBreakdown(taxComponents: TaxComponent[]): any {
    const breakdown: any = {}

    for (const component of taxComponents) {
      switch (component.type) {
        case 'sgst':
          breakdown.sgst = (breakdown.sgst || 0) + component.amount
          break
        case 'cgst':
          breakdown.cgst = (breakdown.cgst || 0) + component.amount
          break
        case 'igst':
          breakdown.igst = (breakdown.igst || 0) + component.amount
          break
        case 'vat':
          breakdown.vat = (breakdown.vat || 0) + component.amount
          break
        default:
          breakdown.other = (breakdown.other || 0) + component.amount
      }
    }

    return breakdown
  }

  async previewTaxCalculation(input: TaxCalculationInput): Promise<{
    scenarios: Array<{
      scenario: string
      description: string
      calculation: TaxCalculationResult
    }>
  }> {
    const scenarios = [
      { scenario: 'intra_state', description: 'Same State Transaction (SGST + CGST)' },
      { scenario: 'inter_state', description: 'Different State Transaction (IGST)' },
      { scenario: 'international', description: 'International Transaction' }
    ]

    const results = []

    for (const scenario of scenarios) {
      try {
        const calculation = await this.calculateTax(input)

        results.push({
          scenario: scenario.scenario,
          description: scenario.description,
          calculation
        })
      } catch (error) {
        console.error(`Error calculating tax for scenario ${scenario.scenario}:`, error)
        results.push({
          scenario: scenario.scenario,
          description: scenario.description,
          calculation: {
            subtotal: input.amount,
            totalTax: 0,
            grandTotal: input.amount,
            taxComponents: [],
            appliedRules: [],
            exemptions: [],
            scenario: scenario.scenario,
            breakdown: {}
          }
        })
      }
    }

    return { scenarios: results }
  }
}
