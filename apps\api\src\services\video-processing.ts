import ffmpeg from 'fluent-ffmpeg'
import { v4 as uuidv4 } from 'uuid'
import { promises as fs } from 'fs'
import path from 'path'
import { fileUploadService } from './file-upload'
import { getTenantStoragePath } from '../middleware/tenant-context'
import type { AuthenticatedUser } from '../middleware/auth'

/**
 * Advanced Video Processing Pipeline for Course Builder System
 * Handles video transcoding, thumbnail generation, streaming optimization, and quality analysis
 */

export interface VideoProcessingOptions {
  generateThumbnails?: boolean
  transcodeToMultipleFormats?: boolean
  optimizeForStreaming?: boolean
  extractMetadata?: boolean
  generatePreview?: boolean
  extractSubtitles?: boolean
  addWatermark?: boolean
  quality?: 'low' | 'medium' | 'high' | 'ultra'
  targetFormats?: VideoFormat[]
  thumbnailCount?: number
  previewDuration?: number
  watermarkText?: string
  watermarkPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
}

export interface VideoFormat {
  name: string
  resolution: string
  bitrate: string
  codec: string
  container: string
}

export interface VideoMetadata {
  duration: number
  width: number
  height: number
  bitrate: number
  fps: number
  codec: string
  format: string
  size: number
  aspectRatio: string
}

export interface ProcessedVideo {
  id: string
  originalFile: string
  metadata: VideoMetadata
  thumbnails: string[]
  transcodedVersions: {
    format: VideoFormat
    filePath: string
    size: number
    url: string
  }[]
  streamingManifest?: string
  dashManifest?: string
  previewClip?: string
  subtitles?: string[]
  watermarkedVideo?: string
  processingTime: number
  status: 'completed' | 'failed' | 'processing'
  error?: string
}

export interface ProcessingJob {
  id: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  progress: number
  startTime: Date
  endTime?: Date
  error?: string
  result?: ProcessedVideo
}

class VideoProcessingService {
  private processingJobs = new Map<string, ProcessingJob>()
  private readonly tempDir = path.join(process.cwd(), 'temp', 'video-processing')
  
  // Predefined video formats for different quality levels
  private readonly videoFormats: Record<string, VideoFormat[]> = {
    low: [
      { name: '480p', resolution: '854x480', bitrate: '1000k', codec: 'libx264', container: 'mp4' }
    ],
    medium: [
      { name: '480p', resolution: '854x480', bitrate: '1000k', codec: 'libx264', container: 'mp4' },
      { name: '720p', resolution: '1280x720', bitrate: '2500k', codec: 'libx264', container: 'mp4' }
    ],
    high: [
      { name: '480p', resolution: '854x480', bitrate: '1000k', codec: 'libx264', container: 'mp4' },
      { name: '720p', resolution: '1280x720', bitrate: '2500k', codec: 'libx264', container: 'mp4' },
      { name: '1080p', resolution: '1920x1080', bitrate: '5000k', codec: 'libx264', container: 'mp4' }
    ],
    ultra: [
      { name: '480p', resolution: '854x480', bitrate: '1000k', codec: 'libx264', container: 'mp4' },
      { name: '720p', resolution: '1280x720', bitrate: '2500k', codec: 'libx264', container: 'mp4' },
      { name: '1080p', resolution: '1920x1080', bitrate: '5000k', codec: 'libx264', container: 'mp4' },
      { name: '1440p', resolution: '2560x1440', bitrate: '8000k', codec: 'libx264', container: 'mp4' }
    ]
  }

  constructor() {
    this.ensureTempDirectory()
  }

  /**
   * Process video file with comprehensive pipeline
   */
  async processVideo(
    user: AuthenticatedUser,
    videoFile: Buffer,
    filename: string,
    options: VideoProcessingOptions = {}
  ): Promise<{ success: boolean; jobId: string; error?: string }> {
    const jobId = uuidv4()
    const job: ProcessingJob = {
      id: jobId,
      status: 'queued',
      progress: 0,
      startTime: new Date()
    }

    this.processingJobs.set(jobId, job)

    try {
      // Start processing in background
      this.processVideoAsync(user, videoFile, filename, options, jobId)
      
      return {
        success: true,
        jobId
      }
    } catch (error) {
      console.error('Error starting video processing:', error)
      job.status = 'failed'
      job.error = 'Failed to start video processing'
      
      return {
        success: false,
        jobId,
        error: 'Failed to start video processing'
      }
    }
  }

  /**
   * Async video processing pipeline
   */
  private async processVideoAsync(
    user: AuthenticatedUser,
    videoFile: Buffer,
    filename: string,
    options: VideoProcessingOptions,
    jobId: string
  ): Promise<void> {
    const job = this.processingJobs.get(jobId)!
    
    try {
      job.status = 'processing'
      job.progress = 10

      // Save temp file
      const tempFilePath = path.join(this.tempDir, `${jobId}_${filename}`)
      await fs.writeFile(tempFilePath, videoFile)
      
      job.progress = 20

      // Extract metadata
      const metadata = await this.extractVideoMetadata(tempFilePath)
      job.progress = 30

      // Generate thumbnails
      let thumbnails: string[] = []
      if (options.generateThumbnails !== false) {
        thumbnails = await this.generateThumbnails(
          user, tempFilePath, metadata, options.thumbnailCount || 5
        )
      }
      job.progress = 50

      // Transcode to multiple formats
      let transcodedVersions: ProcessedVideo['transcodedVersions'] = []
      if (options.transcodeToMultipleFormats !== false) {
        const formats = options.targetFormats || this.videoFormats[options.quality || 'medium']
        transcodedVersions = await this.transcodeToFormats(user, tempFilePath, formats)
      }
      job.progress = 80

      // Generate preview clip
      let previewClip: string | undefined
      if (options.generatePreview) {
        previewClip = await this.generatePreviewClip(
          user, tempFilePath, options.previewDuration || 30
        )
      }
      job.progress = 70

      // Extract subtitles if requested
      let subtitles: string[] = []
      if (options.extractSubtitles) {
        subtitles = await this.extractSubtitles(user, tempFilePath)
      }
      job.progress = 80

      // Add watermark if requested
      let watermarkedVideo: string | undefined
      if (options.addWatermark && options.watermarkText) {
        watermarkedVideo = await this.addWatermarkToVideo(
          user,
          tempFilePath,
          options.watermarkText,
          options.watermarkPosition
        )
      }
      job.progress = 90

      // Generate streaming manifest if needed
      let streamingManifest: string | undefined
      let dashManifest: string | undefined
      if (options.optimizeForStreaming && transcodedVersions.length > 1) {
        streamingManifest = await this.generateStreamingManifest(user, transcodedVersions)
        dashManifest = await this.generateDASHManifest(user, transcodedVersions, metadata)
      }

      const result: ProcessedVideo = {
        id: jobId,
        originalFile: filename,
        metadata,
        thumbnails,
        transcodedVersions,
        streamingManifest,
        dashManifest,
        previewClip,
        subtitles,
        watermarkedVideo,
        processingTime: Date.now() - job.startTime.getTime(),
        status: 'completed'
      }

      job.status = 'completed'
      job.progress = 100
      job.endTime = new Date()
      job.result = result

      // Cleanup temp files
      await this.cleanupTempFiles([tempFilePath])

    } catch (error) {
      console.error('Video processing failed:', error)
      job.status = 'failed'
      job.error = error instanceof Error ? error.message : 'Unknown error'
      job.endTime = new Date()
    }
  }

  /**
   * Get processing job status
   */
  getJobStatus(jobId: string): ProcessingJob | null {
    return this.processingJobs.get(jobId) || null
  }

  /**
   * Get processing job result
   */
  getJobResult(jobId: string): ProcessedVideo | null {
    const job = this.processingJobs.get(jobId)
    return job?.result || null
  }

  /**
   * Extract video metadata using ffprobe
   */
  private async extractVideoMetadata(filePath: string): Promise<VideoMetadata> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err)
          return
        }

        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video')
        if (!videoStream) {
          reject(new Error('No video stream found'))
          return
        }

        resolve({
          duration: metadata.format.duration || 0,
          width: videoStream.width || 0,
          height: videoStream.height || 0,
          bitrate: parseInt(metadata.format.bit_rate || '0'),
          fps: eval(videoStream.r_frame_rate || '0') || 0,
          codec: videoStream.codec_name || '',
          format: metadata.format.format_name || '',
          size: parseInt(metadata.format.size || '0'),
          aspectRatio: videoStream.display_aspect_ratio || `${videoStream.width}:${videoStream.height}`
        })
      })
    })
  }

  /**
   * Generate video thumbnails at different timestamps
   */
  private async generateThumbnails(
    user: AuthenticatedUser,
    filePath: string,
    metadata: VideoMetadata,
    count: number
  ): Promise<string[]> {
    const thumbnails: string[] = []
    const interval = metadata.duration / (count + 1)

    for (let i = 1; i <= count; i++) {
      const timestamp = interval * i
      const thumbnailPath = await this.generateThumbnailAtTime(user, filePath, timestamp, i)
      if (thumbnailPath) {
        thumbnails.push(thumbnailPath)
      }
    }

    return thumbnails
  }

  /**
   * Generate single thumbnail at specific timestamp
   */
  private async generateThumbnailAtTime(
    user: AuthenticatedUser,
    filePath: string,
    timestamp: number,
    index: number
  ): Promise<string | null> {
    return new Promise((resolve) => {
      const outputPath = path.join(this.tempDir, `thumbnail_${index}_${Date.now()}.jpg`)

      ffmpeg(filePath)
        .seekInput(timestamp)
        .frames(1)
        .output(outputPath)
        .on('end', async () => {
          try {
            // Upload thumbnail to storage
            const thumbnailBuffer = await fs.readFile(outputPath)
            const uploadResult = await fileUploadService.uploadFile(
              user,
              thumbnailBuffer,
              `thumbnail_${index}.jpg`,
              'image/jpeg'
            )

            // Cleanup temp file
            await fs.unlink(outputPath)

            resolve(uploadResult.success ? uploadResult.file?.url || null : null)
          } catch (error) {
            console.error('Error uploading thumbnail:', error)
            resolve(null)
          }
        })
        .on('error', (err) => {
          console.error('Error generating thumbnail:', err)
          resolve(null)
        })
        .run()
    })
  }

  /**
   * Transcode video to multiple formats
   */
  private async transcodeToFormats(
    user: AuthenticatedUser,
    filePath: string,
    formats: VideoFormat[]
  ): Promise<ProcessedVideo['transcodedVersions']> {
    const transcodedVersions: ProcessedVideo['transcodedVersions'] = []

    for (const format of formats) {
      try {
        const result = await this.transcodeToFormat(user, filePath, format)
        if (result) {
          transcodedVersions.push(result)
        }
      } catch (error) {
        console.error(`Error transcoding to ${format.name}:`, error)
      }
    }

    return transcodedVersions
  }

  /**
   * Transcode video to specific format
   */
  private async transcodeToFormat(
    user: AuthenticatedUser,
    filePath: string,
    format: VideoFormat
  ): Promise<ProcessedVideo['transcodedVersions'][0] | null> {
    return new Promise((resolve) => {
      const outputPath = path.join(this.tempDir, `transcoded_${format.name}_${Date.now()}.${format.container}`)

      ffmpeg(filePath)
        .videoCodec(format.codec)
        .videoBitrate(format.bitrate)
        .size(format.resolution)
        .format(format.container)
        .output(outputPath)
        .on('end', async () => {
          try {
            // Upload transcoded video
            const videoBuffer = await fs.readFile(outputPath)
            const uploadResult = await fileUploadService.uploadFile(
              user,
              videoBuffer,
              `video_${format.name}.${format.container}`,
              `video/${format.container}`
            )

            // Get file size
            const stats = await fs.stat(outputPath)

            // Cleanup temp file
            await fs.unlink(outputPath)

            if (uploadResult.success && uploadResult.file) {
              resolve({
                format,
                filePath: uploadResult.file.path,
                size: stats.size,
                url: uploadResult.file.url
              })
            } else {
              resolve(null)
            }
          } catch (error) {
            console.error('Error uploading transcoded video:', error)
            resolve(null)
          }
        })
        .on('error', (err) => {
          console.error('Error transcoding video:', err)
          resolve(null)
        })
        .run()
    })
  }

  /**
   * Generate preview clip
   */
  private async generatePreviewClip(
    user: AuthenticatedUser,
    filePath: string,
    duration: number
  ): Promise<string | undefined> {
    return new Promise((resolve) => {
      const outputPath = path.join(this.tempDir, `preview_${Date.now()}.mp4`)

      ffmpeg(filePath)
        .seekInput(10) // Start 10 seconds in
        .duration(duration)
        .videoCodec('libx264')
        .videoBitrate('1000k')
        .size('854x480')
        .output(outputPath)
        .on('end', async () => {
          try {
            const previewBuffer = await fs.readFile(outputPath)
            const uploadResult = await fileUploadService.uploadFile(
              user,
              previewBuffer,
              'preview.mp4',
              'video/mp4'
            )

            await fs.unlink(outputPath)
            resolve(uploadResult.success ? uploadResult.file?.url : undefined)
          } catch (error) {
            console.error('Error uploading preview:', error)
            resolve(undefined)
          }
        })
        .on('error', (err) => {
          console.error('Error generating preview:', err)
          resolve(undefined)
        })
        .run()
    })
  }

  /**
   * Generate HLS streaming manifest for adaptive streaming
   */
  private async generateStreamingManifest(
    user: AuthenticatedUser,
    transcodedVersions: ProcessedVideo['transcodedVersions']
  ): Promise<string | undefined> {
    try {
      // Generate M3U8 master playlist for adaptive streaming
      const masterPlaylist = [
        '#EXTM3U',
        '#EXT-X-VERSION:6',
        '#EXT-X-INDEPENDENT-SEGMENTS',
        ''
      ]

      // Sort versions by quality (lowest to highest)
      const sortedVersions = transcodedVersions.sort((a, b) => {
        const aBandwidth = parseInt(a.format.bitrate.replace('k', '000'))
        const bBandwidth = parseInt(b.format.bitrate.replace('k', '000'))
        return aBandwidth - bBandwidth
      })

      for (const version of sortedVersions) {
        const bandwidth = parseInt(version.format.bitrate.replace('k', '000'))
        const resolution = version.format.resolution
        const [width, height] = resolution.split('x').map(Number)

        // Generate individual playlist for each quality level
        const segmentPlaylist = await this.generateHLSSegments(user, version)

        if (segmentPlaylist) {
          masterPlaylist.push(
            `#EXT-X-STREAM-INF:BANDWIDTH=${bandwidth},RESOLUTION=${width}x${height},CODECS="avc1.42e00a,mp4a.40.2"`
          )
          masterPlaylist.push(segmentPlaylist)
          masterPlaylist.push('')
        }
      }

      // Upload master playlist
      const playlistContent = masterPlaylist.join('\n')
      const playlistBuffer = Buffer.from(playlistContent, 'utf8')

      const uploadResult = await fileUploadService.uploadFile(
        user,
        playlistBuffer,
        'master.m3u8',
        'application/vnd.apple.mpegurl'
      )

      return uploadResult.success ? uploadResult.file?.url : undefined
    } catch (error) {
      console.error('Error generating streaming manifest:', error)
      return undefined
    }
  }

  /**
   * Generate HLS segments for a specific quality level
   */
  private async generateHLSSegments(
    user: AuthenticatedUser,
    version: ProcessedVideo['transcodedVersions'][0]
  ): Promise<string | undefined> {
    return new Promise((resolve) => {
      const segmentDir = path.join(this.tempDir, `segments_${Date.now()}`)
      const playlistPath = path.join(segmentDir, 'playlist.m3u8')

      // Create segments directory
      require('fs').mkdirSync(segmentDir, { recursive: true })

      // Generate HLS segments using ffmpeg
      ffmpeg(version.filePath)
        .outputOptions([
          '-hls_time 10',           // 10 second segments
          '-hls_list_size 0',       // Keep all segments in playlist
          '-hls_segment_filename', path.join(segmentDir, 'segment_%03d.ts'),
          '-f hls'
        ])
        .output(playlistPath)
        .on('end', async () => {
          try {
            // Upload segments and playlist
            const playlistContent = await fs.readFile(playlistPath, 'utf8')
            const segmentFiles = await fs.readdir(segmentDir)

            // Upload all segment files
            const segmentUrls: string[] = []
            for (const segmentFile of segmentFiles) {
              if (segmentFile.endsWith('.ts')) {
                const segmentPath = path.join(segmentDir, segmentFile)
                const segmentBuffer = await fs.readFile(segmentPath)

                const uploadResult = await fileUploadService.uploadFile(
                  user,
                  segmentBuffer,
                  segmentFile,
                  'video/mp2t'
                )

                if (uploadResult.success && uploadResult.file) {
                  segmentUrls.push(uploadResult.file.url)
                }
              }
            }

            // Update playlist with uploaded segment URLs
            let updatedPlaylist = playlistContent
            segmentFiles.forEach((file, index) => {
              if (file.endsWith('.ts') && segmentUrls[index]) {
                updatedPlaylist = updatedPlaylist.replace(file, segmentUrls[index])
              }
            })

            // Upload updated playlist
            const playlistBuffer = Buffer.from(updatedPlaylist, 'utf8')
            const playlistUpload = await fileUploadService.uploadFile(
              user,
              playlistBuffer,
              `playlist_${version.format.name}.m3u8`,
              'application/vnd.apple.mpegurl'
            )

            // Cleanup temp files
            await this.cleanupTempFiles([segmentDir])

            resolve(playlistUpload.success ? playlistUpload.file?.url : undefined)
          } catch (error) {
            console.error('Error uploading HLS segments:', error)
            resolve(undefined)
          }
        })
        .on('error', (err) => {
          console.error('Error generating HLS segments:', err)
          resolve(undefined)
        })
        .run()
    })
  }

  /**
   * Generate DASH manifest for adaptive streaming
   */
  private async generateDASHManifest(
    user: AuthenticatedUser,
    transcodedVersions: ProcessedVideo['transcodedVersions'],
    metadata: VideoMetadata
  ): Promise<string | undefined> {
    try {
      // Generate DASH MPD (Media Presentation Description)
      const mpd = `<?xml version="1.0" encoding="UTF-8"?>
<MPD xmlns="urn:mpeg:dash:schema:mpd:2011"
     profiles="urn:mpeg:dash:profile:isoff-live:2011"
     type="static"
     mediaPresentationDuration="PT${Math.floor(metadata.duration)}S"
     minBufferTime="PT2S">
  <Period>
    <AdaptationSet mimeType="video/mp4" codecs="avc1.42e00a">
${transcodedVersions.map(version => {
  const bandwidth = parseInt(version.format.bitrate.replace('k', '000'))
  const [width, height] = version.format.resolution.split('x').map(Number)

  return `      <Representation id="${version.format.name}"
                      bandwidth="${bandwidth}"
                      width="${width}"
                      height="${height}">
        <BaseURL>${version.url}</BaseURL>
      </Representation>`
}).join('\n')}
    </AdaptationSet>
  </Period>
</MPD>`

      // Upload DASH manifest
      const mpdBuffer = Buffer.from(mpd, 'utf8')
      const uploadResult = await fileUploadService.uploadFile(
        user,
        mpdBuffer,
        'manifest.mpd',
        'application/dash+xml'
      )

      return uploadResult.success ? uploadResult.file?.url : undefined
    } catch (error) {
      console.error('Error generating DASH manifest:', error)
      return undefined
    }
  }

  /**
   * Extract subtitles from video
   */
  private async extractSubtitles(
    user: AuthenticatedUser,
    filePath: string
  ): Promise<string[]> {
    return new Promise((resolve) => {
      const subtitlePaths: string[] = []

      // Extract subtitle streams using ffmpeg
      ffmpeg.ffprobe(filePath, async (err, metadata) => {
        if (err) {
          console.error('Error probing for subtitles:', err)
          resolve([])
          return
        }

        const subtitleStreams = metadata.streams.filter(stream =>
          stream.codec_type === 'subtitle'
        )

        if (subtitleStreams.length === 0) {
          resolve([])
          return
        }

        // Extract each subtitle stream
        for (let i = 0; i < subtitleStreams.length; i++) {
          const outputPath = path.join(this.tempDir, `subtitle_${i}.srt`)

          try {
            await new Promise<void>((resolveExtract, rejectExtract) => {
              ffmpeg(filePath)
                .outputOptions([`-map 0:s:${i}`, '-c:s srt'])
                .output(outputPath)
                .on('end', async () => {
                  try {
                    const subtitleBuffer = await fs.readFile(outputPath)
                    const uploadResult = await fileUploadService.uploadFile(
                      user,
                      subtitleBuffer,
                      `subtitle_${i}.srt`,
                      'text/plain'
                    )

                    if (uploadResult.success && uploadResult.file) {
                      subtitlePaths.push(uploadResult.file.url)
                    }

                    await fs.unlink(outputPath)
                    resolveExtract()
                  } catch (error) {
                    console.error('Error uploading subtitle:', error)
                    resolveExtract()
                  }
                })
                .on('error', (extractErr) => {
                  console.error('Error extracting subtitle:', extractErr)
                  resolveExtract()
                })
                .run()
            })
          } catch (error) {
            console.error(`Error processing subtitle stream ${i}:`, error)
          }
        }

        resolve(subtitlePaths)
      })
    })
  }

  /**
   * Add watermark to video
   */
  private async addWatermarkToVideo(
    user: AuthenticatedUser,
    filePath: string,
    watermarkText: string,
    position: string = 'bottom-right'
  ): Promise<string | null> {
    return new Promise((resolve) => {
      const outputPath = path.join(this.tempDir, `watermarked_${Date.now()}.mp4`)

      // Define watermark position
      const positions = {
        'top-left': 'x=10:y=10',
        'top-right': 'x=w-tw-10:y=10',
        'bottom-left': 'x=10:y=h-th-10',
        'bottom-right': 'x=w-tw-10:y=h-th-10',
        'center': 'x=(w-tw)/2:y=(h-th)/2'
      }

      const positionFilter = positions[position as keyof typeof positions] || positions['bottom-right']

      ffmpeg(filePath)
        .videoFilters([
          {
            filter: 'drawtext',
            options: {
              text: watermarkText,
              fontsize: 24,
              fontcolor: 'white',
              x: positionFilter.split(':')[0].split('=')[1],
              y: positionFilter.split(':')[1].split('=')[1],
              shadowcolor: 'black',
              shadowx: 2,
              shadowy: 2
            }
          }
        ])
        .output(outputPath)
        .on('end', async () => {
          try {
            const watermarkedBuffer = await fs.readFile(outputPath)
            const uploadResult = await fileUploadService.uploadFile(
              user,
              watermarkedBuffer,
              'watermarked_video.mp4',
              'video/mp4'
            )

            await fs.unlink(outputPath)
            resolve(uploadResult.success ? uploadResult.file?.url || null : null)
          } catch (error) {
            console.error('Error uploading watermarked video:', error)
            resolve(null)
          }
        })
        .on('error', (err) => {
          console.error('Error adding watermark:', err)
          resolve(null)
        })
        .run()
    })
  }

  /**
   * Cleanup temporary files
   */
  private async cleanupTempFiles(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        await fs.unlink(filePath)
      } catch (error) {
        console.error('Error cleaning up temp file:', filePath, error)
      }
    }
  }

  /**
   * Ensure temp directory exists
   */
  private async ensureTempDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.tempDir, { recursive: true })
    } catch (error) {
      console.error('Failed to create temp directory:', error)
    }
  }
}

  /**
   * Get all processing jobs for a user
   */
  getAllJobsForUser(userId: string): ProcessingJob[] {
    return Array.from(this.processingJobs.values()).filter(job =>
      job.result?.id.includes(userId) // Simple filtering, could be enhanced
    )
  }

  /**
   * Cancel a processing job
   */
  cancelJob(jobId: string): boolean {
    const job = this.processingJobs.get(jobId)
    if (job && job.status === 'processing') {
      job.status = 'failed'
      job.error = 'Job cancelled by user'
      job.endTime = new Date()
      return true
    }
    return false
  }

  /**
   * Get processing statistics
   */
  getProcessingStats(): {
    totalJobs: number
    completedJobs: number
    failedJobs: number
    processingJobs: number
    queuedJobs: number
  } {
    const jobs = Array.from(this.processingJobs.values())
    return {
      totalJobs: jobs.length,
      completedJobs: jobs.filter(j => j.status === 'completed').length,
      failedJobs: jobs.filter(j => j.status === 'failed').length,
      processingJobs: jobs.filter(j => j.status === 'processing').length,
      queuedJobs: jobs.filter(j => j.status === 'queued').length
    }
  }

  /**
   * Validate video file before processing
   */
  async validateVideoFile(filePath: string): Promise<{ valid: boolean; error?: string }> {
    try {
      const metadata = await this.extractVideoMetadata(filePath)

      // Check duration (max 4 hours)
      if (metadata.duration > 14400) {
        return { valid: false, error: 'Video duration exceeds maximum allowed (4 hours)' }
      }

      // Check resolution (max 4K)
      if (metadata.width > 3840 || metadata.height > 2160) {
        return { valid: false, error: 'Video resolution exceeds maximum allowed (4K)' }
      }

      // Check if video has valid streams
      if (!metadata.codec || metadata.duration === 0) {
        return { valid: false, error: 'Invalid video file or corrupted' }
      }

      return { valid: true }
    } catch (error) {
      return { valid: false, error: 'Failed to validate video file' }
    }
  }

  /**
   * Generate video quality analysis
   */
  async analyzeVideoQuality(filePath: string): Promise<{
    quality: 'poor' | 'fair' | 'good' | 'excellent'
    issues: string[]
    recommendations: string[]
  }> {
    try {
      const metadata = await this.extractVideoMetadata(filePath)
      const issues: string[] = []
      const recommendations: string[] = []

      // Analyze bitrate
      if (metadata.bitrate < 1000000) { // Less than 1 Mbps
        issues.push('Low bitrate detected')
        recommendations.push('Consider increasing bitrate for better quality')
      }

      // Analyze resolution
      if (metadata.width < 1280 || metadata.height < 720) {
        issues.push('Low resolution detected')
        recommendations.push('Consider using higher resolution (720p minimum)')
      }

      // Analyze frame rate
      if (metadata.fps < 24) {
        issues.push('Low frame rate detected')
        recommendations.push('Consider using at least 24 fps for smooth playback')
      }

      // Determine overall quality
      let quality: 'poor' | 'fair' | 'good' | 'excellent' = 'excellent'
      if (issues.length >= 3) quality = 'poor'
      else if (issues.length === 2) quality = 'fair'
      else if (issues.length === 1) quality = 'good'

      return { quality, issues, recommendations }
    } catch (error) {
      return {
        quality: 'poor',
        issues: ['Failed to analyze video quality'],
        recommendations: ['Please check if the video file is valid']
      }
    }
  }
}

export const videoProcessingService = new VideoProcessingService()
