import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import { mockUsers } from '../../frontend/src/test/permission-test-utils'
import { 
  extractTenantContext,
  createTenantFilter,
  autoPopulateTenantFields,
  validateTenantUpdate,
  validateFileAccess,
  getTenantStoragePath
} from '../middleware/tenant-context'

/**
 * Comprehensive Tenant Isolation Tests for Course Builder System
 * Ensures complete data separation between institutes and branches
 */

describe('Tenant Isolation System', () => {
  describe('Tenant Context Extraction', () => {
    it('should extract correct context for super admin', () => {
      const context = extractTenantContext(mockUsers.superAdmin)
      
      expect(context).toEqual({
        instituteId: 'institute-1',
        branchId: 'branch-1',
        userId: 'super-admin-1',
        userRole: 'super_admin',
        isSuperAdmin: true,
        isInstituteAdmin: true
      })
    })

    it('should extract correct context for institute admin', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      
      expect(context).toEqual({
        instituteId: 'institute-1',
        branchId: null,
        userId: 'institute-admin-1',
        userRole: 'institute_admin',
        isSuperAdmin: false,
        isInstituteAdmin: true
      })
    })

    it('should extract correct context for branch manager', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      
      expect(context).toEqual({
        instituteId: 'institute-1',
        branchId: 'branch-1',
        userId: 'branch-manager-1',
        userRole: 'branch_manager',
        isSuperAdmin: false,
        isInstituteAdmin: false
      })
    })

    it('should extract correct context for student', () => {
      const context = extractTenantContext(mockUsers.student)
      
      expect(context).toEqual({
        instituteId: 'institute-1',
        branchId: 'branch-1',
        userId: 'student-1',
        userRole: 'student',
        isSuperAdmin: false,
        isInstituteAdmin: false
      })
    })
  })

  describe('Tenant Filter Creation', () => {
    it('should return empty filter for super admin', () => {
      const context = extractTenantContext(mockUsers.superAdmin)
      const filter = createTenantFilter(context, 'institute')
      
      expect(filter).toEqual({})
    })

    it('should create institute filter for institute admin', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const filter = createTenantFilter(context, 'institute')
      
      expect(filter).toEqual({
        institute_id: {
          equals: 'institute-1'
        }
      })
    })

    it('should create branch filter for branch manager', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      const filter = createTenantFilter(context, 'branch')
      
      expect(filter).toEqual({
        and: [
          {
            institute_id: {
              equals: 'institute-1'
            }
          },
          {
            or: [
              {
                branch_id: {
                  equals: 'branch-1'
                }
              },
              {
                branch_id: {
                  equals: null
                }
              }
            ]
          }
        ]
      })
    })

    it('should create institute filter for institute admin with branch isolation', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const filter = createTenantFilter(context, 'branch')
      
      expect(filter).toEqual({
        institute_id: {
          equals: 'institute-1'
        }
      })
    })
  })

  describe('Auto-populate Tenant Fields', () => {
    it('should auto-populate fields for branch manager', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      const data = { title: 'Test Course' }
      
      const populated = autoPopulateTenantFields(data, context)
      
      expect(populated).toEqual({
        title: 'Test Course',
        institute_id: 'institute-1',
        branch_id: 'branch-1',
        created_by: 'branch-manager-1'
      })
    })

    it('should auto-populate only institute for institute admin', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const data = { title: 'Test Course' }
      
      const populated = autoPopulateTenantFields(data, context)
      
      expect(populated).toEqual({
        title: 'Test Course',
        institute_id: 'institute-1',
        created_by: 'institute-admin-1'
      })
    })

    it('should not override existing values for super admin', () => {
      const context = extractTenantContext(mockUsers.superAdmin)
      const data = { 
        title: 'Test Course',
        institute_id: 'different-institute',
        branch_id: 'different-branch'
      }
      
      const populated = autoPopulateTenantFields(data, context)
      
      expect(populated).toEqual({
        title: 'Test Course',
        institute_id: 'different-institute',
        branch_id: 'different-branch'
      })
    })

    it('should not override existing created_by field', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      const data = { 
        title: 'Test Course',
        created_by: 'existing-user'
      }
      
      const populated = autoPopulateTenantFields(data, context)
      
      expect(populated).toEqual({
        title: 'Test Course',
        institute_id: 'institute-1',
        branch_id: 'branch-1',
        created_by: 'existing-user'
      })
    })
  })

  describe('Tenant Update Validation', () => {
    it('should allow super admin to change institute', () => {
      const context = extractTenantContext(mockUsers.superAdmin)
      const existingData = { institute_id: 'institute-1' }
      const updateData = { institute_id: 'institute-2' }
      
      const result = validateTenantUpdate(existingData, updateData, context)
      
      expect(result.valid).toBe(true)
    })

    it('should prevent non-super-admin from changing institute', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const existingData = { institute_id: 'institute-1' }
      const updateData = { institute_id: 'institute-2' }
      
      const result = validateTenantUpdate(existingData, updateData, context)
      
      expect(result.valid).toBe(false)
      expect(result.error).toBe('Cannot change institute assignment')
    })

    it('should prevent branch manager from changing to different branch', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      const existingData = { branch_id: 'branch-1' }
      const updateData = { branch_id: 'branch-2' }
      
      const result = validateTenantUpdate(existingData, updateData, context)
      
      expect(result.valid).toBe(false)
      expect(result.error).toBe('Cannot assign resource to different branch')
    })

    it('should allow institute admin to change branch', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const existingData = { branch_id: 'branch-1' }
      const updateData = { branch_id: 'branch-2' }
      
      const result = validateTenantUpdate(existingData, updateData, context)
      
      expect(result.valid).toBe(true)
    })

    it('should allow same institute update', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const existingData = { institute_id: 'institute-1' }
      const updateData = { institute_id: 'institute-1', title: 'Updated' }
      
      const result = validateTenantUpdate(existingData, updateData, context)
      
      expect(result.valid).toBe(true)
    })
  })

  describe('File Access Validation', () => {
    it('should allow super admin to access any file', () => {
      const context = extractTenantContext(mockUsers.superAdmin)
      const filePath = 'institutes/different-institute/file.pdf'
      
      const hasAccess = validateFileAccess(filePath, context)
      
      expect(hasAccess).toBe(true)
    })

    it('should allow institute admin to access institute files', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const filePath = 'institutes/institute-1/file.pdf'
      
      const hasAccess = validateFileAccess(filePath, context)
      
      expect(hasAccess).toBe(true)
    })

    it('should deny access to different institute files', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const filePath = 'institutes/different-institute/file.pdf'
      
      const hasAccess = validateFileAccess(filePath, context)
      
      expect(hasAccess).toBe(false)
    })

    it('should allow branch manager to access branch files', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      const filePath = 'institutes/institute-1/branches/branch-1/file.pdf'
      
      const hasAccess = validateFileAccess(filePath, context)
      
      expect(hasAccess).toBe(true)
    })

    it('should deny branch manager access to different branch files', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      const filePath = 'institutes/institute-1/branches/branch-2/file.pdf'
      
      const hasAccess = validateFileAccess(filePath, context)
      
      expect(hasAccess).toBe(false)
    })

    it('should allow institute admin to access any branch files in their institute', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const filePath = 'institutes/institute-1/branches/any-branch/file.pdf'
      
      const hasAccess = validateFileAccess(filePath, context)
      
      expect(hasAccess).toBe(true)
    })
  })

  describe('Tenant Storage Path Generation', () => {
    it('should generate institute-level path for institute admin', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const path = getTenantStoragePath(context, 'document.pdf')
      
      expect(path).toBe('institutes/institute-1/document.pdf')
    })

    it('should generate branch-level path for branch manager', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      const path = getTenantStoragePath(context, 'document.pdf')
      
      expect(path).toBe('institutes/institute-1/branches/branch-1/document.pdf')
    })

    it('should generate branch-level path for users with branch', () => {
      const context = extractTenantContext(mockUsers.trainer)
      const path = getTenantStoragePath(context, 'lesson-video.mp4')
      
      expect(path).toBe('institutes/institute-1/branches/branch-1/lesson-video.mp4')
    })

    it('should handle special characters in filename', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      const path = getTenantStoragePath(context, 'test file (1).pdf')
      
      expect(path).toBe('institutes/institute-1/branches/branch-1/test file (1).pdf')
    })
  })

  describe('Cross-Tenant Data Isolation', () => {
    const institute1Context = extractTenantContext(mockUsers.instituteAdmin)
    const institute2Context = extractTenantContext({
      ...mockUsers.instituteAdmin,
      id: 'institute-admin-2',
      institute: 'institute-2'
    })

    it('should create different filters for different institutes', () => {
      const filter1 = createTenantFilter(institute1Context, 'institute')
      const filter2 = createTenantFilter(institute2Context, 'institute')
      
      expect(filter1).not.toEqual(filter2)
      expect(filter1.institute_id.equals).toBe('institute-1')
      expect(filter2.institute_id.equals).toBe('institute-2')
    })

    it('should prevent cross-institute file access', () => {
      const institute1File = 'institutes/institute-1/file.pdf'
      const institute2File = 'institutes/institute-2/file.pdf'
      
      expect(validateFileAccess(institute1File, institute1Context)).toBe(true)
      expect(validateFileAccess(institute2File, institute1Context)).toBe(false)
      
      expect(validateFileAccess(institute1File, institute2Context)).toBe(false)
      expect(validateFileAccess(institute2File, institute2Context)).toBe(true)
    })

    it('should generate different storage paths for different institutes', () => {
      const path1 = getTenantStoragePath(institute1Context, 'file.pdf')
      const path2 = getTenantStoragePath(institute2Context, 'file.pdf')
      
      expect(path1).toBe('institutes/institute-1/file.pdf')
      expect(path2).toBe('institutes/institute-2/file.pdf')
      expect(path1).not.toBe(path2)
    })
  })

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing branch ID gracefully', () => {
      const context = {
        ...extractTenantContext(mockUsers.branchManager),
        branchId: undefined
      }
      
      const path = getTenantStoragePath(context, 'file.pdf')
      expect(path).toBe('institutes/institute-1/file.pdf')
    })

    it('should handle empty filename', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      const path = getTenantStoragePath(context, '')
      
      expect(path).toBe('institutes/institute-1/branches/branch-1/')
    })

    it('should validate update with missing existing data', () => {
      const context = extractTenantContext(mockUsers.branchManager)
      const result = validateTenantUpdate({}, { title: 'New' }, context)
      
      expect(result.valid).toBe(true)
    })

    it('should handle file path without institute prefix', () => {
      const context = extractTenantContext(mockUsers.instituteAdmin)
      const hasAccess = validateFileAccess('public/file.pdf', context)
      
      expect(hasAccess).toBe(false)
    })
  })
})
