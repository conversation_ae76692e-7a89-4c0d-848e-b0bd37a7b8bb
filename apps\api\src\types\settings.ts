/**
 * Settings Types
 * 
 * Type definitions for the platform settings system
 */

// Setting data types
export type SettingType = 'string' | 'number' | 'boolean' | 'json' | 'url' | 'email' | 'textarea' | 'upload'

// Setting categories
export type SettingCategory = 
  | 'platform' 
  | 'email' 
  | 'security' 
  | 'storage' 
  | 'payment' 
  | 'notification' 
  | 'integration' 
  | 'feature'

// Validation rules for settings
export interface ValidationRules {
  min_length?: number
  max_length?: number
  min_value?: number
  max_value?: number
  pattern?: string
}

// Setting model
export interface Setting {
  id: string
  key: string
  value: string
  description?: string
  category: SettingCategory
  type: SettingType
  is_public: boolean
  is_required?: boolean
  validation_rules?: ValidationRules
  upload?: string | { id: string; url: string; filename: string } // For file uploads
  createdAt: string
  updatedAt: string
}

// Setting creation data
export interface SettingCreationData {
  key: string
  value: string
  description?: string
  category: SettingCategory
  type: SettingType
  is_public?: boolean
  is_required?: boolean
  validation_rules?: ValidationRules
}

// Setting update data
export interface SettingUpdateData {
  value?: string
  description?: string
  category?: SettingCategory
  type?: SettingType
  is_public?: boolean
  is_required?: boolean
  validation_rules?: ValidationRules
}

// Bulk update settings request
export interface BulkUpdateSettingsRequest {
  settings: Array<{
    key: string
    value: string
    description?: string
    category?: SettingCategory
    type?: SettingType
    is_public?: boolean
    is_required?: boolean
    validation_rules?: ValidationRules
  }>
}

// Bulk update settings response
export interface BulkUpdateSettingsResponse {
  results: Array<{
    key: string
    operation: 'created' | 'updated'
    id: string
  }>
  errors: Array<{
    key: string
    error: string
  }>
  success: number
  failed: number
}

// Settings response with pagination
export interface SettingsResponse {
  docs: Setting[]
  totalDocs: number
  limit: number
  totalPages: number
  page: number
  pagingCounter: number
  hasPrevPage: boolean
  hasNextPage: boolean
  prevPage: number | null
  nextPage: number | null
}

// Helper function to convert string value to typed value based on setting type
export function convertSettingValue(value: string, type: SettingType): any {
  switch (type) {
    case 'number':
      return Number(value)
    case 'boolean':
      return value.toLowerCase() === 'true' || value === '1'
    case 'json':
      try {
        return JSON.parse(value)
      } catch {
        return null
      }
    default:
      return value
  }
}

// Helper function to validate setting value based on type
export function validateSettingValue(value: string, type: SettingType): boolean {
  try {
    switch (type) {
      case 'number':
        return !isNaN(Number(value))
      case 'boolean':
        return ['true', 'false', '1', '0'].includes(value.toLowerCase())
      case 'json':
        JSON.parse(value)
        return true
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
      case 'url':
        new URL(value)
        return true
      default:
        return true
    }
  } catch {
    return false
  }
}

// Default platform settings
export const DEFAULT_PLATFORM_SETTINGS: SettingCreationData[] = [
  {
    key: 'platform_name',
    value: 'KISS LMS',
    description: 'The name of the platform displayed to users',
    category: 'platform',
    type: 'string',
    is_public: true,
    is_required: true,
  },
  {
    key: 'platform_url',
    value: 'https://groups-exam.com',
    description: 'The main URL of the platform',
    category: 'platform',
    type: 'url',
    is_public: true,
    is_required: true,
  },
  {
    key: 'support_email',
    value: '<EMAIL>',
    description: 'Email address for support inquiries',
    category: 'platform',
    type: 'email',
    is_public: true,
    is_required: true,
  },
  {
    key: 'maintenance_mode',
    value: 'false',
    description: 'Whether the platform is in maintenance mode',
    category: 'platform',
    type: 'boolean',
    is_public: true,
    is_required: true,
  },
  {
    key: 'allow_registration',
    value: 'true',
    description: 'Whether new institutes can register on the platform',
    category: 'platform',
    type: 'boolean',
    is_public: false,
    is_required: true,
  },
  {
    key: 'require_email_verification',
    value: 'true',
    description: 'Whether email verification is required for new accounts',
    category: 'security',
    type: 'boolean',
    is_public: false,
    is_required: true,
  },
  {
    key: 'max_institutes_per_plan',
    value: '1000',
    description: 'Maximum number of institutes allowed per plan',
    category: 'platform',
    type: 'number',
    is_public: false,
    is_required: true,
  },
  {
    key: 'session_timeout',
    value: '60',
    description: 'Session timeout in minutes',
    category: 'security',
    type: 'number',
    is_public: false,
    is_required: true,
  },
  {
    key: 'max_login_attempts',
    value: '5',
    description: 'Maximum number of login attempts before account lockout',
    category: 'security',
    type: 'number',
    is_public: false,
    is_required: true,
  },
  {
    key: 'platform_address',
    value: '',
    description: 'Physical address of the platform/organization',
    category: 'platform',
    type: 'textarea',
    is_public: true,
    is_required: false,
  },
  {
    key: 'platform_tagline',
    value: 'Empowering Education Through Technology',
    description: 'Platform tagline or slogan',
    category: 'platform',
    type: 'string',
    is_public: true,
    is_required: false,
  },
  {
    key: 'platform_logo',
    value: '',
    description: 'Platform logo image',
    category: 'platform',
    type: 'upload',
    is_public: true,
    is_required: false,
  },
  {
    key: 'platform_favicon',
    value: '',
    description: 'Platform favicon image',
    category: 'platform',
    type: 'upload',
    is_public: true,
    is_required: false,
  },
  // Security Settings
  {
    key: 'session_timeout',
    value: '60',
    description: 'Session timeout in minutes',
    category: 'security',
    type: 'number',
    is_public: false,
    is_required: true,
  },
  {
    key: 'max_login_attempts',
    value: '5',
    description: 'Maximum login attempts before lockout',
    category: 'security',
    type: 'number',
    is_public: false,
    is_required: true,
  },
  // Storage Settings
  {
    key: 'storage_provider',
    value: 's3',
    description: 'File storage provider',
    category: 'storage',
    type: 'string',
    is_public: false,
    is_required: true,
  },
  {
    key: 's3_bucket_name',
    value: '',
    description: 'S3 bucket name',
    category: 'storage',
    type: 'string',
    is_public: false,
    is_required: false,
  },
  // Email Settings
  {
    key: 'smtp_host',
    value: '',
    description: 'SMTP server host',
    category: 'email',
    type: 'string',
    is_public: false,
    is_required: false,
  },
  {
    key: 'smtp_port',
    value: '587',
    description: 'SMTP server port',
    category: 'email',
    type: 'number',
    is_public: false,
    is_required: false,
  },
]
