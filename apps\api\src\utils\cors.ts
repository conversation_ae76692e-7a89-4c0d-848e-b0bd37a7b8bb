export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '86400' // 24 hours
}

export const createCorsResponse = (data: any, options: { status?: number } = {}) => {
  return Response.json(data, {
    status: options.status || 200,
    headers: corsHeaders
  })
}

export const createCorsErrorResponse = (message: string, status: number = 400) => {
  return Response.json(
    { message },
    {
      status,
      headers: corsHeaders
    }
  )
}
