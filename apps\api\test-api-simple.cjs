const http = require('http')

function testAPI() {
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/health',
    method: 'GET'
  }

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`)
    console.log(`Headers: ${JSON.stringify(res.headers)}`)
    
    res.setEncoding('utf8')
    res.on('data', (chunk) => {
      console.log(`Body: ${chunk}`)
    })
    
    res.on('end', () => {
      console.log('✅ API is responding')
    })
  })

  req.on('error', (e) => {
    console.error(`❌ API connection error: ${e.message}`)
  })

  req.setTimeout(5000, () => {
    console.error('❌ API request timeout')
    req.destroy()
  })

  req.end()
}

console.log('Testing API connection...')
testAPI()
