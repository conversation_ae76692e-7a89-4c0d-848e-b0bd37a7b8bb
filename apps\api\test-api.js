// Simple test script to verify API endpoints
const API_BASE = 'http://localhost:3001/api'

async function testAPI() {
  console.log('🧪 Testing API Endpoints...\n')

  try {
    // Test 1: Register a new institute
    console.log('1. Testing Institute Registration...')
    const registerResponse = await fetch(`${API_BASE}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        instituteName: 'Test Academy',
        slug: 'test-academy',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        website: 'https://test-academy.com',
        description: 'A test academy for API testing',
        adminFirstName: 'John',
        adminLastName: 'Doe',
        adminEmail: '<EMAIL>',
        adminPhone: '+91 9876543211',
        password: 'password123',
        city: 'Mumbai',
        state: 'Maharashtra',
        country: 'India'
      }),
    })

    if (registerResponse.ok) {
      const registerData = await registerResponse.json()
      console.log('✅ Institute registration successful:', registerData.message)
    } else {
      const errorData = await registerResponse.json()
      console.log('❌ Institute registration failed:', errorData.message)
    }

    // Test 2: Login with institute admin
    console.log('\n2. Testing Institute Admin Login...')
    const loginResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        userType: 'institute_admin'
      }),
    })

    let authToken = null
    if (loginResponse.ok) {
      const loginData = await loginResponse.json()
      authToken = loginData.token
      console.log('✅ Institute admin login successful')
      console.log('   User:', loginData.user.firstName, loginData.user.lastName)
      console.log('   Role:', loginData.user.role)
    } else {
      const errorData = await loginResponse.json()
      console.log('❌ Institute admin login failed:', errorData.message)
    }

    // Test 3: Get current user profile (if logged in)
    if (authToken) {
      console.log('\n3. Testing Get Current User...')
      const profileResponse = await fetch(`${API_BASE}/users/me`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })

      if (profileResponse.ok) {
        const profileData = await profileResponse.json()
        console.log('✅ Get current user successful')
        console.log('   Email:', profileData.user.email)
        console.log('   Institute:', profileData.user.institute)
      } else {
        const errorData = await profileResponse.json()
        console.log('❌ Get current user failed:', errorData.message)
      }

      // Test 4: Get current institute
      console.log('\n4. Testing Get Current Institute...')
      const instituteResponse = await fetch(`${API_BASE}/institutes/me`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })

      if (instituteResponse.ok) {
        const instituteData = await instituteResponse.json()
        console.log('✅ Get current institute successful')
        console.log('   Name:', instituteData.institute.name)
        console.log('   Subscription:', instituteData.institute.subscriptionPlan)
      } else {
        const errorData = await instituteResponse.json()
        console.log('❌ Get current institute failed:', errorData.message)
      }
    }

    // Test 5: Register a student
    console.log('\n5. Testing Student Registration...')
    const studentRegisterResponse = await fetch(`${API_BASE}/auth/user-register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+91 9876543212',
        password: 'password123'
      }),
    })

    if (studentRegisterResponse.ok) {
      const studentData = await studentRegisterResponse.json()
      console.log('✅ Student registration successful:', studentData.message)
    } else {
      const errorData = await studentRegisterResponse.json()
      console.log('❌ Student registration failed:', errorData.message)
    }

    // Test 6: Login with student
    console.log('\n6. Testing Student Login...')
    const studentLoginResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        userType: 'student'
      }),
    })

    if (studentLoginResponse.ok) {
      const studentLoginData = await studentLoginResponse.json()
      console.log('✅ Student login successful')
      console.log('   User:', studentLoginData.user.firstName, studentLoginData.user.lastName)
      console.log('   Role:', studentLoginData.user.role)
    } else {
      const errorData = await studentLoginResponse.json()
      console.log('❌ Student login failed:', errorData.message)
    }

    console.log('\n🎉 API Testing Complete!')

  } catch (error) {
    console.error('❌ API Test Error:', error.message)
  }
}

// Run the test
testAPI()
