// Simple test to verify Staff collection configuration
const fs = require('fs')
const path = require('path')

console.log('🔍 Testing Staff Collection Configuration...\n')

// Read the Staff collection file
const staffFilePath = path.join(__dirname, 'src/collections/Staff.ts')
const staffContent = fs.readFileSync(staffFilePath, 'utf8')

// Check for key configuration elements
const checks = [
  {
    name: 'useAsTitle points to fullName',
    test: () => staffContent.includes("useAsTitle: 'fullName'"),
    expected: true
  },
  {
    name: 'fullName field exists at top level',
    test: () => {
      const lines = staffContent.split('\n')
      let inFields = false
      let braceCount = 0
      let foundFullName = false
      
      for (const line of lines) {
        if (line.includes('fields: [')) {
          inFields = true
          continue
        }
        
        if (inFields) {
          if (line.includes('{')) braceCount++
          if (line.includes('}')) braceCount--
          
          // Check if we're at the top level (braceCount === 1 means we're in the first level of fields)
          if (braceCount === 1 && line.includes("name: 'fullName'")) {
            foundFullName = true
            break
          }
        }
      }
      
      return foundFullName
    },
    expected: true
  },
  {
    name: 'No default export',
    test: () => !staffContent.includes('export default Staff'),
    expected: true
  },
  {
    name: 'Named export exists',
    test: () => staffContent.includes('export const Staff'),
    expected: true
  }
]

let allPassed = true

checks.forEach(check => {
  const result = check.test()
  const status = result === check.expected ? '✅' : '❌'
  console.log(`${status} ${check.name}: ${result}`)
  
  if (result !== check.expected) {
    allPassed = false
  }
})

console.log('\n' + '='.repeat(50))
console.log(allPassed ? '🎉 All checks passed!' : '⚠️  Some checks failed!')
console.log('='.repeat(50))

if (allPassed) {
  console.log('\n✅ Staff collection should now work with Payload CMS!')
  console.log('Try restarting your development server.')
} else {
  console.log('\n❌ There are still configuration issues.')
  console.log('Please check the Staff collection configuration.')
}
