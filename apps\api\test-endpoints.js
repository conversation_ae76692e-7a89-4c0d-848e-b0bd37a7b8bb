#!/usr/bin/env node

/**
 * Comprehensive API Endpoint Testing Script
 * Tests all major API endpoints to ensure they're working correctly
 */

const API_BASE = 'http://localhost:3001'
const FRONTEND_BASE = 'http://localhost:3000'

// Test endpoints configuration
const API_ENDPOINTS = [
  // Health & Status
  { name: 'Health Check', method: 'GET', path: '/health', expectedStatus: [200] },
  { name: 'API Status', method: 'GET', path: '/api-status', expectedStatus: [200] },
  { name: 'Frontend Routes Status', method: 'GET', path: '/frontend-routes', expectedStatus: [200] },
  
  // Course Management
  { name: 'Get Courses', method: 'GET', path: '/api/admin/courses', expectedStatus: [200, 401] },
  { name: 'Course Builder Courses', method: 'GET', path: '/api/admin/course-builder/courses', expectedStatus: [200, 401] },
  
  // Course Bundles
  { name: 'Get Course Bundles', method: 'GET', path: '/api/admin/course-bundles', expectedStatus: [200, 401] },
  { name: 'Bundle Performance', method: 'GET', path: '/api/admin/course-bundles/performance-comparison', expectedStatus: [200, 401] },
  { name: 'Bundle Suggestions', method: 'GET', path: '/api/admin/course-bundles/suggestions', expectedStatus: [200, 401] },
  
  // Question Banks
  { name: 'Get Question Banks', method: 'GET', path: '/api/admin/question-banks', expectedStatus: [200, 401] },
  { name: 'Question Bank Analytics', method: 'GET', path: '/api/admin/question-banks/analytics', expectedStatus: [200, 401] },
  { name: 'Question Bank Search', method: 'GET', path: '/api/admin/question-banks/search', expectedStatus: [200, 401] },
  
  // Tests
  { name: 'Get Tests', method: 'GET', path: '/api/admin/tests', expectedStatus: [200, 401] },
  { name: 'Test Analytics', method: 'GET', path: '/api/admin/tests/analytics', expectedStatus: [200, 401] },
  
  // Media Processing
  { name: 'Video Processing Jobs', method: 'GET', path: '/api/admin/video-processing/jobs', expectedStatus: [200, 401] },
  
  // Lessons
  { name: 'Get Lessons', method: 'GET', path: '/api/admin/lessons', expectedStatus: [200, 401] },
]

const FRONTEND_ROUTES = [
  // Public Routes
  { name: 'Landing Page', path: '/', expectedStatus: [200] },
  { name: 'Test Endpoints Page', path: '/test-endpoints', expectedStatus: [200] },
  
  // Auth Routes
  { name: 'Login Page', path: '/auth/login', expectedStatus: [200] },
  { name: 'Admin Login', path: '/auth/admin', expectedStatus: [200] },
  
  // Admin Routes (may redirect if not authenticated)
  { name: 'Admin Dashboard', path: '/admin', expectedStatus: [200, 302, 401] },
  { name: 'Course Management', path: '/admin/courses', expectedStatus: [200, 302, 401] },
  { name: 'Course Creation', path: '/admin/courses/create', expectedStatus: [200, 302, 401] },
  { name: 'Staff Management', path: '/admin/staff', expectedStatus: [200, 302, 401] },
  
  // Student Routes
  { name: 'Student Dashboard', path: '/student/dashboard', expectedStatus: [200, 302, 401] },
  { name: 'Student Courses', path: '/student/courses', expectedStatus: [200, 302, 401] },
  
  // Super Admin Routes
  { name: 'Super Admin Dashboard', path: '/super-admin', expectedStatus: [200, 302, 401] },
  { name: 'Institute Management', path: '/super-admin/institutes', expectedStatus: [200, 302, 401] },
]

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

async function testEndpoint(endpoint, baseUrl = API_BASE) {
  const url = `${baseUrl}${endpoint.path}`
  const startTime = Date.now()
  
  try {
    const response = await fetch(url, {
      method: endpoint.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        // Add mock auth for testing (will get 401 but endpoint exists)
        'Authorization': 'Bearer test-token'
      }
    })
    
    const endTime = Date.now()
    const responseTime = endTime - startTime
    const isExpectedStatus = endpoint.expectedStatus.includes(response.status)
    
    if (isExpectedStatus) {
      log(`✅ ${endpoint.name}: ${response.status} (${responseTime}ms)`, 'green')
      return { success: true, status: response.status, responseTime }
    } else {
      log(`❌ ${endpoint.name}: ${response.status} (expected: ${endpoint.expectedStatus.join('/')}) (${responseTime}ms)`, 'red')
      return { success: false, status: response.status, responseTime }
    }
  } catch (error) {
    log(`💥 ${endpoint.name}: ${error.message}`, 'red')
    return { success: false, error: error.message }
  }
}

async function testFrontendRoute(route) {
  const url = `${FRONTEND_BASE}${route.path}`
  const startTime = Date.now()
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    })
    
    const endTime = Date.now()
    const responseTime = endTime - startTime
    const isExpectedStatus = route.expectedStatus.includes(response.status)
    
    if (isExpectedStatus) {
      log(`✅ ${route.name}: ${response.status} (${responseTime}ms)`, 'green')
      return { success: true, status: response.status, responseTime }
    } else {
      log(`❌ ${route.name}: ${response.status} (expected: ${route.expectedStatus.join('/')}) (${responseTime}ms)`, 'red')
      return { success: false, status: response.status, responseTime }
    }
  } catch (error) {
    log(`💥 ${route.name}: ${error.message}`, 'red')
    return { success: false, error: error.message }
  }
}

async function runTests() {
  log('\n🚀 Starting Comprehensive LMS System Testing...', 'bold')
  log('=' .repeat(60), 'blue')
  
  // Test API Endpoints
  log('\n📡 Testing API Endpoints...', 'blue')
  log('-'.repeat(40), 'blue')
  
  const apiResults = []
  for (const endpoint of API_ENDPOINTS) {
    const result = await testEndpoint(endpoint)
    apiResults.push({ ...endpoint, ...result })
    await new Promise(resolve => setTimeout(resolve, 100)) // Small delay
  }
  
  // Test Frontend Routes
  log('\n🌐 Testing Frontend Routes...', 'blue')
  log('-'.repeat(40), 'blue')
  
  const frontendResults = []
  for (const route of FRONTEND_ROUTES) {
    const result = await testFrontendRoute(route)
    frontendResults.push({ ...route, ...result })
    await new Promise(resolve => setTimeout(resolve, 100)) // Small delay
  }
  
  // Summary
  log('\n📊 Test Results Summary', 'bold')
  log('=' .repeat(60), 'blue')
  
  const apiSuccess = apiResults.filter(r => r.success).length
  const apiTotal = apiResults.length
  const frontendSuccess = frontendResults.filter(r => r.success).length
  const frontendTotal = frontendResults.length
  
  log(`\n📡 API Endpoints: ${apiSuccess}/${apiTotal} passed (${Math.round(apiSuccess/apiTotal*100)}%)`, 
      apiSuccess === apiTotal ? 'green' : 'yellow')
  log(`🌐 Frontend Routes: ${frontendSuccess}/${frontendTotal} passed (${Math.round(frontendSuccess/frontendTotal*100)}%)`, 
      frontendSuccess === frontendTotal ? 'green' : 'yellow')
  
  const totalSuccess = apiSuccess + frontendSuccess
  const totalTests = apiTotal + frontendTotal
  
  log(`\n🎯 Overall Success Rate: ${totalSuccess}/${totalTests} (${Math.round(totalSuccess/totalTests*100)}%)`, 
      totalSuccess === totalTests ? 'green' : 'yellow')
  
  // Failed tests
  const failedTests = [...apiResults, ...frontendResults].filter(r => !r.success)
  if (failedTests.length > 0) {
    log('\n❌ Failed Tests:', 'red')
    failedTests.forEach(test => {
      log(`   • ${test.name}: ${test.error || `Status ${test.status}`}`, 'red')
    })
  }
  
  log('\n✨ Testing Complete!', 'bold')
  
  if (totalSuccess === totalTests) {
    log('🎉 All tests passed! System is fully operational.', 'green')
  } else {
    log('⚠️  Some tests failed. Check the details above.', 'yellow')
  }
}

// Run the tests
runTests().catch(error => {
  log(`💥 Test runner failed: ${error.message}`, 'red')
  process.exit(1)
})
