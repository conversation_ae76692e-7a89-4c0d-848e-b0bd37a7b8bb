// Test login functionality
async function testLogin() {
  console.log('🔐 Testing login functionality...\n')

  try {
    // Test 1: Custom auth endpoint
    console.log('1. Testing custom auth endpoint: /api/auth/login')
    const customAuthResponse = await fetch('http://localhost:3002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'SuperAdmin@123',
        userType: 'super_admin'
      }),
    })

    console.log('Status:', customAuthResponse.status)
    const customAuthText = await customAuthResponse.text()
    console.log('Response:', customAuthText)

  } catch (error) {
    console.error('❌ Custom auth test failed:', error.message)
  }

  try {
    // Test 2: Payload built-in login endpoint
    console.log('\n2. Testing Payload built-in endpoint: /api/users/login')
    const payloadResponse = await fetch('http://localhost:3002/api/users/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'SuperAdmin@123'
      }),
    })

    console.log('Status:', payloadResponse.status)
    const payloadText = await payloadResponse.text()
    console.log('Response:', payloadText)

  } catch (error) {
    console.error('❌ Payload auth test failed:', error.message)
  }

  try {
    // Test 3: Check if user exists
    console.log('\n3. Testing user lookup: /api/users')
    const usersResponse = await fetch('http://localhost:3002/api/users', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    console.log('Status:', usersResponse.status)
    const usersText = await usersResponse.text()
    console.log('Response:', usersText.substring(0, 500) + '...')

  } catch (error) {
    console.error('❌ Users lookup test failed:', error.message)
  }
}

console.log('🧪 Login Test Suite\n')
testLogin()
  .then(() => {
    console.log('\n✅ Test completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Test failed:', error.message)
    process.exit(1)
  })
