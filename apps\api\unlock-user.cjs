// Unlock user account by resetting login attempts and lock status
const { Client } = require('pg')

async function unlockUser() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'lms_new',
    user: 'postgres',
    password: '1234'
  })
  
  try {
    console.log('🔓 Unlocking user account...\n')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Check current user status
    const userCheck = await client.query(
      'SELECT id, email, login_attempts, lock_until, locked_until FROM users WHERE email = $1',
      ['<EMAIL>']
    )

    if (userCheck.rows.length === 0) {
      console.log('❌ User not found with email: <EMAIL>')
      return
    }

    const user = userCheck.rows[0]
    console.log('📋 Current user status:')
    console.log('🆔 ID:', user.id)
    console.log('📧 Email:', user.email)
    console.log('🔢 Login Attempts:', user.login_attempts)
    console.log('🔒 Lock Until:', user.lock_until || user.locked_until || 'Not locked')

    // Reset login attempts and unlock the account
    const unlockQuery = `
      UPDATE users 
      SET 
        login_attempts = 0,
        lock_until = NULL,
        locked_until = NULL
      WHERE email = $1
      RETURNING id, email, login_attempts, lock_until, locked_until
    `

    const result = await client.query(unlockQuery, ['<EMAIL>'])
    const updatedUser = result.rows[0]

    console.log('\n🎉 User account unlocked successfully!')
    console.log('🆔 ID:', updatedUser.id)
    console.log('📧 Email:', updatedUser.email)
    console.log('🔢 Login Attempts:', updatedUser.login_attempts)
    console.log('🔒 Lock Status:', updatedUser.lock_until || updatedUser.locked_until || 'Unlocked')

    console.log('\n🔐 You can now login with:')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: SuperAdmin@123')
    console.log('🌐 Admin Panel: http://localhost:3002/admin')
    console.log('🌐 Frontend Login: http://localhost:3002/auth/admin/login')

  } catch (error) {
    console.error('❌ Error unlocking user:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Database connection failed. Please check:')
      console.log('   - PostgreSQL is running')
      console.log('   - Database "lms_new" exists')
      console.log('   - Credentials are correct (postgres:1234)')
    }
  } finally {
    await client.end()
    console.log('\n🔌 Database connection closed')
  }
}

console.log('🔓 User Account Unlock Tool\n')
unlockUser()
  .then(() => {
    console.log('\n✅ Unlock process completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Unlock process failed:', error.message)
    process.exit(1)
  })
