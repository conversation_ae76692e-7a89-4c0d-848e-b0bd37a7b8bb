{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps extends React.ComponentProps<\"textarea\"> {\n  error?: string | boolean\n}\n\nfunction Textarea({ className, error, ...props }: TextareaProps) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      aria-invalid={!!error}\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        error && \"border-destructive ring-destructive/20\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAMA,SAAS,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAsB;IAC7D,qBACE,2XAAC;QACC,aAAU;QACV,gBAAc,CAAC,CAAC;QAChB,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,ucACA,SAAS,0CACT;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface SwitchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {\n  checked?: boolean\n  onCheckedChange?: (checked: boolean) => void\n}\n\nconst Switch = React.forwardRef<HTMLInputElement, SwitchProps>(\n  ({ className, checked, onCheckedChange, onChange, ...props }, ref) => {\n    const [internalChecked, setInternalChecked] = React.useState(checked || false)\n    const isChecked = checked !== undefined ? checked : internalChecked\n\n    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n      const newChecked = event.target.checked\n      if (checked === undefined) {\n        setInternalChecked(newChecked)\n      }\n      onCheckedChange?.(newChecked)\n      onChange?.(event)\n    }\n\n    return (\n      <label className=\"relative inline-flex items-center cursor-pointer\">\n        <input\n          type=\"checkbox\"\n          ref={ref}\n          className=\"sr-only peer\"\n          checked={isChecked}\n          onChange={handleChange}\n          {...props}\n        />\n        <div\n          className={cn(\n            \"relative w-11 h-6 rounded-full transition-colors duration-200 ease-in-out\",\n            \"focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2\",\n            isChecked\n              ? \"bg-blue-600\"\n              : \"bg-gray-200\",\n            className\n          )}\n        >\n          <div\n            className={cn(\n              \"absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md transition-transform duration-200 ease-in-out\",\n              isChecked ? \"translate-x-5\" : \"translate-x-0\"\n            )}\n          />\n        </div>\n      </label>\n    )\n  }\n)\nSwitch.displayName = \"Switch\"\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,uBAAS,CAAA,GAAA,kVAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,kVAAA,CAAA,WAAc,AAAD,EAAE,WAAW;IACxE,MAAM,YAAY,YAAY,YAAY,UAAU;IAEpD,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,MAAM,MAAM,CAAC,OAAO;QACvC,IAAI,YAAY,WAAW;YACzB,mBAAmB;QACrB;QACA,kBAAkB;QAClB,WAAW;IACb;IAEA,qBACE,2XAAC;QAAM,WAAU;;0BACf,2XAAC;gBACC,MAAK;gBACL,KAAK;gBACL,WAAU;gBACV,SAAS;gBACT,UAAU;gBACT,GAAG,KAAK;;;;;;0BAEX,2XAAC;gBACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,6EACA,6EACA,YACI,gBACA,eACJ;0BAGF,cAAA,2XAAC;oBACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,mHACA,YAAY,kBAAkB;;;;;;;;;;;;;;;;;AAM1C;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/api/settings.ts"], "sourcesContent": ["/**\n * Settings API Client\n * \n * API functions for managing platform settings\n */\n\nimport { api } from '../api'\n\n// Types\nexport type SettingType = 'string' | 'number' | 'boolean' | 'json' | 'url' | 'email' | 'textarea' | 'upload' | 'media'\nexport type SettingCategory = \n  | 'platform' \n  | 'email' \n  | 'security' \n  | 'storage' \n  | 'payment' \n  | 'notification' \n  | 'integration' \n  | 'feature'\n\nexport interface ValidationRules {\n  min_length?: number\n  max_length?: number\n  min_value?: number\n  max_value?: number\n  pattern?: string\n}\n\nexport interface Setting {\n  id: string\n  key: string\n  value: string\n  description?: string\n  category: SettingCategory\n  type: SettingType\n  is_public: boolean\n  is_required?: boolean\n  validation_rules?: ValidationRules\n  upload?: string | { id: string; url: string; filename: string } // For file uploads\n  mediaRef?: { id: string; url: string; filename: string } // For media type settings\n  mediaData?: { id: string; url: string; filename: string } // Populated media data\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface SettingCreationData {\n  key: string\n  value: string\n  description?: string\n  category: SettingCategory\n  type: SettingType\n  is_public?: boolean\n  is_required?: boolean\n  validation_rules?: ValidationRules\n  mediaRef?: string | number // For media type settings\n}\n\nexport interface SettingUpdateData {\n  key?: string\n  value?: string\n  description?: string\n  category?: SettingCategory\n  type?: SettingType\n  is_public?: boolean\n  is_required?: boolean\n  validation_rules?: ValidationRules\n}\n\nexport interface SettingsFilters {\n  category?: SettingCategory\n  type?: SettingType\n  is_public?: boolean\n  search?: string\n}\n\nexport interface SettingsResponse {\n  success: boolean\n  settings: Setting[]\n  totalDocs: number\n  limit?: number\n  totalPages?: number\n  page?: number\n  category?: string\n}\n\nexport interface BulkUpdateSettingsRequest {\n  settings: SettingCreationData[]\n}\n\nexport interface BulkUpdateSettingsResponse {\n  results: Array<{\n    key: string\n    operation: 'created' | 'updated'\n    id: string\n  }>\n  errors: Array<{\n    key: string\n    error: string\n  }>\n  success: number\n  failed: number\n}\n\n/**\n * Settings API functions\n */\nexport const settingsApi = {\n  /**\n   * Get all settings with optional filtering\n   */\n  async getSettings(filters?: SettingsFilters): Promise<SettingsResponse> {\n    const params = new URLSearchParams()\n    \n    if (filters?.category) params.append('category', filters.category)\n    if (filters?.type) params.append('type', filters.type)\n    if (filters?.is_public !== undefined) params.append('is_public', filters.is_public.toString())\n    if (filters?.search) params.append('search', filters.search)\n    \n    const queryString = params.toString()\n    const url = queryString ? `/api/platform/settings?${queryString}` : '/api/platform/settings'\n\n    return api.get(url)\n  },\n\n  /**\n   * Get a specific setting by key\n   */\n  async getSettingByKey(key: string): Promise<Setting> {\n    return api.get(`/api/platform/settings/${key}`)\n  },\n\n  /**\n   * Get settings by category\n   */\n  async getSettingsByCategory(category: SettingCategory): Promise<SettingsResponse> {\n    return api.get(`/api/platform/settings/category/${category}`)\n  },\n\n  /**\n   * Create a new setting\n   */\n  async createSetting(data: SettingCreationData): Promise<Setting> {\n    return api.post('/api/platform/settings', data)\n  },\n\n  /**\n   * Update a setting by key\n   */\n  async updateSetting(key: string, data: SettingUpdateData): Promise<Setting> {\n    return api.put(`/api/platform/settings/${key}`, data)\n  },\n\n  /**\n   * Delete a setting by key\n   */\n  async deleteSetting(key: string): Promise<{ message: string }> {\n    return api.delete(`/api/platform/settings/${key}`)\n  },\n\n  /**\n   * Bulk update multiple settings\n   */\n  async bulkUpdateSettings(settings: SettingCreationData[]): Promise<BulkUpdateSettingsResponse> {\n    return api.post('/api/platform/settings/bulk', { settings })\n  },\n\n  /**\n   * Get public settings (no authentication required)\n   */\n  async getPublicSettings(): Promise<SettingsResponse> {\n    return api.get('/api/platform/settings?is_public=true')\n  },\n\n  /**\n   * Get platform configuration (commonly used settings)\n   */\n  async getPlatformConfig(): Promise<Record<string, string>> {\n    const response = await this.getSettingsByCategory('platform')\n    const config: Record<string, string> = {}\n    \n    response.settings.forEach(setting => {\n      config[setting.key] = setting.value\n    })\n    \n    return config\n  },\n\n  /**\n   * Update platform configuration\n   */\n  async updatePlatformConfig(config: Record<string, string>): Promise<BulkUpdateSettingsResponse> {\n    const settings: SettingCreationData[] = Object.entries(config).map(([key, value]) => ({\n      key,\n      value,\n      category: 'platform',\n      type: 'string',\n    }))\n\n    return this.bulkUpdateSettings(settings)\n  },\n\n  /**\n   * Create or update a single setting (handles both creation and updates)\n   */\n  async createOrUpdateSetting(key: string, value: string, options: {\n    category?: SettingCategory\n    type?: SettingType\n    description?: string\n    is_public?: boolean\n  } = {}): Promise<BulkUpdateSettingsResponse> {\n    const setting: SettingCreationData = {\n      key,\n      value,\n      category: options.category || 'platform',\n      type: options.type || 'string',\n      description: options.description,\n      is_public: options.is_public !== undefined ? options.is_public : true\n    }\n\n    return this.bulkUpdateSettings([setting])\n  },\n\n\n}\n\n/**\n * Helper functions for common setting operations\n */\nexport const settingsHelpers = {\n  /**\n   * Convert setting value to appropriate type\n   */\n  convertValue(value: string, type: SettingType): any {\n    switch (type) {\n      case 'number':\n        return Number(value)\n      case 'boolean':\n        return value.toLowerCase() === 'true' || value === '1'\n      case 'json':\n        try {\n          return JSON.parse(value)\n        } catch {\n          return null\n        }\n      default:\n        return value\n    }\n  },\n\n  /**\n   * Validate setting value based on type\n   */\n  validateValue(value: string, type: SettingType): boolean {\n    try {\n      switch (type) {\n        case 'number':\n          return !isNaN(Number(value))\n        case 'boolean':\n          return ['true', 'false', '1', '0'].includes(value.toLowerCase())\n        case 'json':\n          JSON.parse(value)\n          return true\n        case 'email':\n          return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)\n        case 'url':\n          new URL(value)\n          return true\n        default:\n          return true\n      }\n    } catch {\n      return false\n    }\n  },\n}\n\nexport default settingsApi\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;AAED;;AAoGO,MAAM,cAAc;IACzB;;GAEC,GACD,MAAM,aAAY,OAAyB;QACzC,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI;QACrD,IAAI,SAAS,cAAc,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS,CAAC,QAAQ;QAC3F,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAE3D,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,MAAM,cAAc,CAAC,uBAAuB,EAAE,aAAa,GAAG;QAEpE,OAAO,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;IAEA;;GAEC,GACD,MAAM,iBAAgB,GAAW;QAC/B,OAAO,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK;IAChD;IAEA;;GAEC,GACD,MAAM,uBAAsB,QAAyB;QACnD,OAAO,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,gCAAgC,EAAE,UAAU;IAC9D;IAEA;;GAEC,GACD,MAAM,eAAc,IAAyB;QAC3C,OAAO,qIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,0BAA0B;IAC5C;IAEA;;GAEC,GACD,MAAM,eAAc,GAAW,EAAE,IAAuB;QACtD,OAAO,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK,EAAE;IAClD;IAEA;;GAEC,GACD,MAAM,eAAc,GAAW;QAC7B,OAAO,qIAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,KAAK;IACnD;IAEA;;GAEC,GACD,MAAM,oBAAmB,QAA+B;QACtD,OAAO,qIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,+BAA+B;YAAE;QAAS;IAC5D;IAEA;;GAEC,GACD,MAAM;QACJ,OAAO,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;IAEA;;GAEC,GACD,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,CAAC,qBAAqB,CAAC;QAClD,MAAM,SAAiC,CAAC;QAExC,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAA;YACxB,MAAM,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK;QACrC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,sBAAqB,MAA8B;QACvD,MAAM,WAAkC,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,CAAC;gBACpF;gBACA;gBACA,UAAU;gBACV,MAAM;YACR,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC;IAEA;;GAEC,GACD,MAAM,uBAAsB,GAAW,EAAE,KAAa,EAAE,UAKpD,CAAC,CAAC;QACJ,MAAM,UAA+B;YACnC;YACA;YACA,UAAU,QAAQ,QAAQ,IAAI;YAC9B,MAAM,QAAQ,IAAI,IAAI;YACtB,aAAa,QAAQ,WAAW;YAChC,WAAW,QAAQ,SAAS,KAAK,YAAY,QAAQ,SAAS,GAAG;QACnE;QAEA,OAAO,IAAI,CAAC,kBAAkB,CAAC;YAAC;SAAQ;IAC1C;AAGF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,cAAa,KAAa,EAAE,IAAiB;QAC3C,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO;YAChB,KAAK;gBACH,OAAO,MAAM,WAAW,OAAO,UAAU,UAAU;YACrD,KAAK;gBACH,IAAI;oBACF,OAAO,KAAK,KAAK,CAAC;gBACpB,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,eAAc,KAAa,EAAE,IAAiB;QAC5C,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,OAAO,CAAC,MAAM,OAAO;gBACvB,KAAK;oBACH,OAAO;wBAAC;wBAAQ;wBAAS;wBAAK;qBAAI,CAAC,QAAQ,CAAC,MAAM,WAAW;gBAC/D,KAAK;oBACH,KAAK,KAAK,CAAC;oBACX,OAAO;gBACT,KAAK;oBACH,OAAO,6BAA6B,IAAI,CAAC;gBAC3C,KAAK;oBACH,IAAI,IAAI;oBACR,OAAO;gBACT;oBACE,OAAO;YACX;QACF,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/settings/useSettingsStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools } from 'zustand/middleware'\nimport { toast } from 'sonner'\nimport {\n  settingsApi,\n  type Setting,\n  type SettingCreationData,\n  type SettingUpdateData,\n  type SettingsFilters,\n  type SettingCategory\n} from '@/lib/api/settings'\n\ninterface SettingsState {\n  // State\n  settings: Setting[]\n  settingsByCategory: Record<SettingCategory, Setting[]>\n  currentSetting: Setting | null\n  filters: SettingsFilters\n\n  // Loading states\n  isLoading: boolean\n  isCreating: boolean\n  isUpdating: boolean\n  isDeleting: boolean\n  isBulkUpdating: boolean\n  isUploadingLogo: boolean\n  isUploadingFavicon: boolean\n  isRemovingLogo: boolean\n  isRemovingFavicon: boolean\n\n  // Error state\n  error: string | null\n  uploadError: string | null\n\n  // Platform settings will be included in the main settings array\n\n  // Actions\n  fetchSettings: (filters?: SettingsFilters) => Promise<void>\n  fetchSettingByKey: (key: string) => Promise<Setting | null>\n  fetchSettingsByCategory: (category: SettingCategory) => Promise<void>\n  createSetting: (data: SettingCreationData) => Promise<void>\n  updateSetting: (key: string, data: SettingUpdateData) => Promise<void>\n  deleteSetting: (key: string) => Promise<void>\n  bulkUpdateSettings: (settings: SettingCreationData[]) => Promise<void>\n\n  // Utility actions\n  setFilters: (filters: Partial<SettingsFilters>) => void\n  clearFilters: () => void\n  clearError: () => void\n  clearCurrentSetting: () => void\n\n  // Getters\n  getSettingValue: (key: string) => string | null\n  getSettingsByCategory: (category: SettingCategory) => Setting[]\n  getPublicSettings: () => Setting[]\n\n  // Platform file upload actions\n  uploadPlatformLogo: (file: File) => Promise<boolean>\n  uploadPlatformFavicon: (file: File) => Promise<boolean>\n  removePlatformLogo: () => Promise<boolean>\n  removePlatformFavicon: () => Promise<boolean>\n  processFavicon: (file: File) => Promise<boolean>\n  clearUploadError: () => void\n}\n\nexport const useSettingsStore = create<SettingsState>()(\n  devtools(\n    (set, get) => ({\n      // Initial state\n      settings: [],\n      settingsByCategory: {},\n      currentSetting: null,\n      isLoading: false,\n      isCreating: false,\n      isUpdating: false,\n      isDeleting: false,\n      isBulkUpdating: false,\n      isUploadingLogo: false,\n      isUploadingFavicon: false,\n      isRemovingLogo: false,\n      isRemovingFavicon: false,\n      error: null,\n      uploadError: null,\n      filters: {\n        category: '',\n        scope: '',\n        search: '',\n      },\n\n      // Fetch settings\n      fetchSettings: async (filters = {}) => {\n        set({ isLoading: true })\n        try {\n          const currentFilters = { ...get().filters, ...filters }\n          const response = await settingsApi.getSettings(currentFilters)\n\n          // Group settings by category\n          const settingsByCategory = response.settings.reduce((acc: Record<string, Setting[]>, setting: Setting) => {\n            if (!acc[setting.category]) {\n              acc[setting.category] = []\n            }\n            acc[setting.category].push(setting)\n            return acc\n          }, {})\n\n          set({\n            settings: response.settings,\n            settingsByCategory,\n            isLoading: false,\n            error: null,\n          })\n        } catch (error) {\n          set({\n            isLoading: false,\n            error: (error as Error).message,\n          })\n          toast.error('Failed to fetch settings')\n        }\n      },\n\n      // Fetch setting by key\n      fetchSettingByKey: async (key) => {\n        try {\n          const setting = await settingsApi.getSettingByKey(key)\n\n          // Update current setting\n          set({ currentSetting: setting })\n\n          return setting\n        } catch (error) {\n          set({ error: (error as Error).message })\n          toast.error('Failed to fetch setting')\n          return null\n        }\n      },\n\n      // Fetch settings by category\n      fetchSettingsByCategory: async (category) => {\n        set({ isLoading: true })\n        try {\n          const response = await settingsApi.getSettingsByCategory(category)\n\n          // Update category-specific settings\n          set(state => ({\n            settingsByCategory: {\n              ...state.settingsByCategory,\n              [category]: response.settings\n            },\n            isLoading: false,\n            error: null\n          }))\n        } catch (error) {\n          set({\n            isLoading: false,\n            error: (error as Error).message,\n          })\n          toast.error('Failed to fetch settings by category')\n        }\n      },\n\n      // Create setting\n      createSetting: async (settingData) => {\n        try {\n          await settingsApi.createSetting(settingData)\n\n          // Refresh settings\n          await get().fetchSettings()\n\n          toast.success('Setting Created', {\n            description: 'Setting has been created successfully.'\n          })\n        } catch (error) {\n          set({ error: (error as Error).message })\n          toast.error('Failed to create setting')\n          throw error\n        }\n      },\n\n      // Update setting\n      updateSetting: async (id, updateData) => {\n        try {\n          const response = await fetch('/api/settings', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            credentials: 'include',\n            body: JSON.stringify({ ...updateData, id }),\n          })\n\n          if (!response.ok) {\n            throw new Error('Failed to update setting')\n          }\n\n          // Refresh settings\n          await get().fetchSettings()\n          \n          toast.success('Setting Updated', {\n            description: 'Setting has been updated successfully.'\n          })\n        } catch (error) {\n          set({ error: (error as Error).message })\n          toast.error('Failed to update setting')\n          throw error\n        }\n      },\n\n      // Delete setting\n      deleteSetting: async (id) => {\n        try {\n          const response = await fetch(`/api/settings/${id}`, {\n            method: 'DELETE',\n            credentials: 'include',\n          })\n\n          if (!response.ok) {\n            throw new Error('Failed to delete setting')\n          }\n\n          // Remove from local state\n          const currentSettings = get().settings\n          const updatedSettings = currentSettings.filter(setting => setting.id !== id)\n          \n          // Regroup by category\n          const settingsByCategory = updatedSettings.reduce((acc: Record<string, Setting[]>, setting: Setting) => {\n            if (!acc[setting.category]) {\n              acc[setting.category] = []\n            }\n            acc[setting.category].push(setting)\n            return acc\n          }, {})\n\n          set({\n            settings: updatedSettings,\n            settingsByCategory,\n          })\n          \n          toast.success('Setting Deleted', {\n            description: 'Setting has been deleted successfully.'\n          })\n        } catch (error) {\n          set({ error: (error as Error).message })\n          toast.error('Failed to delete setting')\n          throw error\n        }\n      },\n\n      // Bulk update settings\n      bulkUpdateSettings: async (settings) => {\n        set({ isBulkUpdating: true })\n        try {\n          const data = await settingsApi.bulkUpdateSettings(settings)\n\n          // Refresh settings\n          await get().fetchSettings()\n\n          toast.success('Settings Updated', {\n            description: `${data.success || settings.length} settings updated successfully.`\n          })\n        } catch (error) {\n          set({ error: (error as Error).message })\n          toast.error('Failed to update settings')\n          throw error\n        } finally {\n          set({ isBulkUpdating: false })\n        }\n      },\n\n      // Utility actions\n      setFilters: (filters: Partial<SettingsFilters>) => {\n        set(state => ({\n          filters: { ...state.filters, ...filters }\n        }))\n      },\n\n      clearFilters: () => {\n        set({ filters: {} })\n      },\n\n      clearError: () => {\n        set({ error: null })\n      },\n\n      clearCurrentSetting: () => {\n        set({ currentSetting: null })\n      },\n\n      // Getters\n      getSettingValue: (key: string) => {\n        const setting = get().settings.find(s => s.key === key)\n        return setting ? setting.value : null\n      },\n\n      getSettingsByCategory: (category: SettingCategory) => {\n        return get().settingsByCategory[category] || []\n      },\n\n      getPublicSettings: () => {\n        return get().settings.filter(setting => setting.is_public)\n      },\n\n      // Clear upload error\n      clearUploadError: () => {\n        set({ uploadError: null })\n      },\n\n      // Platform settings are fetched through the main fetchSettings method\n\n      uploadPlatformLogo: async (file: File) => {\n        try {\n          set({ isUploadingLogo: true, uploadError: null })\n\n          const { platformSettingsAPI } = await import('@/lib/api/platform-settings')\n          const result = await platformSettingsAPI.uploadPlatformLogo(file)\n\n          if (result.success) {\n            // Refresh platform settings\n            await get().fetchSettings()\n            set({ isUploadingLogo: false })\n            return true\n          } else {\n            throw new Error(result.message || 'Failed to upload logo')\n          }\n        } catch (error) {\n          console.error('❌ Upload platform logo error:', error)\n          set({\n            uploadError: error instanceof Error ? error.message : 'Failed to upload logo',\n            isUploadingLogo: false\n          })\n          return false\n        }\n      },\n\n      uploadPlatformFavicon: async (file: File) => {\n        try {\n          set({ isUploadingFavicon: true, uploadError: null })\n\n          const { platformSettingsAPI } = await import('@/lib/api/platform-settings')\n          const result = await platformSettingsAPI.uploadPlatformFavicon(file)\n\n          if (result.success) {\n            // Refresh platform settings\n            await get().fetchSettings()\n            set({ isUploadingFavicon: false })\n            return true\n          } else {\n            throw new Error(result.message || 'Failed to upload favicon')\n          }\n        } catch (error) {\n          console.error('❌ Upload platform favicon error:', error)\n          set({\n            uploadError: error instanceof Error ? error.message : 'Failed to upload favicon',\n            isUploadingFavicon: false\n          })\n          return false\n        }\n      },\n\n      removePlatformLogo: async () => {\n        try {\n          set({ isRemovingLogo: true, uploadError: null })\n\n          const { platformSettingsAPI } = await import('@/lib/api/platform-settings')\n          const result = await platformSettingsAPI.removePlatformLogo()\n\n          if (result.success) {\n            // Refresh platform settings\n            await get().fetchSettings()\n            set({ isRemovingLogo: false })\n            return true\n          } else {\n            throw new Error(result.message || 'Failed to remove logo')\n          }\n        } catch (error) {\n          console.error('❌ Remove platform logo error:', error)\n          set({\n            uploadError: error instanceof Error ? error.message : 'Failed to remove logo',\n            isRemovingLogo: false\n          })\n          return false\n        }\n      },\n\n      removePlatformFavicon: async () => {\n        try {\n          set({ isRemovingFavicon: true, uploadError: null })\n\n          const { platformSettingsAPI } = await import('@/lib/api/platform-settings')\n          const result = await platformSettingsAPI.removePlatformFavicon()\n\n          if (result.success) {\n            // Refresh platform settings\n            await get().fetchSettings()\n            set({ isRemovingFavicon: false })\n            return true\n          } else {\n            throw new Error(result.message || 'Failed to remove favicon')\n          }\n        } catch (error) {\n          console.error('❌ Remove platform favicon error:', error)\n          set({\n            uploadError: error instanceof Error ? error.message : 'Failed to remove favicon',\n            isRemovingFavicon: false\n          })\n          return false\n        }\n      },\n\n      processFavicon: async (file: File) => {\n        try {\n          set({ isUploadingFavicon: true, uploadError: null })\n\n          const { platformSettingsAPI } = await import('@/lib/api/platform-settings')\n          const result = await platformSettingsAPI.processFavicon(file)\n\n          if (result.success) {\n            // Refresh platform settings\n            await get().fetchSettings()\n            set({ isUploadingFavicon: false })\n            return true\n          } else {\n            throw new Error(result.message || 'Failed to process favicon')\n          }\n        } catch (error) {\n          console.error('❌ Process favicon error:', error)\n          set({\n            uploadError: error instanceof Error ? error.message : 'Failed to process favicon',\n            isUploadingFavicon: false\n          })\n          return false\n        }\n      },\n\n    }),\n    {\n      name: 'settings-store',\n    }\n  )\n)\n\n// Helper hooks for specific setting categories\nexport const usePlatformSettings = () => {\n  const { settingsByCategory, fetchSettings } = useSettingsStore()\n  \n  return {\n    platformSettings: settingsByCategory.platform || [],\n    fetchPlatformSettings: () => fetchSettings({ category: 'platform' }),\n  }\n}\n\nexport const useSecuritySettings = () => {\n  const { settingsByCategory, fetchSettings } = useSettingsStore()\n  \n  return {\n    securitySettings: settingsByCategory.security || [],\n    fetchSecuritySettings: () => fetchSettings({ category: 'security' }),\n  }\n}\n\n// Helper function to get setting values\nexport const useSettingValue = (key: string): string | null => {\n  const getSettingValue = useSettingsStore(state => state.getSettingValue)\n  return getSettingValue(key)\n}\n\n// Helper function to check if a setting exists\nexport const useHasSetting = (key: string): boolean => {\n  const settings = useSettingsStore(state => state.settings)\n  return settings.some(setting => setting.key === key)\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AA8DO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,SAAM,AAAD,IACnC,CAAA,GAAA,0PAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,UAAU,EAAE;QACZ,oBAAoB,CAAC;QACrB,gBAAgB;QAChB,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAChB,mBAAmB;QACnB,OAAO;QACP,aAAa;QACb,SAAS;YACP,UAAU;YACV,OAAO;YACP,QAAQ;QACV;QAEA,iBAAiB;QACjB,eAAe,OAAO,UAAU,CAAC,CAAC;YAChC,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,iBAAiB;oBAAE,GAAG,MAAM,OAAO;oBAAE,GAAG,OAAO;gBAAC;gBACtD,MAAM,WAAW,MAAM,iJAAA,CAAA,cAAW,CAAC,WAAW,CAAC;gBAE/C,6BAA6B;gBAC7B,MAAM,qBAAqB,SAAS,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAgC;oBACnF,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;wBAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;oBAC5B;oBACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;oBAC3B,OAAO;gBACT,GAAG,CAAC;gBAEJ,IAAI;oBACF,UAAU,SAAS,QAAQ;oBAC3B;oBACA,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,WAAW;oBACX,OAAO,AAAC,MAAgB,OAAO;gBACjC;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,uBAAuB;QACvB,mBAAmB,OAAO;YACxB,IAAI;gBACF,MAAM,UAAU,MAAM,iJAAA,CAAA,cAAW,CAAC,eAAe,CAAC;gBAElD,yBAAyB;gBACzB,IAAI;oBAAE,gBAAgB;gBAAQ;gBAE9B,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;gBACtC,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,6BAA6B;QAC7B,yBAAyB,OAAO;YAC9B,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,iJAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC;gBAEzD,oCAAoC;gBACpC,IAAI,CAAA,QAAS,CAAC;wBACZ,oBAAoB;4BAClB,GAAG,MAAM,kBAAkB;4BAC3B,CAAC,SAAS,EAAE,SAAS,QAAQ;wBAC/B;wBACA,WAAW;wBACX,OAAO;oBACT,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,WAAW;oBACX,OAAO,AAAC,MAAgB,OAAO;gBACjC;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,iBAAiB;QACjB,eAAe,OAAO;YACpB,IAAI;gBACF,MAAM,iJAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAEhC,mBAAmB;gBACnB,MAAM,MAAM,aAAa;gBAEzB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;oBAC/B,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;gBACtC,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,iBAAiB;QACjB,eAAe,OAAO,IAAI;YACxB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;oBAC5C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,aAAa;oBACb,MAAM,KAAK,SAAS,CAAC;wBAAE,GAAG,UAAU;wBAAE;oBAAG;gBAC3C;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,mBAAmB;gBACnB,MAAM,MAAM,aAAa;gBAEzB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;oBAC/B,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;gBACtC,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,iBAAiB;QACjB,eAAe,OAAO;YACpB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;oBAClD,QAAQ;oBACR,aAAa;gBACf;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,0BAA0B;gBAC1B,MAAM,kBAAkB,MAAM,QAAQ;gBACtC,MAAM,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;gBAEzE,sBAAsB;gBACtB,MAAM,qBAAqB,gBAAgB,MAAM,CAAC,CAAC,KAAgC;oBACjF,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;wBAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;oBAC5B;oBACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;oBAC3B,OAAO;gBACT,GAAG,CAAC;gBAEJ,IAAI;oBACF,UAAU;oBACV;gBACF;gBAEA,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;oBAC/B,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;gBACtC,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,uBAAuB;QACvB,oBAAoB,OAAO;YACzB,IAAI;gBAAE,gBAAgB;YAAK;YAC3B,IAAI;gBACF,MAAM,OAAO,MAAM,iJAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;gBAElD,mBAAmB;gBACnB,MAAM,MAAM,aAAa;gBAEzB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC,oBAAoB;oBAChC,aAAa,GAAG,KAAK,OAAO,IAAI,SAAS,MAAM,CAAC,+BAA+B,CAAC;gBAClF;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;gBACtC,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,gBAAgB;gBAAM;YAC9B;QACF;QAEA,kBAAkB;QAClB,YAAY,CAAC;YACX,IAAI,CAAA,QAAS,CAAC;oBACZ,SAAS;wBAAE,GAAG,MAAM,OAAO;wBAAE,GAAG,OAAO;oBAAC;gBAC1C,CAAC;QACH;QAEA,cAAc;YACZ,IAAI;gBAAE,SAAS,CAAC;YAAE;QACpB;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,qBAAqB;YACnB,IAAI;gBAAE,gBAAgB;YAAK;QAC7B;QAEA,UAAU;QACV,iBAAiB,CAAC;YAChB,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;YACnD,OAAO,UAAU,QAAQ,KAAK,GAAG;QACnC;QAEA,uBAAuB,CAAC;YACtB,OAAO,MAAM,kBAAkB,CAAC,SAAS,IAAI,EAAE;QACjD;QAEA,mBAAmB;YACjB,OAAO,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS;QAC3D;QAEA,qBAAqB;QACrB,kBAAkB;YAChB,IAAI;gBAAE,aAAa;YAAK;QAC1B;QAEA,sEAAsE;QAEtE,oBAAoB,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,iBAAiB;oBAAM,aAAa;gBAAK;gBAE/C,MAAM,EAAE,mBAAmB,EAAE,GAAG;gBAChC,MAAM,SAAS,MAAM,oBAAoB,kBAAkB,CAAC;gBAE5D,IAAI,OAAO,OAAO,EAAE;oBAClB,4BAA4B;oBAC5B,MAAM,MAAM,aAAa;oBACzB,IAAI;wBAAE,iBAAiB;oBAAM;oBAC7B,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,IAAI;oBACF,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACtD,iBAAiB;gBACnB;gBACA,OAAO;YACT;QACF;QAEA,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,oBAAoB;oBAAM,aAAa;gBAAK;gBAElD,MAAM,EAAE,mBAAmB,EAAE,GAAG;gBAChC,MAAM,SAAS,MAAM,oBAAoB,qBAAqB,CAAC;gBAE/D,IAAI,OAAO,OAAO,EAAE;oBAClB,4BAA4B;oBAC5B,MAAM,MAAM,aAAa;oBACzB,IAAI;wBAAE,oBAAoB;oBAAM;oBAChC,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,IAAI;oBACF,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACtD,oBAAoB;gBACtB;gBACA,OAAO;YACT;QACF;QAEA,oBAAoB;YAClB,IAAI;gBACF,IAAI;oBAAE,gBAAgB;oBAAM,aAAa;gBAAK;gBAE9C,MAAM,EAAE,mBAAmB,EAAE,GAAG;gBAChC,MAAM,SAAS,MAAM,oBAAoB,kBAAkB;gBAE3D,IAAI,OAAO,OAAO,EAAE;oBAClB,4BAA4B;oBAC5B,MAAM,MAAM,aAAa;oBACzB,IAAI;wBAAE,gBAAgB;oBAAM;oBAC5B,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,IAAI;oBACF,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACtD,gBAAgB;gBAClB;gBACA,OAAO;YACT;QACF;QAEA,uBAAuB;YACrB,IAAI;gBACF,IAAI;oBAAE,mBAAmB;oBAAM,aAAa;gBAAK;gBAEjD,MAAM,EAAE,mBAAmB,EAAE,GAAG;gBAChC,MAAM,SAAS,MAAM,oBAAoB,qBAAqB;gBAE9D,IAAI,OAAO,OAAO,EAAE;oBAClB,4BAA4B;oBAC5B,MAAM,MAAM,aAAa;oBACzB,IAAI;wBAAE,mBAAmB;oBAAM;oBAC/B,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,IAAI;oBACF,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACtD,mBAAmB;gBACrB;gBACA,OAAO;YACT;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,oBAAoB;oBAAM,aAAa;gBAAK;gBAElD,MAAM,EAAE,mBAAmB,EAAE,GAAG;gBAChC,MAAM,SAAS,MAAM,oBAAoB,cAAc,CAAC;gBAExD,IAAI,OAAO,OAAO,EAAE;oBAClB,4BAA4B;oBAC5B,MAAM,MAAM,aAAa;oBACzB,IAAI;wBAAE,oBAAoB;oBAAM;oBAChC,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,IAAI;oBACF,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACtD,oBAAoB;gBACtB;gBACA,OAAO;YACT;QACF;IAEF,CAAC,GACD;IACE,MAAM;AACR;AAKG,MAAM,sBAAsB;IACjC,MAAM,EAAE,kBAAkB,EAAE,aAAa,EAAE,GAAG;IAE9C,OAAO;QACL,kBAAkB,mBAAmB,QAAQ,IAAI,EAAE;QACnD,uBAAuB,IAAM,cAAc;gBAAE,UAAU;YAAW;IACpE;AACF;AAEO,MAAM,sBAAsB;IACjC,MAAM,EAAE,kBAAkB,EAAE,aAAa,EAAE,GAAG;IAE9C,OAAO;QACL,kBAAkB,mBAAmB,QAAQ,IAAI,EAAE;QACnD,uBAAuB,IAAM,cAAc;gBAAE,UAAU;YAAW;IACpE;AACF;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,kBAAkB,iBAAiB,CAAA,QAAS,MAAM,eAAe;IACvE,OAAO,gBAAgB;AACzB;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,WAAW,iBAAiB,CAAA,QAAS,MAAM,QAAQ;IACzD,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,GAAG,KAAK;AAClD", "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {\n  value?: number\n  max?: number\n}\n\nconst Progress = React.forwardRef<HTMLDivElement, ProgressProps>(\n  ({ className, value = 0, max = 100, ...props }, ref) => {\n    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n          className\n        )}\n        {...props}\n      >\n        <div\n          className=\"h-full bg-primary transition-all duration-300 ease-in-out\"\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    )\n  }\n)\nProgress.displayName = \"Progress\"\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,yBAAW,CAAA,GAAA,kVAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,EAAE,GAAG,OAAO,EAAE;IAC9C,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,IAAI;IAE9D,qBACE,2XAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,2XAAC;YACC,WAAU;YACV,OAAO;gBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;YAAC;;;;;;;;;;;AAIzC;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,kVAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,2XAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,kVAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,2XAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,kVAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,2XAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/validation/file-upload.ts"], "sourcesContent": ["import * as Yup from 'yup'\n\n/**\n * File Upload Validation Schemas\n * Comprehensive validation for different file upload types\n */\n\n// File size constants (in bytes)\nexport const FILE_SIZE_LIMITS = {\n  AVATAR: 5 * 1024 * 1024, // 5MB\n  LOGO: 5 * 1024 * 1024, // 5MB\n  FAVICON: 2 * 1024 * 1024, // 2MB\n  DOCUMENT: 10 * 1024 * 1024, // 10MB\n  COURSE_THUMBNAIL: 10 * 1024 * 1024, // 10MB\n  GENERAL: 5 * 1024 * 1024, // 5MB\n} as const\n\n// Supported file types\nexport const SUPPORTED_FILE_TYPES = {\n  IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],\n  FAVICON: ['image/x-icon', 'image/png', 'image/gif', 'image/jpeg', 'image/jpg'],\n  DOCUMENTS: ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n  ALL_IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/x-icon'],\n} as const\n\n// File extension mappings\nexport const FILE_EXTENSIONS = {\n  IMAGES: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],\n  FAVICON: ['.ico', '.png', '.gif', '.jpg', '.jpeg'],\n  DOCUMENTS: ['.pdf', '.txt', '.doc', '.docx'],\n} as const\n\n/**\n * Custom Yup validation methods for files\n */\nexport const fileValidation = {\n  /**\n   * Validate file size\n   */\n  fileSize: (maxSize: number, message?: string) =>\n    Yup.mixed().test(\n      'fileSize',\n      message || `File size must be less than ${formatFileSize(maxSize)}`,\n      (value: any) => {\n        if (!value) return true // Allow empty files (handled by required validation)\n        if (value instanceof File) {\n          return value.size <= maxSize\n        }\n        return true\n      }\n    ),\n\n  /**\n   * Validate file type by MIME type\n   */\n  fileType: (allowedTypes: string[], message?: string) =>\n    Yup.mixed().test(\n      'fileType',\n      message || `File type not supported. Allowed types: ${allowedTypes.join(', ')}`,\n      (value: any) => {\n        if (!value) return true // Allow empty files (handled by required validation)\n        if (value instanceof File) {\n          return allowedTypes.includes(value.type)\n        }\n        return true\n      }\n    ),\n\n  /**\n   * Validate file extension\n   */\n  fileExtension: (allowedExtensions: string[], message?: string) =>\n    Yup.mixed().test(\n      'fileExtension',\n      message || `File extension not supported. Allowed extensions: ${allowedExtensions.join(', ')}`,\n      (value: any) => {\n        if (!value) return true // Allow empty files (handled by required validation)\n        if (value instanceof File) {\n          const extension = '.' + value.name.split('.').pop()?.toLowerCase()\n          return allowedExtensions.includes(extension)\n        }\n        return true\n      }\n    ),\n\n  /**\n   * Validate image dimensions (requires reading the file)\n   */\n  imageDimensions: (\n    minWidth?: number,\n    minHeight?: number,\n    maxWidth?: number,\n    maxHeight?: number,\n    message?: string\n  ) =>\n    Yup.mixed().test(\n      'imageDimensions',\n      message || 'Image dimensions are not valid',\n      async (value: any) => {\n        if (!value) return true // Allow empty files\n        if (!(value instanceof File)) return true\n        if (!value.type.startsWith('image/')) return true\n\n        return new Promise((resolve) => {\n          const img = new Image()\n          const url = URL.createObjectURL(value)\n\n          img.onload = () => {\n            URL.revokeObjectURL(url)\n            \n            let valid = true\n            if (minWidth && img.width < minWidth) valid = false\n            if (minHeight && img.height < minHeight) valid = false\n            if (maxWidth && img.width > maxWidth) valid = false\n            if (maxHeight && img.height > maxHeight) valid = false\n            \n            resolve(valid)\n          }\n\n          img.onerror = () => {\n            URL.revokeObjectURL(url)\n            resolve(false)\n          }\n\n          img.src = url\n        })\n      }\n    ),\n\n  /**\n   * Validate aspect ratio\n   */\n  aspectRatio: (expectedRatio: number, tolerance: number = 0.1, message?: string) =>\n    Yup.mixed().test(\n      'aspectRatio',\n      message || `Image aspect ratio should be approximately ${expectedRatio}:1`,\n      async (value: any) => {\n        if (!value) return true // Allow empty files\n        if (!(value instanceof File)) return true\n        if (!value.type.startsWith('image/')) return true\n\n        return new Promise((resolve) => {\n          const img = new Image()\n          const url = URL.createObjectURL(value)\n\n          img.onload = () => {\n            URL.revokeObjectURL(url)\n            const actualRatio = img.width / img.height\n            const diff = Math.abs(actualRatio - expectedRatio)\n            resolve(diff <= tolerance)\n          }\n\n          img.onerror = () => {\n            URL.revokeObjectURL(url)\n            resolve(false)\n          }\n\n          img.src = url\n        })\n      }\n    ),\n}\n\n/**\n * Pre-built validation schemas for common upload types\n */\nexport const uploadValidationSchemas = {\n  /**\n   * Avatar upload validation\n   */\n  avatar: Yup.object({\n    file: Yup.mixed()\n      .required('Avatar image is required')\n      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.AVATAR))\n      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.IMAGES))\n      .concat(fileValidation.imageDimensions(50, 50, 2048, 2048, 'Avatar should be between 50x50 and 2048x2048 pixels'))\n  }),\n\n  /**\n   * Logo upload validation\n   */\n  logo: Yup.object({\n    file: Yup.mixed()\n      .required('Logo image is required')\n      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.LOGO))\n      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.IMAGES))\n      .concat(fileValidation.imageDimensions(100, 50, 2048, 1024, 'Logo should be between 100x50 and 2048x1024 pixels'))\n  }),\n\n  /**\n   * Favicon upload validation\n   */\n  favicon: Yup.object({\n    file: Yup.mixed()\n      .required('Favicon is required')\n      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.FAVICON))\n      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.FAVICON))\n      .concat(fileValidation.imageDimensions(16, 16, 512, 512, 'Favicon should be between 16x16 and 512x512 pixels'))\n  }),\n\n  /**\n   * Course thumbnail validation\n   */\n  courseThumbnail: Yup.object({\n    file: Yup.mixed()\n      .required('Course thumbnail is required')\n      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.COURSE_THUMBNAIL))\n      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.IMAGES))\n      .concat(fileValidation.imageDimensions(300, 200, 1920, 1080, 'Thumbnail should be between 300x200 and 1920x1080 pixels'))\n      .concat(fileValidation.aspectRatio(16/9, 0.2, 'Thumbnail should have approximately 16:9 aspect ratio'))\n  }),\n\n  /**\n   * Document upload validation\n   */\n  document: Yup.object({\n    file: Yup.mixed()\n      .required('Document is required')\n      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.DOCUMENT))\n      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.DOCUMENTS))\n  }),\n\n  /**\n   * General image upload validation\n   */\n  image: Yup.object({\n    file: Yup.mixed()\n      .required('Image is required')\n      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.GENERAL))\n      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.IMAGES))\n  }),\n}\n\n/**\n * Utility functions\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nexport function getFileExtension(filename: string): string {\n  return '.' + filename.split('.').pop()?.toLowerCase() || ''\n}\n\nexport function isImageFile(file: File): boolean {\n  return SUPPORTED_FILE_TYPES.ALL_IMAGES.includes(file.type)\n}\n\nexport function validateFileClientSide(\n  file: File,\n  options: {\n    maxSize?: number\n    allowedTypes?: string[]\n    allowedExtensions?: string[]\n  } = {}\n): { valid: boolean; errors: string[] } {\n  const errors: string[] = []\n\n  // Check file size\n  if (options.maxSize && file.size > options.maxSize) {\n    errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(options.maxSize)})`)\n  }\n\n  // Check file type\n  if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {\n    errors.push(`File type (${file.type}) is not allowed. Allowed types: ${options.allowedTypes.join(', ')}`)\n  }\n\n  // Check file extension\n  if (options.allowedExtensions) {\n    const extension = getFileExtension(file.name)\n    if (!options.allowedExtensions.includes(extension)) {\n      errors.push(`File extension (${extension}) is not allowed. Allowed extensions: ${options.allowedExtensions.join(', ')}`)\n    }\n  }\n\n  return {\n    valid: errors.length === 0,\n    errors\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAQO,MAAM,mBAAmB;IAC9B,QAAQ,IAAI,OAAO;IACnB,MAAM,IAAI,OAAO;IACjB,SAAS,IAAI,OAAO;IACpB,UAAU,KAAK,OAAO;IACtB,kBAAkB,KAAK,OAAO;IAC9B,SAAS,IAAI,OAAO;AACtB;AAGO,MAAM,uBAAuB;IAClC,QAAQ;QAAC;QAAc;QAAa;QAAa;QAAa;QAAc;KAAgB;IAC5F,SAAS;QAAC;QAAgB;QAAa;QAAa;QAAc;KAAY;IAC9E,WAAW;QAAC;QAAmB;QAAc;QAAsB;KAA0E;IAC7I,YAAY;QAAC;QAAc;QAAa;QAAa;QAAa;QAAc;QAAiB;KAAe;AAClH;AAGO,MAAM,kBAAkB;IAC7B,QAAQ;QAAC;QAAQ;QAAS;QAAQ;QAAQ;QAAS;KAAO;IAC1D,SAAS;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IAClD,WAAW;QAAC;QAAQ;QAAQ;QAAQ;KAAQ;AAC9C;AAKO,MAAM,iBAAiB;IAC5B;;GAEC,GACD,UAAU,CAAC,SAAiB,UAC1B,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IAAI,IAAI,CACd,YACA,WAAW,CAAC,4BAA4B,EAAE,eAAe,UAAU,EACnE,CAAC;YACC,IAAI,CAAC,OAAO,OAAO,KAAK,qDAAqD;;YAC7E,IAAI,iBAAiB,MAAM;gBACzB,OAAO,MAAM,IAAI,IAAI;YACvB;YACA,OAAO;QACT;IAGJ;;GAEC,GACD,UAAU,CAAC,cAAwB,UACjC,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IAAI,IAAI,CACd,YACA,WAAW,CAAC,wCAAwC,EAAE,aAAa,IAAI,CAAC,OAAO,EAC/E,CAAC;YACC,IAAI,CAAC,OAAO,OAAO,KAAK,qDAAqD;;YAC7E,IAAI,iBAAiB,MAAM;gBACzB,OAAO,aAAa,QAAQ,CAAC,MAAM,IAAI;YACzC;YACA,OAAO;QACT;IAGJ;;GAEC,GACD,eAAe,CAAC,mBAA6B,UAC3C,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IAAI,IAAI,CACd,iBACA,WAAW,CAAC,kDAAkD,EAAE,kBAAkB,IAAI,CAAC,OAAO,EAC9F,CAAC;YACC,IAAI,CAAC,OAAO,OAAO,KAAK,qDAAqD;;YAC7E,IAAI,iBAAiB,MAAM;gBACzB,MAAM,YAAY,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;gBACrD,OAAO,kBAAkB,QAAQ,CAAC;YACpC;YACA,OAAO;QACT;IAGJ;;GAEC,GACD,iBAAiB,CACf,UACA,WACA,UACA,WACA,UAEA,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IAAI,IAAI,CACd,mBACA,WAAW,kCACX,OAAO;YACL,IAAI,CAAC,OAAO,OAAO,KAAK,oBAAoB;;YAC5C,IAAI,CAAC,CAAC,iBAAiB,IAAI,GAAG,OAAO;YACrC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,OAAO;YAE7C,OAAO,IAAI,QAAQ,CAAC;gBAClB,MAAM,MAAM,IAAI;gBAChB,MAAM,MAAM,IAAI,eAAe,CAAC;gBAEhC,IAAI,MAAM,GAAG;oBACX,IAAI,eAAe,CAAC;oBAEpB,IAAI,QAAQ;oBACZ,IAAI,YAAY,IAAI,KAAK,GAAG,UAAU,QAAQ;oBAC9C,IAAI,aAAa,IAAI,MAAM,GAAG,WAAW,QAAQ;oBACjD,IAAI,YAAY,IAAI,KAAK,GAAG,UAAU,QAAQ;oBAC9C,IAAI,aAAa,IAAI,MAAM,GAAG,WAAW,QAAQ;oBAEjD,QAAQ;gBACV;gBAEA,IAAI,OAAO,GAAG;oBACZ,IAAI,eAAe,CAAC;oBACpB,QAAQ;gBACV;gBAEA,IAAI,GAAG,GAAG;YACZ;QACF;IAGJ;;GAEC,GACD,aAAa,CAAC,eAAuB,YAAoB,GAAG,EAAE,UAC5D,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IAAI,IAAI,CACd,eACA,WAAW,CAAC,2CAA2C,EAAE,cAAc,EAAE,CAAC,EAC1E,OAAO;YACL,IAAI,CAAC,OAAO,OAAO,KAAK,oBAAoB;;YAC5C,IAAI,CAAC,CAAC,iBAAiB,IAAI,GAAG,OAAO;YACrC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,OAAO;YAE7C,OAAO,IAAI,QAAQ,CAAC;gBAClB,MAAM,MAAM,IAAI;gBAChB,MAAM,MAAM,IAAI,eAAe,CAAC;gBAEhC,IAAI,MAAM,GAAG;oBACX,IAAI,eAAe,CAAC;oBACpB,MAAM,cAAc,IAAI,KAAK,GAAG,IAAI,MAAM;oBAC1C,MAAM,OAAO,KAAK,GAAG,CAAC,cAAc;oBACpC,QAAQ,QAAQ;gBAClB;gBAEA,IAAI,OAAO,GAAG;oBACZ,IAAI,eAAe,CAAC;oBACpB,QAAQ;gBACV;gBAEA,IAAI,GAAG,GAAG;YACZ;QACF;AAEN;AAKO,MAAM,0BAA0B;IACrC;;GAEC,GACD,QAAQ,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;QACjB,MAAM,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IACX,QAAQ,CAAC,4BACT,MAAM,CAAC,eAAe,QAAQ,CAAC,iBAAiB,MAAM,GACtD,MAAM,CAAC,eAAe,QAAQ,CAAC,qBAAqB,MAAM,GAC1D,MAAM,CAAC,eAAe,eAAe,CAAC,IAAI,IAAI,MAAM,MAAM;IAC/D;IAEA;;GAEC,GACD,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;QACf,MAAM,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IACX,QAAQ,CAAC,0BACT,MAAM,CAAC,eAAe,QAAQ,CAAC,iBAAiB,IAAI,GACpD,MAAM,CAAC,eAAe,QAAQ,CAAC,qBAAqB,MAAM,GAC1D,MAAM,CAAC,eAAe,eAAe,CAAC,KAAK,IAAI,MAAM,MAAM;IAChE;IAEA;;GAEC,GACD,SAAS,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;QAClB,MAAM,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IACX,QAAQ,CAAC,uBACT,MAAM,CAAC,eAAe,QAAQ,CAAC,iBAAiB,OAAO,GACvD,MAAM,CAAC,eAAe,QAAQ,CAAC,qBAAqB,OAAO,GAC3D,MAAM,CAAC,eAAe,eAAe,CAAC,IAAI,IAAI,KAAK,KAAK;IAC7D;IAEA;;GAEC,GACD,iBAAiB,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;QAC1B,MAAM,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IACX,QAAQ,CAAC,gCACT,MAAM,CAAC,eAAe,QAAQ,CAAC,iBAAiB,gBAAgB,GAChE,MAAM,CAAC,eAAe,QAAQ,CAAC,qBAAqB,MAAM,GAC1D,MAAM,CAAC,eAAe,eAAe,CAAC,KAAK,KAAK,MAAM,MAAM,6DAC5D,MAAM,CAAC,eAAe,WAAW,CAAC,KAAG,GAAG,KAAK;IAClD;IAEA;;GAEC,GACD,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;QACnB,MAAM,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IACX,QAAQ,CAAC,wBACT,MAAM,CAAC,eAAe,QAAQ,CAAC,iBAAiB,QAAQ,GACxD,MAAM,CAAC,eAAe,QAAQ,CAAC,qBAAqB,SAAS;IAClE;IAEA;;GAEC,GACD,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;QAChB,MAAM,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IACX,QAAQ,CAAC,qBACT,MAAM,CAAC,eAAe,QAAQ,CAAC,iBAAiB,OAAO,GACvD,MAAM,CAAC,eAAe,QAAQ,CAAC,qBAAqB,MAAM;IAC/D;AACF;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,MAAM,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;AAC3D;AAEO,SAAS,YAAY,IAAU;IACpC,OAAO,qBAAqB,UAAU,CAAC,QAAQ,CAAC,KAAK,IAAI;AAC3D;AAEO,SAAS,uBACd,IAAU,EACV,UAII,CAAC,CAAC;IAEN,MAAM,SAAmB,EAAE;IAE3B,kBAAkB;IAClB,IAAI,QAAQ,OAAO,IAAI,KAAK,IAAI,GAAG,QAAQ,OAAO,EAAE;QAClD,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,eAAe,KAAK,IAAI,EAAE,gCAAgC,EAAE,eAAe,QAAQ,OAAO,EAAE,CAAC,CAAC;IAC1H;IAEA,kBAAkB;IAClB,IAAI,QAAQ,YAAY,IAAI,CAAC,QAAQ,YAAY,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrE,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,iCAAiC,EAAE,QAAQ,YAAY,CAAC,IAAI,CAAC,OAAO;IAC1G;IAEA,uBAAuB;IACvB,IAAI,QAAQ,iBAAiB,EAAE;QAC7B,MAAM,YAAY,iBAAiB,KAAK,IAAI;QAC5C,IAAI,CAAC,QAAQ,iBAAiB,CAAC,QAAQ,CAAC,YAAY;YAClD,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,UAAU,sCAAsC,EAAE,QAAQ,iBAAiB,CAAC,IAAI,CAAC,OAAO;QACzH;IACF;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/error-display.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  AlertCircle, \n  AlertTriangle, \n  XCircle, \n  Info, \n  RefreshCw, \n  X,\n  FileX,\n  Upload,\n  Wifi,\n  Server\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport type ErrorType = 'validation' | 'network' | 'server' | 'upload' | 'permission' | 'general'\nexport type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical'\n\nexport interface ErrorInfo {\n  type: ErrorType\n  severity: ErrorSeverity\n  message: string\n  details?: string\n  code?: string | number\n  field?: string\n  timestamp?: Date\n  retryable?: boolean\n}\n\ninterface ErrorDisplayProps {\n  error: ErrorInfo | string | null\n  onRetry?: () => void\n  onDismiss?: () => void\n  className?: string\n  compact?: boolean\n  showTimestamp?: boolean\n  showCode?: boolean\n}\n\nexport function ErrorDisplay({\n  error,\n  onRetry,\n  onDismiss,\n  className,\n  compact = false,\n  showTimestamp = false,\n  showCode = false\n}: ErrorDisplayProps) {\n  if (!error) return null\n\n  // Normalize error to ErrorInfo object\n  const errorInfo: ErrorInfo = typeof error === 'string' \n    ? { type: 'general', severity: 'medium', message: error }\n    : error\n\n  const getErrorIcon = () => {\n    switch (errorInfo.type) {\n      case 'validation':\n        return <AlertCircle className=\"h-4 w-4\" />\n      case 'network':\n        return <Wifi className=\"h-4 w-4\" />\n      case 'server':\n        return <Server className=\"h-4 w-4\" />\n      case 'upload':\n        return <FileX className=\"h-4 w-4\" />\n      case 'permission':\n        return <XCircle className=\"h-4 w-4\" />\n      default:\n        return <AlertTriangle className=\"h-4 w-4\" />\n    }\n  }\n\n  const getErrorVariant = () => {\n    switch (errorInfo.severity) {\n      case 'critical':\n      case 'high':\n        return 'destructive'\n      case 'medium':\n        return 'default'\n      case 'low':\n        return 'default'\n      default:\n        return 'default'\n    }\n  }\n\n  const getErrorColor = () => {\n    switch (errorInfo.severity) {\n      case 'critical':\n        return 'text-red-600 border-red-200 bg-red-50'\n      case 'high':\n        return 'text-red-500 border-red-200 bg-red-50'\n      case 'medium':\n        return 'text-orange-600 border-orange-200 bg-orange-50'\n      case 'low':\n        return 'text-yellow-600 border-yellow-200 bg-yellow-50'\n      default:\n        return 'text-gray-600 border-gray-200 bg-gray-50'\n    }\n  }\n\n  const getSeverityBadge = () => {\n    const colors = {\n      critical: 'bg-red-100 text-red-800',\n      high: 'bg-red-100 text-red-700',\n      medium: 'bg-orange-100 text-orange-700',\n      low: 'bg-yellow-100 text-yellow-700'\n    }\n\n    return (\n      <Badge variant=\"secondary\" className={colors[errorInfo.severity]}>\n        {errorInfo.severity.toUpperCase()}\n      </Badge>\n    )\n  }\n\n  if (compact) {\n    return (\n      <Alert variant={getErrorVariant()} className={cn('', className)}>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            {getErrorIcon()}\n            <AlertDescription className=\"mb-0\">\n              {errorInfo.message}\n            </AlertDescription>\n          </div>\n          \n          <div className=\"flex items-center space-x-1\">\n            {errorInfo.retryable && onRetry && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={onRetry}\n                className=\"h-6 w-6 p-0\"\n              >\n                <RefreshCw className=\"h-3 w-3\" />\n              </Button>\n            )}\n            {onDismiss && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={onDismiss}\n                className=\"h-6 w-6 p-0\"\n              >\n                <X className=\"h-3 w-3\" />\n              </Button>\n            )}\n          </div>\n        </div>\n      </Alert>\n    )\n  }\n\n  return (\n    <Card className={cn('border-l-4', getErrorColor(), className)}>\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            {getErrorIcon()}\n            <CardTitle className=\"text-sm font-medium\">\n              {errorInfo.type.charAt(0).toUpperCase() + errorInfo.type.slice(1)} Error\n            </CardTitle>\n            {getSeverityBadge()}\n          </div>\n          \n          {onDismiss && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onDismiss}\n              className=\"h-6 w-6 p-0\"\n            >\n              <X className=\"h-3 w-3\" />\n            </Button>\n          )}\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"pt-0 space-y-3\">\n        <div>\n          <p className=\"text-sm text-gray-900 font-medium\">\n            {errorInfo.message}\n          </p>\n          \n          {errorInfo.details && (\n            <p className=\"text-xs text-gray-600 mt-1\">\n              {errorInfo.details}\n            </p>\n          )}\n        </div>\n\n        {/* Additional info */}\n        {(showCode || showTimestamp || errorInfo.field) && (\n          <div className=\"flex flex-wrap gap-2 text-xs text-gray-500\">\n            {errorInfo.field && (\n              <span className=\"bg-gray-100 px-2 py-1 rounded\">\n                Field: {errorInfo.field}\n              </span>\n            )}\n            \n            {showCode && errorInfo.code && (\n              <span className=\"bg-gray-100 px-2 py-1 rounded\">\n                Code: {errorInfo.code}\n              </span>\n            )}\n            \n            {showTimestamp && errorInfo.timestamp && (\n              <span className=\"bg-gray-100 px-2 py-1 rounded\">\n                {errorInfo.timestamp.toLocaleTimeString()}\n              </span>\n            )}\n          </div>\n        )}\n\n        {/* Actions */}\n        {(errorInfo.retryable && onRetry) && (\n          <div className=\"flex space-x-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={onRetry}\n              className=\"h-8\"\n            >\n              <RefreshCw className=\"h-3 w-3 mr-1\" />\n              Try Again\n            </Button>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n\n// Specialized error display components\nexport function ValidationErrorDisplay({ \n  errors, \n  onDismiss,\n  className \n}: { \n  errors: string[] | Record<string, string>\n  onDismiss?: () => void\n  className?: string \n}) {\n  if (!errors || (Array.isArray(errors) && errors.length === 0) || \n      (typeof errors === 'object' && Object.keys(errors).length === 0)) {\n    return null\n  }\n\n  const errorList = Array.isArray(errors) \n    ? errors \n    : Object.entries(errors).map(([field, message]) => `${field}: ${message}`)\n\n  return (\n    <Alert variant=\"destructive\" className={className}>\n      <AlertCircle className=\"h-4 w-4\" />\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex-1\">\n          <AlertTitle>Validation Errors</AlertTitle>\n          <AlertDescription>\n            <ul className=\"list-disc list-inside space-y-1 mt-2\">\n              {errorList.map((error, index) => (\n                <li key={index} className=\"text-sm\">{error}</li>\n              ))}\n            </ul>\n          </AlertDescription>\n        </div>\n        {onDismiss && (\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onDismiss}\n            className=\"h-6 w-6 p-0 ml-2\"\n          >\n            <X className=\"h-3 w-3\" />\n          </Button>\n        )}\n      </div>\n    </Alert>\n  )\n}\n\nexport function NetworkErrorDisplay({ \n  onRetry, \n  className \n}: { \n  onRetry?: () => void\n  className?: string \n}) {\n  return (\n    <ErrorDisplay\n      error={{\n        type: 'network',\n        severity: 'high',\n        message: 'Network connection failed',\n        details: 'Please check your internet connection and try again.',\n        retryable: true\n      }}\n      onRetry={onRetry}\n      className={className}\n    />\n  )\n}\n\nexport function UploadErrorDisplay({ \n  message, \n  onRetry, \n  onDismiss,\n  className \n}: { \n  message: string\n  onRetry?: () => void\n  onDismiss?: () => void\n  className?: string \n}) {\n  return (\n    <ErrorDisplay\n      error={{\n        type: 'upload',\n        severity: 'medium',\n        message,\n        retryable: !!onRetry\n      }}\n      onRetry={onRetry}\n      onDismiss={onDismiss}\n      className={className}\n    />\n  )\n}\n\n// Utility function to create error info objects\nexport function createErrorInfo(\n  type: ErrorType,\n  message: string,\n  options: Partial<Omit<ErrorInfo, 'type' | 'message'>> = {}\n): ErrorInfo {\n  return {\n    type,\n    message,\n    severity: 'medium',\n    timestamp: new Date(),\n    retryable: type === 'network' || type === 'server',\n    ...options\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAnBA;;;;;;;;AA6CO,SAAS,aAAa,EAC3B,KAAK,EACL,OAAO,EACP,SAAS,EACT,SAAS,EACT,UAAU,KAAK,EACf,gBAAgB,KAAK,EACrB,WAAW,KAAK,EACE;IAClB,IAAI,CAAC,OAAO,OAAO;IAEnB,sCAAsC;IACtC,MAAM,YAAuB,OAAO,UAAU,WAC1C;QAAE,MAAM;QAAW,UAAU;QAAU,SAAS;IAAM,IACtD;IAEJ,MAAM,eAAe;QACnB,OAAQ,UAAU,IAAI;YACpB,KAAK;gBACH,qBAAO,2XAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,2XAAC,sRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,2XAAC,0RAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,2XAAC,4RAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,2XAAC,gSAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,2XAAC,4SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ,UAAU,QAAQ;YACxB,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ,UAAU,QAAQ;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,SAAS;YACb,UAAU;YACV,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QAEA,qBACE,2XAAC,qJAAA,CAAA,QAAK;YAAC,SAAQ;YAAY,WAAW,MAAM,CAAC,UAAU,QAAQ,CAAC;sBAC7D,UAAU,QAAQ,CAAC,WAAW;;;;;;IAGrC;IAEA,IAAI,SAAS;QACX,qBACE,2XAAC,qJAAA,CAAA,QAAK;YAAC,SAAS;YAAmB,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,IAAI;sBACnD,cAAA,2XAAC;gBAAI,WAAU;;kCACb,2XAAC;wBAAI,WAAU;;4BACZ;0CACD,2XAAC,qJAAA,CAAA,mBAAgB;gCAAC,WAAU;0CACzB,UAAU,OAAO;;;;;;;;;;;;kCAItB,2XAAC;wBAAI,WAAU;;4BACZ,UAAU,SAAS,IAAI,yBACtB,2XAAC,sJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,2XAAC,oSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;4BAGxB,2BACC,2XAAC,sJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,2XAAC,gRAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO3B;IAEA,qBACE,2XAAC,oJAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,cAAc,iBAAiB;;0BACjD,2XAAC,oJAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,2XAAC;oBAAI,WAAU;;sCACb,2XAAC;4BAAI,WAAU;;gCACZ;8CACD,2XAAC,oJAAA,CAAA,YAAS;oCAAC,WAAU;;wCAClB,UAAU,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,IAAI,CAAC,KAAK,CAAC;wCAAG;;;;;;;gCAEnE;;;;;;;wBAGF,2BACC,2XAAC,sJAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,2XAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMrB,2XAAC,oJAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,2XAAC;;0CACC,2XAAC;gCAAE,WAAU;0CACV,UAAU,OAAO;;;;;;4BAGnB,UAAU,OAAO,kBAChB,2XAAC;gCAAE,WAAU;0CACV,UAAU,OAAO;;;;;;;;;;;;oBAMvB,CAAC,YAAY,iBAAiB,UAAU,KAAK,mBAC5C,2XAAC;wBAAI,WAAU;;4BACZ,UAAU,KAAK,kBACd,2XAAC;gCAAK,WAAU;;oCAAgC;oCACtC,UAAU,KAAK;;;;;;;4BAI1B,YAAY,UAAU,IAAI,kBACzB,2XAAC;gCAAK,WAAU;;oCAAgC;oCACvC,UAAU,IAAI;;;;;;;4BAIxB,iBAAiB,UAAU,SAAS,kBACnC,2XAAC;gCAAK,WAAU;0CACb,UAAU,SAAS,CAAC,kBAAkB;;;;;;;;;;;;oBAO7C,UAAU,SAAS,IAAI,yBACvB,2XAAC;wBAAI,WAAU;kCACb,cAAA,2XAAC,sJAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,2XAAC,oSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;AAGO,SAAS,uBAAuB,EACrC,MAAM,EACN,SAAS,EACT,SAAS,EAKV;IACC,IAAI,CAAC,UAAW,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,KAAK,KACtD,OAAO,WAAW,YAAY,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAI;QACpE,OAAO;IACT;IAEA,MAAM,YAAY,MAAM,OAAO,CAAC,UAC5B,SACA,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,OAAO,QAAQ,GAAK,GAAG,MAAM,EAAE,EAAE,SAAS;IAE3E,qBACE,2XAAC,qJAAA,CAAA,QAAK;QAAC,SAAQ;QAAc,WAAW;;0BACtC,2XAAC,wSAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,2XAAC;gBAAI,WAAU;;kCACb,2XAAC;wBAAI,WAAU;;0CACb,2XAAC,qJAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,2XAAC,qJAAA,CAAA,mBAAgB;0CACf,cAAA,2XAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,2XAAC;4CAAe,WAAU;sDAAW;2CAA5B;;;;;;;;;;;;;;;;;;;;;oBAKhB,2BACC,2XAAC,sJAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCAEV,cAAA,2XAAC,gRAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMzB;AAEO,SAAS,oBAAoB,EAClC,OAAO,EACP,SAAS,EAIV;IACC,qBACE,2XAAC;QACC,OAAO;YACL,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;YACT,WAAW;QACb;QACA,SAAS;QACT,WAAW;;;;;;AAGjB;AAEO,SAAS,mBAAmB,EACjC,OAAO,EACP,OAAO,EACP,SAAS,EACT,SAAS,EAMV;IACC,qBACE,2XAAC;QACC,OAAO;YACL,MAAM;YACN,UAAU;YACV;YACA,WAAW,CAAC,CAAC;QACf;QACA,SAAS;QACT,WAAW;QACX,WAAW;;;;;;AAGjB;AAGO,SAAS,gBACd,IAAe,EACf,OAAe,EACf,UAAwD,CAAC,CAAC;IAE1D,OAAO;QACL;QACA;QACA,UAAU;QACV,WAAW,IAAI;QACf,WAAW,SAAS,aAAa,SAAS;QAC1C,GAAG,OAAO;IACZ;AACF", "debugId": null}}, {"offset": {"line": 1534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/utils/upload-progress.ts"], "sourcesContent": ["/**\n * Upload Progress Tracking Utilities\n * Comprehensive progress tracking for file uploads\n */\n\nexport interface UploadProgress {\n  id: string\n  filename: string\n  filesize: number\n  loaded: number\n  total: number\n  percentage: number\n  speed: number // bytes per second\n  timeRemaining: number // seconds\n  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error' | 'cancelled'\n  error?: string\n  startTime: number\n  endTime?: number\n}\n\nexport interface UploadProgressOptions {\n  onProgress?: (progress: UploadProgress) => void\n  onComplete?: (progress: UploadProgress) => void\n  onError?: (progress: UploadProgress, error: Error) => void\n  onCancel?: (progress: UploadProgress) => void\n}\n\nclass UploadProgressTracker {\n  private uploads = new Map<string, UploadProgress>()\n  private intervals = new Map<string, NodeJS.Timeout>()\n\n  /**\n   * Start tracking upload progress\n   */\n  startTracking(\n    id: string,\n    file: File,\n    options: UploadProgressOptions = {}\n  ): UploadProgress {\n    const progress: UploadProgress = {\n      id,\n      filename: file.name,\n      filesize: file.size,\n      loaded: 0,\n      total: file.size,\n      percentage: 0,\n      speed: 0,\n      timeRemaining: 0,\n      status: 'pending',\n      startTime: Date.now()\n    }\n\n    this.uploads.set(id, progress)\n\n    // Start progress monitoring\n    const interval = setInterval(() => {\n      const currentProgress = this.uploads.get(id)\n      if (currentProgress && currentProgress.status === 'uploading') {\n        this.calculateSpeed(id)\n        options.onProgress?.(currentProgress)\n      }\n    }, 1000)\n\n    this.intervals.set(id, interval)\n\n    return progress\n  }\n\n  /**\n   * Update upload progress\n   */\n  updateProgress(\n    id: string,\n    loaded: number,\n    options: UploadProgressOptions = {}\n  ): UploadProgress | null {\n    const progress = this.uploads.get(id)\n    if (!progress) return null\n\n    const now = Date.now()\n    const elapsed = (now - progress.startTime) / 1000 // seconds\n    \n    progress.loaded = loaded\n    progress.percentage = Math.round((loaded / progress.total) * 100)\n    progress.status = loaded >= progress.total ? 'processing' : 'uploading'\n\n    // Calculate speed (bytes per second)\n    if (elapsed > 0) {\n      progress.speed = loaded / elapsed\n    }\n\n    // Calculate time remaining\n    if (progress.speed > 0) {\n      const remaining = progress.total - loaded\n      progress.timeRemaining = remaining / progress.speed\n    }\n\n    this.uploads.set(id, progress)\n    options.onProgress?.(progress)\n\n    return progress\n  }\n\n  /**\n   * Mark upload as completed\n   */\n  completeUpload(\n    id: string,\n    options: UploadProgressOptions = {}\n  ): UploadProgress | null {\n    const progress = this.uploads.get(id)\n    if (!progress) return null\n\n    progress.status = 'completed'\n    progress.endTime = Date.now()\n    progress.percentage = 100\n    progress.loaded = progress.total\n\n    this.clearInterval(id)\n    this.uploads.set(id, progress)\n    \n    options.onComplete?.(progress)\n    return progress\n  }\n\n  /**\n   * Mark upload as failed\n   */\n  failUpload(\n    id: string,\n    error: Error,\n    options: UploadProgressOptions = {}\n  ): UploadProgress | null {\n    const progress = this.uploads.get(id)\n    if (!progress) return null\n\n    progress.status = 'error'\n    progress.error = error.message\n    progress.endTime = Date.now()\n\n    this.clearInterval(id)\n    this.uploads.set(id, progress)\n    \n    options.onError?.(progress, error)\n    return progress\n  }\n\n  /**\n   * Cancel upload\n   */\n  cancelUpload(\n    id: string,\n    options: UploadProgressOptions = {}\n  ): UploadProgress | null {\n    const progress = this.uploads.get(id)\n    if (!progress) return null\n\n    progress.status = 'cancelled'\n    progress.endTime = Date.now()\n\n    this.clearInterval(id)\n    this.uploads.set(id, progress)\n    \n    options.onCancel?.(progress)\n    return progress\n  }\n\n  /**\n   * Get upload progress by ID\n   */\n  getProgress(id: string): UploadProgress | null {\n    return this.uploads.get(id) || null\n  }\n\n  /**\n   * Get all upload progress\n   */\n  getAllProgress(): UploadProgress[] {\n    return Array.from(this.uploads.values())\n  }\n\n  /**\n   * Remove upload from tracking\n   */\n  removeUpload(id: string): void {\n    this.clearInterval(id)\n    this.uploads.delete(id)\n  }\n\n  /**\n   * Clear all uploads\n   */\n  clearAll(): void {\n    this.intervals.forEach((interval) => clearInterval(interval))\n    this.intervals.clear()\n    this.uploads.clear()\n  }\n\n  /**\n   * Calculate upload speed\n   */\n  private calculateSpeed(id: string): void {\n    const progress = this.uploads.get(id)\n    if (!progress) return\n\n    const now = Date.now()\n    const elapsed = (now - progress.startTime) / 1000\n\n    if (elapsed > 0) {\n      progress.speed = progress.loaded / elapsed\n      \n      if (progress.speed > 0) {\n        const remaining = progress.total - progress.loaded\n        progress.timeRemaining = remaining / progress.speed\n      }\n    }\n  }\n\n  /**\n   * Clear interval for upload\n   */\n  private clearInterval(id: string): void {\n    const interval = this.intervals.get(id)\n    if (interval) {\n      clearInterval(interval)\n      this.intervals.delete(id)\n    }\n  }\n}\n\n// Create singleton instance\nexport const uploadProgressTracker = new UploadProgressTracker()\n\n/**\n * Format upload speed for display\n */\nexport function formatUploadSpeed(bytesPerSecond: number): string {\n  if (bytesPerSecond === 0) return '0 B/s'\n  \n  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s']\n  const k = 1024\n  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))\n  \n  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(2)) + ' ' + units[i]\n}\n\n/**\n * Format time remaining for display\n */\nexport function formatTimeRemaining(seconds: number): string {\n  if (seconds === 0 || !isFinite(seconds)) return 'Unknown'\n  \n  if (seconds < 60) {\n    return `${Math.round(seconds)}s`\n  } else if (seconds < 3600) {\n    const minutes = Math.floor(seconds / 60)\n    const remainingSeconds = Math.round(seconds % 60)\n    return `${minutes}m ${remainingSeconds}s`\n  } else {\n    const hours = Math.floor(seconds / 3600)\n    const minutes = Math.floor((seconds % 3600) / 60)\n    return `${hours}h ${minutes}m`\n  }\n}\n\n/**\n * Create XMLHttpRequest with progress tracking\n */\nexport function createProgressXHR(\n  uploadId: string,\n  options: UploadProgressOptions = {}\n): XMLHttpRequest {\n  const xhr = new XMLHttpRequest()\n\n  xhr.upload.addEventListener('progress', (event) => {\n    if (event.lengthComputable) {\n      uploadProgressTracker.updateProgress(uploadId, event.loaded, options)\n    }\n  })\n\n  xhr.upload.addEventListener('load', () => {\n    uploadProgressTracker.completeUpload(uploadId, options)\n  })\n\n  xhr.upload.addEventListener('error', () => {\n    uploadProgressTracker.failUpload(\n      uploadId, \n      new Error('Upload failed'), \n      options\n    )\n  })\n\n  xhr.upload.addEventListener('abort', () => {\n    uploadProgressTracker.cancelUpload(uploadId, options)\n  })\n\n  return xhr\n}\n\n/**\n * Create fetch with progress tracking (using ReadableStream)\n */\nexport async function fetchWithProgress(\n  url: string,\n  options: RequestInit & {\n    uploadId?: string\n    file?: File\n    progressOptions?: UploadProgressOptions\n  } = {}\n): Promise<Response> {\n  const { uploadId, file, progressOptions, ...fetchOptions } = options\n\n  if (uploadId && file) {\n    // Start tracking\n    uploadProgressTracker.startTracking(uploadId, file, progressOptions)\n    \n    // For now, we'll use a simple approach since fetch doesn't support upload progress natively\n    // In a real implementation, you might want to use XMLHttpRequest for upload progress\n    try {\n      const response = await fetch(url, fetchOptions)\n      \n      if (response.ok) {\n        uploadProgressTracker.completeUpload(uploadId, progressOptions)\n      } else {\n        uploadProgressTracker.failUpload(\n          uploadId,\n          new Error(`HTTP ${response.status}: ${response.statusText}`),\n          progressOptions\n        )\n      }\n      \n      return response\n    } catch (error) {\n      uploadProgressTracker.failUpload(\n        uploadId,\n        error instanceof Error ? error : new Error('Upload failed'),\n        progressOptions\n      )\n      throw error\n    }\n  }\n\n  return fetch(url, fetchOptions)\n}\n\n/**\n * Generate unique upload ID\n */\nexport function generateUploadId(): string {\n  return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n}\n\nexport default uploadProgressTracker\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAwBD,MAAM;IACI,UAAU,IAAI,MAA6B;IAC3C,YAAY,IAAI,MAA6B;IAErD;;GAEC,GACD,cACE,EAAU,EACV,IAAU,EACV,UAAiC,CAAC,CAAC,EACnB;QAChB,MAAM,WAA2B;YAC/B;YACA,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;YACnB,QAAQ;YACR,OAAO,KAAK,IAAI;YAChB,YAAY;YACZ,OAAO;YACP,eAAe;YACf,QAAQ;YACR,WAAW,KAAK,GAAG;QACrB;QAEA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI;QAErB,4BAA4B;QAC5B,MAAM,WAAW,YAAY;YAC3B,MAAM,kBAAkB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;YACzC,IAAI,mBAAmB,gBAAgB,MAAM,KAAK,aAAa;gBAC7D,IAAI,CAAC,cAAc,CAAC;gBACpB,QAAQ,UAAU,GAAG;YACvB;QACF,GAAG;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;QAEvB,OAAO;IACT;IAEA;;GAEC,GACD,eACE,EAAU,EACV,MAAc,EACd,UAAiC,CAAC,CAAC,EACZ;QACvB,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAClC,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,UAAU,CAAC,MAAM,SAAS,SAAS,IAAI,KAAK,UAAU;;QAE5D,SAAS,MAAM,GAAG;QAClB,SAAS,UAAU,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,SAAS,KAAK,GAAI;QAC7D,SAAS,MAAM,GAAG,UAAU,SAAS,KAAK,GAAG,eAAe;QAE5D,qCAAqC;QACrC,IAAI,UAAU,GAAG;YACf,SAAS,KAAK,GAAG,SAAS;QAC5B;QAEA,2BAA2B;QAC3B,IAAI,SAAS,KAAK,GAAG,GAAG;YACtB,MAAM,YAAY,SAAS,KAAK,GAAG;YACnC,SAAS,aAAa,GAAG,YAAY,SAAS,KAAK;QACrD;QAEA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI;QACrB,QAAQ,UAAU,GAAG;QAErB,OAAO;IACT;IAEA;;GAEC,GACD,eACE,EAAU,EACV,UAAiC,CAAC,CAAC,EACZ;QACvB,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAClC,IAAI,CAAC,UAAU,OAAO;QAEtB,SAAS,MAAM,GAAG;QAClB,SAAS,OAAO,GAAG,KAAK,GAAG;QAC3B,SAAS,UAAU,GAAG;QACtB,SAAS,MAAM,GAAG,SAAS,KAAK;QAEhC,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI;QAErB,QAAQ,UAAU,GAAG;QACrB,OAAO;IACT;IAEA;;GAEC,GACD,WACE,EAAU,EACV,KAAY,EACZ,UAAiC,CAAC,CAAC,EACZ;QACvB,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAClC,IAAI,CAAC,UAAU,OAAO;QAEtB,SAAS,MAAM,GAAG;QAClB,SAAS,KAAK,GAAG,MAAM,OAAO;QAC9B,SAAS,OAAO,GAAG,KAAK,GAAG;QAE3B,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI;QAErB,QAAQ,OAAO,GAAG,UAAU;QAC5B,OAAO;IACT;IAEA;;GAEC,GACD,aACE,EAAU,EACV,UAAiC,CAAC,CAAC,EACZ;QACvB,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAClC,IAAI,CAAC,UAAU,OAAO;QAEtB,SAAS,MAAM,GAAG;QAClB,SAAS,OAAO,GAAG,KAAK,GAAG;QAE3B,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI;QAErB,QAAQ,QAAQ,GAAG;QACnB,OAAO;IACT;IAEA;;GAEC,GACD,YAAY,EAAU,EAAyB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO;IACjC;IAEA;;GAEC,GACD,iBAAmC;QACjC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;IACvC;IAEA;;GAEC,GACD,aAAa,EAAU,EAAQ;QAC7B,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACtB;IAEA;;GAEC,GACD,WAAiB;QACf,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,WAAa,cAAc;QACnD,IAAI,CAAC,SAAS,CAAC,KAAK;QACpB,IAAI,CAAC,OAAO,CAAC,KAAK;IACpB;IAEA;;GAEC,GACD,AAAQ,eAAe,EAAU,EAAQ;QACvC,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAClC,IAAI,CAAC,UAAU;QAEf,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,UAAU,CAAC,MAAM,SAAS,SAAS,IAAI;QAE7C,IAAI,UAAU,GAAG;YACf,SAAS,KAAK,GAAG,SAAS,MAAM,GAAG;YAEnC,IAAI,SAAS,KAAK,GAAG,GAAG;gBACtB,MAAM,YAAY,SAAS,KAAK,GAAG,SAAS,MAAM;gBAClD,SAAS,aAAa,GAAG,YAAY,SAAS,KAAK;YACrD;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,cAAc,EAAU,EAAQ;QACtC,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,UAAU;YACZ,cAAc;YACd,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;AACF;AAGO,MAAM,wBAAwB,IAAI;AAKlC,SAAS,kBAAkB,cAAsB;IACtD,IAAI,mBAAmB,GAAG,OAAO;IAEjC,MAAM,QAAQ;QAAC;QAAO;QAAQ;QAAQ;KAAO;IAC7C,MAAM,IAAI;IACV,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,kBAAkB,KAAK,GAAG,CAAC;IAEzD,OAAO,WAAW,CAAC,iBAAiB,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AAClF;AAKO,SAAS,oBAAoB,OAAe;IACjD,IAAI,YAAY,KAAK,CAAC,SAAS,UAAU,OAAO;IAEhD,IAAI,UAAU,IAAI;QAChB,OAAO,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,CAAC;IAClC,OAAO,IAAI,UAAU,MAAM;QACzB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,KAAK,KAAK,CAAC,UAAU;QAC9C,OAAO,GAAG,QAAQ,EAAE,EAAE,iBAAiB,CAAC,CAAC;IAC3C,OAAO;QACL,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;IAChC;AACF;AAKO,SAAS,kBACd,QAAgB,EAChB,UAAiC,CAAC,CAAC;IAEnC,MAAM,MAAM,IAAI;IAEhB,IAAI,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;QACvC,IAAI,MAAM,gBAAgB,EAAE;YAC1B,sBAAsB,cAAc,CAAC,UAAU,MAAM,MAAM,EAAE;QAC/D;IACF;IAEA,IAAI,MAAM,CAAC,gBAAgB,CAAC,QAAQ;QAClC,sBAAsB,cAAc,CAAC,UAAU;IACjD;IAEA,IAAI,MAAM,CAAC,gBAAgB,CAAC,SAAS;QACnC,sBAAsB,UAAU,CAC9B,UACA,IAAI,MAAM,kBACV;IAEJ;IAEA,IAAI,MAAM,CAAC,gBAAgB,CAAC,SAAS;QACnC,sBAAsB,YAAY,CAAC,UAAU;IAC/C;IAEA,OAAO;AACT;AAKO,eAAe,kBACpB,GAAW,EACX,UAII,CAAC,CAAC;IAEN,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,cAAc,GAAG;IAE7D,IAAI,YAAY,MAAM;QACpB,iBAAiB;QACjB,sBAAsB,aAAa,CAAC,UAAU,MAAM;QAEpD,4FAA4F;QAC5F,qFAAqF;QACrF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,sBAAsB,cAAc,CAAC,UAAU;YACjD,OAAO;gBACL,sBAAsB,UAAU,CAC9B,UACA,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE,GAC3D;YAEJ;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,sBAAsB,UAAU,CAC9B,UACA,iBAAiB,QAAQ,QAAQ,IAAI,MAAM,kBAC3C;YAEF,MAAM;QACR;IACF;IAEA,OAAO,MAAM,KAAK;AACpB;AAKO,SAAS;IACd,OAAO,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC1E;uCAEe", "debugId": null}}, {"offset": {"line": 1764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/file-upload.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useCallback, useState, useRef, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Progress } from '@/components/ui/progress'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Upload, X, Image, FileText, AlertCircle, CheckCircle2 } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { validateFileClientSide, formatFileSize } from '@/lib/validation/file-upload'\nimport { ErrorDisplay, ValidationErrorDisplay } from '@/components/ui/error-display'\nimport {\n  uploadProgressTracker,\n  generateUploadId,\n  formatUploadSpeed,\n  formatTimeRemaining,\n  type UploadProgress\n} from '@/lib/utils/upload-progress'\n\nexport interface FileUploadProps {\n  onFileSelect: (file: File) => void\n  onFileRemove?: () => void\n  accept?: Record<string, string[]>\n  maxSize?: number\n  multiple?: boolean\n  disabled?: boolean\n  loading?: boolean\n  progress?: number\n  error?: string | null\n  success?: boolean\n  currentFile?: {\n    name: string\n    url?: string\n    size?: number\n  }\n  placeholder?: string\n  description?: string\n  className?: string\n  showPreview?: boolean\n  previewClassName?: string\n  // Validation options\n  allowedTypes?: string[]\n  allowedExtensions?: string[]\n  validateOnSelect?: boolean\n  showValidationErrors?: boolean\n  onValidationError?: (errors: string[]) => void\n  // Real-time progress options\n  enableRealTimeProgress?: boolean\n  onProgressUpdate?: (progress: UploadProgress) => void\n  optimisticUpdates?: boolean\n}\n\nexport function FileUpload({\n  onFileSelect,\n  onFileRemove,\n  accept = {\n    'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp', '.svg']\n  },\n  maxSize = 5 * 1024 * 1024, // 5MB\n  multiple = false,\n  disabled = false,\n  loading = false,\n  progress,\n  error,\n  success = false,\n  currentFile,\n  placeholder = 'Click to upload or drag and drop',\n  description = 'SVG, PNG, JPG or GIF (max. 5MB)',\n  className,\n  showPreview = true,\n  previewClassName\n}: FileUploadProps) {\n  const [dragActive, setDragActive] = useState(false)\n  const [validationErrors, setValidationErrors] = useState<string[]>([])\n  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null)\n  const [uploadId, setUploadId] = useState<string | null>(null)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  // Real-time progress tracking\n  useEffect(() => {\n    if (!enableRealTimeProgress || !uploadId) return\n\n    const interval = setInterval(() => {\n      const progress = uploadProgressTracker.getProgress(uploadId)\n      if (progress) {\n        setUploadProgress(progress)\n        onProgressUpdate?.(progress)\n\n        // Clean up completed or failed uploads\n        if (progress.status === 'completed' || progress.status === 'error' || progress.status === 'cancelled') {\n          setTimeout(() => {\n            uploadProgressTracker.removeUpload(uploadId)\n            setUploadProgress(null)\n            setUploadId(null)\n          }, 3000) // Keep for 3 seconds after completion\n        }\n      }\n    }, 100) // Update every 100ms for smooth progress\n\n    return () => clearInterval(interval)\n  }, [uploadId, enableRealTimeProgress, onProgressUpdate])\n\n  const handleFileSelect = useCallback((files: FileList | null) => {\n    if (files && files.length > 0) {\n      const file = files[0]\n\n      // Clear previous validation errors\n      setValidationErrors([])\n\n      // Client-side validation\n      if (validateOnSelect) {\n        const validation = validateFileClientSide(file, {\n          maxSize,\n          allowedTypes,\n          allowedExtensions\n        })\n\n        if (!validation.valid) {\n          setValidationErrors(validation.errors)\n          if (onValidationError) {\n            onValidationError(validation.errors)\n          }\n          return\n        }\n      } else {\n        // Basic validation (legacy)\n        const errors: string[] = []\n\n        // Validate file size\n        if (file.size > maxSize) {\n          errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(maxSize)})`)\n        }\n\n        // Validate file type\n        const acceptedTypes = Object.values(accept).flat()\n        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()\n        const isAccepted = acceptedTypes.length === 0 || acceptedTypes.includes(fileExtension) ||\n                          Object.keys(accept).some(type => file.type.startsWith(type.replace('/*', '')))\n\n        if (!isAccepted) {\n          errors.push(`File type (${file.type}) is not allowed`)\n        }\n\n        if (errors.length > 0) {\n          setValidationErrors(errors)\n          if (onValidationError) {\n            onValidationError(errors)\n          }\n          return\n        }\n      }\n\n      // Start progress tracking if enabled\n      if (enableRealTimeProgress) {\n        const newUploadId = generateUploadId()\n        setUploadId(newUploadId)\n\n        const progress = uploadProgressTracker.startTracking(newUploadId, file, {\n          onProgress: (progress) => {\n            setUploadProgress(progress)\n            onProgressUpdate?.(progress)\n          },\n          onComplete: (progress) => {\n            setUploadProgress(progress)\n            onProgressUpdate?.(progress)\n          },\n          onError: (progress, error) => {\n            setUploadProgress(progress)\n            onProgressUpdate?.(progress)\n            setValidationErrors([error.message])\n          }\n        })\n\n        setUploadProgress(progress)\n      }\n\n      onFileSelect(file)\n    }\n  }, [onFileSelect, accept, maxSize, allowedTypes, allowedExtensions, validateOnSelect, onValidationError, enableRealTimeProgress, onProgressUpdate])\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    if (!dragActive) setDragActive(true)\n  }, [dragActive])\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setDragActive(false)\n  }, [])\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setDragActive(false)\n\n    if (disabled || loading) return\n\n    const files = e.dataTransfer.files\n    handleFileSelect(files)\n  }, [disabled, loading, handleFileSelect])\n\n  const handleClick = useCallback(() => {\n    if (disabled || loading) return\n    fileInputRef.current?.click()\n  }, [disabled, loading])\n\n  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    handleFileSelect(e.target.files)\n  }, [handleFileSelect])\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  const isImage = (filename: string) => {\n    return /\\.(jpg|jpeg|png|gif|webp|svg)$/i.test(filename)\n  }\n\n  return (\n    <div className={cn('space-y-4', className)}>\n      {/* Current File Preview */}\n      {currentFile && showPreview && (\n        <Card className={cn('relative', previewClassName)}>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              {currentFile.url && isImage(currentFile.name) ? (\n                <div className=\"relative\">\n                  <img\n                    src={currentFile.url}\n                    alt={currentFile.name}\n                    className=\"h-16 w-16 rounded-lg object-cover border\"\n                  />\n                  {success && (\n                    <div className=\"absolute -top-1 -right-1 bg-green-500 rounded-full p-1\">\n                      <CheckCircle2 className=\"h-3 w-3 text-white\" />\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <div className=\"h-16 w-16 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center\">\n                  <FileText className=\"h-6 w-6 text-gray-400\" />\n                </div>\n              )}\n              \n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 truncate\">\n                  {currentFile.name}\n                </p>\n                {currentFile.size && (\n                  <p className=\"text-xs text-gray-500\">\n                    {formatFileSize(currentFile.size)}\n                  </p>\n                )}\n                {(loading || uploadProgress?.status === 'uploading') && (\n                  <div className=\"mt-2 space-y-1\">\n                    <Progress\n                      value={uploadProgress?.percentage || progress || 0}\n                      className=\"h-1\"\n                    />\n                    <div className=\"flex justify-between text-xs text-gray-500\">\n                      <span>\n                        {uploadProgress?.percentage || progress || 0}% uploaded\n                      </span>\n                      {uploadProgress && (\n                        <span>\n                          {formatUploadSpeed(uploadProgress.speed)} • {formatTimeRemaining(uploadProgress.timeRemaining)} remaining\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {onFileRemove && !loading && (\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={onFileRemove}\n                  className=\"h-8 w-8 p-0 text-gray-400 hover:text-red-500\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </Button>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Upload Area */}\n      <div\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n        onClick={handleClick}\n        className={cn(\n          'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',\n          {\n            'border-blue-300 bg-blue-50': dragActive,\n            'border-gray-300 hover:border-gray-400': !dragActive && !disabled && !loading,\n            'border-gray-200 bg-gray-50 cursor-not-allowed': disabled || loading,\n            'border-red-300 bg-red-50': error,\n            'border-green-300 bg-green-50': success && !error,\n          }\n        )}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          onChange={handleInputChange}\n          accept={Object.keys(accept).join(',')}\n          multiple={multiple}\n          disabled={disabled || loading}\n          className=\"hidden\"\n        />\n        \n        <div className=\"space-y-2\">\n          {loading ? (\n            <>\n              <div className=\"mx-auto h-12 w-12 text-blue-500 animate-pulse\">\n                <Upload className=\"h-full w-full\" />\n              </div>\n              <p className=\"text-sm text-blue-600\">Uploading...</p>\n            </>\n          ) : success && !error ? (\n            <>\n              <div className=\"mx-auto h-12 w-12 text-green-500\">\n                <CheckCircle2 className=\"h-full w-full\" />\n              </div>\n              <p className=\"text-sm text-green-600\">Upload successful!</p>\n            </>\n          ) : error ? (\n            <>\n              <div className=\"mx-auto h-12 w-12 text-red-500\">\n                <AlertCircle className=\"h-full w-full\" />\n              </div>\n              <p className=\"text-sm text-red-600\">Upload failed</p>\n            </>\n          ) : (\n            <>\n              <div className=\"mx-auto h-12 w-12 text-gray-400\">\n                {currentFile && isImage(currentFile.name) ? (\n                  <Image className=\"h-full w-full\" />\n                ) : (\n                  <Upload className=\"h-full w-full\" />\n                )}\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">{placeholder}</p>\n                <p className=\"text-xs text-gray-500\">{description}</p>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* Validation Errors */}\n      {showValidationErrors && validationErrors.length > 0 && (\n        <ValidationErrorDisplay\n          errors={validationErrors}\n          onDismiss={() => setValidationErrors([])}\n        />\n      )}\n\n      {/* Error Message */}\n      {error && (\n        <Alert variant=\"destructive\">\n          <AlertCircle className=\"h-4 w-4\" />\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      )}\n\n      {/* Success Message */}\n      {success && !error && validationErrors.length === 0 && (\n        <Alert className=\"border-green-200 bg-green-50\">\n          <CheckCircle2 className=\"h-4 w-4 text-green-600\" />\n          <AlertDescription className=\"text-green-800\">\n            File uploaded successfully!\n          </AlertDescription>\n        </Alert>\n      )}\n    </div>\n  )\n}\n\n// Specialized components for different upload types\nexport function LogoUpload(props: Omit<FileUploadProps, 'accept' | 'description' | 'allowedTypes' | 'validateOnSelect' | 'enableRealTimeProgress'>) {\n  return (\n    <FileUpload\n      {...props}\n      accept={{\n        'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp', '.svg']\n      }}\n      allowedTypes={['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']}\n      description=\"SVG, PNG, JPG, GIF or WebP (max. 5MB)\"\n      placeholder=\"Upload platform logo\"\n      validateOnSelect={true}\n      showValidationErrors={true}\n      enableRealTimeProgress={true}\n      optimisticUpdates={true}\n    />\n  )\n}\n\nexport function FaviconUpload(props: Omit<FileUploadProps, 'accept' | 'description' | 'maxSize' | 'allowedTypes' | 'validateOnSelect' | 'enableRealTimeProgress'>) {\n  return (\n    <FileUpload\n      {...props}\n      accept={{\n        'image/*': ['.png', '.ico', '.gif', '.jpg', '.jpeg']\n      }}\n      allowedTypes={['image/x-icon', 'image/png', 'image/gif', 'image/jpeg', 'image/jpg']}\n      maxSize={2 * 1024 * 1024} // 2MB\n      description=\"ICO, PNG, GIF or JPG (max. 2MB, square recommended)\"\n      placeholder=\"Upload platform favicon\"\n      validateOnSelect={true}\n      showValidationErrors={true}\n      enableRealTimeProgress={true}\n      optimisticUpdates={true}\n    />\n  )\n}\n\nexport function AvatarUpload(props: Omit<FileUploadProps, 'accept' | 'description' | 'allowedTypes' | 'validateOnSelect' | 'enableRealTimeProgress'>) {\n  return (\n    <FileUpload\n      {...props}\n      accept={{\n        'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']\n      }}\n      allowedTypes={['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']}\n      description=\"JPG, PNG, GIF or WebP (max. 5MB, square recommended)\"\n      placeholder=\"Upload avatar\"\n      previewClassName=\"max-w-xs\"\n      validateOnSelect={true}\n      showValidationErrors={true}\n      enableRealTimeProgress={true}\n      optimisticUpdates={true}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAoDO,SAAS,WAAW,EACzB,YAAY,EACZ,YAAY,EACZ,SAAS;IACP,WAAW;QAAC;QAAS;QAAQ;QAAQ;QAAQ;QAAS;KAAO;AAC/D,CAAC,EACD,UAAU,IAAI,OAAO,IAAI,EACzB,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,QAAQ,EACR,KAAK,EACL,UAAU,KAAK,EACf,WAAW,EACX,cAAc,kCAAkC,EAChD,cAAc,iCAAiC,EAC/C,SAAS,EACT,cAAc,IAAI,EAClB,gBAAgB,EACA;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,kVAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,kVAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,kVAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,kVAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,eAAe,CAAA,GAAA,kVAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,8BAA8B;IAC9B,CAAA,GAAA,kVAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,0BAA0B,CAAC,UAAU;QAE1C,MAAM,WAAW,YAAY;YAC3B,MAAM,WAAW,6JAAA,CAAA,wBAAqB,CAAC,WAAW,CAAC;YACnD,IAAI,UAAU;gBACZ,kBAAkB;gBAClB,mBAAmB;gBAEnB,uCAAuC;gBACvC,IAAI,SAAS,MAAM,KAAK,eAAe,SAAS,MAAM,KAAK,WAAW,SAAS,MAAM,KAAK,aAAa;oBACrG,WAAW;wBACT,6JAAA,CAAA,wBAAqB,CAAC,YAAY,CAAC;wBACnC,kBAAkB;wBAClB,YAAY;oBACd,GAAG,MAAM,sCAAsC;;gBACjD;YACF;QACF,GAAG,KAAK,yCAAyC;;QAEjD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAU;QAAwB;KAAiB;IAEvD,MAAM,mBAAmB,CAAA,GAAA,kVAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,MAAM,OAAO,KAAK,CAAC,EAAE;YAErB,mCAAmC;YACnC,oBAAoB,EAAE;YAEtB,yBAAyB;YACzB,IAAI,kBAAkB;gBACpB,MAAM,aAAa,CAAA,GAAA,8JAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;oBAC9C;oBACA;oBACA;gBACF;gBAEA,IAAI,CAAC,WAAW,KAAK,EAAE;oBACrB,oBAAoB,WAAW,MAAM;oBACrC,IAAI,mBAAmB;wBACrB,kBAAkB,WAAW,MAAM;oBACrC;oBACA;gBACF;YACF,OAAO;gBACL,4BAA4B;gBAC5B,MAAM,SAAmB,EAAE;gBAE3B,qBAAqB;gBACrB,IAAI,KAAK,IAAI,GAAG,SAAS;oBACvB,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,eAAe,KAAK,IAAI,EAAE,gCAAgC,EAAE,eAAe,SAAS,CAAC,CAAC;gBAClH;gBAEA,qBAAqB;gBACrB,MAAM,gBAAgB,OAAO,MAAM,CAAC,QAAQ,IAAI;gBAChD,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;gBACxD,MAAM,aAAa,cAAc,MAAM,KAAK,KAAK,cAAc,QAAQ,CAAC,kBACtD,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,OAAO,CAAC,MAAM;gBAE3F,IAAI,CAAC,YAAY;oBACf,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC;gBACvD;gBAEA,IAAI,OAAO,MAAM,GAAG,GAAG;oBACrB,oBAAoB;oBACpB,IAAI,mBAAmB;wBACrB,kBAAkB;oBACpB;oBACA;gBACF;YACF;YAEA,qCAAqC;YACrC,IAAI,wBAAwB;gBAC1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD;gBACnC,YAAY;gBAEZ,MAAM,WAAW,6JAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,aAAa,MAAM;oBACtE,YAAY,CAAC;wBACX,kBAAkB;wBAClB,mBAAmB;oBACrB;oBACA,YAAY,CAAC;wBACX,kBAAkB;wBAClB,mBAAmB;oBACrB;oBACA,SAAS,CAAC,UAAU;wBAClB,kBAAkB;wBAClB,mBAAmB;wBACnB,oBAAoB;4BAAC,MAAM,OAAO;yBAAC;oBACrC;gBACF;gBAEA,kBAAkB;YACpB;YAEA,aAAa;QACf;IACF,GAAG;QAAC;QAAc;QAAQ;QAAS;QAAc;QAAmB;QAAkB;QAAmB;QAAwB;KAAiB;IAElJ,MAAM,iBAAiB,CAAA,GAAA,kVAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,CAAC,YAAY,cAAc;IACjC,GAAG;QAAC;KAAW;IAEf,MAAM,kBAAkB,CAAA,GAAA,kVAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,kVAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,YAAY,SAAS;QAEzB,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,iBAAiB;IACnB,GAAG;QAAC;QAAU;QAAS;KAAiB;IAExC,MAAM,cAAc,CAAA,GAAA,kVAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,YAAY,SAAS;QACzB,aAAa,OAAO,EAAE;IACxB,GAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,oBAAoB,CAAA,GAAA,kVAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,iBAAiB,EAAE,MAAM,CAAC,KAAK;IACjC,GAAG;QAAC;KAAiB;IAErB,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,UAAU,CAAC;QACf,OAAO,kCAAkC,IAAI,CAAC;IAChD;IAEA,qBACE,2XAAC;QAAI,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;YAE7B,eAAe,6BACd,2XAAC,oJAAA,CAAA,OAAI;gBAAC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;0BAC9B,cAAA,2XAAC,oJAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,2XAAC;wBAAI,WAAU;;4BACZ,YAAY,GAAG,IAAI,QAAQ,YAAY,IAAI,kBAC1C,2XAAC;gCAAI,WAAU;;kDACb,2XAAC;wCACC,KAAK,YAAY,GAAG;wCACpB,KAAK,YAAY,IAAI;wCACrB,WAAU;;;;;;oCAEX,yBACC,2XAAC;wCAAI,WAAU;kDACb,cAAA,2XAAC,ySAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;qDAK9B,2XAAC;gCAAI,WAAU;0CACb,cAAA,2XAAC,kSAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAIxB,2XAAC;gCAAI,WAAU;;kDACb,2XAAC;wCAAE,WAAU;kDACV,YAAY,IAAI;;;;;;oCAElB,YAAY,IAAI,kBACf,2XAAC;wCAAE,WAAU;kDACV,eAAe,YAAY,IAAI;;;;;;oCAGnC,CAAC,WAAW,gBAAgB,WAAW,WAAW,mBACjD,2XAAC;wCAAI,WAAU;;0DACb,2XAAC,wJAAA,CAAA,WAAQ;gDACP,OAAO,gBAAgB,cAAc,YAAY;gDACjD,WAAU;;;;;;0DAEZ,2XAAC;gDAAI,WAAU;;kEACb,2XAAC;;4DACE,gBAAgB,cAAc,YAAY;4DAAE;;;;;;;oDAE9C,gCACC,2XAAC;;4DACE,CAAA,GAAA,6JAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe,KAAK;4DAAE;4DAAI,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe,aAAa;4DAAE;;;;;;;;;;;;;;;;;;;;;;;;;4BAQ1G,gBAAgB,CAAC,yBAChB,2XAAC,sJAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,2XAAC,gRAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzB,2XAAC;gBACC,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,SAAS;gBACT,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,sFACA;oBACE,8BAA8B;oBAC9B,yCAAyC,CAAC,cAAc,CAAC,YAAY,CAAC;oBACtE,iDAAiD,YAAY;oBAC7D,4BAA4B;oBAC5B,gCAAgC,WAAW,CAAC;gBAC9C;;kCAGF,2XAAC;wBACC,KAAK;wBACL,MAAK;wBACL,UAAU;wBACV,QAAQ,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC;wBACjC,UAAU;wBACV,UAAU,YAAY;wBACtB,WAAU;;;;;;kCAGZ,2XAAC;wBAAI,WAAU;kCACZ,wBACC;;8CACE,2XAAC;oCAAI,WAAU;8CACb,cAAA,2XAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,2XAAC;oCAAE,WAAU;8CAAwB;;;;;;;2CAErC,WAAW,CAAC,sBACd;;8CACE,2XAAC;oCAAI,WAAU;8CACb,cAAA,2XAAC,ySAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,2XAAC;oCAAE,WAAU;8CAAyB;;;;;;;2CAEtC,sBACF;;8CACE,2XAAC;oCAAI,WAAU;8CACb,cAAA,2XAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,2XAAC;oCAAE,WAAU;8CAAuB;;;;;;;yDAGtC;;8CACE,2XAAC;oCAAI,WAAU;8CACZ,eAAe,QAAQ,YAAY,IAAI,kBACtC,2XAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;6DAEjB,2XAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAGtB,2XAAC;;sDACC,2XAAC;4CAAE,WAAU;sDAAyB;;;;;;sDACtC,2XAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;YAQ/C,wBAAwB,iBAAiB,MAAM,GAAG,mBACjD,2XAAC,gKAAA,CAAA,yBAAsB;gBACrB,QAAQ;gBACR,WAAW,IAAM,oBAAoB,EAAE;;;;;;YAK1C,uBACC,2XAAC,qJAAA,CAAA,QAAK;gBAAC,SAAQ;;kCACb,2XAAC,wSAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,2XAAC,qJAAA,CAAA,mBAAgB;kCAAE;;;;;;;;;;;;YAKtB,WAAW,CAAC,SAAS,iBAAiB,MAAM,KAAK,mBAChD,2XAAC,qJAAA,CAAA,QAAK;gBAAC,WAAU;;kCACf,2XAAC,ySAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,2XAAC,qJAAA,CAAA,mBAAgB;wBAAC,WAAU;kCAAiB;;;;;;;;;;;;;;;;;;AAOvD;AAGO,SAAS,WAAW,KAAuH;IAChJ,qBACE,2XAAC;QACE,GAAG,KAAK;QACT,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;gBAAQ;gBAAS;aAAO;QAC/D;QACA,cAAc;YAAC;YAAc;YAAa;YAAa;YAAa;YAAc;SAAgB;QAClG,aAAY;QACZ,aAAY;QACZ,kBAAkB;QAClB,sBAAsB;QACtB,wBAAwB;QACxB,mBAAmB;;;;;;AAGzB;AAEO,SAAS,cAAc,KAAmI;IAC/J,qBACE,2XAAC;QACE,GAAG,KAAK;QACT,QAAQ;YACN,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAQ;gBAAQ;aAAQ;QACtD;QACA,cAAc;YAAC;YAAgB;YAAa;YAAa;YAAc;SAAY;QACnF,SAAS,IAAI,OAAO;QACpB,aAAY;QACZ,aAAY;QACZ,kBAAkB;QAClB,sBAAsB;QACtB,wBAAwB;QACxB,mBAAmB;;;;;;AAGzB;AAEO,SAAS,aAAa,KAAuH;IAClJ,qBACE,2XAAC;QACE,GAAG,KAAK;QACT,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;gBAAQ;aAAQ;QACvD;QACA,cAAc;YAAC;YAAc;YAAa;YAAa;YAAa;SAAa;QACjF,aAAY;QACZ,aAAY;QACZ,kBAAiB;QACjB,kBAAkB;QAClB,sBAAsB;QACtB,wBAAwB;QACxB,mBAAmB;;;;;;AAGzB", "debugId": null}}, {"offset": {"line": 2456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/app/super-admin/settings/platform/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Formik, Form, Field } from 'formik'\nimport * as Yup from 'yup'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Switch } from '@/components/ui/switch'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { toast } from 'sonner'\nimport { useSettingsStore } from '@/stores/settings/useSettingsStore'\nimport { settingsApi, type SettingCreationData } from '@/lib/api/settings'\nimport { fileUploadAPI } from '@/lib/api/file-upload'\nimport { LogoUpload, FaviconUpload } from '@/components/ui/file-upload'\n\nconst platformSettingsSchema = Yup.object({\n  platform_name: Yup.string().required('Platform name is required'),\n  platform_url: Yup.string().url('Invalid URL').required('Platform URL is required'),\n  support_email: Yup.string().email('Invalid email').required('Support email is required'),\n  platform_address: Yup.string().nullable(),\n  platform_tagline: Yup.string().nullable(),\n  platform_logo: Yup.mixed().nullable(),\n  platform_favicon: Yup.mixed().nullable(),\n  maintenance_mode: Yup.boolean(),\n  allow_registration: Yup.boolean(),\n  require_email_verification: Yup.boolean()\n})\n\nexport default function PlatformSettingsPage() {\n  const [isLoading, setIsLoading] = useState(true)\n  const [isSaving, setIsSaving] = useState(false)\n\n  // Helper function to fetch media URL by ID\n  const fetchMediaUrl = async (mediaId: string): Promise<string | null> => {\n    try {\n      console.log('🔍 Fetching media URL for ID:', mediaId)\n\n      // Get auth token from localStorage or useAuthStore\n      const token = localStorage.getItem('token') || localStorage.getItem('payload-token')\n      console.log('🔑 Using auth token:', token ? 'Token found' : 'No token')\n\n      // Use Payload's REST API to get media by ID\n      const response = await fetch(`http://localhost:3001/api/media/${mediaId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      })\n\n      console.log('📡 Media fetch response status:', response.status)\n\n      if (response.ok) {\n        const media = await response.json()\n        console.log('📦 Media data received:', media)\n\n        if (media.url) {\n          const fullUrl = fileUploadAPI.getFileUrl(media.url)\n          console.log('✅ Media URL resolved:', fullUrl)\n          return fullUrl\n        } else {\n          console.log('❌ No URL found in media data')\n        }\n      } else {\n        console.log('❌ Media fetch failed:', response.status, response.statusText)\n      }\n    } catch (error) {\n      console.error('❌ Error fetching media URL:', error)\n    }\n    return null\n  }\n  const [initialValues, setInitialValues] = useState({\n    platform_name: 'KISS LMS',\n    platform_url: 'https://groups-exam.com',\n    support_email: '<EMAIL>',\n    platform_address: '',\n    platform_tagline: 'Empowering Education Through Technology',\n    platform_logo: null as File | null,\n    platform_favicon: null as File | null,\n    maintenance_mode: false,\n    allow_registration: true,\n    require_email_verification: true\n  })\n  const { fetchSettingsByCategory } = useSettingsStore()\n\n  // Load settings on mount\n  useEffect(() => {\n    const loadSettings = async () => {\n      try {\n        setIsLoading(true)\n        await fetchSettingsByCategory('platform')\n\n        // Fetch platform settings to populate form\n        const response = await settingsApi.getSettingsByCategory('platform')\n\n        // Convert settings array to form values\n        const formValues = { ...initialValues }\n        response.settings.forEach(setting => {\n          if (setting.key in formValues) {\n            if (setting.type === 'boolean') {\n              (formValues as any)[setting.key] = setting.value === 'true'\n            } else {\n              (formValues as any)[setting.key] = setting.value\n            }\n          }\n\n          // Skip logo and favicon as they're handled by BrandingUpload component\n        })\n\n        setInitialValues(formValues)\n      } catch (error) {\n        console.error('Error loading settings:', error)\n        toast.error('Failed to load settings')\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    loadSettings()\n  }, [])\n\n  // Helper function to create or update a setting\n  const createOrUpdateSetting = async (key: string, value: string) => {\n    try {\n      console.log(`🔄 Creating/updating setting: ${key} = \"${value}\"`)\n\n      const result = await settingsApi.createOrUpdateSetting(key, value, {\n        category: 'platform',\n        type: 'media',\n        description: key === 'platform_logo' ? 'Platform logo media ID' : 'Platform favicon media ID',\n        is_public: true\n      })\n\n      console.log('✅ Setting operation result:', result)\n\n      if (result.errors && result.errors.length > 0) {\n        console.error('❌ Setting errors:', result.errors)\n        throw new Error(result.errors[0].error)\n      }\n\n    } catch (error: any) {\n      console.error('❌ Error in createOrUpdateSetting:', error)\n      throw error\n    }\n  }\n\n  // Logo and favicon upload is now handled by BrandingUpload component\n\n\n\n  const handleSubmit = async (values: any) => {\n    setIsSaving(true)\n    try {\n      // Convert form values to settings\n      // Convert form values to settings - only include platform-specific settings\n      const settingsToUpdate: SettingCreationData[] = []\n\n      // Add required platform settings\n      if (values.platform_name) {\n        settingsToUpdate.push({\n          key: 'platform_name',\n          value: values.platform_name,\n          category: 'platform',\n          type: 'string',\n          is_public: true\n        })\n      }\n\n      if (values.platform_url) {\n        settingsToUpdate.push({\n          key: 'platform_url',\n          value: values.platform_url,\n          category: 'platform',\n          type: 'url',\n          is_public: true\n        })\n      }\n\n      if (values.support_email) {\n        settingsToUpdate.push({\n          key: 'support_email',\n          value: values.support_email,\n          category: 'platform',\n          type: 'email',\n          is_public: true\n        })\n      }\n\n      // Optional fields - only add if they have non-empty values\n      if (values.platform_address && values.platform_address.trim() !== '') {\n        settingsToUpdate.push({\n          key: 'platform_address',\n          value: values.platform_address,\n          category: 'platform',\n          type: 'textarea',\n          is_public: true\n        })\n      }\n\n      if (values.platform_tagline && values.platform_tagline.trim() !== '') {\n        settingsToUpdate.push({\n          key: 'platform_tagline',\n          value: values.platform_tagline,\n          category: 'platform',\n          type: 'string',\n          is_public: true\n        })\n      }\n\n      settingsToUpdate.push(\n        {\n          key: 'platform_logo',\n          value: values.platform_logo.toString(),\n          category: 'platform',\n          type: 'url',\n          is_public: true\n        },\n        {\n          key: 'platform_favicon',\n          value: values.platform_favicon.toString(),\n          category: 'platform',\n          type: 'url',\n          is_public: true\n        }\n      )\n\n      // Boolean settings - always include\n      settingsToUpdate.push(\n        {\n          key: 'maintenance_mode',\n          value: values.maintenance_mode.toString(),\n          category: 'platform',\n          type: 'boolean',\n          is_public: false\n        },\n        {\n          key: 'allow_registration',\n          value: values.allow_registration.toString(),\n          category: 'platform',\n          type: 'boolean',\n          is_public: false\n        },\n        {\n          key: 'require_email_verification',\n          value: values.require_email_verification.toString(),\n          category: 'platform',\n          type: 'boolean',\n          is_public: false\n        }\n      )\n\n      // File uploads are handled immediately when files are selected\n      // No need to handle them in form submission\n\n      // Save settings using the API\n      await settingsApi.bulkUpdateSettings(settingsToUpdate)\n\n      toast.success('Platform settings saved successfully')\n    } catch (error) {\n      toast.error('Failed to save settings')\n      console.error(error)\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"p-8\">\n        <div className=\"max-w-4xl mx-auto space-y-6\">\n          <div>\n            <h1 className=\"text-2xl font-bold\">Platform Settings</h1>\n            <p className=\"text-muted-foreground\">Loading settings...</p>\n          </div>\n          <div className=\"flex justify-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"p-8\">\n      <div className=\"max-w-4xl mx-auto space-y-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold\">Platform Settings</h1>\n          <p className=\"text-muted-foreground\">Configure global platform settings and policies</p>\n        </div>\n\n        <Formik\n          initialValues={initialValues}\n          validationSchema={platformSettingsSchema}\n          onSubmit={handleSubmit}\n          enableReinitialize={true}\n        >\n          {({ errors, touched, values, setFieldValue, isValid }) => (\n            <Form className=\"space-y-6\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>General Configuration</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"platform_name\">Platform Name</Label>\n                      <Field\n                        as={Input}\n                        id=\"platform_name\"\n                        name=\"platform_name\"\n                        placeholder=\"Groups Exam LMS\"\n                      />\n                      {errors.platform_name && touched.platform_name && (\n                        <p className=\"text-sm text-destructive\">{errors.platform_name}</p>\n                      )}\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"platform_url\">Platform URL</Label>\n                      <Field\n                        as={Input}\n                        id=\"platform_url\"\n                        name=\"platform_url\"\n                        placeholder=\"https://groups-exam.com\"\n                      />\n                      {errors.platform_url && touched.platform_url && (\n                        <p className=\"text-sm text-destructive\">{errors.platform_url}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"support_email\">Support Email</Label>\n                    <Field\n                      as={Input}\n                      id=\"support_email\"\n                      name=\"support_email\"\n                      type=\"email\"\n                      placeholder=\"<EMAIL>\"\n                    />\n                    {errors.support_email && touched.support_email && (\n                      <p className=\"text-sm text-destructive\">{errors.support_email}</p>\n                    )}\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"platform_tagline\">Platform Tagline</Label>\n                    <Field\n                      as={Input}\n                      id=\"platform_tagline\"\n                      name=\"platform_tagline\"\n                      placeholder=\"Empowering Education Through Technology\"\n                    />\n                    {errors.platform_tagline && touched.platform_tagline && (\n                      <p className=\"text-sm text-destructive\">{errors.platform_tagline}</p>\n                    )}\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"platform_address\">Platform Address</Label>\n                    <Field\n                      as={Textarea}\n                      id=\"platform_address\"\n                      name=\"platform_address\"\n                      placeholder=\"Enter your organization's physical address\"\n                      rows={3}\n                    />\n                    {errors.platform_address && touched.platform_address && (\n                      <p className=\"text-sm text-destructive\">{errors.platform_address}</p>\n                    )}\n                  </div>\n\n                </CardContent>\n              </Card>\n\n              {/* Platform Logo Section */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>Platform Logo</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <LogoUpload\n                    onFileSelect={async (file) => {\n                      try {\n                        const result = await fileUploadAPI.uploadFile(file, {\n                          uploadType: 'platform_logo',\n                          folder: 'platform'\n                        })\n                        if (result.success) {\n                          toast.success('Logo uploaded successfully!')\n                          // Refresh settings to get updated data\n                          setTimeout(() => window.location.reload(), 1000)\n                        }\n                      } catch (error) {\n                        console.error('Logo upload error:', error)\n                        toast.error('Failed to upload logo')\n                      }\n                    }}\n                  />\n                </CardContent>\n              </Card>\n\n              {/* Platform Favicon Section */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>Platform Favicon</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <FaviconUpload\n                    onFileSelect={async (file) => {\n                      try {\n                        const result = await fileUploadAPI.uploadFile(file, {\n                          uploadType: 'platform_favicon',\n                          folder: 'platform'\n                        })\n                        if (result.success) {\n                          toast.success('Favicon uploaded successfully!')\n                          // Refresh settings to get updated data\n                          setTimeout(() => window.location.reload(), 1000)\n                        }\n                      } catch (error) {\n                        console.error('Favicon upload error:', error)\n                        toast.error('Failed to upload favicon')\n                      }\n                    }}\n                  />\n                </CardContent>\n              </Card>\n\n              {/* System Configuration */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>System Configuration</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <Label>Maintenance Mode</Label>\n                        <p className=\"text-sm text-gray-500\">\n                          Enable to put the platform in maintenance mode\n                        </p>\n                      </div>\n                      <Switch\n                        checked={values.maintenance_mode}\n                        onCheckedChange={(checked) => setFieldValue('maintenance_mode', checked)}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <Label>Allow Institute Registration</Label>\n                        <p className=\"text-sm text-gray-500\">\n                          Allow new institutes to register on the platform\n                        </p>\n                      </div>\n                      <Switch\n                        checked={values.allow_registration}\n                        onCheckedChange={(checked) => setFieldValue('allow_registration', checked)}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <Label>Require Email Verification</Label>\n                        <p className=\"text-sm text-gray-500\">\n                          Require email verification for new user accounts\n                        </p>\n                      </div>\n                      <Switch\n                        checked={values.require_email_verification}\n                        onCheckedChange={(checked) => setFieldValue('require_email_verification', checked)}\n                      />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n\n              <div className=\"flex justify-end space-x-4\">\n                <Button type=\"button\" variant=\"outline\">\n                  Reset to Defaults\n                </Button>\n                <div className=\"space-y-2\">\n                  {Object.keys(errors).length > 0 && (\n                    <div className=\"text-sm text-destructive\">\n                      Form has validation errors: {Object.keys(errors).join(', ')}\n                    </div>\n                  )}\n                  <Button\n                    type=\"submit\"\n                    disabled={isSaving || !isValid}\n\n                  >\n                    {isSaving ? 'Saving...' : 'Save Settings'}\n                  </Button>\n                </div>\n              </div>\n            </Form>\n          )}\n        </Formik>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;;AAiBA,MAAM,yBAAyB,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;IACxC,eAAe,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC;IACrC,cAAc,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IAAI,GAAG,CAAC,eAAe,QAAQ,CAAC;IACvD,eAAe,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC,iBAAiB,QAAQ,CAAC;IAC5D,kBAAkB,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IAAI,QAAQ;IACvC,kBAAkB,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IAAI,QAAQ;IACvC,eAAe,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IAAI,QAAQ;IACnC,kBAAkB,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IAAI,QAAQ;IACtC,kBAAkB,CAAA,GAAA,mLAAA,CAAA,UAAW,AAAD;IAC5B,oBAAoB,CAAA,GAAA,mLAAA,CAAA,UAAW,AAAD;IAC9B,4BAA4B,CAAA,GAAA,mLAAA,CAAA,UAAW,AAAD;AACxC;AAEe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kVAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,kVAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,2CAA2C;IAC3C,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,mDAAmD;YACnD,MAAM,QAAQ,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;YACpE,QAAQ,GAAG,CAAC,wBAAwB,QAAQ,gBAAgB;YAE5D,4CAA4C;YAC5C,MAAM,WAAW,MAAM,MAAM,CAAC,gCAAgC,EAAE,SAAS,EAAE;gBACzE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,mCAAmC,SAAS,MAAM;YAE9D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,QAAQ,GAAG,CAAC,2BAA2B;gBAEvC,IAAI,MAAM,GAAG,EAAE;oBACb,MAAM,UAAU,uJAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,MAAM,GAAG;oBAClD,QAAQ,GAAG,CAAC,yBAAyB;oBACrC,OAAO;gBACT,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,yBAAyB,SAAS,MAAM,EAAE,SAAS,UAAU;YAC3E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;QACA,OAAO;IACT;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,kVAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,eAAe;QACf,cAAc;QACd,eAAe;QACf,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,4BAA4B;IAC9B;IACA,MAAM,EAAE,uBAAuB,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD;IAEnD,yBAAyB;IACzB,CAAA,GAAA,kVAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI;gBACF,aAAa;gBACb,MAAM,wBAAwB;gBAE9B,2CAA2C;gBAC3C,MAAM,WAAW,MAAM,iJAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC;gBAEzD,wCAAwC;gBACxC,MAAM,aAAa;oBAAE,GAAG,aAAa;gBAAC;gBACtC,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAA;oBACxB,IAAI,QAAQ,GAAG,IAAI,YAAY;wBAC7B,IAAI,QAAQ,IAAI,KAAK,WAAW;4BAC7B,UAAkB,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK,KAAK;wBACvD,OAAO;4BACJ,UAAkB,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK;wBAClD;oBACF;gBAEA,uEAAuE;gBACzE;gBAEA,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,gDAAgD;IAChD,MAAM,wBAAwB,OAAO,KAAa;QAChD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,IAAI,IAAI,EAAE,MAAM,CAAC,CAAC;YAE/D,MAAM,SAAS,MAAM,iJAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC,KAAK,OAAO;gBACjE,UAAU;gBACV,MAAM;gBACN,aAAa,QAAQ,kBAAkB,2BAA2B;gBAClE,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;gBAC7C,QAAQ,KAAK,CAAC,qBAAqB,OAAO,MAAM;gBAChD,MAAM,IAAI,MAAM,OAAO,MAAM,CAAC,EAAE,CAAC,KAAK;YACxC;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;QACR;IACF;IAEA,qEAAqE;IAIrE,MAAM,eAAe,OAAO;QAC1B,YAAY;QACZ,IAAI;YACF,kCAAkC;YAClC,4EAA4E;YAC5E,MAAM,mBAA0C,EAAE;YAElD,iCAAiC;YACjC,IAAI,OAAO,aAAa,EAAE;gBACxB,iBAAiB,IAAI,CAAC;oBACpB,KAAK;oBACL,OAAO,OAAO,aAAa;oBAC3B,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,IAAI,OAAO,YAAY,EAAE;gBACvB,iBAAiB,IAAI,CAAC;oBACpB,KAAK;oBACL,OAAO,OAAO,YAAY;oBAC1B,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,IAAI,OAAO,aAAa,EAAE;gBACxB,iBAAiB,IAAI,CAAC;oBACpB,KAAK;oBACL,OAAO,OAAO,aAAa;oBAC3B,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,2DAA2D;YAC3D,IAAI,OAAO,gBAAgB,IAAI,OAAO,gBAAgB,CAAC,IAAI,OAAO,IAAI;gBACpE,iBAAiB,IAAI,CAAC;oBACpB,KAAK;oBACL,OAAO,OAAO,gBAAgB;oBAC9B,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,IAAI,OAAO,gBAAgB,IAAI,OAAO,gBAAgB,CAAC,IAAI,OAAO,IAAI;gBACpE,iBAAiB,IAAI,CAAC;oBACpB,KAAK;oBACL,OAAO,OAAO,gBAAgB;oBAC9B,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,iBAAiB,IAAI,CACnB;gBACE,KAAK;gBACL,OAAO,OAAO,aAAa,CAAC,QAAQ;gBACpC,UAAU;gBACV,MAAM;gBACN,WAAW;YACb,GACA;gBACE,KAAK;gBACL,OAAO,OAAO,gBAAgB,CAAC,QAAQ;gBACvC,UAAU;gBACV,MAAM;gBACN,WAAW;YACb;YAGF,oCAAoC;YACpC,iBAAiB,IAAI,CACnB;gBACE,KAAK;gBACL,OAAO,OAAO,gBAAgB,CAAC,QAAQ;gBACvC,UAAU;gBACV,MAAM;gBACN,WAAW;YACb,GACA;gBACE,KAAK;gBACL,OAAO,OAAO,kBAAkB,CAAC,QAAQ;gBACzC,UAAU;gBACV,MAAM;gBACN,WAAW;YACb,GACA;gBACE,KAAK;gBACL,OAAO,OAAO,0BAA0B,CAAC,QAAQ;gBACjD,UAAU;gBACV,MAAM;gBACN,WAAW;YACb;YAGF,+DAA+D;YAC/D,4CAA4C;YAE5C,8BAA8B;YAC9B,MAAM,iJAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;YAErC,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,IAAI,WAAW;QACb,qBACE,2XAAC;YAAI,WAAU;sBACb,cAAA,2XAAC;gBAAI,WAAU;;kCACb,2XAAC;;0CACC,2XAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,2XAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,2XAAC;wBAAI,WAAU;kCACb,cAAA,2XAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,2XAAC;QAAI,WAAU;kBACb,cAAA,2XAAC;YAAI,WAAU;;8BACb,2XAAC;;sCACC,2XAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,2XAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAGvC,2XAAC,wNAAA,CAAA,SAAM;oBACL,eAAe;oBACf,kBAAkB;oBAClB,UAAU;oBACV,oBAAoB;8BAEnB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,iBACnD,2XAAC,wNAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,2XAAC,oJAAA,CAAA,OAAI;;sDACH,2XAAC,oJAAA,CAAA,aAAU;sDACT,cAAA,2XAAC,oJAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,2XAAC,oJAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,2XAAC;oDAAI,WAAU;;sEACb,2XAAC;4DAAI,WAAU;;8EACb,2XAAC,qJAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAgB;;;;;;8EAC/B,2XAAC,wNAAA,CAAA,QAAK;oEACJ,IAAI,qJAAA,CAAA,QAAK;oEACT,IAAG;oEACH,MAAK;oEACL,aAAY;;;;;;gEAEb,OAAO,aAAa,IAAI,QAAQ,aAAa,kBAC5C,2XAAC;oEAAE,WAAU;8EAA4B,OAAO,aAAa;;;;;;;;;;;;sEAIjE,2XAAC;4DAAI,WAAU;;8EACb,2XAAC,qJAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAe;;;;;;8EAC9B,2XAAC,wNAAA,CAAA,QAAK;oEACJ,IAAI,qJAAA,CAAA,QAAK;oEACT,IAAG;oEACH,MAAK;oEACL,aAAY;;;;;;gEAEb,OAAO,YAAY,IAAI,QAAQ,YAAY,kBAC1C,2XAAC;oEAAE,WAAU;8EAA4B,OAAO,YAAY;;;;;;;;;;;;;;;;;;8DAKlE,2XAAC;oDAAI,WAAU;;sEACb,2XAAC,qJAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAgB;;;;;;sEAC/B,2XAAC,wNAAA,CAAA,QAAK;4DACJ,IAAI,qJAAA,CAAA,QAAK;4DACT,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,aAAY;;;;;;wDAEb,OAAO,aAAa,IAAI,QAAQ,aAAa,kBAC5C,2XAAC;4DAAE,WAAU;sEAA4B,OAAO,aAAa;;;;;;;;;;;;8DAIjE,2XAAC;oDAAI,WAAU;;sEACb,2XAAC,qJAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAmB;;;;;;sEAClC,2XAAC,wNAAA,CAAA,QAAK;4DACJ,IAAI,qJAAA,CAAA,QAAK;4DACT,IAAG;4DACH,MAAK;4DACL,aAAY;;;;;;wDAEb,OAAO,gBAAgB,IAAI,QAAQ,gBAAgB,kBAClD,2XAAC;4DAAE,WAAU;sEAA4B,OAAO,gBAAgB;;;;;;;;;;;;8DAIpE,2XAAC;oDAAI,WAAU;;sEACb,2XAAC,qJAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAmB;;;;;;sEAClC,2XAAC,wNAAA,CAAA,QAAK;4DACJ,IAAI,wJAAA,CAAA,WAAQ;4DACZ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,MAAM;;;;;;wDAEP,OAAO,gBAAgB,IAAI,QAAQ,gBAAgB,kBAClD,2XAAC;4DAAE,WAAU;sEAA4B,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAQxE,2XAAC,oJAAA,CAAA,OAAI;;sDACH,2XAAC,oJAAA,CAAA,aAAU;sDACT,cAAA,2XAAC,oJAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,2XAAC,oJAAA,CAAA,cAAW;sDACV,cAAA,2XAAC,8JAAA,CAAA,aAAU;gDACT,cAAc,OAAO;oDACnB,IAAI;wDACF,MAAM,SAAS,MAAM,uJAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,MAAM;4DAClD,YAAY;4DACZ,QAAQ;wDACV;wDACA,IAAI,OAAO,OAAO,EAAE;4DAClB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DACd,uCAAuC;4DACvC,WAAW,IAAM,OAAO,QAAQ,CAAC,MAAM,IAAI;wDAC7C;oDACF,EAAE,OAAO,OAAO;wDACd,QAAQ,KAAK,CAAC,sBAAsB;wDACpC,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oDACd;gDACF;;;;;;;;;;;;;;;;;8CAMN,2XAAC,oJAAA,CAAA,OAAI;;sDACH,2XAAC,oJAAA,CAAA,aAAU;sDACT,cAAA,2XAAC,oJAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,2XAAC,oJAAA,CAAA,cAAW;sDACV,cAAA,2XAAC,8JAAA,CAAA,gBAAa;gDACZ,cAAc,OAAO;oDACnB,IAAI;wDACF,MAAM,SAAS,MAAM,uJAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,MAAM;4DAClD,YAAY;4DACZ,QAAQ;wDACV;wDACA,IAAI,OAAO,OAAO,EAAE;4DAClB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DACd,uCAAuC;4DACvC,WAAW,IAAM,OAAO,QAAQ,CAAC,MAAM,IAAI;wDAC7C;oDACF,EAAE,OAAO,OAAO;wDACd,QAAQ,KAAK,CAAC,yBAAyB;wDACvC,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oDACd;gDACF;;;;;;;;;;;;;;;;;8CAMN,2XAAC,oJAAA,CAAA,OAAI;;sDACH,2XAAC,oJAAA,CAAA,aAAU;sDACT,cAAA,2XAAC,oJAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,2XAAC,oJAAA,CAAA,cAAW;4CAAC,WAAU;sDAErB,cAAA,2XAAC;gDAAI,WAAU;;kEACb,2XAAC;wDAAI,WAAU;;0EACb,2XAAC;;kFACC,2XAAC,qJAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,2XAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,2XAAC,sJAAA,CAAA,SAAM;gEACL,SAAS,OAAO,gBAAgB;gEAChC,iBAAiB,CAAC,UAAY,cAAc,oBAAoB;;;;;;;;;;;;kEAIpE,2XAAC;wDAAI,WAAU;;0EACb,2XAAC;;kFACC,2XAAC,qJAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,2XAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,2XAAC,sJAAA,CAAA,SAAM;gEACL,SAAS,OAAO,kBAAkB;gEAClC,iBAAiB,CAAC,UAAY,cAAc,sBAAsB;;;;;;;;;;;;kEAItE,2XAAC;wDAAI,WAAU;;0EACb,2XAAC;;kFACC,2XAAC,qJAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,2XAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,2XAAC,sJAAA,CAAA,SAAM;gEACL,SAAS,OAAO,0BAA0B;gEAC1C,iBAAiB,CAAC,UAAY,cAAc,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQpF,2XAAC;oCAAI,WAAU;;sDACb,2XAAC,sJAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,SAAQ;sDAAU;;;;;;sDAGxC,2XAAC;4CAAI,WAAU;;gDACZ,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,mBAC5B,2XAAC;oDAAI,WAAU;;wDAA2B;wDACX,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC;;;;;;;8DAG1D,2XAAC,sJAAA,CAAA,SAAM;oDACL,MAAK;oDACL,UAAU,YAAY,CAAC;8DAGtB,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C", "debugId": null}}]}