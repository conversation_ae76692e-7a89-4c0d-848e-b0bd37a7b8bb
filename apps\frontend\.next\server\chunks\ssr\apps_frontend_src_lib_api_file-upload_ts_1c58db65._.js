module.exports = {

"[project]/apps/frontend/src/lib/api/file-upload.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/apps/frontend/src/lib/api/file-upload.ts [app-ssr] (ecmascript)");
    });
});
}}),

};