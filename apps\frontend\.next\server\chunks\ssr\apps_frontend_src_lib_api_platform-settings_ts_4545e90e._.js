module.exports = {

"[project]/apps/frontend/src/lib/api/platform-settings.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Platform Settings API Client
 * Comprehensive API client for platform settings management
 */ __turbopack_context__.s({
    "clearStorageCache": (()=>clearStorageCache),
    "default": (()=>__TURBOPACK__default__export__),
    "getPlatformSettings": (()=>getPlatformSettings),
    "getStorageConfig": (()=>getStorageConfig),
    "getStorageSummary": (()=>getStorageSummary),
    "initStorageSettings": (()=>initStorageSettings),
    "platformSettingsAPI": (()=>platformSettingsAPI),
    "processFavicon": (()=>processFavicon),
    "removePlatformFavicon": (()=>removePlatformFavicon),
    "removePlatformLogo": (()=>removePlatformLogo),
    "testStorageConfig": (()=>testStorageConfig),
    "updateS3Config": (()=>updateS3Config),
    "updateStorageProvider": (()=>updateStorageProvider),
    "uploadHealthCheck": (()=>uploadHealthCheck),
    "uploadPlatformFavicon": (()=>uploadPlatformFavicon),
    "uploadPlatformLogo": (()=>uploadPlatformLogo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/frontend/src/lib/api.ts [app-ssr] (ecmascript)");
;
class PlatformSettingsAPI {
    /**
   * Get platform settings
   */ async getPlatformSettings() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get('/api/platform/settings');
    }
    /**
   * Upload platform logo
   */ async uploadPlatformLogo(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('uploadType', 'platform_logo');
        console.log('📤 Uploading platform logo:', {
            name: file.name,
            size: file.size,
            type: file.type
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].postFormData('/api/file-upload', formData);
    }
    /**
   * Upload platform favicon
   */ async uploadPlatformFavicon(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('uploadType', 'platform_favicon');
        console.log('📤 Uploading platform favicon:', {
            name: file.name,
            size: file.size,
            type: file.type
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].postFormData('/api/file-upload', formData);
    }
    /**
   * Remove platform logo
   */ async removePlatformLogo() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/api/platform/settings/bulk', [
            {
                key: 'platform_logo',
                value: '',
                category: 'platform'
            }
        ]);
    }
    /**
   * Remove platform favicon
   */ async removePlatformFavicon() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/api/platform/settings/bulk', [
            {
                key: 'platform_favicon',
                value: '',
                category: 'platform'
            }
        ]);
    }
    /**
   * Process favicon from image (generates multiple sizes)
   */ async processFavicon(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('uploadType', 'platform_favicon');
        formData.append('processFavicon', 'true');
        console.log('🔖 Processing favicon:', {
            name: file.name,
            size: file.size,
            type: file.type
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].postFormData('/api/file-upload', formData);
    }
    /**
   * Get storage configuration
   */ async getStorageConfig() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get('/api/platform/storage/config');
    }
    /**
   * Update storage provider
   */ async updateStorageProvider(provider) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/api/platform/storage/provider', {
            provider
        });
    }
    /**
   * Update S3 configuration
   */ async updateS3Config(config) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/api/platform/storage/s3', config);
    }
    /**
   * Test storage configuration
   */ async testStorageConfig() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/api/platform/storage/test', {});
    }
    /**
   * Clear storage configuration cache
   */ async clearStorageCache() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/api/platform/storage/cache/clear', {});
    }
    /**
   * Initialize storage settings
   */ async initStorageSettings() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/api/platform/storage/init', {});
    }
    /**
   * Get storage settings summary
   */ async getStorageSummary() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get('/api/platform/storage/summary');
    }
    /**
   * Health check for upload system
   */ async uploadHealthCheck() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$frontend$2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get('/api/platform/upload/health');
    }
}
const platformSettingsAPI = new PlatformSettingsAPI();
const getPlatformSettings = ()=>platformSettingsAPI.getPlatformSettings();
const uploadPlatformLogo = (file)=>platformSettingsAPI.uploadPlatformLogo(file);
const uploadPlatformFavicon = (file)=>platformSettingsAPI.uploadPlatformFavicon(file);
const removePlatformLogo = ()=>platformSettingsAPI.removePlatformLogo();
const removePlatformFavicon = ()=>platformSettingsAPI.removePlatformFavicon();
const processFavicon = (file)=>platformSettingsAPI.processFavicon(file);
const getStorageConfig = ()=>platformSettingsAPI.getStorageConfig();
const updateStorageProvider = (provider)=>platformSettingsAPI.updateStorageProvider(provider);
const updateS3Config = (config)=>platformSettingsAPI.updateS3Config(config);
const testStorageConfig = ()=>platformSettingsAPI.testStorageConfig();
const clearStorageCache = ()=>platformSettingsAPI.clearStorageCache();
const initStorageSettings = ()=>platformSettingsAPI.initStorageSettings();
const getStorageSummary = ()=>platformSettingsAPI.getStorageSummary();
const uploadHealthCheck = ()=>platformSettingsAPI.uploadHealthCheck();
const __TURBOPACK__default__export__ = platformSettingsAPI;
}}),

};

//# sourceMappingURL=apps_frontend_src_lib_api_platform-settings_ts_4545e90e._.js.map