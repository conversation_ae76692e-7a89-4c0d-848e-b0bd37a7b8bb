module.exports = {

"[project]/apps/frontend/src/lib/api/platform-settings.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/apps_frontend_src_lib_api_platform-settings_ts_4545e90e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/apps/frontend/src/lib/api/platform-settings.ts [app-ssr] (ecmascript)");
    });
});
}}),

};