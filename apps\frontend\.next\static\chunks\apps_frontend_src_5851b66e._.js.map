{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/sidebar/useSidebarStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\n\n// Types\nexport type UserType = 'super_admin' | 'institute_admin' | 'student'\n\nexport interface NavigationItem {\n  id: string\n  label: string\n  icon: string\n  href: string\n  badge?: number\n  isActive?: boolean\n  children?: NavigationItem[]\n  permissions?: string[]\n  description?: string\n  isModal?: boolean\n}\n\nexport interface SidebarSection {\n  id: string\n  title: string\n  items: NavigationItem[]\n  isCollapsed?: boolean\n}\n\nexport interface BreadcrumbItem {\n  label: string\n  href?: string\n  isActive?: boolean\n}\n\nexport interface SidebarState {\n  // Layout State\n  isCollapsed: boolean\n  isMobileOpen: boolean\n  userType: UserType\n  \n  // Navigation State\n  activeItem: string\n  activeSection: string\n  navigationItems: NavigationItem[]\n  sections: SidebarSection[]\n  breadcrumbs: BreadcrumbItem[]\n  \n  // Search State\n  searchQuery: string\n  searchResults: NavigationItem[]\n  isSearching: boolean\n  \n  // Favorites & Recent\n  favoriteItems: string[]\n  recentItems: string[]\n  \n  // Notifications\n  notifications: Array<{\n    id: string\n    title: string\n    message: string\n    type: 'info' | 'warning' | 'error' | 'success'\n    timestamp: string\n    isRead: boolean\n  }>\n  unreadCount: number\n}\n\ninterface SidebarStore extends SidebarState {\n  // Layout Actions\n  toggleSidebar: () => void\n  setSidebarCollapsed: (collapsed: boolean) => void\n  toggleMobileSidebar: () => void\n  setMobileSidebarOpen: (open: boolean) => void\n  setUserType: (userType: UserType) => void\n  \n  // Navigation Actions\n  setActiveItem: (itemId: string) => void\n  setActiveSection: (sectionId: string) => void\n  setNavigationItems: (items: NavigationItem[]) => void\n  setSections: (sections: SidebarSection[]) => void\n  setBreadcrumbs: (breadcrumbs: BreadcrumbItem[]) => void\n  updateItemBadge: (itemId: string, badge: number) => void\n  \n  // Search Actions\n  setSearchQuery: (query: string) => void\n  performSearch: (query: string) => void\n  clearSearch: () => void\n  \n  // Favorites & Recent Actions\n  addToFavorites: (itemId: string) => void\n  removeFromFavorites: (itemId: string) => void\n  addToRecent: (itemId: string) => void\n  clearRecent: () => void\n  \n  // Notification Actions\n  addNotification: (notification: Omit<SidebarState['notifications'][0], 'id' | 'timestamp' | 'isRead'>) => void\n  markNotificationAsRead: (notificationId: string) => void\n  markAllNotificationsAsRead: () => void\n  removeNotification: (notificationId: string) => void\n  clearNotifications: () => void\n  \n  // Utility Actions\n  initializeNavigation: (userType: UserType) => void\n  filterNavigationByPermissions: (userPermissions?: string[]) => void\n  resetState: () => void\n}\n\n// Helper function to filter navigation items based on user permissions\nconst filterNavigationByRole = (items: NavigationItem[], userType: UserType, userPermissions?: string[]): NavigationItem[] => {\n  return items.map(item => {\n    // For super admin, return all items without filtering\n    if (userType === 'super_admin') {\n      return item\n    }\n\n    // Filter children if they exist\n    let filteredChildren = item.children\n    if (item.children && item.children.length > 0) {\n      filteredChildren = item.children.filter(child => {\n        // If child has permissions, check if user role is included\n        if (child.permissions && child.permissions.length > 0) {\n          return child.permissions.includes(userType) ||\n                 (userPermissions && child.permissions.some(perm => userPermissions.includes(perm)))\n        }\n        // If no permissions specified, allow access\n        return true\n      })\n    }\n\n    // Return item with filtered children\n    return {\n      ...item,\n      children: filteredChildren\n    }\n  }).filter(item => {\n    // Filter parent items based on permissions\n    if (item.permissions && item.permissions.length > 0) {\n      return item.permissions.includes(userType) ||\n             (userPermissions && item.permissions.some(perm => userPermissions.includes(perm)))\n    }\n    // If no permissions specified, allow access\n    return true\n  })\n}\n\n// Default navigation items for each user type\nconst getDefaultNavigation = (userType: UserType): NavigationItem[] => {\n  switch (userType) {\n    case 'super_admin':\n      return [\n        {\n          id: 'dashboard',\n          label: 'Dashboard',\n          icon: 'LayoutDashboard',\n          href: '/super-admin',\n          description: 'Overview and analytics'\n        },\n        {\n          id: 'institutes',\n          label: 'Institute Management',\n          icon: 'Building2',\n          href: '/super-admin/institutes',\n          description: 'Manage institutes and verification'\n        },\n        {\n          id: 'users',\n          label: 'User Management',\n          icon: 'Users',\n          href: '/super-admin/users',\n          description: 'Manage all platform users'\n        },\n        {\n          id: 'billing',\n          label: 'Billing & Finance',\n          icon: 'CreditCard',\n          href: '/super-admin/billing',\n          description: 'Financial management and billing'\n        },\n        {\n          id: 'platform-blog',\n          label: 'Platform Blog',\n          icon: 'FileText',\n          href: '/super-admin/platform-blog',\n          description: 'Manage platform-wide blog posts and announcements'\n        },\n        {\n          id: 'themes',\n          label: 'Theme Management',\n          icon: 'Palette',\n          href: '/super-admin/themes',\n          description: 'Platform and institute themes'\n        },\n        {\n          id: 'analytics',\n          label: 'Analytics & Reports',\n          icon: 'BarChart3',\n          href: '/super-admin/analytics',\n          description: 'Platform analytics and reports'\n        },\n        {\n          id: 'locations',\n          label: 'Location Management',\n          icon: 'MapPin',\n          href: '/super-admin/locations',\n          description: 'Manage countries, states, and districts'\n        },\n       \n        {\n          id: 'tax-management',\n          label: 'Tax Management',\n          icon: 'Calculator',\n          href: '/super-admin/tax-management',\n          description: 'Manage tax components, groups, and rules'\n        },\n        {\n          id: 'role-permissions',\n          label: 'Roles & Permissions',\n          icon: 'Shield',\n          href: '/super-admin/role-permissions',\n          description: 'Manage user roles and permissions'\n        },\n        {\n          id: 'gateway-management',\n          label: 'Gateway Management',\n          icon: 'CreditCard',\n          href: '/super-admin/gateway-management',\n          description: 'Manage payment gateway providers and configurations'          \n        },\n        {\n          id: 'settings',\n          label: 'System Settings',\n          icon: 'Settings',\n          href: '/super-admin/settings',\n          description: 'System configuration and settings',          \n        }\n      ]\n    \n    case 'institute_admin':\n      return [\n        {\n          id: 'dashboard',\n          label: 'Dashboard',\n          icon: 'LayoutDashboard',\n          href: '/admin',\n          description: 'Institute overview and analytics'\n        },\n        {\n          id: 'courses',\n          label: 'Course Management',\n          icon: 'BookOpen',\n          href: '/admin/courses',\n          description: 'Manage courses and curriculum'\n        },\n        {\n          id: 'students',\n          label: 'Student Management',\n          icon: 'GraduationCap',\n          href: '/admin/students',\n          description: 'Manage student enrollments and progress'\n        },\n        {\n          id: 'staff',\n          label: 'Staff Management',\n          icon: 'Users',\n          href: '/admin/staff',\n          description: 'Manage staff members',\n          permissions: ['institute_admin', 'branch_manager']\n        },\n        {\n          id: 'branches',\n          label: 'Branch Management',\n          icon: 'MapPin',\n          href: '/admin/branches',\n          description: 'Manage institute branches and locations'\n        },\n        {\n          id: 'billing',\n          label: 'Billing & Payments',\n          icon: 'CreditCard',\n          href: '/admin/billing',\n          description: 'Student billing and payment management'\n        },\n        {\n          id: 'analytics',\n          label: 'Analytics & Reports',\n          icon: 'BarChart3',\n          href: '/admin/analytics',\n          description: 'Institute analytics and performance reports'\n        },\n        {\n          id: 'blog',\n          label: 'Blog Management',\n          icon: 'PenTool',\n          href: '/admin/blog',\n          description: 'Manage institute blog and content'         \n        },       \n        {\n          id: 'settings',\n          label: 'Institute Settings',\n          icon: 'Settings',\n          href: '/admin/settings',\n          description: 'Institute configuration and preferences'          \n        }\n      ]\n    \n    case 'student':\n      return [\n        {\n          id: 'dashboard',\n          label: 'Dashboard',\n          icon: 'LayoutDashboard',\n          href: '/student',\n          description: 'Your learning dashboard'\n        },\n        {\n          id: 'my-courses',\n          label: 'My Courses',\n          icon: 'BookOpen',\n          href: '/student/courses',\n          description: 'Your enrolled courses and progress'\n        },\n        {\n          id: 'marketplace',\n          label: 'Course Marketplace',\n          icon: 'ShoppingCart',\n          href: '/student/marketplace',\n          description: 'Browse and purchase new courses'\n        },\n        {\n          id: 'assignments',\n          label: 'Assignments & Exams',\n          icon: 'FileText',\n          href: '/student/assignments',\n          description: 'View and submit assignments'\n        },\n        {\n          id: 'live-classes',\n          label: 'Live Classes',\n          icon: 'Video',\n          href: '/student/live-classes',\n          description: 'Join live classes and webinars'\n        },\n        {\n          id: 'progress',\n          label: 'Progress & Analytics',\n          icon: 'TrendingUp',\n          href: '/student/progress',\n          description: 'Track your learning progress'\n        },\n        {\n          id: 'community',\n          label: 'Community',\n          icon: 'MessageCircle',\n          href: '/student/community',\n          description: 'Connect with peers and instructors'\n        },\n        {\n          id: 'payments',\n          label: 'Payments & Billing',\n          icon: 'CreditCard',\n          href: '/student/payments',\n          description: 'Manage payments and billing history'\n        },\n        {\n          id: 'account',\n          label: 'Account Settings',\n          icon: 'User',\n          href: '/student/account',\n          description: 'Manage your account and preferences'\n        },\n        {\n          id: 'support',\n          label: 'Support & Help',\n          icon: 'HelpCircle',\n          href: '/student/support',\n          description: 'Get help and contact support'\n        }\n      ]\n    \n    default:\n      return []\n  }\n}\n\nconst initialState: SidebarState = {\n  isCollapsed: false,\n  isMobileOpen: false,\n  userType: 'super_admin',\n  activeItem: '',\n  activeSection: '',\n  navigationItems: [],\n  sections: [],\n  breadcrumbs: [],\n  searchQuery: '',\n  searchResults: [],\n  isSearching: false,\n  favoriteItems: [],\n  recentItems: [],\n  notifications: [],\n  unreadCount: 0\n}\n\nexport const useSidebarStore = create<SidebarStore>()(\n  persist(\n    (set, get) => ({\n      ...initialState,\n\n      // Layout Actions\n      toggleSidebar: () => set((state) => ({ isCollapsed: !state.isCollapsed })),\n      setSidebarCollapsed: (collapsed) => set({ isCollapsed: collapsed }),\n      toggleMobileSidebar: () => set((state) => ({ isMobileOpen: !state.isMobileOpen })),\n      setMobileSidebarOpen: (open) => set({ isMobileOpen: open }),\n      setUserType: (userType) => {\n        set({ userType })\n        get().initializeNavigation(userType)\n      },\n\n      // Navigation Actions\n      setActiveItem: (itemId) => {\n        set({ activeItem: itemId })\n        get().addToRecent(itemId)\n      },\n      setActiveSection: (sectionId) => set({ activeSection: sectionId }),\n      setNavigationItems: (items) => set({ navigationItems: items }),\n      setSections: (sections) => set({ sections }),\n      setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),\n      updateItemBadge: (itemId, badge) => {\n        set((state) => ({\n          navigationItems: state.navigationItems.map(item =>\n            item.id === itemId ? { ...item, badge } : item\n          )\n        }))\n      },\n\n      // Search Actions\n      setSearchQuery: (query) => set({ searchQuery: query }),\n      performSearch: (query) => {\n        set({ isSearching: true, searchQuery: query })\n        \n        const { navigationItems } = get()\n        const results = navigationItems.filter(item =>\n          item.label.toLowerCase().includes(query.toLowerCase()) ||\n          item.description?.toLowerCase().includes(query.toLowerCase())\n        )\n        \n        set({ searchResults: results, isSearching: false })\n      },\n      clearSearch: () => set({ searchQuery: '', searchResults: [], isSearching: false }),\n\n      // Favorites & Recent Actions\n      addToFavorites: (itemId) => {\n        set((state) => ({\n          favoriteItems: state.favoriteItems.includes(itemId)\n            ? state.favoriteItems\n            : [...state.favoriteItems, itemId]\n        }))\n      },\n      removeFromFavorites: (itemId) => {\n        set((state) => ({\n          favoriteItems: state.favoriteItems.filter(id => id !== itemId)\n        }))\n      },\n      addToRecent: (itemId) => {\n        set((state) => {\n          const filtered = state.recentItems.filter(id => id !== itemId)\n          return {\n            recentItems: [itemId, ...filtered].slice(0, 10) // Keep only last 10\n          }\n        })\n      },\n      clearRecent: () => set({ recentItems: [] }),\n\n      // Notification Actions\n      addNotification: (notification) => {\n        const newNotification = {\n          ...notification,\n          id: Date.now().toString(),\n          timestamp: new Date().toISOString(),\n          isRead: false\n        }\n        \n        set((state) => ({\n          notifications: [newNotification, ...state.notifications],\n          unreadCount: state.unreadCount + 1\n        }))\n      },\n      markNotificationAsRead: (notificationId) => {\n        set((state) => ({\n          notifications: state.notifications.map(notification =>\n            notification.id === notificationId\n              ? { ...notification, isRead: true }\n              : notification\n          ),\n          unreadCount: Math.max(0, state.unreadCount - 1)\n        }))\n      },\n      markAllNotificationsAsRead: () => {\n        set((state) => ({\n          notifications: state.notifications.map(notification => ({\n            ...notification,\n            isRead: true\n          })),\n          unreadCount: 0\n        }))\n      },\n      removeNotification: (notificationId) => {\n        set((state) => {\n          const notification = state.notifications.find(n => n.id === notificationId)\n          return {\n            notifications: state.notifications.filter(n => n.id !== notificationId),\n            unreadCount: notification && !notification.isRead \n              ? Math.max(0, state.unreadCount - 1)\n              : state.unreadCount\n          }\n        })\n      },\n      clearNotifications: () => set({ notifications: [], unreadCount: 0 }),\n\n      // Utility Actions\n      initializeNavigation: (userType) => {\n        const navigationItems = getDefaultNavigation(userType)\n        set({ navigationItems, userType })\n      },\n      filterNavigationByPermissions: (userPermissions) => {\n        const { navigationItems, userType } = get()\n        const filteredItems = filterNavigationByRole(navigationItems, userType, userPermissions)\n        set({ navigationItems: filteredItems })\n      },\n      resetState: () => set(initialState)\n    }),\n    {\n      name: 'sidebar-store',\n      partialize: (state) => ({\n        isCollapsed: state.isCollapsed,\n        userType: state.userType,\n        favoriteItems: state.favoriteItems,\n        recentItems: state.recentItems,\n        notifications: state.notifications,\n        unreadCount: state.unreadCount\n      })\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAyGA,uEAAuE;AACvE,MAAM,yBAAyB,CAAC,OAAyB,UAAoB;IAC3E,OAAO,MAAM,GAAG,CAAC,CAAA;QACf,sDAAsD;QACtD,IAAI,aAAa,eAAe;YAC9B,OAAO;QACT;QAEA,gCAAgC;QAChC,IAAI,mBAAmB,KAAK,QAAQ;QACpC,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC7C,mBAAmB,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA;gBACtC,2DAA2D;gBAC3D,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,MAAM,GAAG,GAAG;oBACrD,OAAO,MAAM,WAAW,CAAC,QAAQ,CAAC,aAC1B,mBAAmB,MAAM,WAAW,CAAC,IAAI,CAAC,CAAA,OAAQ,gBAAgB,QAAQ,CAAC;gBACrF;gBACA,4CAA4C;gBAC5C,OAAO;YACT;QACF;QAEA,qCAAqC;QACrC,OAAO;YACL,GAAG,IAAI;YACP,UAAU;QACZ;IACF,GAAG,MAAM,CAAC,CAAA;QACR,2CAA2C;QAC3C,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;YACnD,OAAO,KAAK,WAAW,CAAC,QAAQ,CAAC,aACzB,mBAAmB,KAAK,WAAW,CAAC,IAAI,CAAC,CAAA,OAAQ,gBAAgB,QAAQ,CAAC;QACpF;QACA,4CAA4C;QAC5C,OAAO;IACT;AACF;AAEA,8CAA8C;AAC9C,MAAM,uBAAuB,CAAC;IAC5B,OAAQ;QACN,KAAK;YACH,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBAEA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;aACD;QAEH,KAAK;YACH,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,aAAa;wBAAC;wBAAmB;qBAAiB;gBACpD;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;aACD;QAEH,KAAK;YACH,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;aACD;QAEH;YACE,OAAO,EAAE;IACb;AACF;AAEA,MAAM,eAA6B;IACjC,aAAa;IACb,cAAc;IACd,UAAU;IACV,YAAY;IACZ,eAAe;IACf,iBAAiB,EAAE;IACnB,UAAU,EAAE;IACZ,aAAa,EAAE;IACf,aAAa;IACb,eAAe,EAAE;IACjB,aAAa;IACb,eAAe,EAAE;IACjB,aAAa,EAAE;IACf,eAAe,EAAE;IACjB,aAAa;AACf;AAEO,MAAM,kBAAkB,CAAA,GAAA,wPAAA,CAAA,SAAM,AAAD,IAClC,CAAA,GAAA,6PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,GAAG,YAAY;QAEf,iBAAiB;QACjB,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,aAAa,CAAC,MAAM,WAAW;gBAAC,CAAC;QACxE,qBAAqB,CAAC,YAAc,IAAI;gBAAE,aAAa;YAAU;QACjE,qBAAqB,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,cAAc,CAAC,MAAM,YAAY;gBAAC,CAAC;QAChF,sBAAsB,CAAC,OAAS,IAAI;gBAAE,cAAc;YAAK;QACzD,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;YACf,MAAM,oBAAoB,CAAC;QAC7B;QAEA,qBAAqB;QACrB,eAAe,CAAC;YACd,IAAI;gBAAE,YAAY;YAAO;YACzB,MAAM,WAAW,CAAC;QACpB;QACA,kBAAkB,CAAC,YAAc,IAAI;gBAAE,eAAe;YAAU;QAChE,oBAAoB,CAAC,QAAU,IAAI;gBAAE,iBAAiB;YAAM;QAC5D,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAC1C,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QACnD,iBAAiB,CAAC,QAAQ;YACxB,IAAI,CAAC,QAAU,CAAC;oBACd,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,OACzC,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE;wBAAM,IAAI;gBAE9C,CAAC;QACH;QAEA,iBAAiB;QACjB,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QACpD,eAAe,CAAC;YACd,IAAI;gBAAE,aAAa;gBAAM,aAAa;YAAM;YAE5C,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,UAAU,gBAAgB,MAAM,CAAC,CAAA,OACrC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACnD,KAAK,WAAW,EAAE,cAAc,SAAS,MAAM,WAAW;YAG5D,IAAI;gBAAE,eAAe;gBAAS,aAAa;YAAM;QACnD;QACA,aAAa,IAAM,IAAI;gBAAE,aAAa;gBAAI,eAAe,EAAE;gBAAE,aAAa;YAAM;QAEhF,6BAA6B;QAC7B,gBAAgB,CAAC;YACf,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,QAAQ,CAAC,UACxC,MAAM,aAAa,GACnB;2BAAI,MAAM,aAAa;wBAAE;qBAAO;gBACtC,CAAC;QACH;QACA,qBAAqB,CAAC;YACpB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;gBACzD,CAAC;QACH;QACA,aAAa,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,WAAW,MAAM,WAAW,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;gBACvD,OAAO;oBACL,aAAa;wBAAC;2BAAW;qBAAS,CAAC,KAAK,CAAC,GAAG,IAAI,oBAAoB;gBACtE;YACF;QACF;QACA,aAAa,IAAM,IAAI;gBAAE,aAAa,EAAE;YAAC;QAEzC,uBAAuB;QACvB,iBAAiB,CAAC;YAChB,MAAM,kBAAkB;gBACtB,GAAG,YAAY;gBACf,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;YACV;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBAAC;2BAAoB,MAAM,aAAa;qBAAC;oBACxD,aAAa,MAAM,WAAW,GAAG;gBACnC,CAAC;QACH;QACA,wBAAwB,CAAC;YACvB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,eACrC,aAAa,EAAE,KAAK,iBAChB;4BAAE,GAAG,YAAY;4BAAE,QAAQ;wBAAK,IAChC;oBAEN,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;gBAC/C,CAAC;QACH;QACA,4BAA4B;YAC1B,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,eAAgB,CAAC;4BACtD,GAAG,YAAY;4BACf,QAAQ;wBACV,CAAC;oBACD,aAAa;gBACf,CAAC;QACH;QACA,oBAAoB,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,eAAe,MAAM,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC5D,OAAO;oBACL,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxD,aAAa,gBAAgB,CAAC,aAAa,MAAM,GAC7C,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG,KAChC,MAAM,WAAW;gBACvB;YACF;QACF;QACA,oBAAoB,IAAM,IAAI;gBAAE,eAAe,EAAE;gBAAE,aAAa;YAAE;QAElE,kBAAkB;QAClB,sBAAsB,CAAC;YACrB,MAAM,kBAAkB,qBAAqB;YAC7C,IAAI;gBAAE;gBAAiB;YAAS;QAClC;QACA,+BAA+B,CAAC;YAC9B,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG;YACtC,MAAM,gBAAgB,uBAAuB,iBAAiB,UAAU;YACxE,IAAI;gBAAE,iBAAiB;YAAc;QACvC;QACA,YAAY,IAAM,IAAI;IACxB,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,aAAa,MAAM,WAAW;YAC9B,UAAU,MAAM,QAAQ;YACxB,eAAe,MAAM,aAAa;YAClC,aAAa,MAAM,WAAW;YAC9B,eAAe,MAAM,aAAa;YAClC,aAAa,MAAM,WAAW;QAChC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/auth/useAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\n\ninterface Role {\n  id: number\n  name: string\n  code: string\n  description?: string\n  level: string\n  permissions: any[]\n  scope: {\n    institute: number | null\n    branch: number | null\n  }\n  isActive: boolean\n  isSystemRole: boolean\n}\n\ninterface User {\n  id: string\n  email: string\n  firstName: string\n  lastName: string\n  role: Role | null // New role relationship object\n  legacyRole: 'super_admin' | 'platform_staff' | 'institute_admin' | 'branch_manager' | 'trainer' | 'institute_staff' | 'student' // Legacy string role\n  avatar?: string\n  institute?: string\n  isActive: boolean\n  lastLogin?: string\n}\n\ninterface AuthState {\n  user: User | null\n  token: string | null\n  isLoading: boolean\n  isAuthenticated: boolean\n\n  // Actions\n  login: (email: string, password: string, userType?: string) => Promise<void>\n  register: (userData: any, userType?: string) => Promise<void>\n  logout: () => void\n  setUser: (user: User | null) => void\n  setToken: (token: string | null) => void\n  setLoading: (loading: boolean) => void\n  initialize: () => void\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      token: null,\n      isLoading: false, // Start with false to prevent loading loops\n      isAuthenticated: false,\n\n      login: async (email: string, password: string, userType = 'student') => {\n        set({ isLoading: true })\n\n        try {\n          // Use the auth login endpoint\n          const endpoint = '/api/auth/login'\n\n          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            credentials: 'include', // Include cookies for cross-origin requests\n            body: JSON.stringify({\n              email,\n              password,\n              userType\n            }),\n          })\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            throw new Error(errorData.message || errorData.error || 'Login failed')\n          }\n\n          const data = await response.json()\n\n          // Validate that the user has the expected role using legacyRole\n          if (userType === 'super_admin' && !['super_admin', 'platform_staff'].includes(data.user.legacyRole)) {\n            throw new Error('Access denied. Super admin privileges required.')\n          }\n          if (userType === 'institute_admin' && !['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(data.user.legacyRole)) {\n            throw new Error('Access denied. Institute admin privileges required.')\n          }\n          if (userType === 'student' && data.user.legacyRole !== 'student') {\n            throw new Error('Access denied. Student account required.')\n          }\n\n          // Store token in localStorage first\n          if (data.token) {\n            localStorage.setItem('auth_token', data.token)\n          }\n\n          // Store additional data for institute admin\n          if (userType === 'institute_admin' && data.user) {\n            // Store user data\n            localStorage.setItem('user_data', JSON.stringify(data.user))\n\n            // Store institute details if available\n            if (data.user.institute) {\n              localStorage.setItem('institute_data', JSON.stringify(data.user.institute))\n            }\n\n            // Store permissions if available\n            if (data.user.role && data.user.role.permissions) {\n              localStorage.setItem('user_permissions', JSON.stringify({\n                role: data.user.role.name || data.user.legacyRole,\n                level: data.user.role.level || 2,\n                permissions: data.user.role.permissions,\n                instituteId: data.user.institute?.id || data.user.institute\n              }))\n            }\n          }\n\n          // Store additional data for super admin\n          if (userType === 'super_admin' && data.user) {\n            localStorage.setItem('user_data', JSON.stringify(data.user))\n\n            if (data.user.role && data.user.role.permissions) {\n              localStorage.setItem('user_permissions', JSON.stringify({\n                role: data.user.role.name || data.user.legacyRole,\n                level: data.user.role.level || 1,\n                permissions: data.user.role.permissions\n              }))\n            }\n          }\n\n          // Then set the auth state\n          set({\n            user: data.user,\n            token: data.token,\n            isAuthenticated: true,\n            isLoading: false\n          })\n\n          // Force persist to localStorage immediately\n          setTimeout(() => {\n            console.log('Auth state set after login:', {\n              user: data.user.email,\n              isAuthenticated: true,\n              userType: userType,\n              storedData: {\n                auth_token: !!localStorage.getItem('auth_token'),\n                user_data: !!localStorage.getItem('user_data'),\n                user_permissions: !!localStorage.getItem('user_permissions'),\n                institute_data: !!localStorage.getItem('institute_data')\n              }\n            })\n          }, 0)\n\n        } catch (error) {\n          set({ isLoading: false })\n          throw error\n        }\n      },\n\n      register: async (userData: any, userType = 'student') => {\n        set({ isLoading: true })\n\n        try {\n          // Determine the correct API endpoint based on user type\n          let endpoint = '/api/auth/register'\n          if (userType === 'institute') {\n            endpoint = '/api/auth/register' // Institute registration endpoint\n          }\n\n          const requestData = {\n            ...userData,\n            role: userType === 'institute' ? 'institute_admin' : 'student'\n          }\n\n          // Debug: Log the data being sent\n          console.log('Registration data being sent:', requestData)\n          console.log('Endpoint:', `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`)\n\n          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            credentials: 'include',\n            body: JSON.stringify(requestData),\n          })\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            throw new Error(errorData.message || errorData.error || 'Registration failed')\n          }\n\n          const data = await response.json()\n\n          // For institute registration, don't auto-login (needs approval)\n          if (userType === 'institute') {\n            set({ isLoading: false })\n            return\n          }\n\n          // For student registration, auto-login\n          set({\n            user: data.user,\n            token: data.token,\n            isAuthenticated: true,\n            isLoading: false\n          })\n\n          if (data.token) {\n            localStorage.setItem('auth_token', data.token)\n          }\n\n        } catch (error) {\n          set({ isLoading: false })\n          throw error\n        }\n      },\n\n      logout: () => {\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false\n        })\n        \n        // Clear localStorage\n        localStorage.removeItem('auth_token')\n        localStorage.removeItem('user_data')\n        localStorage.removeItem('user_permissions')\n        localStorage.removeItem('institute_data')\n        \n        // Redirect based on user role using legacyRole\n        const { user } = get()\n        if (user?.legacyRole === 'super_admin' || user?.legacyRole === 'platform_staff') {\n          window.location.href = '/auth/admin/login'\n        } else if (user?.legacyRole === 'student') {\n          window.location.href = '/auth/user-login'\n        } else {\n          window.location.href = '/auth/login'\n        }\n      },\n\n      setUser: (user: User | null) => {\n        set({ user, isAuthenticated: !!user })\n      },\n\n      setToken: (token: string | null) => {\n        set({ token })\n        if (token) {\n          localStorage.setItem('auth_token', token)\n        } else {\n          localStorage.removeItem('auth_token')\n        }\n      },\n\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading })\n      },\n\n      // Initialize auth state - check persisted state and localStorage\n      initialize: () => {\n        const currentState = get()\n        console.log('🔄 Initialize called, current state:', {\n          hasUser: !!currentState.user,\n          hasToken: !!currentState.token,\n          isAuthenticated: currentState.isAuthenticated,\n          isLoading: currentState.isLoading\n        })\n\n        // If we already have valid auth state, just ensure loading is false\n        if (currentState.user && currentState.token && currentState.isAuthenticated) {\n          console.log('✅ Auth already initialized from persisted state:', currentState.user.email)\n          set({ isLoading: false })\n          return\n        }\n\n        // Check Zustand persistence storage\n        const authStorage = localStorage.getItem('auth-storage')\n\n        console.log('🔍 Checking Zustand auth-storage:', {\n          hasAuthStorage: !!authStorage\n        })\n\n        if (authStorage) {\n          try {\n            const parsedStorage = JSON.parse(authStorage)\n            const { state } = parsedStorage\n\n            if (state && state.user && state.token && state.isAuthenticated) {\n              console.log('📦 Restoring auth state from Zustand storage:', {\n                email: state.user.email,\n                legacyRole: state.user.legacyRole,\n                hasToken: !!state.token\n              })\n\n              set({\n                user: state.user,\n                token: state.token,\n                isAuthenticated: state.isAuthenticated,\n                isLoading: false\n              })\n              return\n            }\n          } catch (error) {\n            console.error('❌ Failed to parse Zustand auth storage:', error)\n            localStorage.removeItem('auth-storage')\n          }\n        }\n\n        // If no valid persisted state, set to not authenticated\n        console.log('❌ No valid auth state found, setting to not authenticated')\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false\n        })\n      }\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated\n      })\n    }\n  )\n)\n\n// Note: Initialization is now handled by components that need auth\n"], "names": [], "mappings": ";;;AA8D0C;AA9D1C;AACA;;;AA8CO,MAAM,eAAe,CAAA,GAAA,wPAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,OAAO;QACP,WAAW;QACX,iBAAiB;QAEjB,OAAO,OAAO,OAAe,UAAkB,WAAW,SAAS;YACjE,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,WAAW;gBAEjB,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,0BAA0B,UAAU,EAAE;oBACvG,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,aAAa;oBACb,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA;wBACA;oBACF;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;gBAC1D;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,gEAAgE;gBAChE,IAAI,aAAa,iBAAiB,CAAC;oBAAC;oBAAe;iBAAiB,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,UAAU,GAAG;oBACnG,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,aAAa,qBAAqB,CAAC;oBAAC;oBAAmB;oBAAkB;oBAAW;iBAAkB,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,UAAU,GAAG;oBACzI,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,aAAa,aAAa,KAAK,IAAI,CAAC,UAAU,KAAK,WAAW;oBAChE,MAAM,IAAI,MAAM;gBAClB;gBAEA,oCAAoC;gBACpC,IAAI,KAAK,KAAK,EAAE;oBACd,aAAa,OAAO,CAAC,cAAc,KAAK,KAAK;gBAC/C;gBAEA,4CAA4C;gBAC5C,IAAI,aAAa,qBAAqB,KAAK,IAAI,EAAE;oBAC/C,kBAAkB;oBAClB,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,KAAK,IAAI;oBAE1D,uCAAuC;oBACvC,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE;wBACvB,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,KAAK,IAAI,CAAC,SAAS;oBAC3E;oBAEA,iCAAiC;oBACjC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;wBAChD,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;4BACtD,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,UAAU;4BACjD,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;4BAC/B,aAAa,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW;4BACvC,aAAa,KAAK,IAAI,CAAC,SAAS,EAAE,MAAM,KAAK,IAAI,CAAC,SAAS;wBAC7D;oBACF;gBACF;gBAEA,wCAAwC;gBACxC,IAAI,aAAa,iBAAiB,KAAK,IAAI,EAAE;oBAC3C,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,KAAK,IAAI;oBAE1D,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;wBAChD,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;4BACtD,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,UAAU;4BACjD,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;4BAC/B,aAAa,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW;wBACzC;oBACF;gBACF;gBAEA,0BAA0B;gBAC1B,IAAI;oBACF,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,iBAAiB;oBACjB,WAAW;gBACb;gBAEA,4CAA4C;gBAC5C,WAAW;oBACT,QAAQ,GAAG,CAAC,+BAA+B;wBACzC,MAAM,KAAK,IAAI,CAAC,KAAK;wBACrB,iBAAiB;wBACjB,UAAU;wBACV,YAAY;4BACV,YAAY,CAAC,CAAC,aAAa,OAAO,CAAC;4BACnC,WAAW,CAAC,CAAC,aAAa,OAAO,CAAC;4BAClC,kBAAkB,CAAC,CAAC,aAAa,OAAO,CAAC;4BACzC,gBAAgB,CAAC,CAAC,aAAa,OAAO,CAAC;wBACzC;oBACF;gBACF,GAAG;YAEL,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,MAAM;YACR;QACF;QAEA,UAAU,OAAO,UAAe,WAAW,SAAS;YAClD,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,wDAAwD;gBACxD,IAAI,WAAW;gBACf,IAAI,aAAa,aAAa;oBAC5B,WAAW,qBAAqB,kCAAkC;;gBACpE;gBAEA,MAAM,cAAc;oBAClB,GAAG,QAAQ;oBACX,MAAM,aAAa,cAAc,oBAAoB;gBACvD;gBAEA,iCAAiC;gBACjC,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,QAAQ,GAAG,CAAC,aAAa,GAAG,6DAAmC,0BAA0B,UAAU;gBAEnG,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,0BAA0B,UAAU,EAAE;oBACvG,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,aAAa;oBACb,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;gBAC1D;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,gEAAgE;gBAChE,IAAI,aAAa,aAAa;oBAC5B,IAAI;wBAAE,WAAW;oBAAM;oBACvB;gBACF;gBAEA,uCAAuC;gBACvC,IAAI;oBACF,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,iBAAiB;oBACjB,WAAW;gBACb;gBAEA,IAAI,KAAK,KAAK,EAAE;oBACd,aAAa,OAAO,CAAC,cAAc,KAAK,KAAK;gBAC/C;YAEF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,MAAM;YACR;QACF;QAEA,QAAQ;YACN,IAAI;gBACF,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,WAAW;YACb;YAEA,qBAAqB;YACrB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YAExB,+CAA+C;YAC/C,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,MAAM,eAAe,iBAAiB,MAAM,eAAe,kBAAkB;gBAC/E,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO,IAAI,MAAM,eAAe,WAAW;gBACzC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;YAAK;QACtC;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;YACZ,IAAI,OAAO;gBACT,aAAa,OAAO,CAAC,cAAc;YACrC,OAAO;gBACL,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,iEAAiE;QACjE,YAAY;YACV,MAAM,eAAe;YACrB,QAAQ,GAAG,CAAC,wCAAwC;gBAClD,SAAS,CAAC,CAAC,aAAa,IAAI;gBAC5B,UAAU,CAAC,CAAC,aAAa,KAAK;gBAC9B,iBAAiB,aAAa,eAAe;gBAC7C,WAAW,aAAa,SAAS;YACnC;YAEA,oEAAoE;YACpE,IAAI,aAAa,IAAI,IAAI,aAAa,KAAK,IAAI,aAAa,eAAe,EAAE;gBAC3E,QAAQ,GAAG,CAAC,oDAAoD,aAAa,IAAI,CAAC,KAAK;gBACvF,IAAI;oBAAE,WAAW;gBAAM;gBACvB;YACF;YAEA,oCAAoC;YACpC,MAAM,cAAc,aAAa,OAAO,CAAC;YAEzC,QAAQ,GAAG,CAAC,qCAAqC;gBAC/C,gBAAgB,CAAC,CAAC;YACpB;YAEA,IAAI,aAAa;gBACf,IAAI;oBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC;oBACjC,MAAM,EAAE,KAAK,EAAE,GAAG;oBAElB,IAAI,SAAS,MAAM,IAAI,IAAI,MAAM,KAAK,IAAI,MAAM,eAAe,EAAE;wBAC/D,QAAQ,GAAG,CAAC,iDAAiD;4BAC3D,OAAO,MAAM,IAAI,CAAC,KAAK;4BACvB,YAAY,MAAM,IAAI,CAAC,UAAU;4BACjC,UAAU,CAAC,CAAC,MAAM,KAAK;wBACzB;wBAEA,IAAI;4BACF,MAAM,MAAM,IAAI;4BAChB,OAAO,MAAM,KAAK;4BAClB,iBAAiB,MAAM,eAAe;4BACtC,WAAW;wBACb;wBACA;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2CAA2C;oBACzD,aAAa,UAAU,CAAC;gBAC1B;YACF;YAEA,wDAAwD;YACxD,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,WAAW;YACb;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;YAClB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH,IAIJ,mEAAmE", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/hooks/useResponsive.ts"], "sourcesContent": ["import { useState, useEffect } from 'react'\n\n// Breakpoint definitions (matching Tailwind CSS)\nexport const breakpoints = {\n  sm: 640,\n  md: 768,\n  lg: 1024,\n  xl: 1280,\n  '2xl': 1536\n} as const\n\nexport type Breakpoint = keyof typeof breakpoints\n\n// Hook to detect current screen size\nexport function useResponsive() {\n  const [screenSize, setScreenSize] = useState<{\n    width: number\n    height: number\n    isMobile: boolean\n    isTablet: boolean\n    isDesktop: boolean\n    isLarge: boolean\n    currentBreakpoint: Breakpoint | 'xs'\n  }>({\n    width: 0,\n    height: 0,\n    isMobile: false,\n    isTablet: false,\n    isDesktop: false,\n    isLarge: false,\n    currentBreakpoint: 'xs'\n  })\n\n  useEffect(() => {\n    const updateScreenSize = () => {\n      const width = window.innerWidth\n      const height = window.innerHeight\n\n      // Determine current breakpoint\n      let currentBreakpoint: Breakpoint | 'xs' = 'xs'\n      if (width >= breakpoints['2xl']) currentBreakpoint = '2xl'\n      else if (width >= breakpoints.xl) currentBreakpoint = 'xl'\n      else if (width >= breakpoints.lg) currentBreakpoint = 'lg'\n      else if (width >= breakpoints.md) currentBreakpoint = 'md'\n      else if (width >= breakpoints.sm) currentBreakpoint = 'sm'\n\n      setScreenSize({\n        width,\n        height,\n        isMobile: width < breakpoints.md,\n        isTablet: width >= breakpoints.md && width < breakpoints.lg,\n        isDesktop: width >= breakpoints.lg,\n        isLarge: width >= breakpoints.xl,\n        currentBreakpoint\n      })\n    }\n\n    // Initial call\n    updateScreenSize()\n\n    // Add event listener\n    window.addEventListener('resize', updateScreenSize)\n\n    // Cleanup\n    return () => window.removeEventListener('resize', updateScreenSize)\n  }, [])\n\n  return screenSize\n}\n\n// Hook to check if screen is at least a certain breakpoint\nexport function useBreakpoint(breakpoint: Breakpoint) {\n  const { width } = useResponsive()\n  return width >= breakpoints[breakpoint]\n}\n\n// Hook for media queries\nexport function useMediaQuery(query: string) {\n  const [matches, setMatches] = useState(false)\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia(query)\n    setMatches(mediaQuery.matches)\n\n    const handler = (event: MediaQueryListEvent) => {\n      setMatches(event.matches)\n    }\n\n    mediaQuery.addEventListener('change', handler)\n    return () => mediaQuery.removeEventListener('change', handler)\n  }, [query])\n\n  return matches\n}\n\n// Utility functions for responsive design\nexport const responsive = {\n  // Check if current screen is mobile\n  isMobile: () => window.innerWidth < breakpoints.md,\n  \n  // Check if current screen is tablet\n  isTablet: () => window.innerWidth >= breakpoints.md && window.innerWidth < breakpoints.lg,\n  \n  // Check if current screen is desktop\n  isDesktop: () => window.innerWidth >= breakpoints.lg,\n  \n  // Get current breakpoint\n  getCurrentBreakpoint: (): Breakpoint | 'xs' => {\n    const width = window.innerWidth\n    if (width >= breakpoints['2xl']) return '2xl'\n    if (width >= breakpoints.xl) return 'xl'\n    if (width >= breakpoints.lg) return 'lg'\n    if (width >= breakpoints.md) return 'md'\n    if (width >= breakpoints.sm) return 'sm'\n    return 'xs'\n  },\n  \n  // Get responsive grid columns\n  getGridColumns: (mobile: number = 1, tablet: number = 2, desktop: number = 3, large: number = 4) => {\n    const width = window.innerWidth\n    if (width >= breakpoints.xl) return large\n    if (width >= breakpoints.lg) return desktop\n    if (width >= breakpoints.md) return tablet\n    return mobile\n  },\n  \n  // Get responsive items per page\n  getItemsPerPage: (mobile: number = 5, tablet: number = 10, desktop: number = 20) => {\n    const width = window.innerWidth\n    if (width >= breakpoints.lg) return desktop\n    if (width >= breakpoints.md) return tablet\n    return mobile\n  }\n}\n\n// Hook for responsive grid columns\nexport function useResponsiveGrid(\n  mobile: number = 1,\n  tablet: number = 2,\n  desktop: number = 3,\n  large: number = 4\n) {\n  const { isMobile, isTablet, isDesktop, isLarge } = useResponsive()\n  \n  if (isLarge) return large\n  if (isDesktop) return desktop\n  if (isTablet) return tablet\n  return mobile\n}\n\n// Hook for responsive items per page\nexport function useResponsiveItemsPerPage(\n  mobile: number = 5,\n  tablet: number = 10,\n  desktop: number = 20\n) {\n  const { isMobile, isTablet } = useResponsive()\n  \n  if (isMobile) return mobile\n  if (isTablet) return tablet\n  return desktop\n}\n\n// Hook for responsive sidebar behavior\nexport function useResponsiveSidebar() {\n  const { isMobile, isTablet } = useResponsive()\n  \n  return {\n    shouldCollapse: isMobile || isTablet,\n    shouldOverlay: isMobile,\n    defaultCollapsed: isMobile || isTablet\n  }\n}\n\n// Hook for responsive table behavior\nexport function useResponsiveTable() {\n  const { isMobile, isTablet } = useResponsive()\n  \n  return {\n    shouldUseCards: isMobile,\n    shouldHideColumns: isMobile || isTablet,\n    shouldScroll: isMobile || isTablet\n  }\n}\n\n// Hook for responsive form layout\nexport function useResponsiveForm() {\n  const { isMobile, isTablet } = useResponsive()\n  \n  return {\n    columns: isMobile ? 1 : isTablet ? 2 : 3,\n    shouldStack: isMobile,\n    shouldUseFullWidth: isMobile\n  }\n}\n\n// Hook for responsive modal behavior\nexport function useResponsiveModal() {\n  const { isMobile } = useResponsive()\n  \n  return {\n    shouldUseFullScreen: isMobile,\n    maxWidth: isMobile ? '100%' : '90%',\n    padding: isMobile ? '1rem' : '2rem'\n  }\n}\n\nexport default useResponsive\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAKO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAQxC;QACD,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,WAAW;QACX,SAAS;QACT,mBAAmB;IACrB;IAEA,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;4DAAmB;oBACvB,MAAM,QAAQ,OAAO,UAAU;oBAC/B,MAAM,SAAS,OAAO,WAAW;oBAEjC,+BAA+B;oBAC/B,IAAI,oBAAuC;oBAC3C,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,oBAAoB;yBAChD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;yBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;yBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;yBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;oBAEtD,cAAc;wBACZ;wBACA;wBACA,UAAU,QAAQ,YAAY,EAAE;wBAChC,UAAU,SAAS,YAAY,EAAE,IAAI,QAAQ,YAAY,EAAE;wBAC3D,WAAW,SAAS,YAAY,EAAE;wBAClC,SAAS,SAAS,YAAY,EAAE;wBAChC;oBACF;gBACF;;YAEA,eAAe;YACf;YAEA,qBAAqB;YACrB,OAAO,gBAAgB,CAAC,UAAU;YAElC,UAAU;YACV;2CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;kCAAG,EAAE;IAEL,OAAO;AACT;GAtDgB;AAyDT,SAAS,cAAc,UAAsB;;IAClD,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,OAAO,SAAS,WAAW,CAAC,WAAW;AACzC;IAHgB;;QACI;;;AAKb,SAAS,cAAc,KAAa;;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,WAAW,WAAW,OAAO;YAE7B,MAAM;mDAAU,CAAC;oBACf,WAAW,MAAM,OAAO;gBAC1B;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;2CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;kCAAG;QAAC;KAAM;IAEV,OAAO;AACT;IAhBgB;AAmBT,MAAM,aAAa;IACxB,oCAAoC;IACpC,UAAU,IAAM,OAAO,UAAU,GAAG,YAAY,EAAE;IAElD,oCAAoC;IACpC,UAAU,IAAM,OAAO,UAAU,IAAI,YAAY,EAAE,IAAI,OAAO,UAAU,GAAG,YAAY,EAAE;IAEzF,qCAAqC;IACrC,WAAW,IAAM,OAAO,UAAU,IAAI,YAAY,EAAE;IAEpD,yBAAyB;IACzB,sBAAsB;QACpB,MAAM,QAAQ,OAAO,UAAU;QAC/B,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,OAAO;QACxC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,OAAO;IACT;IAEA,8BAA8B;IAC9B,gBAAgB,CAAC,SAAiB,CAAC,EAAE,SAAiB,CAAC,EAAE,UAAkB,CAAC,EAAE,QAAgB,CAAC;QAC7F,MAAM,QAAQ,OAAO,UAAU;QAC/B,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,OAAO;IACT;IAEA,gCAAgC;IAChC,iBAAiB,CAAC,SAAiB,CAAC,EAAE,SAAiB,EAAE,EAAE,UAAkB,EAAE;QAC7E,MAAM,QAAQ,OAAO,UAAU;QAC/B,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,OAAO;IACT;AACF;AAGO,SAAS,kBACd,SAAiB,CAAC,EAClB,SAAiB,CAAC,EAClB,UAAkB,CAAC,EACnB,QAAgB,CAAC;;IAEjB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;IAEnD,IAAI,SAAS,OAAO;IACpB,IAAI,WAAW,OAAO;IACtB,IAAI,UAAU,OAAO;IACrB,OAAO;AACT;IAZgB;;QAMqC;;;AAS9C,SAAS,0BACd,SAAiB,CAAC,EAClB,SAAiB,EAAE,EACnB,UAAkB,EAAE;;IAEpB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAE/B,IAAI,UAAU,OAAO;IACrB,IAAI,UAAU,OAAO;IACrB,OAAO;AACT;IAVgB;;QAKiB;;;AAQ1B,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAE/B,OAAO;QACL,gBAAgB,YAAY;QAC5B,eAAe;QACf,kBAAkB,YAAY;IAChC;AACF;IARgB;;QACiB;;;AAU1B,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAE/B,OAAO;QACL,gBAAgB;QAChB,mBAAmB,YAAY;QAC/B,cAAc,YAAY;IAC5B;AACF;IARgB;;QACiB;;;AAU1B,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAE/B,OAAO;QACL,SAAS,WAAW,IAAI,WAAW,IAAI;QACvC,aAAa;QACb,oBAAoB;IACtB;AACF;IARgB;;QACiB;;;AAU1B,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,OAAO;QACL,qBAAqB;QACrB,UAAU,WAAW,SAAS;QAC9B,SAAS,WAAW,SAAS;IAC/B;AACF;IARgB;;QACO;;;uCASR", "debugId": null}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/utils/permissions.tsx"], "sourcesContent": ["import React from 'react'\nimport { UserType } from '@/stores/sidebar/useSidebarStore'\n\n// Permission types\nexport interface Permission {\n  id: string\n  name: string\n  code: string // Add code field for permission checking\n  category: string\n  resource: string\n  action: string\n  scope: 'global' | 'institute' | 'department' | 'own'\n  requiredLevel: 1 | 2 | 3 | 4 | 5\n  level?: string // Add level field from backend\n  isActive?: boolean // Add isActive field from backend\n}\n\nexport interface UserPermissions {\n  role: string\n  level: number\n  permissions: Permission[]\n  department?: string\n  instituteId?: string\n}\n\n// Default permissions for each user type\nexport const DEFAULT_PERMISSIONS = {\n  super_admin: {\n    level: 1,\n    permissions: [\n      // Dashboard\n      'view_dashboard',\n      'manage_dashboard',\n\n      // User & Role Management\n      'manage_users',\n      'manage_roles',\n      'manage_permissions',\n      'view_users',\n      'create_users',\n      'update_users',\n      'delete_users',\n      'view_roles',\n      'create_roles',\n      'update_roles',\n      'delete_roles',\n      'view_permissions',\n      'create_permissions',\n      'update_permissions',\n      'delete_permissions',\n\n      // Institute Management\n      'manage_institutes',\n      'view_institutes',\n      'create_institutes',\n      'update_institutes',\n      'delete_institutes',\n\n      // Location Management\n      'manage_locations',\n      'manage_countries',\n      'manage_states',\n      'manage_districts',\n      'view_locations',\n      'view_countries',\n      'view_states',\n      'view_districts',\n      'create_countries',\n      'create_states',\n      'create_districts',\n      'update_countries',\n      'update_states',\n      'update_districts',\n      'delete_countries',\n      'delete_states',\n      'delete_districts',\n\n      // Tax Management\n      'manage_taxes',\n      'view_taxes',\n      'create_taxes',\n      'update_taxes',\n      'delete_taxes',\n      'calculate_taxes',\n\n      // Theme Management\n      'manage_themes',\n      'view_themes',\n      'create_themes',\n      'update_themes',\n      'delete_themes',\n      'apply_themes',\n\n      // System Management\n      'manage_settings',\n      'view_settings',\n      'update_settings',\n      'manage_billing',\n      'view_billing',\n      'view_analytics',\n      'view_reports',\n      'export_reports',\n\n      // Payment Gateway Management\n      'manage_payment_gateways',\n      'view_payment_gateways',\n      'create_payment_gateways',\n      'update_payment_gateways',\n      'delete_payment_gateways',\n\n      // Course & Content Management\n      'manage_courses',\n      'view_courses',\n      'create_courses',\n      'update_courses',\n      'delete_courses',\n      'manage_content',\n      'view_content',\n      'create_content',\n      'update_content',\n      'delete_content',\n\n      // Student Management\n      'manage_students',\n      'view_students',\n      'create_students',\n      'update_students',\n      'delete_students',\n\n      // Branch Management\n      'manage_branches',\n      'view_branches',\n      'create_branches',\n      'update_branches',\n      'delete_branches',\n\n      // Live Classes & Assignments\n      'manage_live_classes',\n      'view_live_classes',\n      'create_live_classes',\n      'update_live_classes',\n      'delete_live_classes',\n      'join_live_classes',\n      'view_assignments',\n      'create_assignments',\n      'update_assignments',\n      'delete_assignments',\n      'grade_assignments',\n      'submit_assignments',\n\n      // Communication & Website\n      'manage_website',\n      'view_website',\n      'update_website',\n      'send_notifications',\n      'manage_communication',\n\n      // System Administration\n      'system_admin',\n      'access_all'\n    ]\n  },\n  institute_admin: {\n    level: 2,\n    permissions: [\n      'manage_courses',\n      'manage_students',\n      'manage_branches',\n      'manage_institute_billing',\n      'view_institute_analytics',\n      'manage_website',\n      'manage_institute_settings',\n      'manage_institute_payment_gateways',\n      'configure_payment_gateways'\n    ]\n  },\n  student: {\n    level: 5,\n    permissions: [\n      'view_courses',\n      'enroll_courses',\n      'view_assignments',\n      'submit_assignments',\n      'join_live_classes',\n      'view_progress',\n      'manage_account',\n      'access_community'\n    ]\n  }\n}\n\n// Navigation permissions mapping - Updated for new permission structure\nexport const NAVIGATION_PERMISSIONS = {\n  // Super Admin Dashboard\n  'super-admin': ['view_dashboard', 'manage_dashboard'],\n  'super-admin-dashboard': ['view_dashboard', 'manage_dashboard'],\n\n  // User & Role Management\n  'super-admin-users': ['manage_users', 'view_users'],\n  'super-admin-role-permissions': ['manage_roles', 'manage_permissions', 'view_roles', 'view_permissions'],\n\n  // Institute Management\n  'super-admin-institutes': ['manage_institutes', 'view_institutes'],\n\n  // Location Management\n  'super-admin-locations': ['manage_locations', 'view_locations'],\n  'super-admin-countries': ['manage_countries', 'view_countries'],\n  'super-admin-states': ['manage_states', 'view_states'],\n  'super-admin-districts': ['manage_districts', 'view_districts'],\n\n  // Tax Management\n  'super-admin-tax': ['manage_taxes', 'view_taxes'],\n  'super-admin-tax-management': ['manage_taxes', 'view_taxes', 'calculate_taxes'],\n\n  // Theme Management\n  'super-admin-themes': ['manage_themes', 'view_themes', 'apply_themes'],\n\n  // Billing & Finance\n  'super-admin-billing': ['manage_billing', 'view_billing'],\n\n  // Payment Gateway Management\n  'super-admin-gateway-management': ['manage_payment_gateways', 'view_payment_gateways'],\n\n  // Analytics & Reports\n  'super-admin-analytics': ['view_analytics', 'view_reports', 'export_reports'],\n\n  // System Settings\n  'super-admin-settings': ['manage_settings', 'view_settings', 'update_settings'],\n\n  // Website Management\n  'super-admin-website': ['manage_website', 'view_website', 'update_website'],\n\n  // Institute Admin\n  'institute-admin-dashboard': [],\n  'institute-admin-courses': ['manage_courses'],\n  'institute-admin-students': ['manage_students'],\n  'institute-admin-branches': ['manage_branches'],\n  'institute-admin-billing': ['manage_institute_billing'],\n  'institute-admin-analytics': ['view_institute_analytics'],\n  'institute-admin-website': ['manage_website'],\n  'institute-admin-settings': ['manage_institute_settings'],\n\n  // Institute Admin Payment Gateway Configuration\n  'admin-settings-payment-gateways': ['manage_institute_payment_gateways', 'configure_payment_gateways'],\n\n  // Student\n  'student-dashboard': [],\n  'student-courses': ['view_courses'],\n  'student-marketplace': ['view_courses'],\n  'student-assignments': ['view_assignments'],\n  'student-live-classes': ['join_live_classes'],\n  'student-progress': ['view_progress'],\n  'student-community': ['access_community'],\n  'student-payments': ['manage_account'],\n  'student-account': ['manage_account'],\n  'student-support': []\n}\n\n/**\n * Check if user has a specific permission using the new structure\n */\nexport function hasPermission(\n  userPermissions: UserPermissions,\n  requiredPermission: string,\n  resource?: string,\n  scope?: 'global' | 'institute' | 'department' | 'own'\n): boolean {\n  console.log(`🔍 hasPermission check: ${requiredPermission}`, {\n    userRole: userPermissions.role,\n    permissionCount: userPermissions.permissions.length,\n    isSuperAdmin: userPermissions.role === 'super_admin'\n  })\n\n  // Super admin and platform staff have all permissions\n  if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {\n    console.log(`✅ Permission granted (${userPermissions.role} has all permissions)`)\n    return true\n  }\n\n  // Check if user has the specific permission by code, name, or id\n  const hasDirectPermission = userPermissions.permissions.some(\n    permission =>\n      permission.code === requiredPermission ||\n      permission.name === requiredPermission ||\n      permission.id === requiredPermission ||\n      permission.name.toLowerCase() === requiredPermission.toLowerCase() ||\n      permission.code?.toLowerCase() === requiredPermission.toLowerCase()\n  )\n\n  console.log(`🔍 Direct permission check result: ${hasDirectPermission}`)\n\n  if (!hasDirectPermission) {\n    // Log available permissions for debugging\n    const availablePermissions = userPermissions.permissions.map(p => p.code || p.name).slice(0, 10)\n    console.log(`❌ Permission \"${requiredPermission}\" not found. Available permissions (first 10):`, availablePermissions)\n    return false\n  }\n\n  // If scope is specified, check scope restrictions\n  if (scope) {\n    const permission = userPermissions.permissions.find(\n      p => p.code === requiredPermission || p.name === requiredPermission || p.id === requiredPermission\n    )\n\n    if (!permission) return false\n\n    // Check scope hierarchy: global > institute > department > own\n    const scopeHierarchy = ['global', 'institute', 'department', 'own']\n    const userScopeIndex = scopeHierarchy.indexOf(permission.scope)\n    const requiredScopeIndex = scopeHierarchy.indexOf(scope)\n\n    const scopeAllowed = userScopeIndex <= requiredScopeIndex\n    console.log(`🔍 Scope check: ${permission.scope} vs ${scope} = ${scopeAllowed}`)\n    return scopeAllowed\n  }\n\n  console.log(`✅ Permission \"${requiredPermission}\" granted`)\n  return true\n}\n\n/**\n * Check if user can access a navigation item using new permission structure\n */\nexport function canAccessNavigation(\n  userPermissions: UserPermissions,\n  navigationId: string\n): boolean {\n  console.log(`🔍 canAccessNavigation check: ${navigationId}`, {\n    userRole: userPermissions.role,\n    isSuperAdmin: userPermissions.role === 'super_admin'\n  })\n\n  // Super admin can access everything\n  if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {\n    console.log(`✅ Navigation access granted (${userPermissions.role} has all access)`)\n    return true\n  }\n\n  const requiredPermissions = NAVIGATION_PERMISSIONS[navigationId as keyof typeof NAVIGATION_PERMISSIONS]\n\n  // If no permissions required, allow access\n  if (!requiredPermissions || requiredPermissions.length === 0) {\n    console.log(`✅ Navigation access granted (no permissions required for ${navigationId})`)\n    return true\n  }\n\n  // Check if user has any of the required permissions\n  const hasAccess = requiredPermissions.some(permission =>\n    hasPermission(userPermissions, permission)\n  )\n\n  console.log(`🔍 Navigation access result for ${navigationId}: ${hasAccess}`)\n  console.log(`   Required permissions: ${requiredPermissions.join(', ')}`)\n\n  return hasAccess\n}\n\n/**\n * Filter navigation items based on user permissions\n */\nexport function filterNavigationByPermissions(\n  navigationItems: any[],\n  userPermissions: UserPermissions\n): any[] {\n  // Super admin has access to all navigation items\n  if (userPermissions.role === 'super_admin') {\n    return navigationItems\n  }\n\n  // Prevent infinite loops by checking if navigationItems is valid\n  if (!Array.isArray(navigationItems) || navigationItems.length === 0) {\n    return []\n  }\n\n  return navigationItems.filter(item => {\n    // Ensure item has required properties\n    if (!item || !item.href) {\n      return false\n    }\n\n    // Generate navigation ID from href\n    const navigationId = item.href.replace('/', '').replace(/\\//g, '-')\n\n    // Check if user can access this navigation item\n    const canAccess = canAccessNavigation(userPermissions, navigationId)\n\n    // If item has children, filter them recursively\n    if (item.children && Array.isArray(item.children) && item.children.length > 0) {\n      const filteredChildren = filterNavigationByPermissions(item.children, userPermissions)\n\n      // If no children are accessible, hide the parent too\n      if (filteredChildren.length === 0 && !canAccess) {\n        return false\n      }\n\n      // Update children with filtered list (avoid mutation)\n      item = { ...item, children: filteredChildren }\n    }\n\n    return canAccess\n  })\n}\n\n/**\n * Check if user has minimum required level\n */\nexport function hasMinimumLevel(\n  userLevel: number,\n  requiredLevel: number\n): boolean {\n  // Lower numbers = higher level (1 = highest, 5 = lowest)\n  return userLevel <= requiredLevel\n}\n\n/**\n * Get user permissions from user object with new structure\n */\nexport function getUserPermissions(user: any): UserPermissions {\n  if (!user) {\n    return {\n      role: 'guest',\n      level: 5,\n      permissions: [],\n      department: undefined,\n      instituteId: undefined\n    }\n  }\n\n  console.log('🔍 getUserPermissions - Processing user:', {\n    email: user.email,\n    legacyRole: user.legacyRole,\n    hasRole: !!user.role,\n    roleType: typeof user.role,\n    hasRolePermissions: !!(user.role && user.role.permissions),\n    rolePermissionsCount: user.role?.permissions?.length || 0\n  })\n\n  // Use legacyRole for role checking (consistent with auth system)\n  const role = user.legacyRole || 'student'\n\n  // Get level from role relationship or default\n  let level = 5\n  if (user.role && typeof user.role === 'object' && user.role.level) {\n    level = parseInt(user.role.level) || 5\n  } else {\n    level = DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS]?.level || 5\n  }\n\n  // Extract permissions from user.role.permissions array (new structure)\n  let permissions: Permission[] = []\n\n  if (user.role && typeof user.role === 'object' && Array.isArray(user.role.permissions)) {\n    console.log(`🔍 Found ${user.role.permissions.length} permissions in user.role.permissions`)\n\n    // Convert backend permission objects to frontend format\n    permissions = user.role.permissions\n      .filter((perm: any) => perm && perm.isActive !== false) // Only include active permissions\n      .map((perm: any) => ({\n        id: perm.id || perm.code,\n        name: perm.name || perm.code,\n        code: perm.code, // Include the code field for permission checking\n        category: perm.category || 'general',\n        resource: perm.resource || 'general',\n        action: perm.action || 'access',\n        scope: 'global' as const,\n        requiredLevel: level,\n        level: perm.level || '1',\n        isActive: perm.isActive !== false\n      }))\n\n    console.log(`✅ Processed ${permissions.length} active permissions`)\n\n    // Log sample permissions for debugging\n    if (permissions.length > 0) {\n      console.log('📝 Sample permissions:', permissions.slice(0, 5).map(p => ({\n        name: p.name,\n        code: p.code,\n        category: p.category,\n        resource: p.resource,\n        action: p.action\n      })))\n    }\n  } else if (user.permissions && Array.isArray(user.permissions)) {\n    // Fallback: Handle legacy simple permission strings\n    console.log('🔄 Using legacy permissions format')\n    permissions = user.permissions.map((perm: string) => ({\n      id: perm,\n      name: perm,\n      code: perm,\n      category: 'general',\n      resource: 'general',\n      action: 'access',\n      scope: 'global' as const,\n      requiredLevel: level,\n      level: '1',\n      isActive: true\n    }))\n  } else {\n    console.log('⚠️ No permissions found in user object')\n  }\n\n  const department = user.employment?.department || user.department\n  const instituteId = user.institute?.id || user.institute\n\n  const result = {\n    role,\n    level,\n    permissions,\n    department,\n    instituteId\n  }\n\n  console.log('✅ getUserPermissions result:', {\n    role: result.role,\n    level: result.level,\n    permissionCount: result.permissions.length,\n    department: result.department,\n    instituteId: result.instituteId\n  })\n\n  return result\n}\n\n/**\n * Check if user can perform CRUD operations\n */\nexport function canPerformAction(\n  userPermissions: UserPermissions,\n  action: 'create' | 'read' | 'update' | 'delete',\n  resource: string,\n  scope?: 'global' | 'institute' | 'department' | 'own'\n): boolean {\n  const permissionName = `${action}_${resource}`\n  return hasPermission(userPermissions, permissionName, resource, scope)\n}\n\n/**\n * Get allowed actions for a resource\n */\nexport function getAllowedActions(\n  userPermissions: UserPermissions,\n  resource: string\n): string[] {\n  const actions = ['create', 'read', 'update', 'delete']\n  \n  return actions.filter(action =>\n    canPerformAction(userPermissions, action as any, resource)\n  )\n}\n\n/**\n * Check if user is in same department\n */\nexport function isSameDepartment(\n  userDepartment: string | undefined,\n  targetDepartment: string | undefined\n): boolean {\n  if (!userDepartment || !targetDepartment) return false\n  return userDepartment === targetDepartment\n}\n\n/**\n * Check if user is in same institute\n */\nexport function isSameInstitute(\n  userInstituteId: string | undefined,\n  targetInstituteId: string | undefined\n): boolean {\n  if (!userInstituteId || !targetInstituteId) return false\n  return userInstituteId === targetInstituteId\n}\n\n/**\n * Permission-based component wrapper\n */\nexport function withPermission<T extends object>(\n  Component: React.ComponentType<T>,\n  requiredPermission: string,\n  fallback?: React.ComponentType<T>\n) {\n  return function PermissionWrapper(props: T) {\n    // This would be used with a permission context or hook\n    // For now, returning the component as-is\n    return <Component {...props} />\n  }\n}\n\n/**\n * Generate permission key for caching\n */\nexport function generatePermissionKey(\n  userId: string,\n  permission: string,\n  resource?: string,\n  scope?: string\n): string {\n  return `${userId}:${permission}:${resource || 'any'}:${scope || 'any'}`\n}\n\n/**\n * Validate permission structure\n */\nexport function validatePermission(permission: any): permission is Permission {\n  return (\n    permission &&\n    typeof permission.id === 'string' &&\n    typeof permission.name === 'string' &&\n    typeof permission.category === 'string' &&\n    typeof permission.resource === 'string' &&\n    typeof permission.action === 'string' &&\n    ['global', 'institute', 'department', 'own'].includes(permission.scope) &&\n    [1, 2, 3, 4, 5].includes(permission.requiredLevel)\n  )\n}\n\n/**\n * Debug function to test super admin permissions with new structure\n */\nexport function debugSuperAdminPermissions(user: any) {\n  console.log('=== SUPER ADMIN PERMISSION DEBUG ===')\n  console.log('User object:', {\n    email: user.email,\n    legacyRole: user.legacyRole,\n    hasRole: !!user.role,\n    roleType: typeof user.role,\n    roleName: user.role?.name,\n    roleCode: user.role?.code,\n    rolePermissionsCount: user.role?.permissions?.length || 0\n  })\n\n  const userPermissions = getUserPermissions(user)\n  console.log('Processed user permissions:', {\n    role: userPermissions.role,\n    level: userPermissions.level,\n    permissionCount: userPermissions.permissions.length,\n    department: userPermissions.department,\n    instituteId: userPermissions.instituteId\n  })\n\n  // Test key permissions with new codes\n  const testPermissions = [\n    'manage_users',\n    'manage_roles',\n    'manage_permissions',\n    'view_users',\n    'view_dashboard',\n    'manage_taxes',\n    'manage_locations',\n    'manage_themes',\n    'system_admin',\n    'access_all'\n  ]\n\n  console.log('Permission test results:')\n  testPermissions.forEach(permission => {\n    const hasAccess = hasPermission(userPermissions, permission)\n    console.log(`  ${permission}: ${hasAccess}`)\n  })\n\n  // Test navigation access with new navigation IDs\n  const testNavigation = [\n    'super-admin',\n    'super-admin-dashboard',\n    'super-admin-role-permissions',\n    'super-admin-users',\n    'super-admin-institutes',\n    'super-admin-tax',\n    'super-admin-locations',\n    'super-admin-themes',\n    'super-admin-settings'\n  ]\n\n  console.log('Navigation access test results:')\n  testNavigation.forEach(navId => {\n    const hasAccess = canAccessNavigation(userPermissions, navId)\n    console.log(`  ${navId}: ${hasAccess}`)\n  })\n\n  // Show sample permissions\n  if (userPermissions.permissions.length > 0) {\n    console.log('Sample permissions (first 10):')\n    userPermissions.permissions.slice(0, 10).forEach((perm, index) => {\n      console.log(`  ${index + 1}. ${perm.name} (${perm.code}) - ${perm.category}/${perm.resource}`)\n    })\n  }\n\n  console.log('=== END DEBUG ===')\n\n  return userPermissions\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA0BO,MAAM,sBAAsB;IACjC,aAAa;QACX,OAAO;QACP,aAAa;YACX,YAAY;YACZ;YACA;YAEA,yBAAyB;YACzB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,uBAAuB;YACvB;YACA;YACA;YACA;YACA;YAEA,sBAAsB;YACtB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,iBAAiB;YACjB;YACA;YACA;YACA;YACA;YACA;YAEA,mBAAmB;YACnB;YACA;YACA;YACA;YACA;YACA;YAEA,oBAAoB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,6BAA6B;YAC7B;YACA;YACA;YACA;YACA;YAEA,8BAA8B;YAC9B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,qBAAqB;YACrB;YACA;YACA;YACA;YACA;YAEA,oBAAoB;YACpB;YACA;YACA;YACA;YACA;YAEA,6BAA6B;YAC7B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,0BAA0B;YAC1B;YACA;YACA;YACA;YACA;YAEA,wBAAwB;YACxB;YACA;SACD;IACH;IACA,iBAAiB;QACf,OAAO;QACP,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,SAAS;QACP,OAAO;QACP,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,yBAAyB;IACpC,wBAAwB;IACxB,eAAe;QAAC;QAAkB;KAAmB;IACrD,yBAAyB;QAAC;QAAkB;KAAmB;IAE/D,yBAAyB;IACzB,qBAAqB;QAAC;QAAgB;KAAa;IACnD,gCAAgC;QAAC;QAAgB;QAAsB;QAAc;KAAmB;IAExG,uBAAuB;IACvB,0BAA0B;QAAC;QAAqB;KAAkB;IAElE,sBAAsB;IACtB,yBAAyB;QAAC;QAAoB;KAAiB;IAC/D,yBAAyB;QAAC;QAAoB;KAAiB;IAC/D,sBAAsB;QAAC;QAAiB;KAAc;IACtD,yBAAyB;QAAC;QAAoB;KAAiB;IAE/D,iBAAiB;IACjB,mBAAmB;QAAC;QAAgB;KAAa;IACjD,8BAA8B;QAAC;QAAgB;QAAc;KAAkB;IAE/E,mBAAmB;IACnB,sBAAsB;QAAC;QAAiB;QAAe;KAAe;IAEtE,oBAAoB;IACpB,uBAAuB;QAAC;QAAkB;KAAe;IAEzD,6BAA6B;IAC7B,kCAAkC;QAAC;QAA2B;KAAwB;IAEtF,sBAAsB;IACtB,yBAAyB;QAAC;QAAkB;QAAgB;KAAiB;IAE7E,kBAAkB;IAClB,wBAAwB;QAAC;QAAmB;QAAiB;KAAkB;IAE/E,qBAAqB;IACrB,uBAAuB;QAAC;QAAkB;QAAgB;KAAiB;IAE3E,kBAAkB;IAClB,6BAA6B,EAAE;IAC/B,2BAA2B;QAAC;KAAiB;IAC7C,4BAA4B;QAAC;KAAkB;IAC/C,4BAA4B;QAAC;KAAkB;IAC/C,2BAA2B;QAAC;KAA2B;IACvD,6BAA6B;QAAC;KAA2B;IACzD,2BAA2B;QAAC;KAAiB;IAC7C,4BAA4B;QAAC;KAA4B;IAEzD,gDAAgD;IAChD,mCAAmC;QAAC;QAAqC;KAA6B;IAEtG,UAAU;IACV,qBAAqB,EAAE;IACvB,mBAAmB;QAAC;KAAe;IACnC,uBAAuB;QAAC;KAAe;IACvC,uBAAuB;QAAC;KAAmB;IAC3C,wBAAwB;QAAC;KAAoB;IAC7C,oBAAoB;QAAC;KAAgB;IACrC,qBAAqB;QAAC;KAAmB;IACzC,oBAAoB;QAAC;KAAiB;IACtC,mBAAmB;QAAC;KAAiB;IACrC,mBAAmB,EAAE;AACvB;AAKO,SAAS,cACd,eAAgC,EAChC,kBAA0B,EAC1B,QAAiB,EACjB,KAAqD;IAErD,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,oBAAoB,EAAE;QAC3D,UAAU,gBAAgB,IAAI;QAC9B,iBAAiB,gBAAgB,WAAW,CAAC,MAAM;QACnD,cAAc,gBAAgB,IAAI,KAAK;IACzC;IAEA,sDAAsD;IACtD,IAAI,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK,kBAAkB;QACvF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;QAChF,OAAO;IACT;IAEA,iEAAiE;IACjE,MAAM,sBAAsB,gBAAgB,WAAW,CAAC,IAAI,CAC1D,CAAA,aACE,WAAW,IAAI,KAAK,sBACpB,WAAW,IAAI,KAAK,sBACpB,WAAW,EAAE,KAAK,sBAClB,WAAW,IAAI,CAAC,WAAW,OAAO,mBAAmB,WAAW,MAChE,WAAW,IAAI,EAAE,kBAAkB,mBAAmB,WAAW;IAGrE,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,qBAAqB;IAEvE,IAAI,CAAC,qBAAqB;QACxB,0CAA0C;QAC1C,MAAM,uBAAuB,gBAAgB,WAAW,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG;QAC7F,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,mBAAmB,8CAA8C,CAAC,EAAE;QACjG,OAAO;IACT;IAEA,kDAAkD;IAClD,IAAI,OAAO;QACT,MAAM,aAAa,gBAAgB,WAAW,CAAC,IAAI,CACjD,CAAA,IAAK,EAAE,IAAI,KAAK,sBAAsB,EAAE,IAAI,KAAK,sBAAsB,EAAE,EAAE,KAAK;QAGlF,IAAI,CAAC,YAAY,OAAO;QAExB,+DAA+D;QAC/D,MAAM,iBAAiB;YAAC;YAAU;YAAa;YAAc;SAAM;QACnE,MAAM,iBAAiB,eAAe,OAAO,CAAC,WAAW,KAAK;QAC9D,MAAM,qBAAqB,eAAe,OAAO,CAAC;QAElD,MAAM,eAAe,kBAAkB;QACvC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,WAAW,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,EAAE,cAAc;QAC/E,OAAO;IACT;IAEA,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,mBAAmB,SAAS,CAAC;IAC1D,OAAO;AACT;AAKO,SAAS,oBACd,eAAgC,EAChC,YAAoB;IAEpB,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,cAAc,EAAE;QAC3D,UAAU,gBAAgB,IAAI;QAC9B,cAAc,gBAAgB,IAAI,KAAK;IACzC;IAEA,oCAAoC;IACpC,IAAI,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK,kBAAkB;QACvF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,gBAAgB,IAAI,CAAC,gBAAgB,CAAC;QAClF,OAAO;IACT;IAEA,MAAM,sBAAsB,sBAAsB,CAAC,aAAoD;IAEvG,2CAA2C;IAC3C,IAAI,CAAC,uBAAuB,oBAAoB,MAAM,KAAK,GAAG;QAC5D,QAAQ,GAAG,CAAC,CAAC,yDAAyD,EAAE,aAAa,CAAC,CAAC;QACvF,OAAO;IACT;IAEA,oDAAoD;IACpD,MAAM,YAAY,oBAAoB,IAAI,CAAC,CAAA,aACzC,cAAc,iBAAiB;IAGjC,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,aAAa,EAAE,EAAE,WAAW;IAC3E,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,oBAAoB,IAAI,CAAC,OAAO;IAExE,OAAO;AACT;AAKO,SAAS,8BACd,eAAsB,EACtB,eAAgC;IAEhC,iDAAiD;IACjD,IAAI,gBAAgB,IAAI,KAAK,eAAe;QAC1C,OAAO;IACT;IAEA,iEAAiE;IACjE,IAAI,CAAC,MAAM,OAAO,CAAC,oBAAoB,gBAAgB,MAAM,KAAK,GAAG;QACnE,OAAO,EAAE;IACX;IAEA,OAAO,gBAAgB,MAAM,CAAC,CAAA;QAC5B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;YACvB,OAAO;QACT;QAEA,mCAAmC;QACnC,MAAM,eAAe,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,OAAO;QAE/D,gDAAgD;QAChD,MAAM,YAAY,oBAAoB,iBAAiB;QAEvD,gDAAgD;QAChD,IAAI,KAAK,QAAQ,IAAI,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC7E,MAAM,mBAAmB,8BAA8B,KAAK,QAAQ,EAAE;YAEtE,qDAAqD;YACrD,IAAI,iBAAiB,MAAM,KAAK,KAAK,CAAC,WAAW;gBAC/C,OAAO;YACT;YAEA,sDAAsD;YACtD,OAAO;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAiB;QAC/C;QAEA,OAAO;IACT;AACF;AAKO,SAAS,gBACd,SAAiB,EACjB,aAAqB;IAErB,yDAAyD;IACzD,OAAO,aAAa;AACtB;AAKO,SAAS,mBAAmB,IAAS;IAC1C,IAAI,CAAC,MAAM;QACT,OAAO;YACL,MAAM;YACN,OAAO;YACP,aAAa,EAAE;YACf,YAAY;YACZ,aAAa;QACf;IACF;IAEA,QAAQ,GAAG,CAAC,4CAA4C;QACtD,OAAO,KAAK,KAAK;QACjB,YAAY,KAAK,UAAU;QAC3B,SAAS,CAAC,CAAC,KAAK,IAAI;QACpB,UAAU,OAAO,KAAK,IAAI;QAC1B,oBAAoB,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,WAAW;QACzD,sBAAsB,KAAK,IAAI,EAAE,aAAa,UAAU;IAC1D;IAEA,iEAAiE;IACjE,MAAM,OAAO,KAAK,UAAU,IAAI;IAEhC,8CAA8C;IAC9C,IAAI,QAAQ;IACZ,IAAI,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,CAAC,KAAK,EAAE;QACjE,QAAQ,SAAS,KAAK,IAAI,CAAC,KAAK,KAAK;IACvC,OAAO;QACL,QAAQ,mBAAmB,CAAC,KAAyC,EAAE,SAAS;IAClF;IAEA,uEAAuE;IACvE,IAAI,cAA4B,EAAE;IAElC,IAAI,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,YAAY,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG;QACtF,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,qCAAqC,CAAC;QAE3F,wDAAwD;QACxD,cAAc,KAAK,IAAI,CAAC,WAAW,CAChC,MAAM,CAAC,CAAC,OAAc,QAAQ,KAAK,QAAQ,KAAK,OAAO,kCAAkC;SACzF,GAAG,CAAC,CAAC,OAAc,CAAC;gBACnB,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI;gBACxB,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI;gBAC5B,MAAM,KAAK,IAAI;gBACf,UAAU,KAAK,QAAQ,IAAI;gBAC3B,UAAU,KAAK,QAAQ,IAAI;gBAC3B,QAAQ,KAAK,MAAM,IAAI;gBACvB,OAAO;gBACP,eAAe;gBACf,OAAO,KAAK,KAAK,IAAI;gBACrB,UAAU,KAAK,QAAQ,KAAK;YAC9B,CAAC;QAEH,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,YAAY,MAAM,CAAC,mBAAmB,CAAC;QAElE,uCAAuC;QACvC,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,QAAQ,GAAG,CAAC,0BAA0B,YAAY,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,CAAC;oBACtE,MAAM,EAAE,IAAI;oBACZ,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,QAAQ;oBACpB,UAAU,EAAE,QAAQ;oBACpB,QAAQ,EAAE,MAAM;gBAClB,CAAC;QACH;IACF,OAAO,IAAI,KAAK,WAAW,IAAI,MAAM,OAAO,CAAC,KAAK,WAAW,GAAG;QAC9D,oDAAoD;QACpD,QAAQ,GAAG,CAAC;QACZ,cAAc,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,OAAiB,CAAC;gBACpD,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACP,eAAe;gBACf,OAAO;gBACP,UAAU;YACZ,CAAC;IACH,OAAO;QACL,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,aAAa,KAAK,UAAU,EAAE,cAAc,KAAK,UAAU;IACjE,MAAM,cAAc,KAAK,SAAS,EAAE,MAAM,KAAK,SAAS;IAExD,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;IACF;IAEA,QAAQ,GAAG,CAAC,gCAAgC;QAC1C,MAAM,OAAO,IAAI;QACjB,OAAO,OAAO,KAAK;QACnB,iBAAiB,OAAO,WAAW,CAAC,MAAM;QAC1C,YAAY,OAAO,UAAU;QAC7B,aAAa,OAAO,WAAW;IACjC;IAEA,OAAO;AACT;AAKO,SAAS,iBACd,eAAgC,EAChC,MAA+C,EAC/C,QAAgB,EAChB,KAAqD;IAErD,MAAM,iBAAiB,GAAG,OAAO,CAAC,EAAE,UAAU;IAC9C,OAAO,cAAc,iBAAiB,gBAAgB,UAAU;AAClE;AAKO,SAAS,kBACd,eAAgC,EAChC,QAAgB;IAEhB,MAAM,UAAU;QAAC;QAAU;QAAQ;QAAU;KAAS;IAEtD,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,iBAAiB,iBAAiB,QAAe;AAErD;AAKO,SAAS,iBACd,cAAkC,EAClC,gBAAoC;IAEpC,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,OAAO;IACjD,OAAO,mBAAmB;AAC5B;AAKO,SAAS,gBACd,eAAmC,EACnC,iBAAqC;IAErC,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,OAAO;IACnD,OAAO,oBAAoB;AAC7B;AAKO,SAAS,eACd,SAAiC,EACjC,kBAA0B,EAC1B,QAAiC;IAEjC,OAAO,SAAS,kBAAkB,KAAQ;QACxC,uDAAuD;QACvD,yCAAyC;QACzC,qBAAO,0UAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;AACF;AAKO,SAAS,sBACd,MAAc,EACd,UAAkB,EAClB,QAAiB,EACjB,KAAc;IAEd,OAAO,GAAG,OAAO,CAAC,EAAE,WAAW,CAAC,EAAE,YAAY,MAAM,CAAC,EAAE,SAAS,OAAO;AACzE;AAKO,SAAS,mBAAmB,UAAe;IAChD,OACE,cACA,OAAO,WAAW,EAAE,KAAK,YACzB,OAAO,WAAW,IAAI,KAAK,YAC3B,OAAO,WAAW,QAAQ,KAAK,YAC/B,OAAO,WAAW,QAAQ,KAAK,YAC/B,OAAO,WAAW,MAAM,KAAK,YAC7B;QAAC;QAAU;QAAa;QAAc;KAAM,CAAC,QAAQ,CAAC,WAAW,KAAK,KACtE;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE,CAAC,QAAQ,CAAC,WAAW,aAAa;AAErD;AAKO,SAAS,2BAA2B,IAAS;IAClD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,gBAAgB;QAC1B,OAAO,KAAK,KAAK;QACjB,YAAY,KAAK,UAAU;QAC3B,SAAS,CAAC,CAAC,KAAK,IAAI;QACpB,UAAU,OAAO,KAAK,IAAI;QAC1B,UAAU,KAAK,IAAI,EAAE;QACrB,UAAU,KAAK,IAAI,EAAE;QACrB,sBAAsB,KAAK,IAAI,EAAE,aAAa,UAAU;IAC1D;IAEA,MAAM,kBAAkB,mBAAmB;IAC3C,QAAQ,GAAG,CAAC,+BAA+B;QACzC,MAAM,gBAAgB,IAAI;QAC1B,OAAO,gBAAgB,KAAK;QAC5B,iBAAiB,gBAAgB,WAAW,CAAC,MAAM;QACnD,YAAY,gBAAgB,UAAU;QACtC,aAAa,gBAAgB,WAAW;IAC1C;IAEA,sCAAsC;IACtC,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,QAAQ,GAAG,CAAC;IACZ,gBAAgB,OAAO,CAAC,CAAA;QACtB,MAAM,YAAY,cAAc,iBAAiB;QACjD,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,WAAW,EAAE,EAAE,WAAW;IAC7C;IAEA,iDAAiD;IACjD,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,QAAQ,GAAG,CAAC;IACZ,eAAe,OAAO,CAAC,CAAA;QACrB,MAAM,YAAY,oBAAoB,iBAAiB;QACvD,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,WAAW;IACxC;IAEA,0BAA0B;IAC1B,IAAI,gBAAgB,WAAW,CAAC,MAAM,GAAG,GAAG;QAC1C,QAAQ,GAAG,CAAC;QACZ,gBAAgB,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,MAAM;YACtD,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;QAC/F;IACF;IAEA,QAAQ,GAAG,CAAC;IAEZ,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/contexts/PermissionContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport {\n  getUserPermissions,\n  hasPermission,\n  canAccessNavigation,\n  canPerformAction,\n  getAllowedActions,\n  filterNavigationByPermissions,\n  debugSuperAdminPermissions,\n  UserPermissions,\n  Permission\n} from '@/utils/permissions'\n\ninterface PermissionContextType {\n  userPermissions: UserPermissions\n  hasPermission: (permission: string, resource?: string, scope?: 'global' | 'institute' | 'department' | 'own') => boolean\n  canAccessNavigation: (navigationId: string) => boolean\n  canPerformAction: (action: 'create' | 'read' | 'update' | 'delete', resource: string, scope?: 'global' | 'institute' | 'department' | 'own') => boolean\n  getAllowedActions: (resource: string) => string[]\n  filterNavigationByPermissions: (navigationItems: any[]) => any[]\n  isLoading: boolean\n  refreshPermissions: () => Promise<void>\n}\n\nconst PermissionContext = createContext<PermissionContextType | undefined>(undefined)\n\ninterface PermissionProviderProps {\n  children: React.ReactNode\n}\n\nexport function PermissionProvider({ children }: PermissionProviderProps) {\n  const { user, isAuthenticated } = useAuthStore()\n  const [userPermissions, setUserPermissions] = useState<UserPermissions>({\n    role: 'guest',\n    level: 5,\n    permissions: [],\n    department: undefined,\n    instituteId: undefined\n  })\n  const [isLoading, setIsLoading] = useState(true)\n\n  // Update permissions when user changes\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      console.log('🔄 Processing user permissions...')\n      console.log('User object:', user)\n      console.log('User role:', user.role)\n      console.log('User legacyRole:', user.legacyRole)\n\n      // Extract permissions from the new structure\n      const permissions = getUserPermissions(user)\n\n      // Store user data with permissions in localStorage for persistence\n      try {\n        localStorage.setItem('user_data', JSON.stringify(user))\n        localStorage.setItem('user_permissions', JSON.stringify(permissions))\n        console.log('✅ User data and permissions stored in localStorage')\n      } catch (error) {\n        console.error('❌ Failed to store user data in localStorage:', error)\n      }\n\n      // Debug super admin permissions\n      if (user.legacyRole === 'super_admin') {\n        console.log('🔍 Super admin detected, running permission debug...')\n        debugSuperAdminPermissions(user)\n      }\n\n      setUserPermissions(permissions)\n      setIsLoading(false)\n\n      console.log(`✅ Processed ${permissions.permissions.length} permissions for ${permissions.role}`)\n    } else {\n      // Clear stored data when not authenticated\n      localStorage.removeItem('user_data')\n      localStorage.removeItem('user_permissions')\n\n      setUserPermissions({\n        role: 'guest',\n        level: 5,\n        permissions: [],\n        department: undefined,\n        instituteId: undefined\n      })\n      setIsLoading(false)\n      console.log('🔄 Cleared user permissions (not authenticated)')\n    }\n  }, [user, isAuthenticated])\n\n  // Initialize permissions on mount and try to restore from localStorage\n  useEffect(() => {\n    // Try to restore user data from localStorage if no current user\n    if (!isAuthenticated && !user) {\n      try {\n        const storedUserData = localStorage.getItem('user_data')\n        const storedPermissions = localStorage.getItem('user_permissions')\n\n        if (storedUserData && storedPermissions) {\n          console.log('🔄 Attempting to restore user data from localStorage')\n          const userData = JSON.parse(storedUserData)\n          const permissionsData = JSON.parse(storedPermissions)\n\n          console.log('📦 Restored user data from localStorage:', {\n            email: userData.email,\n            role: userData.legacyRole,\n            permissionCount: permissionsData.permissions.length\n          })\n\n          setUserPermissions(permissionsData)\n          setIsLoading(false)\n        } else {\n          setIsLoading(false)\n        }\n      } catch (error) {\n        console.error('❌ Failed to restore user data from localStorage:', error)\n        localStorage.removeItem('user_data')\n        localStorage.removeItem('user_permissions')\n        setIsLoading(false)\n      }\n    }\n  }, [isAuthenticated, user])\n\n  // Refresh permissions from server\n  const refreshPermissions = async () => {\n    if (!user) return\n\n    setIsLoading(true)\n    try {\n      // Fetch updated user data with permissions\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/auth/verify`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include'\n      })\n\n      const data = await response.json()\n\n      if (data.success && data.user) {\n        const permissions = getUserPermissions(data.user)\n        setUserPermissions(permissions)\n      }\n    } catch (error) {\n      console.error('Error refreshing permissions:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Permission checking functions\n  const checkPermission = (\n    permission: string, \n    resource?: string, \n    scope?: 'global' | 'institute' | 'department' | 'own'\n  ) => {\n    return hasPermission(userPermissions, permission, resource, scope)\n  }\n\n  const checkNavigationAccess = (navigationId: string) => {\n    return canAccessNavigation(userPermissions, navigationId)\n  }\n\n  const checkActionPermission = (\n    action: 'create' | 'read' | 'update' | 'delete',\n    resource: string,\n    scope?: 'global' | 'institute' | 'department' | 'own'\n  ) => {\n    return canPerformAction(userPermissions, action, resource, scope)\n  }\n\n  const getResourceActions = (resource: string) => {\n    return getAllowedActions(userPermissions, resource)\n  }\n\n  const filterNavigation = (navigationItems: any[]) => {\n    return filterNavigationByPermissions(navigationItems, userPermissions)\n  }\n\n  const contextValue: PermissionContextType = {\n    userPermissions,\n    hasPermission: checkPermission,\n    canAccessNavigation: checkNavigationAccess,\n    canPerformAction: checkActionPermission,\n    getAllowedActions: getResourceActions,\n    filterNavigationByPermissions: filterNavigation,\n    isLoading,\n    refreshPermissions\n  }\n\n  return (\n    <PermissionContext.Provider value={contextValue}>\n      {children}\n    </PermissionContext.Provider>\n  )\n}\n\n// Hook to use permission context\nexport function usePermissions() {\n  const context = useContext(PermissionContext)\n  if (context === undefined) {\n    throw new Error('usePermissions must be used within a PermissionProvider')\n  }\n  return context\n}\n\n// Higher-order component for permission-based rendering\ninterface WithPermissionProps {\n  permission: string\n  resource?: string\n  scope?: 'global' | 'institute' | 'department' | 'own'\n  fallback?: React.ReactNode\n  children: React.ReactNode\n}\n\nexport function WithPermission({ \n  permission, \n  resource, \n  scope, \n  fallback = null, \n  children \n}: WithPermissionProps) {\n  const { hasPermission } = usePermissions()\n  \n  if (hasPermission(permission, resource, scope)) {\n    return <>{children}</>\n  }\n  \n  return <>{fallback}</>\n}\n\n// Component for action-based permission checking\ninterface WithActionPermissionProps {\n  action: 'create' | 'read' | 'update' | 'delete'\n  resource: string\n  scope?: 'global' | 'institute' | 'department' | 'own'\n  fallback?: React.ReactNode\n  children: React.ReactNode\n}\n\nexport function WithActionPermission({ \n  action, \n  resource, \n  scope, \n  fallback = null, \n  children \n}: WithActionPermissionProps) {\n  const { canPerformAction } = usePermissions()\n  \n  if (canPerformAction(action, resource, scope)) {\n    return <>{children}</>\n  }\n  \n  return <>{fallback}</>\n}\n\n// Component for navigation access checking\ninterface WithNavigationAccessProps {\n  navigationId: string\n  fallback?: React.ReactNode\n  children: React.ReactNode\n}\n\nexport function WithNavigationAccess({ \n  navigationId, \n  fallback = null, \n  children \n}: WithNavigationAccessProps) {\n  const { canAccessNavigation } = usePermissions()\n  \n  if (canAccessNavigation(navigationId)) {\n    return <>{children}</>\n  }\n  \n  return <>{fallback}</>\n}\n\n// Hook for checking multiple permissions\nexport function useMultiplePermissions(permissions: Array<{\n  permission: string\n  resource?: string\n  scope?: 'global' | 'institute' | 'department' | 'own'\n}>) {\n  const { hasPermission } = usePermissions()\n  \n  return permissions.map(({ permission, resource, scope }) => ({\n    permission,\n    resource,\n    scope,\n    hasAccess: hasPermission(permission, resource, scope)\n  }))\n}\n\n// Hook for checking role-based access\nexport function useRoleAccess() {\n  const { userPermissions } = usePermissions()\n\n  return {\n    isSuperAdmin: userPermissions.role === 'super_admin',\n    isInstituteAdmin: userPermissions.role === 'institute_admin',\n    isStudent: userPermissions.role === 'student',\n    role: userPermissions.role,\n    level: userPermissions.level,\n    department: userPermissions.department,\n    instituteId: userPermissions.instituteId,\n    // Helper function to check if user has admin privileges\n    hasAdminAccess: () => userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff',\n    // Helper function to check if user can manage roles/permissions\n    canManageRoles: () => userPermissions.role === 'super_admin'\n  }\n}\n\nexport default PermissionProvider\n"], "names": [], "mappings": ";;;;;;;;;;AAmIsC;;AAjItC;AACA;AACA;;;AAJA;;;;AA2BA,MAAM,kCAAoB,CAAA,GAAA,0SAAA,CAAA,gBAAa,AAAD,EAAqC;AAMpE,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;;IACtE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAmB;QACtE,MAAM;QACN,OAAO;QACP,aAAa,EAAE;QACf,YAAY;QACZ,aAAa;IACf;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,uCAAuC;IACvC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,mBAAmB,MAAM;gBAC3B,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,gBAAgB;gBAC5B,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;gBACnC,QAAQ,GAAG,CAAC,oBAAoB,KAAK,UAAU;gBAE/C,6CAA6C;gBAC7C,MAAM,cAAc,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE;gBAEvC,mEAAmE;gBACnE,IAAI;oBACF,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;oBACjD,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;oBACxD,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gDAAgD;gBAChE;gBAEA,gCAAgC;gBAChC,IAAI,KAAK,UAAU,KAAK,eAAe;oBACrC,QAAQ,GAAG,CAAC;oBACZ,CAAA,GAAA,mJAAA,CAAA,6BAA0B,AAAD,EAAE;gBAC7B;gBAEA,mBAAmB;gBACnB,aAAa;gBAEb,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,YAAY,WAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,YAAY,IAAI,EAAE;YACjG,OAAO;gBACL,2CAA2C;gBAC3C,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBAExB,mBAAmB;oBACjB,MAAM;oBACN,OAAO;oBACP,aAAa,EAAE;oBACf,YAAY;oBACZ,aAAa;gBACf;gBACA,aAAa;gBACb,QAAQ,GAAG,CAAC;YACd;QACF;uCAAG;QAAC;QAAM;KAAgB;IAE1B,uEAAuE;IACvE,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;wCAAE;YACR,gEAAgE;YAChE,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B,IAAI;oBACF,MAAM,iBAAiB,aAAa,OAAO,CAAC;oBAC5C,MAAM,oBAAoB,aAAa,OAAO,CAAC;oBAE/C,IAAI,kBAAkB,mBAAmB;wBACvC,QAAQ,GAAG,CAAC;wBACZ,MAAM,WAAW,KAAK,KAAK,CAAC;wBAC5B,MAAM,kBAAkB,KAAK,KAAK,CAAC;wBAEnC,QAAQ,GAAG,CAAC,4CAA4C;4BACtD,OAAO,SAAS,KAAK;4BACrB,MAAM,SAAS,UAAU;4BACzB,iBAAiB,gBAAgB,WAAW,CAAC,MAAM;wBACrD;wBAEA,mBAAmB;wBACnB,aAAa;oBACf,OAAO;wBACL,aAAa;oBACf;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oDAAoD;oBAClE,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa;gBACf;YACF;QACF;uCAAG;QAAC;QAAiB;KAAK;IAE1B,kCAAkC;IAClC,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,2CAA2C;YAC3C,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,wBAAwB,gBAAgB,CAAC,EAAE;gBAC5G,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,eAAe;oBAC/D,gBAAgB;gBAClB;gBACA,aAAa;YACf;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBAC7B,MAAM,cAAc,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI;gBAChD,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,aAAa;QACf;IACF;IAEA,gCAAgC;IAChC,MAAM,kBAAkB,CACtB,YACA,UACA;QAEA,OAAO,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,YAAY,UAAU;IAC9D;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,CAAA,GAAA,mJAAA,CAAA,sBAAmB,AAAD,EAAE,iBAAiB;IAC9C;IAEA,MAAM,wBAAwB,CAC5B,QACA,UACA;QAEA,OAAO,CAAA,GAAA,mJAAA,CAAA,mBAAgB,AAAD,EAAE,iBAAiB,QAAQ,UAAU;IAC7D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD,EAAE,iBAAiB;IAC5C;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,CAAA,GAAA,mJAAA,CAAA,gCAA6B,AAAD,EAAE,iBAAiB;IACxD;IAEA,MAAM,eAAsC;QAC1C;QACA,eAAe;QACf,qBAAqB;QACrB,kBAAkB;QAClB,mBAAmB;QACnB,+BAA+B;QAC/B;QACA;IACF;IAEA,qBACE,0UAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;GArKgB;;QACoB,4JAAA,CAAA,eAAY;;;KADhC;AAwKT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAiBT,SAAS,eAAe,EAC7B,UAAU,EACV,QAAQ,EACR,KAAK,EACL,WAAW,IAAI,EACf,QAAQ,EACY;;IACpB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,IAAI,cAAc,YAAY,UAAU,QAAQ;QAC9C,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;IAdgB;;QAOY;;;MAPZ;AAyBT,SAAS,qBAAqB,EACnC,MAAM,EACN,QAAQ,EACR,KAAK,EACL,WAAW,IAAI,EACf,QAAQ,EACkB;;IAC1B,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAE7B,IAAI,iBAAiB,QAAQ,UAAU,QAAQ;QAC7C,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;IAdgB;;QAOe;;;MAPf;AAuBT,SAAS,qBAAqB,EACnC,YAAY,EACZ,WAAW,IAAI,EACf,QAAQ,EACkB;;IAC1B,MAAM,EAAE,mBAAmB,EAAE,GAAG;IAEhC,IAAI,oBAAoB,eAAe;QACrC,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;IAZgB;;QAKkB;;;MALlB;AAeT,SAAS,uBAAuB,WAIrC;;IACA,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,OAAO,YAAY,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAK,CAAC;YAC3D;YACA;YACA;YACA,WAAW,cAAc,YAAY,UAAU;QACjD,CAAC;AACH;IAbgB;;QAKY;;;AAWrB,SAAS;;IACd,MAAM,EAAE,eAAe,EAAE,GAAG;IAE5B,OAAO;QACL,cAAc,gBAAgB,IAAI,KAAK;QACvC,kBAAkB,gBAAgB,IAAI,KAAK;QAC3C,WAAW,gBAAgB,IAAI,KAAK;QACpC,MAAM,gBAAgB,IAAI;QAC1B,OAAO,gBAAgB,KAAK;QAC5B,YAAY,gBAAgB,UAAU;QACtC,aAAa,gBAAgB,WAAW;QACxC,wDAAwD;QACxD,gBAAgB,IAAM,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK;QACzF,gEAAgE;QAChE,gBAAgB,IAAM,gBAAgB,IAAI,KAAK;IACjD;AACF;IAhBgB;;QACc;;;uCAiBf", "debugId": null}}, {"offset": {"line": 1918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/hooks/useSidebarNavigation.ts"], "sourcesContent": ["import { useEffect, useMemo } from 'react'\nimport { useSidebarStore, NavigationItem } from '@/stores/sidebar/useSidebarStore'\nimport { usePermissions } from '@/contexts/PermissionContext'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\n\n/**\n * Hook that integrates sidebar store with permission-aware navigation\n * This hook ensures that navigation items are properly filtered based on user permissions\n * and that the sidebar store is kept in sync with the permission system\n */\nexport function useSidebarNavigation() {\n  const { user } = useAuthStore()\n  const { userPermissions, filterNavigationByPermissions } = usePermissions()\n  const {\n    navigationItems,\n    userType,\n    setNavigationItems,\n    filterNavigationByPermissions: sidebarFilterPermissions,\n    initializeNavigation\n  } = useSidebarStore()\n\n  // Filter navigation items based on permissions\n  const filteredNavigationItems = useMemo(() => {\n    if (!Array.isArray(navigationItems) || navigationItems.length === 0) {\n      return []\n    }\n\n    // For super admin, return all items without filtering\n    if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {\n      return navigationItems\n    }\n\n    // Use the permission context filtering for complex permission checks\n    return filterNavigationByPermissions(navigationItems)\n  }, [navigationItems, userPermissions.role, filterNavigationByPermissions])\n\n  // Helper function to check if a navigation item is accessible\n  const isNavigationItemAccessible = (item: NavigationItem): boolean => {\n    // Super admin has access to everything\n    if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {\n      return true\n    }\n\n    // Check item permissions\n    if (item.permissions && item.permissions.length > 0) {\n      // Check if user role is in the permissions array\n      if (item.permissions.includes(userPermissions.role)) {\n        return true\n      }\n      \n      // Check if user has any of the required permissions\n      return item.permissions.some(permission => \n        userPermissions.permissions.some(userPerm => \n          userPerm.code === permission || userPerm.name === permission\n        )\n      )\n    }\n\n    // If no permissions specified, allow access\n    return true\n  }\n\n  // Helper function to filter navigation items recursively\n  const filterNavigationItemsRecursively = (items: NavigationItem[]): NavigationItem[] => {\n    return items.map(item => {\n      let filteredItem = { ...item }\n\n      // Filter children if they exist\n      if (item.children && item.children.length > 0) {\n        const filteredChildren = filterNavigationItemsRecursively(item.children)\n          .filter(child => isNavigationItemAccessible(child))\n        \n        filteredItem.children = filteredChildren\n      }\n\n      return filteredItem\n    }).filter(item => isNavigationItemAccessible(item))\n  }\n\n  // Enhanced filtered navigation with recursive filtering\n  const enhancedFilteredNavigation = useMemo(() => {\n    if (!Array.isArray(navigationItems) || navigationItems.length === 0) {\n      return []\n    }\n\n    // For super admin, return all items\n    if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {\n      return navigationItems\n    }\n\n    return filterNavigationItemsRecursively(navigationItems)\n  }, [navigationItems, userPermissions.role, userPermissions.permissions])\n\n  // Update sidebar store when user permissions change\n  useEffect(() => {\n    if (user && userPermissions.role) {\n      // Extract permission codes for sidebar filtering\n      const permissionCodes = userPermissions.permissions.map(p => p.code || p.name)\n      sidebarFilterPermissions(permissionCodes)\n    }\n  }, [user, userPermissions.permissions, sidebarFilterPermissions])\n\n  // Get navigation items by section\n  const getNavigationBySection = (sectionName: string): NavigationItem[] => {\n    return enhancedFilteredNavigation.filter(item => \n      item.href.includes(sectionName.toLowerCase())\n    )\n  }\n\n  // Check if specific navigation item is accessible\n  const isNavigationAccessible = (href: string): boolean => {\n    const findItemByHref = (items: NavigationItem[]): NavigationItem | undefined => {\n      for (const item of items) {\n        if (item.href === href) {\n          return item\n        }\n        if (item.children) {\n          const found = findItemByHref(item.children)\n          if (found) return found\n        }\n      }\n      return undefined\n    }\n\n    const item = findItemByHref(enhancedFilteredNavigation)\n    return !!item\n  }\n\n  // Get accessible navigation count\n  const getNavigationStats = () => {\n    const countItems = (items: NavigationItem[]): number => {\n      return items.reduce((count, item) => {\n        let itemCount = 1\n        if (item.children) {\n          itemCount += countItems(item.children)\n        }\n        return count + itemCount\n      }, 0)\n    }\n\n    const accessibleCount = countItems(enhancedFilteredNavigation)\n    const totalCount = countItems(navigationItems)\n\n    return {\n      accessible: accessibleCount,\n      total: totalCount,\n      restricted: totalCount - accessibleCount\n    }\n  }\n\n  // Find navigation item by ID\n  const findNavigationItem = (id: string): NavigationItem | undefined => {\n    const findById = (items: NavigationItem[]): NavigationItem | undefined => {\n      for (const item of items) {\n        if (item.id === id) {\n          return item\n        }\n        if (item.children) {\n          const found = findById(item.children)\n          if (found) return found\n        }\n      }\n      return undefined\n    }\n\n    return findById(enhancedFilteredNavigation)\n  }\n\n  return {\n    // Navigation items\n    navigationItems: enhancedFilteredNavigation,\n    rawNavigationItems: navigationItems,\n    \n    // Permission checks\n    isNavigationItemAccessible,\n    isNavigationAccessible,\n    \n    // Utility functions\n    getNavigationBySection,\n    getNavigationStats,\n    findNavigationItem,\n    \n    // Store actions\n    setNavigationItems,\n    initializeNavigation,\n    \n    // User info\n    userType,\n    userRole: userPermissions.role,\n    \n    // Stats\n    navigationStats: getNavigationStats()\n  }\n}\n\n/**\n * Hook for checking specific navigation permissions\n */\nexport function useNavigationAccess(navigationIds: string[]) {\n  const { isNavigationAccessible } = useSidebarNavigation()\n\n  const accessMap = useMemo(() => {\n    return navigationIds.reduce((acc, id) => {\n      acc[id] = isNavigationAccessible(`/${id.replace(/-/g, '/')}`)\n      return acc\n    }, {} as Record<string, boolean>)\n  }, [navigationIds, isNavigationAccessible])\n\n  const accessibleIds = navigationIds.filter(id => accessMap[id])\n  const restrictedIds = navigationIds.filter(id => !accessMap[id])\n\n  return {\n    accessMap,\n    accessibleIds,\n    restrictedIds,\n    hasAccessToAny: accessibleIds.length > 0,\n    hasAccessToAll: restrictedIds.length === 0\n  }\n}\n\n/**\n * Hook for payment gateway navigation access\n */\nexport function usePaymentGatewayNavigation() {\n  const { isNavigationAccessible, userRole } = useSidebarNavigation()\n\n  const superAdminAccess = isNavigationAccessible('/super-admin/gateway-management')\n  const instituteAdminAccess = isNavigationAccessible('/admin/settings/payment-gateways')\n\n  return {\n    canAccessSuperAdminGateways: superAdminAccess,\n    canAccessInstituteAdminGateways: instituteAdminAccess,\n    canAccessAnyGateways: superAdminAccess || instituteAdminAccess,\n    userRole,\n    isSuperAdmin: userRole === 'super_admin',\n    isInstituteAdmin: userRole === 'institute_admin'\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAOO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,eAAe,EAAE,6BAA6B,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,EACJ,eAAe,EACf,QAAQ,EACR,kBAAkB,EAClB,+BAA+B,wBAAwB,EACvD,oBAAoB,EACrB,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAElB,+CAA+C;IAC/C,MAAM,0BAA0B,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD;iEAAE;YACtC,IAAI,CAAC,MAAM,OAAO,CAAC,oBAAoB,gBAAgB,MAAM,KAAK,GAAG;gBACnE,OAAO,EAAE;YACX;YAEA,sDAAsD;YACtD,IAAI,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK,kBAAkB;gBACvF,OAAO;YACT;YAEA,qEAAqE;YACrE,OAAO,8BAA8B;QACvC;gEAAG;QAAC;QAAiB,gBAAgB,IAAI;QAAE;KAA8B;IAEzE,8DAA8D;IAC9D,MAAM,6BAA6B,CAAC;QAClC,uCAAuC;QACvC,IAAI,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK,kBAAkB;YACvF,OAAO;QACT;QAEA,yBAAyB;QACzB,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;YACnD,iDAAiD;YACjD,IAAI,KAAK,WAAW,CAAC,QAAQ,CAAC,gBAAgB,IAAI,GAAG;gBACnD,OAAO;YACT;YAEA,oDAAoD;YACpD,OAAO,KAAK,WAAW,CAAC,IAAI,CAAC,CAAA,aAC3B,gBAAgB,WAAW,CAAC,IAAI,CAAC,CAAA,WAC/B,SAAS,IAAI,KAAK,cAAc,SAAS,IAAI,KAAK;QAGxD;QAEA,4CAA4C;QAC5C,OAAO;IACT;IAEA,yDAAyD;IACzD,MAAM,mCAAmC,CAAC;QACxC,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,IAAI,eAAe;gBAAE,GAAG,IAAI;YAAC;YAE7B,gCAAgC;YAChC,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC7C,MAAM,mBAAmB,iCAAiC,KAAK,QAAQ,EACpE,MAAM,CAAC,CAAA,QAAS,2BAA2B;gBAE9C,aAAa,QAAQ,GAAG;YAC1B;YAEA,OAAO;QACT,GAAG,MAAM,CAAC,CAAA,OAAQ,2BAA2B;IAC/C;IAEA,wDAAwD;IACxD,MAAM,6BAA6B,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD;oEAAE;YACzC,IAAI,CAAC,MAAM,OAAO,CAAC,oBAAoB,gBAAgB,MAAM,KAAK,GAAG;gBACnE,OAAO,EAAE;YACX;YAEA,oCAAoC;YACpC,IAAI,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK,kBAAkB;gBACvF,OAAO;YACT;YAEA,OAAO,iCAAiC;QAC1C;mEAAG;QAAC;QAAiB,gBAAgB,IAAI;QAAE,gBAAgB,WAAW;KAAC;IAEvE,oDAAoD;IACpD,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,QAAQ,gBAAgB,IAAI,EAAE;gBAChC,iDAAiD;gBACjD,MAAM,kBAAkB,gBAAgB,WAAW,CAAC,GAAG;sEAAC,CAAA,IAAK,EAAE,IAAI,IAAI,EAAE,IAAI;;gBAC7E,yBAAyB;YAC3B;QACF;yCAAG;QAAC;QAAM,gBAAgB,WAAW;QAAE;KAAyB;IAEhE,kCAAkC;IAClC,MAAM,yBAAyB,CAAC;QAC9B,OAAO,2BAA2B,MAAM,CAAC,CAAA,OACvC,KAAK,IAAI,CAAC,QAAQ,CAAC,YAAY,WAAW;IAE9C;IAEA,kDAAkD;IAClD,MAAM,yBAAyB,CAAC;QAC9B,MAAM,iBAAiB,CAAC;YACtB,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,IAAI,KAAK,MAAM;oBACtB,OAAO;gBACT;gBACA,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,QAAQ,eAAe,KAAK,QAAQ;oBAC1C,IAAI,OAAO,OAAO;gBACpB;YACF;YACA,OAAO;QACT;QAEA,MAAM,OAAO,eAAe;QAC5B,OAAO,CAAC,CAAC;IACX;IAEA,kCAAkC;IAClC,MAAM,qBAAqB;QACzB,MAAM,aAAa,CAAC;YAClB,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO;gBAC1B,IAAI,YAAY;gBAChB,IAAI,KAAK,QAAQ,EAAE;oBACjB,aAAa,WAAW,KAAK,QAAQ;gBACvC;gBACA,OAAO,QAAQ;YACjB,GAAG;QACL;QAEA,MAAM,kBAAkB,WAAW;QACnC,MAAM,aAAa,WAAW;QAE9B,OAAO;YACL,YAAY;YACZ,OAAO;YACP,YAAY,aAAa;QAC3B;IACF;IAEA,6BAA6B;IAC7B,MAAM,qBAAqB,CAAC;QAC1B,MAAM,WAAW,CAAC;YAChB,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,EAAE,KAAK,IAAI;oBAClB,OAAO;gBACT;gBACA,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,QAAQ,SAAS,KAAK,QAAQ;oBACpC,IAAI,OAAO,OAAO;gBACpB;YACF;YACA,OAAO;QACT;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL,mBAAmB;QACnB,iBAAiB;QACjB,oBAAoB;QAEpB,oBAAoB;QACpB;QACA;QAEA,oBAAoB;QACpB;QACA;QACA;QAEA,gBAAgB;QAChB;QACA;QAEA,YAAY;QACZ;QACA,UAAU,gBAAgB,IAAI;QAE9B,QAAQ;QACR,iBAAiB;IACnB;AACF;GAvLgB;;QACG,4JAAA,CAAA,eAAY;QAC8B,4JAAA,CAAA,iBAAc;QAOrE,kKAAA,CAAA,kBAAe;;;AAmLd,SAAS,oBAAoB,aAAuB;;IACzD,MAAM,EAAE,sBAAsB,EAAE,GAAG;IAEnC,MAAM,YAAY,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD;kDAAE;YACxB,OAAO,cAAc,MAAM;0DAAC,CAAC,KAAK;oBAChC,GAAG,CAAC,GAAG,GAAG,uBAAuB,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,MAAM,MAAM;oBAC5D,OAAO;gBACT;yDAAG,CAAC;QACN;iDAAG;QAAC;QAAe;KAAuB;IAE1C,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA,KAAM,SAAS,CAAC,GAAG;IAC9D,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA,KAAM,CAAC,SAAS,CAAC,GAAG;IAE/D,OAAO;QACL;QACA;QACA;QACA,gBAAgB,cAAc,MAAM,GAAG;QACvC,gBAAgB,cAAc,MAAM,KAAK;IAC3C;AACF;IApBgB;;QACqB;;;AAwB9B,SAAS;;IACd,MAAM,EAAE,sBAAsB,EAAE,QAAQ,EAAE,GAAG;IAE7C,MAAM,mBAAmB,uBAAuB;IAChD,MAAM,uBAAuB,uBAAuB;IAEpD,OAAO;QACL,6BAA6B;QAC7B,iCAAiC;QACjC,sBAAsB,oBAAoB;QAC1C;QACA,cAAc,aAAa;QAC3B,kBAAkB,aAAa;IACjC;AACF;IAdgB;;QAC+B", "debugId": null}}, {"offset": {"line": 2163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/utils/navigationUtils.ts"], "sourcesContent": ["import { NavigationItem } from '@/stores/sidebar/useSidebarStore'\n\n/**\n * Check if a navigation item should be considered active based on the current pathname\n * This includes checking if the current path matches the item's href or any of its children's hrefs\n */\nexport function isNavigationItemActive(item: NavigationItem, pathname: string): boolean {\n  // Direct match\n  if (pathname === item.href) {\n    return true\n  }\n\n  // Check if current path starts with the item's href (for nested routes)\n  // But only if the item's href is not just a root path like '/'\n  if (item.href !== '/' && pathname.startsWith(item.href)) {\n    return true\n  }\n\n  // Check children recursively\n  if (item.children && item.children.length > 0) {\n    return item.children.some(child => isNavigationItemActive(child, pathname))\n  }\n\n  return false\n}\n\n/**\n * Check if a navigation item's children contain the active path\n * This is useful for determining if a parent item should be expanded\n */\nexport function hasActiveChild(item: NavigationItem, pathname: string): boolean {\n  if (!item.children || item.children.length === 0) {\n    return false\n  }\n\n  return item.children.some(child => isNavigationItemActive(child, pathname))\n}\n\n/**\n * Get the active child item for a navigation item\n */\nexport function getActiveChild(item: NavigationItem, pathname: string): NavigationItem | null {\n  if (!item.children || item.children.length === 0) {\n    return null\n  }\n\n  for (const child of item.children) {\n    if (isNavigationItemActive(child, pathname)) {\n      return child\n    }\n  }\n\n  return null\n}\n\n/**\n * Check if a navigation item should be expanded based on the current pathname\n */\nexport function shouldExpandNavigationItem(item: NavigationItem, pathname: string): boolean {\n  // Expand if the item itself is active\n  if (pathname === item.href) {\n    return true\n  }\n\n  // Expand if any child is active\n  return hasActiveChild(item, pathname)\n}\n\n/**\n * Get breadcrumb items for the current pathname based on navigation structure\n */\nexport function getBreadcrumbsFromNavigation(\n  navigationItems: NavigationItem[], \n  pathname: string\n): Array<{ label: string; href: string }> {\n  const breadcrumbs: Array<{ label: string; href: string }> = []\n\n  function findPath(items: NavigationItem[], currentPath: Array<{ label: string; href: string }>): boolean {\n    for (const item of items) {\n      const newPath = [...currentPath, { label: item.label, href: item.href }]\n\n      if (isNavigationItemActive(item, pathname)) {\n        breadcrumbs.push(...newPath)\n        return true\n      }\n\n      if (item.children && findPath(item.children, newPath)) {\n        return true\n      }\n    }\n    return false\n  }\n\n  findPath(navigationItems, [])\n  return breadcrumbs\n}\n"], "names": [], "mappings": ";;;;;;;AAMO,SAAS,uBAAuB,IAAoB,EAAE,QAAgB;IAC3E,eAAe;IACf,IAAI,aAAa,KAAK,IAAI,EAAE;QAC1B,OAAO;IACT;IAEA,wEAAwE;IACxE,+DAA+D;IAC/D,IAAI,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;QACvD,OAAO;IACT;IAEA,6BAA6B;IAC7B,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;QAC7C,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,uBAAuB,OAAO;IACnE;IAEA,OAAO;AACT;AAMO,SAAS,eAAe,IAAoB,EAAE,QAAgB;IACnE,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,GAAG;QAChD,OAAO;IACT;IAEA,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,uBAAuB,OAAO;AACnE;AAKO,SAAS,eAAe,IAAoB,EAAE,QAAgB;IACnE,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,GAAG;QAChD,OAAO;IACT;IAEA,KAAK,MAAM,SAAS,KAAK,QAAQ,CAAE;QACjC,IAAI,uBAAuB,OAAO,WAAW;YAC3C,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAKO,SAAS,2BAA2B,IAAoB,EAAE,QAAgB;IAC/E,sCAAsC;IACtC,IAAI,aAAa,KAAK,IAAI,EAAE;QAC1B,OAAO;IACT;IAEA,gCAAgC;IAChC,OAAO,eAAe,MAAM;AAC9B;AAKO,SAAS,6BACd,eAAiC,EACjC,QAAgB;IAEhB,MAAM,cAAsD,EAAE;IAE9D,SAAS,SAAS,KAAuB,EAAE,WAAmD;QAC5F,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,UAAU;mBAAI;gBAAa;oBAAE,OAAO,KAAK,KAAK;oBAAE,MAAM,KAAK,IAAI;gBAAC;aAAE;YAExE,IAAI,uBAAuB,MAAM,WAAW;gBAC1C,YAAY,IAAI,IAAI;gBACpB,OAAO;YACT;YAEA,IAAI,KAAK,QAAQ,IAAI,SAAS,KAAK,QAAQ,EAAE,UAAU;gBACrD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,SAAS,iBAAiB,EAAE;IAC5B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/SidebarItem.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useState, useEffect } from 'react'\nimport { usePathname } from 'next/navigation'\nimport { useSidebarStore, NavigationItem } from '@/stores/sidebar/useSidebarStore'\nimport { isNavigationItemActive, shouldExpandNavigationItem } from '@/utils/navigationUtils'\nimport {\n  Star,\n  StarOff,\n  ChevronRight,\n  ChevronDown\n} from 'lucide-react'\nimport { LucideIcon } from 'lucide-react'\nimport * as Icons from 'lucide-react'\n\ninterface SidebarItemProps {\n  item: NavigationItem\n  isActive: boolean\n  isCollapsed: boolean\n  onClick: () => void\n  variant?: 'default' | 'compact'\n}\n\nexport function SidebarItem({\n  item,\n  isActive,\n  isCollapsed,\n  onClick,\n  variant = 'default'\n}: SidebarItemProps) {\n  const {\n    favoriteItems,\n    addToFavorites,\n    removeFromFavorites\n  } = useSidebarStore()\n\n  const pathname = usePathname()\n  const [isExpanded, setIsExpanded] = useState(false)\n  const isFavorite = favoriteItems.includes(item.id)\n  const hasChildren = item.children && item.children.length > 0\n\n  // Auto-expand if this item or any of its children are active\n  useEffect(() => {\n    if (shouldExpandNavigationItem(item, pathname)) {\n      setIsExpanded(true)\n    }\n  }, [item, pathname])\n\n  // Get the icon component\n  const IconComponent = (Icons as any)[item.icon] as LucideIcon\n  \n  const handleFavoriteToggle = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    \n    if (isFavorite) {\n      removeFromFavorites(item.id)\n    } else {\n      addToFavorites(item.id)\n    }\n  }\n\n  const handleExpandToggle = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setIsExpanded(!isExpanded)\n  }\n\n  const handleItemClick = () => {\n    onClick()\n    if (hasChildren) {\n      setIsExpanded(!isExpanded)\n    }\n  }\n\n  const baseClasses = `\n    group relative flex items-center w-full text-left transition-all duration-200\n    ${variant === 'compact' ? 'px-3 py-1.5' : 'px-3 py-2'}\n    ${isActive \n      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' \n      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n    }\n    rounded-lg mx-1\n  `\n\n  const content = (\n    <>\n      {/* Icon */}\n      <div className={`flex-shrink-0 ${isCollapsed ? 'mx-auto' : 'mr-3'}`}>\n        {IconComponent && (\n          <IconComponent \n            className={`w-5 h-5 ${\n              isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'\n            }`} \n          />\n        )}\n      </div>\n\n      {/* Label and Badge */}\n      {!isCollapsed && (\n        <>\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex items-center justify-between\">\n              <span className={`text-sm font-medium truncate ${\n                variant === 'compact' ? 'text-xs' : ''\n              }`}>\n                {item.label}\n              </span>\n              \n              {/* Badge */}\n              {item.badge && item.badge > 0 && (\n                <span className=\"ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full\">\n                  {item.badge > 99 ? '99+' : item.badge}\n                </span>\n              )}\n            </div>\n            \n            {/* Description for non-compact variant */}\n            {variant !== 'compact' && item.description && (\n              <p className=\"text-xs text-gray-500 mt-0.5 truncate\">\n                {item.description}\n              </p>\n            )}\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex items-center space-x-1 ml-2\">\n            {/* Favorite Toggle */}\n            <div\n              onClick={handleFavoriteToggle}\n              className=\"p-1 rounded opacity-0 group-hover:opacity-100 hover:bg-gray-200 transition-all cursor-pointer\"\n              title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}\n              role=\"button\"\n              tabIndex={0}\n              onKeyDown={(e) => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  e.preventDefault()\n                  handleFavoriteToggle(e)\n                }\n              }}\n            >\n              {isFavorite ? (\n                <Star className=\"w-3 h-3 text-yellow-500 fill-current\" />\n              ) : (\n                <StarOff className=\"w-3 h-3 text-gray-400\" />\n              )}\n            </div>\n\n            {/* Expand Toggle for items with children */}\n            {hasChildren && (\n              <div\n                onClick={handleExpandToggle}\n                className=\"p-1 rounded hover:bg-gray-200 transition-colors cursor-pointer\"\n                role=\"button\"\n                tabIndex={0}\n                onKeyDown={(e) => {\n                  if (e.key === 'Enter' || e.key === ' ') {\n                    e.preventDefault()\n                    handleExpandToggle(e)\n                  }\n                }}\n              >\n                {isExpanded ? (\n                  <ChevronDown className=\"w-3 h-3 text-gray-400\" />\n                ) : (\n                  <ChevronRight className=\"w-3 h-3 text-gray-400\" />\n                )}\n              </div>\n            )}\n          </div>\n        </>\n      )}\n\n      {/* Tooltip for collapsed sidebar */}\n      {isCollapsed && (\n        <div className=\"absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50\">\n          {item.label}\n          {item.badge && item.badge > 0 && (\n            <span className=\"ml-1 px-1.5 py-0.5 text-xs bg-red-500 rounded-full\">\n              {item.badge > 99 ? '99+' : item.badge}\n            </span>\n          )}\n        </div>\n      )}\n    </>\n  )\n\n  return (\n    <div>\n      {/* Main Item */}\n      {hasChildren || item.isModal ? (\n        <button\n          onClick={handleItemClick}\n          className={baseClasses}\n        >\n          {content}\n        </button>\n      ) : (\n        <Link\n          href={item.href}\n          onClick={handleItemClick}\n          className={baseClasses}\n        >\n          {content}\n        </Link>\n      )}\n\n      {/* Children Items */}\n      {hasChildren && isExpanded && !isCollapsed && (\n        <div className=\"ml-6 mt-1 space-y-1\">\n          {item.children?.map((child) => (\n            <SidebarItem\n              key={child.id}\n              item={child}\n              isActive={isNavigationItemActive(child, pathname)}\n              isCollapsed={false}\n              onClick={() => onClick()}\n              variant=\"compact\"\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default SidebarItem\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAOA;;;AAdA;;;;;;;;AAwBO,SAAS,YAAY,EAC1B,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,SAAS,EACF;;IACjB,MAAM,EACJ,aAAa,EACb,cAAc,EACd,mBAAmB,EACpB,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,WAAW,CAAA,GAAA,kRAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;IACjD,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;IAE5D,6DAA6D;IAC7D,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAA,GAAA,sJAAA,CAAA,6BAA0B,AAAD,EAAE,MAAM,WAAW;gBAC9C,cAAc;YAChB;QACF;gCAAG;QAAC;QAAM;KAAS;IAEnB,yBAAyB;IACzB,MAAM,gBAAgB,AAAC,wPAAa,CAAC,KAAK,IAAI,CAAC;IAE/C,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,YAAY;YACd,oBAAoB,KAAK,EAAE;QAC7B,OAAO;YACL,eAAe,KAAK,EAAE;QACxB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,kBAAkB;QACtB;QACA,IAAI,aAAa;YACf,cAAc,CAAC;QACjB;IACF;IAEA,MAAM,cAAc,CAAC;;IAEnB,EAAE,YAAY,YAAY,gBAAgB,YAAY;IACtD,EAAE,WACE,wDACA,qDACH;;EAEH,CAAC;IAED,MAAM,wBACJ;;0BAEE,0UAAC;gBAAI,WAAW,CAAC,cAAc,EAAE,cAAc,YAAY,QAAQ;0BAChE,+BACC,0UAAC;oBACC,WAAW,CAAC,QAAQ,EAClB,WAAW,kBAAkB,2CAC7B;;;;;;;;;;;YAMP,CAAC,6BACA;;kCACE,0UAAC;wBAAI,WAAU;;0CACb,0UAAC;gCAAI,WAAU;;kDACb,0UAAC;wCAAK,WAAW,CAAC,6BAA6B,EAC7C,YAAY,YAAY,YAAY,IACpC;kDACC,KAAK,KAAK;;;;;;oCAIZ,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,0UAAC;wCAAK,WAAU;kDACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;;;;;;4BAM1C,YAAY,aAAa,KAAK,WAAW,kBACxC,0UAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;;;;;;;kCAMvB,0UAAC;wBAAI,WAAU;;0CAEb,0UAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAO,aAAa,0BAA0B;gCAC9C,MAAK;gCACL,UAAU;gCACV,WAAW,CAAC;oCACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;wCACtC,EAAE,cAAc;wCAChB,qBAAqB;oCACvB;gCACF;0CAEC,2BACC,0UAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,0UAAC,mSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;4BAKtB,6BACC,0UAAC;gCACC,SAAS;gCACT,WAAU;gCACV,MAAK;gCACL,UAAU;gCACV,WAAW,CAAC;oCACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;wCACtC,EAAE,cAAc;wCAChB,mBAAmB;oCACrB;gCACF;0CAEC,2BACC,0UAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,0UAAC,6SAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;YASnC,6BACC,0UAAC;gBAAI,WAAU;;oBACZ,KAAK,KAAK;oBACV,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,0UAAC;wBAAK,WAAU;kCACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;;;;;;;;IAQjD,qBACE,0UAAC;;YAEE,eAAe,KAAK,OAAO,iBAC1B,0UAAC;gBACC,SAAS;gBACT,WAAW;0BAEV;;;;;qCAGH,0UAAC,4SAAA,CAAA,UAAI;gBACH,MAAM,KAAK,IAAI;gBACf,SAAS;gBACT,WAAW;0BAEV;;;;;;YAKJ,eAAe,cAAc,CAAC,6BAC7B,0UAAC;gBAAI,WAAU;0BACZ,KAAK,QAAQ,EAAE,IAAI,CAAC,sBACnB,0UAAC;wBAEC,MAAM;wBACN,UAAU,CAAA,GAAA,sJAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO;wBACxC,aAAa;wBACb,SAAS,IAAM;wBACf,SAAQ;uBALH,MAAM,EAAE;;;;;;;;;;;;;;;;AAY3B;GAzMgB;;QAWV,kKAAA,CAAA,kBAAe;QAEF,kRAAA,CAAA,cAAW;;;KAbd;uCA2MD", "debugId": null}}, {"offset": {"line": 2529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/UserProfile.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { \n  User, \n  LogOut, \n  Setting<PERSON>, \n  ChevronUp,\n  ChevronDown\n} from 'lucide-react'\n\ninterface UserProfileProps {\n  user: any // User type from auth store\n  isCollapsed: boolean\n  onLogout: () => void\n}\n\nexport function UserProfile({ user, isCollapsed, onLogout }: UserProfileProps) {\n  const [showMenu, setShowMenu] = useState(false)\n  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false)\n\n  const handleLogoutClick = () => {\n    setShowLogoutConfirm(true)\n    setShowMenu(false)\n  }\n\n  const handleConfirmLogout = () => {\n    onLogout()\n    setShowLogoutConfirm(false)\n  }\n\n  const handleCancelLogout = () => {\n    setShowLogoutConfirm(false)\n  }\n\n  if (isCollapsed) {\n    return (\n      <div className=\"p-2\">\n        <div className=\"relative\">\n          <button\n            onClick={() => setShowMenu(!showMenu)}\n            className=\"w-full p-2 rounded-lg hover:bg-gray-100 transition-colors group\"\n            title={user?.personalInfo?.fullName || user?.email || 'User Menu'}\n          >\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mx-auto\">\n              {user?.personalInfo?.avatar ? (\n                <img \n                  src={user.personalInfo.avatar} \n                  alt={user.personalInfo.fullName || user.email}\n                  className=\"w-8 h-8 rounded-full object-cover\"\n                />\n              ) : (\n                <User className=\"w-4 h-4 text-white\" />\n              )}\n            </div>\n          </button>\n\n          {/* Collapsed Menu */}\n          {showMenu && (\n            <div className=\"absolute bottom-full left-0 mb-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50\">\n              <div className=\"p-3 border-b border-gray-200\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n                    {user?.personalInfo?.avatar ? (\n                      <img \n                        src={user.personalInfo.avatar} \n                        alt={user.personalInfo.fullName || user.email}\n                        className=\"w-8 h-8 rounded-full object-cover\"\n                      />\n                    ) : (\n                      <User className=\"w-4 h-4 text-white\" />\n                    )}\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"text-sm font-medium text-gray-900 truncate\">\n                      {user?.personalInfo?.fullName || user?.email || 'User'}\n                    </div>\n                    <div className=\"text-xs text-gray-500 capitalize\">\n                      {user?.role?.name || 'User'}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"py-1\">\n                <button\n                  onClick={() => {\n                    setShowMenu(false)\n                    // Navigate to settings\n                  }}\n                  className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                >\n                  <Settings className=\"w-4 h-4 mr-2\" />\n                  Settings\n                </button>\n                <button\n                  onClick={handleLogoutClick}\n                  className=\"flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\"\n                >\n                  <LogOut className=\"w-4 h-4 mr-2\" />\n                  Sign Out\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"p-4\">\n      {/* User Info */}\n      <div className=\"relative\">\n        <button\n          onClick={() => setShowMenu(!showMenu)}\n          className=\"w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors group\"\n        >\n          <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\">\n            {user?.personalInfo?.avatar ? (\n              <img \n                src={user.personalInfo.avatar} \n                alt={user.personalInfo.fullName || user.email}\n                className=\"w-10 h-10 rounded-full object-cover\"\n              />\n            ) : (\n              <User className=\"w-5 h-5 text-white\" />\n            )}\n          </div>\n          \n          <div className=\"flex-1 min-w-0 text-left\">\n            <div className=\"text-sm font-medium text-gray-900 truncate\">\n              {user?.personalInfo?.fullName || user?.email || 'User'}\n            </div>\n            <div className=\"text-xs text-gray-500 capitalize\">\n              {user?.role?.name || 'User'}\n            </div>\n          </div>\n          \n          {showMenu ? (\n            <ChevronDown className=\"w-4 h-4 text-gray-400\" />\n          ) : (\n            <ChevronUp className=\"w-4 h-4 text-gray-400\" />\n          )}\n        </button>\n\n        {/* Expanded Menu */}\n        {showMenu && (\n          <div className=\"absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50\">\n            <div className=\"py-1\">\n              <button\n                onClick={() => {\n                  setShowMenu(false)\n                  // Navigate to settings\n                }}\n                className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                <Settings className=\"w-4 h-4 mr-2\" />\n                Settings\n              </button>\n              <button\n                onClick={handleLogoutClick}\n                className=\"flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\"\n              >\n                <LogOut className=\"w-4 h-4 mr-2\" />\n                Sign Out\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Logout Confirmation Modal */}\n      {showLogoutConfirm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Confirm Sign Out\n            </h3>\n            <p className=\"text-sm text-gray-600 mb-4\">\n              Are you sure you want to sign out? You'll need to sign in again to access your account.\n            </p>\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={handleCancelLogout}\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleConfirmLogout}\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors\"\n              >\n                Sign Out\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default UserProfile\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAiBO,SAAS,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAoB;;IAC3E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,oBAAoB;QACxB,qBAAqB;QACrB,YAAY;IACd;IAEA,MAAM,sBAAsB;QAC1B;QACA,qBAAqB;IACvB;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;IACvB;IAEA,IAAI,aAAa;QACf,qBACE,0UAAC;YAAI,WAAU;sBACb,cAAA,0UAAC;gBAAI,WAAU;;kCACb,0UAAC;wBACC,SAAS,IAAM,YAAY,CAAC;wBAC5B,WAAU;wBACV,OAAO,MAAM,cAAc,YAAY,MAAM,SAAS;kCAEtD,cAAA,0UAAC;4BAAI,WAAU;sCACZ,MAAM,cAAc,uBACnB,0UAAC;gCACC,KAAK,KAAK,YAAY,CAAC,MAAM;gCAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;gCAC7C,WAAU;;;;;qDAGZ,0UAAC,yRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;oBAMrB,0BACC,0UAAC;wBAAI,WAAU;;0CACb,0UAAC;gCAAI,WAAU;0CACb,cAAA,0UAAC;oCAAI,WAAU;;sDACb,0UAAC;4CAAI,WAAU;sDACZ,MAAM,cAAc,uBACnB,0UAAC;gDACC,KAAK,KAAK,YAAY,CAAC,MAAM;gDAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;gDAC7C,WAAU;;;;;qEAGZ,0UAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,0UAAC;4CAAI,WAAU;;8DACb,0UAAC;oDAAI,WAAU;8DACZ,MAAM,cAAc,YAAY,MAAM,SAAS;;;;;;8DAElD,0UAAC;oDAAI,WAAU;8DACZ,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAM7B,0UAAC;gCAAI,WAAU;;kDACb,0UAAC;wCACC,SAAS;4CACP,YAAY;wCACZ,uBAAuB;wCACzB;wCACA,WAAU;;0DAEV,0UAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,0UAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,0UAAC,iSAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASnD;IAEA,qBACE,0UAAC;QAAI,WAAU;;0BAEb,0UAAC;gBAAI,WAAU;;kCACb,0UAAC;wBACC,SAAS,IAAM,YAAY,CAAC;wBAC5B,WAAU;;0CAEV,0UAAC;gCAAI,WAAU;0CACZ,MAAM,cAAc,uBACnB,0UAAC;oCACC,KAAK,KAAK,YAAY,CAAC,MAAM;oCAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;oCAC7C,WAAU;;;;;yDAGZ,0UAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAIpB,0UAAC;gCAAI,WAAU;;kDACb,0UAAC;wCAAI,WAAU;kDACZ,MAAM,cAAc,YAAY,MAAM,SAAS;;;;;;kDAElD,0UAAC;wCAAI,WAAU;kDACZ,MAAM,MAAM,QAAQ;;;;;;;;;;;;4BAIxB,yBACC,0UAAC,2SAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,0UAAC,uSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;oBAKxB,0BACC,0UAAC;wBAAI,WAAU;kCACb,cAAA,0UAAC;4BAAI,WAAU;;8CACb,0UAAC;oCACC,SAAS;wCACP,YAAY;oCACZ,uBAAuB;oCACzB;oCACA,WAAU;;sDAEV,0UAAC,iSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,0UAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,0UAAC,iSAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAS5C,mCACC,0UAAC;gBAAI,WAAU;0BACb,cAAA,0UAAC;oBAAI,WAAU;;sCACb,0UAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,0UAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,0UAAC;4BAAI,WAAU;;8CACb,0UAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,0UAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAxLgB;KAAA;uCA0LD", "debugId": null}}, {"offset": {"line": 2946, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/SidebarSearch.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport Link from 'next/link'\nimport { useSidebarStore } from '@/stores/sidebar/useSidebarStore'\nimport { Search, X } from 'lucide-react'\nimport * as Icons from 'lucide-react'\n\nexport function SidebarSearch() {\n  const {\n    searchQuery,\n    searchResults,\n    isSearching,\n    setSearchQuery,\n    performSearch,\n    clearSearch\n  } = useSidebarStore()\n  \n  const [showResults, setShowResults] = useState(false)\n  const searchRef = useRef<HTMLDivElement>(null)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  // Handle search input\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value\n    setSearchQuery(query)\n    \n    if (query.trim()) {\n      performSearch(query)\n      setShowResults(true)\n    } else {\n      setShowResults(false)\n    }\n  }\n\n  // Handle search submit\n  const handleSearchSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (searchQuery.trim()) {\n      performSearch(searchQuery)\n      setShowResults(true)\n    }\n  }\n\n  // Clear search\n  const handleClearSearch = () => {\n    clearSearch()\n    setShowResults(false)\n    inputRef.current?.focus()\n  }\n\n  // Close results when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setShowResults(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  // Handle keyboard navigation\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      setShowResults(false)\n      inputRef.current?.blur()\n    }\n  }\n\n  return (\n    <div ref={searchRef} className=\"relative\">\n      <form onSubmit={handleSearchSubmit}>\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n          <input\n            ref={inputRef}\n            type=\"text\"\n            placeholder=\"Search navigation...\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            onKeyDown={handleKeyDown}\n            onFocus={() => searchQuery && setShowResults(true)}\n            className=\"w-full pl-10 pr-8 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n          {searchQuery && (\n            <button\n              type=\"button\"\n              onClick={handleClearSearch}\n              className=\"absolute right-2 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded\"\n            >\n              <X className=\"w-3 h-3 text-gray-400\" />\n            </button>\n          )}\n        </div>\n      </form>\n\n      {/* Search Results */}\n      {showResults && (\n        <div className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto\">\n          {isSearching ? (\n            <div className=\"p-3 text-center text-sm text-gray-500\">\n              Searching...\n            </div>\n          ) : searchResults.length > 0 ? (\n            <div className=\"py-1\">\n              {searchResults.map((item) => {\n                const IconComponent = (Icons as any)[item.icon]\n                \n                return (\n                  <Link\n                    key={item.id}\n                    href={item.href}\n                    onClick={() => {\n                      setShowResults(false)\n                      setSearchQuery('')\n                    }}\n                    className=\"flex items-center px-3 py-2 text-sm hover:bg-gray-50 transition-colors\"\n                  >\n                    <div className=\"flex-shrink-0 mr-3\">\n                      {IconComponent && (\n                        <IconComponent className=\"w-4 h-4 text-gray-400\" />\n                      )}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"font-medium text-gray-900 truncate\">\n                        {item.label}\n                      </div>\n                      {item.description && (\n                        <div className=\"text-xs text-gray-500 truncate\">\n                          {item.description}\n                        </div>\n                      )}\n                    </div>\n                    {item.badge && item.badge > 0 && (\n                      <span className=\"ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full\">\n                        {item.badge > 99 ? '99+' : item.badge}\n                      </span>\n                    )}\n                  </Link>\n                )\n              })}\n            </div>\n          ) : searchQuery ? (\n            <div className=\"p-3 text-center text-sm text-gray-500\">\n              No results found for \"{searchQuery}\"\n            </div>\n          ) : null}\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default SidebarSearch\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,EACJ,WAAW,EACX,aAAa,EACb,WAAW,EACX,cAAc,EACd,aAAa,EACb,WAAW,EACZ,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,YAAY,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,WAAW,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,sBAAsB;IACtB,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QAEf,IAAI,MAAM,IAAI,IAAI;YAChB,cAAc;YACd,eAAe;QACjB,OAAO;YACL,eAAe;QACjB;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,cAAc;YACd,eAAe;QACjB;IACF;IAEA,eAAe;IACf,MAAM,oBAAoB;QACxB;QACA,eAAe;QACf,SAAS,OAAO,EAAE;IACpB;IAEA,sCAAsC;IACtC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;8DAAqB,CAAC;oBAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC1E,eAAe;oBACjB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;2CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;kCAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB,eAAe;YACf,SAAS,OAAO,EAAE;QACpB;IACF;IAEA,qBACE,0UAAC;QAAI,KAAK;QAAW,WAAU;;0BAC7B,0UAAC;gBAAK,UAAU;0BACd,cAAA,0UAAC;oBAAI,WAAU;;sCACb,0UAAC,6RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,0UAAC;4BACC,KAAK;4BACL,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU;4BACV,WAAW;4BACX,SAAS,IAAM,eAAe,eAAe;4BAC7C,WAAU;;;;;;wBAEX,6BACC,0UAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,0UAAC,mRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAOpB,6BACC,0UAAC;gBAAI,WAAU;0BACZ,4BACC,0UAAC;oBAAI,WAAU;8BAAwC;;;;;2BAGrD,cAAc,MAAM,GAAG,kBACzB,0UAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC;wBAClB,MAAM,gBAAgB,AAAC,wPAAa,CAAC,KAAK,IAAI,CAAC;wBAE/C,qBACE,0UAAC,4SAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,SAAS;gCACP,eAAe;gCACf,eAAe;4BACjB;4BACA,WAAU;;8CAEV,0UAAC;oCAAI,WAAU;8CACZ,+BACC,0UAAC;wCAAc,WAAU;;;;;;;;;;;8CAG7B,0UAAC;oCAAI,WAAU;;sDACb,0UAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;wCAEZ,KAAK,WAAW,kBACf,0UAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW;;;;;;;;;;;;gCAItB,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,0UAAC;oCAAK,WAAU;8CACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;2BAzBpC,KAAK,EAAE;;;;;oBA8BlB;;;;;2BAEA,4BACF,0UAAC;oBAAI,WAAU;;wBAAwC;wBAC9B;wBAAY;;;;;;2BAEnC;;;;;;;;;;;;AAKd;GAjJgB;;QAQV,kKAAA,CAAA,kBAAe;;;KARL;uCAmJD", "debugId": null}}, {"offset": {"line": 3197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 3216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,0UAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 3279, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps extends React.ComponentProps<\"input\"> {\n  error?: string | boolean\n}\n\nfunction Input({ className, type, error, ...props }: InputProps) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      aria-invalid={!!error}\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        error && \"border-destructive ring-destructive/20\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAmB;IAC7D,qBACE,0UAAC;QACC,MAAM;QACN,aAAU;QACV,gBAAc,CAAC,CAAC;QAChB,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA,SAAS,0CACT;QAED,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 3312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,0UAAC,gXAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = \"Avatar\"\n\nconst AvatarImage = React.forwardRef<\n  HTMLImageElement,\n  React.ImgHTMLAttributes<HTMLImageElement>\n>(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = \"AvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = \"AvatarFallback\"\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,0UAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,0UAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,0UAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,0UAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,0UAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,0UAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,0UAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,0UAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,0UAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,0UAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 3523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/api.ts"], "sourcesContent": ["/**\n * API utility functions for making requests to the backend\n */\n\n// Get the API base URL from environment variables\nconst getApiBaseUrl = (): string => {\n  // Always use the full backend URL for API calls\n  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'\n}\n\n/**\n * Create a full API URL from a relative path\n */\nexport const createApiUrl = (path: string): string => {\n  const baseUrl = getApiBaseUrl()\n\n  // Remove leading slash from path if present\n  const cleanPath = path.startsWith('/') ? path.slice(1) : path\n\n  // Combine base URL with path\n  return `${baseUrl}/${cleanPath}`\n}\n\n/**\n * Get the auth token from localStorage or Zustand storage\n */\nconst getAuthToken = (): string | null => {\n  if (typeof window !== 'undefined') {\n    // First try direct auth_token\n    let token = localStorage.getItem('auth_token')\n\n    // If not found, try Zustand auth storage\n    if (!token) {\n      try {\n        const authStorage = localStorage.getItem('auth-storage')\n        if (authStorage) {\n          const parsed = JSON.parse(authStorage)\n          token = parsed?.state?.token || null\n        }\n      } catch (error) {\n        console.error('Failed to parse auth storage:', error)\n      }\n    }\n\n    console.log('🔍 getAuthToken:', {\n      hasToken: !!token,\n      tokenLength: token?.length,\n      tokenPreview: token ? `${token.substring(0, 10)}...${token.substring(token.length - 10)}` : 'null',\n      directToken: !!localStorage.getItem('auth_token'),\n      zustandToken: !!localStorage.getItem('auth-storage')\n    })\n    return token\n  }\n  return null\n}\n\n/**\n * Make an API request with proper error handling\n */\nexport const apiRequest = async (\n  path: string,\n  options: RequestInit = {}\n): Promise<Response> => {\n  const url = createApiUrl(path)\n  const token = getAuthToken()\n\n  const defaultOptions: RequestInit = {\n    credentials: 'include',\n    headers: {\n      'Content-Type': 'application/json',\n      ...(token && { 'Authorization': `Bearer ${token}` }),\n      ...options.headers,\n    },\n  }\n\n\n\n  const finalOptions = {\n    ...defaultOptions,\n    ...options,\n    headers: {\n      ...defaultOptions.headers,\n      ...options.headers,\n    },\n  }\n\n  console.log('🔍 API Request:', {\n    url,\n    method: finalOptions.method || 'GET',\n    hasAuth: !!(finalOptions.headers as any)?.['Authorization'],\n    authHeader: (finalOptions.headers as any)?.['Authorization'],\n    allHeaders: finalOptions.headers,\n    token: token ? `${token.substring(0, 10)}...${token.substring(token.length - 10)}` : 'NO_TOKEN'\n  })\n\n  try {\n    const response = await fetch(url, finalOptions)\n    console.log('✅ API Response:', {\n      url,\n      status: response.status,\n      ok: response.ok\n    })\n    return response\n  } catch (error) {\n    console.error('❌ API request failed:', { url, error })\n    throw error\n  }\n}\n\n/**\n * Make a GET request to the API\n */\nexport const apiGet = async (path: string, params?: Record<string, string>): Promise<Response> => {\n  let url = path\n  \n  if (params) {\n    const searchParams = new URLSearchParams(params)\n    url = `${path}?${searchParams.toString()}`\n  }\n  \n  return apiRequest(url, { method: 'GET' })\n}\n\n/**\n * Make a POST request to the API\n */\nexport const apiPost = async (path: string, data?: any): Promise<Response> => {\n  return apiRequest(path, {\n    method: 'POST',\n    body: data ? JSON.stringify(data) : undefined,\n  })\n}\n\n/**\n * Make a PUT request to the API\n */\nexport const apiPut = async (path: string, data?: any): Promise<Response> => {\n  return apiRequest(path, {\n    method: 'PUT',\n    body: data ? JSON.stringify(data) : undefined,\n  })\n}\n\n/**\n * Make a PATCH request to the API\n */\nexport const apiPatch = async (path: string, data?: any): Promise<Response> => {\n  return apiRequest(path, {\n    method: 'PATCH',\n    body: data ? JSON.stringify(data) : undefined,\n  })\n}\n\n/**\n * Make a DELETE request to the API\n */\nexport const apiDelete = async (path: string): Promise<Response> => {\n  return apiRequest(path, { method: 'DELETE' })\n}\n\n/**\n * Make a DELETE request to the API with request body\n */\nexport const apiDeleteWithBody = async (path: string, data?: any): Promise<Response> => {\n  const options: RequestInit = {\n    method: 'DELETE',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  }\n\n  if (data) {\n    options.body = JSON.stringify(data)\n  }\n\n  return apiRequest(path, options)\n}\n\n/**\n * Handle API response and extract JSON data\n */\nexport const handleApiResponse = async <T = any>(response: Response): Promise<T> => {\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}))\n\n    // Handle specific authorization errors\n    if (response.status === 401) {\n      console.error('🔒 Authorization failed - token may be invalid or expired')\n\n      // Only clear localStorage if the error specifically indicates token issues\n      // Don't automatically clear on every 401 - let the auth store handle it\n      const errorMessage = errorData.message || errorData.error || ''\n      if (errorMessage.includes('token') || errorMessage.includes('expired') || errorMessage.includes('invalid')) {\n        console.warn('🗑️ Clearing potentially invalid auth tokens')\n        if (typeof window !== 'undefined') {\n          localStorage.removeItem('auth_token')\n          localStorage.removeItem('auth-storage')\n        }\n      } else {\n        console.warn('⚠️ 401 error but not clearing tokens - may be permission issue')\n      }\n    }\n\n    if (response.status === 403) {\n      console.error('🚫 Access forbidden - insufficient permissions')\n    }\n\n    throw new Error(errorData.message || errorData.error || `HTTP ${response.status}`)\n  }\n\n  return response.json()\n}\n\n/**\n * Make a complete API call with error handling and JSON parsing\n */\nexport const apiCall = async <T = any>(\n  path: string,\n  options: RequestInit = {}\n): Promise<T> => {\n  const response = await apiRequest(path, options)\n  return handleApiResponse<T>(response)\n}\n\n/**\n * Verify if authorization token is available and valid format\n */\nexport const verifyAuthToken = (): { hasToken: boolean; tokenInfo: string } => {\n  const token = getAuthToken()\n  return {\n    hasToken: !!token,\n    tokenInfo: token ? `${token.substring(0, 10)}...${token.substring(token.length - 10)}` : 'No token'\n  }\n}\n\n/**\n * Debug function to check current auth state\n */\nexport const debugAuthState = () => {\n  if (typeof window === 'undefined') return { error: 'Not in browser' }\n\n  const directToken = localStorage.getItem('auth_token')\n  const authStorage = localStorage.getItem('auth-storage')\n  const userData = localStorage.getItem('user_data')\n\n  let zustandToken = null\n  try {\n    if (authStorage) {\n      const parsed = JSON.parse(authStorage)\n      zustandToken = parsed?.state?.token\n    }\n  } catch (e) {\n    // ignore\n  }\n\n  return {\n    directToken: directToken ? `${directToken.substring(0, 10)}...` : null,\n    zustandToken: zustandToken ? `${zustandToken.substring(0, 10)}...` : null,\n    hasUserData: !!userData,\n    authStorageKeys: authStorage ? Object.keys(JSON.parse(authStorage)) : [],\n    currentToken: getAuthToken()\n  }\n}\n\n/**\n * Convenience methods for common API operations\n */\nexport const api = {\n  get: async <T = any>(path: string, params?: Record<string, string>): Promise<T> => {\n    const response = await apiGet(path, params)\n    return handleApiResponse<T>(response)\n  },\n\n  post: async <T = any>(path: string, data?: any): Promise<T> => {\n    const response = await apiPost(path, data)\n    return handleApiResponse<T>(response)\n  },\n\n  put: async <T = any>(path: string, data?: any): Promise<T> => {\n    const response = await apiPut(path, data)\n    return handleApiResponse<T>(response)\n  },\n\n  patch: async <T = any>(path: string, data?: any): Promise<T> => {\n    const response = await apiPatch(path, data)\n    return handleApiResponse<T>(response)\n  },\n\n  delete: async <T = any>(path: string): Promise<T> => {\n    const response = await apiDelete(path)\n    return handleApiResponse<T>(response)\n  },\n\n  deleteWithBody: async <T = any>(path: string, data?: any): Promise<T> => {\n    const response = await apiDeleteWithBody(path, data)\n    return handleApiResponse<T>(response)\n  },\n\n  // Utility method to check auth status\n  checkAuth: () => verifyAuthToken(),\n\n  // Utility method to get auth token\n  getToken: () => getAuthToken(),\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,kDAAkD;;;;;;;;;;;;;;;;AAGzC;AAFT,MAAM,gBAAgB;IACpB,gDAAgD;IAChD,OAAO,6DAAmC;AAC5C;AAKO,MAAM,eAAe,CAAC;IAC3B,MAAM,UAAU;IAEhB,4CAA4C;IAC5C,MAAM,YAAY,KAAK,UAAU,CAAC,OAAO,KAAK,KAAK,CAAC,KAAK;IAEzD,6BAA6B;IAC7B,OAAO,GAAG,QAAQ,CAAC,EAAE,WAAW;AAClC;AAEA;;CAEC,GACD,MAAM,eAAe;IACnB,wCAAmC;QACjC,8BAA8B;QAC9B,IAAI,QAAQ,aAAa,OAAO,CAAC;QAEjC,yCAAyC;QACzC,IAAI,CAAC,OAAO;YACV,IAAI;gBACF,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,QAAQ,QAAQ,OAAO,SAAS;gBAClC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;QAEA,QAAQ,GAAG,CAAC,oBAAoB;YAC9B,UAAU,CAAC,CAAC;YACZ,aAAa,OAAO;YACpB,cAAc,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG,KAAK,GAAG;YAC5F,aAAa,CAAC,CAAC,aAAa,OAAO,CAAC;YACpC,cAAc,CAAC,CAAC,aAAa,OAAO,CAAC;QACvC;QACA,OAAO;IACT;;AAEF;AAKO,MAAM,aAAa,OACxB,MACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,aAAa;IACzB,MAAM,QAAQ;IAEd,MAAM,iBAA8B;QAClC,aAAa;QACb,SAAS;YACP,gBAAgB;YAChB,GAAI,SAAS;gBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;YAAC,CAAC;YACnD,GAAG,QAAQ,OAAO;QACpB;IACF;IAIA,MAAM,eAAe;QACnB,GAAG,cAAc;QACjB,GAAG,OAAO;QACV,SAAS;YACP,GAAG,eAAe,OAAO;YACzB,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,QAAQ,GAAG,CAAC,mBAAmB;QAC7B;QACA,QAAQ,aAAa,MAAM,IAAI;QAC/B,SAAS,CAAC,CAAE,aAAa,OAAO,EAAU,CAAC,gBAAgB;QAC3D,YAAa,aAAa,OAAO,EAAU,CAAC,gBAAgB;QAC5D,YAAY,aAAa,OAAO;QAChC,OAAO,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG,KAAK,GAAG;IACvF;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAClC,QAAQ,GAAG,CAAC,mBAAmB;YAC7B;YACA,QAAQ,SAAS,MAAM;YACvB,IAAI,SAAS,EAAE;QACjB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;YAAE;YAAK;QAAM;QACpD,MAAM;IACR;AACF;AAKO,MAAM,SAAS,OAAO,MAAc;IACzC,IAAI,MAAM;IAEV,IAAI,QAAQ;QACV,MAAM,eAAe,IAAI,gBAAgB;QACzC,MAAM,GAAG,KAAK,CAAC,EAAE,aAAa,QAAQ,IAAI;IAC5C;IAEA,OAAO,WAAW,KAAK;QAAE,QAAQ;IAAM;AACzC;AAKO,MAAM,UAAU,OAAO,MAAc;IAC1C,OAAO,WAAW,MAAM;QACtB,QAAQ;QACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;IACtC;AACF;AAKO,MAAM,SAAS,OAAO,MAAc;IACzC,OAAO,WAAW,MAAM;QACtB,QAAQ;QACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;IACtC;AACF;AAKO,MAAM,WAAW,OAAO,MAAc;IAC3C,OAAO,WAAW,MAAM;QACtB,QAAQ;QACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;IACtC;AACF;AAKO,MAAM,YAAY,OAAO;IAC9B,OAAO,WAAW,MAAM;QAAE,QAAQ;IAAS;AAC7C;AAKO,MAAM,oBAAoB,OAAO,MAAc;IACpD,MAAM,UAAuB;QAC3B,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;IACF;IAEA,IAAI,MAAM;QACR,QAAQ,IAAI,GAAG,KAAK,SAAS,CAAC;IAChC;IAEA,OAAO,WAAW,MAAM;AAC1B;AAKO,MAAM,oBAAoB,OAAgB;IAC/C,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QAEvD,uCAAuC;QACvC,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,QAAQ,KAAK,CAAC;YAEd,2EAA2E;YAC3E,wEAAwE;YACxE,MAAM,eAAe,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;YAC7D,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,YAAY;gBAC1G,QAAQ,IAAI,CAAC;gBACb,wCAAmC;oBACjC,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;gBAC1B;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,QAAQ,KAAK,CAAC;QAChB;QAEA,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;IACnF;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,UAAU,OACrB,MACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,WAAW,MAAM,WAAW,MAAM;IACxC,OAAO,kBAAqB;AAC9B;AAKO,MAAM,kBAAkB;IAC7B,MAAM,QAAQ;IACd,OAAO;QACL,UAAU,CAAC,CAAC;QACZ,WAAW,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG,KAAK,GAAG;IAC3F;AACF;AAKO,MAAM,iBAAiB;IAC5B,uCAAmC;;IAAiC;IAEpE,MAAM,cAAc,aAAa,OAAO,CAAC;IACzC,MAAM,cAAc,aAAa,OAAO,CAAC;IACzC,MAAM,WAAW,aAAa,OAAO,CAAC;IAEtC,IAAI,eAAe;IACnB,IAAI;QACF,IAAI,aAAa;YACf,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,eAAe,QAAQ,OAAO;QAChC;IACF,EAAE,OAAO,GAAG;IACV,SAAS;IACX;IAEA,OAAO;QACL,aAAa,cAAc,GAAG,YAAY,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;QAClE,cAAc,eAAe,GAAG,aAAa,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;QACrE,aAAa,CAAC,CAAC;QACf,iBAAiB,cAAc,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,gBAAgB,EAAE;QACxE,cAAc;IAChB;AACF;AAKO,MAAM,MAAM;IACjB,KAAK,OAAgB,MAAc;QACjC,MAAM,WAAW,MAAM,OAAO,MAAM;QACpC,OAAO,kBAAqB;IAC9B;IAEA,MAAM,OAAgB,MAAc;QAClC,MAAM,WAAW,MAAM,QAAQ,MAAM;QACrC,OAAO,kBAAqB;IAC9B;IAEA,KAAK,OAAgB,MAAc;QACjC,MAAM,WAAW,MAAM,OAAO,MAAM;QACpC,OAAO,kBAAqB;IAC9B;IAEA,OAAO,OAAgB,MAAc;QACnC,MAAM,WAAW,MAAM,SAAS,MAAM;QACtC,OAAO,kBAAqB;IAC9B;IAEA,QAAQ,OAAgB;QACtB,MAAM,WAAW,MAAM,UAAU;QACjC,OAAO,kBAAqB;IAC9B;IAEA,gBAAgB,OAAgB,MAAc;QAC5C,MAAM,WAAW,MAAM,kBAAkB,MAAM;QAC/C,OAAO,kBAAqB;IAC9B;IAEA,sCAAsC;IACtC,WAAW,IAAM;IAEjB,mCAAmC;IACnC,UAAU,IAAM;AAClB", "debugId": null}}, {"offset": {"line": 3772, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/user/useUserStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools } from 'zustand/middleware'\nimport { toast } from 'sonner'\nimport { api } from '@/lib/api'\n\n// Types\nexport interface MediaObject {\n  id: number\n  url: string\n  filename?: string\n  mimeType?: string\n  filesize?: number\n  width?: number\n  height?: number\n  sizes?: {\n    avatar_small?: { url: string; width: number; height: number }\n    avatar_medium?: { url: string; width: number; height: number }\n    avatar_large?: { url: string; width: number; height: number }\n    [key: string]: any\n  }\n}\n\ninterface User {\n  id: string\n  firstName: string\n  lastName: string\n  email: string\n  phone?: string\n  avatar?: string | number | MediaObject | null\n  role?: any\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface UserProfileData {\n  firstName: string\n  lastName: string\n  email: string\n  phone?: string\n  password?: string\n  confirmPassword?: string\n  avatar?: string | File\n}\n\ninterface UserState {\n  // State\n  user: User | null\n  isLoading: boolean\n  isUpdating: boolean\n  error: string | null\n\n  // Actions\n  fetchCurrentUser: () => Promise<void>\n  updateProfile: (data: UserProfileData) => Promise<void>\n  uploadAvatar: (file: File) => Promise<string>\n  removeAvatar: () => Promise<void>\n  clearError: () => void\n  setUser: (user: User | null) => void\n}\n\nexport const useUserStore = create<UserState>()(\n  devtools(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isLoading: false,\n      isUpdating: false,\n      error: null,\n\n      // Actions\n      fetchCurrentUser: async () => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.get('/api/super-admin/user/me')\n          // Handle both response formats: { user: ... } or direct user object\n          const userData = response.user || response\n          set({ user: userData, isLoading: false })\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user'\n          set({ error: errorMessage, isLoading: false })\n          toast.error('Failed to load profile')\n        }\n      },\n\n      updateProfile: async (data: UserProfileData) => {\n        set({ isUpdating: true, error: null })\n        try {\n          // Prepare the update data\n          const updateData: any = {\n            firstName: data.firstName,\n            lastName: data.lastName,\n            email: data.email,\n            phone: data.phone\n          }\n\n          // Handle avatar if it's a file\n          if (data.avatar instanceof File) {\n            await get().uploadAvatar(data.avatar)\n            // Don't include avatar in updateData - the upload already updated the user record\n            console.log('🖼️ Avatar uploaded, user record already updated')\n          } else if (data.avatar === null) {\n            // Only send avatar if it's explicitly null (for removal)\n            updateData.avatar = null\n          } else if (typeof data.avatar === 'number') {\n            // If avatar is a media ID, use it directly\n            updateData.avatar = data.avatar\n          }\n          // Don't send avatar URLs - they should be media IDs\n\n          // Handle password update if provided\n          if (data.password && data.password.trim() !== '') {\n            updateData.password = data.password\n          }\n\n          // Remove confirmPassword from update data\n          delete updateData.confirmPassword\n\n          const response = await api.put('/api/super-admin/user/me', updateData)\n\n          // Handle response format: { success: true, user: ... }\n          const userData = response.user || response\n\n          set({\n            user: userData,\n            isUpdating: false,\n            error: null\n          })\n          \n          toast.success('Profile updated successfully')\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Failed to update profile'\n          set({ error: errorMessage, isUpdating: false })\n          toast.error('Failed to update profile')\n          throw error\n        }\n      },\n\n      uploadAvatar: async (file: File): Promise<string> => {\n        try {\n          // Validate file\n          if (file.size > 5 * 1024 * 1024) {\n            throw new Error('File too large. Please select an image smaller than 5MB')\n          }\n\n          if (!file.type.startsWith('image/')) {\n            throw new Error('Please select a valid image file')\n          }\n\n          // For now, create a local URL for the file\n          // In a real implementation, you would upload to your storage service\n          // TODO: Implement actual file upload to your storage service\n          const localUrl = URL.createObjectURL(file)\n\n          // Simulate upload delay\n          await new Promise(resolve => setTimeout(resolve, 1000))\n\n          return localUrl\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Failed to upload avatar'\n          toast.error(errorMessage)\n          throw error\n        }\n      },\n\n      removeAvatar: async () => {\n        set({ isUpdating: true, error: null })\n        try {\n          console.log('🗑️ Removing avatar via new API...')\n\n          // Use the new api.deleteWithBody method for DELETE with request body\n          const result = await api.deleteWithBody('/api/remove-avatar', {\n            removeFromUser: true // Remove avatar reference from user profile\n          })\n\n          console.log('📦 Remove avatar response:', result)\n\n          // Handle response format: { success: true, deletedMedia: ..., userUpdated: ... }\n          if (result.success) {\n            // Fetch updated user data to get the latest state\n            await get().fetchCurrentUser()\n\n            toast.success('Avatar removed successfully')\n          } else {\n            throw new Error(result.message || 'Failed to remove avatar')\n          }\n\n          set({ isUpdating: false, error: null })\n        } catch (error) {\n          console.error('❌ Remove avatar error:', error)\n          const errorMessage = error instanceof Error ? error.message : 'Failed to remove avatar'\n          set({ error: errorMessage, isUpdating: false })\n          toast.error('Failed to remove avatar')\n          throw error\n        }\n      },\n\n      clearError: () => {\n        set({ error: null })\n      },\n\n      setUser: (user: User | null) => {\n        set({ user })\n      }\n    }),\n    {\n      name: 'user-store',\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAyDO,MAAM,eAAe,CAAA,GAAA,wPAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6PAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,WAAW;QACX,YAAY;QACZ,OAAO;QAEP,UAAU;QACV,kBAAkB;YAChB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;gBAC/B,oEAAoE;gBACpE,MAAM,WAAW,SAAS,IAAI,IAAI;gBAClC,IAAI;oBAAE,MAAM;oBAAU,WAAW;gBAAM;YACzC,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBAAE,YAAY;gBAAM,OAAO;YAAK;YACpC,IAAI;gBACF,0BAA0B;gBAC1B,MAAM,aAAkB;oBACtB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;gBACnB;gBAEA,+BAA+B;gBAC/B,IAAI,KAAK,MAAM,YAAY,MAAM;oBAC/B,MAAM,MAAM,YAAY,CAAC,KAAK,MAAM;oBACpC,kFAAkF;oBAClF,QAAQ,GAAG,CAAC;gBACd,OAAO,IAAI,KAAK,MAAM,KAAK,MAAM;oBAC/B,yDAAyD;oBACzD,WAAW,MAAM,GAAG;gBACtB,OAAO,IAAI,OAAO,KAAK,MAAM,KAAK,UAAU;oBAC1C,2CAA2C;oBAC3C,WAAW,MAAM,GAAG,KAAK,MAAM;gBACjC;gBACA,oDAAoD;gBAEpD,qCAAqC;gBACrC,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,IAAI,OAAO,IAAI;oBAChD,WAAW,QAAQ,GAAG,KAAK,QAAQ;gBACrC;gBAEA,0CAA0C;gBAC1C,OAAO,WAAW,eAAe;gBAEjC,MAAM,WAAW,MAAM,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,4BAA4B;gBAE3D,uDAAuD;gBACvD,MAAM,WAAW,SAAS,IAAI,IAAI;gBAElC,IAAI;oBACF,MAAM;oBACN,YAAY;oBACZ,OAAO;gBACT;gBAEA,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,YAAY;gBAAM;gBAC7C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,cAAc,OAAO;YACnB,IAAI;gBACF,gBAAgB;gBAChB,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;oBAC/B,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBACnC,MAAM,IAAI,MAAM;gBAClB;gBAEA,2CAA2C;gBAC3C,qEAAqE;gBACrE,6DAA6D;gBAC7D,MAAM,WAAW,IAAI,eAAe,CAAC;gBAErC,wBAAwB;gBACxB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,cAAc;YACZ,IAAI;gBAAE,YAAY;gBAAM,OAAO;YAAK;YACpC,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,qEAAqE;gBACrE,MAAM,SAAS,MAAM,wIAAA,CAAA,MAAG,CAAC,cAAc,CAAC,sBAAsB;oBAC5D,gBAAgB,KAAK,4CAA4C;gBACnE;gBAEA,QAAQ,GAAG,CAAC,8BAA8B;gBAE1C,iFAAiF;gBACjF,IAAI,OAAO,OAAO,EAAE;oBAClB,kDAAkD;oBAClD,MAAM,MAAM,gBAAgB;oBAE5B,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;gBAEA,IAAI;oBAAE,YAAY;oBAAO,OAAO;gBAAK;YACvC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,YAAY;gBAAM;gBAC7C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK;QACb;IACF,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 3942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/api/file-upload.ts"], "sourcesContent": ["// Helper function to get auth token\nconst getAuthToken = (): string | null => {\n  if (typeof window !== 'undefined') {\n    // First try direct auth_token\n    let token = localStorage.getItem('auth_token')\n\n    // If not found, try Zustand auth storage\n    if (!token) {\n      try {\n        const authStorage = localStorage.getItem('auth-storage')\n        if (authStorage) {\n          const parsed = JSON.parse(authStorage)\n          token = parsed?.state?.token || null\n        }\n      } catch (error) {\n        console.error('Failed to parse auth storage:', error)\n      }\n    }\n\n    return token\n  }\n  return null\n}\n\n// File upload types\nexport type UploadType = 'avatar' | 'course_thumbnail' | 'institute_logo' | 'platform_logo' | 'platform_favicon' | 'document' | 'general'\n\n// Upload options\nexport interface UploadOptions {\n  uploadType: UploadType\n  updateUserField?: string // e.g., 'avatar' to update user.avatar field\n  folder?: string\n  onProgress?: (progress: number) => void\n}\n\n// Upload result\nexport interface UploadResult {\n  success: boolean\n  message: string\n  media?: {\n    id: string\n    filename: string\n    url: string\n    sizes?: Record<string, {\n      url: string\n      width: number\n      height: number\n    }>\n    alt: string\n    mediaType: string\n  }\n  user?: any // Updated user object if updateUserField was used\n  uploadType: string\n}\n\n// File info\nexport interface FileInfo {\n  id: string\n  filename: string\n  url: string\n  mediaType: string\n  filesize: number\n  mimeType: string\n  alt?: string\n  createdAt: string\n  updatedAt: string\n  sizes?: Record<string, any>\n}\n\n// Files response\nexport interface FilesResponse {\n  success: boolean\n  files: FileInfo[]\n  totalDocs: number\n  totalPages: number\n  page: number\n  limit: number\n  hasNextPage: boolean\n  hasPrevPage: boolean\n}\n\nclass FileUploadAPI {\n  private baseUrl: string\n\n  constructor() {\n    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'\n  }\n\n  /**\n   * Upload a file using the common upload API\n   */\n  async uploadFile(file: File, options: UploadOptions): Promise<UploadResult> {\n    try {\n      const formData = new FormData()\n      formData.append('file', file)\n      formData.append('uploadType', options.uploadType)\n      \n      if (options.updateUserField) {\n        formData.append('updateUserField', options.updateUserField)\n      }\n      \n      if (options.folder) {\n        formData.append('folder', options.folder)\n      }\n\n      console.log('📤 Uploading file:', {\n        name: file.name,\n        size: file.size,\n        type: file.type,\n        uploadType: options.uploadType,\n        updateUserField: options.updateUserField\n      })\n\n      const token = getAuthToken()\n      if (!token) {\n        throw new Error('No authentication token found')\n      }\n\n      const response = await fetch(`${this.baseUrl}/api/upload`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n        body: formData,\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      const result: UploadResult = await response.json()\n      \n      console.log('📦 Upload result:', result)\n      \n      return result\n    } catch (error) {\n      console.error('❌ Upload error:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Upload profile avatar (convenience method)\n   */\n  async uploadAvatar(file: File): Promise<UploadResult> {\n    return this.uploadFile(file, {\n      uploadType: 'avatar',\n      updateUserField: 'avatar',\n      folder: 'avatars'\n    })\n  }\n\n  /**\n   * Upload course thumbnail (convenience method)\n   */\n  async uploadCourseThumbnail(file: File): Promise<UploadResult> {\n    return this.uploadFile(file, {\n      uploadType: 'course_thumbnail',\n      folder: 'courses'\n    })\n  }\n\n  /**\n   * Upload institute logo (convenience method)\n   */\n  async uploadInstituteLogo(file: File): Promise<UploadResult> {\n    return this.uploadFile(file, {\n      uploadType: 'institute_logo',\n      folder: 'institutes'\n    })\n  }\n\n  /**\n   * Upload document (convenience method)\n   */\n  async uploadDocument(file: File): Promise<UploadResult> {\n    return this.uploadFile(file, {\n      uploadType: 'document',\n      folder: 'documents'\n    })\n  }\n\n  /**\n   * Upload platform logo using new platform settings endpoint\n   */\n  async uploadPlatformLogo(file: File): Promise<UploadResult> {\n    try {\n      const formData = new FormData()\n      formData.append('file', file)\n\n      console.log('📤 Uploading platform logo:', {\n        name: file.name,\n        size: file.size,\n        type: file.type\n      })\n\n      const token = getAuthToken()\n      if (!token) {\n        throw new Error('No authentication token found')\n      }\n\n      const response = await fetch(`${this.baseUrl}/api/platform/settings/logo`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n        body: formData,\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\n      }\n\n      const result = await response.json()\n\n      console.log('📦 Platform logo upload result:', result)\n\n      // Transform response to match UploadResult interface\n      return {\n        success: result.success,\n        message: result.message,\n        media: result.data?.media ? {\n          id: result.data.media.id,\n          filename: result.data.media.filename,\n          url: result.data.media.url,\n          sizes: result.data.media.sizes,\n          alt: result.data.media.alt || 'Platform Logo',\n          mediaType: 'platform_logo'\n        } : undefined,\n        uploadType: 'platform_logo'\n      }\n    } catch (error) {\n      console.error('❌ Platform logo upload error:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Upload platform favicon using new platform settings endpoint\n   */\n  async uploadPlatformFavicon(file: File): Promise<UploadResult> {\n    try {\n      const formData = new FormData()\n      formData.append('file', file)\n\n      console.log('📤 Uploading platform favicon:', {\n        name: file.name,\n        size: file.size,\n        type: file.type\n      })\n\n      const token = getAuthToken()\n      if (!token) {\n        throw new Error('No authentication token found')\n      }\n\n      const response = await fetch(`${this.baseUrl}/api/platform/settings/favicon`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n        body: formData,\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\n      }\n\n      const result = await response.json()\n\n      console.log('📦 Platform favicon upload result:', result)\n\n      // Transform response to match UploadResult interface\n      return {\n        success: result.success,\n        message: result.message,\n        media: result.data?.media ? {\n          id: result.data.media.id,\n          filename: result.data.media.filename,\n          url: result.data.media.url,\n          sizes: result.data.media.sizes,\n          alt: result.data.media.alt || 'Platform Favicon',\n          mediaType: 'platform_favicon'\n        } : undefined,\n        uploadType: 'platform_favicon'\n      }\n    } catch (error) {\n      console.error('❌ Platform favicon upload error:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Get platform branding assets (logo and favicon)\n   */\n  async getPlatformBranding(): Promise<{\n    success: boolean\n    data?: {\n      logo: any | null\n      favicon: any | null\n    }\n    message?: string\n  }> {\n    try {\n      const token = getAuthToken()\n      if (!token) {\n        throw new Error('No authentication token found')\n      }\n\n      const response = await fetch(`${this.baseUrl}/api/platform/settings/branding`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\n      }\n\n      const result = await response.json()\n\n      console.log('🎨 Platform branding:', result)\n\n      return result\n    } catch (error) {\n      console.error('❌ Get platform branding error:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Remove platform logo\n   */\n  async removePlatformLogo(): Promise<{ success: boolean; message: string }> {\n    try {\n      const token = getAuthToken()\n      if (!token) {\n        throw new Error('No authentication token found')\n      }\n\n      const response = await fetch(`${this.baseUrl}/api/platform/settings/logo`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\n      }\n\n      const result = await response.json()\n\n      console.log('🗑️ Remove platform logo result:', result)\n\n      return result\n    } catch (error) {\n      console.error('❌ Remove platform logo error:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Remove platform favicon\n   */\n  async removePlatformFavicon(): Promise<{ success: boolean; message: string }> {\n    try {\n      const token = getAuthToken()\n      if (!token) {\n        throw new Error('No authentication token found')\n      }\n\n      const response = await fetch(`${this.baseUrl}/api/platform/settings/favicon`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\n      }\n\n      const result = await response.json()\n\n      console.log('🗑️ Remove platform favicon result:', result)\n\n      return result\n    } catch (error) {\n      console.error('❌ Remove platform favicon error:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Process favicon from image (generates multiple sizes)\n   */\n  async processFavicon(file: File): Promise<{\n    success: boolean\n    data?: any\n    message?: string\n  }> {\n    try {\n      const formData = new FormData()\n      formData.append('file', file)\n\n      console.log('🔖 Processing favicon:', {\n        name: file.name,\n        size: file.size,\n        type: file.type\n      })\n\n      const token = getAuthToken()\n      if (!token) {\n        throw new Error('No authentication token found')\n      }\n\n      const response = await fetch(`${this.baseUrl}/api/platform/favicon/process`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n        body: formData,\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\n      }\n\n      const result = await response.json()\n\n      console.log('🔖 Favicon processing result:', result)\n\n      return result\n    } catch (error) {\n      console.error('❌ Favicon processing error:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Get user's uploaded files\n   */\n  async getMyFiles(options: {\n    mediaType?: string\n    page?: number\n    limit?: number\n  } = {}): Promise<FilesResponse> {\n    try {\n      const params = new URLSearchParams()\n      \n      if (options.mediaType) {\n        params.append('mediaType', options.mediaType)\n      }\n      if (options.page) {\n        params.append('page', options.page.toString())\n      }\n      if (options.limit) {\n        params.append('limit', options.limit.toString())\n      }\n\n      const url = `${this.baseUrl}/api/upload/my-files${params.toString() ? '?' + params.toString() : ''}`\n      \n      const token = getAuthToken()\n      if (!token) {\n        throw new Error('No authentication token found')\n      }\n\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      const result: FilesResponse = await response.json()\n      \n      console.log('📂 My files:', result)\n      \n      return result\n    } catch (error) {\n      console.error('❌ Get files error:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Get user's avatars only\n   */\n  async getMyAvatars(): Promise<FilesResponse> {\n    return this.getMyFiles({ mediaType: 'user_avatar' })\n  }\n\n  /**\n   * Delete a file\n   */\n  async deleteFile(fileId: string): Promise<{ success: boolean; message: string }> {\n    try {\n      const token = getAuthToken()\n      if (!token) {\n        throw new Error('No authentication token found')\n      }\n\n      const response = await fetch(`${this.baseUrl}/api/upload/${fileId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      const result = await response.json()\n      \n      console.log('🗑️ Delete result:', result)\n      \n      return result\n    } catch (error) {\n      console.error('❌ Delete error:', error)\n      throw error\n    }\n  }\n\n  /**\n   * Get full file URL\n   */\n  getFileUrl(path: string): string {\n    if (path.startsWith('http')) {\n      return path // Already a full URL (S3)\n    }\n\n    // For local storage, use the Next.js media route handler on port 3001\n    // This uses the /media/[...path]/route.ts handler we created\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'\n    return `${backendUrl}${path}` // Direct backend URL with media route handler\n  }\n\n  /**\n   * Get avatar URL for specific size\n   */\n  getAvatarUrl(media: FileInfo, size: 'small' | 'medium' | 'large' | 'profile' = 'medium'): string {\n    const sizeKey = `avatar_${size}`\n    \n    if (media.sizes && media.sizes[sizeKey]) {\n      return this.getFileUrl(media.sizes[sizeKey].url)\n    }\n    \n    // Fallback to original image\n    return this.getFileUrl(media.url)\n  }\n\n  /**\n   * Validate file before upload\n   */\n  validateFile(file: File, uploadType: UploadType): { valid: boolean; message?: string } {\n    const validations: Record<UploadType, { maxSize: number; allowedTypes: string[] }> = {\n      avatar: {\n        maxSize: 5 * 1024 * 1024, // 5MB\n        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']\n      },\n      course_thumbnail: {\n        maxSize: 10 * 1024 * 1024, // 10MB\n        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']\n      },\n      institute_logo: {\n        maxSize: 5 * 1024 * 1024, // 5MB\n        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']\n      },\n      platform_logo: {\n        maxSize: 5 * 1024 * 1024, // 5MB\n        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']\n      },\n      platform_favicon: {\n        maxSize: 2 * 1024 * 1024, // 2MB\n        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/x-icon', 'image/vnd.microsoft.icon']\n      },\n      document: {\n        maxSize: 50 * 1024 * 1024, // 50MB\n        allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']\n      },\n      general: {\n        maxSize: 25 * 1024 * 1024, // 25MB\n        allowedTypes: [] // Allow all types\n      }\n    }\n\n    const config = validations[uploadType]\n    \n    // Check file size\n    if (file.size > config.maxSize) {\n      return {\n        valid: false,\n        message: `File size must be less than ${Math.round(config.maxSize / 1024 / 1024)}MB`\n      }\n    }\n\n    // Check file type (if restrictions exist)\n    if (config.allowedTypes.length > 0 && !config.allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        message: `File type ${file.type} is not allowed for ${uploadType}`\n      }\n    }\n\n    return { valid: true }\n  }\n}\n\n// Export singleton instance\nexport const fileUploadAPI = new FileUploadAPI()\n\n// Export convenience functions\nexport const uploadAvatar = (file: File) => fileUploadAPI.uploadAvatar(file)\nexport const uploadCourseThumbnail = (file: File) => fileUploadAPI.uploadCourseThumbnail(file)\nexport const uploadInstituteLogo = (file: File) => fileUploadAPI.uploadInstituteLogo(file)\nexport const uploadPlatformLogo = (file: File) => fileUploadAPI.uploadPlatformLogo(file)\nexport const uploadPlatformFavicon = (file: File) => fileUploadAPI.uploadPlatformFavicon(file)\nexport const uploadDocument = (file: File) => fileUploadAPI.uploadDocument(file)\nexport const getMyFiles = (options?: Parameters<typeof fileUploadAPI.getMyFiles>[0]) => fileUploadAPI.getMyFiles(options)\nexport const getMyAvatars = () => fileUploadAPI.getMyAvatars()\nexport const deleteFile = (fileId: string) => fileUploadAPI.deleteFile(fileId)\nexport const getFileUrl = (path: string) => fileUploadAPI.getFileUrl(path)\nexport const getAvatarUrl = (media: FileInfo, size?: Parameters<typeof fileUploadAPI.getAvatarUrl>[1]) => fileUploadAPI.getAvatarUrl(media, size)\nexport const validateFile = (file: File, uploadType: UploadType) => fileUploadAPI.validateFile(file, uploadType)\n\n// Platform branding functions\nexport const getPlatformBranding = () => fileUploadAPI.getPlatformBranding()\nexport const removePlatformLogo = () => fileUploadAPI.removePlatformLogo()\nexport const removePlatformFavicon = () => fileUploadAPI.removePlatformFavicon()\nexport const processFavicon = (file: File) => fileUploadAPI.processFavicon(file)\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;;;;;;;;;;;;;;;AAqFjB;AApFnB,MAAM,eAAe;IACnB,wCAAmC;QACjC,8BAA8B;QAC9B,IAAI,QAAQ,aAAa,OAAO,CAAC;QAEjC,yCAAyC;QACzC,IAAI,CAAC,OAAO;YACV,IAAI;gBACF,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,QAAQ,QAAQ,OAAO,SAAS;gBAClC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;QAEA,OAAO;IACT;;AAEF;AA2DA,MAAM;IACI,QAAe;IAEvB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,6DAAmC;IACpD;IAEA;;GAEC,GACD,MAAM,WAAW,IAAU,EAAE,OAAsB,EAAyB;QAC1E,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,cAAc,QAAQ,UAAU;YAEhD,IAAI,QAAQ,eAAe,EAAE;gBAC3B,SAAS,MAAM,CAAC,mBAAmB,QAAQ,eAAe;YAC5D;YAEA,IAAI,QAAQ,MAAM,EAAE;gBAClB,SAAS,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC1C;YAEA,QAAQ,GAAG,CAAC,sBAAsB;gBAChC,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,YAAY,QAAQ,UAAU;gBAC9B,iBAAiB,QAAQ,eAAe;YAC1C;YAEA,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAuB,MAAM,SAAS,IAAI;YAEhD,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,IAAU,EAAyB;QACpD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;YAC3B,YAAY;YACZ,iBAAiB;YACjB,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,IAAU,EAAyB;QAC7D,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;YAC3B,YAAY;YACZ,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,oBAAoB,IAAU,EAAyB;QAC3D,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;YAC3B,YAAY;YACZ,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,IAAU,EAAyB;QACtD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;YAC3B,YAAY;YACZ,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,IAAU,EAAyB;QAC1D,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,QAAQ,GAAG,CAAC,+BAA+B;gBACzC,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;YACjB;YAEA,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,EAAE;gBACzE,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC/E;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,qDAAqD;YACrD,OAAO;gBACL,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,OAAO,OAAO,IAAI,EAAE,QAAQ;oBAC1B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;oBACxB,UAAU,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;oBACpC,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG;oBAC1B,OAAO,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAC9B,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI;oBAC9B,WAAW;gBACb,IAAI;gBACJ,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,IAAU,EAAyB;QAC7D,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,QAAQ,GAAG,CAAC,kCAAkC;gBAC5C,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;YACjB;YAEA,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,EAAE;gBAC5E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC/E;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,QAAQ,GAAG,CAAC,sCAAsC;YAElD,qDAAqD;YACrD,OAAO;gBACL,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,OAAO,OAAO,IAAI,EAAE,QAAQ;oBAC1B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;oBACxB,UAAU,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;oBACpC,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG;oBAC1B,OAAO,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAC9B,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI;oBAC9B,WAAW;gBACb,IAAI;gBACJ,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,sBAOH;QACD,IAAI;YACF,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,+BAA+B,CAAC,EAAE;gBAC7E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC/E;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,QAAQ,GAAG,CAAC,yBAAyB;YAErC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,qBAAqE;QACzE,IAAI;YACF,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,EAAE;gBACzE,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC/E;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,wBAAwE;QAC5E,IAAI;YACF,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,EAAE;gBAC5E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC/E;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,IAAU,EAI5B;QACD,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,QAAQ,GAAG,CAAC,0BAA0B;gBACpC,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;YACjB;YAEA,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,EAAE;gBAC3E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC/E;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,UAIb,CAAC,CAAC,EAA0B;QAC9B,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,QAAQ,SAAS,EAAE;gBACrB,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YAC9C;YACA,IAAI,QAAQ,IAAI,EAAE;gBAChB,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ;YAC7C;YACA,IAAI,QAAQ,KAAK,EAAE;gBACjB,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;YAC/C;YAEA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,OAAO,QAAQ,KAAK,MAAM,OAAO,QAAQ,KAAK,IAAI;YAEpG,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAwB,MAAM,SAAS,IAAI;YAEjD,QAAQ,GAAG,CAAC,gBAAgB;YAE5B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,eAAuC;QAC3C,OAAO,IAAI,CAAC,UAAU,CAAC;YAAE,WAAW;QAAc;IACpD;IAEA;;GAEC,GACD,MAAM,WAAW,MAAc,EAAkD;QAC/E,IAAI;YACF,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,EAAE;gBACnE,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,QAAQ,GAAG,CAAC,sBAAsB;YAElC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,WAAW,IAAY,EAAU;QAC/B,IAAI,KAAK,UAAU,CAAC,SAAS;YAC3B,OAAO,KAAK,0BAA0B;;QACxC;QAEA,sEAAsE;QACtE,6DAA6D;QAC7D,MAAM,aAAa,6DAAmC;QACtD,OAAO,GAAG,aAAa,MAAM,CAAC,8CAA8C;;IAC9E;IAEA;;GAEC,GACD,aAAa,KAAe,EAAE,OAAiD,QAAQ,EAAU;QAC/F,MAAM,UAAU,CAAC,OAAO,EAAE,MAAM;QAEhC,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,QAAQ,EAAE;YACvC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,QAAQ,CAAC,GAAG;QACjD;QAEA,6BAA6B;QAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;IAClC;IAEA;;GAEC,GACD,aAAa,IAAU,EAAE,UAAsB,EAAwC;QACrF,MAAM,cAA+E;YACnF,QAAQ;gBACN,SAAS,IAAI,OAAO;gBACpB,cAAc;oBAAC;oBAAc;oBAAa;oBAAa;iBAAa;YACtE;YACA,kBAAkB;gBAChB,SAAS,KAAK,OAAO;gBACrB,cAAc;oBAAC;oBAAc;oBAAa;oBAAa;iBAAa;YACtE;YACA,gBAAgB;gBACd,SAAS,IAAI,OAAO;gBACpB,cAAc;oBAAC;oBAAc;oBAAa;oBAAa;oBAAc;iBAAgB;YACvF;YACA,eAAe;gBACb,SAAS,IAAI,OAAO;gBACpB,cAAc;oBAAC;oBAAc;oBAAa;oBAAa;oBAAc;iBAAgB;YACvF;YACA,kBAAkB;gBAChB,SAAS,IAAI,OAAO;gBACpB,cAAc;oBAAC;oBAAc;oBAAa;oBAAa;oBAAc;oBAAgB;iBAA2B;YAClH;YACA,UAAU;gBACR,SAAS,KAAK,OAAO;gBACrB,cAAc;oBAAC;oBAAmB;oBAAsB;iBAA0E;YACpI;YACA,SAAS;gBACP,SAAS,KAAK,OAAO;gBACrB,cAAc,EAAE,CAAC,kBAAkB;YACrC;QACF;QAEA,MAAM,SAAS,WAAW,CAAC,WAAW;QAEtC,kBAAkB;QAClB,IAAI,KAAK,IAAI,GAAG,OAAO,OAAO,EAAE;YAC9B,OAAO;gBACL,OAAO;gBACP,SAAS,CAAC,4BAA4B,EAAE,KAAK,KAAK,CAAC,OAAO,OAAO,GAAG,OAAO,MAAM,EAAE,CAAC;YACtF;QACF;QAEA,0CAA0C;QAC1C,IAAI,OAAO,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,YAAY,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YAC9E,OAAO;gBACL,OAAO;gBACP,SAAS,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,oBAAoB,EAAE,YAAY;YACpE;QACF;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;AACF;AAGO,MAAM,gBAAgB,IAAI;AAG1B,MAAM,eAAe,CAAC,OAAe,cAAc,YAAY,CAAC;AAChE,MAAM,wBAAwB,CAAC,OAAe,cAAc,qBAAqB,CAAC;AAClF,MAAM,sBAAsB,CAAC,OAAe,cAAc,mBAAmB,CAAC;AAC9E,MAAM,qBAAqB,CAAC,OAAe,cAAc,kBAAkB,CAAC;AAC5E,MAAM,wBAAwB,CAAC,OAAe,cAAc,qBAAqB,CAAC;AAClF,MAAM,iBAAiB,CAAC,OAAe,cAAc,cAAc,CAAC;AACpE,MAAM,aAAa,CAAC,UAA6D,cAAc,UAAU,CAAC;AAC1G,MAAM,eAAe,IAAM,cAAc,YAAY;AACrD,MAAM,aAAa,CAAC,SAAmB,cAAc,UAAU,CAAC;AAChE,MAAM,aAAa,CAAC,OAAiB,cAAc,UAAU,CAAC;AAC9D,MAAM,eAAe,CAAC,OAAiB,OAA4D,cAAc,YAAY,CAAC,OAAO;AACrI,MAAM,eAAe,CAAC,MAAY,aAA2B,cAAc,YAAY,CAAC,MAAM;AAG9F,MAAM,sBAAsB,IAAM,cAAc,mBAAmB;AACnE,MAAM,qBAAqB,IAAM,cAAc,kBAAkB;AACjE,MAAM,wBAAwB,IAAM,cAAc,qBAAqB;AACvE,MAAM,iBAAiB,CAAC,OAAe,cAAc,cAAc,CAAC", "debugId": null}}, {"offset": {"line": 4477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/modals/ProfileSettingsModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { Formik, Form, Field } from 'formik'\nimport * as Yup from 'yup'\nimport { toast } from 'sonner'\nimport { X, Upload, Trash2, User, Eye, EyeOff, Loader2 } from 'lucide-react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { useUserStore } from '@/stores/user/useUserStore'\nimport { uploadAvatar as uploadAvatarAPI, validateFile, getFileUrl } from '@/lib/api/file-upload'\n\ninterface ProfileSettingsModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nconst profileSchema = Yup.object({\n  firstName: Yup.string().required('First name is required'),\n  lastName: Yup.string().required('Last name is required'),\n  email: Yup.string().email('Invalid email').required('Email is required'),\n  phone: Yup.string(),\n  password: Yup.string().min(6, 'Password must be at least 6 characters'),\n  confirmPassword: Yup.string().oneOf([Yup.ref('password')], 'Passwords must match')\n})\n\n// Helper function to extract avatar URL from different formats\nconst getAvatarUrl = (avatar: any): string | null => {\n  if (!avatar) return null\n\n  if (typeof avatar === 'string') {\n    // If avatar is already a URL string, use it directly\n    return avatar\n  } else if (typeof avatar === 'object' && avatar && 'url' in avatar) {\n    // If avatar is a media object, extract the URL\n    const avatarUrl = avatar.sizes?.avatar_medium?.url || avatar.url\n    return getFileUrl(avatarUrl)\n  } else {\n    // If avatar is something else (like a media ID), we can't display it directly\n    console.log('🖼️ Avatar format not supported for display:', avatar)\n    return null\n  }\n}\n\nexport function ProfileSettingsModal({ isOpen, onClose }: ProfileSettingsModalProps) {\n\n  const {\n    user,\n    isLoading,\n    isUpdating,\n    updateProfile,\n    removeAvatar,\n    fetchCurrentUser\n  } = useUserStore()\n\n  const [avatar, setAvatar] = useState<string | null>(null)\n  const [isUploading, setIsUploading] = useState(false)\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  // Load user data when modal opens\n  useEffect(() => {\n    if (isOpen && !user) {\n      fetchCurrentUser()\n    }\n  }, [isOpen, user, fetchCurrentUser])\n\n  // Update avatar state when user data changes\n  useEffect(() => {\n    setAvatar(getAvatarUrl(user?.avatar))\n  }, [user?.avatar])\n\n  if (!isOpen) return null\n\n  // Show loading state while fetching user data\n  if (isLoading || !user) {\n    return (\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n        <div\n          className=\"absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300\"\n          onClick={onClose}\n        />\n        <div className=\"relative bg-white rounded-xl shadow-2xl w-full max-w-2xl transform transition-all duration-300 scale-100\">\n          <div className=\"flex items-center justify-center p-8\">\n            <div className=\"flex items-center space-x-3\">\n              <Loader2 className=\"w-6 h-6 animate-spin text-primary\" />\n              <span className=\"text-lg font-medium text-gray-700\">Loading profile...</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (!file) return\n\n    // Validate file before upload\n    const validation = validateFile(file, 'avatar')\n    if (!validation.valid) {\n      toast.error(validation.message || 'Invalid file')\n      return\n    }\n\n    setIsUploading(true)\n    try {\n      // Create a preview URL for immediate feedback\n      const previewUrl = URL.createObjectURL(file)\n      setAvatar(previewUrl)\n\n      console.log('🚀 Uploading avatar using new API...')\n\n      // Upload the file using the new common API\n      const result = await uploadAvatarAPI(file)\n\n      if (result.success && result.media) {\n        // Use the medium size avatar for display\n        const avatarUrl = result.media.sizes?.avatar_medium?.url || result.media.url\n        const fullAvatarUrl = getFileUrl(avatarUrl)\n        setAvatar(fullAvatarUrl)\n\n        // Update user store with new avatar info\n        if (result.user) {\n          // The API already updated the user's avatar field\n          console.log('✅ User avatar updated:', result.user)\n        }\n\n        toast.success('Avatar uploaded successfully')\n        console.log('📊 Avatar upload result:', {\n          mediaId: result.media.id,\n          url: result.media.url,\n          sizes: Object.keys(result.media.sizes || {})\n        })\n      } else {\n        throw new Error(result.message || 'Upload failed')\n      }\n    } catch (error) {\n      console.error('❌ Avatar upload error:', error)\n      toast.error(error instanceof Error ? error.message : 'Failed to upload avatar')\n      // Reset to previous avatar on error\n      setAvatar(getAvatarUrl(user?.avatar))\n    } finally {\n      setIsUploading(false)\n    }\n  }\n\n  const handleRemoveAvatar = async () => {\n    // Show confirmation dialog\n    const confirmed = window.confirm(\n      'Are you sure you want to remove your avatar? This action cannot be undone.'\n    )\n\n    if (!confirmed) {\n      return\n    }\n\n    try {\n      console.log('🗑️ Starting avatar removal...')\n      await removeAvatar()\n      setAvatar(null)\n      if (fileInputRef.current) {\n        fileInputRef.current.value = ''\n      }\n      console.log('✅ Avatar removal completed')\n    } catch (error) {\n      console.error('❌ Avatar removal failed:', error)\n      // Error handling is done in the store (toast notification)\n    }\n  }\n\n  const handleSubmit = async (values: any) => {\n    try {\n      const profileData = {\n        ...values,\n        // Don't send avatar URL - the avatar upload already updated the user record\n        // Only send avatar if it's null (for removal)\n        ...(avatar === null && { avatar: null })\n      }\n\n      // Remove password fields if they're empty\n      if (!values.password) {\n        delete profileData.password\n        delete profileData.confirmPassword\n      }\n\n      await updateProfile(profileData)\n      onClose()\n    } catch (error) {\n      // Error handling is done in the store\n      console.error('Profile update error:', error)\n    }\n  }\n\n  const getInitialValues = () => ({\n    firstName: user?.firstName || '',\n    lastName: user?.lastName || '',\n    email: user?.email || '',\n    phone: user?.phone || '',\n    password: '',\n    confirmPassword: ''\n  })\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n      {/* Backdrop */}\n      <div\n        className=\"absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300\"\n        onClick={onClose}\n      />\n\n      {/* Modal */}\n      <div className=\"relative bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden transform transition-all duration-300 scale-100\">\n        <div className=\"overflow-y-auto max-h-[90vh]\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-100 bg-gray-50/50\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Profile Settings</h2>\n            <p className=\"text-sm text-gray-600 mt-1\">Manage your personal information and preferences</p>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200 text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 bg-white\">\n          <Formik\n            initialValues={getInitialValues()}\n            validationSchema={profileSchema}\n            onSubmit={handleSubmit}\n          >\n            {({ errors, touched }) => (\n              <Form className=\"space-y-6\">\n                {/* Avatar Section */}\n                <Card className=\"border-0 shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50\">\n                  <CardHeader className=\"pb-4\">\n                    <CardTitle className=\"text-lg font-semibold text-gray-800 flex items-center\">\n                      <User className=\"w-5 h-5 mr-2 text-blue-600\" />\n                      Profile Picture\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"flex items-center space-x-6\">\n                      <Avatar className=\"h-20 w-20\">\n                        <AvatarImage src={avatar || undefined} />\n                        <AvatarFallback className=\"text-lg\">\n                          <User className=\"w-8 h-8\" />\n                        </AvatarFallback>\n                      </Avatar>\n                      \n                      <div className=\"flex-1\">\n                        <div className=\"flex space-x-3\">\n                          <Button\n                            type=\"button\"\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => fileInputRef.current?.click()}\n                            disabled={isUploading}\n                          >\n                            <Upload className=\"w-4 h-4 mr-2\" />\n                            {isUploading ? 'Uploading...' : 'Upload'}\n                          </Button>\n                          \n                          {avatar && (\n                            <Button\n                              type=\"button\"\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={handleRemoveAvatar}\n                              disabled={isUpdating}\n                            >\n                              {isUpdating ? (\n                                <>\n                                  <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                                  Removing...\n                                </>\n                              ) : (\n                                <>\n                                  <Trash2 className=\"w-4 h-4 mr-2\" />\n                                  Remove\n                                </>\n                              )}\n                            </Button>\n                          )}\n                        </div>\n                        <p className=\"text-sm text-gray-500 mt-2\">\n                          JPG, PNG or GIF. Max size 5MB.\n                        </p>\n                      </div>\n                      \n                      <input\n                        ref={fileInputRef}\n                        type=\"file\"\n                        accept=\"image/*\"\n                        onChange={handleAvatarUpload}\n                        className=\"hidden\"\n                      />\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* Personal Information */}\n                <Card className=\"border-0 shadow-sm\">\n                  <CardHeader className=\"pb-4\">\n                    <CardTitle className=\"text-lg font-semibold text-gray-800\">Personal Information</CardTitle>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div className=\"space-y-2\">\n                        <Label htmlFor=\"firstName\">First Name</Label>\n                        <Field\n                          as={Input}\n                          id=\"firstName\"\n                          name=\"firstName\"\n                          placeholder=\"Enter first name\"\n                        />\n                        {errors.firstName && touched.firstName && (\n                          <p className=\"text-sm text-destructive\">{String(errors.firstName)}</p>\n                        )}\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label htmlFor=\"lastName\">Last Name</Label>\n                        <Field\n                          as={Input}\n                          id=\"lastName\"\n                          name=\"lastName\"\n                          placeholder=\"Enter last name\"\n                        />\n                        {errors.lastName && touched.lastName && (\n                          <p className=\"text-sm text-destructive\">{String(errors.lastName)}</p>\n                        )}\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"email\">Email</Label>\n                      <Field\n                        as={Input}\n                        id=\"email\"\n                        name=\"email\"\n                        type=\"email\"\n                        placeholder=\"Enter email address\"\n                      />\n                      {errors.email && touched.email && (\n                        <p className=\"text-sm text-destructive\">{String(errors.email)}</p>\n                      )}\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"phone\">Phone Number</Label>\n                      <Field\n                        as={Input}\n                        id=\"phone\"\n                        name=\"phone\"\n                        placeholder=\"Enter phone number\"\n                      />\n                      {errors.phone && touched.phone && (\n                        <p className=\"text-sm text-destructive\">{String(errors.phone)}</p>\n                      )}\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* Password Section */}\n                <Card className=\"border-0 shadow-sm bg-gradient-to-r from-amber-50 to-orange-50\">\n                  <CardHeader className=\"pb-4\">\n                    <CardTitle className=\"text-lg font-semibold text-gray-800\">Change Password</CardTitle>\n                    <p className=\"text-sm text-amber-700 bg-amber-100 px-3 py-2 rounded-lg\">\n                      Leave blank to keep your current password\n                    </p>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"password\">New Password</Label>\n                      <div className=\"relative\">\n                        <Field\n                          as={Input}\n                          id=\"password\"\n                          name=\"password\"\n                          type={showPassword ? 'text' : 'password'}\n                          placeholder=\"Enter new password\"\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowPassword(!showPassword)}\n                          className=\"absolute right-3 top-1/2 transform -translate-y-1/2\"\n                        >\n                          {showPassword ? (\n                            <EyeOff className=\"w-4 h-4 text-gray-400\" />\n                          ) : (\n                            <Eye className=\"w-4 h-4 text-gray-400\" />\n                          )}\n                        </button>\n                      </div>\n                      {errors.password && touched.password && (\n                        <p className=\"text-sm text-destructive\">{String(errors.password)}</p>\n                      )}\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"confirmPassword\">Confirm Password</Label>\n                      <div className=\"relative\">\n                        <Field\n                          as={Input}\n                          id=\"confirmPassword\"\n                          name=\"confirmPassword\"\n                          type={showConfirmPassword ? 'text' : 'password'}\n                          placeholder=\"Confirm new password\"\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                          className=\"absolute right-3 top-1/2 transform -translate-y-1/2\"\n                        >\n                          {showConfirmPassword ? (\n                            <EyeOff className=\"w-4 h-4 text-gray-400\" />\n                          ) : (\n                            <Eye className=\"w-4 h-4 text-gray-400\" />\n                          )}\n                        </button>\n                      </div>\n                      {errors.confirmPassword && touched.confirmPassword && (\n                        <p className=\"text-sm text-destructive\">{String(errors.confirmPassword)}</p>\n                      )}\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* Footer */}\n                <div className=\"flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-100 bg-gray-50/30 -mx-6 px-6 py-4 rounded-b-xl\">\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    onClick={onClose}\n                    className=\"px-6 py-2 border-gray-300 hover:bg-gray-50\"\n                  >\n                    Cancel\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    disabled={isUpdating}\n                    className=\"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white shadow-sm\"\n                  >\n                    {isUpdating ? (\n                      <>\n                        <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                        Saving...\n                      </>\n                    ) : (\n                      'Save Changes'\n                    )}\n                  </Button>\n                </div>\n              </Form>\n            )}\n          </Formik>\n        </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;;AAoBA,MAAM,gBAAgB,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,EAAE;IAC/B,WAAW,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC;IACjC,UAAU,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC;IAChC,OAAO,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC,iBAAiB,QAAQ,CAAC;IACpD,OAAO,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD;IAChB,UAAU,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,IAAI,GAAG,CAAC,GAAG;IAC9B,iBAAiB,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QAAC,CAAA,GAAA,sLAAA,CAAA,MAAO,AAAD,EAAE;KAAY,EAAE;AAC7D;AAEA,+DAA+D;AAC/D,MAAM,eAAe,CAAC;IACpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,IAAI,OAAO,WAAW,UAAU;QAC9B,qDAAqD;QACrD,OAAO;IACT,OAAO,IAAI,OAAO,WAAW,YAAY,UAAU,SAAS,QAAQ;QAClE,+CAA+C;QAC/C,MAAM,YAAY,OAAO,KAAK,EAAE,eAAe,OAAO,OAAO,GAAG;QAChE,OAAO,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE;IACpB,OAAO;QACL,8EAA8E;QAC9E,QAAQ,GAAG,CAAC,gDAAgD;QAC5D,OAAO;IACT;AACF;AAEO,SAAS,qBAAqB,EAAE,MAAM,EAAE,OAAO,EAA6B;;IAEjF,MAAM,EACJ,IAAI,EACJ,SAAS,EACT,UAAU,EACV,aAAa,EACb,YAAY,EACZ,gBAAgB,EACjB,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,eAAe,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,kCAAkC;IAClC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,UAAU,CAAC,MAAM;gBACnB;YACF;QACF;yCAAG;QAAC;QAAQ;QAAM;KAAiB;IAEnC,6CAA6C;IAC7C,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;0CAAE;YACR,UAAU,aAAa,MAAM;QAC/B;yCAAG;QAAC,MAAM;KAAO;IAEjB,IAAI,CAAC,QAAQ,OAAO;IAEpB,8CAA8C;IAC9C,IAAI,aAAa,CAAC,MAAM;QACtB,qBACE,0UAAC;YAAI,WAAU;;8BACb,0UAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAEX,0UAAC;oBAAI,WAAU;8BACb,cAAA,0UAAC;wBAAI,WAAU;kCACb,cAAA,0UAAC;4BAAI,WAAU;;8CACb,0UAAC,wSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,0UAAC;oCAAK,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMhE;IAEA,MAAM,qBAAqB,OAAO;QAChC,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,8BAA8B;QAC9B,MAAM,aAAa,CAAA,GAAA,0JAAA,CAAA,eAAY,AAAD,EAAE,MAAM;QACtC,IAAI,CAAC,WAAW,KAAK,EAAE;YACrB,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,OAAO,IAAI;YAClC;QACF;QAEA,eAAe;QACf,IAAI;YACF,8CAA8C;YAC9C,MAAM,aAAa,IAAI,eAAe,CAAC;YACvC,UAAU;YAEV,QAAQ,GAAG,CAAC;YAEZ,2CAA2C;YAC3C,MAAM,SAAS,MAAM,CAAA,GAAA,0JAAA,CAAA,eAAe,AAAD,EAAE;YAErC,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBAClC,yCAAyC;gBACzC,MAAM,YAAY,OAAO,KAAK,CAAC,KAAK,EAAE,eAAe,OAAO,OAAO,KAAK,CAAC,GAAG;gBAC5E,MAAM,gBAAgB,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE;gBACjC,UAAU;gBAEV,yCAAyC;gBACzC,IAAI,OAAO,IAAI,EAAE;oBACf,kDAAkD;oBAClD,QAAQ,GAAG,CAAC,0BAA0B,OAAO,IAAI;gBACnD;gBAEA,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,QAAQ,GAAG,CAAC,4BAA4B;oBACtC,SAAS,OAAO,KAAK,CAAC,EAAE;oBACxB,KAAK,OAAO,KAAK,CAAC,GAAG;oBACrB,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC;gBAC5C;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACrD,oCAAoC;YACpC,UAAU,aAAa,MAAM;QAC/B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB;QACzB,2BAA2B;QAC3B,MAAM,YAAY,OAAO,OAAO,CAC9B;QAGF,IAAI,CAAC,WAAW;YACd;QACF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM;YACN,UAAU;YACV,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;YACA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,2DAA2D;QAC7D;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,cAAc;gBAClB,GAAG,MAAM;gBACT,4EAA4E;gBAC5E,8CAA8C;gBAC9C,GAAI,WAAW,QAAQ;oBAAE,QAAQ;gBAAK,CAAC;YACzC;YAEA,0CAA0C;YAC1C,IAAI,CAAC,OAAO,QAAQ,EAAE;gBACpB,OAAO,YAAY,QAAQ;gBAC3B,OAAO,YAAY,eAAe;YACpC;YAEA,MAAM,cAAc;YACpB;QACF,EAAE,OAAO,OAAO;YACd,sCAAsC;YACtC,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,mBAAmB,IAAM,CAAC;YAC9B,WAAW,MAAM,aAAa;YAC9B,UAAU,MAAM,YAAY;YAC5B,OAAO,MAAM,SAAS;YACtB,OAAO,MAAM,SAAS;YACtB,UAAU;YACV,iBAAiB;QACnB,CAAC;IAED,qBACE,0UAAC;QAAI,WAAU;;0BAEb,0UAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,0UAAC;gBAAI,WAAU;0BACb,cAAA,0UAAC;oBAAI,WAAU;;sCAEf,0UAAC;4BAAI,WAAU;;8CACb,0UAAC;;sDACC,0UAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,0UAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAE5C,0UAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,0UAAC,mRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,0UAAC;4BAAI,WAAU;sCACb,cAAA,0UAAC,2NAAA,CAAA,SAAM;gCACL,eAAe;gCACf,kBAAkB;gCAClB,UAAU;0CAET,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,iBACnB,0UAAC,2NAAA,CAAA,OAAI;wCAAC,WAAU;;0DAEd,0UAAC,uJAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,0UAAC,uJAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,0UAAC,uJAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,0UAAC,yRAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAA+B;;;;;;;;;;;;kEAInD,0UAAC,uJAAA,CAAA,cAAW;kEACV,cAAA,0UAAC;4DAAI,WAAU;;8EACb,0UAAC,yJAAA,CAAA,SAAM;oEAAC,WAAU;;sFAChB,0UAAC,yJAAA,CAAA,cAAW;4EAAC,KAAK,UAAU;;;;;;sFAC5B,0UAAC,yJAAA,CAAA,iBAAc;4EAAC,WAAU;sFACxB,cAAA,0UAAC,yRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;;;;;;;8EAIpB,0UAAC;oEAAI,WAAU;;sFACb,0UAAC;4EAAI,WAAU;;8FACb,0UAAC,yJAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS,IAAM,aAAa,OAAO,EAAE;oFACrC,UAAU;;sGAEV,0UAAC,6RAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;wFACjB,cAAc,iBAAiB;;;;;;;gFAGjC,wBACC,0UAAC,yJAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS;oFACT,UAAU;8FAET,2BACC;;0GACE,0UAAC,wSAAA,CAAA,UAAO;gGAAC,WAAU;;;;;;4FAA8B;;qHAInD;;0GACE,0UAAC,iSAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;;;;;;;;sFAO7C,0UAAC;4EAAE,WAAU;sFAA6B;;;;;;;;;;;;8EAK5C,0UAAC;oEACC,KAAK;oEACL,MAAK;oEACL,QAAO;oEACP,UAAU;oEACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAOlB,0UAAC,uJAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,0UAAC,uJAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,0UAAC,uJAAA,CAAA,YAAS;4DAAC,WAAU;sEAAsC;;;;;;;;;;;kEAE7D,0UAAC,uJAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,0UAAC;gEAAI,WAAU;;kFACb,0UAAC;wEAAI,WAAU;;0FACb,0UAAC,wJAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAY;;;;;;0FAC3B,0UAAC,2NAAA,CAAA,QAAK;gFACJ,IAAI,wJAAA,CAAA,QAAK;gFACT,IAAG;gFACH,MAAK;gFACL,aAAY;;;;;;4EAEb,OAAO,SAAS,IAAI,QAAQ,SAAS,kBACpC,0UAAC;gFAAE,WAAU;0FAA4B,OAAO,OAAO,SAAS;;;;;;;;;;;;kFAIpE,0UAAC;wEAAI,WAAU;;0FACb,0UAAC,wJAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAW;;;;;;0FAC1B,0UAAC,2NAAA,CAAA,QAAK;gFACJ,IAAI,wJAAA,CAAA,QAAK;gFACT,IAAG;gFACH,MAAK;gFACL,aAAY;;;;;;4EAEb,OAAO,QAAQ,IAAI,QAAQ,QAAQ,kBAClC,0UAAC;gFAAE,WAAU;0FAA4B,OAAO,OAAO,QAAQ;;;;;;;;;;;;;;;;;;0EAKrE,0UAAC;gEAAI,WAAU;;kFACb,0UAAC,wJAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAQ;;;;;;kFACvB,0UAAC,2NAAA,CAAA,QAAK;wEACJ,IAAI,wJAAA,CAAA,QAAK;wEACT,IAAG;wEACH,MAAK;wEACL,MAAK;wEACL,aAAY;;;;;;oEAEb,OAAO,KAAK,IAAI,QAAQ,KAAK,kBAC5B,0UAAC;wEAAE,WAAU;kFAA4B,OAAO,OAAO,KAAK;;;;;;;;;;;;0EAIhE,0UAAC;gEAAI,WAAU;;kFACb,0UAAC,wJAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAQ;;;;;;kFACvB,0UAAC,2NAAA,CAAA,QAAK;wEACJ,IAAI,wJAAA,CAAA,QAAK;wEACT,IAAG;wEACH,MAAK;wEACL,aAAY;;;;;;oEAEb,OAAO,KAAK,IAAI,QAAQ,KAAK,kBAC5B,0UAAC;wEAAE,WAAU;kFAA4B,OAAO,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;0DAOpE,0UAAC,uJAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,0UAAC,uJAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,0UAAC,uJAAA,CAAA,YAAS;gEAAC,WAAU;0EAAsC;;;;;;0EAC3D,0UAAC;gEAAE,WAAU;0EAA2D;;;;;;;;;;;;kEAI1E,0UAAC,uJAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,0UAAC;gEAAI,WAAU;;kFACb,0UAAC,wJAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAW;;;;;;kFAC1B,0UAAC;wEAAI,WAAU;;0FACb,0UAAC,2NAAA,CAAA,QAAK;gFACJ,IAAI,wJAAA,CAAA,QAAK;gFACT,IAAG;gFACH,MAAK;gFACL,MAAM,eAAe,SAAS;gFAC9B,aAAY;;;;;;0FAEd,0UAAC;gFACC,MAAK;gFACL,SAAS,IAAM,gBAAgB,CAAC;gFAChC,WAAU;0FAET,6BACC,0UAAC,iSAAA,CAAA,SAAM;oFAAC,WAAU;;;;;yGAElB,0UAAC,uRAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;;;;;;;;;;;;oEAIpB,OAAO,QAAQ,IAAI,QAAQ,QAAQ,kBAClC,0UAAC;wEAAE,WAAU;kFAA4B,OAAO,OAAO,QAAQ;;;;;;;;;;;;0EAInE,0UAAC;gEAAI,WAAU;;kFACb,0UAAC,wJAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAkB;;;;;;kFACjC,0UAAC;wEAAI,WAAU;;0FACb,0UAAC,2NAAA,CAAA,QAAK;gFACJ,IAAI,wJAAA,CAAA,QAAK;gFACT,IAAG;gFACH,MAAK;gFACL,MAAM,sBAAsB,SAAS;gFACrC,aAAY;;;;;;0FAEd,0UAAC;gFACC,MAAK;gFACL,SAAS,IAAM,uBAAuB,CAAC;gFACvC,WAAU;0FAET,oCACC,0UAAC,iSAAA,CAAA,SAAM;oFAAC,WAAU;;;;;yGAElB,0UAAC,uRAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;;;;;;;;;;;;oEAIpB,OAAO,eAAe,IAAI,QAAQ,eAAe,kBAChD,0UAAC;wEAAE,WAAU;kFAA4B,OAAO,OAAO,eAAe;;;;;;;;;;;;;;;;;;;;;;;;0DAO9E,0UAAC;gDAAI,WAAU;;kEACb,0UAAC,yJAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS;wDACT,WAAU;kEACX;;;;;;kEAGD,0UAAC,yJAAA,CAAA,SAAM;wDACL,MAAK;wDACL,UAAU;wDACV,WAAU;kEAET,2BACC;;8EACE,0UAAC,wSAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAA8B;;2EAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYtB;GAvagB;;QASV,4JAAA,CAAA,eAAY;;;KATF", "debugId": null}}, {"offset": {"line": 5416, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/hooks/useProfileModal.ts"], "sourcesContent": ["'use client'\n\nimport { create } from 'zustand'\n\ninterface ProfileModalState {\n  isOpen: boolean\n  openModal: () => void\n  closeModal: () => void\n}\n\nconst useProfileModalStore = create<ProfileModalState>((set) => ({\n  isOpen: false,\n  openModal: () => set({ isOpen: true }),\n  closeModal: () => set({ isOpen: false }),\n}))\n\nexport function useProfileModal() {\n  const { isOpen, openModal, closeModal } = useProfileModalStore()\n\n  return {\n    isOpen,\n    openModal,\n    closeModal\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAUA,MAAM,uBAAuB,CAAA,GAAA,wPAAA,CAAA,SAAM,AAAD,EAAqB,CAAC,MAAQ,CAAC;QAC/D,QAAQ;QACR,WAAW,IAAM,IAAI;gBAAE,QAAQ;YAAK;QACpC,YAAY,IAAM,IAAI;gBAAE,QAAQ;YAAM;IACxC,CAAC;AAEM,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG;IAE1C,OAAO;QACL;QACA;QACA;IACF;AACF;GARgB;;QAC4B", "debugId": null}}, {"offset": {"line": 5454, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/frontend/src/components/layout/Sidebar.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"navigationContent\": \"Sidebar-module__ZM0Xeq__navigationContent\",\n  \"sidebarContainer\": \"Sidebar-module__ZM0Xeq__sidebarContainer\",\n  \"sidebarFooter\": \"Sidebar-module__ZM0Xeq__sidebarFooter\",\n  \"sidebarHeader\": \"Sidebar-module__ZM0Xeq__sidebarHeader\",\n  \"sidebarScrollContainer\": \"Sidebar-module__ZM0Xeq__sidebarScrollContainer\",\n  \"sidebarSearch\": \"Sidebar-module__ZM0Xeq__sidebarSearch\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 5468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useSidebarStore, UserType } from '@/stores/sidebar/useSidebarStore'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport { useSidebarNavigation } from '@/hooks/useSidebarNavigation'\nimport {\n  ChevronLeft,\n  ChevronRight,\n  Search,\n  Star,\n  Clock,\n  LogOut,\n  User,\n  Settings,\n  Menu,\n  X,\n  Home,\n  Bookmark,\n  History\n} from 'lucide-react'\nimport * as Icons from 'lucide-react'\nimport { SidebarItem } from './SidebarItem'\nimport { UserProfile } from './UserProfile'\nimport { SidebarSearch } from './SidebarSearch'\nimport { isNavigationItemActive } from '@/utils/navigationUtils'\nimport { ProfileSettingsModal } from '@/components/modals/ProfileSettingsModal'\nimport { useProfileModal } from '@/hooks/useProfileModal'\nimport styles from './Sidebar.module.css'\n\ninterface SidebarProps {\n  userType: UserType\n}\n\nexport function Sidebar({ userType }: SidebarProps) {\n  const pathname = usePathname()\n  const {\n    isCollapsed,\n    isMobileOpen,\n    favoriteItems,\n    recentItems,\n    toggleSidebar,\n    setActiveItem,\n    setMobileSidebarOpen\n  } = useSidebarStore()\n\n  const { user, logout } = useAuthStore()\n  const { navigationItems, navigationStats } = useSidebarNavigation()\n  const [showSearch, setShowSearch] = useState(false)\n  const { isOpen, openModal, closeModal } = useProfileModal()\n\n  // Safeguard: Ensure navigationItems is always an array\n  const safeNavigationItems = Array.isArray(navigationItems) ? navigationItems : []\n\n  // Show loading state if navigation items are not loaded yet\n  const isNavigationLoading = safeNavigationItems.length === 0\n\n  // Get favorite navigation items\n  const favoriteNavItems = safeNavigationItems.filter(item =>\n    item && favoriteItems.includes(item.id)\n  )\n\n  // Get recent navigation items\n  const recentNavItems = safeNavigationItems.filter(item =>\n    item && recentItems.includes(item.id)\n  ).slice(0, 5)\n\n  const handleItemClick = (itemId: string) => {\n    // Find the clicked item\n    const clickedItem = safeNavigationItems.find(item => item.id === itemId)\n\n    // Handle modal items\n    if (clickedItem?.isModal) {\n      if (itemId === 'profile') {\n        openModal()\n      }\n      // Close mobile sidebar when modal item is clicked\n      if (isMobileOpen) {\n        setMobileSidebarOpen(false)\n      }\n      return\n    }\n\n    setActiveItem(itemId)\n    // Close mobile sidebar when item is clicked\n    if (isMobileOpen) {\n      setMobileSidebarOpen(false)\n    }\n  }\n\n  const handleLogout = async () => {\n    try {\n      await logout()\n    } catch (error) {\n      console.error('Logout error:', error)\n    }\n  }\n\n  return (\n    <>\n      {/* Desktop Sidebar */}\n      <div\n        className={`${styles.sidebarContainer} sidebar-container ${\n          isCollapsed ? 'w-16' : 'w-64'\n        } hidden lg:block`}\n      >\n        {/* Sidebar Header */}\n        <div className={`${styles.sidebarHeader} sidebar-fixed-section flex items-center justify-between`}>\n          {!isCollapsed && (\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">LMS</span>\n              </div>\n              <span className=\"font-semibold text-gray-900\">Groups Exam</span>\n            </div>\n          )}\n\n          <button\n            onClick={toggleSidebar}\n            className=\"p-1.5 rounded-lg hover:bg-gray-100 transition-colors\"\n            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}\n          >\n            {isCollapsed ? (\n              <ChevronRight className=\"w-4 h-4 text-gray-600\" />\n            ) : (\n              <ChevronLeft className=\"w-4 h-4 text-gray-600\" />\n            )}\n          </button>\n        </div>\n\n        {/* Search */}\n        {!isCollapsed && (\n          <div className={`${styles.sidebarSearch} sidebar-fixed-section`}>\n            <SidebarSearch />\n          </div>\n        )}\n\n        {/* Navigation - Scrollable Area */}\n        <div className={`${styles.sidebarScrollContainer} sidebar-scroll-area`}>\n          <div className={styles.navigationContent}>\n            {/* Main Navigation */}\n            <nav className=\"px-2 space-y-1\">\n              {safeNavigationItems.map((item) => (\n                <SidebarItem\n                  key={item.id}\n                  item={item}\n                  isActive={isNavigationItemActive(item, pathname)}\n                  isCollapsed={isCollapsed}\n                  onClick={() => handleItemClick(item.id)}\n                />\n              ))}\n            </nav>\n\n            {/* Favorites Section */}\n            {!isCollapsed && favoriteNavItems.length > 0 && (\n              <div className=\"mt-6 px-2\">\n                <div className=\"flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  <Star className=\"w-3 h-3 mr-2\" />\n                  Favorites\n                </div>\n                <div className=\"space-y-1\">\n                  {favoriteNavItems.map((item) => (\n                    <SidebarItem\n                      key={`fav-${item.id}`}\n                      item={item}\n                      isActive={pathname === item.href}\n                      isCollapsed={false}\n                      onClick={() => handleItemClick(item.id)}\n                      variant=\"compact\"\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Recent Section */}\n            {!isCollapsed && recentNavItems.length > 0 && (\n              <div className=\"mt-6 px-2\">\n                <div className=\"flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  <Clock className=\"w-3 h-3 mr-2\" />\n                  Recent\n                </div>\n                <div className=\"space-y-1\">\n                  {recentNavItems.map((item) => (\n                    <SidebarItem\n                      key={`recent-${item.id}`}\n                      item={item}\n                      isActive={pathname === item.href}\n                      isCollapsed={false}\n                      onClick={() => handleItemClick(item.id)}\n                      variant=\"compact\"\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Bottom Padding for Better Scrolling */}\n            <div className=\"h-4\"></div>\n          </div>\n        </div>\n\n        {/* User Profile Section */}\n        <div className={`${styles.sidebarFooter} sidebar-fixed-section`}>\n          <UserProfile\n            user={user}\n            isCollapsed={isCollapsed}\n            onLogout={handleLogout}\n          />\n        </div>\n      </div>\n\n      {/* Mobile Sidebar */}\n      <div\n        className={`${styles.sidebarContainer} w-64 transform transition-transform duration-300 ease-in-out lg:hidden ${\n          isMobileOpen ? 'translate-x-0' : '-translate-x-full'\n        }`}\n        style={{ zIndex: 50 }}\n      >\n        {/* Mobile Sidebar Header */}\n        <div className={`${styles.sidebarHeader} flex items-center justify-between`}>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">LMS</span>\n            </div>\n            <span className=\"font-semibold text-gray-900\">Groups Exam</span>\n          </div>\n\n          <button\n            onClick={() => setMobileSidebarOpen(false)}\n            className=\"p-1.5 rounded-lg hover:bg-gray-100 transition-colors\"\n          >\n            <ChevronLeft className=\"w-4 h-4 text-gray-600\" />\n          </button>\n        </div>\n\n        {/* Mobile Search */}\n        <div className={styles.sidebarSearch}>\n          <SidebarSearch />\n        </div>\n\n        {/* Mobile Navigation - Scrollable Area */}\n        <div className={styles.sidebarScrollContainer}>\n          <div className={styles.navigationContent}>\n            <nav className=\"px-2 space-y-1\">\n              {safeNavigationItems.map((item) => (\n                <SidebarItem\n                  key={item.id}\n                  item={item}\n                  isActive={isNavigationItemActive(item, pathname)}\n                  isCollapsed={false}\n                  onClick={() => handleItemClick(item.id)}\n                />\n              ))}\n            </nav>\n\n            {/* Mobile Favorites */}\n            {favoriteNavItems.length > 0 && (\n              <div className=\"mt-6 px-2\">\n                <div className=\"flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  <Star className=\"w-3 h-3 mr-2\" />\n                  Favorites\n                </div>\n                <div className=\"space-y-1\">\n                  {favoriteNavItems.map((item) => (\n                    <SidebarItem\n                      key={`mobile-fav-${item.id}`}\n                      item={item}\n                      isActive={pathname === item.href}\n                      isCollapsed={false}\n                      onClick={() => handleItemClick(item.id)}\n                      variant=\"compact\"\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Bottom Padding for Better Mobile Scrolling */}\n            <div className=\"h-4\"></div>\n          </div>\n        </div>\n\n        {/* Mobile User Profile */}\n        <div className={styles.sidebarFooter}>\n          <UserProfile\n            user={user}\n            isCollapsed={false}\n            onLogout={handleLogout}\n          />\n        </div>\n      </div>\n\n      {/* Profile Settings Modal */}\n      <ProfileSettingsModal\n        isOpen={isOpen}\n        onClose={closeModal}\n      />\n    </>\n  )\n}\n\nexport default Sidebar\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA9BA;;;;;;;;;;;;;;AAoCO,SAAS,QAAQ,EAAE,QAAQ,EAAgB;;IAChD,MAAM,WAAW,CAAA,GAAA,kRAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EACJ,WAAW,EACX,YAAY,EACZ,aAAa,EACb,WAAW,EACX,aAAa,EACb,aAAa,EACb,oBAAoB,EACrB,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD;IACpC,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,uBAAoB,AAAD;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,kBAAe,AAAD;IAExD,uDAAuD;IACvD,MAAM,sBAAsB,MAAM,OAAO,CAAC,mBAAmB,kBAAkB,EAAE;IAEjF,4DAA4D;IAC5D,MAAM,sBAAsB,oBAAoB,MAAM,KAAK;IAE3D,gCAAgC;IAChC,MAAM,mBAAmB,oBAAoB,MAAM,CAAC,CAAA,OAClD,QAAQ,cAAc,QAAQ,CAAC,KAAK,EAAE;IAGxC,8BAA8B;IAC9B,MAAM,iBAAiB,oBAAoB,MAAM,CAAC,CAAA,OAChD,QAAQ,YAAY,QAAQ,CAAC,KAAK,EAAE,GACpC,KAAK,CAAC,GAAG;IAEX,MAAM,kBAAkB,CAAC;QACvB,wBAAwB;QACxB,MAAM,cAAc,oBAAoB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAEjE,qBAAqB;QACrB,IAAI,aAAa,SAAS;YACxB,IAAI,WAAW,WAAW;gBACxB;YACF;YACA,kDAAkD;YAClD,IAAI,cAAc;gBAChB,qBAAqB;YACvB;YACA;QACF;QAEA,cAAc;QACd,4CAA4C;QAC5C,IAAI,cAAc;YAChB,qBAAqB;QACvB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,qBACE;;0BAEE,0UAAC;gBACC,WAAW,GAAG,yKAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,mBAAmB,EACvD,cAAc,SAAS,OACxB,gBAAgB,CAAC;;kCAGlB,0UAAC;wBAAI,WAAW,GAAG,yKAAA,CAAA,UAAM,CAAC,aAAa,CAAC,wDAAwD,CAAC;;4BAC9F,CAAC,6BACA,0UAAC;gCAAI,WAAU;;kDACb,0UAAC;wCAAI,WAAU;kDACb,cAAA,0UAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,0UAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;0CAIlD,0UAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAO,cAAc,mBAAmB;0CAEvC,4BACC,0UAAC,6SAAA,CAAA,eAAY;oCAAC,WAAU;;;;;yDAExB,0UAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAM5B,CAAC,6BACA,0UAAC;wBAAI,WAAW,GAAG,yKAAA,CAAA,UAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC;kCAC7D,cAAA,0UAAC,oKAAA,CAAA,gBAAa;;;;;;;;;;kCAKlB,0UAAC;wBAAI,WAAW,GAAG,yKAAA,CAAA,UAAM,CAAC,sBAAsB,CAAC,oBAAoB,CAAC;kCACpE,cAAA,0UAAC;4BAAI,WAAW,yKAAA,CAAA,UAAM,CAAC,iBAAiB;;8CAEtC,0UAAC;oCAAI,WAAU;8CACZ,oBAAoB,GAAG,CAAC,CAAC,qBACxB,0UAAC,kKAAA,CAAA,cAAW;4CAEV,MAAM;4CACN,UAAU,CAAA,GAAA,sJAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;4CACvC,aAAa;4CACb,SAAS,IAAM,gBAAgB,KAAK,EAAE;2CAJjC,KAAK,EAAE;;;;;;;;;;gCAUjB,CAAC,eAAe,iBAAiB,MAAM,GAAG,mBACzC,0UAAC;oCAAI,WAAU;;sDACb,0UAAC;4CAAI,WAAU;;8DACb,0UAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,0UAAC;4CAAI,WAAU;sDACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,0UAAC,kKAAA,CAAA,cAAW;oDAEV,MAAM;oDACN,UAAU,aAAa,KAAK,IAAI;oDAChC,aAAa;oDACb,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,SAAQ;mDALH,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;;;;;;;gCAa9B,CAAC,eAAe,eAAe,MAAM,GAAG,mBACvC,0UAAC;oCAAI,WAAU;;sDACb,0UAAC;4CAAI,WAAU;;8DACb,0UAAC,2RAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,0UAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,0UAAC,kKAAA,CAAA,cAAW;oDAEV,MAAM;oDACN,UAAU,aAAa,KAAK,IAAI;oDAChC,aAAa;oDACb,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,SAAQ;mDALH,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;;;;;;;8CAalC,0UAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,0UAAC;wBAAI,WAAW,GAAG,yKAAA,CAAA,UAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC;kCAC7D,cAAA,0UAAC,kKAAA,CAAA,cAAW;4BACV,MAAM;4BACN,aAAa;4BACb,UAAU;;;;;;;;;;;;;;;;;0BAMhB,0UAAC;gBACC,WAAW,GAAG,yKAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,wEAAwE,EAC5G,eAAe,kBAAkB,qBACjC;gBACF,OAAO;oBAAE,QAAQ;gBAAG;;kCAGpB,0UAAC;wBAAI,WAAW,GAAG,yKAAA,CAAA,UAAM,CAAC,aAAa,CAAC,kCAAkC,CAAC;;0CACzE,0UAAC;gCAAI,WAAU;;kDACb,0UAAC;wCAAI,WAAU;kDACb,cAAA,0UAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,0UAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;0CAGhD,0UAAC;gCACC,SAAS,IAAM,qBAAqB;gCACpC,WAAU;0CAEV,cAAA,0UAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK3B,0UAAC;wBAAI,WAAW,yKAAA,CAAA,UAAM,CAAC,aAAa;kCAClC,cAAA,0UAAC,oKAAA,CAAA,gBAAa;;;;;;;;;;kCAIhB,0UAAC;wBAAI,WAAW,yKAAA,CAAA,UAAM,CAAC,sBAAsB;kCAC3C,cAAA,0UAAC;4BAAI,WAAW,yKAAA,CAAA,UAAM,CAAC,iBAAiB;;8CACtC,0UAAC;oCAAI,WAAU;8CACZ,oBAAoB,GAAG,CAAC,CAAC,qBACxB,0UAAC,kKAAA,CAAA,cAAW;4CAEV,MAAM;4CACN,UAAU,CAAA,GAAA,sJAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;4CACvC,aAAa;4CACb,SAAS,IAAM,gBAAgB,KAAK,EAAE;2CAJjC,KAAK,EAAE;;;;;;;;;;gCAUjB,iBAAiB,MAAM,GAAG,mBACzB,0UAAC;oCAAI,WAAU;;sDACb,0UAAC;4CAAI,WAAU;;8DACb,0UAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,0UAAC;4CAAI,WAAU;sDACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,0UAAC,kKAAA,CAAA,cAAW;oDAEV,MAAM;oDACN,UAAU,aAAa,KAAK,IAAI;oDAChC,aAAa;oDACb,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,SAAQ;mDALH,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;;;;;;;8CAatC,0UAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,0UAAC;wBAAI,WAAW,yKAAA,CAAA,UAAM,CAAC,aAAa;kCAClC,cAAA,0UAAC,kKAAA,CAAA,cAAW;4BACV,MAAM;4BACN,aAAa;4BACb,UAAU;;;;;;;;;;;;;;;;;0BAMhB,0UAAC,2KAAA,CAAA,uBAAoB;gBACnB,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;GA1QgB;;QACG,kRAAA,CAAA,cAAW;QASxB,kKAAA,CAAA,kBAAe;QAEM,4JAAA,CAAA,eAAY;QACQ,2JAAA,CAAA,uBAAoB;QAEvB,sJAAA,CAAA,kBAAe;;;KAf3C;uCA4QD", "debugId": null}}, {"offset": {"line": 5991, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/config/navigation/superAdminNavigation.ts"], "sourcesContent": ["import { NavigationItem } from '@/stores/sidebar/useSidebarStore'\r\n\r\nexport const superAdminNavigationConfig: NavigationItem[] = [\r\n  {\r\n    id: 'dashboard',\r\n    label: 'Dashboard',\r\n    icon: 'LayoutDashboard',\r\n    href: '/super-admin',\r\n    description: 'Overview and analytics'\r\n  },\r\n  {\r\n    id: 'institutes',\r\n    label: 'Institute Management',\r\n    icon: 'Building2',\r\n    href: '/super-admin/institutes',\r\n    description: 'Manage institutes and verification'\r\n  },\r\n  {\r\n    id: 'users',\r\n    label: 'User Management',\r\n    icon: 'Users',\r\n    href: '/super-admin/users',\r\n    description: 'Manage all platform users'\r\n  },\r\n  {\r\n    id: 'themes',\r\n    label: 'Theme Management',\r\n    icon: 'Palette',\r\n    href: '/super-admin/themes',\r\n    description: 'Platform and institute themes'\r\n  },\r\n  {\r\n    id: 'gateway-management',\r\n    label: 'Gateway Management',\r\n    icon: 'CreditCard',\r\n    href: '/super-admin/gateway-management',\r\n    description: 'Manage payment gateway providers and configurations'\r\n  },\r\n  {\r\n    id: 'platform-blog',\r\n    label: 'Platform Blog',\r\n    icon: 'FileText',\r\n    href: '/super-admin/platform-blog',\r\n    description: 'Manage platform-wide blog posts and announcements',\r\n    children: [\r\n      {\r\n        id: 'platform-blog-posts',\r\n        label: 'All Posts',\r\n        icon: 'FileText',\r\n        href: '/super-admin/platform-blog',\r\n        description: 'View and manage all platform blog posts'\r\n      },\r\n      {\r\n        id: 'platform-blog-create',\r\n        label: 'Create Post',\r\n        icon: 'Plus',\r\n        href: '/super-admin/platform-blog/create',\r\n        description: 'Create a new platform blog post'\r\n      },\r\n      {\r\n        id: 'platform-blog-categories',\r\n        label: 'Categories',\r\n        icon: 'Tag',\r\n        href: '/super-admin/platform-blog/categories',\r\n        description: 'Manage blog post categories'\r\n      },\r\n      {\r\n        id: 'platform-blog-analytics',\r\n        label: 'Analytics',\r\n        icon: 'BarChart3',\r\n        href: '/super-admin/platform-blog/analytics',\r\n        description: 'View blog performance analytics'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 'analytics',\r\n    label: 'Analytics',\r\n    icon: 'BarChart3',\r\n    href: '/super-admin/analytics',\r\n    description: 'Platform analytics and insights'\r\n  },\r\n  {\r\n    id: 'role-permissions',\r\n    label: 'Roles & Permissions',\r\n    icon: 'Shield',\r\n    href: '/super-admin/role-permissions',\r\n    description: 'Manage user roles and permissions'\r\n  },\r\n  {\r\n    id: 'settings',\r\n    label: 'Settings',\r\n    icon: 'Settings',\r\n    href: '/super-admin/settings',\r\n    description: 'Platform configuration and settings',\r\n    children: [\r\n      {\r\n        id: 'settings-platform',\r\n        label: 'Platform Settings',\r\n        icon: 'Settings',\r\n        href: '/super-admin/settings/platform',\r\n        description: 'General platform configuration'\r\n      },\r\n      {\r\n        id: 'settings-domains',\r\n        label: 'Domain Management',\r\n        icon: 'Globe',\r\n        href: '/super-admin/settings/domains',\r\n        description: 'Manage custom domain requests and DNS'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 'profile',\r\n    label: 'Profile',\r\n    icon: 'User',\r\n    href: '#',\r\n    description: 'Manage your profile settings',\r\n    isModal: true\r\n  }\r\n]"], "names": [], "mappings": ";;;AAEO,MAAM,6BAA+C;IAC1D;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBA<PERSON>,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;IACX;CACD", "debugId": null}}, {"offset": {"line": 6122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/config/navigation/instituteAdminNavigation.ts"], "sourcesContent": ["import { NavigationItem } from '@/stores/sidebar/useSidebarStore'\n\nexport const instituteAdminNavigationConfig: NavigationItem[] = [\n  {\n    id: 'dashboard',\n    label: 'Dashboard',\n    icon: 'LayoutDashboard',\n    href: '/admin',\n    description: 'Institute overview and analytics',\n    badge: 0\n  },\n\n  {\n    id: 'students',\n    label: 'Student Management',\n    icon: 'GraduationCap',\n    href: '/admin/students',\n    description: 'Manage student accounts and enrollment',\n    badge: 0,\n    children: [\n      {\n        id: 'students-list',\n        label: 'All Students',\n        icon: 'Users',\n        href: '/institute-admin/students',\n        description: 'View all students'\n      },\n      {\n        id: 'students-enrollment',\n        label: 'Enrollments',\n        icon: 'UserPlus',\n        href: '/institute-admin/students/enrollment',\n        description: 'Manage course enrollments'\n      },\n      {\n        id: 'students-progress',\n        label: 'Progress Tracking',\n        icon: 'TrendingUp',\n        href: '/institute-admin/students/progress',\n        description: 'Track student progress'\n      },\n      {\n        id: 'students-certificates',\n        label: 'Certificates',\n        icon: 'Award',\n        href: '/institute-admin/students/certificates',\n        description: 'Manage certificates'\n      }\n    ]\n  },\n  {\n    id: 'staff',\n    label: 'Staff Management',\n    icon: 'Users',\n    href: '/admin/staff',\n    description: 'Manage institute staff members',\n    badge: 0,\n    permissions: ['institute_admin', 'branch_manager'],\n\n  },\n\n  {\n    id: 'courses',\n    label: 'Course Management',\n    icon: 'BookOpen',\n    href: '/admin/courses',\n    description: 'Create and manage courses',\n    badge: 0,\n    children: [\n      {\n        id: 'courses-list',\n        label: 'All Courses',\n        icon: 'BookOpen',\n        href: '/admin/courses',\n        description: 'View and manage all courses'\n      },\n      {\n        id: 'course-builder',\n        label: 'Course Builder',\n        icon: 'Edit3',\n        href: '/admin/course-builder',\n        description: 'Build course content'\n      },\n      {\n        id: 'courses-analytics',\n        label: 'Course Analytics',\n        icon: 'BarChart3',\n        href: '/admin/courses/analytics',\n        description: 'Course performance metrics'\n      },\n      {\n        id: 'courses-categories',\n        label: 'Categories',\n        icon: 'FolderOpen',\n        href: '/admin/courses/categories',\n        description: 'Manage course categories'\n      }\n    ]\n  },\n\n  {\n    id: 'live-classes',\n    label: 'Live Classes',\n    icon: 'Video',\n    href: '/institute-admin/live-classes',\n    description: 'Manage live classes and sessions',\n    badge: 2,\n    children: [\n      {\n        id: 'live-classes-schedule',\n        label: 'Class Schedule',\n        icon: 'Calendar',\n        href: '/institute-admin/live-classes/schedule',\n        description: 'Manage class schedules'\n      },\n      {\n        id: 'live-classes-rooms',\n        label: 'Virtual Rooms',\n        icon: 'Monitor',\n        href: '/institute-admin/live-classes/rooms',\n        description: 'Manage virtual classrooms'\n      },\n      {\n        id: 'live-classes-recordings',\n        label: 'Recordings',\n        icon: 'PlayCircle',\n        href: '/institute-admin/live-classes/recordings',\n        description: 'Class recordings'\n      }\n    ]\n  },\n  {\n    id: 'exams',\n    label: 'Exams & Assessments',\n    icon: 'FileText',\n    href: '/institute-admin/exams',\n    description: 'Manage exams and assessments',\n    badge: 0,\n    children: [\n      {\n        id: 'exams-list',\n        label: 'All Exams',\n        icon: 'List',\n        href: '/institute-admin/exams',\n        description: 'View all exams'\n      },\n      {\n        id: 'exams-create',\n        label: 'Create Exam',\n        icon: 'Plus',\n        href: '/institute-admin/exams/create',\n        description: 'Create new exam'\n      },\n      {\n        id: 'exams-results',\n        label: 'Results',\n        icon: 'BarChart3',\n        href: '/institute-admin/exams/results',\n        description: 'Exam results and analytics'\n      },\n      {\n        id: 'exams-question-bank',\n        label: 'Question Bank',\n        icon: 'HelpCircle',\n        href: '/institute-admin/exams/questions',\n        description: 'Manage question bank'\n      }\n    ]\n  },\n  {\n    id: 'billing',\n    label: 'Billing & Payments',\n    icon: 'CreditCard',\n    href: '/institute-admin/billing',\n    description: 'Manage billing and payments',\n    badge: 0,\n\n    children: [\n      {\n        id: 'billing-overview',\n        label: 'Billing Overview',\n        icon: 'DollarSign',\n        href: '/institute-admin/billing',\n        description: 'Revenue and billing overview'\n      },\n      {\n        id: 'billing-transactions',\n        label: 'Transactions',\n        icon: 'Receipt',\n        href: '/institute-admin/billing/transactions',\n        description: 'Payment transactions'\n      },\n      {\n        id: 'billing-subscriptions',\n        label: 'Subscriptions',\n        icon: 'CreditCard',\n        href: '/institute-admin/billing/subscriptions',\n        description: 'Student subscriptions'\n      },\n      {\n        id: 'billing-reports',\n        label: 'Financial Reports',\n        icon: 'FileText',\n        href: '/institute-admin/billing/reports',\n        description: 'Financial reports'\n      }\n    ]\n  },\n  {\n    id: 'marketplace',\n    label: 'Marketplace',\n    icon: 'ShoppingBag',\n    href: '/institute-admin/marketplace',\n    description: 'Manage course marketplace',\n    badge: 0,\n\n    children: [\n      {\n        id: 'marketplace-courses',\n        label: 'Published Courses',\n        icon: 'BookOpen',\n        href: '/institute-admin/marketplace/courses',\n        description: 'Courses in marketplace'\n      },\n      {\n        id: 'marketplace-orders',\n        label: 'Orders',\n        icon: 'ShoppingCart',\n        href: '/institute-admin/marketplace/orders',\n        description: 'Course orders'\n      },\n      {\n        id: 'marketplace-reviews',\n        label: 'Reviews & Ratings',\n        icon: 'Star',\n        href: '/institute-admin/marketplace/reviews',\n        description: 'Course reviews'\n      },\n      {\n        id: 'marketplace-analytics',\n        label: 'Sales Analytics',\n        icon: 'TrendingUp',\n        href: '/institute-admin/marketplace/analytics',\n        description: 'Sales performance'\n      }\n    ]\n  },\n  {\n    id: 'blog',\n    label: 'Blog Management',\n    icon: 'PenTool',\n    href: '/admin/blog',\n    description: 'Manage institute blog and content',\n    badge: 0,\n\n    permissions: ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'],\n    children: [\n      {\n        id: 'blog-dashboard',\n        label: 'Blog Dashboard',\n        icon: 'BarChart3',\n        href: '/admin/blog',\n        description: 'Blog analytics and overview'\n      },\n      {\n        id: 'blog-posts',\n        label: 'All Posts',\n        icon: 'FileText',\n        href: '/admin/blog/posts',\n        description: 'Manage all blog posts'\n      },\n      {\n        id: 'blog-create',\n        label: 'Create Post',\n        icon: 'Plus',\n        href: '/admin/blog/posts/new',\n        description: 'Write a new blog post'\n      },\n      {\n        id: 'blog-categories',\n        label: 'Categories',\n        icon: 'FolderOpen',\n        href: '/admin/blog/categories',\n        description: 'Organize content categories'\n      },\n      {\n        id: 'blog-drafts',\n        label: 'Drafts',\n        icon: 'Archive',\n        href: '/admin/blog/drafts',\n        description: 'Draft posts'\n      },\n      {\n        id: 'blog-scheduled',\n        label: 'Scheduled',\n        icon: 'Calendar',\n        href: '/admin/blog/scheduled',\n        description: 'Scheduled posts'\n      },\n      {\n        id: 'blog-analytics',\n        label: 'Blog Analytics',\n        icon: 'TrendingUp',\n        href: '/admin/blog/analytics',\n        description: 'Detailed blog analytics'\n      },\n      {\n        id: 'blog-settings',\n        label: 'Blog Settings',\n        icon: 'Settings',\n        href: '/admin/blog/settings',\n        description: 'Blog configuration'\n      }\n    ]\n  },\n  {\n    id: 'analytics',\n    label: 'Analytics & Reports',\n    icon: 'BarChart3',\n    href: '/institute-admin/analytics',\n    description: 'Institute analytics and insights',\n    badge: 0,\n\n    children: [\n      {\n        id: 'analytics-overview',\n        label: 'Overview',\n        icon: 'BarChart3',\n        href: '/institute-admin/analytics',\n        description: 'Institute overview metrics'\n      },\n      {\n        id: 'analytics-students',\n        label: 'Student Analytics',\n        icon: 'Users',\n        href: '/institute-admin/analytics/students',\n        description: 'Student engagement metrics'\n      },\n      {\n        id: 'analytics-courses',\n        label: 'Course Analytics',\n        icon: 'BookOpen',\n        href: '/institute-admin/analytics/courses',\n        description: 'Course performance metrics'\n      },\n      {\n        id: 'analytics-revenue',\n        label: 'Revenue Analytics',\n        icon: 'DollarSign',\n        href: '/institute-admin/analytics/revenue',\n        description: 'Revenue and financial metrics'\n      }\n    ]\n  },\n  {\n    id: 'settings',\n    label: 'Institute Settings',\n    icon: 'Settings',\n    href: '/admin/settings',\n    description: 'Institute configuration and settings',\n    badge: 0,\n\n    permissions: ['institute_admin', 'branch_manager', 'institute_staff'],\n    children: [\n      {\n        id: 'settings-general',\n        label: 'General Settings',\n        icon: 'Settings',\n        href: '/institute-admin/settings/general',\n        description: 'Basic institute settings'\n      },\n      {\n        id: 'settings-domain',\n        label: 'Domain Settings',\n        icon: 'Globe',\n        href: '/institute-admin/settings/domain',\n        description: 'Custom domain configuration'\n      },\n      {\n        id: 'settings-theme',\n        label: 'Theme & Branding',\n        icon: 'Palette',\n        href: '/institute-admin/settings/theme',\n        description: 'Customize appearance'\n      },\n      {\n        id: 'settings-payment',\n        label: 'Payment Gateways',\n        icon: 'CreditCard',\n        href: '/admin/settings/payment-gateways',\n        description: 'Configure payment gateways',\n        permissions: ['institute_admin', 'branch_manager']\n      },\n      {\n        id: 'settings-notifications',\n        label: 'Notifications',\n        icon: 'Bell',\n        href: '/institute-admin/settings/notifications',\n        description: 'Notification preferences'\n      },\n      {\n        id: 'settings-integrations',\n        label: 'Integrations',\n        icon: 'Plug',\n        href: '/institute-admin/settings/integrations',\n        description: 'Third-party integrations'\n      }\n    ]\n  }\n]\n\n// Quick access items for institute admin\nexport const instituteAdminQuickAccess = [\n  {\n    id: 'quick-live-classes',\n    label: 'Live Classes Today',\n    icon: 'Video',\n    href: '/institute-admin/live-classes/schedule',\n    count: 2\n  },\n  {\n    id: 'quick-new-enrollments',\n    label: 'New Enrollments',\n    icon: 'UserPlus',\n    href: '/institute-admin/students/enrollment',\n    count: 8\n  },\n  {\n    id: 'quick-pending-exams',\n    label: 'Pending Exams',\n    icon: 'FileText',\n    href: '/institute-admin/exams',\n    count: 3\n  },\n  {\n    id: 'quick-revenue',\n    label: 'Today\\'s Revenue',\n    icon: 'DollarSign',\n    href: '/institute-admin/billing',\n    value: '$1,250'\n  }\n]\n\n// Favorite items for institute admin\nexport const instituteAdminFavorites = [\n  'courses',\n  'students',\n  'live-classes',\n  'analytics'\n]\n\n// Recent items for institute admin (this would be dynamic)\nexport const instituteAdminRecentItems = [\n  {\n    id: 'recent-1',\n    label: 'Course Analytics',\n    href: '/institute-admin/courses/analytics',\n    timestamp: new Date().toISOString()\n  },\n  {\n    id: 'recent-2',\n    label: 'Student Progress',\n    href: '/institute-admin/students/progress',\n    timestamp: new Date(Date.now() - 1800000).toISOString()\n  },\n  {\n    id: 'recent-3',\n    label: 'Live Class Schedule',\n    href: '/institute-admin/live-classes/schedule',\n    timestamp: new Date(Date.now() - 3600000).toISOString()\n  }\n]\n\nexport default instituteAdminNavigationConfig\n"], "names": [], "mappings": ";;;;;;;AAEO,MAAM,iCAAmD;IAC9D;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IAEA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;YAAC;YAAmB;SAAiB;IAEpD;IAEA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QAEP,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QAEP,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QAEP,aAAa;YAAC;YAAmB;YAAkB;YAAW;SAAkB;QAChF,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QAEP,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QAEP,aAAa;YAAC;YAAmB;YAAkB;SAAkB;QACrE,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,aAAa;oBAAC;oBAAmB;iBAAiB;YACpD;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;CACD;AAGM,MAAM,4BAA4B;IACvC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAGM,MAAM,0BAA0B;IACrC;IACA;IACA;IACA;CACD;AAGM,MAAM,4BAA4B;IACvC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;IACvD;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;IACvD;CACD;uCAEc", "debugId": null}}, {"offset": {"line": 6609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/config/navigation/studentNavigation.ts"], "sourcesContent": ["import { NavigationItem } from '@/stores/sidebar/useSidebarStore'\n\nexport const studentNavigationConfig: NavigationItem[] = [\n  {\n    id: 'dashboard',\n    label: 'Dashboard',\n    icon: 'LayoutDashboard',\n    href: '/student',\n    description: 'Your learning overview',\n    badge: 0,\n    section: 'main'\n  },\n  {\n    id: 'my-courses',\n    label: 'My Courses',\n    icon: 'BookOpen',\n    href: '/student/courses',\n    description: 'Your enrolled courses',\n    badge: 2,\n    section: 'main',\n    children: [\n      {\n        id: 'courses-active',\n        label: 'Active Courses',\n        icon: 'Play',\n        href: '/student/courses/active',\n        description: 'Currently enrolled courses'\n      },\n      {\n        id: 'courses-completed',\n        label: 'Completed Courses',\n        icon: 'CheckCircle',\n        href: '/student/courses/completed',\n        description: 'Finished courses'\n      },\n      {\n        id: 'courses-favorites',\n        label: 'Favorites',\n        icon: 'Heart',\n        href: '/student/courses/favorites',\n        description: 'Your favorite courses'\n      },\n      {\n        id: 'courses-downloads',\n        label: 'Downloads',\n        icon: 'Download',\n        href: '/student/courses/downloads',\n        description: 'Downloaded content'\n      }\n    ]\n  },\n  {\n    id: 'marketplace',\n    label: 'Course Marketplace',\n    icon: 'ShoppingBag',\n    href: '/student/marketplace',\n    description: 'Browse and purchase courses',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'marketplace-browse',\n        label: 'Browse Courses',\n        icon: 'Search',\n        href: '/student/marketplace',\n        description: 'Explore available courses'\n      },\n      {\n        id: 'marketplace-categories',\n        label: 'Categories',\n        icon: 'Tag',\n        href: '/student/marketplace/categories',\n        description: 'Browse by category'\n      },\n      {\n        id: 'marketplace-wishlist',\n        label: 'Wishlist',\n        icon: 'Heart',\n        href: '/student/marketplace/wishlist',\n        description: 'Your course wishlist'\n      },\n      {\n        id: 'marketplace-cart',\n        label: 'Shopping Cart',\n        icon: 'ShoppingCart',\n        href: '/student/marketplace/cart',\n        description: 'Items in your cart',\n        badge: 1\n      }\n    ]\n  },\n  {\n    id: 'live-classes',\n    label: 'Live Classes',\n    icon: 'Video',\n    href: '/student/live-classes',\n    description: 'Attend live sessions',\n    badge: 1,\n    section: 'main',\n    children: [\n      {\n        id: 'live-classes-schedule',\n        label: 'Class Schedule',\n        icon: 'Calendar',\n        href: '/student/live-classes/schedule',\n        description: 'Your class schedule'\n      },\n      {\n        id: 'live-classes-upcoming',\n        label: 'Upcoming Classes',\n        icon: 'Clock',\n        href: '/student/live-classes/upcoming',\n        description: 'Classes starting soon',\n        badge: 1\n      },\n      {\n        id: 'live-classes-recordings',\n        label: 'Recorded Classes',\n        icon: 'PlayCircle',\n        href: '/student/live-classes/recordings',\n        description: 'Watch recorded sessions'\n      }\n    ]\n  },\n  {\n    id: 'assignments',\n    label: 'Assignments & Exams',\n    icon: 'FileText',\n    href: '/student/assignments',\n    description: 'Your assignments and tests',\n    badge: 3,\n    section: 'main',\n    children: [\n      {\n        id: 'assignments-pending',\n        label: 'Pending Assignments',\n        icon: 'Clock',\n        href: '/student/assignments/pending',\n        description: 'Assignments to complete',\n        badge: 2\n      },\n      {\n        id: 'assignments-submitted',\n        label: 'Submitted',\n        icon: 'CheckCircle',\n        href: '/student/assignments/submitted',\n        description: 'Completed assignments'\n      },\n      {\n        id: 'assignments-exams',\n        label: 'Exams',\n        icon: 'FileText',\n        href: '/student/assignments/exams',\n        description: 'Scheduled exams',\n        badge: 1\n      },\n      {\n        id: 'assignments-results',\n        label: 'Results',\n        icon: 'BarChart3',\n        href: '/student/assignments/results',\n        description: 'Your exam results'\n      }\n    ]\n  },\n  {\n    id: 'progress',\n    label: 'Progress & Analytics',\n    icon: 'TrendingUp',\n    href: '/student/progress',\n    description: 'Track your learning progress',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'progress-overview',\n        label: 'Learning Overview',\n        icon: 'BarChart3',\n        href: '/student/progress',\n        description: 'Your learning statistics'\n      },\n      {\n        id: 'progress-achievements',\n        label: 'Achievements',\n        icon: 'Award',\n        href: '/student/progress/achievements',\n        description: 'Badges and achievements'\n      },\n      {\n        id: 'progress-certificates',\n        label: 'Certificates',\n        icon: 'Award',\n        href: '/student/progress/certificates',\n        description: 'Your certificates'\n      },\n      {\n        id: 'progress-goals',\n        label: 'Learning Goals',\n        icon: 'Target',\n        href: '/student/progress/goals',\n        description: 'Set and track goals'\n      }\n    ]\n  },\n  {\n    id: 'discussions',\n    label: 'Discussions',\n    icon: 'MessageSquare',\n    href: '/student/discussions',\n    description: 'Course discussions and forums',\n    badge: 5,\n    section: 'main',\n    children: [\n      {\n        id: 'discussions-forums',\n        label: 'Course Forums',\n        icon: 'MessageSquare',\n        href: '/student/discussions/forums',\n        description: 'Course discussion forums'\n      },\n      {\n        id: 'discussions-qa',\n        label: 'Q&A',\n        icon: 'HelpCircle',\n        href: '/student/discussions/qa',\n        description: 'Ask questions'\n      },\n      {\n        id: 'discussions-study-groups',\n        label: 'Study Groups',\n        icon: 'Users',\n        href: '/student/discussions/study-groups',\n        description: 'Join study groups'\n      }\n    ]\n  },\n  {\n    id: 'library',\n    label: 'Digital Library',\n    icon: 'Library',\n    href: '/student/library',\n    description: 'Access learning resources',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'library-books',\n        label: 'E-Books',\n        icon: 'Book',\n        href: '/student/library/books',\n        description: 'Digital textbooks'\n      },\n      {\n        id: 'library-articles',\n        label: 'Articles',\n        icon: 'FileText',\n        href: '/student/library/articles',\n        description: 'Research articles'\n      },\n      {\n        id: 'library-videos',\n        label: 'Video Library',\n        icon: 'PlayCircle',\n        href: '/student/library/videos',\n        description: 'Educational videos'\n      },\n      {\n        id: 'library-notes',\n        label: 'My Notes',\n        icon: 'StickyNote',\n        href: '/student/library/notes',\n        description: 'Your personal notes'\n      }\n    ]\n  },\n  {\n    id: 'billing',\n    label: 'Billing & Payments',\n    icon: 'CreditCard',\n    href: '/student/billing',\n    description: 'Manage payments and subscriptions',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'billing-overview',\n        label: 'Payment History',\n        icon: 'Receipt',\n        href: '/student/billing',\n        description: 'Your payment history'\n      },\n      {\n        id: 'billing-subscriptions',\n        label: 'Subscriptions',\n        icon: 'CreditCard',\n        href: '/student/billing/subscriptions',\n        description: 'Active subscriptions'\n      },\n      {\n        id: 'billing-invoices',\n        label: 'Invoices',\n        icon: 'FileText',\n        href: '/student/billing/invoices',\n        description: 'Download invoices'\n      },\n      {\n        id: 'billing-payment-methods',\n        label: 'Payment Methods',\n        icon: 'Wallet',\n        href: '/student/billing/payment-methods',\n        description: 'Manage payment methods'\n      }\n    ]\n  },\n  {\n    id: 'settings',\n    label: 'Account Settings',\n    icon: 'Settings',\n    href: '/student/settings',\n    description: 'Manage your account',\n    badge: 0,\n    section: 'settings',\n    children: [\n      {\n        id: 'settings-profile',\n        label: 'Profile Settings',\n        icon: 'User',\n        href: '/student/settings/profile',\n        description: 'Update your profile'\n      },\n      {\n        id: 'settings-preferences',\n        label: 'Learning Preferences',\n        icon: 'Settings',\n        href: '/student/settings/preferences',\n        description: 'Customize your experience'\n      },\n      {\n        id: 'settings-notifications',\n        label: 'Notifications',\n        icon: 'Bell',\n        href: '/student/settings/notifications',\n        description: 'Notification preferences'\n      },\n      {\n        id: 'settings-privacy',\n        label: 'Privacy & Security',\n        icon: 'Shield',\n        href: '/student/settings/privacy',\n        description: 'Privacy settings'\n      },\n      {\n        id: 'settings-downloads',\n        label: 'Download Settings',\n        icon: 'Download',\n        href: '/student/settings/downloads',\n        description: 'Offline content settings'\n      }\n    ]\n  }\n]\n\n// Quick access items for students\nexport const studentQuickAccess = [\n  {\n    id: 'quick-upcoming-class',\n    label: 'Next Live Class',\n    icon: 'Video',\n    href: '/student/live-classes/upcoming',\n    time: '2:30 PM'\n  },\n  {\n    id: 'quick-pending-assignments',\n    label: 'Pending Assignments',\n    icon: 'FileText',\n    href: '/student/assignments/pending',\n    count: 2\n  },\n  {\n    id: 'quick-new-messages',\n    label: 'New Messages',\n    icon: 'MessageSquare',\n    href: '/student/discussions',\n    count: 5\n  },\n  {\n    id: 'quick-progress',\n    label: 'Course Progress',\n    icon: 'TrendingUp',\n    href: '/student/progress',\n    value: '78%'\n  }\n]\n\n// Favorite items for students\nexport const studentFavorites = [\n  'my-courses',\n  'live-classes',\n  'assignments',\n  'progress'\n]\n\n// Recent items for students (this would be dynamic)\nexport const studentRecentItems = [\n  {\n    id: 'recent-1',\n    label: 'JavaScript Fundamentals',\n    href: '/student/courses/javascript-fundamentals',\n    timestamp: new Date().toISOString()\n  },\n  {\n    id: 'recent-2',\n    label: 'Assignment: React Components',\n    href: '/student/assignments/react-components',\n    timestamp: new Date(Date.now() - 1800000).toISOString()\n  },\n  {\n    id: 'recent-3',\n    label: 'Live Class: Advanced CSS',\n    href: '/student/live-classes/advanced-css',\n    timestamp: new Date(Date.now() - 3600000).toISOString()\n  }\n]\n\nexport default studentNavigationConfig\n"], "names": [], "mappings": ";;;;;;;AAEO,MAAM,0BAA4C;IACvD;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBAC<PERSON>,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;CACD;AAGM,MAAM,qBAAqB;IAChC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAGM,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;CACD;AAGM,MAAM,qBAAqB;IAChC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;IACvD;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;IACvD;CACD;uCAEc", "debugId": null}}, {"offset": {"line": 7041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/utils/navigationConfig.ts"], "sourcesContent": ["import { NavigationItem, UserType } from '@/stores/sidebar/useSidebarStore'\nimport { superAdminNavigationConfig } from '@/config/navigation/superAdminNavigation'\nimport { instituteAdminNavigationConfig } from '@/config/navigation/instituteAdminNavigation'\nimport { studentNavigationConfig } from '@/config/navigation/studentNavigation'\n\n// Navigation configuration mapping\nexport const navigationConfigs: Record<UserType, NavigationItem[]> = {\n  super_admin: superAdminNavigationConfig,\n  institute_admin: instituteAdminNavigationConfig,\n  student: studentNavigationConfig\n}\n\n// Get navigation items for a specific user type\nexport function getNavigationForUserType(userType: UserType): NavigationItem[] {\n  return navigationConfigs[userType] || []\n}\n\n// Get navigation item by ID (recursive search)\nexport function findNavigationItem(\n  items: NavigationItem[], \n  itemId: string\n): NavigationItem | null {\n  for (const item of items) {\n    if (item.id === itemId) {\n      return item\n    }\n    if (item.children) {\n      const found = findNavigationItem(item.children, itemId)\n      if (found) return found\n    }\n  }\n  return null\n}\n\n// Get navigation item by href (recursive search)\nexport function findNavigationItemByHref(\n  items: NavigationItem[], \n  href: string\n): NavigationItem | null {\n  for (const item of items) {\n    if (item.href === href) {\n      return item\n    }\n    if (item.children) {\n      const found = findNavigationItemByHref(item.children, href)\n      if (found) return found\n    }\n  }\n  return null\n}\n\n// Get all navigation items as flat array (for search)\nexport function getFlatNavigationItems(items: NavigationItem[]): NavigationItem[] {\n  const flatItems: NavigationItem[] = []\n  \n  function flatten(items: NavigationItem[]) {\n    for (const item of items) {\n      flatItems.push(item)\n      if (item.children) {\n        flatten(item.children)\n      }\n    }\n  }\n  \n  flatten(items)\n  return flatItems\n}\n\n// Search navigation items\nexport function searchNavigationItems(\n  items: NavigationItem[], \n  query: string\n): NavigationItem[] {\n  const flatItems = getFlatNavigationItems(items)\n  const searchTerm = query.toLowerCase().trim()\n  \n  if (!searchTerm) return []\n  \n  return flatItems.filter(item => \n    item.label.toLowerCase().includes(searchTerm) ||\n    item.description?.toLowerCase().includes(searchTerm) ||\n    item.href.toLowerCase().includes(searchTerm)\n  )\n}\n\n// Get navigation breadcrumbs for a given path\nexport function getNavigationBreadcrumbs(\n  items: NavigationItem[], \n  currentPath: string\n): NavigationItem[] {\n  const breadcrumbs: NavigationItem[] = []\n  \n  function findPath(items: NavigationItem[], path: NavigationItem[]): boolean {\n    for (const item of items) {\n      const currentPath = [...path, item]\n      \n      if (item.href === currentPath || currentPath.startsWith(item.href + '/')) {\n        breadcrumbs.push(...currentPath)\n        return true\n      }\n      \n      if (item.children && findPath(item.children, currentPath)) {\n        return true\n      }\n    }\n    return false\n  }\n  \n  findPath(items, [])\n  return breadcrumbs\n}\n\n// Get navigation items by section\nexport function getNavigationBySection(\n  items: NavigationItem[], \n  section: string\n): NavigationItem[] {\n  return items.filter(item => item.section === section)\n}\n\n// Get navigation items with badges\nexport function getNavigationItemsWithBadges(items: NavigationItem[]): NavigationItem[] {\n  const flatItems = getFlatNavigationItems(items)\n  return flatItems.filter(item => item.badge && item.badge > 0)\n}\n\n// Calculate total badge count\nexport function getTotalBadgeCount(items: NavigationItem[]): number {\n  const flatItems = getFlatNavigationItems(items)\n  return flatItems.reduce((total, item) => total + (item.badge || 0), 0)\n}\n\n// Get parent navigation item\nexport function getParentNavigationItem(\n  items: NavigationItem[], \n  childId: string\n): NavigationItem | null {\n  for (const item of items) {\n    if (item.children) {\n      const found = item.children.find(child => child.id === childId)\n      if (found) return item\n      \n      const parentInChildren = getParentNavigationItem(item.children, childId)\n      if (parentInChildren) return parentInChildren\n    }\n  }\n  return null\n}\n\n// Check if navigation item is active (including children)\nexport function isNavigationItemActive(\n  item: NavigationItem, \n  currentPath: string\n): boolean {\n  if (currentPath === item.href || currentPath.startsWith(item.href + '/')) {\n    return true\n  }\n  \n  if (item.children) {\n    return item.children.some(child => isNavigationItemActive(child, currentPath))\n  }\n  \n  return false\n}\n\n// Get navigation item depth\nexport function getNavigationItemDepth(\n  items: NavigationItem[], \n  itemId: string, \n  currentDepth: number = 0\n): number {\n  for (const item of items) {\n    if (item.id === itemId) {\n      return currentDepth\n    }\n    if (item.children) {\n      const depth = getNavigationItemDepth(item.children, itemId, currentDepth + 1)\n      if (depth !== -1) return depth\n    }\n  }\n  return -1\n}\n\n// Validate navigation structure\nexport function validateNavigationStructure(items: NavigationItem[]): {\n  isValid: boolean\n  errors: string[]\n} {\n  const errors: string[] = []\n  const seenIds = new Set<string>()\n  const seenHrefs = new Set<string>()\n  \n  function validate(items: NavigationItem[], path: string = '') {\n    for (const item of items) {\n      const currentPath = path ? `${path} > ${item.label}` : item.label\n      \n      // Check for duplicate IDs\n      if (seenIds.has(item.id)) {\n        errors.push(`Duplicate ID \"${item.id}\" found at ${currentPath}`)\n      }\n      seenIds.add(item.id)\n      \n      // Check for duplicate hrefs\n      if (seenHrefs.has(item.href)) {\n        errors.push(`Duplicate href \"${item.href}\" found at ${currentPath}`)\n      }\n      seenHrefs.add(item.href)\n      \n      // Check required fields\n      if (!item.label) {\n        errors.push(`Missing label at ${currentPath}`)\n      }\n      if (!item.href) {\n        errors.push(`Missing href at ${currentPath}`)\n      }\n      if (!item.icon) {\n        errors.push(`Missing icon at ${currentPath}`)\n      }\n      \n      // Validate children\n      if (item.children) {\n        validate(item.children, currentPath)\n      }\n    }\n  }\n  \n  validate(items)\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\n// Get navigation statistics\nexport function getNavigationStatistics(items: NavigationItem[]) {\n  const flatItems = getFlatNavigationItems(items)\n  const sections = new Set(items.map(item => item.section).filter(Boolean))\n  const maxDepth = Math.max(...flatItems.map(item => \n    getNavigationItemDepth(items, item.id)\n  ))\n  const itemsWithBadges = getNavigationItemsWithBadges(items)\n  const totalBadgeCount = getTotalBadgeCount(items)\n  \n  return {\n    totalItems: flatItems.length,\n    topLevelItems: items.length,\n    sections: sections.size,\n    maxDepth: maxDepth + 1, // Convert 0-based to 1-based\n    itemsWithBadges: itemsWithBadges.length,\n    totalBadgeCount,\n    averageChildrenPerItem: items.reduce((sum, item) => \n      sum + (item.children?.length || 0), 0\n    ) / items.length\n  }\n}\n\n// Export utility functions\nexport const navigationUtils = {\n  getNavigationForUserType,\n  findNavigationItem,\n  findNavigationItemByHref,\n  getFlatNavigationItems,\n  searchNavigationItems,\n  getNavigationBreadcrumbs,\n  getNavigationBySection,\n  getNavigationItemsWithBadges,\n  getTotalBadgeCount,\n  getParentNavigationItem,\n  isNavigationItemActive,\n  getNavigationItemDepth,\n  validateNavigationStructure,\n  getNavigationStatistics\n}\n\nexport default navigationUtils\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;;;;AAGO,MAAM,oBAAwD;IACnE,aAAa,0KAAA,CAAA,6BAA0B;IACvC,iBAAiB,8KAAA,CAAA,iCAA8B;IAC/C,SAAS,uKAAA,CAAA,0BAAuB;AAClC;AAGO,SAAS,yBAAyB,QAAkB;IACzD,OAAO,iBAAiB,CAAC,SAAS,IAAI,EAAE;AAC1C;AAGO,SAAS,mBACd,KAAuB,EACvB,MAAc;IAEd,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,EAAE,KAAK,QAAQ;YACtB,OAAO;QACT;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,QAAQ,mBAAmB,KAAK,QAAQ,EAAE;YAChD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAGO,SAAS,yBACd,KAAuB,EACvB,IAAY;IAEZ,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,IAAI,KAAK,MAAM;YACtB,OAAO;QACT;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,QAAQ,yBAAyB,KAAK,QAAQ,EAAE;YACtD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAGO,SAAS,uBAAuB,KAAuB;IAC5D,MAAM,YAA8B,EAAE;IAEtC,SAAS,QAAQ,KAAuB;QACtC,KAAK,MAAM,QAAQ,MAAO;YACxB,UAAU,IAAI,CAAC;YACf,IAAI,KAAK,QAAQ,EAAE;gBACjB,QAAQ,KAAK,QAAQ;YACvB;QACF;IACF;IAEA,QAAQ;IACR,OAAO;AACT;AAGO,SAAS,sBACd,KAAuB,EACvB,KAAa;IAEb,MAAM,YAAY,uBAAuB;IACzC,MAAM,aAAa,MAAM,WAAW,GAAG,IAAI;IAE3C,IAAI,CAAC,YAAY,OAAO,EAAE;IAE1B,OAAO,UAAU,MAAM,CAAC,CAAA,OACtB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eAClC,KAAK,WAAW,EAAE,cAAc,SAAS,eACzC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;AAErC;AAGO,SAAS,yBACd,KAAuB,EACvB,WAAmB;IAEnB,MAAM,cAAgC,EAAE;IAExC,SAAS,SAAS,KAAuB,EAAE,IAAsB;QAC/D,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,cAAc;mBAAI;gBAAM;aAAK;YAEnC,IAAI,KAAK,IAAI,KAAK,eAAe,YAAY,UAAU,CAAC,KAAK,IAAI,GAAG,MAAM;gBACxE,YAAY,IAAI,IAAI;gBACpB,OAAO;YACT;YAEA,IAAI,KAAK,QAAQ,IAAI,SAAS,KAAK,QAAQ,EAAE,cAAc;gBACzD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,SAAS,OAAO,EAAE;IAClB,OAAO;AACT;AAGO,SAAS,uBACd,KAAuB,EACvB,OAAe;IAEf,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;AAC/C;AAGO,SAAS,6BAA6B,KAAuB;IAClE,MAAM,YAAY,uBAAuB;IACzC,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG;AAC7D;AAGO,SAAS,mBAAmB,KAAuB;IACxD,MAAM,YAAY,uBAAuB;IACzC,OAAO,UAAU,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG;AACtE;AAGO,SAAS,wBACd,KAAuB,EACvB,OAAe;IAEf,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,QAAQ,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YACvD,IAAI,OAAO,OAAO;YAElB,MAAM,mBAAmB,wBAAwB,KAAK,QAAQ,EAAE;YAChE,IAAI,kBAAkB,OAAO;QAC/B;IACF;IACA,OAAO;AACT;AAGO,SAAS,uBACd,IAAoB,EACpB,WAAmB;IAEnB,IAAI,gBAAgB,KAAK,IAAI,IAAI,YAAY,UAAU,CAAC,KAAK,IAAI,GAAG,MAAM;QACxE,OAAO;IACT;IAEA,IAAI,KAAK,QAAQ,EAAE;QACjB,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,uBAAuB,OAAO;IACnE;IAEA,OAAO;AACT;AAGO,SAAS,uBACd,KAAuB,EACvB,MAAc,EACd,eAAuB,CAAC;IAExB,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,EAAE,KAAK,QAAQ;YACtB,OAAO;QACT;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,QAAQ,uBAAuB,KAAK,QAAQ,EAAE,QAAQ,eAAe;YAC3E,IAAI,UAAU,CAAC,GAAG,OAAO;QAC3B;IACF;IACA,OAAO,CAAC;AACV;AAGO,SAAS,4BAA4B,KAAuB;IAIjE,MAAM,SAAmB,EAAE;IAC3B,MAAM,UAAU,IAAI;IACpB,MAAM,YAAY,IAAI;IAEtB,SAAS,SAAS,KAAuB,EAAE,OAAe,EAAE;QAC1D,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,cAAc,OAAO,GAAG,KAAK,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,KAAK,KAAK;YAEjE,0BAA0B;YAC1B,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,GAAG;gBACxB,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,WAAW,EAAE,aAAa;YACjE;YACA,QAAQ,GAAG,CAAC,KAAK,EAAE;YAEnB,4BAA4B;YAC5B,IAAI,UAAU,GAAG,CAAC,KAAK,IAAI,GAAG;gBAC5B,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,aAAa;YACrE;YACA,UAAU,GAAG,CAAC,KAAK,IAAI;YAEvB,wBAAwB;YACxB,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,aAAa;YAC/C;YACA,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa;YAC9C;YACA,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa;YAC9C;YAEA,oBAAoB;YACpB,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,KAAK,QAAQ,EAAE;YAC1B;QACF;IACF;IAEA,SAAS;IAET,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,SAAS,wBAAwB,KAAuB;IAC7D,MAAM,YAAY,uBAAuB;IACzC,MAAM,WAAW,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,EAAE,MAAM,CAAC;IAChE,MAAM,WAAW,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,OACzC,uBAAuB,OAAO,KAAK,EAAE;IAEvC,MAAM,kBAAkB,6BAA6B;IACrD,MAAM,kBAAkB,mBAAmB;IAE3C,OAAO;QACL,YAAY,UAAU,MAAM;QAC5B,eAAe,MAAM,MAAM;QAC3B,UAAU,SAAS,IAAI;QACvB,UAAU,WAAW;QACrB,iBAAiB,gBAAgB,MAAM;QACvC;QACA,wBAAwB,MAAM,MAAM,CAAC,CAAC,KAAK,OACzC,MAAM,CAAC,KAAK,QAAQ,EAAE,UAAU,CAAC,GAAG,KAClC,MAAM,MAAM;IAClB;AACF;AAGO,MAAM,kBAAkB;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 7262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/shared/navigation/Breadcrumbs.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useSidebarStore } from '@/stores/sidebar/useSidebarStore'\nimport { useResponsive } from '@/hooks/useResponsive'\nimport { \n  ChevronRight, \n  Home, \n  MoreHorizontal,\n  ArrowLeft\n} from 'lucide-react'\nimport { navigationUtils } from '@/utils/navigationConfig'\n\ninterface BreadcrumbItem {\n  label: string\n  href: string\n  isActive: boolean\n  icon?: React.ComponentType<any>\n}\n\ninterface BreadcrumbsProps {\n  maxItems?: number\n  showHomeIcon?: boolean\n  showBackButton?: boolean\n  onBack?: () => void\n  className?: string\n}\n\nexport function Breadcrumbs({ \n  maxItems = 4, \n  showHomeIcon = true, \n  showBackButton = false,\n  onBack,\n  className = '' \n}: BreadcrumbsProps) {\n  const pathname = usePathname()\n  const { navigationItems, userType } = useSidebarStore()\n  const { isMobile } = useResponsive()\n\n  // Generate breadcrumbs from current path and navigation structure\n  const generateBreadcrumbs = (): BreadcrumbItem[] => {\n    const pathSegments = pathname.split('/').filter(Boolean)\n    const breadcrumbs: BreadcrumbItem[] = []\n\n    // Add home breadcrumb\n    const homeHref = `/${userType?.replace('_', '-') || ''}`\n    breadcrumbs.push({\n      label: 'Dashboard',\n      href: homeHref,\n      isActive: pathname === homeHref,\n      icon: showHomeIcon ? Home : undefined\n    })\n\n    // Build breadcrumbs from navigation structure\n    let currentPath = ''\n    for (let i = 0; i < pathSegments.length; i++) {\n      currentPath += `/${pathSegments[i]}`\n      \n      // Skip the user type segment (e.g., 'super-admin', 'institute-admin', 'student')\n      if (i === 0 && pathSegments[i].includes('admin')) continue\n      if (i === 0 && pathSegments[i] === 'student') continue\n      \n      // Find matching navigation item\n      const navItem = navigationUtils.findNavigationItemByHref(navigationItems, currentPath)\n      \n      if (navItem) {\n        breadcrumbs.push({\n          label: navItem.label,\n          href: currentPath,\n          isActive: i === pathSegments.length - 1\n        })\n      } else {\n        // Fallback to formatted segment name\n        const label = pathSegments[i]\n          .split('-')\n          .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n          .join(' ')\n        \n        breadcrumbs.push({\n          label,\n          href: currentPath,\n          isActive: i === pathSegments.length - 1\n        })\n      }\n    }\n\n    return breadcrumbs\n  }\n\n  const breadcrumbs = generateBreadcrumbs()\n\n  // Handle breadcrumb overflow\n  const getDisplayBreadcrumbs = () => {\n    if (breadcrumbs.length <= maxItems) {\n      return breadcrumbs\n    }\n\n    const firstItem = breadcrumbs[0]\n    const lastItems = breadcrumbs.slice(-2) // Always show last 2 items\n    const middleItems = breadcrumbs.slice(1, -2)\n\n    if (middleItems.length === 0) {\n      return breadcrumbs\n    }\n\n    return [\n      firstItem,\n      { label: '...', href: '#', isActive: false },\n      ...lastItems\n    ]\n  }\n\n  const displayBreadcrumbs = getDisplayBreadcrumbs()\n\n  // Mobile breadcrumbs (simplified)\n  if (isMobile) {\n    const currentItem = breadcrumbs[breadcrumbs.length - 1]\n    const parentItem = breadcrumbs.length > 1 ? breadcrumbs[breadcrumbs.length - 2] : null\n\n    return (\n      <div className={`flex items-center space-x-2 ${className}`}>\n        {showBackButton && onBack && (\n          <button\n            onClick={onBack}\n            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"w-4 h-4 text-gray-600\" />\n          </button>\n        )}\n        \n        {parentItem && (\n          <>\n            <Link\n              href={parentItem.href}\n              className=\"text-sm text-gray-600 hover:text-gray-900 transition-colors truncate max-w-24\"\n            >\n              {parentItem.label}\n            </Link>\n            <ChevronRight className=\"w-3 h-3 text-gray-400 flex-shrink-0\" />\n          </>\n        )}\n        \n        <span className=\"text-sm font-medium text-gray-900 truncate\">\n          {currentItem?.label}\n        </span>\n      </div>\n    )\n  }\n\n  // Desktop breadcrumbs\n  return (\n    <nav className={`flex items-center space-x-1 ${className}`} aria-label=\"Breadcrumb\">\n      <ol className=\"flex items-center space-x-1\">\n        {displayBreadcrumbs.map((item, index) => (\n          <li key={index} className=\"flex items-center\">\n            {index > 0 && (\n              <ChevronRight className=\"w-4 h-4 text-gray-400 mx-2 flex-shrink-0\" />\n            )}\n            \n            {item.label === '...' ? (\n              <div className=\"flex items-center space-x-1\">\n                <MoreHorizontal className=\"w-4 h-4 text-gray-400\" />\n              </div>\n            ) : item.isActive ? (\n              <span className=\"flex items-center space-x-1 text-sm font-medium text-gray-900\">\n                {item.icon && <item.icon className=\"w-4 h-4\" />}\n                <span>{item.label}</span>\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors\"\n              >\n                {item.icon && <item.icon className=\"w-4 h-4\" />}\n                <span>{item.label}</span>\n              </Link>\n            )}\n          </li>\n        ))}\n      </ol>\n    </nav>\n  )\n}\n\n// Breadcrumb separator component\nexport function BreadcrumbSeparator({ className = '' }: { className?: string }) {\n  return <ChevronRight className={`w-4 h-4 text-gray-400 ${className}`} />\n}\n\n// Custom breadcrumb component for manual breadcrumbs\ninterface CustomBreadcrumbsProps {\n  items: Array<{\n    label: string\n    href?: string\n    isActive?: boolean\n    icon?: React.ComponentType<any>\n  }>\n  className?: string\n}\n\nexport function CustomBreadcrumbs({ items, className = '' }: CustomBreadcrumbsProps) {\n  const { isMobile } = useResponsive()\n\n  if (isMobile && items.length > 2) {\n    const currentItem = items[items.length - 1]\n    const parentItem = items[items.length - 2]\n\n    return (\n      <div className={`flex items-center space-x-2 ${className}`}>\n        {parentItem.href ? (\n          <Link\n            href={parentItem.href}\n            className=\"text-sm text-gray-600 hover:text-gray-900 transition-colors truncate max-w-24\"\n          >\n            {parentItem.label}\n          </Link>\n        ) : (\n          <span className=\"text-sm text-gray-600 truncate max-w-24\">\n            {parentItem.label}\n          </span>\n        )}\n        <ChevronRight className=\"w-3 h-3 text-gray-400 flex-shrink-0\" />\n        <span className=\"text-sm font-medium text-gray-900 truncate\">\n          {currentItem.label}\n        </span>\n      </div>\n    )\n  }\n\n  return (\n    <nav className={`flex items-center space-x-1 ${className}`} aria-label=\"Breadcrumb\">\n      <ol className=\"flex items-center space-x-1\">\n        {items.map((item, index) => (\n          <li key={index} className=\"flex items-center\">\n            {index > 0 && (\n              <ChevronRight className=\"w-4 h-4 text-gray-400 mx-2 flex-shrink-0\" />\n            )}\n            \n            {item.isActive || !item.href ? (\n              <span className=\"flex items-center space-x-1 text-sm font-medium text-gray-900\">\n                {item.icon && <item.icon className=\"w-4 h-4\" />}\n                <span>{item.label}</span>\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors\"\n              >\n                {item.icon && <item.icon className=\"w-4 h-4\" />}\n                <span>{item.label}</span>\n              </Link>\n            )}\n          </li>\n        ))}\n      </ol>\n    </nav>\n  )\n}\n\nexport default Breadcrumbs\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;;;AAbA;;;;;;;AA8BO,SAAS,YAAY,EAC1B,WAAW,CAAC,EACZ,eAAe,IAAI,EACnB,iBAAiB,KAAK,EACtB,MAAM,EACN,YAAY,EAAE,EACG;;IACjB,MAAM,WAAW,CAAA,GAAA,kRAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IACpD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD;IAEjC,kEAAkE;IAClE,MAAM,sBAAsB;QAC1B,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QAChD,MAAM,cAAgC,EAAE;QAExC,sBAAsB;QACtB,MAAM,WAAW,CAAC,CAAC,EAAE,UAAU,QAAQ,KAAK,QAAQ,IAAI;QACxD,YAAY,IAAI,CAAC;YACf,OAAO;YACP,MAAM;YACN,UAAU,aAAa;YACvB,MAAM,eAAe,0RAAA,CAAA,OAAI,GAAG;QAC9B;QAEA,8CAA8C;QAC9C,IAAI,cAAc;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,eAAe,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,EAAE;YAEpC,iFAAiF;YACjF,IAAI,MAAM,KAAK,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU;YAClD,IAAI,MAAM,KAAK,YAAY,CAAC,EAAE,KAAK,WAAW;YAE9C,gCAAgC;YAChC,MAAM,UAAU,uJAAA,CAAA,kBAAe,CAAC,wBAAwB,CAAC,iBAAiB;YAE1E,IAAI,SAAS;gBACX,YAAY,IAAI,CAAC;oBACf,OAAO,QAAQ,KAAK;oBACpB,MAAM;oBACN,UAAU,MAAM,aAAa,MAAM,GAAG;gBACxC;YACF,OAAO;gBACL,qCAAqC;gBACrC,MAAM,QAAQ,YAAY,CAAC,EAAE,CAC1B,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;gBAER,YAAY,IAAI,CAAC;oBACf;oBACA,MAAM;oBACN,UAAU,MAAM,aAAa,MAAM,GAAG;gBACxC;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;IAEpB,6BAA6B;IAC7B,MAAM,wBAAwB;QAC5B,IAAI,YAAY,MAAM,IAAI,UAAU;YAClC,OAAO;QACT;QAEA,MAAM,YAAY,WAAW,CAAC,EAAE;QAChC,MAAM,YAAY,YAAY,KAAK,CAAC,CAAC,GAAG,2BAA2B;;QACnE,MAAM,cAAc,YAAY,KAAK,CAAC,GAAG,CAAC;QAE1C,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,OAAO;QACT;QAEA,OAAO;YACL;YACA;gBAAE,OAAO;gBAAO,MAAM;gBAAK,UAAU;YAAM;eACxC;SACJ;IACH;IAEA,MAAM,qBAAqB;IAE3B,kCAAkC;IAClC,IAAI,UAAU;QACZ,MAAM,cAAc,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;QACvD,MAAM,aAAa,YAAY,MAAM,GAAG,IAAI,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,GAAG;QAElF,qBACE,0UAAC;YAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;;gBACvD,kBAAkB,wBACjB,0UAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,0UAAC,uSAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;gBAIxB,4BACC;;sCACE,0UAAC,4SAAA,CAAA,UAAI;4BACH,MAAM,WAAW,IAAI;4BACrB,WAAU;sCAET,WAAW,KAAK;;;;;;sCAEnB,0UAAC,6SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;8BAI5B,0UAAC;oBAAK,WAAU;8BACb,aAAa;;;;;;;;;;;;IAItB;IAEA,sBAAsB;IACtB,qBACE,0UAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;QAAE,cAAW;kBACrE,cAAA,0UAAC;YAAG,WAAU;sBACX,mBAAmB,GAAG,CAAC,CAAC,MAAM,sBAC7B,0UAAC;oBAAe,WAAU;;wBACvB,QAAQ,mBACP,0UAAC,6SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAGzB,KAAK,KAAK,KAAK,sBACd,0UAAC;4BAAI,WAAU;sCACb,cAAA,0UAAC,uSAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;mCAE1B,KAAK,QAAQ,iBACf,0UAAC;4BAAK,WAAU;;gCACb,KAAK,IAAI,kBAAI,0UAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACnC,0UAAC;8CAAM,KAAK,KAAK;;;;;;;;;;;iDAGnB,0UAAC,4SAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;;gCAET,KAAK,IAAI,kBAAI,0UAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACnC,0UAAC;8CAAM,KAAK,KAAK;;;;;;;;;;;;;mBApBd;;;;;;;;;;;;;;;AA4BnB;GA1JgB;;QAOG,kRAAA,CAAA,cAAW;QACU,kKAAA,CAAA,kBAAe;QAChC,oJAAA,CAAA,gBAAa;;;KATpB;AA6JT,SAAS,oBAAoB,EAAE,YAAY,EAAE,EAA0B;IAC5E,qBAAO,0UAAC,6SAAA,CAAA,eAAY;QAAC,WAAW,CAAC,sBAAsB,EAAE,WAAW;;;;;;AACtE;MAFgB;AAeT,SAAS,kBAAkB,EAAE,KAAK,EAAE,YAAY,EAAE,EAA0B;;IACjF,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD;IAEjC,IAAI,YAAY,MAAM,MAAM,GAAG,GAAG;QAChC,MAAM,cAAc,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC3C,MAAM,aAAa,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAE1C,qBACE,0UAAC;YAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;;gBACvD,WAAW,IAAI,iBACd,0UAAC,4SAAA,CAAA,UAAI;oBACH,MAAM,WAAW,IAAI;oBACrB,WAAU;8BAET,WAAW,KAAK;;;;;yCAGnB,0UAAC;oBAAK,WAAU;8BACb,WAAW,KAAK;;;;;;8BAGrB,0UAAC,6SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;8BACxB,0UAAC;oBAAK,WAAU;8BACb,YAAY,KAAK;;;;;;;;;;;;IAI1B;IAEA,qBACE,0UAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;QAAE,cAAW;kBACrE,cAAA,0UAAC;YAAG,WAAU;sBACX,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,0UAAC;oBAAe,WAAU;;wBACvB,QAAQ,mBACP,0UAAC,6SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAGzB,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,iBAC1B,0UAAC;4BAAK,WAAU;;gCACb,KAAK,IAAI,kBAAI,0UAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACnC,0UAAC;8CAAM,KAAK,KAAK;;;;;;;;;;;iDAGnB,0UAAC,4SAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;;gCAET,KAAK,IAAI,kBAAI,0UAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACnC,0UAAC;8CAAM,KAAK,KAAK;;;;;;;;;;;;;mBAhBd;;;;;;;;;;;;;;;AAwBnB;IAzDgB;;QACO,oJAAA,CAAA,gBAAa;;;MADpB;uCA2DD", "debugId": null}}, {"offset": {"line": 7667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/shared/navigation/NavigationSearch.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport Link from 'next/link'\nimport { useSidebarStore } from '@/stores/sidebar/useSidebarStore'\nimport { useResponsive } from '@/hooks/useResponsive'\nimport { \n  Search, \n  X, \n  ArrowRight,\n  Clock,\n  Star,\n  Zap,\n  Command\n} from 'lucide-react'\nimport * as Icons from 'lucide-react'\nimport { navigationUtils } from '@/utils/navigationConfig'\n\ninterface NavigationSearchProps {\n  placeholder?: string\n  showShortcut?: boolean\n  onItemSelect?: (item: any) => void\n  className?: string\n}\n\nexport function NavigationSearch({ \n  placeholder = \"Search navigation...\", \n  showShortcut = true,\n  onItemSelect,\n  className = '' \n}: NavigationSearchProps) {\n  const { \n    navigationItems, \n    recentItems, \n    favoriteItems,\n    addToRecent \n  } = useSidebarStore()\n  const { isMobile } = useResponsive()\n  \n  const [isOpen, setIsOpen] = useState(false)\n  const [query, setQuery] = useState('')\n  const [selectedIndex, setSelectedIndex] = useState(0)\n  const [searchResults, setSearchResults] = useState<any[]>([])\n  \n  const searchRef = useRef<HTMLDivElement>(null)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  // Search navigation items\n  useEffect(() => {\n    if (query.trim()) {\n      const results = navigationUtils.searchNavigationItems(navigationItems, query)\n      setSearchResults(results)\n      setSelectedIndex(0)\n    } else {\n      setSearchResults([])\n    }\n  }, [query, navigationItems])\n\n  // Handle keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      // Cmd/Ctrl + K to open search\n      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {\n        e.preventDefault()\n        setIsOpen(true)\n        setTimeout(() => inputRef.current?.focus(), 100)\n      }\n      \n      // Escape to close\n      if (e.key === 'Escape') {\n        setIsOpen(false)\n        setQuery('')\n      }\n      \n      // Arrow navigation\n      if (isOpen && searchResults.length > 0) {\n        if (e.key === 'ArrowDown') {\n          e.preventDefault()\n          setSelectedIndex(prev => \n            prev < searchResults.length - 1 ? prev + 1 : 0\n          )\n        } else if (e.key === 'ArrowUp') {\n          e.preventDefault()\n          setSelectedIndex(prev => \n            prev > 0 ? prev - 1 : searchResults.length - 1\n          )\n        } else if (e.key === 'Enter') {\n          e.preventDefault()\n          const selectedItem = searchResults[selectedIndex]\n          if (selectedItem) {\n            handleItemSelect(selectedItem)\n          }\n        }\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [isOpen, searchResults, selectedIndex])\n\n  // Click outside to close\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside)\n      return () => document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [isOpen])\n\n  const handleItemSelect = (item: any) => {\n    addToRecent(item)\n    onItemSelect?.(item)\n    setIsOpen(false)\n    setQuery('')\n  }\n\n  const getQuickActions = () => {\n    const actions = []\n    \n    // Recent items\n    if (recentItems.length > 0) {\n      actions.push({\n        category: 'Recent',\n        icon: Clock,\n        items: recentItems.slice(0, 3)\n      })\n    }\n    \n    // Favorite items\n    if (favoriteItems.length > 0) {\n      const favoriteNavItems = favoriteItems\n        .map(id => navigationUtils.findNavigationItem(navigationItems, id))\n        .filter(Boolean)\n        .slice(0, 3)\n      \n      if (favoriteNavItems.length > 0) {\n        actions.push({\n          category: 'Favorites',\n          icon: Star,\n          items: favoriteNavItems\n        })\n      }\n    }\n    \n    // Quick access items\n    const quickAccessItems = navigationItems\n      .filter(item => item.badge && item.badge > 0)\n      .slice(0, 3)\n    \n    if (quickAccessItems.length > 0) {\n      actions.push({\n        category: 'Quick Access',\n        icon: Zap,\n        items: quickAccessItems\n      })\n    }\n    \n    return actions\n  }\n\n  const quickActions = getQuickActions()\n\n  return (\n    <div ref={searchRef} className={`relative ${className}`}>\n      {/* Search Input */}\n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <Search className=\"w-4 h-4 text-gray-400\" />\n        </div>\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          onFocus={() => setIsOpen(true)}\n          placeholder={placeholder}\n          className={`w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm ${\n            isMobile ? 'text-base' : ''\n          }`}\n        />\n        {showShortcut && !isMobile && (\n          <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n            <div className=\"flex items-center space-x-1 text-xs text-gray-400\">\n              <Command className=\"w-3 h-3\" />\n              <span>K</span>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Search Results Dropdown */}\n      {isOpen && (\n        <div className=\"absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto\">\n          {query.trim() ? (\n            // Search Results\n            <div>\n              {searchResults.length > 0 ? (\n                <div className=\"p-2\">\n                  <div className=\"text-xs font-medium text-gray-500 uppercase tracking-wider px-2 py-1 mb-2\">\n                    Search Results ({searchResults.length})\n                  </div>\n                  {searchResults.map((item, index) => (\n                    <SearchResultItem\n                      key={item.id}\n                      item={item}\n                      isSelected={index === selectedIndex}\n                      onClick={() => handleItemSelect(item)}\n                    />\n                  ))}\n                </div>\n              ) : (\n                <div className=\"p-4 text-center text-gray-500\">\n                  <Search className=\"w-8 h-8 mx-auto mb-2 text-gray-300\" />\n                  <div className=\"text-sm\">No results found for \"{query}\"</div>\n                </div>\n              )}\n            </div>\n          ) : (\n            // Quick Actions\n            <div className=\"p-2\">\n              {quickActions.length > 0 ? (\n                quickActions.map((section, sectionIndex) => (\n                  <div key={sectionIndex} className=\"mb-4 last:mb-0\">\n                    <div className=\"flex items-center space-x-2 px-2 py-1 mb-2\">\n                      <section.icon className=\"w-4 h-4 text-gray-400\" />\n                      <span className=\"text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        {section.category}\n                      </span>\n                    </div>\n                    {section.items.map((item: any, itemIndex: number) => (\n                      <SearchResultItem\n                        key={item.id}\n                        item={item}\n                        isSelected={false}\n                        onClick={() => handleItemSelect(item)}\n                      />\n                    ))}\n                  </div>\n                ))\n              ) : (\n                <div className=\"p-4 text-center text-gray-500\">\n                  <div className=\"text-sm\">Start typing to search navigation...</div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Search result item component\ninterface SearchResultItemProps {\n  item: any\n  isSelected: boolean\n  onClick: () => void\n}\n\nfunction SearchResultItem({ item, isSelected, onClick }: SearchResultItemProps) {\n  const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>\n\n  return (\n    <button\n      onClick={onClick}\n      className={`w-full flex items-center space-x-3 px-2 py-2 rounded-lg text-left transition-colors ${\n        isSelected ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'\n      }`}\n    >\n      {/* Icon */}\n      <div className={`flex-shrink-0 ${\n        isSelected ? 'text-blue-600' : 'text-gray-400'\n      }`}>\n        {IconComponent && <IconComponent className=\"w-4 h-4\" />}\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 min-w-0\">\n        <div className=\"text-sm font-medium text-gray-900 truncate\">\n          {item.label}\n        </div>\n        {item.description && (\n          <div className=\"text-xs text-gray-500 truncate\">\n            {item.description}\n          </div>\n        )}\n      </div>\n\n      {/* Badge */}\n      {item.badge && item.badge > 0 && (\n        <span className=\"flex-shrink-0 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full\">\n          {item.badge > 9 ? '9+' : item.badge}\n        </span>\n      )}\n\n      {/* Arrow */}\n      <ArrowRight className=\"w-3 h-3 text-gray-400 flex-shrink-0\" />\n    </button>\n  )\n}\n\n// Global search modal for mobile\nexport function GlobalSearchModal() {\n  const [isOpen, setIsOpen] = useState(false)\n  const { isMobile } = useResponsive()\n\n  if (!isMobile) return null\n\n  return (\n    <>\n      {/* Search Button */}\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n      >\n        <Search className=\"w-5 h-5 text-gray-600\" />\n      </button>\n\n      {/* Modal */}\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 bg-black bg-opacity-50\">\n          <div className=\"bg-white h-full\">\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n              <h2 className=\"text-lg font-medium text-gray-900\">Search</h2>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n              >\n                <X className=\"w-5 h-5 text-gray-500\" />\n              </button>\n            </div>\n\n            {/* Search Content */}\n            <div className=\"p-4\">\n              <NavigationSearch\n                placeholder=\"Search navigation...\"\n                showShortcut={false}\n                onItemSelect={() => setIsOpen(false)}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n\nexport default NavigationSearch\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAhBA;;;;;;;AAyBO,SAAS,iBAAiB,EAC/B,cAAc,sBAAsB,EACpC,eAAe,IAAI,EACnB,YAAY,EACZ,YAAY,EAAE,EACQ;;IACtB,MAAM,EACJ,eAAe,EACf,WAAW,EACX,aAAa,EACb,WAAW,EACZ,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAClB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD;IAEjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE5D,MAAM,YAAY,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,WAAW,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,0BAA0B;IAC1B,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,MAAM,IAAI,IAAI;gBAChB,MAAM,UAAU,uJAAA,CAAA,kBAAe,CAAC,qBAAqB,CAAC,iBAAiB;gBACvE,iBAAiB;gBACjB,iBAAiB;YACnB,OAAO;gBACL,iBAAiB,EAAE;YACrB;QACF;qCAAG;QAAC;QAAO;KAAgB;IAE3B,4BAA4B;IAC5B,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;4DAAgB,CAAC;oBACrB,8BAA8B;oBAC9B,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB,UAAU;wBACV;wEAAW,IAAM,SAAS,OAAO,EAAE;uEAAS;oBAC9C;oBAEA,kBAAkB;oBAClB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB,UAAU;wBACV,SAAS;oBACX;oBAEA,mBAAmB;oBACnB,IAAI,UAAU,cAAc,MAAM,GAAG,GAAG;wBACtC,IAAI,EAAE,GAAG,KAAK,aAAa;4BACzB,EAAE,cAAc;4BAChB;4EAAiB,CAAA,OACf,OAAO,cAAc,MAAM,GAAG,IAAI,OAAO,IAAI;;wBAEjD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;4BAC9B,EAAE,cAAc;4BAChB;4EAAiB,CAAA,OACf,OAAO,IAAI,OAAO,IAAI,cAAc,MAAM,GAAG;;wBAEjD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;4BAC5B,EAAE,cAAc;4BAChB,MAAM,eAAe,aAAa,CAAC,cAAc;4BACjD,IAAI,cAAc;gCAChB,iBAAiB;4BACnB;wBACF;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;8CAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;qCAAG;QAAC;QAAQ;QAAe;KAAc;IAEzC,yBAAyB;IACzB,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB,CAAC;oBAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC1E,UAAU;oBACZ;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,aAAa;gBACvC;kDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;YACzD;QACF;qCAAG;QAAC;KAAO;IAEX,MAAM,mBAAmB,CAAC;QACxB,YAAY;QACZ,eAAe;QACf,UAAU;QACV,SAAS;IACX;IAEA,MAAM,kBAAkB;QACtB,MAAM,UAAU,EAAE;QAElB,eAAe;QACf,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,QAAQ,IAAI,CAAC;gBACX,UAAU;gBACV,MAAM,2RAAA,CAAA,QAAK;gBACX,OAAO,YAAY,KAAK,CAAC,GAAG;YAC9B;QACF;QAEA,iBAAiB;QACjB,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,mBAAmB,cACtB,GAAG,CAAC,CAAA,KAAM,uJAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,iBAAiB,KAC9D,MAAM,CAAC,SACP,KAAK,CAAC,GAAG;YAEZ,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,QAAQ,IAAI,CAAC;oBACX,UAAU;oBACV,MAAM,yRAAA,CAAA,OAAI;oBACV,OAAO;gBACT;YACF;QACF;QAEA,qBAAqB;QACrB,MAAM,mBAAmB,gBACtB,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,GAC1C,KAAK,CAAC,GAAG;QAEZ,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,QAAQ,IAAI,CAAC;gBACX,UAAU;gBACV,MAAM,uRAAA,CAAA,MAAG;gBACT,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,qBACE,0UAAC;QAAI,KAAK;QAAW,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAErD,0UAAC;gBAAI,WAAU;;kCACb,0UAAC;wBAAI,WAAU;kCACb,cAAA,0UAAC,6RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,0UAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,UAAU;wBACzB,aAAa;wBACb,WAAW,CAAC,2HAA2H,EACrI,WAAW,cAAc,IACzB;;;;;;oBAEH,gBAAgB,CAAC,0BAChB,0UAAC;wBAAI,WAAU;kCACb,cAAA,0UAAC;4BAAI,WAAU;;8CACb,0UAAC,+RAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,0UAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;YAOb,wBACC,0UAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,KACT,iBAAiB;8BACjB,0UAAC;8BACE,cAAc,MAAM,GAAG,kBACtB,0UAAC;wBAAI,WAAU;;0CACb,0UAAC;gCAAI,WAAU;;oCAA4E;oCACxE,cAAc,MAAM;oCAAC;;;;;;;4BAEvC,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,0UAAC;oCAEC,MAAM;oCACN,YAAY,UAAU;oCACtB,SAAS,IAAM,iBAAiB;mCAH3B,KAAK,EAAE;;;;;;;;;;6CAQlB,0UAAC;wBAAI,WAAU;;0CACb,0UAAC,6RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,0UAAC;gCAAI,WAAU;;oCAAU;oCAAuB;oCAAM;;;;;;;;;;;;;;;;;2BAK5D,gBAAgB;8BAChB,0UAAC;oBAAI,WAAU;8BACZ,aAAa,MAAM,GAAG,IACrB,aAAa,GAAG,CAAC,CAAC,SAAS,6BACzB,0UAAC;4BAAuB,WAAU;;8CAChC,0UAAC;oCAAI,WAAU;;sDACb,0UAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;sDACxB,0UAAC;4CAAK,WAAU;sDACb,QAAQ,QAAQ;;;;;;;;;;;;gCAGpB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAW,0BAC7B,0UAAC;wCAEC,MAAM;wCACN,YAAY;wCACZ,SAAS,IAAM,iBAAiB;uCAH3B,KAAK,EAAE;;;;;;2BATR;;;;kDAkBZ,0UAAC;wBAAI,WAAU;kCACb,cAAA,0UAAC;4BAAI,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C;GAtOgB;;QAWV,kKAAA,CAAA,kBAAe;QACE,oJAAA,CAAA,gBAAa;;;KAZpB;AA+OhB,SAAS,iBAAiB,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAyB;IAC5E,MAAM,gBAAgB,AAAC,wPAAa,CAAC,KAAK,IAAI,CAAC;IAE/C,qBACE,0UAAC;QACC,SAAS;QACT,WAAW,CAAC,oFAAoF,EAC9F,aAAa,6BAA6B,oBAC1C;;0BAGF,0UAAC;gBAAI,WAAW,CAAC,cAAc,EAC7B,aAAa,kBAAkB,iBAC/B;0BACC,+BAAiB,0UAAC;oBAAc,WAAU;;;;;;;;;;;0BAI7C,0UAAC;gBAAI,WAAU;;kCACb,0UAAC;wBAAI,WAAU;kCACZ,KAAK,KAAK;;;;;;oBAEZ,KAAK,WAAW,kBACf,0UAAC;wBAAI,WAAU;kCACZ,KAAK,WAAW;;;;;;;;;;;;YAMtB,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,0UAAC;gBAAK,WAAU;0BACb,KAAK,KAAK,GAAG,IAAI,OAAO,KAAK,KAAK;;;;;;0BAKvC,0UAAC,ySAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;;;;;;;AAG5B;MAxCS;AA2CF,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD;IAEjC,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE;;0BAEE,0UAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;0BAEV,cAAA,0UAAC,6RAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;YAInB,wBACC,0UAAC;gBAAI,WAAU;0BACb,cAAA,0UAAC;oBAAI,WAAU;;sCAEb,0UAAC;4BAAI,WAAU;;8CACb,0UAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,0UAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAEV,cAAA,0UAAC,mRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,0UAAC;4BAAI,WAAU;sCACb,cAAA,0UAAC;gCACC,aAAY;gCACZ,cAAc;gCACd,cAAc,IAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;IA5CgB;;QAEO,oJAAA,CAAA,gBAAa;;;MAFpB;uCA8CD", "debugId": null}}, {"offset": {"line": 8223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/NotificationDropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { useSidebarStore } from '@/stores/sidebar/useSidebarStore'\nimport { \n  Bell, \n  X, \n  CheckCheck,\n  Info,\n  AlertTriangle,\n  AlertCircle,\n  CheckCircle\n} from 'lucide-react'\n\ninterface NotificationDropdownProps {\n  notifications: Array<{\n    id: string\n    title: string\n    message: string\n    type: 'info' | 'warning' | 'error' | 'success'\n    timestamp: string\n    isRead: boolean\n  }>\n  onClose: () => void\n}\n\nexport function NotificationDropdown({ notifications, onClose }: NotificationDropdownProps) {\n  const {\n    markNotificationAsRead,\n    markAllNotificationsAsRead,\n    removeNotification,\n    clearNotifications\n  } = useSidebarStore()\n  \n  const dropdownRef = useRef<HTMLDivElement>(null)\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        onClose()\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [onClose])\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'info':\n        return <Info className=\"w-4 h-4 text-blue-500\" />\n      case 'warning':\n        return <AlertTriangle className=\"w-4 h-4 text-yellow-500\" />\n      case 'error':\n        return <AlertCircle className=\"w-4 h-4 text-red-500\" />\n      case 'success':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n      default:\n        return <Bell className=\"w-4 h-4 text-gray-500\" />\n    }\n  }\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    \n    if (diffInMinutes < 1) return 'Just now'\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`\n    return `${Math.floor(diffInMinutes / 1440)}d ago`\n  }\n\n  const handleNotificationClick = (notificationId: string, isRead: boolean) => {\n    if (!isRead) {\n      markNotificationAsRead(notificationId)\n    }\n  }\n\n  const handleMarkAllAsRead = () => {\n    markAllNotificationsAsRead()\n  }\n\n  const handleClearAll = () => {\n    clearNotifications()\n  }\n\n  return (\n    <div \n      ref={dropdownRef}\n      className=\"absolute right-0 top-full mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50\"\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Notifications</h3>\n        <button\n          onClick={onClose}\n          className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\n        >\n          <X className=\"w-4 h-4 text-gray-400\" />\n        </button>\n      </div>\n\n      {/* Actions */}\n      {notifications.length > 0 && (\n        <div className=\"flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50\">\n          <button\n            onClick={handleMarkAllAsRead}\n            className=\"flex items-center text-sm text-blue-600 hover:text-blue-700 transition-colors\"\n          >\n            <CheckCheck className=\"w-4 h-4 mr-1\" />\n            Mark all as read\n          </button>\n          <button\n            onClick={handleClearAll}\n            className=\"text-sm text-red-600 hover:text-red-700 transition-colors\"\n          >\n            Clear all\n          </button>\n        </div>\n      )}\n\n      {/* Notifications List */}\n      <div className=\"max-h-96 overflow-y-auto\">\n        {notifications.length === 0 ? (\n          <div className=\"p-6 text-center\">\n            <Bell className=\"w-8 h-8 text-gray-300 mx-auto mb-2\" />\n            <p className=\"text-sm text-gray-500\">No notifications</p>\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {notifications.map((notification) => (\n              <div\n                key={notification.id}\n                onClick={() => handleNotificationClick(notification.id, notification.isRead)}\n                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${\n                  !notification.isRead ? 'bg-blue-50' : ''\n                }`}\n              >\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"flex-shrink-0 mt-0.5\">\n                    {getNotificationIcon(notification.type)}\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between\">\n                      <p className={`text-sm font-medium ${\n                        !notification.isRead ? 'text-gray-900' : 'text-gray-700'\n                      }`}>\n                        {notification.title}\n                      </p>\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation()\n                          removeNotification(notification.id)\n                        }}\n                        className=\"p-1 hover:bg-gray-200 rounded transition-colors\"\n                      >\n                        <X className=\"w-3 h-3 text-gray-400\" />\n                      </button>\n                    </div>\n                    \n                    <p className=\"text-sm text-gray-600 mt-1\">\n                      {notification.message}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between mt-2\">\n                      <span className=\"text-xs text-gray-500\">\n                        {formatTimestamp(notification.timestamp)}\n                      </span>\n                      {!notification.isRead && (\n                        <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport default NotificationDropdown\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AA0BO,SAAS,qBAAqB,EAAE,aAAa,EAAE,OAAO,EAA6B;;IACxF,MAAM,EACJ,sBAAsB,EACtB,0BAA0B,EAC1B,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,cAAc,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;qEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;kDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;yCAAG;QAAC;KAAQ;IAEZ,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,0UAAC,yRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,0UAAC,+SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,0UAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,0UAAC,kTAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,0UAAC,yRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,KAAK,CAAC;QACtD,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACzE,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;IACnD;IAEA,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,IAAI,CAAC,QAAQ;YACX,uBAAuB;QACzB;IACF;IAEA,MAAM,sBAAsB;QAC1B;IACF;IAEA,MAAM,iBAAiB;QACrB;IACF;IAEA,qBACE,0UAAC;QACC,KAAK;QACL,WAAU;;0BAGV,0UAAC;gBAAI,WAAU;;kCACb,0UAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,0UAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,0UAAC,mRAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAKhB,cAAc,MAAM,GAAG,mBACtB,0UAAC;gBAAI,WAAU;;kCACb,0UAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,0UAAC,ySAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGzC,0UAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,0UAAC;gBAAI,WAAU;0BACZ,cAAc,MAAM,KAAK,kBACxB,0UAAC;oBAAI,WAAU;;sCACb,0UAAC,yRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,0UAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;yCAGvC,0UAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,0UAAC;4BAEC,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,MAAM;4BAC3E,WAAW,CAAC,sDAAsD,EAChE,CAAC,aAAa,MAAM,GAAG,eAAe,IACtC;sCAEF,cAAA,0UAAC;gCAAI,WAAU;;kDACb,0UAAC;wCAAI,WAAU;kDACZ,oBAAoB,aAAa,IAAI;;;;;;kDAGxC,0UAAC;wCAAI,WAAU;;0DACb,0UAAC;gDAAI,WAAU;;kEACb,0UAAC;wDAAE,WAAW,CAAC,oBAAoB,EACjC,CAAC,aAAa,MAAM,GAAG,kBAAkB,iBACzC;kEACC,aAAa,KAAK;;;;;;kEAErB,0UAAC;wDACC,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,mBAAmB,aAAa,EAAE;wDACpC;wDACA,WAAU;kEAEV,cAAA,0UAAC,mRAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIjB,0UAAC;gDAAE,WAAU;0DACV,aAAa,OAAO;;;;;;0DAGvB,0UAAC;gDAAI,WAAU;;kEACb,0UAAC;wDAAK,WAAU;kEACb,gBAAgB,aAAa,SAAS;;;;;;oDAExC,CAAC,aAAa,MAAM,kBACnB,0UAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;2BAtClB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;AAkDpC;GA9JgB;;QAMV,kKAAA,CAAA,kBAAe;;;KANL;uCAgKD", "debugId": null}}, {"offset": {"line": 8564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/ProfileDropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport Link from 'next/link'\nimport { UserType } from '@/stores/sidebar/useSidebarStore'\nimport {\n  User,\n  Settings,\n  HelpCircle,\n  LogOut,\n  CreditCard,\n  Shield,\n  Bell\n} from 'lucide-react'\nimport { ProfileSettingsModal } from '@/components/modals/ProfileSettingsModal'\nimport { useProfileModal } from '@/hooks/useProfileModal'\n\ninterface ProfileDropdownProps {\n  user: any // User type from auth store\n  userType: UserType\n  onLogout: () => void\n  onClose: () => void\n}\n\nexport function ProfileDropdown({ user, userType, onLogout, onClose }: ProfileDropdownProps) {\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const { isOpen, openModal, closeModal } = useProfileModal()\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        onClose()\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [onClose])\n\n  const getProfileLinks = () => {\n    // Get the correct path prefix based on user type\n    const getPathPrefix = () => {\n      if (userType === 'super_admin') return 'super-admin'\n      if (userType === 'institute_admin') return 'admin'\n      if (userType === 'student') return 'student'\n      return 'admin' // fallback\n    }\n\n    const pathPrefix = getPathPrefix()\n\n    const baseLinks = [\n      {\n        icon: User,\n        label: 'Profile',\n        href: `/${pathPrefix}/profile`,\n        description: 'Manage your profile information',\n        isModal: true // Flag to indicate this should open a modal\n      },\n\n    ]\n\n    // Add user-type specific links\n    if (userType === 'super_admin') {\n      baseLinks.push(\n        {\n          icon: Settings,\n          label: 'Settings',\n          href: `/${pathPrefix}/settings`,\n          description: 'Account and preferences',\n          isModal: false\n        },\n        {\n          icon: Shield,\n          label: 'System Settings',\n          href: `/${pathPrefix}/settings/platform`,\n          description: 'Platform configuration',\n          isModal: false\n        }\n      )\n    } else if (userType === 'institute_admin') {\n      baseLinks.push(\n        {\n          icon: Settings,\n          label: 'Settings',\n          href: `/admin/settings`,\n          description: 'Account and preferences',\n          isModal: false\n        },\n        {\n          icon: CreditCard,\n          label: 'Billing',\n          href: '/admin/billing',\n          description: 'Manage billing and payments',\n          isModal: false\n        }\n      )\n    } else if (userType === 'student') {\n      baseLinks.push(\n        {\n          icon: CreditCard,\n          label: 'Payments',\n          href: `/${pathPrefix}/payments`,\n          description: 'Payment history and methods',\n          isModal: false\n        },\n        {\n          icon: Bell,\n          label: 'Notifications',\n          href: `/${pathPrefix}/notifications`,\n          description: 'Notification preferences',\n          isModal: false\n        }\n      )\n    }\n\n    baseLinks.push({\n      icon: HelpCircle,\n      label: 'Help & Support',\n      href: `/${pathPrefix}/support`,\n      description: 'Get help and contact support',\n      isModal: false\n    })\n\n    return baseLinks\n  }\n\n  const profileLinks = getProfileLinks()\n\n  const handleLinkClick = (link: any) => {\n    if (link.isModal) {\n      openModal()\n    }\n    onClose()\n  }\n\n  const handleLogoutClick = () => {\n    onLogout()\n    onClose()\n  }\n\n  return (\n    <div \n      ref={dropdownRef}\n      className=\"absolute right-0 top-full mt-2 w-72 bg-white border border-gray-200 rounded-lg shadow-lg z-50\"\n    >\n      {/* User Info Header */}\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center\">\n            {user?.personalInfo?.avatar ? (\n              <img \n                src={user.personalInfo.avatar} \n                alt={user.personalInfo.fullName || user.email}\n                className=\"w-12 h-12 rounded-full object-cover\"\n              />\n            ) : (\n              <User className=\"w-6 h-6 text-white\" />\n            )}\n          </div>\n          \n          <div className=\"flex-1 min-w-0\">\n            <div className=\"text-sm font-medium text-gray-900 truncate\">\n              {user?.personalInfo?.fullName || user?.email || 'User'}\n            </div>\n            <div className=\"text-sm text-gray-500 truncate\">\n              {user?.personalInfo?.email || user?.email}\n            </div>\n            <div className=\"text-xs text-gray-400 capitalize mt-1\">\n              {user?.role?.name || userType.replace('_', ' ')}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Menu Items */}\n      <div className=\"py-1\">\n        {profileLinks.map((link, index) => (\n          link.isModal ? (\n            <button\n              key={index}\n              onClick={() => handleLinkClick(link)}\n              className=\"w-full flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors text-left\"\n            >\n              <link.icon className=\"w-4 h-4 mr-3 text-gray-400\" />\n              <div className=\"flex-1\">\n                <div className=\"font-medium\">{link.label}</div>\n                <div className=\"text-xs text-gray-500\">{link.description}</div>\n              </div>\n            </button>\n          ) : (\n            <Link\n              key={index}\n              href={link.href}\n              onClick={() => handleLinkClick(link)}\n              className=\"flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors\"\n            >\n              <link.icon className=\"w-4 h-4 mr-3 text-gray-400\" />\n              <div className=\"flex-1\">\n                <div className=\"font-medium\">{link.label}</div>\n                <div className=\"text-xs text-gray-500\">{link.description}</div>\n              </div>\n            </Link>\n          )\n        ))}\n      </div>\n\n      {/* Logout */}\n      <div className=\"border-t border-gray-200\">\n        <button\n          onClick={handleLogoutClick}\n          className=\"flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors\"\n        >\n          <LogOut className=\"w-4 h-4 mr-3\" />\n          <div className=\"flex-1 text-left\">\n            <div className=\"font-medium\">Sign Out</div>\n            <div className=\"text-xs text-red-500\">Sign out of your account</div>\n          </div>\n        </button>\n      </div>\n\n      {/* Profile Settings Modal */}\n      <ProfileSettingsModal\n        isOpen={isOpen}\n        onClose={closeModal}\n      />\n    </div>\n  )\n}\n\nexport default ProfileDropdown\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAfA;;;;;;AAwBO,SAAS,gBAAgB,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAwB;;IACzF,MAAM,cAAc,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,kBAAe,AAAD;IAExD,uCAAuC;IACvC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;gEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;oCAAG;QAAC;KAAQ;IAEZ,MAAM,kBAAkB;QACtB,iDAAiD;QACjD,MAAM,gBAAgB;YACpB,IAAI,aAAa,eAAe,OAAO;YACvC,IAAI,aAAa,mBAAmB,OAAO;YAC3C,IAAI,aAAa,WAAW,OAAO;YACnC,OAAO,QAAQ,WAAW;;QAC5B;QAEA,MAAM,aAAa;QAEnB,MAAM,YAAY;YAChB;gBACE,MAAM,yRAAA,CAAA,OAAI;gBACV,OAAO;gBACP,MAAM,CAAC,CAAC,EAAE,WAAW,QAAQ,CAAC;gBAC9B,aAAa;gBACb,SAAS,KAAK,4CAA4C;YAC5D;SAED;QAED,+BAA+B;QAC/B,IAAI,aAAa,eAAe;YAC9B,UAAU,IAAI,CACZ;gBACE,MAAM,iSAAA,CAAA,WAAQ;gBACd,OAAO;gBACP,MAAM,CAAC,CAAC,EAAE,WAAW,SAAS,CAAC;gBAC/B,aAAa;gBACb,SAAS;YACX,GACA;gBACE,MAAM,6RAAA,CAAA,SAAM;gBACZ,OAAO;gBACP,MAAM,CAAC,CAAC,EAAE,WAAW,kBAAkB,CAAC;gBACxC,aAAa;gBACb,SAAS;YACX;QAEJ,OAAO,IAAI,aAAa,mBAAmB;YACzC,UAAU,IAAI,CACZ;gBACE,MAAM,iSAAA,CAAA,WAAQ;gBACd,OAAO;gBACP,MAAM,CAAC,eAAe,CAAC;gBACvB,aAAa;gBACb,SAAS;YACX,GACA;gBACE,MAAM,ySAAA,CAAA,aAAU;gBAChB,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;QAEJ,OAAO,IAAI,aAAa,WAAW;YACjC,UAAU,IAAI,CACZ;gBACE,MAAM,ySAAA,CAAA,aAAU;gBAChB,OAAO;gBACP,MAAM,CAAC,CAAC,EAAE,WAAW,SAAS,CAAC;gBAC/B,aAAa;gBACb,SAAS;YACX,GACA;gBACE,MAAM,yRAAA,CAAA,OAAI;gBACV,OAAO;gBACP,MAAM,CAAC,CAAC,EAAE,WAAW,cAAc,CAAC;gBACpC,aAAa;gBACb,SAAS;YACX;QAEJ;QAEA,UAAU,IAAI,CAAC;YACb,MAAM,qTAAA,CAAA,aAAU;YAChB,OAAO;YACP,MAAM,CAAC,CAAC,EAAE,WAAW,QAAQ,CAAC;YAC9B,aAAa;YACb,SAAS;QACX;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,OAAO,EAAE;YAChB;QACF;QACA;IACF;IAEA,MAAM,oBAAoB;QACxB;QACA;IACF;IAEA,qBACE,0UAAC;QACC,KAAK;QACL,WAAU;;0BAGV,0UAAC;gBAAI,WAAU;0BACb,cAAA,0UAAC;oBAAI,WAAU;;sCACb,0UAAC;4BAAI,WAAU;sCACZ,MAAM,cAAc,uBACnB,0UAAC;gCACC,KAAK,KAAK,YAAY,CAAC,MAAM;gCAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;gCAC7C,WAAU;;;;;qDAGZ,0UAAC,yRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIpB,0UAAC;4BAAI,WAAU;;8CACb,0UAAC;oCAAI,WAAU;8CACZ,MAAM,cAAc,YAAY,MAAM,SAAS;;;;;;8CAElD,0UAAC;oCAAI,WAAU;8CACZ,MAAM,cAAc,SAAS,MAAM;;;;;;8CAEtC,0UAAC;oCAAI,WAAU;8CACZ,MAAM,MAAM,QAAQ,SAAS,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOnD,0UAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,QACvB,KAAK,OAAO,iBACV,0UAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;0CAEV,0UAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;0CACrB,0UAAC;gCAAI,WAAU;;kDACb,0UAAC;wCAAI,WAAU;kDAAe,KAAK,KAAK;;;;;;kDACxC,0UAAC;wCAAI,WAAU;kDAAyB,KAAK,WAAW;;;;;;;;;;;;;uBAPrD;;;;6CAWP,0UAAC,4SAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;0CAEV,0UAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;0CACrB,0UAAC;gCAAI,WAAU;;kDACb,0UAAC;wCAAI,WAAU;kDAAe,KAAK,KAAK;;;;;;kDACxC,0UAAC;wCAAI,WAAU;kDAAyB,KAAK,WAAW;;;;;;;;;;;;;uBARrD;;;;;;;;;;0BAgBb,0UAAC;gBAAI,WAAU;0BACb,cAAA,0UAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,0UAAC,iSAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,0UAAC;4BAAI,WAAU;;8CACb,0UAAC;oCAAI,WAAU;8CAAc;;;;;;8CAC7B,0UAAC;oCAAI,WAAU;8CAAuB;;;;;;;;;;;;;;;;;;;;;;;0BAM5C,0UAAC,2KAAA,CAAA,uBAAoB;gBACnB,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB;GA5MgB;;QAE4B,sJAAA,CAAA,kBAAe;;;KAF3C;uCA8MD", "debugId": null}}, {"offset": {"line": 8941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/institute-admin/institute.ts"], "sourcesContent": ["/**\n * Institute Admin API functions for institute management\n */\n\nimport { api } from '../api'\n\n// Types\nexport interface Institute {\n  id: string\n  name: string\n  tagline?: string\n  email: string\n  phone?: string\n  website?: string\n  description?: string\n  customDomain?: string\n  domainVerified?: boolean\n  logo?: any\n  favicon?: any\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface InstituteStats {\n  totalStudents: number\n  totalBranches: number\n  totalCourses: number\n  recentStudents: number\n  growthRate: number\n}\n\nexport interface UpdateInstituteData {\n  name?: string\n  tagline?: string\n  email?: string\n  phone?: string\n  website?: string\n  description?: string\n  customDomain?: string\n}\n\n// API Functions\nexport const instituteApi = {\n  /**\n   * Get institute details\n   */\n  getDetails: async (): Promise<{ success: boolean; data: Institute }> => {\n    return api.get('/api/institute-admin/institute')\n  },\n\n  /**\n   * Update institute details\n   */\n  updateDetails: async (data: UpdateInstituteData): Promise<{ success: boolean; data: Institute; message: string }> => {\n    return api.put('/api/institute-admin/institute', data)\n  },\n\n  /**\n   * Get institute statistics\n   */\n  getStats: async (branch?: string): Promise<{ success: boolean; data: InstituteStats }> => {\n    const params = branch ? { branch } : undefined\n    return api.get('/api/institute-admin/institute/stats', params)\n  },\n\n  /**\n   * Upload institute logo\n   */\n  uploadLogo: async (file: File): Promise<{ success: boolean; data: any; message: string }> => {\n    const formData = new FormData()\n    formData.append('file', file)\n    \n    const response = await fetch('/api/institute-admin/institute/logo', {\n      method: 'POST',\n      credentials: 'include',\n      body: formData\n    })\n    \n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}))\n      throw new Error(errorData.message || errorData.error || `HTTP ${response.status}`)\n    }\n    \n    return response.json()\n  },\n\n  /**\n   * Upload institute favicon\n   */\n  uploadFavicon: async (file: File): Promise<{ success: boolean; data: any; message: string }> => {\n    const formData = new FormData()\n    formData.append('file', file)\n    \n    const response = await fetch('/api/institute-admin/institute/favicon', {\n      method: 'POST',\n      credentials: 'include',\n      body: formData\n    })\n    \n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}))\n      throw new Error(errorData.message || errorData.error || `HTTP ${response.status}`)\n    }\n    \n    return response.json()\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAsCO,MAAM,eAAe;IAC1B;;GAEC,GACD,YAAY;QACV,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;IAEA;;GAEC,GACD,eAAe,OAAO;QACpB,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,kCAAkC;IACnD;IAEA;;GAEC,GACD,UAAU,OAAO;QACf,MAAM,SAAS,SAAS;YAAE;QAAO,IAAI;QACrC,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,wCAAwC;IACzD;IAEA;;GAEC,GACD,YAAY,OAAO;QACjB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,WAAW,MAAM,MAAM,uCAAuC;YAClE,QAAQ;YACR,aAAa;YACb,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;QACnF;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,eAAe,OAAO;QACpB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,WAAW,MAAM,MAAM,0CAA0C;YACrE,QAAQ;YACR,aAAa;YACb,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;QACnF;QAEA,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 9009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/institute-admin/branches.ts"], "sourcesContent": ["/**\n * Institute Admin API functions for branch management\n */\n\nimport { api, verifyAuthToken } from '../api'\n\n// Types\nexport interface Branch {\n  id: string\n  name: string\n  code: string\n  institute: string\n  location: {\n    address: string\n    country: {\n      id: string\n      name: string\n    }\n    state: {\n      id: string\n      name: string\n    }\n    district: {\n      id: string\n      name: string\n    }\n    pincode?: string\n    coordinates?: {\n      latitude?: number\n      longitude?: number\n    }\n  }\n  contact?: {\n    phone?: string\n    email?: string\n    website?: string\n  }\n  taxInformation?: {\n    gstNumber?: string\n    panNumber?: string\n    taxRegistrationNumber?: string\n    isGstRegistered?: boolean\n  }\n\n  isActive: boolean\n  isHeadOffice?: boolean\n  workingDays?: {\n    monday?: boolean\n    tuesday?: boolean\n    wednesday?: boolean\n    thursday?: boolean\n    friday?: boolean\n    saturday?: boolean\n    sunday?: boolean\n  }\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface CreateBranchData {\n  name: string\n  code?: string\n  location: {\n    address: string\n    country: string\n    state: string\n    district: string\n    pincode?: string\n    coordinates?: {\n      latitude?: number\n      longitude?: number\n    }\n  }\n  contact?: {\n    phone?: string\n    email?: string\n    website?: string\n  }\n  taxInformation?: {\n    gstNumber?: string\n    panNumber?: string\n    taxRegistrationNumber?: string\n    isGstRegistered?: boolean\n  }\n\n  isHeadOffice?: boolean\n  workingDays?: {\n    monday?: boolean\n    tuesday?: boolean\n    wednesday?: boolean\n    thursday?: boolean\n    friday?: boolean\n    saturday?: boolean\n    sunday?: boolean\n  }\n\n  // Operating hours fields (matching database columns)\n  operatingHours?: {\n    openTime?: string\n    closeTime?: string\n  }\n}\n\nexport interface UpdateBranchData {\n  name?: string\n  code?: string\n  location?: {\n    address?: string\n    country?: string\n    state?: string\n    district?: string\n    pincode?: string\n    coordinates?: {\n      latitude?: number\n      longitude?: number\n    }\n  }\n  contact?: {\n    phone?: string\n    email?: string\n    website?: string\n  }\n  taxInformation?: {\n    gstNumber?: string\n    panNumber?: string\n    taxRegistrationNumber?: string\n    isGstRegistered?: boolean\n  }\n\n  isActive?: boolean\n  isHeadOffice?: boolean\n  workingDays?: {\n    monday?: boolean\n    tuesday?: boolean\n    wednesday?: boolean\n    thursday?: boolean\n    friday?: boolean\n    saturday?: boolean\n    sunday?: boolean\n  }\n  // Soft delete fields\n  isDeleted?: boolean\n  deletedAt?: string | null\n}\n\nexport interface BranchesResponse {\n  success: boolean\n  data: Branch[]\n  pagination: {\n    page: number\n    limit: number\n    totalPages: number\n    totalDocs: number\n    hasNextPage: boolean\n    hasPrevPage: boolean\n  }\n}\n\nexport interface BranchParams {\n  page?: number\n  limit?: number\n  search?: string\n}\n\n// API Functions\nexport const branchesApi = {\n  /**\n   * Get all branches for the institute\n   */\n  getAll: async (params?: BranchParams): Promise<BranchesResponse> => {\n    const queryParams: Record<string, string> = {}\n    \n    if (params?.page) queryParams.page = params.page.toString()\n    if (params?.limit) queryParams.limit = params.limit.toString()\n    if (params?.search) queryParams.search = params.search\n    \n    return api.get('/api/institute-admin/branches', queryParams)\n  },\n\n  /**\n   * Get branch details by ID\n   */\n  getById: async (id: string): Promise<{ success: boolean; data: Branch }> => {\n    return api.get(`/api/institute-admin/branches/${id}`)\n  },\n\n  /**\n   * Create a new branch\n   */\n  create: async (data: CreateBranchData): Promise<{ success: boolean; data: Branch; message: string }> => {\n    console.log('📤 Frontend sending branch data:', JSON.stringify(data, null, 2))\n\n    // Debug: Check auth state before API call\n    const authState = verifyAuthToken()\n    console.log('🔐 Auth state before API call:', authState)\n\n    // Check if user is logged in\n    if (!authState.hasToken) {\n      console.error('❌ No auth token found! User might not be logged in.')\n      throw new Error('Authentication required. Please login again.')\n    }\n\n    const result = await api.post('/api/institute-admin/branches', data)\n    console.log('📥 Frontend received response:', result)\n    return result\n  },\n\n  /**\n   * Update branch details\n   */\n  update: async (id: string, data: UpdateBranchData): Promise<{ success: boolean; data: Branch; message: string }> => {\n    return api.put(`/api/institute-admin/branches/${id}`, data)\n  },\n\n  /**\n   * Delete a branch\n   */\n  delete: async (id: string): Promise<{ success: boolean; message: string }> => {\n    return api.delete(`/api/institute-admin/branches/${id}`)\n  },\n\n  /**\n   * Toggle branch status (active/inactive)\n   */\n  toggleStatus: async (id: string): Promise<{ success: boolean; data: Branch; message: string }> => {\n    return api.put(`/api/institute-admin/branches/${id}/toggle-status`)\n  },\n\n  /**\n   * Get branch statistics\n   */\n  getStats: async (branchId?: string): Promise<{\n    success: boolean;\n    data: {\n      totalStudents: number\n      totalStaff: number\n      totalCourses: number\n      activeEnrollments: number\n      monthlyRevenue: number\n      recentActivities: any[]\n    }\n  }> => {\n    const url = branchId\n      ? `/api/institute-admin/branches/${branchId}/stats`\n      : '/api/institute-admin/branches/stats'\n    return api.get(url)\n  },\n\n  /**\n   * Get branch students\n   */\n  getStudents: async (branchId: string, params?: BranchParams): Promise<{\n    success: boolean\n    data: any[]\n    pagination: {\n      page: number\n      limit: number\n      totalPages: number\n      totalDocs: number\n      hasNextPage: boolean\n      hasPrevPage: boolean\n    }\n  }> => {\n    const queryParams: Record<string, string> = {}\n\n    if (params?.page) queryParams.page = params.page.toString()\n    if (params?.limit) queryParams.limit = params.limit.toString()\n    if (params?.search) queryParams.search = params.search\n\n    return api.get(`/api/institute-admin/branches/${branchId}/students`, queryParams)\n  },\n\n  /**\n   * Get branch staff\n   */\n  getStaff: async (branchId: string, params?: BranchParams): Promise<{\n    success: boolean\n    data: any[]\n    pagination: {\n      page: number\n      limit: number\n      totalPages: number\n      totalDocs: number\n      hasNextPage: boolean\n      hasPrevPage: boolean\n    }\n  }> => {\n    const queryParams: Record<string, string> = {}\n\n    if (params?.page) queryParams.page = params.page.toString()\n    if (params?.limit) queryParams.limit = params.limit.toString()\n    if (params?.search) queryParams.search = params.search\n\n    return api.get(`/api/institute-admin/branches/${branchId}/staff`, queryParams)\n  },\n\n  /**\n   * Check authorization status\n   */\n  checkAuth: () => verifyAuthToken()\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAiKO,MAAM,cAAc;IACzB;;GAEC,GACD,QAAQ,OAAO;QACb,MAAM,cAAsC,CAAC;QAE7C,IAAI,QAAQ,MAAM,YAAY,IAAI,GAAG,OAAO,IAAI,CAAC,QAAQ;QACzD,IAAI,QAAQ,OAAO,YAAY,KAAK,GAAG,OAAO,KAAK,CAAC,QAAQ;QAC5D,IAAI,QAAQ,QAAQ,YAAY,MAAM,GAAG,OAAO,MAAM;QAEtD,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,iCAAiC;IAClD;IAEA;;GAEC,GACD,SAAS,OAAO;QACd,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,IAAI;IACtD;IAEA;;GAEC,GACD,QAAQ,OAAO;QACb,QAAQ,GAAG,CAAC,oCAAoC,KAAK,SAAS,CAAC,MAAM,MAAM;QAE3E,0CAA0C;QAC1C,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD;QAChC,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,6BAA6B;QAC7B,IAAI,CAAC,UAAU,QAAQ,EAAE;YACvB,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,MAAM,wIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,iCAAiC;QAC/D,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO;IACT;IAEA;;GAEC,GACD,QAAQ,OAAO,IAAY;QACzB,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,IAAI,EAAE;IACxD;IAEA;;GAEC,GACD,QAAQ,OAAO;QACb,OAAO,wIAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,8BAA8B,EAAE,IAAI;IACzD;IAEA;;GAEC,GACD,cAAc,OAAO;QACnB,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,GAAG,cAAc,CAAC;IACpE;IAEA;;GAEC,GACD,UAAU,OAAO;QAWf,MAAM,MAAM,WACR,CAAC,8BAA8B,EAAE,SAAS,MAAM,CAAC,GACjD;QACJ,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;IAEA;;GAEC,GACD,aAAa,OAAO,UAAkB;QAYpC,MAAM,cAAsC,CAAC;QAE7C,IAAI,QAAQ,MAAM,YAAY,IAAI,GAAG,OAAO,IAAI,CAAC,QAAQ;QACzD,IAAI,QAAQ,OAAO,YAAY,KAAK,GAAG,OAAO,KAAK,CAAC,QAAQ;QAC5D,IAAI,QAAQ,QAAQ,YAAY,MAAM,GAAG,OAAO,MAAM;QAEtD,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,SAAS,SAAS,CAAC,EAAE;IACvE;IAEA;;GAEC,GACD,UAAU,OAAO,UAAkB;QAYjC,MAAM,cAAsC,CAAC;QAE7C,IAAI,QAAQ,MAAM,YAAY,IAAI,GAAG,OAAO,IAAI,CAAC,QAAQ;QACzD,IAAI,QAAQ,OAAO,YAAY,KAAK,GAAG,OAAO,KAAK,CAAC,QAAQ;QAC5D,IAAI,QAAQ,QAAQ,YAAY,MAAM,GAAG,OAAO,MAAM;QAEtD,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,SAAS,MAAM,CAAC,EAAE;IACpE;IAEA;;GAEC,GACD,WAAW,IAAM,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD;AACjC", "debugId": null}}, {"offset": {"line": 9099, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/institute-admin/domain.ts"], "sourcesContent": ["/**\n * Institute Admin API functions for domain management\n */\n\nimport { api } from '../api'\n\n// Types\nexport interface DomainRequest {\n  id: string\n  domainName: string\n  status: 'pending' | 'reviewing' | 'approved' | 'rejected' | 'active'\n  requestedAt: string\n  reviewedAt?: string\n  reviewedBy?: any\n  rejectionReason?: string\n  notes?: string\n  dnsConfiguration?: {\n    aRecord?: string\n    cnameRecord?: string\n    txtRecord?: string\n  }\n  sslStatus?: 'not_configured' | 'pending' | 'active' | 'failed'\n}\n\nexport interface CreateDomainRequestData {\n  domainName: string\n  purpose?: string\n}\n\n// API Functions\nexport const domainApi = {\n  /**\n   * Create a new domain request\n   */\n  createRequest: async (data: CreateDomainRequestData): Promise<{ \n    success: boolean\n    data: DomainRequest\n    message: string \n  }> => {\n    return api.post('/api/institute-admin/domain-request', data)\n  },\n\n  /**\n   * Get domain request status\n   */\n  getStatus: async (): Promise<{ \n    success: boolean\n    data: DomainRequest | null\n    message?: string \n  }> => {\n    return api.get('/api/institute-admin/domain-request/status')\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AA0BO,MAAM,YAAY;IACvB;;GAEC,GACD,eAAe,OAAO;QAKpB,OAAO,wIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,uCAAuC;IACzD;IAEA;;GAEC,GACD,WAAW;QAKT,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;AACF", "debugId": null}}, {"offset": {"line": 9127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/institute-admin/index.ts"], "sourcesContent": ["/**\n * Institute Admin API exports\n */\n\nexport * from './institute'\nexport * from './branches'\nexport * from './domain'\n"], "names": [], "mappings": "AAAA;;CAEC;AAED;AACA;AACA", "debugId": null}}, {"offset": {"line": 9156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/institute/useInstituteStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools, persist } from 'zustand/middleware'\nimport { toast } from 'sonner'\nimport { useBranchStore } from './useBranchStore'\nimport { instituteApi, branchesApi, type Institute as ApiInstitute, type UpdateInstituteData, type Branch as ApiBranch } from '@/lib/institute-admin'\nimport { api } from '@/lib/api'\n\n// Use the API Branch type\ntype Branch = ApiBranch\n\ninterface Student {\n  id: string\n  firstName: string\n  lastName: string\n  email: string\n  phone: string\n  branch: any\n  lastActivity: string\n  status: 'active' | 'inactive' | 'suspended'\n  createdAt: string\n}\n\ninterface InstituteStats {\n  totalStudents: number\n  activeStudents: number\n  totalCourses: number\n  publishedCourses: number\n  totalBranches: number\n  activeBranches: number\n  monthlyRevenue: number\n  totalRevenue: number\n  enrollmentsThisMonth: number\n  completionRate: number\n}\n\ninterface InstituteState {\n  // Data\n  institute: any | null\n  branches: Branch[]\n  students: Student[]\n  instituteStats: InstituteStats\n  selectedBranch: Branch | null\n  selectedStudent: Student | null\n\n  // UI State\n  isLoading: boolean\n  error: string | null\n\n  // Filters\n  studentFilters: {\n    search?: string\n    branch?: string\n    status?: string\n  }\n  branchFilters: {\n    search?: string\n    status?: string\n  }\n\n  // Actions\n  setSelectedBranch: (branch: Branch | null) => void\n  setSelectedStudent: (student: Student | null) => void\n  setStudentFilters: (filters: Partial<typeof studentFilters>) => void\n  setBranchFilters: (filters: Partial<typeof branchFilters>) => void\n\n  // API Actions\n  fetchInstituteData: (force?: boolean) => Promise<void>\n  fetchBranches: () => Promise<void>\n  fetchStudents: (branchId?: string) => Promise<void>\n  fetchInstituteStats: (branchId?: string) => Promise<void>\n  \n  createBranch: (branchData: any) => Promise<void>\n  updateBranch: (id: string, branchData: any) => Promise<void>\n  deleteBranch: (id: string) => Promise<void>\n  \n  createStudent: (studentData: Partial<Student>) => Promise<void>\n  updateStudent: (id: string, studentData: Partial<Student>) => Promise<void>\n  deleteStudent: (id: string) => Promise<void>\n  \n  updateInstituteProfile: (profileData: any) => Promise<void>\n\n  // Utility Actions\n  clearError: () => void\n}\n\nconst initialStats: InstituteStats = {\n  totalStudents: 0,\n  activeStudents: 0,\n  totalCourses: 0,\n  publishedCourses: 0,\n  totalBranches: 0,\n  activeBranches: 0,\n  monthlyRevenue: 0,\n  totalRevenue: 0,\n  enrollmentsThisMonth: 0,\n  completionRate: 0\n}\n\nexport const useInstituteStore = create<InstituteState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n      // Initial State\n      institute: null,\n      branches: [],\n      students: [],\n      instituteStats: initialStats,\n      selectedBranch: null,\n      selectedStudent: null,\n      isLoading: false,\n      error: null,\n      studentFilters: {},\n      branchFilters: {},\n\n      // UI Actions\n      setSelectedBranch: (branch) => set({ selectedBranch: branch }),\n      setSelectedStudent: (student) => set({ selectedStudent: student }),\n      setStudentFilters: (filters) => set((state) => ({\n        studentFilters: { ...state.studentFilters, ...filters }\n      })),\n      setBranchFilters: (filters) => set((state) => ({\n        branchFilters: { ...state.branchFilters, ...filters }\n      })),\n\n      // API Actions\n      fetchInstituteData: async (force = false) => {\n        const { institute } = get()\n\n        // Skip fetch if data already exists and not forced\n        if (institute && !force) {\n          console.log('📦 Institute data already cached, skipping API call')\n          return\n        }\n\n        try {\n          console.log('🔄 Fetching institute data from API...')\n          const response = await instituteApi.getDetails()\n\n          if (response.success) {\n            set({ institute: response.data })\n            console.log('✅ Institute data fetched and cached')\n          } else {\n            throw new Error('Failed to fetch institute data')\n          }\n        } catch (error) {\n          console.error('Failed to fetch institute data:', error)\n          set({ error: error instanceof Error ? error.message : 'Failed to fetch institute data' })\n        }\n      },\n\n      fetchBranches: async () => {\n        set({ isLoading: true, error: null })\n        try {\n          const data = await branchesApi.getAll()\n\n          if (data.success) {\n            set({\n              branches: data.data,\n              isLoading: false\n            })\n          } else {\n            throw new Error('Failed to fetch branches')\n          }\n        } catch (error) {\n          set({\n            error: error instanceof Error ? error.message : 'Unknown error',\n            isLoading: false\n          })\n          toast.error('Failed to fetch branches')\n        }\n      },\n\n      fetchStudents: async (branchId?: string) => {\n        set({ isLoading: true, error: null })\n        try {\n          const { studentFilters } = get()\n          const params = new URLSearchParams({\n            ...(studentFilters.search && { search: studentFilters.search }),\n            ...(branchId && { branch: branchId }),\n            ...(studentFilters.status && { status: studentFilters.status })\n          })\n\n          const data = await api.get(`/api/institute-admin/students?${params}`)\n\n          if (data.success) {\n            set({\n              students: data.data,\n              isLoading: false\n            })\n          } else {\n            throw new Error(data.error || 'Failed to fetch students')\n          }\n        } catch (error) {\n          set({\n            error: error instanceof Error ? error.message : 'Unknown error',\n            isLoading: false\n          })\n          toast.error('Failed to fetch students')\n        }\n      },\n\n      fetchInstituteStats: async (branchId?: string) => {\n        try {\n          const params = new URLSearchParams()\n          if (branchId) {\n            params.append('branch', branchId)\n          }\n\n          const data = await instituteApi.getStats(branchId)\n\n          if (data.success) {\n            set({ instituteStats: data.data })\n          } else {\n            throw new Error('Failed to fetch institute stats')\n          }\n        } catch (error) {\n          console.error('Failed to fetch institute stats:', error)\n        }\n      },\n\n      createBranch: async (branchData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const data = await branchesApi.create(branchData)\n\n          if (data.success) {\n            await get().fetchBranches()\n            await get().fetchInstituteStats()\n            toast.success('Branch Created', {\n              description: `Branch \"${data.data.name}\" has been created successfully.`\n            })\n            set({ isLoading: false })\n          } else {\n            throw new Error('Failed to create branch')\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Unknown error'\n          set({ error: errorMessage, isLoading: false })\n          toast.error('Failed to create branch', {\n            description: errorMessage\n          })\n          throw error\n        }\n      },\n\n      updateBranch: async (id, branchData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const data = await branchesApi.update(id, branchData)\n\n          if (data.success) {\n            // Update branch in local state\n            set((state) => ({\n              branches: state.branches.map(branch =>\n                branch.id === id ? { ...branch, ...data.data } : branch\n              ),\n              selectedBranch: state.selectedBranch?.id === id\n                ? { ...state.selectedBranch, ...data.data }\n                : state.selectedBranch,\n              isLoading: false\n            }))\n\n            toast.success('Branch Updated', {\n              description: `Branch \"${data.data.name}\" has been updated successfully.`\n            })\n          } else {\n            throw new Error('Failed to update branch')\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Unknown error'\n          set({ error: errorMessage, isLoading: false })\n          toast.error('Failed to update branch', {\n            description: errorMessage\n          })\n          throw error\n        }\n      },\n\n      deleteBranch: async (id) => {\n        set({ isLoading: true, error: null })\n        try {\n          const data = await branchesApi.delete(id)\n\n          if (data.success) {\n            // Remove branch from local state\n            set((state) => ({\n              branches: state.branches.filter(branch => branch.id !== id),\n              selectedBranch: state.selectedBranch?.id === id ? null : state.selectedBranch,\n              isLoading: false\n            }))\n\n            await get().fetchInstituteStats()\n            toast.success('Branch Deleted', {\n              description: 'Branch has been deleted successfully.'\n            })\n          } else {\n            throw new Error('Failed to delete branch')\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Unknown error'\n          set({ error: errorMessage, isLoading: false })\n          toast.error('Failed to delete branch', {\n            description: errorMessage\n          })\n          throw error\n        }\n      },\n\n      createStudent: async (studentData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const data = await api.post('/api/institute-admin/students', studentData)\n\n          if (data.success) {\n            await get().fetchStudents()\n            await get().fetchInstituteStats()\n            toast.success('Student Created', {\n              description: `Student \"${data.data.firstName} ${data.data.lastName}\" has been created successfully.`\n            })\n            set({ isLoading: false })\n          } else {\n            throw new Error('Failed to create student')\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Unknown error'\n          set({ error: errorMessage, isLoading: false })\n          toast.error('Failed to create student', {\n            description: errorMessage\n          })\n          throw error\n        }\n      },\n\n      updateStudent: async (id, studentData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const data = await api.put(`/api/institute-admin/students/${id}`, studentData)\n\n          if (data.success) {\n            // Update student in local state\n            set((state) => ({\n              students: state.students.map(student =>\n                student.id === id ? { ...student, ...data.data } : student\n              ),\n              selectedStudent: state.selectedStudent?.id === id\n                ? { ...state.selectedStudent, ...data.data }\n                : state.selectedStudent,\n              isLoading: false\n            }))\n\n            toast.success('Student Updated', {\n              description: `Student has been updated successfully.`\n            })\n          } else {\n            throw new Error('Failed to update student')\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Unknown error'\n          set({ error: errorMessage, isLoading: false })\n          toast.error('Failed to update student', {\n            description: errorMessage\n          })\n          throw error\n        }\n      },\n\n      deleteStudent: async (id) => {\n        set({ isLoading: true, error: null })\n        try {\n          const data = await api.delete(`/api/institute-admin/students/${id}`)\n\n          if (data.success) {\n            // Remove student from local state\n            set((state) => ({\n              students: state.students.filter(student => student.id !== id),\n              selectedStudent: state.selectedStudent?.id === id ? null : state.selectedStudent,\n              isLoading: false\n            }))\n\n            await get().fetchInstituteStats()\n            toast.success('Student Deleted', {\n              description: 'Student has been deleted successfully.'\n            })\n          } else {\n            throw new Error('Failed to delete student')\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Unknown error'\n          set({ error: errorMessage, isLoading: false })\n          toast.error('Failed to delete student', {\n            description: errorMessage\n          })\n          throw error\n        }\n      },\n\n      updateInstituteProfile: async (profileData: UpdateInstituteData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await instituteApi.updateDetails(profileData)\n\n          if (response.success) {\n            set({\n              institute: { ...get().institute, ...response.data },\n              isLoading: false\n            })\n\n            toast.success('Profile Updated', {\n              description: response.message || 'Institute profile has been updated successfully.'\n            })\n          } else {\n            throw new Error('Failed to update profile')\n          }\n        } catch (error) {\n          const errorMessage = error instanceof Error ? error.message : 'Unknown error'\n          set({ error: errorMessage, isLoading: false })\n          toast.error('Failed to update profile', {\n            description: errorMessage\n          })\n          throw error\n        }\n      },\n\n      // Utility Actions\n      clearError: () => set({ error: null })\n    }),\n    {\n      name: 'institute-storage',\n      // Only persist essential data, not loading states\n      partialize: (state) => ({\n        institute: state.institute,\n        branches: state.branches,\n        students: state.students,\n        instituteStats: state.instituteStats\n      })\n    }\n  ),\n  {\n    name: 'institute-store'\n  }\n))\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AACA;;;;;;AAgFA,MAAM,eAA+B;IACnC,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,kBAAkB;IAClB,eAAe;IACf,gBAAgB;IAChB,gBAAgB;IAChB,cAAc;IACd,sBAAsB;IACtB,gBAAgB;AAClB;AAEO,MAAM,oBAAoB,CAAA,GAAA,wPAAA,CAAA,SAAM,AAAD,IACpC,CAAA,GAAA,6PAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACf,gBAAgB;QAChB,WAAW;QACX,UAAU,EAAE;QACZ,UAAU,EAAE;QACZ,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,WAAW;QACX,OAAO;QACP,gBAAgB,CAAC;QACjB,eAAe,CAAC;QAEhB,aAAa;QACb,mBAAmB,CAAC,SAAW,IAAI;gBAAE,gBAAgB;YAAO;QAC5D,oBAAoB,CAAC,UAAY,IAAI;gBAAE,iBAAiB;YAAQ;QAChE,mBAAmB,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC9C,gBAAgB;wBAAE,GAAG,MAAM,cAAc;wBAAE,GAAG,OAAO;oBAAC;gBACxD,CAAC;QACD,kBAAkB,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC7C,eAAe;wBAAE,GAAG,MAAM,aAAa;wBAAE,GAAG,OAAO;oBAAC;gBACtD,CAAC;QAED,cAAc;QACd,oBAAoB,OAAO,QAAQ,KAAK;YACtC,MAAM,EAAE,SAAS,EAAE,GAAG;YAEtB,mDAAmD;YACnD,IAAI,aAAa,CAAC,OAAO;gBACvB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW,MAAM,oKAAA,CAAA,eAAY,CAAC,UAAU;gBAE9C,IAAI,SAAS,OAAO,EAAE;oBACpB,IAAI;wBAAE,WAAW,SAAS,IAAI;oBAAC;oBAC/B,QAAQ,GAAG,CAAC;gBACd,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,IAAI;oBAAE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAAiC;YACzF;QACF;QAEA,eAAe;YACb,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,OAAO,MAAM,mKAAA,CAAA,cAAW,CAAC,MAAM;gBAErC,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBACF,UAAU,KAAK,IAAI;wBACnB,WAAW;oBACb;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,EAAE,cAAc,EAAE,GAAG;gBAC3B,MAAM,SAAS,IAAI,gBAAgB;oBACjC,GAAI,eAAe,MAAM,IAAI;wBAAE,QAAQ,eAAe,MAAM;oBAAC,CAAC;oBAC9D,GAAI,YAAY;wBAAE,QAAQ;oBAAS,CAAC;oBACpC,GAAI,eAAe,MAAM,IAAI;wBAAE,QAAQ,eAAe,MAAM;oBAAC,CAAC;gBAChE;gBAEA,MAAM,OAAO,MAAM,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,QAAQ;gBAEpE,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBACF,UAAU,KAAK,IAAI;wBACnB,WAAW;oBACb;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,qBAAqB,OAAO;YAC1B,IAAI;gBACF,MAAM,SAAS,IAAI;gBACnB,IAAI,UAAU;oBACZ,OAAO,MAAM,CAAC,UAAU;gBAC1B;gBAEA,MAAM,OAAO,MAAM,oKAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;gBAEzC,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBAAE,gBAAgB,KAAK,IAAI;oBAAC;gBAClC,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA,cAAc,OAAO;YACnB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,OAAO,MAAM,mKAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBAEtC,IAAI,KAAK,OAAO,EAAE;oBAChB,MAAM,MAAM,aAAa;oBACzB,MAAM,MAAM,mBAAmB;oBAC/B,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;wBAC9B,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC;oBAC1E;oBACA,IAAI;wBAAE,WAAW;oBAAM;gBACzB,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;oBACrC,aAAa;gBACf;gBACA,MAAM;YACR;QACF;QAEA,cAAc,OAAO,IAAI;YACvB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,OAAO,MAAM,mKAAA,CAAA,cAAW,CAAC,MAAM,CAAC,IAAI;gBAE1C,IAAI,KAAK,OAAO,EAAE;oBAChB,+BAA+B;oBAC/B,IAAI,CAAC,QAAU,CAAC;4BACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,SAC3B,OAAO,EAAE,KAAK,KAAK;oCAAE,GAAG,MAAM;oCAAE,GAAG,KAAK,IAAI;gCAAC,IAAI;4BAEnD,gBAAgB,MAAM,cAAc,EAAE,OAAO,KACzC;gCAAE,GAAG,MAAM,cAAc;gCAAE,GAAG,KAAK,IAAI;4BAAC,IACxC,MAAM,cAAc;4BACxB,WAAW;wBACb,CAAC;oBAED,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;wBAC9B,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC;oBAC1E;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;oBACrC,aAAa;gBACf;gBACA,MAAM;YACR;QACF;QAEA,cAAc,OAAO;YACnB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,OAAO,MAAM,mKAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBAEtC,IAAI,KAAK,OAAO,EAAE;oBAChB,iCAAiC;oBACjC,IAAI,CAAC,QAAU,CAAC;4BACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;4BACxD,gBAAgB,MAAM,cAAc,EAAE,OAAO,KAAK,OAAO,MAAM,cAAc;4BAC7E,WAAW;wBACb,CAAC;oBAED,MAAM,MAAM,mBAAmB;oBAC/B,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;wBAC9B,aAAa;oBACf;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;oBACrC,aAAa;gBACf;gBACA,MAAM;YACR;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,OAAO,MAAM,wIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,iCAAiC;gBAE7D,IAAI,KAAK,OAAO,EAAE;oBAChB,MAAM,MAAM,aAAa;oBACzB,MAAM,MAAM,mBAAmB;oBAC/B,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;wBAC/B,aAAa,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,gCAAgC,CAAC;oBACtG;oBACA,IAAI;wBAAE,WAAW;oBAAM;gBACzB,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC,4BAA4B;oBACtC,aAAa;gBACf;gBACA,MAAM;YACR;QACF;QAEA,eAAe,OAAO,IAAI;YACxB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,OAAO,MAAM,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,IAAI,EAAE;gBAElE,IAAI,KAAK,OAAO,EAAE;oBAChB,gCAAgC;oBAChC,IAAI,CAAC,QAAU,CAAC;4BACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,UAC3B,QAAQ,EAAE,KAAK,KAAK;oCAAE,GAAG,OAAO;oCAAE,GAAG,KAAK,IAAI;gCAAC,IAAI;4BAErD,iBAAiB,MAAM,eAAe,EAAE,OAAO,KAC3C;gCAAE,GAAG,MAAM,eAAe;gCAAE,GAAG,KAAK,IAAI;4BAAC,IACzC,MAAM,eAAe;4BACzB,WAAW;wBACb,CAAC;oBAED,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;wBAC/B,aAAa,CAAC,sCAAsC,CAAC;oBACvD;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC,4BAA4B;oBACtC,aAAa;gBACf;gBACA,MAAM;YACR;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,OAAO,MAAM,wIAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,8BAA8B,EAAE,IAAI;gBAEnE,IAAI,KAAK,OAAO,EAAE;oBAChB,kCAAkC;oBAClC,IAAI,CAAC,QAAU,CAAC;4BACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;4BAC1D,iBAAiB,MAAM,eAAe,EAAE,OAAO,KAAK,OAAO,MAAM,eAAe;4BAChF,WAAW;wBACb,CAAC;oBAED,MAAM,MAAM,mBAAmB;oBAC/B,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;wBAC/B,aAAa;oBACf;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC,4BAA4B;oBACtC,aAAa;gBACf;gBACA,MAAM;YACR;QACF;QAEA,wBAAwB,OAAO;YAC7B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,oKAAA,CAAA,eAAY,CAAC,aAAa,CAAC;gBAElD,IAAI,SAAS,OAAO,EAAE;oBACpB,IAAI;wBACF,WAAW;4BAAE,GAAG,MAAM,SAAS;4BAAE,GAAG,SAAS,IAAI;wBAAC;wBAClD,WAAW;oBACb;oBAEA,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;wBAC/B,aAAa,SAAS,OAAO,IAAI;oBACnC;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC,4BAA4B;oBACtC,aAAa;gBACf;gBACA,MAAM;YACR;QACF;QAEA,kBAAkB;QAClB,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC,GACD;IACE,MAAM;IACN,kDAAkD;IAClD,YAAY,CAAC,QAAU,CAAC;YACtB,WAAW,MAAM,SAAS;YAC1B,UAAU,MAAM,QAAQ;YACxB,UAAU,MAAM,QAAQ;YACxB,gBAAgB,MAAM,cAAc;QACtC,CAAC;AACH,IAEF;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 9578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/institute/useBranchStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools } from 'zustand/middleware'\nimport { toast } from 'sonner'\nimport { branchesApi, type Branch, type CreateBranchData, type UpdateBranchData } from '@/lib/institute-admin'\n\ninterface PaginationInfo {\n  page: number\n  limit: number\n  totalPages: number\n  totalDocs: number\n  hasNextPage: boolean\n  hasPrevPage: boolean\n}\n\ninterface BranchState {\n  // Data state\n  branches: Branch[]\n  selectedBranch: Branch | null\n  pagination: PaginationInfo\n\n  // Loading states\n  isLoading: boolean\n  isCreating: boolean\n  isUpdating: boolean\n  isDeleting: boolean\n\n  // UI state\n  showCreateBranchModal: boolean\n  showEditBranchModal: boolean\n  showDeleteConfirmModal: boolean\n  editingBranch: Branch | null\n  deletingBranch: Branch | null\n\n  // Filters and search\n  searchQuery: string\n  statusFilter: 'all' | 'active' | 'inactive'\n\n  // Actions - Data operations\n  fetchBranches: (params?: { page?: number; limit?: number; search?: string }) => Promise<void>\n  createBranch: (data: CreateBranchData) => Promise<void>\n  updateBranch: (id: string, data: UpdateBranchData) => Promise<void>\n  deleteBranch: (id: string) => Promise<void>\n  toggleBranchStatus: (id: string) => Promise<void>\n\n  // Actions - UI state\n  setSelectedBranch: (branch: Branch | null) => void\n  setShowCreateBranchModal: (show: boolean) => void\n  setShowEditBranchModal: (show: boolean) => void\n  setShowDeleteConfirmModal: (show: boolean) => void\n  setEditingBranch: (branch: Branch | null) => void\n  setDeletingBranch: (branch: Branch | null) => void\n  setSearchQuery: (query: string) => void\n  setStatusFilter: (filter: 'all' | 'active' | 'inactive') => void\n\n  // Utility functions\n  clearSelection: () => void\n  isAllBranchesView: () => boolean\n  getSelectedBranchId: () => string | null\n  getBranchById: (id: string) => Branch | undefined\n  getActiveBranches: () => Branch[]\n  getInactiveBranches: () => Branch[]\n  resetFilters: () => void\n}\n\nexport const useBranchStore = create<BranchState>()(\n  devtools(\n    (set, get) => ({\n      // Initial state\n      branches: [],\n      selectedBranch: null,\n      pagination: {\n        page: 1,\n        limit: 20,\n        totalPages: 0,\n        totalDocs: 0,\n        hasNextPage: false,\n        hasPrevPage: false,\n      },\n\n      // Loading states\n      isLoading: false,\n      isCreating: false,\n      isUpdating: false,\n      isDeleting: false,\n\n      // UI state\n      showCreateBranchModal: false,\n      showEditBranchModal: false,\n      showDeleteConfirmModal: false,\n      editingBranch: null,\n      deletingBranch: null,\n\n      // Filters\n      searchQuery: '',\n      statusFilter: 'all',\n\n      // Data operations\n      fetchBranches: async (params) => {\n        set({ isLoading: true })\n        try {\n          const response = await branchesApi.getAll({\n            page: params?.page || get().pagination.page,\n            limit: params?.limit || get().pagination.limit,\n            search: params?.search || get().searchQuery,\n          })\n\n          if (response.success) {\n            set({\n              branches: response.data,\n              pagination: response.pagination,\n              isLoading: false,\n            })\n          } else {\n            throw new Error('Failed to fetch branches')\n          }\n        } catch (error) {\n          console.error('Error fetching branches:', error)\n          toast.error('Failed to load branches')\n          set({ isLoading: false })\n        }\n      },\n\n      createBranch: async (data) => {\n        set({ isCreating: true })\n        try {\n          // Debug: Check authorization before making request\n          const authCheck = branchesApi.checkAuth?.() || { hasToken: false, tokenInfo: 'No auth check available' }\n          console.log('🔐 Branch Creation Auth Check:', authCheck)\n\n          // Additional debug: Check full auth state\n          if (typeof window !== 'undefined') {\n            console.log('🔍 Full Auth State:', {\n              directToken: localStorage.getItem('auth_token') ? 'EXISTS' : 'MISSING',\n              authStorage: localStorage.getItem('auth-storage') ? 'EXISTS' : 'MISSING',\n              userData: localStorage.getItem('user_data') ? 'EXISTS' : 'MISSING'\n            })\n          }\n\n          const response = await branchesApi.create(data)\n\n          if (response.success) {\n            // Add new branch to the list\n            set((state) => ({\n              branches: [response.data, ...state.branches],\n              isCreating: false,\n              showCreateBranchModal: false,\n            }))\n\n            toast.success(response.message || 'Branch created successfully!')\n\n            // Refresh the list to get updated data\n            get().fetchBranches()\n          } else {\n            throw new Error('Failed to create branch')\n          }\n        } catch (error: any) {\n          console.error('Error creating branch:', error)\n\n          // Provide specific error messages for auth issues\n          if (error.message?.includes('401') || error.message?.includes('Authorization')) {\n            toast.error('Authentication failed. Please login again.')\n          } else if (error.message?.includes('403') || error.message?.includes('forbidden')) {\n            toast.error('Access denied. Insufficient permissions.')\n          } else {\n            toast.error(error.message || 'Failed to create branch')\n          }\n\n          set({ isCreating: false })\n        }\n      },\n\n      updateBranch: async (id, data) => {\n        set({ isUpdating: true })\n        try {\n          const response = await branchesApi.update(id, data)\n\n          if (response.success) {\n            // Update branch in the list\n            set((state) => ({\n              branches: state.branches.map((branch) =>\n                branch.id === id ? { ...branch, ...response.data } : branch\n              ),\n              isUpdating: false,\n              showEditBranchModal: false,\n              editingBranch: null,\n            }))\n\n            // Update selected branch if it's the one being edited\n            const currentSelected = get().selectedBranch\n            if (currentSelected && currentSelected.id === id) {\n              set({ selectedBranch: { ...currentSelected, ...response.data } })\n            }\n\n            toast.success(response.message || 'Branch updated successfully!')\n          } else {\n            throw new Error('Failed to update branch')\n          }\n        } catch (error) {\n          console.error('Error updating branch:', error)\n          toast.error('Failed to update branch')\n          set({ isUpdating: false })\n        }\n      },\n\n      deleteBranch: async (id) => {\n        set({ isDeleting: true })\n        try {\n          const response = await branchesApi.delete(id)\n\n          if (response.success) {\n            // Remove branch from the list\n            set((state) => ({\n              branches: state.branches.filter((branch) => branch.id !== id),\n              isDeleting: false,\n              showDeleteConfirmModal: false,\n              deletingBranch: null,\n            }))\n\n            // Clear selection if deleted branch was selected\n            const currentSelected = get().selectedBranch\n            if (currentSelected && currentSelected.id === id) {\n              get().clearSelection()\n            }\n\n            toast.success(response.message || 'Branch deleted successfully!')\n          } else {\n            throw new Error('Failed to delete branch')\n          }\n        } catch (error) {\n          console.error('Error deleting branch:', error)\n          toast.error('Failed to delete branch')\n          set({ isDeleting: false })\n        }\n      },\n\n      // Soft delete branch (set is_deleted flag)\n      softDeleteBranch: async (id: string) => {\n        set({ isDeleting: true })\n        try {\n          // Update branch with is_deleted flag\n          const response = await branchesApi.update(id, {\n            isActive: false,\n            isDeleted: true,\n            deletedAt: new Date().toISOString()\n          })\n\n          if (response.success) {\n            // Remove from local state\n            set((state) => ({\n              branches: state.branches.filter((branch) => branch.id !== id),\n              isDeleting: false,\n              showDeleteConfirmModal: false,\n              deletingBranch: null,\n            }))\n\n            // Clear selection if deleted branch was selected\n            const currentSelected = get().selectedBranch\n            if (currentSelected && currentSelected.id === id) {\n              get().clearSelection()\n            }\n\n            toast.success('Branch moved to trash')\n          } else {\n            throw new Error('Failed to delete branch')\n          }\n        } catch (error) {\n          console.error('Error soft deleting branch:', error)\n          toast.error('Failed to delete branch')\n          set({ isDeleting: false })\n        }\n      },\n\n      // Restore soft deleted branch\n      restoreBranch: async (id: string) => {\n        set({ isUpdating: true })\n        try {\n          const response = await branchesApi.update(id, {\n            isActive: true,\n            isDeleted: false,\n            deletedAt: null\n          })\n\n          if (response.success) {\n            // Refresh branches list\n            await get().fetchBranches()\n            toast.success('Branch restored successfully')\n          } else {\n            throw new Error('Failed to restore branch')\n          }\n        } catch (error) {\n          console.error('Error restoring branch:', error)\n          toast.error('Failed to restore branch')\n          set({ isUpdating: false })\n        }\n      },\n\n      toggleBranchStatus: async (id) => {\n        try {\n          const response = await branchesApi.toggleStatus(id)\n\n          if (response.success) {\n            // Update branch status in the list\n            set((state) => ({\n              branches: state.branches.map((branch) =>\n                branch.id === id ? { ...branch, isActive: !branch.isActive } : branch\n              ),\n            }))\n\n            // Update selected branch if it's the one being toggled\n            const currentSelected = get().selectedBranch\n            if (currentSelected && currentSelected.id === id) {\n              set({ selectedBranch: { ...currentSelected, isActive: !currentSelected.isActive } })\n            }\n\n            toast.success(response.message || 'Branch status updated successfully!')\n          } else {\n            throw new Error('Failed to toggle branch status')\n          }\n        } catch (error) {\n          console.error('Error toggling branch status:', error)\n          toast.error('Failed to update branch status')\n        }\n      },\n\n      // UI Actions\n      setSelectedBranch: (branch) => {\n        set({ selectedBranch: branch })\n\n        // Store in localStorage for persistence\n        if (branch) {\n          localStorage.setItem('selectedBranch', JSON.stringify(branch))\n        } else {\n          localStorage.removeItem('selectedBranch')\n        }\n      },\n\n      setShowCreateBranchModal: (show) => set({ showCreateBranchModal: show }),\n\n      setShowEditBranchModal: (show) => set({\n        showEditBranchModal: show,\n        editingBranch: show ? get().editingBranch : null\n      }),\n\n      setShowDeleteConfirmModal: (show) => set({\n        showDeleteConfirmModal: show,\n        deletingBranch: show ? get().deletingBranch : null\n      }),\n\n      setEditingBranch: (branch) => set({ editingBranch: branch }),\n      setDeletingBranch: (branch) => set({ deletingBranch: branch }),\n      setSearchQuery: (query) => set({ searchQuery: query }),\n      setStatusFilter: (filter) => set({ statusFilter: filter }),\n\n      // Utility functions\n      clearSelection: () => {\n        set({ selectedBranch: null })\n        localStorage.removeItem('selectedBranch')\n      },\n\n      isAllBranchesView: () => get().selectedBranch === null,\n\n      getSelectedBranchId: () => get().selectedBranch?.id || null,\n\n      getBranchById: (id) => get().branches.find((branch) => branch.id === id),\n\n      getActiveBranches: () => get().branches.filter((branch) => branch.isActive),\n\n      getInactiveBranches: () => get().branches.filter((branch) => !branch.isActive),\n\n      resetFilters: () => set({ searchQuery: '', statusFilter: 'all' }),\n    }),\n    {\n      name: 'branch-store',\n    }\n  )\n)\n\n// Initialize from localStorage on app start\nif (typeof window !== 'undefined') {\n  const storedBranch = localStorage.getItem('selectedBranch')\n  if (storedBranch) {\n    try {\n      const branch = JSON.parse(storedBranch)\n      useBranchStore.getState().setSelectedBranch(branch)\n    } catch (error) {\n      console.error('Failed to parse stored branch:', error)\n      localStorage.removeItem('selectedBranch')\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAAA;;;;;AA6DO,MAAM,iBAAiB,CAAA,GAAA,wPAAA,CAAA,SAAM,AAAD,IACjC,CAAA,GAAA,6PAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,UAAU,EAAE;QACZ,gBAAgB;QAChB,YAAY;YACV,MAAM;YACN,OAAO;YACP,YAAY;YACZ,WAAW;YACX,aAAa;YACb,aAAa;QACf;QAEA,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,YAAY;QAEZ,WAAW;QACX,uBAAuB;QACvB,qBAAqB;QACrB,wBAAwB;QACxB,eAAe;QACf,gBAAgB;QAEhB,UAAU;QACV,aAAa;QACb,cAAc;QAEd,kBAAkB;QAClB,eAAe,OAAO;YACpB,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,mKAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBACxC,MAAM,QAAQ,QAAQ,MAAM,UAAU,CAAC,IAAI;oBAC3C,OAAO,QAAQ,SAAS,MAAM,UAAU,CAAC,KAAK;oBAC9C,QAAQ,QAAQ,UAAU,MAAM,WAAW;gBAC7C;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,IAAI;wBACF,UAAU,SAAS,IAAI;wBACvB,YAAY,SAAS,UAAU;wBAC/B,WAAW;oBACb;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;QAEA,cAAc,OAAO;YACnB,IAAI;gBAAE,YAAY;YAAK;YACvB,IAAI;gBACF,mDAAmD;gBACnD,MAAM,YAAY,mKAAA,CAAA,cAAW,CAAC,SAAS,QAAQ;oBAAE,UAAU;oBAAO,WAAW;gBAA0B;gBACvG,QAAQ,GAAG,CAAC,kCAAkC;gBAE9C,0CAA0C;gBAC1C,wCAAmC;oBACjC,QAAQ,GAAG,CAAC,uBAAuB;wBACjC,aAAa,aAAa,OAAO,CAAC,gBAAgB,WAAW;wBAC7D,aAAa,aAAa,OAAO,CAAC,kBAAkB,WAAW;wBAC/D,UAAU,aAAa,OAAO,CAAC,eAAe,WAAW;oBAC3D;gBACF;gBAEA,MAAM,WAAW,MAAM,mKAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBAE1C,IAAI,SAAS,OAAO,EAAE;oBACpB,6BAA6B;oBAC7B,IAAI,CAAC,QAAU,CAAC;4BACd,UAAU;gCAAC,SAAS,IAAI;mCAAK,MAAM,QAAQ;6BAAC;4BAC5C,YAAY;4BACZ,uBAAuB;wBACzB,CAAC;oBAED,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,OAAO,IAAI;oBAElC,uCAAuC;oBACvC,MAAM,aAAa;gBACrB,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,0BAA0B;gBAExC,kDAAkD;gBAClD,IAAI,MAAM,OAAO,EAAE,SAAS,UAAU,MAAM,OAAO,EAAE,SAAS,kBAAkB;oBAC9E,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,UAAU,MAAM,OAAO,EAAE,SAAS,cAAc;oBACjF,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO;oBACL,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;gBAC/B;gBAEA,IAAI;oBAAE,YAAY;gBAAM;YAC1B;QACF;QAEA,cAAc,OAAO,IAAI;YACvB,IAAI;gBAAE,YAAY;YAAK;YACvB,IAAI;gBACF,MAAM,WAAW,MAAM,mKAAA,CAAA,cAAW,CAAC,MAAM,CAAC,IAAI;gBAE9C,IAAI,SAAS,OAAO,EAAE;oBACpB,4BAA4B;oBAC5B,IAAI,CAAC,QAAU,CAAC;4BACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAC5B,OAAO,EAAE,KAAK,KAAK;oCAAE,GAAG,MAAM;oCAAE,GAAG,SAAS,IAAI;gCAAC,IAAI;4BAEvD,YAAY;4BACZ,qBAAqB;4BACrB,eAAe;wBACjB,CAAC;oBAED,sDAAsD;oBACtD,MAAM,kBAAkB,MAAM,cAAc;oBAC5C,IAAI,mBAAmB,gBAAgB,EAAE,KAAK,IAAI;wBAChD,IAAI;4BAAE,gBAAgB;gCAAE,GAAG,eAAe;gCAAE,GAAG,SAAS,IAAI;4BAAC;wBAAE;oBACjE;oBAEA,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,OAAO,IAAI;gBACpC,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,IAAI;oBAAE,YAAY;gBAAM;YAC1B;QACF;QAEA,cAAc,OAAO;YACnB,IAAI;gBAAE,YAAY;YAAK;YACvB,IAAI;gBACF,MAAM,WAAW,MAAM,mKAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBAE1C,IAAI,SAAS,OAAO,EAAE;oBACpB,8BAA8B;oBAC9B,IAAI,CAAC,QAAU,CAAC;4BACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,SAAW,OAAO,EAAE,KAAK;4BAC1D,YAAY;4BACZ,wBAAwB;4BACxB,gBAAgB;wBAClB,CAAC;oBAED,iDAAiD;oBACjD,MAAM,kBAAkB,MAAM,cAAc;oBAC5C,IAAI,mBAAmB,gBAAgB,EAAE,KAAK,IAAI;wBAChD,MAAM,cAAc;oBACtB;oBAEA,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,OAAO,IAAI;gBACpC,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,IAAI;oBAAE,YAAY;gBAAM;YAC1B;QACF;QAEA,2CAA2C;QAC3C,kBAAkB,OAAO;YACvB,IAAI;gBAAE,YAAY;YAAK;YACvB,IAAI;gBACF,qCAAqC;gBACrC,MAAM,WAAW,MAAM,mKAAA,CAAA,cAAW,CAAC,MAAM,CAAC,IAAI;oBAC5C,UAAU;oBACV,WAAW;oBACX,WAAW,IAAI,OAAO,WAAW;gBACnC;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,0BAA0B;oBAC1B,IAAI,CAAC,QAAU,CAAC;4BACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,SAAW,OAAO,EAAE,KAAK;4BAC1D,YAAY;4BACZ,wBAAwB;4BACxB,gBAAgB;wBAClB,CAAC;oBAED,iDAAiD;oBACjD,MAAM,kBAAkB,MAAM,cAAc;oBAC5C,IAAI,mBAAmB,gBAAgB,EAAE,KAAK,IAAI;wBAChD,MAAM,cAAc;oBACtB;oBAEA,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,IAAI;oBAAE,YAAY;gBAAM;YAC1B;QACF;QAEA,8BAA8B;QAC9B,eAAe,OAAO;YACpB,IAAI;gBAAE,YAAY;YAAK;YACvB,IAAI;gBACF,MAAM,WAAW,MAAM,mKAAA,CAAA,cAAW,CAAC,MAAM,CAAC,IAAI;oBAC5C,UAAU;oBACV,WAAW;oBACX,WAAW;gBACb;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,wBAAwB;oBACxB,MAAM,MAAM,aAAa;oBACzB,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,IAAI;oBAAE,YAAY;gBAAM;YAC1B;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,MAAM,WAAW,MAAM,mKAAA,CAAA,cAAW,CAAC,YAAY,CAAC;gBAEhD,IAAI,SAAS,OAAO,EAAE;oBACpB,mCAAmC;oBACnC,IAAI,CAAC,QAAU,CAAC;4BACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAC5B,OAAO,EAAE,KAAK,KAAK;oCAAE,GAAG,MAAM;oCAAE,UAAU,CAAC,OAAO,QAAQ;gCAAC,IAAI;wBAEnE,CAAC;oBAED,uDAAuD;oBACvD,MAAM,kBAAkB,MAAM,cAAc;oBAC5C,IAAI,mBAAmB,gBAAgB,EAAE,KAAK,IAAI;wBAChD,IAAI;4BAAE,gBAAgB;gCAAE,GAAG,eAAe;gCAAE,UAAU,CAAC,gBAAgB,QAAQ;4BAAC;wBAAE;oBACpF;oBAEA,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,OAAO,IAAI;gBACpC,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,aAAa;QACb,mBAAmB,CAAC;YAClB,IAAI;gBAAE,gBAAgB;YAAO;YAE7B,wCAAwC;YACxC,IAAI,QAAQ;gBACV,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YACxD,OAAO;gBACL,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,0BAA0B,CAAC,OAAS,IAAI;gBAAE,uBAAuB;YAAK;QAEtE,wBAAwB,CAAC,OAAS,IAAI;gBACpC,qBAAqB;gBACrB,eAAe,OAAO,MAAM,aAAa,GAAG;YAC9C;QAEA,2BAA2B,CAAC,OAAS,IAAI;gBACvC,wBAAwB;gBACxB,gBAAgB,OAAO,MAAM,cAAc,GAAG;YAChD;QAEA,kBAAkB,CAAC,SAAW,IAAI;gBAAE,eAAe;YAAO;QAC1D,mBAAmB,CAAC,SAAW,IAAI;gBAAE,gBAAgB;YAAO;QAC5D,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QACpD,iBAAiB,CAAC,SAAW,IAAI;gBAAE,cAAc;YAAO;QAExD,oBAAoB;QACpB,gBAAgB;YACd,IAAI;gBAAE,gBAAgB;YAAK;YAC3B,aAAa,UAAU,CAAC;QAC1B;QAEA,mBAAmB,IAAM,MAAM,cAAc,KAAK;QAElD,qBAAqB,IAAM,MAAM,cAAc,EAAE,MAAM;QAEvD,eAAe,CAAC,KAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAW,OAAO,EAAE,KAAK;QAErE,mBAAmB,IAAM,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,SAAW,OAAO,QAAQ;QAE1E,qBAAqB,IAAM,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,SAAW,CAAC,OAAO,QAAQ;QAE7E,cAAc,IAAM,IAAI;gBAAE,aAAa;gBAAI,cAAc;YAAM;IACjE,CAAC,GACD;IACE,MAAM;AACR;AAIJ,4CAA4C;AAC5C,wCAAmC;IACjC,MAAM,eAAe,aAAa,OAAO,CAAC;IAC1C,IAAI,cAAc;QAChB,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,eAAe,QAAQ,GAAG,iBAAiB,CAAC;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,aAAa,UAAU,CAAC;QAC1B;IACF;AACF", "debugId": null}}, {"offset": {"line": 9934, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,0UAAC;QAAI,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 9982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface DropdownMenuProps {\n  children: React.ReactNode\n}\n\ninterface DropdownMenuContextType {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nconst DropdownMenuContext = React.createContext<DropdownMenuContextType | undefined>(undefined)\n\nconst DropdownMenu = ({ children }: DropdownMenuProps) => {\n  const [open, setOpen] = React.useState(false)\n\n  return (\n    <DropdownMenuContext.Provider value={{ open, setOpen }}>\n      <div className=\"relative inline-block text-left\">\n        {children}\n      </div>\n    </DropdownMenuContext.Provider>\n  )\n}\n\nconst DropdownMenuTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & {\n    asChild?: boolean\n  }\n>(({ className, children, asChild = false, ...props }, ref) => {\n  const context = React.useContext(DropdownMenuContext)\n  if (!context) throw new Error(\"DropdownMenuTrigger must be used within DropdownMenu\")\n\n  const handleClick = () => {\n    context.setOpen(!context.open)\n  }\n\n  if (asChild) {\n    return React.cloneElement(children as React.ReactElement, {\n      onClick: handleClick,\n      ref,\n    })\n  }\n\n  return (\n    <button\n      ref={ref}\n      className={cn(\n        \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\",\n        className\n      )}\n      onClick={handleClick}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n})\nDropdownMenuTrigger.displayName = \"DropdownMenuTrigger\"\n\nconst DropdownMenuContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    align?: \"start\" | \"center\" | \"end\"\n    sideOffset?: number\n  }\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => {\n  const context = React.useContext(DropdownMenuContext)\n  if (!context) throw new Error(\"DropdownMenuContent must be used within DropdownMenu\")\n\n  const contentRef = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (contentRef.current && !contentRef.current.contains(event.target as Node)) {\n        context.setOpen(false)\n      }\n    }\n\n    if (context.open) {\n      document.addEventListener(\"mousedown\", handleClickOutside)\n    }\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside)\n    }\n  }, [context.open, context.setOpen])\n\n  if (!context.open) return null\n\n  return (\n    <div\n      ref={contentRef}\n      className={cn(\n        \"absolute z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\n        align === \"start\" && \"left-0\",\n        align === \"center\" && \"left-1/2 transform -translate-x-1/2\",\n        align === \"end\" && \"right-0\",\n        className\n      )}\n      style={{ top: `calc(100% + ${sideOffset}px)` }}\n      {...props}\n    />\n  )\n})\nDropdownMenuContent.displayName = \"DropdownMenuContent\"\n\nconst DropdownMenuItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => {\n  const context = React.useContext(DropdownMenuContext)\n\n  const handleClick = (event: React.MouseEvent) => {\n    if (props.onClick) {\n      props.onClick(event)\n    }\n    context?.setOpen(false)\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 hover:bg-accent hover:text-accent-foreground\",\n        inset && \"pl-8\",\n        className\n      )}\n      onClick={handleClick}\n      {...props}\n    />\n  )\n})\nDropdownMenuItem.displayName = \"DropdownMenuItem\"\n\nconst DropdownMenuSeparator = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = \"DropdownMenuSeparator\"\n\nconst DropdownMenuLabel = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = \"DropdownMenuLabel\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuLabel,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;;;AAHA;;;AAcA,MAAM,oCAAsB,CAAA,GAAA,0SAAA,CAAA,gBAAmB,AAAD,EAAuC;AAErF,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAqB;;IACnD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAc,AAAD,EAAE;IAEvC,qBACE,0UAAC,oBAAoB,QAAQ;QAAC,OAAO;YAAE;YAAM;QAAQ;kBACnD,cAAA,0UAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;GAVM;KAAA;AAYN,MAAM,oCAAsB,IAAA,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,YAKzC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;;IACrD,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,cAAc;QAClB,QAAQ,OAAO,CAAC,CAAC,QAAQ,IAAI;IAC/B;IAEA,IAAI,SAAS;QACX,qBAAO,CAAA,GAAA,0SAAA,CAAA,eAAkB,AAAD,EAAE,UAAgC;YACxD,SAAS;YACT;QACF;IACF;IAEA,qBACE,0UAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,iPACA;QAEF,SAAS;QACR,GAAG,KAAK;kBAER;;;;;;AAGP;;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,oCAAsB,IAAA,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,YAMzC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE;;IAC5D,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,aAAa,CAAA,GAAA,0SAAA,CAAA,SAAY,AAAD,EAAkB;IAEhD,CAAA,GAAA,0SAAA,CAAA,YAAe,AAAD;yCAAE;YACd,MAAM;oEAAqB,CAAC;oBAC1B,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC5E,QAAQ,OAAO,CAAC;oBAClB;gBACF;;YAEA,IAAI,QAAQ,IAAI,EAAE;gBAChB,SAAS,gBAAgB,CAAC,aAAa;YACzC;YAEA;iDAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;wCAAG;QAAC,QAAQ,IAAI;QAAE,QAAQ,OAAO;KAAC;IAElC,IAAI,CAAC,QAAQ,IAAI,EAAE,OAAO;IAE1B,qBACE,0UAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,iHACA,UAAU,WAAW,UACrB,UAAU,YAAY,uCACtB,UAAU,SAAS,WACnB;QAEF,OAAO;YAAE,KAAK,CAAC,YAAY,EAAE,WAAW,GAAG,CAAC;QAAC;QAC5C,GAAG,KAAK;;;;;;AAGf;;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,iCAAmB,IAAA,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,YAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;;IACjC,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,MAAM,cAAc,CAAC;QACnB,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,OAAO,CAAC;QAChB;QACA,SAAS,QAAQ;IACnB;IAEA,qBACE,0UAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,gRACA,SAAS,QACT;QAEF,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,sCAAwB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,QAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,0UAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG;AAEpC,MAAM,kCAAoB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,QAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,0UAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 10159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/institute/BranchSelectorCompact.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useInstituteStore } from '@/stores/institute/useInstituteStore'\nimport { useBranchStore } from '@/stores/institute/useBranchStore'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Building2, \n  ChevronDown, \n  Plus, \n  MapPin,\n  CheckCircle\n} from 'lucide-react'\n\ninterface Branch {\n  id: string\n  name: string\n  code?: string\n  location?: {\n    address: string\n    district?: { name: string }\n    state?: { name: string }\n  }\n  isActive?: boolean\n  isHeadOffice?: boolean\n  isDeleted?: boolean\n}\n\ninterface BranchSelectorCompactProps {\n  className?: string\n}\n\nexport function BranchSelectorCompact({ className = \"\" }: BranchSelectorCompactProps) {\n  const { branches, fetchBranches, isLoading } = useInstituteStore()\n  const { \n    selectedBranch, \n    setSelectedBranch, \n    setShowCreateBranchModal \n  } = useBranchStore()\n\n  useEffect(() => {\n    fetchBranches()\n  }, [fetchBranches])\n\n  const handleBranchSelect = (branch: Branch | null) => {\n    setSelectedBranch(branch)\n  }\n\n  const handleAddBranch = () => {\n    setShowCreateBranchModal(true)\n  }\n\n  const activeBranches = branches.filter((branch: Branch) => branch.isActive && !branch.isDeleted)\n\n  return (\n    <div className={`flex items-center space-x-2 ${className}`}>\n      <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"h-8 px-3 text-sm\"\n            disabled={isLoading}\n          >\n            <Building2 className=\"h-3 w-3 mr-2\" />\n            <span className=\"max-w-32 truncate\">\n              {selectedBranch ? selectedBranch.name : 'All Branches'}\n            </span>\n            <ChevronDown className=\"h-3 w-3 ml-2\" />\n          </Button>\n        </DropdownMenuTrigger>\n\n        <DropdownMenuContent className=\"w-72\" align=\"end\">\n          <DropdownMenuLabel className=\"flex items-center justify-between\">\n            <span>Select Branch</span>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleAddBranch}\n              className=\"h-6 w-6 p-0\"\n            >\n              <Plus className=\"h-3 w-3\" />\n            </Button>\n          </DropdownMenuLabel>\n          <DropdownMenuSeparator />\n          \n          {/* All Branches Option */}\n          <DropdownMenuItem\n            onClick={() => handleBranchSelect(null)}\n            className=\"flex items-center justify-between\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <Building2 className=\"h-4 w-4\" />\n              <span>All Branches</span>\n            </div>\n            {!selectedBranch && <CheckCircle className=\"h-4 w-4 text-green-600\" />}\n          </DropdownMenuItem>\n\n          <DropdownMenuSeparator />\n\n          {/* Individual Branches */}\n          {activeBranches.length > 0 ? (\n            activeBranches.map((branch: Branch) => (\n              <DropdownMenuItem\n                key={branch.id}\n                onClick={() => handleBranchSelect(branch)}\n                className=\"flex items-center justify-between p-3\"\n              >\n                <div className=\"flex items-start space-x-3 min-w-0 flex-1\">\n                  <Building2 className=\"h-4 w-4 mt-0.5 flex-shrink-0\" />\n                  <div className=\"min-w-0 flex-1\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-medium truncate\">{branch.name}</span>\n                      {branch.isHeadOffice && (\n                        <Badge variant=\"secondary\" className=\"text-xs\">\n                          HQ\n                        </Badge>\n                      )}\n                    </div>\n                    <div className=\"text-xs text-gray-500 truncate\">\n                      {branch.code}\n                    </div>\n                    {branch.location?.address && (\n                      <div className=\"flex items-center space-x-1 text-xs text-gray-500 mt-1\">\n                        <MapPin className=\"h-3 w-3\" />\n                        <span className=\"truncate\">\n                          {branch.location.district?.name}, {branch.location.state?.name}\n                        </span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n                {selectedBranch?.id === branch.id && (\n                  <CheckCircle className=\"h-4 w-4 text-green-600 flex-shrink-0\" />\n                )}\n              </DropdownMenuItem>\n            ))\n          ) : (\n            <DropdownMenuItem className=\"opacity-50 cursor-not-allowed\">\n              <span className=\"text-gray-500\">No branches found</span>\n            </DropdownMenuItem>\n          )}\n        </DropdownMenuContent>\n      </DropdownMenu>\n\n      {/* Current branch indicator */}\n      {selectedBranch && (\n        <Badge variant=\"outline\" className=\"text-xs\">\n          {selectedBranch.code || selectedBranch.name}\n        </Badge>\n      )}\n    </div>\n  )\n}\n\nexport default BranchSelectorCompact\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;;;AAfA;;;;;;;;AAyCO,SAAS,sBAAsB,EAAE,YAAY,EAAE,EAA8B;;IAClF,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD;IAC/D,MAAM,EACJ,cAAc,EACd,iBAAiB,EACjB,wBAAwB,EACzB,GAAG,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD;IAEjB,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;2CAAE;YACR;QACF;0CAAG;QAAC;KAAc;IAElB,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;IACpB;IAEA,MAAM,kBAAkB;QACtB,yBAAyB;IAC3B;IAEA,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,SAAmB,OAAO,QAAQ,IAAI,CAAC,OAAO,SAAS;IAE/F,qBACE,0UAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;;0BACxD,0UAAC,mKAAA,CAAA,eAAY;;kCACX,0UAAC,mKAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,0UAAC,yJAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,UAAU;;8CAEV,0UAAC,uSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,0UAAC;oCAAK,WAAU;8CACb,iBAAiB,eAAe,IAAI,GAAG;;;;;;8CAE1C,0UAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI3B,0UAAC,mKAAA,CAAA,sBAAmB;wBAAC,WAAU;wBAAO,OAAM;;0CAC1C,0UAAC,mKAAA,CAAA,oBAAiB;gCAAC,WAAU;;kDAC3B,0UAAC;kDAAK;;;;;;kDACN,0UAAC,yJAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,0UAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGpB,0UAAC,mKAAA,CAAA,wBAAqB;;;;;0CAGtB,0UAAC,mKAAA,CAAA,mBAAgB;gCACf,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;kDAEV,0UAAC;wCAAI,WAAU;;0DACb,0UAAC,uSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,0UAAC;0DAAK;;;;;;;;;;;;oCAEP,CAAC,gCAAkB,0UAAC,kTAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAG7C,0UAAC,mKAAA,CAAA,wBAAqB;;;;;4BAGrB,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,uBAClB,0UAAC,mKAAA,CAAA,mBAAgB;oCAEf,SAAS,IAAM,mBAAmB;oCAClC,WAAU;;sDAEV,0UAAC;4CAAI,WAAU;;8DACb,0UAAC,uSAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,0UAAC;oDAAI,WAAU;;sEACb,0UAAC;4DAAI,WAAU;;8EACb,0UAAC;oEAAK,WAAU;8EAAwB,OAAO,IAAI;;;;;;gEAClD,OAAO,YAAY,kBAClB,0UAAC,wJAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAAU;;;;;;;;;;;;sEAKnD,0UAAC;4DAAI,WAAU;sEACZ,OAAO,IAAI;;;;;;wDAEb,OAAO,QAAQ,EAAE,yBAChB,0UAAC;4DAAI,WAAU;;8EACb,0UAAC,iSAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,0UAAC;oEAAK,WAAU;;wEACb,OAAO,QAAQ,CAAC,QAAQ,EAAE;wEAAK;wEAAG,OAAO,QAAQ,CAAC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;wCAMnE,gBAAgB,OAAO,OAAO,EAAE,kBAC/B,0UAAC,kTAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;mCA7BpB,OAAO,EAAE;;;;0DAkClB,0UAAC,mKAAA,CAAA,mBAAgB;gCAAC,WAAU;0CAC1B,cAAA,0UAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;YAOvC,gCACC,0UAAC,wJAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAChC,eAAe,IAAI,IAAI,eAAe,IAAI;;;;;;;;;;;;AAKrD;GAzHgB;;QACiC,sKAAA,CAAA,oBAAiB;QAK5D,mKAAA,CAAA,iBAAc;;;KANJ;uCA2HD", "debugId": null}}, {"offset": {"line": 10501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { usePathname } from 'next/navigation'\nimport { useSidebarStore, UserType } from '@/stores/sidebar/useSidebarStore'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport {\n  Menu,\n  Search,\n  Bell,\n  ChevronDown,\n  ChevronRight,\n  User,\n  Settings,\n  LogOut,\n  HelpCircle\n} from 'lucide-react'\nimport { useResponsive } from '@/hooks/useResponsive'\nimport { Breadcrumbs } from '@/components/shared/navigation/Breadcrumbs'\nimport { NavigationSearch, GlobalSearchModal } from '@/components/shared/navigation/NavigationSearch'\nimport { NotificationDropdown } from './NotificationDropdown'\nimport { ProfileDropdown } from './ProfileDropdown'\nimport { BranchSelectorCompact } from '@/components/institute/BranchSelectorCompact'\n\ninterface HeaderProps {\n  userType: UserType\n}\n\nexport function Header({ userType }: HeaderProps) {\n  const pathname = usePathname()\n  const {\n    isCollapsed,\n    isMobileOpen,\n    toggleSidebar,\n    setMobileSidebarOpen,\n    navigationItems,\n    notifications,\n    unreadCount\n  } = useSidebarStore()\n\n  const { user, logout } = useAuthStore()\n  const { isMobile } = useResponsive()\n  const [showNotifications, setShowNotifications] = useState(false)\n  const [showProfile, setShowProfile] = useState(false)\n  const [searchQuery, setSearchQuery] = useState('')\n\n  // Generate breadcrumbs from current path\n  const generateBreadcrumbs = () => {\n    const pathSegments = pathname.split('/').filter(Boolean)\n    const breadcrumbs = []\n\n    // Add home/dashboard\n    const dashboardPath = `/${pathSegments[0] || ''}`\n    const dashboardItem = navigationItems.find(item => item.href === dashboardPath)\n    if (dashboardItem) {\n      breadcrumbs.push({\n        label: dashboardItem.label,\n        href: dashboardItem.href,\n        isActive: pathname === dashboardItem.href\n      })\n    }\n\n    // Add subsequent segments\n    for (let i = 1; i < pathSegments.length; i++) {\n      const segmentPath = `/${pathSegments.slice(0, i + 1).join('/')}`\n      const segmentItem = navigationItems.find(item => item.href === segmentPath)\n      \n      if (segmentItem) {\n        breadcrumbs.push({\n          label: segmentItem.label,\n          href: segmentItem.href,\n          isActive: pathname === segmentItem.href\n        })\n      } else {\n        // Fallback for dynamic segments\n        const label = pathSegments[i].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n        breadcrumbs.push({\n          label,\n          href: segmentPath,\n          isActive: i === pathSegments.length - 1\n        })\n      }\n    }\n\n    return breadcrumbs\n  }\n\n  const breadcrumbs = generateBreadcrumbs()\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Implement search functionality\n    console.log('Search query:', searchQuery)\n  }\n\n  const handleLogout = async () => {\n    try {\n      await logout()\n      setShowProfile(false)\n    } catch (error) {\n      console.error('Logout error:', error)\n    }\n  }\n\n  const handleMobileMenuToggle = () => {\n    if (isMobile) {\n      setMobileSidebarOpen(!isMobileOpen)\n    } else {\n      toggleSidebar()\n    }\n  }\n\n\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 sticky top-0 z-30\">\n      <div className=\"flex items-center justify-between px-4 lg:px-6 h-16\">\n        {/* Left Section */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Mobile Menu Button */}\n          <button\n            onClick={handleMobileMenuToggle}\n            className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors lg:hidden\"\n          >\n            <Menu className=\"w-5 h-5 text-gray-600\" />\n          </button>\n\n          {/* Breadcrumbs */}\n          <div className=\"hidden md:block\">\n            <Breadcrumbs\n              maxItems={4}\n              showHomeIcon={true}\n              className=\"text-sm\"\n            />\n          </div>\n        </div>\n\n        {/* Center Section - Search */}\n        <div className=\"flex-1 max-w-md mx-4 hidden md:block\">\n          <NavigationSearch\n            placeholder=\"Search navigation...\"\n            showShortcut={true}\n          />\n        </div>\n\n        {/* Mobile Search */}\n        <div className=\"md:hidden\">\n          <GlobalSearchModal />\n        </div>\n\n        {/* Right Section */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Branch Selector for Institute Admin */}\n          {userType === 'institute_admin' && (\n            <BranchSelectorCompact className=\"hidden lg:flex\" />\n          )}\n\n          {/* Notifications */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowNotifications(!showNotifications)}\n              className=\"relative p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n            >\n              <Bell className=\"w-5 h-5 text-gray-600\" />\n              {unreadCount > 0 && (\n                <span className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                  {unreadCount > 9 ? '9+' : unreadCount}\n                </span>\n              )}\n            </button>\n\n            {/* Notification Dropdown */}\n            {showNotifications && (\n              <NotificationDropdown\n                notifications={notifications}\n                onClose={() => setShowNotifications(false)}\n              />\n            )}\n          </div>\n\n          {/* Profile Dropdown */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowProfile(!showProfile)}\n              className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n            >\n              <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n                {user?.personalInfo?.avatar ? (\n                  <img \n                    src={user.personalInfo.avatar} \n                    alt={user.personalInfo.fullName || user.email}\n                    className=\"w-8 h-8 rounded-full object-cover\"\n                  />\n                ) : (\n                  <User className=\"w-4 h-4 text-white\" />\n                )}\n              </div>\n              \n              <div className=\"hidden md:block text-left\">\n                <div className=\"text-sm font-medium text-gray-900\">\n                  {user?.personalInfo?.fullName || user?.email || 'User'}\n                </div>\n                <div className=\"text-xs text-gray-500 capitalize\">\n                  {user?.role?.name || 'Admin'}\n                </div>\n              </div>\n              \n              <ChevronDown className=\"w-4 h-4 text-gray-400\" />\n            </button>\n\n            {/* Profile Dropdown Menu */}\n            {showProfile && (\n              <ProfileDropdown\n                user={user}\n                userType={userType}\n                onLogout={handleLogout}\n                onClose={() => setShowProfile(false)}\n              />\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;;;AAtBA;;;;;;;;;;;;AA4BO,SAAS,OAAO,EAAE,QAAQ,EAAe;;IAC9C,MAAM,WAAW,CAAA,GAAA,kRAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EACJ,WAAW,EACX,YAAY,EACZ,aAAa,EACb,oBAAoB,EACpB,eAAe,EACf,aAAa,EACb,WAAW,EACZ,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD;IACpC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD;IACjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,yCAAyC;IACzC,MAAM,sBAAsB;QAC1B,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QAChD,MAAM,cAAc,EAAE;QAEtB,qBAAqB;QACrB,MAAM,gBAAgB,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,IAAI,IAAI;QACjD,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QACjE,IAAI,eAAe;YACjB,YAAY,IAAI,CAAC;gBACf,OAAO,cAAc,KAAK;gBAC1B,MAAM,cAAc,IAAI;gBACxB,UAAU,aAAa,cAAc,IAAI;YAC3C;QACF;QAEA,0BAA0B;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,cAAc,CAAC,CAAC,EAAE,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM;YAChE,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;YAE/D,IAAI,aAAa;gBACf,YAAY,IAAI,CAAC;oBACf,OAAO,YAAY,KAAK;oBACxB,MAAM,YAAY,IAAI;oBACtB,UAAU,aAAa,YAAY,IAAI;gBACzC;YACF,OAAO;gBACL,gCAAgC;gBAChC,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;gBACpF,YAAY,IAAI,CAAC;oBACf;oBACA,MAAM;oBACN,UAAU,MAAM,aAAa,MAAM,GAAG;gBACxC;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;IAEpB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,iCAAiC;QACjC,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,UAAU;YACZ,qBAAqB,CAAC;QACxB,OAAO;YACL;QACF;IACF;IAIA,qBACE,0UAAC;QAAO,WAAU;kBAChB,cAAA,0UAAC;YAAI,WAAU;;8BAEb,0UAAC;oBAAI,WAAU;;sCAEb,0UAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,0UAAC,yRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIlB,0UAAC;4BAAI,WAAU;sCACb,cAAA,0UAAC,gLAAA,CAAA,cAAW;gCACV,UAAU;gCACV,cAAc;gCACd,WAAU;;;;;;;;;;;;;;;;;8BAMhB,0UAAC;oBAAI,WAAU;8BACb,cAAA,0UAAC,qLAAA,CAAA,mBAAgB;wBACf,aAAY;wBACZ,cAAc;;;;;;;;;;;8BAKlB,0UAAC;oBAAI,WAAU;8BACb,cAAA,0UAAC,qLAAA,CAAA,oBAAiB;;;;;;;;;;8BAIpB,0UAAC;oBAAI,WAAU;;wBAEZ,aAAa,mCACZ,0UAAC,+KAAA,CAAA,wBAAqB;4BAAC,WAAU;;;;;;sCAInC,0UAAC;4BAAI,WAAU;;8CACb,0UAAC;oCACC,SAAS,IAAM,qBAAqB,CAAC;oCACrC,WAAU;;sDAEV,0UAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,mBACb,0UAAC;4CAAK,WAAU;sDACb,cAAc,IAAI,OAAO;;;;;;;;;;;;gCAM/B,mCACC,0UAAC,2KAAA,CAAA,uBAAoB;oCACnB,eAAe;oCACf,SAAS,IAAM,qBAAqB;;;;;;;;;;;;sCAM1C,0UAAC;4BAAI,WAAU;;8CACb,0UAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;;sDAEV,0UAAC;4CAAI,WAAU;sDACZ,MAAM,cAAc,uBACnB,0UAAC;gDACC,KAAK,KAAK,YAAY,CAAC,MAAM;gDAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;gDAC7C,WAAU;;;;;qEAGZ,0UAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAIpB,0UAAC;4CAAI,WAAU;;8DACb,0UAAC;oDAAI,WAAU;8DACZ,MAAM,cAAc,YAAY,MAAM,SAAS;;;;;;8DAElD,0UAAC;oDAAI,WAAU;8DACZ,MAAM,MAAM,QAAQ;;;;;;;;;;;;sDAIzB,0UAAC,2SAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;gCAIxB,6BACC,0UAAC,sKAAA,CAAA,kBAAe;oCACd,MAAM;oCACN,UAAU;oCACV,UAAU;oCACV,SAAS,IAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GApMgB;;QACG,kRAAA,CAAA,cAAW;QASxB,kKAAA,CAAA,kBAAe;QAEM,4JAAA,CAAA,eAAY;QAChB,oJAAA,CAAA,gBAAa;;;KAbpB;uCAsMD", "debugId": null}}, {"offset": {"line": 10846, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/shared/navigation/MobileNavigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useSidebarStore, NavigationItem } from '@/stores/sidebar/useSidebarStore'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport { useResponsive } from '@/hooks/useResponsive'\nimport { \n  Home, \n  Menu, \n  X, \n  User, \n  Settings, \n  LogOut,\n  ChevronRight\n} from 'lucide-react'\nimport * as Icons from 'lucide-react'\n\nexport function MobileNavigation() {\n  const pathname = usePathname()\n  const { isMobile } = useResponsive()\n  const { navigationItems } = useSidebarStore()\n  const { user, logout } = useAuthStore()\n  const [isOpen, setIsOpen] = useState(false)\n  const [expandedItems, setExpandedItems] = useState<string[]>([])\n\n  if (!isMobile) return null\n\n  const toggleExpanded = (itemId: string) => {\n    setExpandedItems(prev => \n      prev.includes(itemId) \n        ? prev.filter(id => id !== itemId)\n        : [...prev, itemId]\n    )\n  }\n\n  const handleItemClick = () => {\n    setIsOpen(false)\n    setExpandedItems([])\n  }\n\n  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {\n    const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>\n    const isActive = pathname === item.href\n    const hasChildren = item.children && item.children.length > 0\n    const isExpanded = expandedItems.includes(item.id)\n\n    return (\n      <div key={item.id}>\n        {hasChildren ? (\n          <button\n            onClick={() => toggleExpanded(item.id)}\n            className={`w-full flex items-center justify-between px-4 py-3 text-left transition-colors ${\n              level > 0 ? 'pl-8' : ''\n            } ${\n              isActive \n                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' \n                : 'text-gray-700 hover:bg-gray-50'\n            }`}\n          >\n            <div className=\"flex items-center space-x-3\">\n              {IconComponent && (\n                <IconComponent className={`w-5 h-5 ${\n                  isActive ? 'text-blue-600' : 'text-gray-400'\n                }`} />\n              )}\n              <span className=\"font-medium\">{item.label}</span>\n              {item.badge && item.badge > 0 && (\n                <span className=\"ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full\">\n                  {item.badge > 9 ? '9+' : item.badge}\n                </span>\n              )}\n            </div>\n            <ChevronRight className={`w-4 h-4 text-gray-400 transition-transform ${\n              isExpanded ? 'rotate-90' : ''\n            }`} />\n          </button>\n        ) : (\n          <Link\n            href={item.href}\n            onClick={handleItemClick}\n            className={`flex items-center space-x-3 px-4 py-3 transition-colors ${\n              level > 0 ? 'pl-8' : ''\n            } ${\n              isActive \n                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' \n                : 'text-gray-700 hover:bg-gray-50'\n            }`}\n          >\n            {IconComponent && (\n              <IconComponent className={`w-5 h-5 ${\n                isActive ? 'text-blue-600' : 'text-gray-400'\n              }`} />\n            )}\n            <span className=\"font-medium\">{item.label}</span>\n            {item.badge && item.badge > 0 && (\n              <span className=\"ml-auto px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full\">\n                {item.badge > 9 ? '9+' : item.badge}\n              </span>\n            )}\n          </Link>\n        )}\n\n        {/* Children */}\n        {hasChildren && isExpanded && (\n          <div className=\"bg-gray-50\">\n            {item.children?.map(child => renderNavigationItem(child, level + 1))}\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  return (\n    <>\n      {/* Mobile Navigation Button */}\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"fixed bottom-4 right-4 z-50 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors lg:hidden\"\n      >\n        <Menu className=\"w-6 h-6\" />\n      </button>\n\n      {/* Mobile Navigation Overlay */}\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 lg:hidden\">\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 bg-black bg-opacity-50\"\n            onClick={() => setIsOpen(false)}\n          />\n\n          {/* Navigation Panel */}\n          <div className=\"fixed inset-y-0 right-0 w-80 max-w-[85vw] bg-white shadow-xl\">\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">Navigation</h2>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n              >\n                <X className=\"w-5 h-5 text-gray-500\" />\n              </button>\n            </div>\n\n            {/* User Info */}\n            <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\">\n                  {user?.personalInfo?.avatar ? (\n                    <img \n                      src={user.personalInfo.avatar} \n                      alt={user.personalInfo.fullName || user.email}\n                      className=\"w-10 h-10 rounded-full object-cover\"\n                    />\n                  ) : (\n                    <User className=\"w-5 h-5 text-white\" />\n                  )}\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"text-sm font-medium text-gray-900 truncate\">\n                    {user?.personalInfo?.fullName || user?.email || 'User'}\n                  </div>\n                  <div className=\"text-xs text-gray-500 capitalize\">\n                    {user?.role?.name || 'User'}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Navigation Items */}\n            <div className=\"flex-1 overflow-y-auto\">\n              <nav className=\"py-2\">\n                {navigationItems.map(item => renderNavigationItem(item))}\n              </nav>\n            </div>\n\n            {/* Footer Actions */}\n            <div className=\"border-t border-gray-200 p-4 space-y-2\">\n              <Link\n                href=\"/settings\"\n                onClick={handleItemClick}\n                className=\"flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors\"\n              >\n                <Settings className=\"w-5 h-5 text-gray-400\" />\n                <span className=\"font-medium\">Settings</span>\n              </Link>\n              <button\n                onClick={() => {\n                  logout()\n                  handleItemClick()\n                }}\n                className=\"flex items-center space-x-3 w-full px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\"\n              >\n                <LogOut className=\"w-5 h-5\" />\n                <span className=\"font-medium\">Sign Out</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n\n// Bottom Navigation Bar for Mobile\nexport function MobileBottomNavigation() {\n  const pathname = usePathname()\n  const { isMobile } = useResponsive()\n  const { navigationItems } = useSidebarStore()\n\n  if (!isMobile) return null\n\n  // Get the most important navigation items for bottom bar\n  const bottomNavItems = navigationItems.slice(0, 4).map(item => ({\n    ...item,\n    isActive: pathname === item.href || pathname.startsWith(item.href + '/')\n  }))\n\n  return (\n    <div className=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-40 lg:hidden\">\n      <div className=\"grid grid-cols-4 h-16\">\n        {bottomNavItems.map(item => {\n          const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>\n          \n          return (\n            <Link\n              key={item.id}\n              href={item.href}\n              className={`flex flex-col items-center justify-center space-y-1 transition-colors ${\n                item.isActive \n                  ? 'text-blue-600 bg-blue-50' \n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              <div className=\"relative\">\n                {IconComponent && <IconComponent className=\"w-5 h-5\" />}\n                {item.badge && item.badge > 0 && (\n                  <span className=\"absolute -top-2 -right-2 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {item.badge > 9 ? '9+' : item.badge}\n                  </span>\n                )}\n              </div>\n              <span className=\"text-xs font-medium truncate max-w-full\">\n                {item.label}\n              </span>\n            </Link>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n\nexport default MobileNavigation\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAjBA;;;;;;;;;AAmBO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,kRAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD;IACjC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAC1C,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD;IACpC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,UACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,UACzB;mBAAI;gBAAM;aAAO;IAEzB;IAEA,MAAM,kBAAkB;QACtB,UAAU;QACV,iBAAiB,EAAE;IACrB;IAEA,MAAM,uBAAuB,CAAC,MAAsB,QAAgB,CAAC;QACnE,MAAM,gBAAgB,AAAC,wPAAa,CAAC,KAAK,IAAI,CAAC;QAC/C,MAAM,WAAW,aAAa,KAAK,IAAI;QACvC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;QAEjD,qBACE,0UAAC;;gBACE,4BACC,0UAAC;oBACC,SAAS,IAAM,eAAe,KAAK,EAAE;oBACrC,WAAW,CAAC,+EAA+E,EACzF,QAAQ,IAAI,SAAS,GACtB,CAAC,EACA,WACI,wDACA,kCACJ;;sCAEF,0UAAC;4BAAI,WAAU;;gCACZ,+BACC,0UAAC;oCAAc,WAAW,CAAC,QAAQ,EACjC,WAAW,kBAAkB,iBAC7B;;;;;;8CAEJ,0UAAC;oCAAK,WAAU;8CAAe,KAAK,KAAK;;;;;;gCACxC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,0UAAC;oCAAK,WAAU;8CACb,KAAK,KAAK,GAAG,IAAI,OAAO,KAAK,KAAK;;;;;;;;;;;;sCAIzC,0UAAC,6SAAA,CAAA,eAAY;4BAAC,WAAW,CAAC,2CAA2C,EACnE,aAAa,cAAc,IAC3B;;;;;;;;;;;yCAGJ,0UAAC,4SAAA,CAAA,UAAI;oBACH,MAAM,KAAK,IAAI;oBACf,SAAS;oBACT,WAAW,CAAC,wDAAwD,EAClE,QAAQ,IAAI,SAAS,GACtB,CAAC,EACA,WACI,wDACA,kCACJ;;wBAED,+BACC,0UAAC;4BAAc,WAAW,CAAC,QAAQ,EACjC,WAAW,kBAAkB,iBAC7B;;;;;;sCAEJ,0UAAC;4BAAK,WAAU;sCAAe,KAAK,KAAK;;;;;;wBACxC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,0UAAC;4BAAK,WAAU;sCACb,KAAK,KAAK,GAAG,IAAI,OAAO,KAAK,KAAK;;;;;;;;;;;;gBAO1C,eAAe,4BACd,0UAAC;oBAAI,WAAU;8BACZ,KAAK,QAAQ,EAAE,IAAI,CAAA,QAAS,qBAAqB,OAAO,QAAQ;;;;;;;WA1D7D,KAAK,EAAE;;;;;IA+DrB;IAEA,qBACE;;0BAEE,0UAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;0BAEV,cAAA,0UAAC,yRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;YAIjB,wBACC,0UAAC;gBAAI,WAAU;;kCAEb,0UAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAI3B,0UAAC;wBAAI,WAAU;;0CAEb,0UAAC;gCAAI,WAAU;;kDACb,0UAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,0UAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,0UAAC,mRAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,0UAAC;gCAAI,WAAU;0CACb,cAAA,0UAAC;oCAAI,WAAU;;sDACb,0UAAC;4CAAI,WAAU;sDACZ,MAAM,cAAc,uBACnB,0UAAC;gDACC,KAAK,KAAK,YAAY,CAAC,MAAM;gDAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;gDAC7C,WAAU;;;;;qEAGZ,0UAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,0UAAC;4CAAI,WAAU;;8DACb,0UAAC;oDAAI,WAAU;8DACZ,MAAM,cAAc,YAAY,MAAM,SAAS;;;;;;8DAElD,0UAAC;oDAAI,WAAU;8DACZ,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAO7B,0UAAC;gCAAI,WAAU;0CACb,cAAA,0UAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAA,OAAQ,qBAAqB;;;;;;;;;;;0CAKtD,0UAAC;gCAAI,WAAU;;kDACb,0UAAC,4SAAA,CAAA,UAAI;wCACH,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,0UAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,0UAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,0UAAC;wCACC,SAAS;4CACP;4CACA;wCACF;wCACA,WAAU;;0DAEV,0UAAC,iSAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,0UAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GAzLgB;;QACG,kRAAA,CAAA,cAAW;QACP,oJAAA,CAAA,gBAAa;QACN,kKAAA,CAAA,kBAAe;QAClB,4JAAA,CAAA,eAAY;;;KAJvB;AA4LT,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,kRAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD;IACjC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAE1C,IAAI,CAAC,UAAU,OAAO;IAEtB,yDAAyD;IACzD,MAAM,iBAAiB,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC9D,GAAG,IAAI;YACP,UAAU,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;QACtE,CAAC;IAED,qBACE,0UAAC;QAAI,WAAU;kBACb,cAAA,0UAAC;YAAI,WAAU;sBACZ,eAAe,GAAG,CAAC,CAAA;gBAClB,MAAM,gBAAgB,AAAC,wPAAa,CAAC,KAAK,IAAI,CAAC;gBAE/C,qBACE,0UAAC,4SAAA,CAAA,UAAI;oBAEH,MAAM,KAAK,IAAI;oBACf,WAAW,CAAC,sEAAsE,EAChF,KAAK,QAAQ,GACT,6BACA,qCACJ;;sCAEF,0UAAC;4BAAI,WAAU;;gCACZ,+BAAiB,0UAAC;oCAAc,WAAU;;;;;;gCAC1C,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,0UAAC;oCAAK,WAAU;8CACb,KAAK,KAAK,GAAG,IAAI,OAAO,KAAK,KAAK;;;;;;;;;;;;sCAIzC,0UAAC;4BAAK,WAAU;sCACb,KAAK,KAAK;;;;;;;mBAjBR,KAAK,EAAE;;;;;YAqBlB;;;;;;;;;;;AAIR;IA9CgB;;QACG,kRAAA,CAAA,cAAW;QACP,oJAAA,CAAA,gBAAa;QACN,kKAAA,CAAA,kBAAe;;;MAH7B;uCAgDD", "debugId": null}}, {"offset": {"line": 11319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/SuperAdminLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode, useEffect } from 'react'\nimport { useSidebarStore } from '@/stores/sidebar/useSidebarStore'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport { useResponsive } from '@/hooks/useResponsive'\nimport { Sidebar } from './Sidebar'\nimport { Header } from './Header'\nimport { MobileNavigation } from '@/components/shared/navigation/MobileNavigation'\nimport { superAdminNavigationConfig } from '@/config/navigation/superAdminNavigation'\n\ninterface SuperAdminLayoutProps {\n  children: ReactNode\n}\n\nexport function SuperAdminLayout({ children }: SuperAdminLayoutProps) {\n  const {\n    isCollapsed,\n    isMobileOpen,\n    navigationItems,\n    setNavigationItems,\n    setMobileSidebarOpen,\n    setUserType\n  } = useSidebarStore()\n  const { user, isLoading, isAuthenticated, initialize } = useAuthStore()\n  const { isMobile, isTablet } = useResponsive()\nconsole.log(user);\n  // Initialize auth once on mount\n  useEffect(() => {\n    initialize()\n  }, []) // Empty dependency array - run only once\n\n  // Initialize navigation once on mount\n  useEffect(() => {\n    // Only set navigation items if they haven't been set yet or if they're different\n    if (navigationItems.length === 0 || navigationItems[0]?.id !== superAdminNavigationConfig[0]?.id) {\n      setNavigationItems(superAdminNavigationConfig)\n    }\n    setUserType('super_admin')\n  }, []) // Empty dependency array - run only once\n\n  // Auto-collapse sidebar on mobile/tablet\n  useEffect(() => {\n    if (isMobile && isMobileOpen) {\n      setMobileSidebarOpen(false)\n    }\n  }, [isMobile, isMobileOpen, setMobileSidebarOpen])\n\n  // Show loading while auth is initializing\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <div className=\"text-gray-600\">Loading...</div>\n        </div>\n      </div>\n    )\n  }\n\n  // Verify user has super admin access using legacyRole\n  if (!user || (user.legacyRole !== 'super_admin' && user.legacyRole !== 'platform_staff')) {\n    console.log('❌ SuperAdminLayout access denied:', {\n      hasUser: !!user,\n      userEmail: user?.email,\n      userLegacyRole: user?.legacyRole,\n      userRole: user?.role\n    })\n\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"text-red-500 text-lg font-medium mb-2\">Access Denied</div>\n          <div className=\"text-gray-600\">You don't have permission to access this area.</div>\n          <div className=\"text-xs text-gray-500 mt-2\">\n            Role: {user?.legacyRole || 'none'} (Expected: super_admin)\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  console.log('✅ SuperAdminLayout access granted:', {\n    userEmail: user.email,\n    userLegacyRole: user.legacyRole,\n    user:user\n  })\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile Navigation */}\n      {isMobile && <MobileNavigation />}\n\n      {/* Desktop/Tablet Layout */}\n      {!isMobile && (\n        <div className=\"flex h-screen\">\n          {/* Sidebar */}\n          <div className={`\n            ${isCollapsed ? 'w-16' : 'w-64'} \n            transition-all duration-300 ease-in-out\n            flex-shrink-0\n            ${isMobile ? 'hidden' : 'block'}\n          `}>\n            <Sidebar userType=\"super_admin\" />\n          </div>\n\n          {/* Main Content Area */}\n          <div className=\"flex-1 flex flex-col overflow-hidden\">\n            {/* Header */}\n            <Header userType=\"super_admin\" />\n\n            {/* Page Content */}\n            <main className=\"flex-1 overflow-y-auto\">\n              <div className=\"p-6\">\n                {children}\n              </div>\n            </main>\n          </div>\n        </div>\n      )}\n\n      {/* Mobile Layout */}\n      {isMobile && (\n        <div className=\"pb-20\">\n          {/* Mobile Header */}\n          <div className=\"bg-white border-b border-gray-200 sticky top-0 z-30\">\n            <Header userType=\"super_admin\" />\n          </div>\n\n          {/* Mobile Content */}\n          <main className=\"p-4\">\n            {children}\n          </main>\n        </div>\n      )}\n\n      {/* Mobile Sidebar Overlay */}\n      {isMobile && isMobileOpen && (\n        <div className=\"fixed inset-0 z-50\">\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 bg-black bg-opacity-50\"\n            onClick={() => setMobileSidebarOpen(false)}\n          />\n\n          {/* Sidebar */}\n          <div className=\"fixed inset-y-0 left-0 w-64 bg-white shadow-xl\">\n            <Sidebar userType=\"super_admin\" />\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default SuperAdminLayout\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAeO,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;;IAClE,MAAM,EACJ,WAAW,EACX,YAAY,EACZ,eAAe,EACf,kBAAkB,EAClB,oBAAoB,EACpB,WAAW,EACZ,GAAG,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAClB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD;IACpE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD;IAC7C,QAAQ,GAAG,CAAC;IACV,gCAAgC;IAChC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG,EAAE,EAAE,yCAAyC;;IAEhD,sCAAsC;IACtC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;sCAAE;YACR,iFAAiF;YACjF,IAAI,gBAAgB,MAAM,KAAK,KAAK,eAAe,CAAC,EAAE,EAAE,OAAO,0KAAA,CAAA,6BAA0B,CAAC,EAAE,EAAE,IAAI;gBAChG,mBAAmB,0KAAA,CAAA,6BAA0B;YAC/C;YACA,YAAY;QACd;qCAAG,EAAE,EAAE,yCAAyC;;IAEhD,yCAAyC;IACzC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,YAAY,cAAc;gBAC5B,qBAAqB;YACvB;QACF;qCAAG;QAAC;QAAU;QAAc;KAAqB;IAEjD,0CAA0C;IAC1C,IAAI,WAAW;QACb,qBACE,0UAAC;YAAI,WAAU;sBACb,cAAA,0UAAC;gBAAI,WAAU;;kCACb,0UAAC;wBAAI,WAAU;;;;;;kCACf,0UAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIvC;IAEA,sDAAsD;IACtD,IAAI,CAAC,QAAS,KAAK,UAAU,KAAK,iBAAiB,KAAK,UAAU,KAAK,kBAAmB;QACxF,QAAQ,GAAG,CAAC,qCAAqC;YAC/C,SAAS,CAAC,CAAC;YACX,WAAW,MAAM;YACjB,gBAAgB,MAAM;YACtB,UAAU,MAAM;QAClB;QAEA,qBACE,0UAAC;YAAI,WAAU;sBACb,cAAA,0UAAC;gBAAI,WAAU;;kCACb,0UAAC;wBAAI,WAAU;kCAAwC;;;;;;kCACvD,0UAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,0UAAC;wBAAI,WAAU;;4BAA6B;4BACnC,MAAM,cAAc;4BAAO;;;;;;;;;;;;;;;;;;IAK5C;IAEA,QAAQ,GAAG,CAAC,sCAAsC;QAChD,WAAW,KAAK,KAAK;QACrB,gBAAgB,KAAK,UAAU;QAC/B,MAAK;IACP;IAEA,qBACE,0UAAC;QAAI,WAAU;;YAEZ,0BAAY,0UAAC,qLAAA,CAAA,mBAAgB;;;;;YAG7B,CAAC,0BACA,0UAAC;gBAAI,WAAU;;kCAEb,0UAAC;wBAAI,WAAW,CAAC;YACf,EAAE,cAAc,SAAS,OAAO;;;YAGhC,EAAE,WAAW,WAAW,QAAQ;UAClC,CAAC;kCACC,cAAA,0UAAC,8JAAA,CAAA,UAAO;4BAAC,UAAS;;;;;;;;;;;kCAIpB,0UAAC;wBAAI,WAAU;;0CAEb,0UAAC,6JAAA,CAAA,SAAM;gCAAC,UAAS;;;;;;0CAGjB,0UAAC;gCAAK,WAAU;0CACd,cAAA,0UAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;;;;;;;YAQV,0BACC,0UAAC;gBAAI,WAAU;;kCAEb,0UAAC;wBAAI,WAAU;kCACb,cAAA,0UAAC,6JAAA,CAAA,SAAM;4BAAC,UAAS;;;;;;;;;;;kCAInB,0UAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;YAMN,YAAY,8BACX,0UAAC;gBAAI,WAAU;;kCAEb,0UAAC;wBACC,WAAU;wBACV,SAAS,IAAM,qBAAqB;;;;;;kCAItC,0UAAC;wBAAI,WAAU;kCACb,cAAA,0UAAC,8JAAA,CAAA,UAAO;4BAAC,UAAS;;;;;;;;;;;;;;;;;;;;;;;AAM9B;GA1IgB;;QAQV,kKAAA,CAAA,kBAAe;QACsC,4JAAA,CAAA,eAAY;QACtC,oJAAA,CAAA,gBAAa;;;KAV9B;uCA4ID", "debugId": null}}]}