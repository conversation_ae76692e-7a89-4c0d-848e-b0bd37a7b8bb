{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps extends React.ComponentProps<\"textarea\"> {\n  error?: string | boolean\n}\n\nfunction Textarea({ className, error, ...props }: TextareaProps) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      aria-invalid={!!error}\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        error && \"border-destructive ring-destructive/20\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAMA,SAAS,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAsB;IAC7D,qBACE,0UAAC;QACC,aAAU;QACV,gBAAc,CAAC,CAAC;QAChB,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,ucACA,SAAS,0CACT;QAED,GAAG,KAAK;;;;;;AAGf;KAbS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface SwitchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {\n  checked?: boolean\n  onCheckedChange?: (checked: boolean) => void\n}\n\nconst Switch = React.forwardRef<HTMLInputElement, SwitchProps>(\n  ({ className, checked, onCheckedChange, onChange, ...props }, ref) => {\n    const [internalChecked, setInternalChecked] = React.useState(checked || false)\n    const isChecked = checked !== undefined ? checked : internalChecked\n\n    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n      const newChecked = event.target.checked\n      if (checked === undefined) {\n        setInternalChecked(newChecked)\n      }\n      onCheckedChange?.(newChecked)\n      onChange?.(event)\n    }\n\n    return (\n      <label className=\"relative inline-flex items-center cursor-pointer\">\n        <input\n          type=\"checkbox\"\n          ref={ref}\n          className=\"sr-only peer\"\n          checked={isChecked}\n          onChange={handleChange}\n          {...props}\n        />\n        <div\n          className={cn(\n            \"relative w-11 h-6 rounded-full transition-colors duration-200 ease-in-out\",\n            \"focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2\",\n            isChecked\n              ? \"bg-blue-600\"\n              : \"bg-gray-200\",\n            className\n          )}\n        >\n          <div\n            className={cn(\n              \"absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md transition-transform duration-200 ease-in-out\",\n              isChecked ? \"translate-x-5\" : \"translate-x-0\"\n            )}\n          />\n        </div>\n      </label>\n    )\n  }\n)\nSwitch.displayName = \"Switch\"\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUA,MAAM,uBAAS,GAAA,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,UAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAc,AAAD,EAAE,WAAW;IACxE,MAAM,YAAY,YAAY,YAAY,UAAU;IAEpD,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,MAAM,MAAM,CAAC,OAAO;QACvC,IAAI,YAAY,WAAW;YACzB,mBAAmB;QACrB;QACA,kBAAkB;QAClB,WAAW;IACb;IAEA,qBACE,0UAAC;QAAM,WAAU;;0BACf,0UAAC;gBACC,MAAK;gBACL,KAAK;gBACL,WAAU;gBACV,SAAS;gBACT,UAAU;gBACT,GAAG,KAAK;;;;;;0BAEX,0UAAC;gBACC,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,6EACA,6EACA,YACI,gBACA,eACJ;0BAGF,cAAA,0UAAC;oBACC,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,mHACA,YAAY,kBAAkB;;;;;;;;;;;;;;;;;AAM1C;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/api/settings.ts"], "sourcesContent": ["/**\n * Settings API Client\n * \n * API functions for managing platform settings\n */\n\nimport { api } from '../api'\n\n// Types\nexport type SettingType = 'string' | 'number' | 'boolean' | 'json' | 'url' | 'email' | 'textarea' | 'upload' | 'media'\nexport type SettingCategory = \n  | 'platform' \n  | 'email' \n  | 'security' \n  | 'storage' \n  | 'payment' \n  | 'notification' \n  | 'integration' \n  | 'feature'\n\nexport interface ValidationRules {\n  min_length?: number\n  max_length?: number\n  min_value?: number\n  max_value?: number\n  pattern?: string\n}\n\nexport interface Setting {\n  id: string\n  key: string\n  value: string\n  description?: string\n  category: SettingCategory\n  type: SettingType\n  is_public: boolean\n  is_required?: boolean\n  validation_rules?: ValidationRules\n  upload?: string | { id: string; url: string; filename: string } // For file uploads\n  mediaRef?: { id: string; url: string; filename: string } // For media type settings\n  mediaData?: { id: string; url: string; filename: string } // Populated media data\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface SettingCreationData {\n  key: string\n  value: string\n  description?: string\n  category: SettingCategory\n  type: SettingType\n  is_public?: boolean\n  is_required?: boolean\n  validation_rules?: ValidationRules\n  mediaRef?: string | number // For media type settings\n}\n\nexport interface SettingUpdateData {\n  key?: string\n  value?: string\n  description?: string\n  category?: SettingCategory\n  type?: SettingType\n  is_public?: boolean\n  is_required?: boolean\n  validation_rules?: ValidationRules\n}\n\nexport interface SettingsFilters {\n  category?: SettingCategory\n  type?: SettingType\n  is_public?: boolean\n  search?: string\n}\n\nexport interface SettingsResponse {\n  success: boolean\n  settings: Setting[]\n  totalDocs: number\n  limit?: number\n  totalPages?: number\n  page?: number\n  category?: string\n}\n\nexport interface BulkUpdateSettingsRequest {\n  settings: SettingCreationData[]\n}\n\nexport interface BulkUpdateSettingsResponse {\n  results: Array<{\n    key: string\n    operation: 'created' | 'updated'\n    id: string\n  }>\n  errors: Array<{\n    key: string\n    error: string\n  }>\n  success: number\n  failed: number\n}\n\n/**\n * Settings API functions\n */\nexport const settingsApi = {\n  /**\n   * Get all settings with optional filtering\n   */\n  async getSettings(filters?: SettingsFilters): Promise<SettingsResponse> {\n    const params = new URLSearchParams()\n    \n    if (filters?.category) params.append('category', filters.category)\n    if (filters?.type) params.append('type', filters.type)\n    if (filters?.is_public !== undefined) params.append('is_public', filters.is_public.toString())\n    if (filters?.search) params.append('search', filters.search)\n    \n    const queryString = params.toString()\n    const url = queryString ? `/api/platform/settings?${queryString}` : '/api/platform/settings'\n\n    return api.get(url)\n  },\n\n  /**\n   * Get a specific setting by key\n   */\n  async getSettingByKey(key: string): Promise<Setting> {\n    return api.get(`/api/platform/settings/${key}`)\n  },\n\n  /**\n   * Get settings by category\n   */\n  async getSettingsByCategory(category: SettingCategory): Promise<SettingsResponse> {\n    return api.get(`/api/platform/settings/category/${category}`)\n  },\n\n  /**\n   * Create a new setting\n   */\n  async createSetting(data: SettingCreationData): Promise<Setting> {\n    return api.post('/api/platform/settings', data)\n  },\n\n  /**\n   * Update a setting by key\n   */\n  async updateSetting(key: string, data: SettingUpdateData): Promise<Setting> {\n    return api.put(`/api/platform/settings/${key}`, data)\n  },\n\n  /**\n   * Delete a setting by key\n   */\n  async deleteSetting(key: string): Promise<{ message: string }> {\n    return api.delete(`/api/platform/settings/${key}`)\n  },\n\n  /**\n   * Bulk update multiple settings\n   */\n  async bulkUpdateSettings(settings: SettingCreationData[]): Promise<BulkUpdateSettingsResponse> {\n    return api.post('/api/platform/settings/bulk', { settings })\n  },\n\n  /**\n   * Get public settings (no authentication required)\n   */\n  async getPublicSettings(): Promise<SettingsResponse> {\n    return api.get('/api/platform/settings?is_public=true')\n  },\n\n  /**\n   * Get platform configuration (commonly used settings)\n   */\n  async getPlatformConfig(): Promise<Record<string, string>> {\n    const response = await this.getSettingsByCategory('platform')\n    const config: Record<string, string> = {}\n    \n    response.settings.forEach(setting => {\n      config[setting.key] = setting.value\n    })\n    \n    return config\n  },\n\n  /**\n   * Update platform configuration\n   */\n  async updatePlatformConfig(config: Record<string, string>): Promise<BulkUpdateSettingsResponse> {\n    const settings: SettingCreationData[] = Object.entries(config).map(([key, value]) => ({\n      key,\n      value,\n      category: 'platform',\n      type: 'string',\n    }))\n\n    return this.bulkUpdateSettings(settings)\n  },\n\n  /**\n   * Create or update a single setting (handles both creation and updates)\n   */\n  async createOrUpdateSetting(key: string, value: string, options: {\n    category?: SettingCategory\n    type?: SettingType\n    description?: string\n    is_public?: boolean\n  } = {}): Promise<BulkUpdateSettingsResponse> {\n    const setting: SettingCreationData = {\n      key,\n      value,\n      category: options.category || 'platform',\n      type: options.type || 'string',\n      description: options.description,\n      is_public: options.is_public !== undefined ? options.is_public : true\n    }\n\n    return this.bulkUpdateSettings([setting])\n  },\n\n\n}\n\n/**\n * Helper functions for common setting operations\n */\nexport const settingsHelpers = {\n  /**\n   * Convert setting value to appropriate type\n   */\n  convertValue(value: string, type: SettingType): any {\n    switch (type) {\n      case 'number':\n        return Number(value)\n      case 'boolean':\n        return value.toLowerCase() === 'true' || value === '1'\n      case 'json':\n        try {\n          return JSON.parse(value)\n        } catch {\n          return null\n        }\n      default:\n        return value\n    }\n  },\n\n  /**\n   * Validate setting value based on type\n   */\n  validateValue(value: string, type: SettingType): boolean {\n    try {\n      switch (type) {\n        case 'number':\n          return !isNaN(Number(value))\n        case 'boolean':\n          return ['true', 'false', '1', '0'].includes(value.toLowerCase())\n        case 'json':\n          JSON.parse(value)\n          return true\n        case 'email':\n          return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)\n        case 'url':\n          new URL(value)\n          return true\n        default:\n          return true\n      }\n    } catch {\n      return false\n    }\n  },\n}\n\nexport default settingsApi\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;AAED;;AAoGO,MAAM,cAAc;IACzB;;GAEC,GACD,MAAM,aAAY,OAAyB;QACzC,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI;QACrD,IAAI,SAAS,cAAc,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS,CAAC,QAAQ;QAC3F,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAE3D,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,MAAM,cAAc,CAAC,uBAAuB,EAAE,aAAa,GAAG;QAEpE,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;IAEA;;GAEC,GACD,MAAM,iBAAgB,GAAW;QAC/B,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK;IAChD;IAEA;;GAEC,GACD,MAAM,uBAAsB,QAAyB;QACnD,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,gCAAgC,EAAE,UAAU;IAC9D;IAEA;;GAEC,GACD,MAAM,eAAc,IAAyB;QAC3C,OAAO,wIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,0BAA0B;IAC5C;IAEA;;GAEC,GACD,MAAM,eAAc,GAAW,EAAE,IAAuB;QACtD,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK,EAAE;IAClD;IAEA;;GAEC,GACD,MAAM,eAAc,GAAW;QAC7B,OAAO,wIAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,KAAK;IACnD;IAEA;;GAEC,GACD,MAAM,oBAAmB,QAA+B;QACtD,OAAO,wIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,+BAA+B;YAAE;QAAS;IAC5D;IAEA;;GAEC,GACD,MAAM;QACJ,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;IAEA;;GAEC,GACD,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,CAAC,qBAAqB,CAAC;QAClD,MAAM,SAAiC,CAAC;QAExC,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAA;YACxB,MAAM,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK;QACrC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,sBAAqB,MAA8B;QACvD,MAAM,WAAkC,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,CAAC;gBACpF;gBACA;gBACA,UAAU;gBACV,MAAM;YACR,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC;IAEA;;GAEC,GACD,MAAM,uBAAsB,GAAW,EAAE,KAAa,EAAE,UAKpD,CAAC,CAAC;QACJ,MAAM,UAA+B;YACnC;YACA;YACA,UAAU,QAAQ,QAAQ,IAAI;YAC9B,MAAM,QAAQ,IAAI,IAAI;YACtB,aAAa,QAAQ,WAAW;YAChC,WAAW,QAAQ,SAAS,KAAK,YAAY,QAAQ,SAAS,GAAG;QACnE;QAEA,OAAO,IAAI,CAAC,kBAAkB,CAAC;YAAC;SAAQ;IAC1C;AAGF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,cAAa,KAAa,EAAE,IAAiB;QAC3C,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO;YAChB,KAAK;gBACH,OAAO,MAAM,WAAW,OAAO,UAAU,UAAU;YACrD,KAAK;gBACH,IAAI;oBACF,OAAO,KAAK,KAAK,CAAC;gBACpB,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,eAAc,KAAa,EAAE,IAAiB;QAC5C,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,OAAO,CAAC,MAAM,OAAO;gBACvB,KAAK;oBACH,OAAO;wBAAC;wBAAQ;wBAAS;wBAAK;qBAAI,CAAC,QAAQ,CAAC,MAAM,WAAW;gBAC/D,KAAK;oBACH,KAAK,KAAK,CAAC;oBACX,OAAO;gBACT,KAAK;oBACH,OAAO,6BAA6B,IAAI,CAAC;gBAC3C,KAAK;oBACH,IAAI,IAAI;oBACR,OAAO;gBACT;oBACE,OAAO;YACX;QACF,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/settings/useSettingsStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools } from 'zustand/middleware'\nimport { toast } from 'sonner'\nimport {\n  settingsApi,\n  type Setting,\n  type SettingCreationData,\n  type SettingUpdateData,\n  type SettingsFilters,\n  type SettingCategory\n} from '@/lib/api/settings'\n\ninterface SettingsState {\n  // State\n  settings: Setting[]\n  settingsByCategory: Record<SettingCategory, Setting[]>\n  currentSetting: Setting | null\n  filters: SettingsFilters\n\n  // Loading states\n  isLoading: boolean\n  isCreating: boolean\n  isUpdating: boolean\n  isDeleting: boolean\n  isBulkUpdating: boolean\n  isUploadingLogo: boolean\n  isUploadingFavicon: boolean\n  isRemovingLogo: boolean\n  isRemovingFavicon: boolean\n\n  // Error state\n  error: string | null\n  uploadError: string | null\n\n  // Platform branding state\n  platformBranding: {\n    logo: any | null\n    favicon: any | null\n  } | null\n\n  // Actions\n  fetchSettings: (filters?: SettingsFilters) => Promise<void>\n  fetchSettingByKey: (key: string) => Promise<Setting | null>\n  fetchSettingsByCategory: (category: SettingCategory) => Promise<void>\n  createSetting: (data: SettingCreationData) => Promise<void>\n  updateSetting: (key: string, data: SettingUpdateData) => Promise<void>\n  deleteSetting: (key: string) => Promise<void>\n  bulkUpdateSettings: (settings: SettingCreationData[]) => Promise<void>\n\n  // Utility actions\n  setFilters: (filters: Partial<SettingsFilters>) => void\n  clearFilters: () => void\n  clearError: () => void\n  clearCurrentSetting: () => void\n\n  // Getters\n  getSettingValue: (key: string) => string | null\n  getSettingsByCategory: (category: SettingCategory) => Setting[]\n  getPublicSettings: () => Setting[]\n\n  // Platform branding actions\n  fetchPlatformBranding: () => Promise<void>\n  uploadPlatformLogo: (file: File) => Promise<boolean>\n  uploadPlatformFavicon: (file: File) => Promise<boolean>\n  removePlatformLogo: () => Promise<boolean>\n  removePlatformFavicon: () => Promise<boolean>\n  processFavicon: (file: File) => Promise<boolean>\n  clearUploadError: () => void\n}\n\nexport const useSettingsStore = create<SettingsState>()(\n  devtools(\n    (set, get) => ({\n      // Initial state\n      settings: [],\n      settingsByCategory: {},\n      currentSetting: null,\n      isLoading: false,\n      isCreating: false,\n      isUpdating: false,\n      isDeleting: false,\n      isBulkUpdating: false,\n      isUploadingLogo: false,\n      isUploadingFavicon: false,\n      isRemovingLogo: false,\n      isRemovingFavicon: false,\n      error: null,\n      uploadError: null,\n      platformBranding: null,\n      filters: {\n        category: '',\n        scope: '',\n        search: '',\n      },\n\n      // Fetch settings\n      fetchSettings: async (filters = {}) => {\n        set({ isLoading: true })\n        try {\n          const currentFilters = { ...get().filters, ...filters }\n          const response = await settingsApi.getSettings(currentFilters)\n\n          // Group settings by category\n          const settingsByCategory = response.settings.reduce((acc: Record<string, Setting[]>, setting: Setting) => {\n            if (!acc[setting.category]) {\n              acc[setting.category] = []\n            }\n            acc[setting.category].push(setting)\n            return acc\n          }, {})\n\n          set({\n            settings: response.settings,\n            settingsByCategory,\n            isLoading: false,\n            error: null,\n          })\n        } catch (error) {\n          set({\n            isLoading: false,\n            error: (error as Error).message,\n          })\n          toast.error('Failed to fetch settings')\n        }\n      },\n\n      // Fetch setting by key\n      fetchSettingByKey: async (key) => {\n        try {\n          const setting = await settingsApi.getSettingByKey(key)\n\n          // Update current setting\n          set({ currentSetting: setting })\n\n          return setting\n        } catch (error) {\n          set({ error: (error as Error).message })\n          toast.error('Failed to fetch setting')\n          return null\n        }\n      },\n\n      // Fetch settings by category\n      fetchSettingsByCategory: async (category) => {\n        set({ isLoading: true })\n        try {\n          const response = await settingsApi.getSettingsByCategory(category)\n\n          // Update category-specific settings\n          set(state => ({\n            settingsByCategory: {\n              ...state.settingsByCategory,\n              [category]: response.settings\n            },\n            isLoading: false,\n            error: null\n          }))\n        } catch (error) {\n          set({\n            isLoading: false,\n            error: (error as Error).message,\n          })\n          toast.error('Failed to fetch settings by category')\n        }\n      },\n\n      // Create setting\n      createSetting: async (settingData) => {\n        try {\n          await settingsApi.createSetting(settingData)\n\n          // Refresh settings\n          await get().fetchSettings()\n\n          toast.success('Setting Created', {\n            description: 'Setting has been created successfully.'\n          })\n        } catch (error) {\n          set({ error: (error as Error).message })\n          toast.error('Failed to create setting')\n          throw error\n        }\n      },\n\n      // Update setting\n      updateSetting: async (id, updateData) => {\n        try {\n          const response = await fetch('/api/settings', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            credentials: 'include',\n            body: JSON.stringify({ ...updateData, id }),\n          })\n\n          if (!response.ok) {\n            throw new Error('Failed to update setting')\n          }\n\n          // Refresh settings\n          await get().fetchSettings()\n          \n          toast.success('Setting Updated', {\n            description: 'Setting has been updated successfully.'\n          })\n        } catch (error) {\n          set({ error: (error as Error).message })\n          toast.error('Failed to update setting')\n          throw error\n        }\n      },\n\n      // Delete setting\n      deleteSetting: async (id) => {\n        try {\n          const response = await fetch(`/api/settings/${id}`, {\n            method: 'DELETE',\n            credentials: 'include',\n          })\n\n          if (!response.ok) {\n            throw new Error('Failed to delete setting')\n          }\n\n          // Remove from local state\n          const currentSettings = get().settings\n          const updatedSettings = currentSettings.filter(setting => setting.id !== id)\n          \n          // Regroup by category\n          const settingsByCategory = updatedSettings.reduce((acc: Record<string, Setting[]>, setting: Setting) => {\n            if (!acc[setting.category]) {\n              acc[setting.category] = []\n            }\n            acc[setting.category].push(setting)\n            return acc\n          }, {})\n\n          set({\n            settings: updatedSettings,\n            settingsByCategory,\n          })\n          \n          toast.success('Setting Deleted', {\n            description: 'Setting has been deleted successfully.'\n          })\n        } catch (error) {\n          set({ error: (error as Error).message })\n          toast.error('Failed to delete setting')\n          throw error\n        }\n      },\n\n      // Bulk update settings\n      bulkUpdateSettings: async (settings) => {\n        set({ isBulkUpdating: true })\n        try {\n          const data = await settingsApi.bulkUpdateSettings(settings)\n\n          // Refresh settings\n          await get().fetchSettings()\n\n          toast.success('Settings Updated', {\n            description: `${data.success || settings.length} settings updated successfully.`\n          })\n        } catch (error) {\n          set({ error: (error as Error).message })\n          toast.error('Failed to update settings')\n          throw error\n        } finally {\n          set({ isBulkUpdating: false })\n        }\n      },\n\n      // Utility actions\n      setFilters: (filters: Partial<SettingsFilters>) => {\n        set(state => ({\n          filters: { ...state.filters, ...filters }\n        }))\n      },\n\n      clearFilters: () => {\n        set({ filters: {} })\n      },\n\n      clearError: () => {\n        set({ error: null })\n      },\n\n      clearCurrentSetting: () => {\n        set({ currentSetting: null })\n      },\n\n      // Getters\n      getSettingValue: (key: string) => {\n        const setting = get().settings.find(s => s.key === key)\n        return setting ? setting.value : null\n      },\n\n      getSettingsByCategory: (category: SettingCategory) => {\n        return get().settingsByCategory[category] || []\n      },\n\n      getPublicSettings: () => {\n        return get().settings.filter(setting => setting.is_public)\n      },\n\n      // Clear upload error\n      clearUploadError: () => {\n        set({ uploadError: null })\n      },\n\n      // Platform branding actions\n      fetchPlatformBranding: async () => {\n        try {\n          set({ isLoading: true, error: null })\n\n          const { fileUploadAPI } = await import('@/lib/api/file-upload')\n          const result = await fileUploadAPI.getPlatformBranding()\n\n          if (result.success) {\n            set({\n              platformBranding: result.data || { logo: null, favicon: null },\n              isLoading: false\n            })\n          } else {\n            throw new Error(result.message || 'Failed to fetch platform branding')\n          }\n        } catch (error) {\n          console.error('❌ Fetch platform branding error:', error)\n          set({\n            error: error instanceof Error ? error.message : 'Failed to fetch platform branding',\n            isLoading: false\n          })\n        }\n      },\n\n      uploadPlatformLogo: async (file: File) => {\n        try {\n          set({ isUploadingLogo: true, uploadError: null })\n\n          const { fileUploadAPI } = await import('@/lib/api/file-upload')\n          const result = await fileUploadAPI.uploadPlatformLogo(file)\n\n          if (result.success) {\n            // Refresh platform branding\n            await get().fetchPlatformBranding()\n            set({ isUploadingLogo: false })\n            return true\n          } else {\n            throw new Error(result.message || 'Failed to upload logo')\n          }\n        } catch (error) {\n          console.error('❌ Upload platform logo error:', error)\n          set({\n            uploadError: error instanceof Error ? error.message : 'Failed to upload logo',\n            isUploadingLogo: false\n          })\n          return false\n        }\n      },\n\n      uploadPlatformFavicon: async (file: File) => {\n        try {\n          set({ isUploadingFavicon: true, uploadError: null })\n\n          const { fileUploadAPI } = await import('@/lib/api/file-upload')\n          const result = await fileUploadAPI.uploadPlatformFavicon(file)\n\n          if (result.success) {\n            // Refresh platform branding\n            await get().fetchPlatformBranding()\n            set({ isUploadingFavicon: false })\n            return true\n          } else {\n            throw new Error(result.message || 'Failed to upload favicon')\n          }\n        } catch (error) {\n          console.error('❌ Upload platform favicon error:', error)\n          set({\n            uploadError: error instanceof Error ? error.message : 'Failed to upload favicon',\n            isUploadingFavicon: false\n          })\n          return false\n        }\n      },\n\n      removePlatformLogo: async () => {\n        try {\n          set({ isRemovingLogo: true, uploadError: null })\n\n          const { fileUploadAPI } = await import('@/lib/api/file-upload')\n          const result = await fileUploadAPI.removePlatformLogo()\n\n          if (result.success) {\n            // Refresh platform branding\n            await get().fetchPlatformBranding()\n            set({ isRemovingLogo: false })\n            return true\n          } else {\n            throw new Error(result.message || 'Failed to remove logo')\n          }\n        } catch (error) {\n          console.error('❌ Remove platform logo error:', error)\n          set({\n            uploadError: error instanceof Error ? error.message : 'Failed to remove logo',\n            isRemovingLogo: false\n          })\n          return false\n        }\n      },\n\n      removePlatformFavicon: async () => {\n        try {\n          set({ isRemovingFavicon: true, uploadError: null })\n\n          const { fileUploadAPI } = await import('@/lib/api/file-upload')\n          const result = await fileUploadAPI.removePlatformFavicon()\n\n          if (result.success) {\n            // Refresh platform branding\n            await get().fetchPlatformBranding()\n            set({ isRemovingFavicon: false })\n            return true\n          } else {\n            throw new Error(result.message || 'Failed to remove favicon')\n          }\n        } catch (error) {\n          console.error('❌ Remove platform favicon error:', error)\n          set({\n            uploadError: error instanceof Error ? error.message : 'Failed to remove favicon',\n            isRemovingFavicon: false\n          })\n          return false\n        }\n      },\n\n      processFavicon: async (file: File) => {\n        try {\n          set({ isUploadingFavicon: true, uploadError: null })\n\n          const { fileUploadAPI } = await import('@/lib/api/file-upload')\n          const result = await fileUploadAPI.processFavicon(file)\n\n          if (result.success) {\n            // Refresh platform branding\n            await get().fetchPlatformBranding()\n            set({ isUploadingFavicon: false })\n            return true\n          } else {\n            throw new Error(result.message || 'Failed to process favicon')\n          }\n        } catch (error) {\n          console.error('❌ Process favicon error:', error)\n          set({\n            uploadError: error instanceof Error ? error.message : 'Failed to process favicon',\n            isUploadingFavicon: false\n          })\n          return false\n        }\n      },\n\n    }),\n    {\n      name: 'settings-store',\n    }\n  )\n)\n\n// Helper hooks for specific setting categories\nexport const usePlatformSettings = () => {\n  const { settingsByCategory, fetchSettings } = useSettingsStore()\n  \n  return {\n    platformSettings: settingsByCategory.platform || [],\n    fetchPlatformSettings: () => fetchSettings({ category: 'platform' }),\n  }\n}\n\nexport const useSecuritySettings = () => {\n  const { settingsByCategory, fetchSettings } = useSettingsStore()\n  \n  return {\n    securitySettings: settingsByCategory.security || [],\n    fetchSecuritySettings: () => fetchSettings({ category: 'security' }),\n  }\n}\n\n// Helper function to get setting values\nexport const useSettingValue = (key: string): string | null => {\n  const getSettingValue = useSettingsStore(state => state.getSettingValue)\n  return getSettingValue(key)\n}\n\n// Helper function to check if a setting exists\nexport const useHasSetting = (key: string): boolean => {\n  const settings = useSettingsStore(state => state.settings)\n  return settings.some(setting => setting.key === key)\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAmEO,MAAM,mBAAmB,CAAA,GAAA,wPAAA,CAAA,SAAM,AAAD,IACnC,CAAA,GAAA,6PAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,UAAU,EAAE;QACZ,oBAAoB,CAAC;QACrB,gBAAgB;QAChB,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAChB,mBAAmB;QACnB,OAAO;QACP,aAAa;QACb,kBAAkB;QAClB,SAAS;YACP,UAAU;YACV,OAAO;YACP,QAAQ;QACV;QAEA,iBAAiB;QACjB,eAAe,OAAO,UAAU,CAAC,CAAC;YAChC,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,iBAAiB;oBAAE,GAAG,MAAM,OAAO;oBAAE,GAAG,OAAO;gBAAC;gBACtD,MAAM,WAAW,MAAM,oJAAA,CAAA,cAAW,CAAC,WAAW,CAAC;gBAE/C,6BAA6B;gBAC7B,MAAM,qBAAqB,SAAS,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAgC;oBACnF,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;wBAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;oBAC5B;oBACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;oBAC3B,OAAO;gBACT,GAAG,CAAC;gBAEJ,IAAI;oBACF,UAAU,SAAS,QAAQ;oBAC3B;oBACA,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,WAAW;oBACX,OAAO,AAAC,MAAgB,OAAO;gBACjC;gBACA,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,uBAAuB;QACvB,mBAAmB,OAAO;YACxB,IAAI;gBACF,MAAM,UAAU,MAAM,oJAAA,CAAA,cAAW,CAAC,eAAe,CAAC;gBAElD,yBAAyB;gBACzB,IAAI;oBAAE,gBAAgB;gBAAQ;gBAE9B,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;gBACtC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,6BAA6B;QAC7B,yBAAyB,OAAO;YAC9B,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,oJAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC;gBAEzD,oCAAoC;gBACpC,IAAI,CAAA,QAAS,CAAC;wBACZ,oBAAoB;4BAClB,GAAG,MAAM,kBAAkB;4BAC3B,CAAC,SAAS,EAAE,SAAS,QAAQ;wBAC/B;wBACA,WAAW;wBACX,OAAO;oBACT,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,WAAW;oBACX,OAAO,AAAC,MAAgB,OAAO;gBACjC;gBACA,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,iBAAiB;QACjB,eAAe,OAAO;YACpB,IAAI;gBACF,MAAM,oJAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAEhC,mBAAmB;gBACnB,MAAM,MAAM,aAAa;gBAEzB,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;oBAC/B,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;gBACtC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,iBAAiB;QACjB,eAAe,OAAO,IAAI;YACxB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;oBAC5C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,aAAa;oBACb,MAAM,KAAK,SAAS,CAAC;wBAAE,GAAG,UAAU;wBAAE;oBAAG;gBAC3C;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,mBAAmB;gBACnB,MAAM,MAAM,aAAa;gBAEzB,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;oBAC/B,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;gBACtC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,iBAAiB;QACjB,eAAe,OAAO;YACpB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;oBAClD,QAAQ;oBACR,aAAa;gBACf;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,0BAA0B;gBAC1B,MAAM,kBAAkB,MAAM,QAAQ;gBACtC,MAAM,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;gBAEzE,sBAAsB;gBACtB,MAAM,qBAAqB,gBAAgB,MAAM,CAAC,CAAC,KAAgC;oBACjF,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;wBAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;oBAC5B;oBACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;oBAC3B,OAAO;gBACT,GAAG,CAAC;gBAEJ,IAAI;oBACF,UAAU;oBACV;gBACF;gBAEA,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;oBAC/B,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;gBACtC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,uBAAuB;QACvB,oBAAoB,OAAO;YACzB,IAAI;gBAAE,gBAAgB;YAAK;YAC3B,IAAI;gBACF,MAAM,OAAO,MAAM,oJAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;gBAElD,mBAAmB;gBACnB,MAAM,MAAM,aAAa;gBAEzB,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC,oBAAoB;oBAChC,aAAa,GAAG,KAAK,OAAO,IAAI,SAAS,MAAM,CAAC,+BAA+B,CAAC;gBAClF;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO,AAAC,MAAgB,OAAO;gBAAC;gBACtC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,gBAAgB;gBAAM;YAC9B;QACF;QAEA,kBAAkB;QAClB,YAAY,CAAC;YACX,IAAI,CAAA,QAAS,CAAC;oBACZ,SAAS;wBAAE,GAAG,MAAM,OAAO;wBAAE,GAAG,OAAO;oBAAC;gBAC1C,CAAC;QACH;QAEA,cAAc;YACZ,IAAI;gBAAE,SAAS,CAAC;YAAE;QACpB;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,qBAAqB;YACnB,IAAI;gBAAE,gBAAgB;YAAK;QAC7B;QAEA,UAAU;QACV,iBAAiB,CAAC;YAChB,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;YACnD,OAAO,UAAU,QAAQ,KAAK,GAAG;QACnC;QAEA,uBAAuB,CAAC;YACtB,OAAO,MAAM,kBAAkB,CAAC,SAAS,IAAI,EAAE;QACjD;QAEA,mBAAmB;YACjB,OAAO,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS;QAC3D;QAEA,qBAAqB;QACrB,kBAAkB;YAChB,IAAI;gBAAE,aAAa;YAAK;QAC1B;QAEA,4BAA4B;QAC5B,uBAAuB;YACrB,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,MAAM,SAAS,MAAM,cAAc,mBAAmB;gBAEtD,IAAI,OAAO,OAAO,EAAE;oBAClB,IAAI;wBACF,kBAAkB,OAAO,IAAI,IAAI;4BAAE,MAAM;4BAAM,SAAS;wBAAK;wBAC7D,WAAW;oBACb;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;YACF;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,iBAAiB;oBAAM,aAAa;gBAAK;gBAE/C,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,MAAM,SAAS,MAAM,cAAc,kBAAkB,CAAC;gBAEtD,IAAI,OAAO,OAAO,EAAE;oBAClB,4BAA4B;oBAC5B,MAAM,MAAM,qBAAqB;oBACjC,IAAI;wBAAE,iBAAiB;oBAAM;oBAC7B,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,IAAI;oBACF,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACtD,iBAAiB;gBACnB;gBACA,OAAO;YACT;QACF;QAEA,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,oBAAoB;oBAAM,aAAa;gBAAK;gBAElD,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,MAAM,SAAS,MAAM,cAAc,qBAAqB,CAAC;gBAEzD,IAAI,OAAO,OAAO,EAAE;oBAClB,4BAA4B;oBAC5B,MAAM,MAAM,qBAAqB;oBACjC,IAAI;wBAAE,oBAAoB;oBAAM;oBAChC,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,IAAI;oBACF,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACtD,oBAAoB;gBACtB;gBACA,OAAO;YACT;QACF;QAEA,oBAAoB;YAClB,IAAI;gBACF,IAAI;oBAAE,gBAAgB;oBAAM,aAAa;gBAAK;gBAE9C,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,MAAM,SAAS,MAAM,cAAc,kBAAkB;gBAErD,IAAI,OAAO,OAAO,EAAE;oBAClB,4BAA4B;oBAC5B,MAAM,MAAM,qBAAqB;oBACjC,IAAI;wBAAE,gBAAgB;oBAAM;oBAC5B,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,IAAI;oBACF,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACtD,gBAAgB;gBAClB;gBACA,OAAO;YACT;QACF;QAEA,uBAAuB;YACrB,IAAI;gBACF,IAAI;oBAAE,mBAAmB;oBAAM,aAAa;gBAAK;gBAEjD,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,MAAM,SAAS,MAAM,cAAc,qBAAqB;gBAExD,IAAI,OAAO,OAAO,EAAE;oBAClB,4BAA4B;oBAC5B,MAAM,MAAM,qBAAqB;oBACjC,IAAI;wBAAE,mBAAmB;oBAAM;oBAC/B,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,IAAI;oBACF,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACtD,mBAAmB;gBACrB;gBACA,OAAO;YACT;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,oBAAoB;oBAAM,aAAa;gBAAK;gBAElD,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,MAAM,SAAS,MAAM,cAAc,cAAc,CAAC;gBAElD,IAAI,OAAO,OAAO,EAAE;oBAClB,4BAA4B;oBAC5B,MAAM,MAAM,qBAAqB;oBACjC,IAAI;wBAAE,oBAAoB;oBAAM;oBAChC,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,IAAI;oBACF,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACtD,oBAAoB;gBACtB;gBACA,OAAO;YACT;QACF;IAEF,CAAC,GACD;IACE,MAAM;AACR;AAKG,MAAM,sBAAsB;;IACjC,MAAM,EAAE,kBAAkB,EAAE,aAAa,EAAE,GAAG;IAE9C,OAAO;QACL,kBAAkB,mBAAmB,QAAQ,IAAI,EAAE;QACnD,uBAAuB,IAAM,cAAc;gBAAE,UAAU;YAAW;IACpE;AACF;GAPa;;QACmC;;;AAQzC,MAAM,sBAAsB;;IACjC,MAAM,EAAE,kBAAkB,EAAE,aAAa,EAAE,GAAG;IAE9C,OAAO;QACL,kBAAkB,mBAAmB,QAAQ,IAAI,EAAE;QACnD,uBAAuB,IAAM,cAAc;gBAAE,UAAU;YAAW;IACpE;AACF;IAPa;;QACmC;;;AASzC,MAAM,kBAAkB,CAAC;;IAC9B,MAAM,kBAAkB;6DAAiB,CAAA,QAAS,MAAM,eAAe;;IACvE,OAAO,gBAAgB;AACzB;IAHa;;QACa;;;AAKnB,MAAM,gBAAgB,CAAC;;IAC5B,MAAM,WAAW;oDAAiB,CAAA,QAAS,MAAM,QAAQ;;IACzD,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,GAAG,KAAK;AAClD;IAHa;;QACM", "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/app/super-admin/settings/platform/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Formik, Form, Field } from 'formik'\nimport * as Yup from 'yup'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Switch } from '@/components/ui/switch'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { toast } from 'sonner'\nimport { useSettingsStore } from '@/stores/settings/useSettingsStore'\nimport { settingsApi, type SettingCreationData } from '@/lib/api/settings'\nimport { fileUploadAPI } from '@/lib/api/file-upload'\nimport { BrandingUpload } from '@/components/platform/branding-upload'\nimport { X, Upload, Image, Settings, Globe, Shield, Bell } from 'lucide-react'\n\nconst platformSettingsSchema = Yup.object({\n  platform_name: Yup.string().required('Platform name is required'),\n  platform_url: Yup.string().url('Invalid URL').required('Platform URL is required'),\n  support_email: Yup.string().email('Invalid email').required('Support email is required'),\n  platform_address: Yup.string().nullable(),\n  platform_tagline: Yup.string().nullable(),\n  platform_logo: Yup.mixed().nullable(),\n  platform_favicon: Yup.mixed().nullable(),\n  maintenance_mode: Yup.boolean(),\n  allow_registration: Yup.boolean(),\n  require_email_verification: Yup.boolean()\n})\n\nexport default function PlatformSettingsPage() {\n  const [isLoading, setIsLoading] = useState(true)\n  const [isSaving, setIsSaving] = useState(false)\n  const [currentLogo, setCurrentLogo] = useState<string | null>(null)\n  const [currentFavicon, setCurrentFavicon] = useState<string | null>(null)\n  const [isUploadingLogo, setIsUploadingLogo] = useState(false)\n  const [isUploadingFavicon, setIsUploadingFavicon] = useState(false)\n\n  // Helper function to fetch media URL by ID\n  const fetchMediaUrl = async (mediaId: string): Promise<string | null> => {\n    try {\n      console.log('🔍 Fetching media URL for ID:', mediaId)\n\n      // Get auth token from localStorage or useAuthStore\n      const token = localStorage.getItem('token') || localStorage.getItem('payload-token')\n      console.log('🔑 Using auth token:', token ? 'Token found' : 'No token')\n\n      // Use Payload's REST API to get media by ID\n      const response = await fetch(`http://localhost:3001/api/media/${mediaId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      })\n\n      console.log('📡 Media fetch response status:', response.status)\n\n      if (response.ok) {\n        const media = await response.json()\n        console.log('📦 Media data received:', media)\n\n        if (media.url) {\n          const fullUrl = fileUploadAPI.getFileUrl(media.url)\n          console.log('✅ Media URL resolved:', fullUrl)\n          return fullUrl\n        } else {\n          console.log('❌ No URL found in media data')\n        }\n      } else {\n        console.log('❌ Media fetch failed:', response.status, response.statusText)\n      }\n    } catch (error) {\n      console.error('❌ Error fetching media URL:', error)\n    }\n    return null\n  }\n  const [initialValues, setInitialValues] = useState({\n    platform_name: 'KISS LMS',\n    platform_url: 'https://groups-exam.com',\n    support_email: '<EMAIL>',\n    platform_address: '',\n    platform_tagline: 'Empowering Education Through Technology',\n    platform_logo: null as File | null,\n    platform_favicon: null as File | null,\n    maintenance_mode: false,\n    allow_registration: true,\n    require_email_verification: true\n  })\n  const { fetchSettingsByCategory } = useSettingsStore()\n\n  // Load settings on mount\n  useEffect(() => {\n    const loadSettings = async () => {\n      try {\n        setIsLoading(true)\n        await fetchSettingsByCategory('platform')\n\n        // Fetch platform settings to populate form\n        const response = await settingsApi.getSettingsByCategory('platform')\n\n        // Convert settings array to form values\n        const formValues = { ...initialValues }\n        response.settings.forEach(setting => {\n          if (setting.key in formValues) {\n            if (setting.type === 'boolean') {\n              (formValues as any)[setting.key] = setting.value === 'true'\n            } else {\n              (formValues as any)[setting.key] = setting.value\n            }\n          }\n\n          // Handle logo and favicon - check for populated mediaData first\n          if (setting.key === 'platform_logo') {\n            if (setting.type === 'media' && setting.mediaData?.url) {\n              // Use populated media data\n              setCurrentLogo(fileUploadAPI.getFileUrl(setting.mediaData.url))\n            } else if (setting.value) {\n              // Fallback: direct URL or fetch by ID\n              if (setting.type === 'media') {\n                fetchMediaUrl(setting.value).then(url => {\n                  if (url) setCurrentLogo(url)\n                })\n              } else {\n                setCurrentLogo(fileUploadAPI.getFileUrl(setting.value))\n              }\n            }\n          }\n\n          if (setting.key === 'platform_favicon') {\n            if (setting.type === 'media' && setting.mediaData?.url) {\n              // Use populated media data\n              setCurrentFavicon(fileUploadAPI.getFileUrl(setting.mediaData.url))\n            } else if (setting.value) {\n              // Fallback: direct URL or fetch by ID\n              if (setting.type === 'media') {\n                fetchMediaUrl(setting.value).then(url => {\n                  if (url) setCurrentFavicon(url)\n                })\n              } else {\n                setCurrentFavicon(fileUploadAPI.getFileUrl(setting.value))\n              }\n            }\n          }\n        })\n\n        setInitialValues(formValues)\n      } catch (error) {\n        console.error('Error loading settings:', error)\n        toast.error('Failed to load settings')\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    loadSettings()\n  }, [])\n\n  // Helper function to create or update a setting\n  const createOrUpdateSetting = async (key: string, value: string) => {\n    try {\n      console.log(`🔄 Creating/updating setting: ${key} = \"${value}\"`)\n\n      const result = await settingsApi.createOrUpdateSetting(key, value, {\n        category: 'platform',\n        type: 'media',\n        description: key === 'platform_logo' ? 'Platform logo media ID' : 'Platform favicon media ID',\n        is_public: true\n      })\n\n      console.log('✅ Setting operation result:', result)\n\n      if (result.errors && result.errors.length > 0) {\n        console.error('❌ Setting errors:', result.errors)\n        throw new Error(result.errors[0].error)\n      }\n\n    } catch (error: any) {\n      console.error('❌ Error in createOrUpdateSetting:', error)\n      throw error\n    }\n  }\n\n  // Remove logo function\n  const removeLogo = async () => {\n    try {\n      // Remove from settings\n      await createOrUpdateSetting('platform_logo', '')\n      setCurrentLogo(null)\n      toast.success('Logo removed')\n    } catch (error) {\n      console.error('Error removing logo:', error)\n      toast.error('Failed to remove logo')\n    }\n  }\n\n  // Remove favicon function\n  const removeFavicon = async () => {\n    try {\n      // Remove from settings\n      await createOrUpdateSetting('platform_favicon', '')\n      setCurrentFavicon(null)\n      toast.success('Favicon removed')\n    } catch (error) {\n      console.error('Error removing favicon:', error)\n      toast.error('Failed to remove favicon')\n    }\n  }\n\n  // Upload logo function\n  const handleLogoUpload = async (file: File) => {\n    if (!file) return\n\n    setIsUploadingLogo(true)\n    try {\n      console.log('📤 Uploading platform logo:', file.name)\n\n      // Upload file using the file upload API\n      const result = await fileUploadAPI.uploadFile(file, {\n        uploadType: 'platform_logo',\n        folder: 'platform'\n      })\n\n      if (result.success && result.media) {\n        console.log('✅ Logo uploaded successfully:', result.media.url)\n\n        // Save media ID to platform settings (convert to string)\n        await createOrUpdateSetting('platform_logo', result.media.id.toString())\n\n        // Update current logo display\n        setCurrentLogo(fileUploadAPI.getFileUrl(result.media.url))\n        toast.success('Logo uploaded successfully')\n      } else {\n        throw new Error(result.message || 'Upload failed')\n      }\n    } catch (error) {\n      console.error('❌ Logo upload error:', error)\n      toast.error('Failed to upload logo')\n    } finally {\n      setIsUploadingLogo(false)\n    }\n  }\n\n  // Upload favicon function\n  const handleFaviconUpload = async (file: File) => {\n    if (!file) return\n\n    setIsUploadingFavicon(true)\n    try {\n      console.log('📤 Uploading platform favicon:', file.name)\n\n      // Upload file using the file upload API\n      const result = await fileUploadAPI.uploadFile(file, {\n        uploadType: 'platform_favicon',\n        folder: 'platform'\n      })\n\n      if (result.success && result.media) {\n        console.log('✅ Favicon uploaded successfully:', result.media.url)\n\n        // Save media ID to platform settings (convert to string)\n        await createOrUpdateSetting('platform_favicon', result.media.id.toString())\n\n        // Update current favicon display\n        setCurrentFavicon(fileUploadAPI.getFileUrl(result.media.url))\n        toast.success('Favicon uploaded successfully')\n      } else {\n        throw new Error(result.message || 'Upload failed')\n      }\n    } catch (error) {\n      console.error('❌ Favicon upload error:', error)\n      toast.error('Failed to upload favicon')\n    } finally {\n      setIsUploadingFavicon(false)\n    }\n  }\n\n\n\n  const handleSubmit = async (values: any) => {\n    setIsSaving(true)\n    try {\n      // Convert form values to settings\n      // Convert form values to settings - only include platform-specific settings\n      const settingsToUpdate: SettingCreationData[] = []\n\n      // Add required platform settings\n      if (values.platform_name) {\n        settingsToUpdate.push({\n          key: 'platform_name',\n          value: values.platform_name,\n          category: 'platform',\n          type: 'string',\n          is_public: true\n        })\n      }\n\n      if (values.platform_url) {\n        settingsToUpdate.push({\n          key: 'platform_url',\n          value: values.platform_url,\n          category: 'platform',\n          type: 'url',\n          is_public: true\n        })\n      }\n\n      if (values.support_email) {\n        settingsToUpdate.push({\n          key: 'support_email',\n          value: values.support_email,\n          category: 'platform',\n          type: 'email',\n          is_public: true\n        })\n      }\n\n      // Optional fields - only add if they have non-empty values\n      if (values.platform_address && values.platform_address.trim() !== '') {\n        settingsToUpdate.push({\n          key: 'platform_address',\n          value: values.platform_address,\n          category: 'platform',\n          type: 'textarea',\n          is_public: true\n        })\n      }\n\n      if (values.platform_tagline && values.platform_tagline.trim() !== '') {\n        settingsToUpdate.push({\n          key: 'platform_tagline',\n          value: values.platform_tagline,\n          category: 'platform',\n          type: 'string',\n          is_public: true\n        })\n      }\n\n      settingsToUpdate.push(\n        {\n          key: 'platform_logo',\n          value: values.platform_logo.toString(),\n          category: 'platform',\n          type: 'url',\n          is_public: true\n        },\n        {\n          key: 'platform_favicon',\n          value: values.platform_favicon.toString(),\n          category: 'platform',\n          type: 'url',\n          is_public: true\n        }\n      )\n\n      // Boolean settings - always include\n      settingsToUpdate.push(\n        {\n          key: 'maintenance_mode',\n          value: values.maintenance_mode.toString(),\n          category: 'platform',\n          type: 'boolean',\n          is_public: false\n        },\n        {\n          key: 'allow_registration',\n          value: values.allow_registration.toString(),\n          category: 'platform',\n          type: 'boolean',\n          is_public: false\n        },\n        {\n          key: 'require_email_verification',\n          value: values.require_email_verification.toString(),\n          category: 'platform',\n          type: 'boolean',\n          is_public: false\n        }\n      )\n\n      // File uploads are handled immediately when files are selected\n      // No need to handle them in form submission\n\n      // Save settings using the API\n      await settingsApi.bulkUpdateSettings(settingsToUpdate)\n\n      toast.success('Platform settings saved successfully')\n    } catch (error) {\n      toast.error('Failed to save settings')\n      console.error(error)\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"p-8\">\n        <div className=\"max-w-4xl mx-auto space-y-6\">\n          <div>\n            <h1 className=\"text-2xl font-bold\">Platform Settings</h1>\n            <p className=\"text-muted-foreground\">Loading settings...</p>\n          </div>\n          <div className=\"flex justify-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"p-8\">\n      <div className=\"max-w-4xl mx-auto space-y-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold\">Platform Settings</h1>\n          <p className=\"text-muted-foreground\">Configure global platform settings and policies</p>\n        </div>\n\n        <Formik\n          initialValues={initialValues}\n          validationSchema={platformSettingsSchema}\n          onSubmit={handleSubmit}\n          enableReinitialize={true}\n        >\n          {({ errors, touched, values, setFieldValue, isValid }) => (\n            <Form className=\"space-y-6\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>General Configuration</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"platform_name\">Platform Name</Label>\n                      <Field\n                        as={Input}\n                        id=\"platform_name\"\n                        name=\"platform_name\"\n                        placeholder=\"Groups Exam LMS\"\n                      />\n                      {errors.platform_name && touched.platform_name && (\n                        <p className=\"text-sm text-destructive\">{errors.platform_name}</p>\n                      )}\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"platform_url\">Platform URL</Label>\n                      <Field\n                        as={Input}\n                        id=\"platform_url\"\n                        name=\"platform_url\"\n                        placeholder=\"https://groups-exam.com\"\n                      />\n                      {errors.platform_url && touched.platform_url && (\n                        <p className=\"text-sm text-destructive\">{errors.platform_url}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"support_email\">Support Email</Label>\n                    <Field\n                      as={Input}\n                      id=\"support_email\"\n                      name=\"support_email\"\n                      type=\"email\"\n                      placeholder=\"<EMAIL>\"\n                    />\n                    {errors.support_email && touched.support_email && (\n                      <p className=\"text-sm text-destructive\">{errors.support_email}</p>\n                    )}\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"platform_tagline\">Platform Tagline</Label>\n                    <Field\n                      as={Input}\n                      id=\"platform_tagline\"\n                      name=\"platform_tagline\"\n                      placeholder=\"Empowering Education Through Technology\"\n                    />\n                    {errors.platform_tagline && touched.platform_tagline && (\n                      <p className=\"text-sm text-destructive\">{errors.platform_tagline}</p>\n                    )}\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"platform_address\">Platform Address</Label>\n                    <Field\n                      as={Textarea}\n                      id=\"platform_address\"\n                      name=\"platform_address\"\n                      placeholder=\"Enter your organization's physical address\"\n                      rows={3}\n                    />\n                    {errors.platform_address && touched.platform_address && (\n                      <p className=\"text-sm text-destructive\">{errors.platform_address}</p>\n                    )}\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"platform_logo\">Platform Logo</Label>\n\n                      {/* Current Logo Display */}\n                      {currentLogo && (\n                        <div className=\"relative inline-block\">\n                          <img\n                            src={currentLogo}\n                            alt=\"Current Logo\"\n                            className=\"h-20 w-auto border rounded-md\"\n                          />\n                          <Button\n                            type=\"button\"\n                            variant=\"destructive\"\n                            size=\"sm\"\n                            className=\"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0\"\n                            onClick={removeLogo}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      )}\n\n                      <div className=\"flex items-center space-x-2\">\n                        <Input\n                          id=\"platform_logo\"\n                          name=\"platform_logo\"\n                          type=\"file\"\n                          accept=\"image/*\"\n                          onChange={async (e) => {\n                            const file = e.target.files?.[0]\n                            if (file) {\n                              await handleLogoUpload(file)\n                              // Clear the input\n                              e.target.value = ''\n                            }\n                          }}\n                          className=\"hidden\"\n                        />\n                        <Button\n                          type=\"button\"\n                          variant=\"outline\"\n                          disabled={isUploadingLogo}\n                          onClick={() => document.getElementById('platform_logo')?.click()}\n                        >\n                          <Upload className=\"h-4 w-4 mr-2\" />\n                          {isUploadingLogo ? 'Uploading...' : (currentLogo ? 'Change Logo' : 'Upload Logo')}\n                        </Button>\n                      </div>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Upload your platform logo (PNG, JPG, SVG)\n                      </p>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"platform_favicon\">Platform Favicon</Label>\n\n                      {/* Current Favicon Display */}\n                      {currentFavicon && (\n                        <div className=\"relative inline-block\">\n                          <img\n                            src={currentFavicon}\n                            alt=\"Current Favicon\"\n                            className=\"h-8 w-8 border rounded-md\"\n                          />\n                          <Button\n                            type=\"button\"\n                            variant=\"destructive\"\n                            size=\"sm\"\n                            className=\"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0\"\n                            onClick={removeFavicon}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      )}\n\n                      <div className=\"flex items-center space-x-2\">\n                        <Input\n                          id=\"platform_favicon\"\n                          name=\"platform_favicon\"\n                          type=\"file\"\n                          accept=\"image/*,.ico\"\n                          onChange={async (e) => {\n                            const file = e.target.files?.[0]\n                            if (file) {\n                              await handleFaviconUpload(file)\n                              // Clear the input\n                              e.target.value = ''\n                            }\n                          }}\n                          className=\"hidden\"\n                        />\n                        <Button\n                          type=\"button\"\n                          variant=\"outline\"\n                          disabled={isUploadingFavicon}\n                          onClick={() => document.getElementById('platform_favicon')?.click()}\n                        >\n                          <Image className=\"h-4 w-4 mr-2\" />\n                          {isUploadingFavicon ? 'Uploading...' : (currentFavicon ? 'Change Favicon' : 'Upload Favicon')}\n                        </Button>\n                      </div>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Upload your platform favicon (ICO, PNG)\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <Label>Maintenance Mode</Label>\n                        <p className=\"text-sm text-gray-500\">\n                          Enable to put the platform in maintenance mode\n                        </p>\n                      </div>\n                      <Switch\n                        checked={values.maintenance_mode}\n                        onCheckedChange={(checked) => setFieldValue('maintenance_mode', checked)}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <Label>Allow Institute Registration</Label>\n                        <p className=\"text-sm text-gray-500\">\n                          Allow new institutes to register on the platform\n                        </p>\n                      </div>\n                      <Switch\n                        checked={values.allow_registration}\n                        onCheckedChange={(checked) => setFieldValue('allow_registration', checked)}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <Label>Require Email Verification</Label>\n                        <p className=\"text-sm text-gray-500\">\n                          Require email verification for new user accounts\n                        </p>\n                      </div>\n                      <Switch\n                        checked={values.require_email_verification}\n                        onCheckedChange={(checked) => setFieldValue('require_email_verification', checked)}\n                      />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n\n              <div className=\"flex justify-end space-x-4\">\n                <Button type=\"button\" variant=\"outline\">\n                  Reset to Defaults\n                </Button>\n                <div className=\"space-y-2\">\n                  {Object.keys(errors).length > 0 && (\n                    <div className=\"text-sm text-destructive\">\n                      Form has validation errors: {Object.keys(errors).join(', ')}\n                    </div>\n                  )}\n                  <Button\n                    type=\"submit\"\n                    disabled={isSaving || !isValid}\n\n                  >\n                    {isSaving ? 'Saving...' : 'Save Settings'}\n                  </Button>\n                </div>\n              </div>\n            </Form>\n          )}\n        </Formik>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;;;AAhBA;;;;;;;;;;;;;;;AAkBA,MAAM,yBAAyB,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,EAAE;IACxC,eAAe,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC;IACrC,cAAc,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,IAAI,GAAG,CAAC,eAAe,QAAQ,CAAC;IACvD,eAAe,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC,iBAAiB,QAAQ,CAAC;IAC5D,kBAAkB,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,IAAI,QAAQ;IACvC,kBAAkB,CAAA,GAAA,sLAAA,CAAA,SAAU,AAAD,IAAI,QAAQ;IACvC,eAAe,CAAA,GAAA,sLAAA,CAAA,QAAS,AAAD,IAAI,QAAQ;IACnC,kBAAkB,CAAA,GAAA,sLAAA,CAAA,QAAS,AAAD,IAAI,QAAQ;IACtC,kBAAkB,CAAA,GAAA,sLAAA,CAAA,UAAW,AAAD;IAC5B,oBAAoB,CAAA,GAAA,sLAAA,CAAA,UAAW,AAAD;IAC9B,4BAA4B,CAAA,GAAA,sLAAA,CAAA,UAAW,AAAD;AACxC;AAEe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,2CAA2C;IAC3C,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,mDAAmD;YACnD,MAAM,QAAQ,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;YACpE,QAAQ,GAAG,CAAC,wBAAwB,QAAQ,gBAAgB;YAE5D,4CAA4C;YAC5C,MAAM,WAAW,MAAM,MAAM,CAAC,gCAAgC,EAAE,SAAS,EAAE;gBACzE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,mCAAmC,SAAS,MAAM;YAE9D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,QAAQ,GAAG,CAAC,2BAA2B;gBAEvC,IAAI,MAAM,GAAG,EAAE;oBACb,MAAM,UAAU,0JAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,MAAM,GAAG;oBAClD,QAAQ,GAAG,CAAC,yBAAyB;oBACrC,OAAO;gBACT,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,yBAAyB,SAAS,MAAM,EAAE,SAAS,UAAU;YAC3E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;QACA,OAAO;IACT;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,eAAe;QACf,cAAc;QACd,eAAe;QACf,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,4BAA4B;IAC9B;IACA,MAAM,EAAE,uBAAuB,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,mBAAgB,AAAD;IAEnD,yBAAyB;IACzB,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;+DAAe;oBACnB,IAAI;wBACF,aAAa;wBACb,MAAM,wBAAwB;wBAE9B,2CAA2C;wBAC3C,MAAM,WAAW,MAAM,oJAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC;wBAEzD,wCAAwC;wBACxC,MAAM,aAAa;4BAAE,GAAG,aAAa;wBAAC;wBACtC,SAAS,QAAQ,CAAC,OAAO;2EAAC,CAAA;gCACxB,IAAI,QAAQ,GAAG,IAAI,YAAY;oCAC7B,IAAI,QAAQ,IAAI,KAAK,WAAW;wCAC7B,UAAkB,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK,KAAK;oCACvD,OAAO;wCACJ,UAAkB,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK;oCAClD;gCACF;gCAEA,gEAAgE;gCAChE,IAAI,QAAQ,GAAG,KAAK,iBAAiB;oCACnC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,SAAS,EAAE,KAAK;wCACtD,2BAA2B;wCAC3B,eAAe,0JAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,QAAQ,SAAS,CAAC,GAAG;oCAC/D,OAAO,IAAI,QAAQ,KAAK,EAAE;wCACxB,sCAAsC;wCACtC,IAAI,QAAQ,IAAI,KAAK,SAAS;4CAC5B,cAAc,QAAQ,KAAK,EAAE,IAAI;+FAAC,CAAA;oDAChC,IAAI,KAAK,eAAe;gDAC1B;;wCACF,OAAO;4CACL,eAAe,0JAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,QAAQ,KAAK;wCACvD;oCACF;gCACF;gCAEA,IAAI,QAAQ,GAAG,KAAK,oBAAoB;oCACtC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,SAAS,EAAE,KAAK;wCACtD,2BAA2B;wCAC3B,kBAAkB,0JAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,QAAQ,SAAS,CAAC,GAAG;oCAClE,OAAO,IAAI,QAAQ,KAAK,EAAE;wCACxB,sCAAsC;wCACtC,IAAI,QAAQ,IAAI,KAAK,SAAS;4CAC5B,cAAc,QAAQ,KAAK,EAAE,IAAI;+FAAC,CAAA;oDAChC,IAAI,KAAK,kBAAkB;gDAC7B;;wCACF,OAAO;4CACL,kBAAkB,0JAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,QAAQ,KAAK;wCAC1D;oCACF;gCACF;4BACF;;wBAEA,iBAAiB;oBACnB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;yCAAG,EAAE;IAEL,gDAAgD;IAChD,MAAM,wBAAwB,OAAO,KAAa;QAChD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,IAAI,IAAI,EAAE,MAAM,CAAC,CAAC;YAE/D,MAAM,SAAS,MAAM,oJAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC,KAAK,OAAO;gBACjE,UAAU;gBACV,MAAM;gBACN,aAAa,QAAQ,kBAAkB,2BAA2B;gBAClE,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;gBAC7C,QAAQ,KAAK,CAAC,qBAAqB,OAAO,MAAM;gBAChD,MAAM,IAAI,MAAM,OAAO,MAAM,CAAC,EAAE,CAAC,KAAK;YACxC;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,aAAa;QACjB,IAAI;YACF,uBAAuB;YACvB,MAAM,sBAAsB,iBAAiB;YAC7C,eAAe;YACf,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB;QACpB,IAAI;YACF,uBAAuB;YACvB,MAAM,sBAAsB,oBAAoB;YAChD,kBAAkB;YAClB,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,MAAM;QAEX,mBAAmB;QACnB,IAAI;YACF,QAAQ,GAAG,CAAC,+BAA+B,KAAK,IAAI;YAEpD,wCAAwC;YACxC,MAAM,SAAS,MAAM,0JAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,MAAM;gBAClD,YAAY;gBACZ,QAAQ;YACV;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBAClC,QAAQ,GAAG,CAAC,iCAAiC,OAAO,KAAK,CAAC,GAAG;gBAE7D,yDAAyD;gBACzD,MAAM,sBAAsB,iBAAiB,OAAO,KAAK,CAAC,EAAE,CAAC,QAAQ;gBAErE,8BAA8B;gBAC9B,eAAe,0JAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC,GAAG;gBACxD,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,0BAA0B;IAC1B,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,MAAM;QAEX,sBAAsB;QACtB,IAAI;YACF,QAAQ,GAAG,CAAC,kCAAkC,KAAK,IAAI;YAEvD,wCAAwC;YACxC,MAAM,SAAS,MAAM,0JAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,MAAM;gBAClD,YAAY;gBACZ,QAAQ;YACV;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBAClC,QAAQ,GAAG,CAAC,oCAAoC,OAAO,KAAK,CAAC,GAAG;gBAEhE,yDAAyD;gBACzD,MAAM,sBAAsB,oBAAoB,OAAO,KAAK,CAAC,EAAE,CAAC,QAAQ;gBAExE,iCAAiC;gBACjC,kBAAkB,0JAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC,GAAG;gBAC3D,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,sBAAsB;QACxB;IACF;IAIA,MAAM,eAAe,OAAO;QAC1B,YAAY;QACZ,IAAI;YACF,kCAAkC;YAClC,4EAA4E;YAC5E,MAAM,mBAA0C,EAAE;YAElD,iCAAiC;YACjC,IAAI,OAAO,aAAa,EAAE;gBACxB,iBAAiB,IAAI,CAAC;oBACpB,KAAK;oBACL,OAAO,OAAO,aAAa;oBAC3B,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,IAAI,OAAO,YAAY,EAAE;gBACvB,iBAAiB,IAAI,CAAC;oBACpB,KAAK;oBACL,OAAO,OAAO,YAAY;oBAC1B,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,IAAI,OAAO,aAAa,EAAE;gBACxB,iBAAiB,IAAI,CAAC;oBACpB,KAAK;oBACL,OAAO,OAAO,aAAa;oBAC3B,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,2DAA2D;YAC3D,IAAI,OAAO,gBAAgB,IAAI,OAAO,gBAAgB,CAAC,IAAI,OAAO,IAAI;gBACpE,iBAAiB,IAAI,CAAC;oBACpB,KAAK;oBACL,OAAO,OAAO,gBAAgB;oBAC9B,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,IAAI,OAAO,gBAAgB,IAAI,OAAO,gBAAgB,CAAC,IAAI,OAAO,IAAI;gBACpE,iBAAiB,IAAI,CAAC;oBACpB,KAAK;oBACL,OAAO,OAAO,gBAAgB;oBAC9B,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;YACF;YAEA,iBAAiB,IAAI,CACnB;gBACE,KAAK;gBACL,OAAO,OAAO,aAAa,CAAC,QAAQ;gBACpC,UAAU;gBACV,MAAM;gBACN,WAAW;YACb,GACA;gBACE,KAAK;gBACL,OAAO,OAAO,gBAAgB,CAAC,QAAQ;gBACvC,UAAU;gBACV,MAAM;gBACN,WAAW;YACb;YAGF,oCAAoC;YACpC,iBAAiB,IAAI,CACnB;gBACE,KAAK;gBACL,OAAO,OAAO,gBAAgB,CAAC,QAAQ;gBACvC,UAAU;gBACV,MAAM;gBACN,WAAW;YACb,GACA;gBACE,KAAK;gBACL,OAAO,OAAO,kBAAkB,CAAC,QAAQ;gBACzC,UAAU;gBACV,MAAM;gBACN,WAAW;YACb,GACA;gBACE,KAAK;gBACL,OAAO,OAAO,0BAA0B,CAAC,QAAQ;gBACjD,UAAU;gBACV,MAAM;gBACN,WAAW;YACb;YAGF,+DAA+D;YAC/D,4CAA4C;YAE5C,8BAA8B;YAC9B,MAAM,oJAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;YAErC,iPAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,iPAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,IAAI,WAAW;QACb,qBACE,0UAAC;YAAI,WAAU;sBACb,cAAA,0UAAC;gBAAI,WAAU;;kCACb,0UAAC;;0CACC,0UAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,0UAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,0UAAC;wBAAI,WAAU;kCACb,cAAA,0UAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,0UAAC;QAAI,WAAU;kBACb,cAAA,0UAAC;YAAI,WAAU;;8BACb,0UAAC;;sCACC,0UAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,0UAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAGvC,0UAAC,2NAAA,CAAA,SAAM;oBACL,eAAe;oBACf,kBAAkB;oBAClB,UAAU;oBACV,oBAAoB;8BAEnB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,iBACnD,0UAAC,2NAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,0UAAC,uJAAA,CAAA,OAAI;;sDACH,0UAAC,uJAAA,CAAA,aAAU;sDACT,cAAA,0UAAC,uJAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,0UAAC,uJAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,0UAAC;oDAAI,WAAU;;sEACb,0UAAC;4DAAI,WAAU;;8EACb,0UAAC,wJAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAgB;;;;;;8EAC/B,0UAAC,2NAAA,CAAA,QAAK;oEACJ,IAAI,wJAAA,CAAA,QAAK;oEACT,IAAG;oEACH,MAAK;oEACL,aAAY;;;;;;gEAEb,OAAO,aAAa,IAAI,QAAQ,aAAa,kBAC5C,0UAAC;oEAAE,WAAU;8EAA4B,OAAO,aAAa;;;;;;;;;;;;sEAIjE,0UAAC;4DAAI,WAAU;;8EACb,0UAAC,wJAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAe;;;;;;8EAC9B,0UAAC,2NAAA,CAAA,QAAK;oEACJ,IAAI,wJAAA,CAAA,QAAK;oEACT,IAAG;oEACH,MAAK;oEACL,aAAY;;;;;;gEAEb,OAAO,YAAY,IAAI,QAAQ,YAAY,kBAC1C,0UAAC;oEAAE,WAAU;8EAA4B,OAAO,YAAY;;;;;;;;;;;;;;;;;;8DAKlE,0UAAC;oDAAI,WAAU;;sEACb,0UAAC,wJAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAgB;;;;;;sEAC/B,0UAAC,2NAAA,CAAA,QAAK;4DACJ,IAAI,wJAAA,CAAA,QAAK;4DACT,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,aAAY;;;;;;wDAEb,OAAO,aAAa,IAAI,QAAQ,aAAa,kBAC5C,0UAAC;4DAAE,WAAU;sEAA4B,OAAO,aAAa;;;;;;;;;;;;8DAIjE,0UAAC;oDAAI,WAAU;;sEACb,0UAAC,wJAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAmB;;;;;;sEAClC,0UAAC,2NAAA,CAAA,QAAK;4DACJ,IAAI,wJAAA,CAAA,QAAK;4DACT,IAAG;4DACH,MAAK;4DACL,aAAY;;;;;;wDAEb,OAAO,gBAAgB,IAAI,QAAQ,gBAAgB,kBAClD,0UAAC;4DAAE,WAAU;sEAA4B,OAAO,gBAAgB;;;;;;;;;;;;8DAIpE,0UAAC;oDAAI,WAAU;;sEACb,0UAAC,wJAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAmB;;;;;;sEAClC,0UAAC,2NAAA,CAAA,QAAK;4DACJ,IAAI,2JAAA,CAAA,WAAQ;4DACZ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,MAAM;;;;;;wDAEP,OAAO,gBAAgB,IAAI,QAAQ,gBAAgB,kBAClD,0UAAC;4DAAE,WAAU;sEAA4B,OAAO,gBAAgB;;;;;;;;;;;;8DAIpE,0UAAC;oDAAI,WAAU;;sEACb,0UAAC;4DAAI,WAAU;;8EACb,0UAAC,wJAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAgB;;;;;;gEAG9B,6BACC,0UAAC;oEAAI,WAAU;;sFACb,0UAAC;4EACC,KAAK;4EACL,KAAI;4EACJ,WAAU;;;;;;sFAEZ,0UAAC,yJAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,MAAK;4EACL,WAAU;4EACV,SAAS;sFAET,cAAA,0UAAC,mRAAA,CAAA,IAAC;gFAAC,WAAU;;;;;;;;;;;;;;;;;8EAKnB,0UAAC;oEAAI,WAAU;;sFACb,0UAAC,wJAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,MAAK;4EACL,QAAO;4EACP,UAAU,OAAO;gFACf,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;gFAChC,IAAI,MAAM;oFACR,MAAM,iBAAiB;oFACvB,kBAAkB;oFAClB,EAAE,MAAM,CAAC,KAAK,GAAG;gFACnB;4EACF;4EACA,WAAU;;;;;;sFAEZ,0UAAC,yJAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,UAAU;4EACV,SAAS,IAAM,SAAS,cAAc,CAAC,kBAAkB;;8FAEzD,0UAAC,6RAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFACjB,kBAAkB,iBAAkB,cAAc,gBAAgB;;;;;;;;;;;;;8EAGvE,0UAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAK/C,0UAAC;4DAAI,WAAU;;8EACb,0UAAC,wJAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAmB;;;;;;gEAGjC,gCACC,0UAAC;oEAAI,WAAU;;sFACb,0UAAC;4EACC,KAAK;4EACL,KAAI;4EACJ,WAAU;;;;;;sFAEZ,0UAAC,yJAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,MAAK;4EACL,WAAU;4EACV,SAAS;sFAET,cAAA,0UAAC,mRAAA,CAAA,IAAC;gFAAC,WAAU;;;;;;;;;;;;;;;;;8EAKnB,0UAAC;oEAAI,WAAU;;sFACb,0UAAC,wJAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,MAAK;4EACL,QAAO;4EACP,UAAU,OAAO;gFACf,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;gFAChC,IAAI,MAAM;oFACR,MAAM,oBAAoB;oFAC1B,kBAAkB;oFAClB,EAAE,MAAM,CAAC,KAAK,GAAG;gFACnB;4EACF;4EACA,WAAU;;;;;;sFAEZ,0UAAC,yJAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,UAAU;4EACV,SAAS,IAAM,SAAS,cAAc,CAAC,qBAAqB;;8FAE5D,0UAAC,2RAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAChB,qBAAqB,iBAAkB,iBAAiB,mBAAmB;;;;;;;;;;;;;8EAGhF,0UAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;8DAMjD,0UAAC;oDAAI,WAAU;;sEACb,0UAAC;4DAAI,WAAU;;8EACb,0UAAC;;sFACC,0UAAC,wJAAA,CAAA,QAAK;sFAAC;;;;;;sFACP,0UAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAIvC,0UAAC,yJAAA,CAAA,SAAM;oEACL,SAAS,OAAO,gBAAgB;oEAChC,iBAAiB,CAAC,UAAY,cAAc,oBAAoB;;;;;;;;;;;;sEAIpE,0UAAC;4DAAI,WAAU;;8EACb,0UAAC;;sFACC,0UAAC,wJAAA,CAAA,QAAK;sFAAC;;;;;;sFACP,0UAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAIvC,0UAAC,yJAAA,CAAA,SAAM;oEACL,SAAS,OAAO,kBAAkB;oEAClC,iBAAiB,CAAC,UAAY,cAAc,sBAAsB;;;;;;;;;;;;sEAItE,0UAAC;4DAAI,WAAU;;8EACb,0UAAC;;sFACC,0UAAC,wJAAA,CAAA,QAAK;sFAAC;;;;;;sFACP,0UAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAIvC,0UAAC,yJAAA,CAAA,SAAM;oEACL,SAAS,OAAO,0BAA0B;oEAC1C,iBAAiB,CAAC,UAAY,cAAc,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQpF,0UAAC;oCAAI,WAAU;;sDACb,0UAAC,yJAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,SAAQ;sDAAU;;;;;;sDAGxC,0UAAC;4CAAI,WAAU;;gDACZ,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,mBAC5B,0UAAC;oDAAI,WAAU;;wDAA2B;wDACX,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC;;;;;;;8DAG1D,0UAAC,yJAAA,CAAA,SAAM;oDACL,MAAK;oDACL,UAAU,YAAY,CAAC;8DAGtB,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C;GAzoBwB;;QA0Dc,oKAAA,CAAA,mBAAgB;;;KA1D9B", "debugId": null}}]}