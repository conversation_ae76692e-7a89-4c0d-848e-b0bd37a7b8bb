{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/api/platform-settings.ts"], "sourcesContent": ["/**\n * Platform Settings API Client\n * Comprehensive API client for platform settings management\n */\n\nimport { api } from '@/lib/api'\n\nexport interface PlatformBranding {\n  logo: MediaFile | null\n  favicon: MediaFile | null\n}\n\nexport interface MediaFile {\n  id: string\n  filename: string\n  url: string\n  filesize: number\n  mimeType: string\n  alt?: string\n  sizes?: Record<string, {\n    url: string\n    width: number\n    height: number\n  }>\n}\n\nexport interface StorageConfig {\n  provider: 'local' | 's3'\n  local?: {\n    uploadDir: string\n    baseUrl: string\n    publicPath: string\n  }\n  s3?: {\n    bucket: string\n    region: string\n    endpoint?: string\n    publicUrl?: string\n    cdnUrl?: string\n    hasAccessKey: boolean\n    hasSecretKey: boolean\n  }\n}\n\nexport interface StorageSummary {\n  provider: string\n  localConfigured: boolean\n  s3Configured: boolean\n  brandingConfigured: boolean\n}\n\nexport interface ApiResponse<T = any> {\n  success: boolean\n  message?: string\n  data?: T\n}\n\nexport interface UploadResponse {\n  success: boolean\n  message: string\n  data: {\n    upload: {\n      id: string\n      filename: string\n      url: string\n      size: number\n    }\n    media: MediaFile\n  }\n}\n\nclass PlatformSettingsAPI {\n\n  /**\n   * Get platform branding assets\n   */\n  async getPlatformBranding(): Promise<ApiResponse<PlatformBranding>> {\n    return api.get('/api/platform/settings/branding')\n  }\n\n  /**\n   * Upload platform logo\n   */\n  async uploadPlatformLogo(file: File): Promise<UploadResponse> {\n    const formData = new FormData()\n    formData.append('file', file)\n\n    console.log('📤 Uploading platform logo:', {\n      name: file.name,\n      size: file.size,\n      type: file.type\n    })\n\n    return api.postFormData('/api/platform/settings/logo', formData)\n  }\n\n  /**\n   * Upload platform favicon\n   */\n  async uploadPlatformFavicon(file: File): Promise<UploadResponse> {\n    const formData = new FormData()\n    formData.append('file', file)\n\n    console.log('📤 Uploading platform favicon:', {\n      name: file.name,\n      size: file.size,\n      type: file.type\n    })\n\n    return api.postFormData('/api/platform/settings/favicon', formData)\n  }\n\n  /**\n   * Remove platform logo\n   */\n  async removePlatformLogo(): Promise<ApiResponse> {\n    return api.delete('/api/platform/settings/logo')\n  }\n\n  /**\n   * Remove platform favicon\n   */\n  async removePlatformFavicon(): Promise<ApiResponse> {\n    return api.delete('/api/platform/settings/favicon')\n  }\n\n  /**\n   * Process favicon from image (generates multiple sizes)\n   */\n  async processFavicon(file: File): Promise<ApiResponse> {\n    const formData = new FormData()\n    formData.append('file', file)\n\n    console.log('🔖 Processing favicon:', {\n      name: file.name,\n      size: file.size,\n      type: file.type\n    })\n\n    return api.postFormData('/api/platform/favicon/process', formData)\n  }\n\n  /**\n   * Get storage configuration\n   */\n  async getStorageConfig(): Promise<ApiResponse<StorageConfig>> {\n    return api.get('/api/platform/storage/config')\n  }\n\n  /**\n   * Update storage provider\n   */\n  async updateStorageProvider(provider: 'local' | 's3'): Promise<ApiResponse<{ provider: string }>> {\n    return api.post('/api/platform/storage/provider', { provider })\n  }\n\n  /**\n   * Update S3 configuration\n   */\n  async updateS3Config(config: {\n    bucket: string\n    region: string\n    accessKeyId: string\n    secretAccessKey: string\n    endpoint?: string\n    publicUrl?: string\n    cdnUrl?: string\n  }): Promise<ApiResponse> {\n    return api.post('/api/platform/storage/s3', config)\n  }\n\n  /**\n   * Test storage configuration\n   */\n  async testStorageConfig(): Promise<ApiResponse<{\n    provider: string\n    healthy: boolean\n    validation: boolean\n  }>> {\n    return api.post('/api/platform/storage/test', {})\n  }\n\n  /**\n   * Clear storage configuration cache\n   */\n  async clearStorageCache(): Promise<ApiResponse> {\n    return api.post('/api/platform/storage/cache/clear', {})\n  }\n\n  /**\n   * Initialize storage settings\n   */\n  async initStorageSettings(): Promise<ApiResponse<{\n    summary: StorageSummary\n    validation: {\n      valid: boolean\n      issues: string[]\n    }\n  }>> {\n    return api.post('/api/platform/storage/init', {})\n  }\n\n  /**\n   * Get storage settings summary\n   */\n  async getStorageSummary(): Promise<ApiResponse<{\n    summary: StorageSummary\n    validation: {\n      valid: boolean\n      issues: string[]\n    }\n  }>> {\n    return api.get('/api/platform/storage/summary')\n  }\n\n  /**\n   * Health check for upload system\n   */\n  async uploadHealthCheck(): Promise<ApiResponse<{\n    healthy: boolean\n    details: any\n  }>> {\n    return api.get('/api/platform/upload/health')\n  }\n}\n\n// Create singleton instance\nexport const platformSettingsAPI = new PlatformSettingsAPI()\n\n// Export convenience functions\nexport const getPlatformBranding = () => platformSettingsAPI.getPlatformBranding()\nexport const uploadPlatformLogo = (file: File) => platformSettingsAPI.uploadPlatformLogo(file)\nexport const uploadPlatformFavicon = (file: File) => platformSettingsAPI.uploadPlatformFavicon(file)\nexport const removePlatformLogo = () => platformSettingsAPI.removePlatformLogo()\nexport const removePlatformFavicon = () => platformSettingsAPI.removePlatformFavicon()\nexport const processFavicon = (file: File) => platformSettingsAPI.processFavicon(file)\nexport const getStorageConfig = () => platformSettingsAPI.getStorageConfig()\nexport const updateStorageProvider = (provider: 'local' | 's3') => platformSettingsAPI.updateStorageProvider(provider)\nexport const updateS3Config = (config: Parameters<typeof platformSettingsAPI.updateS3Config>[0]) => platformSettingsAPI.updateS3Config(config)\nexport const testStorageConfig = () => platformSettingsAPI.testStorageConfig()\nexport const clearStorageCache = () => platformSettingsAPI.clearStorageCache()\nexport const initStorageSettings = () => platformSettingsAPI.initStorageSettings()\nexport const getStorageSummary = () => platformSettingsAPI.getStorageSummary()\nexport const uploadHealthCheck = () => platformSettingsAPI.uploadHealthCheck()\n\nexport default platformSettingsAPI\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;AAED;;AAkEA,MAAM;IAEJ;;GAEC,GACD,MAAM,sBAA8D;QAClE,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;IAEA;;GAEC,GACD,MAAM,mBAAmB,IAAU,EAA2B;QAC5D,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,QAAQ,GAAG,CAAC,+BAA+B;YACzC,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;QACjB;QAEA,OAAO,wIAAA,CAAA,MAAG,CAAC,YAAY,CAAC,+BAA+B;IACzD;IAEA;;GAEC,GACD,MAAM,sBAAsB,IAAU,EAA2B;QAC/D,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,QAAQ,GAAG,CAAC,kCAAkC;YAC5C,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;QACjB;QAEA,OAAO,wIAAA,CAAA,MAAG,CAAC,YAAY,CAAC,kCAAkC;IAC5D;IAEA;;GAEC,GACD,MAAM,qBAA2C;QAC/C,OAAO,wIAAA,CAAA,MAAG,CAAC,MAAM,CAAC;IACpB;IAEA;;GAEC,GACD,MAAM,wBAA8C;QAClD,OAAO,wIAAA,CAAA,MAAG,CAAC,MAAM,CAAC;IACpB;IAEA;;GAEC,GACD,MAAM,eAAe,IAAU,EAAwB;QACrD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,QAAQ,GAAG,CAAC,0BAA0B;YACpC,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;QACjB;QAEA,OAAO,wIAAA,CAAA,MAAG,CAAC,YAAY,CAAC,iCAAiC;IAC3D;IAEA;;GAEC,GACD,MAAM,mBAAwD;QAC5D,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;IAEA;;GAEC,GACD,MAAM,sBAAsB,QAAwB,EAA8C;QAChG,OAAO,wIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,kCAAkC;YAAE;QAAS;IAC/D;IAEA;;GAEC,GACD,MAAM,eAAe,MAQpB,EAAwB;QACvB,OAAO,wIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,4BAA4B;IAC9C;IAEA;;GAEC,GACD,MAAM,oBAIF;QACF,OAAO,wIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,8BAA8B,CAAC;IACjD;IAEA;;GAEC,GACD,MAAM,oBAA0C;QAC9C,OAAO,wIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,qCAAqC,CAAC;IACxD;IAEA;;GAEC,GACD,MAAM,sBAMF;QACF,OAAO,wIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,8BAA8B,CAAC;IACjD;IAEA;;GAEC,GACD,MAAM,oBAMF;QACF,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;IAEA;;GAEC,GACD,MAAM,oBAGF;QACF,OAAO,wIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;IACjB;AACF;AAGO,MAAM,sBAAsB,IAAI;AAGhC,MAAM,sBAAsB,IAAM,oBAAoB,mBAAmB;AACzE,MAAM,qBAAqB,CAAC,OAAe,oBAAoB,kBAAkB,CAAC;AAClF,MAAM,wBAAwB,CAAC,OAAe,oBAAoB,qBAAqB,CAAC;AACxF,MAAM,qBAAqB,IAAM,oBAAoB,kBAAkB;AACvE,MAAM,wBAAwB,IAAM,oBAAoB,qBAAqB;AAC7E,MAAM,iBAAiB,CAAC,OAAe,oBAAoB,cAAc,CAAC;AAC1E,MAAM,mBAAmB,IAAM,oBAAoB,gBAAgB;AACnE,MAAM,wBAAwB,CAAC,WAA6B,oBAAoB,qBAAqB,CAAC;AACtG,MAAM,iBAAiB,CAAC,SAAqE,oBAAoB,cAAc,CAAC;AAChI,MAAM,oBAAoB,IAAM,oBAAoB,iBAAiB;AACrE,MAAM,oBAAoB,IAAM,oBAAoB,iBAAiB;AACrE,MAAM,sBAAsB,IAAM,oBAAoB,mBAAmB;AACzE,MAAM,oBAAoB,IAAM,oBAAoB,iBAAiB;AACrE,MAAM,oBAAoB,IAAM,oBAAoB,iBAAiB;uCAE7D", "debugId": null}}]}