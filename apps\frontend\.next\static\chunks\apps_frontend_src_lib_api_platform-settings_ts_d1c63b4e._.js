(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/apps/frontend/src/lib/api/platform-settings.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/apps_frontend_src_lib_api_platform-settings_ts_c28e689b._.js",
  "static/chunks/apps_frontend_src_lib_api_platform-settings_ts_ab67551b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/apps/frontend/src/lib/api/platform-settings.ts [app-client] (ecmascript)");
    });
});
}}),
}]);