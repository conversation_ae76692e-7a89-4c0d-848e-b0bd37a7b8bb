# LMS Frontend Application

This is the frontend application for the Learning Management System (LMS) built with Next.js 15, TypeScript, and Tailwind CSS.

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Git

### Installation

1. Clone the repository and navigate to frontend:
```bash
cd lms/apps/frontend
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Start the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## 🔐 Authentication Routes

### Platform Level (groupsexam.com)
- `/auth/admin/login` - Super Admin & Platform Staff
- `/auth/register` - Institute Registration (one-time)

### Institute Level (institute-domain.com)
- `/auth/login` - Institute Admin, Staff, Trainers
- `/auth/user-register` - Student Registration
- `/auth/user-login` - Student Login

## 🎨 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui + Radix UI
- **State Management**: Zustand
- **Form Handling**: Formik
- **Validation**: Yup

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication pages
│   ├── admin/             # Institute Admin dashboard
│   ├── super-admin/       # Super Admin dashboard
│   └── page.tsx           # Student dashboard (home)
├── components/            # Reusable components
├── stores/               # Zustand state management
└── lib/                  # Utility functions
```

## 🚀 Deployment

Build the application:
```bash
npm run build
npm start
```
