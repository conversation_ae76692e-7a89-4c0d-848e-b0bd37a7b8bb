import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  async redirects() {
    return [
      // No redirects - removed /admin/courses -> /admin/course-builder redirect
    ];
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/:path*`,
      },
    ];
  },
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  },
  // Allow cross-origin requests from custom domains during development
  allowedDevOrigins: [
    'hello.local:3000',
    '*.local:3000'
  ],
};

export default nextConfig;
