import { renderHook } from '@testing-library/react'
import { useSidebarNavigation, usePaymentGatewayNavigation } from '@/hooks/useSidebarNavigation'

// Mock the permission context
jest.mock('@/contexts/PermissionContext', () => ({
  usePermissions: () => ({
    userPermissions: {
      role: 'institute_admin',
      permissions: [
        { code: 'manage_institute_payment_gateways', name: 'Manage Institute Payment Gateways' },
        { code: 'configure_payment_gateways', name: 'Configure Payment Gateways' }
      ]
    },
    filterNavigationByPermissions: (items: any[]) => items.filter(item => 
      !item.permissions || item.permissions.includes('institute_admin')
    )
  })
}))

// Mock the auth store
jest.mock('@/stores/auth/useAuthStore', () => ({
  useAuthStore: () => ({
    user: {
      id: '1',
      role: 'institute_admin',
      email: '<EMAIL>'
    }
  })
}))

// Mock the sidebar store
jest.mock('@/stores/sidebar/useSidebarStore', () => ({
  useSidebarStore: () => ({
    navigationItems: [
      {
        id: 'dashboard',
        label: 'Dashboard',
        icon: 'LayoutDashboard',
        href: '/admin',
        description: 'Institute overview'
      },
      {
        id: 'settings',
        label: 'Settings',
        icon: 'Settings',
        href: '/admin/settings',
        description: 'Institute settings',
        permissions: ['institute_admin', 'branch_manager'],
        children: [
          {
            id: 'settings-general',
            label: 'General Settings',
            icon: 'Settings',
            href: '/admin/settings/general',
            description: 'Basic settings'
          },
          {
            id: 'settings-payment',
            label: 'Payment Gateways',
            icon: 'CreditCard',
            href: '/admin/settings/payment-gateways',
            description: 'Configure payment gateways',
            permissions: ['institute_admin', 'branch_manager']
          }
        ]
      }
    ],
    userType: 'institute_admin',
    setNavigationItems: jest.fn(),
    filterNavigationByPermissions: jest.fn(),
    initializeNavigation: jest.fn()
  })
}))

describe('useSidebarNavigation Hook', () => {
  describe('Navigation Filtering', () => {
    it('should return filtered navigation items', () => {
      const { result } = renderHook(() => useSidebarNavigation())

      expect(result.current.navigationItems).toBeDefined()
      expect(Array.isArray(result.current.navigationItems)).toBe(true)
      expect(result.current.navigationItems.length).toBeGreaterThan(0)
    })

    it('should include payment gateway navigation for institute admin', () => {
      const { result } = renderHook(() => useSidebarNavigation())

      const settingsItem = result.current.navigationItems.find(item => item.id === 'settings')
      expect(settingsItem).toBeDefined()

      const paymentGatewayItem = settingsItem?.children?.find(
        child => child.id === 'settings-payment'
      )
      expect(paymentGatewayItem).toBeDefined()
      expect(paymentGatewayItem?.href).toBe('/admin/settings/payment-gateways')
    })

    it('should check navigation item accessibility', () => {
      const { result } = renderHook(() => useSidebarNavigation())

      // Dashboard should be accessible (no permissions required)
      expect(result.current.isNavigationAccessible('/admin')).toBe(true)

      // Payment gateways should be accessible for institute admin
      expect(result.current.isNavigationAccessible('/admin/settings/payment-gateways')).toBe(true)
    })

    it('should provide navigation statistics', () => {
      const { result } = renderHook(() => useSidebarNavigation())

      expect(result.current.navigationStats).toBeDefined()
      expect(typeof result.current.navigationStats.accessible).toBe('number')
      expect(typeof result.current.navigationStats.total).toBe('number')
      expect(typeof result.current.navigationStats.restricted).toBe('number')
    })
  })

  describe('Navigation Utilities', () => {
    it('should find navigation items by section', () => {
      const { result } = renderHook(() => useSidebarNavigation())

      const settingsItems = result.current.getNavigationBySection('settings')
      expect(Array.isArray(settingsItems)).toBe(true)
      expect(settingsItems.some(item => item.href.includes('settings'))).toBe(true)
    })

    it('should find navigation item by ID', () => {
      const { result } = renderHook(() => useSidebarNavigation())

      const dashboardItem = result.current.findNavigationItem('dashboard')
      expect(dashboardItem).toBeDefined()
      expect(dashboardItem?.id).toBe('dashboard')
      expect(dashboardItem?.href).toBe('/admin')
    })

    it('should find nested navigation items', () => {
      const { result } = renderHook(() => useSidebarNavigation())

      const paymentItem = result.current.findNavigationItem('settings-payment')
      expect(paymentItem).toBeDefined()
      expect(paymentItem?.id).toBe('settings-payment')
      expect(paymentItem?.href).toBe('/admin/settings/payment-gateways')
    })
  })

  describe('User Information', () => {
    it('should provide user type and role information', () => {
      const { result } = renderHook(() => useSidebarNavigation())

      expect(result.current.userType).toBe('institute_admin')
      expect(result.current.userRole).toBe('institute_admin')
    })
  })
})

describe('usePaymentGatewayNavigation Hook', () => {
  it('should provide payment gateway access information', () => {
    const { result } = renderHook(() => usePaymentGatewayNavigation())

    expect(result.current.userRole).toBe('institute_admin')
    expect(result.current.isSuperAdmin).toBe(false)
    expect(result.current.isInstituteAdmin).toBe(true)
  })

  it('should check payment gateway navigation access', () => {
    const { result } = renderHook(() => usePaymentGatewayNavigation())

    // Institute admin should have access to institute admin gateways
    expect(result.current.canAccessInstituteAdminGateways).toBe(true)
    
    // Institute admin should not have access to super admin gateways
    expect(result.current.canAccessSuperAdminGateways).toBe(false)
    
    // Should have access to at least one type of gateways
    expect(result.current.canAccessAnyGateways).toBe(true)
  })
})

// Test with Super Admin permissions
describe('useSidebarNavigation Hook - Super Admin', () => {
  beforeEach(() => {
    // Mock super admin permissions
    jest.clearAllMocks()
    jest.doMock('@/contexts/PermissionContext', () => ({
      usePermissions: () => ({
        userPermissions: {
          role: 'super_admin',
          permissions: [
            { code: 'manage_payment_gateways', name: 'Manage Payment Gateways' },
            { code: 'view_payment_gateways', name: 'View Payment Gateways' }
          ]
        },
        filterNavigationByPermissions: (items: any[]) => items // Super admin sees all
      })
    }))
  })

  it('should provide super admin access to payment gateways', () => {
    const { result } = renderHook(() => usePaymentGatewayNavigation())

    expect(result.current.isSuperAdmin).toBe(true)
    expect(result.current.isInstituteAdmin).toBe(false)
    expect(result.current.canAccessAnyGateways).toBe(true)
  })
})
