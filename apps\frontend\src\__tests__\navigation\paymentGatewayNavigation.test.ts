import { superAdminNavigationConfig } from '@/config/navigation/superAdminNavigation'
import { instituteAdminNavigationConfig } from '@/config/navigation/instituteAdminNavigation'
import { NAVIGATION_PERMISSIONS } from '@/utils/permissions'

describe('Payment Gateway Navigation', () => {
  describe('Super Admin Navigation', () => {
    it('should include gateway management as a top-level navigation item', () => {
      const gatewayManagementItem = superAdminNavigationConfig.find(item => item.id === 'gateway-management')

      expect(gatewayManagementItem).toBeDefined()
      expect(gatewayManagementItem?.label).toBe('Gateway Management')
      expect(gatewayManagementItem?.icon).toBe('CreditCard')
      expect(gatewayManagementItem?.href).toBe('/super-admin/gateway-management')
      expect(gatewayManagementItem?.description).toBe('Manage payment gateway providers and configurations')
    })

    it('should have correct navigation permissions for super admin gateway management', () => {
      const navigationId = 'super-admin-gateway-management'
      const permissions = NAVIGATION_PERMISSIONS[navigationId]

      expect(permissions).toBeDefined()
      expect(permissions).toContain('manage_payment_gateways')
      expect(permissions).toContain('view_payment_gateways')
    })
  })

  describe('Institute Admin Navigation', () => {
    it('should include payment gateways in settings children', () => {
      const settingsItem = instituteAdminNavigationConfig.find(item => item.id === 'settings')
      
      expect(settingsItem).toBeDefined()
      expect(settingsItem?.children).toBeDefined()
      
      const paymentGatewayItem = settingsItem?.children?.find(
        child => child.id === 'settings-payment'
      )
      
      expect(paymentGatewayItem).toBeDefined()
      expect(paymentGatewayItem?.label).toBe('Payment Gateways')
      expect(paymentGatewayItem?.icon).toBe('CreditCard')
      expect(paymentGatewayItem?.href).toBe('/admin/settings/payment-gateways')
      expect(paymentGatewayItem?.description).toBe('Configure payment gateways')
    })

    it('should have correct permissions for institute admin payment gateways', () => {
      const settingsItem = instituteAdminNavigationConfig.find(item => item.id === 'settings')
      const paymentGatewayItem = settingsItem?.children?.find(
        child => child.id === 'settings-payment'
      )
      
      expect(paymentGatewayItem?.permissions).toBeDefined()
      expect(paymentGatewayItem?.permissions).toContain('institute_admin')
      expect(paymentGatewayItem?.permissions).toContain('branch_manager')
    })

    it('should have correct navigation permissions for institute admin payment gateways', () => {
      const navigationId = 'admin-settings-payment-gateways'
      const permissions = NAVIGATION_PERMISSIONS[navigationId]
      
      expect(permissions).toBeDefined()
      expect(permissions).toContain('manage_institute_payment_gateways')
      expect(permissions).toContain('configure_payment_gateways')
    })

    it('should have settings section with proper permissions', () => {
      const settingsItem = instituteAdminNavigationConfig.find(item => item.id === 'settings')
      
      expect(settingsItem?.permissions).toBeDefined()
      expect(settingsItem?.permissions).toContain('institute_admin')
      expect(settingsItem?.permissions).toContain('branch_manager')
      expect(settingsItem?.permissions).toContain('institute_staff')
    })
  })

  describe('Navigation Structure Validation', () => {
    it('should have valid navigation structure for super admin', () => {
      superAdminNavigationConfig.forEach(item => {
        expect(item.id).toBeDefined()
        expect(item.label).toBeDefined()
        expect(item.icon).toBeDefined()
        expect(item.href).toBeDefined()
        
        if (item.children) {
          item.children.forEach(child => {
            expect(child.id).toBeDefined()
            expect(child.label).toBeDefined()
            expect(child.icon).toBeDefined()
            expect(child.href).toBeDefined()
          })
        }
      })
    })

    it('should have valid navigation structure for institute admin', () => {
      instituteAdminNavigationConfig.forEach(item => {
        expect(item.id).toBeDefined()
        expect(item.label).toBeDefined()
        expect(item.icon).toBeDefined()
        expect(item.href).toBeDefined()
        
        if (item.children) {
          item.children.forEach(child => {
            expect(child.id).toBeDefined()
            expect(child.label).toBeDefined()
            expect(child.icon).toBeDefined()
            expect(child.href).toBeDefined()
          })
        }
      })
    })

    it('should have unique navigation IDs within each configuration', () => {
      // Test super admin navigation
      const superAdminIds = new Set()
      const collectIds = (items: any[]) => {
        items.forEach(item => {
          expect(superAdminIds.has(item.id)).toBe(false)
          superAdminIds.add(item.id)
          if (item.children) {
            collectIds(item.children)
          }
        })
      }
      collectIds(superAdminNavigationConfig)

      // Test institute admin navigation
      const instituteAdminIds = new Set()
      const collectInstituteIds = (items: any[]) => {
        items.forEach(item => {
          expect(instituteAdminIds.has(item.id)).toBe(false)
          instituteAdminIds.add(item.id)
          if (item.children) {
            collectInstituteIds(item.children)
          }
        })
      }
      collectInstituteIds(instituteAdminNavigationConfig)
    })
  })

  describe('Permission Integration', () => {
    it('should have all required permissions defined in NAVIGATION_PERMISSIONS', () => {
      const requiredNavigationIds = [
        'super-admin-gateway-management',
        'admin-settings-payment-gateways'
      ]

      requiredNavigationIds.forEach(navId => {
        expect(NAVIGATION_PERMISSIONS[navId]).toBeDefined()
        expect(Array.isArray(NAVIGATION_PERMISSIONS[navId])).toBe(true)
        expect(NAVIGATION_PERMISSIONS[navId].length).toBeGreaterThan(0)
      })
    })

    it('should have payment gateway permissions in role permissions', () => {
      // This test would need to import the role permissions
      // For now, we'll just verify the structure exists
      expect(NAVIGATION_PERMISSIONS).toBeDefined()
      expect(typeof NAVIGATION_PERMISSIONS).toBe('object')
    })
  })
})
