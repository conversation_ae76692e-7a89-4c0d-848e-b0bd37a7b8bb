import { renderHook, act } from '@testing-library/react'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useSidebarNavigation } from '@/hooks/useSidebarNavigation'

// Mock the permission context
jest.mock('@/contexts/PermissionContext', () => ({
  usePermissions: () => ({
    userPermissions: {
      role: 'super_admin',
      permissions: [
        { code: 'manage_payment_gateways', name: 'Manage Payment Gateways' },
        { code: 'view_payment_gateways', name: 'View Payment Gateways' }
      ]
    },
    filterNavigationByPermissions: (items: any[]) => items
  })
}))

// Mock the auth store
jest.mock('@/stores/auth/useAuthStore', () => ({
  useAuthStore: () => ({
    user: {
      id: '1',
      role: 'super_admin',
      email: '<EMAIL>'
    }
  })
}))

describe('Sidebar Store Payment Gateway Integration', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useSidebarStore.setState({
      navigationItems: [],
      userType: 'super_admin',
      isCollapsed: false,
      isMobileOpen: false,
      activeItem: '',
      activeSection: '',
      favoriteItems: [],
      recentItems: [],
      sections: [],
      breadcrumbs: [],
      notifications: [],
      unreadCount: 0
    })
  })

  describe('Super Admin Navigation', () => {
    it('should include payment gateways in default navigation', () => {
      const { result } = renderHook(() => useSidebarStore())

      act(() => {
        result.current.initializeNavigation('super_admin')
      })

      const settingsItem = result.current.navigationItems.find(item => item.id === 'settings')
      expect(settingsItem).toBeDefined()
      expect(settingsItem?.children).toBeDefined()

      const paymentGatewayItem = settingsItem?.children?.find(
        child => child.id === 'settings-payment-gateways'
      )

      expect(paymentGatewayItem).toBeDefined()
      expect(paymentGatewayItem?.label).toBe('Payment Gateways')
      expect(paymentGatewayItem?.icon).toBe('CreditCard')
      expect(paymentGatewayItem?.href).toBe('/super-admin/settings/payment-gateways')
      expect(paymentGatewayItem?.permissions).toContain('manage_payment_gateways')
      expect(paymentGatewayItem?.permissions).toContain('view_payment_gateways')
    })

    it('should have proper settings structure with children', () => {
      const { result } = renderHook(() => useSidebarStore())

      act(() => {
        result.current.initializeNavigation('super_admin')
      })

      const settingsItem = result.current.navigationItems.find(item => item.id === 'settings')
      expect(settingsItem?.children).toBeDefined()
      expect(settingsItem?.children?.length).toBeGreaterThan(0)

      // Check that all expected settings children are present
      const childIds = settingsItem?.children?.map(child => child.id) || []
      expect(childIds).toContain('settings-payment-gateways')
      expect(childIds).toContain('settings-platform')
      expect(childIds).toContain('settings-domains')
    })
  })

  describe('Institute Admin Navigation', () => {
    it('should include payment gateways in default navigation', () => {
      const { result } = renderHook(() => useSidebarStore())

      act(() => {
        result.current.initializeNavigation('institute_admin')
      })

      const settingsItem = result.current.navigationItems.find(item => item.id === 'settings')
      expect(settingsItem).toBeDefined()
      expect(settingsItem?.children).toBeDefined()
      expect(settingsItem?.permissions).toContain('institute_admin')
      expect(settingsItem?.permissions).toContain('branch_manager')

      const paymentGatewayItem = settingsItem?.children?.find(
        child => child.id === 'settings-payment'
      )

      expect(paymentGatewayItem).toBeDefined()
      expect(paymentGatewayItem?.label).toBe('Payment Gateways')
      expect(paymentGatewayItem?.icon).toBe('CreditCard')
      expect(paymentGatewayItem?.href).toBe('/admin/settings/payment-gateways')
      expect(paymentGatewayItem?.permissions).toContain('institute_admin')
      expect(paymentGatewayItem?.permissions).toContain('branch_manager')
    })

    it('should have proper settings structure with children', () => {
      const { result } = renderHook(() => useSidebarStore())

      act(() => {
        result.current.initializeNavigation('institute_admin')
      })

      const settingsItem = result.current.navigationItems.find(item => item.id === 'settings')
      expect(settingsItem?.children).toBeDefined()
      expect(settingsItem?.children?.length).toBeGreaterThan(0)

      // Check that all expected settings children are present
      const childIds = settingsItem?.children?.map(child => child.id) || []
      expect(childIds).toContain('settings-payment')
      expect(childIds).toContain('settings-general')
      expect(childIds).toContain('settings-domain')
      expect(childIds).toContain('settings-theme')
    })
  })

  describe('Permission Filtering', () => {
    it('should filter navigation items based on permissions', () => {
      const { result } = renderHook(() => useSidebarStore())

      act(() => {
        result.current.initializeNavigation('institute_admin')
      })

      const initialItemCount = result.current.navigationItems.length

      act(() => {
        result.current.filterNavigationByPermissions(['institute_admin'])
      })

      // Navigation should still be present after filtering
      expect(result.current.navigationItems.length).toBeGreaterThan(0)
      
      // Settings item should still be accessible
      const settingsItem = result.current.navigationItems.find(item => item.id === 'settings')
      expect(settingsItem).toBeDefined()
    })

    it('should handle empty permissions gracefully', () => {
      const { result } = renderHook(() => useSidebarStore())

      act(() => {
        result.current.initializeNavigation('super_admin')
      })

      act(() => {
        result.current.filterNavigationByPermissions([])
      })

      // Super admin should still have access to all items
      expect(result.current.navigationItems.length).toBeGreaterThan(0)
    })
  })

  describe('Navigation Management', () => {
    it('should allow setting custom navigation items', () => {
      const { result } = renderHook(() => useSidebarStore())

      const customNavigation = [
        {
          id: 'custom',
          label: 'Custom Item',
          icon: 'Star',
          href: '/custom',
          description: 'Custom navigation item'
        }
      ]

      act(() => {
        result.current.setNavigationItems(customNavigation)
      })

      expect(result.current.navigationItems).toEqual(customNavigation)
    })

    it('should reset state properly', () => {
      const { result } = renderHook(() => useSidebarStore())

      // Set some state
      act(() => {
        result.current.initializeNavigation('super_admin')
        result.current.setActiveItem('test-item')
      })

      expect(result.current.navigationItems.length).toBeGreaterThan(0)
      expect(result.current.activeItem).toBe('test-item')

      // Reset state
      act(() => {
        result.current.resetState()
      })

      expect(result.current.navigationItems).toEqual([])
      expect(result.current.activeItem).toBe('')
    })
  })

  describe('User Type Management', () => {
    it('should update navigation when user type changes', () => {
      const { result } = renderHook(() => useSidebarStore())

      // Start with super admin
      act(() => {
        result.current.setUserType('super_admin')
      })

      expect(result.current.userType).toBe('super_admin')
      
      // Should have super admin navigation
      const superAdminSettings = result.current.navigationItems.find(item => item.id === 'settings')
      const superAdminPaymentGateways = superAdminSettings?.children?.find(
        child => child.id === 'settings-payment-gateways'
      )
      expect(superAdminPaymentGateways?.href).toBe('/super-admin/settings/payment-gateways')

      // Switch to institute admin
      act(() => {
        result.current.setUserType('institute_admin')
      })

      expect(result.current.userType).toBe('institute_admin')
      
      // Should have institute admin navigation
      const instituteAdminSettings = result.current.navigationItems.find(item => item.id === 'settings')
      const instituteAdminPaymentGateways = instituteAdminSettings?.children?.find(
        child => child.id === 'settings-payment'
      )
      expect(instituteAdminPaymentGateways?.href).toBe('/admin/settings/payment-gateways')
    })
  })
})
