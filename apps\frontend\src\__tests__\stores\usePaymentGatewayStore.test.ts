import { renderHook, act } from '@testing-library/react'
import { usePaymentGatewayStore } from '@/stores/super-admin/usePaymentGatewayStore'
import { api } from '@/lib/api'

// Mock the API
jest.mock('@/lib/api', () => ({
  api: {
    get: jest.fn(),
    post: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  }
}))

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  }
}))

const mockApi = api as jest.Mocked<typeof api>

describe('usePaymentGatewayStore', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Reset store state
    usePaymentGatewayStore.setState({
      gateways: [],
      loading: false,
      error: null,
    })
  })

  describe('fetchGateways', () => {
    it('should fetch gateways successfully', async () => {
      const mockGateways = [
        {
          id: '1',
          name: '<PERSON><PERSON><PERSON><PERSON>',
          supportedCurrencies: [{ currency: 'INR' }],
          supportedMethods: [{ method: 'credit_card' }],
          requiredConfigFields: { key_id: 'string', key_secret: 'string' },
          optionalConfigFields: {},
          fees: { transactionFeeType: 'percentage', transactionFeePercentage: 2.5 },
          isActive: true,
          isFeatured: false,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ]

      mockApi.get.mockResolvedValueOnce({ gateways: mockGateways })

      const { result } = renderHook(() => usePaymentGatewayStore())

      await act(async () => {
        await result.current.fetchGateways()
      })

      expect(mockApi.get).toHaveBeenCalledWith('/super-admin/payment-gateways')
      expect(result.current.gateways).toEqual(mockGateways)
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(null)
    })

    it('should handle fetch error', async () => {
      const errorMessage = 'Failed to fetch gateways'
      mockApi.get.mockRejectedValueOnce({
        response: { data: { error: errorMessage } }
      })

      const { result } = renderHook(() => usePaymentGatewayStore())

      await act(async () => {
        await result.current.fetchGateways()
      })

      expect(result.current.gateways).toEqual([])
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(errorMessage)
    })
  })

  describe('createGateway', () => {
    it('should create gateway successfully', async () => {
      const newGateway = {
        name: 'Stripe',
        supportedCurrencies: [{ currency: 'USD' }],
        supportedMethods: [{ method: 'credit_card' }],
        fees: { transactionFeeType: 'percentage', transactionFeePercentage: 2.9 },
        isActive: true,
        isFeatured: false
      }

      const createdGateway = {
        id: '2',
        ...newGateway,
        requiredConfigFields: {},
        optionalConfigFields: {},
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01'
      }

      mockApi.post.mockResolvedValueOnce({ gateway: createdGateway })

      const { result } = renderHook(() => usePaymentGatewayStore())

      await act(async () => {
        await result.current.createGateway(newGateway)
      })

      expect(mockApi.post).toHaveBeenCalledWith('/super-admin/payment-gateways', newGateway)
      expect(result.current.gateways).toContain(createdGateway)
      expect(result.current.loading).toBe(false)
    })
  })

  describe('updateGateway', () => {
    it('should update gateway successfully', async () => {
      const existingGateway = {
        id: '1',
        name: 'Razorpay',
        supportedCurrencies: [{ currency: 'INR' }],
        supportedMethods: [{ method: 'credit_card' }],
        requiredConfigFields: {},
        optionalConfigFields: {},
        fees: { transactionFeeType: 'percentage', transactionFeePercentage: 2.5 },
        isActive: true,
        isFeatured: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01'
      }

      const updateData = { name: 'Razorpay Updated', isFeatured: true }
      const updatedGateway = { ...existingGateway, ...updateData }

      // Set initial state
      usePaymentGatewayStore.setState({ gateways: [existingGateway] })

      mockApi.patch.mockResolvedValueOnce({ gateway: updatedGateway })

      const { result } = renderHook(() => usePaymentGatewayStore())

      await act(async () => {
        await result.current.updateGateway('1', updateData)
      })

      expect(mockApi.patch).toHaveBeenCalledWith('/super-admin/payment-gateways/1', updateData)
      expect(result.current.gateways[0]).toEqual(updatedGateway)
    })
  })

  describe('deleteGateway', () => {
    it('should delete gateway successfully', async () => {
      const gateway = {
        id: '1',
        name: 'Razorpay',
        provider: 'razorpay',
        supportedCurrencies: [{ currency: 'INR' }],
        supportedMethods: [{ method: 'credit_card' }],
        requiredConfigFields: {},
        optionalConfigFields: {},
        fees: { transactionFeeType: 'percentage', transactionFeePercentage: 2.5 },
        isActive: true,
        isFeatured: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01'
      }

      // Set initial state
      usePaymentGatewayStore.setState({ gateways: [gateway] })

      mockApi.delete.mockResolvedValueOnce({})

      const { result } = renderHook(() => usePaymentGatewayStore())

      await act(async () => {
        await result.current.deleteGateway('1')
      })

      expect(mockApi.delete).toHaveBeenCalledWith('/super-admin/payment-gateways/1')
      expect(result.current.gateways).toEqual([])
    })
  })

  describe('toggleGatewayStatus', () => {
    it('should toggle gateway status', async () => {
      const gateway = {
        id: '1',
        name: 'Razorpay',
        supportedCurrencies: [{ currency: 'INR' }],
        supportedMethods: [{ method: 'credit_card' }],
        requiredConfigFields: {},
        optionalConfigFields: {},
        fees: { transactionFeeType: 'percentage', transactionFeePercentage: 2.5 },
        isActive: true,
        isFeatured: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01'
      }

      const updatedGateway = { ...gateway, isActive: false }

      // Set initial state
      usePaymentGatewayStore.setState({ gateways: [gateway] })

      mockApi.patch.mockResolvedValueOnce({ gateway: updatedGateway })

      const { result } = renderHook(() => usePaymentGatewayStore())

      await act(async () => {
        await result.current.toggleGatewayStatus('1', false)
      })

      expect(mockApi.patch).toHaveBeenCalledWith('/super-admin/payment-gateways/1', { isActive: false })
    })
  })
})
