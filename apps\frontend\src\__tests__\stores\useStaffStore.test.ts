import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useStaffStore } from '@/stores/institute-admin/useStaffStore'
import { api } from '@/lib/api'

// Mock the API
vi.mock('@/lib/api', () => ({
  api: {
    get: vi.fn(),
    post: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
  }
}))

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    loading: vi.fn(() => 'loading-id'),
    success: vi.fn(),
    error: vi.fn(),
    dismiss: vi.fn(),
  }
}))

describe('useStaffStore', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useStaffStore.setState({
      staffMembers: [],
      selectedStaff: null,
      availableBranches: [],
      availableRoles: [],
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isFetchingRoles: false,
      isFetchingBranches: false,
      error: null,
      pagination: {
        page: 1,
        limit: 20,
        totalPages: 0,
        totalDocs: 0,
        hasNextPage: false,
        hasPrevPage: false
      },
      filters: {
        search: '',
        role: 'all',
        branch_id: 'all',
        status: 'all'
      }
    })

    // Clear all mocks
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const state = useStaffStore.getState()
      
      expect(state.staffMembers).toEqual([])
      expect(state.selectedStaff).toBeNull()
      expect(state.isLoading).toBe(false)
      expect(state.filters.search).toBe('')
      expect(state.filters.role).toBe('all')
      expect(state.filters.status).toBe('all')
    })
  })

  describe('Filters', () => {
    it('should update filters correctly', () => {
      const { setFilters } = useStaffStore.getState()
      
      setFilters({ search: 'john', role: 'trainer' })
      
      const state = useStaffStore.getState()
      expect(state.filters.search).toBe('john')
      expect(state.filters.role).toBe('trainer')
      expect(state.pagination.page).toBe(1) // Should reset to page 1
    })

    it('should clear error when setting filters', () => {
      useStaffStore.setState({ error: 'Some error' })
      
      const { clearError } = useStaffStore.getState()
      clearError()
      
      const state = useStaffStore.getState()
      expect(state.error).toBeNull()
    })
  })

  describe('Fetch Staff', () => {
    it('should fetch staff successfully', async () => {
      const mockStaffData = [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          legacyRole: 'trainer',
          isActive: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ]

      const mockResponse = {
        success: true,
        data: mockStaffData,
        pagination: {
          page: 1,
          limit: 20,
          totalPages: 1,
          totalDocs: 1,
          hasNextPage: false,
          hasPrevPage: false
        }
      }

      vi.mocked(api.get).mockResolvedValueOnce(mockResponse)

      const { fetchStaff } = useStaffStore.getState()
      await fetchStaff()

      const state = useStaffStore.getState()
      expect(state.staffMembers).toEqual(mockStaffData)
      expect(state.isLoading).toBe(false)
      expect(api.get).toHaveBeenCalledWith('/api/institute-admin/staff', {
        page: '1',
        limit: '20'
      })
    })

    it('should handle fetch staff error', async () => {
      const mockError = new Error('Failed to fetch')
      vi.mocked(api.get).mockRejectedValueOnce(mockError)

      const { fetchStaff } = useStaffStore.getState()
      await fetchStaff()

      const state = useStaffStore.getState()
      expect(state.error).toBe('Failed to fetch')
      expect(state.isLoading).toBe(false)
    })
  })

  describe('Create Staff', () => {
    it('should create staff successfully', async () => {
      const mockStaffData = {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        password: 'password123',
        legacyRole: 'trainer',
        isActive: true
      }

      const mockResponse = {
        success: true,
        data: { id: '2', ...mockStaffData },
        message: 'Staff created successfully'
      }

      vi.mocked(api.post).mockResolvedValueOnce(mockResponse)
      vi.mocked(api.get).mockResolvedValueOnce({
        success: true,
        data: [],
        pagination: {
          page: 1,
          limit: 20,
          totalPages: 0,
          totalDocs: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      })

      const { createStaff } = useStaffStore.getState()
      await createStaff(mockStaffData)

      expect(api.post).toHaveBeenCalledWith('/api/institute-admin/staff', mockStaffData)
      expect(api.get).toHaveBeenCalled() // fetchStaff should be called to refresh
    })

    it('should handle create staff error', async () => {
      const mockError = new Error('Email already exists')
      vi.mocked(api.post).mockRejectedValueOnce(mockError)

      const mockStaffData = {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        password: 'password123',
        legacyRole: 'trainer',
        isActive: true
      }

      const { createStaff } = useStaffStore.getState()
      
      await expect(createStaff(mockStaffData)).rejects.toThrow('Email already exists')
      
      const state = useStaffStore.getState()
      expect(state.error).toBe('Email already exists')
      expect(state.isCreating).toBe(false)
    })
  })

  describe('Update Staff', () => {
    it('should update staff successfully', async () => {
      const mockUpdateData = {
        firstName: 'John Updated',
        lastName: 'Doe',
        email: '<EMAIL>'
      }

      const mockResponse = {
        success: true,
        data: { id: '1', ...mockUpdateData },
        message: 'Staff updated successfully'
      }

      vi.mocked(api.patch).mockResolvedValueOnce(mockResponse)
      vi.mocked(api.get).mockResolvedValueOnce({
        success: true,
        data: [],
        pagination: {
          page: 1,
          limit: 20,
          totalPages: 0,
          totalDocs: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      })

      const { updateStaff } = useStaffStore.getState()
      await updateStaff('1', mockUpdateData)

      expect(api.patch).toHaveBeenCalledWith('/api/institute-admin/staff/1', mockUpdateData)
      expect(api.get).toHaveBeenCalled() // fetchStaff should be called to refresh
    })
  })

  describe('Delete Staff', () => {
    it('should delete staff successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'Staff deleted successfully'
      }

      vi.mocked(api.delete).mockResolvedValueOnce(mockResponse)
      vi.mocked(api.get).mockResolvedValueOnce({
        success: true,
        data: [],
        pagination: {
          page: 1,
          limit: 20,
          totalPages: 0,
          totalDocs: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      })

      const { deleteStaff } = useStaffStore.getState()
      await deleteStaff('1')

      expect(api.delete).toHaveBeenCalledWith('/api/institute-admin/staff/1')
      expect(api.get).toHaveBeenCalled() // fetchStaff should be called to refresh
    })
  })

  describe('Toggle Staff Status', () => {
    it('should toggle staff status successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'Staff status updated successfully'
      }

      vi.mocked(api.patch).mockResolvedValueOnce(mockResponse)
      vi.mocked(api.get).mockResolvedValueOnce({
        success: true,
        data: [],
        pagination: {
          page: 1,
          limit: 20,
          totalPages: 0,
          totalDocs: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      })

      const { toggleStaffStatus } = useStaffStore.getState()
      await toggleStaffStatus('1')

      expect(api.patch).toHaveBeenCalledWith('/api/institute-admin/staff/1/status')
      expect(api.get).toHaveBeenCalled() // fetchStaff should be called to refresh
    })
  })

  describe('Fetch Roles and Branches', () => {
    it('should fetch available roles', async () => {
      const mockRoles = [
        { id: '1', name: 'trainer', description: 'Trainer', isActive: true },
        { id: '2', name: 'institute_staff', description: 'Institute Staff', isActive: true }
      ]

      vi.mocked(api.get).mockResolvedValueOnce({
        success: true,
        data: mockRoles
      })

      const { fetchAvailableRoles } = useStaffStore.getState()
      await fetchAvailableRoles()

      const state = useStaffStore.getState()
      expect(state.availableRoles).toEqual(mockRoles)
      expect(api.get).toHaveBeenCalledWith('/api/institute-admin/roles')
    })

    it('should fetch available branches', async () => {
      const mockBranches = [
        { id: '1', name: 'Main Branch', code: 'MAIN', isActive: true },
        { id: '2', name: 'Secondary Branch', code: 'SEC', isActive: true }
      ]

      vi.mocked(api.get).mockResolvedValueOnce({
        success: true,
        data: mockBranches
      })

      const { fetchAvailableBranches } = useStaffStore.getState()
      await fetchAvailableBranches()

      const state = useStaffStore.getState()
      expect(state.availableBranches).toEqual(mockBranches)
      expect(api.get).toHaveBeenCalledWith('/api/institute-admin/branches')
    })
  })
})
