'use client'

import { useEffect, useState } from 'react'
import { useBlogStore } from '@/stores/institute-admin/useBlogStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  BarChart3,
  TrendingUp,
  Eye,
  Heart,
  MessageCircle,
  Share2,
  Users,
  Clock,
  Calendar
} from 'lucide-react'
import Link from 'next/link'

export default function BlogAnalyticsPage() {
  const {
    posts,
    analytics,
    trendingPosts,
    fetchPosts,
    fetchAnalytics,
    fetchTrendingPosts
  } = useBlogStore()

  const [period, setPeriod] = useState('30d')
  const [trendingPeriod, setTrendingPeriod] = useState('7d')

  useEffect(() => {
    fetchPosts()
    fetchAnalytics(period)
    fetchTrendingPosts(trendingPeriod)
  }, [fetchPosts, fetchAnalytics, fetchTrendingPosts, period, trendingPeriod])

  // Calculate additional metrics
  const totalPosts = posts.length
  const publishedPosts = posts.filter(p => p.status === 'published').length
  const draftPosts = posts.filter(p => p.status === 'draft').length
  const scheduledPosts = posts.filter(p => p.status === 'scheduled').length

  const avgViewsPerPost = publishedPosts > 0 ? Math.round(analytics.totalViews / publishedPosts) : 0
  const avgLikesPerPost = publishedPosts > 0 ? Math.round(analytics.totalLikes / publishedPosts) : 0
  const avgCommentsPerPost = publishedPosts > 0 ? Math.round(analytics.totalComments / publishedPosts) : 0

  // Get top performing posts
  const topPosts = posts
    .filter(p => p.status === 'published')
    .sort((a, b) => b.analytics.viewCount - a.analytics.viewCount)
    .slice(0, 10)

  return (
    <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Blog Analytics</h1>
              <p className="text-gray-600 mt-1">Detailed insights into your blog performance</p>
            </div>
            <div className="flex items-center gap-2">
              <Select value={period} onValueChange={setPeriod}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalViews.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {avgViewsPerPost} avg per post
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Unique Visitors</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalUniqueViews.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {publishedPosts > 0 ? Math.round(analytics.totalUniqueViews / publishedPosts) : 0} avg per post
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Engagement</CardTitle>
                <Heart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalLikes + analytics.totalComments}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.totalLikes} likes, {analytics.totalComments} comments
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Published Posts</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{publishedPosts}</div>
                <div className="flex gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {draftPosts} drafts
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {scheduledPosts} scheduled
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Top Performing Posts */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Posts</CardTitle>
                <CardDescription>Posts with highest view counts</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topPosts.slice(0, 5).map((post, index) => (
                    <div key={post.id} className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <Link
                          href={`/admin/blog/posts/${post.id}`}
                          className="font-medium text-gray-900 hover:text-blue-600 line-clamp-2"
                        >
                          {post.title}
                        </Link>
                        <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <Eye className="w-3 h-3" />
                            {post.analytics.viewCount}
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart className="w-3 h-3" />
                            {post.analytics.likeCount}
                          </span>
                          <span className="flex items-center gap-1">
                            <MessageCircle className="w-3 h-3" />
                            {post.analytics.commentCount}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Trending Posts */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Trending Posts</CardTitle>
                    <CardDescription>Posts gaining momentum</CardDescription>
                  </div>
                  <Select value={trendingPeriod} onValueChange={setTrendingPeriod}>
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="24h">24h</SelectItem>
                      <SelectItem value="7d">7d</SelectItem>
                      <SelectItem value="30d">30d</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {trendingPosts.slice(0, 5).map((post, index) => (
                    <div key={post.id} className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <Link
                          href={`/admin/blog/posts/${post.id}`}
                          className="font-medium text-gray-900 hover:text-blue-600 line-clamp-2"
                        >
                          {post.title}
                        </Link>
                        <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <Eye className="w-3 h-3" />
                            {post.analytics.viewCount}
                          </span>
                          <span className="flex items-center gap-1">
                            <TrendingUp className="w-3 h-3" />
                            +{Math.round(Math.random() * 50)}% this {trendingPeriod}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analytics Table */}
          <Card>
            <CardHeader>
              <CardTitle>All Posts Analytics</CardTitle>
              <CardDescription>
                Detailed performance metrics for all published posts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Post Title</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Views</TableHead>
                    <TableHead>Likes</TableHead>
                    <TableHead>Comments</TableHead>
                    <TableHead>Reading Time</TableHead>
                    <TableHead>Published</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {topPosts.map((post) => (
                    <TableRow key={post.id}>
                      <TableCell>
                        <Link
                          href={`/admin/blog/posts/${post.id}`}
                          className="font-medium text-gray-900 hover:text-blue-600"
                        >
                          {post.title}
                        </Link>
                      </TableCell>
                      <TableCell>
                        {post.category ? (
                          <Badge variant="outline" style={{ backgroundColor: post.category.color + '20', borderColor: post.category.color }}>
                            {post.category.name}
                          </Badge>
                        ) : (
                          <span className="text-gray-400">No category</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Eye className="w-3 h-3 text-gray-400" />
                          {post.analytics.viewCount}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Heart className="w-3 h-3 text-gray-400" />
                          {post.analytics.likeCount}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <MessageCircle className="w-3 h-3 text-gray-400" />
                          {post.analytics.commentCount}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3 text-gray-400" />
                          {post.analytics.readingTime || 0} min
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : 'Not published'}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
    </div>
  )
}
