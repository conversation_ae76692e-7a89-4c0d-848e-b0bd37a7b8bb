'use client'

import { useEffect, useState } from 'react'
import { useBlogStore } from '@/stores/institute-admin/useBlogStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import {
  FolderOpen,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Palette
} from 'lucide-react'
import { useFormik } from 'formik'
import * as Yup from 'yup'

const categorySchema = Yup.object({
  name: Yup.string().required('Name is required').max(100, 'Name too long'),
  slug: Yup.string().required('Slug is required').max(100, 'Slug too long'),
  description: Yup.string().max(500, 'Description too long'),
  color: Yup.string().matches(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
  icon: Yup.string(),
  displayOrder: Yup.number().min(0, 'Order must be positive')
})

export default function BlogCategoriesPage() {
  const {
    categories,
    categoriesLoading,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory
  } = useBlogStore()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<any>(null)

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  // Helper function to generate slug
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s]+/g, '') // Remove special characters but keep spaces
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/(^-|-$)/g, '') // Remove leading/trailing hyphens
  }

  const createFormik = useFormik({
    initialValues: {
      name: '',
      slug: '',
      description: '',
      color: '#3B82F6',
      icon: '',
      displayOrder: 0,
      isActive: true
    },
    validationSchema: categorySchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        await createCategory(values)
        resetForm()
        setIsCreateDialogOpen(false)
      } catch (error) {
        console.error('Failed to create category:', error)
      }
    }
  })

  const editFormik = useFormik({
    initialValues: {
      name: '',
      slug: '',
      description: '',
      color: '#3B82F6',
      icon: '',
      displayOrder: 0,
      isActive: true
    },
    validationSchema: categorySchema,
    onSubmit: async (values) => {
      try {
        await updateCategory(editingCategory.id, values)
        setIsEditDialogOpen(false)
        setEditingCategory(null)
      } catch (error) {
        console.error('Failed to update category:', error)
      }
    }
  })

  // Auto-generate slug from name
  useEffect(() => {
    if (createFormik.values.name) {
      const slug = generateSlug(createFormik.values.name)
      createFormik.setFieldValue('slug', slug)
    } else {
      createFormik.setFieldValue('slug', '')
    }
  }, [createFormik.values.name])

  useEffect(() => {
    if (editFormik.values.name && editingCategory) {
      // Only auto-generate if user hasn't manually changed the slug
      const originalSlug = generateSlug(editingCategory.name)
      const currentSlug = editFormik.values.slug

      // If current slug matches the original generated slug, update it
      if (currentSlug === originalSlug || currentSlug === editingCategory.slug) {
        const newSlug = generateSlug(editFormik.values.name)
        editFormik.setFieldValue('slug', newSlug)
      }
    }
  }, [editFormik.values.name, editingCategory])

  const handleEditCategory = (category: any) => {
    setEditingCategory(category)
    editFormik.setValues({
      name: category.name,
      slug: category.slug,
      description: category.description || '',
      color: category.color || '#3B82F6',
      icon: category.icon || '',
      displayOrder: category.displayOrder,
      isActive: category.isActive
    })
    setIsEditDialogOpen(true)
  }

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      await deleteCategory(categoryId)
    } catch (error) {
      console.error('Failed to delete category:', error)
    }
  }

  return (
    <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Blog Categories</h1>
              <p className="text-gray-600 mt-1">Organize your blog content with categories</p>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Category
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Create New Category</DialogTitle>
                  <DialogDescription>
                    Add a new category to organize your blog posts.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={createFormik.handleSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="name" className="text-right">
                        Name
                      </Label>
                      <Input
                        id="name"
                        name="name"
                        value={createFormik.values.name}
                        onChange={createFormik.handleChange}
                        className="col-span-3"
                        placeholder="Category name"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="slug" className="text-right">
                        Slug
                      </Label>
                      <Input
                        id="slug"
                        name="slug"
                        value={createFormik.values.slug}
                        onChange={createFormik.handleChange}
                        className="col-span-3"
                        placeholder="url-friendly-slug"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="description" className="text-right">
                        Description
                      </Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={createFormik.values.description}
                        onChange={createFormik.handleChange}
                        className="col-span-3"
                        placeholder="Category description"
                        rows={3}
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="color" className="text-right">
                        Color
                      </Label>
                      <div className="col-span-3 flex items-center gap-2">
                        <Input
                          id="color"
                          name="color"
                          type="color"
                          value={createFormik.values.color}
                          onChange={createFormik.handleChange}
                          className="w-16 h-10"
                        />
                        <Input
                          value={createFormik.values.color}
                          onChange={(e) => createFormik.setFieldValue('color', e.target.value)}
                          placeholder="#3B82F6"
                          className="flex-1"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="icon" className="text-right">
                        Icon
                      </Label>
                      <Input
                        id="icon"
                        name="icon"
                        value={createFormik.values.icon}
                        onChange={createFormik.handleChange}
                        className="col-span-3"
                        placeholder="book, graduation-cap, etc."
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="displayOrder" className="text-right">
                        Order
                      </Label>
                      <Input
                        id="displayOrder"
                        name="displayOrder"
                        type="number"
                        value={createFormik.values.displayOrder}
                        onChange={createFormik.handleChange}
                        className="col-span-3"
                        min="0"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="isActive" className="text-right">
                        Active
                      </Label>
                      <Switch
                        id="isActive"
                        checked={createFormik.values.isActive}
                        onCheckedChange={(checked) => createFormik.setFieldValue('isActive', checked)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="submit" disabled={categoriesLoading}>
                      Create Category
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Categories Table */}
          <Card>
            <CardHeader>
              <CardTitle>Categories ({categories.length})</CardTitle>
              <CardDescription>
                Manage your blog categories and their organization
              </CardDescription>
            </CardHeader>
            <CardContent>
              {categoriesLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full" />
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Color</TableHead>
                      <TableHead>Order</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {categories.map((category) => (
                      <TableRow key={category.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {category.color && (
                              <div
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: category.color }}
                              />
                            )}
                            <div>
                              <div className="font-medium">{category.name}</div>
                              <div className="text-sm text-gray-500">/{category.slug}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="max-w-xs truncate">
                            {category.description || 'No description'}
                          </div>
                        </TableCell>
                        <TableCell>
                          {category.color && (
                            <Badge variant="outline" style={{ backgroundColor: category.color + '20', borderColor: category.color }}>
                              {category.color}
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{category.displayOrder}</TableCell>
                        <TableCell>
                          <Badge variant={category.isActive ? 'default' : 'secondary'}>
                            {category.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{new Date(category.createdAt).toLocaleDateString()}</div>
                            <div className="text-gray-500">
                              by {category.createdBy.firstName} {category.createdBy.lastName}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEditCategory(category)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      This action cannot be undone. This will permanently delete the category.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction onClick={() => handleDeleteCategory(category.id)}>
                                      Delete
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* Edit Category Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Edit Category</DialogTitle>
                <DialogDescription>
                  Update the category information.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={editFormik.handleSubmit}>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-name" className="text-right">
                      Name
                    </Label>
                    <Input
                      id="edit-name"
                      name="name"
                      value={editFormik.values.name}
                      onChange={editFormik.handleChange}
                      className="col-span-3"
                      placeholder="Category name"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-slug" className="text-right">
                      Slug
                    </Label>
                    <Input
                      id="edit-slug"
                      name="slug"
                      value={editFormik.values.slug}
                      onChange={editFormik.handleChange}
                      className="col-span-3"
                      placeholder="url-friendly-slug"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-description" className="text-right">
                      Description
                    </Label>
                    <Textarea
                      id="edit-description"
                      name="description"
                      value={editFormik.values.description}
                      onChange={editFormik.handleChange}
                      className="col-span-3"
                      placeholder="Category description"
                      rows={3}
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-color" className="text-right">
                      Color
                    </Label>
                    <div className="col-span-3 flex items-center gap-2">
                      <Input
                        id="edit-color"
                        name="color"
                        type="color"
                        value={editFormik.values.color}
                        onChange={editFormik.handleChange}
                        className="w-16 h-10"
                      />
                      <Input
                        value={editFormik.values.color}
                        onChange={(e) => editFormik.setFieldValue('color', e.target.value)}
                        placeholder="#3B82F6"
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-icon" className="text-right">
                      Icon
                    </Label>
                    <Input
                      id="edit-icon"
                      name="icon"
                      value={editFormik.values.icon}
                      onChange={editFormik.handleChange}
                      className="col-span-3"
                      placeholder="book, graduation-cap, etc."
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-displayOrder" className="text-right">
                      Order
                    </Label>
                    <Input
                      id="edit-displayOrder"
                      name="displayOrder"
                      type="number"
                      value={editFormik.values.displayOrder}
                      onChange={editFormik.handleChange}
                      className="col-span-3"
                      min="0"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-isActive" className="text-right">
                      Active
                    </Label>
                    <Switch
                      id="edit-isActive"
                      checked={editFormik.values.isActive}
                      onCheckedChange={(checked) => editFormik.setFieldValue('isActive', checked)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" disabled={categoriesLoading}>
                    Update Category
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
    </div>
  )
}
