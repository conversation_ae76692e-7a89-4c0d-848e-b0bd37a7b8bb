'use client'

import { useEffect, useState } from 'react'
import { useBlogStore } from '@/stores/institute-admin/useBlogStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import {
  FileText,
  MoreHorizontal,
  Edit,
  Send,
  Trash2,
  Eye,
  Plus,
  Search
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export default function DraftsPage() {
  const router = useRouter()
  const {
    posts,
    postsLoading,
    fetchPosts,
    deletePost,
    publishPost
  } = useBlogStore()

  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchPosts({ status: 'draft' })
  }, [fetchPosts])

  const draftPosts = posts.filter(post => post.status === 'draft')

  // Filter drafts based on search term
  const filteredDrafts = draftPosts.filter(post => {
    const matchesSearch = searchTerm === '' || 
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.excerpt?.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesSearch
  })

  const handleDeletePost = async (postId: string) => {
    try {
      await deletePost(postId)
      fetchPosts({ status: 'draft' }) // Refresh the list
    } catch (error) {
      console.error('Failed to delete post:', error)
    }
  }

  const handlePublishPost = async (postId: string) => {
    try {
      await publishPost(postId)
      fetchPosts({ status: 'draft' }) // Refresh the list
    } catch (error) {
      console.error('Failed to publish post:', error)
    }
  }

  return (
    <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Draft Posts</h1>
              <p className="text-gray-600 mt-1">Manage your unpublished blog posts</p>
            </div>
            <Button asChild>
              <Link href="/admin/blog/posts/new">
                <Plus className="w-4 h-4 mr-2" />
                Create New Post
              </Link>
            </Button>
          </div>

          {/* Search */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search draft posts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>

          {/* Drafts */}
          <Card>
            <CardHeader>
              <CardTitle>Draft Posts ({filteredDrafts.length})</CardTitle>
              <CardDescription>
                {filteredDrafts.length} of {draftPosts.length} draft posts
              </CardDescription>
            </CardHeader>
            <CardContent>
              {postsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full" />
                </div>
              ) : filteredDrafts.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {searchTerm ? 'No matching drafts found' : 'No Draft Posts'}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm 
                      ? 'Try adjusting your search terms to find what you\'re looking for.'
                      : 'You don\'t have any draft posts yet. Start writing your first post!'
                    }
                  </p>
                  {!searchTerm && (
                    <Button asChild>
                      <Link href="/admin/blog/posts/new">
                        Create New Post
                      </Link>
                    </Button>
                  )}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Last Modified</TableHead>
                      <TableHead>Author</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDrafts.map((post) => (
                      <TableRow key={post.id}>
                        <TableCell>
                          <div>
                            <Link
                              href={`/admin/blog/posts/${post.id}/edit`}
                              className="font-medium text-gray-900 hover:text-blue-600"
                            >
                              {post.title}
                            </Link>
                            {post.excerpt && (
                              <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                                {post.excerpt}
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {post.category ? (
                            <Badge variant="outline" style={{ backgroundColor: post.category.color + '20', borderColor: post.category.color }}>
                              {post.category.name}
                            </Badge>
                          ) : (
                            <span className="text-gray-400">No category</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{new Date(post.updatedAt).toLocaleDateString()}</div>
                            <div className="text-gray-500">
                              {new Date(post.updatedAt).toLocaleTimeString()}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{post.author.firstName} {post.author.lastName}</div>
                            {post.lastEditedBy && post.lastEditedBy.id !== post.author.id && (
                              <div className="text-gray-500">
                                Edited by {post.lastEditedBy.firstName} {post.lastEditedBy.lastName}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => router.push(`/admin/blog/posts/${post.id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                Preview
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/admin/blog/posts/${post.id}/edit`)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handlePublishPost(post.id)}>
                                <Send className="mr-2 h-4 w-4" />
                                Publish
                              </DropdownMenuItem>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      This action cannot be undone. This will permanently delete the draft post.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction onClick={() => handleDeletePost(post.id)}>
                                      Delete
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
    </div>
  )
}
