'use client'

import { ReactNode } from 'react'
import BlogSidebar from '@/components/institute-admin/blog/BlogSidebar'

interface BlogLayoutProps {
  children: ReactNode
}

export default function BlogLayout({ children }: BlogLayoutProps) {
  return (
    <div className="flex flex-col h-full">
      {/* Horizontal Blog Navigation */}
      <BlogSidebar />
      
      {/* Main Content Area */}
      <div className="flex-1 overflow-hidden">
        <main className="h-full overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  )
}
