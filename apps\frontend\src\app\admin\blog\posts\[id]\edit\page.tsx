'use client'

import { useEffect } from 'react'
import { useParams } from 'next/navigation'
import { useBlogStore } from '@/stores/institute-admin/useBlogStore'
import BlogEditor from '@/components/institute-admin/blog/BlogEditor'

export default function EditBlogPostPage() {
  const params = useParams()
  const { currentPost, fetchPost, postsLoading } = useBlogStore()
  
  const postId = params.id as string

  useEffect(() => {
    if (postId) {
      fetchPost(postId)
    }
  }, [postId, fetchPost])

  if (postsLoading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <BlogSidebar />

        <div className="flex-1 overflow-y-auto flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4" />
            <p className="text-gray-600">Loading post...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!currentPost) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-gray-600">Post not found</p>
        </div>
      </div>
    )
  }

  return <BlogEditor mode="edit" post={currentPost} />
}
