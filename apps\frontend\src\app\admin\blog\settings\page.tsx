'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import {
  Settings,
  Save,
  Globe,
  MessageCircle,
  Bell,
  Shield,
  Palette
} from 'lucide-react'
import { toast } from 'sonner'

export default function BlogSettingsPage() {
  const [settings, setSettings] = useState({
    // General Settings
    blogTitle: 'Institute Blog',
    blogDescription: 'Latest news and updates from our institute',
    blogUrl: 'blog',
    postsPerPage: 10,
    
    // Comment Settings
    allowComments: true,
    moderateComments: true,
    requireApproval: true,
    allowGuestComments: false,
    
    // SEO Settings
    enableSEO: true,
    defaultMetaDescription: 'Stay updated with the latest news and insights from our institute',
    enableSitemap: true,
    enableRobotsTxt: true,
    
    // Notification Settings
    emailNotifications: true,
    newPostNotifications: true,
    commentNotifications: true,
    
    // Display Settings
    showAuthor: true,
    showDate: true,
    showCategories: true,
    showTags: true,
    showReadingTime: true,
    showSocialShare: true,
    
    // Advanced Settings
    enableAnalytics: true,
    enableCache: true,
    cacheExpiry: 3600
  })

  const handleSave = () => {
    // In a real implementation, this would save to the backend
    toast.success('Blog settings saved successfully')
  }

  const handleReset = () => {
    // Reset to default values
    setSettings({
      blogTitle: 'Institute Blog',
      blogDescription: 'Latest news and updates from our institute',
      blogUrl: 'blog',
      postsPerPage: 10,
      allowComments: true,
      moderateComments: true,
      requireApproval: true,
      allowGuestComments: false,
      enableSEO: true,
      defaultMetaDescription: 'Stay updated with the latest news and insights from our institute',
      enableSitemap: true,
      enableRobotsTxt: true,
      emailNotifications: true,
      newPostNotifications: true,
      commentNotifications: true,
      showAuthor: true,
      showDate: true,
      showCategories: true,
      showTags: true,
      showReadingTime: true,
      showSocialShare: true,
      enableAnalytics: true,
      enableCache: true,
      cacheExpiry: 3600
    })
    toast.success('Settings reset to defaults')
  }

  return (
    <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Blog Settings</h1>
              <p className="text-gray-600 mt-1">Configure your blog preferences and behavior</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleReset}>
                Reset to Defaults
              </Button>
              <Button onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Save Settings
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* General Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="w-4 h-4 mr-2" />
                  General Settings
                </CardTitle>
                <CardDescription>
                  Basic configuration for your blog
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="blogTitle">Blog Title</Label>
                  <Input
                    id="blogTitle"
                    value={settings.blogTitle}
                    onChange={(e) => setSettings({...settings, blogTitle: e.target.value})}
                    placeholder="Enter blog title"
                  />
                </div>
                <div>
                  <Label htmlFor="blogDescription">Blog Description</Label>
                  <Textarea
                    id="blogDescription"
                    value={settings.blogDescription}
                    onChange={(e) => setSettings({...settings, blogDescription: e.target.value})}
                    placeholder="Enter blog description"
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="blogUrl">Blog URL Slug</Label>
                  <Input
                    id="blogUrl"
                    value={settings.blogUrl}
                    onChange={(e) => setSettings({...settings, blogUrl: e.target.value})}
                    placeholder="blog"
                  />
                </div>
                <div>
                  <Label htmlFor="postsPerPage">Posts Per Page</Label>
                  <Select
                    value={settings.postsPerPage.toString()}
                    onValueChange={(value) => setSettings({...settings, postsPerPage: parseInt(value)})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5 posts</SelectItem>
                      <SelectItem value="10">10 posts</SelectItem>
                      <SelectItem value="15">15 posts</SelectItem>
                      <SelectItem value="20">20 posts</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Comment Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Comment Settings
                </CardTitle>
                <CardDescription>
                  Manage how comments work on your blog
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="allowComments">Allow Comments</Label>
                  <Switch
                    id="allowComments"
                    checked={settings.allowComments}
                    onCheckedChange={(checked) => setSettings({...settings, allowComments: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="moderateComments">Moderate Comments</Label>
                  <Switch
                    id="moderateComments"
                    checked={settings.moderateComments}
                    onCheckedChange={(checked) => setSettings({...settings, moderateComments: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="requireApproval">Require Approval</Label>
                  <Switch
                    id="requireApproval"
                    checked={settings.requireApproval}
                    onCheckedChange={(checked) => setSettings({...settings, requireApproval: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="allowGuestComments">Allow Guest Comments</Label>
                  <Switch
                    id="allowGuestComments"
                    checked={settings.allowGuestComments}
                    onCheckedChange={(checked) => setSettings({...settings, allowGuestComments: checked})}
                  />
                </div>
              </CardContent>
            </Card>

            {/* SEO Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="w-4 h-4 mr-2" />
                  SEO Settings
                </CardTitle>
                <CardDescription>
                  Search engine optimization configuration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="enableSEO">Enable SEO Features</Label>
                  <Switch
                    id="enableSEO"
                    checked={settings.enableSEO}
                    onCheckedChange={(checked) => setSettings({...settings, enableSEO: checked})}
                  />
                </div>
                <div>
                  <Label htmlFor="defaultMetaDescription">Default Meta Description</Label>
                  <Textarea
                    id="defaultMetaDescription"
                    value={settings.defaultMetaDescription}
                    onChange={(e) => setSettings({...settings, defaultMetaDescription: e.target.value})}
                    placeholder="Default meta description for posts"
                    rows={3}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="enableSitemap">Generate Sitemap</Label>
                  <Switch
                    id="enableSitemap"
                    checked={settings.enableSitemap}
                    onCheckedChange={(checked) => setSettings({...settings, enableSitemap: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="enableRobotsTxt">Generate Robots.txt</Label>
                  <Switch
                    id="enableRobotsTxt"
                    checked={settings.enableRobotsTxt}
                    onCheckedChange={(checked) => setSettings({...settings, enableRobotsTxt: checked})}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Display Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Palette className="w-4 h-4 mr-2" />
                  Display Settings
                </CardTitle>
                <CardDescription>
                  Control what information is shown on posts
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="showAuthor">Show Author</Label>
                  <Switch
                    id="showAuthor"
                    checked={settings.showAuthor}
                    onCheckedChange={(checked) => setSettings({...settings, showAuthor: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="showDate">Show Date</Label>
                  <Switch
                    id="showDate"
                    checked={settings.showDate}
                    onCheckedChange={(checked) => setSettings({...settings, showDate: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="showCategories">Show Categories</Label>
                  <Switch
                    id="showCategories"
                    checked={settings.showCategories}
                    onCheckedChange={(checked) => setSettings({...settings, showCategories: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="showTags">Show Tags</Label>
                  <Switch
                    id="showTags"
                    checked={settings.showTags}
                    onCheckedChange={(checked) => setSettings({...settings, showTags: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="showReadingTime">Show Reading Time</Label>
                  <Switch
                    id="showReadingTime"
                    checked={settings.showReadingTime}
                    onCheckedChange={(checked) => setSettings({...settings, showReadingTime: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="showSocialShare">Show Social Share</Label>
                  <Switch
                    id="showSocialShare"
                    checked={settings.showSocialShare}
                    onCheckedChange={(checked) => setSettings({...settings, showSocialShare: checked})}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Notification Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="w-4 h-4 mr-2" />
                  Notification Settings
                </CardTitle>
                <CardDescription>
                  Configure email notifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="emailNotifications">Email Notifications</Label>
                  <Switch
                    id="emailNotifications"
                    checked={settings.emailNotifications}
                    onCheckedChange={(checked) => setSettings({...settings, emailNotifications: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="newPostNotifications">New Post Notifications</Label>
                  <Switch
                    id="newPostNotifications"
                    checked={settings.newPostNotifications}
                    onCheckedChange={(checked) => setSettings({...settings, newPostNotifications: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="commentNotifications">Comment Notifications</Label>
                  <Switch
                    id="commentNotifications"
                    checked={settings.commentNotifications}
                    onCheckedChange={(checked) => setSettings({...settings, commentNotifications: checked})}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Advanced Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="w-4 h-4 mr-2" />
                  Advanced Settings
                </CardTitle>
                <CardDescription>
                  Performance and technical configuration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="enableAnalytics">Enable Analytics</Label>
                  <Switch
                    id="enableAnalytics"
                    checked={settings.enableAnalytics}
                    onCheckedChange={(checked) => setSettings({...settings, enableAnalytics: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="enableCache">Enable Caching</Label>
                  <Switch
                    id="enableCache"
                    checked={settings.enableCache}
                    onCheckedChange={(checked) => setSettings({...settings, enableCache: checked})}
                  />
                </div>
                <div>
                  <Label htmlFor="cacheExpiry">Cache Expiry (seconds)</Label>
                  <Input
                    id="cacheExpiry"
                    type="number"
                    value={settings.cacheExpiry}
                    onChange={(e) => setSettings({...settings, cacheExpiry: parseInt(e.target.value) || 3600})}
                    min="60"
                    max="86400"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
    </div>
  )
}
