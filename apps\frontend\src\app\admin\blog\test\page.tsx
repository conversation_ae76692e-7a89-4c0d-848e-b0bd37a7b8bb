'use client'

import { useState, useEffect } from 'react'
import { useBlogStore } from '@/stores/institute-admin/useBlogStore'
import NavigationTest from '@/components/institute-admin/blog/NavigationTest'
import SidebarVisibilityTest from '@/components/debug/SidebarVisibilityTest'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  TestTube,
  Database,
  FolderOpen,
  TrendingUp,
  Eye,
  Heart,
  MessageCircle
} from 'lucide-react'
import { toast } from 'sonner'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message?: string
  duration?: number
}

export default function BlogTestPage() {
  const {
    posts,
    categories,
    analytics,
    trendingPosts,
    fetchPosts,
    fetchCategories,
    fetchAnalytics,
    fetchTrendingPosts,
    createPost,
    createCategory
  } = useBlogStore()

  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const updateTestResult = (name: string, status: 'success' | 'error', message?: string, duration?: number) => {
    setTestResults(prev => prev.map(test => 
      test.name === name 
        ? { ...test, status, message, duration }
        : test
    ))
  }

  const runTest = async (testName: string, testFn: () => Promise<void>) => {
    const startTime = Date.now()
    try {
      await testFn()
      const duration = Date.now() - startTime
      updateTestResult(testName, 'success', 'Passed', duration)
    } catch (error: any) {
      const duration = Date.now() - startTime
      updateTestResult(testName, 'error', error.message || 'Failed', duration)
    }
  }

  const runAllTests = async () => {
    setIsRunning(true)
    
    // Initialize test results
    const tests = [
      'Store Initialization',
      'Fetch Posts',
      'Fetch Categories', 
      'Fetch Analytics',
      'Fetch Trending Posts',
      'Create Category',
      'Create Post',
      'Search Functionality',
      'Authentication Check',
      'Role-based Access'
    ]

    setTestResults(tests.map(name => ({ name, status: 'pending' })))

    // Test 1: Store Initialization
    await runTest('Store Initialization', async () => {
      if (typeof useBlogStore !== 'function') {
        throw new Error('Blog store not properly initialized')
      }
    })

    // Test 2: Fetch Posts
    await runTest('Fetch Posts', async () => {
      await fetchPosts()
      if (!Array.isArray(posts)) {
        throw new Error('Posts not fetched properly')
      }
    })

    // Test 3: Fetch Categories
    await runTest('Fetch Categories', async () => {
      await fetchCategories()
      if (!Array.isArray(categories)) {
        throw new Error('Categories not fetched properly')
      }
    })

    // Test 4: Fetch Analytics
    await runTest('Fetch Analytics', async () => {
      await fetchAnalytics()
      if (!analytics || typeof analytics.totalViews !== 'number') {
        throw new Error('Analytics not fetched properly')
      }
    })

    // Test 5: Fetch Trending Posts
    await runTest('Fetch Trending Posts', async () => {
      await fetchTrendingPosts()
      if (!Array.isArray(trendingPosts)) {
        throw new Error('Trending posts not fetched properly')
      }
    })

    // Test 6: Create Category
    await runTest('Create Category', async () => {
      const testCategory = {
        name: 'Test Category',
        slug: 'test-category',
        description: 'A test category for integration testing',
        color: '#3B82F6',
        isActive: true,
        displayOrder: 999
      }
      
      try {
        await createCategory(testCategory)
      } catch (error: any) {
        // If it fails due to duplicate, that's actually good - means the endpoint works
        if (error.response?.data?.error?.includes('duplicate') || 
            error.response?.data?.error?.includes('already exists')) {
          return // Pass the test
        }
        throw error
      }
    })

    // Test 7: Create Post
    await runTest('Create Post', async () => {
      const testPost = {
        title: 'Test Blog Post',
        slug: 'test-blog-post',
        excerpt: 'This is a test blog post for integration testing',
        content: 'This is the content of the test blog post. It contains some sample text to verify that the blog post creation functionality is working properly.',
        status: 'draft',
        tags: [{ tag: 'test' }, { tag: 'integration' }],
        seo: {
          title: 'Test Blog Post - SEO Title',
          description: 'SEO description for the test blog post'
        },
        settings: {
          allowComments: true,
          isFeatured: false,
          isSticky: false
        }
      }
      
      try {
        await createPost(testPost)
      } catch (error: any) {
        // If it fails due to duplicate, that's actually good - means the endpoint works
        if (error.response?.data?.error?.includes('duplicate') || 
            error.response?.data?.error?.includes('already exists')) {
          return // Pass the test
        }
        throw error
      }
    })

    // Test 8: Search Functionality
    await runTest('Search Functionality', async () => {
      // This would test the search functionality
      // For now, we'll just check if the search function exists
      if (typeof useBlogStore.getState().searchPosts !== 'function') {
        throw new Error('Search functionality not available')
      }
    })

    // Test 9: Authentication Check
    await runTest('Authentication Check', async () => {
      // Check if we can access the current user context
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }
    })

    // Test 10: Role-based Access
    await runTest('Role-based Access', async () => {
      // Check if user has proper institute admin access
      const userStr = localStorage.getItem('user')
      if (!userStr) {
        throw new Error('No user information found')
      }
      
      const user = JSON.parse(userStr)
      if (!user.institute) {
        throw new Error('User not associated with an institute')
      }
      
      if (!['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
        throw new Error('User does not have blog management permissions')
      }
    })

    setIsRunning(false)
    
    // Show summary
    const passedTests = testResults.filter(t => t.status === 'success').length
    const totalTests = testResults.length
    
    if (passedTests === totalTests) {
      toast.success(`All ${totalTests} tests passed! Blog system is ready.`)
    } else {
      toast.error(`${totalTests - passedTests} tests failed. Please check the results.`)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600" />
      case 'pending':
        return isRunning ? <Loader2 className="w-5 h-5 text-blue-600 animate-spin" /> : <AlertCircle className="w-5 h-5 text-gray-400" />
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-600">Passed</Badge>
      case 'error':
        return <Badge variant="destructive">Failed</Badge>
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const passedTests = testResults.filter(t => t.status === 'success').length
  const failedTests = testResults.filter(t => t.status === 'error').length
  const totalTests = testResults.length

  return (
    <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Blog System Integration Test</h1>
              <p className="text-gray-600 mt-1">Verify that all blog management features are working correctly</p>
            </div>
            <Button 
              onClick={runAllTests} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              {isRunning ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <TestTube className="w-4 h-4" />
              )}
              {isRunning ? 'Running Tests...' : 'Run All Tests'}
            </Button>
          </div>

          {/* Test Summary */}
          {testResults.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Tests</p>
                      <p className="text-2xl font-bold">{totalTests}</p>
                    </div>
                    <TestTube className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Passed</p>
                      <p className="text-2xl font-bold text-green-600">{passedTests}</p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Failed</p>
                      <p className="text-2xl font-bold text-red-600">{failedTests}</p>
                    </div>
                    <XCircle className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Test Results */}
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>
                Detailed results for each integration test
              </CardDescription>
            </CardHeader>
            <CardContent>
              {testResults.length === 0 ? (
                <div className="text-center py-8">
                  <TestTube className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Tests Run Yet</h3>
                  <p className="text-gray-600 mb-4">Click "Run All Tests" to start the integration testing process.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {testResults.map((test, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(test.status)}
                        <div>
                          <h4 className="font-medium">{test.name}</h4>
                          {test.message && (
                            <p className="text-sm text-gray-600">{test.message}</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {test.duration && (
                          <span className="text-xs text-gray-500">{test.duration}ms</span>
                        )}
                        {getStatusBadge(test.status)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Sidebar Visibility Test */}
          <SidebarVisibilityTest />

          {/* Navigation Integration Test */}
          <NavigationTest />

          {/* System Information */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>System Information</CardTitle>
              <CardDescription>
                Current system status and configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Database className="w-4 h-4 text-blue-600" />
                    <span className="font-medium">Posts:</span>
                    <span>{posts.length}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FolderOpen className="w-4 h-4 text-green-600" />
                    <span className="font-medium">Categories:</span>
                    <span>{categories.length}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-4 h-4 text-orange-600" />
                    <span className="font-medium">Trending Posts:</span>
                    <span>{trendingPosts.length}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Eye className="w-4 h-4 text-purple-600" />
                    <span className="font-medium">Total Views:</span>
                    <span>{analytics.totalViews}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Heart className="w-4 h-4 text-red-600" />
                    <span className="font-medium">Total Likes:</span>
                    <span>{analytics.totalLikes}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MessageCircle className="w-4 h-4 text-blue-600" />
                    <span className="font-medium">Total Comments:</span>
                    <span>{analytics.totalComments}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
    </div>
  )
}
