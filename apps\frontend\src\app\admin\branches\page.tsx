'use client'

import { useEffect, useState } from 'react'
import { Plus, Search, Filter, MoreHorizontal, MapPin, Phone, Mail, Building2, Users, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { useBranchStore } from '@/stores/institute/useBranchStore'
import { CreateBranchModal } from '@/components/institute/branches/CreateBranchModal'
import { EditBranchModal } from '@/components/institute/branches/EditBranchModal'
import { DeleteBranchModal } from '@/components/institute/branches/DeleteBranchModal'
import { BranchWizardModal } from '@/components/institute/branches/BranchWizardModal'

export default function BranchesPage() {
  const {
    branches,
    pagination,
    isLoading,
    searchQuery,
    statusFilter,
    showCreateBranchModal,
    showEditBranchModal,
    showDeleteConfirmModal,
    fetchBranches,
    setSearchQuery,
    setStatusFilter,
    setShowCreateBranchModal,
    setShowEditBranchModal,
    setShowDeleteConfirmModal,
    setEditingBranch,
    setDeletingBranch,
    toggleBranchStatus,
    getActiveBranches,
    getInactiveBranches,
  } = useBranchStore()

  const [searchInput, setSearchInput] = useState(searchQuery)
  const [showWizardModal, setShowWizardModal] = useState(false)
  const [wizardMode, setWizardMode] = useState<'create' | 'edit'>('create')
  const [wizardEditingBranch, setWizardEditingBranch] = useState<any>(null)

  // Fetch branches on component mount
  useEffect(() => {
    fetchBranches()
  }, [fetchBranches])

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchQuery(searchInput)
      fetchBranches({ search: searchInput })
    }, 500)

    return () => clearTimeout(timer)
  }, [searchInput, setSearchQuery, fetchBranches])

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value as 'all' | 'active' | 'inactive')
    fetchBranches()
  }

  const handleCreateWithWizard = () => {
    setWizardMode('create')
    setWizardEditingBranch(null)
    setShowWizardModal(true)
  }

  const handleEditWithWizard = (branch: any) => {
    setWizardMode('edit')
    setWizardEditingBranch(branch)
    setShowWizardModal(true)
  }

  const handleCloseWizard = () => {
    setShowWizardModal(false)
    setWizardEditingBranch(null)
  }

  // Filter branches based on status
  const filteredBranches = branches.filter((branch) => {
    if (statusFilter === 'active') return branch.isActive
    if (statusFilter === 'inactive') return !branch.isActive
    return true
  })

  const handleEditBranch = (branch: any) => {
    setEditingBranch(branch)
    setShowEditBranchModal(true)
  }

  const handleDeleteBranch = (branch: any) => {
    setDeletingBranch(branch)
    setShowDeleteConfirmModal(true)
  }

  const activeBranches = getActiveBranches()
  const inactiveBranches = getInactiveBranches()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Branches</h1>
          <p className="text-muted-foreground">
            Manage your institute branches and locations
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowCreateBranchModal(true)} variant="outline">
            <Plus className="mr-2 h-4 w-4" />
            Quick Add
          </Button>
          <Button onClick={handleCreateWithWizard}>
            <Plus className="mr-2 h-4 w-4" />
            Add Branch (Wizard)
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Branches</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{branches.length}</div>
            <p className="text-xs text-muted-foreground">
              Across all locations
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Branches</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeBranches.length}</div>
            <p className="text-xs text-muted-foreground">
              Currently operational
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Branches</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inactiveBranches.length}</div>
            <p className="text-xs text-muted-foreground">
              Temporarily closed
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search branches..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
          <SelectTrigger className="w-[180px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Branches</SelectItem>
            <SelectItem value="active">Active Only</SelectItem>
            <SelectItem value="inactive">Inactive Only</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Branches Grid */}
      {isLoading ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredBranches.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No branches found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchQuery || statusFilter !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Get started by creating your first branch'}
            </p>
            {!searchQuery && statusFilter === 'all' && (
              <Button onClick={() => setShowCreateBranchModal(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Branch
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredBranches.map((branch) => (
            <Card key={branch.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{branch.name}</CardTitle>
                    <CardDescription className="flex items-center">
                      <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                        {branch.code}
                      </span>
                      {branch.isHeadOffice && (
                        <Badge variant="secondary" className="ml-2">
                          Head Office
                        </Badge>
                      )}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEditBranch(branch)}>
                        Quick Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditWithWizard(branch)}>
                        Edit with Wizard
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => toggleBranchStatus(branch.id)}>
                        {branch.isActive ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDeleteBranch(branch)}
                        className="text-destructive"
                      >
                        Move to Trash
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <Badge variant={branch.isActive ? 'default' : 'secondary'}>
                  {branch.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start space-x-2">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p>{branch.location.address}</p>
                    <p className="text-muted-foreground">
                      {branch.location.district?.name}, {branch.location.state?.name}
                    </p>
                  </div>
                </div>
                {branch.contact?.phone && (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{branch.contact.phone}</span>
                  </div>
                )}
                {branch.contact?.email && (
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{branch.contact.email}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Modals */}
      <CreateBranchModal />
      <EditBranchModal />
      <DeleteBranchModal />

      {/* Wizard Modal */}
      <BranchWizardModal
        isOpen={showWizardModal}
        onClose={handleCloseWizard}
        editingBranch={wizardEditingBranch}
        mode={wizardMode}
      />
    </div>
  )
}
