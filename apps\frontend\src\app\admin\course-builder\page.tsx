'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { BookOpen, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function CourseBuilderPage() {
  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="container mx-auto py-6 px-4 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/admin/courses">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Courses
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Course Builder</h1>
            <p className="text-gray-600 mt-1">
              Build and manage course content
            </p>
          </div>
        </div>

        {/* Coming Soon Card */}
        <Card className="border-gray-200">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <BookOpen className="h-8 w-8 text-blue-600" />
            </div>
            <CardTitle className="text-xl">Course Builder Coming Soon</CardTitle>
            <CardDescription className="text-base">
              The advanced course builder with drag-and-drop functionality, 
              lesson management, and content creation tools is currently under development.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">Planned Features:</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Drag-and-drop lesson organization</li>
                  <li>• Rich content editor with multimedia support</li>
                  <li>• Quiz and assessment builder</li>
                  <li>• Video upload and management</li>
                  <li>• Interactive content elements</li>
                  <li>• Progress tracking and analytics</li>
                </ul>
              </div>
              
              <div className="flex justify-center space-x-3">
                <Link href="/admin/courses">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Manage Courses
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
