'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import { coursesApi } from '@/lib/api/courses'

const courseSchema = Yup.object({
  title: Yup.string().required('Title is required'),
  description: Yup.string(),
})

export default function CreateCoursePage() {
  const router = useRouter()
  const [isSaving, setIsSaving] = useState(false)

  const handleSubmit = async (values: { title: string; description?: string }) => {
    setIsSaving(true)
    try {
      // @ts-ignore
      await coursesApi.createCourse(values)
      toast.success('Course created successfully')
      router.push('/admin/courses')
    } catch (error) {
      toast.error('Failed to create course')
      console.error(error)
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Create New Course</h1>
          <p className="text-muted-foreground">Fill in the details to create a new course</p>
        </div>

        <Formik
          initialValues={{ title: '', description: '' }}
          validationSchema={courseSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched }) => (
            <Form className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Course Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Course Title</Label>
                    <Field
                      as={Input}
                      id="title"
                      name="title"
                      placeholder="e.g., Introduction to Web Development"
                    />
                    {errors.title && touched.title && (
                      <p className="text-sm text-destructive">{errors.title}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Course Description</Label>
                    <Field
                      as={Textarea}
                      id="description"
                      name="description"
                      placeholder="A brief summary of the course content"
                      rows={5}
                    />
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end">
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? 'Creating...' : 'Create Course'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  )
}