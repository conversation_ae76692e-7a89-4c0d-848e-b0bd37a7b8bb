'use client'

import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useInstituteStore } from '@/stores/institute/useInstituteStore'
import { useBranchStore } from '@/stores/institute/useBranchStore'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Users,
  BookOpen,
  Building2,
  DollarSign,
  TrendingUp,
  UserPlus,
  Plus,
  BarChart3,
  Calendar,
  Target,
  AlertTriangle,
  GraduationCap,
  MapPin
} from 'lucide-react'

export default function InstituteAdminDashboard() {
  const { user, logout, isLoading: authLoading, initialize } = useAuthStore()
  const {
    institute,
    branches,
    students,
    instituteStats,
    isLoading,
    error,
    fetchInstituteData,
    fetchBranches,
    fetchStudents,
    fetchInstituteStats,
    clearError
  } = useInstituteStore()

  const { selectedBranch } = useBranchStore()
  const [activeTab, setActiveTab] = useState('overview')

  // Initialize auth on component mount
  useEffect(() => {
    console.log('🚀 Admin dashboard mounting, initializing auth...')
    initialize()
  }, [initialize])

  // Handle authentication check after auth is initialized
  useEffect(() => {
    // Debug: Log user data in admin dashboard
    console.log('=== ADMIN DASHBOARD DEBUG ===')
    console.log('Auth Loading:', authLoading)
    console.log('User object:', user)
    console.log('User email:', user?.email)
    console.log('Legacy Role:', user?.legacyRole)
    console.log('Role object:', user?.role)
    console.log('Institute:', user?.institute)
    console.log('Is Active:', user?.isActive)
    console.log('============================')

    // Don't redirect while auth is still loading
    if (authLoading) {
      console.log('⏳ Auth still loading, waiting...')
      return
    }

    // Redirect if not authenticated or not institute admin
    if (!user || !['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
      console.log('❌ Redirecting to login - User not authorized:', {
        hasUser: !!user,
        legacyRole: user?.legacyRole,
        authLoading
      })
      window.location.href = '/auth/login'
      return
    }

    console.log('✅ User authorized, staying on dashboard')
  }, [user, authLoading])

  useEffect(() => {
    if (user && ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
      fetchInstituteData()
      fetchBranches()

      // Fetch data with branch filtering
      const branchId = selectedBranch?.id
      fetchStudents(branchId)
      fetchInstituteStats(branchId)
    }
  }, [user, selectedBranch, fetchInstituteData, fetchBranches, fetchStudents, fetchInstituteStats])

  // Refetch data when branch selection changes
  useEffect(() => {
    if (user && ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
      const branchId = selectedBranch?.id
      fetchStudents(branchId)
      fetchInstituteStats(branchId)
    }
  }, [selectedBranch])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  // Show loading while auth is initializing or user is not loaded
  if (authLoading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {authLoading ? 'Initializing authentication...' : 'Loading dashboard...'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">Institute Dashboard</h1>
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                {user?.role?.name?.toUpperCase()}
              </Badge>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {user.firstName} {user.lastName}
              </span>
              <Button variant="outline" size="sm" onClick={logout}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Error Alert */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      )}

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Welcome Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Welcome to {institute?.name || 'Your Institute'} Dashboard! 🏫
                </h2>
                <p className="text-gray-600">
                  Manage your institute, branches, students, and courses from this central dashboard.
                </p>
              </div>
              {selectedBranch && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="font-medium text-blue-900">
                        {selectedBranch.name}
                      </div>
                      <div className="text-sm text-blue-700">
                        Viewing branch-specific data
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Institute Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{instituteStats.totalStudents}</div>
                <p className="text-xs text-muted-foreground">
                  <Badge variant="secondary" className="mr-1">
                    {instituteStats.activeStudents}
                  </Badge>
                  active students
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{instituteStats.totalCourses}</div>
                <p className="text-xs text-muted-foreground">
                  <Badge variant="secondary" className="mr-1">
                    {instituteStats.publishedCourses}
                  </Badge>
                  published courses
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Branches</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{instituteStats.totalBranches}</div>
                <p className="text-xs text-muted-foreground">
                  <Badge variant="secondary" className="mr-1">
                    {instituteStats.activeBranches}
                  </Badge>
                  active branches
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(instituteStats.monthlyRevenue)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {instituteStats.enrollmentsThisMonth} enrollments this month
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="students">Students</TabsTrigger>
              <TabsTrigger value="branches">Branches</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>
                    Common tasks and shortcuts for institute management
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Button
                      variant="outline"
                      className="h-20 flex flex-col items-center space-y-2"
                      onClick={() => setActiveTab('students')}
                    >
                      <UserPlus className="h-6 w-6" />
                      <span className="text-sm">Add Student</span>
                    </Button>

                    <Button
                      variant="outline"
                      className="h-20 flex flex-col items-center space-y-2"
                      onClick={() => setActiveTab('branches')}
                    >
                      <Building2 className="h-6 w-6" />
                      <span className="text-sm">Manage Branches</span>
                    </Button>

                    <Button
                      variant="outline"
                      className="h-20 flex flex-col items-center space-y-2"
                    >
                      <BookOpen className="h-6 w-6" />
                      <span className="text-sm">Manage Courses</span>
                    </Button>

                    <Button
                      variant="outline"
                      className="h-20 flex flex-col items-center space-y-2"
                      onClick={() => setActiveTab('analytics')}
                    >
                      <BarChart3 className="h-6 w-6" />
                      <span className="text-sm">View Reports</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recent Students */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Users className="h-5 w-5" />
                      <span>Recent Students</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {students.slice(0, 5).length > 0 ? (
                      <div className="space-y-3">
                        {students.slice(0, 5).map((student) => (
                          <div key={student.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div>
                              <p className="font-medium">{student.firstName} {student.lastName}</p>
                              <p className="text-sm text-gray-500">{student.email}</p>
                            </div>
                            <div className="text-right">
                              <Badge variant={student.status === 'active' ? 'default' : 'secondary'}>
                                {student.status}
                              </Badge>
                              <p className="text-xs text-gray-500 mt-1">
                                {student.enrolledCourses} courses
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-6 text-gray-500">
                        <Users className="h-10 w-10 mx-auto mb-3 text-gray-300" />
                        <p>No students enrolled yet</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Branch Performance */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Building2 className="h-5 w-5" />
                      <span>Branch Performance</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {branches.slice(0, 5).length > 0 ? (
                      <div className="space-y-3">
                        {branches.slice(0, 5).map((branch) => (
                          <div key={branch.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div>
                              <p className="font-medium">{branch.name}</p>
                              <p className="text-sm text-gray-500 flex items-center">
                                <MapPin className="h-3 w-3 mr-1" />
                                {branch.city}, {branch.state}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">{branch.studentsCount} students</p>
                              <p className="text-sm text-gray-500">
                                {formatCurrency(branch.revenue)}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-6 text-gray-500">
                        <Building2 className="h-10 w-10 mx-auto mb-3 text-gray-300" />
                        <p>No branches created yet</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Students Tab */}
            <TabsContent value="students" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Student Management</CardTitle>
                      <CardDescription>
                        Manage student enrollments and track their progress
                      </CardDescription>
                    </div>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Student
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-gray-500">
                    <GraduationCap className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium mb-2">Student Management Coming Soon</h3>
                    <p>Comprehensive student management features are being developed!</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Branches Tab */}
            <TabsContent value="branches" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Branch Management</CardTitle>
                      <CardDescription>
                        Manage your institute branches and their operations
                      </CardDescription>
                    </div>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Branch
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-gray-500">
                    <Building2 className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium mb-2">Branch Management Coming Soon</h3>
                    <p>Advanced branch management features are being developed!</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Analytics Tab */}
            <TabsContent value="analytics" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Enrollment Trends</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-gray-500">
                      <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>Enrollment analytics coming soon...</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Revenue Analytics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-gray-500">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>Revenue analytics coming soon...</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
