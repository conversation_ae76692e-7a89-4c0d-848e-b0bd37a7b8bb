'use client'

import { useState, useEffect } from 'react'
import { useInstituteStore } from '@/stores/institute/useInstituteStore'
import { domainApi } from '@/lib/institute-admin'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  Globe, 
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowLeft,
  Info
} from 'lucide-react'
import { toast } from 'sonner'
import Link from 'next/link'

export default function DomainSettingsPage() {
  const {
    institute,
    isLoading,
    error,
    fetchInstituteData
  } = useInstituteStore()

  const [customDomain, setCustomDomain] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Load institute data
  useEffect(() => {
    fetchInstituteData()
  }, [fetchInstituteData])

  // Set initial domain value
  useEffect(() => {
    if (institute?.customDomain) {
      setCustomDomain(institute.customDomain)
    }
  }, [institute])

  const handleDomainRequest = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!customDomain.trim()) {
      toast.error('Please enter a custom domain')
      return
    }

    // Basic domain validation
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/
    if (!domainRegex.test(customDomain)) {
      toast.error('Please enter a valid domain name (e.g., academy.com)')
      return
    }

    try {
      setIsSubmitting(true)

      // Use the new domain request API
      const response = await domainApi.createRequest({
        domainName: customDomain.toLowerCase().trim(),
        purpose: 'Custom domain for institute website'
      })

      if (response.success) {
        toast.success(response.message || 'Domain request submitted successfully! Waiting for admin approval.')
        await fetchInstituteData(true) // Force refresh
      } else {
        throw new Error('Failed to submit domain request')
      }
    } catch (error) {
      console.error('Failed to request domain:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to submit domain request')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p>Loading domain settings...</p>
        </div>
      </div>
    )
  }

  const hasExistingDomain = institute?.customDomain
  const isDomainVerified = institute?.domainVerified

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/admin/settings">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold flex items-center space-x-2">
            <Globe className="h-6 w-6" />
            <span>Domain Settings</span>
          </h1>
          <p className="text-gray-600">Request a custom domain for your institute</p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Domain Request Card */}
      <Card>
        <CardHeader>
          <CardTitle>Custom Domain Request</CardTitle>
          <CardDescription>
            {hasExistingDomain 
              ? 'Your domain request status' 
              : 'Request a custom domain for your institute (one-time only)'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Information Alert */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Important:</strong> Domain requests can only be submitted once. 
              Please ensure you enter the correct domain name before submitting.
            </AlertDescription>
          </Alert>

          {/* Existing Domain Status */}
          {hasExistingDomain ? (
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">Requested Domain</p>
                    <p className="text-lg font-mono text-blue-600">{institute.customDomain}</p>
                    <p className="text-sm text-gray-500 mt-1">
                      Submitted for admin review
                    </p>
                  </div>
                  <Badge variant={isDomainVerified ? "default" : "secondary"} className="ml-4">
                    {isDomainVerified ? (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Verified & Active
                      </>
                    ) : (
                      <>
                        <Clock className="h-3 w-3 mr-1" />
                        Pending Approval
                      </>
                    )}
                  </Badge>
                </div>
              </div>

              {isDomainVerified ? (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Congratulations!</strong> Your domain has been verified and is now active. 
                    Students can access your institute at <strong>{institute.customDomain}</strong>
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert>
                  <Clock className="h-4 w-4" />
                  <AlertDescription>
                    Your domain request is pending admin approval. You will be notified once it's processed.
                    This usually takes 1-2 business days.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          ) : (
            /* New Domain Request Form */
            <form onSubmit={handleDomainRequest} className="space-y-4">
              <div>
                <Label htmlFor="customDomain">Custom Domain *</Label>
                <Input
                  id="customDomain"
                  value={customDomain}
                  onChange={(e) => setCustomDomain(e.target.value)}
                  placeholder="academy.com"
                  className="font-mono"
                  required
                />
                <p className="text-sm text-gray-500 mt-1">
                  Enter your desired domain name (without http:// or www). Example: academy.com
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Domain Requirements:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Domain must be owned by you</li>
                  <li>• You'll need to configure DNS settings after approval</li>
                  <li>• SSL certificate will be automatically provided</li>
                  <li>• Domain request cannot be changed once submitted</li>
                </ul>
              </div>

              <div className="flex justify-end">
                <Button 
                  type="submit" 
                  disabled={isSubmitting || !customDomain.trim()}
                  className="min-w-[160px]"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Globe className="h-4 w-4 mr-2" />
                      Submit Domain Request
                    </>
                  )}
                </Button>
              </div>
            </form>
          )}

          {/* Help Section */}
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-2">Need Help?</h4>
            <p className="text-sm text-gray-600">
              If you have questions about domain setup or need assistance, 
              please contact our support <NAME_EMAIL>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
