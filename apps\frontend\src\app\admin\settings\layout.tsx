'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { 
  Settings, 
  Building, 
  Globe, 
  Users, 
  CreditCard, 
  BookOpen, 
  Palette,
  Mail,
  Shield,
  Bell,
  FileText,
  BarChart
} from 'lucide-react'

interface SettingsNavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  category: string
}

const settingsNavigation: SettingsNavItem[] = [

   {
    title: 'Institute Settings',
    href: '/admin/settings',
    icon: Globe,
    description: 'Update institute profile and settings',
    category: 'Institute Profile'
  },
   // Domain & Website
  {
    title: 'Domain Settings',
    href: '/admin/settings/domain',
    icon: Globe,
    description: 'Custom domain request and website configuration',
    category: 'Domain & Website'
  },
  {
    title: 'Theme Settings',
    href: '/admin/settings/theme',
    icon: Palette,
    description: 'Choose and customize your institute website theme',
    category: 'Domain & Website'
  },
 

  {
    title: 'Certificate Templates',
    href: '/admin/settings/certificates',
    icon: FileText,
    description: 'Design and manage certificate templates',
    category: 'Course Settings'
  },   
  {
    title: 'Payment Gateways',
    href: '/admin/settings/payment-gateways',
    icon: CreditCard,
    description: 'Configure payment gateways and methods',
    category: 'Billing & Payments'
  },
 
 
  // Communication
  {
    title: 'Email Templates',
    href: '/admin/settings/email-templates',
    icon: Mail,
    description: 'Customize email templates and notifications',
    category: 'Communication'
  },
  {
    title: 'Notifications',
    href: '/admin/settings/notifications',
    icon: Bell,
    description: 'Configure notification preferences',
    category: 'Communication'
  },
  {
    title: 'Announcements',
    href: '/admin/settings/announcements',
    icon: Bell,
    description: 'Manage announcements and messaging',
    category: 'Communication'
  },

  // Security & Privacy
  {
    title: 'Security Settings',
    href: '/admin/settings/security',
    icon: Shield,
    description: 'Two-factor authentication and session management',
    category: 'Security & Privacy'
  },
  {
    title: 'Privacy Settings',
    href: '/admin/settings/privacy',
    icon: Shield,
    description: 'Data privacy and export settings',
    category: 'Security & Privacy'
  }
]

const categories = Array.from(new Set(settingsNavigation.map(item => item.category)))

export default function InstituteAdminSettingsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  const filteredNavigation = selectedCategory
    ? settingsNavigation.filter(item => item.category === selectedCategory)
    : settingsNavigation

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Settings Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-2 mb-6">
            <Settings className="h-6 w-6 text-blue-600" />
            <h1 className="text-xl font-semibold text-gray-900">Institute Settings</h1>
          </div>

          {/* Category Filter */}
          <div className="mb-6">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === null ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(null)}
              >
                All
              </Button>
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="text-xs"
                >
                  {category.split(' ')[0]}
                </Button>
              ))}
            </div>
          </div>

          {/* Navigation Items */}
          <nav>
            {filteredNavigation.map((item) => {
              const isActive = pathname === item.href
              const Icon = item.icon

              return (
                <Link key={item.href} href={item.href}>
                  <Card className={cn(
                    "transition-all duration-200 cursor-pointer hover:shadow-md mb-1",
                    isActive 
                      ? "border-blue-500 bg-blue-50 shadow-sm" 
                      : "border-gray-200 hover:border-gray-300"
                  )}>
                    <CardContent className="p-2">
                      <div className="flex items-start space-x-3">
                        <Icon className={cn(
                          "h-5 w-4 mt-0.5 flex-shrink-0",
                          isActive ? "text-blue-600" : "text-gray-500"
                        )} />
                        <div className="flex-1 min-w-0">
                          <h3 className={cn(
                            "text-sm font-medium",
                            isActive ? "text-blue-900" : "text-gray-900"
                          )}>
                            {item.title}
                          </h3>
                          <p className={cn(
                            "text-xs mt-1",
                            isActive ? "text-blue-700" : "text-gray-500"
                          )}>
                            {item.description}
                          </p>
                          {selectedCategory === null && (
                            <span className={cn(
                              "inline-block text-xs px-2 py-1 rounded-full mt-2",
                              isActive 
                                ? "bg-blue-100 text-blue-800" 
                                : "bg-gray-100 text-gray-600"
                            )}>
                              {item.category}
                            </span>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-8">
          {children}
        </div>
      </div>
    </div>
  )
}
