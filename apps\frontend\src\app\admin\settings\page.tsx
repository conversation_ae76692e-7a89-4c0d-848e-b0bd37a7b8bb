'use client'

import { useState, useEffect } from 'react'
import { useInstituteStore } from '@/stores/institute/useInstituteStore'
import { instituteApi } from '@/lib/institute-admin'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'

import {
  Settings,
  Image,
  AlertTriangle
} from 'lucide-react'
import { toast } from 'sonner'

export default function InstituteSettingsPage() {
  const {
    institute, 
    isLoading, 
    error, 
    fetchInstituteData, 
    updateInstituteProfile 
  } = useInstituteStore()

  // Form states
  const [formData, setFormData] = useState({
    name: '',
    tagline: '',
    email: '',
    phone: '',
    website: '',
    description: ''
  })
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [faviconFile, setFaviconFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [faviconPreview, setFaviconPreview] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Load institute data only if not already available
  useEffect(() => {
    // Only fetch if we don't have institute data
    if (!institute) {
      fetchInstituteData()
    }
  }, [institute, fetchInstituteData])

  // Update form when institute data loads
  useEffect(() => {
    if (institute) {
      setFormData({
        name: institute.name || '',
        tagline: institute.tagline || '',
        email: institute.email || '',
        phone: institute.phone || '',
        website: institute.website || '',
        description: institute.description || ''
      })
      
      // Set logo preview if exists
      if (institute.logo?.url) {
        setLogoPreview(institute.logo.url)
      }
      
      // Set favicon preview if exists
      if (institute.favicon?.url) {
        setFaviconPreview(institute.favicon.url)
      }
    }
  }, [institute])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleFileChange = (type: 'logo' | 'favicon', file: File | null) => {
    if (type === 'logo') {
      setLogoFile(file)
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => setLogoPreview(e.target?.result as string)
        reader.readAsDataURL(file)
      } else {
        setLogoPreview(null)
      }
    } else {
      setFaviconFile(file)
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => setFaviconPreview(e.target?.result as string)
        reader.readAsDataURL(file)
      } else {
        setFaviconPreview(null)
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Create FormData for file uploads
      const submitData = new FormData()
      
      // Add text fields
      Object.entries(formData).forEach(([key, value]) => {
        submitData.append(key, value)
      })
      
      // Add files if selected
      if (logoFile) {
        submitData.append('logo', logoFile)
      }
      if (faviconFile) {
        submitData.append('favicon', faviconFile)
      }

      await updateInstituteProfile(submitData)
      toast.success('Institute settings updated successfully!')

      // Force refresh data after update
      await fetchInstituteData(true)
    } catch (error) {
      console.error('Failed to update institute settings:', error)
      toast.error('Failed to update institute settings')
    } finally {
      setIsSubmitting(false)
    }
  }



  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p>Loading institute settings...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-2">
        <Settings className="h-6 w-6" />
        <h1 className="text-2xl font-bold">Institute Settings</h1>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Institute Information */}
        <Card>
          <CardHeader>
            <CardTitle>Institute Information</CardTitle>
            <CardDescription>
              Update your institute's basic information, logo, and branding
            </CardDescription>
          </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Institute Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Enter institute name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="tagline">Tagline</Label>
                    <Input
                      id="tagline"
                      value={formData.tagline}
                      onChange={(e) => handleInputChange('tagline', e.target.value)}
                      placeholder="Enter institute tagline"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Contact Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Contact Phone</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="+****************"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="website">Website URL</Label>
                  <Input
                    id="website"
                    value={formData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="https://www.institute.com"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Describe your institute..."
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Logo and Branding Section */}
            <Card>
              <CardHeader>
                <CardTitle>Logo & Branding</CardTitle>
                <CardDescription>
                  Upload your institute logo and favicon
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Logo Upload */}
                <div>
                  <Label>Institute Logo</Label>
                  <div className="mt-2 flex items-center space-x-4">
                    <div className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                      {logoPreview ? (
                        <img 
                          src={logoPreview} 
                          alt="Logo preview" 
                          className="w-full h-full object-contain rounded-lg"
                        />
                      ) : (
                        <Image className="h-8 w-8 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1">
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleFileChange('logo', e.target.files?.[0] || null)}
                        className="mb-2"
                      />
                      <p className="text-sm text-gray-500">
                        Recommended: 200x200px, PNG or JPG format
                      </p>
                    </div>
                  </div>
                </div>

                {/* Favicon Upload */}
                <div>
                  <Label>Institute Favicon</Label>
                  <div className="mt-2 flex items-center space-x-4">
                    <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                      {faviconPreview ? (
                        <img 
                          src={faviconPreview} 
                          alt="Favicon preview" 
                          className="w-full h-full object-contain rounded-lg"
                        />
                      ) : (
                        <Image className="h-6 w-6 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1">
                      <Input
                        type="file"
                        accept="image/*,.ico"
                        onChange={(e) => handleFileChange('favicon', e.target.files?.[0] || null)}
                        className="mb-2"
                      />
                      <p className="text-sm text-gray-500">
                        Recommended: 32x32px or 16x16px, ICO, PNG, or SVG format
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="min-w-[120px]"
          >
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </div>
  )
}
