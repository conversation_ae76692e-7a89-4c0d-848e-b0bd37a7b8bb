'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useInstituteGatewayStore } from '@/stores/institute-admin/useInstituteGatewayStore'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  CreditCard, 
  CheckCircle, 
  XCircle, 
  Clock,
  ExternalLink,
  TestTube,
  Trash2,
  Star,
  AlertCircle
} from 'lucide-react'
import { GatewayConfigurationForm } from '@/components/institute-admin/payment-gateways/GatewayConfigurationForm'
import { DeleteConfirmDialog } from '@/components/shared/DeleteConfirmDialog'

export default function PaymentGatewaysPage() {
  const router = useRouter()
  const [authChecked, setAuthChecked] = useState(false)
  const [selectedGateway, setSelectedGateway] = useState(null)
  const [showConfigForm, setShowConfigForm] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [configToDelete, setConfigToDelete] = useState(null)

  const { user, isAuthenticated, isLoading, initialize } = useAuthStore()
  const {
    availableGateways,
    availableGatewaysLoading,
    configuredGateways,
    configuredGatewaysLoading,
    fetchAvailableGateways,
    fetchConfiguredGateways,
    deleteGatewayConfig,
    testGatewayConfig,
    setCurrentGateway
  } = useInstituteGatewayStore()

  // Initialize auth
  useEffect(() => {
    initialize()
  }, [initialize])

  // Check authentication and authorization
  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated || !user) {
        router.push('/auth/login')
        return
      }

      // Check if user has institute admin access
      if (user.legacyRole !== 'institute_admin' && user.legacyRole !== 'branch_manager') {
        router.push('/unauthorized')
        return
      }

      setAuthChecked(true)
    }
  }, [isAuthenticated, isLoading, user, router])

  // Fetch data when authenticated
  useEffect(() => {
    if (authChecked) {
      fetchAvailableGateways()
      fetchConfiguredGateways()
    }
  }, [authChecked, fetchAvailableGateways, fetchConfiguredGateways])

  const handleConfigure = (gateway: any) => {
    setSelectedGateway(gateway)
    setCurrentGateway(gateway)
    setShowConfigForm(true)
  }

  const handleDelete = (config: any) => {
    setConfigToDelete(config)
    setShowDeleteDialog(true)
  }

  const confirmDelete = async () => {
    if (configToDelete) {
      try {
        await deleteGatewayConfig(configToDelete.id)
        setShowDeleteDialog(false)
        setConfigToDelete(null)
      } catch (error) {
        // Error handling is done in the store
      }
    }
  }

  const handleTest = async (config: any) => {
    await testGatewayConfig(config.id)
  }

  const getConfigurationStatus = (gateway: any) => {
    const config = configuredGateways.find(c => c.gateway.id === gateway.id)
    if (!config) return 'not_configured'
    if (!config.isActive) return 'configured_inactive'
    return 'configured_active'
  }

  const getStatusBadge = (status: string, isPrimary: boolean = false) => {
    if (isPrimary) {
      return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Primary</Badge>
    }
    
    switch (status) {
      case 'configured_active':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Active</Badge>
      case 'configured_inactive':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Configured</Badge>
      case 'not_configured':
        return <Badge variant="outline">Not Configured</Badge>
      default:
        return null
    }
  }

  const getStatusIcon = (status: string, isPrimary: boolean = false) => {
    if (isPrimary) {
      return <Star className="h-5 w-5 text-blue-600 fill-current" />
    }
    
    switch (status) {
      case 'configured_active':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'configured_inactive':
        return <Clock className="h-5 w-5 text-yellow-600" />
      case 'not_configured':
        return <XCircle className="h-5 w-5 text-gray-400" />
      default:
        return null
    }
  }

  const getPrimaryGateway = () => {
    return configuredGateways.find(config => config.isPrimary)
  }

  // Show loading while checking auth
  if (isLoading || !authChecked) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-gray-600">Loading...</div>
        </div>
      </div>
    )
  }

  if (availableGatewaysLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-gray-600">Loading payment gateways...</div>
        </div>
      </div>
    )
  }

  const primaryGateway = getPrimaryGateway()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
          Payment Gateway Settings
        </h1>
        <p className="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">
          Configure payment gateways for your institute
        </p>
      </div>

      {/* Primary Gateway Info */}
      {primaryGateway ? (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-900">
              <Star className="h-5 w-5 fill-current" />
              Primary Payment Gateway
            </CardTitle>
            <CardDescription className="text-blue-700">
              This is your default payment gateway for processing transactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              {primaryGateway.gateway.logoUrl ? (
                <img 
                  src={primaryGateway.gateway.logoUrl} 
                  alt={primaryGateway.gateway.name} 
                  className="h-8 w-8 object-contain"
                />
              ) : (
                <CreditCard className="h-8 w-8 text-blue-600" />
              )}
              <div>
                <div className="font-medium text-blue-900">{primaryGateway.gateway.name}</div>
                <div className="text-sm text-blue-700">
                  Status: {primaryGateway.isActive ? 'Active' : 'Inactive'} | 
                  Mode: {primaryGateway.testMode ? 'Test' : 'Live'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-900">
              <AlertCircle className="h-5 w-5" />
              No Primary Gateway Set
            </CardTitle>
            <CardDescription className="text-orange-700">
              You haven't configured a primary payment gateway yet. Configure one below to start accepting payments.
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* Available Gateways */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Available Payment Gateways</h2>
        
        {availableGateways.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Payment Gateways Available</h3>
              <p className="text-gray-600">
                No payment gateways have been set up by the platform administrator yet.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableGateways.map((gateway) => {
              const status = getConfigurationStatus(gateway)
              const config = configuredGateways.find(c => c.gateway.id === gateway.id)
              const isPrimary = config?.isPrimary || false

              return (
                <Card key={gateway.id} className={`relative ${isPrimary ? 'ring-2 ring-blue-500' : ''}`}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        {gateway.logoUrl ? (
                          <img 
                            src={gateway.logoUrl} 
                            alt={gateway.name} 
                            className="h-8 w-8 object-contain"
                          />
                        ) : (
                          <CreditCard className="h-8 w-8 text-gray-400" />
                        )}
                        <div>
                          <CardTitle className="text-lg">{gateway.name}</CardTitle>
                          <div className="flex gap-1 mt-1">
                            {gateway.isFeatured && (
                              <Badge variant="secondary" className="text-xs">Featured</Badge>
                            )}
                            {getStatusBadge(status, isPrimary)}
                          </div>
                        </div>
                      </div>
                      {getStatusIcon(status, isPrimary)}
                    </div>
                    
                    {gateway.description && (
                      <CardDescription className="text-sm">
                        {gateway.description}
                      </CardDescription>
                    )}
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Supported Features */}
                    <div className="space-y-2">
                      <div>
                        <span className="text-sm font-medium">Currencies:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {gateway.supportedCurrencies.slice(0, 3).map((currency) => (
                            <Badge key={currency} variant="outline" className="text-xs">
                              {currency}
                            </Badge>
                          ))}
                          {gateway.supportedCurrencies.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{gateway.supportedCurrencies.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Configuration Details */}
                    {config && (
                      <div className="space-y-2 pt-2 border-t">
                        <div className="flex items-center justify-between text-sm">
                          <span>Test Mode</span>
                          <Badge variant={config.testMode ? "secondary" : "outline"}>
                            {config.testMode ? "Enabled" : "Disabled"}
                          </Badge>
                        </div>
                        {config.lastTestedAt && (
                          <div className="text-xs text-muted-foreground">
                            Last tested: {new Date(config.lastTestedAt).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button
                        onClick={() => handleConfigure(gateway)}
                        size="sm"
                        className="flex-1"
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        {status === 'not_configured' ? 'Configure' : 'Edit'}
                      </Button>

                      {config && (
                        <>
                          <Button
                            onClick={() => handleTest(config)}
                            size="sm"
                            variant="outline"
                          >
                            <TestTube className="h-4 w-4" />
                          </Button>
                          <Button
                            onClick={() => handleDelete(config)}
                            size="sm"
                            variant="outline"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}

                      {gateway.documentationUrl && (
                        <Button
                          size="sm"
                          variant="ghost"
                          asChild
                        >
                          <a 
                            href={gateway.documentationUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>

      {/* Configuration Form */}
      <GatewayConfigurationForm
        isOpen={showConfigForm}
        onClose={() => {
          setShowConfigForm(false)
          setSelectedGateway(null)
          fetchConfiguredGateways() // Refresh the list
        }}
        gateway={selectedGateway}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => {
          setShowDeleteDialog(false)
          setConfigToDelete(null)
        }}
        onConfirm={confirmDelete}
        title="Delete Gateway Configuration"
        description={`Are you sure you want to delete the configuration for ${configToDelete?.gateway?.name}? This action cannot be undone.`}
      />
    </div>
  )
}
