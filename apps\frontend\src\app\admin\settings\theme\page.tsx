'use client'

import { ThemeSelector } from '@/components/institute-admin/themes/ThemeSelector'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Info, Palette } from 'lucide-react'

export default function ThemeSettingsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-primary/10 rounded-lg">
          <Palette className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl font-bold">Theme Settings</h1>
          <p className="text-muted-foreground">
            Customize the appearance of your institute website
          </p>
        </div>
      </div>

      {/* Information Card */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>About Themes:</strong> Themes control the visual appearance of your institute's public website. 
          Students will see your selected theme when they visit your custom domain. The course marketplace 
          maintains a consistent layout across all themes while adapting to your chosen colors and fonts.
        </AlertDescription>
      </Alert>

      {/* Theme Benefits */}
      <Card>
        <CardHeader>
          <CardTitle>Theme Features</CardTitle>
          <CardDescription>
            All institute themes include these features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Palette className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <h4 className="font-semibold">Custom Branding</h4>
                <p className="text-sm text-muted-foreground">
                  Your logo, colors, and fonts throughout the website
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Palette className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-semibold">Responsive Design</h4>
                <p className="text-sm text-muted-foreground">
                  Optimized for desktop, tablet, and mobile devices
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Palette className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <h4 className="font-semibold">Course Marketplace</h4>
                <p className="text-sm text-muted-foreground">
                  Integrated course browsing and purchase system
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Palette className="h-4 w-4 text-orange-600" />
              </div>
              <div>
                <h4 className="font-semibold">SEO Optimized</h4>
                <p className="text-sm text-muted-foreground">
                  Built-in SEO features for better search rankings
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <Palette className="h-4 w-4 text-red-600" />
              </div>
              <div>
                <h4 className="font-semibold">Fast Loading</h4>
                <p className="text-sm text-muted-foreground">
                  Optimized for speed and performance
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="p-2 bg-teal-100 rounded-lg">
                <Palette className="h-4 w-4 text-teal-600" />
              </div>
              <div>
                <h4 className="font-semibold">Easy Customization</h4>
                <p className="text-sm text-muted-foreground">
                  Customize colors, fonts, and content easily
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Theme Selector */}
      <ThemeSelector />
    </div>
  )
}
