'use client'

import React, { useState } from 'react'
import { useStaffStore, type StaffMember } from '@/stores/institute-admin/useStaffStore'
import StaffList from '@/components/institute-admin/staff-management/StaffList'
import StaffCreateForm from '@/components/institute-admin/staff-management/StaffCreateForm'
import StaffEditForm from '@/components/institute-admin/staff-management/StaffEditForm'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ArrowLeft } from 'lucide-react'

type ViewMode = 'list' | 'create' | 'edit'

export default function StaffManagementPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null)
  const [showDialog, setShowDialog] = useState(false)

  const handleCreateStaff = () => {
    setViewMode('create')
    setShowDialog(true)
  }

  const handleEditStaff = (staff: StaffMember) => {
    setSelectedStaff(staff)
    setViewMode('edit')
    setShowDialog(true)
  }

  const handleFormSuccess = () => {
    setShowDialog(false)
    setViewMode('list')
    setSelectedStaff(null)
  }

  const handleFormCancel = () => {
    setShowDialog(false)
    setViewMode('list')
    setSelectedStaff(null)
  }

  const getDialogTitle = () => {
    switch (viewMode) {
      case 'create':
        return 'Add New Staff Member'
      case 'edit':
        return `Edit ${selectedStaff?.firstName} ${selectedStaff?.lastName}`
      default:
        return ''
    }
  }

  return (
    <div className="container mx-auto py-6 px-4">
      {/* Main Content */}
      <StaffList 
        onCreateStaff={handleCreateStaff}
        onEditStaff={handleEditStaff}
      />

      {/* Form Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{getDialogTitle()}</DialogTitle>
          </DialogHeader>
          
          {viewMode === 'create' && (
            <StaffCreateForm
              onSuccess={handleFormSuccess}
              onCancel={handleFormCancel}
            />
          )}
          
          {viewMode === 'edit' && selectedStaff && (
            <StaffEditForm
              staff={selectedStaff}
              onSuccess={handleFormSuccess}
              onCancel={handleFormCancel}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
