import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const headersList = await headers()

    // Get cache data from headers (set by enhanced middleware)
    const cacheDataHeader = headersList.get('x-institute-cache-data')
    const cacheDomain = headersList.get('x-cache-domain')
    const domainError = headersList.get('x-domain-error')

    console.log('📋 Headers API called')
    console.log('📦 Cache data header:', cacheDataHeader ? 'Present' : 'Missing')
    console.log('🌐 Cache domain:', cacheDomain)
    console.log('❌ Domain error:', domainError)

    if (domainError) {
      return NextResponse.json({
        success: false,
        error: domainError,
        cacheData: null
      })
    }

    if (cacheDataHeader) {
      try {
        const cacheData = JSON.parse(cacheDataHeader)
        console.log('✅ Parsed cache data:', {
          institute: cacheData.institute?.name,
          theme: cacheData.theme?.name,
          domain: cacheData.domain,
          timestamp: new Date(cacheData.timestamp).toISOString()
        })

        return NextResponse.json({
          success: true,
          cacheData,
          domain: cacheDomain
        })
      } catch (error) {
        console.error('❌ Error parsing cache data:', error)
      }
    }

    return NextResponse.json({
      success: false,
      error: 'No cache data available',
      cacheData: null
    })

  } catch (error) {
    console.error('❌ Headers API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to get header data',
      cacheData: null
    }, { status: 500 })
  }
}
