'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { showToast } from '@/lib/toast'

export default function SuperAdminLoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const { login, isLoading, user, isAuthenticated, initialize } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    // Check if user is already logged in
    initialize()
  }, [initialize])

  useEffect(() => {
    // If already authenticated as super admin, redirect to dashboard
    if (!isLoading && isAuthenticated && user && (user.legacyRole === 'super_admin' || user.legacyRole === 'platform_staff')) {
      router.push('/super-admin')
    }
  }, [isAuthenticated, user, isLoading, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email || !password) {
      showToast.error('Please fill in all fields')
      return
    }

    try {
      await login(email, password, 'super_admin')
      showToast.loginSuccess('Login successful! Redirecting...')

      // Small delay to ensure auth state is persisted before navigation
      setTimeout(() => {
        router.push('/super-admin')
      }, 100)
    } catch (error) {
      showToast.loginError((error as Error).message)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-blue-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6 text-blue-900">Super Admin Login</h1>
        <p className="text-center text-gray-600 mb-4">
          Access the platform administration panel
        </p>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter your password"
              required
            />
          </div>
          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isLoading ? 'Signing in...' : 'Sign In as Super Admin'}
          </button>
        </form>
        <div className="mt-6 text-center text-xs text-gray-500">
          <p>Route: /auth/admin/login (✅ Correct as per documentation)</p>
        </div>
      </div>
    </div>
  )
}
