'use client'

import { useState, useEffect } from 'react'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { showToast } from '@/lib/toast'

export default function InstituteAdminLoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const { login, isLoading, user, isAuthenticated, initialize } = useAuthStore()

  // Check if user is already logged in and redirect accordingly
  useEffect(() => {
    // Initialize auth state
    initialize()
  }, [initialize])

  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('🔄 User already logged in, redirecting...', {
        email: user.email,
        legacyRole: user.legacyRole
      })

      // Redirect based on user role
      if (user.legacyRole === 'super_admin') {
        window.location.href = '/super-admin'
      } else if (['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
        window.location.href = '/admin'
      } else if (user.legacyRole === 'student') {
        window.location.href = '/student/dashboard'
      } else {
        // Default redirect for unknown roles
        window.location.href = '/admin'
      }
    }
  }, [isAuthenticated, user])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email || !password) {
      showToast.error('Please fill in all fields')
      return
    }

    try {
      await login(email, password, 'institute_admin')
      showToast.loginSuccess('Redirecting to dashboard...')

      // Redirect to institute admin dashboard
      setTimeout(() => {
        window.location.href = '/admin'
      }, 1000)
    } catch (error) {
      showToast.loginError((error as Error).message)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-purple-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6 text-purple-900">Institute Admin Login</h1>
        <p className="text-center text-gray-600 mb-4">
          Access your institute management panel
        </p>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
              placeholder="Enter your password"
              required
            />
          </div>
          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
          >
            {isLoading ? 'Signing in...' : 'Sign In'}
          </button>
        </form>
        <div className="mt-6 text-center">
          <a href="/auth/register" className="text-purple-600 hover:text-purple-500">
            Don't have an account? Register here
          </a>
        </div>
        <div className="mt-2 text-center text-xs text-gray-500">
          <p>Route: /auth/login (✅ Correct as per documentation)</p>
        </div>
      </div>
    </div>
  )
}
