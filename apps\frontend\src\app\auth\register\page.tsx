'use client'

import { useState } from 'react'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { showToast } from '@/lib/toast'

export default function InstituteRegistrationPage() {
  const [formData, setFormData] = useState({
    // Institute Information
    instituteName: '',
    slug: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    country: 'India',
    pincode: '',

    // Admin Information
    firstName: '',
    lastName: '',
    adminEmail: '',
    password: '',
    confirmPassword: ''
  })

  const { register, isLoading } = useAuthStore()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    if (!formData.instituteName || !formData.email || !formData.firstName || !formData.lastName || !formData.adminEmail || !formData.password) {
      showToast.error('Please fill in all required fields')
      return
    }

    if (formData.password !== formData.confirmPassword) {
      showToast.error('Passwords do not match')
      return
    }

    if (formData.password.length < 6) {
      showToast.error('Password must be at least 6 characters long')
      return
    }

    try {
      // Generate slug if not provided
      const slug = formData.slug || formData.instituteName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')

      await register({
        // Institute data
        instituteName: formData.instituteName,
        slug: slug,
        email: formData.email, // Institute email
        phone: formData.phone, // Institute phone
        city: formData.city,
        state: formData.state,
        country: formData.country,

        // Admin user data
        adminFirstName: formData.firstName,
        adminLastName: formData.lastName,
        adminEmail: formData.adminEmail,
        adminPhone: formData.phone, // Can use same phone or add separate field
        password: formData.password
      }, 'institute')

      showToast.registerSuccess('Institute registration submitted successfully! Please wait for admin approval.')

      // Redirect to login page after delay
      setTimeout(() => {
        window.location.href = '/auth/login'
      }, 3000)
    } catch (error) {
      showToast.registerError((error as Error).message)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-purple-50 py-12">
      <div className="max-w-2xl w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6 text-purple-900">Institute Registration</h1>
        <p className="text-center text-gray-600 mb-6">
          Register your institute to start managing courses and students
        </p>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Institute Information */}
          <div className="border-b pb-6">
            <h2 className="text-lg font-semibold mb-4">Institute Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Institute Name *</label>
                <input
                  type="text"
                  name="instituteName"
                  value={formData.instituteName}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Demo Institute"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Slug (URL)</label>
                <input
                  type="text"
                  name="slug"
                  value={formData.slug}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="demo-institute"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Institute Email *</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Phone</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="+91 9876543210"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700">Address</label>
                <input
                  type="text"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Street address"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">City</label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="City"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">State</label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="State"
                />
              </div>
            </div>
          </div>

          {/* Admin Information */}
          <div>
            <h2 className="text-lg font-semibold mb-4">Admin Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">First Name *</label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="John"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Last Name *</label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Doe"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Admin Email *</label>
                <input
                  type="email"
                  name="adminEmail"
                  value={formData.adminEmail}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Password *</label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Enter password"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Confirm Password *</label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Confirm password"
                  required
                />
              </div>
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
          >
            {isLoading ? 'Registering...' : 'Register Institute'}
          </button>
        </form>
        
        <div className="mt-6 text-center">
          <a href="/auth/login" className="text-purple-600 hover:text-purple-500">
            Already have an account? Sign in here
          </a>
        </div>
        <div className="mt-2 text-center text-xs text-gray-500">
          <p>Route: /auth/register (✅ Correct as per documentation)</p>
        </div>
      </div>
    </div>
  )
}
