'use client'

import { useState, useEffect } from 'react'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { showToast } from '@/lib/toast'

export default function StudentLoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const { login, isLoading, user, isAuthenticated, initialize } = useAuthStore()

  // Check if user is already logged in and redirect accordingly
  useEffect(() => {
    // Initialize auth state
    initialize()
  }, [initialize])

  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('🔄 User already logged in, redirecting...', {
        email: user.email,
        legacyRole: user.legacyRole
      })

      // Redirect based on user role
      if (user.legacyRole === 'super_admin') {
        window.location.href = '/super-admin'
      } else if (['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)) {
        window.location.href = '/admin'
      } else if (user.legacyRole === 'student') {
        window.location.href = '/student/dashboard'
      } else {
        // Default redirect for unknown roles
        window.location.href = '/student/dashboard'
      }
    }
  }, [isAuthenticated, user])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email || !password) {
      showToast.error('Please fill in all fields')
      return
    }

    try {
      await login(email, password, 'student')
      showToast.loginSuccess('Redirecting to dashboard...')
      
      setTimeout(() => {
        window.location.href = '/student/dashboard'
      }, 1000)
    } catch (error) {
      showToast.loginError((error as Error).message)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-green-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6 text-green-900">Student Login</h1>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input 
              type="email" 
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <input 
              type="password" 
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              placeholder="Enter your password"
              required
            />
          </div>
          <button 
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
          >
            {isLoading ? 'Signing in...' : 'Sign In'}
          </button>
        </form>
        <div className="mt-6 text-center">
          <a href="/auth/user-register" className="text-green-600 hover:text-green-500">
            Don't have an account? Register here
          </a>
        </div>
      </div>
    </div>
  )
}