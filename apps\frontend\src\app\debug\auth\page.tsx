'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { usePermissions } from '@/contexts/PermissionContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

export default function AuthDebugPage() {
  const { user, isAuthenticated, isLoading, initialize, login } = useAuthStore()
  const { userPermissions } = usePermissions()

  useEffect(() => {
    console.log('🔄 AuthDebugPage mounted, initializing auth...')
    initialize()
  }, [initialize])

  const testLogin = async () => {
    try {
      console.log('🔄 Testing login...')
      await login('<EMAIL>', '123456', 'super_admin')
      console.log('✅ Login successful')
    } catch (error) {
      console.error('❌ Login failed:', error)
    }
  }

  const clearAllData = () => {
    localStorage.clear()
    window.location.reload()
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🔍 Authentication Debug Page</CardTitle>
          <CardDescription>
            Debug information for authentication and permission system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Auth Store State */}
          <div>
            <h3 className="text-lg font-semibold mb-3">🏪 Auth Store State</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <strong>Is Loading:</strong>
                <Badge variant={isLoading ? "secondary" : "default"}>
                  {isLoading ? 'Loading...' : 'Ready'}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <strong>Is Authenticated:</strong>
                <Badge variant={isAuthenticated ? "default" : "destructive"}>
                  {isAuthenticated ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <strong>Has User:</strong>
                <Badge variant={user ? "default" : "destructive"}>
                  {user ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <strong>User Email:</strong>
                <span>{user?.email || 'N/A'}</span>
              </div>
              <div className="flex items-center gap-2">
                <strong>Legacy Role:</strong>
                <Badge variant={user?.legacyRole === 'super_admin' ? "default" : "secondary"}>
                  {user?.legacyRole || 'N/A'}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <strong>Role Name:</strong>
                <span>{user?.role?.name || 'N/A'}</span>
              </div>
            </div>
          </div>

          {/* Permission Context State */}
          <div>
            <h3 className="text-lg font-semibold mb-3">🔐 Permission Context State</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <strong>Processed Role:</strong>
                <Badge variant={userPermissions.role === 'super_admin' ? "default" : "secondary"}>
                  {userPermissions.role}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <strong>Permission Count:</strong>
                <Badge variant={userPermissions.permissions.length > 0 ? "default" : "destructive"}>
                  {userPermissions.permissions.length}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <strong>Permission Level:</strong>
                <span>{userPermissions.level}</span>
              </div>
              <div className="flex items-center gap-2">
                <strong>Department:</strong>
                <span>{userPermissions.department || 'N/A'}</span>
              </div>
            </div>
          </div>

          {/* localStorage State */}
          <div>
            <h3 className="text-lg font-semibold mb-3">💾 localStorage State</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <strong>Auth Token:</strong>
                <Badge variant={localStorage.getItem('auth_token') ? "default" : "destructive"}>
                  {localStorage.getItem('auth_token') ? 'Present' : 'Missing'}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <strong>User Data:</strong>
                <Badge variant={localStorage.getItem('user_data') ? "default" : "destructive"}>
                  {localStorage.getItem('user_data') ? 'Present' : 'Missing'}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <strong>User Permissions:</strong>
                <Badge variant={localStorage.getItem('user_permissions') ? "default" : "destructive"}>
                  {localStorage.getItem('user_permissions') ? 'Present' : 'Missing'}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <strong>Auth Storage:</strong>
                <Badge variant={localStorage.getItem('auth-storage') ? "default" : "destructive"}>
                  {localStorage.getItem('auth-storage') ? 'Present' : 'Missing'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Raw Data */}
          <div>
            <h3 className="text-lg font-semibold mb-3">📄 Raw Data</h3>
            <div className="space-y-4">
              <div>
                <strong>User Object:</strong>
                <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>
              <div>
                <strong>User Permissions (first 5):</strong>
                <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                  {JSON.stringify(userPermissions.permissions.slice(0, 5), null, 2)}
                </pre>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div>
            <h3 className="text-lg font-semibold mb-3">🎮 Actions</h3>
            <div className="flex gap-4">
              <Button onClick={testLogin} variant="default">
                Test Login
              </Button>
              <Button onClick={() => initialize()} variant="outline">
                Re-initialize Auth
              </Button>
              <Button onClick={clearAllData} variant="destructive">
                Clear All Data
              </Button>
            </div>
          </div>

          {/* Navigation Test */}
          <div>
            <h3 className="text-lg font-semibold mb-3">🧭 Navigation Test</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <strong>Can access role-permissions:</strong>
                <Badge variant={
                  isAuthenticated && user && 
                  (user.legacyRole === 'super_admin' || user.legacyRole === 'platform_staff') 
                    ? "default" : "destructive"
                }>
                  {isAuthenticated && user && 
                   (user.legacyRole === 'super_admin' || user.legacyRole === 'platform_staff') 
                     ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="text-sm text-gray-600">
                Condition: isAuthenticated={isAuthenticated ? 'true' : 'false'} && 
                hasUser={user ? 'true' : 'false'} && 
                legacyRole="{user?.legacyRole || 'none'}"
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
