'use client'

import { useState } from 'react'
import { Header } from '@/components/layout/Header'
import { EnhancedHeader } from '@/components/layout/EnhancedHeader'
import { HeaderBarDemo } from '@/components/layout/demo/HeaderBarDemo'
import { SuperAdminLayout } from '@/components/layout/SuperAdminLayout'
import { ResponsiveCard } from '@/components/shared/layout/ResponsiveContainer'
import { 
  Monitor,
  Smartphone,
  Tablet,
  Settings,
  Palette,
  Search,
  Bell,
  User,
  Navigation
} from 'lucide-react'

export default function HeaderBarDemoPage() {
  const [activeDemo, setActiveDemo] = useState<'standard' | 'enhanced' | 'overview'>('overview')
  const [headerConfig, setHeaderConfig] = useState({
    showBreadcrumbs: true,
    showSearch: true,
    showNotifications: true,
    showProfile: true,
    showThemeToggle: true,
    showFullscreenToggle: true
  })

  const demoOptions = [
    {
      id: 'overview',
      label: 'Overview & Features',
      description: 'Comprehensive overview of header capabilities',
      icon: Monitor,
      color: 'bg-blue-600'
    },
    {
      id: 'standard',
      label: 'Standard Header',
      description: 'Default header implementation',
      icon: Navigation,
      color: 'bg-green-600'
    },
    {
      id: 'enhanced',
      label: 'Enhanced Header',
      description: 'Advanced header with additional features',
      icon: Settings,
      color: 'bg-purple-600'
    }
  ]

  const renderDemoContent = () => {
    switch (activeDemo) {
      case 'standard':
        return (
          <div className="space-y-6">
            <ResponsiveCard>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Standard Header</h3>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <Header userType="super_admin" />
                <div className="p-4 bg-gray-50">
                  <p className="text-sm text-gray-600">
                    This is the standard header implementation with core features:
                    navigation toggle, breadcrumbs, search, notifications, and user profile.
                  </p>
                </div>
              </div>
            </ResponsiveCard>
          </div>
        )
      
      case 'enhanced':
        return (
          <div className="space-y-6">
            <ResponsiveCard>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Enhanced Header Configuration</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                {Object.entries(headerConfig).map(([key, value]) => (
                  <label key={key} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => setHeaderConfig(prev => ({
                        ...prev,
                        [key]: e.target.checked
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </span>
                  </label>
                ))}
              </div>
              
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <EnhancedHeader 
                  userType="super_admin"
                  {...headerConfig}
                />
                <div className="p-4 bg-gray-50">
                  <p className="text-sm text-gray-600">
                    Enhanced header with configurable features including theme toggle, 
                    fullscreen mode, current time display, and advanced interactions.
                  </p>
                </div>
              </div>
            </ResponsiveCard>
          </div>
        )
      
      default:
        return <HeaderBarDemo />
    }
  }

  return (
    <SuperAdminLayout>
      <div className="space-y-6">
        {/* Demo Navigation */}
        <ResponsiveCard>
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Header Bar Component Demo
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {demoOptions.map((option) => (
              <button
                key={option.id}
                onClick={() => setActiveDemo(option.id as any)}
                className={`p-4 rounded-lg border-2 text-left transition-colors ${
                  activeDemo === option.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-3 mb-2">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${option.color}`}>
                    <option.icon className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{option.label}</div>
                    {activeDemo === option.id && (
                      <div className="text-xs text-blue-600">Active</div>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-600">{option.description}</p>
              </button>
            ))}
          </div>
        </ResponsiveCard>

        {/* Demo Content */}
        {renderDemoContent()}

        {/* Header Component Summary */}
        <ResponsiveCard>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Header Bar Component Summary
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Navigation className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Navigation</div>
                <div className="text-xs text-gray-600">Menu & breadcrumbs</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                <Search className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Search</div>
                <div className="text-xs text-gray-600">Global search</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
              <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <Bell className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Notifications</div>
                <div className="text-xs text-gray-600">Real-time alerts</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
              <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Profile</div>
                <div className="text-xs text-gray-600">User management</div>
              </div>
            </div>
          </div>
          
          <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Key Features:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
              <div>• Responsive design for all device types</div>
              <div>• Automatic breadcrumb generation</div>
              <div>• Intelligent search with keyboard shortcuts</div>
              <div>• Real-time notification system</div>
              <div>• User profile management</div>
              <div>• Theme and fullscreen toggles</div>
              <div>• Mobile-optimized interactions</div>
              <div>• Accessibility compliance (WCAG)</div>
            </div>
          </div>
        </ResponsiveCard>
      </div>
    </SuperAdminLayout>
  )
}
