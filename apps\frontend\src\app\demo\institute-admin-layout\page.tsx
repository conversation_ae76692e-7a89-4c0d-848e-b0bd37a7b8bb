'use client'

import { InstituteAdminLayout } from '@/components/layout/InstituteAdminLayout'
import { InstituteAdminLayoutDemo } from '@/components/institute/demo/InstituteAdminLayoutDemo'
import { InstituteAdminDashboard } from '@/components/institute/dashboard/InstituteAdminDashboard'
import { CourseManagement } from '@/components/institute/courses/CourseManagement'
import { useState } from 'react'
import { 
  LayoutDashboard,
  BookOpen,
  GraduationCap,
  UserCheck,
  Video,
  FileText,
  CreditCard,
  ShoppingBag,
  Settings,
  Monitor
} from 'lucide-react'

export default function InstituteAdminLayoutPage() {
  const [activeDemo, setActiveDemo] = useState<'layout' | 'dashboard' | 'courses'>('layout')

  const demoOptions = [
    {
      id: 'layout',
      label: 'Layout Overview',
      description: 'Explore the complete Institute Admin Layout structure',
      icon: Monitor,
      color: 'bg-blue-600'
    },
    {
      id: 'dashboard',
      label: 'Dashboard Demo',
      description: 'See the Institute Admin Dashboard in action',
      icon: LayoutDashboard,
      color: 'bg-green-600'
    },
    {
      id: 'courses',
      label: 'Course Management',
      description: 'Experience the course management interface',
      icon: BookOpen,
      color: 'bg-purple-600'
    }
  ]

  const renderDemoContent = () => {
    switch (activeDemo) {
      case 'dashboard':
        return <InstituteAdminDashboard />
      case 'courses':
        return <CourseManagement />
      default:
        return <InstituteAdminLayoutDemo />
    }
  }

  return (
    <InstituteAdminLayout>
      <div className="space-y-6">
        {/* Demo Navigation */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Institute Admin Layout Demo
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {demoOptions.map((option) => (
              <button
                key={option.id}
                onClick={() => setActiveDemo(option.id as any)}
                className={`p-4 rounded-lg border-2 text-left transition-colors ${
                  activeDemo === option.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-3 mb-2">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${option.color}`}>
                    <option.icon className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{option.label}</div>
                    {activeDemo === option.id && (
                      <div className="text-xs text-blue-600">Active</div>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-600">{option.description}</p>
              </button>
            ))}
          </div>
        </div>

        {/* Demo Content */}
        {renderDemoContent()}

        {/* Layout Features Summary */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Institute Admin Layout Features
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <LayoutDashboard className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Dashboard</div>
                <div className="text-xs text-gray-600">Institute overview</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                <BookOpen className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Courses</div>
                <div className="text-xs text-gray-600">Course management</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <GraduationCap className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Students</div>
                <div className="text-xs text-gray-600">Student management</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                <UserCheck className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Instructors</div>
                <div className="text-xs text-gray-600">Instructor management</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                <Video className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Live Classes</div>
                <div className="text-xs text-gray-600">Virtual classrooms</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
                <FileText className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Exams</div>
                <div className="text-xs text-gray-600">Assessments & tests</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-pink-600 rounded-lg flex items-center justify-center">
                <CreditCard className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Billing</div>
                <div className="text-xs text-gray-600">Payments & revenue</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-teal-600 rounded-lg flex items-center justify-center">
                <ShoppingBag className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-medium text-gray-900">Marketplace</div>
                <div className="text-xs text-gray-600">Course sales</div>
              </div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-white rounded-lg border border-blue-200">
            <h4 className="font-medium text-gray-900 mb-2">Key Capabilities:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
              <div>• Comprehensive course and curriculum management</div>
              <div>• Student enrollment and progress tracking</div>
              <div>• Instructor scheduling and performance metrics</div>
              <div>• Live class management with virtual rooms</div>
              <div>• Exam creation and automated grading</div>
              <div>• Revenue tracking and financial reporting</div>
              <div>• Course marketplace with sales analytics</div>
              <div>• Mobile-responsive design for all devices</div>
            </div>
          </div>
        </div>
      </div>
    </InstituteAdminLayout>
  )
}
