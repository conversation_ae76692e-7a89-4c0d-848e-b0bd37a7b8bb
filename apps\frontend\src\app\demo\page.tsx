'use client'

import React from 'react'
import Link from 'next/link'
import { ExternalLink, Users, GraduationCap, Settings } from 'lucide-react'

export default function DemoPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Groups Exam LMS Demo
          </h1>
          <p className="text-xl text-gray-600">
            Explore different parts of our comprehensive Learning Management System
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Platform Landing Page */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                  <Settings className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Platform Landing Page</h3>
                  <p className="text-sm text-gray-600">groups-exam.com</p>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                The main marketing page for institute owners to learn about and sign up for the LMS platform.
              </p>
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Hero section with value proposition
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Features showcase
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Pricing and CTA buttons
                </div>
              </div>
              <Link
                href="/"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                View Platform Landing
                <ExternalLink className="h-4 w-4 ml-2" />
              </Link>
            </div>
          </div>

          {/* Institute Marketplace */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                  <GraduationCap className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Institute Marketplace</h3>
                  <p className="text-sm text-gray-600">abc-institute.com</p>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                Amazon-style course marketplace where students can browse, search, and purchase courses.
              </p>
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Course catalog with search & filters
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Shopping cart & wishlist
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Course comparison tool
                </div>
              </div>
              <Link
                href="/courses"
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
              >
                View Course Marketplace
                <ExternalLink className="h-4 w-4 ml-2" />
              </Link>
            </div>
          </div>

          {/* Authentication Pages */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Authentication System</h3>
                  <p className="text-sm text-gray-600">Login & Registration</p>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                Separate authentication flows for different user types with role-based access control.
              </p>
              <div className="space-y-3">
                <Link
                  href="/auth/admin/login"
                  className="block w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-purple-600 hover:text-purple-600 transition-colors duration-200 text-center"
                >
                  Super Admin Login
                </Link>
                <Link
                  href="/auth/register"
                  className="block w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-purple-600 hover:text-purple-600 transition-colors duration-200 text-center"
                >
                  Institute Registration
                </Link>
                <Link
                  href="/auth/login"
                  className="block w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-purple-600 hover:text-purple-600 transition-colors duration-200 text-center"
                >
                  Institute Admin Login
                </Link>
                <Link
                  href="/auth/user-login"
                  className="block w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-purple-600 hover:text-purple-600 transition-colors duration-200 text-center"
                >
                  Student Login
                </Link>
              </div>
            </div>
          </div>

          {/* Admin Dashboards */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                  <Settings className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Admin Dashboards</h3>
                  <p className="text-sm text-gray-600">Management Interfaces</p>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                Comprehensive admin interfaces for platform and institute management.
              </p>
              <div className="space-y-3">
                <Link
                  href="/super-admin"
                  className="block w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-orange-600 hover:text-orange-600 transition-colors duration-200 text-center"
                >
                  Super Admin Dashboard
                </Link>
                <Link
                  href="/admin"
                  className="block w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-orange-600 hover:text-orange-600 transition-colors duration-200 text-center"
                >
                  Institute Admin Dashboard
                </Link>
                <Link
                  href="/student/dashboard"
                  className="block w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-orange-600 hover:text-orange-600 transition-colors duration-200 text-center"
                >
                  Student Dashboard
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Features */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/cart"
              className="flex items-center p-3 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors duration-200"
            >
              <span className="text-2xl mr-3">🛒</span>
              <span className="font-medium">Shopping Cart</span>
            </Link>
            <Link
              href="/wishlist"
              className="flex items-center p-3 border border-gray-200 rounded-lg hover:border-red-500 hover:bg-red-50 transition-colors duration-200"
            >
              <span className="text-2xl mr-3">❤️</span>
              <span className="font-medium">Wishlist</span>
            </Link>
            <Link
              href="/compare"
              className="flex items-center p-3 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors duration-200"
            >
              <span className="text-2xl mr-3">📊</span>
              <span className="font-medium">Course Comparison</span>
            </Link>
          </div>
        </div>

        {/* Back to Home */}
        <div className="mt-8 text-center">
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200"
          >
            Back to Platform Landing Page
          </Link>
        </div>
      </div>
    </div>
  )
}
