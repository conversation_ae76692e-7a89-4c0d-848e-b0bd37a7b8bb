'use client'

import { useEffect, useState } from 'react'
import { headers } from 'next/headers'

interface Institute {
  id: string
  name: string
  slug: string
}

interface Theme {
  id: string
  name: string
  colors: any
  fonts: any
}

export default function InstituteHeadersPage() {
  const [institute, setInstitute] = useState<Institute | null>(null)
  const [theme, setTheme] = useState<Theme | null>(null)

  useEffect(() => {
    // Try to get data from headers (set by middleware)
    const getHeaderData = async () => {
      try {
        const response = await fetch('/api/institute-data', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
        
        if (response.ok) {
          const data = await response.json()
          console.log('📋 Header data received:', data)
          
          if (data.institute) {
            setInstitute(data.institute)
          }
          
          if (data.theme) {
            setTheme(data.theme)
          }
        }
      } catch (error) {
        console.error('❌ Error getting header data:', error)
      }
    }

    getHeaderData()
  }, [])

  if (!institute) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading institute from headers...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 rounded-full bg-primary flex items-center justify-center text-white font-bold text-xl">
                {institute.name.charAt(0)}
              </div>
              <div>
                <h1 className="text-2xl font-bold">
                  {institute.name}
                </h1>
                <p className="text-sm text-muted-foreground">Headers Approach</p>
              </div>
            </div>
            
            {theme && (
              <div className="text-sm">
                Theme: {theme.name}
              </div>
            )}
          </div>
        </div>
      </header>

      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Welcome to {institute.name}
          </h2>
          <p className="text-xl mb-8">
            Data loaded from middleware headers
          </p>
          
          <div className="bg-white/10 p-4 rounded-lg max-w-2xl mx-auto">
            <h3 className="font-semibold mb-2">Institute Data:</h3>
            <pre className="text-left text-sm">
              {JSON.stringify(institute, null, 2)}
            </pre>
            
            {theme && (
              <>
                <h3 className="font-semibold mb-2 mt-4">Theme Data:</h3>
                <pre className="text-left text-sm">
                  {JSON.stringify(theme, null, 2)}
                </pre>
              </>
            )}
          </div>
        </div>
      </section>
    </div>
  )
}
