import { headers } from 'next/headers'
import { InstituteThemeProvider } from '@/components/shared/theme/InstituteThemeProvider'
import { getInstituteData, getThemeData } from '@/lib/institute-resolver'

export default async function InstituteLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: { slug: string }
}) {
  // Get data from middleware headers
  const headersList = headers()
  const instituteId = headersList.get('x-institute-id')
  const themeId = headersList.get('x-theme-id')
  const customDomain = headersList.get('x-custom-domain')
  const instituteData = headersList.get('x-institute-data')
  const themeData = headersList.get('x-theme-data')

  // Parse data from headers (fallback to API if needed)
  let institute, theme

  try {
    institute = instituteData ? JSON.parse(instituteData) : null
    theme = themeData ? JSON.parse(themeData) : null
  } catch (error) {
    console.error('Error parsing header data:', error)
  }

  // Fallback to API calls if header data is not available
  if (!institute || !theme) {
    const [instituteResult, themeResult] = await Promise.all([
      getInstituteData(instituteId),
      getThemeData(themeId, instituteId)
    ])
    institute = institute || instituteResult
    theme = theme || themeResult
  }

  if (!institute) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Institute Not Found</h1>
          <p className="text-gray-600">The requested institute could not be found.</p>
        </div>
      </div>
    )
  }

  if (!theme) {
    console.warn('No theme found, using default theme')
    theme = {
      id: 'education-modern',
      name: 'Education Modern',
      slug: 'education-modern',
      type: 'institute',
      colors: {
        primary: '#059669',
        secondary: '#6b7280',
        accent: '#f59e0b',
        background: '#ffffff',
        text: '#1f2937'
      },
      fonts: {
        heading: 'Poppins',
        body: 'Inter'
      }
    }
  }

  console.log('🎨 Loading institute with theme:', {
    institute: institute.name,
    theme: theme.name,
    domain: customDomain
  })

  return (
    <InstituteThemeProvider 
      institute={institute}
      theme={theme}
      customDomain={customDomain || ''}
    >
      <div className="institute-layout min-h-screen">
        {children}
      </div>
    </InstituteThemeProvider>
  )
}

// Generate metadata for SEO
export async function generateMetadata({
  params
}: {
  params: { slug: string }
}) {
  const headersList = headers()
  const instituteName = headersList.get('x-institute-name')
  const customDomain = headersList.get('x-custom-domain')

  return {
    title: instituteName ? `${instituteName} - Online Learning Platform` : 'Online Learning Platform',
    description: `Discover courses and enhance your skills with ${instituteName || 'our institute'}`,
    keywords: 'online learning, courses, education, training',
    openGraph: {
      title: instituteName ? `${instituteName} - Online Learning Platform` : 'Online Learning Platform',
      description: `Discover courses and enhance your skills with ${instituteName || 'our institute'}`,
      url: customDomain ? `https://${customDomain}` : undefined,
      siteName: instituteName || 'Online Learning Platform',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: instituteName ? `${instituteName} - Online Learning Platform` : 'Online Learning Platform',
      description: `Discover courses and enhance your skills with ${instituteName || 'our institute'}`,
    },
    alternates: {
      canonical: customDomain ? `https://${customDomain}` : undefined,
    },
  }
}
