import { InstituteHeader } from '@/components/institute/InstituteHeader'
import { InstituteHero } from '@/components/institute/InstituteHero'
import { FeaturedCourses } from '@/components/institute/FeaturedCourses'
import { CourseMarketplace } from '@/components/marketplace/CourseMarketplace'
import { InstituteFooter } from '@/components/institute/InstituteFooter'

export default function InstituteLandingPage({
  params
}: {
  params: { slug: string }
}) {
  return (
    <div className="institute-landing">
      {/* Header with institute branding */}
      <InstituteHeader />
      
      {/* Hero section with institute theme */}
      <InstituteHero />
      
      {/* Featured courses section */}
      <FeaturedCourses />
      
      {/* Course marketplace (standard across all institutes) */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Browse All Courses
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover our comprehensive collection of courses designed to help you achieve your learning goals.
            </p>
          </div>
          <CourseMarketplace />
        </div>
      </section>
      
      {/* Footer with institute theme */}
      <InstituteFooter />
    </div>
  )
}

// Generate static params for known institutes (optional optimization)
export async function generateStaticParams() {
  // In production, you might want to fetch all institute slugs
  // For now, return empty array to use dynamic rendering
  return []
}

// Enable ISR (Incremental Static Regeneration) for better performance
export const revalidate = 3600 // Revalidate every hour
