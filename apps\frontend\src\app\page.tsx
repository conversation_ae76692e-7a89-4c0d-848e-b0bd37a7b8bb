'use client'

import React, { useEffect, useState } from 'react'
import PlatformWrapper from '@/components/shared/integration/PlatformWrapper'

// Platform theme components
import PlatformHeader from '@/components/themes/platform/saas-modern/PlatformHeader'
import Hero from '@/components/themes/platform/saas-modern/Hero'
import Features from '@/components/themes/platform/saas-modern/Features'
import Pricing from '@/components/themes/platform/saas-modern/Pricing'
import Testimonials from '@/components/themes/platform/saas-modern/Testimonials'
import Contact from '@/components/themes/platform/saas-modern/Contact'
import Footer from '@/components/themes/platform/saas-modern/Footer'

// Institute theme components
import InstituteWrapper from '@/components/shared/integration/InstituteWrapper'
import InstituteThemeRenderer from '@/components/shared/integration/InstituteThemeRenderer'

// Domain caching utilities
import { getDomainData, initializeCacheManagement } from '@/utils/domainCache'

interface InstituteData {
  id: number
  name: string
  slug: string
  tagline?: string
  description?: string
  email?: string
  phone?: string
  website?: string
  theme?: {
    id: number
    name: string
    slug: string
    colors?: {
      primary: string
      secondary: string
      accent: string
      background: string
      foreground: string
    }
    fonts?: {
      heading: string
      body: string
    }
  }
}

export default function HomePage() {
  const [pageType, setPageType] = useState<'platform' | 'institute' | 'loading'>('loading')
  const [instituteData, setInstituteData] = useState<InstituteData | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Initialize cache management in the background
    initializeCacheManagement()

    const detectDomainAndLoadData = async () => {
      try {
        // Get current domain
        const currentDomain = window.location.host
        console.log('🔍 Detecting domain type:', currentDomain)

        // Check if this is a system domain
        const systemDomains = ['localhost', '127.0.0.1', 'admin.localhost', 'super-admin.localhost']
        const domainWithoutPort = currentDomain.split(':')[0]
        const isSystemDomain = systemDomains.includes(domainWithoutPort)

        console.log('🔍 Domain analysis:', {
          currentDomain,
          domainWithoutPort,
          systemDomains,
          isSystemDomain,
          includes_localhost: systemDomains.includes('localhost'),
          includes_hello_local: systemDomains.includes('hello.local'),
          domain_split: currentDomain.split(':'),
          exact_match_check: domainWithoutPort === 'hello.local'
        })

        if (isSystemDomain) {
          console.log('🏠 System domain detected - showing platform page')
          setPageType('platform')
          return
        }

        // This is a custom domain - get institute data with caching
        console.log('🌐 Custom domain detected - getting institute data with caching...')

        // Use the cached domain data function
        const data = await getDomainData(currentDomain)

        console.log('📋 Domain data received:', data)

        if (data.success && data.institute) {
          console.log('✅ Institute data loaded:', data.institute.name)
          setInstituteData(data.institute)
          setPageType('institute')

          // Apply theme CSS variables
          if (data.institute.theme?.colors) {
            const root = document.documentElement
            const colors = data.institute.theme.colors

            root.style.setProperty('--primary', colors.primary || '#10B981')
            root.style.setProperty('--secondary', colors.secondary || '#3B82F6')
            root.style.setProperty('--accent', colors.accent || '#F59E0B')
            root.style.setProperty('--background', colors.background || '#FFFFFF')
            root.style.setProperty('--foreground', colors.foreground || '#000000')

            console.log('🎨 Theme colors applied:', data.institute.theme.name)
          }
        } else {
          throw new Error(data.error || 'Institute not found for this domain')
        }
      } catch (err) {
        console.error('❌ Error detecting domain/loading data:', err)
        setError(err instanceof Error ? err.message : 'Failed to load page data')
        setPageType('platform') // Fallback to platform
      }
    }

    detectDomainAndLoadData()
  }, [])

  // Loading state
  if (pageType === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Page</h1>
          <p className="text-muted-foreground mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  // Render platform landing page
  if (pageType === 'platform') {
    return (
      <PlatformWrapper userType="platform">
        <div className="min-h-screen">
          <PlatformHeader />
          <Hero />
          <Features />
          <Pricing />
          <Testimonials />
          <Contact />
          <Footer />
        </div>
      </PlatformWrapper>
    )
  }

  // Render institute landing page
  if (pageType === 'institute' && instituteData) {
    return (
      <InstituteWrapper institute={instituteData}>
        <div className="min-h-screen">
          {/* We'll create dynamic theme components here */}
          <InstituteThemeRenderer
            institute={instituteData}
            theme={instituteData.theme}
          />
        </div>
      </InstituteWrapper>
    )
  }

  // Fallback
  return null
}