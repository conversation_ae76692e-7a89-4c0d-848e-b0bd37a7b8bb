'use client'

import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useStudentStore } from '@/stores/student/useStudentStore'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  BookOpen,
  Clock,
  Award,
  TrendingUp,
  Play,
  Search,
  Filter,
  Star,
  Calendar,
  Target,
  Zap,
  ChevronRight,
  AlertTriangle
} from 'lucide-react'

export default function StudentDashboard() {
  const { user, logout } = useAuthStore()
  const {
    enrolledCourses,
    dashboardStats,
    certificates,
    isLoading,
    error,
    fetchEnrolledCourses,
    fetchDashboardStats,
    fetchCertificates,
    setCurrentCourse,
    clearError
  } = useStudentStore()

  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (!user || user.legacyRole !== 'student') {
      window.location.href = '/auth/user-login'
    }
  }, [user])

  useEffect(() => {
    if (user?.legacyRole === 'student') {
      fetchEnrolledCourses()
      fetchDashboardStats()
      fetchCertificates()
    }
  }, [user, fetchEnrolledCourses, fetchDashboardStats, fetchCertificates])

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-500'
    if (percentage >= 60) return 'bg-blue-500'
    if (percentage >= 40) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">Learning Dashboard</h1>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                Student
              </Badge>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome back, {user.firstName} {user.lastName}
              </span>
              <Button variant="outline" size="sm" onClick={logout}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Error Alert */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      )}

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Welcome Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome back to your learning journey! 🎓
            </h2>
            <p className="text-gray-600">
              Continue where you left off and explore new courses to expand your knowledge.
            </p>
          </div>

          {/* Dashboard Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Enrolled Courses</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.totalCourses}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardStats.inProgressCourses} in progress
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Learning Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatTime(dashboardStats.totalTimeSpent)}
                </div>
                <p className="text-xs text-muted-foreground">
                  total time spent learning
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Certificates</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.certificatesEarned}</div>
                <p className="text-xs text-muted-foreground">
                  certificates earned
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Learning Streak</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.streakDays}</div>
                <p className="text-xs text-muted-foreground">
                  days in a row
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="courses">My Courses</TabsTrigger>
              <TabsTrigger value="browse">Browse Courses</TabsTrigger>
              <TabsTrigger value="certificates">Certificates</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              {/* Continue Learning Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Play className="h-5 w-5" />
                    <span>Continue Learning</span>
                  </CardTitle>
                  <CardDescription>
                    Pick up where you left off in your courses
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {enrolledCourses.filter(course => course.status === 'active' && course.progress.percentage < 100).length > 0 ? (
                    <div className="space-y-4">
                      {enrolledCourses
                        .filter(course => course.status === 'active' && course.progress.percentage < 100)
                        .slice(0, 3)
                        .map((enrollment) => (
                          <div key={enrollment.id} className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                              {enrollment.course.thumbnail ? (
                                <img
                                  src={enrollment.course.thumbnail.url}
                                  alt={enrollment.course.title}
                                  className="w-full h-full object-cover rounded-lg"
                                />
                              ) : (
                                <BookOpen className="h-8 w-8 text-white" />
                              )}
                            </div>

                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-gray-900 truncate">
                                {enrollment.course.title}
                              </h3>
                              <p className="text-sm text-gray-500 mb-2">
                                {enrollment.course.shortDescription}
                              </p>

                              <div className="flex items-center space-x-4 text-xs text-gray-500">
                                <span>{enrollment.course.level}</span>
                                <span>•</span>
                                <span>{formatTime(enrollment.course.duration.hours * 60 + enrollment.course.duration.minutes)}</span>
                                <span>•</span>
                                <span>{enrollment.progress.completedLessons}/{enrollment.progress.totalLessons} lessons</span>
                              </div>

                              <div className="mt-2">
                                <div className="flex items-center justify-between text-sm mb-1">
                                  <span>Progress</span>
                                  <span>{enrollment.progress.percentage}%</span>
                                </div>
                                <Progress value={enrollment.progress.percentage} className="h-2" />
                              </div>
                            </div>

                            <Button
                              onClick={() => {
                                setCurrentCourse(enrollment)
                                // TODO: Navigate to course player
                              }}
                              className="flex-shrink-0"
                            >
                              Continue
                              <ChevronRight className="h-4 w-4 ml-1" />
                            </Button>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No courses in progress. Start learning by browsing available courses!</p>
                      <Button
                        className="mt-4"
                        onClick={() => setActiveTab('browse')}
                      >
                        Browse Courses
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Recent Achievements */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Award className="h-5 w-5" />
                    <span>Recent Achievements</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {certificates.length > 0 ? (
                    <div className="space-y-3">
                      {certificates.slice(0, 3).map((certificate) => (
                        <div key={certificate.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                          <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                            <Award className="h-5 w-5 text-yellow-600" />
                          </div>
                          <div className="flex-1">
                            <p className="font-medium">{certificate.courseName}</p>
                            <p className="text-sm text-gray-500">
                              Completed on {new Date(certificate.issuedDate).toLocaleDateString()}
                            </p>
                          </div>
                          <Button variant="outline" size="sm">
                            View Certificate
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <Award className="h-10 w-10 mx-auto mb-3 text-gray-300" />
                      <p>No certificates earned yet. Complete courses to earn certificates!</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* My Courses Tab */}
            <TabsContent value="courses" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>My Enrolled Courses</CardTitle>
                  <CardDescription>
                    Manage and track your enrolled courses
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {enrolledCourses.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {enrolledCourses.map((enrollment) => (
                        <Card key={enrollment.id} className="hover:shadow-md transition-shadow">
                          <CardContent className="p-4">
                            <div className="w-full h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mb-4">
                              {enrollment.course.thumbnail ? (
                                <img
                                  src={enrollment.course.thumbnail.url}
                                  alt={enrollment.course.title}
                                  className="w-full h-full object-cover rounded-lg"
                                />
                              ) : (
                                <BookOpen className="h-12 w-12 text-white" />
                              )}
                            </div>

                            <h3 className="font-semibold mb-2 line-clamp-2">
                              {enrollment.course.title}
                            </h3>

                            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                              <Badge variant="outline">{enrollment.course.level}</Badge>
                              <span>•</span>
                              <span>{formatTime(enrollment.course.duration.hours * 60 + enrollment.course.duration.minutes)}</span>
                            </div>

                            <div className="mb-4">
                              <div className="flex items-center justify-between text-sm mb-1">
                                <span>Progress</span>
                                <span>{enrollment.progress.percentage}%</span>
                              </div>
                              <Progress value={enrollment.progress.percentage} className="h-2" />
                            </div>

                            <div className="flex items-center justify-between">
                              <Badge
                                variant={enrollment.status === 'completed' ? 'default' : 'secondary'}
                                className={enrollment.status === 'completed' ? 'bg-green-100 text-green-800' : ''}
                              >
                                {enrollment.status}
                              </Badge>

                              <Button
                                size="sm"
                                onClick={() => {
                                  setCurrentCourse(enrollment)
                                  // TODO: Navigate to course player
                                }}
                              >
                                {enrollment.progress.percentage === 100 ? 'Review' : 'Continue'}
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12 text-gray-500">
                      <BookOpen className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                      <h3 className="text-lg font-medium mb-2">No Enrolled Courses</h3>
                      <p className="mb-4">Start your learning journey by enrolling in courses!</p>
                      <Button onClick={() => setActiveTab('browse')}>
                        Browse Courses
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Browse Courses Tab */}
            <TabsContent value="browse" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Browse Available Courses</CardTitle>
                  <CardDescription>
                    Discover new courses to expand your knowledge
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-gray-500">
                    <Search className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium mb-2">Course Browser Coming Soon</h3>
                    <p>We're working on an amazing course browsing experience for you!</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Certificates Tab */}
            <TabsContent value="certificates" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>My Certificates</CardTitle>
                  <CardDescription>
                    View and download your earned certificates
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {certificates.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {certificates.map((certificate) => (
                        <Card key={certificate.id} className="hover:shadow-md transition-shadow">
                          <CardContent className="p-4">
                            <div className="w-full h-32 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mb-4">
                              <Award className="h-12 w-12 text-white" />
                            </div>

                            <h3 className="font-semibold mb-2">
                              {certificate.courseName}
                            </h3>

                            <p className="text-sm text-gray-500 mb-3">
                              Issued on {new Date(certificate.issuedDate).toLocaleDateString()}
                            </p>

                            {certificate.grade && (
                              <Badge className="mb-3">
                                Grade: {certificate.grade}
                              </Badge>
                            )}

                            <div className="flex space-x-2">
                              <Button size="sm" className="flex-1">
                                View
                              </Button>
                              <Button variant="outline" size="sm" className="flex-1">
                                Download
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12 text-gray-500">
                      <Award className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                      <h3 className="text-lg font-medium mb-2">No Certificates Yet</h3>
                      <p className="mb-4">Complete courses to earn certificates and showcase your achievements!</p>
                      <Button onClick={() => setActiveTab('courses')}>
                        View My Courses
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
