'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { 
  Settings, 
  User, 
  Shield, 
  Bell, 
  BookOpen, 
  CreditCard, 
  Database,
  Target,
  Globe,
  Video,
  Smartphone,
  Download
} from 'lucide-react'

interface SettingsNavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  category: string
}

const settingsNavigation: SettingsNavItem[] = [
  // Profile Management
  {
    title: 'Profile Settings',
    href: '/student/settings/profile',
    icon: User,
    description: 'Personal information and profile picture',
    category: 'Profile Management'
  },
  {
    title: 'Emergency Contact',
    href: '/student/settings/emergency-contact',
    icon: User,
    description: 'Emergency contact information',
    category: 'Profile Management'
  },

  // Account Security
  {
    title: 'Password & Security',
    href: '/student/settings/security',
    icon: Shield,
    description: 'Change password and security settings',
    category: 'Account Security'
  },
  {
    title: 'Two-Factor Authentication',
    href: '/student/settings/2fa',
    icon: Shield,
    description: 'Enable 2FA for enhanced security',
    category: 'Account Security'
  },
  {
    title: 'Active Sessions',
    href: '/student/settings/sessions',
    icon: Smartphone,
    description: 'Manage your active login sessions',
    category: 'Account Security'
  },
  {
    title: 'Login History',
    href: '/student/settings/login-history',
    icon: Shield,
    description: 'View your recent login activity',
    category: 'Account Security'
  },

  // Notification Preferences
  {
    title: 'Email Notifications',
    href: '/student/settings/notifications/email',
    icon: Bell,
    description: 'Configure email notification preferences',
    category: 'Notification Preferences'
  },
  {
    title: 'Push Notifications',
    href: '/student/settings/notifications/push',
    icon: Bell,
    description: 'Manage push notification settings',
    category: 'Notification Preferences'
  },
  {
    title: 'Course Updates',
    href: '/student/settings/notifications/courses',
    icon: BookOpen,
    description: 'Course and assignment notifications',
    category: 'Notification Preferences'
  },

  // Learning Preferences
  {
    title: 'Language & Region',
    href: '/student/settings/language',
    icon: Globe,
    description: 'Language and timezone settings',
    category: 'Learning Preferences'
  },
  {
    title: 'Video Preferences',
    href: '/student/settings/video',
    icon: Video,
    description: 'Video quality and playback settings',
    category: 'Learning Preferences'
  },
  {
    title: 'Accessibility',
    href: '/student/settings/accessibility',
    icon: User,
    description: 'Accessibility and display options',
    category: 'Learning Preferences'
  },

  // Billing & Payments
  {
    title: 'Payment Methods',
    href: '/student/settings/payment-methods',
    icon: CreditCard,
    description: 'Manage your payment methods',
    category: 'Billing & Payments'
  },
  {
    title: 'Purchase History',
    href: '/student/settings/purchases',
    icon: CreditCard,
    description: 'View your course purchases and receipts',
    category: 'Billing & Payments'
  },
  {
    title: 'Subscriptions',
    href: '/student/settings/subscriptions',
    icon: CreditCard,
    description: 'Manage your active subscriptions',
    category: 'Billing & Payments'
  },

  // Privacy & Data
  {
    title: 'Privacy Settings',
    href: '/student/settings/privacy',
    icon: Database,
    description: 'Control your privacy and data sharing',
    category: 'Privacy & Data'
  },
  {
    title: 'Data Export',
    href: '/student/settings/data-export',
    icon: Download,
    description: 'Download your personal data',
    category: 'Privacy & Data'
  },
  {
    title: 'Account Deletion',
    href: '/student/settings/delete-account',
    icon: Database,
    description: 'Request account deletion',
    category: 'Privacy & Data'
  },

  // Goals & Progress
  {
    title: 'Learning Goals',
    href: '/student/settings/goals',
    icon: Target,
    description: 'Set and track your learning goals',
    category: 'Goals & Progress'
  },
  {
    title: 'Study Schedule',
    href: '/student/settings/schedule',
    icon: Target,
    description: 'Manage your study schedule',
    category: 'Goals & Progress'
  },
  {
    title: 'Progress Tracking',
    href: '/student/settings/progress',
    icon: Target,
    description: 'View your learning progress and achievements',
    category: 'Goals & Progress'
  }
]

const categories = Array.from(new Set(settingsNavigation.map(item => item.category)))

export default function StudentSettingsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  const filteredNavigation = selectedCategory
    ? settingsNavigation.filter(item => item.category === selectedCategory)
    : settingsNavigation

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Settings Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-2 mb-6">
            <Settings className="h-6 w-6 text-blue-600" />
            <h1 className="text-xl font-semibold text-gray-900">My Settings</h1>
          </div>

          {/* Category Filter */}
          <div className="mb-6">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === null ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(null)}
              >
                All
              </Button>
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="text-xs"
                >
                  {category.split(' ')[0]}
                </Button>
              ))}
            </div>
          </div>

          {/* Navigation Items */}
          <nav className="space-y-2">
            {filteredNavigation.map((item) => {
              const isActive = pathname === item.href
              const Icon = item.icon

              return (
                <Link key={item.href} href={item.href}>
                  <Card className={cn(
                    "transition-all duration-200 cursor-pointer hover:shadow-md",
                    isActive 
                      ? "border-blue-500 bg-blue-50 shadow-sm" 
                      : "border-gray-200 hover:border-gray-300"
                  )}>
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        <Icon className={cn(
                          "h-5 w-5 mt-0.5 flex-shrink-0",
                          isActive ? "text-blue-600" : "text-gray-500"
                        )} />
                        <div className="flex-1 min-w-0">
                          <h3 className={cn(
                            "text-sm font-medium",
                            isActive ? "text-blue-900" : "text-gray-900"
                          )}>
                            {item.title}
                          </h3>
                          <p className={cn(
                            "text-xs mt-1",
                            isActive ? "text-blue-700" : "text-gray-500"
                          )}>
                            {item.description}
                          </p>
                          {selectedCategory === null && (
                            <span className={cn(
                              "inline-block text-xs px-2 py-1 rounded-full mt-2",
                              isActive 
                                ? "bg-blue-100 text-blue-800" 
                                : "bg-gray-100 text-gray-600"
                            )}>
                              {item.category}
                            </span>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-8">
          {children}
        </div>
      </div>
    </div>
  )
}
