'use client'

import { useEffect, useState } from 'react'
import { useBillingStore } from '@/stores/billing/useBillingStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { BillsList } from '@/components/billing/BillsList'
import { CommissionTracker } from '@/components/billing/CommissionTracker'
import { BillingFilters } from '@/components/billing/BillingFilters'
import { BillGenerationDialog } from '@/components/billing/BillGenerationDialog'
import { 
  DollarSign, 
  FileText, 
  Clock, 
  AlertTriangle,
  Plus,
  Download,
  TrendingUp,
  Users
} from 'lucide-react'

export default function BillingPage() {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [showGenerateDialog, setShowGenerateDialog] = useState(false)
  
  const {
    dashboardData,
    bills,
    isLoading,
    error,
    fetchDashboardData,
    fetchBills,
    clearError
  } = useBillingStore()

  useEffect(() => {
    fetchDashboardData()
  }, [fetchDashboardData])

  useEffect(() => {
    if (activeTab === 'bills') {
      fetchBills()
    }
  }, [activeTab, fetchBills])

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    clearError()
  }

  const formatCurrency = (amount: number, currency = 'INR') => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Billing & Payments</h1>
          <p className="text-muted-foreground">
            Manage monthly bills, commission tracking, and payment processing
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={() => setShowGenerateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Generate Bill
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Dashboard Overview */}
      {dashboardData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Month</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(dashboardData.summary.currentMonthTotal)}
              </div>
              <p className="text-xs text-muted-foreground">
                <Badge variant="secondary" className="mr-1">
                  {dashboardData.summary.billsCount.current}
                </Badge>
                bills generated
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Bills</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(dashboardData.summary.pendingTotal)}
              </div>
              <p className="text-xs text-muted-foreground">
                <Badge variant="secondary" className="mr-1">
                  {dashboardData.summary.billsCount.pending}
                </Badge>
                pending payment
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overdue Bills</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {formatCurrency(dashboardData.summary.overdueTotal)}
              </div>
              <p className="text-xs text-muted-foreground">
                <Badge variant="destructive" className="mr-1">
                  {dashboardData.summary.billsCount.overdue}
                </Badge>
                overdue bills
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Commission</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(dashboardData.commissionSummary.totalCommission)}
              </div>
              <p className="text-xs text-muted-foreground">
                <Badge variant="secondary" className="mr-1">
                  {dashboardData.commissionSummary.purchaseCount}
                </Badge>
                purchases this month
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard" className="flex items-center space-x-2">
            <DollarSign className="h-4 w-4" />
            <span>Dashboard</span>
          </TabsTrigger>
          
          <TabsTrigger value="bills" className="flex items-center space-x-2">
            <FileText className="h-4 w-4" />
            <span>Bills</span>
            {bills.length > 0 && (
              <Badge variant="secondary" className="ml-1">
                {bills.length}
              </Badge>
            )}
          </TabsTrigger>
          
          <TabsTrigger value="commission" className="flex items-center space-x-2">
            <TrendingUp className="h-4 w-4" />
            <span>Commission</span>
          </TabsTrigger>
          
          <TabsTrigger value="reports" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>Reports</span>
          </TabsTrigger>
        </TabsList>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-6">
          {dashboardData && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Bills */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Bills</CardTitle>
                  <CardDescription>Latest generated bills</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {dashboardData.currentMonthBills.slice(0, 5).map((bill) => (
                      <div key={bill.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{bill.billNumber}</p>
                          <p className="text-sm text-gray-500">
                            {typeof bill.branch === 'object' ? bill.branch.name : 'Branch'}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatCurrency(bill.amounts.totalAmount)}</p>
                          <Badge variant={
                            bill.status === 'paid' ? 'default' : 
                            bill.status === 'overdue' ? 'destructive' : 'secondary'
                          }>
                            {bill.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Commission Summary */}
              <Card>
                <CardHeader>
                  <CardTitle>Commission Breakdown</CardTitle>
                  <CardDescription>Recent course purchases and commissions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {dashboardData.recentPurchases.slice(0, 5).map((purchase) => (
                      <div key={purchase.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{purchase.course.title}</p>
                          <p className="text-sm text-gray-500">
                            {purchase.student.firstName} {purchase.student.lastName}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatCurrency(purchase.commissionDetails.commissionAmount)}</p>
                          <p className="text-sm text-gray-500">
                            {purchase.commissionDetails.commissionRate}% commission
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* Bills Tab */}
        <TabsContent value="bills" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Bills Management</CardTitle>
              <CardDescription>
                View and manage monthly bills for all branches
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <BillingFilters type="bills" />
                <BillsList />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Commission Tab */}
        <TabsContent value="commission" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Commission Tracking</CardTitle>
              <CardDescription>
                Track course purchases and commission calculations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <BillingFilters type="commission" />
                <CommissionTracker />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Reports Tab */}
        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing Reports</CardTitle>
              <CardDescription>
                Generate and download billing reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-gray-500">Billing reports feature coming soon...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Bill Generation Dialog */}
      <BillGenerationDialog 
        open={showGenerateDialog} 
        onOpenChange={setShowGenerateDialog}
      />
    </div>
  )
}
