'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { usePaymentGatewayStore } from '@/stores/super-admin/usePaymentGatewayStore'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Grid3X3, 
  List, 
  CreditCard,
  ExternalLink,
  Settings
} from 'lucide-react'
import { MultiStepPaymentGatewayForm } from '@/components/super-admin/payment-gateways/MultiStepPaymentGatewayForm'
import { DeleteConfirmDialog } from '@/components/shared/DeleteConfirmDialog'
import { PaymentGateway } from '@/stores/super-admin/usePaymentGatewayStore'

export default function PaymentGatewaysPage() {
  const router = useRouter()
  const [authChecked, setAuthChecked] = useState(false)
  const [viewMode, setViewMode] = useState<'list' | 'cards'>('cards')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [gatewayToDelete, setGatewayToDelete] = useState<PaymentGateway | null>(null)

  const { user, isAuthenticated, isLoading, initialize } = useAuthStore()
  const {
    gateways,
    loading,
    error,
    fetchGateways,
    toggleGatewayStatus,
    deleteGateway
  } = usePaymentGatewayStore()

  // Initialize auth
  useEffect(() => {
    const timer = setTimeout(() => {
      initialize()
    }, 100)
    return () => clearTimeout(timer)
  }, [initialize])

  // Check authentication
  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated || user?.role?.code !== 'super_admin') {
        router.push('/auth/login')
        return
      }
      setAuthChecked(true)
    }
  }, [isAuthenticated, isLoading, user, router])

  // Fetch gateways
  useEffect(() => {
    if (authChecked) {
      fetchGateways()
    }
  }, [authChecked, fetchGateways])

  const handleEdit = (gateway: PaymentGateway) => {
    setSelectedGateway(gateway)
    setShowEditForm(true)
  }

  const handleDelete = (gateway: PaymentGateway) => {
    setGatewayToDelete(gateway)
    setShowDeleteDialog(true)
  }

  const confirmDelete = async () => {
    if (gatewayToDelete) {
      try {
        await deleteGateway(gatewayToDelete.id)
        setShowDeleteDialog(false)
        setGatewayToDelete(null)
      } catch (error) {
        // Error handled in store
      }
    }
  }

  const handleToggleStatus = async (gateway: PaymentGateway, isActive: boolean) => {
    await toggleGatewayStatus(gateway.id, isActive)
  }

  if (isLoading || !authChecked) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8 max-w-7xl">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 sm:mb-8">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Payment Gateways</h1>
          <p className="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">
            Manage available payment gateways for institutes
          </p>
        </div>

        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-lg p-1 w-full sm:w-auto justify-center">
            <Button
              variant={viewMode === 'cards' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('cards')}
              className="px-3 flex-1 sm:flex-none"
            >
              <Grid3X3 className="w-4 h-4 sm:mr-1" />
              <span className="hidden sm:inline">Cards</span>
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="px-3 flex-1 sm:flex-none"
            >
              <List className="w-4 h-4 sm:mr-1" />
              <span className="hidden sm:inline">List</span>
            </Button>
          </div>

          <Button onClick={() => setShowCreateForm(true)} className="w-full sm:w-auto">
            <Plus className="w-4 h-4 mr-2" />
            Add Gateway
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Gateways Display */}
      {!loading && (
        <>
          {viewMode === 'cards' ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {gateways.map((gateway) => {
                // Safety check for gateway object
                if (!gateway || !gateway.id) return null

                return (
                <Card key={gateway.id} className="relative flex flex-col h-full">
                  <CardHeader className="pb-3 flex-shrink-0">
                    <div className="flex flex-col gap-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                          {gateway.logoUrl && (
                            <img
                              src={gateway.logoUrl}
                              alt={gateway.name}
                              className="w-6 h-6 sm:w-8 sm:h-8 object-contain flex-shrink-0"
                            />
                          )}
                          <div className="min-w-0 flex-1">
                            <CardTitle className="text-base sm:text-lg truncate">{gateway.name || 'Unnamed Gateway'}</CardTitle>
                            <CardDescription className="text-xs sm:text-sm">
                              Payment Gateway
                            </CardDescription>
                          </div>
                        </div>

                        <Switch
                          checked={gateway.isActive}
                          onCheckedChange={(checked) => handleToggleStatus(gateway, checked)}
                          size="sm"
                          className="flex-shrink-0"
                        />
                      </div>

                      {gateway.isFeatured && (
                        <Badge variant="secondary" className="text-xs w-fit">
                          Featured
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  
                  <CardContent className="flex-1 flex flex-col justify-between">
                    {/* Supported Currencies */}
                    <div className="space-y-3">
                      <div>
                        <p className="text-xs sm:text-sm font-medium text-gray-700 mb-2">
                          Supported Currencies
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {gateway.supportedCurrencies?.slice(0, 3).map((curr, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {typeof curr === 'string' ? curr : (curr?.currency || 'N/A')}
                            </Badge>
                          )) || (
                            <Badge variant="outline" className="text-xs">
                              No currencies
                            </Badge>
                          )}
                          {gateway.supportedCurrencies && gateway.supportedCurrencies.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{gateway.supportedCurrencies.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="pt-4 border-t mt-4">
                      <div className="flex flex-col sm:flex-row gap-2">
                        <div className="flex gap-2 flex-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(gateway)}
                            className="flex-1 sm:flex-none"
                          >
                            <Edit className="w-3 h-3 sm:mr-1" />
                            <span className="hidden sm:inline">Edit</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(gateway)}
                            className="text-red-600 hover:text-red-700 flex-1 sm:flex-none"
                          >
                            <Trash2 className="w-3 h-3 sm:mr-1" />
                            <span className="hidden sm:inline">Delete</span>
                          </Button>
                        </div>

                        {gateway.documentationUrl && (
                          <Button
                            variant="ghost"
                            size="sm"
                            asChild
                            className="w-full sm:w-auto"
                          >
                            <a
                              href={gateway.documentationUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center justify-center"
                            >
                              <ExternalLink className="w-3 h-3 sm:mr-1" />
                              <span className="hidden sm:inline ml-1">Docs</span>
                            </a>
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
                )
              })}
            </div>
          ) : (
            <div className="bg-white rounded-lg border">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Gateway</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Currencies</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Payment Methods</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {gateways.map((gateway) => {
                      // Safety check for gateway object
                      if (!gateway || !gateway.id) return null

                      return (
                      <tr key={gateway.id} className="hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-3">
                            {gateway.logoUrl && (
                              <img
                                src={gateway.logoUrl}
                                alt={gateway.name || 'Gateway'}
                                className="w-6 h-6 object-contain"
                              />
                            )}
                            <div>
                              <p className="font-medium text-gray-900">{gateway.name || 'Unnamed Gateway'}</p>
                              {gateway.isFeatured && (
                                <Badge variant="secondary" className="text-xs mt-1">
                                  Featured
                                </Badge>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex flex-wrap gap-1">
                            {gateway.supportedCurrencies?.slice(0, 3).map((curr, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {typeof curr === 'string' ? curr : (curr?.currency || 'N/A')}
                              </Badge>
                            )) || (
                              <Badge variant="outline" className="text-xs">
                                No currencies
                              </Badge>
                            )}
                            {gateway.supportedCurrencies && gateway.supportedCurrencies.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{gateway.supportedCurrencies.length - 3}
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex flex-wrap gap-1">
                            {gateway.supportedMethods?.slice(0, 2).map((method, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {typeof method === 'string'
                                  ? method.replace(/_/g, ' ')
                                  : (method?.method?.replace(/_/g, ' ') || 'N/A')
                                }
                              </Badge>
                            )) || (
                              <Badge variant="outline" className="text-xs">
                                No methods
                              </Badge>
                            )}
                            {gateway.supportedMethods && gateway.supportedMethods.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{gateway.supportedMethods.length - 2}
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <Switch
                            checked={gateway.isActive}
                            onCheckedChange={(checked) => handleToggleStatus(gateway, checked)}
                          />
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(gateway)}
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(gateway)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                            {gateway.documentationUrl && (
                              <Button
                                variant="ghost"
                                size="sm"
                                asChild
                              >
                                <a 
                                  href={gateway.documentationUrl} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                >
                                  <ExternalLink className="w-3 h-3" />
                                </a>
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {gateways.length === 0 && !loading && (
            <div className="text-center py-12">
              <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No payment gateways configured
              </h3>
              <p className="text-gray-600 mb-4">
                Add your first payment gateway to get started.
              </p>
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Gateway
              </Button>
            </div>
          )}
        </>
      )}

      {/* Create Gateway Form */}
      <MultiStepPaymentGatewayForm
        isOpen={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onSuccess={() => {
          setShowCreateForm(false)
          fetchGateways()
        }}
      />

      {/* Edit Gateway Form */}
      <MultiStepPaymentGatewayForm
        isOpen={showEditForm}
        onClose={() => {
          setShowEditForm(false)
          setSelectedGateway(null)
        }}
        gateway={selectedGateway}
        onSuccess={() => {
          setShowEditForm(false)
          setSelectedGateway(null)
          fetchGateways()
        }}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => {
          setShowDeleteDialog(false)
          setGatewayToDelete(null)
        }}
        onConfirm={confirmDelete}
        title="Delete Payment Gateway"
        description={`Are you sure you want to delete "${gatewayToDelete?.name}"? This action cannot be undone.`}
      />
    </div>
  )
}
