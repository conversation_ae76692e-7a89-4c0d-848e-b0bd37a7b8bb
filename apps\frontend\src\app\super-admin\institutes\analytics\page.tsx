'use client'

import { useEffect, useState } from 'react'
import { useInstituteStore } from '@/stores/institute/useInstituteStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  BarChart3, 
  TrendingUp,
  TrendingDown,
  Users,
  Building2,
  DollarSign,
  BookOpen,
  Calendar,
  Download,
  Filter,
  AlertTriangle,
  Activity,
  Target,
  Award
} from 'lucide-react'

export default function InstituteAnalyticsPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [timeRange, setTimeRange] = useState('30d')
  
  const {
    analytics,
    isLoading,
    error,
    fetchAnalytics,
    clearError
  } = useInstituteStore()

  useEffect(() => {
    fetchAnalytics()
  }, [fetchAnalytics])

  const handleExport = () => {
    // Implement export functionality
    console.log('Exporting analytics data')
  }

  const mockAnalytics = {
    overview: {
      totalInstitutes: 156,
      activeInstitutes: 142,
      totalStudents: 12450,
      totalRevenue: 2450000,
      growthRate: 12.5,
      averageStudentsPerInstitute: 87.7
    },
    performance: {
      topPerformingInstitutes: [
        { name: 'Tech Excellence Academy', students: 450, revenue: 125000, growth: 15.2 },
        { name: 'Global Learning Institute', students: 380, revenue: 98000, growth: 12.8 },
        { name: 'Future Skills University', students: 320, revenue: 85000, growth: 10.5 }
      ],
      lowPerformingInstitutes: [
        { name: 'Basic Learning Center', students: 25, revenue: 8500, growth: -2.1 },
        { name: 'Simple Education Hub', students: 18, revenue: 6200, growth: -5.3 }
      ]
    },
    trends: {
      monthlyGrowth: [
        { month: 'Jan', institutes: 145, students: 11200 },
        { month: 'Feb', institutes: 148, students: 11580 },
        { month: 'Mar', institutes: 152, students: 11950 },
        { month: 'Apr', institutes: 156, students: 12450 }
      ]
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading analytics...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Institute Analytics</h1>
          <p className="text-gray-600 mt-1">Performance metrics and insights for all institutes</p>
        </div>
        <div className="flex items-center gap-3">
          <select 
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={clearError}
              className="ml-2"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Institutes</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalytics.overview.totalInstitutes}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              +{mockAnalytics.overview.growthRate}% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Institutes</CardTitle>
            <Activity className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalytics.overview.activeInstitutes}</div>
            <p className="text-xs text-muted-foreground">
              {((mockAnalytics.overview.activeInstitutes / mockAnalytics.overview.totalInstitutes) * 100).toFixed(1)}% active rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalytics.overview.totalStudents.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Avg {mockAnalytics.overview.averageStudentsPerInstitute} per institute
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(mockAnalytics.overview.totalRevenue / 100000).toFixed(1)}L</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Analytics</CardTitle>
          <CardDescription>Comprehensive performance insights and trends</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Institute Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Active Institutes</span>
                        <span className="font-medium">{mockAnalytics.overview.activeInstitutes}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Inactive Institutes</span>
                        <span className="font-medium">{mockAnalytics.overview.totalInstitutes - mockAnalytics.overview.activeInstitutes}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Average Students</span>
                        <span className="font-medium">{mockAnalytics.overview.averageStudentsPerInstitute}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Growth Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Monthly Growth Rate</span>
                        <Badge variant="outline" className="text-green-600">
                          +{mockAnalytics.overview.growthRate}%
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Student Growth</span>
                        <Badge variant="outline" className="text-blue-600">
                          +8.2%
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Revenue Growth</span>
                        <Badge variant="outline" className="text-green-600">
                          +15.3%
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="performance" className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Award className="h-5 w-5 text-yellow-500" />
                      Top Performing Institutes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {mockAnalytics.performance.topPerformingInstitutes.map((institute, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <div>
                            <p className="font-medium text-sm">{institute.name}</p>
                            <p className="text-xs text-gray-600">{institute.students} students • ₹{(institute.revenue / 1000).toFixed(0)}K revenue</p>
                          </div>
                          <Badge variant="outline" className="text-green-600">
                            +{institute.growth}%
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Target className="h-5 w-5 text-red-500" />
                      Needs Attention
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {mockAnalytics.performance.lowPerformingInstitutes.map((institute, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                          <div>
                            <p className="font-medium text-sm">{institute.name}</p>
                            <p className="text-xs text-gray-600">{institute.students} students • ₹{(institute.revenue / 1000).toFixed(0)}K revenue</p>
                          </div>
                          <Badge variant="outline" className="text-red-600">
                            {institute.growth}%
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="trends" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Growth Trends</CardTitle>
                  <CardDescription>Monthly institute and student growth over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Growth Chart</h3>
                    <p className="text-gray-600 mb-4">Interactive charts showing institute and student growth trends</p>
                    <Button>View Detailed Charts</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
