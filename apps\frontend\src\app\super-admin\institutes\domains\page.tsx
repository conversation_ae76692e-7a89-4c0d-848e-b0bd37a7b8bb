'use client'

import { useEffect, useState } from 'react'
import { useInstituteStore } from '@/stores/institute/useInstituteStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Globe, 
  CheckCircle,
  XCircle,
  Eye,
  ExternalLink,
  Building2,
  Calendar,
  AlertTriangle,
  Filter,
  Search,
  Clock,
  Shield
} from 'lucide-react'

export default function DomainRequestsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedRequest, setSelectedRequest] = useState<any>(null)
  
  const {
    domainRequests,
    isLoading,
    error,
    fetchDomainRequests,
    approveDomainRequest,
    rejectDomainRequest,
    clearError
  } = useInstituteStore()

  useEffect(() => {
    fetchDomainRequests()
  }, [fetchDomainRequests])

  const handleApprove = async (requestId: string) => {
    try {
      await approveDomainRequest(requestId)
      // Show success message
    } catch (error) {
      console.error('Failed to approve domain request:', error)
    }
  }

  const handleReject = async (requestId: string) => {
    try {
      await rejectDomainRequest(requestId)
      // Show success message
    } catch (error) {
      console.error('Failed to reject domain request:', error)
    }
  }

  const handleViewDetails = (request: any) => {
    setSelectedRequest(request)
  }

  const mockDomainRequests = [
    {
      id: '1',
      instituteName: 'Tech Excellence Academy',
      currentDomain: 'techexcellence.lms.com',
      requestedDomain: 'academy.techexcellence.edu',
      requestedAt: '2024-01-15',
      status: 'pending',
      dnsVerified: true,
      sslRequired: true,
      reason: 'Brand consistency and professional appearance'
    },
    {
      id: '2',
      instituteName: 'Global Learning Institute',
      currentDomain: 'globallearning.lms.com',
      requestedDomain: 'learn.globalinstitute.org',
      requestedAt: '2024-01-14',
      status: 'pending',
      dnsVerified: false,
      sslRequired: true,
      reason: 'Custom branding for better student experience'
    },
    {
      id: '3',
      instituteName: 'Future Skills University',
      currentDomain: 'futureskills.lms.com',
      requestedDomain: 'portal.futureskills.edu',
      requestedAt: '2024-01-13',
      status: 'pending',
      dnsVerified: true,
      sslRequired: false,
      reason: 'University policy requires .edu domain'
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="text-yellow-600 border-yellow-600">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        )
      case 'approved':
        return (
          <Badge variant="outline" className="text-green-600 border-green-600">
            <CheckCircle className="h-3 w-3 mr-1" />
            Approved
          </Badge>
        )
      case 'rejected':
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        )
      default:
        return null
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading domain requests...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Domain Requests</h1>
          <p className="text-gray-600 mt-1">Manage custom domain requests from institutes</p>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="text-lg px-3 py-1">
            <Globe className="h-4 w-4 mr-1" />
            {mockDomainRequests.length} Requests
          </Badge>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={clearError}
              className="ml-2"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search domain requests..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Domain Requests List */}
      <div className="grid gap-6">
        {mockDomainRequests.map((request) => (
          <Card key={request.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-blue-600" />
                    {request.instituteName}
                  </CardTitle>
                  <CardDescription className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-gray-500">Current:</span>
                      <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                        {request.currentDomain}
                      </code>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-gray-500">Requested:</span>
                      <code className="bg-blue-50 px-2 py-1 rounded text-xs text-blue-700">
                        {request.requestedDomain}
                      </code>
                      <ExternalLink className="h-3 w-3 text-gray-400" />
                    </div>
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Calendar className="h-4 w-4" />
                      Requested: {request.requestedAt}
                    </div>
                  </CardDescription>
                </div>
                {getStatusBadge(request.status)}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Technical Status */}
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${request.dnsVerified ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className="text-sm text-gray-600">
                      DNS {request.dnsVerified ? 'Verified' : 'Not Verified'}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className={`h-4 w-4 ${request.sslRequired ? 'text-blue-500' : 'text-gray-400'}`} />
                    <span className="text-sm text-gray-600">
                      SSL {request.sslRequired ? 'Required' : 'Not Required'}
                    </span>
                  </div>
                </div>

                {/* Reason */}
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Reason: </span>
                    {request.reason}
                  </p>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleViewDetails(request)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                  <div className="flex items-center gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleReject(request.id)}
                      className="text-red-600 border-red-600 hover:bg-red-50"
                    >
                      <XCircle className="h-4 w-4 mr-1" />
                      Reject
                    </Button>
                    <Button 
                      size="sm"
                      onClick={() => handleApprove(request.id)}
                      className="bg-green-600 hover:bg-green-700"
                      disabled={!request.dnsVerified}
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Approve
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {mockDomainRequests.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Domain Requests</h3>
            <p className="text-gray-600">No custom domain requests have been submitted yet.</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
