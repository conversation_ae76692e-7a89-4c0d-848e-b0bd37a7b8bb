'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useInstituteManagementStore } from '@/stores/super-admin/useInstituteManagementStore'
import { InstituteFilters } from '@/components/institute-management/InstituteFilters'
import { InstitutesList } from '@/components/institute-management/InstitutesList'
import { InstituteCards } from '@/components/institute-management/InstituteCards'
import { InstituteFormSimple as InstituteForm } from '@/components/institute-management/InstituteFormSimple'
import { InstituteStatistics } from '@/components/institute-management/InstituteStatistics'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Building2,
  Plus,
  List,
  Grid3X3,
  Download,
  Upload,
  Al<PERSON><PERSON>riangle,
  Loader2,
  <PERSON><PERSON><PERSON>3,
  <PERSON><PERSON>ir<PERSON>
} from 'lucide-react'

export default function InstituteManagementPage() {
  const router = useRouter()
  const [authChecked, setAuthChecked] = useState(false)
  const [activeTab, setActiveTab] = useState('institutes')

  const { user, isAuthenticated, isLoading, initialize } = useAuthStore()
  const {
    institutes,
    isLoading: storeLoading,
    error,
    viewMode,
    showCreateForm,
    showEditForm,
    selectedInstitute,
    statistics,
    fetchInstitutes,
    fetchStatistics,
    setViewMode,
    setShowCreateForm,
    setShowEditForm,
    clearError
  } = useInstituteManagementStore()

  // Initialize auth
  useEffect(() => {
    const timer = setTimeout(() => {
      initialize()
    }, 100)
    return () => clearTimeout(timer)
  }, [initialize])

  // Check authentication
  useEffect(() => {
    if (!isLoading) {
      setAuthChecked(true)

      if (!isAuthenticated) {
        router.push('/auth/admin/login')
        return
      }

      if (!user || user.legacyRole !== 'super_admin') {
        router.push('/auth/admin/login')
        return
      }
    }
  }, [user, isAuthenticated, isLoading, router])

  // Load data
  useEffect(() => {
    if (authChecked && isAuthenticated && user) {
      fetchInstitutes()
      fetchStatistics()
    }
  }, [authChecked, isAuthenticated, user])

  if (isLoading || !authChecked) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!isAuthenticated || !user || user.legacyRole !== 'super_admin') {
    return null
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Building2 className="h-8 w-8" />
            Institute Management
          </h1>
          <p className="text-muted-foreground">
            Manage institutes, create admin accounts, and monitor activities
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode(viewMode === 'list' ? 'cards' : 'list')}
          >
            {viewMode === 'list' ? <Grid3X3 className="h-4 w-4" /> : <List className="h-4 w-4" />}
            {viewMode === 'list' ? 'Card View' : 'List View'}
          </Button>
          <Button onClick={() => setShowCreateForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Institute
          </Button>
        </div>
      </div>

      {/* Statistics */}
      {statistics && <InstituteStatistics statistics={statistics} />}

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Tabbed Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="institutes" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Institutes
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Institutes Tab */}
        <TabsContent value="institutes" className="space-y-6">
          <InstituteFilters />

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Institutes ({institutes.length})</span>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setViewMode(viewMode === 'list' ? 'cards' : 'list')}
                  >
                    {viewMode === 'list' ? <Grid3X3 className="h-4 w-4" /> : <List className="h-4 w-4" />}
                    {viewMode === 'list' ? 'Card View' : 'List View'}
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Import
                  </Button>
                </div>
              </CardTitle>
              <CardDescription>
                Manage institute accounts and their administrators
              </CardDescription>
            </CardHeader>
            <CardContent>
              {storeLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  Loading institutes...
                </div>
              ) : institutes.length === 0 ? (
                <div className="text-center py-8">
                  <Building2 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No institutes found</h3>
                  <p className="text-gray-500 mb-4">Get started by creating your first institute.</p>
                  <Button onClick={() => setShowCreateForm(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Institute
                  </Button>
                </div>
              ) : viewMode === 'list' ? (
                <InstitutesList institutes={institutes} />
              ) : (
                <InstituteCards institutes={institutes} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₹45,231</div>
                <p className="text-xs text-muted-foreground">+20.1% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">+2350</div>
                <p className="text-xs text-muted-foreground">+180.1% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12,234</div>
                <p className="text-xs text-muted-foreground">+19% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">+573</div>
                <p className="text-xs text-muted-foreground">+201 since last hour</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Institute Performance</CardTitle>
              <CardDescription>Analytics and insights for all institutes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Dashboard</h3>
                <p className="text-gray-500">Detailed analytics and reporting features coming soon.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>


      </Tabs>

      {/* Create Form Modal */}
      {showCreateForm && (
        <InstituteForm
          isOpen={showCreateForm}
          onClose={() => setShowCreateForm(false)}
          mode="create"
        />
      )}

      {/* Edit Form Modal */}
      {showEditForm && selectedInstitute && (
        <InstituteForm
          isOpen={showEditForm}
          onClose={() => setShowEditForm(false)}
          mode="edit"
          institute={selectedInstitute}
        />
      )}
    </div>
  )
}
