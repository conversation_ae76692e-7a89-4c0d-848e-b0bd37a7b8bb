'use client'

import { useEffect, useState } from 'react'
import { useInstituteStore } from '@/stores/institute/useInstituteStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Clock, 
  CheckCircle,
  XCircle,
  Eye,
  FileText,
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  AlertTriangle,
  Filter,
  Search
} from 'lucide-react'

export default function PendingVerificationPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedInstitute, setSelectedInstitute] = useState<any>(null)
  
  const {
    pendingInstitutes,
    isLoading,
    error,
    fetchPendingInstitutes,
    approveInstitute,
    rejectInstitute,
    clearError
  } = useInstituteStore()

  useEffect(() => {
    fetchPendingInstitutes()
  }, [fetchPendingInstitutes])

  const handleApprove = async (instituteId: string) => {
    try {
      await approveInstitute(instituteId)
      // Show success message
    } catch (error) {
      console.error('Failed to approve institute:', error)
    }
  }

  const handleReject = async (instituteId: string) => {
    try {
      await rejectInstitute(instituteId)
      // Show success message
    } catch (error) {
      console.error('Failed to reject institute:', error)
    }
  }

  const handleViewDetails = (institute: any) => {
    setSelectedInstitute(institute)
  }

  const mockPendingInstitutes = [
    {
      id: '1',
      name: 'Tech Excellence Academy',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      address: 'Mumbai, Maharashtra',
      submittedAt: '2024-01-15',
      documentsCount: 5,
      status: 'pending'
    },
    {
      id: '2', 
      name: 'Global Learning Institute',
      email: '<EMAIL>',
      phone: '+91 8765432109',
      address: 'Bangalore, Karnataka',
      submittedAt: '2024-01-14',
      documentsCount: 7,
      status: 'pending'
    },
    {
      id: '3',
      name: 'Future Skills University',
      email: '<EMAIL>',
      phone: '+91 7654321098',
      address: 'Delhi, NCR',
      submittedAt: '2024-01-13',
      documentsCount: 6,
      status: 'pending'
    }
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading pending institutes...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Pending Verification</h1>
          <p className="text-gray-600 mt-1">Review and approve institute registration requests</p>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="text-lg px-3 py-1">
            <Clock className="h-4 w-4 mr-1" />
            {mockPendingInstitutes.length} Pending
          </Badge>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={clearError}
              className="ml-2"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search pending institutes..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Pending Institutes List */}
      <div className="grid gap-6">
        {mockPendingInstitutes.map((institute) => (
          <Card key={institute.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-blue-600" />
                    {institute.name}
                  </CardTitle>
                  <CardDescription className="space-y-1">
                    <div className="flex items-center gap-4 text-sm">
                      <span className="flex items-center gap-1">
                        <Mail className="h-4 w-4" />
                        {institute.email}
                      </span>
                      <span className="flex items-center gap-1">
                        <Phone className="h-4 w-4" />
                        {institute.phone}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-sm">
                      <span className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        {institute.address}
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        Submitted: {institute.submittedAt}
                      </span>
                    </div>
                  </CardDescription>
                </div>
                <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                  <Clock className="h-3 w-3 mr-1" />
                  Pending
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1 text-sm text-gray-600">
                    <FileText className="h-4 w-4" />
                    {institute.documentsCount} Documents
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleViewDetails(institute)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleReject(institute.id)}
                    className="text-red-600 border-red-600 hover:bg-red-50"
                  >
                    <XCircle className="h-4 w-4 mr-1" />
                    Reject
                  </Button>
                  <Button 
                    size="sm"
                    onClick={() => handleApprove(institute.id)}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Approve
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {mockPendingInstitutes.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Pending Verifications</h3>
            <p className="text-gray-600">All institute registration requests have been processed.</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
