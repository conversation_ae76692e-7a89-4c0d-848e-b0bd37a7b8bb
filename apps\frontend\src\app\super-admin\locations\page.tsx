'use client'

import { useState, useEffect } from 'react'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { LocationFilters } from '@/components/locations/LocationFilters'
import { LocationTabs } from '@/components/locations/LocationTabs'
import { CountriesList } from '@/components/locations/CountriesList'
import { StatesList } from '@/components/locations/StatesList'
import { DistrictsList } from '@/components/locations/DistrictsList'
import { LocationBreadcrumb } from '@/components/locations/LocationBreadcrumb'
import { CountryForm } from '@/components/locations/CountryForm'
import { StateForm } from '@/components/locations/StateForm'
import { DistrictForm } from '@/components/locations/DistrictForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Download, Upload, AlertTriangle } from 'lucide-react'

export default function LocationManagementPage() {
  const [activeTab, setActiveTab] = useState<'countries' | 'states' | 'districts'>('countries')

  const {
    isLoading,
    error,
    selectedCountry,
    selectedState,
    fetchCountries,
    fetchStates,
    fetchDistricts,
    clearError
  } = useLocationStore()

  useEffect(() => {
    // Load initial data based on active tab
    switch (activeTab) {
      case 'countries':
        fetchCountries()
        break
      case 'states':
        if (selectedCountry) {
          fetchStates(selectedCountry.id)
        } else {
          // Load all states when no country is selected
          fetchStates()
        }
        break
      case 'districts':
        if (selectedState) {
          fetchDistricts(selectedState.id)
        } else if (selectedCountry) {
          fetchDistricts(undefined, selectedCountry.id)
        } else {
          // Load all districts when no country/state is selected
          fetchDistricts()
        }
        break
    }
  }, [activeTab, selectedCountry, selectedState])

  const handleTabChange = (tab: 'countries' | 'states' | 'districts') => {
    setActiveTab(tab)
    clearError()
  }

  const getTabTitle = () => {
    switch (activeTab) {
      case 'countries':
        return 'Countries Management'
      case 'states':
        return selectedCountry
          ? `States in ${selectedCountry.name}`
          : 'States Management'
      case 'districts':
        return selectedState
          ? `Districts in ${selectedState.name}`
          : selectedCountry
          ? `Districts in ${selectedCountry.name}`
          : 'Districts Management'
    }
  }



  const getTabDescription = () => {
    switch (activeTab) {
      case 'countries':
        return 'Manage countries and their basic information'
      case 'states':
        return selectedCountry
          ? `Manage states and provinces in ${selectedCountry.name}`
          : 'Manage states and provinces'
      case 'districts':
        return selectedState
          ? `Manage districts and cities in ${selectedState.name}`
          : selectedCountry
          ? `Manage districts and cities in ${selectedCountry.name}`
          : 'Manage districts and cities'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Location Management</h1>
          <p className="text-gray-600 mt-1">
            Manage countries, states, and districts for the platform
          </p>
        </div>

        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          {activeTab === 'countries' && (
            <CountryForm mode="create" onSuccess={() => fetchCountries()} />
          )}
          {activeTab === 'states' && selectedCountry && (
            <StateForm mode="create" onSuccess={() => fetchStates(selectedCountry.id)} />
          )}
          {activeTab === 'districts' && selectedState && (
            <DistrictForm mode="create" onSuccess={() => fetchDistricts(selectedState.id)} />
          )}
        </div>
      </div>

      {/* Breadcrumb */}
      <LocationBreadcrumb />

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>{getTabTitle()}</CardTitle>
          <p className="text-sm text-gray-600">{getTabDescription()}</p>
        </CardHeader>
        <CardContent>
          {/* Tabs */}
          <LocationTabs
            activeTab={activeTab}
            onTabChange={handleTabChange}
            selectedCountry={selectedCountry}
            selectedState={selectedState}
          />

          {/* Filters */}
          <LocationFilters activeTab={activeTab} />

          {/* Content based on active tab */}
          <div className="mt-6">
            {isLoading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <>
                {activeTab === 'countries' && <CountriesList />}
                {activeTab === 'states' && <StatesList />}
                {activeTab === 'districts' && <DistrictsList />}
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
