'use client'

import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { usePlatformBlogStore } from '@/stores/super-admin/usePlatformBlogStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { RichTextEditor } from '@/components/ui/rich-text-editor'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft,
  Save,
  Send,
  Calendar,
  X,
  Plus
} from 'lucide-react'
import Link from 'next/link'
import { toast } from 'sonner'

const validationSchema = Yup.object({
  title: Yup.string()
    .required('Title is required')
    .max(200, 'Title must be less than 200 characters'),
  excerpt: Yup.string()
    .max(300, 'Excerpt must be less than 300 characters'),
  content: Yup.string()
    .required('Content is required'),
  targetAudience: Yup.array()
    .min(1, 'At least one target audience is required'),
  category: Yup.string(),
  isAnnouncement: Yup.boolean(),
  announcementPriority: Yup.string()
    .when('isAnnouncement', {
      is: true,
      then: (schema) => schema.required('Priority is required for announcements')
    }),
  allowComments: Yup.boolean(),
  isFeatured: Yup.boolean(),
  isSticky: Yup.boolean(),
  showOnDashboard: Yup.boolean(),
  seoTitle: Yup.string()
    .max(150, 'SEO title must be less than 150 characters'),
  seoDescription: Yup.string()
    .max(300, 'SEO description must be less than 300 characters')
})

export default function EditPlatformBlogPostPage() {
  const params = useParams()
  const router = useRouter()
  const { 
    currentPost, 
    postsLoading, 
    categories, 
    fetchPost, 
    fetchCategories, 
    updatePost 
  } = usePlatformBlogStore()
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState('')
  const [scheduledDate, setScheduledDate] = useState('')

  const postId = params.id as string

  // Fetch post and categories on component mount
  useEffect(() => {
    if (postId) {
      fetchPost(postId)
      fetchCategories()
    }
  }, [postId, fetchPost, fetchCategories])

  // Initialize form when post is loaded
  useEffect(() => {
    if (currentPost) {
      formik.setValues({
        title: currentPost.title || '',
        excerpt: currentPost.excerpt || '',
        content: currentPost.content || '',
        targetAudience: currentPost.targetAudience || ['public'],
        category: currentPost.category?.id || '',
        isAnnouncement: currentPost.isAnnouncement || false,
        announcementPriority: currentPost.announcementPriority || 'low',
        allowComments: currentPost.settings?.allowComments ?? true,
        isFeatured: currentPost.settings?.isFeatured || false,
        isSticky: currentPost.settings?.isSticky || false,
        showOnDashboard: currentPost.settings?.showOnDashboard || false,
        seoTitle: currentPost.seo?.title || '',
        seoDescription: currentPost.seo?.description || ''
      })
      
      // Set tags
      if (currentPost.tags) {
        setTags(currentPost.tags.map(tagObj => tagObj.tag))
      }
      
      // Set scheduled date if exists
      if (currentPost.scheduledFor) {
        const date = new Date(currentPost.scheduledFor)
        setScheduledDate(date.toISOString().slice(0, 16))
      }
    }
  }, [currentPost])

  const formik = useFormik({
    initialValues: {
      title: '',
      excerpt: '',
      content: '',
      targetAudience: ['public'],
      category: '',
      isAnnouncement: false,
      announcementPriority: 'low',
      allowComments: true,
      isFeatured: false,
      isSticky: false,
      showOnDashboard: false,
      seoTitle: '',
      seoDescription: ''
    },
    validationSchema,
    onSubmit: async (values) => {
      // This will be handled by the action buttons
    }
  })

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleUpdate = async (status?: string) => {
    setIsSubmitting(true)
    try {
      const postData = {
        ...formik.values,
        tags: tags.map(tag => ({ tag })),
        status: status || currentPost?.status,
        seo: {
          title: formik.values.seoTitle,
          description: formik.values.seoDescription
        },
        settings: {
          allowComments: formik.values.allowComments,
          isFeatured: formik.values.isFeatured,
          isSticky: formik.values.isSticky,
          showOnDashboard: formik.values.showOnDashboard
        }
      }

      if (status === 'published') {
        postData.publishedAt = new Date().toISOString()
      }

      if (status === 'scheduled' && scheduledDate) {
        postData.scheduledFor = scheduledDate
      }

      await updatePost(postId, postData)
      router.push(`/super-admin/platform-blog/${postId}`)
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSchedule = async () => {
    if (!scheduledDate) {
      toast.error('Please select a schedule date')
      return
    }
    await handleUpdate('scheduled')
  }

  if (postsLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading post...</p>
        </div>
      </div>
    )
  }

  if (!currentPost) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Post Not Found</h2>
          <p className="text-muted-foreground mb-4">The requested blog post could not be found.</p>
          <Link href="/super-admin/platform-blog">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Posts
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/super-admin/platform-blog/${postId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Post
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Edit Platform Blog Post</h1>
            <p className="text-muted-foreground">
              Update your platform-wide blog post or announcement
            </p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => handleUpdate()}
            disabled={isSubmitting}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
          {currentPost.status !== 'published' && (
            <Button 
              onClick={() => handleUpdate('published')}
              disabled={isSubmitting}
            >
              <Send className="h-4 w-4 mr-2" />
              Publish Now
            </Button>
          )}
        </div>
      </div>

      <form onSubmit={formik.handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  name="title"
                  value={formik.values.title}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="Enter post title..."
                  className={formik.touched.title && formik.errors.title ? 'border-red-500' : ''}
                />
                {formik.touched.title && formik.errors.title && (
                  <p className="text-sm text-red-500 mt-1">{formik.errors.title}</p>
                )}
              </div>

              <div>
                <Label htmlFor="excerpt">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  name="excerpt"
                  value={formik.values.excerpt}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="Brief summary for previews..."
                  rows={3}
                  className={formik.touched.excerpt && formik.errors.excerpt ? 'border-red-500' : ''}
                />
                {formik.touched.excerpt && formik.errors.excerpt && (
                  <p className="text-sm text-red-500 mt-1">{formik.errors.excerpt}</p>
                )}
              </div>

              <div>
                <Label htmlFor="content">Content *</Label>
                <RichTextEditor
                  value={formik.values.content}
                  onChange={(value) => formik.setFieldValue('content', value)}
                  placeholder="Write your blog post content..."
                  rows={12}
                  className={formik.touched.content && formik.errors.content ? 'border-red-500' : ''}
                />
                {formik.touched.content && formik.errors.content && (
                  <p className="text-sm text-red-500 mt-1">{formik.errors.content}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* SEO Settings */}
          <Card>
            <CardHeader>
              <CardTitle>SEO Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="seoTitle">SEO Title</Label>
                <Input
                  id="seoTitle"
                  name="seoTitle"
                  value={formik.values.seoTitle}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="SEO optimized title..."
                  className={formik.touched.seoTitle && formik.errors.seoTitle ? 'border-red-500' : ''}
                />
                {formik.touched.seoTitle && formik.errors.seoTitle && (
                  <p className="text-sm text-red-500 mt-1">{formik.errors.seoTitle}</p>
                )}
              </div>

              <div>
                <Label htmlFor="seoDescription">SEO Description</Label>
                <Textarea
                  id="seoDescription"
                  name="seoDescription"
                  value={formik.values.seoDescription}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="SEO meta description..."
                  rows={3}
                  className={formik.touched.seoDescription && formik.errors.seoDescription ? 'border-red-500' : ''}
                />
                {formik.touched.seoDescription && formik.errors.seoDescription && (
                  <p className="text-sm text-red-500 mt-1">{formik.errors.seoDescription}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Publishing Options */}
          <Card>
            <CardHeader>
              <CardTitle>Publishing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formik.values.category}
                  onValueChange={(value) => formik.setFieldValue('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Target Audience *</Label>
                <div className="space-y-2 mt-2">
                  {[
                    { value: 'institute_admin', label: 'Institute Admins' },
                    { value: 'student', label: 'Students' },
                    { value: 'staff', label: 'Staff' },
                    { value: 'public', label: 'Public' }
                  ].map((audience) => (
                    <div key={audience.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={audience.value}
                        checked={formik.values.targetAudience.includes(audience.value)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            formik.setFieldValue('targetAudience', [...formik.values.targetAudience, audience.value])
                          } else {
                            formik.setFieldValue('targetAudience', formik.values.targetAudience.filter(a => a !== audience.value))
                          }
                        }}
                      />
                      <Label htmlFor={audience.value}>{audience.label}</Label>
                    </div>
                  ))}
                </div>
                {formik.touched.targetAudience && formik.errors.targetAudience && (
                  <p className="text-sm text-red-500 mt-1">{formik.errors.targetAudience}</p>
                )}
              </div>

              <div className="border-t pt-4">
                <Label htmlFor="scheduledDate">Schedule for Later</Label>
                <Input
                  id="scheduledDate"
                  type="datetime-local"
                  value={scheduledDate}
                  onChange={(e) => setScheduledDate(e.target.value)}
                  className="mt-2"
                />
                {scheduledDate && (
                  <Button
                    onClick={handleSchedule}
                    disabled={isSubmitting}
                    className="w-full mt-2"
                    variant="outline"
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule Post
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Announcement Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Announcement Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isAnnouncement"
                  checked={formik.values.isAnnouncement}
                  onCheckedChange={(checked) => formik.setFieldValue('isAnnouncement', checked)}
                />
                <Label htmlFor="isAnnouncement">Mark as Announcement</Label>
              </div>

              {formik.values.isAnnouncement && (
                <div>
                  <Label htmlFor="announcementPriority">Priority Level</Label>
                  <Select
                    value={formik.values.announcementPriority}
                    onValueChange={(value) => formik.setFieldValue('announcementPriority', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                  {formik.touched.announcementPriority && formik.errors.announcementPriority && (
                    <p className="text-sm text-red-500 mt-1">{formik.errors.announcementPriority}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tags */}
          <Card>
            <CardHeader>
              <CardTitle>Tags</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add tag..."
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault()
                      addTag()
                    }
                  }}
                />
                <Button type="button" onClick={addTag} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Post Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Post Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="allowComments"
                  checked={formik.values.allowComments}
                  onCheckedChange={(checked) => formik.setFieldValue('allowComments', checked)}
                />
                <Label htmlFor="allowComments">Allow Comments</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isFeatured"
                  checked={formik.values.isFeatured}
                  onCheckedChange={(checked) => formik.setFieldValue('isFeatured', checked)}
                />
                <Label htmlFor="isFeatured">Featured Post</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isSticky"
                  checked={formik.values.isSticky}
                  onCheckedChange={(checked) => formik.setFieldValue('isSticky', checked)}
                />
                <Label htmlFor="isSticky">Pin to Top</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="showOnDashboard"
                  checked={formik.values.showOnDashboard}
                  onCheckedChange={(checked) => formik.setFieldValue('showOnDashboard', checked)}
                />
                <Label htmlFor="showOnDashboard">Show on Institute Dashboards</Label>
              </div>
            </CardContent>
          </Card>
        </div>
      </form>
    </div>
  )
}
