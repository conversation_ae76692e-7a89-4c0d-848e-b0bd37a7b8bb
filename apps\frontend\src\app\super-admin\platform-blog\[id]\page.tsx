'use client'

import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { usePlatformBlogStore } from '@/stores/super-admin/usePlatformBlogStore'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft,
  Edit,
  Trash2,
  Send,
  Calendar,
  Eye,
  Heart,
  Share2,
  Clock,
  User,
  Tag,
  Users
} from 'lucide-react'
import Link from 'next/link'

export default function PlatformBlogPostDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { 
    currentPost, 
    postsLoading, 
    fetchPost, 
    deletePost, 
    publishPost 
  } = usePlatformBlogStore()
  
  const [isDeleting, setIsDeleting] = useState(false)
  const [isPublishing, setIsPublishing] = useState(false)

  const postId = params.id as string

  useEffect(() => {
    if (postId) {
      fetchPost(postId)
    }
  }, [postId, fetchPost])

  const handleDelete = async () => {
    if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
      setIsDeleting(true)
      try {
        await deletePost(postId)
        router.push('/super-admin/platform-blog')
      } catch (error) {
        // Error handled in store
      } finally {
        setIsDeleting(false)
      }
    }
  }

  const handlePublish = async () => {
    setIsPublishing(true)
    try {
      await publishPost(postId)
    } catch (error) {
      // Error handled in store
    } finally {
      setIsPublishing(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'secondary',
      published: 'default',
      scheduled: 'outline',
      archived: 'destructive'
    }
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>
  }

  const getAnnouncementBadge = (priority: string) => {
    const variants = {
      low: 'secondary',
      medium: 'default',
      high: 'destructive',
      critical: 'destructive'
    }
    return <Badge variant={variants[priority] || 'secondary'}>📢 {priority}</Badge>
  }

  if (postsLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading post...</p>
        </div>
      </div>
    )
  }

  if (!currentPost) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Post Not Found</h2>
          <p className="text-muted-foreground mb-4">The requested blog post could not be found.</p>
          <Link href="/super-admin/platform-blog">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Posts
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/super-admin/platform-blog">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Posts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">{currentPost.title}</h1>
            <p className="text-muted-foreground">
              Platform Blog Post Details
            </p>
          </div>
        </div>
        
        <div className="flex gap-2">
          {currentPost.status === 'draft' && (
            <Button 
              onClick={handlePublish}
              disabled={isPublishing}
            >
              <Send className="h-4 w-4 mr-2" />
              Publish Now
            </Button>
          )}
          <Link href={`/super-admin/platform-blog/${postId}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Button 
            variant="destructive" 
            onClick={handleDelete}
            disabled={isDeleting}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Post Content */}
          <Card>
            <CardHeader>
              <div className="flex flex-wrap gap-2 mb-4">
                {getStatusBadge(currentPost.status)}
                {currentPost.isAnnouncement && currentPost.announcementPriority && 
                  getAnnouncementBadge(currentPost.announcementPriority)
                }
                {currentPost.category && (
                  <Badge variant="outline">{currentPost.category.name}</Badge>
                )}
                {currentPost.settings?.isFeatured && (
                  <Badge variant="outline">⭐ Featured</Badge>
                )}
                {currentPost.settings?.isSticky && (
                  <Badge variant="outline">📌 Pinned</Badge>
                )}
              </div>
              <CardTitle className="text-2xl">{currentPost.title}</CardTitle>
              {currentPost.excerpt && (
                <p className="text-lg text-muted-foreground">{currentPost.excerpt}</p>
              )}
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{
                  __html: currentPost.content
                    // Convert markdown-like syntax to HTML
                    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
                    .replace(/\*(.*)\*/gim, '<em>$1</em>')
                    .replace(/`(.*)`/gim, '<code>$1</code>')
                    .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')
                    .replace(/\n/gim, '<br>')
                    .replace(/^- (.*$)/gim, '<li>$1</li>')
                    .replace(/^1\. (.*$)/gim, '<li>$1</li>')
                    .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
                }} />
              </div>
            </CardContent>
          </Card>

          {/* Tags */}
          {currentPost.tags && currentPost.tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tag className="h-5 w-5" />
                  Tags
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {currentPost.tags.map((tagObj, index) => (
                    <Badge key={index} variant="secondary">
                      {tagObj.tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* SEO Information */}
          {currentPost.seo && (currentPost.seo.title || currentPost.seo.description) && (
            <Card>
              <CardHeader>
                <CardTitle>SEO Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {currentPost.seo.title && (
                  <div>
                    <h4 className="font-medium mb-1">SEO Title</h4>
                    <p className="text-muted-foreground">{currentPost.seo.title}</p>
                  </div>
                )}
                {currentPost.seo.description && (
                  <div>
                    <h4 className="font-medium mb-1">SEO Description</h4>
                    <p className="text-muted-foreground">{currentPost.seo.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Post Information */}
          <Card>
            <CardHeader>
              <CardTitle>Post Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2 text-sm">
                <User className="h-4 w-4" />
                <span>By {currentPost.author?.firstName} {currentPost.author?.lastName}</span>
              </div>
              
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4" />
                <span>Created {new Date(currentPost.createdAt).toLocaleDateString()}</span>
              </div>

              {currentPost.publishedAt && (
                <div className="flex items-center gap-2 text-sm">
                  <Send className="h-4 w-4" />
                  <span>Published {new Date(currentPost.publishedAt).toLocaleDateString()}</span>
                </div>
              )}

              {currentPost.scheduledFor && (
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4" />
                  <span>Scheduled for {new Date(currentPost.scheduledFor).toLocaleDateString()}</span>
                </div>
              )}

              {currentPost.analytics?.readingTime && (
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4" />
                  <span>{currentPost.analytics.readingTime} min read</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Target Audience */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Target Audience
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {currentPost.targetAudience.map((audience) => (
                  <Badge key={audience} variant="outline">
                    {audience.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Analytics */}
          {currentPost.analytics && (
            <Card>
              <CardHeader>
                <CardTitle>Analytics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <span className="text-sm">Views</span>
                  </div>
                  <span className="font-medium">{currentPost.analytics.viewCount}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Heart className="h-4 w-4" />
                    <span className="text-sm">Likes</span>
                  </div>
                  <span className="font-medium">{currentPost.analytics.likeCount}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Share2 className="h-4 w-4" />
                    <span className="text-sm">Shares</span>
                  </div>
                  <span className="font-medium">{currentPost.analytics.shareCount}</span>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Audience Breakdown</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Institute Admins</span>
                      <span>{currentPost.analytics.instituteAdminViews}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Students</span>
                      <span>{currentPost.analytics.studentViews}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Staff</span>
                      <span>{currentPost.analytics.staffViews}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Public</span>
                      <span>{currentPost.analytics.publicViews}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Post Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Comments</span>
                <Badge variant={currentPost.settings?.allowComments ? 'default' : 'secondary'}>
                  {currentPost.settings?.allowComments ? 'Enabled' : 'Disabled'}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Featured</span>
                <Badge variant={currentPost.settings?.isFeatured ? 'default' : 'secondary'}>
                  {currentPost.settings?.isFeatured ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Pinned</span>
                <Badge variant={currentPost.settings?.isSticky ? 'default' : 'secondary'}>
                  {currentPost.settings?.isSticky ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Dashboard</span>
                <Badge variant={currentPost.settings?.showOnDashboard ? 'default' : 'secondary'}>
                  {currentPost.settings?.showOnDashboard ? 'Shown' : 'Hidden'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
