'use client'

import React, { useEffect, useState } from 'react'
import { usePlatformBlogStore } from '@/stores/super-admin/usePlatformBlogStore'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  Eye,
  Heart,
  Share2,
  Users,
  FileText,
  Calendar,
  Target,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'

export default function PlatformBlogAnalyticsPage() {
  const {
    analytics,
    analyticsLoading,
    fetchAnalytics,
    posts,
    fetchPosts
  } = usePlatformBlogStore()

  const [selectedPeriod, setSelectedPeriod] = useState('30d')

  useEffect(() => {
    fetchAnalytics(selectedPeriod)
    fetchPosts()
  }, [selectedPeriod, fetchAnalytics, fetchPosts])

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period)
    fetchAnalytics(period)
  }

  const getTopPerformingPosts = () => {
    return posts
      .filter(post => post.analytics?.viewCount > 0)
      .sort((a, b) => (b.analytics?.viewCount || 0) - (a.analytics?.viewCount || 0))
      .slice(0, 5)
  }

  const getAudienceEngagement = () => {
    const total = analytics.totalViews
    if (total === 0) return []

    return [
      {
        audience: 'Institute Admins',
        views: analytics.instituteAdminViews,
        percentage: ((analytics.instituteAdminViews / total) * 100).toFixed(1)
      },
      {
        audience: 'Students',
        views: analytics.studentViews,
        percentage: ((analytics.studentViews / total) * 100).toFixed(1)
      },
      {
        audience: 'Staff',
        views: analytics.staffViews,
        percentage: ((analytics.staffViews / total) * 100).toFixed(1)
      },
      {
        audience: 'Public',
        views: analytics.publicViews,
        percentage: ((analytics.publicViews / total) * 100).toFixed(1)
      }
    ].sort((a, b) => b.views - a.views)
  }

  const topPosts = getTopPerformingPosts()
  const audienceData = getAudienceEngagement()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/super-admin/platform-blog">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Posts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Platform Blog Analytics</h1>
            <p className="text-muted-foreground">
              Comprehensive analytics for platform blog performance
            </p>
          </div>
        </div>
        
        <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
            <SelectItem value="1y">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {analyticsLoading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading analytics...</p>
        </div>
      ) : (
        <>
          {/* Overview Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalViews.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.totalUniqueViews.toLocaleString()} unique views
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalPosts}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.publishedPosts} published
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Engagement</CardTitle>
                <Heart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalLikes}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.totalShares} shares
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Announcements</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.announcementPosts}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.scheduledPosts} scheduled
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Content Performance */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Performing Posts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Top Performing Posts
                </CardTitle>
              </CardHeader>
              <CardContent>
                {topPosts.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">
                    No posts with analytics data yet
                  </p>
                ) : (
                  <div className="space-y-4">
                    {topPosts.map((post, index) => (
                      <div key={post.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                            <h4 className="font-medium text-sm line-clamp-1">{post.title}</h4>
                          </div>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Eye className="h-3 w-3" />
                              {post.analytics?.viewCount}
                            </span>
                            <span className="flex items-center gap-1">
                              <Heart className="h-3 w-3" />
                              {post.analytics?.likeCount}
                            </span>
                            <span className="flex items-center gap-1">
                              <Share2 className="h-3 w-3" />
                              {post.analytics?.shareCount}
                            </span>
                          </div>
                        </div>
                        <Badge variant={post.isAnnouncement ? 'default' : 'secondary'}>
                          {post.isAnnouncement ? 'Announcement' : 'Post'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Audience Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Audience Engagement
                </CardTitle>
              </CardHeader>
              <CardContent>
                {audienceData.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">
                    No audience data available yet
                  </p>
                ) : (
                  <div className="space-y-4">
                    {audienceData.map((audience) => (
                      <div key={audience.audience} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{audience.audience}</span>
                          <span className="text-sm text-muted-foreground">
                            {audience.views} views ({audience.percentage}%)
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${audience.percentage}%` }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Post Status Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Content Status Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{analytics.publishedPosts}</div>
                  <p className="text-sm text-muted-foreground">Published</p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{analytics.draftPosts}</div>
                  <p className="text-sm text-muted-foreground">Drafts</p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{analytics.scheduledPosts}</div>
                  <p className="text-sm text-muted-foreground">Scheduled</p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{analytics.announcementPosts}</div>
                  <p className="text-sm text-muted-foreground">Announcements</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Engagement Rate</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Based on likes and shares relative to views
                  </p>
                  <div className="text-2xl font-bold">
                    {analytics.totalViews > 0 
                      ? (((analytics.totalLikes + analytics.totalShares) / analytics.totalViews) * 100).toFixed(1)
                      : '0'
                    }%
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Average Reading Time</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Estimated reading time across all posts
                  </p>
                  <div className="text-2xl font-bold">
                    {posts.length > 0 
                      ? Math.round(posts.reduce((acc, post) => acc + (post.analytics?.readingTime || 0), 0) / posts.length)
                      : 0
                    } min
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
