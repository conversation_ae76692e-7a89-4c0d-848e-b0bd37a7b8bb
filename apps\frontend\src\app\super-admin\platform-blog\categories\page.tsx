'use client'

import React, { useEffect, useState, memo } from 'react'
import { usePlatformBlogStore } from '@/stores/super-admin/usePlatformBlogStore'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Plus,
  Edit,
  Trash2,
  ArrowLeft,
  Palette,
  Tag
} from 'lucide-react'
import Link from 'next/link'

const validationSchema = Yup.object({
  name: Yup.string()
    .required('Category name is required')
    .max(100, 'Name must be less than 100 characters'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  color: Yup.string()
    .matches(/^#[0-9A-F]{6}$/i, 'Color must be a valid hex color'),
  icon: Yup.string()
    .max(50, 'Icon name must be less than 50 characters')
})

// CategoryForm component moved outside to prevent re-creation on each render
// Using React.memo to prevent unnecessary re-renders
const CategoryForm = memo(({ formik, isEdit = false, onCancel }) => (
  <form onSubmit={formik.handleSubmit} className="space-y-4">
    <div>
      <Label htmlFor="name">Category Name *</Label>
      <Input
        id="name"
        name="name"
        value={formik.values.name}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        placeholder="Enter category name..."
        className={formik.touched.name && formik.errors.name ? 'border-red-500' : ''}
        autoComplete="off"
      />
      {formik.touched.name && formik.errors.name && (
        <p className="text-sm text-red-500 mt-1">{formik.errors.name}</p>
      )}
    </div>

    <div>
      <Label htmlFor="description">Description</Label>
      <Textarea
        id="description"
        name="description"
        value={formik.values.description}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        placeholder="Enter category description..."
        rows={3}
        className={formik.touched.description && formik.errors.description ? 'border-red-500' : ''}
        autoComplete="off"
      />
      {formik.touched.description && formik.errors.description && (
        <p className="text-sm text-red-500 mt-1">{formik.errors.description}</p>
      )}
    </div>

    <div>
      <Label htmlFor="color">Color *</Label>
      <div className="flex items-center gap-2">
        <input
          id="color"
          name="color"
          type="color"
          value={formik.values.color}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          className="w-16 h-10 p-1 border rounded"
        />
        <Input
          name="color"
          value={formik.values.color}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          placeholder="#3B82F6"
          className={formik.touched.color && formik.errors.color ? 'border-red-500' : ''}
          autoComplete="off"
        />
      </div>
      {formik.touched.color && formik.errors.color && (
        <p className="text-sm text-red-500 mt-1">{formik.errors.color}</p>
      )}
    </div>

    <div>
      <Label htmlFor="icon">Icon Name</Label>
      <Input
        id="icon"
        name="icon"
        value={formik.values.icon}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        placeholder="e.g., BookOpen, Code, etc."
        className={formik.touched.icon && formik.errors.icon ? 'border-red-500' : ''}
        autoComplete="off"
      />
      {formik.touched.icon && formik.errors.icon && (
        <p className="text-sm text-red-500 mt-1">{formik.errors.icon}</p>
      )}
    </div>

    <div className="flex justify-end gap-2 pt-4">
      <Button
        type="button"
        variant="outline"
        onClick={onCancel}
      >
        Cancel
      </Button>
      <Button type="submit" disabled={formik.isSubmitting}>
        {formik.isSubmitting ? 'Saving...' : (isEdit ? 'Update Category' : 'Create Category')}
      </Button>
    </div>
  </form>
))

export default function PlatformBlogCategoriesPage() {
  const {
    categories,
    categoriesLoading,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory
  } = usePlatformBlogStore()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  const createFormik = useFormik({
    initialValues: {
      name: '',
      description: '',
      color: '#3B82F6',
      icon: ''
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      setIsSubmitting(true)
      try {
        await createCategory(values)
        resetForm()
        setIsCreateDialogOpen(false)
      } catch (error) {
        // Error handled in store
      } finally {
        setIsSubmitting(false)
      }
    }
  })

  const editFormik = useFormik({
    initialValues: {
      name: '',
      description: '',
      color: '#3B82F6',
      icon: ''
    },
    validationSchema,
    onSubmit: async (values) => {
      if (!editingCategory) return

      setIsSubmitting(true)
      try {
        await updateCategory(editingCategory.id, values)
        setIsEditDialogOpen(false)
        setEditingCategory(null)
      } catch (error) {
        // Error handled in store
      } finally {
        setIsSubmitting(false)
      }
    }
  })

  const handleEdit = (category) => {
    setEditingCategory(category)
    editFormik.setValues({
      name: category.name || '',
      description: category.description || '',
      color: category.color || '#3B82F6',
      icon: category.icon || ''
    })
    setIsEditDialogOpen(true)
  }

  const handleDelete = async (categoryId: string) => {
    if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      await deleteCategory(categoryId)
    }
  }



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/super-admin/platform-blog">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Posts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Platform Blog Categories</h1>
            <p className="text-muted-foreground">
              Manage categories for platform blog posts
            </p>
          </div>
        </div>
        
        <Dialog
          open={isCreateDialogOpen}
          onOpenChange={(open) => {
            setIsCreateDialogOpen(open)
            if (!open) {
              // Reset form when dialog closes
              setTimeout(() => {
                createFormik.resetForm()
              }, 100)
            }
          }}
        >
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Category
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Category</DialogTitle>
            </DialogHeader>
            <CategoryForm
              key="create-form"
              formik={createFormik}
              onCancel={() => setIsCreateDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Categories Grid */}
      {categoriesLoading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading categories...</p>
        </div>
      ) : categories.length === 0 ? (
        <div className="text-center py-12">
          <Tag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No Categories Found</h3>
          <p className="text-muted-foreground mb-4">
            Create your first category to organize your platform blog posts.
          </p>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Category
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <Card key={category.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {category.color && (
                      <div 
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                    )}
                    <CardTitle className="text-lg">{category.name}</CardTitle>
                  </div>
                  <div className="flex gap-1">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleEdit(category)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleDelete(category.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {category.description && (
                  <p className="text-muted-foreground mb-3">{category.description}</p>
                )}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {category.icon && (
                      <Badge variant="outline" className="text-xs">
                        {category.icon}
                      </Badge>
                    )}
                    <Badge variant={category.isActive ? 'default' : 'secondary'}>
                      {category.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  {category.color && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Palette className="h-3 w-3" />
                      {category.color}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
          </DialogHeader>
          <CategoryForm
            key={`edit-form-${editingCategory?.id || 'new'}`}
            formik={editFormik}
            isEdit={true}
            onCancel={() => {
              setIsEditDialogOpen(false)
              setEditingCategory(null)
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
