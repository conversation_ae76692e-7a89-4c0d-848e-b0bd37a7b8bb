'use client'

import { useEffect, useState } from 'react'
import { usePlatformBlogStore } from '@/stores/super-admin/usePlatformBlogStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Eye,
  Edit,
  Trash2,
  Calendar,
  Users,
  TrendingUp,
  Tag
} from 'lucide-react'
import Link from 'next/link'

export default function PlatformBlogManagementPage() {
  const {
    posts,
    categories,
    postsLoading,
    analytics,
    viewMode,
    selectedStatus,
    selectedCategory,
    selectedTargetAudience,
    showAnnouncementsOnly,
    fetchPosts,
    fetchCategories,
    fetchAnalytics,
    setFilters,
    clearFilters,
    setViewMode,
    deletePost
  } = usePlatformBlogStore()

  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    fetchPosts()
    fetchCategories()
    fetchAnalytics()
  }, [fetchPosts, fetchCategories, fetchAnalytics])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchPosts({ search: searchQuery })
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters({ [key]: value === 'all' ? null : value })
    fetchPosts()
  }

  const handleDeletePost = async (postId: string) => {
    if (confirm('Are you sure you want to delete this post?')) {
      await deletePost(postId)
    }
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      draft: 'secondary',
      published: 'default',
      scheduled: 'outline',
      archived: 'destructive'
    }
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>
  }

  const getAnnouncementBadge = (priority: string) => {
    const variants: Record<string, any> = {
      low: 'secondary',
      medium: 'default',
      high: 'destructive',
      critical: 'destructive'
    }
    return <Badge variant={variants[priority] || 'secondary'}>📢 {priority}</Badge>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Platform Blog Management</h1>
          <p className="text-muted-foreground">
            Manage platform-wide blog posts and announcements
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/super-admin/platform-blog/categories">
            <Button variant="outline">
              <Tag className="h-4 w-4 mr-2" />
              Categories
            </Button>
          </Link>
          <Link href="/super-admin/platform-blog/create">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Post
            </Button>
          </Link>
        </div>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalPosts}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.publishedPosts} published
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalViews.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.totalUniqueViews.toLocaleString()} unique
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Announcements</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.announcementPosts}</div>
            <p className="text-xs text-muted-foreground">
              Active announcements
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.scheduledPosts}</div>
            <p className="text-xs text-muted-foreground">
              Posts scheduled
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search posts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </form>
        
        <div className="flex gap-2">
          <Select value={selectedStatus || 'all'} onValueChange={(value) => handleFilterChange('status', value)}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="scheduled">Scheduled</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedCategory || 'all'} onValueChange={(value) => handleFilterChange('category', value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={selectedTargetAudience || 'all'} onValueChange={(value) => handleFilterChange('targetAudience', value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Audience" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Audiences</SelectItem>
              <SelectItem value="institute_admin">Institute Admins</SelectItem>
              <SelectItem value="student">Students</SelectItem>
              <SelectItem value="staff">Staff</SelectItem>
              <SelectItem value="public">Public</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={clearFilters}>
            <Filter className="h-4 w-4 mr-2" />
            Clear
          </Button>
          
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'card' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('card')}
            >
              <Grid className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Posts List/Grid */}
      {postsLoading ? (
        <div className="text-center py-8">Loading posts...</div>
      ) : posts.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No posts found</p>
        </div>
      ) : (
        <div className={viewMode === 'card' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {posts.map((post) => (
            <Card key={post.id} className={viewMode === 'list' ? 'p-6' : ''}>
              <CardHeader className={viewMode === 'list' ? 'p-0 pb-4' : ''}>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{post.title}</CardTitle>
                    {post.excerpt && (
                      <p className="text-sm text-muted-foreground mt-2">{post.excerpt}</p>
                    )}
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Link href={`/super-admin/platform-blog/${post.id}`}>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link href={`/super-admin/platform-blog/${post.id}/edit`}>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleDeletePost(post.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className={viewMode === 'list' ? 'p-0' : ''}>
                <div className="flex flex-wrap gap-2 mb-3">
                  {getStatusBadge(post.status)}
                  {post.isAnnouncement && post.announcementPriority && 
                    getAnnouncementBadge(post.announcementPriority)
                  }
                  {post.category && (
                    <Badge variant="outline">{post.category.name}</Badge>
                  )}
                </div>
                <div className="flex justify-between items-center text-sm text-muted-foreground">
                  <span>By {post.author?.firstName} {post.author?.lastName}</span>
                  <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                </div>
                {post.analytics && (
                  <div className="flex gap-4 mt-2 text-sm text-muted-foreground">
                    <span>{post.analytics.viewCount} views</span>
                    <span>{post.analytics.likeCount} likes</span>
                    <span>{post.analytics.readingTime}min read</span>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
