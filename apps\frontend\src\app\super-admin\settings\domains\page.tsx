'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { CheckCircle, XCircle, Clock, ExternalLink } from 'lucide-react'

interface DomainRequest {
  id: string
  instituteName: string
  requestedDomain: string
  currentDomain: string
  status: 'pending' | 'approved' | 'rejected' | 'active'
  requestedAt: string
  requestedBy: string
  notes?: string
  sslStatus?: 'pending' | 'active' | 'failed'
}

export default function DomainManagementPage() {
  const [domainRequests, setDomainRequests] = useState<DomainRequest[]>([])
  const [selectedRequest, setSelectedRequest] = useState<DomainRequest | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchDomainRequests()
  }, [])

  const fetchDomainRequests = async () => {
    try {
      const response = await fetch('/api/admin/domain-requests')
      const data = await response.json()
      setDomainRequests(data.requests || [])
    } catch (error) {
      toast.error('Failed to fetch domain requests')
    } finally {
      setIsLoading(false)
    }
  }

  const handleApproveRequest = async (requestId: string) => {
    try {
      const response = await fetch(`/api/admin/domain-requests/${requestId}/approve`, {
        method: 'POST'
      })

      if (response.ok) {
        toast.success('Domain Approved', {
          description: 'Domain request has been approved and SSL setup initiated.'
        })
        fetchDomainRequests()
      } else {
        throw new Error('Failed to approve domain')
      }
    } catch (error) {
      toast.error('Approval Failed', {
        description: 'Unable to approve domain request.'
      })
    }
  }

  const handleRejectRequest = async (requestId: string, reason: string) => {
    try {
      const response = await fetch(`/api/admin/domain-requests/${requestId}/reject`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reason })
      })

      if (response.ok) {
        toast.warning('Domain Rejected', {
          description: 'Domain request has been rejected.'
        })
        fetchDomainRequests()
      } else {
        throw new Error('Failed to reject domain')
      }
    } catch (error) {
      toast.error('Rejection Failed', {
        description: 'Unable to reject domain request.'
      })
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'default',
      approved: 'secondary',
      rejected: 'destructive',
      active: 'default'
    }
    return <Badge variant={variants[status as keyof typeof variants] as any}>{status}</Badge>
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'approved':
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  if (isLoading) {
    return <div>Loading domain requests...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Domain Management</h1>
          <p className="text-gray-600">Manage custom domain requests from institutes</p>
        </div>
        <Button>
          <ExternalLink className="h-4 w-4 mr-2" />
          DNS Configuration Guide
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Requests</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {domainRequests.filter(r => r.status === 'pending').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Domains</p>
                <p className="text-2xl font-bold text-green-600">
                  {domainRequests.filter(r => r.status === 'active').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approved</p>
                <p className="text-2xl font-bold text-blue-600">
                  {domainRequests.filter(r => r.status === 'approved').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rejected</p>
                <p className="text-2xl font-bold text-red-600">
                  {domainRequests.filter(r => r.status === 'rejected').length}
                </p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Domain Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle>Domain Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Institute</TableHead>
                <TableHead>Requested Domain</TableHead>
                <TableHead>Current Domain</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>SSL Status</TableHead>
                <TableHead>Requested Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {domainRequests.map((request) => (
                <TableRow key={request.id}>
                  <TableCell className="font-medium">{request.instituteName}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span>{request.requestedDomain}</span>
                      {request.status === 'active' && (
                        <ExternalLink className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-gray-500">{request.currentDomain}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(request.status)}
                      {getStatusBadge(request.status)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {request.sslStatus && getStatusBadge(request.sslStatus)}
                  </TableCell>
                  <TableCell>{new Date(request.requestedAt).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {request.status === 'pending' && (
                        <>
                          <Button
                            size="sm"
                            onClick={() => handleApproveRequest(request.id)}
                          >
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedRequest(request)}
                          >
                            Reject
                          </Button>
                        </>
                      )}
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button size="sm" variant="ghost">
                            View Details
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Domain Request Details</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label>Institute Name</Label>
                              <p className="text-sm text-gray-600">{request.instituteName}</p>
                            </div>
                            <div>
                              <Label>Requested Domain</Label>
                              <p className="text-sm text-gray-600">{request.requestedDomain}</p>
                            </div>
                            <div>
                              <Label>Requested By</Label>
                              <p className="text-sm text-gray-600">{request.requestedBy}</p>
                            </div>
                            {request.notes && (
                              <div>
                                <Label>Notes</Label>
                                <p className="text-sm text-gray-600">{request.notes}</p>
                              </div>
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
