'use client'

import { useState, useEffect } from 'react'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useSettingsStore } from '@/stores/settings/useSettingsStore'
import { settingsApi, type SettingCreationData } from '@/lib/api/settings'

const emailSettingsSchema = Yup.object({
  smtp_host: Yup.string().required('SMTP host is required'),
  smtp_port: Yup.number().min(1).max(65535).required('SMTP port is required'),
  smtp_username: Yup.string().required('SMTP username is required'),
  smtp_password: Yup.string().required('SMTP password is required'),
  smtp_encryption: Yup.string().required('SMTP encryption is required'),
  smtp_from_email: Yup.string().email('Invalid email').required('From email is required'),
  smtp_from_name: Yup.string().required('From name is required'),
  smtp_reply_to: Yup.string().email('Invalid email'),
  email_verification_enabled: Yup.boolean(),
  email_notifications_enabled: Yup.boolean(),
  email_queue_enabled: Yup.boolean(),
  email_template_header: Yup.string(),
  email_template_footer: Yup.string()
})

export default function EmailSettingsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [testEmailLoading, setTestEmailLoading] = useState(false)
  const [initialValues, setInitialValues] = useState({
    smtp_host: '',
    smtp_port: 587,
    smtp_username: '',
    smtp_password: '',
    smtp_encryption: 'tls',
    smtp_from_email: '',
    smtp_from_name: '',
    smtp_reply_to: '',
    email_verification_enabled: true,
    email_notifications_enabled: true,
    email_queue_enabled: false,
    email_template_header: '',
    email_template_footer: ''
  })
  const { bulkUpdateSettings, fetchSettingsByCategory } = useSettingsStore()

  // Load settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true)
        await fetchSettingsByCategory('email')

        // Fetch email settings to populate form
        const response = await settingsApi.getSettingsByCategory('email')

        // Convert settings array to form values
        const formValues = { ...initialValues }
        response.settings.forEach(setting => {
          if (setting.key in formValues) {
            if (setting.type === 'boolean') {
              (formValues as any)[setting.key] = setting.value === 'true'
            } else if (setting.type === 'number') {
              (formValues as any)[setting.key] = Number(setting.value)
            } else {
              (formValues as any)[setting.key] = setting.value
            }
          }
        })

        setInitialValues(formValues)
      } catch (error) {
        console.error('Error loading email settings:', error)
        toast.error('Failed to load email settings')
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, [])

  const handleSubmit = async (values: any) => {
    setIsSaving(true)
    try {
      // Convert form values to settings
      const settingsToUpdate: SettingCreationData[] = [
        {
          key: 'smtp_host',
          value: values.smtp_host,
          category: 'email',
          type: 'string',
          is_public: false
        },
        {
          key: 'smtp_port',
          value: values.smtp_port.toString(),
          category: 'email',
          type: 'number',
          is_public: false
        },
        {
          key: 'smtp_username',
          value: values.smtp_username,
          category: 'email',
          type: 'string',
          is_public: false
        },
        {
          key: 'smtp_password',
          value: values.smtp_password,
          category: 'email',
          type: 'string',
          is_public: false
        },
        {
          key: 'smtp_encryption',
          value: values.smtp_encryption,
          category: 'email',
          type: 'string',
          is_public: false
        },
        {
          key: 'smtp_from_email',
          value: values.smtp_from_email,
          category: 'email',
          type: 'email',
          is_public: false
        },
        {
          key: 'smtp_from_name',
          value: values.smtp_from_name,
          category: 'email',
          type: 'string',
          is_public: false
        },
        {
          key: 'smtp_reply_to',
          value: values.smtp_reply_to,
          category: 'email',
          type: 'email',
          is_public: false
        },
        {
          key: 'email_verification_enabled',
          value: values.email_verification_enabled.toString(),
          category: 'email',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'email_notifications_enabled',
          value: values.email_notifications_enabled.toString(),
          category: 'email',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'email_queue_enabled',
          value: values.email_queue_enabled.toString(),
          category: 'email',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'email_template_header',
          value: values.email_template_header,
          category: 'email',
          type: 'textarea',
          is_public: false
        },
        {
          key: 'email_template_footer',
          value: values.email_template_footer,
          category: 'email',
          type: 'textarea',
          is_public: false
        }
      ]

      await bulkUpdateSettings(settingsToUpdate)
      toast.success('Email settings saved successfully')
    } catch (error) {
      toast.error('Failed to save email settings')
      console.error(error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleTestEmail = async () => {
    setTestEmailLoading(true)
    try {
      // In a real implementation, this would send a test email
      await new Promise(resolve => setTimeout(resolve, 2000))
      toast.success('Test email sent successfully')
    } catch (error) {
      toast.error('Failed to send test email')
    } finally {
      setTestEmailLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="max-w-4xl mx-auto space-y-6">
          <div>
            <h1 className="text-2xl font-bold">Email & SMTP Settings</h1>
            <p className="text-muted-foreground">Loading settings...</p>
          </div>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Email & SMTP Settings</h1>
          <p className="text-muted-foreground">Configure email server and notification settings</p>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={emailSettingsSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ errors, touched, values, setFieldValue }) => (
            <Form className="space-y-6">
              {/* SMTP Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle>SMTP Configuration</CardTitle>
                  <CardDescription>Configure your email server settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="smtp_host">SMTP Host</Label>
                      <Field
                        as={Input}
                        id="smtp_host"
                        name="smtp_host"
                        placeholder="smtp.gmail.com"
                      />
                      {errors.smtp_host && touched.smtp_host && (
                        <p className="text-sm text-destructive">{errors.smtp_host}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="smtp_port">SMTP Port</Label>
                      <Field
                        as={Input}
                        id="smtp_port"
                        name="smtp_port"
                        type="number"
                        placeholder="587"
                      />
                      {errors.smtp_port && touched.smtp_port && (
                        <p className="text-sm text-destructive">{errors.smtp_port}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="smtp_username">SMTP Username</Label>
                      <Field
                        as={Input}
                        id="smtp_username"
                        name="smtp_username"
                        placeholder="<EMAIL>"
                      />
                      {errors.smtp_username && touched.smtp_username && (
                        <p className="text-sm text-destructive">{errors.smtp_username}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="smtp_password">SMTP Password</Label>
                      <Field
                        as={Input}
                        id="smtp_password"
                        name="smtp_password"
                        type="password"
                        placeholder="••••••••••••••••"
                      />
                      {errors.smtp_password && touched.smtp_password && (
                        <p className="text-sm text-destructive">{errors.smtp_password}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="smtp_encryption">Encryption</Label>
                      <Select
                        value={values.smtp_encryption}
                        onValueChange={(value) => setFieldValue('smtp_encryption', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select encryption" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          <SelectItem value="tls">TLS</SelectItem>
                          <SelectItem value="ssl">SSL</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.smtp_encryption && touched.smtp_encryption && (
                        <p className="text-sm text-destructive">{errors.smtp_encryption}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleTestEmail}
                      disabled={testEmailLoading}
                    >
                      {testEmailLoading ? 'Sending...' : 'Send Test Email'}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Email Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>Email Settings</CardTitle>
                  <CardDescription>Configure sender information and email behavior</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="smtp_from_email">From Email</Label>
                      <Field
                        as={Input}
                        id="smtp_from_email"
                        name="smtp_from_email"
                        type="email"
                        placeholder="<EMAIL>"
                      />
                      {errors.smtp_from_email && touched.smtp_from_email && (
                        <p className="text-sm text-destructive">{errors.smtp_from_email}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="smtp_from_name">From Name</Label>
                      <Field
                        as={Input}
                        id="smtp_from_name"
                        name="smtp_from_name"
                        placeholder="KISS LMS"
                      />
                      {errors.smtp_from_name && touched.smtp_from_name && (
                        <p className="text-sm text-destructive">{errors.smtp_from_name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="smtp_reply_to">Reply-To Email (Optional)</Label>
                      <Field
                        as={Input}
                        id="smtp_reply_to"
                        name="smtp_reply_to"
                        type="email"
                        placeholder="<EMAIL>"
                      />
                      {errors.smtp_reply_to && touched.smtp_reply_to && (
                        <p className="text-sm text-destructive">{errors.smtp_reply_to}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Enable Email Verification</Label>
                        <p className="text-sm text-muted-foreground">
                          Require email verification for new accounts
                        </p>
                      </div>
                      <Switch
                        checked={values.email_verification_enabled}
                        onCheckedChange={(checked) => setFieldValue('email_verification_enabled', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Enable Email Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Send system notifications via email
                        </p>
                      </div>
                      <Switch
                        checked={values.email_notifications_enabled}
                        onCheckedChange={(checked) => setFieldValue('email_notifications_enabled', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Enable Email Queue</Label>
                        <p className="text-sm text-muted-foreground">
                          Queue emails for background processing
                        </p>
                      </div>
                      <Switch
                        checked={values.email_queue_enabled}
                        onCheckedChange={(checked) => setFieldValue('email_queue_enabled', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Email Templates */}
              <Card>
                <CardHeader>
                  <CardTitle>Email Templates</CardTitle>
                  <CardDescription>Customize email template header and footer</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email_template_header">Email Header (HTML)</Label>
                    <Field
                      as={Textarea}
                      id="email_template_header"
                      name="email_template_header"
                      placeholder="<div style='text-align: center;'><img src='logo.png' alt='Logo'></div>"
                      rows={4}
                    />
                    <p className="text-sm text-muted-foreground">
                      HTML content to include at the top of all emails
                    </p>
                    {errors.email_template_header && touched.email_template_header && (
                      <p className="text-sm text-destructive">{errors.email_template_header}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email_template_footer">Email Footer (HTML)</Label>
                    <Field
                      as={Textarea}
                      id="email_template_footer"
                      name="email_template_footer"
                      placeholder="<p style='text-align: center; color: #666;'>© 2024 KISS LMS. All rights reserved.</p>"
                      rows={4}
                    />
                    <p className="text-sm text-muted-foreground">
                      HTML content to include at the bottom of all emails
                    </p>
                    {errors.email_template_footer && touched.email_template_footer && (
                      <p className="text-sm text-destructive">{errors.email_template_footer}</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? 'Saving...' : 'Save Email Settings'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  )
}
