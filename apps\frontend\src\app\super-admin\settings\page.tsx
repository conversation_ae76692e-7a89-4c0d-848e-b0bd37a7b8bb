'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import {
  Settings,
  Globe,
  Shield,
  Mail,
  Database,
  ArrowRight,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

// Settings overview data
const settingsOverview = [
  {
    title: 'Platform Settings',
    href: '/super-admin/settings/platform',
    icon: Globe,
    description: 'Core platform configuration including branding, contact info, and basic settings',
    status: 'configured',
    lastUpdated: '2 hours ago',
    category: 'Platform'
  },
  {
    title: 'Security Settings',
    href: '/super-admin/settings/security',
    icon: Shield,
    description: 'Authentication, session management, and security policies',
    status: 'needs_attention',
    lastUpdated: 'Never',
    category: 'Platform'
  },
  {
    title: 'Email & SMTP',
    href: '/super-admin/settings/email',
    icon: Mail,
    description: 'Email server configuration and notification templates',
    status: 'needs_attention',
    lastUpdated: 'Never',
    category: 'Platform'
  },
  {
    title: 'Storage & CDN',
    href: '/super-admin/settings/storage',
    icon: Database,
    description: 'File storage, media management, and CDN configuration',
    status: 'needs_attention',
    lastUpdated: 'Never',
    category: 'Platform'
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'configured':
      return 'text-green-600 bg-green-50 border-green-200'
    case 'needs_attention':
      return 'text-orange-600 bg-orange-50 border-orange-200'
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'configured':
      return CheckCircle
    case 'needs_attention':
      return AlertCircle
    default:
      return Settings
  }
}

export default function SettingsIndexPage() {
  return (
    <div className="p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Settings Overview</h1>
          <p className="text-muted-foreground mt-1">
            Configure and manage your platform settings
          </p>
        </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold">1</p>
                <p className="text-sm text-muted-foreground">Configured</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-2xl font-bold">3</p>
                <p className="text-sm text-muted-foreground">Need Attention</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">4</p>
                <p className="text-sm text-muted-foreground">Total Settings</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Globe className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">25%</p>
                <p className="text-sm text-muted-foreground">Completion</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Settings Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {settingsOverview.map((setting) => {
          const Icon = setting.icon
          const StatusIcon = getStatusIcon(setting.status)
          
          return (
            <Card key={setting.href} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Icon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{setting.title}</CardTitle>
                      <Badge variant="secondary" className="mt-1">
                        {setting.category}
                      </Badge>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded-full border ${getStatusColor(setting.status)}`}>
                    <StatusIcon className="h-3 w-3" />
                    <span className="text-xs font-medium capitalize">
                      {setting.status.replace('_', ' ')}
                    </span>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <CardDescription className="text-sm">
                  {setting.description}
                </CardDescription>
                
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">
                    Last updated: {setting.lastUpdated}
                  </div>
                  
                  <Link href={setting.href}>
                    <Button size="sm" className="group">
                      Configure
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Quick Actions */}
      <div className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common settings tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link href="/super-admin/settings/platform">
                <Button variant="outline" className="w-full justify-start">
                  <Globe className="mr-2 h-4 w-4" />
                  Update Platform Info
                </Button>
              </Link>
              
              <Link href="/super-admin/settings/security">
                <Button variant="outline" className="w-full justify-start">
                  <Shield className="mr-2 h-4 w-4" />
                  Configure Security
                </Button>
              </Link>
              
              <Link href="/super-admin/settings/email">
                <Button variant="outline" className="w-full justify-start">
                  <Mail className="mr-2 h-4 w-4" />
                  Setup Email
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
      </div>
    </div>
  )
}
