'use client'

import { useState, useEffect } from 'react'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import { useSettingsStore } from '@/stores/settings/useSettingsStore'
import { settingsApi, type SettingCreationData } from '@/lib/api/settings'
import { fileUploadAPI } from '@/lib/api/file-upload'
import { BrandingUpload } from '@/components/platform/branding-upload'

const platformSettingsSchema = Yup.object({
  platform_name: Yup.string().required('Platform name is required'),
  platform_url: Yup.string().url('Invalid URL').required('Platform URL is required'),
  support_email: Yup.string().email('Invalid email').required('Support email is required'),
  platform_address: Yup.string().nullable(),
  platform_tagline: Yup.string().nullable(),
  platform_logo: Yup.mixed().nullable(),
  platform_favicon: Yup.mixed().nullable(),
  maintenance_mode: Yup.boolean(),
  allow_registration: Yup.boolean(),
  require_email_verification: Yup.boolean()
})

export default function PlatformSettingsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  // Helper function to fetch media URL by ID
  const fetchMediaUrl = async (mediaId: string): Promise<string | null> => {
    try {
      console.log('🔍 Fetching media URL for ID:', mediaId)

      // Get auth token from localStorage or useAuthStore
      const token = localStorage.getItem('token') || localStorage.getItem('payload-token')
      console.log('🔑 Using auth token:', token ? 'Token found' : 'No token')

      // Use Payload's REST API to get media by ID
      const response = await fetch(`http://localhost:3001/api/media/${mediaId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      console.log('📡 Media fetch response status:', response.status)

      if (response.ok) {
        const media = await response.json()
        console.log('📦 Media data received:', media)

        if (media.url) {
          const fullUrl = fileUploadAPI.getFileUrl(media.url)
          console.log('✅ Media URL resolved:', fullUrl)
          return fullUrl
        } else {
          console.log('❌ No URL found in media data')
        }
      } else {
        console.log('❌ Media fetch failed:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('❌ Error fetching media URL:', error)
    }
    return null
  }
  const [initialValues, setInitialValues] = useState({
    platform_name: 'KISS LMS',
    platform_url: 'https://groups-exam.com',
    support_email: '<EMAIL>',
    platform_address: '',
    platform_tagline: 'Empowering Education Through Technology',
    platform_logo: null as File | null,
    platform_favicon: null as File | null,
    maintenance_mode: false,
    allow_registration: true,
    require_email_verification: true
  })
  const { fetchSettingsByCategory } = useSettingsStore()

  // Load settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true)
        await fetchSettingsByCategory('platform')

        // Fetch platform settings to populate form
        const response = await settingsApi.getSettingsByCategory('platform')

        // Convert settings array to form values
        const formValues = { ...initialValues }
        response.settings.forEach(setting => {
          if (setting.key in formValues) {
            if (setting.type === 'boolean') {
              (formValues as any)[setting.key] = setting.value === 'true'
            } else {
              (formValues as any)[setting.key] = setting.value
            }
          }

          // Skip logo and favicon as they're handled by BrandingUpload component
        })

        setInitialValues(formValues)
      } catch (error) {
        console.error('Error loading settings:', error)
        toast.error('Failed to load settings')
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, [])

  // Helper function to create or update a setting
  const createOrUpdateSetting = async (key: string, value: string) => {
    try {
      console.log(`🔄 Creating/updating setting: ${key} = "${value}"`)

      const result = await settingsApi.createOrUpdateSetting(key, value, {
        category: 'platform',
        type: 'media',
        description: key === 'platform_logo' ? 'Platform logo media ID' : 'Platform favicon media ID',
        is_public: true
      })

      console.log('✅ Setting operation result:', result)

      if (result.errors && result.errors.length > 0) {
        console.error('❌ Setting errors:', result.errors)
        throw new Error(result.errors[0].error)
      }

    } catch (error: any) {
      console.error('❌ Error in createOrUpdateSetting:', error)
      throw error
    }
  }

  // Logo and favicon upload is now handled by BrandingUpload component



  const handleSubmit = async (values: any) => {
    setIsSaving(true)
    try {
      // Convert form values to settings
      // Convert form values to settings - only include platform-specific settings
      const settingsToUpdate: SettingCreationData[] = []

      // Add required platform settings
      if (values.platform_name) {
        settingsToUpdate.push({
          key: 'platform_name',
          value: values.platform_name,
          category: 'platform',
          type: 'string',
          is_public: true
        })
      }

      if (values.platform_url) {
        settingsToUpdate.push({
          key: 'platform_url',
          value: values.platform_url,
          category: 'platform',
          type: 'url',
          is_public: true
        })
      }

      if (values.support_email) {
        settingsToUpdate.push({
          key: 'support_email',
          value: values.support_email,
          category: 'platform',
          type: 'email',
          is_public: true
        })
      }

      // Optional fields - only add if they have non-empty values
      if (values.platform_address && values.platform_address.trim() !== '') {
        settingsToUpdate.push({
          key: 'platform_address',
          value: values.platform_address,
          category: 'platform',
          type: 'textarea',
          is_public: true
        })
      }

      if (values.platform_tagline && values.platform_tagline.trim() !== '') {
        settingsToUpdate.push({
          key: 'platform_tagline',
          value: values.platform_tagline,
          category: 'platform',
          type: 'string',
          is_public: true
        })
      }

      settingsToUpdate.push(
        {
          key: 'platform_logo',
          value: values.platform_logo.toString(),
          category: 'platform',
          type: 'url',
          is_public: true
        },
        {
          key: 'platform_favicon',
          value: values.platform_favicon.toString(),
          category: 'platform',
          type: 'url',
          is_public: true
        }
      )

      // Boolean settings - always include
      settingsToUpdate.push(
        {
          key: 'maintenance_mode',
          value: values.maintenance_mode.toString(),
          category: 'platform',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'allow_registration',
          value: values.allow_registration.toString(),
          category: 'platform',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'require_email_verification',
          value: values.require_email_verification.toString(),
          category: 'platform',
          type: 'boolean',
          is_public: false
        }
      )

      // File uploads are handled immediately when files are selected
      // No need to handle them in form submission

      // Save settings using the API
      await settingsApi.bulkUpdateSettings(settingsToUpdate)

      toast.success('Platform settings saved successfully')
    } catch (error) {
      toast.error('Failed to save settings')
      console.error(error)
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="max-w-4xl mx-auto space-y-6">
          <div>
            <h1 className="text-2xl font-bold">Platform Settings</h1>
            <p className="text-muted-foreground">Loading settings...</p>
          </div>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Platform Settings</h1>
          <p className="text-muted-foreground">Configure global platform settings and policies</p>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={platformSettingsSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ errors, touched, values, setFieldValue, isValid }) => (
            <Form className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>General Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="platform_name">Platform Name</Label>
                      <Field
                        as={Input}
                        id="platform_name"
                        name="platform_name"
                        placeholder="Groups Exam LMS"
                      />
                      {errors.platform_name && touched.platform_name && (
                        <p className="text-sm text-destructive">{errors.platform_name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="platform_url">Platform URL</Label>
                      <Field
                        as={Input}
                        id="platform_url"
                        name="platform_url"
                        placeholder="https://groups-exam.com"
                      />
                      {errors.platform_url && touched.platform_url && (
                        <p className="text-sm text-destructive">{errors.platform_url}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="support_email">Support Email</Label>
                    <Field
                      as={Input}
                      id="support_email"
                      name="support_email"
                      type="email"
                      placeholder="<EMAIL>"
                    />
                    {errors.support_email && touched.support_email && (
                      <p className="text-sm text-destructive">{errors.support_email}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="platform_tagline">Platform Tagline</Label>
                    <Field
                      as={Input}
                      id="platform_tagline"
                      name="platform_tagline"
                      placeholder="Empowering Education Through Technology"
                    />
                    {errors.platform_tagline && touched.platform_tagline && (
                      <p className="text-sm text-destructive">{errors.platform_tagline}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="platform_address">Platform Address</Label>
                    <Field
                      as={Textarea}
                      id="platform_address"
                      name="platform_address"
                      placeholder="Enter your organization's physical address"
                      rows={3}
                    />
                    {errors.platform_address && touched.platform_address && (
                      <p className="text-sm text-destructive">{errors.platform_address}</p>
                    )}
                  </div>

                </CardContent>
              </Card>

              {/* Platform Branding Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Platform Branding</CardTitle>
                </CardHeader>
                <CardContent>
                  <BrandingUpload />
                </CardContent>
              </Card>

              {/* System Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle>System Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Maintenance Mode</Label>
                        <p className="text-sm text-gray-500">
                          Enable to put the platform in maintenance mode
                        </p>
                      </div>
                      <Switch
                        checked={values.maintenance_mode}
                        onCheckedChange={(checked) => setFieldValue('maintenance_mode', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Allow Institute Registration</Label>
                        <p className="text-sm text-gray-500">
                          Allow new institutes to register on the platform
                        </p>
                      </div>
                      <Switch
                        checked={values.allow_registration}
                        onCheckedChange={(checked) => setFieldValue('allow_registration', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Require Email Verification</Label>
                        <p className="text-sm text-gray-500">
                          Require email verification for new user accounts
                        </p>
                      </div>
                      <Switch
                        checked={values.require_email_verification}
                        onCheckedChange={(checked) => setFieldValue('require_email_verification', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>


              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline">
                  Reset to Defaults
                </Button>
                <div className="space-y-2">
                  {Object.keys(errors).length > 0 && (
                    <div className="text-sm text-destructive">
                      Form has validation errors: {Object.keys(errors).join(', ')}
                    </div>
                  )}
                  <Button
                    type="submit"
                    disabled={isSaving || !isValid}

                  >
                    {isSaving ? 'Saving...' : 'Save Settings'}
                  </Button>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  )
}
