'use client'

import { useState, useEffect } from 'react'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useSettingsStore } from '@/stores/settings/useSettingsStore'
import { settingsApi, type SettingCreationData } from '@/lib/api/settings'

const securitySettingsSchema = Yup.object({
  session_timeout: Yup.number().min(5).max(1440).required('Session timeout is required'),
  max_login_attempts: Yup.number().min(1).max(10).required('Max login attempts is required'),
  password_min_length: Yup.number().min(6).max(50).required('Password minimum length is required'),
  require_password_uppercase: Yup.boolean(),
  require_password_lowercase: Yup.boolean(),
  require_password_numbers: Yup.boolean(),
  require_password_symbols: Yup.boolean(),
  enable_two_factor: Yup.boolean(),
  enable_captcha: Yup.boolean(),
  account_lockout_duration: Yup.number().min(1).max(1440).required('Account lockout duration is required'),
  password_expiry_days: Yup.number().min(0).max(365),
  enable_password_history: Yup.boolean(),
  password_history_count: Yup.number().min(1).max(24)
})

export default function SecuritySettingsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [initialValues, setInitialValues] = useState({
    session_timeout: 60,
    max_login_attempts: 5,
    password_min_length: 8,
    require_password_uppercase: true,
    require_password_lowercase: true,
    require_password_numbers: true,
    require_password_symbols: false,
    enable_two_factor: false,
    enable_captcha: true,
    account_lockout_duration: 30,
    password_expiry_days: 90,
    enable_password_history: true,
    password_history_count: 5
  })
  const { bulkUpdateSettings, fetchSettingsByCategory } = useSettingsStore()

  // Load settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true)
        await fetchSettingsByCategory('security')

        // Fetch security settings to populate form
        const response = await settingsApi.getSettingsByCategory('security')

        // Convert settings array to form values
        const formValues = { ...initialValues }
        response.settings.forEach(setting => {
          if (setting.key in formValues) {
            if (setting.type === 'boolean') {
              (formValues as any)[setting.key] = setting.value === 'true'
            } else if (setting.type === 'number') {
              (formValues as any)[setting.key] = Number(setting.value)
            } else {
              (formValues as any)[setting.key] = setting.value
            }
          }
        })

        setInitialValues(formValues)
      } catch (error) {
        console.error('Error loading security settings:', error)
        toast.error('Failed to load security settings')
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, [])

  const handleSubmit = async (values: any) => {
    setIsSaving(true)
    try {
      // Convert form values to settings
      const settingsToUpdate: SettingCreationData[] = [
        {
          key: 'session_timeout',
          value: values.session_timeout.toString(),
          category: 'security',
          type: 'number',
          is_public: false
        },
        {
          key: 'max_login_attempts',
          value: values.max_login_attempts.toString(),
          category: 'security',
          type: 'number',
          is_public: false
        },
        {
          key: 'password_min_length',
          value: values.password_min_length.toString(),
          category: 'security',
          type: 'number',
          is_public: false
        },
        {
          key: 'require_password_uppercase',
          value: values.require_password_uppercase.toString(),
          category: 'security',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'require_password_lowercase',
          value: values.require_password_lowercase.toString(),
          category: 'security',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'require_password_numbers',
          value: values.require_password_numbers.toString(),
          category: 'security',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'require_password_symbols',
          value: values.require_password_symbols.toString(),
          category: 'security',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'enable_two_factor',
          value: values.enable_two_factor.toString(),
          category: 'security',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'enable_captcha',
          value: values.enable_captcha.toString(),
          category: 'security',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'account_lockout_duration',
          value: values.account_lockout_duration.toString(),
          category: 'security',
          type: 'number',
          is_public: false
        },
        {
          key: 'password_expiry_days',
          value: values.password_expiry_days.toString(),
          category: 'security',
          type: 'number',
          is_public: false
        },
        {
          key: 'enable_password_history',
          value: values.enable_password_history.toString(),
          category: 'security',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'password_history_count',
          value: values.password_history_count.toString(),
          category: 'security',
          type: 'number',
          is_public: false
        }
      ]

      await bulkUpdateSettings(settingsToUpdate)
      toast.success('Security settings saved successfully')
    } catch (error) {
      toast.error('Failed to save security settings')
      console.error(error)
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="max-w-4xl mx-auto space-y-6">
          <div>
            <h1 className="text-2xl font-bold">Security Settings</h1>
            <p className="text-muted-foreground">Loading settings...</p>
          </div>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Security Settings</h1>
          <p className="text-muted-foreground">Configure authentication and security policies</p>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={securitySettingsSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ errors, touched, values, setFieldValue }) => (
            <Form className="space-y-6">
              {/* Authentication Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>Authentication</CardTitle>
                  <CardDescription>Configure login and session settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="session_timeout">Session Timeout (minutes)</Label>
                      <Field
                        as={Input}
                        id="session_timeout"
                        name="session_timeout"
                        type="number"
                        min="5"
                        max="1440"
                      />
                      {errors.session_timeout && touched.session_timeout && (
                        <p className="text-sm text-destructive">{errors.session_timeout}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="max_login_attempts">Max Login Attempts</Label>
                      <Field
                        as={Input}
                        id="max_login_attempts"
                        name="max_login_attempts"
                        type="number"
                        min="1"
                        max="10"
                      />
                      {errors.max_login_attempts && touched.max_login_attempts && (
                        <p className="text-sm text-destructive">{errors.max_login_attempts}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="account_lockout_duration">Account Lockout Duration (minutes)</Label>
                      <Field
                        as={Input}
                        id="account_lockout_duration"
                        name="account_lockout_duration"
                        type="number"
                        min="1"
                        max="1440"
                      />
                      {errors.account_lockout_duration && touched.account_lockout_duration && (
                        <p className="text-sm text-destructive">{errors.account_lockout_duration}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Enable Two-Factor Authentication</Label>
                        <p className="text-sm text-muted-foreground">
                          Require 2FA for all user accounts
                        </p>
                      </div>
                      <Switch
                        checked={values.enable_two_factor}
                        onCheckedChange={(checked) => setFieldValue('enable_two_factor', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Enable CAPTCHA</Label>
                        <p className="text-sm text-muted-foreground">
                          Show CAPTCHA on login forms
                        </p>
                      </div>
                      <Switch
                        checked={values.enable_captcha}
                        onCheckedChange={(checked) => setFieldValue('enable_captcha', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Password Policy */}
              <Card>
                <CardHeader>
                  <CardTitle>Password Policy</CardTitle>
                  <CardDescription>Configure password requirements and policies</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="password_min_length">Minimum Password Length</Label>
                      <Field
                        as={Input}
                        id="password_min_length"
                        name="password_min_length"
                        type="number"
                        min="6"
                        max="50"
                      />
                      {errors.password_min_length && touched.password_min_length && (
                        <p className="text-sm text-destructive">{errors.password_min_length}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password_expiry_days">Password Expiry (days, 0 = never)</Label>
                      <Field
                        as={Input}
                        id="password_expiry_days"
                        name="password_expiry_days"
                        type="number"
                        min="0"
                        max="365"
                      />
                      {errors.password_expiry_days && touched.password_expiry_days && (
                        <p className="text-sm text-destructive">{errors.password_expiry_days}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Require Uppercase Letters</Label>
                      <Switch
                        checked={values.require_password_uppercase}
                        onCheckedChange={(checked) => setFieldValue('require_password_uppercase', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label>Require Lowercase Letters</Label>
                      <Switch
                        checked={values.require_password_lowercase}
                        onCheckedChange={(checked) => setFieldValue('require_password_lowercase', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label>Require Numbers</Label>
                      <Switch
                        checked={values.require_password_numbers}
                        onCheckedChange={(checked) => setFieldValue('require_password_numbers', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label>Require Special Characters</Label>
                      <Switch
                        checked={values.require_password_symbols}
                        onCheckedChange={(checked) => setFieldValue('require_password_symbols', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Enable Password History</Label>
                        <p className="text-sm text-muted-foreground">
                          Prevent reusing recent passwords
                        </p>
                      </div>
                      <Switch
                        checked={values.enable_password_history}
                        onCheckedChange={(checked) => setFieldValue('enable_password_history', checked)}
                      />
                    </div>

                    {values.enable_password_history && (
                      <div className="space-y-2">
                        <Label htmlFor="password_history_count">Password History Count</Label>
                        <Field
                          as={Input}
                          id="password_history_count"
                          name="password_history_count"
                          type="number"
                          min="1"
                          max="24"
                        />
                        {errors.password_history_count && touched.password_history_count && (
                          <p className="text-sm text-destructive">{errors.password_history_count}</p>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? 'Saving...' : 'Save Security Settings'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  )
}
