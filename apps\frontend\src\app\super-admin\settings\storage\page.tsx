'use client'

import { useState, useEffect } from 'react'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useSettingsStore } from '@/stores/settings/useSettingsStore'
import { settingsApi, type SettingCreationData } from '@/lib/api/settings'

const storageSettingsSchema = Yup.object({
  storage_provider: Yup.string().required('Storage provider is required'),
  s3_bucket_name: Yup.string().when('storage_provider', {
    is: 's3',
    then: (schema) => schema.required('S3 bucket name is required'),
    otherwise: (schema) => schema
  }),
  s3_region: Yup.string().when('storage_provider', {
    is: 's3',
    then: (schema) => schema.required('S3 region is required'),
    otherwise: (schema) => schema
  }),
  s3_access_key: Yup.string().when('storage_provider', {
    is: 's3',
    then: (schema) => schema.required('S3 access key is required'),
    otherwise: (schema) => schema
  }),
  s3_secret_key: Yup.string().when('storage_provider', {
    is: 's3',
    then: (schema) => schema.required('S3 secret key is required'),
    otherwise: (schema) => schema
  }),
  s3_endpoint: Yup.string().url('Invalid URL format'),
  cdn_enabled: Yup.boolean(),
  cdn_url: Yup.string().when('cdn_enabled', {
    is: true,
    then: (schema) => schema.url('Invalid CDN URL').required('CDN URL is required'),
    otherwise: (schema) => schema
  }),
  max_file_size: Yup.number().min(1).max(1024).required('Max file size is required'),
  allowed_file_types: Yup.string().required('Allowed file types is required')
})

export default function StorageSettingsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [initialValues, setInitialValues] = useState({
    storage_provider: 's3',
    s3_bucket_name: '',
    s3_region: 'us-east-1',
    s3_access_key: '',
    s3_secret_key: '',
    s3_endpoint: '',
    cdn_enabled: false,
    cdn_url: '',
    max_file_size: 10,
    allowed_file_types: 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx'
  })
  const { bulkUpdateSettings, fetchSettingsByCategory } = useSettingsStore()

  // Load settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true)
        await fetchSettingsByCategory('storage')

        // Fetch storage settings to populate form
        const response = await settingsApi.getSettingsByCategory('storage')

        // Convert settings array to form values
        const formValues = { ...initialValues }
        response.settings.forEach(setting => {
          if (setting.key in formValues) {
            if (setting.type === 'boolean') {
              (formValues as any)[setting.key] = setting.value === 'true'
            } else if (setting.type === 'number') {
              (formValues as any)[setting.key] = Number(setting.value)
            } else {
              (formValues as any)[setting.key] = setting.value
            }
          }
        })

        setInitialValues(formValues)
      } catch (error) {
        console.error('Error loading storage settings:', error)
        toast.error('Failed to load storage settings')
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, [])

  const handleSubmit = async (values: any) => {
    setIsSaving(true)
    try {
      // Convert form values to settings
      const settingsToUpdate: SettingCreationData[] = [
        {
          key: 'storage_provider',
          value: values.storage_provider,
          category: 'storage',
          type: 'string',
          is_public: false
        },
        {
          key: 's3_bucket_name',
          value: values.s3_bucket_name,
          category: 'storage',
          type: 'string',
          is_public: false
        },
        {
          key: 's3_region',
          value: values.s3_region,
          category: 'storage',
          type: 'string',
          is_public: false
        },
        {
          key: 's3_access_key',
          value: values.s3_access_key,
          category: 'storage',
          type: 'string',
          is_public: false
        },
        {
          key: 's3_secret_key',
          value: values.s3_secret_key,
          category: 'storage',
          type: 'string',
          is_public: false
        },
        {
          key: 's3_endpoint',
          value: values.s3_endpoint,
          category: 'storage',
          type: 'url',
          is_public: false
        },
        {
          key: 'cdn_enabled',
          value: values.cdn_enabled.toString(),
          category: 'storage',
          type: 'boolean',
          is_public: false
        },
        {
          key: 'cdn_url',
          value: values.cdn_url,
          category: 'storage',
          type: 'url',
          is_public: false
        },
        {
          key: 'max_file_size',
          value: values.max_file_size.toString(),
          category: 'storage',
          type: 'number',
          is_public: false
        },
        {
          key: 'allowed_file_types',
          value: values.allowed_file_types,
          category: 'storage',
          type: 'string',
          is_public: false
        }
      ]

      await bulkUpdateSettings(settingsToUpdate)
      toast.success('Storage settings saved successfully')
    } catch (error) {
      toast.error('Failed to save storage settings')
      console.error(error)
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="max-w-4xl mx-auto space-y-6">
          <div>
            <h1 className="text-2xl font-bold">Storage & CDN Settings</h1>
            <p className="text-muted-foreground">Loading settings...</p>
          </div>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Storage & CDN Settings</h1>
          <p className="text-muted-foreground">Configure file storage and content delivery settings</p>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={storageSettingsSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ errors, touched, values, setFieldValue }) => (
            <Form className="space-y-6">
              {/* Storage Provider */}
              <Card>
                <CardHeader>
                  <CardTitle>Storage Provider</CardTitle>
                  <CardDescription>Configure your file storage provider</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="storage_provider">Storage Provider</Label>
                    <Select
                      value={values.storage_provider}
                      onValueChange={(value) => setFieldValue('storage_provider', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select storage provider" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="s3">Amazon S3</SelectItem>
                        <SelectItem value="local">Local Storage</SelectItem>
                        <SelectItem value="gcs">Google Cloud Storage</SelectItem>
                        <SelectItem value="azure">Azure Blob Storage</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.storage_provider && touched.storage_provider && (
                      <p className="text-sm text-destructive">{errors.storage_provider}</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* S3 Configuration */}
              {values.storage_provider === 's3' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Amazon S3 Configuration</CardTitle>
                    <CardDescription>Configure your Amazon S3 bucket settings</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="s3_bucket_name">Bucket Name</Label>
                        <Field
                          as={Input}
                          id="s3_bucket_name"
                          name="s3_bucket_name"
                          placeholder="my-app-bucket"
                        />
                        {errors.s3_bucket_name && touched.s3_bucket_name && (
                          <p className="text-sm text-destructive">{errors.s3_bucket_name}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="s3_region">Region</Label>
                        <Select
                          value={values.s3_region}
                          onValueChange={(value) => setFieldValue('s3_region', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select region" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="us-east-1">US East (N. Virginia)</SelectItem>
                            <SelectItem value="us-west-1">US West (N. California)</SelectItem>
                            <SelectItem value="us-west-2">US West (Oregon)</SelectItem>
                            <SelectItem value="eu-west-1">Europe (Ireland)</SelectItem>
                            <SelectItem value="eu-central-1">Europe (Frankfurt)</SelectItem>
                            <SelectItem value="ap-southeast-1">Asia Pacific (Singapore)</SelectItem>
                            <SelectItem value="ap-south-1">Asia Pacific (Mumbai)</SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.s3_region && touched.s3_region && (
                          <p className="text-sm text-destructive">{errors.s3_region}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="s3_access_key">Access Key ID</Label>
                        <Field
                          as={Input}
                          id="s3_access_key"
                          name="s3_access_key"
                          type="password"
                          placeholder="AKIA..."
                        />
                        {errors.s3_access_key && touched.s3_access_key && (
                          <p className="text-sm text-destructive">{errors.s3_access_key}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="s3_secret_key">Secret Access Key</Label>
                        <Field
                          as={Input}
                          id="s3_secret_key"
                          name="s3_secret_key"
                          type="password"
                          placeholder="••••••••••••••••"
                        />
                        {errors.s3_secret_key && touched.s3_secret_key && (
                          <p className="text-sm text-destructive">{errors.s3_secret_key}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="s3_endpoint">Custom Endpoint (Optional)</Label>
                      <Field
                        as={Input}
                        id="s3_endpoint"
                        name="s3_endpoint"
                        placeholder="https://s3.amazonaws.com"
                      />
                      <p className="text-sm text-muted-foreground">
                        Leave empty to use default AWS S3 endpoint
                      </p>
                      {errors.s3_endpoint && touched.s3_endpoint && (
                        <p className="text-sm text-destructive">{errors.s3_endpoint}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* CDN Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle>CDN Configuration</CardTitle>
                  <CardDescription>Configure content delivery network settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Enable CDN</Label>
                      <p className="text-sm text-muted-foreground">
                        Use CDN for faster file delivery
                      </p>
                    </div>
                    <Switch
                      checked={values.cdn_enabled}
                      onCheckedChange={(checked) => setFieldValue('cdn_enabled', checked)}
                    />
                  </div>

                  {values.cdn_enabled && (
                    <div className="space-y-2">
                      <Label htmlFor="cdn_url">CDN URL</Label>
                      <Field
                        as={Input}
                        id="cdn_url"
                        name="cdn_url"
                        placeholder="https://cdn.example.com"
                      />
                      <p className="text-sm text-muted-foreground">
                        Your CDN distribution URL (e.g., CloudFront, CloudFlare)
                      </p>
                      {errors.cdn_url && touched.cdn_url && (
                        <p className="text-sm text-destructive">{errors.cdn_url}</p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* File Upload Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>File Upload Settings</CardTitle>
                  <CardDescription>Configure file upload restrictions and policies</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="max_file_size">Max File Size (MB)</Label>
                      <Field
                        as={Input}
                        id="max_file_size"
                        name="max_file_size"
                        type="number"
                        min="1"
                        max="1024"
                      />
                      {errors.max_file_size && touched.max_file_size && (
                        <p className="text-sm text-destructive">{errors.max_file_size}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="allowed_file_types">Allowed File Types</Label>
                    <Field
                      as={Textarea}
                      id="allowed_file_types"
                      name="allowed_file_types"
                      placeholder="jpg,jpeg,png,gif,pdf,doc,docx"
                      rows={3}
                    />
                    <p className="text-sm text-muted-foreground">
                      Comma-separated list of allowed file extensions
                    </p>
                    {errors.allowed_file_types && touched.allowed_file_types && (
                      <p className="text-sm text-destructive">{errors.allowed_file_types}</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? 'Saving...' : 'Save Storage Settings'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  )
}
