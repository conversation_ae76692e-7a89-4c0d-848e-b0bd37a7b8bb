'use client'

import { useEffect, useState, useRef } from 'react'
import { useTaxStore } from '@/stores/tax/useTaxStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { TaxComponentsList } from '@/components/tax/TaxComponentsList'
import { TaxGroupsList } from '@/components/tax/TaxGroupsList'
import { TaxRulesList } from '@/components/tax/TaxRulesList'
import { TaxCalculator } from '@/components/tax/TaxCalculator'


import { TaxFilters } from '@/components/tax/TaxFilters'
import { TaxComponentForm } from '@/components/tax/TaxComponentForm'
import { TaxGroupForm } from '@/components/tax/TaxGroupForm'
import { TaxRuleForm } from '@/components/tax/TaxRuleForm'
import { 
  Calculator, 
  Settings, 
  FileText, 
  Users, 
  AlertTriangle,
  Plus,
  Download,
  Upload
} from 'lucide-react'

export default function TaxManagementPage() {
  const [activeTab, setActiveTab] = useState('components')
  const [createComponentOpen, setCreateComponentOpen] = useState(false)
  const [createGroupOpen, setCreateGroupOpen] = useState(false)
  const [createRuleOpen, setCreateRuleOpen] = useState(false)
  const lastFetchedTab = useRef('')

  // Use a single selector to prevent multiple subscriptions
  const {
    taxComponents,
    taxGroups,
    taxRules,
    isLoading,
    error,
    filters,
    fetchTaxComponents,
    fetchTaxGroups,
    fetchTaxRules,
    clearError
  } = useTaxStore()

  useEffect(() => {
    // Clear any previous errors when switching tabs
    if (error) {
      clearError()
    }

    // Only fetch if we haven't already fetched for this tab or if it's the initial load
    if (lastFetchedTab.current !== activeTab) {
      lastFetchedTab.current = activeTab

      // Use setTimeout to prevent immediate re-renders
      const timeoutId = setTimeout(() => {
        switch (activeTab) {
          case 'components':
            fetchTaxComponents()
            break
          case 'groups':
            fetchTaxGroups()
            break
          case 'rules':
            fetchTaxRules()
            break
        }
      }, 0)

      return () => clearTimeout(timeoutId)
    }
  }, [activeTab])

  // Handle filter changes - only call API for current active tab
  useEffect(() => {
    // Skip initial render and only respond to filter changes
    if (lastFetchedTab.current === activeTab) {
      const timeoutId = setTimeout(() => {
        switch (activeTab) {
          case 'components':
            fetchTaxComponents()
            break
          case 'groups':
            fetchTaxGroups()
            break
          case 'rules':
            fetchTaxRules()
            break
        }
      }, 300) // Debounce filter changes

      return () => clearTimeout(timeoutId)
    }
  }, [filters, activeTab])

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    clearError()
  }

  const handleAddNew = () => {
    switch (activeTab) {
      case 'components':
        setCreateComponentOpen(true)
        break
      case 'groups':
        setCreateGroupOpen(true)
        break
      case 'rules':
        setCreateRuleOpen(true)
        break
    }
  }

  const getTabStats = () => {
    return {
      components: {
        total: taxComponents.length,
        active: taxComponents.filter(c => c.isActive).length
      },
      groups: {
        total: taxGroups.length,
        active: taxGroups.filter(g => g.isActive).length
      },
      rules: {
        total: taxRules.length,
        active: taxRules.filter(r => r.isActive).length
      }
    }
  }

  const stats = getTabStats()

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Tax Management</h1>
          <p className="text-muted-foreground">
            Manage tax components, groups, rules, and calculations for the platform
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={handleAddNew}>
            <Plus className="h-4 w-4 mr-2" />
            Add New
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tax Components</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.components.total}</div>
            <div className="text-xs text-muted-foreground flex items-center">
              <Badge variant="secondary" className="mr-1">
                {stats.components.active}
              </Badge>
              <span>active components</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tax Groups</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.groups.total}</div>
            <div className="text-xs text-muted-foreground flex items-center">
              <Badge variant="secondary" className="mr-1">
                {stats.groups.active}
              </Badge>
              <span>active groups</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tax Rules</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.rules.total}</div>
            <div className="text-xs text-muted-foreground flex items-center">
              <Badge variant="secondary" className="mr-1">
                {stats.rules.active}
              </Badge>
              <span>active rules</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tax Calculator</CardTitle>
            <Calculator className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">GST</div>
            <p className="text-xs text-muted-foreground">
              SGST + CGST / IGST
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="components" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>Components</span>
            {stats.components.total > 0 && (
              <Badge variant="secondary" className="ml-1">
                {stats.components.total}
              </Badge>
            )}
          </TabsTrigger>
          
          <TabsTrigger value="groups" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>Groups</span>
            {stats.groups.total > 0 && (
              <Badge variant="secondary" className="ml-1">
                {stats.groups.total}
              </Badge>
            )}
          </TabsTrigger>
          
          <TabsTrigger value="rules" className="flex items-center space-x-2">
            <FileText className="h-4 w-4" />
            <span>Rules</span>
            {stats.rules.total > 0 && (
              <Badge variant="secondary" className="ml-1">
                {stats.rules.total}
              </Badge>
            )}
          </TabsTrigger>
          
          <TabsTrigger value="calculator" className="flex items-center space-x-2">
            <Calculator className="h-4 w-4" />
            <span>Calculator</span>
          </TabsTrigger>
        </TabsList>

        {/* Tax Components Tab */}
        <TabsContent value="components" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Components</CardTitle>
              <CardDescription>
                Manage individual tax components like SGST, CGST, IGST, VAT, etc.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <TaxFilters key="components-filter" type="components" />
                <TaxComponentsList />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tax Groups Tab */}
        <TabsContent value="groups" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Groups</CardTitle>
              <CardDescription>
                Create and manage tax groups that combine multiple tax components.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <TaxFilters key="groups-filter" type="groups" />
                <TaxGroupsList />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tax Rules Tab */}
        <TabsContent value="rules" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Rules</CardTitle>
              <CardDescription>
                Define rules that determine which tax groups apply to different scenarios.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <TaxFilters key="rules-filter" type="rules" />
                <TaxRulesList />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tax Calculator Tab */}
        <TabsContent value="calculator" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Calculator</CardTitle>
              <CardDescription>
                Calculate taxes for different scenarios and preview tax breakdowns.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TaxCalculator />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Dialogs */}
      <TaxComponentForm
        mode="create"
        open={createComponentOpen}
        onOpenChange={setCreateComponentOpen}
        onSuccess={() => {
          // Reset the fetch tracking to allow refresh
          lastFetchedTab.current = ''
          setCreateComponentOpen(false)
          // Fetch data for current tab only
          if (activeTab === 'components') {
            fetchTaxComponents()
          }
        }}
        trigger={<div style={{ display: 'none' }} />}
      />

      <TaxGroupForm
        mode="create"
        open={createGroupOpen}
        onOpenChange={setCreateGroupOpen}
        onSuccess={() => {
          // Reset the fetch tracking to allow refresh
          lastFetchedTab.current = ''
          setCreateGroupOpen(false)
          // Fetch data for current tab only
          if (activeTab === 'groups') {
            fetchTaxGroups()
          }
        }}
        trigger={<div style={{ display: 'none' }} />}
      />

      <TaxRuleForm
        mode="create"
        open={createRuleOpen}
        onOpenChange={setCreateRuleOpen}
        onSuccess={() => {
          // Reset the fetch tracking to allow refresh
          lastFetchedTab.current = ''
          setCreateRuleOpen(false)
          // Fetch data for current tab only
          if (activeTab === 'rules') {
            fetchTaxRules()
          }
        }}
        trigger={<div style={{ display: 'none' }} />}
      />
    </div>
  )
}
