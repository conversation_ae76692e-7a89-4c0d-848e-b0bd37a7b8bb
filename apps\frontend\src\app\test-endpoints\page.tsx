'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react'

interface EndpointTest {
  name: string
  url: string
  method: 'GET' | 'POST' | 'PATCH' | 'DELETE'
  expectedStatus: number
  category: string
}

const API_ENDPOINTS: EndpointTest[] = [
  // Course Management
  { name: 'Get Courses', url: '/api/admin/courses', method: 'GET', expectedStatus: 200, category: 'Courses' },
  { name: 'Get Course Builder Courses', url: '/api/admin/course-builder/courses', method: 'GET', expectedStatus: 200, category: 'Courses' },
  
  // Course Bundles
  { name: 'Get Course Bundles', url: '/api/admin/course-bundles', method: 'GET', expectedStatus: 200, category: 'Bundles' },
  { name: 'Calculate Bundle Pricing', url: '/api/admin/course-bundles/calculate-pricing', method: 'POST', expectedStatus: 200, category: 'Bundles' },
  { name: 'Bundle Performance Comparison', url: '/api/admin/course-bundles/performance-comparison', method: 'GET', expectedStatus: 200, category: 'Bundles' },
  { name: 'Bundle Suggestions', url: '/api/admin/course-bundles/suggestions', method: 'GET', expectedStatus: 200, category: 'Bundles' },
  
  // Question Banks
  { name: 'Get Question Banks', url: '/api/admin/question-banks', method: 'GET', expectedStatus: 200, category: 'Questions' },
  { name: 'Question Bank Analytics', url: '/api/admin/question-banks/analytics', method: 'GET', expectedStatus: 200, category: 'Questions' },
  { name: 'Question Bank Search', url: '/api/admin/question-banks/search', method: 'GET', expectedStatus: 200, category: 'Questions' },
  
  // Tests
  { name: 'Get Tests', url: '/api/admin/tests', method: 'GET', expectedStatus: 200, category: 'Tests' },
  
  // Media Processing
  { name: 'Media Processing Status', url: '/api/admin/media/processing/status', method: 'GET', expectedStatus: 200, category: 'Media' },
  { name: 'Video Processing Jobs', url: '/api/admin/video-processing/jobs', method: 'GET', expectedStatus: 200, category: 'Media' },
  
  // Lessons
  { name: 'Get Lessons', url: '/api/admin/lessons', method: 'GET', expectedStatus: 200, category: 'Lessons' },
]

const FRONTEND_ROUTES = [
  // Admin Routes
  { name: 'Admin Dashboard', url: '/admin', category: 'Admin' },
  { name: 'Course Management', url: '/admin/courses', category: 'Admin' },
  { name: 'Course Creation', url: '/admin/courses/create', category: 'Admin' },
  { name: 'Course Builder', url: '/admin/course-builder', category: 'Admin' },
  { name: 'Staff Management', url: '/admin/staff', category: 'Admin' },
  { name: 'Student Management', url: '/admin/students', category: 'Admin' },
  { name: 'Settings', url: '/admin/settings', category: 'Admin' },
  
  // Student Routes
  { name: 'Student Dashboard', url: '/student/dashboard', category: 'Student' },
  { name: 'Student Courses', url: '/student/courses', category: 'Student' },
  { name: 'Student Settings', url: '/student/settings', category: 'Student' },
  
  // Super Admin Routes
  { name: 'Super Admin Dashboard', url: '/super-admin', category: 'Super Admin' },
  { name: 'Institute Management', url: '/super-admin/institutes', category: 'Super Admin' },
  { name: 'Platform Blog', url: '/super-admin/platform-blog', category: 'Super Admin' },
  
  // Auth Routes
  { name: 'Login', url: '/auth/login', category: 'Auth' },
  { name: 'Register', url: '/auth/register', category: 'Auth' },
  { name: 'Admin Login', url: '/auth/admin', category: 'Auth' },
]

export default function TestEndpointsPage() {
  const [apiResults, setApiResults] = useState<Record<string, { status: 'pending' | 'success' | 'error', message: string }>>({})
  const [routeResults, setRouteResults] = useState<Record<string, { status: 'pending' | 'success' | 'error', message: string }>>({})
  const [testing, setTesting] = useState(false)

  const testApiEndpoint = async (endpoint: EndpointTest) => {
    setApiResults(prev => ({ ...prev, [endpoint.name]: { status: 'pending', message: 'Testing...' } }))
    
    try {
      const response = await fetch(`http://localhost:3001${endpoint.url}`, {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
          // Add mock auth header for testing
          'Authorization': 'Bearer mock-token'
        },
        body: endpoint.method === 'POST' ? JSON.stringify({
          course_ids: ['1', '2'], // Mock data for bundle pricing
          discount_type: 'percentage',
          discount_value: 20
        }) : undefined
      })

      const isSuccess = response.status === endpoint.expectedStatus || response.status === 401 // 401 is expected without proper auth
      
      setApiResults(prev => ({ 
        ...prev, 
        [endpoint.name]: { 
          status: isSuccess ? 'success' : 'error', 
          message: `Status: ${response.status} ${isSuccess ? '✓' : '✗'}` 
        } 
      }))
    } catch (error) {
      setApiResults(prev => ({ 
        ...prev, 
        [endpoint.name]: { 
          status: 'error', 
          message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` 
        } 
      }))
    }
  }

  const testFrontendRoute = async (route: { name: string, url: string, category: string }) => {
    setRouteResults(prev => ({ ...prev, [route.name]: { status: 'pending', message: 'Testing...' } }))
    
    try {
      const response = await fetch(`http://localhost:3000${route.url}`, {
        method: 'GET',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
      })

      const isSuccess = response.status === 200 || response.status === 302 || response.status === 401
      
      setRouteResults(prev => ({ 
        ...prev, 
        [route.name]: { 
          status: isSuccess ? 'success' : 'error', 
          message: `Status: ${response.status} ${isSuccess ? '✓' : '✗'}` 
        } 
      }))
    } catch (error) {
      setRouteResults(prev => ({ 
        ...prev, 
        [route.name]: { 
          status: 'error', 
          message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` 
        } 
      }))
    }
  }

  const testAllEndpoints = async () => {
    setTesting(true)
    
    // Test API endpoints
    for (const endpoint of API_ENDPOINTS) {
      await testApiEndpoint(endpoint)
      await new Promise(resolve => setTimeout(resolve, 100)) // Small delay
    }
    
    // Test frontend routes
    for (const route of FRONTEND_ROUTES) {
      await testFrontendRoute(route)
      await new Promise(resolve => setTimeout(resolve, 100)) // Small delay
    }
    
    setTesting(false)
  }

  const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const groupByCategory = (items: any[], results: Record<string, any>) => {
    const grouped = items.reduce((acc, item) => {
      if (!acc[item.category]) acc[item.category] = []
      acc[item.category].push({ ...item, result: results[item.name] })
      return acc
    }, {} as Record<string, any[]>)
    return grouped
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">LMS System Testing Dashboard</h1>
          <p className="text-muted-foreground">Test API endpoints and frontend routes</p>
        </div>
        
        <Button onClick={testAllEndpoints} disabled={testing} size="lg">
          {testing ? 'Testing...' : 'Test All Endpoints & Routes'}
        </Button>
      </div>

      {/* API Endpoints Testing */}
      <Card>
        <CardHeader>
          <CardTitle>API Endpoints Testing</CardTitle>
        </CardHeader>
        <CardContent>
          {Object.entries(groupByCategory(API_ENDPOINTS, apiResults)).map(([category, endpoints]) => (
            <div key={category} className="mb-6">
              <h3 className="text-lg font-semibold mb-3">{category}</h3>
              <div className="grid gap-2">
                {endpoints.map((endpoint) => (
                  <div key={endpoint.name} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {endpoint.result && getStatusIcon(endpoint.result.status)}
                      <span className="font-medium">{endpoint.name}</span>
                      <Badge variant="outline">{endpoint.method}</Badge>
                      <code className="text-sm bg-muted px-2 py-1 rounded">{endpoint.url}</code>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {endpoint.result?.message || 'Not tested'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Frontend Routes Testing */}
      <Card>
        <CardHeader>
          <CardTitle>Frontend Routes Testing</CardTitle>
        </CardHeader>
        <CardContent>
          {Object.entries(groupByCategory(FRONTEND_ROUTES, routeResults)).map(([category, routes]) => (
            <div key={category} className="mb-6">
              <h3 className="text-lg font-semibold mb-3">{category}</h3>
              <div className="grid gap-2">
                {routes.map((route) => (
                  <div key={route.name} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {route.result && getStatusIcon(route.result.status)}
                      <span className="font-medium">{route.name}</span>
                      <code className="text-sm bg-muted px-2 py-1 rounded">{route.url}</code>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {route.result?.message || 'Not tested'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
}
