'use client'

import { useState } from 'react'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'

export default function TestLoginPage() {
  const { user, isAuthenticated, isLoading, login, initialize } = useAuthStore()
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('123456')
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  const handleLogin = async () => {
    try {
      setError('')
      setMessage('Attempting login...')
      
      console.log('🔄 Starting login test...')
      await login(email, password, 'super_admin')
      
      setMessage('Login successful! Check auth state below.')
      console.log('✅ Login test successful')
      
      // Wait a moment then check storage
      setTimeout(() => {
        const authStorage = localStorage.getItem('auth-storage')
        console.log('📦 Auth storage after login:', authStorage)
      }, 1000)
      
    } catch (err: any) {
      setError(err.message || 'Login failed')
      console.error('❌ Login test failed:', err)
    }
  }

  const checkStorage = () => {
    const authStorage = localStorage.getItem('auth-storage')
    const authToken = localStorage.getItem('auth_token')
    const userData = localStorage.getItem('user_data')
    
    console.log('📦 Storage check:', {
      authStorage: authStorage ? 'present' : 'missing',
      authToken: authToken ? 'present' : 'missing',
      userData: userData ? 'present' : 'missing'
    })
    
    if (authStorage) {
      try {
        const parsed = JSON.parse(authStorage)
        console.log('📦 Parsed auth storage:', parsed)
      } catch (e) {
        console.error('❌ Failed to parse auth storage:', e)
      }
    }
  }

  const clearStorage = () => {
    localStorage.clear()
    setMessage('Storage cleared')
    window.location.reload()
  }

  const testRolePermissions = () => {
    window.location.href = '/super-admin/role-permissions'
  }

  return (
    <div className="container mx-auto py-6 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>🧪 Login Test Page</CardTitle>
          <CardDescription>
            Test the login functionality and check authentication state
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Login Form */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="123456"
              />
            </div>
            <Button 
              onClick={handleLogin} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Logging in...' : 'Test Login'}
            </Button>
          </div>

          {/* Messages */}
          {message && (
            <Alert>
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}
          
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Auth State */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Current Auth State</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Is Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}
              </div>
              <div>
                <strong>Is Loading:</strong> {isLoading ? '⏳ Yes' : '✅ No'}
              </div>
              <div>
                <strong>Has User:</strong> {user ? '✅ Yes' : '❌ No'}
              </div>
              <div>
                <strong>User Email:</strong> {user?.email || 'N/A'}
              </div>
              <div>
                <strong>Legacy Role:</strong> {user?.legacyRole || 'N/A'}
              </div>
              <div>
                <strong>Role Name:</strong> {user?.role?.name || 'N/A'}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 flex-wrap">
            <Button onClick={() => initialize()} variant="outline">
              Re-initialize
            </Button>
            <Button onClick={checkStorage} variant="outline">
              Check Storage
            </Button>
            <Button onClick={clearStorage} variant="destructive">
              Clear Storage
            </Button>
            <Button onClick={testRolePermissions} variant="default">
              Test Role-Permissions Page
            </Button>
          </div>

          {/* Raw Data */}
          {user && (
            <div>
              <h3 className="text-lg font-semibold mb-2">Raw User Data</h3>
              <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto max-h-40">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
