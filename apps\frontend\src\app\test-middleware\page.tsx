'use client'

import { useSearchParams } from 'next/navigation'

export default function TestMiddlewarePage() {
  const searchParams = useSearchParams()
  
  const allParams = Object.fromEntries(searchParams.entries())
  
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Middleware Test Page</h1>
      
      <div className="mb-4">
        <h2 className="text-lg font-semibold">URL Search Parameters:</h2>
        <pre className="bg-gray-100 p-4 rounded mt-2">
          {JSON.stringify(allParams, null, 2)}
        </pre>
      </div>
      
      <div className="mb-4">
        <h2 className="text-lg font-semibold">Current URL:</h2>
        <p className="bg-gray-100 p-2 rounded mt-2">
          {typeof window !== 'undefined' ? window.location.href : 'Server-side'}
        </p>
      </div>
      
      <div className="text-sm text-gray-600">
        <p>If middleware is working, you should see parameters added by middleware.</p>
        <p>Visit: http://hello.local:3000/test-middleware</p>
      </div>
    </div>
  )
}
