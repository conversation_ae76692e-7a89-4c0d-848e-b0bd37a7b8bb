'use client'

import React from 'react'
import { usePermissions, Permission, UserRole } from '@/hooks/usePermissions'

interface PermissionGateProps {
  children: React.ReactNode
  permission?: Permission | Permission[]
  role?: UserRole | UserRole[]
  requireAll?: boolean
  fallback?: React.ReactNode
  institute?: string
  branch?: string
}

/**
 * PermissionGate Component
 * Conditionally renders children based on user permissions and roles
 */
export const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permission,
  role,
  requireAll = false,
  fallback = null,
  institute,
  branch
}) => {
  const { 
    hasPermission, 
    hasAnyPermission, 
    hasAllPermissions, 
    getUserRole,
    user 
  } = usePermissions()

  // Check authentication
  if (!user) {
    return <>{fallback}</>
  }

  // Check institute access if specified
  if (institute && user.institute !== institute) {
    return <>{fallback}</>
  }

  // Check branch access if specified
  if (branch && user.branch !== branch) {
    // Institute admin can access all branches in their institute
    if (user.legacyRole !== 'institute_admin' && user.role !== 'institute_admin') {
      return <>{fallback}</>
    }
  }

  // Check role-based access
  if (role) {
    const allowedRoles = Array.isArray(role) ? role : [role]
    const userRole = getUserRole()
    
    if (!userRole || !allowedRoles.includes(userRole)) {
      return <>{fallback}</>
    }
  }

  // Check permission-based access
  if (permission) {
    const permissions = Array.isArray(permission) ? permission : [permission]
    
    const hasAccess = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions)
    
    if (!hasAccess) {
      return <>{fallback}</>
    }
  }

  return <>{children}</>
}

/**
 * Higher-order component for permission-based rendering
 */
export const withPermission = (
  permission: Permission | Permission[],
  options: {
    requireAll?: boolean
    fallback?: React.ReactNode
    role?: UserRole | UserRole[]
  } = {}
) => {
  return function <P extends object>(Component: React.ComponentType<P>) {
    const WrappedComponent: React.FC<P> = (props) => {
      return (
        <PermissionGate
          permission={permission}
          role={options.role}
          requireAll={options.requireAll}
          fallback={options.fallback}
        >
          <Component {...props} />
        </PermissionGate>
      )
    }

    WrappedComponent.displayName = `withPermission(${Component.displayName || Component.name})`
    return WrappedComponent
  }
}

/**
 * Course Management Permission Gates
 */
export const CourseManagementGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate
    permission={[
      Permission.COURSES_CREATE,
      Permission.COURSES_UPDATE,
      Permission.COURSES_DELETE
    ]}
    requireAll={false}
    {...props}
  />
)

export const CourseCreateGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.COURSES_CREATE} {...props} />
)

export const CourseEditGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.COURSES_UPDATE} {...props} />
)

export const CourseDeleteGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.COURSES_DELETE} {...props} />
)

export const CoursePublishGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.COURSES_PUBLISH} {...props} />
)

/**
 * Lesson Management Permission Gates
 */
export const LessonManagementGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate
    permission={[
      Permission.LESSONS_CREATE,
      Permission.LESSONS_UPDATE,
      Permission.LESSONS_DELETE
    ]}
    requireAll={false}
    {...props}
  />
)

export const LessonCreateGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.LESSONS_CREATE} {...props} />
)

export const LessonEditGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.LESSONS_UPDATE} {...props} />
)

export const LessonDeleteGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.LESSONS_DELETE} {...props} />
)

/**
 * Content Management Permission Gates
 */
export const ContentManagementGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate
    permission={[
      Permission.CONTENT_CREATE,
      Permission.CONTENT_UPDATE,
      Permission.CONTENT_DELETE
    ]}
    requireAll={false}
    {...props}
  />
)

export const ContentUploadGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.CONTENT_UPLOAD} {...props} />
)

/**
 * Assessment Management Permission Gates
 */
export const AssessmentManagementGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate
    permission={[
      Permission.ASSESSMENTS_CREATE,
      Permission.ASSESSMENTS_UPDATE,
      Permission.ASSESSMENTS_DELETE
    ]}
    requireAll={false}
    {...props}
  />
)

export const AssessmentGradeGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.ASSESSMENTS_GRADE} {...props} />
)

/**
 * Analytics Permission Gates
 */
export const AnalyticsGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.ANALYTICS_VIEW} {...props} />
)

export const AnalyticsExportGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate permission={Permission.ANALYTICS_EXPORT} {...props} />
)

/**
 * User Management Permission Gates
 */
export const UserManagementGate: React.FC<Omit<PermissionGateProps, 'permission'>> = (props) => (
  <PermissionGate
    permission={[
      Permission.USERS_CREATE,
      Permission.USERS_UPDATE,
      Permission.USERS_DELETE
    ]}
    requireAll={false}
    {...props}
  />
)

/**
 * Role-based Permission Gates
 */
export const InstituteAdminGate: React.FC<Omit<PermissionGateProps, 'role'>> = (props) => (
  <PermissionGate
    role={[UserRole.SUPER_ADMIN, UserRole.INSTITUTE_ADMIN]}
    {...props}
  />
)

export const BranchManagerGate: React.FC<Omit<PermissionGateProps, 'role'>> = (props) => (
  <PermissionGate
    role={[UserRole.SUPER_ADMIN, UserRole.INSTITUTE_ADMIN, UserRole.BRANCH_MANAGER]}
    {...props}
  />
)

export const TrainerGate: React.FC<Omit<PermissionGateProps, 'role'>> = (props) => (
  <PermissionGate
    role={[UserRole.SUPER_ADMIN, UserRole.INSTITUTE_ADMIN, UserRole.BRANCH_MANAGER, UserRole.TRAINER]}
    {...props}
  />
)

export const StaffGate: React.FC<Omit<PermissionGateProps, 'role'>> = (props) => (
  <PermissionGate
    role={[UserRole.SUPER_ADMIN, UserRole.INSTITUTE_ADMIN, UserRole.BRANCH_MANAGER, UserRole.TRAINER, UserRole.STAFF]}
    {...props}
  />
)

export const StudentGate: React.FC<Omit<PermissionGateProps, 'role'>> = (props) => (
  <PermissionGate
    role={[UserRole.STUDENT, UserRole.LEVEL_4]}
    {...props}
  />
)

/**
 * Custom hook for conditional rendering based on permissions
 */
export const useConditionalRender = () => {
  const permissions = usePermissions()

  const renderIf = (
    condition: boolean | (() => boolean),
    component: React.ReactNode,
    fallback: React.ReactNode = null
  ) => {
    const shouldRender = typeof condition === 'function' ? condition() : condition
    return shouldRender ? component : fallback
  }

  const renderIfPermission = (
    permission: Permission | Permission[],
    component: React.ReactNode,
    fallback: React.ReactNode = null,
    requireAll: boolean = false
  ) => {
    const permissions_array = Array.isArray(permission) ? permission : [permission]
    const hasAccess = requireAll 
      ? permissions.hasAllPermissions(permissions_array)
      : permissions.hasAnyPermission(permissions_array)
    
    return hasAccess ? component : fallback
  }

  const renderIfRole = (
    role: UserRole | UserRole[],
    component: React.ReactNode,
    fallback: React.ReactNode = null
  ) => {
    const allowedRoles = Array.isArray(role) ? role : [role]
    const userRole = permissions.getUserRole()
    const hasAccess = userRole && allowedRoles.includes(userRole)
    
    return hasAccess ? component : fallback
  }

  return {
    renderIf,
    renderIfPermission,
    renderIfRole,
    ...permissions
  }
}

export default PermissionGate
