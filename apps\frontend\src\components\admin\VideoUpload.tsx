'use client'

import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Upload, Video, CheckCircle, AlertCircle, Clock } from 'lucide-react'
import { toast } from 'sonner'
import { api } from '@/lib/api'

interface VideoUploadProps {
  onUploadComplete?: (result: any) => void
  onUploadError?: (error: string) => void
}

interface ProcessingOptions {
  generateThumbnails: boolean
  transcodeToMultipleFormats: boolean
  optimizeForStreaming: boolean
  generatePreview: boolean
  quality: 'low' | 'medium' | 'high' | 'ultra'
  thumbnailCount: number
  previewDuration: number
}

interface ProcessingJob {
  id: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  progress: number
  startTime: string
  endTime?: string
  error?: string
}

const validationSchema = Yup.object({
  thumbnailCount: Yup.number()
    .min(1, 'At least 1 thumbnail required')
    .max(10, 'Maximum 10 thumbnails allowed')
    .required('Thumbnail count is required'),
  previewDuration: Yup.number()
    .min(5, 'Minimum 5 seconds')
    .max(120, 'Maximum 120 seconds')
    .required('Preview duration is required')
})

export default function VideoUpload({ onUploadComplete, onUploadError }: VideoUploadProps) {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [processingJob, setProcessingJob] = useState<ProcessingJob | null>(null)
  const [processingResult, setProcessingResult] = useState<any>(null)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      // Validate file type
      const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm']
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please upload a valid video file (MP4, AVI, MOV, WMV, WebM)')
        return
      }

      // Validate file size (500MB max)
      if (file.size > 500 * 1024 * 1024) {
        toast.error('File size must be less than 500MB')
        return
      }

      setUploadedFile(file)
      toast.success('Video file selected successfully')
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/*': ['.mp4', '.avi', '.mov', '.wmv', '.webm', '.mkv', '.flv']
    },
    multiple: false,
    maxSize: 500 * 1024 * 1024 // 500MB
  })

  const handleUpload = async (values: ProcessingOptions) => {
    if (!uploadedFile) {
      toast.error('Please select a video file first')
      return
    }

    setIsUploading(true)
    
    try {
      const formData = new FormData()
      formData.append('video', uploadedFile)
      formData.append('generateThumbnails', values.generateThumbnails.toString())
      formData.append('transcodeToMultipleFormats', values.transcodeToMultipleFormats.toString())
      formData.append('optimizeForStreaming', values.optimizeForStreaming.toString())
      formData.append('generatePreview', values.generatePreview.toString())
      formData.append('quality', values.quality)
      formData.append('thumbnailCount', values.thumbnailCount.toString())
      formData.append('previewDuration', values.previewDuration.toString())

      const response = await api.post('/admin/video/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      if (response.data.success) {
        toast.success('Video upload started! Processing in background...')
        setProcessingJob({
          id: response.data.jobId,
          status: 'queued',
          progress: 0,
          startTime: new Date().toISOString()
        })
        
        // Start polling for status
        pollProcessingStatus(response.data.jobId)
      } else {
        throw new Error(response.data.error || 'Upload failed')
      }
    } catch (error: any) {
      console.error('Upload error:', error)
      const errorMessage = error.response?.data?.error || error.message || 'Upload failed'
      toast.error(errorMessage)
      onUploadError?.(errorMessage)
    } finally {
      setIsUploading(false)
    }
  }

  const pollProcessingStatus = async (jobId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await api.get(`/admin/video/status/${jobId}`)
        
        if (response.data.success) {
          const job = response.data.job
          setProcessingJob(job)

          if (job.status === 'completed') {
            clearInterval(pollInterval)
            toast.success('Video processing completed!')
            
            // Get the result
            const resultResponse = await api.get(`/admin/video/result/${jobId}`)
            if (resultResponse.data.success) {
              setProcessingResult(resultResponse.data.result)
              onUploadComplete?.(resultResponse.data.result)
            }
          } else if (job.status === 'failed') {
            clearInterval(pollInterval)
            toast.error(`Video processing failed: ${job.error || 'Unknown error'}`)
            onUploadError?.(job.error || 'Processing failed')
          }
        }
      } catch (error) {
        console.error('Error polling status:', error)
        clearInterval(pollInterval)
      }
    }, 2000) // Poll every 2 seconds

    // Stop polling after 30 minutes
    setTimeout(() => {
      clearInterval(pollInterval)
    }, 30 * 60 * 1000)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case 'processing':
      case 'queued':
        return <Clock className="h-5 w-5 text-blue-500 animate-spin" />
      default:
        return null
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="h-5 w-5" />
            Video Upload & Processing
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Upload Area */}
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive
                ? 'border-blue-500 bg-blue-50'
                : uploadedFile
                ? 'border-green-500 bg-green-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            {uploadedFile ? (
              <div>
                <p className="text-lg font-medium text-green-700">{uploadedFile.name}</p>
                <p className="text-sm text-gray-500">{formatFileSize(uploadedFile.size)}</p>
              </div>
            ) : (
              <div>
                <p className="text-lg font-medium">
                  {isDragActive ? 'Drop the video here' : 'Drag & drop a video file here'}
                </p>
                <p className="text-sm text-gray-500">or click to select a file</p>
                <p className="text-xs text-gray-400 mt-2">
                  Supported formats: MP4, AVI, MOV, WMV, WebM (Max: 500MB)
                </p>
              </div>
            )}
          </div>

          {/* Processing Options Form */}
          {uploadedFile && (
            <Formik
              initialValues={{
                generateThumbnails: true,
                transcodeToMultipleFormats: true,
                optimizeForStreaming: false,
                generatePreview: true,
                quality: 'medium' as const,
                thumbnailCount: 5,
                previewDuration: 30
              }}
              validationSchema={validationSchema}
              onSubmit={handleUpload}
            >
              {({ values, setFieldValue, errors, touched }) => (
                <Form className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Processing Options */}
                    <div className="space-y-4">
                      <h3 className="font-medium">Processing Options</h3>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="generateThumbnails"
                          checked={values.generateThumbnails}
                          onCheckedChange={(checked) => setFieldValue('generateThumbnails', checked)}
                        />
                        <Label htmlFor="generateThumbnails">Generate Thumbnails</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="transcodeToMultipleFormats"
                          checked={values.transcodeToMultipleFormats}
                          onCheckedChange={(checked) => setFieldValue('transcodeToMultipleFormats', checked)}
                        />
                        <Label htmlFor="transcodeToMultipleFormats">Transcode to Multiple Formats</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="optimizeForStreaming"
                          checked={values.optimizeForStreaming}
                          onCheckedChange={(checked) => setFieldValue('optimizeForStreaming', checked)}
                        />
                        <Label htmlFor="optimizeForStreaming">Optimize for Streaming</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="generatePreview"
                          checked={values.generatePreview}
                          onCheckedChange={(checked) => setFieldValue('generatePreview', checked)}
                        />
                        <Label htmlFor="generatePreview">Generate Preview Clip</Label>
                      </div>
                    </div>

                    {/* Quality and Settings */}
                    <div className="space-y-4">
                      <h3 className="font-medium">Quality Settings</h3>
                      
                      <div>
                        <Label htmlFor="quality">Video Quality</Label>
                        <Select value={values.quality} onValueChange={(value) => setFieldValue('quality', value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low (480p)</SelectItem>
                            <SelectItem value="medium">Medium (720p)</SelectItem>
                            <SelectItem value="high">High (1080p)</SelectItem>
                            <SelectItem value="ultra">Ultra (1440p)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {values.generateThumbnails && (
                        <div>
                          <Label htmlFor="thumbnailCount">Number of Thumbnails</Label>
                          <Input
                            id="thumbnailCount"
                            type="number"
                            min="1"
                            max="10"
                            value={values.thumbnailCount}
                            onChange={(e) => setFieldValue('thumbnailCount', parseInt(e.target.value))}
                          />
                          {errors.thumbnailCount && touched.thumbnailCount && (
                            <p className="text-sm text-red-500 mt-1">{errors.thumbnailCount}</p>
                          )}
                        </div>
                      )}

                      {values.generatePreview && (
                        <div>
                          <Label htmlFor="previewDuration">Preview Duration (seconds)</Label>
                          <Input
                            id="previewDuration"
                            type="number"
                            min="5"
                            max="120"
                            value={values.previewDuration}
                            onChange={(e) => setFieldValue('previewDuration', parseInt(e.target.value))}
                          />
                          {errors.previewDuration && touched.previewDuration && (
                            <p className="text-sm text-red-500 mt-1">{errors.previewDuration}</p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  <Button
                    type="submit"
                    disabled={isUploading || !!processingJob}
                    className="w-full"
                  >
                    {isUploading ? 'Uploading...' : 'Start Processing'}
                  </Button>
                </Form>
              )}
            </Formik>
          )}
        </CardContent>
      </Card>

      {/* Processing Status */}
      {processingJob && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(processingJob.status)}
              Processing Status
              <Badge variant={processingJob.status === 'completed' ? 'default' : 'secondary'}>
                {processingJob.status}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Progress</span>
                  <span>{processingJob.progress}%</span>
                </div>
                <Progress value={processingJob.progress} className="w-full" />
              </div>
              
              {processingJob.error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-700">{processingJob.error}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Processing Result */}
      {processingResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Processing Complete
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Duration:</span> {formatDuration(processingResult.metadata.duration)}
                </div>
                <div>
                  <span className="font-medium">Resolution:</span> {processingResult.metadata.width}x{processingResult.metadata.height}
                </div>
                <div>
                  <span className="font-medium">Format:</span> {processingResult.metadata.format}
                </div>
                <div>
                  <span className="font-medium">Processing Time:</span> {Math.round(processingResult.processingTime / 1000)}s
                </div>
              </div>

              {processingResult.thumbnails.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Thumbnails ({processingResult.thumbnails.length})</h4>
                  <div className="grid grid-cols-5 gap-2">
                    {processingResult.thumbnails.map((thumbnail: string, index: number) => (
                      <img
                        key={index}
                        src={thumbnail}
                        alt={`Thumbnail ${index + 1}`}
                        className="w-full h-16 object-cover rounded border"
                      />
                    ))}
                  </div>
                </div>
              )}

              {processingResult.transcodedVersions.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Available Formats</h4>
                  <div className="space-y-2">
                    {processingResult.transcodedVersions.map((version: any, index: number) => (
                      <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <span>{version.format.name} ({version.format.resolution})</span>
                        <span className="text-sm text-gray-500">{formatFileSize(version.size)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
