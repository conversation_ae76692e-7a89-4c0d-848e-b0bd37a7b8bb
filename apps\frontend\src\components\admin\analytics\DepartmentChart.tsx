'use client'

interface DepartmentChartProps {
  data: Record<string, number>
}

export function DepartmentChart({ data }: DepartmentChartProps) {
  const total = Object.values(data).reduce((sum, value) => sum + value, 0)
  
  const departmentColors = {
    administration: '#3B82F6',
    academic_affairs: '#8B5CF6',
    student_services: '#10B981',
    technology_it: '#6366F1',
    finance_accounting: '#F59E0B',
    marketing_communications: '#EC4899',
    human_resources: '#F97316'
  }

  const formatDepartmentName = (dept: string) => {
    return dept.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const chartData = Object.entries(data).map(([dept, count]) => ({
    department: dept,
    name: formatDepartmentName(dept),
    count,
    percentage: total > 0 ? (count / total) * 100 : 0,
    color: departmentColors[dept as keyof typeof departmentColors] || '#6B7280'
  })).sort((a, b) => b.count - a.count)

  if (total === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        No data available
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Simple Bar Chart */}
      <div className="space-y-3">
        {chartData.map((item) => (
          <div key={item.department} className="space-y-1">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-gray-700">{item.name}</span>
              </div>
              <div className="text-right">
                <span className="font-medium text-gray-900">{item.count}</span>
                <span className="text-gray-500 ml-1">({item.percentage.toFixed(1)}%)</span>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${item.percentage}%`,
                  backgroundColor: item.color
                }}
              ></div>
            </div>
          </div>
        ))}
      </div>

      {/* Summary */}
      <div className="pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Total Staff</span>
          <span className="font-medium text-gray-900">{total}</span>
        </div>
        <div className="flex items-center justify-between text-sm mt-1">
          <span className="text-gray-500">Departments</span>
          <span className="font-medium text-gray-900">{chartData.length}</span>
        </div>
      </div>
    </div>
  )
}
