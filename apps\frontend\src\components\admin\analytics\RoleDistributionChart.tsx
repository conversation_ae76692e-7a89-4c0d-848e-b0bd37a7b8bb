'use client'

interface RoleDistributionChartProps {
  data: Record<string, number>
}

export function RoleDistributionChart({ data }: RoleDistributionChartProps) {
  const total = Object.values(data).reduce((sum, value) => sum + value, 0)
  
  const roleColors = [
    '#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EC4899', 
    '#F97316', '#6366F1', '#14B8A6', '#F43F5E', '#84CC16'
  ]

  const chartData = Object.entries(data).map(([role, count], index) => ({
    role,
    count,
    percentage: total > 0 ? (count / total) * 100 : 0,
    color: roleColors[index % roleColors.length]
  })).sort((a, b) => b.count - a.count)

  const maxCount = Math.max(...chartData.map(item => item.count))

  if (total === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        No data available
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Horizontal Bar Chart */}
      <div className="space-y-3">
        {chartData.slice(0, 8).map((item) => (
          <div key={item.role} className="space-y-1">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-gray-700 truncate max-w-[120px]" title={item.role}>
                  {item.role}
                </span>
              </div>
              <div className="text-right">
                <span className="font-medium text-gray-900">{item.count}</span>
                <span className="text-gray-500 ml-1">({item.percentage.toFixed(1)}%)</span>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${(item.count / maxCount) * 100}%`,
                  backgroundColor: item.color
                }}
              ></div>
            </div>
          </div>
        ))}
      </div>

      {/* Show more indicator if there are more roles */}
      {chartData.length > 8 && (
        <div className="text-center">
          <span className="text-sm text-gray-500">
            +{chartData.length - 8} more roles
          </span>
        </div>
      )}

      {/* Summary */}
      <div className="pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Total Staff</span>
          <span className="font-medium text-gray-900">{total}</span>
        </div>
        <div className="flex items-center justify-between text-sm mt-1">
          <span className="text-gray-500">Unique Roles</span>
          <span className="font-medium text-gray-900">{chartData.length}</span>
        </div>
      </div>
    </div>
  )
}
