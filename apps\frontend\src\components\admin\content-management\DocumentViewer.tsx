'use client'

import React, { useState, useRef, useEffect } from 'react'
import { 
  FileText, 
  Download, 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Maximize, 
  Minimize,
  ChevronLeft,
  ChevronRight,
  Search,
  BookOpen,
  Eye,
  Upload,
  X,
  Settings
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/hooks/use-toast'
import { FileUploader } from '@/components/admin/file-upload/FileUploader'

export interface DocumentViewerProps {
  documentUrl?: string
  documentName?: string
  allowDownload?: boolean
  allowAnnotations?: boolean
  onDocumentChange?: (url: string, name: string) => void
  className?: string
}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({
  documentUrl,
  documentName,
  allowDownload = true,
  allowAnnotations = false,
  onDocumentChange,
  className = ''
}) => {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [zoom, setZoom] = useState(100)
  const [rotation, setRotation] = useState(0)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [documentType, setDocumentType] = useState<'pdf' | 'office' | 'image' | 'unknown'>('unknown')
  
  const viewerRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  useEffect(() => {
    if (documentUrl) {
      detectDocumentType(documentUrl)
    }
  }, [documentUrl])

  const detectDocumentType = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase()
    
    switch (extension) {
      case 'pdf':
        setDocumentType('pdf')
        break
      case 'doc':
      case 'docx':
      case 'ppt':
      case 'pptx':
      case 'xls':
      case 'xlsx':
        setDocumentType('office')
        break
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        setDocumentType('image')
        break
      default:
        setDocumentType('unknown')
    }
  }

  const handleDocumentUpload = (files: any[]) => {
    if (files.length > 0) {
      const file = files[0]
      onDocumentChange?.(file.url, file.name)
      toast({
        title: 'Document uploaded',
        description: `${file.name} has been uploaded successfully`
      })
    }
  }

  const handleDownload = () => {
    if (documentUrl) {
      const link = document.createElement('a')
      link.href = documentUrl
      link.download = documentName || 'document'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      toast({
        title: 'Download started',
        description: 'Document download has been initiated'
      })
    }
  }

  const handleZoomChange = (newZoom: number[]) => {
    setZoom(newZoom[0])
  }

  const handleRotate = () => {
    setRotation((prev) => (prev + 90) % 360)
  }

  const toggleFullscreen = () => {
    if (!isFullscreen && viewerRef.current) {
      if (viewerRef.current.requestFullscreen) {
        viewerRef.current.requestFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    }
    setIsFullscreen(!isFullscreen)
  }

  const handlePageNavigation = (direction: 'prev' | 'next') => {
    if (direction === 'prev' && currentPage > 1) {
      setCurrentPage(currentPage - 1)
    } else if (direction === 'next' && currentPage < totalPages) {
      setCurrentPage(currentPage + 1)
    }
  }

  const renderDocumentContent = () => {
    if (!documentUrl) {
      return (
        <div className="flex flex-col items-center justify-center h-96 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <FileText className="h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No document selected</h3>
          <p className="text-gray-500 mb-4">Upload a document to view it here</p>
          
          <FileUploader
            onUploadComplete={handleDocumentUpload}
            maxFiles={1}
            acceptedFileTypes={['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx']}
            maxFileSize={50 * 1024 * 1024} // 50MB
            showPreview={false}
          />
        </div>
      )
    }

    const containerStyle = {
      transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
      transformOrigin: 'center center',
      transition: 'transform 0.3s ease'
    }

    switch (documentType) {
      case 'pdf':
        return (
          <div className="relative bg-white rounded-lg shadow-inner">
            <iframe
              src={`${documentUrl}#page=${currentPage}&zoom=${zoom}`}
              className="w-full h-full min-h-96 rounded-lg"
              style={containerStyle}
              title={documentName || 'PDF Document'}
              onLoad={() => setIsLoading(false)}
            />
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            )}
          </div>
        )

      case 'office':
        return (
          <div className="relative bg-white rounded-lg shadow-inner">
            <iframe
              src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(documentUrl)}`}
              className="w-full h-full min-h-96 rounded-lg"
              style={containerStyle}
              title={documentName || 'Office Document'}
              onLoad={() => setIsLoading(false)}
            />
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            )}
          </div>
        )

      case 'image':
        return (
          <div className="flex items-center justify-center bg-gray-50 rounded-lg min-h-96">
            <img
              src={documentUrl}
              alt={documentName || 'Document'}
              className="max-w-full max-h-full object-contain rounded-lg"
              style={containerStyle}
              onLoad={() => setIsLoading(false)}
            />
          </div>
        )

      default:
        return (
          <div className="flex flex-col items-center justify-center h-96 bg-gray-50 rounded-lg">
            <FileText className="h-16 w-16 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Preview not available</h3>
            <p className="text-gray-500 mb-4">This document type cannot be previewed</p>
            {allowDownload && (
              <Button onClick={handleDownload} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download Document
              </Button>
            )}
          </div>
        )
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Viewer
            {documentName && (
              <Badge variant="secondary" className="ml-2">
                {documentName}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {onDocumentChange && (
              <FileUploader
                onUploadComplete={handleDocumentUpload}
                maxFiles={1}
                acceptedFileTypes={['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx']}
                maxFileSize={50 * 1024 * 1024}
                showPreview={false}
                trigger={
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload
                  </Button>
                }
              />
            )}
            
            {allowDownload && documentUrl && (
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Document Controls */}
        {documentUrl && (
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-4">
              {/* Page Navigation */}
              {documentType === 'pdf' && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageNavigation('prev')}
                    disabled={currentPage <= 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      min="1"
                      max={totalPages}
                      value={currentPage}
                      onChange={(e) => setCurrentPage(parseInt(e.target.value) || 1)}
                      className="w-16 text-center"
                    />
                    <span className="text-sm text-gray-500">/ {totalPages}</span>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageNavigation('next')}
                    disabled={currentPage >= totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}

              {/* Zoom Controls */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setZoom(Math.max(25, zoom - 25))}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                
                <div className="w-24">
                  <Slider
                    value={[zoom]}
                    onValueChange={handleZoomChange}
                    min={25}
                    max={200}
                    step={25}
                  />
                </div>
                
                <span className="text-sm text-gray-500 w-12">{zoom}%</span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setZoom(Math.min(200, zoom + 25))}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </div>

              {/* Rotation */}
              <Button variant="outline" size="sm" onClick={handleRotate}>
                <RotateCw className="h-4 w-4" />
              </Button>

              {/* Fullscreen */}
              <Button variant="outline" size="sm" onClick={toggleFullscreen}>
                {isFullscreen ? (
                  <Minimize className="h-4 w-4" />
                ) : (
                  <Maximize className="h-4 w-4" />
                )}
              </Button>
            </div>

            {/* Search */}
            {documentType === 'pdf' && (
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search in document..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-48"
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Document Content */}
        <div 
          ref={viewerRef}
          className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white p-4' : ''}`}
        >
          {renderDocumentContent()}
        </div>

        {/* Document Info */}
        {documentUrl && (
          <div className="flex items-center justify-between text-sm text-gray-500 pt-2 border-t">
            <div className="flex items-center gap-4">
              <span>Type: {documentType.toUpperCase()}</span>
              {documentName && <span>Name: {documentName}</span>}
            </div>
            
            <div className="flex items-center gap-4">
              {allowAnnotations && (
                <div className="flex items-center gap-2">
                  <Switch id="annotations" />
                  <Label htmlFor="annotations">Annotations</Label>
                </div>
              )}
              
              <Badge variant="outline">
                <Eye className="h-3 w-3 mr-1" />
                Viewing
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default DocumentViewer
