'use client'

import React, { useState, useRef, useEffect } from 'react'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize, 
  Minimize,
  SkipBack,
  SkipForward,
  Settings,
  Download,
  Upload,
  RotateCcw,
  Subtitles,
  Share,
  BookOpen,
  BarChart3
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { FileUploader } from '@/components/admin/file-upload/FileUploader'

export interface VideoPlayerProps {
  videoUrl?: string
  videoTitle?: string
  allowDownload?: boolean
  allowSpeedControl?: boolean
  showAnalytics?: boolean
  autoplay?: boolean
  onVideoChange?: (url: string, title: string) => void
  onProgress?: (currentTime: number, duration: number) => void
  className?: string
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoUrl,
  videoTitle,
  allowDownload = true,
  allowSpeedControl = true,
  showAnalytics = false,
  autoplay = false,
  onVideoChange,
  onProgress,
  className = ''
}) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(100)
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [playbackSpeed, setPlaybackSpeed] = useState(1)
  const [showControls, setShowControls] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [showSubtitles, setShowSubtitles] = useState(false)
  const [videoType, setVideoType] = useState<'youtube' | 'vimeo' | 'direct' | 'unknown'>('unknown')
  
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const controlsTimeoutRef = useRef<NodeJS.Timeout>()
  const { toast } = useToast()

  useEffect(() => {
    if (videoUrl) {
      detectVideoType(videoUrl)
    }
  }, [videoUrl])

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
      onProgress?.(video.currentTime, video.duration)
    }

    const handleDurationChange = () => {
      setDuration(video.duration)
    }

    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)
    const handleLoadStart = () => setIsLoading(true)
    const handleCanPlay = () => setIsLoading(false)

    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('durationchange', handleDurationChange)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('loadstart', handleLoadStart)
    video.addEventListener('canplay', handleCanPlay)

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('durationchange', handleDurationChange)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('loadstart', handleLoadStart)
      video.removeEventListener('canplay', handleCanPlay)
    }
  }, [onProgress])

  const detectVideoType = (url: string) => {
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      setVideoType('youtube')
    } else if (url.includes('vimeo.com')) {
      setVideoType('vimeo')
    } else if (url.match(/\.(mp4|webm|ogg|mov|avi)$/i)) {
      setVideoType('direct')
    } else {
      setVideoType('unknown')
    }
  }

  const handleVideoUpload = (files: any[]) => {
    if (files.length > 0) {
      const file = files[0]
      onVideoChange?.(file.url, file.name)
      toast({
        title: 'Video uploaded',
        description: `${file.name} has been uploaded successfully`
      })
    }
  }

  const togglePlayPause = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
  }

  const handleSeek = (newTime: number[]) => {
    const video = videoRef.current
    if (!video) return

    video.currentTime = newTime[0]
    setCurrentTime(newTime[0])
  }

  const handleVolumeChange = (newVolume: number[]) => {
    const video = videoRef.current
    if (!video) return

    const volumeValue = newVolume[0] / 100
    video.volume = volumeValue
    setVolume(newVolume[0])
    setIsMuted(volumeValue === 0)
  }

  const toggleMute = () => {
    const video = videoRef.current
    if (!video) return

    if (isMuted) {
      video.volume = volume / 100
      setIsMuted(false)
    } else {
      video.volume = 0
      setIsMuted(true)
    }
  }

  const handleSpeedChange = (speed: string) => {
    const video = videoRef.current
    if (!video) return

    const speedValue = parseFloat(speed)
    video.playbackRate = speedValue
    setPlaybackSpeed(speedValue)
  }

  const skip = (seconds: number) => {
    const video = videoRef.current
    if (!video) return

    video.currentTime = Math.max(0, Math.min(duration, video.currentTime + seconds))
  }

  const toggleFullscreen = () => {
    if (!isFullscreen && containerRef.current) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    }
    setIsFullscreen(!isFullscreen)
  }

  const handleMouseMove = () => {
    setShowControls(true)
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }
    controlsTimeoutRef.current = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false)
      }
    }, 3000)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const getYouTubeEmbedUrl = (url: string) => {
    const videoId = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)
    return videoId ? `https://www.youtube.com/embed/${videoId[1]}?autoplay=${autoplay ? 1 : 0}&controls=1` : url
  }

  const getVimeoEmbedUrl = (url: string) => {
    const videoId = url.match(/vimeo\.com\/(\d+)/)
    return videoId ? `https://player.vimeo.com/video/${videoId[1]}?autoplay=${autoplay ? 1 : 0}` : url
  }

  const renderVideoContent = () => {
    if (!videoUrl) {
      return (
        <div className="flex flex-col items-center justify-center h-96 bg-gray-900 rounded-lg">
          <Play className="h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No video selected</h3>
          <p className="text-gray-400 mb-4">Upload a video or provide a URL to start</p>
          
          <FileUploader
            onUploadComplete={handleVideoUpload}
            maxFiles={1}
            acceptedFileTypes={['video/*']}
            maxFileSize={500 * 1024 * 1024} // 500MB
            showPreview={false}
          />
        </div>
      )
    }

    switch (videoType) {
      case 'youtube':
        return (
          <iframe
            src={getYouTubeEmbedUrl(videoUrl)}
            className="w-full h-full min-h-96 rounded-lg"
            title={videoTitle || 'YouTube Video'}
            allowFullScreen
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          />
        )

      case 'vimeo':
        return (
          <iframe
            src={getVimeoEmbedUrl(videoUrl)}
            className="w-full h-full min-h-96 rounded-lg"
            title={videoTitle || 'Vimeo Video'}
            allowFullScreen
            allow="autoplay; fullscreen; picture-in-picture"
          />
        )

      case 'direct':
        return (
          <div 
            className="relative bg-black rounded-lg overflow-hidden"
            onMouseMove={handleMouseMove}
            onMouseLeave={() => isPlaying && setShowControls(false)}
          >
            <video
              ref={videoRef}
              src={videoUrl}
              className="w-full h-full min-h-96 object-contain"
              autoPlay={autoplay}
              onLoadedMetadata={() => setDuration(videoRef.current?.duration || 0)}
            />

            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
              </div>
            )}

            {/* Custom Video Controls */}
            <div className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}>
              {/* Progress Bar */}
              <div className="mb-4">
                <Slider
                  value={[currentTime]}
                  onValueChange={handleSeek}
                  max={duration}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-white mt-1">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(duration)}</span>
                </div>
              </div>

              {/* Control Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => skip(-10)}
                    className="text-white hover:bg-white hover:bg-opacity-20"
                  >
                    <SkipBack className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={togglePlayPause}
                    className="text-white hover:bg-white hover:bg-opacity-20"
                  >
                    {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => skip(10)}
                    className="text-white hover:bg-white hover:bg-opacity-20"
                  >
                    <SkipForward className="h-4 w-4" />
                  </Button>

                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleMute}
                      className="text-white hover:bg-white hover:bg-opacity-20"
                    >
                      {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                    </Button>

                    <div className="w-20">
                      <Slider
                        value={[isMuted ? 0 : volume]}
                        onValueChange={handleVolumeChange}
                        max={100}
                        step={1}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {allowSpeedControl && (
                    <Select value={playbackSpeed.toString()} onValueChange={handleSpeedChange}>
                      <SelectTrigger className="w-20 h-8 text-white border-white border-opacity-30">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0.5">0.5x</SelectItem>
                        <SelectItem value="0.75">0.75x</SelectItem>
                        <SelectItem value="1">1x</SelectItem>
                        <SelectItem value="1.25">1.25x</SelectItem>
                        <SelectItem value="1.5">1.5x</SelectItem>
                        <SelectItem value="2">2x</SelectItem>
                      </SelectContent>
                    </Select>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSubtitles(!showSubtitles)}
                    className="text-white hover:bg-white hover:bg-opacity-20"
                  >
                    <Subtitles className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggleFullscreen}
                    className="text-white hover:bg-white hover:bg-opacity-20"
                  >
                    {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return (
          <div className="flex flex-col items-center justify-center h-96 bg-gray-900 rounded-lg">
            <Play className="h-16 w-16 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">Unsupported video format</h3>
            <p className="text-gray-400 mb-4">Please upload a supported video file or provide a valid URL</p>
          </div>
        )
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            Video Player
            {videoTitle && (
              <Badge variant="secondary" className="ml-2">
                {videoTitle}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {onVideoChange && (
              <FileUploader
                onUploadComplete={handleVideoUpload}
                maxFiles={1}
                acceptedFileTypes={['video/*']}
                maxFileSize={500 * 1024 * 1024}
                showPreview={false}
                trigger={
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload
                  </Button>
                }
              />
            )}
            
            {allowDownload && videoUrl && videoType === 'direct' && (
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            )}

            {showAnalytics && (
              <Button variant="outline" size="sm">
                <BarChart3 className="h-4 w-4 mr-2" />
                Analytics
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Video Content */}
        <div 
          ref={containerRef}
          className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-black p-4' : ''}`}
        >
          {renderVideoContent()}
        </div>

        {/* Video Settings */}
        {videoUrl && (
          <div className="flex items-center justify-between text-sm text-gray-500 pt-2 border-t">
            <div className="flex items-center gap-4">
              <span>Type: {videoType.toUpperCase()}</span>
              {duration > 0 && <span>Duration: {formatTime(duration)}</span>}
              {videoType === 'direct' && <span>Speed: {playbackSpeed}x</span>}
            </div>
            
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Switch 
                  id="autoplay" 
                  checked={autoplay}
                  onCheckedChange={() => {}}
                  disabled
                />
                <Label htmlFor="autoplay">Autoplay</Label>
              </div>
              
              <Badge variant="outline">
                <Play className="h-3 w-3 mr-1" />
                {isPlaying ? 'Playing' : 'Paused'}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default VideoPlayer
