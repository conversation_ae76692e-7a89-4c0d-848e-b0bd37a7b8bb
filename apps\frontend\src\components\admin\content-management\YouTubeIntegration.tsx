'use client'

import React, { useState, useEffect } from 'react'
import { 
  Play, 
  Search, 
  ExternalLink, 
  Clock, 
  Eye, 
  Calendar,
  User,
  List,
  CheckCircle,
  AlertCircle,
  Loader2,
  Copy,
  Download
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { useYouTubeMetadata } from '@/hooks/useYouTubeMetadata'

export interface YouTubeVideoData {
  id: string
  title: string
  description: string
  thumbnail: string
  duration: number
  publishedAt: string
  channelTitle: string
  viewCount: number
  embedUrl: string
  originalUrl: string
}

export interface PlaylistData {
  id: string
  title: string
  description: string
  videoCount: number
  videos: YouTubeVideoData[]
  thumbnail: string
}

export interface YouTubeIntegrationProps {
  onVideoSelect?: (videoData: YouTubeVideoData) => void
  onPlaylistSelect?: (playlistData: PlaylistData) => void
  allowPlaylists?: boolean
  maxVideos?: number
  showPreview?: boolean
  className?: string
}

export const YouTubeIntegration: React.FC<YouTubeIntegrationProps> = ({
  onVideoSelect,
  onPlaylistSelect,
  allowPlaylists = true,
  maxVideos = 50,
  showPreview = true,
  className = ''
}) => {
  const [url, setUrl] = useState('')
  const [isValidating, setIsValidating] = useState(false)
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean
    type: 'video' | 'playlist' | 'unknown'
    message: string
  } | null>(null)
  const [selectedVideo, setSelectedVideo] = useState<YouTubeVideoData | null>(null)
  const [selectedPlaylist, setSelectedPlaylist] = useState<PlaylistData | null>(null)
  const [autoExtractMetadata, setAutoExtractMetadata] = useState(true)

  const { toast } = useToast()
  const { extractVideoMetadata, extractPlaylistMetadata, isLoading } = useYouTubeMetadata()

  // YouTube URL validation patterns
  const validateYouTubeUrl = (url: string): { isValid: boolean; type: 'video' | 'playlist' | 'unknown'; message: string } => {
    const videoPatterns = [
      /^https?:\/\/(www\.)?youtube\.com\/watch\?v=[\w-]+/,
      /^https?:\/\/youtu\.be\/[\w-]+/,
      /^https?:\/\/(www\.)?youtube\.com\/embed\/[\w-]+/
    ]
    
    const playlistPatterns = [
      /^https?:\/\/(www\.)?youtube\.com\/playlist\?list=[\w-]+/,
      /^https?:\/\/(www\.)?youtube\.com\/watch\?v=[\w-]+&list=[\w-]+/
    ]

    if (!url.trim()) {
      return { isValid: false, type: 'unknown', message: 'Please enter a YouTube URL' }
    }

    // Check for playlist patterns first
    if (playlistPatterns.some(pattern => pattern.test(url))) {
      if (!allowPlaylists) {
        return { isValid: false, type: 'playlist', message: 'Playlists are not allowed' }
      }
      return { isValid: true, type: 'playlist', message: 'Valid YouTube playlist URL' }
    }

    // Check for video patterns
    if (videoPatterns.some(pattern => pattern.test(url))) {
      return { isValid: true, type: 'video', message: 'Valid YouTube video URL' }
    }

    return { isValid: false, type: 'unknown', message: 'Invalid YouTube URL format' }
  }

  const handleUrlChange = (newUrl: string) => {
    setUrl(newUrl)
    setSelectedVideo(null)
    setSelectedPlaylist(null)
    
    if (newUrl.trim()) {
      const validation = validateYouTubeUrl(newUrl)
      setValidationResult(validation)
      
      // Auto-extract metadata if enabled and URL is valid
      if (validation.isValid && autoExtractMetadata) {
        handleExtractMetadata(newUrl, validation.type)
      }
    } else {
      setValidationResult(null)
    }
  }

  const handleExtractMetadata = async (urlToProcess: string = url, type?: 'video' | 'playlist') => {
    if (!urlToProcess.trim()) return

    const validation = type ? { type } : validateYouTubeUrl(urlToProcess)
    if (!validation.isValid) {
      toast({
        title: 'Invalid URL',
        description: validation.message,
        variant: 'destructive'
      })
      return
    }

    setIsValidating(true)
    
    try {
      if (validation.type === 'video') {
        const videoData = await extractVideoMetadata(urlToProcess)
        if (videoData) {
          setSelectedVideo(videoData)
          toast({
            title: 'Video metadata extracted',
            description: `Successfully loaded: ${videoData.title}`
          })
        }
      } else if (validation.type === 'playlist') {
        const playlistData = await extractPlaylistMetadata(urlToProcess, maxVideos)
        if (playlistData) {
          setSelectedPlaylist(playlistData)
          toast({
            title: 'Playlist metadata extracted',
            description: `Successfully loaded: ${playlistData.title} (${playlistData.videoCount} videos)`
          })
        }
      }
    } catch (error) {
      toast({
        title: 'Extraction failed',
        description: 'Failed to extract metadata from YouTube URL',
        variant: 'destructive'
      })
    } finally {
      setIsValidating(false)
    }
  }

  const handleSelectVideo = () => {
    if (selectedVideo && onVideoSelect) {
      onVideoSelect(selectedVideo)
      toast({
        title: 'Video selected',
        description: `${selectedVideo.title} has been added to your content`
      })
    }
  }

  const handleSelectPlaylist = () => {
    if (selectedPlaylist && onPlaylistSelect) {
      onPlaylistSelect(selectedPlaylist)
      toast({
        title: 'Playlist selected',
        description: `${selectedPlaylist.title} has been added to your content`
      })
    }
  }

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M views`
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K views`
    }
    return `${count} views`
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* URL Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            YouTube Integration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="youtube-url">YouTube URL</Label>
            <div className="flex gap-2">
              <Input
                id="youtube-url"
                data-testid="youtube-url"
                placeholder="https://www.youtube.com/watch?v=... or https://www.youtube.com/playlist?list=..."
                value={url}
                onChange={(e) => handleUrlChange(e.target.value)}
                className={validationResult?.isValid === false ? 'border-red-500' : ''}
              />
              <Button 
                onClick={() => handleExtractMetadata()}
                disabled={!validationResult?.isValid || isValidating || isLoading}
                data-testid="validate-url"
              >
                {isValidating || isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>
            
            {/* Validation Status */}
            {validationResult && (
              <div className={`flex items-center gap-2 text-sm ${
                validationResult.isValid ? 'text-green-600' : 'text-red-600'
              }`}>
                {validationResult.isValid ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                {validationResult.message}
                {validationResult.isValid && (
                  <Badge variant="outline" className="ml-2">
                    {validationResult.type}
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Auto-extract toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              id="auto-extract"
              checked={autoExtractMetadata}
              onCheckedChange={setAutoExtractMetadata}
            />
            <Label htmlFor="auto-extract">Auto-extract metadata</Label>
          </div>
        </CardContent>
      </Card>

      {/* Video Preview */}
      {selectedVideo && showPreview && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Video Preview</span>
              <Button onClick={handleSelectVideo} data-testid="select-video">
                Select Video
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="aspect-video">
                <iframe
                  src={selectedVideo.embedUrl}
                  className="w-full h-full rounded-lg"
                  title={selectedVideo.title}
                  allowFullScreen
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                />
              </div>
              
              <div className="space-y-3">
                <h3 className="font-semibold text-lg" data-testid="video-title">
                  {selectedVideo.title}
                </h3>
                
                <div className="flex flex-wrap gap-2 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    {selectedVideo.channelTitle}
                  </div>
                  <div className="flex items-center gap-1" data-testid="video-duration">
                    <Clock className="h-4 w-4" />
                    {formatDuration(selectedVideo.duration)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    {formatViewCount(selectedVideo.viewCount)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {new Date(selectedVideo.publishedAt).toLocaleDateString()}
                  </div>
                </div>

                {selectedVideo.description && (
                  <div className="space-y-2">
                    <Label>Description</Label>
                    <Textarea
                      value={selectedVideo.description.substring(0, 200) + '...'}
                      readOnly
                      className="text-sm"
                      rows={3}
                    />
                  </div>
                )}

                <div className="flex gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <a href={selectedVideo.originalUrl} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View on YouTube
                    </a>
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => navigator.clipboard.writeText(selectedVideo.originalUrl)}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy URL
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Playlist Preview */}
      {selectedPlaylist && showPreview && allowPlaylists && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <List className="h-5 w-5" />
                Playlist Preview
              </span>
              <Button onClick={handleSelectPlaylist} data-testid="select-playlist">
                Import Playlist
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <img 
                  src={selectedPlaylist.thumbnail} 
                  alt={selectedPlaylist.title}
                  className="w-32 h-24 object-cover rounded-lg"
                />
                <div className="flex-1 space-y-2">
                  <h3 className="font-semibold text-lg">{selectedPlaylist.title}</h3>
                  <p className="text-sm text-gray-600">
                    {selectedPlaylist.videoCount} videos
                  </p>
                  {selectedPlaylist.description && (
                    <p className="text-sm text-gray-700">
                      {selectedPlaylist.description.substring(0, 150)}...
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Videos in Playlist (showing first 5)</Label>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {selectedPlaylist.videos.slice(0, 5).map((video, index) => (
                    <div key={video.id} className="flex items-center gap-3 p-2 border rounded-lg">
                      <span className="text-sm text-gray-500 w-6">{index + 1}</span>
                      <img 
                        src={video.thumbnail} 
                        alt={video.title}
                        className="w-16 h-12 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{video.title}</p>
                        <p className="text-xs text-gray-500">
                          {formatDuration(video.duration)} • {video.channelTitle}
                        </p>
                      </div>
                    </div>
                  ))}
                  {selectedPlaylist.videos.length > 5 && (
                    <p className="text-sm text-gray-500 text-center py-2">
                      ... and {selectedPlaylist.videos.length - 5} more videos
                    </p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
