'use client'

import { useState, useEffect } from 'react'
import {
  Building2,
  Users,
  DollarSign,
  TrendingUp,
  Clock,
  Globe,
  UserPlus,
  AlertCircle,
  CheckCircle,
  BarChart3,
  PieChart,
  Activity,
  FileText
} from 'lucide-react'
import { ResponsiveGrid, ResponsiveCard, ResponsiveSection } from '@/components/shared/layout/ResponsiveContainer'
import { useResponsive } from '@/hooks/useResponsive'

// Mock data - in real app this would come from API
const mockDashboardData = {
  stats: {
    totalInstitutes: 156,
    activeInstitutes: 142,
    pendingVerification: 3,
    totalUsers: 12847,
    newUsersToday: 23,
    totalRevenue: 245670,
    monthlyGrowth: 12.5,
    domainRequests: 1
  },
  recentActivity: [
    {
      id: 1,
      type: 'institute_registration',
      message: 'New institute "Tech Academy" registered',
      timestamp: '2 minutes ago',
      status: 'pending'
    },
    {
      id: 2,
      type: 'domain_request',
      message: 'Domain verification request for "academy.edu"',
      timestamp: '15 minutes ago',
      status: 'pending'
    },
    {
      id: 3,
      type: 'payment',
      message: 'Payment of $299 received from "Science Institute"',
      timestamp: '1 hour ago',
      status: 'completed'
    },
    {
      id: 4,
      type: 'user_registration',
      message: '12 new students registered today',
      timestamp: '2 hours ago',
      status: 'info'
    }
  ],
  topInstitutes: [
    { name: 'Tech Academy', students: 1250, revenue: 15600, growth: 8.2 },
    { name: 'Science Institute', students: 980, revenue: 12400, growth: 12.1 },
    { name: 'Business School', students: 750, revenue: 9800, growth: -2.3 },
    { name: 'Art College', students: 650, revenue: 8200, growth: 15.7 },
    { name: 'Medical Academy', students: 580, revenue: 7300, growth: 6.8 }
  ],
  systemHealth: {
    uptime: '99.9%',
    responseTime: '145ms',
    errorRate: '0.02%',
    activeConnections: 1247
  }
}

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  icon: React.ComponentType<any>
  color: string
  href?: string
}

function MetricCard({ title, value, change, icon: Icon, color, href }: MetricCardProps) {
  const { isMobile } = useResponsive()
  
  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      if (val >= 1000000) return `${(val / 1000000).toFixed(1)}M`
      if (val >= 1000) return `${(val / 1000).toFixed(1)}K`
      return val.toLocaleString()
    }
    return val
  }

  const card = (
    <ResponsiveCard className="hover:shadow-lg transition-shadow cursor-pointer">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-500 mb-1">
            {title}
          </div>
          <div className={`text-2xl font-bold text-gray-900 mb-2 ${isMobile ? 'text-xl' : ''}`}>
            {formatValue(value)}
          </div>
          {change !== undefined && (
            <div className={`flex items-center text-sm ${
              change >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              <TrendingUp className={`w-4 h-4 mr-1 ${change < 0 ? 'rotate-180' : ''}`} />
              <span>{Math.abs(change)}% from last month</span>
            </div>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </ResponsiveCard>
  )

  if (href) {
    return (
      <a href={href} className="block">
        {card}
      </a>
    )
  }

  return card
}

function ActivityItem({ activity }: { activity: any }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      default:
        return <Activity className="w-4 h-4 text-blue-500" />
    }
  }

  return (
    <div className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
      <div className="flex-shrink-0 mt-0.5">
        {getStatusIcon(activity.status)}
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-sm text-gray-900">{activity.message}</div>
        <div className="text-xs text-gray-500 mt-1">{activity.timestamp}</div>
      </div>
    </div>
  )
}

export function SuperAdminDashboard() {
  const { isMobile } = useResponsive()
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d')

  const metrics = [
    {
      title: 'Total Institutes',
      value: mockDashboardData.stats.totalInstitutes,
      change: 8.2,
      icon: Building2,
      color: 'bg-blue-600',
      href: '/super-admin/institutes'
    },
    {
      title: 'Active Users',
      value: mockDashboardData.stats.totalUsers,
      change: 12.5,
      icon: Users,
      color: 'bg-green-600',
      href: '/super-admin/users'
    },
    {
      title: 'Monthly Revenue',
      value: `$${mockDashboardData.stats.totalRevenue.toLocaleString()}`,
      change: mockDashboardData.stats.monthlyGrowth,
      icon: DollarSign,
      color: 'bg-purple-600',
      href: '/super-admin/billing'
    },
    {
      title: 'Platform Blog',
      value: '24',
      change: 15.3,
      icon: FileText,
      color: 'bg-indigo-600',
      href: '/super-admin/platform-blog'
    },
    {
      title: 'Pending Actions',
      value: mockDashboardData.stats.pendingVerification + mockDashboardData.stats.domainRequests,
      icon: AlertCircle,
      color: 'bg-orange-600',
      href: '/super-admin/institutes/pending'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className={`font-bold text-gray-900 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
            Super Admin Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Platform overview and key metrics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          >
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <ResponsiveGrid
        mobileColumns={1}
        tabletColumns={2}
        desktopColumns={4}
        gap={6}
      >
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </ResponsiveGrid>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <ResponsiveCard>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
              <a href="/super-admin/activity" className="text-sm text-blue-600 hover:text-blue-700">
                View all
              </a>
            </div>
            <div className="space-y-1">
              {mockDashboardData.recentActivity.map((activity) => (
                <ActivityItem key={activity.id} activity={activity} />
              ))}
            </div>
          </ResponsiveCard>
        </div>

        {/* Quick Actions */}
        <div>
          <ResponsiveCard>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <a
                href="/super-admin/institutes/pending"
                className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">
                    Pending Verifications
                  </span>
                </div>
                <span className="text-sm font-bold text-yellow-800">
                  {mockDashboardData.stats.pendingVerification}
                </span>
              </a>

              <a
                href="/super-admin/institutes/domains"
                className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <Globe className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">
                    Domain Requests
                  </span>
                </div>
                <span className="text-sm font-bold text-blue-800">
                  {mockDashboardData.stats.domainRequests}
                </span>
              </a>

              <a
                href="/super-admin/users"
                className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <UserPlus className="w-5 h-5 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    New Users Today
                  </span>
                </div>
                <span className="text-sm font-bold text-green-800">
                  {mockDashboardData.stats.newUsersToday}
                </span>
              </a>
            </div>
          </ResponsiveCard>
        </div>
      </div>

      {/* Top Institutes and System Health */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Institutes */}
        <ResponsiveCard>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Top Performing Institutes</h3>
            <BarChart3 className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-3">
            {mockDashboardData.topInstitutes.map((institute, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-gray-900">{institute.name}</div>
                  <div className="text-xs text-gray-500">{institute.students} students</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    ${institute.revenue.toLocaleString()}
                  </div>
                  <div className={`text-xs ${
                    institute.growth >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {institute.growth >= 0 ? '+' : ''}{institute.growth}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ResponsiveCard>

        {/* System Health */}
        <ResponsiveCard>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">System Health</h3>
            <Activity className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Uptime</span>
              <span className="text-sm font-medium text-green-600">
                {mockDashboardData.systemHealth.uptime}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Response Time</span>
              <span className="text-sm font-medium text-gray-900">
                {mockDashboardData.systemHealth.responseTime}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Error Rate</span>
              <span className="text-sm font-medium text-green-600">
                {mockDashboardData.systemHealth.errorRate}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Active Connections</span>
              <span className="text-sm font-medium text-gray-900">
                {mockDashboardData.systemHealth.activeConnections.toLocaleString()}
              </span>
            </div>
          </div>
        </ResponsiveCard>
      </div>
    </div>
  )
}

export default SuperAdminDashboard
