'use client'

import { useState } from 'react'
import { 
  Breadcrumbs, 
  CustomBreadcrumbs 
} from '@/components/shared/navigation/Breadcrumbs'
import { NavigationSearch } from '@/components/shared/navigation/NavigationSearch'
import { ResponsiveCard, ResponsiveGrid } from '@/components/shared/layout/ResponsiveContainer'
import { 
  Home, 
  Settings, 
  Users, 
  Building2,
  Search,
  Navigation,
  ArrowRight,
  ChevronRight,
  Command,
  Clock,
  Star,
  Zap
} from 'lucide-react'

export function BreadcrumbsSearchDemo() {
  const [selectedPath, setSelectedPath] = useState('/super-admin/staff/analytics')
  const [searchQuery, setSearchQuery] = useState('')

  const demoPages = [
    {
      path: '/super-admin',
      label: 'Super Admin Dashboard',
      description: 'Main dashboard with overview'
    },
    {
      path: '/super-admin/institutes',
      label: 'Institute Management',
      description: 'Manage all institutes'
    },
    {
      path: '/super-admin/institutes/pending',
      label: 'Pending Verification',
      description: 'Institutes awaiting verification'
    },
    // {
    //   path: '/super-admin/staff',
    //   label: 'Staff Management',
    //   description: 'Manage platform staff'
    // },
    // {
    //   path: '/super-admin/staff/roles',
    //   label: 'Roles & Permissions',
    //   description: 'Configure roles and permissions'
    // },
    // {
    //   path: '/super-admin/staff/analytics',
    //   label: 'Staff Analytics',
    //   description: 'Staff performance metrics'
    // },
    {
      path: '/institute-admin/courses',
      label: 'Course Management',
      description: 'Manage institute courses'
    },
    {
      path: '/institute-admin/students/progress',
      label: 'Student Progress',
      description: 'Track student learning progress'
    },
    {
      path: '/student/courses/active',
      label: 'Active Courses',
      description: 'Currently enrolled courses'
    }
  ]

  const customBreadcrumbExamples = [
    {
      title: 'Simple Breadcrumbs',
      items: [
        { label: 'Home', href: '/', icon: Home },
        { label: 'Settings', href: '/settings', icon: Settings },
        { label: 'Profile', isActive: true }
      ]
    },
    {
      title: 'Deep Navigation',
      items: [
        { label: 'Dashboard', href: '/', icon: Home },
        { label: 'Institute Management', href: '/institutes', icon: Building2 },
        { label: 'Staff', href: '/institutes/staff', icon: Users },
        { label: 'Analytics', isActive: true }
      ]
    },
    {
      title: 'With Icons',
      items: [
        { label: 'Home', href: '/', icon: Home },
        { label: 'Users', href: '/users', icon: Users },
        { label: 'John Doe', isActive: true }
      ]
    }
  ]

  const searchFeatures = [
    {
      title: 'Keyboard Shortcuts',
      description: 'Press Cmd/Ctrl + K to quickly open search',
      icon: Command
    },
    {
      title: 'Recent Items',
      description: 'Shows recently accessed navigation items',
      icon: Clock
    },
    {
      title: 'Favorites',
      description: 'Quick access to your favorite sections',
      icon: Star
    },
    {
      title: 'Quick Actions',
      description: 'Items with pending actions or notifications',
      icon: Zap
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Breadcrumbs & Search Demo
        </h1>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Explore the navigation breadcrumbs and search functionality. 
          The breadcrumbs automatically adapt to the current page and user type, 
          while the search provides quick access to any navigation item.
        </p>
      </div>

      {/* Live Breadcrumbs Demo */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Live Breadcrumbs Demo</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select a page to see breadcrumbs:
            </label>
            <select
              value={selectedPath}
              onChange={(e) => setSelectedPath(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {demoPages.map((page) => (
                <option key={page.path} value={page.path}>
                  {page.label} ({page.path})
                </option>
              ))}
            </select>
          </div>

          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="text-sm text-gray-600 mb-2">
              Breadcrumbs for: <code className="bg-white px-2 py-1 rounded">{selectedPath}</code>
            </div>
            <div className="bg-white p-3 rounded border">
              {/* This would show actual breadcrumbs based on selectedPath */}
              <nav className="flex items-center space-x-1 text-sm">
                <Home className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600 hover:text-gray-900 cursor-pointer">Dashboard</span>
                <ChevronRight className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600 hover:text-gray-900 cursor-pointer">
                  {selectedPath.split('/')[2]?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
                {selectedPath.split('/').length > 3 && (
                  <>
                    <ChevronRight className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-900 font-medium">
                      {selectedPath.split('/')[3]?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  </>
                )}
              </nav>
            </div>
          </div>
        </div>
      </ResponsiveCard>

      {/* Custom Breadcrumbs Examples */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Custom Breadcrumbs Examples</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={1} desktopColumns={1} gap={4}>
          {customBreadcrumbExamples.map((example, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">{example.title}</h4>
              <div className="bg-white p-3 rounded border">
                <CustomBreadcrumbs items={example.items} />
              </div>
            </div>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Navigation Search Demo */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Navigation Search Demo</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Try searching for navigation items:
            </label>
            <NavigationSearch 
              placeholder="Search navigation items..."
              showShortcut={true}
              onItemSelect={(item) => {
                console.log('Selected item:', item)
              }}
            />
          </div>

          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Search Tips:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Press <kbd className="bg-white px-2 py-1 rounded text-xs">Cmd/Ctrl + K</kbd> to open search quickly</li>
              <li>• Use arrow keys to navigate results</li>
              <li>• Press <kbd className="bg-white px-2 py-1 rounded text-xs">Enter</kbd> to select an item</li>
              <li>• Press <kbd className="bg-white px-2 py-1 rounded text-xs">Escape</kbd> to close search</li>
            </ul>
          </div>
        </div>
      </ResponsiveCard>

      {/* Search Features */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Search Features</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={2} desktopColumns={4} gap={4}>
          {searchFeatures.map((feature, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <feature.icon className="w-5 h-5 text-blue-600" />
                <h4 className="font-medium text-gray-900">{feature.title}</h4>
              </div>
              <p className="text-sm text-gray-600">{feature.description}</p>
            </div>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Responsive Behavior */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Responsive Behavior</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2">Desktop & Tablet</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Full breadcrumb trail with all levels</li>
                <li>• Inline search with keyboard shortcuts</li>
                <li>• Hover effects and tooltips</li>
                <li>• Overflow handling with ellipsis</li>
              </ul>
            </div>

            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <h4 className="font-medium text-orange-900 mb-2">Mobile</h4>
              <ul className="text-sm text-orange-800 space-y-1">
                <li>• Simplified breadcrumbs (parent + current)</li>
                <li>• Modal search interface</li>
                <li>• Touch-friendly interactions</li>
                <li>• Optimized for small screens</li>
              </ul>
            </div>
          </div>
        </div>
      </ResponsiveCard>

      {/* Implementation Notes */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Implementation Highlights</h3>
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Automatic Breadcrumbs</h4>
            <p className="text-sm text-blue-800">
              Breadcrumbs are automatically generated from the current URL path and navigation structure. 
              They adapt to different user types and show relevant navigation hierarchy.
            </p>
          </div>
          
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Intelligent Search</h4>
            <p className="text-sm text-green-800">
              The search functionality indexes all navigation items and provides fuzzy matching. 
              It includes recent items, favorites, and quick actions for better user experience.
            </p>
          </div>
          
          <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 className="font-medium text-purple-900 mb-2">Accessibility</h4>
            <p className="text-sm text-purple-800">
              Both breadcrumbs and search are fully accessible with proper ARIA labels, 
              keyboard navigation, and screen reader support.
            </p>
          </div>
        </div>
      </ResponsiveCard>
    </div>
  )
}

export default BreadcrumbsSearchDemo
