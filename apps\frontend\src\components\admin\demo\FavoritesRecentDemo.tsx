'use client'

import { useState } from 'react'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { FavoritesPanel, CompactFavorites } from '@/components/shared/navigation/FavoritesPanel'
import { RecentItemsPanel, CompactRecentItems, RecentItemsDropdown } from '@/components/shared/navigation/RecentItemsPanel'
import { ResponsiveCard, ResponsiveGrid } from '@/components/shared/layout/ResponsiveContainer'
import { 
  Star, 
  Clock, 
  Heart,
  Bookmark,
  History,
  Zap,
  TrendingUp,
  Users,
  Settings,
  BarChart3,
  Building2
} from 'lucide-react'

export function FavoritesRecentDemo() {
  const { 
    favoriteItems, 
    recentItems, 
    navigationItems,
    addToFavorites,
    removeFromFavorites,
    addToRecent,
    clearRecent
  } = useSidebarStore()
  
  const [activeTab, setActiveTab] = useState<'favorites' | 'recent'>('favorites')

  // Demo actions to populate favorites and recent items
  const demoActions = [
    {
      id: 'add-demo-favorites',
      label: 'Add Demo Favorites',
      description: 'Add some navigation items to favorites for demonstration',
      action: () => {
        const demoFavorites = ['dashboard', 'staff', 'institutes', 'analytics']
        demoFavorites.forEach(id => {
          if (!favoriteItems.includes(id)) {
            addToFavorites(id)
          }
        })
      },
      icon: Star,
      color: 'bg-yellow-600'
    },
    {
      id: 'add-demo-recent',
      label: 'Add Demo Recent Items',
      description: 'Add some items to recent history for demonstration',
      action: () => {
        const demoRecent = ['users', 'billing', 'themes', 'settings']
        demoRecent.forEach(id => addToRecent(id))
      },
      icon: Clock,
      color: 'bg-blue-600'
    },
    {
      id: 'clear-favorites',
      label: 'Clear All Favorites',
      description: 'Remove all items from favorites',
      action: () => {
        favoriteItems.forEach(id => removeFromFavorites(id))
      },
      icon: Star,
      color: 'bg-red-600'
    },
    {
      id: 'clear-recent',
      label: 'Clear Recent Items',
      description: 'Clear the recent items history',
      action: clearRecent,
      icon: History,
      color: 'bg-gray-600'
    }
  ]

  const features = [
    {
      title: 'Smart Favorites',
      description: 'Users can mark frequently used navigation items as favorites for quick access',
      icon: Star,
      benefits: [
        'One-click access to important sections',
        'Persistent across sessions',
        'Visual indicators in navigation',
        'Search within favorites'
      ]
    },
    {
      title: 'Recent History',
      description: 'Automatically tracks recently accessed navigation items',
      icon: Clock,
      benefits: [
        'Automatic tracking of user activity',
        'Quick return to recent sections',
        'Configurable history length',
        'Time-based organization'
      ]
    },
    {
      title: 'Quick Access',
      description: 'Both favorites and recent items provide quick access patterns',
      icon: Zap,
      benefits: [
        'Reduced navigation time',
        'Improved user productivity',
        'Personalized experience',
        'Mobile-optimized interface'
      ]
    }
  ]

  const usageStats = {
    favorites: {
      total: favoriteItems.length,
      maxAllowed: 20,
      mostUsed: favoriteItems.length > 0 ? 'Dashboard' : 'None',
      avgPerUser: 5.2
    },
    recent: {
      total: recentItems.length,
      maxStored: 50,
      oldestItem: recentItems.length > 0 ? '2 hours ago' : 'None',
      avgPerUser: 12.8
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Favorites & Recent Items Demo
        </h1>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Explore the favorites and recent items functionality that helps users 
          quickly access their most important and recently used navigation items.
        </p>
      </div>

      {/* Demo Actions */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Demo Actions</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={2} desktopColumns={4} gap={4}>
          {demoActions.map((action) => (
            <button
              key={action.id}
              onClick={action.action}
              className="p-4 text-left border border-gray-200 rounded-lg hover:shadow-md hover:border-gray-300 transition-all duration-200"
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${action.color}`}>
                  <action.icon className="w-4 h-4 text-white" />
                </div>
                <div className="font-medium text-gray-900">{action.label}</div>
              </div>
              <p className="text-sm text-gray-600">{action.description}</p>
            </button>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Usage Statistics */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Favorites Stats */}
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2 mb-3">
              <Star className="w-5 h-5 text-yellow-600" />
              <h4 className="font-medium text-yellow-900">Favorites</h4>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-2xl font-bold text-yellow-900">
                  {usageStats.favorites.total}
                </div>
                <div className="text-yellow-700">Current Favorites</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-900">
                  {usageStats.favorites.maxAllowed}
                </div>
                <div className="text-yellow-700">Max Allowed</div>
              </div>
              <div>
                <div className="text-sm font-medium text-yellow-900">
                  {usageStats.favorites.mostUsed}
                </div>
                <div className="text-yellow-700">Most Used</div>
              </div>
              <div>
                <div className="text-sm font-medium text-yellow-900">
                  {usageStats.favorites.avgPerUser}
                </div>
                <div className="text-yellow-700">Avg per User</div>
              </div>
            </div>
          </div>

          {/* Recent Stats */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2 mb-3">
              <Clock className="w-5 h-5 text-blue-600" />
              <h4 className="font-medium text-blue-900">Recent Items</h4>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-2xl font-bold text-blue-900">
                  {usageStats.recent.total}
                </div>
                <div className="text-blue-700">Recent Items</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-900">
                  {usageStats.recent.maxStored}
                </div>
                <div className="text-blue-700">Max Stored</div>
              </div>
              <div>
                <div className="text-sm font-medium text-blue-900">
                  {usageStats.recent.oldestItem}
                </div>
                <div className="text-blue-700">Oldest Item</div>
              </div>
              <div>
                <div className="text-sm font-medium text-blue-900">
                  {usageStats.recent.avgPerUser}
                </div>
                <div className="text-blue-700">Avg per User</div>
              </div>
            </div>
          </div>
        </div>
      </ResponsiveCard>

      {/* Interactive Demo */}
      <ResponsiveCard>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Interactive Demo</h3>
          
          {/* Tab Navigation */}
          <div className="flex items-center border border-gray-300 rounded-lg">
            <button
              onClick={() => setActiveTab('favorites')}
              className={`px-4 py-2 text-sm font-medium rounded-l-lg transition-colors ${
                activeTab === 'favorites'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <Star className="w-4 h-4 inline mr-2" />
              Favorites
            </button>
            <button
              onClick={() => setActiveTab('recent')}
              className={`px-4 py-2 text-sm font-medium rounded-r-lg transition-colors ${
                activeTab === 'recent'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <Clock className="w-4 h-4 inline mr-2" />
              Recent
            </button>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'favorites' ? (
          <FavoritesPanel showHeader={false} />
        ) : (
          <RecentItemsPanel showHeader={false} />
        )}
      </ResponsiveCard>

      {/* Compact Views */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Compact Sidebar Views</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Compact Favorites */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-3">Compact Favorites</h4>
            <div className="bg-white border border-gray-200 rounded p-2">
              <CompactFavorites />
            </div>
          </div>

          {/* Compact Recent */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-3">Compact Recent Items</h4>
            <div className="bg-white border border-gray-200 rounded p-2">
              <CompactRecentItems />
            </div>
          </div>
        </div>
      </ResponsiveCard>

      {/* Features Overview */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Features Overview</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={1} desktopColumns={3} gap={6}>
          {features.map((feature, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3 mb-3">
                <feature.icon className="w-6 h-6 text-blue-600" />
                <h4 className="font-medium text-gray-900">{feature.title}</h4>
              </div>
              <p className="text-sm text-gray-600 mb-3">{feature.description}</p>
              <ul className="space-y-1">
                {feature.benefits.map((benefit, benefitIndex) => (
                  <li key={benefitIndex} className="text-xs text-gray-500 flex items-center">
                    <div className="w-1 h-1 bg-blue-600 rounded-full mr-2"></div>
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Implementation Notes */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Implementation Highlights</h3>
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Persistent Storage</h4>
            <p className="text-sm text-blue-800">
              Both favorites and recent items are stored in localStorage and persist across browser sessions. 
              The data is automatically synced with the user's preferences.
            </p>
          </div>
          
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Smart Limits</h4>
            <p className="text-sm text-green-800">
              Recent items are automatically managed with a configurable limit (default 50 items). 
              Favorites have a reasonable limit to prevent UI clutter while allowing flexibility.
            </p>
          </div>
          
          <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 className="font-medium text-purple-900 mb-2">User Experience</h4>
            <p className="text-sm text-purple-800">
              The system provides visual feedback for favorite actions, search functionality within collections, 
              and responsive design that adapts to different screen sizes.
            </p>
          </div>
        </div>
      </ResponsiveCard>
    </div>
  )
}

export default FavoritesRecentDemo
