'use client'

import { useState } from 'react'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useResponsive } from '@/hooks/useResponsive'
import { 
  Monitor, 
  Tablet, 
  Smartphone,
  LayoutDashboard,
  Building2,
  Users,
  GraduationCap,
  Settings,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  Star,
  Search
} from 'lucide-react'
import { ResponsiveCard, ResponsiveGrid } from '@/components/shared/layout/ResponsiveContainer'

export function NavigationDemo() {
  const { 
    isCollapsed, 
    toggleSidebar, 
    navigationItems,
    favoriteItems,
    addToFavorites,
    removeFromFavorites
  } = useSidebarStore()
  const { user } = useAuthStore()
  const { isMobile, isTablet, isDesktop } = useResponsive()
  
  const [selectedUserType, setSelectedUserType] = useState<'super_admin' | 'institute_admin' | 'student'>('super_admin')
  const [demoCollapsed, setDemoCollapsed] = useState(false)

  const userTypeOptions = [
    {
      value: 'super_admin',
      label: 'Super Admin',
      description: 'Platform administration and management',
      icon: Settings,
      color: 'bg-red-600'
    },
    {
      value: 'institute_admin',
      label: 'Institute Admin',
      description: 'Institute management and operations',
      icon: Building2,
      color: 'bg-blue-600'
    },
    {
      value: 'student',
      label: 'Student',
      description: 'Learning and course access',
      icon: GraduationCap,
      color: 'bg-green-600'
    }
  ]

  const deviceTypes = [
    {
      type: 'desktop',
      label: 'Desktop',
      icon: Monitor,
      active: isDesktop,
      description: 'Full sidebar with all features'
    },
    {
      type: 'tablet',
      label: 'Tablet',
      icon: Tablet,
      active: isTablet,
      description: 'Collapsible sidebar'
    },
    {
      type: 'mobile',
      label: 'Mobile',
      icon: Smartphone,
      active: isMobile,
      description: 'Bottom navigation + slide-out menu'
    }
  ]

  const sidebarFeatures = [
    {
      title: 'Collapsible Design',
      description: 'Sidebar can be collapsed to show only icons, saving screen space',
      icon: ChevronLeft
    },
    {
      title: 'Hierarchical Navigation',
      description: 'Multi-level navigation with expandable sections and subsections',
      icon: LayoutDashboard
    },
    {
      title: 'Favorites System',
      description: 'Users can mark frequently used items as favorites for quick access',
      icon: Star
    },
    {
      title: 'Search Functionality',
      description: 'Built-in search to quickly find navigation items',
      icon: Search
    },
    {
      title: 'Badge Notifications',
      description: 'Visual indicators for pending actions and notifications',
      icon: Users
    },
    {
      title: 'Responsive Design',
      description: 'Adapts to different screen sizes with mobile-optimized navigation',
      icon: Monitor
    }
  ]

  const navigationStats = {
    super_admin: {
      totalItems: 8,
      sections: 2,
      maxDepth: 2,
      features: ['Institute Management', 'User Management', 'Theme Management', 'Billing', 'Analytics']
    },
    institute_admin: {
      totalItems: 10,
      sections: 2,
      maxDepth: 2,
      features: ['Course Management', 'Student Management', 'Live Classes', 'Exams', 'Marketplace']
    },
    student: {
      totalItems: 10,
      sections: 2,
      maxDepth: 2,
      features: ['My Courses', 'Live Classes', 'Assignments', 'Progress', 'Marketplace']
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Navigation System Demo</h1>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Explore the comprehensive navigation system designed for different user types and devices. 
          The sidebar adapts to user roles and screen sizes for optimal user experience.
        </p>
      </div>

      {/* Current Device Info */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Current Device Detection</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={3} desktopColumns={3} gap={4}>
          {deviceTypes.map((device) => (
            <div
              key={device.type}
              className={`p-4 rounded-lg border-2 transition-colors ${
                device.active 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-3 mb-2">
                <device.icon className={`w-6 h-6 ${
                  device.active ? 'text-blue-600' : 'text-gray-400'
                }`} />
                <span className={`font-medium ${
                  device.active ? 'text-blue-900' : 'text-gray-600'
                }`}>
                  {device.label}
                </span>
                {device.active && (
                  <span className="px-2 py-1 text-xs bg-blue-600 text-white rounded-full">
                    Active
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-600">{device.description}</p>
            </div>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* User Type Selection */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">User Type Navigation</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={3} desktopColumns={3} gap={4}>
          {userTypeOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => setSelectedUserType(option.value as any)}
              className={`p-4 rounded-lg border-2 text-left transition-colors ${
                selectedUserType === option.value
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${option.color}`}>
                  <option.icon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="font-medium text-gray-900">{option.label}</div>
                  {selectedUserType === option.value && (
                    <div className="text-xs text-blue-600">Selected</div>
                  )}
                </div>
              </div>
              <p className="text-sm text-gray-600">{option.description}</p>
            </button>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Navigation Statistics */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Navigation Statistics - {userTypeOptions.find(u => u.value === selectedUserType)?.label}
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {navigationStats[selectedUserType].totalItems}
            </div>
            <div className="text-sm text-gray-600">Total Items</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {navigationStats[selectedUserType].sections}
            </div>
            <div className="text-sm text-gray-600">Sections</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {navigationStats[selectedUserType].maxDepth}
            </div>
            <div className="text-sm text-gray-600">Max Depth</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {navigationStats[selectedUserType].features.length}
            </div>
            <div className="text-sm text-gray-600">Key Features</div>
          </div>
        </div>
        
        <div>
          <h4 className="font-medium text-gray-900 mb-2">Key Features:</h4>
          <div className="flex flex-wrap gap-2">
            {navigationStats[selectedUserType].features.map((feature, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      </ResponsiveCard>

      {/* Sidebar Features */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Sidebar Features</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={2} desktopColumns={3} gap={4}>
          {sidebarFeatures.map((feature, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <feature.icon className="w-5 h-5 text-blue-600" />
                <h4 className="font-medium text-gray-900">{feature.title}</h4>
              </div>
              <p className="text-sm text-gray-600">{feature.description}</p>
            </div>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Interactive Demo */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Interactive Sidebar Demo</h3>
        <div className="flex items-center space-x-4 mb-6">
          <button
            onClick={() => setDemoCollapsed(!demoCollapsed)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {demoCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
            <span>{demoCollapsed ? 'Expand' : 'Collapse'} Sidebar</span>
          </button>
          <span className="text-sm text-gray-600">
            Current state: {demoCollapsed ? 'Collapsed' : 'Expanded'}
          </span>
        </div>
        
        <div className="bg-gray-100 p-4 rounded-lg">
          <div className="text-sm text-gray-600 mb-2">
            Sidebar Preview ({demoCollapsed ? 'Collapsed' : 'Expanded'} mode):
          </div>
          <div className={`bg-white border border-gray-200 rounded-lg transition-all duration-300 ${
            demoCollapsed ? 'w-16' : 'w-64'
          } h-96 p-4`}>
            <div className="flex items-center justify-between mb-4">
              <div className={`font-bold text-gray-900 ${demoCollapsed ? 'hidden' : 'block'}`}>
                {userTypeOptions.find(u => u.value === selectedUserType)?.label}
              </div>
              <button className="p-1 hover:bg-gray-100 rounded">
                {demoCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
              </button>
            </div>
            
            {!demoCollapsed && (
              <div className="space-y-2">
                <div className="p-2 bg-blue-50 text-blue-700 rounded text-sm">
                  <LayoutDashboard className="w-4 h-4 inline mr-2" />
                  Dashboard
                </div>
                <div className="p-2 hover:bg-gray-50 rounded text-sm">
                  <Users className="w-4 h-4 inline mr-2" />
                  Management
                </div>
                <div className="p-2 hover:bg-gray-50 rounded text-sm">
                  <Settings className="w-4 h-4 inline mr-2" />
                  Settings
                </div>
              </div>
            )}
            
            {demoCollapsed && (
              <div className="space-y-2">
                <div className="p-2 bg-blue-50 text-blue-700 rounded flex justify-center">
                  <LayoutDashboard className="w-4 h-4" />
                </div>
                <div className="p-2 hover:bg-gray-50 rounded flex justify-center">
                  <Users className="w-4 h-4" />
                </div>
                <div className="p-2 hover:bg-gray-50 rounded flex justify-center">
                  <Settings className="w-4 h-4" />
                </div>
              </div>
            )}
          </div>
        </div>
      </ResponsiveCard>

      {/* Implementation Notes */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Implementation Highlights</h3>
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Responsive Design</h4>
            <p className="text-sm text-blue-800">
              The navigation system automatically adapts to different screen sizes, providing optimal 
              user experience across desktop, tablet, and mobile devices.
            </p>
          </div>
          
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Role-Based Navigation</h4>
            <p className="text-sm text-green-800">
              Each user type (Super Admin, Institute Admin, Student) has a customized navigation 
              structure with relevant features and appropriate access levels.
            </p>
          </div>
          
          <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 className="font-medium text-purple-900 mb-2">State Management</h4>
            <p className="text-sm text-purple-800">
              Uses Zustand for efficient state management of sidebar state, favorites, recent items, 
              and user preferences across the application.
            </p>
          </div>
        </div>
      </ResponsiveCard>
    </div>
  )
}

export default NavigationDemo
