'use client'

import { useState } from 'react'
import { useToastNotifications } from '@/hooks/useToastNotifications'
import { 
  CheckCircle, 
  AlertCircle, 
  AlertTriangle, 
  Info,
  Users,
  Shield,
  Settings,
  Database,
  Zap
} from 'lucide-react'

export function ToastDemo() {
  const {
    // staff, // REMOVED
    role,
    auth,
    system,
    validation,
    data,
    success,
    error,
    warning,
    info
  } = useToastNotifications()

  const [demoName, setDemoName] = useState('<PERSON>')
  const [demoEmail, setDemoEmail] = useState('<EMAIL>')

  const toastCategories = [
    {
      title: 'Basic Toast Types',
      icon: Zap,
      color: 'bg-blue-100 text-blue-600',
      demos: [
        {
          label: 'Success Toast',
          action: () => success('Operation Successful', 'Your action was completed successfully.'),
          color: 'bg-green-600 hover:bg-green-700'
        },
        {
          label: 'Error Toast',
          action: () => error('Operation Failed', 'Something went wrong. Please try again.'),
          color: 'bg-red-600 hover:bg-red-700'
        },
        {
          label: 'Warning Toast',
          action: () => warning('Warning Message', 'Please review your input before proceeding.'),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Info Toast',
          action: () => info('Information', 'Here is some helpful information for you.'),
          color: 'bg-blue-600 hover:bg-blue-700'
        }
      ]
    },
    // {
    //   title: 'Staff Management', // REMOVED
    //   icon: Users,
    //   color: 'bg-green-100 text-green-600',
    //   demos: [
    //     {
    //       label: 'Create Staff Success',
    //       action: () => staff.createSuccess(demoName),
    //       color: 'bg-green-600 hover:bg-green-700'
    //     },
    //     {
    //       label: 'Update Staff Success',
    //       action: () => staff.updateSuccess(demoName),
    //       color: 'bg-green-600 hover:bg-green-700'
    //     },
    //     {
    //       label: 'Delete Staff Success',
    //       action: () => staff.deleteSuccess(demoName),
    //       color: 'bg-green-600 hover:bg-green-700'
    //     },
    //     {
    //       label: 'Duplicate Email Warning',
    //       action: () => staff.duplicateEmail(demoEmail),
    //       color: 'bg-yellow-600 hover:bg-yellow-700'
    //     },
    //     {
    //       label: 'Create Staff Error',
    //       action: () => staff.createError('Database connection failed'),
    //       color: 'bg-red-600 hover:bg-red-700'
    //     },
    //     {
    //       label: 'Bulk Action Success',
    //       action: () => staff.bulkActionSuccess(5, 'activated'),
    //       color: 'bg-green-600 hover:bg-green-700'
    //     },
    //     {
    //       label: 'Export Success',
    //       action: () => staff.exportSuccess('CSV'),
    //       color: 'bg-blue-600 hover:bg-blue-700'
    //     },
    //     {
    //       label: 'Import Success',
    //       action: () => staff.importSuccess(12),
    //       color: 'bg-green-600 hover:bg-green-700'
    //     }
    //   ]
    // },
    {
      title: 'Role Management',
      icon: Shield,
      color: 'bg-purple-100 text-purple-600',
      demos: [
        {
          label: 'Create Role Success',
          action: () => role.createSuccess('Senior Developer'),
          color: 'bg-green-600 hover:bg-green-700'
        },
        {
          label: 'Update Role Success',
          action: () => role.updateSuccess('Senior Developer'),
          color: 'bg-green-600 hover:bg-green-700'
        },
        {
          label: 'Delete Role Success',
          action: () => role.deleteSuccess('Senior Developer'),
          color: 'bg-green-600 hover:bg-green-700'
        },
        {
          label: 'Role In Use Warning',
          action: () => role.deleteWarning(3),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Duplicate Role Name',
          action: () => role.duplicateName('Senior Developer'),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Permissions Updated',
          action: () => role.permissionUpdateSuccess(),
          color: 'bg-green-600 hover:bg-green-700'
        }
      ]
    },
    {
      title: 'Authentication',
      icon: Settings,
      color: 'bg-indigo-100 text-indigo-600',
      demos: [
        {
          label: 'Login Success',
          action: () => auth.loginSuccess(demoName),
          color: 'bg-green-600 hover:bg-green-700'
        },
        {
          label: 'Login Error',
          action: () => auth.loginError('Invalid credentials'),
          color: 'bg-red-600 hover:bg-red-700'
        },
        {
          label: 'Logout Success',
          action: () => auth.logoutSuccess(),
          color: 'bg-blue-600 hover:bg-blue-700'
        },
        {
          label: 'Session Expired',
          action: () => auth.sessionExpired(),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Password Changed',
          action: () => auth.passwordChangeSuccess(),
          color: 'bg-green-600 hover:bg-green-700'
        },
        {
          label: 'Profile Updated',
          action: () => auth.profileUpdateSuccess(),
          color: 'bg-green-600 hover:bg-green-700'
        }
      ]
    },
    {
      title: 'System Operations',
      icon: Database,
      color: 'bg-orange-100 text-orange-600',
      demos: [
        {
          label: 'Save Success',
          action: () => system.saveSuccess(),
          color: 'bg-green-600 hover:bg-green-700'
        },
        {
          label: 'Network Error',
          action: () => system.networkError(),
          color: 'bg-red-600 hover:bg-red-700'
        },
        {
          label: 'Server Error',
          action: () => system.serverError(),
          color: 'bg-red-600 hover:bg-red-700'
        },
        {
          label: 'Maintenance Mode',
          action: () => system.maintenanceMode(),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Feature Coming Soon',
          action: () => system.featureComingSoon('Advanced Analytics'),
          color: 'bg-blue-600 hover:bg-blue-700'
        },
        {
          label: 'Auto-save Success',
          action: () => system.autoSaveSuccess(),
          color: 'bg-green-600 hover:bg-green-700'
        },
        {
          label: 'Copy Success',
          action: () => system.copySuccess('Email address'),
          color: 'bg-blue-600 hover:bg-blue-700'
        },
        {
          label: 'Download Complete',
          action: () => system.downloadSuccess('staff-report.pdf'),
          color: 'bg-green-600 hover:bg-green-700'
        }
      ]
    },
    {
      title: 'Validation Messages',
      icon: AlertTriangle,
      color: 'bg-yellow-100 text-yellow-600',
      demos: [
        {
          label: 'Required Field',
          action: () => validation.requiredField('Email address'),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Invalid Email',
          action: () => validation.invalidEmail(),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Invalid Phone',
          action: () => validation.invalidPhone(),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Weak Password',
          action: () => validation.passwordTooWeak(),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Password Mismatch',
          action: () => validation.passwordMismatch(),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'File Too Large',
          action: () => validation.fileTooLarge('5MB'),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Invalid File Type',
          action: () => validation.invalidFileType('PDF, DOC, DOCX'),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        },
        {
          label: 'Form Errors',
          action: () => validation.formErrors(3),
          color: 'bg-yellow-600 hover:bg-yellow-700'
        }
      ]
    },
    {
      title: 'Data Operations',
      icon: Database,
      color: 'bg-cyan-100 text-cyan-600',
      demos: [
        {
          label: 'Data Loaded',
          action: () => data.dataLoaded('staff members', 25),
          color: 'bg-green-600 hover:bg-green-700'
        },
        {
          label: 'No Data Found',
          action: () => data.noDataFound('staff'),
          color: 'bg-blue-600 hover:bg-blue-700'
        },
        {
          label: 'Filter Applied',
          action: () => data.filterApplied(8),
          color: 'bg-blue-600 hover:bg-blue-700'
        },
        {
          label: 'Search Results',
          action: () => data.searchResults(12, 'developer'),
          color: 'bg-blue-600 hover:bg-blue-700'
        },
        {
          label: 'Refresh Success',
          action: () => data.refreshSuccess(),
          color: 'bg-green-600 hover:bg-green-700'
        },
        {
          label: 'Cache Cleared',
          action: () => data.cacheCleared(),
          color: 'bg-blue-600 hover:bg-blue-700'
        }
      ]
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Toast Notifications Demo</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Explore all the different types of toast notifications available in the system. 
          Click any button below to see the notification in action.
        </p>
      </div>

      {/* Demo Controls */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Demo Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Demo Name
            </label>
            <input
              type="text"
              value={demoName}
              onChange={(e) => setDemoName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter a name for demos"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Demo Email
            </label>
            <input
              type="email"
              value={demoEmail}
              onChange={(e) => setDemoEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter an email for demos"
            />
          </div>
        </div>
      </div>

      {/* Toast Categories */}
      <div className="space-y-8">
        {toastCategories.map((category) => (
          <div key={category.title} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${category.color}`}>
                  <category.icon className="w-5 h-5" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{category.title}</h3>
                  <p className="text-sm text-gray-600">
                    {category.demos.length} notification{category.demos.length > 1 ? 's' : ''} available
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                {category.demos.map((demo, index) => (
                  <button
                    key={index}
                    onClick={demo.action}
                    className={`px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors ${demo.color}`}
                  >
                    {demo.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Usage Instructions */}
      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <h3 className="text-lg font-medium text-blue-900 mb-2">Usage Instructions</h3>
        <div className="text-sm text-blue-800 space-y-2">
          <p>• Click any button above to trigger a toast notification</p>
          <p>• Notifications will appear in the top-right corner of the screen</p>
          <p>• Most notifications auto-dismiss after 5 seconds</p>
          <p>• You can manually close notifications by clicking the X button</p>
          <p>• Success and info notifications can have action buttons</p>
        </div>
      </div>
    </div>
  )
}
