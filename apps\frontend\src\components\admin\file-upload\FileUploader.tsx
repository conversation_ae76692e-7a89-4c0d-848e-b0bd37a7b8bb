'use client'

import React, { useState, useCallback, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, X, File, Image, Video, Music, FileText, Archive, AlertCircle, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { ContentUploadGate } from '@/components/PermissionGate'
import { useFileUpload } from '@/hooks/useFileUpload'

export interface UploadedFile {
  id: string
  originalName: string
  filename: string
  mimeType: string
  size: number
  category: string
  url: string
  cdnUrl?: string
  path: string
  uploadedAt: Date
}

export interface FileUploaderProps {
  onUploadComplete?: (files: UploadedFile[]) => void
  onUploadError?: (error: string) => void
  maxFiles?: number
  maxFileSize?: number
  acceptedFileTypes?: string[]
  showPreview?: boolean
  multiple?: boolean
  disabled?: boolean
  className?: string
}

export const FileUploader: React.FC<FileUploaderProps> = ({
  onUploadComplete,
  onUploadError,
  maxFiles = 10,
  maxFileSize = 100 * 1024 * 1024, // 100MB
  acceptedFileTypes = [
    'image/*',
    'video/*',
    'audio/*',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/*'
  ],
  showPreview = true,
  multiple = true,
  disabled = false,
  className = ''
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<Array<{
    file: File
    progress: number
    status: 'uploading' | 'completed' | 'error'
    error?: string
    uploadedFile?: UploadedFile
  }>>([])

  const { toast } = useToast()
  const { uploadFiles, generateUploadUrl } = useFileUpload()
  const abortControllerRef = useRef<AbortController | null>(null)

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (disabled) return

    // Validate file count
    if (acceptedFiles.length > maxFiles) {
      toast({
        title: 'Too many files',
        description: `Maximum ${maxFiles} files allowed`,
        variant: 'destructive'
      })
      return
    }

    // Validate file sizes
    const oversizedFiles = acceptedFiles.filter(file => file.size > maxFileSize)
    if (oversizedFiles.length > 0) {
      toast({
        title: 'File too large',
        description: `Maximum file size is ${Math.round(maxFileSize / 1024 / 1024)}MB`,
        variant: 'destructive'
      })
      return
    }

    // Initialize upload tracking
    const initialFiles = acceptedFiles.map(file => ({
      file,
      progress: 0,
      status: 'uploading' as const
    }))

    setUploadingFiles(initialFiles)

    try {
      abortControllerRef.current = new AbortController()

      const uploadPromises = acceptedFiles.map(async (file, index) => {
        try {
          // Update progress to show upload starting
          setUploadingFiles(prev => prev.map((item, i) => 
            i === index ? { ...item, progress: 10 } : item
          ))

          // Generate upload URL
          const urlResult = await generateUploadUrl(
            file.name,
            file.type,
            file.size
          )

          if (!urlResult.success) {
            throw new Error(urlResult.error || 'Failed to generate upload URL')
          }

          // Update progress
          setUploadingFiles(prev => prev.map((item, i) => 
            i === index ? { ...item, progress: 30 } : item
          ))

          // Upload file
          const uploadResult = await uploadFiles([file], {
            onProgress: (progress) => {
              setUploadingFiles(prev => prev.map((item, i) => 
                i === index ? { ...item, progress: 30 + (progress * 0.7) } : item
              ))
            },
            signal: abortControllerRef.current?.signal
          })

          if (!uploadResult.success) {
            throw new Error(uploadResult.error || 'Upload failed')
          }

          // Mark as completed
          setUploadingFiles(prev => prev.map((item, i) => 
            i === index ? { 
              ...item, 
              progress: 100, 
              status: 'completed',
              uploadedFile: uploadResult.files?.[0]
            } : item
          ))

          return uploadResult.files?.[0]
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Upload failed'
          
          setUploadingFiles(prev => prev.map((item, i) => 
            i === index ? { 
              ...item, 
              status: 'error',
              error: errorMessage
            } : item
          ))

          throw error
        }
      })

      const results = await Promise.allSettled(uploadPromises)
      const successful = results
        .filter((result): result is PromiseFulfilledResult<UploadedFile> => 
          result.status === 'fulfilled' && result.value !== undefined
        )
        .map(result => result.value)

      const failed = results.filter(result => result.status === 'rejected')

      if (successful.length > 0) {
        onUploadComplete?.(successful)
        toast({
          title: 'Upload successful',
          description: `${successful.length} file(s) uploaded successfully`
        })
      }

      if (failed.length > 0) {
        const errorMessage = `${failed.length} file(s) failed to upload`
        onUploadError?.(errorMessage)
        toast({
          title: 'Upload errors',
          description: errorMessage,
          variant: 'destructive'
        })
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      onUploadError?.(errorMessage)
      toast({
        title: 'Upload failed',
        description: errorMessage,
        variant: 'destructive'
      })
    }
  }, [disabled, maxFiles, maxFileSize, generateUploadUrl, uploadFiles, onUploadComplete, onUploadError, toast])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.reduce((acc, type) => {
      acc[type] = []
      return acc
    }, {} as Record<string, string[]>),
    multiple,
    disabled,
    maxFiles,
    maxSize: maxFileSize
  })

  const cancelUpload = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setUploadingFiles([])
  }

  const removeFile = (index: number) => {
    setUploadingFiles(prev => prev.filter((_, i) => i !== index))
  }

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <Image className="h-4 w-4" />
    if (mimeType.startsWith('video/')) return <Video className="h-4 w-4" />
    if (mimeType.startsWith('audio/')) return <Music className="h-4 w-4" />
    if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.startsWith('text/')) return <FileText className="h-4 w-4" />
    if (mimeType.includes('zip') || mimeType.includes('rar')) return <Archive className="h-4 w-4" />
    return <File className="h-4 w-4" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <ContentUploadGate fallback={
      <Card className="border-dashed border-2 border-gray-300">
        <CardContent className="flex flex-col items-center justify-center py-8">
          <AlertCircle className="h-8 w-8 text-gray-400 mb-2" />
          <p className="text-gray-500">You don't have permission to upload files</p>
        </CardContent>
      </Card>
    }>
      <div className={`space-y-4 ${className}`}>
        {/* Drop Zone */}
        <Card 
          className={`border-dashed border-2 transition-colors ${
            isDragActive 
              ? 'border-blue-500 bg-blue-50' 
              : disabled 
                ? 'border-gray-200 bg-gray-50' 
                : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <CardContent 
            {...getRootProps()} 
            className="flex flex-col items-center justify-center py-8 cursor-pointer"
          >
            <input {...getInputProps()} />
            <Upload className={`h-8 w-8 mb-4 ${
              isDragActive ? 'text-blue-500' : disabled ? 'text-gray-400' : 'text-gray-500'
            }`} />
            
            {isDragActive ? (
              <p className="text-blue-600 font-medium">Drop files here...</p>
            ) : (
              <div className="text-center">
                <p className="text-gray-600 font-medium mb-1">
                  {disabled ? 'Upload disabled' : 'Drag & drop files here, or click to select'}
                </p>
                <p className="text-sm text-gray-500">
                  Max {maxFiles} files, up to {Math.round(maxFileSize / 1024 / 1024)}MB each
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Upload Progress */}
        {uploadingFiles.length > 0 && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Uploading Files</h3>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={cancelUpload}
                  disabled={uploadingFiles.every(f => f.status !== 'uploading')}
                >
                  Cancel
                </Button>
              </div>
              
              <div className="space-y-3">
                {uploadingFiles.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        {getFileIcon(item.file.type)}
                        <span className="text-sm font-medium truncate">
                          {item.file.name}
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {formatFileSize(item.file.size)}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {item.status === 'completed' && (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        )}
                        {item.status === 'error' && (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    
                    {item.status === 'uploading' && (
                      <Progress value={item.progress} className="h-2" />
                    )}
                    
                    {item.status === 'error' && item.error && (
                      <p className="text-xs text-red-600">{item.error}</p>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* File Preview */}
        {showPreview && uploadingFiles.some(f => f.status === 'completed') && (
          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-4">Uploaded Files</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {uploadingFiles
                  .filter(f => f.status === 'completed' && f.uploadedFile)
                  .map((item, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-2">
                        {getFileIcon(item.uploadedFile!.mimeType)}
                        <span className="text-sm font-medium truncate">
                          {item.uploadedFile!.originalName}
                        </span>
                      </div>
                      
                      {item.uploadedFile!.category === 'image' && (
                        <img
                          src={item.uploadedFile!.url}
                          alt={item.uploadedFile!.originalName}
                          className="w-full h-32 object-cover rounded"
                        />
                      )}
                      
                      <div className="mt-2 text-xs text-gray-500">
                        <p>{formatFileSize(item.uploadedFile!.size)}</p>
                        <p>{new Date(item.uploadedFile!.uploadedAt).toLocaleString()}</p>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </ContentUploadGate>
  )
}

export default FileUploader
