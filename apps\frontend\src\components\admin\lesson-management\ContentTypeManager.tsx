'use client'

import React, { useState } from 'react'
import { 
  Video, 
  FileText, 
  HelpCircle, 
  BookOpen, 
  Calendar, 
  Upload,
  Link,
  Play,
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { FileUploader } from '@/components/admin/file-upload/FileUploader'
import { RichTextEditor } from '@/components/ui/rich-text-editor'

export interface ContentItem {
  id: string
  type: 'video' | 'document' | 'text' | 'quiz' | 'assignment' | 'interactive'
  title: string
  description?: string
  content?: any
  order: number
  is_required: boolean
  estimated_time?: number
}

export interface ContentTypeManagerProps {
  lessonId: string
  contentItems: ContentItem[]
  onUpdate: (items: ContentItem[]) => void
}

const contentTypes = [
  { 
    value: 'video', 
    label: 'Video Content', 
    icon: Video, 
    description: 'Video lectures, tutorials, or demonstrations',
    color: 'bg-blue-100 text-blue-800'
  },
  { 
    value: 'document', 
    label: 'Document', 
    icon: FileText, 
    description: 'PDFs, presentations, or downloadable resources',
    color: 'bg-green-100 text-green-800'
  },
  { 
    value: 'text', 
    label: 'Text Content', 
    icon: FileText, 
    description: 'Rich text articles, instructions, or reading materials',
    color: 'bg-gray-100 text-gray-800'
  },
  { 
    value: 'quiz', 
    label: 'Quiz/Assessment', 
    icon: HelpCircle, 
    description: 'Interactive quizzes or knowledge checks',
    color: 'bg-purple-100 text-purple-800'
  },
  { 
    value: 'assignment', 
    label: 'Assignment', 
    icon: BookOpen, 
    description: 'Student assignments or projects',
    color: 'bg-orange-100 text-orange-800'
  },
  { 
    value: 'interactive', 
    label: 'Interactive Content', 
    icon: Play, 
    description: 'Simulations, interactive exercises, or embedded content',
    color: 'bg-pink-100 text-pink-800'
  }
]

export const ContentTypeManager: React.FC<ContentTypeManagerProps> = ({
  lessonId,
  contentItems,
  onUpdate
}) => {
  const [selectedType, setSelectedType] = useState<string>('')
  const [editingItem, setEditingItem] = useState<ContentItem | null>(null)
  const [newItem, setNewItem] = useState<Partial<ContentItem>>({})
  const { toast } = useToast()

  const handleAddContent = () => {
    if (!selectedType) {
      toast({
        title: 'Select Content Type',
        description: 'Please select a content type to add',
        variant: 'destructive'
      })
      return
    }

    const newContentItem: ContentItem = {
      id: `content-${Date.now()}`,
      type: selectedType as any,
      title: newItem.title || `New ${selectedType}`,
      description: newItem.description || '',
      content: getDefaultContent(selectedType),
      order: contentItems.length + 1,
      is_required: newItem.is_required || false,
      estimated_time: newItem.estimated_time || 0
    }

    onUpdate([...contentItems, newContentItem])
    setNewItem({})
    setSelectedType('')
    
    toast({
      title: 'Content Added',
      description: `${selectedType} content has been added to the lesson`
    })
  }

  const handleUpdateContent = (itemId: string, updates: Partial<ContentItem>) => {
    const updatedItems = contentItems.map(item =>
      item.id === itemId ? { ...item, ...updates } : item
    )
    onUpdate(updatedItems)
  }

  const handleDeleteContent = (itemId: string) => {
    const updatedItems = contentItems.filter(item => item.id !== itemId)
    onUpdate(updatedItems)
    
    toast({
      title: 'Content Removed',
      description: 'Content item has been removed from the lesson'
    })
  }

  const handleReorderContent = (fromIndex: number, toIndex: number) => {
    const items = [...contentItems]
    const [movedItem] = items.splice(fromIndex, 1)
    items.splice(toIndex, 0, movedItem)
    
    // Update order values
    const reorderedItems = items.map((item, index) => ({
      ...item,
      order: index + 1
    }))
    
    onUpdate(reorderedItems)
  }

  const getDefaultContent = (type: string) => {
    switch (type) {
      case 'video':
        return { url: '', file: '', transcript: '' }
      case 'document':
        return { file: '', downloadable: true }
      case 'text':
        return { html: '', reading_time: 0 }
      case 'quiz':
        return { questions: [], passing_score: 70 }
      case 'assignment':
        return { instructions: '', due_date: '', max_score: 100 }
      case 'interactive':
        return { embed_code: '', external_url: '' }
      default:
        return {}
    }
  }

  const renderContentEditor = (item: ContentItem) => {
    const typeConfig = contentTypes.find(t => t.value === item.type)
    if (!typeConfig) return null

    return (
      <Card key={item.id} className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${typeConfig.color}`}>
                <typeConfig.icon className="h-4 w-4" />
              </div>
              <div>
                <CardTitle className="text-lg">{item.title}</CardTitle>
                <p className="text-sm text-gray-500">{typeConfig.description}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">#{item.order}</Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setEditingItem(item)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDeleteContent(item.id)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {item.type === 'video' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Video URL</Label>
                  <Input
                    placeholder="https://youtube.com/watch?v=..."
                    value={item.content?.url || ''}
                    onChange={(e) => handleUpdateContent(item.id, {
                      content: { ...item.content, url: e.target.value }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Upload Video</Label>
                  <FileUploader
                    onUploadComplete={(files) => {
                      if (files.length > 0) {
                        handleUpdateContent(item.id, {
                          content: { ...item.content, file: files[0].url }
                        })
                      }
                    }}
                    maxFiles={1}
                    acceptedFileTypes={['video/*']}
                    maxFileSize={500 * 1024 * 1024}
                  />
                </div>
              </div>
              
              {(item.content?.url || item.content?.file) && (
                <div className="space-y-2">
                  <Label>Video Transcript (Optional)</Label>
                  <Textarea
                    placeholder="Enter video transcript for accessibility..."
                    rows={4}
                    value={item.content?.transcript || ''}
                    onChange={(e) => handleUpdateContent(item.id, {
                      content: { ...item.content, transcript: e.target.value }
                    })}
                  />
                </div>
              )}
            </div>
          )}

          {item.type === 'document' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Upload Document</Label>
                <FileUploader
                  onUploadComplete={(files) => {
                    if (files.length > 0) {
                      handleUpdateContent(item.id, {
                        content: { ...item.content, file: files[0].url }
                      })
                    }
                  }}
                  maxFiles={1}
                  acceptedFileTypes={['.pdf', '.doc', '.docx', '.ppt', '.pptx']}
                  maxFileSize={50 * 1024 * 1024}
                />
              </div>
              
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id={`downloadable-${item.id}`}
                  checked={item.content?.downloadable || false}
                  onChange={(e) => handleUpdateContent(item.id, {
                    content: { ...item.content, downloadable: e.target.checked }
                  })}
                />
                <Label htmlFor={`downloadable-${item.id}`}>Allow download</Label>
              </div>
            </div>
          )}

          {item.type === 'text' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Content</Label>
                <RichTextEditor
                  value={item.content?.html || ''}
                  onChange={(value) => handleUpdateContent(item.id, {
                    content: { ...item.content, html: value }
                  })}
                  placeholder="Enter your text content here..."
                />
              </div>
              
              <div className="space-y-2">
                <Label>Estimated Reading Time (minutes)</Label>
                <Input
                  type="number"
                  min="0"
                  value={item.content?.reading_time || 0}
                  onChange={(e) => handleUpdateContent(item.id, {
                    content: { ...item.content, reading_time: parseInt(e.target.value) }
                  })}
                />
              </div>
            </div>
          )}

          {item.type === 'quiz' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Quiz Questions</Label>
                <Button size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Question
                </Button>
              </div>
              
              <div className="space-y-2">
                <Label>Passing Score (%)</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={item.content?.passing_score || 70}
                  onChange={(e) => handleUpdateContent(item.id, {
                    content: { ...item.content, passing_score: parseInt(e.target.value) }
                  })}
                />
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
                Quiz builder will be implemented in the assessment module
              </div>
            </div>
          )}

          {item.type === 'assignment' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Assignment Instructions</Label>
                <RichTextEditor
                  value={item.content?.instructions || ''}
                  onChange={(value) => handleUpdateContent(item.id, {
                    content: { ...item.content, instructions: value }
                  })}
                  placeholder="Enter assignment instructions..."
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Due Date</Label>
                  <Input
                    type="datetime-local"
                    value={item.content?.due_date || ''}
                    onChange={(e) => handleUpdateContent(item.id, {
                      content: { ...item.content, due_date: e.target.value }
                    })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Maximum Score</Label>
                  <Input
                    type="number"
                    min="0"
                    value={item.content?.max_score || 100}
                    onChange={(e) => handleUpdateContent(item.id, {
                      content: { ...item.content, max_score: parseInt(e.target.value) }
                    })}
                  />
                </div>
              </div>
            </div>
          )}

          {item.type === 'interactive' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>External URL</Label>
                <Input
                  placeholder="https://example.com/interactive-content"
                  value={item.content?.external_url || ''}
                  onChange={(e) => handleUpdateContent(item.id, {
                    content: { ...item.content, external_url: e.target.value }
                  })}
                />
              </div>
              
              <div className="space-y-2">
                <Label>Embed Code</Label>
                <Textarea
                  placeholder="<iframe src='...' width='100%' height='400'></iframe>"
                  rows={4}
                  value={item.content?.embed_code || ''}
                  onChange={(e) => handleUpdateContent(item.id, {
                    content: { ...item.content, embed_code: e.target.value }
                  })}
                />
              </div>
            </div>
          )}

          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id={`required-${item.id}`}
                  checked={item.is_required}
                  onChange={(e) => handleUpdateContent(item.id, {
                    is_required: e.target.checked
                  })}
                />
                <Label htmlFor={`required-${item.id}`}>Required content</Label>
              </div>
              
              <div className="flex items-center gap-2">
                <Label>Time (min):</Label>
                <Input
                  type="number"
                  min="0"
                  className="w-20"
                  value={item.estimated_time || 0}
                  onChange={(e) => handleUpdateContent(item.id, {
                    estimated_time: parseInt(e.target.value)
                  })}
                />
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleReorderContent(item.order - 1, Math.max(0, item.order - 2))}
                disabled={item.order === 1}
              >
                Move Up
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleReorderContent(item.order - 1, Math.min(contentItems.length - 1, item.order))}
                disabled={item.order === contentItems.length}
              >
                Move Down
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Add New Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add Content to Lesson
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {contentTypes.map((type) => (
              <Button
                key={type.value}
                variant={selectedType === type.value ? "default" : "outline"}
                className="h-auto p-4 flex flex-col items-center gap-2"
                onClick={() => setSelectedType(type.value)}
              >
                <type.icon className="h-6 w-6" />
                <div className="text-center">
                  <div className="font-medium">{type.label}</div>
                  <div className="text-xs text-gray-500">{type.description}</div>
                </div>
              </Button>
            ))}
          </div>

          {selectedType && (
            <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Content Title</Label>
                  <Input
                    placeholder="Enter content title"
                    value={newItem.title || ''}
                    onChange={(e) => setNewItem({ ...newItem, title: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Estimated Time (minutes)</Label>
                  <Input
                    type="number"
                    min="0"
                    value={newItem.estimated_time || 0}
                    onChange={(e) => setNewItem({ ...newItem, estimated_time: parseInt(e.target.value) })}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Description</Label>
                <Textarea
                  placeholder="Brief description of this content"
                  rows={2}
                  value={newItem.description || ''}
                  onChange={(e) => setNewItem({ ...newItem, description: e.target.value })}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="new-required"
                    checked={newItem.is_required || false}
                    onChange={(e) => setNewItem({ ...newItem, is_required: e.target.checked })}
                  />
                  <Label htmlFor="new-required">Required content</Label>
                </div>
                
                <Button onClick={handleAddContent}>
                  Add {selectedType}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Existing Content */}
      {contentItems.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Lesson Content ({contentItems.length})</h3>
            <Badge variant="secondary">
              Total: {contentItems.reduce((sum, item) => sum + (item.estimated_time || 0), 0)} minutes
            </Badge>
          </div>
          
          {contentItems
            .sort((a, b) => a.order - b.order)
            .map(item => renderContentEditor(item))}
        </div>
      )}

      {contentItems.length === 0 && (
        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="p-8 text-center">
            <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No content added yet</h3>
            <p className="text-gray-500">
              Add different types of content to make your lesson engaging and comprehensive.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default ContentTypeManager
