'use client'

import React, { useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { 
  X, 
  Save, 
  Eye, 
  Video, 
  FileText, 
  HelpCircle, 
  BookOpen, 
  Calendar, 
  Upload,
  Link,
  Clock,
  Users,
  Settings,
  Plus,
  Trash2
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { FileUploader } from '@/components/admin/file-upload/FileUploader'
import { RichTextEditor } from '@/components/ui/rich-text-editor'

const validationSchema = Yup.object({
  title: Yup.string()
    .required('Lesson title is required')
    .min(3, 'Title must be at least 3 characters')
    .max(200, 'Title must not exceed 200 characters'),
  type: Yup.string()
    .required('Lesson type is required'),
  description: Yup.string()
    .max(500, 'Description must not exceed 500 characters'),
  duration: Yup.number()
    .min(0, 'Duration cannot be negative')
    .max(600, 'Duration cannot exceed 10 hours'),
  order: Yup.number()
    .required('Lesson order is required')
    .min(1, 'Order must be at least 1')
})

export interface LessonEditorProps {
  lesson?: any
  courseId: string
  isOpen: boolean
  onClose: () => void
  onSave: (lessonData: any) => void
  maxOrder: number
}

const lessonTypes = [
  { value: 'video', label: 'Video', icon: Video, description: 'Video content with player controls' },
  { value: 'text', label: 'Text', icon: FileText, description: 'Rich text content and articles' },
  { value: 'quiz', label: 'Quiz', icon: HelpCircle, description: 'Interactive quiz or assessment' },
  { value: 'assignment', label: 'Assignment', icon: BookOpen, description: 'Student assignment or project' },
  { value: 'live', label: 'Live Session', icon: Calendar, description: 'Scheduled live class or webinar' },
  { value: 'document', label: 'Document', icon: FileText, description: 'PDF or document download' },
  { value: 'interactive', label: 'Interactive', icon: Settings, description: 'Interactive content or simulation' }
]

export const LessonEditor: React.FC<LessonEditorProps> = ({
  lesson,
  courseId,
  isOpen,
  onClose,
  onSave,
  maxOrder
}) => {
  const { toast } = useToast()
  const [saving, setSaving] = useState(false)
  const [attachments, setAttachments] = useState(lesson?.attachments || [])

  const isEditing = !!lesson?.id

  const formik = useFormik({
    initialValues: {
      title: lesson?.title || '',
      slug: lesson?.slug || '',
      type: lesson?.type || 'text',
      description: lesson?.description || '',
      content: lesson?.content || '',
      video_url: lesson?.video_url || '',
      video_file: lesson?.video_file || '',
      duration: lesson?.duration || 0,
      order: lesson?.order || maxOrder + 1,
      is_preview: lesson?.is_preview || false,
      is_mandatory: lesson?.is_mandatory || true,
      status: lesson?.status || 'draft',
      prerequisites: lesson?.prerequisites || [],
      assignment: lesson?.assignment || {
        instructions: '',
        due_date: '',
        max_score: 100,
        submission_type: 'file'
      },
      live_session: lesson?.live_session || {
        scheduled_at: '',
        duration_minutes: 60,
        meeting_url: '',
        meeting_id: '',
        meeting_password: '',
        max_participants: null
      }
    },
    validationSchema,
    onSubmit: async (values) => {
      setSaving(true)
      try {
        // Generate slug if not provided
        if (!values.slug && values.title) {
          values.slug = values.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }

        const lessonData = {
          ...values,
          course: courseId,
          attachments
        }

        await onSave(lessonData)
        
        toast({
          title: isEditing ? 'Lesson updated' : 'Lesson created',
          description: `Lesson "${values.title}" has been ${isEditing ? 'updated' : 'created'} successfully`
        })
        
        onClose()
      } catch (error) {
        toast({
          title: 'Error',
          description: `Failed to ${isEditing ? 'update' : 'create'} lesson`,
          variant: 'destructive'
        })
      } finally {
        setSaving(false)
      }
    },
    enableReinitialize: true
  })

  // Auto-generate slug from title
  React.useEffect(() => {
    if (formik.values.title && !formik.touched.slug) {
      const slug = formik.values.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
      formik.setFieldValue('slug', slug)
    }
  }, [formik.values.title])

  const handleAttachmentUpload = (files: any[]) => {
    const newAttachments = files.map(file => ({
      file: file.url,
      title: file.name,
      description: ''
    }))
    setAttachments(prev => [...prev, ...newAttachments])
  }

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index))
  }

  const updateAttachment = (index: number, field: string, value: string) => {
    setAttachments(prev => prev.map((attachment, i) => 
      i === index ? { ...attachment, [field]: value } : attachment
    ))
  }

  const selectedType = lessonTypes.find(type => type.value === formik.values.type)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {selectedType && <selectedType.icon className="h-5 w-5" />}
            {isEditing ? 'Edit Lesson' : 'Create New Lesson'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
              <TabsTrigger value="attachments">Attachments</TabsTrigger>
            </TabsList>

            {/* Basic Information */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Lesson Title *</Label>
                  <Input
                    id="title"
                    name="title"
                    placeholder="Enter lesson title"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    className={formik.touched.title && formik.errors.title ? 'border-red-500' : ''}
                  />
                  {formik.touched.title && formik.errors.title && (
                    <p className="text-sm text-red-600">{formik.errors.title}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">URL Slug</Label>
                  <Input
                    id="slug"
                    name="slug"
                    placeholder="lesson-url-slug"
                    value={formik.values.slug}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Lesson Type *</Label>
                <Select
                  value={formik.values.type}
                  onValueChange={(value) => formik.setFieldValue('type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select lesson type" />
                  </SelectTrigger>
                  <SelectContent>
                    {lessonTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center gap-2">
                          <type.icon className="h-4 w-4" />
                          <div>
                            <p className="font-medium">{type.label}</p>
                            <p className="text-sm text-gray-500">{type.description}</p>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Brief description of the lesson"
                  rows={3}
                  value={formik.values.description}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  className={formik.touched.description && formik.errors.description ? 'border-red-500' : ''}
                />
                <div className="flex justify-between">
                  {formik.touched.description && formik.errors.description && (
                    <p className="text-sm text-red-600">{formik.errors.description}</p>
                  )}
                  <p className="text-sm text-gray-500 ml-auto">
                    {formik.values.description.length}/500
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="duration">Duration (minutes)</Label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="duration"
                      name="duration"
                      type="number"
                      min="0"
                      max="600"
                      placeholder="0"
                      className="pl-10"
                      value={formik.values.duration}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="order">Lesson Order</Label>
                  <Input
                    id="order"
                    name="order"
                    type="number"
                    min="1"
                    value={formik.values.order}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    className={formik.touched.order && formik.errors.order ? 'border-red-500' : ''}
                  />
                  {formik.touched.order && formik.errors.order && (
                    <p className="text-sm text-red-600">{formik.errors.order}</p>
                  )}
                </div>
              </div>
            </TabsContent>

            {/* Content */}
            <TabsContent value="content" className="space-y-4">
              {formik.values.type === 'video' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="video_url">Video URL</Label>
                    <div className="relative">
                      <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="video_url"
                        name="video_url"
                        placeholder="https://youtube.com/watch?v=..."
                        className="pl-10"
                        value={formik.values.video_url}
                        onChange={formik.handleChange}
                      />
                    </div>
                  </div>

                  <div className="text-center text-gray-500">or</div>

                  <div className="space-y-2">
                    <Label>Upload Video File</Label>
                    <FileUploader
                      onUploadComplete={(files) => {
                        if (files.length > 0) {
                          formik.setFieldValue('video_file', files[0].url)
                        }
                      }}
                      maxFiles={1}
                      acceptedFileTypes={['video/*']}
                      maxFileSize={500 * 1024 * 1024} // 500MB
                    />
                  </div>
                </div>
              )}

              {(formik.values.type === 'text' || formik.values.type === 'interactive') && (
                <div className="space-y-2">
                  <Label>Lesson Content</Label>
                  <RichTextEditor
                    value={formik.values.content}
                    onChange={(value) => formik.setFieldValue('content', value)}
                    placeholder="Enter your lesson content here..."
                  />
                </div>
              )}

              {formik.values.type === 'assignment' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Assignment Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Instructions</Label>
                      <RichTextEditor
                        value={formik.values.assignment.instructions}
                        onChange={(value) => formik.setFieldValue('assignment.instructions', value)}
                        placeholder="Assignment instructions..."
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="due_date">Due Date</Label>
                        <Input
                          id="due_date"
                          type="datetime-local"
                          value={formik.values.assignment.due_date}
                          onChange={(e) => formik.setFieldValue('assignment.due_date', e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="max_score">Maximum Score</Label>
                        <Input
                          id="max_score"
                          type="number"
                          min="0"
                          value={formik.values.assignment.max_score}
                          onChange={(e) => formik.setFieldValue('assignment.max_score', parseInt(e.target.value))}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Submission Type</Label>
                      <Select
                        value={formik.values.assignment.submission_type}
                        onValueChange={(value) => formik.setFieldValue('assignment.submission_type', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="file">File Upload</SelectItem>
                          <SelectItem value="text">Text Entry</SelectItem>
                          <SelectItem value="url">URL Submission</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              )}

              {formik.values.type === 'live' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Live Session Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="scheduled_at">Scheduled Date & Time</Label>
                        <Input
                          id="scheduled_at"
                          type="datetime-local"
                          value={formik.values.live_session.scheduled_at}
                          onChange={(e) => formik.setFieldValue('live_session.scheduled_at', e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="duration_minutes">Duration (minutes)</Label>
                        <Input
                          id="duration_minutes"
                          type="number"
                          min="15"
                          value={formik.values.live_session.duration_minutes}
                          onChange={(e) => formik.setFieldValue('live_session.duration_minutes', parseInt(e.target.value))}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="meeting_url">Meeting URL</Label>
                      <Input
                        id="meeting_url"
                        placeholder="https://zoom.us/j/..."
                        value={formik.values.live_session.meeting_url}
                        onChange={(e) => formik.setFieldValue('live_session.meeting_url', e.target.value)}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="meeting_id">Meeting ID</Label>
                        <Input
                          id="meeting_id"
                          placeholder="123 456 789"
                          value={formik.values.live_session.meeting_id}
                          onChange={(e) => formik.setFieldValue('live_session.meeting_id', e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="meeting_password">Meeting Password</Label>
                        <Input
                          id="meeting_password"
                          placeholder="Optional"
                          value={formik.values.live_session.meeting_password}
                          onChange={(e) => formik.setFieldValue('live_session.meeting_password', e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="max_participants">Maximum Participants</Label>
                      <Input
                        id="max_participants"
                        type="number"
                        min="1"
                        placeholder="Leave empty for unlimited"
                        value={formik.values.live_session.max_participants || ''}
                        onChange={(e) => formik.setFieldValue('live_session.max_participants', e.target.value ? parseInt(e.target.value) : null)}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Settings */}
            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Lesson Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="is_preview">Allow Preview</Label>
                      <p className="text-sm text-gray-500">Students can preview without enrollment</p>
                    </div>
                    <Switch
                      id="is_preview"
                      checked={formik.values.is_preview}
                      onCheckedChange={(checked) => formik.setFieldValue('is_preview', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="is_mandatory">Mandatory Lesson</Label>
                      <p className="text-sm text-gray-500">Required for course completion</p>
                    </div>
                    <Switch
                      id="is_mandatory"
                      checked={formik.values.is_mandatory}
                      onCheckedChange={(checked) => formik.setFieldValue('is_mandatory', checked)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Publication Status</Label>
                    <Select
                      value={formik.values.status}
                      onValueChange={(value) => formik.setFieldValue('status', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="published">Published</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Attachments */}
            <TabsContent value="attachments" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Lesson Attachments
                    <Badge variant="secondary">{attachments.length}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FileUploader
                    onUploadComplete={handleAttachmentUpload}
                    maxFiles={10}
                    acceptedFileTypes={['*']}
                    maxFileSize={50 * 1024 * 1024} // 50MB
                  />

                  {attachments.length > 0 && (
                    <div className="space-y-3">
                      {attachments.map((attachment, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-start gap-4">
                            <div className="flex-1 space-y-2">
                              <Input
                                placeholder="Attachment title"
                                value={attachment.title}
                                onChange={(e) => updateAttachment(index, 'title', e.target.value)}
                              />
                              <Textarea
                                placeholder="Attachment description (optional)"
                                rows={2}
                                value={attachment.description}
                                onChange={(e) => updateAttachment(index, 'description', e.target.value)}
                              />
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeAttachment(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Actions */}
          <div className="flex items-center justify-between pt-6 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  formik.setFieldValue('status', 'draft')
                  formik.handleSubmit()
                }}
                disabled={saving}
              >
                Save as Draft
              </Button>
              
              <Button
                type="submit"
                disabled={saving || !formik.isValid}
              >
                {saving ? 'Saving...' : isEditing ? 'Update Lesson' : 'Create Lesson'}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default LessonEditor
