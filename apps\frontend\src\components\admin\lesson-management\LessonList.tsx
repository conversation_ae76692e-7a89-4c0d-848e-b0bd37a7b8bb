'use client'

import React, { useState, useCallback } from 'react'
import { 
  DragDropContext, 
  Droppable, 
  Draggable,
  DropResult 
} from '@hello-pangea/dnd'
import { 
  GripVertical, 
  Play, 
  FileText, 
  HelpCircle, 
  Calendar, 
  Video, 
  BookOpen,
  MoreVertical,
  Edit,
  Copy,
  Trash2,
  Eye,
  EyeOff,
  Clock,
  Users
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/hooks/use-toast'
import { useLessons } from '@/hooks/useLessons'

export interface Lesson {
  id: string
  title: string
  type: 'video' | 'text' | 'quiz' | 'assignment' | 'live' | 'document' | 'interactive'
  order: number
  duration?: number
  status: 'draft' | 'published' | 'archived'
  is_preview: boolean
  is_mandatory: boolean
  description?: string
  createdAt: string
  updatedAt: string
}

export interface LessonListProps {
  courseId: string
  lessons: Lesson[]
  onReorder: (lessons: Lesson[]) => void
  onEdit: (lesson: Lesson) => void
  onDuplicate: (lesson: Lesson) => void
  onDelete: (lesson: Lesson) => void
  onBulkAction: (action: string, lessonIds: string[]) => void
  loading?: boolean
}

const getLessonIcon = (type: string) => {
  switch (type) {
    case 'video': return Video
    case 'text': return FileText
    case 'quiz': return HelpCircle
    case 'assignment': return BookOpen
    case 'live': return Calendar
    case 'document': return FileText
    case 'interactive': return Play
    default: return FileText
  }
}

const getLessonTypeColor = (type: string) => {
  switch (type) {
    case 'video': return 'bg-blue-100 text-blue-800'
    case 'text': return 'bg-gray-100 text-gray-800'
    case 'quiz': return 'bg-purple-100 text-purple-800'
    case 'assignment': return 'bg-orange-100 text-orange-800'
    case 'live': return 'bg-green-100 text-green-800'
    case 'document': return 'bg-yellow-100 text-yellow-800'
    case 'interactive': return 'bg-pink-100 text-pink-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'published': return 'bg-green-100 text-green-800'
    case 'draft': return 'bg-yellow-100 text-yellow-800'
    case 'archived': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

export const LessonList: React.FC<LessonListProps> = ({
  courseId,
  lessons,
  onReorder,
  onEdit,
  onDuplicate,
  onDelete,
  onBulkAction,
  loading = false
}) => {
  const [selectedLessons, setSelectedLessons] = useState<string[]>([])
  const [draggedItem, setDraggedItem] = useState<string | null>(null)
  const { toast } = useToast()

  const handleDragStart = useCallback((start: any) => {
    setDraggedItem(start.draggableId)
  }, [])

  const handleDragEnd = useCallback((result: DropResult) => {
    setDraggedItem(null)
    
    if (!result.destination) {
      return
    }

    const items = Array.from(lessons)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    // Update order values
    const updatedLessons = items.map((lesson, index) => ({
      ...lesson,
      order: index + 1
    }))

    onReorder(updatedLessons)
    
    toast({
      title: 'Lesson reordered',
      description: 'Lesson order has been updated successfully'
    })
  }, [lessons, onReorder, toast])

  const handleSelectLesson = useCallback((lessonId: string, checked: boolean) => {
    if (checked) {
      setSelectedLessons(prev => [...prev, lessonId])
    } else {
      setSelectedLessons(prev => prev.filter(id => id !== lessonId))
    }
  }, [])

  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedLessons(lessons.map(lesson => lesson.id))
    } else {
      setSelectedLessons([])
    }
  }, [lessons])

  const handleBulkAction = useCallback((action: string) => {
    if (selectedLessons.length === 0) {
      toast({
        title: 'No lessons selected',
        description: 'Please select lessons to perform bulk actions',
        variant: 'destructive'
      })
      return
    }

    onBulkAction(action, selectedLessons)
    setSelectedLessons([])
  }, [selectedLessons, onBulkAction, toast])

  const formatDuration = (minutes?: number) => {
    if (!minutes) return 'No duration'
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="w-6 h-6 bg-gray-200 rounded"></div>
                <div className="w-8 h-8 bg-gray-200 rounded"></div>
                <div className="flex-1">
                  <div className="w-48 h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="w-32 h-3 bg-gray-200 rounded"></div>
                </div>
                <div className="w-16 h-6 bg-gray-200 rounded"></div>
                <div className="w-8 h-8 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedLessons.length > 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium">
                  {selectedLessons.length} lesson{selectedLessons.length !== 1 ? 's' : ''} selected
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedLessons([])}
                >
                  Clear Selection
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('publish')}
                >
                  Publish
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('draft')}
                >
                  Move to Draft
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('duplicate')}
                >
                  Duplicate
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleBulkAction('delete')}
                >
                  Delete
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Select All */}
      {lessons.length > 0 && (
        <div className="flex items-center gap-2 px-2">
          <Checkbox
            checked={selectedLessons.length === lessons.length}
            onCheckedChange={handleSelectAll}
          />
          <span className="text-sm text-gray-600">
            Select all lessons
          </span>
        </div>
      )}

      {/* Lesson List */}
      <DragDropContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        <Droppable droppableId="lessons">
          {(provided, snapshot) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className={`space-y-2 ${snapshot.isDraggingOver ? 'bg-blue-50 rounded-lg p-2' : ''}`}
            >
              {lessons.map((lesson, index) => {
                const Icon = getLessonIcon(lesson.type)
                const isSelected = selectedLessons.includes(lesson.id)
                const isDragging = draggedItem === lesson.id

                return (
                  <Draggable key={lesson.id} draggableId={lesson.id} index={index}>
                    {(provided, snapshot) => (
                      <Card
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className={`transition-all duration-200 ${
                          snapshot.isDragging 
                            ? 'shadow-lg rotate-2 scale-105' 
                            : isSelected 
                              ? 'ring-2 ring-blue-500 bg-blue-50' 
                              : 'hover:shadow-md'
                        }`}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center gap-4">
                            {/* Selection Checkbox */}
                            <Checkbox
                              checked={isSelected}
                              onCheckedChange={(checked) => 
                                handleSelectLesson(lesson.id, checked as boolean)
                              }
                            />

                            {/* Drag Handle */}
                            <div
                              {...provided.dragHandleProps}
                              className="cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600"
                            >
                              <GripVertical className="h-5 w-5" />
                            </div>

                            {/* Lesson Icon */}
                            <div className={`p-2 rounded-lg ${getLessonTypeColor(lesson.type)}`}>
                              <Icon className="h-4 w-4" />
                            </div>

                            {/* Lesson Info */}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h3 className="font-medium text-gray-900 truncate">
                                  {lesson.title}
                                </h3>
                                <Badge variant="outline" className="text-xs">
                                  #{lesson.order}
                                </Badge>
                              </div>
                              
                              <div className="flex items-center gap-4 text-sm text-gray-500">
                                <div className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {formatDuration(lesson.duration)}
                                </div>
                                
                                {lesson.description && (
                                  <span className="truncate max-w-xs">
                                    {lesson.description}
                                  </span>
                                )}
                              </div>
                            </div>

                            {/* Lesson Badges */}
                            <div className="flex items-center gap-2">
                              <Badge className={getLessonTypeColor(lesson.type)}>
                                {lesson.type}
                              </Badge>
                              
                              <Badge className={getStatusColor(lesson.status)}>
                                {lesson.status}
                              </Badge>

                              {lesson.is_preview && (
                                <Badge variant="outline" className="text-xs">
                                  <Eye className="h-3 w-3 mr-1" />
                                  Preview
                                </Badge>
                              )}

                              {lesson.is_mandatory && (
                                <Badge variant="outline" className="text-xs">
                                  Required
                                </Badge>
                              )}
                            </div>

                            {/* Actions Menu */}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => onEdit(lesson)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => onDuplicate(lesson)}>
                                  <Copy className="h-4 w-4 mr-2" />
                                  Duplicate
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => onDelete(lesson)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </Draggable>
                )
              })}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {lessons.length === 0 && (
        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="p-8 text-center">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No lessons yet</h3>
            <p className="text-gray-500 mb-4">
              Start building your course by adding your first lesson.
            </p>
            <Button onClick={() => onEdit({} as Lesson)}>
              Add First Lesson
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default LessonList
