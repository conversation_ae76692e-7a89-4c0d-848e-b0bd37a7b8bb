'use client'

import React from 'react'
import { useMediaDashboardStore } from '@/stores/admin/media-dashboard'
import { mediaDashboardAPI } from '@/lib/api/media-dashboard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Activity, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  FileVideo,
  FileText,
  TrendingUp,
  Users
} from 'lucide-react'

export function DashboardOverview() {
  const { overview, loading } = useMediaDashboardStore()

  if (loading || !overview) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const { queueStats, performanceMetrics, recentActivity } = overview

  return (
    <div className="space-y-6">
      {/* Queue Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Queue Status</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Pending</span>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  {queueStats.pending}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Processing</span>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {queueStats.processing}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Completed</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {queueStats.completed}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Failed</span>
                <Badge variant="secondary" className="bg-red-100 text-red-800">
                  {queueStats.failed}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance Today</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm">Success Rate</span>
                  <span className="text-sm font-medium">
                    {performanceMetrics.successRate.toFixed(1)}%
                  </span>
                </div>
                <Progress value={performanceMetrics.successRate} className="h-2" />
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Completed</span>
                  <div className="font-medium">{performanceMetrics.completedJobsToday}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Failed</span>
                  <div className="font-medium">{performanceMetrics.failedJobsToday}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Metrics</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <span className="text-sm text-muted-foreground">Avg Processing Time</span>
                <div className="text-lg font-medium">
                  {mediaDashboardAPI.formatProcessingTime(performanceMetrics.averageProcessingTime)}
                </div>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Throughput</span>
                <div className="text-lg font-medium">
                  {performanceMetrics.throughput.toFixed(1)} jobs/min
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            Latest processing jobs from the past 24 hours
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentActivity.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No recent activity
            </div>
          ) : (
            <div className="space-y-4">
              {recentActivity.map((job) => (
                <div key={job.id} className="flex items-center space-x-4 p-3 rounded-lg border">
                  {/* Job Type Icon */}
                  <div className="flex-shrink-0">
                    {job.type === 'video' ? (
                      <FileVideo className="h-5 w-5 text-blue-500" />
                    ) : job.type === 'image' ? (
                      <div className="h-5 w-5 text-green-500">🖼️</div>
                    ) : job.type === 'audio' ? (
                      <div className="h-5 w-5 text-purple-500">🎵</div>
                    ) : (
                      <FileText className="h-5 w-5 text-gray-500" />
                    )}
                  </div>

                  {/* Job Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium truncate">
                        {job.filePath.split('/').pop() || job.id}
                      </span>
                      <Badge 
                        variant="secondary" 
                        className={mediaDashboardAPI.getStatusColor(job.status)}
                      >
                        {job.status}
                      </Badge>
                      <Badge 
                        variant="outline" 
                        className={mediaDashboardAPI.getPriorityColor(job.priority)}
                      >
                        {job.priority}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 mt-1 text-xs text-muted-foreground">
                      <span>{mediaDashboardAPI.formatDate(job.createdAt)}</span>
                      <span>{job.type.toUpperCase()}</span>
                      {job.completedAt && job.startedAt && (
                        <span>
                          Processed in {mediaDashboardAPI.formatProcessingTime(
                            new Date(job.completedAt).getTime() - new Date(job.startedAt).getTime()
                          )}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Progress/Status */}
                  <div className="flex-shrink-0">
                    {job.status === 'processing' ? (
                      <div className="w-16">
                        <Progress value={job.progress} className="h-2" />
                        <span className="text-xs text-muted-foreground">
                          {job.progress}%
                        </span>
                      </div>
                    ) : job.status === 'completed' ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : job.status === 'failed' ? (
                      <XCircle className="h-5 w-5 text-red-500" />
                    ) : job.status === 'cancelled' ? (
                      <AlertCircle className="h-5 w-5 text-gray-500" />
                    ) : (
                      <Clock className="h-5 w-5 text-yellow-500" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Processing Distribution Chart */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Job Status Distribution</CardTitle>
            <CardDescription>Current distribution of job statuses</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { status: 'completed', count: queueStats.completed, color: 'bg-green-500' },
                { status: 'processing', count: queueStats.processing, color: 'bg-blue-500' },
                { status: 'pending', count: queueStats.pending, color: 'bg-yellow-500' },
                { status: 'failed', count: queueStats.failed, color: 'bg-red-500' },
                { status: 'cancelled', count: queueStats.cancelled, color: 'bg-gray-500' }
              ].map(({ status, count, color }) => {
                const percentage = queueStats.total > 0 ? (count / queueStats.total) * 100 : 0
                return (
                  <div key={status} className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span className="capitalize">{status}</span>
                      <span>{count} ({percentage.toFixed(1)}%)</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${color}`}
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Job Type Distribution</CardTitle>
            <CardDescription>Types of media being processed</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.length > 0 ? (
                (() => {
                  const typeCount = recentActivity.reduce((acc, job) => {
                    acc[job.type] = (acc[job.type] || 0) + 1
                    return acc
                  }, {} as Record<string, number>)

                  return Object.entries(typeCount).map(([type, count]) => {
                    const percentage = (count / recentActivity.length) * 100
                    const icon = type === 'video' ? '🎥' : type === 'image' ? '🖼️' : type === 'audio' ? '🎵' : '📄'
                    
                    return (
                      <div key={type} className="flex items-center space-x-3">
                        <span className="text-lg">{icon}</span>
                        <div className="flex-1">
                          <div className="flex items-center justify-between text-sm">
                            <span className="capitalize">{type}</span>
                            <span>{count} ({percentage.toFixed(1)}%)</span>
                          </div>
                          <Progress value={percentage} className="h-2 mt-1" />
                        </div>
                      </div>
                    )
                  })
                })()
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No data available
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default DashboardOverview
