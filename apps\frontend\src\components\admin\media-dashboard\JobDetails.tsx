'use client'

import React from 'react'
import { useMediaDashboardStore } from '@/stores/admin/media-dashboard'
import { mediaDashboardAPI } from '@/lib/api/media-dashboard'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
  RefreshCw, 
  X, 
  FileVideo, 
  FileText, 
  Clock, 
  User,
  Calendar,
  AlertCircle,
  CheckCircle,
  XCircle,
  Play,
  Pause
} from 'lucide-react'

export function JobDetails() {
  const {
    currentJob,
    setCurrentJob,
    retryJob,
    cancelJob,
    updateJobPriority
  } = useMediaDashboardStore()

  const handleClose = () => {
    setCurrentJob(null)
  }

  const handleRetry = async () => {
    if (currentJob) {
      await retryJob(currentJob.id)
    }
  }

  const handleCancel = async () => {
    if (currentJob) {
      await cancelJob(currentJob.id)
    }
  }

  const handlePriorityChange = async (priority: any) => {
    if (currentJob) {
      await updateJobPriority(currentJob.id, priority)
    }
  }

  if (!currentJob) {
    return null
  }

  const getJobIcon = () => {
    switch (currentJob.type) {
      case 'video':
        return <FileVideo className="h-5 w-5 text-blue-500" />
      case 'image':
        return <span className="text-lg">🖼️</span>
      case 'audio':
        return <span className="text-lg">🎵</span>
      default:
        return <FileText className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusIcon = () => {
    switch (currentJob.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'processing':
        return <Play className="h-5 w-5 text-blue-500" />
      case 'cancelled':
        return <Pause className="h-5 w-5 text-gray-500" />
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />
    }
  }

  const processingDuration = currentJob.completedAt && currentJob.startedAt
    ? new Date(currentJob.completedAt).getTime() - new Date(currentJob.startedAt).getTime()
    : currentJob.startedAt
    ? Date.now() - new Date(currentJob.startedAt).getTime()
    : 0

  return (
    <Sheet open={!!currentJob} onOpenChange={handleClose}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center space-x-2">
            {getJobIcon()}
            <span>Job Details</span>
          </SheetTitle>
          <SheetDescription>
            Detailed information about processing job {currentJob.id}
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-medium mb-3">Basic Information</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">File Name</span>
                <span className="text-sm font-medium">
                  {currentJob.filePath.split('/').pop() || currentJob.id}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Type</span>
                <Badge variant="outline">
                  {currentJob.type.toUpperCase()}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">MIME Type</span>
                <span className="text-sm font-medium">{currentJob.mimeType}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Priority</span>
                <Badge 
                  variant="outline" 
                  className={mediaDashboardAPI.getPriorityColor(currentJob.priority)}
                >
                  {currentJob.priority.toUpperCase()}
                </Badge>
              </div>
            </div>
          </div>

          <Separator />

          {/* Status Information */}
          <div>
            <h3 className="text-lg font-medium mb-3">Status Information</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Current Status</span>
                <div className="flex items-center space-x-2">
                  {getStatusIcon()}
                  <Badge className={mediaDashboardAPI.getStatusColor(currentJob.status)}>
                    {currentJob.status.toUpperCase()}
                  </Badge>
                </div>
              </div>
              
              {currentJob.status === 'processing' && (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">Progress</span>
                    <span className="text-sm font-medium">{currentJob.progress}%</span>
                  </div>
                  <Progress value={currentJob.progress} className="h-2" />
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Retry Count</span>
                <span className="text-sm font-medium">
                  {currentJob.retryCount} / {currentJob.maxRetries}
                </span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Timing Information */}
          <div>
            <h3 className="text-lg font-medium mb-3">Timing Information</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Created</span>
                <span className="text-sm font-medium">
                  {mediaDashboardAPI.formatDate(currentJob.createdAt)}
                </span>
              </div>
              
              {currentJob.startedAt && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Started</span>
                  <span className="text-sm font-medium">
                    {mediaDashboardAPI.formatDate(currentJob.startedAt)}
                  </span>
                </div>
              )}
              
              {currentJob.completedAt && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Completed</span>
                  <span className="text-sm font-medium">
                    {mediaDashboardAPI.formatDate(currentJob.completedAt)}
                  </span>
                </div>
              )}
              
              {processingDuration > 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Duration</span>
                  <span className="text-sm font-medium">
                    {mediaDashboardAPI.formatProcessingTime(processingDuration)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Error Information */}
          {currentJob.error && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-medium mb-3 text-red-600">Error Information</h3>
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
                    <span className="text-sm text-red-700">{currentJob.error}</span>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Processing Options */}
          {currentJob.options && Object.keys(currentJob.options).length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-medium mb-3">Processing Options</h3>
                <div className="space-y-2">
                  {Object.entries(currentJob.options).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                      <span className="text-sm font-medium">
                        {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : String(value)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Video Processing Details */}
          {currentJob.additionalDetails?.videoProcessing && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-medium mb-3">Video Processing Details</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Processing Status</span>
                    <Badge variant="outline">
                      {currentJob.additionalDetails.videoProcessing.status?.toUpperCase()}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Video Progress</span>
                    <span className="text-sm font-medium">
                      {currentJob.additionalDetails.videoProcessing.progress}%
                    </span>
                  </div>
                  
                  {currentJob.additionalDetails.videoProcessing.result && (
                    <div className="space-y-2">
                      <span className="text-sm text-muted-foreground">Processing Result</span>
                      <div className="text-xs bg-gray-50 p-2 rounded border">
                        <pre className="whitespace-pre-wrap">
                          {JSON.stringify(currentJob.additionalDetails.videoProcessing.result, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Metadata */}
          {currentJob.metadata && Object.keys(currentJob.metadata).length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-medium mb-3">Metadata</h3>
                <div className="space-y-2">
                  {Object.entries(currentJob.metadata).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                      <span className="text-sm font-medium">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Actions */}
          <Separator />
          <div className="flex space-x-2">
            {currentJob.status === 'failed' && (
              <Button onClick={handleRetry} size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Job
              </Button>
            )}
            
            {['pending', 'processing'].includes(currentJob.status) && (
              <Button onClick={handleCancel} variant="destructive" size="sm">
                <X className="h-4 w-4 mr-2" />
                Cancel Job
              </Button>
            )}
            
            {currentJob.status === 'pending' && (
              <div className="flex space-x-1">
                {mediaDashboardAPI.getPriorityOptions().map((priority) => (
                  <Button
                    key={priority.value}
                    onClick={() => handlePriorityChange(priority.value)}
                    variant={currentJob.priority === priority.value ? 'default' : 'outline'}
                    size="sm"
                  >
                    {priority.label}
                  </Button>
                ))}
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

export default JobDetails
