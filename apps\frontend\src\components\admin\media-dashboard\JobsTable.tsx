'use client'

import React, { useEffect, useState } from 'react'
import { useMediaDashboardStore } from '@/stores/admin/media-dashboard'
import { mediaDashboardAPI, ProcessingJob } from '@/lib/api/media-dashboard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Search,
  MoreHorizontal,
  RefreshCw,
  X,
  Play,
  AlertTriangle,
  FileVideo,
  FileText,
  Eye,
  ArrowUpDown
} from 'lucide-react'

export function JobsTable() {
  const {
    jobs,
    jobsLoading,
    pagination,
    filters,
    fetchJobs,
    setFilters,
    setPagination,
    resetFilters,
    retryJob,
    cancelJob,
    updateJobPriority,
    setCurrentJob
  } = useMediaDashboardStore()

  const [searchInput, setSearchInput] = useState(filters.search)

  useEffect(() => {
    fetchJobs()
  }, [fetchJobs])

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilters({ search: searchInput })
    }, 500)

    return () => clearTimeout(timer)
  }, [searchInput, setFilters])

  const handleStatusFilter = (status: string) => {
    setFilters({ status: status === 'all' ? undefined : status as ProcessingJob['status'] })
  }

  const handleTypeFilter = (type: string) => {
    setFilters({ type: type === 'all' ? undefined : type as ProcessingJob['type'] })
  }

  const handlePriorityFilter = (priority: string) => {
    setFilters({ priority: priority === 'all' ? undefined : priority as ProcessingJob['priority'] })
  }

  const handleSort = (sortBy: string) => {
    const newSortOrder = filters.sortBy === sortBy && filters.sortOrder === 'desc' ? 'asc' : 'desc'
    setFilters({ sortBy, sortOrder: newSortOrder })
  }

  const handlePageChange = (page: number) => {
    setPagination({ page })
  }

  const handleJobAction = async (action: string, jobId: string, priority?: ProcessingJob['priority']) => {
    switch (action) {
      case 'retry':
        await retryJob(jobId)
        break
      case 'cancel':
        await cancelJob(jobId)
        break
      case 'priority':
        if (priority) {
          await updateJobPriority(jobId, priority)
        }
        break
      case 'details':
        const job = jobs.find(j => j.id === jobId)
        if (job) {
          setCurrentJob(job)
        }
        break
    }
  }

  const getJobIcon = (type: ProcessingJob['type']) => {
    switch (type) {
      case 'video':
        return <FileVideo className="h-4 w-4 text-blue-500" />
      case 'image':
        return <span className="text-sm">🖼️</span>
      case 'audio':
        return <span className="text-sm">🎵</span>
      default:
        return <FileText className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Processing Jobs</CardTitle>
        <CardDescription>
          Monitor and manage all media processing operations
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search jobs..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={filters.status || 'all'} onValueChange={handleStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              {mediaDashboardAPI.getJobStatusOptions().map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.type || 'all'} onValueChange={handleTypeFilter}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              {mediaDashboardAPI.getJobTypeOptions().map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.icon} {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.priority || 'all'} onValueChange={handlePriorityFilter}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priority</SelectItem>
              {mediaDashboardAPI.getPriorityOptions().map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={resetFilters}>
            Clear Filters
          </Button>
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">Type</TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('filePath')}
                    className="h-auto p-0 font-medium"
                  >
                    File
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('createdAt')}
                    className="h-auto p-0 font-medium"
                  >
                    Created
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>Duration</TableHead>
                <TableHead className="w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {jobsLoading ? (
                [...Array(5)].map((_, i) => (
                  <TableRow key={i}>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                  </TableRow>
                ))
              ) : jobs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                    No processing jobs found
                  </TableCell>
                </TableRow>
              ) : (
                jobs.map((job) => (
                  <TableRow key={job.id}>
                    <TableCell>
                      {getJobIcon(job.type)}
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {job.filePath.split('/').pop() || job.id}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {job.mimeType}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={mediaDashboardAPI.getStatusColor(job.status)}>
                        {job.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={mediaDashboardAPI.getPriorityColor(job.priority)}>
                        {job.priority}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {job.status === 'processing' ? (
                        <div className="w-16">
                          <Progress value={job.progress} className="h-2" />
                          <span className="text-xs text-muted-foreground">
                            {job.progress}%
                          </span>
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">
                          {job.status === 'completed' ? '100%' : '-'}
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {mediaDashboardAPI.formatDate(job.createdAt)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {job.completedAt && job.startedAt
                          ? mediaDashboardAPI.formatProcessingTime(
                              new Date(job.completedAt).getTime() - new Date(job.startedAt).getTime()
                            )
                          : job.startedAt
                          ? mediaDashboardAPI.formatProcessingTime(
                              Date.now() - new Date(job.startedAt).getTime()
                            )
                          : '-'
                        }
                      </span>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleJobAction('details', job.id)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          {job.status === 'failed' && (
                            <DropdownMenuItem onClick={() => handleJobAction('retry', job.id)}>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              Retry
                            </DropdownMenuItem>
                          )}
                          {['pending', 'processing'].includes(job.status) && (
                            <DropdownMenuItem onClick={() => handleJobAction('cancel', job.id)}>
                              <X className="mr-2 h-4 w-4" />
                              Cancel
                            </DropdownMenuItem>
                          )}
                          {job.status === 'pending' && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuLabel>Set Priority</DropdownMenuLabel>
                              {mediaDashboardAPI.getPriorityOptions().map((priority) => (
                                <DropdownMenuItem
                                  key={priority.value}
                                  onClick={() => handleJobAction('priority', job.id, priority.value as ProcessingJob['priority'])}
                                >
                                  {priority.label}
                                </DropdownMenuItem>
                              ))}
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} jobs
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={!pagination.hasPrevPage}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.hasNextPage}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default JobsTable
