'use client'

import React, { useEffect } from 'react'
import { useMediaDashboardStore } from '@/stores/admin/media-dashboard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { 
  RefreshCw, 
  Activity, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Play,
  Pause,
  TrendingUp,
  Server,
  FileVideo,
  FileText
} from 'lucide-react'
import { DashboardOverview } from './DashboardOverview'
import { JobsTable } from './JobsTable'
import { JobDetails } from './JobDetails'
import { SystemHealth } from './SystemHealth'

export function MediaDashboard() {
  const {
    overview,
    loading,
    refreshing,
    autoRefresh,
    fetchOverview,
    fetchJobs,
    refreshData,
    setAutoRefresh
  } = useMediaDashboardStore()

  useEffect(() => {
    // Initial data fetch
    refreshData()
    
    // Cleanup auto-refresh on unmount
    return () => {
      setAutoRefresh(false)
    }
  }, [refreshData, setAutoRefresh])

  const handleRefresh = () => {
    refreshData()
  }

  const handleAutoRefreshToggle = (enabled: boolean) => {
    setAutoRefresh(enabled)
  }

  if (loading && !overview) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Loading dashboard...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Media Processing Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor and manage video and document processing operations
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Auto-refresh toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              id="auto-refresh"
              checked={autoRefresh}
              onCheckedChange={handleAutoRefreshToggle}
            />
            <Label htmlFor="auto-refresh" className="text-sm">
              Auto-refresh
            </Label>
          </div>
          
          {/* Manual refresh button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      {overview && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Total Jobs */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.queueStats.total}</div>
              <p className="text-xs text-muted-foreground">
                {overview.queueStats.pending} pending, {overview.queueStats.processing} processing
              </p>
            </CardContent>
          </Card>

          {/* Success Rate */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {overview.performanceMetrics.successRate.toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {overview.queueStats.completed} completed, {overview.queueStats.failed} failed
              </p>
            </CardContent>
          </Card>

          {/* Average Processing Time */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Processing Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(overview.queueStats.averageProcessingTime / 1000)}s
              </div>
              <p className="text-xs text-muted-foreground">
                Throughput: {overview.queueStats.throughput.toFixed(1)} jobs/min
              </p>
            </CardContent>
          </Card>

          {/* System Health */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Health</CardTitle>
              <Server className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className={`h-2 w-2 rounded-full ${
                  overview.systemHealth.videoProcessingService.status === 'healthy' 
                    ? 'bg-green-500' 
                    : overview.systemHealth.videoProcessingService.status === 'degraded'
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
                }`} />
                <span className="text-sm font-medium">
                  {overview.systemHealth.videoProcessingService.status === 'healthy' 
                    ? 'All Systems Operational' 
                    : overview.systemHealth.videoProcessingService.status === 'degraded'
                    ? 'Performance Degraded'
                    : 'System Issues Detected'
                  }
                </span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {overview.systemHealth.videoProcessingService.activeJobs} active jobs
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="jobs">Processing Jobs</TabsTrigger>
          <TabsTrigger value="health">System Health</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <DashboardOverview />
        </TabsContent>

        <TabsContent value="jobs" className="space-y-4">
          <JobsTable />
        </TabsContent>

        <TabsContent value="health" className="space-y-4">
          <SystemHealth />
        </TabsContent>
      </Tabs>

      {/* Job Details Modal/Sidebar would go here */}
      <JobDetails />
    </div>
  )
}

export default MediaDashboard
