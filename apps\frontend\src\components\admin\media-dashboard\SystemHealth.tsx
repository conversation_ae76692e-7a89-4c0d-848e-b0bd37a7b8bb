'use client'

import React from 'react'
import { useMediaDashboardStore } from '@/stores/admin/media-dashboard'
import { mediaDashboardAPI } from '@/lib/api/media-dashboard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Server, 
  Activity, 
  Clock, 
  Cpu, 
  HardDrive,
  Wifi,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react'

export function SystemHealth() {
  const { overview, loading } = useMediaDashboardStore()

  if (loading || !overview) {
    return (
      <div className="grid gap-4 md:grid-cols-2">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const { systemHealth, queueStats, performanceMetrics } = overview

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'degraded':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'down':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Server className="h-5 w-5 text-gray-500" />
    }
  }

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'degraded':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'down':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  // Calculate system load based on active jobs and queue length
  const videoServiceLoad = systemHealth.videoProcessingService.queueLength > 0 
    ? Math.min((systemHealth.videoProcessingService.activeJobs / 5) * 100, 100) // Assuming max 5 concurrent jobs
    : 0

  const documentServiceLoad = systemHealth.documentProcessingService.queueLength > 0
    ? Math.min((systemHealth.documentProcessingService.activeJobs / 3) * 100, 100) // Assuming max 3 concurrent jobs
    : 0

  return (
    <div className="space-y-6">
      {/* Service Status Overview */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Video Processing Service</CardTitle>
            {getHealthIcon(systemHealth.videoProcessingService.status)}
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getHealthColor(systemHealth.videoProcessingService.status)}`}>
                {systemHealth.videoProcessingService.status.toUpperCase()}
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Active Jobs</span>
                  <div className="font-medium">{systemHealth.videoProcessingService.activeJobs}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Queue Length</span>
                  <div className="font-medium">{systemHealth.videoProcessingService.queueLength}</div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm text-muted-foreground">System Load</span>
                  <span className="text-sm font-medium">{videoServiceLoad.toFixed(0)}%</span>
                </div>
                <Progress value={videoServiceLoad} className="h-2" />
              </div>

              <div>
                <span className="text-sm text-muted-foreground">Avg Processing Time</span>
                <div className="font-medium">
                  {mediaDashboardAPI.formatProcessingTime(systemHealth.videoProcessingService.averageProcessingTime)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Document Processing Service</CardTitle>
            {getHealthIcon(systemHealth.documentProcessingService.status)}
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getHealthColor(systemHealth.documentProcessingService.status)}`}>
                {systemHealth.documentProcessingService.status.toUpperCase()}
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Active Jobs</span>
                  <div className="font-medium">{systemHealth.documentProcessingService.activeJobs}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Queue Length</span>
                  <div className="font-medium">{systemHealth.documentProcessingService.queueLength}</div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm text-muted-foreground">System Load</span>
                  <span className="text-sm font-medium">{documentServiceLoad.toFixed(0)}%</span>
                </div>
                <Progress value={documentServiceLoad} className="h-2" />
              </div>

              <div>
                <span className="text-sm text-muted-foreground">Avg Processing Time</span>
                <div className="font-medium">
                  {mediaDashboardAPI.formatProcessingTime(systemHealth.documentProcessingService.averageProcessingTime)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Queue Performance</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <div className="text-2xl font-bold">{queueStats.throughput.toFixed(1)}</div>
                <p className="text-xs text-muted-foreground">jobs per minute</p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Success Rate</span>
                  <span className="font-medium">{performanceMetrics.successRate.toFixed(1)}%</span>
                </div>
                <Progress value={performanceMetrics.successRate} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Times</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <div className="text-2xl font-bold">
                  {mediaDashboardAPI.formatProcessingTime(performanceMetrics.averageProcessingTime)}
                </div>
                <p className="text-xs text-muted-foreground">average processing time</p>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-muted-foreground">Video Avg</span>
                  <div className="font-medium">
                    {mediaDashboardAPI.formatProcessingTime(systemHealth.videoProcessingService.averageProcessingTime)}
                  </div>
                </div>
                <div>
                  <span className="text-muted-foreground">Doc Avg</span>
                  <div className="font-medium">
                    {mediaDashboardAPI.formatProcessingTime(systemHealth.documentProcessingService.averageProcessingTime)}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Resources</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {/* This would be connected to actual system metrics in a real implementation */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm">CPU Usage</span>
                  <span className="text-sm font-medium">45%</span>
                </div>
                <Progress value={45} className="h-2" />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm">Memory Usage</span>
                  <span className="text-sm font-medium">62%</span>
                </div>
                <Progress value={62} className="h-2" />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm">Disk Usage</span>
                  <span className="text-sm font-medium">38%</span>
                </div>
                <Progress value={38} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Alerts */}
      <Card>
        <CardHeader>
          <CardTitle>System Alerts</CardTitle>
          <CardDescription>
            Current system status and recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {/* Generate alerts based on system status */}
            {systemHealth.videoProcessingService.status === 'degraded' && (
              <div className="flex items-start space-x-3 p-3 rounded-lg bg-yellow-50 border border-yellow-200">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <div className="font-medium text-yellow-800">Video Processing Performance Degraded</div>
                  <div className="text-sm text-yellow-700">
                    Processing times are higher than normal. Consider checking system resources.
                  </div>
                </div>
              </div>
            )}

            {queueStats.failed > 5 && (
              <div className="flex items-start space-x-3 p-3 rounded-lg bg-red-50 border border-red-200">
                <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <div className="font-medium text-red-800">High Failure Rate Detected</div>
                  <div className="text-sm text-red-700">
                    {queueStats.failed} jobs have failed. Review error logs and consider system maintenance.
                  </div>
                </div>
              </div>
            )}

            {queueStats.pending > 20 && (
              <div className="flex items-start space-x-3 p-3 rounded-lg bg-blue-50 border border-blue-200">
                <Activity className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <div className="font-medium text-blue-800">High Queue Volume</div>
                  <div className="text-sm text-blue-700">
                    {queueStats.pending} jobs are pending. Consider scaling processing capacity.
                  </div>
                </div>
              </div>
            )}

            {systemHealth.videoProcessingService.status === 'healthy' && 
             systemHealth.documentProcessingService.status === 'healthy' && 
             queueStats.failed <= 5 && 
             queueStats.pending <= 20 && (
              <div className="flex items-start space-x-3 p-3 rounded-lg bg-green-50 border border-green-200">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <div className="font-medium text-green-800">All Systems Operational</div>
                  <div className="text-sm text-green-700">
                    All processing services are running normally with good performance metrics.
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SystemHealth
