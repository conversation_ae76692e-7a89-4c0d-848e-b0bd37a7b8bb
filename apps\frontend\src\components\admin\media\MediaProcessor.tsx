'use client'

import React, { useState, useCallback } from 'react'
import { 
  Image, 
  Video, 
  Music, 
  FileText, 
  Settings, 
  Play, 
  Pause, 
  Download,
  Eye,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Label } from '@/components/ui/label'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import { ContentCreateGate } from '@/components/PermissionGate'
import { useMediaProcessing } from '@/hooks/useMediaProcessing'

export interface MediaFile {
  id: string
  originalName: string
  filename: string
  mimeType: string
  size: number
  category: string
  url: string
  path: string
  uploadedAt: Date
}

export interface ProcessingOptions {
  generateThumbnail?: boolean
  optimizeImage?: boolean
  transcodeVideo?: boolean
  quality?: number
  maxWidth?: number
  maxHeight?: number
  videoFormats?: string[]
}

export interface MediaProcessorProps {
  file: MediaFile
  onProcessingComplete?: (processed: any) => void
  onProcessingError?: (error: string) => void
  className?: string
}

export const MediaProcessor: React.FC<MediaProcessorProps> = ({
  file,
  onProcessingComplete,
  onProcessingError,
  className = ''
}) => {
  const [options, setOptions] = useState<ProcessingOptions>({
    generateThumbnail: true,
    optimizeImage: file.category === 'image',
    transcodeVideo: file.category === 'video',
    quality: 85,
    maxWidth: 1920,
    maxHeight: 1080,
    videoFormats: ['mp4', 'webm']
  })

  const [processing, setProcessing] = useState(false)
  const [processed, setProcessed] = useState<any>(null)
  const [jobId, setJobId] = useState<string | null>(null)
  const [progress, setProgress] = useState(0)

  const { toast } = useToast()
  const { processMedia, getJobStatus, generateThumbnail, optimizeImage } = useMediaProcessing()

  const getFileIcon = (category: string) => {
    switch (category) {
      case 'image': return <Image className="h-5 w-5" />
      case 'video': return <Video className="h-5 w-5" />
      case 'audio': return <Music className="h-5 w-5" />
      default: return <FileText className="h-5 w-5" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleProcess = useCallback(async () => {
    setProcessing(true)
    setProgress(0)

    try {
      const result = await processMedia(file.path, file.mimeType, options)

      if (result.success) {
        setJobId(result.jobId || null)
        setProcessed(result.processed)
        
        // Poll for job status if we have a job ID
        if (result.jobId) {
          const pollInterval = setInterval(async () => {
            const status = await getJobStatus(result.jobId!)
            
            if (status.success && status.data) {
              setProgress(status.data.progress)
              
              if (status.data.status === 'completed') {
                clearInterval(pollInterval)
                setProcessing(false)
                onProcessingComplete?.(result.processed)
                toast({
                  title: 'Processing complete',
                  description: 'Media file has been processed successfully'
                })
              } else if (status.data.status === 'error') {
                clearInterval(pollInterval)
                setProcessing(false)
                onProcessingError?.('Processing failed')
                toast({
                  title: 'Processing failed',
                  description: 'An error occurred while processing the media file',
                  variant: 'destructive'
                })
              }
            }
          }, 1000)

          // Cleanup interval after 5 minutes
          setTimeout(() => clearInterval(pollInterval), 5 * 60 * 1000)
        } else {
          setProcessing(false)
          onProcessingComplete?.(result.processed)
          toast({
            title: 'Processing complete',
            description: 'Media file has been processed successfully'
          })
        }
      } else {
        setProcessing(false)
        onProcessingError?.(result.error || 'Processing failed')
        toast({
          title: 'Processing failed',
          description: result.error || 'An error occurred while processing the media file',
          variant: 'destructive'
        })
      }
    } catch (error) {
      setProcessing(false)
      const errorMessage = error instanceof Error ? error.message : 'Processing failed'
      onProcessingError?.(errorMessage)
      toast({
        title: 'Processing failed',
        description: errorMessage,
        variant: 'destructive'
      })
    }
  }, [file, options, processMedia, getJobStatus, onProcessingComplete, onProcessingError, toast])

  const handleQuickThumbnail = useCallback(async () => {
    try {
      const result = await generateThumbnail(file.path, file.mimeType)
      
      if (result.success) {
        toast({
          title: 'Thumbnail generated',
          description: 'Thumbnail has been generated successfully'
        })
      } else {
        toast({
          title: 'Thumbnail generation failed',
          description: result.error || 'Failed to generate thumbnail',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Thumbnail generation failed',
        description: error instanceof Error ? error.message : 'Failed to generate thumbnail',
        variant: 'destructive'
      })
    }
  }, [file, generateThumbnail, toast])

  const handleQuickOptimize = useCallback(async () => {
    if (file.category !== 'image') return

    try {
      const result = await optimizeImage(file.path, { quality: options.quality })
      
      if (result.success) {
        toast({
          title: 'Image optimized',
          description: 'Image has been optimized successfully'
        })
      } else {
        toast({
          title: 'Image optimization failed',
          description: result.error || 'Failed to optimize image',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Image optimization failed',
        description: error instanceof Error ? error.message : 'Failed to optimize image',
        variant: 'destructive'
      })
    }
  }, [file, options.quality, optimizeImage, toast])

  return (
    <ContentCreateGate fallback={
      <Card className="border-dashed border-2 border-gray-300">
        <CardContent className="flex flex-col items-center justify-center py-8">
          <AlertCircle className="h-8 w-8 text-gray-400 mb-2" />
          <p className="text-gray-500">You don't have permission to process media files</p>
        </CardContent>
      </Card>
    }>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getFileIcon(file.category)}
            Media Processor
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* File Info */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              {getFileIcon(file.category)}
              <div>
                <p className="font-medium">{file.originalName}</p>
                <p className="text-sm text-gray-500">
                  {formatFileSize(file.size)} • {file.mimeType}
                </p>
              </div>
            </div>
            <Badge variant="secondary">{file.category}</Badge>
          </div>

          {/* Processing Progress */}
          {processing && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Processing...</span>
                <span className="text-sm text-gray-500">{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {/* Processed Results */}
          {processed && (
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Processing Results
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {processed.thumbnail && (
                  <div className="border rounded-lg p-3">
                    <p className="text-sm font-medium mb-2">Thumbnail</p>
                    <img
                      src={processed.thumbnail.url}
                      alt="Thumbnail"
                      className="w-full h-32 object-cover rounded"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {processed.thumbnail.dimensions.width}x{processed.thumbnail.dimensions.height}
                    </p>
                  </div>
                )}
                
                {processed.optimized && (
                  <div className="border rounded-lg p-3">
                    <p className="text-sm font-medium mb-2">Optimized</p>
                    <div className="space-y-1">
                      <p className="text-xs text-gray-500">
                        Size: {formatFileSize(processed.optimized.size)}
                      </p>
                      <p className="text-xs text-gray-500">
                        Quality: {processed.optimized.quality}%
                      </p>
                      {processed.optimized.dimensions && (
                        <p className="text-xs text-gray-500">
                          {processed.optimized.dimensions.width}x{processed.optimized.dimensions.height}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Processing Options */}
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>
            
            <TabsContent value="basic" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="thumbnail">Generate Thumbnail</Label>
                  <Switch
                    id="thumbnail"
                    checked={options.generateThumbnail}
                    onCheckedChange={(checked) => 
                      setOptions(prev => ({ ...prev, generateThumbnail: checked }))
                    }
                  />
                </div>
                
                {file.category === 'image' && (
                  <div className="flex items-center justify-between">
                    <Label htmlFor="optimize">Optimize Image</Label>
                    <Switch
                      id="optimize"
                      checked={options.optimizeImage}
                      onCheckedChange={(checked) => 
                        setOptions(prev => ({ ...prev, optimizeImage: checked }))
                      }
                    />
                  </div>
                )}
                
                {file.category === 'video' && (
                  <div className="flex items-center justify-between">
                    <Label htmlFor="transcode">Transcode Video</Label>
                    <Switch
                      id="transcode"
                      checked={options.transcodeVideo}
                      onCheckedChange={(checked) => 
                        setOptions(prev => ({ ...prev, transcodeVideo: checked }))
                      }
                    />
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="advanced" className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Quality: {options.quality}%</Label>
                  <Slider
                    value={[options.quality || 85]}
                    onValueChange={([value]) => 
                      setOptions(prev => ({ ...prev, quality: value }))
                    }
                    max={100}
                    min={10}
                    step={5}
                    className="w-full"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Max Width</Label>
                    <input
                      type="number"
                      value={options.maxWidth || 1920}
                      onChange={(e) => 
                        setOptions(prev => ({ ...prev, maxWidth: parseInt(e.target.value) }))
                      }
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Max Height</Label>
                    <input
                      type="number"
                      value={options.maxHeight || 1080}
                      onChange={(e) => 
                        setOptions(prev => ({ ...prev, maxHeight: parseInt(e.target.value) }))
                      }
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={handleProcess}
              disabled={processing}
              className="flex-1"
            >
              {processing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Settings className="h-4 w-4 mr-2" />
                  Process Media
                </>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={handleQuickThumbnail}
              disabled={processing}
            >
              <Eye className="h-4 w-4 mr-2" />
              Thumbnail
            </Button>
            
            {file.category === 'image' && (
              <Button
                variant="outline"
                onClick={handleQuickOptimize}
                disabled={processing}
              >
                <Image className="h-4 w-4 mr-2" />
                Optimize
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </ContentCreateGate>
  )
}

export default MediaProcessor
