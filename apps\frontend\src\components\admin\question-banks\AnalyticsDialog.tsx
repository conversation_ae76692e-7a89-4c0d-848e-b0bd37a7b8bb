'use client'

import React, { useEffect } from 'react'
import { useQuestionBankStore } from '@/stores/admin/question-banks'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  BarChart3, 
  Pie<PERSON>hart, 
  TrendingUp, 
  FileText, 
  Clock, 
  Target,
  Image,
  Loader2
} from 'lucide-react'

interface AnalyticsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  questionBankId: string | null
}

export function AnalyticsDialog({ 
  open, 
  onOpenChange, 
  questionBankId 
}: AnalyticsDialogProps) {
  const { 
    analytics, 
    currentQuestionBank, 
    loading,
    fetchAnalytics,
    fetchQuestionBank 
  } = useQuestionBankStore()

  useEffect(() => {
    if (open && questionBankId) {
      fetchQuestionBank(questionBankId)
      fetchAnalytics(questionBankId)
    }
  }, [open, questionBankId, fetchQuestionBank, fetchAnalytics])

  if (!questionBankId) return null

  const renderDifficultyChart = () => {
    if (!analytics?.difficultyDistribution) return null

    const { easy, medium, hard } = analytics.difficultyDistribution
    const total = easy + medium + hard

    if (total === 0) return <div className="text-center text-muted-foreground">No data available</div>

    return (
      <div className="space-y-3">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">Easy</span>
            <span className="text-sm font-medium">{easy} ({((easy / total) * 100).toFixed(1)}%)</span>
          </div>
          <Progress value={(easy / total) * 100} className="h-2 bg-green-100">
            <div className="h-full bg-green-500 rounded-full" style={{ width: `${(easy / total) * 100}%` }} />
          </Progress>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">Medium</span>
            <span className="text-sm font-medium">{medium} ({((medium / total) * 100).toFixed(1)}%)</span>
          </div>
          <Progress value={(medium / total) * 100} className="h-2 bg-yellow-100">
            <div className="h-full bg-yellow-500 rounded-full" style={{ width: `${(medium / total) * 100}%` }} />
          </Progress>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">Hard</span>
            <span className="text-sm font-medium">{hard} ({((hard / total) * 100).toFixed(1)}%)</span>
          </div>
          <Progress value={(hard / total) * 100} className="h-2 bg-red-100">
            <div className="h-full bg-red-500 rounded-full" style={{ width: `${(hard / total) * 100}%` }} />
          </Progress>
        </div>
      </div>
    )
  }

  const renderTypeDistribution = () => {
    if (!analytics?.typeDistribution) return null

    const typeLabels: Record<string, string> = {
      'multiple_choice_single': 'Multiple Choice (Single)',
      'multiple_choice_multiple': 'Multiple Choice (Multiple)',
      'true_false': 'True/False',
      'fill_blanks': 'Fill in Blanks',
      'essay': 'Essay',
      'matching': 'Matching',
      'ordering': 'Ordering'
    }

    const types = Object.entries(analytics.typeDistribution)
    const total = types.reduce((sum, [, count]) => sum + count, 0)

    if (total === 0) return <div className="text-center text-muted-foreground">No data available</div>

    return (
      <div className="space-y-2">
        {types.map(([type, count]) => (
          <div key={type} className="flex items-center justify-between">
            <span className="text-sm">{typeLabels[type] || type}</span>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">{count}</span>
              <Badge variant="outline" className="text-xs">
                {((count / total) * 100).toFixed(1)}%
              </Badge>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Question Bank Analytics</span>
          </DialogTitle>
          <DialogDescription>
            {currentQuestionBank?.title && (
              <span>Analytics and insights for "{currentQuestionBank.title}"</span>
            )}
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading analytics...</span>
          </div>
        ) : analytics ? (
          <div className="space-y-6">
            {/* Overview Cards */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Questions</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.totalQuestions}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Points</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.averagePoints}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">With Images</CardTitle>
                  <Image className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.questionsWithImages}</div>
                  <p className="text-xs text-muted-foreground">
                    {analytics.totalQuestions > 0 
                      ? `${((analytics.questionsWithImages / analytics.totalQuestions) * 100).toFixed(1)}%`
                      : '0%'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Timed Questions</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.questionsWithTimeLimit}</div>
                  <p className="text-xs text-muted-foreground">
                    {analytics.totalQuestions > 0 
                      ? `${((analytics.questionsWithTimeLimit / analytics.totalQuestions) * 100).toFixed(1)}%`
                      : '0%'
                    }
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Charts */}
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <PieChart className="h-4 w-4" />
                    <span>Difficulty Distribution</span>
                  </CardTitle>
                  <CardDescription>
                    Breakdown of questions by difficulty level
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {renderDifficultyChart()}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="h-4 w-4" />
                    <span>Question Types</span>
                  </CardTitle>
                  <CardDescription>
                    Distribution of different question types
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {renderTypeDistribution()}
                </CardContent>
              </Card>
            </div>

            {/* Question Bank Information */}
            <Card>
              <CardHeader>
                <CardTitle>Question Bank Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Title</span>
                      <span className="text-sm font-medium">{currentQuestionBank?.title}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Category</span>
                      <span className="text-sm font-medium">
                        {currentQuestionBank?.category || 'Uncategorized'}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Sharing</span>
                      <Badge variant={currentQuestionBank?.is_shared ? 'default' : 'outline'}>
                        {currentQuestionBank?.is_shared ? 'Shared' : 'Private'}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Created</span>
                      <span className="text-sm font-medium">
                        {currentQuestionBank?.createdAt && 
                          new Date(currentQuestionBank.createdAt).toLocaleDateString()
                        }
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Last Updated</span>
                      <span className="text-sm font-medium">
                        {currentQuestionBank?.updatedAt && 
                          new Date(currentQuestionBank.updatedAt).toLocaleDateString()
                        }
                      </span>
                    </div>
                  </div>
                </div>

                {currentQuestionBank?.description && (
                  <div className="mt-4">
                    <div className="text-sm text-muted-foreground mb-1">Description</div>
                    <div className="text-sm">{currentQuestionBank.description}</div>
                  </div>
                )}

                {currentQuestionBank?.tags && currentQuestionBank.tags.length > 0 && (
                  <div className="mt-4">
                    <div className="text-sm text-muted-foreground mb-2">Tags</div>
                    <div className="flex flex-wrap gap-1">
                      {currentQuestionBank.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag.tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recommendations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4" />
                  <span>Recommendations</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {analytics.totalQuestions < 10 && (
                    <div className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
                      Consider adding more questions to improve assessment variety
                    </div>
                  )}
                  
                  {analytics.questionsWithImages === 0 && analytics.totalQuestions > 5 && (
                    <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                      Adding images to questions can improve engagement and clarity
                    </div>
                  )}
                  
                  {analytics.difficultyDistribution.medium === 0 && analytics.totalQuestions > 3 && (
                    <div className="text-sm text-purple-600 bg-purple-50 p-2 rounded">
                      Consider adding medium difficulty questions for better balance
                    </div>
                  )}
                  
                  {Object.keys(analytics.typeDistribution).length === 1 && analytics.totalQuestions > 5 && (
                    <div className="text-sm text-green-600 bg-green-50 p-2 rounded">
                      Diversifying question types can create more engaging assessments
                    </div>
                  )}
                  
                  {analytics.totalQuestions >= 10 && 
                   analytics.difficultyDistribution.easy > 0 && 
                   analytics.difficultyDistribution.medium > 0 && 
                   analytics.difficultyDistribution.hard > 0 && 
                   Object.keys(analytics.typeDistribution).length > 2 && (
                    <div className="text-sm text-green-600 bg-green-50 p-2 rounded">
                      ✓ Well-balanced question bank with good variety and distribution
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No analytics data available
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default AnalyticsDialog
