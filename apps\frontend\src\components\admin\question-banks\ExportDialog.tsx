'use client'

import React, { useState } from 'react'
import { useQuestionBankStore } from '@/stores/admin/question-banks'
import { QuestionBank } from '@/lib/api/question-banks'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Download, 
  FileText, 
  Database,
  CheckCircle,
  Loader2,
  Info
} from 'lucide-react'
import { toast } from 'sonner'

interface ExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  questionBanks: QuestionBank[]
}

export function ExportDialog({ 
  open, 
  onOpenChange, 
  questionBanks 
}: ExportDialogProps) {
  const { exportQuestionBank, exporting } = useQuestionBankStore()
  const [exportType, setExportType] = useState<'single' | 'multiple'>('single')
  const [selectedQuestionBank, setSelectedQuestionBank] = useState<string>('')
  const [selectedQuestionBanks, setSelectedQuestionBanks] = useState<string[]>([])
  const [format, setFormat] = useState<'json' | 'csv'>('json')
  const [includeAnalytics, setIncludeAnalytics] = useState(false)

  const handleExport = async () => {
    try {
      if (exportType === 'single') {
        if (!selectedQuestionBank) {
          toast.error('Please select a question bank to export')
          return
        }
        
        await exportQuestionBank(selectedQuestionBank, format)
        toast.success('Question bank exported successfully')
      } else {
        if (selectedQuestionBanks.length === 0) {
          toast.error('Please select at least one question bank to export')
          return
        }
        
        // For multiple exports, we'll export each one individually
        for (const qbId of selectedQuestionBanks) {
          await exportQuestionBank(qbId, format)
        }
        toast.success(`${selectedQuestionBanks.length} question banks exported successfully`)
      }
      
      onOpenChange(false)
    } catch (error) {
      toast.error('Failed to export question bank(s)')
    }
  }

  const handleClose = () => {
    setExportType('single')
    setSelectedQuestionBank('')
    setSelectedQuestionBanks([])
    setFormat('json')
    setIncludeAnalytics(false)
    onOpenChange(false)
  }

  const handleQuestionBankToggle = (questionBankId: string, checked: boolean) => {
    if (checked) {
      setSelectedQuestionBanks([...selectedQuestionBanks, questionBankId])
    } else {
      setSelectedQuestionBanks(selectedQuestionBanks.filter(id => id !== questionBankId))
    }
  }

  const getSelectedQuestionBankInfo = () => {
    if (exportType === 'single' && selectedQuestionBank) {
      const qb = questionBanks.find(q => q.id === selectedQuestionBank)
      return qb ? {
        count: 1,
        totalQuestions: qb.question_count || 0,
        names: [qb.title]
      } : null
    } else if (exportType === 'multiple' && selectedQuestionBanks.length > 0) {
      const selectedBanks = questionBanks.filter(qb => selectedQuestionBanks.includes(qb.id))
      return {
        count: selectedBanks.length,
        totalQuestions: selectedBanks.reduce((sum, qb) => sum + (qb.question_count || 0), 0),
        names: selectedBanks.map(qb => qb.title)
      }
    }
    return null
  }

  const selectedInfo = getSelectedQuestionBankInfo()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Export Question Banks</DialogTitle>
          <DialogDescription>
            Export your question banks to CSV or JSON format for backup or sharing
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Type */}
          <div className="space-y-3">
            <Label>Export Type</Label>
            <RadioGroup value={exportType} onValueChange={(value: 'single' | 'multiple') => setExportType(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="single" id="single" />
                <Label htmlFor="single">Single Question Bank</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="multiple" id="multiple" />
                <Label htmlFor="multiple">Multiple Question Banks</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Single Question Bank Selection */}
          {exportType === 'single' && (
            <div className="space-y-2">
              <Label>Select Question Bank</Label>
              <Select value={selectedQuestionBank} onValueChange={setSelectedQuestionBank}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a question bank to export" />
                </SelectTrigger>
                <SelectContent>
                  {questionBanks.map((qb) => (
                    <SelectItem key={qb.id} value={qb.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{qb.title}</span>
                        <span className="text-sm text-muted-foreground ml-2">
                          ({qb.question_count || 0} questions)
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Multiple Question Banks Selection */}
          {exportType === 'multiple' && (
            <div className="space-y-2">
              <Label>Select Question Banks</Label>
              <div className="max-h-48 overflow-y-auto border rounded-lg p-3 space-y-2">
                {questionBanks.map((qb) => (
                  <div key={qb.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={qb.id}
                      checked={selectedQuestionBanks.includes(qb.id)}
                      onCheckedChange={(checked) => handleQuestionBankToggle(qb.id, !!checked)}
                    />
                    <Label htmlFor={qb.id} className="flex-1 cursor-pointer">
                      <div className="flex items-center justify-between">
                        <span>{qb.title}</span>
                        <span className="text-sm text-muted-foreground">
                          ({qb.question_count || 0} questions)
                        </span>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
              {selectedQuestionBanks.length > 0 && (
                <div className="text-sm text-muted-foreground">
                  {selectedQuestionBanks.length} question bank(s) selected
                </div>
              )}
            </div>
          )}

          {/* Format Selection */}
          <div className="space-y-3">
            <Label>Export Format</Label>
            <RadioGroup value={format} onValueChange={(value: 'json' | 'csv') => setFormat(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="json" id="json" />
                <Label htmlFor="json" className="flex items-center space-x-2">
                  <Database className="h-4 w-4" />
                  <span>JSON (Structured data with metadata)</span>
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="csv" id="csv" />
                <Label htmlFor="csv" className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>CSV (Spreadsheet compatible)</span>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Export Options */}
          <div className="space-y-3">
            <Label>Export Options</Label>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="analytics"
                checked={includeAnalytics}
                onCheckedChange={(checked) => setIncludeAnalytics(!!checked)}
              />
              <Label htmlFor="analytics">Include analytics and usage statistics</Label>
            </div>
          </div>

          {/* Export Summary */}
          {selectedInfo && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <div>
                    <strong>Export Summary:</strong>
                  </div>
                  <div>
                    • {selectedInfo.count} question bank{selectedInfo.count > 1 ? 's' : ''}
                  </div>
                  <div>
                    • {selectedInfo.totalQuestions} total questions
                  </div>
                  <div>
                    • Format: {format.toUpperCase()}
                  </div>
                  {selectedInfo.count <= 3 && (
                    <div className="mt-2">
                      <strong>Selected:</strong>
                      <ul className="list-disc list-inside ml-2">
                        {selectedInfo.names.map((name, index) => (
                          <li key={index} className="text-sm">{name}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Format Information */}
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              {format === 'json' ? (
                <div>
                  <strong>JSON Export:</strong> Includes complete question data, metadata, options, 
                  correct answers, explanations, and question bank information. Best for backup 
                  and system migration.
                </div>
              ) : (
                <div>
                  <strong>CSV Export:</strong> Tabular format suitable for spreadsheet applications. 
                  Includes question content, type, difficulty, points, and answers. Options are 
                  JSON-encoded in a single column.
                </div>
              )}
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={exporting}>
            Cancel
          </Button>
          <Button 
            onClick={handleExport} 
            disabled={
              exporting || 
              (exportType === 'single' && !selectedQuestionBank) ||
              (exportType === 'multiple' && selectedQuestionBanks.length === 0)
            }
          >
            {exporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default ExportDialog
