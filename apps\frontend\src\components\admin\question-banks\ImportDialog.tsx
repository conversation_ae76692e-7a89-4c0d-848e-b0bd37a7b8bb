'use client'

import React, { useState, useCallback } from 'react'
import { useQuestionBankStore } from '@/stores/admin/question-banks'
import { questionBankAPI } from '@/lib/api/question-banks'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Upload, 
  FileText, 
  Download, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Loader2
} from 'lucide-react'
import { useDropzone } from 'react-dropzone'
import { toast } from 'sonner'

interface ImportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ImportDialog({ open, onOpenChange }: ImportDialogProps) {
  const { questionBanks, importing, importQuestions } = useQuestionBankStore()
  const [selectedQuestionBank, setSelectedQuestionBank] = useState<string>('')
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [importResult, setImportResult] = useState<any>(null)
  const [step, setStep] = useState<'select' | 'upload' | 'result'>('select')

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      const validTypes = ['text/csv', 'application/json', 'text/plain']
      const validExtensions = ['.csv', '.json']
      
      const isValidType = validTypes.includes(file.type) || 
                         validExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
      
      if (isValidType) {
        setUploadedFile(file)
        setStep('upload')
      } else {
        toast.error('Please upload a CSV or JSON file')
      }
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/json': ['.json'],
      'text/plain': ['.csv']
    },
    multiple: false
  })

  const handleImport = async () => {
    if (!selectedQuestionBank || !uploadedFile) {
      toast.error('Please select a question bank and upload a file')
      return
    }

    try {
      const result = await importQuestions(selectedQuestionBank, uploadedFile)
      setImportResult(result)
      setStep('result')
      
      if (result && result.imported > 0) {
        toast.success(`Successfully imported ${result.imported} questions`)
      }
    } catch (error) {
      toast.error('Failed to import questions')
    }
  }

  const handleDownloadTemplate = () => {
    questionBankAPI.downloadSampleTemplate()
    toast.success('Template downloaded successfully')
  }

  const handleClose = () => {
    setStep('select')
    setSelectedQuestionBank('')
    setUploadedFile(null)
    setImportResult(null)
    onOpenChange(false)
  }

  const handleStartOver = () => {
    setStep('select')
    setUploadedFile(null)
    setImportResult(null)
  }

  const renderSelectStep = () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Select Question Bank</Label>
        <Select value={selectedQuestionBank} onValueChange={setSelectedQuestionBank}>
          <SelectTrigger>
            <SelectValue placeholder="Choose a question bank to import into" />
          </SelectTrigger>
          <SelectContent>
            {questionBanks.map((qb) => (
              <SelectItem key={qb.id} value={qb.id}>
                {qb.title} ({qb.question_count || 0} questions)
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>Upload File</Label>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadTemplate}
          >
            <Download className="h-4 w-4 mr-2" />
            Download Template
          </Button>
        </div>

        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragActive 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <input {...getInputProps()} />
          <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
          {isDragActive ? (
            <p className="text-blue-600">Drop the file here...</p>
          ) : (
            <div>
              <p className="text-gray-600 mb-2">
                Drag and drop a CSV or JSON file here, or click to browse
              </p>
              <p className="text-sm text-gray-500">
                Supported formats: .csv, .json
              </p>
            </div>
          )}
        </div>

        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Make sure your file follows the template format. Download the template above for reference.
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )

  const renderUploadStep = () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Selected Question Bank</Label>
        <div className="p-3 bg-gray-50 rounded-lg">
          {questionBanks.find(qb => qb.id === selectedQuestionBank)?.title}
        </div>
      </div>

      <div className="space-y-2">
        <Label>Selected File</Label>
        <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          <FileText className="h-5 w-5 text-gray-500" />
          <div className="flex-1">
            <div className="font-medium">{uploadedFile?.name}</div>
            <div className="text-sm text-gray-500">
              {uploadedFile?.size ? `${(uploadedFile.size / 1024).toFixed(1)} KB` : ''}
            </div>
          </div>
        </div>
      </div>

      {importing && (
        <div className="space-y-2">
          <Label>Import Progress</Label>
          <div className="space-y-2">
            <Progress value={50} className="h-2" />
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Processing questions...</span>
            </div>
          </div>
        </div>
      )}

      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          This will add questions to the selected question bank. Make sure the file format is correct.
        </AlertDescription>
      </Alert>
    </div>
  )

  const renderResultStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        {importResult?.imported > 0 ? (
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
        ) : (
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        )}
        <h3 className="text-lg font-medium mb-2">Import Complete</h3>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{importResult?.total || 0}</div>
          <div className="text-sm text-blue-600">Total Questions</div>
        </div>
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{importResult?.imported || 0}</div>
          <div className="text-sm text-green-600">Successfully Imported</div>
        </div>
        <div className="text-center p-4 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-600">{importResult?.failed || 0}</div>
          <div className="text-sm text-red-600">Failed</div>
        </div>
      </div>

      {importResult?.errors && importResult.errors.length > 0 && (
        <div className="space-y-2">
          <Label>Import Errors</Label>
          <div className="max-h-40 overflow-y-auto space-y-1">
            {importResult.errors.map((error: string, index: number) => (
              <div key={index} className="text-sm text-red-600 p-2 bg-red-50 rounded">
                {error}
              </div>
            ))}
          </div>
        </div>
      )}

      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          {importResult?.imported > 0 
            ? `${importResult.imported} questions have been added to your question bank.`
            : 'No questions were imported. Please check your file format and try again.'
          }
        </AlertDescription>
      </Alert>
    </div>
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Import Questions</DialogTitle>
          <DialogDescription>
            Import questions from a CSV or JSON file into your question bank
          </DialogDescription>
        </DialogHeader>

        {step === 'select' && renderSelectStep()}
        {step === 'upload' && renderUploadStep()}
        {step === 'result' && renderResultStep()}

        <DialogFooter>
          {step === 'select' && (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button 
                onClick={() => setStep('upload')} 
                disabled={!selectedQuestionBank}
              >
                Next
              </Button>
            </>
          )}
          
          {step === 'upload' && (
            <>
              <Button variant="outline" onClick={handleStartOver}>
                Back
              </Button>
              <Button 
                onClick={handleImport} 
                disabled={!uploadedFile || importing}
              >
                {importing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Importing...
                  </>
                ) : (
                  'Import Questions'
                )}
              </Button>
            </>
          )}
          
          {step === 'result' && (
            <>
              <Button variant="outline" onClick={handleStartOver}>
                Import More
              </Button>
              <Button onClick={handleClose}>
                Done
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default ImportDialog
