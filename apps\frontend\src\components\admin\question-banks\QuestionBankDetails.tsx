'use client'

import React, { useEffect, useState } from 'react'
import { useQuestionBankStore } from '@/stores/admin/question-banks'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ArrowLeft, 
  Edit, 
  Share, 
  BarChart3, 
  Download,
  Upload,
  Plus,
  BookOpen,
  Users,
  Calendar,
  Tag,
  FileText,
  Clock,
  Target,
  Image
} from 'lucide-react'

interface QuestionBankDetailsProps {
  questionBankId: string
  onBack: () => void
  onEdit: () => void
}

export function QuestionBankDetails({
  questionBankId,
  onBack,
  onEdit
}: QuestionBankDetailsProps) {
  const {
    currentQuestionBank,
    questions,
    analytics,
    loading,
    fetchQuestionBank,
    fetchAnalytics,
    shareQuestionBank,
    exportQuestionBank
  } = useQuestionBankStore()

  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (questionBankId) {
      fetchQuestionBank(questionBankId, true) // Include questions
      fetchAnalytics(questionBankId)
    }
  }, [questionBankId, fetchQuestionBank, fetchAnalytics])

  const handleShare = async () => {
    if (currentQuestionBank) {
      await shareQuestionBank(currentQuestionBank.id, !currentQuestionBank.is_shared)
    }
  }

  const handleExport = async (format: 'json' | 'csv') => {
    if (currentQuestionBank) {
      await exportQuestionBank(currentQuestionBank.id, format)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getQuestionTypeLabel = (type: string) => {
    const typeLabels: Record<string, string> = {
      'multiple_choice_single': 'Multiple Choice (Single)',
      'multiple_choice_multiple': 'Multiple Choice (Multiple)',
      'true_false': 'True/False',
      'fill_blanks': 'Fill in Blanks',
      'essay': 'Essay',
      'matching': 'Matching',
      'ordering': 'Ordering'
    }
    return typeLabels[type] || type
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'hard':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading || !currentQuestionBank) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
        </div>
        <div className="grid gap-4 md:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{currentQuestionBank.title}</h1>
            <p className="text-muted-foreground">{currentQuestionBank.description}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => handleExport('json')}>
            <Download className="h-4 w-4 mr-2" />
            Export JSON
          </Button>
          <Button variant="outline" onClick={() => handleExport('csv')}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="outline" onClick={handleShare}>
            <Share className="h-4 w-4 mr-2" />
            {currentQuestionBank.is_shared ? 'Unshare' : 'Share'}
          </Button>
          <Button onClick={onEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Questions</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{questions.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Points</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.averagePoints || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">With Images</CardTitle>
            <Image className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.questionsWithImages || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sharing Status</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge variant={currentQuestionBank.is_shared ? 'default' : 'outline'}>
              {currentQuestionBank.is_shared ? 'Shared' : 'Private'}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="questions">Questions ({questions.length})</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Category</span>
                  <Badge variant="secondary">
                    {currentQuestionBank.category || 'Uncategorized'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Course</span>
                  <span className="text-sm font-medium">
                    {typeof currentQuestionBank.course === 'string' 
                      ? currentQuestionBank.course 
                      : currentQuestionBank.course?.title || 'N/A'
                    }
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Created</span>
                  <span className="text-sm font-medium">
                    {formatDate(currentQuestionBank.createdAt)}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Last Updated</span>
                  <span className="text-sm font-medium">
                    {formatDate(currentQuestionBank.updatedAt)}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Tags */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Tag className="h-4 w-4" />
                  <span>Tags</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {currentQuestionBank.tags && currentQuestionBank.tags.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {currentQuestionBank.tags.map((tag, index) => (
                      <Badge key={index} variant="outline">
                        {tag.tag}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">No tags assigned</div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {questions.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <BookOpen className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                <div className="text-muted-foreground mb-2">No questions in this bank</div>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Questions
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {questions.map((question, index) => (
                <Card key={question.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-base">
                          Question {index + 1}
                        </CardTitle>
                        <CardDescription className="mt-2">
                          {question.content}
                        </CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">
                          {getQuestionTypeLabel(question.type)}
                        </Badge>
                        <Badge className={getDifficultyColor(question.difficulty)}>
                          {question.difficulty}
                        </Badge>
                        <Badge variant="secondary">
                          {question.points} pts
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  
                  {question.options && question.options.length > 0 && (
                    <CardContent>
                      <div className="space-y-2">
                        <div className="text-sm font-medium">Options:</div>
                        <div className="space-y-1">
                          {question.options.map((option, optionIndex) => (
                            <div 
                              key={optionIndex} 
                              className={`text-sm p-2 rounded ${
                                option.is_correct 
                                  ? 'bg-green-50 text-green-800 border border-green-200' 
                                  : 'bg-gray-50'
                              }`}
                            >
                              {String.fromCharCode(65 + optionIndex)}. {option.text}
                              {option.is_correct && (
                                <span className="ml-2 text-xs font-medium">✓ Correct</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      {question.explanation && (
                        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                          <div className="text-sm font-medium text-blue-800 mb-1">Explanation:</div>
                          <div className="text-sm text-blue-700">{question.explanation}</div>
                        </div>
                      )}
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {analytics ? (
            <div className="grid gap-6 md:grid-cols-2">
              {/* Difficulty Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Difficulty Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.difficultyDistribution).map(([difficulty, count]) => {
                      const percentage = analytics.totalQuestions > 0 
                        ? (count / analytics.totalQuestions) * 100 
                        : 0
                      
                      return (
                        <div key={difficulty} className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm capitalize">{difficulty}</span>
                            <span className="text-sm font-medium">{count} ({percentage.toFixed(1)}%)</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${
                                difficulty === 'easy' ? 'bg-green-500' :
                                difficulty === 'medium' ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Question Types */}
              <Card>
                <CardHeader>
                  <CardTitle>Question Types</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(analytics.typeDistribution).map(([type, count]) => (
                      <div key={type} className="flex items-center justify-between">
                        <span className="text-sm">{getQuestionTypeLabel(type)}</span>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <BarChart3 className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                <div className="text-muted-foreground">No analytics data available</div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default QuestionBankDetails
