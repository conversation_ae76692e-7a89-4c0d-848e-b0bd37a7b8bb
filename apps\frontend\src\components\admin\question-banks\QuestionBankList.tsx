'use client'

import React from 'react'
import { useQuestionBankStore } from '@/stores/admin/question-banks'
import { QuestionBank } from '@/lib/api/question-banks'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  MoreHorizontal,
  Eye,
  Edit,
  Copy,
  Trash2,
  Share,
  BarChart3,
  BookOpen,
  Users,
  Calendar,
  ArrowUpDown
} from 'lucide-react'

interface QuestionBankListProps {
  questionBanks: QuestionBank[]
  loading: boolean
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalDocs: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
  onView: (id: string) => void
  onEdit: (id: string) => void
  getActions: (questionBank: QuestionBank) => Array<{
    label: string
    icon: any
    onClick: () => void
    destructive?: boolean
  }>
}

export function QuestionBankList({
  questionBanks,
  loading,
  pagination,
  onView,
  onEdit,
  getActions
}: QuestionBankListProps) {
  const { setPagination, setFilters, filters } = useQuestionBankStore()

  const handleSort = (sortBy: string) => {
    const newSortOrder = filters.sortBy === sortBy && filters.sortOrder === 'desc' ? 'asc' : 'desc'
    setFilters({ sortBy, sortOrder: newSortOrder })
  }

  const handlePageChange = (page: number) => {
    setPagination({ page })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getDifficultyDistribution = (questionBank: QuestionBank) => {
    const dist = questionBank.difficulty_distribution
    if (!dist) return null

    const total = (dist.easy || 0) + (dist.medium || 0) + (dist.hard || 0)
    if (total === 0) return null

    return (
      <div className="flex space-x-1">
        {dist.easy && dist.easy > 0 && (
          <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
            E: {dist.easy}
          </Badge>
        )}
        {dist.medium && dist.medium > 0 && (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs">
            M: {dist.medium}
          </Badge>
        )}
        {dist.hard && dist.hard > 0 && (
          <Badge variant="secondary" className="bg-red-100 text-red-800 text-xs">
            H: {dist.hard}
          </Badge>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Question Banks</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Question Banks</CardTitle>
        <CardDescription>
          Manage your question repositories and assessment content
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('title')}
                    className="h-auto p-0 font-medium"
                  >
                    Title
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Questions</TableHead>
                <TableHead>Difficulty</TableHead>
                <TableHead>Sharing</TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('createdAt')}
                    className="h-auto p-0 font-medium"
                  >
                    Created
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead className="w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {questionBanks.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex flex-col items-center space-y-2">
                      <BookOpen className="h-8 w-8 text-muted-foreground" />
                      <div className="text-muted-foreground">No question banks found</div>
                      <div className="text-sm text-muted-foreground">
                        Create your first question bank to get started
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                questionBanks.map((questionBank) => (
                  <TableRow key={questionBank.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{questionBank.title}</div>
                        {questionBank.description && (
                          <div className="text-sm text-muted-foreground line-clamp-2">
                            {questionBank.description}
                          </div>
                        )}
                        {questionBank.tags && questionBank.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {questionBank.tags.slice(0, 3).map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag.tag}
                              </Badge>
                            ))}
                            {questionBank.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{questionBank.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {questionBank.category ? (
                        <Badge variant="secondary">{questionBank.category}</Badge>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{questionBank.question_count || 0}</span>
                        <span className="text-sm text-muted-foreground">questions</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getDifficultyDistribution(questionBank) || (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {questionBank.is_shared ? (
                          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                            <Users className="h-3 w-3 mr-1" />
                            Shared
                          </Badge>
                        ) : (
                          <Badge variant="outline">Private</Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{formatDate(questionBank.createdAt)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          {getActions(questionBank).map((action, index) => (
                            <React.Fragment key={action.label}>
                              {index > 0 && action.destructive && <DropdownMenuSeparator />}
                              <DropdownMenuItem
                                onClick={action.onClick}
                                className={action.destructive ? 'text-red-600' : ''}
                              >
                                <action.icon className="mr-2 h-4 w-4" />
                                {action.label}
                              </DropdownMenuItem>
                            </React.Fragment>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.totalDocs)} of{' '}
              {pagination.totalDocs} question banks
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={!pagination.hasPrevPage}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.hasNextPage}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default QuestionBankList
