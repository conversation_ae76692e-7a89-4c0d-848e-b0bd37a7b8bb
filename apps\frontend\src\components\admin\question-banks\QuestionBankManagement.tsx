'use client'

import React, { useEffect, useState } from 'react'
import { useQuestionBankStore } from '@/stores/admin/question-banks'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  MoreHorizontal,
  Eye,
  Edit,
  Copy,
  Trash2,
  Share,
  Bar<PERSON>hart3,
  FileText,
  BookOpen,
  Users
} from 'lucide-react'
import { QuestionBankList } from './QuestionBankList'
import { QuestionBankForm } from './QuestionBankForm'
import { QuestionBankDetails } from './QuestionBankDetails'
import { ImportDialog } from './ImportDialog'
import { ExportDialog } from './ExportDialog'
import { AnalyticsDialog } from './AnalyticsDialog'

export function QuestionBankManagement() {
  const {
    questionBanks,
    loading,
    pagination,
    filters,
    fetchQuestionBanks,
    setFilters,
    resetFilters,
    deleteQuestionBank,
    duplicateQuestionBank,
    shareQuestionBank
  } = useQuestionBankStore()

  const [searchInput, setSearchInput] = useState(filters.search)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showAnalyticsDialog, setShowAnalyticsDialog] = useState(false)
  const [selectedQuestionBank, setSelectedQuestionBank] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'list' | 'details'>('list')

  useEffect(() => {
    fetchQuestionBanks()
  }, [fetchQuestionBanks])

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilters({ search: searchInput })
    }, 500)

    return () => clearTimeout(timer)
  }, [searchInput, setFilters])

  const handleCreateNew = () => {
    setShowCreateForm(true)
    setSelectedQuestionBank(null)
  }

  const handleEdit = (questionBankId: string) => {
    setSelectedQuestionBank(questionBankId)
    setShowCreateForm(true)
  }

  const handleView = (questionBankId: string) => {
    setSelectedQuestionBank(questionBankId)
    setViewMode('details')
  }

  const handleDuplicate = async (questionBankId: string) => {
    const questionBank = questionBanks.find(qb => qb.id === questionBankId)
    if (questionBank) {
      await duplicateQuestionBank(questionBankId, `${questionBank.title} (Copy)`)
    }
  }

  const handleDelete = async (questionBankId: string) => {
    if (confirm('Are you sure you want to delete this question bank? This action cannot be undone.')) {
      await deleteQuestionBank(questionBankId)
    }
  }

  const handleShare = async (questionBankId: string, isShared: boolean) => {
    await shareQuestionBank(questionBankId, !isShared)
  }

  const handleAnalytics = (questionBankId: string) => {
    setSelectedQuestionBank(questionBankId)
    setShowAnalyticsDialog(true)
  }

  const handleCategoryFilter = (category: string) => {
    setFilters({ category: category === 'all' ? '' : category })
  }

  const handleSharedFilter = (shared: string) => {
    setFilters({ 
      shared: shared === 'all' ? undefined : shared === 'shared' ? true : false 
    })
  }

  const getQuestionBankActions = (questionBank: any) => [
    {
      label: 'View Details',
      icon: Eye,
      onClick: () => handleView(questionBank.id)
    },
    {
      label: 'Edit',
      icon: Edit,
      onClick: () => handleEdit(questionBank.id)
    },
    {
      label: 'Duplicate',
      icon: Copy,
      onClick: () => handleDuplicate(questionBank.id)
    },
    {
      label: 'Analytics',
      icon: BarChart3,
      onClick: () => handleAnalytics(questionBank.id)
    },
    {
      label: questionBank.is_shared ? 'Unshare' : 'Share',
      icon: Share,
      onClick: () => handleShare(questionBank.id, questionBank.is_shared)
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: () => handleDelete(questionBank.id),
      destructive: true
    }
  ]

  if (viewMode === 'details' && selectedQuestionBank) {
    return (
      <QuestionBankDetails
        questionBankId={selectedQuestionBank}
        onBack={() => {
          setViewMode('list')
          setSelectedQuestionBank(null)
        }}
        onEdit={() => handleEdit(selectedQuestionBank)}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Question Banks</h1>
          <p className="text-muted-foreground">
            Create and manage question repositories for assessments
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowImportDialog(true)}
          >
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          
          <Button
            variant="outline"
            onClick={() => setShowExportDialog(true)}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          
          <Button onClick={handleCreateNew}>
            <Plus className="h-4 w-4 mr-2" />
            Create Question Bank
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Question Banks</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pagination.totalDocs}</div>
            <p className="text-xs text-muted-foreground">
              {questionBanks.filter(qb => qb.is_shared).length} shared
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Questions</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {questionBanks.reduce((sum, qb) => sum + (qb.question_count || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all question banks
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Filter className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(questionBanks.map(qb => qb.category).filter(Boolean)).size}
            </div>
            <p className="text-xs text-muted-foreground">
              Unique categories
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shared Banks</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {questionBanks.filter(qb => qb.is_shared).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Available to others
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search question banks..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={filters.category || 'all'} onValueChange={handleCategoryFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {Array.from(new Set(questionBanks.map(qb => qb.category).filter(Boolean))).map((category) => (
                  <SelectItem key={category} value={category!}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select 
              value={filters.shared === undefined ? 'all' : filters.shared ? 'shared' : 'private'} 
              onValueChange={handleSharedFilter}
            >
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Sharing" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Banks</SelectItem>
                <SelectItem value="shared">Shared</SelectItem>
                <SelectItem value="private">Private</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={resetFilters}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Question Bank List */}
      <QuestionBankList
        questionBanks={questionBanks}
        loading={loading}
        pagination={pagination}
        onView={handleView}
        onEdit={handleEdit}
        getActions={getQuestionBankActions}
      />

      {/* Dialogs */}
      <QuestionBankForm
        open={showCreateForm}
        onOpenChange={setShowCreateForm}
        questionBankId={selectedQuestionBank}
        onSuccess={() => {
          setShowCreateForm(false)
          setSelectedQuestionBank(null)
        }}
      />

      <ImportDialog
        open={showImportDialog}
        onOpenChange={setShowImportDialog}
      />

      <ExportDialog
        open={showExportDialog}
        onOpenChange={setShowExportDialog}
        questionBanks={questionBanks}
      />

      <AnalyticsDialog
        open={showAnalyticsDialog}
        onOpenChange={setShowAnalyticsDialog}
        questionBankId={selectedQuestionBank}
      />
    </div>
  )
}

export default QuestionBankManagement
