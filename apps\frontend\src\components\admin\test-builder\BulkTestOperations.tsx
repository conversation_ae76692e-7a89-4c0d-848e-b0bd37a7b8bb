'use client'

import React, { useState } from 'react'
import { useTestStore } from '@/stores/admin/tests'
import { BulkTestConfig, testAPI } from '@/lib/api/tests'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Plus,
  Copy,
  Download,
  Upload,
  Trash2,
  Edit,
  FileText,
  Users,
  Calendar,
  Settings,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { toast } from 'sonner'

interface BulkTestOperationsProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedTests?: string[]
  onSuccess?: () => void
}

export function BulkTestOperations({
  open,
  onOpenChange,
  selectedTests = [],
  onSuccess
}: BulkTestOperationsProps) {
  const {
    templates,
    saving,
    bulkCreateFromTemplate,
    bulkDuplicate,
    bulkUpdate,
    bulkDelete,
    bulkExport,
    bulkImport,
    fetchTemplates
  } = useTestStore()

  const [activeTab, setActiveTab] = useState('create')
  const [selectedTemplate, setSelectedTemplate] = useState('')
  const [testConfigs, setTestConfigs] = useState<BulkTestConfig[]>([])
  const [importData, setImportData] = useState('')
  const [exportOptions, setExportOptions] = useState({
    format: 'json' as 'json' | 'csv',
    includeQuestions: true,
    includeAnalytics: false
  })

  React.useEffect(() => {
    if (open) {
      fetchTemplates()
    }
  }, [open, fetchTemplates])

  const handleAddTestConfig = () => {
    setTestConfigs([...testConfigs, {
      title: '',
      description: '',
      lesson: '',
      branch_id: ''
    }])
  }

  const handleUpdateTestConfig = (index: number, field: keyof BulkTestConfig, value: string) => {
    const updated = [...testConfigs]
    updated[index] = { ...updated[index], [field]: value }
    setTestConfigs(updated)
  }

  const handleRemoveTestConfig = (index: number) => {
    setTestConfigs(testConfigs.filter((_, i) => i !== index))
  }

  const handleCreateFromTemplate = async () => {
    if (!selectedTemplate || testConfigs.length === 0) {
      toast.error('Please select a template and add test configurations')
      return
    }

    try {
      const result = await bulkCreateFromTemplate(selectedTemplate, testConfigs)
      toast.success(`Created ${result.total_created} tests successfully`)
      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      toast.error('Failed to create tests from template')
    }
  }

  const handleDuplicate = async () => {
    if (selectedTests.length === 0) {
      toast.error('Please select tests to duplicate')
      return
    }

    try {
      const result = await bulkDuplicate(selectedTests, '{original_name} (Copy)')
      toast.success(`Duplicated ${result.total_duplicated} tests successfully`)
      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      toast.error('Failed to duplicate tests')
    }
  }

  const handleBulkUpdate = async (updates: any) => {
    if (selectedTests.length === 0) {
      toast.error('Please select tests to update')
      return
    }

    try {
      const result = await bulkUpdate(selectedTests, updates)
      toast.success(`Updated ${result.total_updated} tests successfully`)
      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      toast.error('Failed to update tests')
    }
  }

  const handleDelete = async () => {
    if (selectedTests.length === 0) {
      toast.error('Please select tests to delete')
      return
    }

    if (!confirm(`Are you sure you want to delete ${selectedTests.length} tests? This action cannot be undone.`)) {
      return
    }

    try {
      const result = await bulkDelete(selectedTests)
      toast.success(`Deleted ${result.total_deleted} tests successfully`)
      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      toast.error('Failed to delete tests')
    }
  }

  const handleExport = async () => {
    if (selectedTests.length === 0) {
      toast.error('Please select tests to export')
      return
    }

    try {
      await bulkExport(selectedTests, exportOptions)
      toast.success(`Exported ${selectedTests.length} tests successfully`)
    } catch (error) {
      toast.error('Failed to export tests')
    }
  }

  const handleImport = async () => {
    if (!importData.trim()) {
      toast.error('Please provide import data')
      return
    }

    try {
      const tests = JSON.parse(importData)
      const validation = testAPI.validateBulkData(tests, 'import')
      
      if (!validation.isValid) {
        toast.error(`Validation failed: ${validation.errors.join(', ')}`)
        return
      }

      const result = await bulkImport(tests, {
        overwrite_existing: false,
        create_missing_questions: true
      })
      
      toast.success(`Imported ${result.total_imported} tests successfully`)
      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      toast.error('Failed to import tests. Please check the data format.')
    }
  }

  const renderCreateFromTemplate = () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Select Template</Label>
        <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
          <SelectTrigger>
            <SelectValue placeholder="Choose a template" />
          </SelectTrigger>
          <SelectContent>
            {templates.map((template) => (
              <SelectItem key={template.id} value={template.id}>
                {template.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>Test Configurations</Label>
          <Button onClick={handleAddTestConfig} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Test
          </Button>
        </div>

        {testConfigs.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-8 w-8 mx-auto mb-2" />
            <div>No test configurations added</div>
            <div className="text-sm">Click "Add Test" to create test configurations</div>
          </div>
        ) : (
          <div className="space-y-4 max-h-60 overflow-y-auto">
            {testConfigs.map((config, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Title *</Label>
                      <Input
                        placeholder="Test title"
                        value={config.title}
                        onChange={(e) => handleUpdateTestConfig(index, 'title', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Lesson</Label>
                      <Input
                        placeholder="Lesson ID"
                        value={config.lesson || ''}
                        onChange={(e) => handleUpdateTestConfig(index, 'lesson', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2 md:col-span-2">
                      <Label>Description</Label>
                      <Textarea
                        placeholder="Test description"
                        value={config.description || ''}
                        onChange={(e) => handleUpdateTestConfig(index, 'description', e.target.value)}
                        rows={2}
                      />
                    </div>
                  </div>
                  <div className="flex justify-end mt-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveTestConfig(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={() => onOpenChange(false)}>
          Cancel
        </Button>
        <Button 
          onClick={handleCreateFromTemplate} 
          disabled={saving || !selectedTemplate || testConfigs.length === 0}
        >
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Creating...
            </>
          ) : (
            <>
              <Plus className="h-4 w-4 mr-2" />
              Create {testConfigs.length} Tests
            </>
          )}
        </Button>
      </div>
    </div>
  )

  const renderBulkActions = () => (
    <div className="space-y-6">
      <div className="text-sm text-muted-foreground">
        {selectedTests.length} test(s) selected for bulk operations
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Copy className="h-4 w-4" />
              <span>Duplicate Tests</span>
            </CardTitle>
            <CardDescription>
              Create copies of selected tests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleDuplicate} disabled={saving || selectedTests.length === 0}>
              {saving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Copy className="h-4 w-4 mr-2" />}
              Duplicate {selectedTests.length} Tests
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Trash2 className="h-4 w-4" />
              <span>Delete Tests</span>
            </CardTitle>
            <CardDescription>
              Permanently delete selected tests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              variant="destructive" 
              onClick={handleDelete} 
              disabled={saving || selectedTests.length === 0}
            >
              {saving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Trash2 className="h-4 w-4 mr-2" />}
              Delete {selectedTests.length} Tests
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Edit className="h-4 w-4" />
            <span>Bulk Update</span>
          </CardTitle>
          <CardDescription>
            Update common properties for selected tests
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Button 
              onClick={() => handleBulkUpdate({ is_published: true })}
              disabled={saving || selectedTests.length === 0}
            >
              Publish All
            </Button>
            <Button 
              onClick={() => handleBulkUpdate({ is_published: false })}
              disabled={saving || selectedTests.length === 0}
            >
              Unpublish All
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bulk Test Operations</DialogTitle>
          <DialogDescription>
            Perform operations on multiple tests efficiently
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="create">Create</TabsTrigger>
            <TabsTrigger value="actions">Actions</TabsTrigger>
            <TabsTrigger value="export">Export</TabsTrigger>
            <TabsTrigger value="import">Import</TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-6">
            {renderCreateFromTemplate()}
          </TabsContent>

          <TabsContent value="actions" className="space-y-6">
            {renderBulkActions()}
          </TabsContent>

          <TabsContent value="export" className="space-y-6">
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Export {selectedTests.length} selected test(s)
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Format</Label>
                  <Select 
                    value={exportOptions.format} 
                    onValueChange={(value: 'json' | 'csv') => 
                      setExportOptions({...exportOptions, format: value})
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="json">JSON</SelectItem>
                      <SelectItem value="csv">CSV</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Options</Label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={exportOptions.includeQuestions}
                        onChange={(e) => setExportOptions({
                          ...exportOptions, 
                          includeQuestions: e.target.checked
                        })}
                      />
                      <span className="text-sm">Include Questions</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={exportOptions.includeAnalytics}
                        onChange={(e) => setExportOptions({
                          ...exportOptions, 
                          includeAnalytics: e.target.checked
                        })}
                      />
                      <span className="text-sm">Include Analytics</span>
                    </label>
                  </div>
                </div>
              </div>

              <Button 
                onClick={handleExport} 
                disabled={saving || selectedTests.length === 0}
                className="w-full"
              >
                {saving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Export {selectedTests.length} Tests
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="import" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Import Data (JSON)</Label>
                <Textarea
                  placeholder="Paste JSON data here..."
                  value={importData}
                  onChange={(e) => setImportData(e.target.value)}
                  rows={10}
                />
              </div>

              <Button 
                onClick={handleImport} 
                disabled={saving || !importData.trim()}
                className="w-full"
              >
                {saving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Importing...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Import Tests
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

export default BulkTestOperations
