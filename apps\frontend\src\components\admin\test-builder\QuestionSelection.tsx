'use client'

import React, { useState, useEffect } from 'react'
import { useTestStore } from '@/stores/admin/tests'
import { useQuestionBankStore } from '@/stores/admin/question-banks'
import { TestQuestion } from '@/lib/api/tests'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Plus,
  Search,
  Filter,
  Shuffle,
  GripVertical,
  Trash2,
  Eye,
  Target,
  Clock,
  Book<PERSON><PERSON>,
  ArrowUpDown
} from 'lucide-react'
import { Drag<PERSON><PERSON><PERSON>ontext, Droppable, Draggable } from '@hello-pangea/dnd'

interface QuestionSelectionProps {
  testId?: string
  questions: TestQuestion[]
  onQuestionsChange: () => void
}

export function QuestionSelection({ 
  testId, 
  questions, 
  onQuestionsChange 
}: QuestionSelectionProps) {
  const {
    addQuestionsToTest,
    removeQuestionFromTest,
    reorderTestQuestions,
    randomizeTest
  } = useTestStore()

  const {
    questionBanks,
    questions: bankQuestions,
    fetchQuestionBanks,
    fetchQuestions
  } = useQuestionBankStore()

  const [selectedQuestionBank, setSelectedQuestionBank] = useState<string>('')
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [difficultyFilter, setDifficultyFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')

  useEffect(() => {
    fetchQuestionBanks()
  }, [fetchQuestionBanks])

  useEffect(() => {
    if (selectedQuestionBank) {
      fetchQuestions(selectedQuestionBank)
    }
  }, [selectedQuestionBank, fetchQuestions])

  const handleAddQuestions = async () => {
    if (!testId || selectedQuestions.length === 0) return

    try {
      await addQuestionsToTest(testId, selectedQuestions)
      setSelectedQuestions([])
      onQuestionsChange()
    } catch (error) {
      console.error('Failed to add questions:', error)
    }
  }

  const handleRemoveQuestion = async (questionId: string) => {
    if (!testId) return

    try {
      await removeQuestionFromTest(testId, questionId)
      onQuestionsChange()
    } catch (error) {
      console.error('Failed to remove question:', error)
    }
  }

  const handleReorderQuestions = async (result: any) => {
    if (!result.destination || !testId) return

    const items = Array.from(questions)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    const questionOrders = items.map((question, index) => ({
      questionId: question.id,
      order: index + 1
    }))

    try {
      await reorderTestQuestions(testId, questionOrders)
      onQuestionsChange()
    } catch (error) {
      console.error('Failed to reorder questions:', error)
    }
  }

  const handleRandomizeQuestions = async () => {
    if (!testId) return

    try {
      await randomizeTest(testId, {
        randomizeQuestions: true,
        randomizeAnswers: true,
        preserveDifficulty: true
      })
      onQuestionsChange()
    } catch (error) {
      console.error('Failed to randomize questions:', error)
    }
  }

  const filteredBankQuestions = bankQuestions.filter(question => {
    const matchesSearch = question.content.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDifficulty = difficultyFilter === 'all' || question.difficulty === difficultyFilter
    const matchesType = typeFilter === 'all' || question.type === typeFilter
    const notAlreadyAdded = !questions.find(q => q.id === question.id)
    
    return matchesSearch && matchesDifficulty && matchesType && notAlreadyAdded
  })

  const getQuestionTypeLabel = (type: string) => {
    const typeLabels: Record<string, string> = {
      'multiple_choice_single': 'Multiple Choice (Single)',
      'multiple_choice_multiple': 'Multiple Choice (Multiple)',
      'true_false': 'True/False',
      'fill_blanks': 'Fill in Blanks',
      'essay': 'Essay',
      'matching': 'Matching',
      'ordering': 'Ordering'
    }
    return typeLabels[type] || type
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'hard':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Question Management</span>
          </CardTitle>
          <CardDescription>
            Add questions to your test and organize them
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-sm">
                <span className="font-medium">{questions.length}</span> questions added
              </div>
              <div className="text-sm text-muted-foreground">
                Total points: {questions.reduce((sum, q) => sum + q.points, 0)}
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={handleRandomizeQuestions}
                disabled={questions.length === 0}
              >
                <Shuffle className="h-4 w-4 mr-2" />
                Randomize
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="add" className="space-y-6">
        <TabsList>
          <TabsTrigger value="add">Add Questions</TabsTrigger>
          <TabsTrigger value="manage">Manage Questions ({questions.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="add" className="space-y-6">
          {/* Question Bank Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Select Question Bank</CardTitle>
              <CardDescription>
                Choose a question bank to browse and add questions from
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Select value={selectedQuestionBank} onValueChange={setSelectedQuestionBank}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a question bank" />
                </SelectTrigger>
                <SelectContent>
                  {questionBanks.map((bank) => (
                    <SelectItem key={bank.id} value={bank.id}>
                      {bank.title} ({bank.question_count || 0} questions)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedQuestionBank && (
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search questions..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  
                  <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Difficulty</SelectItem>
                      <SelectItem value="easy">Easy</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="hard">Hard</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Question Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="multiple_choice_single">Multiple Choice (Single)</SelectItem>
                      <SelectItem value="multiple_choice_multiple">Multiple Choice (Multiple)</SelectItem>
                      <SelectItem value="true_false">True/False</SelectItem>
                      <SelectItem value="fill_blanks">Fill in Blanks</SelectItem>
                      <SelectItem value="essay">Essay</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Available Questions */}
          {selectedQuestionBank && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Available Questions</CardTitle>
                    <CardDescription>
                      Select questions to add to your test
                    </CardDescription>
                  </div>
                  
                  {selectedQuestions.length > 0 && (
                    <Button onClick={handleAddQuestions}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Selected ({selectedQuestions.length})
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {filteredBankQuestions.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <BookOpen className="h-8 w-8 mx-auto mb-4" />
                    <div>No questions found</div>
                    <div className="text-sm">Try adjusting your filters or search term</div>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50px]">
                            <Checkbox
                              checked={selectedQuestions.length === filteredBankQuestions.length}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedQuestions(filteredBankQuestions.map(q => q.id))
                                } else {
                                  setSelectedQuestions([])
                                }
                              }}
                            />
                          </TableHead>
                          <TableHead>Question</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Difficulty</TableHead>
                          <TableHead>Points</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredBankQuestions.map((question) => (
                          <TableRow key={question.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedQuestions.includes(question.id)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setSelectedQuestions([...selectedQuestions, question.id])
                                  } else {
                                    setSelectedQuestions(selectedQuestions.filter(id => id !== question.id))
                                  }
                                }}
                              />
                            </TableCell>
                            <TableCell>
                              <div className="max-w-md">
                                <div className="font-medium line-clamp-2">{question.content}</div>
                                {question.time_limit && (
                                  <div className="flex items-center text-sm text-muted-foreground mt-1">
                                    <Clock className="h-3 w-3 mr-1" />
                                    {question.time_limit} min
                                  </div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {getQuestionTypeLabel(question.type)}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge className={getDifficultyColor(question.difficulty)}>
                                {question.difficulty}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant="secondary">{question.points} pts</Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="manage" className="space-y-6">
          {questions.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <Target className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                <div className="text-muted-foreground mb-2">No questions added yet</div>
                <div className="text-sm text-muted-foreground">
                  Switch to the "Add Questions" tab to start building your test
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Test Questions</CardTitle>
                <CardDescription>
                  Drag and drop to reorder questions, or remove unwanted ones
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DragDropContext onDragEnd={handleReorderQuestions}>
                  <Droppable droppableId="questions">
                    {(provided) => (
                      <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-2">
                        {questions.map((question, index) => (
                          <Draggable key={question.id} draggableId={question.id} index={index}>
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className={`p-4 border rounded-lg ${
                                  snapshot.isDragging ? 'shadow-lg' : ''
                                }`}
                              >
                                <div className="flex items-start space-x-4">
                                  <div {...provided.dragHandleProps} className="mt-1">
                                    <GripVertical className="h-4 w-4 text-muted-foreground" />
                                  </div>
                                  
                                  <div className="flex-1">
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <div className="font-medium">Question {index + 1}</div>
                                        <div className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                          {question.content}
                                        </div>
                                      </div>
                                      
                                      <div className="flex items-center space-x-2 ml-4">
                                        <Badge variant="outline">
                                          {getQuestionTypeLabel(question.type)}
                                        </Badge>
                                        <Badge className={getDifficultyColor(question.difficulty)}>
                                          {question.difficulty}
                                        </Badge>
                                        <Badge variant="secondary">{question.points} pts</Badge>
                                        
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleRemoveQuestion(question.id)}
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default QuestionSelection
