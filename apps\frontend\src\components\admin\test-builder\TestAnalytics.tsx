'use client'

import React, { useEffect, useState } from 'react'
import { useTestStore } from '@/stores/admin/tests'
import { TestPreview, testAPI } from '@/lib/api/tests'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  BarChart3,
  TrendingUp,
  Clock,
  Target,
  AlertTriangle,
  CheckCircle,
  Info,
  PieChart,
  Activity,
  RefreshCw,
  Download,
  Users,
  FileText,
  Loader2
} from 'lucide-react'

interface TestAnalyticsProps {
  testId?: string
  preview: TestPreview | null
}

export function TestAnalytics({ testId, preview }: TestAnalyticsProps) {
  const {
    analytics,
    performanceAnalytics,
    questionAnalytics,
    analyticsLoading,
    fetchAnalytics,
    fetchPerformanceAnalytics,
    fetchQuestionAnalytics,
    clearAnalytics
  } = useTestStore()

  const [timeframe, setTimeframe] = useState('30d')
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (testId) {
      fetchAnalytics(testId)
      fetchPerformanceAnalytics(testId, timeframe)
      fetchQuestionAnalytics(testId)
    }

    return () => {
      clearAnalytics()
    }
  }, [testId, timeframe, fetchAnalytics, fetchPerformanceAnalytics, fetchQuestionAnalytics, clearAnalytics])

  const handleRefresh = () => {
    if (testId) {
      fetchAnalytics(testId)
      fetchPerformanceAnalytics(testId, timeframe)
      fetchQuestionAnalytics(testId)
    }
  }

  const handleExportAnalytics = () => {
    if (analytics && performanceAnalytics && questionAnalytics) {
      const data = {
        test_id: testId,
        generated_at: new Date().toISOString(),
        timeframe,
        analytics,
        performance: performanceAnalytics,
        questions: questionAnalytics
      }

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `test-analytics-${testId}-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  if (!testId) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <BarChart3 className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
          <div className="text-muted-foreground mb-2">No test selected</div>
          <div className="text-sm text-muted-foreground">
            Save the test first to view analytics and insights
          </div>
        </CardContent>
      </Card>
    )
  }

  if (analyticsLoading && !analytics) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <Loader2 className="h-8 w-8 mx-auto mb-4 text-muted-foreground animate-spin" />
          <div className="text-muted-foreground mb-2">Loading analytics...</div>
          <div className="text-sm text-muted-foreground">
            Analyzing test performance and generating insights
          </div>
        </CardContent>
      </Card>
    )
  }

  const { test, questions, statistics } = preview

  const getTestComplexity = () => {
    const factors = {
      questionCount: statistics.totalQuestions,
      timePerQuestion: test.time_limit ? test.time_limit / statistics.totalQuestions : 0,
      difficultySpread: Object.keys(statistics.difficultyDistribution).length,
      typeVariety: Object.keys(statistics.questionTypes).length
    }

    let complexity = 0
    
    // Question count factor (0-3 points)
    if (factors.questionCount > 50) complexity += 3
    else if (factors.questionCount > 25) complexity += 2
    else if (factors.questionCount > 10) complexity += 1

    // Time pressure factor (0-2 points)
    if (factors.timePerQuestion < 1) complexity += 2
    else if (factors.timePerQuestion < 2) complexity += 1

    // Difficulty spread factor (0-2 points)
    if (factors.difficultySpread === 3) complexity += 2
    else if (factors.difficultySpread === 2) complexity += 1

    // Type variety factor (0-2 points)
    if (factors.typeVariety > 3) complexity += 2
    else if (factors.typeVariety > 1) complexity += 1

    // Normalize to 0-10 scale
    const normalizedComplexity = Math.min(10, Math.round((complexity / 9) * 10))

    if (normalizedComplexity <= 3) return { level: 'Low', color: 'bg-green-100 text-green-800', score: normalizedComplexity }
    if (normalizedComplexity <= 6) return { level: 'Medium', color: 'bg-yellow-100 text-yellow-800', score: normalizedComplexity }
    return { level: 'High', color: 'bg-red-100 text-red-800', score: normalizedComplexity }
  }

  const getRecommendations = () => {
    const recommendations = []

    // Question count recommendations
    if (statistics.totalQuestions < 5) {
      recommendations.push({
        type: 'warning',
        title: 'Low Question Count',
        message: 'Consider adding more questions for better assessment reliability'
      })
    } else if (statistics.totalQuestions > 100) {
      recommendations.push({
        type: 'info',
        title: 'High Question Count',
        message: 'Ensure adequate time allocation for this comprehensive test'
      })
    }

    // Time allocation recommendations
    const timePerQuestion = test.time_limit ? test.time_limit / statistics.totalQuestions : 0
    if (timePerQuestion < 0.5) {
      recommendations.push({
        type: 'warning',
        title: 'Time Pressure',
        message: 'Very limited time per question may cause student stress'
      })
    } else if (timePerQuestion > 5) {
      recommendations.push({
        type: 'info',
        title: 'Generous Time Allocation',
        message: 'Students have ample time per question'
      })
    }

    // Difficulty distribution recommendations
    const { easy, medium, hard } = statistics.difficultyDistribution
    const total = easy + medium + hard
    if (total > 0) {
      const easyPercent = (easy / total) * 100
      const hardPercent = (hard / total) * 100

      if (easyPercent > 70) {
        recommendations.push({
          type: 'info',
          title: 'Easy-Heavy Test',
          message: 'Consider adding more challenging questions for better discrimination'
        })
      } else if (hardPercent > 50) {
        recommendations.push({
          type: 'warning',
          title: 'Challenging Test',
          message: 'High difficulty may result in low pass rates'
        })
      } else if (easyPercent > 20 && hardPercent > 20) {
        recommendations.push({
          type: 'success',
          title: 'Balanced Difficulty',
          message: 'Good mix of difficulty levels for comprehensive assessment'
        })
      }
    }

    // Question type recommendations
    const typeCount = Object.keys(statistics.questionTypes).length
    if (typeCount === 1) {
      recommendations.push({
        type: 'info',
        title: 'Single Question Type',
        message: 'Consider adding variety with different question types'
      })
    } else if (typeCount > 4) {
      recommendations.push({
        type: 'success',
        title: 'Diverse Question Types',
        message: 'Good variety of question types for comprehensive assessment'
      })
    }

    // Security recommendations
    if (test.security_settings?.require_webcam || test.security_settings?.prevent_tab_switching) {
      recommendations.push({
        type: 'info',
        title: 'Security Measures Active',
        message: 'Test integrity measures are in place'
      })
    }

    return recommendations
  }

  const complexity = getTestComplexity()
  const recommendations = getRecommendations()

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  return (
    <div className="space-y-6">
      {/* Test Complexity Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Test Complexity Analysis</span>
          </CardTitle>
          <CardDescription>
            Overall assessment of test difficulty and complexity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Complexity Level</span>
                <Badge className={complexity.color}>
                  {complexity.level} ({complexity.score}/10)
                </Badge>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Question Count Impact</span>
                  <span>{statistics.totalQuestions > 25 ? 'High' : statistics.totalQuestions > 10 ? 'Medium' : 'Low'}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Time Pressure</span>
                  <span>
                    {test.time_limit && statistics.totalQuestions > 0
                      ? `${(test.time_limit / statistics.totalQuestions).toFixed(1)} min/question`
                      : 'Not set'
                    }
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Difficulty Spread</span>
                  <span>{Object.keys(statistics.difficultyDistribution).length} levels</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Question Variety</span>
                  <span>{Object.keys(statistics.questionTypes).length} types</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="text-sm font-medium">Complexity Factors</div>
              <div className="space-y-3">
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm">Question Volume</span>
                    <span className="text-sm">{Math.min(100, (statistics.totalQuestions / 50) * 100).toFixed(0)}%</span>
                  </div>
                  <Progress value={Math.min(100, (statistics.totalQuestions / 50) * 100)} className="h-2" />
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm">Time Constraint</span>
                    <span className="text-sm">
                      {test.time_limit && statistics.totalQuestions > 0
                        ? `${Math.min(100, ((2 - (test.time_limit / statistics.totalQuestions)) / 2) * 100).toFixed(0)}%`
                        : '0%'
                      }
                    </span>
                  </div>
                  <Progress 
                    value={
                      test.time_limit && statistics.totalQuestions > 0
                        ? Math.min(100, ((2 - (test.time_limit / statistics.totalQuestions)) / 2) * 100)
                        : 0
                    } 
                    className="h-2" 
                  />
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm">Content Diversity</span>
                    <span className="text-sm">{((Object.keys(statistics.questionTypes).length / 7) * 100).toFixed(0)}%</span>
                  </div>
                  <Progress value={(Object.keys(statistics.questionTypes).length / 7) * 100} className="h-2" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Predictions */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Performance Predictions</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Expected Pass Rate</span>
                <Badge variant="outline">
                  {complexity.level === 'Low' ? '85-95%' : 
                   complexity.level === 'Medium' ? '70-85%' : '50-70%'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Average Completion Time</span>
                <Badge variant="outline">
                  {test.time_limit ? `${Math.round(test.time_limit * 0.8)}-${test.time_limit} min` : 'Not set'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Student Stress Level</span>
                <Badge variant="outline">
                  {complexity.level === 'Low' ? 'Low' : 
                   complexity.level === 'Medium' ? 'Moderate' : 'High'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PieChart className="h-5 w-5" />
              <span>Score Distribution Forecast</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {complexity.level === 'Low' && (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm">90-100%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '40%' }}></div>
                    </div>
                    <span className="text-sm">40%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">80-89%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '35%' }}></div>
                    </div>
                    <span className="text-sm">35%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">70-79%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '20%' }}></div>
                    </div>
                    <span className="text-sm">20%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Below 70%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-red-500 h-2 rounded-full" style={{ width: '5%' }}></div>
                    </div>
                    <span className="text-sm">5%</span>
                  </div>
                </div>
              </>
            )}
            
            {complexity.level === 'Medium' && (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm">90-100%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '25%' }}></div>
                    </div>
                    <span className="text-sm">25%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">80-89%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '40%' }}></div>
                    </div>
                    <span className="text-sm">40%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">70-79%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '25%' }}></div>
                    </div>
                    <span className="text-sm">25%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Below 70%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-red-500 h-2 rounded-full" style={{ width: '10%' }}></div>
                    </div>
                    <span className="text-sm">10%</span>
                  </div>
                </div>
              </>
            )}
            
            {complexity.level === 'High' && (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm">90-100%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '15%' }}></div>
                    </div>
                    <span className="text-sm">15%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">80-89%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '25%' }}></div>
                    </div>
                    <span className="text-sm">25%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">70-79%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '35%' }}></div>
                    </div>
                    <span className="text-sm">35%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Below 70%</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-red-500 h-2 rounded-full" style={{ width: '25%' }}></div>
                    </div>
                    <span className="text-sm">25%</span>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Recommendations</span>
          </CardTitle>
          <CardDescription>
            Suggestions to improve your test design
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recommendations.length === 0 ? (
            <div className="text-center py-4">
              <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <div className="text-sm text-muted-foreground">
                Your test configuration looks good! No specific recommendations at this time.
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {recommendations.map((rec, index) => (
                <div 
                  key={index} 
                  className={`p-3 rounded-lg border ${
                    rec.type === 'success' ? 'bg-green-50 border-green-200' :
                    rec.type === 'warning' ? 'bg-amber-50 border-amber-200' :
                    'bg-blue-50 border-blue-200'
                  }`}
                >
                  <div className="flex items-start space-x-2">
                    {rec.type === 'success' ? (
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    ) : rec.type === 'warning' ? (
                      <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5" />
                    ) : (
                      <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                    )}
                    <div>
                      <div className={`font-medium text-sm ${
                        rec.type === 'success' ? 'text-green-800' :
                        rec.type === 'warning' ? 'text-amber-800' :
                        'text-blue-800'
                      }`}>
                        {rec.title}
                      </div>
                      <div className={`text-sm mt-1 ${
                        rec.type === 'success' ? 'text-green-700' :
                        rec.type === 'warning' ? 'text-amber-700' :
                        'text-blue-700'
                      }`}>
                        {rec.message}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default TestAnalytics
