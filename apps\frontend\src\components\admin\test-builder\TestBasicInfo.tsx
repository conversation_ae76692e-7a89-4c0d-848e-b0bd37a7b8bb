'use client'

import React from 'react'
import { Test, testAPI } from '@/lib/api/tests'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Clock, 
  Users, 
  Shield,
  Calendar,
  Info
} from 'lucide-react'
import { useFormik } from 'formik'
import * as Yup from 'yup'

interface TestBasicInfoProps {
  test: Test | null
  onUpdate: (updates: Partial<Test>) => void
}

const validationSchema = Yup.object({
  title: Yup.string()
    .required('Test title is required')
    .min(3, 'Title must be at least 3 characters')
    .max(100, 'Title must be less than 100 characters'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  type: Yup.string()
    .required('Test type is required'),
  lesson: Yup.string()
    .required('Lesson is required'),
  instructions: Yup.string()
    .max(1000, 'Instructions must be less than 1000 characters')
})

export function TestBasicInfo({ test, onUpdate }: TestBasicInfoProps) {
  const formik = useFormik({
    initialValues: {
      title: test?.title || '',
      description: test?.description || '',
      type: test?.type || '',
      lesson: test?.lesson || '',
      instructions: test?.instructions || '',
      is_template: test?.is_template || false
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values) => {
      onUpdate(values)
    }
  })

  const handleFieldChange = (field: string, value: any) => {
    formik.setFieldValue(field, value)
    onUpdate({ [field]: value })
  }

  // Mock data - in real app, these would come from APIs
  const lessons = [
    { id: '1', title: 'Introduction to Mathematics' },
    { id: '2', title: 'Basic Algebra' },
    { id: '3', title: 'Geometry Fundamentals' },
    { id: '4', title: 'Statistics and Probability' },
    { id: '5', title: 'Advanced Calculus' }
  ]

  const testTypes = testAPI.getTestTypes()

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Basic Information</span>
          </CardTitle>
          <CardDescription>
            Set up the fundamental details for your test
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Test Title */}
            <div className="space-y-2">
              <Label htmlFor="title">Test Title *</Label>
              <Input
                id="title"
                name="title"
                placeholder="Enter test title"
                value={formik.values.title}
                onChange={(e) => handleFieldChange('title', e.target.value)}
                onBlur={formik.handleBlur}
                className={formik.touched.title && formik.errors.title ? 'border-red-500' : ''}
              />
              {formik.touched.title && formik.errors.title && (
                <p className="text-sm text-red-500">{formik.errors.title}</p>
              )}
            </div>

            {/* Test Type */}
            <div className="space-y-2">
              <Label htmlFor="type">Test Type *</Label>
              <Select
                value={formik.values.type}
                onValueChange={(value) => handleFieldChange('type', value)}
              >
                <SelectTrigger className={formik.touched.type && formik.errors.type ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select test type" />
                </SelectTrigger>
                <SelectContent>
                  {testTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {formik.touched.type && formik.errors.type && (
                <p className="text-sm text-red-500">{formik.errors.type}</p>
              )}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter a brief description of this test"
              rows={3}
              value={formik.values.description}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              onBlur={formik.handleBlur}
              className={formik.touched.description && formik.errors.description ? 'border-red-500' : ''}
            />
            {formik.touched.description && formik.errors.description && (
              <p className="text-sm text-red-500">{formik.errors.description}</p>
            )}
          </div>

          {/* Lesson Association */}
          <div className="space-y-2">
            <Label htmlFor="lesson">Associated Lesson *</Label>
            <Select
              value={formik.values.lesson}
              onValueChange={(value) => handleFieldChange('lesson', value)}
            >
              <SelectTrigger className={formik.touched.lesson && formik.errors.lesson ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select a lesson" />
              </SelectTrigger>
              <SelectContent>
                {lessons.map((lesson) => (
                  <SelectItem key={lesson.id} value={lesson.id}>
                    {lesson.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {formik.touched.lesson && formik.errors.lesson && (
              <p className="text-sm text-red-500">{formik.errors.lesson}</p>
            )}
          </div>

          {/* Instructions */}
          <div className="space-y-2">
            <Label htmlFor="instructions">Test Instructions</Label>
            <Textarea
              id="instructions"
              name="instructions"
              placeholder="Enter instructions for students taking this test"
              rows={4}
              value={formik.values.instructions}
              onChange={(e) => handleFieldChange('instructions', e.target.value)}
              onBlur={formik.handleBlur}
              className={formik.touched.instructions && formik.errors.instructions ? 'border-red-500' : ''}
            />
            {formik.touched.instructions && formik.errors.instructions && (
              <p className="text-sm text-red-500">{formik.errors.instructions}</p>
            )}
            <p className="text-sm text-muted-foreground">
              These instructions will be shown to students before they start the test
            </p>
          </div>

          {/* Template Option */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_template"
              checked={formik.values.is_template}
              onCheckedChange={(checked) => handleFieldChange('is_template', checked)}
            />
            <Label htmlFor="is_template">Save as template</Label>
            <div className="text-sm text-muted-foreground">
              Templates can be reused to create new tests quickly
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Information Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Test Type</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {formik.values.type ? (
              <Badge variant="secondary">
                {testTypes.find(t => t.value === formik.values.type)?.label || formik.values.type}
              </Badge>
            ) : (
              <span className="text-sm text-muted-foreground">Not selected</span>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Associated Lesson</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {formik.values.lesson ? (
              <div className="text-sm font-medium">
                {lessons.find(l => l.id === formik.values.lesson)?.title || 'Selected'}
              </div>
            ) : (
              <span className="text-sm text-muted-foreground">Not selected</span>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Template Status</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge variant={formik.values.is_template ? 'default' : 'outline'}>
              {formik.values.is_template ? 'Template' : 'Regular Test'}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Help Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="h-5 w-5" />
            <span>Test Type Guidelines</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <div>
                <div className="font-medium text-sm">Practice Test</div>
                <div className="text-sm text-muted-foreground">
                  For student practice and self-assessment. Usually allows multiple attempts.
                </div>
              </div>
              <div>
                <div className="font-medium text-sm">Quiz</div>
                <div className="text-sm text-muted-foreground">
                  Short assessments with limited time and attempts. Good for quick knowledge checks.
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <div className="font-medium text-sm">Graded Test</div>
                <div className="text-sm text-muted-foreground">
                  Formal assessments that contribute to final grades. Usually one attempt allowed.
                </div>
              </div>
              <div>
                <div className="font-medium text-sm">Final Exam</div>
                <div className="text-sm text-muted-foreground">
                  Comprehensive end-of-course assessments with strict security and time limits.
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default TestBasicInfo
