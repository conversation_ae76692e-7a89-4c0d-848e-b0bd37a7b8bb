'use client'

import React, { useEffect, useState } from 'react'
import { useTestStore } from '@/stores/admin/tests'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  Plus,
  Settings,
  Eye,
  Save,
  Publish,
  Copy,
  FileText,
  Clock,
  Target,
  Users,
  BarChart3,
  ArrowLeft,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { TestBasicInfo } from './TestBasicInfo'
import { QuestionSelection } from './QuestionSelection'
import { TestConfiguration } from './TestConfiguration'
import { TestPreview } from './TestPreview'
import { TestAnalytics } from './TestAnalytics'

interface TestBuilderProps {
  testId?: string
  onBack?: () => void
  onSave?: (test: any) => void
}

export function TestBuilder({ testId, onBack, onSave }: TestBuilderProps) {
  const {
    currentTest,
    testQuestions,
    testPreview,
    loading,
    saving,
    publishing,
    fetchTest,
    createTest,
    updateTest,
    publishTest,
    fetchTestPreview,
    setCurrentTest
  } = useTestStore()

  const [activeTab, setActiveTab] = useState('basic')
  const [isNewTest, setIsNewTest] = useState(!testId)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  useEffect(() => {
    if (testId) {
      fetchTest(testId)
      setIsNewTest(false)
    } else {
      setCurrentTest(null)
      setIsNewTest(true)
    }
  }, [testId, fetchTest, setCurrentTest])

  useEffect(() => {
    if (currentTest?.id) {
      fetchTestPreview(currentTest.id)
    }
  }, [currentTest?.id, fetchTestPreview])

  const handleSave = async () => {
    if (!currentTest) return

    try {
      let savedTest
      if (isNewTest) {
        savedTest = await createTest(currentTest)
        if (savedTest) {
          setIsNewTest(false)
          setHasUnsavedChanges(false)
        }
      } else {
        savedTest = await updateTest(currentTest.id, currentTest)
        if (savedTest) {
          setHasUnsavedChanges(false)
        }
      }
      
      if (savedTest && onSave) {
        onSave(savedTest)
      }
    } catch (error) {
      console.error('Failed to save test:', error)
    }
  }

  const handlePublish = async () => {
    if (!currentTest?.id) return

    try {
      const success = await publishTest(currentTest.id, !currentTest.is_published)
      if (success) {
        setHasUnsavedChanges(false)
      }
    } catch (error) {
      console.error('Failed to publish test:', error)
    }
  }

  const getTabStatus = (tab: string) => {
    if (!currentTest) return 'incomplete'
    
    switch (tab) {
      case 'basic':
        return currentTest.title && currentTest.lesson ? 'complete' : 'incomplete'
      case 'questions':
        return testQuestions.length > 0 ? 'complete' : 'incomplete'
      case 'configuration':
        return currentTest.time_limit && currentTest.passing_score ? 'complete' : 'incomplete'
      case 'preview':
        return testPreview ? 'complete' : 'incomplete'
      default:
        return 'incomplete'
    }
  }

  const getCompletionPercentage = () => {
    const tabs = ['basic', 'questions', 'configuration', 'preview']
    const completedTabs = tabs.filter(tab => getTabStatus(tab) === 'complete').length
    return (completedTabs / tabs.length) * 100
  }

  const canPublish = () => {
    return currentTest?.id && 
           currentTest.title && 
           currentTest.lesson && 
           testQuestions.length > 0 &&
           currentTest.time_limit &&
           currentTest.passing_score
  }

  if (loading && !currentTest) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button variant="outline" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
        </div>
        <div className="grid gap-4 md:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button variant="outline" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {isNewTest ? 'Create New Test' : currentTest?.title || 'Edit Test'}
            </h1>
            <p className="text-muted-foreground">
              {isNewTest 
                ? 'Build a comprehensive test with questions and configurations'
                : 'Modify test settings, questions, and configurations'
              }
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {hasUnsavedChanges && (
            <Badge variant="outline" className="text-amber-600 border-amber-600">
              <AlertCircle className="h-3 w-3 mr-1" />
              Unsaved Changes
            </Badge>
          )}
          
          <Button variant="outline" onClick={handleSave} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save'}
          </Button>
          
          <Button 
            onClick={handlePublish} 
            disabled={!canPublish() || publishing}
            variant={currentTest?.is_published ? 'outline' : 'default'}
          >
            <Publish className="h-4 w-4 mr-2" />
            {publishing 
              ? 'Publishing...' 
              : currentTest?.is_published 
                ? 'Unpublish' 
                : 'Publish'
            }
          </Button>
        </div>
      </div>

      {/* Progress Indicator */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Test Completion Progress</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-muted-foreground">
                {Math.round(getCompletionPercentage())}% Complete
              </span>
            </div>
            <Progress value={getCompletionPercentage()} className="h-2" />
            
            <div className="grid grid-cols-4 gap-4 mt-4">
              {[
                { key: 'basic', label: 'Basic Info', icon: FileText },
                { key: 'questions', label: 'Questions', icon: Target },
                { key: 'configuration', label: 'Settings', icon: Settings },
                { key: 'preview', label: 'Preview', icon: Eye }
              ].map(({ key, label, icon: Icon }) => {
                const status = getTabStatus(key)
                return (
                  <div key={key} className="flex items-center space-x-2">
                    <div className={`p-1 rounded-full ${
                      status === 'complete' 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-gray-100 text-gray-400'
                    }`}>
                      {status === 'complete' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Icon className="h-4 w-4" />
                      )}
                    </div>
                    <span className={`text-sm ${
                      status === 'complete' ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {label}
                    </span>
                  </div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      {testPreview && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Questions</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{testPreview.statistics.totalQuestions}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Points</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{testPreview.statistics.totalPoints}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Estimated Duration</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{testPreview.statistics.estimatedDuration} min</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <Badge variant={currentTest?.is_published ? 'default' : 'outline'}>
                {currentTest?.is_published ? 'Published' : 'Draft'}
              </Badge>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic" className="flex items-center space-x-2">
            <FileText className="h-4 w-4" />
            <span>Basic Info</span>
          </TabsTrigger>
          <TabsTrigger value="questions" className="flex items-center space-x-2">
            <Target className="h-4 w-4" />
            <span>Questions</span>
          </TabsTrigger>
          <TabsTrigger value="configuration" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>Configuration</span>
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center space-x-2">
            <Eye className="h-4 w-4" />
            <span>Preview</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          <TestBasicInfo 
            test={currentTest}
            onUpdate={(updates) => {
              if (currentTest) {
                setCurrentTest({ ...currentTest, ...updates })
                setHasUnsavedChanges(true)
              }
            }}
          />
        </TabsContent>

        <TabsContent value="questions" className="space-y-6">
          <QuestionSelection 
            testId={currentTest?.id}
            questions={testQuestions}
            onQuestionsChange={() => setHasUnsavedChanges(true)}
          />
        </TabsContent>

        <TabsContent value="configuration" className="space-y-6">
          <TestConfiguration 
            test={currentTest}
            onUpdate={(updates) => {
              if (currentTest) {
                setCurrentTest({ ...currentTest, ...updates })
                setHasUnsavedChanges(true)
              }
            }}
          />
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <TestPreview 
            testId={currentTest?.id}
            preview={testPreview}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <TestAnalytics 
            testId={currentTest?.id}
            preview={testPreview}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default TestBuilder
