'use client'

import React from 'react'
import { Test } from '@/lib/api/tests'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  Clock, 
  Target, 
  Shield, 
  Shuffle,
  Calendar,
  AlertTriangle,
  Info
} from 'lucide-react'
import { useFormik } from 'formik'
import * as Yup from 'yup'

interface TestConfigurationProps {
  test: Test | null
  onUpdate: (updates: Partial<Test>) => void
}

const validationSchema = Yup.object({
  time_limit: Yup.number()
    .min(1, 'Time limit must be at least 1 minute')
    .max(480, 'Time limit cannot exceed 8 hours'),
  attempts_allowed: Yup.number()
    .min(1, 'At least 1 attempt must be allowed')
    .max(10, 'Maximum 10 attempts allowed'),
  passing_score: Yup.number()
    .min(0, 'Passing score cannot be negative')
    .max(100, 'Passing score cannot exceed 100'),
  scheduled_start: Yup.string(),
  scheduled_end: Yup.string()
})

export function TestConfiguration({ test, onUpdate }: TestConfigurationProps) {
  const formik = useFormik({
    initialValues: {
      time_limit: test?.time_limit || 60,
      attempts_allowed: test?.attempts_allowed || 1,
      passing_score: test?.passing_score || 70,
      randomize_questions: test?.randomize_questions || false,
      randomize_answers: test?.randomize_answers || false,
      scheduled_start: test?.scheduled_start || '',
      scheduled_end: test?.scheduled_end || '',
      security_settings: {
        require_webcam: test?.security_settings?.require_webcam || false,
        prevent_tab_switching: test?.security_settings?.prevent_tab_switching || false
      }
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values) => {
      onUpdate(values)
    }
  })

  const handleFieldChange = (field: string, value: any) => {
    if (field.startsWith('security_settings.')) {
      const securityField = field.split('.')[1]
      const newSecuritySettings = {
        ...formik.values.security_settings,
        [securityField]: value
      }
      formik.setFieldValue('security_settings', newSecuritySettings)
      onUpdate({ security_settings: newSecuritySettings })
    } else {
      formik.setFieldValue(field, value)
      onUpdate({ [field]: value })
    }
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const getSecurityLevel = () => {
    const settings = formik.values.security_settings
    let level = 0
    if (settings.require_webcam) level++
    if (settings.prevent_tab_switching) level++
    
    switch (level) {
      case 0: return { label: 'Basic', color: 'bg-gray-100 text-gray-800' }
      case 1: return { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' }
      case 2: return { label: 'High', color: 'bg-red-100 text-red-800' }
      default: return { label: 'Basic', color: 'bg-gray-100 text-gray-800' }
    }
  }

  return (
    <div className="space-y-6">
      {/* Timing Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>Timing & Attempts</span>
          </CardTitle>
          <CardDescription>
            Configure time limits and attempt restrictions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Time Limit */}
            <div className="space-y-2">
              <Label htmlFor="time_limit">Time Limit (minutes) *</Label>
              <Input
                id="time_limit"
                name="time_limit"
                type="number"
                min="1"
                max="480"
                placeholder="60"
                value={formik.values.time_limit}
                onChange={(e) => handleFieldChange('time_limit', parseInt(e.target.value) || 0)}
                onBlur={formik.handleBlur}
                className={formik.touched.time_limit && formik.errors.time_limit ? 'border-red-500' : ''}
              />
              {formik.touched.time_limit && formik.errors.time_limit && (
                <p className="text-sm text-red-500">{formik.errors.time_limit}</p>
              )}
              <p className="text-sm text-muted-foreground">
                Duration: {formatDuration(formik.values.time_limit)}
              </p>
            </div>

            {/* Attempts Allowed */}
            <div className="space-y-2">
              <Label htmlFor="attempts_allowed">Attempts Allowed *</Label>
              <Input
                id="attempts_allowed"
                name="attempts_allowed"
                type="number"
                min="1"
                max="10"
                placeholder="1"
                value={formik.values.attempts_allowed}
                onChange={(e) => handleFieldChange('attempts_allowed', parseInt(e.target.value) || 1)}
                onBlur={formik.handleBlur}
                className={formik.touched.attempts_allowed && formik.errors.attempts_allowed ? 'border-red-500' : ''}
              />
              {formik.touched.attempts_allowed && formik.errors.attempts_allowed && (
                <p className="text-sm text-red-500">{formik.errors.attempts_allowed}</p>
              )}
            </div>
          </div>

          {/* Passing Score */}
          <div className="space-y-2">
            <Label htmlFor="passing_score">Passing Score (%) *</Label>
            <Input
              id="passing_score"
              name="passing_score"
              type="number"
              min="0"
              max="100"
              placeholder="70"
              value={formik.values.passing_score}
              onChange={(e) => handleFieldChange('passing_score', parseInt(e.target.value) || 0)}
              onBlur={formik.handleBlur}
              className={formik.touched.passing_score && formik.errors.passing_score ? 'border-red-500' : ''}
            />
            {formik.touched.passing_score && formik.errors.passing_score && (
              <p className="text-sm text-red-500">{formik.errors.passing_score}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Randomization Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shuffle className="h-5 w-5" />
            <span>Randomization</span>
          </CardTitle>
          <CardDescription>
            Configure question and answer randomization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="randomize_questions"
              checked={formik.values.randomize_questions}
              onCheckedChange={(checked) => handleFieldChange('randomize_questions', checked)}
            />
            <Label htmlFor="randomize_questions">Randomize question order</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="randomize_answers"
              checked={formik.values.randomize_answers}
              onCheckedChange={(checked) => handleFieldChange('randomize_answers', checked)}
            />
            <Label htmlFor="randomize_answers">Randomize answer options</Label>
          </div>

          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="flex items-start space-x-2">
              <Info className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-700">
                <div className="font-medium">Randomization Benefits:</div>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Reduces cheating by presenting different question orders</li>
                  <li>Answer randomization prevents pattern memorization</li>
                  <li>Maintains test integrity across multiple attempts</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Security Settings</span>
          </CardTitle>
          <CardDescription>
            Configure security measures for test integrity
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="require_webcam"
              checked={formik.values.security_settings.require_webcam}
              onCheckedChange={(checked) => handleFieldChange('security_settings.require_webcam', checked)}
            />
            <Label htmlFor="require_webcam">Require webcam monitoring</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="prevent_tab_switching"
              checked={formik.values.security_settings.prevent_tab_switching}
              onCheckedChange={(checked) => handleFieldChange('security_settings.prevent_tab_switching', checked)}
            />
            <Label htmlFor="prevent_tab_switching">Prevent tab switching</Label>
          </div>

          <div className="p-3 bg-amber-50 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5" />
              <div className="text-sm text-amber-700">
                <div className="font-medium">Security Notice:</div>
                <div className="mt-1">
                  High security settings may require additional browser permissions and 
                  could affect user experience. Test thoroughly before deployment.
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Scheduling */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Scheduling (Optional)</span>
          </CardTitle>
          <CardDescription>
            Set availability window for the test
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="scheduled_start">Available From</Label>
              <Input
                id="scheduled_start"
                name="scheduled_start"
                type="datetime-local"
                value={formik.values.scheduled_start}
                onChange={(e) => handleFieldChange('scheduled_start', e.target.value)}
                onBlur={formik.handleBlur}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="scheduled_end">Available Until</Label>
              <Input
                id="scheduled_end"
                name="scheduled_end"
                type="datetime-local"
                value={formik.values.scheduled_end}
                onChange={(e) => handleFieldChange('scheduled_end', e.target.value)}
                onBlur={formik.handleBlur}
              />
            </div>
          </div>

          {formik.values.scheduled_start && formik.values.scheduled_end && (
            <div className="text-sm text-muted-foreground">
              Test will be available for{' '}
              {Math.round(
                (new Date(formik.values.scheduled_end).getTime() - 
                 new Date(formik.values.scheduled_start).getTime()) / 
                (1000 * 60 * 60 * 24)
              )}{' '}
              days
            </div>
          )}
        </CardContent>
      </Card>

      {/* Configuration Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Configuration Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Time & Attempts</div>
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Duration</span>
                  <Badge variant="outline">{formatDuration(formik.values.time_limit)}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Attempts</span>
                  <Badge variant="outline">{formik.values.attempts_allowed}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Passing Score</span>
                  <Badge variant="outline">{formik.values.passing_score}%</Badge>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Randomization</div>
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Questions</span>
                  <Badge variant={formik.values.randomize_questions ? 'default' : 'outline'}>
                    {formik.values.randomize_questions ? 'Randomized' : 'Fixed Order'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Answers</span>
                  <Badge variant={formik.values.randomize_answers ? 'default' : 'outline'}>
                    {formik.values.randomize_answers ? 'Randomized' : 'Fixed Order'}
                  </Badge>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Security</div>
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Level</span>
                  <Badge className={getSecurityLevel().color}>
                    {getSecurityLevel().label}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Webcam</span>
                  <Badge variant={formik.values.security_settings.require_webcam ? 'default' : 'outline'}>
                    {formik.values.security_settings.require_webcam ? 'Required' : 'Optional'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Tab Lock</span>
                  <Badge variant={formik.values.security_settings.prevent_tab_switching ? 'default' : 'outline'}>
                    {formik.values.security_settings.prevent_tab_switching ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default TestConfiguration
