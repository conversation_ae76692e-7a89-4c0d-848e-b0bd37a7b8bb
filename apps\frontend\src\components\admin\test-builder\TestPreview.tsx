'use client'

import React, { useState } from 'react'
import { TestPreview as TestPreviewType } from '@/lib/api/tests'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { 
  Eye, 
  Clock, 
  Target, 
  FileText, 
  BarChart3,
  CheckCircle,
  AlertCircle,
  Info,
  Play,
  Pause
} from 'lucide-react'

interface TestPreviewProps {
  testId?: string
  preview: TestPreviewType | null
}

export function TestPreview({ testId, preview }: TestPreviewProps) {
  const [showAnswers, setShowAnswers] = useState(false)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)

  if (!preview) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <Eye className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
          <div className="text-muted-foreground mb-2">No preview available</div>
          <div className="text-sm text-muted-foreground">
            Complete the basic information and add questions to generate a preview
          </div>
        </CardContent>
      </Card>
    )
  }

  const { test, questions, statistics } = preview
  const currentQuestion = questions[currentQuestionIndex]

  const getQuestionTypeLabel = (type: string) => {
    const typeLabels: Record<string, string> = {
      'multiple_choice_single': 'Multiple Choice (Single)',
      'multiple_choice_multiple': 'Multiple Choice (Multiple)',
      'true_false': 'True/False',
      'fill_blanks': 'Fill in Blanks',
      'essay': 'Essay',
      'matching': 'Matching',
      'ordering': 'Ordering'
    }
    return typeLabels[type] || type
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'hard':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  return (
    <div className="space-y-6">
      {/* Test Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <span>Test Preview</span>
          </CardTitle>
          <CardDescription>
            Preview how your test will appear to students
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold">{test.title}</h2>
              {test.description && (
                <p className="text-muted-foreground mt-2">{test.description}</p>
              )}
            </div>

            <div className="flex items-center space-x-4">
              <Badge variant="secondary">{test.type}</Badge>
              {test.time_limit && (
                <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>{formatDuration(test.time_limit)}</span>
                </div>
              )}
              <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                <Target className="h-4 w-4" />
                <span>{statistics.totalPoints} points</span>
              </div>
            </div>

            {test.instructions && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-start space-x-2">
                  <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div>
                    <div className="font-medium text-blue-800">Instructions</div>
                    <div className="text-blue-700 mt-1">{test.instructions}</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Test Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Questions</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.totalQuestions}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Points</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.totalPoints}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Estimated Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.estimatedDuration} min</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Passing Score</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{test.passing_score}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Difficulty Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Difficulty Distribution</CardTitle>
          <CardDescription>
            Breakdown of questions by difficulty level
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(statistics.difficultyDistribution).map(([difficulty, count]) => {
              const percentage = statistics.totalQuestions > 0 
                ? (count / statistics.totalQuestions) * 100 
                : 0
              
              return (
                <div key={difficulty} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm capitalize">{difficulty}</span>
                    <span className="text-sm font-medium">{count} ({percentage.toFixed(1)}%)</span>
                  </div>
                  <Progress value={percentage} className="h-2" />
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Question Types */}
      <Card>
        <CardHeader>
          <CardTitle>Question Types</CardTitle>
          <CardDescription>
            Distribution of different question types
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 md:grid-cols-2">
            {Object.entries(statistics.questionTypes).map(([type, count]) => (
              <div key={type} className="flex items-center justify-between p-2 border rounded">
                <span className="text-sm">{getQuestionTypeLabel(type)}</span>
                <Badge variant="outline">{count}</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Question Preview */}
      {questions.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Question Preview</CardTitle>
                <CardDescription>
                  Navigate through questions to see how they'll appear
                </CardDescription>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-answers"
                    checked={showAnswers}
                    onCheckedChange={setShowAnswers}
                  />
                  <Label htmlFor="show-answers">Show Answers</Label>
                </div>
                
                <div className="text-sm text-muted-foreground">
                  {currentQuestionIndex + 1} of {questions.length}
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Question Navigation */}
              <div className="flex items-center justify-between">
                <Button
                  variant="outline"
                  onClick={() => setCurrentQuestionIndex(Math.max(0, currentQuestionIndex - 1))}
                  disabled={currentQuestionIndex === 0}
                >
                  Previous
                </Button>
                
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">
                    {getQuestionTypeLabel(currentQuestion.type)}
                  </Badge>
                  <Badge className={getDifficultyColor(currentQuestion.difficulty)}>
                    {currentQuestion.difficulty}
                  </Badge>
                  <Badge variant="secondary">{currentQuestion.points} pts</Badge>
                </div>
                
                <Button
                  variant="outline"
                  onClick={() => setCurrentQuestionIndex(Math.min(questions.length - 1, currentQuestionIndex + 1))}
                  disabled={currentQuestionIndex === questions.length - 1}
                >
                  Next
                </Button>
              </div>

              {/* Current Question */}
              <div className="p-6 border rounded-lg">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <h3 className="text-lg font-medium">
                      Question {currentQuestionIndex + 1}
                    </h3>
                    {currentQuestion.time_limit && (
                      <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>{currentQuestion.time_limit} min</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="text-base">{currentQuestion.content}</div>

                  {/* Question Options */}
                  {currentQuestion.options && currentQuestion.options.length > 0 && (
                    <div className="space-y-2">
                      {currentQuestion.options.map((option, index) => (
                        <div 
                          key={index} 
                          className={`p-3 border rounded ${
                            showAnswers && option.is_correct 
                              ? 'bg-green-50 border-green-200' 
                              : 'bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">
                              {String.fromCharCode(65 + index)}.
                            </span>
                            <span>{option.text}</span>
                            {showAnswers && option.is_correct && (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Correct Answer for non-multiple choice */}
                  {showAnswers && currentQuestion.correct_answer && !currentQuestion.options && (
                    <div className="p-3 bg-green-50 border border-green-200 rounded">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-green-800">Correct Answer:</span>
                        <span className="text-green-700">{currentQuestion.correct_answer}</span>
                      </div>
                    </div>
                  )}

                  {/* Explanation */}
                  {showAnswers && currentQuestion.explanation && (
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                      <div className="flex items-start space-x-2">
                        <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                        <div>
                          <div className="font-medium text-blue-800">Explanation:</div>
                          <div className="text-blue-700 mt-1">{currentQuestion.explanation}</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Question Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}%</span>
                </div>
                <Progress value={((currentQuestionIndex + 1) / questions.length) * 100} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Validation Issues */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5" />
            <span>Validation Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {statistics.totalQuestions === 0 && (
              <div className="flex items-center space-x-2 text-amber-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">No questions added to the test</span>
              </div>
            )}
            
            {!test.time_limit && (
              <div className="flex items-center space-x-2 text-amber-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">Time limit not set</span>
              </div>
            )}
            
            {!test.passing_score && (
              <div className="flex items-center space-x-2 text-amber-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">Passing score not set</span>
              </div>
            )}

            {statistics.totalQuestions > 0 && test.time_limit && test.passing_score && (
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">Test is ready for publication</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default TestPreview
