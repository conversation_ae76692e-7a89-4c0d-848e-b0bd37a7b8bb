'use client'

import React, { useEffect, useState } from 'react'
import { useTestStore } from '@/stores/admin/tests'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Copy,
  Trash2,
  BarChart3,
  Users,
  Calendar,
  FileText,
  Settings,
  Download,
  Upload
} from 'lucide-react'
import { TestBuilder, BulkTestOperations } from '@/components/admin/test-builder'

export function TestManagement() {
  const {
    tests,
    loading,
    pagination,
    filters,
    fetchTests,
    setFilters,
    resetFilters,
    deleteTest,
    duplicateTest
  } = useTestStore()

  const [selectedTests, setSelectedTests] = useState<string[]>([])
  const [searchInput, setSearchInput] = useState(filters.search)
  const [showTestBuilder, setShowTestBuilder] = useState(false)
  const [showBulkOperations, setShowBulkOperations] = useState(false)
  const [editingTestId, setEditingTestId] = useState<string | null>(null)

  useEffect(() => {
    fetchTests()
  }, [fetchTests])

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilters({ search: searchInput })
    }, 500)

    return () => clearTimeout(timer)
  }, [searchInput, setFilters])

  const handleSelectTest = (testId: string, checked: boolean) => {
    if (checked) {
      setSelectedTests([...selectedTests, testId])
    } else {
      setSelectedTests(selectedTests.filter(id => id !== testId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTests(tests.map(test => test.id))
    } else {
      setSelectedTests([])
    }
  }

  const handleCreateNew = () => {
    setEditingTestId(null)
    setShowTestBuilder(true)
  }

  const handleEdit = (testId: string) => {
    setEditingTestId(testId)
    setShowTestBuilder(true)
  }

  const handleDuplicate = async (testId: string) => {
    const test = tests.find(t => t.id === testId)
    if (test) {
      await duplicateTest(testId, `${test.title} (Copy)`)
    }
  }

  const handleDelete = async (testId: string) => {
    if (confirm('Are you sure you want to delete this test? This action cannot be undone.')) {
      await deleteTest(testId)
    }
  }

  const handleBulkOperations = () => {
    setShowBulkOperations(true)
  }

  const getTestActions = (test: any) => [
    {
      label: 'View Details',
      icon: Eye,
      onClick: () => handleEdit(test.id)
    },
    {
      label: 'Edit',
      icon: Edit,
      onClick: () => handleEdit(test.id)
    },
    {
      label: 'Duplicate',
      icon: Copy,
      onClick: () => handleDuplicate(test.id)
    },
    {
      label: 'Analytics',
      icon: BarChart3,
      onClick: () => {
        // Navigate to analytics view
        console.log('View analytics for test:', test.id)
      }
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: () => handleDelete(test.id),
      destructive: true
    }
  ]

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusBadge = (test: any) => {
    if (test.is_published) {
      return <Badge variant="default">Published</Badge>
    }
    return <Badge variant="outline">Draft</Badge>
  }

  if (showTestBuilder) {
    return (
      <TestBuilder
        testId={editingTestId}
        onBack={() => {
          setShowTestBuilder(false)
          setEditingTestId(null)
        }}
        onSave={() => {
          fetchTests() // Refresh the list
        }}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Test Management</h1>
          <p className="text-muted-foreground">
            Create, manage, and analyze your assessments
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {selectedTests.length > 0 && (
            <Button variant="outline" onClick={handleBulkOperations}>
              <Settings className="h-4 w-4 mr-2" />
              Bulk Operations ({selectedTests.length})
            </Button>
          )}
          
          <Button onClick={handleCreateNew}>
            <Plus className="h-4 w-4 mr-2" />
            Create Test
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tests</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pagination.totalDocs}</div>
            <p className="text-xs text-muted-foreground">
              {tests.filter(t => t.is_published).length} published
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Draft Tests</CardTitle>
            <Edit className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tests.filter(t => !t.is_published).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Unpublished tests
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Templates</CardTitle>
            <Copy className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tests.filter(t => t.is_template).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Reusable templates
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tests.filter(t => {
                const created = new Date(t.createdAt)
                const now = new Date()
                return created.getMonth() === now.getMonth() && 
                       created.getFullYear() === now.getFullYear()
              }).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Tests created
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search tests..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={filters.type || 'all'} onValueChange={(value) => setFilters({ type: value === 'all' ? '' : value })}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Test Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="quiz">Quiz</SelectItem>
                <SelectItem value="practice">Practice</SelectItem>
                <SelectItem value="graded_test">Graded Test</SelectItem>
                <SelectItem value="final_exam">Final Exam</SelectItem>
              </SelectContent>
            </Select>

            <Select 
              value={filters.is_published === undefined ? 'all' : filters.is_published ? 'published' : 'draft'} 
              onValueChange={(value) => setFilters({ 
                is_published: value === 'all' ? undefined : value === 'published' 
              })}
            >
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={resetFilters}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tests Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Tests</CardTitle>
              <CardDescription>
                Manage your test library and assessments
              </CardDescription>
            </div>
            
            {selectedTests.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  {selectedTests.length} selected
                </span>
                <Button variant="outline" size="sm" onClick={handleBulkOperations}>
                  <Settings className="h-4 w-4 mr-2" />
                  Bulk Actions
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">
                    <Checkbox
                      checked={selectedTests.length === tests.length && tests.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Questions</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="animate-pulse">Loading tests...</div>
                    </TableCell>
                  </TableRow>
                ) : tests.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center space-y-2">
                        <FileText className="h-8 w-8 text-muted-foreground" />
                        <div className="text-muted-foreground">No tests found</div>
                        <div className="text-sm text-muted-foreground">
                          Create your first test to get started
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  tests.map((test) => (
                    <TableRow key={test.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedTests.includes(test.id)}
                          onCheckedChange={(checked) => handleSelectTest(test.id, !!checked)}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{test.title}</div>
                          {test.description && (
                            <div className="text-sm text-muted-foreground line-clamp-2">
                              {test.description}
                            </div>
                          )}
                          {test.is_template && (
                            <Badge variant="outline" className="text-xs">
                              Template
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{test.type}</Badge>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(test)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{test.question_count || 0}</span>
                          <span className="text-sm text-muted-foreground">questions</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{formatDate(test.createdAt)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            {getTestActions(test).map((action, index) => (
                              <React.Fragment key={action.label}>
                                {index > 0 && action.destructive && <DropdownMenuSeparator />}
                                <DropdownMenuItem
                                  onClick={action.onClick}
                                  className={action.destructive ? 'text-red-600' : ''}
                                >
                                  <action.icon className="mr-2 h-4 w-4" />
                                  {action.label}
                                </DropdownMenuItem>
                              </React.Fragment>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.totalDocs)} of{' '}
                {pagination.totalDocs} tests
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({ page: pagination.page - 1 })}
                  disabled={!pagination.hasPrevPage}
                >
                  Previous
                </Button>
                <span className="text-sm">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({ page: pagination.page + 1 })}
                  disabled={!pagination.hasNextPage}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bulk Operations Dialog */}
      <BulkTestOperations
        open={showBulkOperations}
        onOpenChange={setShowBulkOperations}
        selectedTests={selectedTests}
        onSuccess={() => {
          setSelectedTests([])
          fetchTests()
        }}
      />
    </div>
  )
}

export default TestManagement
