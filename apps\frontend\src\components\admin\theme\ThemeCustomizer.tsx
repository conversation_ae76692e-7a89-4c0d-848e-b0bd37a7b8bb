'use client'

import React, { useState, useEffect } from 'react'
import { useTheme, ThemeCustomizations } from '@/components/shared/theme/ThemeProvider'
import { Save, RotateCcw, Upload, Palette, Type, Image } from 'lucide-react'

interface ThemeCustomizerProps {
  onSave?: (customizations: ThemeCustomizations) => void
}

export default function ThemeCustomizer({ onSave }: ThemeCustomizerProps) {
  const { currentTheme, customizations, updateCustomizations } = useTheme()
  const [localCustomizations, setLocalCustomizations] = useState<ThemeCustomizations>(customizations)
  const [activeTab, setActiveTab] = useState<'colors' | 'fonts' | 'branding' | 'content'>('colors')
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    setLocalCustomizations(customizations)
  }, [customizations])

  const handleColorChange = (colorKey: string, value: string) => {
    setLocalCustomizations(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorKey]: value
      }
    }))
  }

  const handleFontChange = (fontKey: string, value: string) => {
    setLocalCustomizations(prev => ({
      ...prev,
      fonts: {
        ...prev.fonts,
        [fontKey]: value
      }
    }))
  }

  const handleContentChange = (key: string, value: any) => {
    setLocalCustomizations(prev => ({
      ...prev,
      content: {
        ...prev.content,
        [key]: value
      }
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Update theme context
      updateCustomizations(localCustomizations)
      
      // Save to backend
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/institute-admin/themes/apply`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({ customizations: localCustomizations })
        }
      )

      if (!response.ok) {
        throw new Error('Failed to save customizations')
      }

      const data = await response.json()
      
      if (data.success) {
        onSave?.(localCustomizations)
        alert('Theme customizations saved successfully!')
      } else {
        throw new Error(data.error || 'Failed to save customizations')
      }
    } catch (err) {
      console.error('Save error:', err)
      alert(err instanceof Error ? err.message : 'Failed to save customizations')
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    setLocalCustomizations({})
    updateCustomizations({})
  }

  const handlePreview = () => {
    updateCustomizations(localCustomizations)
  }

  const tabs = [
    { id: 'colors', label: 'Colors', icon: Palette },
    { id: 'fonts', label: 'Typography', icon: Type },
    { id: 'branding', label: 'Branding', icon: Image },
    { id: 'content', label: 'Content', icon: Upload }
  ]

  const colorFields = [
    { key: 'primary', label: 'Primary Color', description: 'Main brand color' },
    { key: 'secondary', label: 'Secondary Color', description: 'Secondary brand color' },
    { key: 'accent', label: 'Accent Color', description: 'Highlight and accent color' },
    { key: 'background', label: 'Background', description: 'Main background color' },
    { key: 'text', label: 'Text Color', description: 'Primary text color' },
    { key: 'muted', label: 'Muted Text', description: 'Secondary text color' },
    { key: 'border', label: 'Border Color', description: 'Border and divider color' }
  ]

  const fontFields = [
    { key: 'heading', label: 'Heading Font', description: 'Font for headings and titles' },
    { key: 'body', label: 'Body Font', description: 'Font for body text and content' },
    { key: 'mono', label: 'Monospace Font', description: 'Font for code and monospace text' }
  ]

  const getColorValue = (key: string) => {
    return localCustomizations.colors?.[key] || currentTheme?.colors[key] || '#000000'
  }

  const getFontValue = (key: string) => {
    return localCustomizations.fonts?.[key] || currentTheme?.fonts[key] || 'Inter, sans-serif'
  }

  if (!currentTheme) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No theme selected. Please select a theme first.</p>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Theme Customizer</h2>
            <p className="text-gray-600 mt-1">
              Customize {currentTheme.name} to match your brand
            </p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handlePreview}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-green-600 hover:text-green-600 transition-colors duration-200"
            >
              Preview
            </button>
            <button
              onClick={handleReset}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-red-600 hover:text-red-600 transition-colors duration-200 flex items-center"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </button>
            <button
              onClick={handleSave}
              disabled={saving}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Saving...' : 'Save'}
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Colors Tab */}
        {activeTab === 'colors' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Color Scheme</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {colorFields.map((field) => (
                  <div key={field.key} className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      {field.label}
                    </label>
                    <p className="text-xs text-gray-500">{field.description}</p>
                    <div className="flex items-center space-x-3">
                      <input
                        type="color"
                        value={getColorValue(field.key)}
                        onChange={(e) => handleColorChange(field.key, e.target.value)}
                        className="h-10 w-20 border border-gray-300 rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={getColorValue(field.key)}
                        onChange={(e) => handleColorChange(field.key, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        placeholder="#000000"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Fonts Tab */}
        {activeTab === 'fonts' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Typography</h3>
              <div className="space-y-6">
                {fontFields.map((field) => (
                  <div key={field.key} className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      {field.label}
                    </label>
                    <p className="text-xs text-gray-500">{field.description}</p>
                    <select
                      value={getFontValue(field.key)}
                      onChange={(e) => handleFontChange(field.key, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value="Inter, sans-serif">Inter</option>
                      <option value="Poppins, sans-serif">Poppins</option>
                      <option value="Roboto, sans-serif">Roboto</option>
                      <option value="Open Sans, sans-serif">Open Sans</option>
                      <option value="Lato, sans-serif">Lato</option>
                      <option value="Montserrat, sans-serif">Montserrat</option>
                      <option value="Nunito, sans-serif">Nunito</option>
                      <option value="Source Sans Pro, sans-serif">Source Sans Pro</option>
                      <option value="Fira Code, monospace">Fira Code (Mono)</option>
                      <option value="JetBrains Mono, monospace">JetBrains Mono</option>
                    </select>
                    <div 
                      className="p-3 border border-gray-200 rounded-lg bg-gray-50"
                      style={{ fontFamily: getFontValue(field.key) }}
                    >
                      <p className="text-lg">The quick brown fox jumps over the lazy dog</p>
                      <p className="text-sm text-gray-600 mt-1">Preview of {field.label.toLowerCase()}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Branding Tab */}
        {activeTab === 'branding' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Brand Assets</h3>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Logo URL
                  </label>
                  <input
                    type="url"
                    value={localCustomizations.logo || ''}
                    onChange={(e) => setLocalCustomizations(prev => ({ ...prev, logo: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="https://example.com/logo.png"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Favicon URL
                  </label>
                  <input
                    type="url"
                    value={localCustomizations.favicon || ''}
                    onChange={(e) => setLocalCustomizations(prev => ({ ...prev, favicon: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="https://example.com/favicon.ico"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content Tab */}
        {activeTab === 'content' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Content Customization</h3>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Institute Name
                  </label>
                  <input
                    type="text"
                    value={localCustomizations.content?.instituteName || ''}
                    onChange={(e) => handleContentChange('instituteName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Your Institute Name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Custom CSS
                  </label>
                  <textarea
                    value={localCustomizations.customCSS || ''}
                    onChange={(e) => setLocalCustomizations(prev => ({ ...prev, customCSS: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent h-32"
                    placeholder="/* Add your custom CSS here */"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Advanced: Add custom CSS to further customize your theme
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
