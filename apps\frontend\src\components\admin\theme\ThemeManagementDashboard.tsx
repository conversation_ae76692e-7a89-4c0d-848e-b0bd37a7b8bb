'use client'

import React, { useState, useEffect } from 'react'
import { useTheme, Theme } from '@/components/shared/theme/ThemeProvider'
import ThemeSelector from './ThemeSelector'
import ThemeCustomizer from './ThemeCustomizer'
import { 
  Palette, 
  BarChart3, 
  Settings, 
  Eye, 
  Download, 
  Upload, 
  Star,
  Users,
  TrendingUp,
  Calendar,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react'

interface ThemeAnalytics {
  totalThemes: number
  activeThemes: number
  mostPopularTheme: string
  totalUsage: number
  recentActivity: Array<{
    id: string
    action: string
    theme: string
    user: string
    timestamp: string
  }>
  usageByTheme: Array<{
    name: string
    usage: number
    rating: number
  }>
}

interface ThemeManagementDashboardProps {
  userType: 'platform' | 'institute'
}

export default function ThemeManagementDashboard({ userType }: ThemeManagementDashboardProps) {
  const { currentTheme } = useTheme()
  const [activeTab, setActiveTab] = useState<'overview' | 'themes' | 'customize' | 'analytics'>('overview')
  const [analytics, setAnalytics] = useState<ThemeAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')

  useEffect(() => {
    fetchAnalytics()
  }, [userType])

  const fetchAnalytics = async () => {
    setLoading(true)
    try {
      // Mock analytics data - in real app, this would come from API
      const mockAnalytics: ThemeAnalytics = {
        totalThemes: userType === 'platform' ? 8 : 12,
        activeThemes: userType === 'platform' ? 3 : 5,
        mostPopularTheme: userType === 'platform' ? 'SaaS Modern' : 'Education Modern',
        totalUsage: userType === 'platform' ? 1250 : 890,
        recentActivity: [
          {
            id: '1',
            action: 'Applied theme',
            theme: 'Education Modern',
            user: 'John Doe',
            timestamp: '2024-01-15T10:30:00Z'
          },
          {
            id: '2',
            action: 'Customized colors',
            theme: 'SaaS Modern',
            user: 'Jane Smith',
            timestamp: '2024-01-15T09:15:00Z'
          },
          {
            id: '3',
            action: 'Updated logo',
            theme: 'Education Modern',
            user: 'Mike Johnson',
            timestamp: '2024-01-14T16:45:00Z'
          }
        ],
        usageByTheme: [
          { name: 'Education Modern', usage: 450, rating: 4.8 },
          { name: 'SaaS Modern', usage: 320, rating: 4.6 },
          { name: 'Corporate Clean', usage: 120, rating: 4.4 },
          { name: 'Creative Studio', usage: 80, rating: 4.2 }
        ]
      }
      setAnalytics(mockAnalytics)
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'themes', label: 'Themes', icon: Palette },
    { id: 'customize', label: 'Customize', icon: Settings },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp }
  ]

  const deviceIcons = {
    desktop: Monitor,
    tablet: Tablet,
    mobile: Smartphone
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Theme Management</h1>
          <p className="text-gray-600 mt-1">
            Manage and customize {userType === 'platform' ? 'platform' : 'institute'} themes
          </p>
        </div>
        
        {currentTheme && (
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-600">Current Theme</p>
              <p className="font-semibold text-gray-900">{currentTheme.name}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center">
              <Palette className="h-6 w-6 text-white" />
            </div>
          </div>
        )}
      </div>

      {/* Stats Cards */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Palette className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Themes</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.totalThemes}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Eye className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Themes</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.activeThemes}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Usage</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.totalUsage.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Most Popular</p>
                <p className="text-lg font-bold text-gray-900">{analytics.mostPopularTheme}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'overview' && analytics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Activity */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
              <div className="space-y-4">
                {analytics.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-900">
                        <span className="font-medium">{activity.user}</span> {activity.action}
                      </p>
                      <p className="text-xs text-gray-500">
                        {activity.theme} • {formatDate(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Theme Usage */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Theme Usage</h3>
              <div className="space-y-4">
                {analytics.usageByTheme.map((theme, index) => (
                  <div key={theme.name} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        index === 0 ? 'bg-green-500' :
                        index === 1 ? 'bg-blue-500' :
                        index === 2 ? 'bg-purple-500' : 'bg-gray-400'
                      }`}></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{theme.name}</p>
                        <div className="flex items-center">
                          <Star className="h-3 w-3 text-yellow-400 fill-current mr-1" />
                          <span className="text-xs text-gray-600">
                            {typeof theme.rating === 'object' ? theme.rating.average?.toFixed(1) : theme.rating}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{theme.usage}</p>
                      <p className="text-xs text-gray-500">users</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'themes' && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Available Themes</h3>
              <div className="flex items-center space-x-4">
                {/* Device Preview Toggle */}
                <div className="flex border border-gray-300 rounded-lg">
                  {Object.entries(deviceIcons).map(([device, Icon]) => (
                    <button
                      key={device}
                      onClick={() => setPreviewDevice(device as any)}
                      className={`p-2 ${
                        previewDevice === device 
                          ? 'bg-green-600 text-white' 
                          : 'text-gray-600 hover:text-green-600'
                      }`}
                      title={device.charAt(0).toUpperCase() + device.slice(1)}
                    >
                      <Icon className="h-4 w-4" />
                    </button>
                  ))}
                </div>
                
                <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-green-600 hover:text-green-600 transition-colors duration-200 flex items-center">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Theme
                </button>
              </div>
            </div>
            
            <ThemeSelector userType={userType} />
          </div>
        )}

        {activeTab === 'customize' && (
          <div>
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Theme Customization</h3>
              <p className="text-gray-600">
                Customize your current theme to match your brand and preferences.
              </p>
            </div>
            
            <ThemeCustomizer />
          </div>
        )}

        {activeTab === 'analytics' && analytics && (
          <div className="space-y-8">
            {/* Usage Chart */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Theme Usage Over Time</h3>
              <div className="h-64 flex items-end justify-center space-x-4">
                {analytics.usageByTheme.map((theme, index) => (
                  <div key={theme.name} className="flex flex-col items-center">
                    <div 
                      className={`w-12 rounded-t ${
                        index === 0 ? 'bg-green-500' :
                        index === 1 ? 'bg-blue-500' :
                        index === 2 ? 'bg-purple-500' : 'bg-gray-400'
                      }`}
                      style={{ height: `${(theme.usage / Math.max(...analytics.usageByTheme.map(t => t.usage))) * 200}px` }}
                    ></div>
                    <p className="text-xs text-gray-600 mt-2 text-center">{theme.name}</p>
                    <p className="text-xs font-medium text-gray-900">{theme.usage}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium text-gray-600">Average Rating</h4>
                  <Star className="h-4 w-4 text-yellow-400" />
                </div>
                <p className="text-2xl font-bold text-gray-900">4.6</p>
                <p className="text-sm text-green-600">+0.2 from last month</p>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium text-gray-600">Theme Switches</h4>
                  <TrendingUp className="h-4 w-4 text-blue-500" />
                </div>
                <p className="text-2xl font-bold text-gray-900">156</p>
                <p className="text-sm text-green-600">+12% from last month</p>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium text-gray-600">Customizations</h4>
                  <Settings className="h-4 w-4 text-purple-500" />
                </div>
                <p className="text-2xl font-bold text-gray-900">89</p>
                <p className="text-sm text-green-600">+8% from last month</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
