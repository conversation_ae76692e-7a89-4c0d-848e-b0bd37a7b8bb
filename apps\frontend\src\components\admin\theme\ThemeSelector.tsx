'use client'

import React, { useState, useEffect } from 'react'
import { useTheme, Theme } from '@/components/shared/theme/ThemeProvider'
import { Eye, Check, Settings, Star, Download } from 'lucide-react'

interface ThemeSelectorProps {
  userType: 'platform' | 'institute'
  onThemeSelect?: (theme: Theme) => void
}

export default function ThemeSelector({ userType, onThemeSelect }: ThemeSelectorProps) {
  const { currentTheme, setTheme } = useTheme()
  const [availableThemes, setAvailableThemes] = useState<Theme[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTheme, setSelectedTheme] = useState<Theme | null>(null)
  const [previewMode, setPreviewMode] = useState(false)

  // Fetch available themes
  useEffect(() => {
    fetchThemes()
  }, [userType])

  const fetchThemes = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/themes-list?type=${userType}&active=true`,
        {
          credentials: 'include'
        }
      )

      if (!response.ok) {
        throw new Error('Failed to fetch themes')
      }

      const data = await response.json()

      if (data.success) {
        setAvailableThemes(data.themes)
      } else {
        throw new Error(data.error || 'Failed to fetch themes')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch themes')
      console.error('Theme fetch error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleThemePreview = (theme: Theme) => {
    setSelectedTheme(theme)
    setPreviewMode(true)
    setTheme(theme)
  }

  const handleThemeApply = async (theme: Theme) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/institute-admin/themes/apply`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({ themeId: theme.id })
        }
      )

      if (!response.ok) {
        throw new Error('Failed to apply theme')
      }

      const data = await response.json()

      if (data.success) {
        setTheme(theme)
        setPreviewMode(false)
        onThemeSelect?.(theme)
        
        // Show success message
        alert('Theme applied successfully!')
      } else {
        throw new Error(data.error || 'Failed to apply theme')
      }
    } catch (err) {
      console.error('Theme application error:', err)
      alert(err instanceof Error ? err.message : 'Failed to apply theme')
    }
  }

  const handleCancelPreview = () => {
    setPreviewMode(false)
    setSelectedTheme(null)
    if (currentTheme) {
      setTheme(currentTheme)
    }
  }

  const ThemeCard = ({ theme }: { theme: Theme }) => {
    const isCurrentTheme = currentTheme?.id === theme.id
    const isSelected = selectedTheme?.id === theme.id

    return (
      <div className={`bg-white rounded-lg shadow-md overflow-hidden transition-all duration-200 ${
        isSelected ? 'ring-2 ring-green-500 shadow-lg' : 'hover:shadow-lg'
      }`}>
        {/* Preview Image */}
        <div className="relative">
          <img
            src={theme.previewImage || '/images/theme-placeholder.jpg'}
            alt={`${theme.name} preview`}
            className="w-full h-48 object-cover"
          />
          
          {/* Badges */}
          <div className="absolute top-3 left-3 flex flex-col space-y-1">
            {theme.isFeatured && (
              <span className="px-2 py-1 bg-yellow-500 text-white text-xs font-medium rounded">
                <Star className="inline h-3 w-3 mr-1" />
                Featured
              </span>
            )}
            {theme.isDefault && (
              <span className="px-2 py-1 bg-blue-500 text-white text-xs font-medium rounded">
                Default
              </span>
            )}
          </div>

          {/* Current Theme Badge */}
          {isCurrentTheme && (
            <div className="absolute top-3 right-3">
              <span className="px-2 py-1 bg-green-500 text-white text-xs font-medium rounded flex items-center">
                <Check className="h-3 w-3 mr-1" />
                Current
              </span>
            </div>
          )}

          {/* Overlay Actions */}
          <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
            <div className="flex space-x-2">
              <button
                onClick={() => handleThemePreview(theme)}
                className="px-4 py-2 bg-white text-gray-900 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </button>
              {!isCurrentTheme && (
                <button
                  onClick={() => handleThemeApply(theme)}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Apply
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Theme Info */}
        <div className="p-4">
          <div className="flex items-start justify-between mb-2">
            <h3 className="text-lg font-semibold text-gray-900">{theme.name}</h3>
            <span className="text-sm text-gray-500">v{theme.version}</span>
          </div>
          
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {theme.description}
          </p>

          {/* Category */}
          <div className="flex items-center justify-between mb-3">
            <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded">
              {theme.category.replace('_', ' ').toUpperCase()}
            </span>
            {theme.rating && (
              <div className="flex items-center">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-600 ml-1">
                  {typeof theme.rating === 'object'
                    ? `${theme.rating.average?.toFixed(1) || 0} (${theme.rating.count || 0})`
                    : theme.rating
                  }
                </span>
              </div>
            )}
          </div>

          {/* Features */}
          {theme.features && theme.features.length > 0 && (
            <div className="mb-3">
              <p className="text-xs text-gray-500 mb-1">Features:</p>
              <div className="flex flex-wrap gap-1">
                {theme.features.slice(0, 3).map((feature, index) => (
                  <span key={index} className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded">
                    {feature.replace('_', ' ')}
                  </span>
                ))}
                {theme.features.length > 3 && (
                  <span className="px-2 py-1 bg-gray-50 text-gray-600 text-xs rounded">
                    +{theme.features.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2">
            <button
              onClick={() => handleThemePreview(theme)}
              className="flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-green-600 hover:text-green-600 transition-colors duration-200 text-sm flex items-center justify-center"
            >
              <Eye className="h-4 w-4 mr-1" />
              Preview
            </button>
            
            {!isCurrentTheme && (
              <button
                onClick={() => handleThemeApply(theme)}
                className="flex-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 text-sm flex items-center justify-center"
              >
                <Download className="h-4 w-4 mr-1" />
                Apply
              </button>
            )}

            <button className="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-green-600 hover:text-green-600 transition-colors duration-200">
              <Settings className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-gray-200 h-48 rounded-t-lg"></div>
            <div className="bg-white p-4 rounded-b-lg">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={fetchThemes}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div>
      {/* Preview Mode Banner */}
      {previewMode && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Eye className="h-5 w-5 text-yellow-600 mr-2" />
              <span className="text-yellow-800 font-medium">
                Preview Mode: {selectedTheme?.name}
              </span>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => selectedTheme && handleThemeApply(selectedTheme)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 text-sm"
              >
                Apply Theme
              </button>
              <button
                onClick={handleCancelPreview}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-gray-400 transition-colors duration-200 text-sm"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Theme Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availableThemes.map((theme) => (
          <ThemeCard key={theme.id} theme={theme} />
        ))}
      </div>

      {/* Empty State */}
      {availableThemes.length === 0 && (
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <Settings className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No themes available</h3>
          <p className="text-gray-600">
            {userType === 'platform' 
              ? 'No platform themes are currently available.' 
              : 'No institute themes are currently available.'}
          </p>
        </div>
      )}
    </div>
  )
}
