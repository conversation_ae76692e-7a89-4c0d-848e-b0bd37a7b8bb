'use client'

import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Eye, Send, CreditCard, Download } from 'lucide-react'

interface BillCardProps {
  bill: any
}

export function BillCard({ bill }: BillCardProps) {
  const formatCurrency = (amount: number, currency = 'INR') => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      case 'viewed':
        return 'bg-purple-100 text-purple-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-lg">{bill.billNumber}</h3>
            <p className="text-sm text-gray-500">
              {typeof bill.branch === 'object' ? bill.branch.name : 'Branch'}
            </p>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Send className="h-4 w-4 mr-2" />
                Send Bill
              </DropdownMenuItem>
              <DropdownMenuItem>
                <CreditCard className="h-4 w-4 mr-2" />
                Record Payment
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Billing Period */}
        <div>
          <span className="text-sm text-gray-500">Billing Period:</span>
          <p className="font-medium">
            {new Date(0, bill.billingPeriod.month - 1).toLocaleString('default', { month: 'long' })} {bill.billingPeriod.year}
          </p>
        </div>

        {/* Amount Breakdown */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Base Fee:</span>
            <span>{formatCurrency(bill.amounts.baseFee)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Commission:</span>
            <span>{formatCurrency(bill.amounts.commissionAmount)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Tax:</span>
            <span>{formatCurrency(bill.amounts.taxAmount)}</span>
          </div>
          <div className="flex justify-between font-bold text-lg border-t pt-2">
            <span>Total:</span>
            <span className="text-primary">{formatCurrency(bill.amounts.totalAmount)}</span>
          </div>
        </div>

        {/* Status and Due Date */}
        <div className="flex items-center justify-between">
          <Badge className={`${getStatusColor(bill.status)} capitalize`}>
            {bill.status}
          </Badge>
          <div className="text-sm text-gray-500">
            Due: {new Date(bill.dates.dueDate).toLocaleDateString()}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
