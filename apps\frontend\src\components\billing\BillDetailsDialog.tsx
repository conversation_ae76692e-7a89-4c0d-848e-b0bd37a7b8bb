'use client'

import { useBillingStore } from '@/stores/billing/useBillingStore'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { FileText, Building, Calendar, DollarSign, Receipt } from 'lucide-react'

interface BillDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function BillDetailsDialog({ open, onOpenChange }: BillDetailsDialogProps) {
  const { selectedBill } = useBillingStore()

  if (!selectedBill) return null

  const formatCurrency = (amount: number, currency = 'INR') => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      case 'viewed':
        return 'bg-purple-100 text-purple-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Bill Details - {selectedBill.billNumber}</span>
          </DialogTitle>
          <DialogDescription>
            Detailed view of the monthly bill including commission breakdown and tax calculations.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Bill Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-4 w-4" />
                <span>Bill Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Bill Number</label>
                  <p className="font-mono">{selectedBill.billNumber}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div>
                    <Badge className={`${getStatusColor(selectedBill.status)} capitalize`}>
                      {selectedBill.status}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Branch</label>
                <p className="font-medium">
                  {typeof selectedBill.branch === 'object' ? selectedBill.branch.name : 'Branch'}
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Billing Period</label>
                  <p className="font-medium">
                    {new Date(0, selectedBill.billingPeriod.month - 1).toLocaleString('default', { month: 'long' })} {selectedBill.billingPeriod.year}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Due Date</label>
                  <p className="font-medium">
                    {new Date(selectedBill.dates.dueDate).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Generated Date</label>
                  <p>{new Date(selectedBill.dates.generatedDate).toLocaleDateString()}</p>
                </div>
                {selectedBill.dates.paidDate && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Paid Date</label>
                    <p>{new Date(selectedBill.dates.paidDate).toLocaleDateString()}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Amount Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4" />
                <span>Amount Breakdown</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Base Fee:</span>
                  <span className="font-medium">{formatCurrency(selectedBill.amounts.baseFee)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span>Commission:</span>
                  <span className="font-medium">{formatCurrency(selectedBill.amounts.commissionAmount)}</span>
                </div>
                
                <Separator />
                
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span className="font-medium">{formatCurrency(selectedBill.amounts.subtotal)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span>Tax ({selectedBill.taxDetails?.taxScenario}):</span>
                  <span className="font-medium">{formatCurrency(selectedBill.amounts.taxAmount)}</span>
                </div>
                
                <Separator />
                
                <div className="flex justify-between text-lg font-bold">
                  <span>Total Amount:</span>
                  <span className="text-primary">{formatCurrency(selectedBill.amounts.totalAmount)}</span>
                </div>
              </div>

              {/* Tax Components */}
              {selectedBill.taxDetails?.taxComponents && selectedBill.taxDetails.taxComponents.length > 0 && (
                <div className="mt-4">
                  <label className="text-sm font-medium text-gray-500">Tax Components:</label>
                  <div className="space-y-1 mt-2">
                    {selectedBill.taxDetails.taxComponents.map((component: any, index: number) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span>{component.componentName} ({component.rate}%)</span>
                        <span>{formatCurrency(component.amount)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Commission Details */}
        {selectedBill.commissionDetails && selectedBill.commissionDetails.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Receipt className="h-4 w-4" />
                <span>Commission Details</span>
              </CardTitle>
              <CardDescription>
                Course purchases that contributed to this bill's commission
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {selectedBill.commissionDetails.map((detail: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium">{detail.courseTitle}</p>
                      <p className="text-sm text-gray-500">
                        {detail.studentName} • {new Date(detail.purchaseDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(detail.purchaseAmount)}</p>
                      <p className="text-sm text-gray-500">
                        {detail.commissionRate}% = {formatCurrency(detail.commissionAmount)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex justify-between font-medium">
                  <span>Total Commission ({selectedBill.commissionDetails.length} purchases):</span>
                  <span className="text-primary">{formatCurrency(selectedBill.amounts.commissionAmount)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Payment Details */}
        {selectedBill.paymentDetails && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>Payment Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Payment Method</label>
                  <p className="font-medium capitalize">{selectedBill.paymentDetails.paymentMethod}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Transaction ID</label>
                  <p className="font-mono">{selectedBill.paymentDetails.transactionId}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </DialogContent>
    </Dialog>
  )
}
