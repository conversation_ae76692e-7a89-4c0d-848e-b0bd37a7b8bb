'use client'

import { useState } from 'react'
import { useBillingStore } from '@/stores/billing/useBillingStore'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, FileText, Users } from 'lucide-react'

interface BillGenerationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function BillGenerationDialog({ open, onOpenChange }: BillGenerationDialogProps) {
  const [activeTab, setActiveTab] = useState('single')
  const [singleBillData, setSingleBillData] = useState({
    branchId: '',
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    baseFee: 0
  })
  const [bulkBillData, setBulkBillData] = useState({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear()
  })

  const { generateBill, generateBulkBills, isLoading } = useBillingStore()

  const handleSingleBillGeneration = async () => {
    try {
      await generateBill(
        singleBillData.branchId,
        singleBillData.month,
        singleBillData.year,
        singleBillData.baseFee
      )
      onOpenChange(false)
      // Reset form
      setSingleBillData({
        branchId: '',
        month: new Date().getMonth() + 1,
        year: new Date().getFullYear(),
        baseFee: 0
      })
    } catch (error) {
      console.error('Failed to generate bill:', error)
    }
  }

  const handleBulkBillGeneration = async () => {
    try {
      await generateBulkBills(bulkBillData.month, bulkBillData.year)
      onOpenChange(false)
      // Reset form
      setBulkBillData({
        month: new Date().getMonth() + 1,
        year: new Date().getFullYear()
      })
    } catch (error) {
      console.error('Failed to generate bulk bills:', error)
    }
  }

  const getMonthOptions = () => [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' },
  ]

  const getCurrentYear = () => new Date().getFullYear()
  const getYearOptions = () => {
    const currentYear = getCurrentYear()
    const years = []
    for (let i = currentYear; i >= currentYear - 2; i--) {
      years.push(i)
    }
    return years
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Generate Bills</DialogTitle>
          <DialogDescription>
            Generate monthly bills for branches based on their commission and subscription fees.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="single" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Single Bill</span>
            </TabsTrigger>
            <TabsTrigger value="bulk" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Bulk Bills</span>
            </TabsTrigger>
          </TabsList>

          {/* Single Bill Generation */}
          <TabsContent value="single" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Generate Single Bill</CardTitle>
                <CardDescription>
                  Generate a bill for a specific branch and billing period.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="branch">Branch</Label>
                  <Select
                    value={singleBillData.branchId}
                    onValueChange={(value) => setSingleBillData(prev => ({ ...prev, branchId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select branch" />
                    </SelectTrigger>
                    <SelectContent>
                      {/* TODO: Load branches from API */}
                      <SelectItem value="branch1">Main Branch</SelectItem>
                      <SelectItem value="branch2">Secondary Branch</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="month">Month</Label>
                    <Select
                      value={singleBillData.month.toString()}
                      onValueChange={(value) => setSingleBillData(prev => ({ ...prev, month: parseInt(value) }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {getMonthOptions().map((month) => (
                          <SelectItem key={month.value} value={month.value.toString()}>
                            {month.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="year">Year</Label>
                    <Select
                      value={singleBillData.year.toString()}
                      onValueChange={(value) => setSingleBillData(prev => ({ ...prev, year: parseInt(value) }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {getYearOptions().map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="baseFee">Base Fee (Optional)</Label>
                  <Input
                    id="baseFee"
                    type="number"
                    placeholder="Enter base subscription fee"
                    value={singleBillData.baseFee}
                    onChange={(e) => setSingleBillData(prev => ({ ...prev, baseFee: parseFloat(e.target.value) || 0 }))}
                  />
                </div>
              </CardContent>
            </Card>

            <DialogFooter>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleSingleBillGeneration} 
                disabled={isLoading || !singleBillData.branchId}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <FileText className="h-4 w-4 mr-2" />
                )}
                Generate Bill
              </Button>
            </DialogFooter>
          </TabsContent>

          {/* Bulk Bill Generation */}
          <TabsContent value="bulk" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Generate Bulk Bills</CardTitle>
                <CardDescription>
                  Generate bills for all active branches for a specific billing period.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="bulkMonth">Month</Label>
                    <Select
                      value={bulkBillData.month.toString()}
                      onValueChange={(value) => setBulkBillData(prev => ({ ...prev, month: parseInt(value) }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {getMonthOptions().map((month) => (
                          <SelectItem key={month.value} value={month.value.toString()}>
                            {month.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="bulkYear">Year</Label>
                    <Select
                      value={bulkBillData.year.toString()}
                      onValueChange={(value) => setBulkBillData(prev => ({ ...prev, year: parseInt(value) }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {getYearOptions().map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    <strong>Note:</strong> This will generate bills for all active branches. 
                    Existing bills for the selected period will be skipped.
                  </p>
                </div>
              </CardContent>
            </Card>

            <DialogFooter>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleBulkBillGeneration} 
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Users className="h-4 w-4 mr-2" />
                )}
                Generate Bulk Bills
              </Button>
            </DialogFooter>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
