'use client'

import { useState } from 'react'
import { useBillingStore } from '@/stores/billing/useBillingStore'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { BillDetailsDialog } from './BillDetailsDialog'
import { PaymentDialog } from './PaymentDialog'
import { MoreHorizontal, Eye, Send, CreditCard, Download, AlertTriangle } from 'lucide-react'

interface BillListItemProps {
  bill: any
}

export function BillListItem({ bill }: BillListItemProps) {
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  
  const { updateBillStatus, setSelectedBill } = useBillingStore()

  const formatCurrency = (amount: number, currency = 'INR') => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      case 'viewed':
        return 'bg-purple-100 text-purple-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const isOverdue = () => {
    const dueDate = new Date(bill.dates.dueDate)
    const today = new Date()
    return dueDate < today && bill.status !== 'paid'
  }

  const handleViewDetails = () => {
    setSelectedBill(bill)
    setShowDetailsDialog(true)
  }

  const handleMarkAsSent = async () => {
    try {
      await updateBillStatus(bill.id, 'sent')
    } catch (error) {
      console.error('Failed to mark bill as sent:', error)
    }
  }

  const handleMarkAsViewed = async () => {
    try {
      await updateBillStatus(bill.id, 'viewed')
    } catch (error) {
      console.error('Failed to mark bill as viewed:', error)
    }
  }

  const handleRecordPayment = () => {
    setSelectedBill(bill)
    setShowPaymentDialog(true)
  }

  return (
    <>
      <Card className="hover:shadow-sm transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            {/* Bill Info */}
            <div className="flex items-center space-x-4 flex-1">
              {/* Bill Number and Branch */}
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">
                    {bill.billNumber.split('-')[1]?.slice(-2) || 'B'}
                  </span>
                </div>
                <div>
                  <h3 className="font-semibold">{bill.billNumber}</h3>
                  <p className="text-sm text-gray-500">
                    {typeof bill.branch === 'object' ? bill.branch.name : 'Branch'}
                  </p>
                </div>
              </div>

              {/* Billing Period */}
              <div className="hidden md:block">
                <div className="text-sm">
                  <span className="text-gray-500">Period: </span>
                  <span className="font-medium">
                    {new Date(0, bill.billingPeriod.month - 1).toLocaleString('default', { month: 'long' })} {bill.billingPeriod.year}
                  </span>
                </div>
                <div className="text-xs text-gray-500">
                  Due: {new Date(bill.dates.dueDate).toLocaleDateString()}
                  {isOverdue() && (
                    <AlertTriangle className="inline h-3 w-3 ml-1 text-red-500" />
                  )}
                </div>
              </div>

              {/* Amounts */}
              <div className="hidden lg:flex items-center space-x-6">
                <div className="text-center">
                  <div className="text-sm text-gray-500">Base Fee</div>
                  <div className="font-medium">{formatCurrency(bill.amounts.baseFee)}</div>
                </div>
                
                <div className="text-center">
                  <div className="text-sm text-gray-500">Commission</div>
                  <div className="font-medium">{formatCurrency(bill.amounts.commissionAmount)}</div>
                </div>
                
                <div className="text-center">
                  <div className="text-sm text-gray-500">Tax</div>
                  <div className="font-medium">{formatCurrency(bill.amounts.taxAmount)}</div>
                </div>
              </div>

              {/* Total Amount */}
              <div className="text-right">
                <div className="text-lg font-bold text-primary">
                  {formatCurrency(bill.amounts.totalAmount)}
                </div>
                <div className="text-xs text-gray-500">
                  {bill.amounts.currency}
                </div>
              </div>
            </div>

            {/* Status and Actions */}
            <div className="flex items-center space-x-3">
              <Badge className={`${getStatusColor(bill.status)} capitalize`}>
                {bill.status}
              </Badge>

              {/* Quick Actions */}
              <div className="flex items-center space-x-1">
                {bill.status === 'pending' && (
                  <Button variant="outline" size="sm" onClick={handleMarkAsSent}>
                    <Send className="h-3 w-3 mr-1" />
                    Send
                  </Button>
                )}
                
                {(bill.status === 'sent' || bill.status === 'viewed') && (
                  <Button variant="outline" size="sm" onClick={handleRecordPayment}>
                    <CreditCard className="h-3 w-3 mr-1" />
                    Payment
                  </Button>
                )}
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleViewDetails}>
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </DropdownMenuItem>
                  
                  {bill.status === 'pending' && (
                    <DropdownMenuItem onClick={handleMarkAsSent}>
                      <Send className="h-4 w-4 mr-2" />
                      Mark as Sent
                    </DropdownMenuItem>
                  )}
                  
                  {bill.status === 'sent' && (
                    <DropdownMenuItem onClick={handleMarkAsViewed}>
                      <Eye className="h-4 w-4 mr-2" />
                      Mark as Viewed
                    </DropdownMenuItem>
                  )}
                  
                  {(bill.status === 'sent' || bill.status === 'viewed') && (
                    <DropdownMenuItem onClick={handleRecordPayment}>
                      <CreditCard className="h-4 w-4 mr-2" />
                      Record Payment
                    </DropdownMenuItem>
                  )}
                  
                  <DropdownMenuItem>
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Mobile Details */}
          <div className="md:hidden mt-3 pt-3 border-t border-gray-100">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-500">Period:</div>
                <div className="font-medium">
                  {new Date(0, bill.billingPeriod.month - 1).toLocaleString('default', { month: 'long' })} {bill.billingPeriod.year}
                </div>
              </div>
              
              <div>
                <div className="text-sm text-gray-500">Due Date:</div>
                <div className="font-medium flex items-center">
                  {new Date(bill.dates.dueDate).toLocaleDateString()}
                  {isOverdue() && (
                    <AlertTriangle className="h-3 w-3 ml-1 text-red-500" />
                  )}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mt-3">
              <div>
                <div className="text-sm text-gray-500">Base Fee:</div>
                <div className="font-medium">{formatCurrency(bill.amounts.baseFee)}</div>
              </div>
              
              <div>
                <div className="text-sm text-gray-500">Commission:</div>
                <div className="font-medium">{formatCurrency(bill.amounts.commissionAmount)}</div>
              </div>
              
              <div>
                <div className="text-sm text-gray-500">Tax:</div>
                <div className="font-medium">{formatCurrency(bill.amounts.taxAmount)}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dialogs */}
      <BillDetailsDialog 
        open={showDetailsDialog} 
        onOpenChange={setShowDetailsDialog}
      />
      
      <PaymentDialog 
        open={showPaymentDialog} 
        onOpenChange={setShowPaymentDialog}
      />
    </>
  )
}
