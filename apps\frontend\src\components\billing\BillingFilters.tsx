'use client'

import { useState } from 'react'
import { useBillingStore } from '@/stores/billing/useBillingStore'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Filter, X, Calendar } from 'lucide-react'

interface BillingFiltersProps {
  type: 'bills' | 'commission'
}

export function BillingFilters({ type }: BillingFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const {
    filters,
    setFilters,
    resetFilters,
    fetchBills,
    fetchCoursePurchases
  } = useBillingStore()

  const handleStatusChange = (value: string) => {
    setFilters({ status: value === 'all' ? undefined : value })
    
    if (type === 'bills') {
      fetchBills()
    } else {
      fetchCoursePurchases()
    }
  }

  const handleMonthChange = (value: string) => {
    setFilters({ month: value === 'all' ? undefined : parseInt(value) })
    
    if (type === 'bills') {
      fetchBills()
    } else {
      fetchCoursePurchases()
    }
  }

  const handleYearChange = (value: string) => {
    setFilters({ year: value === 'all' ? undefined : parseInt(value) })
    
    if (type === 'bills') {
      fetchBills()
    } else {
      fetchCoursePurchases()
    }
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.status) count++
    if (filters.month) count++
    if (filters.year) count++
    if (filters.branchId) count++
    return count
  }

  const getCurrentYear = () => new Date().getFullYear()
  const getYearOptions = () => {
    const currentYear = getCurrentYear()
    const years = []
    for (let i = currentYear; i >= currentYear - 5; i--) {
      years.push(i)
    }
    return years
  }

  const getMonthOptions = () => [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' },
  ]

  const getStatusOptions = () => {
    if (type === 'bills') {
      return [
        { value: 'all', label: 'All Status' },
        { value: 'pending', label: 'Pending' },
        { value: 'sent', label: 'Sent' },
        { value: 'viewed', label: 'Viewed' },
        { value: 'paid', label: 'Paid' },
        { value: 'overdue', label: 'Overdue' },
        { value: 'cancelled', label: 'Cancelled' },
      ]
    } else {
      return [
        { value: 'all', label: 'All Status' },
        { value: 'completed', label: 'Completed' },
        { value: 'pending', label: 'Pending' },
        { value: 'failed', label: 'Failed' },
        { value: 'refunded', label: 'Refunded' },
      ]
    }
  }

  return (
    <div className="space-y-4">
      {/* Basic Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="flex flex-1 gap-4 items-center">
          {/* Status Filter */}
          <Select value={filters.status || 'all'} onValueChange={handleStatusChange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {getStatusOptions().map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Month Filter */}
          <Select value={filters.month?.toString() || 'all'} onValueChange={handleMonthChange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select month" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Months</SelectItem>
              {getMonthOptions().map((month) => (
                <SelectItem key={month.value} value={month.value.toString()}>
                  {month.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Year Filter */}
          <Select value={filters.year?.toString() || 'all'} onValueChange={handleYearChange}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Select year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Years</SelectItem>
              {getYearOptions().map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Advanced Filters Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="relative"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {getActiveFiltersCount() > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
              >
                {getActiveFiltersCount()}
              </Badge>
            )}
          </Button>
        </div>

        {/* Quick Date Filters */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const currentDate = new Date()
              setFilters({
                month: currentDate.getMonth() + 1,
                year: currentDate.getFullYear()
              })
              if (type === 'bills') {
                fetchBills()
              } else {
                fetchCoursePurchases()
              }
            }}
          >
            <Calendar className="h-4 w-4 mr-2" />
            This Month
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const lastMonth = new Date()
              lastMonth.setMonth(lastMonth.getMonth() - 1)
              setFilters({
                month: lastMonth.getMonth() + 1,
                year: lastMonth.getFullYear()
              })
              if (type === 'bills') {
                fetchBills()
              } else {
                fetchCoursePurchases()
              }
            }}
          >
            Last Month
          </Button>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="p-4 border rounded-lg bg-muted/50 space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Advanced Filters</h4>
            <Button variant="ghost" size="sm" onClick={resetFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {type === 'bills' && (
              <>
                <div>
                  <label className="text-sm font-medium mb-2 block">Amount Range</label>
                  <div className="flex space-x-2">
                    <input 
                      type="number" 
                      placeholder="Min amount" 
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    />
                    <input 
                      type="number" 
                      placeholder="Max amount" 
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Due Date Range</label>
                  <div className="flex space-x-2">
                    <input 
                      type="date" 
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    />
                    <input 
                      type="date" 
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    />
                  </div>
                </div>
              </>
            )}

            {type === 'commission' && (
              <>
                <div>
                  <label className="text-sm font-medium mb-2 block">Commission Range</label>
                  <div className="flex space-x-2">
                    <input 
                      type="number" 
                      placeholder="Min %" 
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    />
                    <input 
                      type="number" 
                      placeholder="Max %" 
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Purchase Amount</label>
                  <div className="flex space-x-2">
                    <input 
                      type="number" 
                      placeholder="Min amount" 
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    />
                    <input 
                      type="number" 
                      placeholder="Max amount" 
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    />
                  </div>
                </div>
              </>
            )}

            <div>
              <label className="text-sm font-medium mb-2 block">Currency</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Currencies</SelectItem>
                  <SelectItem value="INR">Indian Rupee (₹)</SelectItem>
                  <SelectItem value="USD">US Dollar ($)</SelectItem>
                  <SelectItem value="EUR">Euro (€)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {getActiveFiltersCount() > 0 && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-500">Active filters:</span>
          {filters.status && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>Status: {filters.status}</span>
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => {
                  setFilters({ status: undefined })
                  if (type === 'bills') fetchBills()
                  else fetchCoursePurchases()
                }}
              />
            </Badge>
          )}
          {filters.month && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>Month: {getMonthOptions().find(m => m.value === filters.month)?.label}</span>
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => {
                  setFilters({ month: undefined })
                  if (type === 'bills') fetchBills()
                  else fetchCoursePurchases()
                }}
              />
            </Badge>
          )}
          {filters.year && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>Year: {filters.year}</span>
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => {
                  setFilters({ year: undefined })
                  if (type === 'bills') fetchBills()
                  else fetchCoursePurchases()
                }}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
