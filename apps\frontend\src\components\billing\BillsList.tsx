'use client'

import { useBillingStore } from '@/stores/billing/useBillingStore'
import { BillCard } from './BillCard'
import { BillListItem } from './BillListItem'
import { BillingPagination } from './BillingPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { FileText } from 'lucide-react'

export function BillsList() {
  const {
    bills,
    billsPagination,
    isLoading,
    fetchBills
  } = useBillingStore()

  const handlePageChange = (page: number) => {
    fetchBills(page)
  }

  if (isLoading && bills.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (bills.length === 0) {
    return (
      <EmptyState
        icon={FileText}
        title="No bills found"
        description="No bills match your current filters. Try adjusting your search criteria or generate new bills."
        action={{
          label: "Generate Bill",
          onClick: () => {
            // TODO: Open generate dialog
          }
        }}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Bills List */}
      <div className="space-y-2">
        {bills.map((bill) => (
          <BillListItem
            key={bill.id}
            bill={bill}
          />
        ))}
      </div>

      {/* Pagination */}
      <BillingPagination
        pagination={billsPagination}
        onPageChange={handlePageChange}
      />
    </div>
  )
}
