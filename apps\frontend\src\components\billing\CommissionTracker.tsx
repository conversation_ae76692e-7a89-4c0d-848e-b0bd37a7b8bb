'use client'

import { useBillingStore } from '@/stores/billing/useBillingStore'
import { BillingPagination } from './BillingPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TrendingUp } from 'lucide-react'

export function CommissionTracker() {
  const {
    coursePurchases,
    purchasesPagination,
    isLoading,
    fetchCoursePurchases
  } = useBillingStore()

  const handlePageChange = (page: number) => {
    fetchCoursePurchases(page)
  }

  const formatCurrency = (amount: number, currency = 'INR') => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'refunded':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading && coursePurchases.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (coursePurchases.length === 0) {
    return (
      <EmptyState
        icon={TrendingUp}
        title="No course purchases found"
        description="No course purchases match your current filters. Commission tracking will appear here once students start purchasing courses."
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Commission Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-gray-500">Total Revenue</div>
            <div className="text-2xl font-bold">
              {formatCurrency(
                coursePurchases.reduce((sum, p) => sum + p.purchaseDetails.finalAmount, 0)
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-gray-500">Total Commission</div>
            <div className="text-2xl font-bold text-primary">
              {formatCurrency(
                coursePurchases.reduce((sum, p) => sum + p.commissionDetails.commissionAmount, 0)
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-gray-500">Purchases</div>
            <div className="text-2xl font-bold">
              {coursePurchases.length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-gray-500">Avg. Commission Rate</div>
            <div className="text-2xl font-bold">
              {coursePurchases.length > 0 
                ? (coursePurchases.reduce((sum, p) => sum + p.commissionDetails.commissionRate, 0) / coursePurchases.length).toFixed(1)
                : 0}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Purchases List */}
      <div className="space-y-2">
        {coursePurchases.map((purchase) => (
          <Card key={purchase.id} className="hover:shadow-sm transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                {/* Purchase Info */}
                <div className="flex items-center space-x-4 flex-1">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-5 w-5 text-white" />
                  </div>
                  
                  <div>
                    <h3 className="font-semibold">{purchase.course.title}</h3>
                    <p className="text-sm text-gray-500">
                      {purchase.student.firstName} {purchase.student.lastName}
                    </p>
                  </div>

                  {/* Purchase Details */}
                  <div className="hidden md:flex items-center space-x-6">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Purchase Amount</div>
                      <div className="font-medium">{formatCurrency(purchase.purchaseDetails.finalAmount)}</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Commission Rate</div>
                      <div className="font-medium">{purchase.commissionDetails.commissionRate}%</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Commission Amount</div>
                      <div className="font-medium text-primary">{formatCurrency(purchase.commissionDetails.commissionAmount)}</div>
                    </div>
                  </div>

                  {/* Branch Info */}
                  <div className="hidden lg:block">
                    <div className="text-sm text-gray-500">Branch:</div>
                    <div className="font-medium">{purchase.branch.name}</div>
                  </div>
                </div>

                {/* Status and Date */}
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <Badge className={`${getPaymentStatusColor(purchase.paymentDetails.paymentStatus)} capitalize`}>
                      {purchase.paymentDetails.paymentStatus}
                    </Badge>
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(purchase.purchaseDate).toLocaleDateString()}
                    </div>
                  </div>

                  {purchase.billingInfo.addedToBill && (
                    <Badge variant="outline" className="text-xs">
                      Billed
                    </Badge>
                  )}
                </div>
              </div>

              {/* Mobile Details */}
              <div className="md:hidden mt-3 pt-3 border-t border-gray-100">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <div className="text-sm text-gray-500">Amount:</div>
                    <div className="font-medium">{formatCurrency(purchase.purchaseDetails.finalAmount)}</div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-gray-500">Rate:</div>
                    <div className="font-medium">{purchase.commissionDetails.commissionRate}%</div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-gray-500">Commission:</div>
                    <div className="font-medium text-primary">{formatCurrency(purchase.commissionDetails.commissionAmount)}</div>
                  </div>
                </div>

                <div className="mt-2">
                  <div className="text-sm text-gray-500">Branch:</div>
                  <div className="font-medium">{purchase.branch.name}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      <BillingPagination
        pagination={purchasesPagination}
        onPageChange={handlePageChange}
      />
    </div>
  )
}
