'use client'

import { useState } from 'react'
import { useBillingStore } from '@/stores/billing/useBillingStore'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, CreditCard, DollarSign } from 'lucide-react'

interface PaymentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function PaymentDialog({ open, onOpenChange }: PaymentDialogProps) {
  const [paymentData, setPaymentData] = useState({
    paymentMethod: '',
    transactionId: '',
    paidAmount: 0,
    notes: ''
  })

  const { selectedBill, updateBillStatus, isLoading } = useBillingStore()

  const formatCurrency = (amount: number, currency = 'INR') => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const handleRecordPayment = async () => {
    if (!selectedBill) return

    try {
      const paymentDetails = {
        paymentMethod: paymentData.paymentMethod,
        transactionId: paymentData.transactionId,
        paidBy: 'current_user_id', // This should come from auth context
        paymentGatewayResponse: {
          amount: paymentData.paidAmount,
          notes: paymentData.notes,
          recordedAt: new Date().toISOString()
        }
      }

      await updateBillStatus(selectedBill.id, 'paid', paymentDetails)
      onOpenChange(false)
      
      // Reset form
      setPaymentData({
        paymentMethod: '',
        transactionId: '',
        paidAmount: 0,
        notes: ''
      })
    } catch (error) {
      console.error('Failed to record payment:', error)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setPaymentData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (!selectedBill) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>Record Payment</span>
          </DialogTitle>
          <DialogDescription>
            Record payment details for bill {selectedBill.billNumber}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Bill Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4" />
                <span>Bill Summary</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Bill Number</label>
                  <p className="font-mono">{selectedBill.billNumber}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Branch</label>
                  <p className="font-medium">
                    {typeof selectedBill.branch === 'object' ? selectedBill.branch.name : 'Branch'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Total Amount</label>
                  <p className="text-lg font-bold text-primary">
                    {formatCurrency(selectedBill.amounts.totalAmount)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Due Date</label>
                  <p className="font-medium">
                    {new Date(selectedBill.dates.dueDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Details Form */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Details</CardTitle>
              <CardDescription>
                Enter the payment information to mark this bill as paid
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select
                  value={paymentData.paymentMethod}
                  onValueChange={(value) => handleInputChange('paymentMethod', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="razorpay">Razorpay</SelectItem>
                    <SelectItem value="stripe">Stripe</SelectItem>
                    <SelectItem value="paypal">PayPal</SelectItem>
                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                    <SelectItem value="upi">UPI</SelectItem>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="cheque">Cheque</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="transactionId">Transaction ID</Label>
                <Input
                  id="transactionId"
                  placeholder="Enter transaction/reference ID"
                  value={paymentData.transactionId}
                  onChange={(e) => handleInputChange('transactionId', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="paidAmount">Paid Amount</Label>
                <Input
                  id="paidAmount"
                  type="number"
                  placeholder="Enter paid amount"
                  value={paymentData.paidAmount || selectedBill.amounts.totalAmount}
                  onChange={(e) => handleInputChange('paidAmount', parseFloat(e.target.value) || 0)}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Bill amount: {formatCurrency(selectedBill.amounts.totalAmount)}
                </p>
              </div>

              <div>
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Add any additional notes about the payment"
                  value={paymentData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Payment Verification */}
          {paymentData.paidAmount > 0 && paymentData.paidAmount !== selectedBill.amounts.totalAmount && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <p className="text-sm text-yellow-800">
                    <strong>Amount Mismatch:</strong> Paid amount ({formatCurrency(paymentData.paidAmount)}) 
                    differs from bill amount ({formatCurrency(selectedBill.amounts.totalAmount)}).
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleRecordPayment} 
            disabled={isLoading || !paymentData.paymentMethod || !paymentData.transactionId}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <CreditCard className="h-4 w-4 mr-2" />
            )}
            Record Payment
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
