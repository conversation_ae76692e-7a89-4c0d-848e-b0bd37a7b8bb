'use client'

import React, { useEffect, useState } from 'react'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Loader2 } from 'lucide-react'
import { api } from '@/lib/api'

interface Country {
  id: number
  name: string
  code: string
}

interface State {
  id: number
  name: string
  code: string
  country: number
}

interface District {
  id: number
  name: string
  code: string
  state: number
}

interface CascadingLocationDropdownProps {
  // Values
  countryValue?: string
  stateValue?: string
  districtValue?: string
  
  // Change handlers
  onCountryChange: (value: string) => void
  onStateChange: (value: string) => void
  onDistrictChange: (value: string) => void
  
  // Validation errors
  countryError?: string
  stateError?: string
  districtError?: string
  
  // Touched states
  countryTouched?: boolean
  stateTouched?: boolean
  districtTouched?: boolean
  
  // Required flags
  countryRequired?: boolean
  stateRequired?: boolean
  districtRequired?: boolean
  
  // Disabled states
  disabled?: boolean
  
  // Labels
  countryLabel?: string
  stateLabel?: string
  districtLabel?: string
}

export function CascadingLocationDropdown({
  countryValue,
  stateValue,
  districtValue,
  onCountryChange,
  onStateChange,
  onDistrictChange,
  countryError,
  stateError,
  districtError,
  countryTouched,
  stateTouched,
  districtTouched,
  countryRequired = false,
  stateRequired = false,
  districtRequired = false,
  disabled = false,
  countryLabel = "Country",
  stateLabel = "State",
  districtLabel = "District"
}: CascadingLocationDropdownProps) {
  
  // Data states
  const [countries, setCountries] = useState<Country[]>([])
  const [states, setStates] = useState<State[]>([])
  const [districts, setDistricts] = useState<District[]>([])
  
  // Loading states
  const [loadingCountries, setLoadingCountries] = useState(false)
  const [loadingStates, setLoadingStates] = useState(false)
  const [loadingDistricts, setLoadingDistricts] = useState(false)

  // Fetch countries on component mount
  useEffect(() => {
    fetchCountries()
  }, [])

  // Debug location values
  useEffect(() => {
    console.log('CascadingLocationDropdown Debug:')
    console.log('- countryValue:', countryValue, 'Type:', typeof countryValue)
    console.log('- stateValue:', stateValue, 'Type:', typeof stateValue)
    console.log('- districtValue:', districtValue, 'Type:', typeof districtValue)
    console.log('- countries loaded:', countries.length)
    console.log('- states loaded:', states.length)
    console.log('- districts loaded:', districts.length)

    // Check if values match loaded options
    if (countryValue && countries.length > 0) {
      const matchingCountry = countries.find(c => String(c.id) === countryValue)
      console.log('- Country match:', matchingCountry ? `${matchingCountry.name} (${matchingCountry.id})` : 'NOT FOUND')
    }

    if (stateValue && states.length > 0) {
      const matchingState = states.find(s => String(s.id) === stateValue)
      console.log('- State match:', matchingState ? `${matchingState.name} (${matchingState.id})` : 'NOT FOUND')
    }

    if (districtValue && districts.length > 0) {
      const matchingDistrict = districts.find(d => String(d.id) === districtValue)
      console.log('- District match:', matchingDistrict ? `${matchingDistrict.name} (${matchingDistrict.id})` : 'NOT FOUND')
    }
  }, [countryValue, stateValue, districtValue, countries, states, districts])

  // Fetch states when country changes
  useEffect(() => {
    if (countryValue && countryValue !== 'select-country') {
      fetchStates(countryValue)
    } else {
      setStates([])
      setDistricts([])
    }
  }, [countryValue])

  // Fetch districts when state changes
  useEffect(() => {
    if (stateValue && stateValue !== 'select-state') {
      fetchDistricts(stateValue)
    } else {
      setDistricts([])
    }
  }, [stateValue])

  // Custom handlers that manage cascading
  const handleCountryChange = (value: string) => {
    onCountryChange(value)
    // Clear dependent selections
    if (stateValue !== 'select-state') {
      onStateChange('select-state')
    }
    if (districtValue !== 'select-district') {
      onDistrictChange('select-district')
    }
  }

  const handleStateChange = (value: string) => {
    onStateChange(value)
    // Clear dependent selection
    if (districtValue !== 'select-district') {
      onDistrictChange('select-district')
    }
  }

  const handleDistrictChange = (value: string) => {
    onDistrictChange(value)
  }

  const fetchCountries = async () => {
    setLoadingCountries(true)
    try {
      const response = await api.get('/api/locations/countries?page=1&limit=300&isActive=true&sort=name')
      if (response.success) {
        setCountries(response.countries || [])
      }
    } catch (error) {
      console.error('Failed to fetch countries:', error)
    } finally {
      setLoadingCountries(false)
    }
  }

  const fetchStates = async (countryId: string) => {
    setLoadingStates(true)
    try {
      const response = await api.get(`/api/locations/states?countryId=${countryId}&page=1&limit=100&isActive=true&sort=name`)
      if (response.success) {
        setStates(response.states || [])
      }
    } catch (error) {
      console.error('Failed to fetch states:', error)
    } finally {
      setLoadingStates(false)
    }
  }

  const fetchDistricts = async (stateId: string) => {
    setLoadingDistricts(true)
    try {
      const response = await api.get(`/api/locations/districts?stateId=${stateId}&page=1&limit=200&isActive=true&sort=name`)
      if (response.success) {
        setDistricts(response.districts || [])
      }
    } catch (error) {
      console.error('Failed to fetch districts:', error)
    } finally {
      setLoadingDistricts(false)
    }
  }

  return (
    <div className="space-y-4">
      {/* Country Dropdown */}
      <div className="space-y-2">
        <Label htmlFor="country">
          {countryLabel} {countryRequired && <span className="text-red-500">*</span>}
        </Label>
        <Select
          value={countryValue || 'select-country'}
          onValueChange={handleCountryChange}
          disabled={disabled || loadingCountries}
        >
          <SelectTrigger className={countryError && countryTouched ? 'border-red-500' : ''}>
            <SelectValue placeholder={loadingCountries ? "Loading countries..." : "Select a country"} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="select-country" disabled>
              {loadingCountries ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Loading countries...
                </div>
              ) : (
                "Select a country"
              )}
            </SelectItem>
            {countries.map((country) => (
              <SelectItem key={country.id} value={String(country.id)}>
                {country.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {countryError && countryTouched && (
          <p className="text-sm text-red-500 flex items-center gap-1">
            <span>{countryError}</span>
          </p>
        )}
      </div>

      {/* State Dropdown */}
      <div className="space-y-2">
        <Label htmlFor="state">
          {stateLabel} {stateRequired && <span className="text-red-500">*</span>}
        </Label>
        <Select
          value={stateValue || 'select-state'}
          onValueChange={handleStateChange}
          disabled={disabled || !countryValue || countryValue === 'select-country' || loadingStates}
        >
          <SelectTrigger className={stateError && stateTouched ? 'border-red-500' : ''}>
            <SelectValue placeholder={
              !countryValue || countryValue === 'select-country' 
                ? "Select country first" 
                : loadingStates 
                  ? "Loading states..." 
                  : "Select a state"
            } />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="select-state" disabled>
              {!countryValue || countryValue === 'select-country' ? (
                "Select country first"
              ) : loadingStates ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Loading states...
                </div>
              ) : (
                "Select a state"
              )}
            </SelectItem>
            {states.map((state) => (
              <SelectItem key={state.id} value={String(state.id)}>
                {state.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {stateError && stateTouched && (
          <p className="text-sm text-red-500 flex items-center gap-1">
            <span>{stateError}</span>
          </p>
        )}
      </div>

      {/* District Dropdown */}
      <div className="space-y-2">
        <Label htmlFor="district">
          {districtLabel} {districtRequired && <span className="text-red-500">*</span>}
        </Label>
        <Select
          value={districtValue || 'select-district'}
          onValueChange={onDistrictChange}
          disabled={disabled || !stateValue || stateValue === 'select-state' || loadingDistricts}
        >
          <SelectTrigger className={districtError && districtTouched ? 'border-red-500' : ''}>
            <SelectValue placeholder={
              !stateValue || stateValue === 'select-state' 
                ? "Select state first" 
                : loadingDistricts 
                  ? "Loading districts..." 
                  : "Select a district"
            } />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="select-district" disabled>
              {!stateValue || stateValue === 'select-state' ? (
                "Select state first"
              ) : loadingDistricts ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Loading districts...
                </div>
              ) : (
                "Select a district"
              )}
            </SelectItem>
            {districts.map((district) => (
              <SelectItem key={district.id} value={String(district.id)}>
                {district.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {districtError && districtTouched && (
          <p className="text-sm text-red-500 flex items-center gap-1">
            <span>{districtError}</span>
          </p>
        )}
      </div>
    </div>
  )
}
