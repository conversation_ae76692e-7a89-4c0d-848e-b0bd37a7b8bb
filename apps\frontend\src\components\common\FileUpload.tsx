import React, { useState, useRef } from 'react'
import { Upload, X, FileText, Image, User, Building } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { toast } from 'sonner'
import { 
  fileUploadAPI, 
  UploadType, 
  UploadResult, 
  validateFile,
  getFileUrl 
} from '@/lib/api/file-upload'

interface FileUploadProps {
  uploadType: UploadType
  onUploadSuccess?: (result: UploadResult) => void
  onUploadError?: (error: Error) => void
  updateUserField?: string // e.g., 'avatar' to update user.avatar
  folder?: string
  className?: string
  children?: React.ReactNode
  accept?: string
  maxSize?: number
  multiple?: boolean
}

const FileUpload: React.FC<FileUploadProps> = ({
  uploadType,
  onUploadSuccess,
  onUploadError,
  updateUserField,
  folder,
  className = '',
  children,
  accept,
  maxSize,
  multiple = false
}) => {
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Get upload type configuration
  const getUploadConfig = () => {
    const configs = {
      avatar: {
        accept: 'image/*',
        maxSize: 5 * 1024 * 1024, // 5MB
        icon: User,
        title: 'Upload Avatar',
        description: 'JPG, PNG, GIF up to 5MB'
      },
      course_thumbnail: {
        accept: 'image/*',
        maxSize: 10 * 1024 * 1024, // 10MB
        icon: Image,
        title: 'Upload Course Thumbnail',
        description: 'JPG, PNG, GIF up to 10MB'
      },
      institute_logo: {
        accept: 'image/*',
        maxSize: 5 * 1024 * 1024, // 5MB
        icon: Building,
        title: 'Upload Institute Logo',
        description: 'JPG, PNG, GIF, SVG up to 5MB'
      },
      document: {
        accept: '.pdf,.doc,.docx',
        maxSize: 50 * 1024 * 1024, // 50MB
        icon: FileText,
        title: 'Upload Document',
        description: 'PDF, DOC, DOCX up to 50MB'
      },
      general: {
        accept: '*',
        maxSize: 25 * 1024 * 1024, // 25MB
        icon: Upload,
        title: 'Upload File',
        description: 'Any file up to 25MB'
      }
    }
    return configs[uploadType] || configs.general
  }

  const config = getUploadConfig()
  const Icon = config.icon

  const handleFiles = async (files: FileList) => {
    if (files.length === 0) return

    const file = files[0] // For now, handle single file
    
    // Validate file
    const validation = validateFile(file, uploadType)
    if (!validation.valid) {
      toast.error(validation.message || 'Invalid file')
      onUploadError?.(new Error(validation.message || 'Invalid file'))
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      console.log('🚀 Uploading file:', {
        name: file.name,
        size: file.size,
        type: file.type,
        uploadType
      })

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      const result = await fileUploadAPI.uploadFile(file, {
        uploadType,
        updateUserField,
        folder,
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (result.success) {
        toast.success(result.message || 'File uploaded successfully')
        onUploadSuccess?.(result)
        
        console.log('✅ Upload successful:', {
          mediaId: result.media?.id,
          url: result.media?.url,
          sizes: Object.keys(result.media?.sizes || {})
        })
      } else {
        throw new Error(result.message || 'Upload failed')
      }
    } catch (error) {
      console.error('❌ Upload error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      toast.error(errorMessage)
      onUploadError?.(error instanceof Error ? error : new Error(errorMessage))
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files)
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files)
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  // Custom children or default upload area
  if (children) {
    return (
      <div className={className}>
        <div
          onClick={openFileDialog}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          className="cursor-pointer"
        >
          {children}
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept={accept || config.accept}
          onChange={handleFileInputChange}
          multiple={multiple}
          className="hidden"
          disabled={isUploading}
        />
      </div>
    )
  }

  // Default upload area
  return (
    <Card className={`${className} ${dragActive ? 'border-blue-500 bg-blue-50' : ''}`}>
      <CardContent className="p-6">
        <div
          onClick={openFileDialog}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          className={`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all
            ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
            ${isUploading ? 'pointer-events-none opacity-50' : ''}
          `}
        >
          <div className="flex flex-col items-center space-y-4">
            <div className="p-3 bg-gray-100 rounded-full">
              <Icon className="w-8 h-8 text-gray-600" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {isUploading ? 'Uploading...' : config.title}
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                {isUploading ? `${uploadProgress}% complete` : config.description}
              </p>
            </div>

            {isUploading && (
              <div className="w-full max-w-xs">
                <div className="bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
              </div>
            )}

            {!isUploading && (
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-2" />
                Choose File
              </Button>
            )}
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept={accept || config.accept}
          onChange={handleFileInputChange}
          multiple={multiple}
          className="hidden"
          disabled={isUploading}
        />
      </CardContent>
    </Card>
  )
}

export default FileUpload

// Convenience components for specific upload types
export const AvatarUpload: React.FC<Omit<FileUploadProps, 'uploadType'>> = (props) => (
  <FileUpload {...props} uploadType="avatar" updateUserField="avatar" />
)

export const CourseThumbnailUpload: React.FC<Omit<FileUploadProps, 'uploadType'>> = (props) => (
  <FileUpload {...props} uploadType="course_thumbnail" />
)

export const InstituteLogoUpload: React.FC<Omit<FileUploadProps, 'uploadType'>> = (props) => (
  <FileUpload {...props} uploadType="institute_logo" />
)

export const DocumentUpload: React.FC<Omit<FileUploadProps, 'uploadType'>> = (props) => (
  <FileUpload {...props} uploadType="document" />
)
