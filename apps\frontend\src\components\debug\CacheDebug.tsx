'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { InstituteCache } from '@/utils/instituteCache'

export function CacheDebug() {
  const [stats, setStats] = useState({ totalEntries: 0, domains: [] })
  const [currentDomain, setCurrentDomain] = useState('')

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setCurrentDomain(window.location.host)
      updateStats()
    }
  }, [])

  const updateStats = () => {
    const newStats = InstituteCache.getStats()
    setStats(newStats)
  }

  const clearCache = () => {
    InstituteCache.clearAll()
    updateStats()
  }

  const clearCurrentDomain = () => {
    InstituteCache.remove(currentDomain)
    updateStats()
  }

  const getCachedData = () => {
    return InstituteCache.getFresh(currentDomain)
  }

  const cachedData = getCachedData()

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Institute Cache Debug</CardTitle>
        <CardDescription>
          Monitor and manage cached institute data
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Domain */}
        <div>
          <h3 className="font-semibold mb-2">Current Domain</h3>
          <Badge variant="outline">{currentDomain}</Badge>
        </div>

        {/* Cache Stats */}
        <div>
          <h3 className="font-semibold mb-2">Cache Statistics</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-muted p-3 rounded">
              <div className="text-2xl font-bold">{stats.totalEntries}</div>
              <div className="text-sm text-muted-foreground">Total Entries</div>
            </div>
            <div className="bg-muted p-3 rounded">
              <div className="text-2xl font-bold">{stats.domains.length}</div>
              <div className="text-sm text-muted-foreground">Unique Domains</div>
            </div>
          </div>
        </div>

        {/* Cached Domains */}
        {stats.domains.length > 0 && (
          <div>
            <h3 className="font-semibold mb-2">Cached Domains</h3>
            <div className="flex flex-wrap gap-2">
              {stats.domains.map(domain => (
                <Badge 
                  key={domain} 
                  variant={domain === currentDomain ? "default" : "secondary"}
                >
                  {domain}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Current Domain Cache */}
        {cachedData && (
          <div>
            <h3 className="font-semibold mb-2">Current Domain Cache</h3>
            <div className="bg-muted p-3 rounded">
              <div className="text-sm space-y-1">
                <div><strong>Institute:</strong> {cachedData.institute.name}</div>
                <div><strong>Theme:</strong> {cachedData.theme?.name || 'None'}</div>
                <div><strong>Cached:</strong> {new Date(cachedData.timestamp).toLocaleString()}</div>
                <div><strong>Age:</strong> {Math.round((Date.now() - cachedData.timestamp) / 1000)}s</div>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2">
          <Button onClick={updateStats} variant="outline" size="sm">
            Refresh Stats
          </Button>
          <Button onClick={clearCurrentDomain} variant="outline" size="sm">
            Clear Current Domain
          </Button>
          <Button onClick={clearCache} variant="destructive" size="sm">
            Clear All Cache
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
