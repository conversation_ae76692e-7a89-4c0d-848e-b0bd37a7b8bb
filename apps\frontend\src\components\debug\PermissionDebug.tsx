'use client'

import { usePermissions } from '@/contexts/PermissionContext'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { canAccessNavigation, hasPermission } from '@/utils/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

export function PermissionDebug() {
  const { user, isAuthenticated } = useAuthStore()
  const { userPermissions, isLoading } = usePermissions()

  if (isLoading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>🔄 Loading Permissions...</CardTitle>
        </CardHeader>
      </Card>
    )
  }

  if (!isAuthenticated || !user) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>❌ Not Authenticated</CardTitle>
          <CardDescription>Please login to view permission debug information</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  // Test permissions
  const testPermissions = [
    'view_dashboard',
    'manage_users',
    'manage_roles',
    'manage_permissions',
    'manage_taxes',
    'manage_locations',
    'manage_themes',
    'view_analytics',
    'system_admin',
    'access_all'
  ]

  // Test navigation items
  const testNavigation = [
    'super-admin',
    'super-admin-dashboard',
    'super-admin-role-permissions',
    'super-admin-users',
    'super-admin-institutes',
    'super-admin-tax',
    'super-admin-locations',
    'super-admin-themes',
    'super-admin-settings'
  ]

  const clearStoredData = () => {
    localStorage.removeItem('user_data')
    localStorage.removeItem('user_permissions')
    localStorage.removeItem('auth_token')
    console.log('🗑️ Cleared all stored data')
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            🔍 Permission Debug Information
            <Button variant="outline" size="sm" onClick={clearStoredData}>
              Clear Stored Data
            </Button>
          </CardTitle>
          <CardDescription>
            Debug information for the permission system with new structure
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* User Information */}
          <div>
            <h3 className="text-lg font-semibold mb-3">👤 User Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><strong>Email:</strong> {user.email}</div>
              <div><strong>Legacy Role:</strong> {user.legacyRole}</div>
              <div><strong>Role Name:</strong> {user.role?.name || 'N/A'}</div>
              <div><strong>Role Code:</strong> {user.role?.code || 'N/A'}</div>
              <div><strong>Role Level:</strong> {user.role?.level || 'N/A'}</div>
              <div><strong>Is Authenticated:</strong> {isAuthenticated ? '✅' : '❌'}</div>
            </div>
          </div>

          {/* Permission Summary */}
          <div>
            <h3 className="text-lg font-semibold mb-3">📊 Permission Summary</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><strong>Processed Role:</strong> {userPermissions.role}</div>
              <div><strong>Permission Level:</strong> {userPermissions.level}</div>
              <div><strong>Total Permissions:</strong> {userPermissions.permissions.length}</div>
              <div><strong>Is Super Admin:</strong> {userPermissions.role === 'super_admin' ? '✅' : '❌'}</div>
            </div>
          </div>

          {/* Raw Permission Data */}
          <div>
            <h3 className="text-lg font-semibold mb-3">🔧 Raw Permission Data</h3>
            <div className="text-sm">
              <div><strong>Backend Permissions Count:</strong> {user.role?.permissions?.length || 0}</div>
              <div className="mt-2">
                <strong>Sample Backend Permissions:</strong>
                <div className="mt-1 space-y-1">
                  {user.role?.permissions?.slice(0, 5).map((perm: any, index: number) => (
                    <div key={index} className="text-xs bg-gray-100 p-2 rounded">
                      {perm.name} ({perm.code}) - {perm.category}/{perm.resource}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Permission Tests */}
          <div>
            <h3 className="text-lg font-semibold mb-3">🧪 Permission Tests</h3>
            <div className="grid grid-cols-2 gap-2">
              {testPermissions.map(permission => {
                const hasAccess = hasPermission(userPermissions, permission)
                return (
                  <div key={permission} className="flex items-center justify-between p-2 border rounded">
                    <span className="text-sm">{permission}</span>
                    <Badge variant={hasAccess ? "default" : "secondary"}>
                      {hasAccess ? '✅' : '❌'}
                    </Badge>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Navigation Tests */}
          <div>
            <h3 className="text-lg font-semibold mb-3">🧭 Navigation Access Tests</h3>
            <div className="grid grid-cols-1 gap-2">
              {testNavigation.map(navId => {
                const hasAccess = canAccessNavigation(userPermissions, navId)
                return (
                  <div key={navId} className="flex items-center justify-between p-2 border rounded">
                    <span className="text-sm">{navId}</span>
                    <Badge variant={hasAccess ? "default" : "secondary"}>
                      {hasAccess ? '✅' : '❌'}
                    </Badge>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Storage Information */}
          <div>
            <h3 className="text-lg font-semibold mb-3">💾 Storage Information</h3>
            <div className="text-sm space-y-2">
              <div><strong>Auth Token:</strong> {localStorage.getItem('auth_token') ? '✅ Present' : '❌ Missing'}</div>
              <div><strong>User Data:</strong> {localStorage.getItem('user_data') ? '✅ Present' : '❌ Missing'}</div>
              <div><strong>User Permissions:</strong> {localStorage.getItem('user_permissions') ? '✅ Present' : '❌ Missing'}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
