'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { usePermissionAwareNavigation } from '@/hooks/usePermissionAwareNavigation'
import { useResponsive } from '@/hooks/useResponsive'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Bug,
  User,
  Navigation,
  Eye,
  EyeOff,
  RefreshCw
} from 'lucide-react'

export default function SidebarDebug() {
  const pathname = usePathname()
  const { user, isAuthenticated } = useAuthStore()
  const { 
    isCollapsed, 
    isMobileOpen, 
    navigationItems: storeNavigationItems,
    userType,
    setNavigationItems
  } = useSidebarStore()
  const { navigationItems: filteredNavigationItems } = usePermissionAwareNavigation()
  const { isMobile, isTablet } = useResponsive()
  
  const [debugInfo, setDebugInfo] = useState<any>({})
  const [showRawData, setShowRawData] = useState(false)

  // Collect debug information
  useEffect(() => {
    // Import and get default navigation for institute admin
    const getDefaultNavigation = async () => {
      try {
        const { getDefaultNavigation } = await import('@/stores/sidebar/useSidebarStore')
        return getDefaultNavigation ? getDefaultNavigation('institute_admin') : []
      } catch (error) {
        console.error('Error importing default navigation:', error)
        return []
      }
    }

    const collectInfo = async () => {
      const defaultNav = await getDefaultNavigation()

      const info = {
        // Authentication
        isAuthenticated,
        user: user ? {
          id: user.id,
          email: user.email,
          legacyRole: user.legacyRole,
          institute: user.institute,
          firstName: user.firstName,
          lastName: user.lastName
        } : null,

        // Sidebar State
        sidebar: {
          isCollapsed,
          isMobileOpen,
          userType,
          storeNavigationItemsCount: storeNavigationItems?.length || 0,
          filteredNavigationItemsCount: filteredNavigationItems?.length || 0,
          defaultNavigationItemsCount: defaultNav?.length || 0
        },

      // Responsive
      responsive: {
        isMobile,
        isTablet,
        viewport: typeof window !== 'undefined' ? {
          width: window.innerWidth,
          height: window.innerHeight
        } : null
      },

      // Current Route
      route: {
        pathname,
        isBlogRoute: pathname.startsWith('/admin/blog'),
        isAdminRoute: pathname.startsWith('/admin')
      },

      // Navigation Items
      navigation: {
        storeItems: storeNavigationItems,
        filteredItems: filteredNavigationItems,
        defaultItems: defaultNav,
        blogItem: storeNavigationItems?.find(item => item.id === 'blog'),
        filteredBlogItem: filteredNavigationItems?.find(item => item.id === 'blog'),
        defaultBlogItem: defaultNav?.find(item => item.id === 'blog')
      }
    }

      setDebugInfo(info)
    }

    collectInfo()
  }, [
    isAuthenticated, user, isCollapsed, isMobileOpen, userType,
    storeNavigationItems, filteredNavigationItems, isMobile, isTablet, pathname
  ])

  const refreshNavigation = () => {
    // Force refresh navigation by importing and setting it again
    import('@/config/navigation/instituteAdminNavigation').then(({ instituteAdminNavigationConfig }) => {
      setNavigationItems(instituteAdminNavigationConfig)
    })
  }

  const hasRequiredRole = user && ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(user.legacyRole)

  return (
    <div className="space-y-6 p-6 bg-gray-50 min-h-screen">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Bug className="w-6 h-6" />
            Sidebar Debug Information
          </h1>
          <p className="text-gray-600 mt-1">Troubleshooting sidebar navigation issues</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowRawData(!showRawData)}
          >
            {showRawData ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
            {showRawData ? 'Hide' : 'Show'} Raw Data
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshNavigation}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh Navigation
          </Button>
        </div>
      </div>

      {/* Quick Status */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Authentication</p>
                <p className="text-lg font-bold">
                  {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
                </p>
              </div>
              {isAuthenticated ? (
                <CheckCircle className="h-8 w-8 text-green-600" />
              ) : (
                <XCircle className="h-8 w-8 text-red-600" />
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Role Access</p>
                <p className="text-lg font-bold">
                  {hasRequiredRole ? 'Authorized' : 'Unauthorized'}
                </p>
              </div>
              {hasRequiredRole ? (
                <CheckCircle className="h-8 w-8 text-green-600" />
              ) : (
                <XCircle className="h-8 w-8 text-red-600" />
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Navigation Items</p>
                <p className="text-lg font-bold">
                  {debugInfo.sidebar?.filteredNavigationItemsCount || 0}
                </p>
              </div>
              {(debugInfo.sidebar?.filteredNavigationItemsCount || 0) > 0 ? (
                <CheckCircle className="h-8 w-8 text-green-600" />
              ) : (
                <XCircle className="h-8 w-8 text-red-600" />
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Blog Item</p>
                <p className="text-lg font-bold">
                  {debugInfo.navigation?.filteredBlogItem ? 'Found' : 'Missing'}
                </p>
              </div>
              {debugInfo.navigation?.filteredBlogItem ? (
                <CheckCircle className="h-8 w-8 text-green-600" />
              ) : (
                <XCircle className="h-8 w-8 text-red-600" />
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            User Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          {debugInfo.user ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p><strong>Name:</strong> {debugInfo.user.firstName} {debugInfo.user.lastName}</p>
                <p><strong>Email:</strong> {debugInfo.user.email}</p>
                <p><strong>Role:</strong> <Badge variant="outline">{debugInfo.user.legacyRole}</Badge></p>
              </div>
              <div>
                <p><strong>Institute ID:</strong> {debugInfo.user.institute || 'None'}</p>
                <p><strong>User ID:</strong> {debugInfo.user.id}</p>
                <p><strong>Has Required Role:</strong> {hasRequiredRole ? '✅ Yes' : '❌ No'}</p>
              </div>
            </div>
          ) : (
            <p className="text-red-600">No user information available</p>
          )}
        </CardContent>
      </Card>

      {/* Sidebar State */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Navigation className="w-5 h-5" />
            Sidebar State
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p><strong>Collapsed:</strong> {debugInfo.sidebar?.isCollapsed ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Mobile Open:</strong> {debugInfo.sidebar?.isMobileOpen ? '✅ Yes' : '❌ No'}</p>
              <p><strong>User Type:</strong> <Badge>{debugInfo.sidebar?.userType}</Badge></p>
            </div>
            <div>
              <p><strong>Is Mobile:</strong> {debugInfo.responsive?.isMobile ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Is Tablet:</strong> {debugInfo.responsive?.isTablet ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Viewport:</strong> {debugInfo.responsive?.viewport ? `${debugInfo.responsive.viewport.width}x${debugInfo.responsive.viewport.height}` : 'Unknown'}</p>
            </div>
            <div>
              <p><strong>Store Items:</strong> {debugInfo.sidebar?.storeNavigationItemsCount}</p>
              <p><strong>Filtered Items:</strong> {debugInfo.sidebar?.filteredNavigationItemsCount}</p>
              <p><strong>Default Items:</strong> {debugInfo.sidebar?.defaultNavigationItemsCount}</p>
              <p><strong>Current Path:</strong> <Badge variant="outline">{pathname}</Badge></p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation Items */}
      <Card>
        <CardHeader>
          <CardTitle>Navigation Items Analysis</CardTitle>
          <CardDescription>
            Comparing store navigation items vs filtered navigation items
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Blog Navigation Item Status</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-3 bg-gray-50 rounded">
                  <p className="font-medium">In Default Config:</p>
                  {debugInfo.navigation?.defaultBlogItem ? (
                    <div className="text-sm mt-1">
                      <p>✅ Found: {debugInfo.navigation.defaultBlogItem.label}</p>
                      <p>Href: {debugInfo.navigation.defaultBlogItem.href}</p>
                      <p>Children: {debugInfo.navigation.defaultBlogItem.children?.length || 0}</p>
                    </div>
                  ) : (
                    <p className="text-red-600 text-sm mt-1">❌ Not found in default config</p>
                  )}
                </div>
                <div className="p-3 bg-gray-50 rounded">
                  <p className="font-medium">In Store:</p>
                  {debugInfo.navigation?.blogItem ? (
                    <div className="text-sm mt-1">
                      <p>✅ Found: {debugInfo.navigation.blogItem.label}</p>
                      <p>Href: {debugInfo.navigation.blogItem.href}</p>
                      <p>Children: {debugInfo.navigation.blogItem.children?.length || 0}</p>
                    </div>
                  ) : (
                    <p className="text-red-600 text-sm mt-1">❌ Not found in store</p>
                  )}
                </div>
                <div className="p-3 bg-gray-50 rounded">
                  <p className="font-medium">After Filtering:</p>
                  {debugInfo.navigation?.filteredBlogItem ? (
                    <div className="text-sm mt-1">
                      <p>✅ Found: {debugInfo.navigation.filteredBlogItem.label}</p>
                      <p>Href: {debugInfo.navigation.filteredBlogItem.href}</p>
                      <p>Children: {debugInfo.navigation.filteredBlogItem.children?.length || 0}</p>
                    </div>
                  ) : (
                    <p className="text-red-600 text-sm mt-1">❌ Filtered out by permissions</p>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-medium mb-2">All Navigation Items</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="font-medium mb-2">Default Items ({debugInfo.sidebar?.defaultNavigationItemsCount || 0}):</p>
                  <div className="space-y-1 max-h-40 overflow-y-auto">
                    {debugInfo.navigation?.defaultItems?.map((item: any) => (
                      <div key={item.id} className="text-sm p-2 bg-gray-50 rounded">
                        <p className="font-medium">{item.label}</p>
                        <p className="text-gray-600">{item.href}</p>
                        {item.children && <p className="text-xs text-blue-600">+{item.children.length} children</p>}
                      </div>
                    )) || <p className="text-gray-500">No default items</p>}
                  </div>
                </div>
                <div>
                  <p className="font-medium mb-2">Store Items ({debugInfo.sidebar?.storeNavigationItemsCount || 0}):</p>
                  <div className="space-y-1 max-h-40 overflow-y-auto">
                    {debugInfo.navigation?.storeItems?.map((item: any) => (
                      <div key={item.id} className="text-sm p-2 bg-gray-50 rounded">
                        <p className="font-medium">{item.label}</p>
                        <p className="text-gray-600">{item.href}</p>
                        {item.children && <p className="text-xs text-blue-600">+{item.children.length} children</p>}
                      </div>
                    )) || <p className="text-gray-500">No items in store</p>}
                  </div>
                </div>
                <div>
                  <p className="font-medium mb-2">Filtered Items ({debugInfo.sidebar?.filteredNavigationItemsCount || 0}):</p>
                  <div className="space-y-1 max-h-40 overflow-y-auto">
                    {debugInfo.navigation?.filteredItems?.map((item: any) => (
                      <div key={item.id} className="text-sm p-2 bg-gray-50 rounded">
                        <p className="font-medium">{item.label}</p>
                        <p className="text-gray-600">{item.href}</p>
                        {item.children && <p className="text-xs text-blue-600">+{item.children.length} children</p>}
                      </div>
                    )) || <p className="text-gray-500">No filtered items</p>}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Raw Debug Data */}
      {showRawData && (
        <Card>
          <CardHeader>
            <CardTitle>Raw Debug Data</CardTitle>
            <CardDescription>
              Complete debug information for technical analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto max-h-96">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            Troubleshooting Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {!isAuthenticated && (
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <p className="text-red-800 font-medium">❌ User not authenticated</p>
                <p className="text-red-600 text-sm">Please log in to access the institute admin interface.</p>
              </div>
            )}

            {!hasRequiredRole && isAuthenticated && (
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <p className="text-red-800 font-medium">❌ Insufficient permissions</p>
                <p className="text-red-600 text-sm">Your role ({user?.legacyRole}) doesn't have access to institute admin features.</p>
              </div>
            )}

            {(debugInfo.sidebar?.storeNavigationItemsCount || 0) === 0 && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-yellow-800 font-medium">⚠️ No navigation items in store</p>
                <p className="text-yellow-600 text-sm">Navigation items haven't been loaded. Try refreshing the page.</p>
              </div>
            )}

            {!debugInfo.navigation?.blogItem && (debugInfo.sidebar?.storeNavigationItemsCount || 0) > 0 && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-yellow-800 font-medium">⚠️ Blog navigation item missing from store</p>
                <p className="text-yellow-600 text-sm">The blog navigation item wasn't found in the navigation configuration.</p>
              </div>
            )}

            {debugInfo.navigation?.blogItem && !debugInfo.navigation?.filteredBlogItem && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-yellow-800 font-medium">⚠️ Blog navigation item filtered out</p>
                <p className="text-yellow-600 text-sm">The blog item exists but was filtered out by the permission system.</p>
              </div>
            )}

            {debugInfo.navigation?.filteredBlogItem && (
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <p className="text-green-800 font-medium">✅ Blog navigation item is available</p>
                <p className="text-green-600 text-sm">The blog management navigation should be visible in the sidebar.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
