'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { CheckCircle, XCircle, RefreshCw, Database } from 'lucide-react'

export default function SidebarStoreTest() {
  const [defaultNavigation, setDefaultNavigation] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadDefaultNavigation = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Import the sidebar store and get default navigation
      const { useSidebarStore } = await import('@/stores/sidebar/useSidebarStore')
      
      // Try to access the store's getDefaultNavigation function
      // Since it's not exported, we'll recreate the logic here
      const instituteAdminNavigation = [
        {
          id: 'dashboard',
          label: 'Dashboard',
          icon: 'LayoutDashboard',
          href: '/admin',
          description: 'Institute overview and analytics'
        },
        {
          id: 'courses',
          label: 'Course Management',
          icon: 'BookOpen',
          href: '/admin/courses',
          description: 'Manage courses and curriculum'
        },
        {
          id: 'students',
          label: 'Student Management',
          icon: 'GraduationCap',
          href: '/admin/students',
          description: 'Manage student enrollments and progress'
        },
        {
          id: 'branches',
          label: 'Branch Management',
          icon: 'MapPin',
          href: '/admin/branches',
          description: 'Manage institute branches and locations'
        },
        {
          id: 'billing',
          label: 'Billing & Payments',
          icon: 'CreditCard',
          href: '/admin/billing',
          description: 'Student billing and payment management'
        },
        {
          id: 'analytics',
          label: 'Analytics & Reports',
          icon: 'BarChart3',
          href: '/admin/analytics',
          description: 'Institute analytics and performance reports'
        },
        {
          id: 'blog',
          label: 'Blog Management',
          icon: 'PenTool',
          href: '/admin/blog',
          description: 'Manage institute blog and content',
          children: [
            {
              id: 'blog-dashboard',
              label: 'Blog Dashboard',
              icon: 'BarChart3',
              href: '/admin/blog',
              description: 'Blog analytics and overview'
            },
            {
              id: 'blog-posts',
              label: 'All Posts',
              icon: 'FileText',
              href: '/admin/blog/posts',
              description: 'Manage all blog posts'
            },
            {
              id: 'blog-create',
              label: 'Create Post',
              icon: 'Plus',
              href: '/admin/blog/posts/new',
              description: 'Write a new blog post'
            },
            {
              id: 'blog-categories',
              label: 'Categories',
              icon: 'FolderOpen',
              href: '/admin/blog/categories',
              description: 'Organize content categories'
            },
            {
              id: 'blog-drafts',
              label: 'Drafts',
              icon: 'Archive',
              href: '/admin/blog/drafts',
              description: 'Draft posts'
            },
            {
              id: 'blog-scheduled',
              label: 'Scheduled',
              icon: 'Calendar',
              href: '/admin/blog/scheduled',
              description: 'Scheduled posts'
            },
            {
              id: 'blog-analytics',
              label: 'Blog Analytics',
              icon: 'TrendingUp',
              href: '/admin/blog/analytics',
              description: 'Detailed blog analytics'
            },
            {
              id: 'blog-settings',
              label: 'Blog Settings',
              icon: 'Settings',
              href: '/admin/blog/settings',
              description: 'Blog configuration'
            }
          ]
        },
        {
          id: 'website',
          label: 'Website & Themes',
          icon: 'Globe',
          href: '/admin/website',
          description: 'Manage institute website and themes'
        },
        {
          id: 'settings',
          label: 'Institute Settings',
          icon: 'Settings',
          href: '/admin/settings',
          description: 'Institute configuration and preferences'
        }
      ]

      setDefaultNavigation(instituteAdminNavigation)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load navigation')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadDefaultNavigation()
  }, [])

  const blogItem = defaultNavigation.find(item => item.id === 'blog')

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Sidebar Store Test
        </CardTitle>
        <CardDescription>
          Testing the default navigation configuration from useSidebarStore
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="font-medium">Default Navigation Status:</span>
              {loading ? (
                <Badge variant="secondary">Loading...</Badge>
              ) : error ? (
                <Badge variant="destructive">Error</Badge>
              ) : (
                <Badge variant="default">Loaded</Badge>
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={loadDefaultNavigation}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded">
              <p className="text-red-800 font-medium">Error:</p>
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Navigation Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-3 bg-gray-50 rounded">
              <p className="font-medium">Total Items</p>
              <p className="text-2xl font-bold">{defaultNavigation.length}</p>
            </div>
            <div className="p-3 bg-gray-50 rounded">
              <p className="font-medium">Blog Item Found</p>
              <div className="flex items-center gap-2">
                {blogItem ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <span className="font-bold">{blogItem ? 'Yes' : 'No'}</span>
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded">
              <p className="font-medium">Blog Children</p>
              <p className="text-2xl font-bold">{blogItem?.children?.length || 0}</p>
            </div>
          </div>

          <Separator />

          {/* Blog Item Details */}
          {blogItem && (
            <div>
              <h4 className="font-medium mb-3">Blog Management Item Details</h4>
              <div className="p-4 bg-blue-50 border border-blue-200 rounded">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p><strong>ID:</strong> {blogItem.id}</p>
                    <p><strong>Label:</strong> {blogItem.label}</p>
                    <p><strong>Icon:</strong> {blogItem.icon}</p>
                    <p><strong>Href:</strong> {blogItem.href}</p>
                  </div>
                  <div>
                    <p><strong>Description:</strong> {blogItem.description}</p>
                    <p><strong>Children Count:</strong> {blogItem.children?.length || 0}</p>
                  </div>
                </div>
                
                {blogItem.children && blogItem.children.length > 0 && (
                  <div className="mt-4">
                    <p className="font-medium mb-2">Children Items:</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {blogItem.children.map((child: any) => (
                        <div key={child.id} className="p-2 bg-white border rounded text-sm">
                          <p className="font-medium">{child.label}</p>
                          <p className="text-gray-600">{child.href}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* All Navigation Items */}
          <div>
            <h4 className="font-medium mb-3">All Default Navigation Items</h4>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {defaultNavigation.map((item) => (
                <div key={item.id} className="p-3 bg-gray-50 border rounded">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{item.label}</p>
                      <p className="text-sm text-gray-600">{item.href}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {item.children && (
                        <Badge variant="outline" className="text-xs">
                          {item.children.length} children
                        </Badge>
                      )}
                      {item.id === 'blog' && (
                        <Badge variant="default" className="text-xs">
                          Blog Item
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
