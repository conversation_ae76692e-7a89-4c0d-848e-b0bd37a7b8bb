'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, Eye } from 'lucide-react'

export default function SidebarVisibilityTest() {
  const [sidebarElements, setSidebarElements] = useState<{
    sidebarContainer: boolean
    sidebarComponent: boolean
    navigationItems: number
    blogNavigation: boolean
  }>({
    sidebarContainer: false,
    sidebarComponent: false,
    navigationItems: 0,
    blogNavigation: false
  })

  useEffect(() => {
    const checkSidebarElements = () => {
      // Check for sidebar container
      const sidebarContainer = document.querySelector('[data-testid="sidebar-container"]') || 
                              document.querySelector('.sidebar') ||
                              document.querySelector('[class*="sidebar"]')

      // Check for sidebar component
      const sidebarComponent = document.querySelector('[data-testid="sidebar"]') ||
                              document.querySelector('.sidebar-content')

      // Check for navigation items
      const navItems = document.querySelectorAll('[data-testid="nav-item"]') ||
                      document.querySelectorAll('.nav-item') ||
                      document.querySelectorAll('[class*="nav-item"]')

      // Check for blog navigation specifically
      const blogNav = document.querySelector('[data-testid="nav-item-blog"]') ||
                     document.querySelector('[href="/admin/blog"]') ||
                     Array.from(document.querySelectorAll('a')).find(a => a.textContent?.includes('Blog Management'))

      setSidebarElements({
        sidebarContainer: !!sidebarContainer,
        sidebarComponent: !!sidebarComponent,
        navigationItems: navItems.length,
        blogNavigation: !!blogNav
      })
    }

    // Check immediately
    checkSidebarElements()

    // Check after a delay to allow for dynamic rendering
    const timer = setTimeout(checkSidebarElements, 1000)

    // Set up observer for DOM changes
    const observer = new MutationObserver(checkSidebarElements)
    observer.observe(document.body, { childList: true, subtree: true })

    return () => {
      clearTimeout(timer)
      observer.disconnect()
    }
  }, [])

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="w-5 h-5" />
          Sidebar Visibility Test
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <span className="text-sm font-medium">Sidebar Container</span>
              <div className="flex items-center gap-2">
                {sidebarElements.sidebarContainer ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-600" />
                )}
                <Badge variant={sidebarElements.sidebarContainer ? "default" : "destructive"}>
                  {sidebarElements.sidebarContainer ? "Found" : "Missing"}
                </Badge>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <span className="text-sm font-medium">Sidebar Component</span>
              <div className="flex items-center gap-2">
                {sidebarElements.sidebarComponent ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-600" />
                )}
                <Badge variant={sidebarElements.sidebarComponent ? "default" : "destructive"}>
                  {sidebarElements.sidebarComponent ? "Found" : "Missing"}
                </Badge>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <span className="text-sm font-medium">Navigation Items</span>
              <div className="flex items-center gap-2">
                {sidebarElements.navigationItems > 0 ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-600" />
                )}
                <Badge variant={sidebarElements.navigationItems > 0 ? "default" : "destructive"}>
                  {sidebarElements.navigationItems} items
                </Badge>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <span className="text-sm font-medium">Blog Navigation</span>
              <div className="flex items-center gap-2">
                {sidebarElements.blogNavigation ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-600" />
                )}
                <Badge variant={sidebarElements.blogNavigation ? "default" : "destructive"}>
                  {sidebarElements.blogNavigation ? "Found" : "Missing"}
                </Badge>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded">
            <h4 className="font-medium text-blue-900 mb-2">Troubleshooting Steps:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>1. Check if you're logged in with the correct role (institute_admin, branch_manager, trainer, or institute_staff)</li>
              <li>2. Verify that the page is using the InstituteAdminLayout component</li>
              <li>3. Check browser console for any JavaScript errors</li>
              <li>4. Try refreshing the page to reload navigation items</li>
              <li>5. Check if the sidebar is collapsed or hidden on mobile</li>
            </ul>
          </div>

          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h4 className="font-medium mb-2">Current Page Info:</h4>
            <div className="text-sm space-y-1">
              <p><strong>URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'Unknown'}</p>
              <p><strong>Pathname:</strong> {typeof window !== 'undefined' ? window.location.pathname : 'Unknown'}</p>
              <p><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent.substring(0, 100) + '...' : 'Unknown'}</p>
              <p><strong>Viewport:</strong> {typeof window !== 'undefined' ? `${window.innerWidth}x${window.innerHeight}` : 'Unknown'}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
