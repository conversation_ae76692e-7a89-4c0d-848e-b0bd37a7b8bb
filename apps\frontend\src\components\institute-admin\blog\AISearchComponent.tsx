'use client'

import { useState, useEffect, useRef } from 'react'
import { useBlogStore } from '@/stores/institute-admin/useBlogStore'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import {
  Search,
  Sparkles,
  TrendingUp,
  Clock,
  Tag,
  Filter,
  X
} from 'lucide-react'

interface SearchSuggestion {
  type: 'recent' | 'trending' | 'category' | 'tag'
  value: string
  count?: number
}

export default function AISearchComponent() {
  const {
    posts,
    categories,
    searchQuery,
    searchPosts,
    setSearchQuery,
    selectedCategory,
    selectedTags,
    setFilters,
    clearFilters
  } = useBlogStore()

  const [localQuery, setLocalQuery] = useState(searchQuery)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)

  // Generate search suggestions
  useEffect(() => {
    const generateSuggestions = () => {
      const recentSearches: SearchSuggestion[] = [
        { type: 'recent', value: 'course announcements' },
        { type: 'recent', value: 'exam preparation' },
        { type: 'recent', value: 'student success stories' }
      ]

      const trendingTopics: SearchSuggestion[] = [
        { type: 'trending', value: 'online learning', count: 15 },
        { type: 'trending', value: 'career guidance', count: 12 },
        { type: 'trending', value: 'study tips', count: 8 }
      ]

      const categoryOptions: SearchSuggestion[] = categories.map(cat => ({
        type: 'category' as const,
        value: cat.name,
        count: cat.postCount
      }))

      const popularTags: SearchSuggestion[] = [
        { type: 'tag', value: 'education', count: 25 },
        { type: 'tag', value: 'technology', count: 18 },
        { type: 'tag', value: 'career', count: 15 }
      ]

      setSuggestions([
        ...recentSearches,
        ...trendingTopics,
        ...categoryOptions.slice(0, 3),
        ...popularTags
      ])
    }

    generateSuggestions()
  }, [categories])

  // Handle search
  const handleSearch = async (query: string) => {
    if (!query.trim()) return

    setIsSearching(true)
    setShowSuggestions(false)

    try {
      await searchPosts(query.trim())
      setSearchQuery(query.trim())
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setIsSearching(false)
    }
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    if (suggestion.type === 'category') {
      const category = categories.find(cat => cat.name === suggestion.value)
      if (category) {
        setFilters({ category: category.id })
        setLocalQuery('')
        setShowSuggestions(false)
      }
    } else if (suggestion.type === 'tag') {
      setFilters({ tags: [suggestion.value] })
      setLocalQuery('')
      setShowSuggestions(false)
    } else {
      setLocalQuery(suggestion.value)
      handleSearch(suggestion.value)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(localQuery)
    }
  }

  // Click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const hasActiveFilters = selectedCategory || selectedTags.length > 0

  return (
    <div ref={searchRef} className="relative w-full max-w-2xl">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <Input
          type="text"
          placeholder="Search posts with AI-powered suggestions..."
          value={localQuery}
          onChange={(e) => setLocalQuery(e.target.value)}
          onKeyPress={handleKeyPress}
          onFocus={() => setShowSuggestions(true)}
          className="pl-12 pr-12 h-12 text-lg"
        />
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
          {isSearching && (
            <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full" />
          )}
          <Sparkles className="w-5 h-5 text-blue-500" />
        </div>
      </div>

      {/* Active Filters */}
      {hasActiveFilters && (
        <div className="flex items-center gap-2 mt-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-500">Active filters:</span>

          {selectedCategory && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Category: {categories.find(c => c.id === selectedCategory)?.name}
              <X
                className="w-3 h-3 cursor-pointer"
                onClick={() => setFilters({ category: null })}
              />
            </Badge>
          )}

          {selectedTags.map((tag) => (
            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
              Tag: {tag}
              <X
                className="w-3 h-3 cursor-pointer"
                onClick={() => setFilters({ tags: selectedTags.filter(t => t !== tag) })}
              />
            </Badge>
          ))}

          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-xs"
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Search Suggestions */}
      {showSuggestions && (
        <Card className="absolute top-full left-0 right-0 mt-2 z-50 max-h-96 overflow-y-auto">
          <CardContent className="p-4">
            {/* Recent Searches */}
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Recent Searches</span>
              </div>
              <div className="space-y-1">
                {suggestions.filter(s => s.type === 'recent').map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="block w-full text-left px-2 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded"
                  >
                    {suggestion.value}
                  </button>
                ))}
              </div>
            </div>

            <Separator className="my-3" />

            {/* Trending Topics */}
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="w-4 h-4 text-orange-500" />
                <span className="text-sm font-medium text-gray-700">Trending Topics</span>
              </div>
              <div className="space-y-1">
                {suggestions.filter(s => s.type === 'trending').map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="flex items-center justify-between w-full px-2 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded"
                  >
                    <span>{suggestion.value}</span>
                    {suggestion.count && (
                      <Badge variant="outline" className="text-xs">
                        {suggestion.count} posts
                      </Badge>
                    )}
                  </button>
                ))}
              </div>
            </div>

            <Separator className="my-3" />

            {/* Categories */}
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <Filter className="w-4 h-4 text-blue-500" />
                <span className="text-sm font-medium text-gray-700">Categories</span>
              </div>
              <div className="space-y-1">
                {suggestions.filter(s => s.type === 'category').map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="flex items-center justify-between w-full px-2 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded"
                  >
                    <span>{suggestion.value}</span>
                    {suggestion.count && (
                      <Badge variant="outline" className="text-xs">
                        {suggestion.count}
                      </Badge>
                    )}
                  </button>
                ))}
              </div>
            </div>

            <Separator className="my-3" />

            {/* Popular Tags */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Tag className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium text-gray-700">Popular Tags</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {suggestions.filter(s => s.type === 'tag').map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
                  >
                    #{suggestion.value}
                    {suggestion.count && (
                      <span className="text-gray-500">({suggestion.count})</span>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
