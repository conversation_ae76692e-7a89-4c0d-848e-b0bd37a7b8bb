'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { useBlogStore, BlogPost } from '@/stores/institute-admin/useBlogStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { RichTextEditor } from '@/components/ui/rich-text-editor'
import {
  Save,
  Eye,
  Send,
  Calendar,
  Image,
  Tag,
  Settings,
  Search,
  X
} from 'lucide-react'

interface BlogEditorProps {
  post?: BlogPost
  mode: 'create' | 'edit'
}

const blogPostSchema = Yup.object({
  title: Yup.string().required('Title is required').max(200, 'Title too long'),
  slug: Yup.string()
    .required('Slug is required')
    .max(200, 'Slug too long')
    .matches(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  excerpt: Yup.string().max(300, 'Excerpt too long'),
  content: Yup.string().required('Content is required'),
  category: Yup.string(),
  seoTitle: Yup.string().max(150, 'SEO title too long'),
  seoDescription: Yup.string().max(300, 'SEO description too long')
})

export default function BlogEditor({ post, mode }: BlogEditorProps) {
  const router = useRouter()
  const {
    categories,
    createPost,
    updatePost,
    publishPost,
    schedulePost,
    fetchCategories,
    checkSlugUniqueness
  } = useBlogStore()

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentTag, setCurrentTag] = useState('')
  const [tags, setTags] = useState<string[]>(post?.tags.map(t => t.tag) || [])
  const [scheduledDate, setScheduledDate] = useState('')
  const [slugValidationMessage, setSlugValidationMessage] = useState('')

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  const formik = useFormik({
    initialValues: {
      title: post?.title || '',
      slug: post?.slug || '',
      excerpt: post?.excerpt || '',
      content: post?.content || '',
      category: post?.category?.id || '',
      featuredImage: post?.featuredImage?.url || '',
      seoTitle: post?.seo.title || '',
      seoDescription: post?.seo.description || '',
      allowComments: post?.settings.allowComments ?? true,
      isFeatured: post?.settings.isFeatured ?? false,
      isSticky: post?.settings.isSticky ?? false
    },
    validationSchema: blogPostSchema,
    onSubmit: async (values) => {
      // This will be handled by specific action buttons
    }
  })

  // Auto-generate slug from title
  useEffect(() => {
    if (formik.values.title) {
      const slug = formik.values.title
        .toLowerCase()
        .trim()
        .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special characters but keep letters, numbers and spaces
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
        .replace(/^-|-$/g, '') // Remove leading/trailing hyphens

      formik.setFieldValue('slug', slug)
    }
  }, [formik.values.title])

  const handleAddTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim())) {
      setTags([...tags, currentTag.trim()])
      setCurrentTag('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleSlugChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSlug = e.target.value
    formik.setFieldValue('slug', newSlug)

    if (newSlug.trim()) {
      setSlugValidationMessage('Checking availability...')

      try {
        const isUnique = await checkSlugUniqueness(newSlug, post?.id)
        if (isUnique) {
          setSlugValidationMessage('✓ Slug is available')
        } else {
          setSlugValidationMessage('⚠ This slug is already in use')
        }
      } catch (error) {
        setSlugValidationMessage('Could not validate slug')
      }
    } else {
      setSlugValidationMessage('')
    }
  }

  const validateSlugBeforeSubmit = async () => {
    if (!formik.values.slug) {
      toast.error('Slug is required')
      return false
    }

    const isUnique = await checkSlugUniqueness(formik.values.slug, post?.id)
    if (!isUnique) {
      toast.error('This slug is already in use. Please choose a different one.')
      return false
    }

    return true
  }

  const handleSaveDraft = async () => {
    if (!(await validateSlugBeforeSubmit())) {
      return
    }

    setIsSubmitting(true)
    try {
      const postData = {
        ...formik.values,
        category: formik.values.category,
        content: formik.values.content ? {
          root: {
            type: 'root',
            children: [{
              type: 'paragraph',
              children: [{
                type: 'text',
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: formik.values.content,
                version: 1
              }],
              direction: null,
              format: '',
              indent: 0,
              textFormat: 0,
              textStyle: '',
              version: 1
            }],
            direction: null,
            format: '',
            indent: 0,
            version: 1
          }
        } : undefined,
        tags: tags.map(tag => ({ tag })),
        status: 'draft',
        seo: {
          title: formik.values.seoTitle,
          description: formik.values.seoDescription
        },
        settings: {
          allowComments: formik.values.allowComments,
          isFeatured: formik.values.isFeatured,
          isSticky: formik.values.isSticky
        }
      }

      if (mode === 'create') {
        await createPost(postData)
        router.push('/admin/blog/posts')
      } else {
        await updatePost(post!.id, postData)
      }
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePublish = async () => {
    if (!(await validateSlugBeforeSubmit())) {
      return
    }

    setIsSubmitting(true)
    try {
      const postData = {
        ...formik.values,
        category: formik.values.category,
        content: formik.values.content ? {
          root: {
            type: 'root',
            children: [{
              type: 'paragraph',
              children: [{
                type: 'text',
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: formik.values.content,
                version: 1
              }],
              direction: null,
              format: '',
              indent: 0,
              textFormat: 0,
              textStyle: '',
              version: 1
            }],
            direction: null,
            format: '',
            indent: 0,
            version: 1
          }
        } : undefined,
        tags: tags.map(tag => ({ tag })),
        status: 'published',
        publishedAt: new Date().toISOString(),
        seo: {
          title: formik.values.seoTitle,
          description: formik.values.seoDescription
        },
        settings: {
          allowComments: formik.values.allowComments,
          isFeatured: formik.values.isFeatured,
          isSticky: formik.values.isSticky
        }
      }

      if (mode === 'create') {
        await createPost(postData)
      } else {
        await updatePost(post!.id, postData)
      }

      router.push('/admin/blog/posts')
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSchedule = async () => {
    if (!scheduledDate) return

    if (!(await validateSlugBeforeSubmit())) {
      return
    }

    setIsSubmitting(true)
    try {
      const postData = {
        ...formik.values,
        category: formik.values.category,
        content: formik.values.content ? {
          root: {
            type: 'root',
            children: [{
              type: 'paragraph',
              children: [{
                type: 'text',
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: formik.values.content,
                version: 1
              }],
              direction: null,
              format: '',
              indent: 0,
              textFormat: 0,
              textStyle: '',
              version: 1
            }],
            direction: null,
            format: '',
            indent: 0,
            version: 1
          }
        } : undefined,
        tags: tags.map(tag => ({ tag })),
        status: 'scheduled',
        scheduledFor: scheduledDate,
        seo: {
          title: formik.values.seoTitle,
          description: formik.values.seoDescription
        },
        settings: {
          allowComments: formik.values.allowComments,
          isFeatured: formik.values.isFeatured,
          isSticky: formik.values.isSticky
        }
      }

      if (mode === 'create') {
        await createPost(postData)
      } else {
        await updatePost(post!.id, postData)
      }

      router.push('/admin/blog/posts')
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">
          {mode === 'create' ? 'Create New Post' : 'Edit Post'}
        </h1>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button
            variant="outline"
            onClick={handleSaveDraft}
            disabled={isSubmitting}
          >
            <Save className="w-4 h-4 mr-2" />
            Save Draft
          </Button>
          <Button
            onClick={handlePublish}
            disabled={isSubmitting}
          >
            <Send className="w-4 h-4 mr-2" />
            Publish
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Post Content</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  name="title"
                  value={formik.values.title}
                  onChange={formik.handleChange}
                  placeholder="Enter post title..."
                  className={formik.errors.title && formik.touched.title ? 'border-red-500' : ''}
                />
                {formik.errors.title && formik.touched.title && (
                  <p className="text-sm text-red-500 mt-1">{formik.errors.title}</p>
                )}
              </div>

              <div>
                <Label htmlFor="slug">URL Slug</Label>
                <Input
                  id="slug"
                  name="slug"
                  value={formik.values.slug}
                  onChange={handleSlugChange}
                  placeholder="url-friendly-slug"
                  className={formik.errors.slug && formik.touched.slug ? 'border-red-500' : ''}
                />
                {formik.errors.slug && formik.touched.slug && (
                  <p className="text-sm text-red-500 mt-1">{formik.errors.slug}</p>
                )}
                {slugValidationMessage && (
                  <p className={`text-sm mt-1 ${
                    slugValidationMessage.includes('✓') ? 'text-green-600' :
                    slugValidationMessage.includes('⚠') ? 'text-red-500' :
                    'text-gray-500'
                  }`}>
                    {slugValidationMessage}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="excerpt">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  name="excerpt"
                  value={formik.values.excerpt}
                  onChange={formik.handleChange}
                  placeholder="Brief summary of the post..."
                  rows={3}
                  maxLength={300}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {formik.values.excerpt.length}/300 characters
                </p>
              </div>

              <div>
                <Label htmlFor="content">Content</Label>
                <RichTextEditor
                  value={formik.values.content}
                  onChange={(value) => formik.setFieldValue('content', value)}
                  placeholder="Write your blog post content..."
                  rows={15}
                />
                {formik.errors.content && formik.touched.content && (
                  <p className="text-sm text-red-500 mt-1">{formik.errors.content}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Category & Tags */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Tag className="w-4 h-4 mr-2" />
                Category & Tags
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formik.values.category}
                  onValueChange={(value) => formik.setFieldValue('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category..." />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Tags</Label>
                <div className="flex gap-2 mb-2">
                  <Input
                    value={currentTag}
                    onChange={(e) => setCurrentTag(e.target.value)}
                    placeholder="Add tag..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                  />
                  <Button type="button" onClick={handleAddTag} size="sm">
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-1">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X
                        className="w-3 h-3 cursor-pointer"
                        onClick={() => handleRemoveTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Image className="w-4 h-4 mr-2" />
                Featured Image
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Input
                name="featuredImage"
                value={formik.values.featuredImage}
                onChange={formik.handleChange}
                placeholder="Image URL..."
              />
              {formik.values.featuredImage && (
                <img
                  src={formik.values.featuredImage}
                  alt="Featured"
                  className="mt-2 w-full h-32 object-cover rounded"
                />
              )}
            </CardContent>
          </Card>

          {/* Post Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="allowComments">Allow Comments</Label>
                <Switch
                  id="allowComments"
                  checked={formik.values.allowComments}
                  onCheckedChange={(checked) => formik.setFieldValue('allowComments', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="isFeatured">Featured Post</Label>
                <Switch
                  id="isFeatured"
                  checked={formik.values.isFeatured}
                  onCheckedChange={(checked) => formik.setFieldValue('isFeatured', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="isSticky">Sticky Post</Label>
                <Switch
                  id="isSticky"
                  checked={formik.values.isSticky}
                  onCheckedChange={(checked) => formik.setFieldValue('isSticky', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* SEO */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Search className="w-4 h-4 mr-2" />
                SEO
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="seoTitle">SEO Title</Label>
                <Input
                  id="seoTitle"
                  name="seoTitle"
                  value={formik.values.seoTitle}
                  onChange={formik.handleChange}
                  placeholder="SEO optimized title..."
                  maxLength={150}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {formik.values.seoTitle.length}/150 characters
                </p>
              </div>

              <div>
                <Label htmlFor="seoDescription">SEO Description</Label>
                <Textarea
                  id="seoDescription"
                  name="seoDescription"
                  value={formik.values.seoDescription}
                  onChange={formik.handleChange}
                  placeholder="SEO meta description..."
                  rows={3}
                  maxLength={300}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {formik.values.seoDescription.length}/300 characters
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                Schedule
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="scheduledDate">Schedule for later</Label>
                <Input
                  id="scheduledDate"
                  type="datetime-local"
                  value={scheduledDate}
                  onChange={(e) => setScheduledDate(e.target.value)}
                />
              </div>
              {scheduledDate && (
                <Button
                  onClick={handleSchedule}
                  disabled={isSubmitting}
                  className="w-full"
                  variant="outline"
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  Schedule Post
                </Button>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
