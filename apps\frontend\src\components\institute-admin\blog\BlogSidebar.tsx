'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useBlogStore } from '@/stores/institute-admin/useBlogStore'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  PenTool,
  FileText,
  BarChart3,
  Settings,
  Search,
  Plus,
  TrendingUp,
  Calendar,
  Archive
} from 'lucide-react'

const blogNavItems = [
  {
    title: 'Overview',
    href: '/admin/blog',
    icon: BarChart3,
    description: 'Blog analytics and insights'
  },
  {
    title: 'All Posts',
    href: '/admin/blog/posts',
    icon: FileText,
    description: 'Manage all blog posts'
  },
  {
    title: 'Create Post',
    href: '/admin/blog/posts/new',
    icon: PenTool,
    description: 'Write a new blog post'
  },

  {
    title: 'Scheduled',
    href: '/admin/blog/scheduled',
    icon: Calendar,
    description: 'Scheduled posts'
  },
  {
    title: 'Drafts',
    href: '/admin/blog/drafts',
    icon: Archive,
    description: 'Draft posts'
  },
  {
    title: 'Analytics',
    href: '/admin/blog/analytics',
    icon: TrendingUp,
    description: 'Detailed analytics'
  },
  {
    title: 'Settings',
    href: '/admin/blog/settings',
    icon: Settings,
    description: 'Blog configuration'
  },
  ...(process.env.NODE_ENV === 'development' ? [{
    title: 'Test',
    href: '/admin/blog/test',
    icon: Settings,
    description: 'Integration tests'
  }] : [])
]

export default function BlogSidebar() {
  const pathname = usePathname()
  const {
    posts,
    searchQuery,
    setSearchQuery,
    searchPosts
  } = useBlogStore()

  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (localSearchQuery.trim()) {
      searchPosts(localSearchQuery.trim())
      setSearchQuery(localSearchQuery.trim())
    }
  }

  // Get post counts by status
  const postCounts = posts.reduce((acc, post) => {
    acc[post.status] = (acc[post.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return (
    <div className="bg-white border-b border-gray-200 sticky top-0 z-10 shadow-sm">
      <div className="px-6 py-4">
        {/* Header Row */}
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-xl font-semibold text-gray-900">Blog Management</h2>
          <div className="flex items-center gap-3">
            {/* Search */}
            <form onSubmit={handleSearch} className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search posts..."
                value={localSearchQuery}
                onChange={(e) => setLocalSearchQuery(e.target.value)}
                className="pl-10 w-64 h-9"
              />
            </form>

            {/* New Post Button */}
            <Button size="sm" asChild className="bg-blue-600 hover:bg-blue-700">
              <Link href="/admin/blog/posts/new">
                <Plus className="w-4 h-4 mr-1" />
                New Post
              </Link>
            </Button>
          </div>
        </div>

        {/* Horizontal Navigation */}
        <nav className="flex items-center gap-2 overflow-x-auto pb-1">
          {blogNavItems.map((item) => {
            const isActive = pathname === item.href
            const Icon = item.icon

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors whitespace-nowrap min-w-fit',
                  isActive
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-gray-200'
                )}
              >
                <Icon className="w-4 h-4 mr-2 flex-shrink-0" />
                <span className="font-medium">{item.title}</span>
                {item.title === 'Drafts' && postCounts.draft > 0 && (
                  <Badge variant={isActive ? "secondary" : "outline"} className="ml-2 text-xs">
                    {postCounts.draft}
                  </Badge>
                )}
                {item.title === 'Scheduled' && postCounts.scheduled > 0 && (
                  <Badge variant={isActive ? "secondary" : "outline"} className="ml-2 text-xs">
                    {postCounts.scheduled}
                  </Badge>
                )}
              </Link>
            )
          })}
        </nav>
      </div>
    </div>
  )
}
