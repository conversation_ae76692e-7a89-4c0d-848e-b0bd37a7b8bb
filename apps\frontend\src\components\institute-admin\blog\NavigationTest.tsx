'use client'

import { usePathname } from 'next/navigation'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { isNavigationItemActive, shouldExpandNavigationItem } from '@/utils/navigationUtils'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, Navigation } from 'lucide-react'

export default function NavigationTest() {
  const pathname = usePathname()
  const { navigationItems } = useSidebarStore()

  // Find the blog navigation item
  const blogNavItem = navigationItems.find(item => item.id === 'blog')

  if (!blogNavItem) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-700">
            <XCircle className="w-5 h-5" />
            Navigation Test Failed
          </CardTitle>
          <CardDescription className="text-red-600">
            Blog navigation item not found in navigation configuration
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  const isBlogActive = isNavigationItemActive(blogNavItem, pathname)
  const shouldExpand = shouldExpandNavigationItem(blogNavItem, pathname)

  // Test all blog routes
  const blogRoutes = [
    '/admin/blog',
    '/admin/blog/posts',
    '/admin/blog/posts/new',
    '/admin/blog/categories',
    '/admin/blog/drafts',
    '/admin/blog/scheduled',
    '/admin/blog/analytics',
    '/admin/blog/settings'
  ]

  const routeTests = blogRoutes.map(route => ({
    route,
    isActive: isNavigationItemActive(blogNavItem, route),
    shouldExpand: shouldExpandNavigationItem(blogNavItem, route)
  }))

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Navigation className="w-5 h-5" />
            Blog Navigation Integration Test
          </CardTitle>
          <CardDescription>
            Testing the blog management navigation integration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current State */}
          <div>
            <h4 className="font-medium mb-2">Current State</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <span className="text-sm">Current Path:</span>
                <Badge variant="outline">{pathname}</Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <span className="text-sm">Blog Active:</span>
                {isBlogActive ? (
                  <Badge variant="default" className="bg-green-600">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Yes
                  </Badge>
                ) : (
                  <Badge variant="secondary">
                    <XCircle className="w-3 h-3 mr-1" />
                    No
                  </Badge>
                )}
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <span className="text-sm">Should Expand:</span>
                {shouldExpand ? (
                  <Badge variant="default" className="bg-blue-600">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Yes
                  </Badge>
                ) : (
                  <Badge variant="secondary">
                    <XCircle className="w-3 h-3 mr-1" />
                    No
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Blog Navigation Item Details */}
          <div>
            <h4 className="font-medium mb-2">Blog Navigation Item</h4>
            <div className="p-3 bg-gray-50 rounded">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div><strong>ID:</strong> {blogNavItem.id}</div>
                <div><strong>Label:</strong> {blogNavItem.label}</div>
                <div><strong>Icon:</strong> {blogNavItem.icon}</div>
                <div><strong>Href:</strong> {blogNavItem.href}</div>
                <div><strong>Section:</strong> {blogNavItem.section}</div>
                <div><strong>Children:</strong> {blogNavItem.children?.length || 0}</div>
              </div>
            </div>
          </div>

          {/* Route Tests */}
          <div>
            <h4 className="font-medium mb-2">Route Tests</h4>
            <div className="space-y-2">
              {routeTests.map(({ route, isActive, shouldExpand }) => (
                <div key={route} className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm font-mono">{route}</span>
                  <div className="flex items-center gap-2">
                    <Badge variant={isActive ? "default" : "secondary"} className="text-xs">
                      {isActive ? "Active" : "Inactive"}
                    </Badge>
                    <Badge variant={shouldExpand ? "default" : "secondary"} className="text-xs">
                      {shouldExpand ? "Expand" : "Collapse"}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Children Tests */}
          {blogNavItem.children && (
            <div>
              <h4 className="font-medium mb-2">Children Navigation Items</h4>
              <div className="space-y-2">
                {blogNavItem.children.map(child => {
                  const childActive = isNavigationItemActive(child, pathname)
                  return (
                    <div key={child.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <span className="text-sm">{child.label}</span>
                        <Badge variant="outline" className="text-xs">{child.href}</Badge>
                      </div>
                      <Badge variant={childActive ? "default" : "secondary"} className="text-xs">
                        {childActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  )
                })}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
