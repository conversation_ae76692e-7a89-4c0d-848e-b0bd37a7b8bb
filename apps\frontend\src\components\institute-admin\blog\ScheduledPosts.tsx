'use client'

import { useState, useEffect } from 'react'
import { useBlogStore } from '@/stores/institute-admin/useBlogStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Calendar,
  Clock,
  MoreHorizontal,
  Edit,
  Send,
  Trash2,
  Eye,
  CalendarDays
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export default function ScheduledPosts() {
  const router = useRouter()
  const {
    posts,
    fetchPosts,
    updatePost,
    deletePost,
    publishPost
  } = useBlogStore()

  const [selectedPost, setSelectedPost] = useState<any>(null)
  const [newScheduleDate, setNewScheduleDate] = useState('')
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false)

  useEffect(() => {
    fetchPosts({ status: 'scheduled' })
  }, [fetchPosts])

  const scheduledPosts = posts.filter(post => post.status === 'scheduled')

  const handlePublishNow = async (postId: string) => {
    try {
      await publishPost(postId)
      fetchPosts({ status: 'scheduled' }) // Refresh the list
    } catch (error) {
      console.error('Failed to publish post:', error)
    }
  }

  const handleReschedule = async () => {
    if (!selectedPost || !newScheduleDate) return

    try {
      await updatePost(selectedPost.id, {
        scheduledFor: newScheduleDate
      })
      
      setIsRescheduleDialogOpen(false)
      setSelectedPost(null)
      setNewScheduleDate('')
      fetchPosts({ status: 'scheduled' }) // Refresh the list
    } catch (error) {
      console.error('Failed to reschedule post:', error)
    }
  }

  const handleDeletePost = async (postId: string) => {
    try {
      await deletePost(postId)
      fetchPosts({ status: 'scheduled' }) // Refresh the list
    } catch (error) {
      console.error('Failed to delete post:', error)
    }
  }

  const openRescheduleDialog = (post: any) => {
    setSelectedPost(post)
    setNewScheduleDate(post.scheduledFor ? new Date(post.scheduledFor).toISOString().slice(0, 16) : '')
    setIsRescheduleDialogOpen(true)
  }

  const getTimeUntilPublish = (scheduledFor: string) => {
    const now = new Date()
    const scheduled = new Date(scheduledFor)
    const diff = scheduled.getTime() - now.getTime()

    if (diff < 0) {
      return 'Overdue'
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) {
      return `${days}d ${hours}h`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  }

  const isOverdue = (scheduledFor: string) => {
    return new Date(scheduledFor) < new Date()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Scheduled Posts</h2>
          <p className="text-gray-600 mt-1">Manage posts scheduled for future publication</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            {scheduledPosts.length} scheduled
          </Badge>
        </div>
      </div>

      {/* Scheduled Posts */}
      <Card>
        <CardHeader>
          <CardTitle>Scheduled Posts ({scheduledPosts.length})</CardTitle>
          <CardDescription>
            Posts waiting to be published automatically
          </CardDescription>
        </CardHeader>
        <CardContent>
          {scheduledPosts.length === 0 ? (
            <div className="text-center py-8">
              <CalendarDays className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Scheduled Posts</h3>
              <p className="text-gray-600 mb-4">You don't have any posts scheduled for publication.</p>
              <Button asChild>
                <Link href="/admin/blog/posts/new">
                  Create New Post
                </Link>
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Scheduled For</TableHead>
                  <TableHead>Time Until Publish</TableHead>
                  <TableHead>Author</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {scheduledPosts.map((post) => (
                  <TableRow key={post.id}>
                    <TableCell>
                      <div>
                        <Link
                          href={`/admin/blog/posts/${post.id}`}
                          className="font-medium text-gray-900 hover:text-blue-600"
                        >
                          {post.title}
                        </Link>
                        {post.excerpt && (
                          <p className="text-sm text-gray-500 mt-1 line-clamp-1">
                            {post.excerpt}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {post.category ? (
                        <Badge variant="outline" style={{ backgroundColor: post.category.color + '20', borderColor: post.category.color }}>
                          {post.category.name}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">No category</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium">
                          {new Date(post.scheduledFor).toLocaleDateString()}
                        </div>
                        <div className="text-gray-500">
                          {new Date(post.scheduledFor).toLocaleTimeString()}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={isOverdue(post.scheduledFor) ? 'destructive' : 'secondary'}
                        className="flex items-center gap-1 w-fit"
                      >
                        <Clock className="w-3 h-3" />
                        {getTimeUntilPublish(post.scheduledFor)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{post.author.firstName} {post.author.lastName}</div>
                        <div className="text-gray-500">
                          Created {new Date(post.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => router.push(`/admin/blog/posts/${post.id}`)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => router.push(`/admin/blog/posts/${post.id}/edit`)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => openRescheduleDialog(post)}>
                            <Calendar className="mr-2 h-4 w-4" />
                            Reschedule
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handlePublishNow(post.id)}>
                            <Send className="mr-2 h-4 w-4" />
                            Publish Now
                          </DropdownMenuItem>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  This action cannot be undone. This will permanently delete the scheduled post.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDeletePost(post.id)}>
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Reschedule Dialog */}
      <Dialog open={isRescheduleDialogOpen} onOpenChange={setIsRescheduleDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Reschedule Post</DialogTitle>
            <DialogDescription>
              Change the scheduled publication date and time for this post.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newScheduleDate" className="text-right text-sm font-medium">
                New Date & Time
              </label>
              <Input
                id="newScheduleDate"
                type="datetime-local"
                value={newScheduleDate}
                onChange={(e) => setNewScheduleDate(e.target.value)}
                className="col-span-3"
                min={new Date().toISOString().slice(0, 16)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRescheduleDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleReschedule} disabled={!newScheduleDate}>
              Reschedule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
