'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  MoreVertical, 
  Edit3, 
  Eye, 
  Info, 
  Users, 
  MessageSquare,
  BookOpen,
  DollarSign,
  Calendar
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Course } from '@/stores/institute-admin/useCourseStore'
// import { formatDistanceToNow } from 'date-fns'

interface CourseCardProps {
  course: Course
  onEdit?: (course: Course) => void
  onPreview?: (course: Course) => void
  onViewInfo?: (course: Course) => void
  onViewLearners?: (course: Course) => void
  onViewDiscussions?: (course: Course) => void
}

export function CourseCard({ 
  course, 
  onEdit, 
  onPreview, 
  onViewInfo, 
  onViewLear<PERSON>, 
  onViewDiscussions 
}: CourseCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'archived':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPricingDisplay = () => {
    if (course.pricing_type === 'free') {
      return (
        <div className="flex items-center space-x-1 text-green-600">
          <DollarSign className="h-4 w-4" />
          <span className="font-medium">Free</span>
        </div>
      )
    }

    if (course.pricing_type === 'one_time') {
      return (
        <div className="flex items-center space-x-2">
          <DollarSign className="h-4 w-4 text-blue-600" />
          <div className="flex flex-col">
            {course.discount_percentage && course.discount_percentage > 0 ? (
              <>
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold text-blue-600">
                    ${course.final_price?.toFixed(2)}
                  </span>
                  <span className="text-sm text-gray-500 line-through">
                    ${course.price_amount?.toFixed(2)}
                  </span>
                </div>
                <span className="text-xs text-green-600">
                  {course.discount_percentage}% off
                </span>
              </>
            ) : (
              <span className="text-lg font-bold text-blue-600">
                ${course.price_amount?.toFixed(2)}
              </span>
            )}
          </div>
        </div>
      )
    }

    return null
  }

  return (
    <Card className="group relative overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-blue-100 border-gray-200 hover:border-blue-300">
      {/* Course Thumbnail */}
      <div className="relative h-48 bg-gradient-to-br from-blue-50 to-indigo-100 overflow-hidden">
        {course.thumbnail?.url ? (
          <img
            src={course.thumbnail.url}
            alt={course.thumbnail.alt || course.title}
            className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <BookOpen className="h-16 w-16 text-blue-300" />
          </div>
        )}
        
        {/* Status Badge */}
        <div className="absolute top-3 left-3">
          <Badge 
            variant="secondary" 
            className={`${getStatusColor(course.status)} text-xs font-medium`}
          >
            {course.status.charAt(0).toUpperCase() + course.status.slice(1)}
          </Badge>
        </div>

        {/* Action Menu */}
        <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="secondary" 
                size="sm" 
                className="h-8 w-8 p-0 bg-white/90 hover:bg-white shadow-sm"
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => onEdit?.(course)}>
                <Edit3 className="h-4 w-4 mr-2" />
                Course Builder
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onPreview?.(course)}>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onViewInfo?.(course)}>
                <Info className="h-4 w-4 mr-2" />
                Information
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onViewLearners?.(course)}>
                <Users className="h-4 w-4 mr-2" />
                Learners
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onViewDiscussions?.(course)}>
                <MessageSquare className="h-4 w-4 mr-2" />
                Discussions
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Course Content */}
      <CardHeader className="pb-3">
        <div className="space-y-2">
          <h3 className="font-semibold text-lg leading-tight line-clamp-2 text-gray-900">
            {course.title}
          </h3>
          <p className="text-sm text-gray-600 line-clamp-3 leading-relaxed">
            {course.description}
          </p>
        </div>
      </CardHeader>

      <CardContent className="pt-0 pb-4">
        <div className="space-y-3">
          {/* Pricing */}
          <div className="flex items-center justify-between">
            {getPricingDisplay()}
          </div>

          {/* Branch Info */}
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>{course.branch.name}</span>
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-0 pb-4">
        <div className="w-full flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-1">
            <Calendar className="h-3 w-3" />
            <span>
              Created {new Date(course.createdAt).toLocaleDateString()}
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <span>by</span>
            <span className="font-medium text-gray-700">
              {course.created_by.firstName} {course.created_by.lastName}
            </span>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
