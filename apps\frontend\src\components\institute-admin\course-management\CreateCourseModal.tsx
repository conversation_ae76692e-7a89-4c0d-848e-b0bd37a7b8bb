'use client'

import React, { useState, useEffect } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
// import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Card, CardContent } from '@/components/ui/card'
import { Loader2, DollarSign, Gift } from 'lucide-react'
import { useCourseStore, CourseCreationData } from '@/stores/institute-admin/useCourseStore'

interface CreateCourseModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const validationSchema = Yup.object({
  title: Yup.string()
    .required('Title is required')
    .max(100, 'Title must be 100 characters or less')
    .trim(),
  description: Yup.string()
    .required('Description is required')
    .min(50, 'Description must be at least 50 characters')
    .trim(),
  pricing_type: Yup.string()
    .oneOf(['free', 'one_time'], 'Invalid pricing type')
    .required('Pricing type is required'),
  price_amount: Yup.number()
    .when('pricing_type', {
      is: 'one_time',
      then: (schema) => schema
        .required('Price is required for paid courses')
        .min(0.01, 'Price must be greater than 0'),
      otherwise: (schema) => schema.nullable()
    }),
  discount_percentage: Yup.number()
    .when('pricing_type', {
      is: 'one_time',
      then: (schema) => schema
        .min(0, 'Discount cannot be negative')
        .max(100, 'Discount cannot exceed 100%')
        .nullable(),
      otherwise: (schema) => schema.nullable()
    })
})

export function CreateCourseModal({ open, onOpenChange }: CreateCourseModalProps) {
  const { createCourse, loading } = useCourseStore()
  const [finalPrice, setFinalPrice] = useState<number | null>(null)

  const formik = useFormik<CourseCreationData>({
    initialValues: {
      title: '',
      description: '',
      pricing_type: 'free',
      price_amount: undefined,
      discount_percentage: undefined,
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      const result = await createCourse(values)
      if (result) {
        resetForm()
        onOpenChange(false)
      }
    },
  })

  // Calculate final price when price or discount changes
  useEffect(() => {
    if (formik.values.pricing_type === 'one_time' && formik.values.price_amount) {
      const discount = formik.values.discount_percentage || 0
      const calculated = formik.values.price_amount * (1 - discount / 100)
      setFinalPrice(calculated)
    } else {
      setFinalPrice(null)
    }
  }, [formik.values.price_amount, formik.values.discount_percentage, formik.values.pricing_type])

  const handleClose = () => {
    if (!loading) {
      formik.resetForm()
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Create New Course</DialogTitle>
          <DialogDescription>
            Fill in the details below to create a new course for your institute.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-medium">
              Course Title *
            </Label>
            <Input
              id="title"
              name="title"
              placeholder="Enter course title"
              value={formik.values.title}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              className={formik.touched.title && formik.errors.title ? 'border-red-500' : ''}
            />
            {formik.touched.title && formik.errors.title && (
              <p className="text-sm text-red-600">{formik.errors.title}</p>
            )}
            <p className="text-xs text-gray-500">
              {formik.values.title.length}/100 characters
            </p>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">
              Course Description *
            </Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Describe what students will learn in this course"
              rows={4}
              value={formik.values.description}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              className={formik.touched.description && formik.errors.description ? 'border-red-500' : ''}
            />
            {formik.touched.description && formik.errors.description && (
              <p className="text-sm text-red-600">{formik.errors.description}</p>
            )}
            <p className="text-xs text-gray-500">
              {formik.values.description.length} characters (minimum 50)
            </p>
          </div>

          {/* Pricing Configuration */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">Pricing Configuration *</Label>
            
            <div className="space-y-3">
              {/* Free Plan */}
              <Card
                className={`cursor-pointer transition-all ${formik.values.pricing_type === 'free' ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'}`}
                onClick={() => formik.setFieldValue('pricing_type', 'free')}
              >
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      value="free"
                      id="free"
                      name="pricing_type"
                      checked={formik.values.pricing_type === 'free'}
                      onChange={() => formik.setFieldValue('pricing_type', 'free')}
                      className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Gift className="h-5 w-5 text-green-600" />
                        <Label htmlFor="free" className="font-medium cursor-pointer">
                          Free Plan
                        </Label>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        Make this course available to all students at no cost
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* One-time Payment Plan */}
              <Card
                className={`cursor-pointer transition-all ${formik.values.pricing_type === 'one_time' ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'}`}
                onClick={() => formik.setFieldValue('pricing_type', 'one_time')}
              >
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      value="one_time"
                      id="one_time"
                      name="pricing_type"
                      checked={formik.values.pricing_type === 'one_time'}
                      onChange={() => formik.setFieldValue('pricing_type', 'one_time')}
                      className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-5 w-5 text-blue-600" />
                        <Label htmlFor="one_time" className="font-medium cursor-pointer">
                          One-time Payment
                        </Label>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        Students pay once to access the course permanently
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Pricing Details for One-time Payment */}
            {formik.values.pricing_type === 'one_time' && (
              <div className="space-y-4 pl-4 border-l-2 border-blue-200 bg-blue-50/50 p-4 rounded-r-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Price Amount */}
                  <div className="space-y-2">
                    <Label htmlFor="price_amount" className="text-sm font-medium">
                      Total Price ($) *
                    </Label>
                    <Input
                      id="price_amount"
                      name="price_amount"
                      type="number"
                      step="0.01"
                      min="0.01"
                      placeholder="0.00"
                      value={formik.values.price_amount || ''}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      className={formik.touched.price_amount && formik.errors.price_amount ? 'border-red-500' : ''}
                    />
                    {formik.touched.price_amount && formik.errors.price_amount && (
                      <p className="text-sm text-red-600">{formik.errors.price_amount}</p>
                    )}
                  </div>

                  {/* Discount Percentage */}
                  <div className="space-y-2">
                    <Label htmlFor="discount_percentage" className="text-sm font-medium">
                      Discount (%)
                    </Label>
                    <Input
                      id="discount_percentage"
                      name="discount_percentage"
                      type="number"
                      min="0"
                      max="100"
                      placeholder="0"
                      value={formik.values.discount_percentage || ''}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      className={formik.touched.discount_percentage && formik.errors.discount_percentage ? 'border-red-500' : ''}
                    />
                    {formik.touched.discount_percentage && formik.errors.discount_percentage && (
                      <p className="text-sm text-red-600">{formik.errors.discount_percentage}</p>
                    )}
                  </div>
                </div>

                {/* Final Price Display */}
                {finalPrice !== null && (
                  <div className="bg-white p-3 rounded-lg border">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Final Price:</span>
                      <span className="text-lg font-bold text-green-600">
                        ${finalPrice.toFixed(2)}
                      </span>
                    </div>
                    {formik.values.discount_percentage && formik.values.discount_percentage > 0 && (
                      <p className="text-xs text-gray-500 mt-1">
                        Original: ${formik.values.price_amount?.toFixed(2)} • 
                        Discount: {formik.values.discount_percentage}% • 
                        Savings: ${((formik.values.price_amount || 0) - finalPrice).toFixed(2)}
                      </p>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading || !formik.isValid}
              className="min-w-[120px]"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Course'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
