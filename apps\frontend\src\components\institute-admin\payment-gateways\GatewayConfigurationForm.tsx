'use client'

import { useState, useEffect } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { useInstituteGatewayStore, PaymentGateway, ConfigField } from '@/stores/institute-admin/useInstituteGatewayStore'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Eye, EyeOff, TestTube, Save, ExternalLink } from 'lucide-react'

interface GatewayConfigurationFormProps {
  isOpen: boolean
  onClose: () => void
  gateway: PaymentGateway | null
}

export function GatewayConfigurationForm({ isOpen, onClose, gateway }: GatewayConfigurationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({})
  
  const { 
    currentConfig, 
    getGatewayConfig, 
    saveGatewayConfig, 
    testGatewayConfig,
    setCurrentConfig 
  } = useInstituteGatewayStore()

  // Load existing configuration when gateway changes
  useEffect(() => {
    if (gateway && isOpen) {
      getGatewayConfig(gateway.id)
    }
  }, [gateway, isOpen, getGatewayConfig])

  // Create validation schema based on gateway fields
  const createValidationSchema = () => {
    if (!gateway) return Yup.object()

    const schemaFields: Record<string, any> = {}

    // Add validation for required fields
    gateway.requiredConfigFields.forEach((field: ConfigField) => {
      let validator = Yup.string()
      
      if (field.isRequired) {
        validator = validator.required(`${field.label} is required`)
      }
      
      if (field.type === 'email') {
        validator = validator.email('Must be a valid email')
      } else if (field.type === 'url') {
        validator = validator.url('Must be a valid URL')
      } else if (field.type === 'number') {
        validator = Yup.number().required(`${field.label} is required`)
      }
      
      schemaFields[field.key] = validator
    })

    // Add validation for optional fields
    gateway.optionalConfigFields.forEach((field: ConfigField) => {
      let validator = Yup.string()
      
      if (field.type === 'email') {
        validator = validator.email('Must be a valid email')
      } else if (field.type === 'url') {
        validator = validator.url('Must be a valid URL')
      } else if (field.type === 'number') {
        validator = Yup.number()
      }
      
      schemaFields[field.key] = validator
    })

    return Yup.object().shape(schemaFields)
  }

  // Create initial values based on gateway fields and existing config
  const createInitialValues = () => {
    if (!gateway) return {}

    const values: Record<string, any> = {}

    // Set initial values for required fields
    gateway.requiredConfigFields.forEach((field: ConfigField) => {
      values[field.key] = currentConfig?.configuration?.[field.key] || ''
    })

    // Set initial values for optional fields
    gateway.optionalConfigFields.forEach((field: ConfigField) => {
      values[field.key] = currentConfig?.configuration?.[field.key] || ''
    })

    // Add form-level settings
    values.isActive = currentConfig?.isActive ?? false
    values.testMode = currentConfig?.testMode ?? true
    values.isPrimary = currentConfig?.isPrimary ?? false
    values.notes = currentConfig?.notes || ''

    return values
  }

  const formik = useFormik({
    initialValues: createInitialValues(),
    validationSchema: createValidationSchema(),
    enableReinitialize: true,
    onSubmit: async (values) => {
      if (!gateway) return

      setIsSubmitting(true)
      try {
        // Extract configuration values (exclude form-level settings)
        const configuration: Record<string, any> = {}
        
        gateway.requiredConfigFields.forEach((field: ConfigField) => {
          if (values[field.key] !== undefined && values[field.key] !== '') {
            configuration[field.key] = values[field.key]
          }
        })

        gateway.optionalConfigFields.forEach((field: ConfigField) => {
          if (values[field.key] !== undefined && values[field.key] !== '') {
            configuration[field.key] = values[field.key]
          }
        })

        await saveGatewayConfig(gateway.id, configuration, {
          isActive: values.isActive,
          testMode: values.testMode,
          isPrimary: values.isPrimary,
          notes: values.notes
        })

        onClose()
      } catch (error) {
        // Error handling is done in the store
      } finally {
        setIsSubmitting(false)
      }
    },
  })

  const handleTest = async () => {
    if (!currentConfig) return

    setIsTesting(true)
    try {
      await testGatewayConfig(currentConfig.id)
    } finally {
      setIsTesting(false)
    }
  }

  const handleClose = () => {
    formik.resetForm()
    setCurrentConfig(null)
    setShowPasswords({})
    onClose()
  }

  const togglePasswordVisibility = (fieldKey: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldKey]: !prev[fieldKey]
    }))
  }

  const renderField = (field: ConfigField) => {
    const value = formik.values[field.key] || ''
    const error = formik.touched[field.key] && formik.errors[field.key]
    const isPassword = field.type === 'password'
    const showPassword = showPasswords[field.key]

    return (
      <div key={field.key} className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor={field.key} className="text-sm font-medium">
            {field.label}
            {field.isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {isPassword && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => togglePasswordVisibility(field.key)}
              className="h-6 w-6 p-0"
            >
              {showPassword ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
            </Button>
          )}
        </div>
        
        <Input
          id={field.key}
          name={field.key}
          type={isPassword && !showPassword ? 'password' : 'text'}
          value={value}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          placeholder={field.placeholder}
          className={error ? 'border-red-500' : ''}
        />
        
        {field.description && (
          <p className="text-xs text-muted-foreground">{field.description}</p>
        )}
        
        {error && (
          <p className="text-xs text-red-600">{error}</p>
        )}
      </div>
    )
  }

  if (!gateway) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Configure {gateway.name}
            {gateway.logoUrl && (
              <img src={gateway.logoUrl} alt={gateway.name} className="h-6 w-6" />
            )}
          </DialogTitle>
          <DialogDescription>
            Configure your {gateway.name} payment gateway credentials and settings
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Gateway Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Gateway Information</CardTitle>
              <CardDescription>
                {gateway.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Supported Currencies</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {gateway.supportedCurrencies.map((currency) => (
                      <Badge key={currency} variant="secondary" className="text-xs">
                        {currency}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Payment Methods</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {gateway.supportedMethods.slice(0, 3).map((method) => (
                      <Badge key={method} variant="outline" className="text-xs">
                        {method.replace(/_/g, ' ')}
                      </Badge>
                    ))}
                    {gateway.supportedMethods.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{gateway.supportedMethods.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              
              {gateway.documentationUrl && (
                <div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    asChild
                  >
                    <a 
                      href={gateway.documentationUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center gap-2"
                    >
                      <ExternalLink className="h-4 w-4" />
                      View Documentation
                    </a>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Required Configuration Fields */}
          {gateway.requiredConfigFields.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Required Configuration</CardTitle>
                <CardDescription>
                  These fields are required to use this payment gateway
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {gateway.requiredConfigFields.map(renderField)}
              </CardContent>
            </Card>
          )}

          {/* Optional Configuration Fields */}
          {gateway.optionalConfigFields.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Optional Configuration</CardTitle>
                <CardDescription>
                  These fields are optional but may enhance functionality
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {gateway.optionalConfigFields.map(renderField)}
              </CardContent>
            </Card>
          )}

          {/* Gateway Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Gateway Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="testMode">Test Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable test/sandbox mode for development
                  </p>
                </div>
                <Switch
                  id="testMode"
                  checked={formik.values.testMode}
                  onCheckedChange={(checked) => formik.setFieldValue('testMode', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="isPrimary">Primary Gateway</Label>
                  <p className="text-sm text-muted-foreground">
                    Set as default payment gateway for your institute
                  </p>
                </div>
                <Switch
                  id="isPrimary"
                  checked={formik.values.isPrimary}
                  onCheckedChange={(checked) => formik.setFieldValue('isPrimary', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="isActive">Active</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable this gateway for payments
                  </p>
                </div>
                <Switch
                  id="isActive"
                  checked={formik.values.isActive}
                  onCheckedChange={(checked) => formik.setFieldValue('isActive', checked)}
                />
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formik.values.notes}
                  onChange={formik.handleChange}
                  placeholder="Internal notes about this configuration"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-between pt-6 border-t">
            <div>
              {currentConfig && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleTest}
                  disabled={isTesting}
                >
                  <TestTube className="w-4 h-4 mr-2" />
                  {isTesting ? 'Testing...' : 'Test Configuration'}
                </Button>
              )}
            </div>

            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                <Save className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Saving...' : 'Save Configuration'}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
