'use client'

import { useState, useEffect } from 'react'
import { useInstituteGatewayStore } from '@/stores/institute-admin/useInstituteGatewayStore'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Settings, 
  CreditCard, 
  CheckCircle, 
  XCircle, 
  Clock,
  ExternalLink,
  TestTube,
  Trash2
} from 'lucide-react'
import { GatewayConfigurationForm } from './GatewayConfigurationForm'
import { DeleteConfirmDialog } from '@/components/shared/DeleteConfirmDialog'

export function PaymentGatewaysPage() {
  const [selectedGateway, setSelectedGateway] = useState(null)
  const [showConfigForm, setShowConfigForm] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [configToDelete, setConfigToDelete] = useState(null)

  const {
    availableGateways,
    availableGatewaysLoading,
    configuredGateways,
    configuredGatewaysLoading,
    fetchAvailableGateways,
    fetchConfiguredGateways,
    deleteGatewayConfig,
    testGatewayConfig,
    setCurrentGateway
  } = useInstituteGatewayStore()

  useEffect(() => {
    fetchAvailableGateways()
    fetchConfiguredGateways()
  }, [fetchAvailableGateways, fetchConfiguredGateways])

  const handleConfigure = (gateway: any) => {
    setSelectedGateway(gateway)
    setCurrentGateway(gateway)
    setShowConfigForm(true)
  }

  const handleDelete = (config: any) => {
    setConfigToDelete(config)
    setShowDeleteDialog(true)
  }

  const confirmDelete = async () => {
    if (configToDelete) {
      try {
        await deleteGatewayConfig(configToDelete.id)
        setShowDeleteDialog(false)
        setConfigToDelete(null)
      } catch (error) {
        // Error handling is done in the store
      }
    }
  }

  const handleTest = async (config: any) => {
    await testGatewayConfig(config.id)
  }

  const getConfigurationStatus = (gateway: any) => {
    const config = configuredGateways.find(c => c.gateway.id === gateway.id)
    if (!config) return 'not_configured'
    if (!config.isActive) return 'configured_inactive'
    return 'configured_active'
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'configured_active':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Active</Badge>
      case 'configured_inactive':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Configured</Badge>
      case 'not_configured':
        return <Badge variant="outline">Not Configured</Badge>
      default:
        return null
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'configured_active':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'configured_inactive':
        return <Clock className="h-5 w-5 text-yellow-600" />
      case 'not_configured':
        return <XCircle className="h-5 w-5 text-gray-400" />
      default:
        return null
    }
  }

  if (availableGatewaysLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-gray-600">Loading payment gateways...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
          Payment Gateways
        </h1>
        <p className="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">
          Configure payment gateways for your institute
        </p>
      </div>

      {/* Available Gateways */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Available Payment Gateways</h2>
        
        {availableGateways.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Payment Gateways Available</h3>
              <p className="text-gray-600">
                No payment gateways have been set up by the platform administrator yet.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableGateways.map((gateway) => {
              const status = getConfigurationStatus(gateway)
              const config = configuredGateways.find(c => c.gateway.id === gateway.id)

              return (
                <Card key={gateway.id} className="relative">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        {gateway.logoUrl ? (
                          <img 
                            src={gateway.logoUrl} 
                            alt={gateway.name} 
                            className="h-8 w-8 object-contain"
                          />
                        ) : (
                          <CreditCard className="h-8 w-8 text-gray-400" />
                        )}
                        <div>
                          <CardTitle className="text-lg">{gateway.name}</CardTitle>
                          {gateway.isFeatured && (
                            <Badge variant="secondary" className="text-xs mt-1">Featured</Badge>
                          )}
                        </div>
                      </div>
                      {getStatusIcon(status)}
                    </div>
                    
                    {gateway.description && (
                      <CardDescription className="text-sm">
                        {gateway.description}
                      </CardDescription>
                    )}
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Status */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Status</span>
                      {getStatusBadge(status)}
                    </div>

                    {/* Supported Features */}
                    <div className="space-y-2">
                      <div>
                        <span className="text-sm font-medium">Currencies:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {gateway.supportedCurrencies.slice(0, 3).map((currency) => (
                            <Badge key={currency} variant="outline" className="text-xs">
                              {currency}
                            </Badge>
                          ))}
                          {gateway.supportedCurrencies.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{gateway.supportedCurrencies.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Configuration Details */}
                    {config && (
                      <div className="space-y-2 pt-2 border-t">
                        <div className="flex items-center justify-between text-sm">
                          <span>Test Mode</span>
                          <Badge variant={config.testMode ? "secondary" : "outline"}>
                            {config.testMode ? "Enabled" : "Disabled"}
                          </Badge>
                        </div>
                        {config.lastTestedAt && (
                          <div className="text-xs text-muted-foreground">
                            Last tested: {new Date(config.lastTestedAt).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button
                        onClick={() => handleConfigure(gateway)}
                        size="sm"
                        className="flex-1"
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        {status === 'not_configured' ? 'Configure' : 'Edit'}
                      </Button>

                      {config && (
                        <>
                          <Button
                            onClick={() => handleTest(config)}
                            size="sm"
                            variant="outline"
                          >
                            <TestTube className="h-4 w-4" />
                          </Button>
                          <Button
                            onClick={() => handleDelete(config)}
                            size="sm"
                            variant="outline"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}

                      {gateway.documentationUrl && (
                        <Button
                          size="sm"
                          variant="ghost"
                          asChild
                        >
                          <a 
                            href={gateway.documentationUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>

      {/* Configuration Form */}
      <GatewayConfigurationForm
        isOpen={showConfigForm}
        onClose={() => {
          setShowConfigForm(false)
          setSelectedGateway(null)
          fetchConfiguredGateways() // Refresh the list
        }}
        gateway={selectedGateway}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => {
          setShowDeleteDialog(false)
          setConfigToDelete(null)
        }}
        onConfirm={confirmDelete}
        title="Delete Gateway Configuration"
        description={`Are you sure you want to delete the configuration for ${configToDelete?.gateway?.name}? This action cannot be undone.`}
      />
    </div>
  )
}
