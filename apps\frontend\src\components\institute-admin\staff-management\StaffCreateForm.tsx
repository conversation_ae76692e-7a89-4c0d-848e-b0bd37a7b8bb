'use client'

import React, { useEffect } from 'react'
import { useFormik } from 'formik'
import { useStaffStore } from '@/stores/institute-admin/useStaffStore'
import { 
  staffCreateValidationSchema, 
  defaultStaffCreateValues,
  type StaffCreateFormData 
} from '@/lib/validations/staffValidation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { UserPlus, Eye, EyeOff } from 'lucide-react'
import { toast } from 'sonner'

interface StaffCreateFormProps {
  onSuccess: () => void
  onCancel: () => void
}

export default function StaffCreateForm({ onSuccess, onCancel }: StaffCreateFormProps) {
  const {
    availableRoles,
    availableBranches,
    isCreating,
    isFetchingRoles,
    isFetchingBranches,
    createStaff,
    fetchAvailableRoles,
    fetchAvailableBranches
  } = useStaffStore()

  const [showPassword, setShowPassword] = React.useState(false)

  useEffect(() => {
    fetchAvailableRoles()
    fetchAvailableBranches()
  }, [])

  const formik = useFormik<StaffCreateFormData>({
    initialValues: defaultStaffCreateValues,
    validationSchema: staffCreateValidationSchema,
    onSubmit: async (values) => {
      try {
        await createStaff({
          firstName: values.firstName,
          lastName: values.lastName,
          email: values.email,
          phone: values.phone || undefined,
          password: values.password,
          legacyRole: values.legacyRole,
          branch_id: values.branch_id || undefined,
          isActive: values.isActive
        })
        onSuccess()
      } catch (error) {
        // Error is already handled in the store
      }
    }
  })

  const handleRoleChange = (value: string) => {
    formik.setFieldValue('legacyRole', value)
  }

  const handleBranchChange = (value: string) => {
    formik.setFieldValue('branch_id', value === 'no-branch' ? '' : value)
  }

  const handleActiveChange = (checked: boolean) => {
    formik.setFieldValue('isActive', checked)
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserPlus className="h-5 w-5" />
          Add New Staff Member
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Personal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name *</Label>
              <Input
                id="firstName"
                name="firstName"
                type="text"
                placeholder="Enter first name"
                value={formik.values.firstName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className={formik.touched.firstName && formik.errors.firstName ? 'border-destructive' : ''}
              />
              {formik.touched.firstName && formik.errors.firstName && (
                <p className="text-sm text-destructive">{formik.errors.firstName}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name *</Label>
              <Input
                id="lastName"
                name="lastName"
                type="text"
                placeholder="Enter last name"
                value={formik.values.lastName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className={formik.touched.lastName && formik.errors.lastName ? 'border-destructive' : ''}
              />
              {formik.touched.lastName && formik.errors.lastName && (
                <p className="text-sm text-destructive">{formik.errors.lastName}</p>
              )}
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="Enter email address"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className={formik.touched.email && formik.errors.email ? 'border-destructive' : ''}
              />
              {formik.touched.email && formik.errors.email && (
                <p className="text-sm text-destructive">{formik.errors.email}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                placeholder="Enter phone number"
                value={formik.values.phone}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className={formik.touched.phone && formik.errors.phone ? 'border-destructive' : ''}
              />
              {formik.touched.phone && formik.errors.phone && (
                <p className="text-sm text-destructive">{formik.errors.phone}</p>
              )}
            </div>
          </div>

          {/* Password */}
          <div className="space-y-2">
            <Label htmlFor="password">Password *</Label>
            <div className="relative">
              <Input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter password"
                value={formik.values.password}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className={formik.touched.password && formik.errors.password ? 'border-destructive pr-10' : 'pr-10'}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {formik.touched.password && formik.errors.password && (
              <p className="text-sm text-destructive">{formik.errors.password}</p>
            )}
          </div>

          {/* Role and Branch */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="legacyRole">Role *</Label>
              <Select value={formik.values.legacyRole} onValueChange={handleRoleChange}>
                <SelectTrigger className={formik.touched.legacyRole && formik.errors.legacyRole ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {isFetchingRoles ? (
                    <SelectItem value="loading" disabled>Loading roles...</SelectItem>
                  ) : availableRoles.length === 0 ? (
                    <SelectItem value="no-roles" disabled>No roles available</SelectItem>
                  ) : (
                    availableRoles.map((role) => (
                      <SelectItem key={role.id} value={role.name}>
                        {role.name|| role.description}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {formik.touched.legacyRole && formik.errors.legacyRole && (
                <p className="text-sm text-destructive">{formik.errors.legacyRole}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="branch_id">Branch</Label>
              <Select value={formik.values.branch_id || 'no-branch'} onValueChange={handleBranchChange}>
                <SelectTrigger className={formik.touched.branch_id && formik.errors.branch_id ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Select a branch" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="no-branch">No branch assigned</SelectItem>
                  {isFetchingBranches ? (
                    <SelectItem value="loading" disabled>Loading branches...</SelectItem>
                  ) : availableBranches.length === 0 ? (
                    <SelectItem value="no-branches" disabled>No branches available</SelectItem>
                  ) : (
                    availableBranches.map((branch) => (
                      <SelectItem key={branch.id} value={branch.id}>
                        {branch.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {formik.touched.branch_id && formik.errors.branch_id && (
                <p className="text-sm text-destructive">{formik.errors.branch_id}</p>
              )}
            </div>
          </div>

          {/* Status */}
          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formik.values.isActive}
              onCheckedChange={handleActiveChange}
            />
            <Label htmlFor="isActive">Active</Label>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating || !formik.isValid}>
              {isCreating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Create Staff Member
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
