'use client'

import React, { useState } from 'react'
import { useStaffStore } from '@/stores/institute-admin/useStaffStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Filter, 
  X, 
  RotateCcw,
  Users,
  Building,
  Shield
} from 'lucide-react'

export default function StaffFilters() {
  const {
    availableRoles,
    availableBranches,
    filters,
    setFilters,
    staffMembers
  } = useStaffStore()

  const [searchInput, setSearchInput] = useState(filters.search)

  const handleSearchChange = (value: string) => {
    setSearchInput(value)
  }

  const handleSearchSubmit = () => {
    setFilters({ search: searchInput })
  }

  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearchSubmit()
    }
  }

  const handleRoleFilter = (role: string) => {
    setFilters({ role })
  }

  const handleBranchFilter = (branch_id: string) => {
    setFilters({ branch_id })
  }

  const handleStatusFilter = (status: 'all' | 'active' | 'inactive') => {
    setFilters({ status })
  }

  const clearAllFilters = () => {
    setSearchInput('')
    setFilters({
      search: '',
      role: 'all',
      branch_id: 'all',
      status: 'all'
    })
  }

  const hasActiveFilters = 
    filters.search || 
    filters.role !== 'all' || 
    filters.branch_id !== 'all' || 
    filters.status !== 'all'

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.role !== 'all') count++
    if (filters.branch_id !== 'all') count++
    if (filters.status !== 'all') count++
    return count
  }

  const getSelectedRoleName = () => {
    if (filters.role === 'all') return 'All Roles'
    const role = availableRoles.find(r => r.name === filters.role)
    return role ? (role.description || role.name) : filters.role
  }

  const getSelectedBranchName = () => {
    if (filters.branch_id === 'all') return 'All Branches'
    const branch = availableBranches.find(b => b.id === filters.branch_id)
    return branch ? branch.name : 'Unknown Branch'
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFilterCount()} active
              </Badge>
            )}
          </CardTitle>
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllFilters}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-3 w-3" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search">Search Staff</Label>
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="search"
                placeholder="Search by name or email..."
                value={searchInput}
                onChange={(e) => handleSearchChange(e.target.value)}
                onKeyPress={handleSearchKeyPress}
                className="pl-10"
              />
            </div>
            <Button onClick={handleSearchSubmit} size="sm">
              Search
            </Button>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Role Filter */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Role
            </Label>
            <Select value={filters.role} onValueChange={handleRoleFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                {availableRoles.map((role) => (
                  <SelectItem key={role.id} value={role.name}>
                    {role.description || role.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Branch Filter */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              Branch
            </Label>
            <Select value={filters.branch_id} onValueChange={handleBranchFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by branch" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Branches</SelectItem>
                {availableBranches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.id}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Status
            </Label>
            <Select value={filters.status} onValueChange={handleStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Active Filters:</Label>
            <div className="flex flex-wrap gap-2">
              {filters.search && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Search: "{filters.search}"
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 ml-1"
                    onClick={() => {
                      setSearchInput('')
                      setFilters({ search: '' })
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              {filters.role !== 'all' && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Role: {getSelectedRoleName()}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 ml-1"
                    onClick={() => setFilters({ role: 'all' })}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              {filters.branch_id !== 'all' && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Branch: {getSelectedBranchName()}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 ml-1"
                    onClick={() => setFilters({ branch_id: 'all' })}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              {filters.status !== 'all' && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Status: {filters.status === 'active' ? 'Active' : 'Inactive'}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 ml-1"
                    onClick={() => setFilters({ status: 'all' })}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Results Summary */}
        <div className="text-sm text-muted-foreground">
          Showing {staffMembers.length} staff member{staffMembers.length !== 1 ? 's' : ''}
          {hasActiveFilters && ' matching your filters'}
        </div>
      </CardContent>
    </Card>
  )
}
