'use client'

import { useEffect, useState } from 'react'
import { useInstituteThemeStore } from '@/stores/institute-admin/useInstituteThemeStore'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Eye, Check, Star, Users, Palette, Monitor } from 'lucide-react'
import { toast } from 'sonner'
import { notifyThemeChange } from '@/lib/theme-utils'

export function ThemeSelector() {
  const {
    themes,
    currentTheme,
    isLoading,
    error,
    fetchThemes,
    getCurrentTheme,
    applyTheme,
    previewThemeById,
    clearError
  } = useInstituteThemeStore()

  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [applyingThemeId, setApplyingThemeId] = useState<string | null>(null)

  useEffect(() => {
    fetchThemes()
    getCurrentTheme()
  }, [fetchThemes, getCurrentTheme])

  const categories = ['all', ...Array.from(new Set(themes.map(theme => theme.category).filter(Boolean)))] as string[]
  
  const filteredThemes = selectedCategory === 'all' 
    ? themes 
    : themes.filter(theme => theme.category === selectedCategory)

  const handleApplyTheme = async (themeId: string) => {
    setApplyingThemeId(themeId)
    try {
      await applyTheme(themeId)
      toast.success('Theme applied successfully!')

      // Automatically refresh current theme data after successful application
      console.log('🔄 Refreshing current theme after successful application...')
      await getCurrentTheme()

      // Also refresh themes list to update UI state
      await fetchThemes()

      // Notify other components about theme change
      notifyThemeChange({
        themeId,
        source: 'theme-selector'
      })

      console.log('✅ Theme data refreshed successfully')
    } catch (error) {
      console.error('❌ Failed to apply theme:', error)
      toast.error('Failed to apply theme')
    } finally {
      setApplyingThemeId(null)
    }
  }

  const handlePreviewTheme = async (themeId: string) => {
    try {
      await previewThemeById(themeId)
      toast.success('Theme preview loaded')
    } catch (error) {
      toast.error('Failed to load theme preview')
    }
  }

  if (isLoading && themes.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Theme Selection</h2>
            <p className="text-muted-foreground">Choose a theme for your institute website</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full mb-4" />
                <div className="flex gap-2">
                  <Skeleton className="h-8 flex-1" />
                  <Skeleton className="h-8 w-16" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {error}
          <Button 
            variant="outline" 
            size="sm" 
            className="ml-2"
            onClick={() => {
              clearError()
              fetchThemes()
            }}
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Theme Selection</h2>
          <p className="text-muted-foreground">
            Choose a theme for your institute website. {currentTheme && `Currently using: ${currentTheme.name}`}
          </p>
        </div>
      </div>

      {/* Current Theme */}
      {currentTheme && (
        <Card className="border-primary">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Check className="h-5 w-5 text-primary" />
                  Current Theme: {currentTheme.name}
                  {isLoading && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <div className="w-3 h-3 border border-primary border-t-transparent rounded-full animate-spin" />
                      Updating...
                    </div>
                  )}
                </CardTitle>
                <CardDescription>{currentTheme.description}</CardDescription>
              </div>
              <Badge variant="default">Active</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Palette className="h-4 w-4" />
                {currentTheme.category || 'General'}
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {currentTheme.usageCount || 0} institutes
              </div>
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4" />
                {typeof currentTheme.rating === 'object'
                  ? `${currentTheme.rating.average?.toFixed(1) || 0}/5`
                  : `${currentTheme.rating || 0}/5`
                }
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Category Filter */}
      <div className="flex gap-2 flex-wrap">
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category)}
          >
            {category === 'all' ? 'All Themes' : category}
          </Button>
        ))}
      </div>

      {/* Theme Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredThemes.map((theme) => (
          <Card 
            key={theme.id} 
            className={`transition-all hover:shadow-lg ${
              currentTheme?.id === theme.id ? 'border-primary ring-2 ring-primary/20' : ''
            }`}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {theme.name}
                    {currentTheme?.id === theme.id && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </CardTitle>
                  <CardDescription>{theme.description}</CardDescription>
                </div>
                {theme.category && (
                  <Badge variant="secondary">{theme.category}</Badge>
                )}
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Theme Preview */}
              <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                {theme.preview ? (
                  <img 
                    src={theme.preview} 
                    alt={`${theme.name} preview`}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <div className="text-center text-muted-foreground">
                    <Monitor className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">Preview not available</p>
                  </div>
                )}
              </div>

              {/* Theme Colors */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Colors:</span>
                <div className="flex gap-1">
                  {Object.entries(theme.colors).slice(0, 4).map(([key, color]) => (
                    <div
                      key={key}
                      className="w-4 h-4 rounded-full border border-gray-200"
                      style={{ backgroundColor: color }}
                      title={`${key}: ${color}`}
                    />
                  ))}
                </div>
              </div>

              {/* Theme Stats */}
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  {theme.usageCount || 0}
                </div>
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4" />
                  {theme.rating?.average?.toFixed(1) || 0}/5
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => handlePreviewTheme(theme.id)}
                  disabled={isLoading}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                
                {currentTheme?.id !== theme.id && (
                  <Button
                    size="sm"
                    className="flex-1"
                    onClick={() => handleApplyTheme(theme.id)}
                    disabled={applyingThemeId === theme.id || isLoading}
                  >
                    {applyingThemeId === theme.id ? (
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />
                        Applying...
                      </div>
                    ) : 'Apply'}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredThemes.length === 0 && (
        <div className="text-center py-12">
          <Monitor className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No themes found</h3>
          <p className="text-muted-foreground">
            {selectedCategory === 'all' 
              ? 'No themes are available at the moment.' 
              : `No themes found in the "${selectedCategory}" category.`
            }
          </p>
        </div>
      )}
    </div>
  )
}
