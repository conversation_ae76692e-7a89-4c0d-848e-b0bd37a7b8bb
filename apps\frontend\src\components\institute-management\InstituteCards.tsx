'use client'

import { useState } from 'react'
import { useInstituteManagementStore, Institute } from '@/stores/super-admin/useInstituteManagementStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Globe, 
  CheckCircle, 
  XCircle,
  Building2,
  Mail,
  Phone,
  ExternalLink,
  MapPin,
  Calendar
} from 'lucide-react'

interface InstituteCardsProps {
  institutes: Institute[]
}

export function InstituteCards({ institutes }: InstituteCardsProps) {
  const { 
    setSelectedInstitute, 
    setShowEditForm, 
    deleteInstitute, 
    verifyDomain,
    isLoading 
  } = useInstituteManagementStore()

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [instituteToDelete, setInstituteToDelete] = useState<Institute | null>(null)

  const handleEdit = (institute: Institute) => {
    setSelectedInstitute(institute)
    setShowEditForm(true)
  }

  const handleDeleteClick = (institute: Institute) => {
    setInstituteToDelete(institute)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (instituteToDelete) {
      await deleteInstitute(instituteToDelete.id)
      setDeleteDialogOpen(false)
      setInstituteToDelete(null)
    }
  }

  const handleVerifyDomain = async (institute: Institute) => {
    await verifyDomain(institute.id)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (institutes.length === 0) {
    return (
      <div className="text-center py-8">
        <Building2 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No institutes found</h3>
        <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
      </div>
    )
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {institutes.map((institute) => (
          <Card key={institute.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={institute.logo?.url} alt={institute.name} />
                    <AvatarFallback>
                      {institute.name.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">{institute.name}</CardTitle>
                    <CardDescription className="text-sm">
                      {institute.slug}
                    </CardDescription>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => handleEdit(institute)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    {institute.customDomain && !institute.domainVerified && (
                      <DropdownMenuItem onClick={() => handleVerifyDomain(institute)}>
                        <Globe className="mr-2 h-4 w-4" />
                        Verify Domain
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => handleDeleteClick(institute)}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Status Badges */}
              <div className="flex gap-2 mt-2">
                <Badge variant={institute.isActive ? "default" : "secondary"}>
                  {institute.isActive ? (
                    <><CheckCircle className="h-3 w-3 mr-1" />Active</>
                  ) : (
                    <><XCircle className="h-3 w-3 mr-1" />Inactive</>
                  )}
                </Badge>
                {institute.customDomain && (
                  <Badge variant={institute.domainVerified ? "default" : "outline"}>
                    <Globe className="h-3 w-3 mr-1" />
                    {institute.domainVerified ? "Verified" : "Pending"}
                  </Badge>
                )}
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              {/* Tagline */}
              {institute.tagline && (
                <p className="text-sm text-muted-foreground italic">
                  "{institute.tagline}"
                </p>
              )}

              {/* Contact Information */}
              <div className="space-y-2">
                {institute.email && (
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{institute.email}</span>
                  </div>
                )}
                {institute.phone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{institute.phone}</span>
                  </div>
                )}
                {institute.website && (
                  <div className="flex items-center gap-2 text-sm">
                    <ExternalLink className="h-4 w-4 text-muted-foreground" />
                    <a 
                      href={institute.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline truncate"
                    >
                      Visit Website
                    </a>
                  </div>
                )}
              </div>

              {/* Address */}
              {institute.addressStreet && (
                <div className="flex items-start gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <span className="text-muted-foreground">
                    {institute.addressStreet}
                    {institute.zipCode && `, ${institute.zipCode}`}
                  </span>
                </div>
              )}

              {/* Custom Domain */}
              {institute.customDomain && (
                <div className="flex items-center gap-2 text-sm">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <span className="truncate">{institute.customDomain}</span>
                </div>
              )}

              {/* Created Date */}
              <div className="flex items-center gap-2 text-sm text-muted-foreground pt-2 border-t">
                <Calendar className="h-4 w-4" />
                <span>Created {formatDate(institute.createdAt)}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the institute "{instituteToDelete?.name}" and all associated data. 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
              disabled={isLoading}
            >
              Delete Institute
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
