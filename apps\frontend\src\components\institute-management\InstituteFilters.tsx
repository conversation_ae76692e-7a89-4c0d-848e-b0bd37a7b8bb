'use client'

import { useState } from 'react'
import { useInstituteManagementStore } from '@/stores/super-admin/useInstituteManagementStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Filter, 
  X,
  CheckCircle,
  XCircle,
  Globe,
  Building2
} from 'lucide-react'

export function InstituteFilters() {
  const { 
    filters, 
    setFilters, 
    fetchInstitutes,
    pagination 
  } = useInstituteManagementStore()

  const [localSearch, setLocalSearch] = useState(filters.search)

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setFilters({ search: localSearch })
    fetchInstitutes(1, { ...filters, search: localSearch })
  }

  const handleActiveFilter = (value: string) => {
    const isActive = value === 'all' ? undefined : value === 'true'
    setFilters({ isActive })
    fetchInstitutes(1, { ...filters, isActive })
  }

  const handleDomainFilter = (value: string) => {
    const domainVerified = value === 'all' ? undefined : value === 'true'
    setFilters({ domainVerified })
    fetchInstitutes(1, { ...filters, domainVerified })
  }

  const clearFilters = () => {
    setLocalSearch('')
    setFilters({ search: '', isActive: undefined, domainVerified: undefined })
    fetchInstitutes(1, { search: '', isActive: undefined, domainVerified: undefined })
  }

  const hasActiveFilters = filters.search || filters.isActive !== undefined || filters.domainVerified !== undefined

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Search & Filter
            </CardTitle>
            <CardDescription>
              Find institutes by name, email, or status
            </CardDescription>
          </div>
          {hasActiveFilters && (
            <Button variant="outline" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <form onSubmit={handleSearchSubmit} className="flex items-center gap-4">
          <div className="flex-1">
            <Label htmlFor="search">Search Institutes</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="search"
                type="text"
                placeholder="Search by name, email, slug, or website..."
                value={localSearch}
                onChange={(e) => setLocalSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Button type="submit" className="mt-6">
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
        </form>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="status-filter">Status Filter</Label>
            <Select
              value={filters.isActive === undefined ? 'all' : filters.isActive.toString()}
              onValueChange={handleActiveFilter}
            >
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Institutes</SelectItem>
                <SelectItem value="true">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Active Only
                  </div>
                </SelectItem>
                <SelectItem value="false">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-600" />
                    Inactive Only
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="domain-filter">Domain Verification</Label>
            <Select
              value={filters.domainVerified === undefined ? 'all' : filters.domainVerified.toString()}
              onValueChange={handleDomainFilter}
            >
              <SelectTrigger>
                <SelectValue placeholder="Filter by domain status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Domains</SelectItem>
                <SelectItem value="true">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-blue-600" />
                    Verified Only
                  </div>
                </SelectItem>
                <SelectItem value="false">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-gray-600" />
                    Unverified Only
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-muted-foreground">Active filters:</span>
            {filters.search && (
              <Badge variant="secondary">
                Search: "{filters.search}"
              </Badge>
            )}
            {filters.isActive !== undefined && (
              <Badge variant="secondary">
                Status: {filters.isActive ? 'Active' : 'Inactive'}
              </Badge>
            )}
            {filters.domainVerified !== undefined && (
              <Badge variant="secondary">
                Domain: {filters.domainVerified ? 'Verified' : 'Unverified'}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
