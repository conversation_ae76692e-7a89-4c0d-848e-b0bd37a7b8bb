'use client'

import { useEffect, useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { useInstituteManagementStore, Institute } from '@/stores/super-admin/useInstituteManagementStore'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import {
  Loader2,
  Building2,
  User,
  Mail,
  Lock,
  Globe,
  MapPin,
  Phone,
  Link,
  CheckCircle,
  Info,
  ArrowLeft,
  ArrowRight,
  Upload,
  X,
  Eye,
  EyeOff,
  Sparkles,
  Shield,
  Users,
  Settings
} from 'lucide-react'

interface InstituteFormProps {
  isOpen: boolean
  onClose: () => void
  mode: 'create' | 'edit'
  institute?: Institute
}

const validationSchema = Yup.object({
  // Institute fields
  name: Yup.string().required('Institute name is required'),
  email: Yup.string().email('Invalid email format'),
  phone: Yup.string(),
  website: Yup.string().url('Invalid URL format'),
  tagline: Yup.string(),
  customDomain: Yup.string(),

  // Address fields
  addressStreet: Yup.string(),
  cityId: Yup.string(),
  stateId: Yup.string(),
  countryId: Yup.string(),
  districtId: Yup.string(),
  zipCode: Yup.string(),

  // Admin fields (only for create mode)
  adminFirstName: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.required('Admin first name is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminLastName: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.required('Admin last name is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminEmail: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.email('Invalid email').required('Admin email is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminPassword: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.min(6, 'Password must be at least 6 characters').required('Admin password is required'),
    otherwise: (schema) => schema.notRequired()
  }),
})

export function InstituteForm({ isOpen, onClose, mode, institute }: InstituteFormProps) {
  const { createInstitute, updateInstitute, isLoading } = useInstituteManagementStore()
  const [currentStep, setCurrentStep] = useState(1)
  const [showPassword, setShowPassword] = useState(false)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)

  const totalSteps = mode === 'create' ? 3 : 1

  const formik = useFormik({
    initialValues: {
      // Institute fields
      name: institute?.name || '',
      email: institute?.email || '',
      phone: institute?.phone || '',
      website: institute?.website || '',
      tagline: institute?.tagline || '',
      customDomain: institute?.customDomain || '',

      // Address fields
      addressStreet: institute?.addressStreet || '',
      cityId: institute?.cityId || '',
      stateId: institute?.stateId || '',
      countryId: institute?.countryId || '',
      districtId: institute?.districtId || '',
      zipCode: institute?.zipCode || '',

      // Admin fields (only for create)
      adminFirstName: '',
      adminLastName: '',
      adminEmail: '',
      adminPassword: '',
    },
    validationSchema,
    validationContext: { mode },
    onSubmit: async (values) => {
      let success = false

      if (mode === 'create') {
        success = await createInstitute(values)
      } else if (mode === 'edit' && institute) {
        const { adminFirstName, adminLastName, adminEmail, adminPassword, ...updateData } = values
        success = await updateInstitute(institute.id, updateData)
      }

      if (success) {
        onClose()
      }
    },
  })

  // Reset form when institute changes
  useEffect(() => {
    if (institute && mode === 'edit') {
      formik.setValues({
        name: institute.name || '',
        email: institute.email || '',
        phone: institute.phone || '',
        website: institute.website || '',
        tagline: institute.tagline || '',
        customDomain: institute.customDomain || '',
        addressStreet: institute.addressStreet || '',
        cityId: institute.cityId || '',
        stateId: institute.stateId || '',
        countryId: institute.countryId || '',
        districtId: institute.districtId || '',
        zipCode: institute.zipCode || '',
        adminFirstName: '',
        adminLastName: '',
        adminEmail: '',
        adminPassword: '',
      })
    }
  }, [institute, mode])

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return 'Basic Information'
      case 2: return 'Location & Settings'
      case 3: return 'Administrator Account'
      default: return ''
    }
  }

  const getStepDescription = (step: number) => {
    switch (step) {
      case 1: return 'Enter institute name, contact details, and branding'
      case 2: return 'Configure address, domain, and location settings'
      case 3: return 'Create administrator account for institute management'
      default: return ''
    }
  }

  const getStepIcon = (step: number) => {
    switch (step) {
      case 1: return Building2
      case 2: return Settings
      case 3: return User
      default: return Building2
    }
  }

  const isStepValid = (step: number) => {
    switch (step) {
      case 1:
        return formik.values.name && !formik.errors.name
      case 2:
        return true // Optional fields
      case 3:
        return mode === 'edit' || (
          formik.values.adminFirstName &&
          formik.values.adminLastName &&
          formik.values.adminEmail &&
          formik.values.adminPassword &&
          !formik.errors.adminFirstName &&
          !formik.errors.adminLastName &&
          !formik.errors.adminEmail &&
          !formik.errors.adminPassword
        )
      default:
        return false
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[95vh] overflow-hidden p-0">
        {/* Enhanced Header */}
        <div className="relative bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 text-white">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Building2 className="h-8 w-8 text-white" />
                </div>
                <div>
                  <DialogTitle className="text-2xl font-bold text-white">
                    {mode === 'create' ? 'Create New Institute' : 'Edit Institute'}
                  </DialogTitle>
                  <DialogDescription className="text-blue-100 text-base mt-1">
                    {mode === 'create'
                      ? 'Set up a new educational institute with complete configuration'
                      : 'Update institute information and settings'
                    }
                  </DialogDescription>
                </div>
              </div>

              {mode === 'create' && (
                <div className="text-right">
                  <div className="text-blue-100 text-sm font-medium mb-1">
                    Step {currentStep} of {totalSteps}
                  </div>
                  <Progress
                    value={(currentStep / totalSteps) * 100}
                    className="w-32 h-2 bg-white/20"
                  />
                </div>
              )}
            </div>

            {/* Enhanced Progress Steps */}
            {mode === 'create' && (
              <div className="mt-8">
                <div className="flex items-center justify-between max-w-2xl">
                  {[1, 2, 3].map((step) => {
                    const StepIcon = getStepIcon(step)
                    const isActive = step === currentStep
                    const isCompleted = step < currentStep
                    const isValid = isStepValid(step)

                    return (
                      <div key={step} className="flex items-center">
                        <div className="flex flex-col items-center">
                          <div className={`
                            flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300
                            ${isCompleted
                              ? 'bg-green-500 border-green-500 text-white shadow-lg'
                              : isActive
                                ? 'bg-white border-white text-blue-600 shadow-lg scale-110'
                                : 'border-white/40 text-white/60'
                            }
                          `}>
                            {isCompleted ? (
                              <CheckCircle className="h-6 w-6" />
                            ) : (
                              <StepIcon className="h-6 w-6" />
                            )}
                          </div>
                          <div className="mt-2 text-center">
                            <div className={`text-sm font-medium ${
                              isActive ? 'text-white' : 'text-blue-100'
                            }`}>
                              {getStepTitle(step)}
                            </div>
                            <div className="text-xs text-blue-200 mt-1 max-w-24">
                              {getStepDescription(step)}
                            </div>
                          </div>
                        </div>
                        {step < 3 && (
                          <div className={`w-24 h-0.5 mx-4 transition-all duration-300 ${
                            step < currentStep ? 'bg-green-400' : 'bg-white/30'
                          }`} />
                        )}
                      </div>
                    )
                  })}
                </div>

                {/* Address Information */}
                <Card className="border-l-4 border-l-purple-500">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-base flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-purple-600" />
                      Physical Address
                    </CardTitle>
                    <CardDescription>
                      Where your institute is located
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="addressStreet" className="text-sm font-medium">Street Address</Label>
                      <Textarea
                        id="addressStreet"
                        name="addressStreet"
                        placeholder="123 Education Street, Building A, Floor 2"
                        value={formik.values.addressStreet}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        className="mt-1 focus:border-purple-500 resize-none"
                        rows={2}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <Label htmlFor="countryId" className="text-sm font-medium">Country</Label>
                        <Select
                          value={formik.values.countryId}
                          onValueChange={(value) => formik.setFieldValue('countryId', value)}
                        >
                          <SelectTrigger className="mt-1 focus:border-purple-500">
                            <SelectValue placeholder="Select country" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">🇺🇸 United States</SelectItem>
                            <SelectItem value="2">🇨🇦 Canada</SelectItem>
                            <SelectItem value="3">🇬🇧 United Kingdom</SelectItem>
                            <SelectItem value="4">🇦🇺 Australia</SelectItem>
                            <SelectItem value="5">🇮🇳 India</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="stateId" className="text-sm font-medium">State/Province</Label>
                        <Select
                          value={formik.values.stateId}
                          onValueChange={(value) => formik.setFieldValue('stateId', value)}
                        >
                          <SelectTrigger className="mt-1 focus:border-purple-500">
                            <SelectValue placeholder="Select state" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">California</SelectItem>
                            <SelectItem value="2">New York</SelectItem>
                            <SelectItem value="3">Texas</SelectItem>
                            <SelectItem value="4">Florida</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="cityId" className="text-sm font-medium">City</Label>
                        <Select
                          value={formik.values.cityId}
                          onValueChange={(value) => formik.setFieldValue('cityId', value)}
                        >
                          <SelectTrigger className="mt-1 focus:border-purple-500">
                            <SelectValue placeholder="Select city" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">Los Angeles</SelectItem>
                            <SelectItem value="2">San Francisco</SelectItem>
                            <SelectItem value="3">San Diego</SelectItem>
                            <SelectItem value="4">Sacramento</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="zipCode" className="text-sm font-medium">ZIP/Postal Code</Label>
                        <Input
                          id="zipCode"
                          name="zipCode"
                          placeholder="90210"
                          value={formik.values.zipCode}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className="mt-1 focus:border-purple-500"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Domain Settings */}
                <Card className="border-l-4 border-l-indigo-500">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Globe className="h-4 w-4 text-indigo-600" />
                      Domain Configuration
                    </CardTitle>
                    <CardDescription>
                      Set up custom domain for your institute (optional)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div>
                      <Label htmlFor="customDomain" className="flex items-center gap-2 text-sm font-medium">
                        <Link className="h-4 w-4 text-gray-500" />
                        Custom Domain
                        <Badge variant="secondary" className="text-xs">Optional</Badge>
                      </Label>
                      <Input
                        id="customDomain"
                        name="customDomain"
                        placeholder="myinstitute.com"
                        value={formik.values.customDomain}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        className="mt-1 focus:border-indigo-500"
                      />
                      <div className="mt-2 p-3 bg-indigo-50 rounded-lg">
                        <div className="flex items-start gap-2">
                          <Info className="h-4 w-4 text-indigo-600 mt-0.5" />
                          <div className="text-sm text-indigo-700">
                            <p className="font-medium">Custom Domain Benefits:</p>
                            <ul className="mt-1 space-y-1 text-xs">
                              <li>• Students access courses via your domain</li>
                              <li>• Professional branding and trust</li>
                              <li>• Better SEO and marketing</li>
                              <li>• Domain verification required before activation</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>

        {/* Form Content */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={formik.handleSubmit} className="p-8 space-y-8">

            {/* Step 1: Basic Information */}
            {(mode === 'edit' || currentStep === 1) && (
              <div className="space-y-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Building2 className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
                    <p className="text-sm text-gray-600">Enter the fundamental details about your institute</p>
                  </div>
                </div>

                {/* Institute Name & Branding */}
                <Card className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Sparkles className="h-4 w-4 text-blue-600" />
                      Institute Identity
                    </CardTitle>
                    <CardDescription>
                      Define your institute's name and visual identity
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                      {/* Logo Upload */}
                      <div className="lg:col-span-1">
                        <Label className="text-sm font-medium">Institute Logo</Label>
                        <div className="mt-2">
                          <div className="flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 transition-colors cursor-pointer bg-gray-50">
                            {logoPreview ? (
                              <div className="relative">
                                <img src={logoPreview} alt="Logo preview" className="h-24 w-24 object-cover rounded-lg" />
                                <button
                                  type="button"
                                  onClick={() => setLogoPreview(null)}
                                  className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                                >
                                  <X className="h-3 w-3" />
                                </button>
                              </div>
                            ) : (
                              <div className="text-center">
                                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                <p className="text-sm text-gray-600">Upload Logo</p>
                                <p className="text-xs text-gray-400">PNG, JPG up to 2MB</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Name and Tagline */}
                      <div className="lg:col-span-2 space-y-4">
                        <div>
                          <Label htmlFor="name" className="flex items-center gap-2 text-sm font-medium">
                            Institute Name
                            <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="name"
                            name="name"
                            placeholder="e.g., ABC Academy of Excellence"
                            value={formik.values.name}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className={`mt-1 ${formik.touched.name && formik.errors.name ? 'border-red-500 focus:border-red-500' : 'focus:border-blue-500'}`}
                          />
                          {formik.touched.name && formik.errors.name && (
                            <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                              <Info className="h-3 w-3" />
                              {formik.errors.name}
                            </p>
                          )}
                          {formik.values.name && (
                            <div className="mt-2 p-2 bg-blue-50 rounded-md">
                              <p className="text-xs text-blue-700">
                                <strong>URL Slug:</strong> {formik.values.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')}
                              </p>
                            </div>
                          )}
                        </div>

                        <div>
                          <Label htmlFor="tagline" className="text-sm font-medium">Institute Tagline</Label>
                          <Input
                            id="tagline"
                            name="tagline"
                            placeholder="e.g., Empowering minds, shaping futures"
                            value={formik.values.tagline}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className="mt-1 focus:border-blue-500"
                          />
                          <p className="text-xs text-gray-500 mt-1">A brief, inspiring description of your institute</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Contact Information */}
                <Card className="border-l-4 border-l-green-500">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Mail className="h-4 w-4 text-green-600" />
                      Contact Information
                    </CardTitle>
                    <CardDescription>
                      How people can reach your institute
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="email" className="flex items-center gap-2 text-sm font-medium">
                          <Mail className="h-4 w-4 text-gray-500" />
                          Email Address
                        </Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={formik.values.email}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className={`mt-1 ${formik.touched.email && formik.errors.email ? 'border-red-500' : 'focus:border-green-500'}`}
                        />
                        {formik.touched.email && formik.errors.email && (
                          <p className="text-red-500 text-xs mt-1">{formik.errors.email}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="phone" className="flex items-center gap-2 text-sm font-medium">
                          <Phone className="h-4 w-4 text-gray-500" />
                          Phone Number
                        </Label>
                        <Input
                          id="phone"
                          name="phone"
                          placeholder="+****************"
                          value={formik.values.phone}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className="mt-1 focus:border-green-500"
                        />
                      </div>

                      <div>
                        <Label htmlFor="website" className="flex items-center gap-2 text-sm font-medium">
                          <Globe className="h-4 w-4 text-gray-500" />
                          Website URL
                        </Label>
                        <Input
                          id="website"
                          name="website"
                          placeholder="https://institute.com"
                          value={formik.values.website}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className={`mt-1 ${formik.touched.website && formik.errors.website ? 'border-red-500' : 'focus:border-green-500'}`}
                        />
                        {formik.touched.website && formik.errors.website && (
                          <p className="text-red-500 text-xs mt-1">{formik.errors.website}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Step 2: Location & Settings */}
            {mode === 'create' && currentStep === 2 && (
              <div className="space-y-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Settings className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Location & Settings</h3>
                    <p className="text-sm text-gray-600">Configure address, domain, and operational settings</p>
                  </div>
                </div>

                      <div>
                        <Label htmlFor="email" className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          Contact Email
                        </Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={formik.values.email}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className={formik.touched.email && formik.errors.email ? 'border-red-500' : ''}
                        />
                        {formik.touched.email && formik.errors.email && (
                          <p className="text-red-500 text-xs mt-1">{formik.errors.email}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="phone" className="flex items-center gap-2">
                          <Phone className="h-4 w-4" />
                          Contact Phone
                        </Label>
                        <Input
                          id="phone"
                          name="phone"
                          placeholder="+****************"
                          value={formik.values.phone}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className={formik.touched.phone && formik.errors.phone ? 'border-red-500' : ''}
                        />
                        {formik.touched.phone && formik.errors.phone && (
                          <p className="text-red-500 text-xs mt-1">{formik.errors.phone}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="website" className="flex items-center gap-2">
                          <Globe className="h-4 w-4" />
                          Website URL
                        </Label>
                        <Input
                          id="website"
                          name="website"
                          placeholder="https://institute.com"
                          value={formik.values.website}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className={formik.touched.website && formik.errors.website ? 'border-red-500' : ''}
                        />
                        {formik.touched.website && formik.errors.website && (
                          <p className="text-red-500 text-xs mt-1">{formik.errors.website}</p>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="tagline">Institute Tagline</Label>
                        <Input
                          id="tagline"
                          name="tagline"
                          placeholder="Empowering minds, shaping futures"
                          value={formik.values.tagline}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className={formik.touched.tagline && formik.errors.tagline ? 'border-red-500' : ''}
                        />
                        {formik.touched.tagline && formik.errors.tagline && (
                          <p className="text-red-500 text-xs mt-1">{formik.errors.tagline}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="customDomain" className="flex items-center gap-2">
                          <Link className="h-4 w-4" />
                          Custom Domain
                          <Badge variant="secondary" className="text-xs">Optional</Badge>
                        </Label>
                        <Input
                          id="customDomain"
                          name="customDomain"
                          placeholder="myinstitute.com"
                          value={formik.values.customDomain}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className={formik.touched.customDomain && formik.errors.customDomain ? 'border-red-500' : ''}
                        />
                        {formik.touched.customDomain && formik.errors.customDomain && (
                          <p className="text-red-500 text-xs mt-1">{formik.errors.customDomain}</p>
                        )}
                        <p className="text-xs text-muted-foreground mt-1">
                          Students will access courses via this domain
                        </p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Address Information Section */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Address & Location
                    </h4>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="addressStreet">Street Address</Label>
                        <Input
                          id="addressStreet"
                          name="addressStreet"
                          placeholder="123 Education Street"
                          value={formik.values.addressStreet}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className={formik.touched.addressStreet && formik.errors.addressStreet ? 'border-red-500' : ''}
                        />
                        {formik.touched.addressStreet && formik.errors.addressStreet && (
                          <p className="text-red-500 text-xs mt-1">{formik.errors.addressStreet}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="countryId">Country</Label>
                          <Select
                            value={formik.values.countryId}
                            onValueChange={(value) => formik.setFieldValue('countryId', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select country" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">United States</SelectItem>
                              <SelectItem value="2">Canada</SelectItem>
                              <SelectItem value="3">United Kingdom</SelectItem>
                              <SelectItem value="4">Australia</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="stateId">State/Province</Label>
                          <Select
                            value={formik.values.stateId}
                            onValueChange={(value) => formik.setFieldValue('stateId', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select state" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">California</SelectItem>
                              <SelectItem value="2">New York</SelectItem>
                              <SelectItem value="3">Texas</SelectItem>
                              <SelectItem value="4">Florida</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="cityId">City</Label>
                          <Select
                            value={formik.values.cityId}
                            onValueChange={(value) => formik.setFieldValue('cityId', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select city" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">Los Angeles</SelectItem>
                              <SelectItem value="2">San Francisco</SelectItem>
                              <SelectItem value="3">San Diego</SelectItem>
                              <SelectItem value="4">Sacramento</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="districtId">District</Label>
                          <Select
                            value={formik.values.districtId}
                            onValueChange={(value) => formik.setFieldValue('districtId', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select district" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">Downtown</SelectItem>
                              <SelectItem value="2">Westside</SelectItem>
                              <SelectItem value="3">East District</SelectItem>
                              <SelectItem value="4">North District</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="zipCode">ZIP/Postal Code</Label>
                          <Input
                            id="zipCode"
                            name="zipCode"
                            placeholder="90210"
                            value={formik.values.zipCode}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className={formik.touched.zipCode && formik.errors.zipCode ? 'border-red-500' : ''}
                          />
                          {formik.touched.zipCode && formik.errors.zipCode && (
                            <p className="text-red-500 text-xs mt-1">{formik.errors.zipCode}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 3: Administrator Account (only for create mode) */}
            {mode === 'create' && currentStep === 3 && (
              <div className="space-y-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-emerald-100 rounded-lg">
                    <User className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Administrator Account</h3>
                    <p className="text-sm text-gray-600">Create the primary admin account for institute management</p>
                  </div>
                </div>

                {/* Admin Account Creation */}
                <Card className="border-l-4 border-l-emerald-500">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Shield className="h-4 w-4 text-emerald-600" />
                      Administrator Details
                    </CardTitle>
                    <CardDescription>
                      This person will have full control over the institute
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Personal Information */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Personal Information
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="adminFirstName" className="flex items-center gap-2 text-sm font-medium">
                            First Name
                            <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="adminFirstName"
                            name="adminFirstName"
                            placeholder="John"
                            value={formik.values.adminFirstName}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className={`mt-1 ${formik.touched.adminFirstName && formik.errors.adminFirstName ? 'border-red-500' : 'focus:border-emerald-500'}`}
                          />
                          {formik.touched.adminFirstName && formik.errors.adminFirstName && (
                            <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                              <Info className="h-3 w-3" />
                              {formik.errors.adminFirstName}
                            </p>
                          )}
                        </div>

                        <div>
                          <Label htmlFor="adminLastName" className="flex items-center gap-2 text-sm font-medium">
                            Last Name
                            <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="adminLastName"
                            name="adminLastName"
                            placeholder="Doe"
                            value={formik.values.adminLastName}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className={`mt-1 ${formik.touched.adminLastName && formik.errors.adminLastName ? 'border-red-500' : 'focus:border-emerald-500'}`}
                          />
                          {formik.touched.adminLastName && formik.errors.adminLastName && (
                            <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                              <Info className="h-3 w-3" />
                              {formik.errors.adminLastName}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Login Credentials */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                        <Lock className="h-4 w-4" />
                        Login Credentials
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="adminEmail" className="flex items-center gap-2 text-sm font-medium">
                            <Mail className="h-4 w-4 text-gray-500" />
                            Email Address
                            <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="adminEmail"
                            name="adminEmail"
                            type="email"
                            placeholder="<EMAIL>"
                            value={formik.values.adminEmail}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className={`mt-1 ${formik.touched.adminEmail && formik.errors.adminEmail ? 'border-red-500' : 'focus:border-emerald-500'}`}
                          />
                          {formik.touched.adminEmail && formik.errors.adminEmail && (
                            <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                              <Info className="h-3 w-3" />
                              {formik.errors.adminEmail}
                            </p>
                          )}
                          <p className="text-xs text-gray-500 mt-1">This will be used for login</p>
                        </div>

                        <div>
                          <Label htmlFor="adminPassword" className="flex items-center gap-2 text-sm font-medium">
                            <Lock className="h-4 w-4 text-gray-500" />
                            Password
                            <span className="text-red-500">*</span>
                          </Label>
                          <div className="relative mt-1">
                            <Input
                              id="adminPassword"
                              name="adminPassword"
                              type={showPassword ? "text" : "password"}
                              placeholder="Minimum 6 characters"
                              value={formik.values.adminPassword}
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                              className={`pr-10 ${formik.touched.adminPassword && formik.errors.adminPassword ? 'border-red-500' : 'focus:border-emerald-500'}`}
                            />
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                            >
                              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                          </div>
                          {formik.touched.adminPassword && formik.errors.adminPassword && (
                            <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                              <Info className="h-3 w-3" />
                              {formik.errors.adminPassword}
                            </p>
                          )}
                          <p className="text-xs text-gray-500 mt-1">Strong password recommended</p>
                        </div>
                      </div>
                    </div>

                    {/* Admin Privileges Info */}
                    <div className="bg-gradient-to-r from-emerald-50 to-blue-50 p-4 rounded-lg border border-emerald-200">
                      <div className="flex items-start gap-3">
                        <Shield className="h-5 w-5 text-emerald-600 mt-0.5" />
                        <div>
                          <h4 className="text-sm font-medium text-emerald-900">Administrator Privileges</h4>
                          <p className="text-sm text-emerald-700 mt-1 mb-2">
                            This account will have complete control over the institute with the following permissions:
                          </p>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-emerald-600">
                            <div className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3" />
                              Manage courses and content
                            </div>
                            <div className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3" />
                              Enroll and manage students
                            </div>
                            <div className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3" />
                              Configure institute settings
                            </div>
                            <div className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3" />
                              Access analytics and reports
                            </div>
                            <div className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3" />
                              Manage staff and instructors
                            </div>
                            <div className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3" />
                              Handle billing and subscriptions
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

          </form>
        </div>

        {/* Enhanced Footer Actions */}
        <div className="border-t bg-gray-50 px-8 py-6">
          <div className="flex items-center justify-between">
            {/* Left Side - Previous Button */}
            <div className="flex items-center gap-3">
              {mode === 'create' && currentStep > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Previous
                </Button>
              )}
              {mode === 'create' && currentStep === 1 && (
                <div className="text-sm text-gray-500">
                  Step {currentStep} of {totalSteps}
                </div>
              )}
            </div>

            {/* Right Side - Action Buttons */}
            <div className="flex items-center gap-3">
              <Button
                type="button"
                variant="ghost"
                onClick={onClose}
                className="text-gray-600 hover:text-gray-800"
              >
                Cancel
              </Button>

              {mode === 'create' && currentStep < totalSteps ? (
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={!isStepValid(currentStep)}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6"
                >
                  Continue
                  <ArrowRight className="h-4 w-4" />
                </Button>
              ) : (
                <Button
                  type="submit"
                  disabled={isLoading || (mode === 'create' && !isStepValid(currentStep))}
                  className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8"
                  onClick={formik.handleSubmit}
                >
                  {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                  {!isLoading && <CheckCircle className="h-4 w-4" />}
                  {mode === 'create' ? 'Create Institute' : 'Update Institute'}
                </Button>
              )}
            </div>
          </div>

          {/* Progress Indicator for Create Mode */}
          {mode === 'create' && (
            <div className="mt-4">
              <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                <span>Progress</span>
                <span>{Math.round((currentStep / totalSteps) * 100)}% Complete</span>
              </div>
              <Progress
                value={(currentStep / totalSteps) * 100}
                className="h-2"
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
