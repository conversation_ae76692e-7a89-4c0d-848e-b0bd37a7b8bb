'use client'

import { useEffect, useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { useInstituteManagementStore, Institute } from '@/stores/super-admin/useInstituteManagementStore'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Loader2,
  Building2,
  User,
  Info,
  ArrowLeft,
  ArrowRight,
  Eye,
  EyeOff,
  Settings
} from 'lucide-react'

interface InstituteFormProps {
  isOpen: boolean
  onClose: () => void
  mode: 'create' | 'edit'
  institute?: Institute
}

const validationSchema = Yup.object({
  name: Yup.string().required('Institute name is required'),
  email: Yup.string().email('Invalid email format'),
  phone: Yup.string(),
  website: Yup.string().url('Invalid URL format'),
  tagline: Yup.string(),
  customDomain: Yup.string(),
  addressStreet: Yup.string(),
  cityId: Yup.string(),
  stateId: Yup.string(),
  countryId: Yup.string(),
  districtId: Yup.string(),
  zipCode: Yup.string(),
  adminFirstName: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.required('Admin first name is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminLastName: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.required('Admin last name is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminEmail: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.email('Invalid email').required('Admin email is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminPassword: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.min(6, 'Password must be at least 6 characters').required('Admin password is required'),
    otherwise: (schema) => schema.notRequired()
  }),
})

export function InstituteFormNew({ isOpen, onClose, mode, institute }: InstituteFormProps) {
  const { createInstitute, updateInstitute, isLoading } = useInstituteManagementStore()
  const [currentStep, setCurrentStep] = useState(1)
  const [showPassword, setShowPassword] = useState(false)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)

  const totalSteps = mode === 'create' ? 3 : 1

  const formik = useFormik({
    initialValues: {
      name: institute?.name || '',
      email: institute?.email || '',
      phone: institute?.phone || '',
      website: institute?.website || '',
      tagline: institute?.tagline || '',
      customDomain: institute?.customDomain || '',
      addressStreet: institute?.addressStreet || '',
      cityId: institute?.cityId || '',
      stateId: institute?.stateId || '',
      countryId: institute?.countryId || '',
      districtId: institute?.districtId || '',
      zipCode: institute?.zipCode || '',
      adminFirstName: '',
      adminLastName: '',
      adminEmail: '',
      adminPassword: '',
    },
    validationSchema,
    validationContext: { mode },
    onSubmit: async (values) => {
      let success = false

      if (mode === 'create') {
        success = await createInstitute(values)
      } else if (mode === 'edit' && institute) {
        const { adminFirstName, adminLastName, adminEmail, adminPassword, ...updateData } = values
        success = await updateInstitute(institute.id, updateData)
      }

      if (success) {
        onClose()
      }
    },
  })

  useEffect(() => {
    if (institute && mode === 'edit') {
      formik.setValues({
        name: institute.name || '',
        email: institute.email || '',
        phone: institute.phone || '',
        website: institute.website || '',
        tagline: institute.tagline || '',
        customDomain: institute.customDomain || '',
        addressStreet: institute.addressStreet || '',
        cityId: institute.cityId || '',
        stateId: institute.stateId || '',
        countryId: institute.countryId || '',
        districtId: institute.districtId || '',
        zipCode: institute.zipCode || '',
        adminFirstName: '',
        adminLastName: '',
        adminEmail: '',
        adminPassword: '',
      })
    }
  }, [institute, mode])

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return 'Basic Information'
      case 2: return 'Location & Settings'
      case 3: return 'Administrator Account'
      default: return ''
    }
  }

  const getStepDescription = (step: number) => {
    switch (step) {
      case 1: return 'Enter institute name, contact details, and branding'
      case 2: return 'Configure address, domain, and location settings'
      case 3: return 'Create administrator account for institute management'
      default: return ''
    }
  }

  const getStepIcon = (step: number) => {
    switch (step) {
      case 1: return Building2
      case 2: return Settings
      case 3: return User
      default: return Building2
    }
  }

  const isStepValid = (step: number) => {
    switch (step) {
      case 1:
        return formik.values.name && !formik.errors.name
      case 2:
        return true
      case 3:
        return mode === 'edit' || (
          formik.values.adminFirstName &&
          formik.values.adminLastName &&
          formik.values.adminEmail &&
          formik.values.adminPassword &&
          !formik.errors.adminFirstName &&
          !formik.errors.adminLastName &&
          !formik.errors.adminEmail &&
          !formik.errors.adminPassword
        )
      default:
        return false
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Simple Header */}
        <DialogHeader className="pb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold">
                {mode === 'create' ? 'Create Institute' : 'Edit Institute'}
              </DialogTitle>
              <DialogDescription className="text-gray-600">
                {mode === 'create'
                  ? 'Add a new educational institute'
                  : 'Update institute information'
                }
              </DialogDescription>
            </div>
          </div>

          {/* Simple Progress for Create Mode */}
          {mode === 'create' && (
            <div className="mt-6">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Step {currentStep} of {totalSteps}</span>
                <span>{Math.round((currentStep / totalSteps) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(currentStep / totalSteps) * 100}%` }}
                />
              </div>
              <div className="mt-2 text-sm font-medium text-gray-700">
                {getStepTitle(currentStep)}
              </div>
            </div>
          )}
        </DialogHeader>

        {/* Form Content */}
        <div className="max-h-[60vh] overflow-y-auto">
          <form onSubmit={formik.handleSubmit} className="space-y-6">

            {/* Step 1: Basic Information */}
            {(mode === 'edit' || currentStep === 1) && (
              <div className="space-y-4">
                {/* Institute Name */}
                <div>
                  <Label htmlFor="name" className="text-sm font-medium">
                    Institute Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="Enter institute name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    className={`mt-1 ${formik.touched.name && formik.errors.name ? 'border-red-500' : ''}`}
                  />
                  {formik.touched.name && formik.errors.name && (
                    <p className="text-red-500 text-xs mt-1">{formik.errors.name}</p>
                  )}
                  {formik.values.name && (
                    <p className="text-xs text-gray-500 mt-1">
                      URL: {formik.values.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')}
                    </p>
                  )}
                </div>

                {/* Tagline */}
                <div>
                  <Label htmlFor="tagline" className="text-sm font-medium">Tagline</Label>
                  <Input
                    id="tagline"
                    name="tagline"
                    placeholder="Brief description of your institute"
                    value={formik.values.tagline}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    className="mt-1"
                  />
                </div>

                {/* Contact Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email" className="text-sm font-medium">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formik.values.email}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      className={`mt-1 ${formik.touched.email && formik.errors.email ? 'border-red-500' : ''}`}
                    />
                    {formik.touched.email && formik.errors.email && (
                      <p className="text-red-500 text-xs mt-1">{formik.errors.email}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="phone" className="text-sm font-medium">Phone</Label>
                    <Input
                      id="phone"
                      name="phone"
                      placeholder="+****************"
                      value={formik.values.phone}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      className="mt-1"
                    />
                  </div>
                </div>

                {/* Website */}
                <div>
                  <Label htmlFor="website" className="text-sm font-medium">Website</Label>
                  <Input
                    id="website"
                    name="website"
                    placeholder="https://institute.com"
                    value={formik.values.website}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    className={`mt-1 ${formik.touched.website && formik.errors.website ? 'border-red-500' : ''}`}
                  />
                  {formik.touched.website && formik.errors.website && (
                    <p className="text-red-500 text-xs mt-1">{formik.errors.website}</p>
                  )}
                </div>
              </div>
            )}

                {/* Contact Information */}
                <Card className="border-l-4 border-l-green-500">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Mail className="h-4 w-4 text-green-600" />
                      Contact Information
                    </CardTitle>
                    <CardDescription>
                      How people can reach your institute
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="email" className="flex items-center gap-2 text-sm font-medium">
                          <Mail className="h-4 w-4 text-gray-500" />
                          Email Address
                        </Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={formik.values.email}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className={`mt-1 ${formik.touched.email && formik.errors.email ? 'border-red-500' : 'focus:border-green-500'}`}
                        />
                        {formik.touched.email && formik.errors.email && (
                          <p className="text-red-500 text-xs mt-1">{formik.errors.email}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="phone" className="flex items-center gap-2 text-sm font-medium">
                          <Phone className="h-4 w-4 text-gray-500" />
                          Phone Number
                        </Label>
                        <Input
                          id="phone"
                          name="phone"
                          placeholder="+****************"
                          value={formik.values.phone}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className="mt-1 focus:border-green-500"
                        />
                      </div>

                      <div>
                        <Label htmlFor="website" className="flex items-center gap-2 text-sm font-medium">
                          <Globe className="h-4 w-4 text-gray-500" />
                          Website URL
                        </Label>
                        <Input
                          id="website"
                          name="website"
                          placeholder="https://institute.com"
                          value={formik.values.website}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className={`mt-1 ${formik.touched.website && formik.errors.website ? 'border-red-500' : 'focus:border-green-500'}`}
                        />
                        {formik.touched.website && formik.errors.website && (
                          <p className="text-red-500 text-xs mt-1">{formik.errors.website}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Step 2: Location & Settings */}
            {mode === 'create' && currentStep === 2 && (
              <div className="space-y-4">
                {/* Address */}
                <div>
                  <Label htmlFor="addressStreet" className="text-sm font-medium">Address</Label>
                  <Textarea
                    id="addressStreet"
                    name="addressStreet"
                    placeholder="Street address"
                    value={formik.values.addressStreet}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    className="mt-1 resize-none"
                    rows={2}
                  />
                </div>

                {/* Location Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="countryId" className="text-sm font-medium">Country</Label>
                    <Select
                      value={formik.values.countryId}
                      onValueChange={(value) => formik.setFieldValue('countryId', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select country" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">United States</SelectItem>
                        <SelectItem value="2">Canada</SelectItem>
                        <SelectItem value="3">United Kingdom</SelectItem>
                        <SelectItem value="4">Australia</SelectItem>
                        <SelectItem value="5">India</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="stateId" className="text-sm font-medium">State</Label>
                    <Select
                      value={formik.values.stateId}
                      onValueChange={(value) => formik.setFieldValue('stateId', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select state" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">California</SelectItem>
                        <SelectItem value="2">New York</SelectItem>
                        <SelectItem value="3">Texas</SelectItem>
                        <SelectItem value="4">Florida</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="cityId" className="text-sm font-medium">City</Label>
                    <Select
                      value={formik.values.cityId}
                      onValueChange={(value) => formik.setFieldValue('cityId', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select city" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">Los Angeles</SelectItem>
                        <SelectItem value="2">San Francisco</SelectItem>
                        <SelectItem value="3">San Diego</SelectItem>
                        <SelectItem value="4">Sacramento</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="zipCode" className="text-sm font-medium">ZIP Code</Label>
                    <Input
                      id="zipCode"
                      name="zipCode"
                      placeholder="90210"
                      value={formik.values.zipCode}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      className="mt-1"
                    />
                  </div>
                </div>

                {/* Custom Domain */}
                <div>
                  <Label htmlFor="customDomain" className="text-sm font-medium">
                    Custom Domain <span className="text-xs text-gray-500">(Optional)</span>
                  </Label>
                  <Input
                    id="customDomain"
                    name="customDomain"
                    placeholder="myinstitute.com"
                    value={formik.values.customDomain}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    className="mt-1"
                  />
                  <p className="text-xs text-gray-500 mt-1">Students will access your institute via this domain</p>
                </div>
              </div>
            )}

            {/* Step 3: Administrator Account */}
            {mode === 'create' && currentStep === 3 && (
              <div className="space-y-4">
                {/* Admin Name */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="adminFirstName" className="text-sm font-medium">
                      First Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="adminFirstName"
                      name="adminFirstName"
                      placeholder="John"
                      value={formik.values.adminFirstName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      className={`mt-1 ${formik.touched.adminFirstName && formik.errors.adminFirstName ? 'border-red-500' : ''}`}
                    />
                    {formik.touched.adminFirstName && formik.errors.adminFirstName && (
                      <p className="text-red-500 text-xs mt-1">{formik.errors.adminFirstName}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="adminLastName" className="text-sm font-medium">
                      Last Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="adminLastName"
                      name="adminLastName"
                      placeholder="Doe"
                      value={formik.values.adminLastName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      className={`mt-1 ${formik.touched.adminLastName && formik.errors.adminLastName ? 'border-red-500' : ''}`}
                    />
                    {formik.touched.adminLastName && formik.errors.adminLastName && (
                      <p className="text-red-500 text-xs mt-1">{formik.errors.adminLastName}</p>
                    )}
                  </div>
                </div>

                {/* Admin Credentials */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="adminEmail" className="text-sm font-medium">
                      Email <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="adminEmail"
                      name="adminEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formik.values.adminEmail}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      className={`mt-1 ${formik.touched.adminEmail && formik.errors.adminEmail ? 'border-red-500' : ''}`}
                    />
                    {formik.touched.adminEmail && formik.errors.adminEmail && (
                      <p className="text-red-500 text-xs mt-1">{formik.errors.adminEmail}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="adminPassword" className="text-sm font-medium">
                      Password <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative mt-1">
                      <Input
                        id="adminPassword"
                        name="adminPassword"
                        type={showPassword ? "text" : "password"}
                        placeholder="Minimum 6 characters"
                        value={formik.values.adminPassword}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        className={`pr-10 ${formik.touched.adminPassword && formik.errors.adminPassword ? 'border-red-500' : ''}`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                    {formik.touched.adminPassword && formik.errors.adminPassword && (
                      <p className="text-red-500 text-xs mt-1">{formik.errors.adminPassword}</p>
                    )}
                  </div>
                </div>

                {/* Info Note */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-700">
                      <p className="font-medium">Administrator Account</p>
                      <p>This account will have full access to manage the institute, courses, students, and settings.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Simple Footer Actions */}
        <div className="flex items-center justify-between pt-6 border-t">
          <div>
            {mode === 'create' && currentStep > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>
            )}
          </div>

          <div className="flex items-center gap-3">
            <Button
              type="button"
              variant="ghost"
              onClick={onClose}
            >
              Cancel
            </Button>

            {mode === 'create' && currentStep < totalSteps ? (
              <Button
                type="button"
                onClick={nextStep}
                disabled={!isStepValid(currentStep)}
                className="flex items-center gap-2"
              >
                Next
                <ArrowRight className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={isLoading || (mode === 'create' && !isStepValid(currentStep))}
                className="flex items-center gap-2"
                onClick={formik.handleSubmit}
              >
                {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                {mode === 'create' ? 'Create Institute' : 'Update Institute'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
