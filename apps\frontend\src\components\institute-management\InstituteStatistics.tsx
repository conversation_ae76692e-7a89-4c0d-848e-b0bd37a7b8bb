'use client'

import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { 
  Building2, 
  CheckCircle, 
  XCircle, 
  Globe, 
  TrendingUp 
} from 'lucide-react'

interface Statistics {
  total: number
  active: number
  inactive: number
  verifiedDomains: number
  recentlyCreated: number
}

interface InstituteStatisticsProps {
  statistics: Statistics
}

export function InstituteStatistics({ statistics }: InstituteStatisticsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Institutes</CardTitle>
          <Building2 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.total}</div>
          <p className="text-xs text-muted-foreground">
            All registered institutes
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active</CardTitle>
          <CheckCircle className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.active}</div>
          <p className="text-xs text-muted-foreground">
            {statistics.total > 0 ? Math.round((statistics.active / statistics.total) * 100) : 0}% of total
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Inactive</CardTitle>
          <XCircle className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.inactive}</div>
          <p className="text-xs text-muted-foreground">
            {statistics.total > 0 ? Math.round((statistics.inactive / statistics.total) * 100) : 0}% of total
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Verified Domains</CardTitle>
          <Globe className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.verifiedDomains}</div>
          <p className="text-xs text-muted-foreground">
            Custom domains verified
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Recent</CardTitle>
          <TrendingUp className="h-4 w-4 text-purple-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.recentlyCreated}</div>
          <p className="text-xs text-muted-foreground">
            Created last 30 days
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
