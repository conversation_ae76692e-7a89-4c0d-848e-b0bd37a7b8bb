'use client'

import { useState } from 'react'
import { useInstituteManagementStore, Institute } from '@/stores/super-admin/useInstituteManagementStore'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Globe, 
  CheckCircle, 
  XCircle,
  Building2,
  Mail,
  Phone,
  ExternalLink
} from 'lucide-react'

interface InstitutesListProps {
  institutes: Institute[]
}

export function InstitutesList({ institutes }: InstitutesListProps) {
  const { 
    setSelectedInstitute, 
    setShowEditForm, 
    deleteInstitute, 
    verifyDomain,
    isLoading 
  } = useInstituteManagementStore()

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [instituteToDelete, setInstituteToDelete] = useState<Institute | null>(null)

  const handleEdit = (institute: Institute) => {
    setSelectedInstitute(institute)
    setShowEditForm(true)
  }

  const handleDeleteClick = (institute: Institute) => {
    setInstituteToDelete(institute)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (instituteToDelete) {
      await deleteInstitute(instituteToDelete.id)
      setDeleteDialogOpen(false)
      setInstituteToDelete(null)
    }
  }

  const handleVerifyDomain = async (institute: Institute) => {
    await verifyDomain(institute.id)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (institutes.length === 0) {
    return (
      <div className="text-center py-8">
        <Building2 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No institutes found</h3>
        <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
      </div>
    )
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Institute</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Domain</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {institutes.map((institute) => (
              <TableRow key={institute.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={institute.logo?.url} alt={institute.name} />
                      <AvatarFallback>
                        {institute.name.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{institute.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {institute.slug}
                      </div>
                      {institute.tagline && (
                        <div className="text-xs text-muted-foreground italic">
                          {institute.tagline}
                        </div>
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    {institute.email && (
                      <div className="flex items-center gap-2 text-sm">
                        <Mail className="h-3 w-3" />
                        {institute.email}
                      </div>
                    )}
                    {institute.phone && (
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-3 w-3" />
                        {institute.phone}
                      </div>
                    )}
                    {institute.website && (
                      <div className="flex items-center gap-2 text-sm">
                        <ExternalLink className="h-3 w-3" />
                        <a 
                          href={institute.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          Website
                        </a>
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {institute.customDomain ? (
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      <span className="text-sm">{institute.customDomain}</span>
                      <Badge variant={institute.domainVerified ? "default" : "secondary"}>
                        {institute.domainVerified ? "Verified" : "Pending"}
                      </Badge>
                    </div>
                  ) : (
                    <span className="text-sm text-muted-foreground">No custom domain</span>
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant={institute.isActive ? "default" : "secondary"}>
                    {institute.isActive ? (
                      <><CheckCircle className="h-3 w-3 mr-1" />Active</>
                    ) : (
                      <><XCircle className="h-3 w-3 mr-1" />Inactive</>
                    )}
                  </Badge>
                </TableCell>
                <TableCell>
                  <span className="text-sm">{formatDate(institute.createdAt)}</span>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleEdit(institute)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      {institute.customDomain && !institute.domainVerified && (
                        <DropdownMenuItem onClick={() => handleVerifyDomain(institute)}>
                          <Globe className="mr-2 h-4 w-4" />
                          Verify Domain
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => handleDeleteClick(institute)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the institute "{instituteToDelete?.name}" and all associated data. 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
              disabled={isLoading}
            >
              Delete Institute
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
