'use client'

import { useState, useEffect } from 'react'
import { useInstituteStore } from '@/stores/institute/useInstituteStore'
import { useBranchStore } from '@/stores/institute/useBranchStore'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Building2, 
  ChevronDown, 
  Plus, 
  MapPin,
  Users,
  CheckCircle
} from 'lucide-react'

interface Branch {
  id: string
  name: string
  code?: string
  location?: {
    address: string
    district?: { name: string }
    state?: { name: string }
  }
  isActive?: boolean
  isHeadOffice?: boolean
  isDeleted?: boolean
  _count?: {
    students: number
    staff: number
  }
}

interface BranchSelectorProps {
  isCollapsed?: boolean
  className?: string
}

export function BranchSelector({ isCollapsed = false, className = "" }: BranchSelectorProps) {
  const { branches, fetchBranches, isLoading } = useInstituteStore()
  const {
    selectedBranch,
    setSelectedBranch,
    showCreateBranchModal,
    setShowCreateBranchModal
  } = useBranchStore()

  useEffect(() => {
    fetchBranches()
  }, [fetchBranches])

  const handleBranchSelect = (branch: Branch | null) => {
    setSelectedBranch(branch)
  }

  const handleAddBranch = () => {
    setShowCreateBranchModal(true)
  }

  const activeBranches = branches.filter((branch: Branch) => branch.isActive && !branch.isDeleted)

  // Collapsed view - just show an icon
  if (isCollapsed) {
    return (
      <div className={`px-2 py-2 ${className}`}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="w-full h-10 p-0 justify-center"
              disabled={isLoading}
              title={selectedBranch ? `Current: ${selectedBranch.name}` : 'All Branches'}
            >
              <Building2 className="h-5 w-5 text-gray-600" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-80" align="start">
            <DropdownMenuLabel>Select Branch</DropdownMenuLabel>
            <DropdownMenuSeparator />

            {/* All Branches Option */}
            <DropdownMenuItem
              onClick={() => handleBranchSelect(null)}
              className="flex items-center justify-between"
            >
              <div className="flex items-center space-x-2">
                <Building2 className="h-4 w-4" />
                <span>All Branches</span>
              </div>
              {!selectedBranch && <CheckCircle className="h-4 w-4 text-green-600" />}
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            {/* Individual Branches */}
            {activeBranches.length > 0 ? (
              activeBranches.map((branch: Branch) => (
                <DropdownMenuItem
                  key={branch.id}
                  onClick={() => handleBranchSelect(branch)}
                  className="flex items-center justify-between p-3"
                >
                  <div className="flex items-start space-x-3 min-w-0 flex-1">
                    <Building2 className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium truncate">{branch.name}</span>
                        {branch.isHeadOffice && (
                          <Badge variant="secondary" className="text-xs">
                            HQ
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {branch.code}
                      </div>
                    </div>
                  </div>
                  {selectedBranch?.id === branch.id && (
                    <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                  )}
                </DropdownMenuItem>
              ))
            ) : (
              <DropdownMenuItem className="opacity-50 cursor-not-allowed">
                <span className="text-gray-500">No branches found</span>
              </DropdownMenuItem>
            )}

            <DropdownMenuSeparator />

            <DropdownMenuItem onClick={handleAddBranch}>
              <Plus className="h-4 w-4 mr-2" />
              Add New Branch
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  }

  // Expanded view
  return (
    <div className={`px-3 py-3 border-b border-gray-200 ${className}`}>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">
            Branch Filter
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleAddBranch}
            className="h-6 w-6 p-0 hover:bg-gray-100"
            title="Add new branch"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between text-left font-normal"
              disabled={isLoading}
            >
              <div className="flex items-center space-x-2 min-w-0">
                <Building2 className="h-4 w-4 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  {selectedBranch ? (
                    <div>
                      <div className="font-medium truncate">{selectedBranch.name}</div>
                      <div className="text-xs text-gray-500 truncate">
                        {selectedBranch.code}
                      </div>
                    </div>
                  ) : (
                    <span>All Branches</span>
                  )}
                </div>
              </div>
              <ChevronDown className="h-4 w-4 flex-shrink-0" />
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent className="w-80" align="start">
            <DropdownMenuLabel>Select Branch</DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            {/* All Branches Option */}
            <DropdownMenuItem
              onClick={() => handleBranchSelect(null)}
              className="flex items-center justify-between"
            >
              <div className="flex items-center space-x-2">
                <Building2 className="h-4 w-4" />
                <span>All Branches</span>
              </div>
              {!selectedBranch && <CheckCircle className="h-4 w-4 text-green-600" />}
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            {/* Individual Branches */}
            {activeBranches.length > 0 ? (
              activeBranches.map((branch: Branch) => (
                <DropdownMenuItem
                  key={branch.id}
                  onClick={() => handleBranchSelect(branch)}
                  className="flex items-center justify-between p-3"
                >
                  <div className="flex items-start space-x-3 min-w-0 flex-1">
                    <Building2 className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium truncate">{branch.name}</span>
                        {branch.isHeadOffice && (
                          <Badge variant="secondary" className="text-xs">
                            HQ
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {branch.code}
                      </div>
                      {branch.location?.address && (
                        <div className="flex items-center space-x-1 text-xs text-gray-500 mt-1">
                          <MapPin className="h-3 w-3" />
                          <span className="truncate">
                            {branch.location.district?.name}, {branch.location.state?.name}
                          </span>
                        </div>
                      )}
                      {branch._count && (
                        <div className="flex items-center space-x-3 text-xs text-gray-500 mt-1">
                          <div className="flex items-center space-x-1">
                            <Users className="h-3 w-3" />
                            <span>{branch._count.students} students</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  {selectedBranch?.id === branch.id && (
                    <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                  )}
                </DropdownMenuItem>
              ))
            ) : (
              <DropdownMenuItem className="opacity-50 cursor-not-allowed">
                <span className="text-gray-500">No branches found</span>
              </DropdownMenuItem>
            )}

            <DropdownMenuSeparator />
            
            <DropdownMenuItem onClick={handleAddBranch}>
              <Plus className="h-4 w-4 mr-2" />
              Add New Branch
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Selected Branch Info */}
        {selectedBranch && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-2">
            <div className="flex items-center space-x-2">
              <Building2 className="h-4 w-4 text-blue-600" />
              <div className="min-w-0 flex-1">
                <div className="font-medium text-blue-900 truncate">
                  {selectedBranch.name}
                </div>
                <div className="text-xs text-blue-700">
                  Viewing branch-specific data
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default BranchSelector
