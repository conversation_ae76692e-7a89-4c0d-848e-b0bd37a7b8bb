'use client'

import { useState, useEffect } from 'react'
import { useInstituteStore } from '@/stores/institute/useInstituteStore'
import { useBranchStore } from '@/stores/institute/useBranchStore'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Building2, 
  ChevronDown, 
  Plus, 
  MapPin,
  CheckCircle
} from 'lucide-react'

interface Branch {
  id: string
  name: string
  code?: string
  location?: {
    address: string
    district?: { name: string }
    state?: { name: string }
  }
  isActive?: boolean
  isHeadOffice?: boolean
  isDeleted?: boolean
}

interface BranchSelectorCompactProps {
  className?: string
}

export function BranchSelectorCompact({ className = "" }: BranchSelectorCompactProps) {
  const { branches, fetchBranches, isLoading } = useInstituteStore()
  const { 
    selectedBranch, 
    setSelectedBranch, 
    setShowCreateBranchModal 
  } = useBranchStore()

  useEffect(() => {
    fetchBranches()
  }, [fetchBranches])

  const handleBranchSelect = (branch: Branch | null) => {
    setSelectedBranch(branch)
  }

  const handleAddBranch = () => {
    setShowCreateBranchModal(true)
  }

  const activeBranches = branches.filter((branch: Branch) => branch.isActive && !branch.isDeleted)

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-8 px-3 text-sm"
            disabled={isLoading}
          >
            <Building2 className="h-3 w-3 mr-2" />
            <span className="max-w-32 truncate">
              {selectedBranch ? selectedBranch.name : 'All Branches'}
            </span>
            <ChevronDown className="h-3 w-3 ml-2" />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="w-72" align="end">
          <DropdownMenuLabel className="flex items-center justify-between">
            <span>Select Branch</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAddBranch}
              className="h-6 w-6 p-0"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {/* All Branches Option */}
          <DropdownMenuItem
            onClick={() => handleBranchSelect(null)}
            className="flex items-center justify-between"
          >
            <div className="flex items-center space-x-2">
              <Building2 className="h-4 w-4" />
              <span>All Branches</span>
            </div>
            {!selectedBranch && <CheckCircle className="h-4 w-4 text-green-600" />}
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* Individual Branches */}
          {activeBranches.length > 0 ? (
            activeBranches.map((branch: Branch) => (
              <DropdownMenuItem
                key={branch.id}
                onClick={() => handleBranchSelect(branch)}
                className="flex items-center justify-between p-3"
              >
                <div className="flex items-start space-x-3 min-w-0 flex-1">
                  <Building2 className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium truncate">{branch.name}</span>
                      {branch.isHeadOffice && (
                        <Badge variant="secondary" className="text-xs">
                          HQ
                        </Badge>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 truncate">
                      {branch.code}
                    </div>
                    {branch.location?.address && (
                      <div className="flex items-center space-x-1 text-xs text-gray-500 mt-1">
                        <MapPin className="h-3 w-3" />
                        <span className="truncate">
                          {branch.location.district?.name}, {branch.location.state?.name}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                {selectedBranch?.id === branch.id && (
                  <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                )}
              </DropdownMenuItem>
            ))
          ) : (
            <DropdownMenuItem className="opacity-50 cursor-not-allowed">
              <span className="text-gray-500">No branches found</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Current branch indicator */}
      {selectedBranch && (
        <Badge variant="outline" className="text-xs">
          {selectedBranch.code || selectedBranch.name}
        </Badge>
      )}
    </div>
  )
}

export default BranchSelectorCompact
