'use client'

import { useState, useEffect } from 'react'

import { useBranchStore } from '@/stores/institute/useBranchStore'
import { useLocationStore } from '@/stores/location/useLocationStore'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import { Building2, MapPin, Clock, Phone, Mail, Globe } from 'lucide-react'

export function CreateBranchModal() {
  const { createBranch, isCreating } = useBranchStore()
  const { showCreateBranchModal, setShowCreateBranchModal } = useBranchStore()
  const { 
    countries, 
    states, 
    districts, 
    fetchCountries, 
    fetchStates, 
    fetchDistricts 
  } = useLocationStore()

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    address: '',
    countryId: '',
    stateId: '',
    districtId: '',
    pincode: '',
    phone: '',
    email: '',
    website: '',
    gstNumber: '',
    panNumber: '',
    isGstRegistered: false,
    isHeadOffice: false,
    openTime: '09:00',
    closeTime: '18:00',
    workingDays: {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: true,
      saturday: true,
      sunday: false,
    }
  })

  // Load countries on mount
  useEffect(() => {
    fetchCountries()
  }, [fetchCountries])

  // Load states when country changes
  useEffect(() => {
    if (formData.countryId) {
      fetchStates(formData.countryId)
      setFormData(prev => ({ ...prev, stateId: '', districtId: '' }))
    }
  }, [formData.countryId, fetchStates])

  // Load districts when state changes
  useEffect(() => {
    if (formData.stateId) {
      fetchDistricts(formData.stateId)
      setFormData(prev => ({ ...prev, districtId: '' }))
    }
  }, [formData.stateId, fetchDistricts])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleWorkingDayChange = (day: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      workingDays: {
        ...prev.workingDays,
        [day]: checked
      }
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name || !formData.address || !formData.countryId || !formData.stateId) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      const branchData = {
        name: formData.name,
        code: formData.code || formData.name.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 10),
        location: {
          address: formData.address,
          country: formData.countryId,
          state: formData.stateId,
          district: formData.districtId,
          pincode: formData.pincode,
        },
        contact: {
          phone: formData.phone,
          email: formData.email,
          website: formData.website,
        },
        taxInformation: {
          gstNumber: formData.gstNumber,
          panNumber: formData.panNumber,
          isGstRegistered: formData.isGstRegistered,
        },
        workingDays: formData.workingDays,
        isHeadOffice: formData.isHeadOffice,
      }

      await createBranch(branchData)
      setShowCreateBranchModal(false)
      
      // Reset form
      setFormData({
        name: '',
        code: '',
        address: '',
        countryId: '',
        stateId: '',
        districtId: '',
        pincode: '',
        phone: '',
        email: '',
        website: '',
        gstNumber: '',
        panNumber: '',
        isGstRegistered: false,
        isHeadOffice: false,
        openTime: '09:00',
        closeTime: '18:00',
        workingDays: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: true,
          sunday: false,
        }
      })
      
      toast.success('Branch created successfully!')
    } catch (error) {
      console.error('Failed to create branch:', error)
      toast.error('Failed to create branch')
    }
  }

  return (
    <Dialog open={showCreateBranchModal} onOpenChange={setShowCreateBranchModal}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5" />
            <span>Create New Branch</span>
          </DialogTitle>
          <DialogDescription>
            Add a new branch to your institute
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Branch Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Main Branch"
                  required
                />
              </div>
              <div>
                <Label htmlFor="code">Branch Code</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => handleInputChange('code', e.target.value)}
                  placeholder="MAIN"
                />
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isHeadOffice"
                checked={formData.isHeadOffice}
                onCheckedChange={(checked) => handleInputChange('isHeadOffice', checked)}
              />
              <Label htmlFor="isHeadOffice">Mark as Head Office</Label>
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center space-x-2">
              <MapPin className="h-4 w-4" />
              <span>Location Information</span>
            </h3>
            
            <div>
              <Label htmlFor="address">Address *</Label>
              <Textarea
                id="address"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Enter full address"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select value={formData.countryId} onValueChange={(value) => handleInputChange('countryId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country.id} value={country.id}>
                        {country.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="state">State *</Label>
                <Select value={formData.stateId} onValueChange={(value) => handleInputChange('stateId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select state" />
                  </SelectTrigger>
                  <SelectContent>
                    {states.map((state) => (
                      <SelectItem key={state.id} value={state.id}>
                        {state.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="district">District</Label>
                <Select value={formData.districtId} onValueChange={(value) => handleInputChange('districtId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select district" />
                  </SelectTrigger>
                  <SelectContent>
                    {districts.map((district) => (
                      <SelectItem key={district.id} value={district.id}>
                        {district.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="pincode">PIN Code</Label>
              <Input
                id="pincode"
                value={formData.pincode}
                onChange={(e) => handleInputChange('pincode', e.target.value)}
                placeholder="123456"
              />
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center space-x-2">
              <Phone className="h-4 w-4" />
              <span>Contact Information</span>
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+****************"
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://branch.institute.com"
                />
              </div>
            </div>
          </div>

          {/* Operating Hours */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>Operating Hours</span>
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="openTime">Opening Time</Label>
                <Input
                  id="openTime"
                  type="time"
                  value={formData.openTime}
                  onChange={(e) => handleInputChange('openTime', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="closeTime">Closing Time</Label>
                <Input
                  id="closeTime"
                  type="time"
                  value={formData.closeTime}
                  onChange={(e) => handleInputChange('closeTime', e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label>Working Days</Label>
              <div className="grid grid-cols-4 md:grid-cols-7 gap-2 mt-2">
                {Object.entries(formData.workingDays).map(([day, checked]) => (
                  <div key={day} className="flex items-center space-x-2">
                    <Checkbox
                      id={day}
                      checked={checked}
                      onCheckedChange={(checked) => handleWorkingDayChange(day, checked as boolean)}
                    />
                    <Label htmlFor={day} className="text-sm capitalize">
                      {day.substring(0, 3)}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowCreateBranchModal(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating}>
              {isCreating ? 'Creating...' : 'Create Branch'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateBranchModal
