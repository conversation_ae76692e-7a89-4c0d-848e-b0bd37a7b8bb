'use client'

import React, { useState } from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Loader2, AlertTriangle } from 'lucide-react'
import { useStudentStore } from '@/stores/institute/useStudentStore'
import { toast } from 'sonner'

interface DeleteStudentDialogProps {
  isOpen: boolean
  onClose: () => void
  student: {
    id: string
    firstName: string
    lastName: string
    email: string
  } | null
}

export function DeleteStudentDialog({ isOpen, onClose, student }: DeleteStudentDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const { deleteStudent, fetchStudents } = useStudentStore()

  const handleDelete = async () => {
    if (!student) return

    setIsDeleting(true)
    try {
      await deleteStudent(student.id)
      toast.success(`Student ${student.firstName} ${student.lastName} has been deleted successfully`)
      
      // Refresh the student list
      await fetchStudents()
      
      onClose()
    } catch (error) {
      console.error('Failed to delete student:', error)
      toast.error('Failed to delete student. Please try again.')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleClose = () => {
    if (!isDeleting) {
      onClose()
    }
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Delete Student
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <p>
              Are you sure you want to delete <strong>{student?.firstName} {student?.lastName}</strong>?
            </p>
            <p className="text-sm text-gray-600">
              Email: {student?.email}
            </p>
            <div className="bg-red-50 border border-red-200 rounded-md p-3 mt-3">
              <p className="text-red-800 text-sm font-medium">
                ⚠️ This action cannot be undone!
              </p>
              <p className="text-red-700 text-sm mt-1">
                This will permanently delete:
              </p>
              <ul className="text-red-700 text-sm mt-1 ml-4 list-disc">
                <li>Student account and login access</li>
                <li>All student details (education, personal info, documents)</li>
                <li>Location information</li>
                <li>All associated data</li>
              </ul>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              'Delete Student'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
