'use client'

import { useInstituteTheme } from '@/components/shared/theme/InstituteThemeProvider'
import { Button } from '@/components/ui/button'
import { Menu, X, User, ShoppingCart } from 'lucide-react'
import { useState } from 'react'

export function InstituteHeader() {
  const { institute, theme } = useInstituteTheme()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  return (
    <header 
      className="institute-header sticky top-0 z-50 bg-white shadow-sm border-b"
      style={{
        borderBottomColor: `var(--color-primary)`,
      }}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Institute Name */}
          <div className="flex items-center space-x-4">
            {institute.logo && (
              <img 
                src={institute.logo} 
                alt={institute.name}
                className="h-10 w-auto"
              />
            )}
            <h1 
              className="text-xl font-bold"
              style={{ 
                fontFamily: `var(--font-heading)`,
                color: `var(--color-primary)`
              }}
            >
              {institute.name}
            </h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a 
              href="/" 
              className="text-gray-700 hover:text-primary transition-colors"
              style={{ color: `var(--color-text)` }}
            >
              Home
            </a>
            <a 
              href="/courses" 
              className="text-gray-700 hover:text-primary transition-colors"
              style={{ color: `var(--color-text)` }}
            >
              Courses
            </a>
            <a 
              href="/about" 
              className="text-gray-700 hover:text-primary transition-colors"
              style={{ color: `var(--color-text)` }}
            >
              About
            </a>
            <a 
              href="/contact" 
              className="text-gray-700 hover:text-primary transition-colors"
              style={{ color: `var(--color-text)` }}
            >
              Contact
            </a>
          </nav>

          {/* Action Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="ghost" size="sm">
              <ShoppingCart className="h-4 w-4 mr-2" />
              Cart
            </Button>
            <Button variant="outline" size="sm">
              <User className="h-4 w-4 mr-2" />
              Login
            </Button>
            <Button 
              size="sm"
              style={{
                backgroundColor: `var(--color-primary)`,
                color: `var(--color-background)`
              }}
            >
              Sign Up
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t">
            <nav className="flex flex-col space-y-4">
              <a 
                href="/" 
                className="text-gray-700 hover:text-primary transition-colors"
                style={{ color: `var(--color-text)` }}
              >
                Home
              </a>
              <a 
                href="/courses" 
                className="text-gray-700 hover:text-primary transition-colors"
                style={{ color: `var(--color-text)` }}
              >
                Courses
              </a>
              <a 
                href="/about" 
                className="text-gray-700 hover:text-primary transition-colors"
                style={{ color: `var(--color-text)` }}
              >
                About
              </a>
              <a 
                href="/contact" 
                className="text-gray-700 hover:text-primary transition-colors"
                style={{ color: `var(--color-text)` }}
              >
                Contact
              </a>
              <div className="flex flex-col space-y-2 pt-4 border-t">
                <Button variant="outline" size="sm">
                  <User className="h-4 w-4 mr-2" />
                  Login
                </Button>
                <Button 
                  size="sm"
                  style={{
                    backgroundColor: `var(--color-primary)`,
                    color: `var(--color-background)`
                  }}
                >
                  Sign Up
                </Button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
