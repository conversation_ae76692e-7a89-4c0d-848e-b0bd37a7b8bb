'use client'

import { useInstituteTheme } from '@/components/shared/theme/InstituteThemeProvider'
import { Button } from '@/components/ui/button'
import { ArrowRight, Play, Users, BookOpen, Award } from 'lucide-react'

export function InstituteHero() {
  const { institute, theme } = useInstituteTheme()

  return (
    <section 
      className="relative py-20 lg:py-32 overflow-hidden"
      style={{
        background: `linear-gradient(135deg, ${theme.colors.primary}10 0%, ${theme.colors.secondary}10 100%)`,
      }}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 
                className="text-4xl lg:text-6xl font-bold leading-tight"
                style={{ 
                  fontFamily: `var(--font-heading)`,
                  color: theme.colors.text
                }}
              >
                Welcome to{' '}
                <span 
                  className="block"
                  style={{ color: theme.colors.primary }}
                >
                  {institute.name}
                </span>
              </h1>
              
              <p 
                className="text-xl lg:text-2xl leading-relaxed"
                style={{ 
                  fontFamily: `var(--font-body)`,
                  color: theme.colors.text + '80'
                }}
              >
                {institute.settings?.tagline || 'Empowering minds, shaping futures through quality education and innovative learning experiences.'}
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                size="lg"
                className="text-lg px-8 py-4"
                style={{
                  backgroundColor: theme.colors.primary,
                  color: theme.colors.background
                }}
              >
                Explore Courses
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              
              <Button 
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4"
                style={{
                  borderColor: theme.colors.primary,
                  color: theme.colors.primary
                }}
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8">
              <div className="text-center">
                <div 
                  className="text-3xl font-bold"
                  style={{ color: theme.colors.primary }}
                >
                  500+
                </div>
                <div className="text-sm text-gray-600 flex items-center justify-center mt-1">
                  <Users className="h-4 w-4 mr-1" />
                  Students
                </div>
              </div>
              
              <div className="text-center">
                <div 
                  className="text-3xl font-bold"
                  style={{ color: theme.colors.primary }}
                >
                  50+
                </div>
                <div className="text-sm text-gray-600 flex items-center justify-center mt-1">
                  <BookOpen className="h-4 w-4 mr-1" />
                  Courses
                </div>
              </div>
              
              <div className="text-center">
                <div 
                  className="text-3xl font-bold"
                  style={{ color: theme.colors.primary }}
                >
                  95%
                </div>
                <div className="text-sm text-gray-600 flex items-center justify-center mt-1">
                  <Award className="h-4 w-4 mr-1" />
                  Success Rate
                </div>
              </div>
            </div>
          </div>

          {/* Hero Image */}
          <div className="relative">
            <div 
              className="relative rounded-2xl overflow-hidden shadow-2xl"
              style={{
                background: `linear-gradient(45deg, ${theme.colors.primary}20, ${theme.colors.secondary}20)`
              }}
            >
              <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div 
                    className="w-20 h-20 rounded-full mx-auto flex items-center justify-center"
                    style={{ backgroundColor: theme.colors.primary }}
                  >
                    <BookOpen className="h-10 w-10 text-white" />
                  </div>
                  <p className="text-gray-600 font-medium">
                    Interactive Learning Experience
                  </p>
                </div>
              </div>
            </div>
            
            {/* Floating Elements */}
            <div 
              className="absolute -top-4 -right-4 w-24 h-24 rounded-full flex items-center justify-center shadow-lg"
              style={{ backgroundColor: theme.colors.accent }}
            >
              <Award className="h-12 w-12 text-white" />
            </div>
            
            <div 
              className="absolute -bottom-4 -left-4 w-20 h-20 rounded-full flex items-center justify-center shadow-lg"
              style={{ backgroundColor: theme.colors.secondary }}
            >
              <Users className="h-10 w-10 text-white" />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
