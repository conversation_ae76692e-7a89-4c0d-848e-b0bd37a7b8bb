'use client'

import React, { useEffect } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { useRoleStore } from '@/stores/institute/useRoleStore'
import { AlertCircle, Loader2 } from 'lucide-react'

interface RoleSelectorProps {
  value: string
  onChange: (value: string) => void
  error?: string
  touched?: boolean
  disabled?: boolean
  required?: boolean
  label?: string
  placeholder?: string
}

export function RoleSelector({ 
  value, 
  onChange, 
  error, 
  touched, 
  disabled, 
  required,
  label = 'Student Role',
  placeholder = 'Select a role'
}: RoleSelectorProps) {
  const { studentRoles, isFetchingStudentRoles, fetchStudentRoles } = useRoleStore()

  useEffect(() => {
    if (studentRoles.length === 0 && !isFetchingStudentRoles) {
      fetchStudentRoles()
    }
  }, [fetchStudentRoles, studentRoles.length, isFetchingStudentRoles])

  return (
    <div className="space-y-2">
      <Label htmlFor="role_id">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      <Select
        value={value}
        onValueChange={onChange}
        disabled={disabled || isFetchingStudentRoles}
      >
        <SelectTrigger className={error && touched ? 'border-red-500' : ''}>
          <SelectValue placeholder={isFetchingStudentRoles ? 'Loading roles...' : placeholder} />
          {isFetchingStudentRoles && (
            <Loader2 className="h-4 w-4 animate-spin ml-2" />
          )}
        </SelectTrigger>
        <SelectContent>
          {studentRoles.length === 0 && !isFetchingStudentRoles ? (
            <SelectItem value="no-roles" disabled>
              No roles available
            </SelectItem>
          ) : (
            studentRoles.map((role) => (
              <SelectItem key={role.id} value={role.id}>
                <div className="flex flex-col py-1">
                  <span className="font-medium">{role.name}</span>
                  {role.description && (
                    <span className="text-xs text-gray-500 mt-1">{role.description}</span>
                  )}
                  {role.permissions && role.permissions.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-1">
                      {role.permissions.slice(0, 3).map((permission) => (
                        <span 
                          key={permission} 
                          className="text-xs bg-blue-100 text-blue-700 px-1 py-0.5 rounded"
                        >
                          {permission.replace('_', ' ')}
                        </span>
                      ))}
                      {role.permissions.length > 3 && (
                        <span className="text-xs text-gray-400">
                          +{role.permissions.length - 3} more
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
      {error && touched && (
        <div className="text-sm text-red-500 flex items-center gap-1">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}
      {!error && studentRoles.length === 0 && !isFetchingStudentRoles && (
        <div className="text-sm text-amber-600 flex items-center gap-1">
          <AlertCircle className="h-4 w-4" />
          <span>No student roles configured. Please contact your administrator.</span>
        </div>
      )}
    </div>
  )
}
