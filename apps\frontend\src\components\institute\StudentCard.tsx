'use client'

import React from 'react'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Eye, Mail, Phone, MapPin, Calendar } from 'lucide-react'
import { StudentStatusToggle } from './StudentStatusToggle'

interface Student {
  id: number  // Changed from string to number
  firstName: string
  lastName: string
  email: string
  phone?: string
  address?: string
  dateOfBirth?: string
  gender?: string
  is_active: boolean
  createdAt: string
  branch?: {
    id: number  // Changed from string to number
    name: string
    code: string
  }
  role?: {
    id: number  // Changed from string to number
    name: string
    code: string  // Added code field
  }
}

interface StudentCardProps {
  student: Student
  isSelected: boolean
  onSelect: (checked: boolean) => void
  onEdit: () => void
  onDelete: () => void
  onView: () => void
}

export function StudentCard({ 
  student, 
  isSelected, 
  onSelect, 
  onEdit, 
  onDelete, 
  onView 
}: StudentCardProps) {
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Removed getProgressColor function - not needed anymore

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${
      isSelected ? 'ring-2 ring-blue-500 shadow-md' : ''
    } ${!student.is_active ? 'opacity-75' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3 flex-1">
            <Checkbox
              checked={isSelected}
              onCheckedChange={onSelect}
              className="mt-1"
            />
            <Avatar className="h-12 w-12">
              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${student.firstName}${student.lastName}`} />
              <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                {getInitials(student.firstName, student.lastName)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg truncate">
                {student.firstName} {student.lastName}
              </h3>
              <p className="text-sm text-gray-500 truncate">
                ID: {String(student.id).slice(-8).toUpperCase()}
              </p>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onView}>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Student
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onDelete} className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" />
                Remove Student
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Contact Information */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <span className="text-gray-600 truncate">{student.email}</span>
          </div>
          {student.phone && (
            <div className="flex items-center gap-2 text-sm">
              <Phone className="h-4 w-4 text-gray-400 flex-shrink-0" />
              <span className="text-gray-600">{student.phone}</span>
            </div>
          )}
          {student.address && (
            <div className="flex items-center gap-2 text-sm">
              <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
              <span className="text-gray-600 truncate" title={student.address}>
                {student.address}
              </span>
            </div>
          )}
        </div>

        {/* Branch and Role */}
        <div className="flex flex-wrap gap-2">
          {student.branch && (
            <Badge variant="outline" className="text-xs">
              <MapPin className="h-3 w-3 mr-1" />
              {student.branch.name}
            </Badge>
          )}
          {student.role && (
            <Badge variant="secondary" className="text-xs">
              {student.role.name}
            </Badge>
          )}
        </div>

        {/* Additional Info */}
        <div className="space-y-1">
          {student.dateOfBirth && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-3 w-3 text-gray-400" />
              <span>DOB: {formatDate(student.dateOfBirth)}</span>
            </div>
          )}
          {student.gender && (
            <div className="text-sm text-gray-600 capitalize">
              Gender: {student.gender}
            </div>
          )}
        </div>

        {/* Status */}
        <div className="flex items-center justify-between">
          <StudentStatusToggle student={student} size="sm" />
          <Badge variant={student.is_active ? "default" : "secondary"} className="text-xs">
            {student.is_active ? "Active" : "Inactive"}
          </Badge>
        </div>

        {/* Join Date */}
        <div className="pt-2 border-t border-gray-100">
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Calendar className="h-3 w-3" />
            Joined {formatDate(student.createdAt)}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-2 pt-2">
          <Button size="sm" variant="outline" onClick={onView} className="flex-1">
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          <Button size="sm" variant="outline" onClick={onEdit} className="flex-1">
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
