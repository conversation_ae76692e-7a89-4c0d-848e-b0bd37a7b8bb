'use client'

import React, { useEffect } from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { AlertCircle, Save, X, Loader2 } from 'lucide-react'
import { useStudentStore } from '@/stores/institute/useStudentStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { CascadingLocationDropdown } from '@/components/common/CascadingLocationDropdown'
import { StudentDetailsForm } from './StudentDetailsForm'
// RoleSelector removed - students automatically get student role

// Role-based field visibility logic
const getVisibleFields = (userRole: string, operation: 'create' | 'update') => {
  const baseFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'dateOfBirth', 'gender']
  
  const fieldVisibility = {
    institute_admin: {
      create: [...baseFields, 'password', 'branch_id', 'role_id', 'is_active'],
      update: [...baseFields, 'password', 'branch_id', 'role_id', 'is_active']
    },
    institute_staff: {
      create: [...baseFields, 'password', 'branch_id', 'role_id'],
      update: [...baseFields, 'password', 'branch_id', 'role_id']
    },
    branch_manager: {
      create: [...baseFields, 'password', 'role_id'],
      update: [...baseFields, 'password', 'role_id']
    }
  }
  
  return fieldVisibility[userRole]?.[operation] || baseFields
}

const canEditField = (fieldName: string, userRole: string, operation: 'create' | 'update') => {
  const visibleFields = getVisibleFields(userRole, operation)
  return visibleFields.includes(fieldName)
}

const studentCreateValidationSchema = Yup.object({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email format').required('Email is required'),
  phone: Yup.string(),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
  branch: Yup.string().when('$userRole', {
    is: (role: string) => ['institute_admin', 'institute_staff'].includes(role),
    then: (schema) => schema.required('Branch selection is required').not(['select-branch'], 'Please select a branch'),
    otherwise: (schema) => schema.notRequired()
  }),
  // role_id removed - automatically assigned as student role
  address: Yup.string(),
  dateOfBirth: Yup.date(),
  gender: Yup.string().oneOf(['male', 'female', 'other']),
  // Location validation
  country: Yup.string().required('Country selection is required').not(['select-country'], 'Please select a country'),
  state: Yup.string().required('State selection is required').not(['select-state'], 'Please select a state'),
  district: Yup.string().required('District selection is required').not(['select-district'], 'Please select a district'),
  is_active: Yup.boolean().default(true)
})

interface StudentCreateFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password: string
  branch?: string  // Use 'branch' field name (maps to branch_id in database)
  // role_id removed - automatically assigned as student role
  address?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other' | 'select-gender'
  // Location fields
  country: string
  state: string
  district: string
  is_active: boolean
}

interface StudentCreateFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

export function StudentCreateForm({ isOpen, onClose, onSuccess }: StudentCreateFormProps) {
  const { user } = useAuthStore()
  const {
    availableBranches,
    isCreating,
    createStudent,
    fetchAvailableBranches,
    clearError
  } = useStudentStore()

  useEffect(() => {
    if (isOpen) {
      fetchAvailableBranches()
      clearError()
    }
  }, [isOpen, fetchAvailableBranches, clearError])

  const initialValues: StudentCreateFormData = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    branch: user?.legacyRole === 'branch_manager' ? 'select-branch' : 'select-branch',
    // role_id removed - automatically assigned as student role
    address: '',
    dateOfBirth: '',
    gender: 'select-gender',
    // Location initial values
    country: 'select-country',
    state: 'select-state',
    district: 'select-district',
    is_active: true
  }

  const handleSubmit = async (values: StudentCreateFormData, { resetForm }: any) => {
    try {
      await createStudent(values)
      resetForm()
      onSuccess?.()
      onClose()
    } catch (error) {
      // Error handling is done in the store
    }
  }

  const showBranchSelection = user?.legacyRole !== 'branch_manager'
  const canEditStatus = user?.legacyRole === 'institute_admin'

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Create New Student</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          <Formik
            initialValues={initialValues}
            validationSchema={studentCreateValidationSchema}
            onSubmit={handleSubmit}
            context={{ userRole: user?.legacyRole }}
            enableReinitialize
          >
            {({ values, setFieldValue, errors, touched, isSubmitting }) => (
              <Form className="space-y-6">
                {/* Personal Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <Field
                      as={Input}
                      id="firstName"
                      name="firstName"
                      placeholder="Enter first name"
                      className={errors.firstName && touched.firstName ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="firstName" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.firstName}</span>
                    </ErrorMessage>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Field
                      as={Input}
                      id="lastName"
                      name="lastName"
                      placeholder="Enter last name"
                      className={errors.lastName && touched.lastName ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="lastName" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.lastName}</span>
                    </ErrorMessage>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Field
                      as={Input}
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Enter email address"
                      className={errors.email && touched.email ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="email" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.email}</span>
                    </ErrorMessage>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Field
                      as={Input}
                      id="phone"
                      name="phone"
                      placeholder="Enter phone number"
                      className={errors.phone && touched.phone ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="phone" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.phone}</span>
                    </ErrorMessage>
                  </div>
                </div>

                {/* Password */}
                <div className="space-y-2">
                  <Label htmlFor="password">Password *</Label>
                  <Field
                    as={Input}
                    id="password"
                    name="password"
                    type="password"
                    placeholder="Enter password (minimum 6 characters)"
                    className={errors.password && touched.password ? 'border-red-500' : ''}
                  />
                  <ErrorMessage name="password" component="div" className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.password}</span>
                  </ErrorMessage>
                </div>

                {/* Branch Selection (conditional) */}
                {showBranchSelection && (
                  <div className="space-y-2">
                    <Label htmlFor="branch">Branch *</Label>
                    <Select
                      value={values.branch}
                      onValueChange={(value) => setFieldValue('branch', value)}
                    >
                      <SelectTrigger className={errors.branch && touched.branch ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select a branch" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="select-branch" disabled>
                          Select a branch
                        </SelectItem>
                        {availableBranches.map((branch) => (
                          <SelectItem key={branch.id} value={String(branch.id)}>
                            {branch.name} ({branch.code})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <ErrorMessage name="branch" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.branch}</span>
                    </ErrorMessage>
                  </div>
                )}

                {/* Role Selection removed - students automatically get student role */}

                {/* Additional Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth">Date of Birth</Label>
                    <Field
                      as={Input}
                      id="dateOfBirth"
                      name="dateOfBirth"
                      type="date"
                      className={errors.dateOfBirth && touched.dateOfBirth ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="dateOfBirth" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.dateOfBirth}</span>
                    </ErrorMessage>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="gender">Gender</Label>
                    <Select
                      value={values.gender}
                      onValueChange={(value) => setFieldValue('gender', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="select-gender" disabled>
                          Select gender
                        </SelectItem>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Address */}
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Field
                    as={Textarea}
                    id="address"
                    name="address"
                    placeholder="Enter address"
                    className={errors.address && touched.address ? 'border-red-500' : ''}
                    rows={3}
                  />
                  <ErrorMessage name="address" component="div" className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.address}</span>
                  </ErrorMessage>
                </div>

                {/* Location Information */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-gray-900">Location Information</h4>
                  <CascadingLocationDropdown
                    countryValue={values.country}
                    stateValue={values.state}
                    districtValue={values.district}
                    onCountryChange={(value) => setFieldValue('country', value)}
                    onStateChange={(value) => setFieldValue('state', value)}
                    onDistrictChange={(value) => setFieldValue('district', value)}
                    countryError={errors.country}
                    stateError={errors.state}
                    districtError={errors.district}
                    countryTouched={touched.country}
                    stateTouched={touched.state}
                    districtTouched={touched.district}
                    countryRequired={true}
                    stateRequired={true}
                    districtRequired={true}
                  />
                </div>

                {/* Student Details (Optional) */}
                <StudentDetailsForm
                  values={values}
                  setFieldValue={setFieldValue}
                  errors={errors}
                  touched={touched}
                />

                {/* Status (Institute Admin only) */}
                {canEditStatus && (
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_active"
                      checked={values.is_active}
                      onCheckedChange={(checked) => setFieldValue('is_active', checked)}
                    />
                    <Label htmlFor="is_active">Student is active</Label>
                  </div>
                )}

                {/* Form Actions */}
                <div className="flex justify-end space-x-4 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    disabled={isCreating || isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isCreating || isSubmitting}
                    className="flex items-center gap-2"
                  >
                    {(isCreating || isSubmitting) ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    {(isCreating || isSubmitting) ? 'Creating...' : 'Create Student'}
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </CardContent>
      </Card>
    </div>
  )
}
