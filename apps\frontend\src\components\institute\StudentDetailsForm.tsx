'use client'

import React from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface StudentDetailsFormProps {
  values: any
  setFieldValue: (field: string, value: any) => void
  errors: any
  touched: any
}

export function StudentDetailsForm({ values, setFieldValue, errors, touched }: StudentDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">Additional Student Details (Optional)</h3>
      <p className="text-sm text-gray-600">These details are optional and can be filled later.</p>

      {/* Education Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Education Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="highestQualification">Highest Qualification</Label>
              <Select
                value={values.studentDetails?.education?.highestQualification || 'select-qualification'}
                onValueChange={(value) => setFieldValue('studentDetails.education.highestQualification', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select qualification" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="select-qualification" disabled>Select qualification</SelectItem>
                  <SelectItem value="high_school">High School</SelectItem>
                  <SelectItem value="diploma">Diploma</SelectItem>
                  <SelectItem value="bachelors">Bachelor's Degree</SelectItem>
                  <SelectItem value="masters">Master's Degree</SelectItem>
                  <SelectItem value="phd">PhD</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="institution">Institution</Label>
              <Input
                id="institution"
                value={values.studentDetails?.education?.institution || ''}
                onChange={(e) => setFieldValue('studentDetails.education.institution', e.target.value)}
                placeholder="Name of institution"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="fieldOfStudy">Field of Study</Label>
              <Input
                id="fieldOfStudy"
                value={values.studentDetails?.education?.fieldOfStudy || ''}
                onChange={(e) => setFieldValue('studentDetails.education.fieldOfStudy', e.target.value)}
                placeholder="Field of study"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="graduationYear">Graduation Year</Label>
              <Input
                id="graduationYear"
                type="number"
                value={values.studentDetails?.education?.graduationYear || ''}
                onChange={(e) => setFieldValue('studentDetails.education.graduationYear', parseInt(e.target.value) || null)}
                placeholder="Year of graduation"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Personal Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Personal Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fatherName">Father's Name</Label>
              <Input
                id="fatherName"
                value={values.studentDetails?.personalInfo?.fatherName || ''}
                onChange={(e) => setFieldValue('studentDetails.personalInfo.fatherName', e.target.value)}
                placeholder="Father's name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="motherName">Mother's Name</Label>
              <Input
                id="motherName"
                value={values.studentDetails?.personalInfo?.motherName || ''}
                onChange={(e) => setFieldValue('studentDetails.personalInfo.motherName', e.target.value)}
                placeholder="Mother's name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="emergencyContact">Emergency Contact</Label>
              <Input
                id="emergencyContact"
                value={values.studentDetails?.personalInfo?.emergencyContact || ''}
                onChange={(e) => setFieldValue('studentDetails.personalInfo.emergencyContact', e.target.value)}
                placeholder="Emergency contact number"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="bloodGroup">Blood Group</Label>
              <Select
                value={values.studentDetails?.personalInfo?.bloodGroup || 'select-blood-group'}
                onValueChange={(value) => setFieldValue('studentDetails.personalInfo.bloodGroup', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select blood group" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="select-blood-group" disabled>Select blood group</SelectItem>
                  <SelectItem value="a_positive">A+</SelectItem>
                  <SelectItem value="a_negative">A-</SelectItem>
                  <SelectItem value="b_positive">B+</SelectItem>
                  <SelectItem value="b_negative">B-</SelectItem>
                  <SelectItem value="ab_positive">AB+</SelectItem>
                  <SelectItem value="ab_negative">AB-</SelectItem>
                  <SelectItem value="o_positive">O+</SelectItem>
                  <SelectItem value="o_negative">O-</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Additional Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="hobbies">Hobbies & Interests</Label>
              <Textarea
                id="hobbies"
                value={values.studentDetails?.additionalInfo?.hobbies || ''}
                onChange={(e) => setFieldValue('studentDetails.additionalInfo.hobbies', e.target.value)}
                placeholder="Hobbies and interests"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="skills">Skills</Label>
              <Textarea
                id="skills"
                value={values.studentDetails?.additionalInfo?.skills || ''}
                onChange={(e) => setFieldValue('studentDetails.additionalInfo.skills', e.target.value)}
                placeholder="Skills and competencies"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="goals">Career Goals</Label>
              <Textarea
                id="goals"
                value={values.studentDetails?.additionalInfo?.goals || ''}
                onChange={(e) => setFieldValue('studentDetails.additionalInfo.goals', e.target.value)}
                placeholder="Career goals and aspirations"
                rows={3}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
