'use client'

import React, { useEffect } from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { AlertCircle, Save, X, Loader2 } from 'lucide-react'
import { useStudentStore } from '@/stores/institute/useStudentStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import type { Student } from '@/stores/institute/useStudentStore'
import { CascadingLocationDropdown } from '@/components/common/CascadingLocationDropdown'
import { StudentDetailsForm } from './StudentDetailsForm'
import { RoleSelector } from './RoleSelector'

// Role-based field visibility logic
const getVisibleFields = (userRole: string, operation: 'create' | 'update') => {
  const baseFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'dateOfBirth', 'gender']
  
  const fieldVisibility = {
    institute_admin: {
      create: [...baseFields, 'password', 'branch_id', 'is_active'],
      update: [...baseFields, 'password', 'branch_id', 'is_active']
    },
    institute_staff: {
      create: [...baseFields, 'password', 'branch_id'],
      update: [...baseFields, 'password', 'branch_id']
    },
    branch_manager: {
      create: [...baseFields, 'password'],
      update: [...baseFields, 'password']
    }
  }
  
  return fieldVisibility[userRole as keyof typeof fieldVisibility]?.[operation] || baseFields
}

const canEditField = (fieldName: string, userRole: string, operation: 'create' | 'update') => {
  const visibleFields = getVisibleFields(userRole, operation)
  return visibleFields.includes(fieldName)
}

const studentUpdateValidationSchema = Yup.object({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email format').required('Email is required'),
  phone: Yup.string(),
  password: Yup.string().min(6, 'Password must be at least 6 characters'),
  branch: Yup.string().when('$userRole', {
    is: (role: string) => ['institute_admin', 'institute_staff'].includes(role),
    then: (schema) => schema.required('Branch selection is required').not(['select-branch'], 'Please select a branch'),
    otherwise: (schema) => schema.notRequired()
  }),
  // role_id removed - students always keep student role
  address: Yup.string(),
  dateOfBirth: Yup.date(),
  gender: Yup.string().oneOf(['male', 'female', 'other']),
  // Location validation
  country: Yup.string().required('Country selection is required').not(['select-country'], 'Please select a country'),
  state: Yup.string().required('State selection is required').not(['select-state'], 'Please select a state'),
  district: Yup.string().required('District selection is required').not(['select-district'], 'Please select a district'),
  is_active: Yup.boolean()
})

// Student interface is now imported from the store

interface StudentEditFormProps {
  student: Student
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

export function StudentEditForm({ student, isOpen, onClose, onSuccess }: StudentEditFormProps) {
  const { user } = useAuthStore()
  const {
    availableBranches,
    currentStudent,
    isUpdating,
    isFetching,
    updateStudent,
    fetchAvailableBranches,
    fetchStudentById,
    clearError,
    clearCurrentStudent
  } = useStudentStore()

  useEffect(() => {
    console.log('StudentEditForm useEffect triggered:', { isOpen, student })

    if (isOpen) {
      if (student?.id) {
        console.log('StudentEditForm: Opening with valid student:', student)
        console.log('StudentEditForm: Student ID:', student.id, 'Type:', typeof student.id)
        fetchAvailableBranches()
        fetchStudentById(student.id) // Fetch complete data including StudentDetails
        clearError()
      } else {
        console.error('StudentEditForm: Opened without valid student ID', {
          student,
          isOpen,
          hasStudent: !!student,
          studentId: student?.id,
          studentIdType: typeof student?.id,
          studentKeys: student ? Object.keys(student) : 'no student'
        })
      }
    }
  }, [isOpen, student?.id, fetchAvailableBranches, fetchStudentById, clearError])

  useEffect(() => {
    if (!isOpen) {
      clearCurrentStudent() // Clear when modal closes
    }
  }, [isOpen, clearCurrentStudent])

  // Use currentStudent (complete data) if available, fallback to student prop
  const studentData = currentStudent || student

  // Debug branch selection
  useEffect(() => {
    if (studentData && availableBranches.length > 0) {
      console.log('Branch Selection Debug:')
      console.log('- Student branch:', studentData.branch)
      console.log('- Available branches:', availableBranches)
      console.log('- Form branch value:', studentData?.branch?.id ? String(studentData.branch.id) : 'select-branch')
    }
  }, [studentData, availableBranches])

  // Debug location selection
  useEffect(() => {
    if (studentData) {
      console.log('Location Selection Debug:')
      console.log('- StudentDetails:', (studentData as any)?.studentDetails)
      console.log('- Country:', (studentData as any)?.studentDetails?.country)
      console.log('- State:', (studentData as any)?.studentDetails?.state)
      console.log('- District:', (studentData as any)?.studentDetails?.district)
      console.log('- Form values (converted to strings):')
      console.log('  - country:', (studentData as any)?.studentDetails?.country?.id ? String((studentData as any).studentDetails.country.id) : 'select-country')
      console.log('  - state:', (studentData as any)?.studentDetails?.state?.id ? String((studentData as any).studentDetails.state.id) : 'select-state')
      console.log('  - district:', (studentData as any)?.studentDetails?.district?.id ? String((studentData as any).studentDetails.district.id) : 'select-district')
    }
  }, [studentData])

  // Debug logging for branch data
  console.log('StudentEditForm: studentData:', studentData)
  console.log('StudentEditForm: branch data:', studentData?.branch)
  console.log('StudentEditForm: branch ID:', studentData?.branch?.id, 'Type:', typeof studentData?.branch?.id)
  console.log('StudentEditForm: availableBranches:', availableBranches)

  const initialValues = {
    firstName: studentData?.firstName || '',
    lastName: studentData?.lastName || '',
    email: studentData?.email || '',
    phone: studentData?.phone || '',
    password: '', // Always empty for security
    branch: studentData?.branch?.id ? String(studentData.branch.id) : 'select-branch',
    // role_id removed - students always keep student role
    address: studentData?.address || '',
    dateOfBirth: studentData?.dateOfBirth ? new Date(studentData.dateOfBirth).toISOString().split('T')[0] : '',
    gender: studentData?.gender || 'select-gender',
    // Location initial values from StudentDetails (convert to strings)
    country: (studentData as any)?.studentDetails?.country?.id ? String((studentData as any).studentDetails.country.id) : 'select-country',
    state: (studentData as any)?.studentDetails?.state?.id ? String((studentData as any).studentDetails.state.id) : 'select-state',
    district: (studentData as any)?.studentDetails?.district?.id ? String((studentData as any).studentDetails.district.id) : 'select-district',
    is_active: studentData?.is_active ?? true,
    // StudentDetails initial values
    studentDetails: {
      education: {
        highestQualification: (studentData as any)?.studentDetails?.education?.highestQualification || 'select-qualification',
        institution: (studentData as any)?.studentDetails?.education?.institution || '',
        fieldOfStudy: (studentData as any)?.studentDetails?.education?.fieldOfStudy || '',
        graduationYear: (studentData as any)?.studentDetails?.education?.graduationYear || '',
        percentage: (studentData as any)?.studentDetails?.education?.percentage || ''
      },
      personalInfo: {
        fatherName: (studentData as any)?.studentDetails?.personalInfo?.fatherName || '',
        motherName: (studentData as any)?.studentDetails?.personalInfo?.motherName || '',
        guardianName: (studentData as any)?.studentDetails?.personalInfo?.guardianName || '',
        emergencyContact: (studentData as any)?.studentDetails?.personalInfo?.emergencyContact || '',
        bloodGroup: (studentData as any)?.studentDetails?.personalInfo?.bloodGroup || 'select-blood-group',
        nationality: (studentData as any)?.studentDetails?.personalInfo?.nationality || '',
        religion: (studentData as any)?.studentDetails?.personalInfo?.religion || '',
        caste: (studentData as any)?.studentDetails?.personalInfo?.caste || ''
      },
      documents: {
        aadharNumber: (studentData as any)?.studentDetails?.documents?.aadharNumber || '',
        panNumber: (studentData as any)?.studentDetails?.documents?.panNumber || '',
        passportNumber: (studentData as any)?.studentDetails?.documents?.passportNumber || '',
        drivingLicense: (studentData as any)?.studentDetails?.documents?.drivingLicense || ''
      },
      additionalInfo: {
        hobbies: (studentData as any)?.studentDetails?.additionalInfo?.hobbies || '',
        skills: (studentData as any)?.studentDetails?.additionalInfo?.skills || '',
        experience: (studentData as any)?.studentDetails?.additionalInfo?.experience || '',
        goals: (studentData as any)?.studentDetails?.additionalInfo?.goals || '',
        notes: (studentData as any)?.studentDetails?.additionalInfo?.notes || ''
      }
    }
  }

  const handleSubmit = async (values: any, { setSubmitting }: any) => {
    try {
      // Remove password if empty
      const submitData = { ...values }
      if (!submitData.password || submitData.password.trim() === '') {
        delete submitData.password
      }
      
      await updateStudent(student.id, submitData)
      onSuccess?.()
      onClose()
    } catch (error) {
      // Error handling is done in the store
    } finally {
      setSubmitting(false)
    }
  }

  const showBranchSelection = user?.legacyRole !== 'branch_manager'
  const canEditStatus = user?.legacyRole === 'institute_admin'

  if (!isOpen) return null

  // Safety check: Don't render if no valid student
  const studentIdStr = String(student?.id || '')
  if (!student || !student.id || studentIdStr === 'undefined' || studentIdStr === 'null' || studentIdStr.trim() === '') {
    console.error('StudentEditForm: Cannot render without valid student ID', { student })
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8 space-y-4">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900">Invalid Student</h3>
              <p className="text-gray-600 mt-2">
                Cannot edit student. Please select a valid student from the list.
              </p>
            </div>
            <Button onClick={onClose} variant="outline">
              <X className="h-4 w-4 mr-2" />
              Close
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show loading state while fetching complete student data
  if (isFetching && !currentStudent) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mr-3" />
            <span>Loading student details...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Wait for complete student data before rendering form
  if (!currentStudent && student?.id) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mr-3" />
            <span>Preparing form...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Edit Student</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          <Formik
            initialValues={initialValues}
            validationSchema={studentUpdateValidationSchema}
            onSubmit={handleSubmit}
            context={{ userRole: user?.legacyRole }}
            enableReinitialize
          >
            {({ values, setFieldValue, errors, touched, isSubmitting }) => (
              <Form className="space-y-6">
                {/* Personal Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <Field
                      as={Input}
                      id="firstName"
                      name="firstName"
                      placeholder="Enter first name"
                      className={errors.firstName && touched.firstName ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="firstName" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.firstName}</span>
                    </ErrorMessage>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Field
                      as={Input}
                      id="lastName"
                      name="lastName"
                      placeholder="Enter last name"
                      className={errors.lastName && touched.lastName ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="lastName" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.lastName}</span>
                    </ErrorMessage>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Field
                      as={Input}
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Enter email address"
                      className={errors.email && touched.email ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="email" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.email}</span>
                    </ErrorMessage>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Field
                      as={Input}
                      id="phone"
                      name="phone"
                      placeholder="Enter phone number"
                      className={errors.phone && touched.phone ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="phone" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.phone}</span>
                    </ErrorMessage>
                  </div>
                </div>

                {/* Password (Optional for updates) */}
                <div className="space-y-2">
                  <Label htmlFor="password">Password (leave blank to keep current)</Label>
                  <Field
                    as={Input}
                    id="password"
                    name="password"
                    type="password"
                    placeholder="Enter new password (optional)"
                    className={errors.password && touched.password ? 'border-red-500' : ''}
                  />
                  <ErrorMessage name="password" component="div" className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.password}</span>
                  </ErrorMessage>
                </div>

                {/* Branch Selection (conditional) */}
                {showBranchSelection && (
                  <div className="space-y-2">
                    <Label htmlFor="branch">Branch *</Label>

                    <Select
                      value={values.branch}
                      onValueChange={(value) => {
                        console.log('Branch Select - Value changed to:', value)
                        setFieldValue('branch', value)
                      }}
                    >
                      <SelectTrigger className={errors.branch && touched.branch ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select a branch" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="select-branch" disabled>
                          Select a branch
                        </SelectItem>
                        {availableBranches.map((branch) => (
                          <SelectItem key={branch.id} value={String(branch.id)}>
                            {branch.name} ({branch.code})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <ErrorMessage name="branch" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.branch}</span>
                    </ErrorMessage>
                  </div>
                )}

                {/* Role Selection removed - students always keep student role */}

                {/* Additional Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth">Date of Birth</Label>
                    <Field
                      as={Input}
                      id="dateOfBirth"
                      name="dateOfBirth"
                      type="date"
                      className={errors.dateOfBirth && touched.dateOfBirth ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="dateOfBirth" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.dateOfBirth}</span>
                    </ErrorMessage>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="gender">Gender</Label>
                    <Select
                      value={values.gender}
                      onValueChange={(value) => setFieldValue('gender', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="select-gender" disabled>
                          Select gender
                        </SelectItem>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Address */}
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Field
                    as={Textarea}
                    id="address"
                    name="address"
                    placeholder="Enter address"
                    className={errors.address && touched.address ? 'border-red-500' : ''}
                    rows={3}
                  />
                  <ErrorMessage name="address" component="div" className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.address}</span>
                  </ErrorMessage>
                </div>

                {/* Location Information */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-gray-900">Location Information</h4>
                  <CascadingLocationDropdown
                    countryValue={values.country}
                    stateValue={values.state}
                    districtValue={values.district}
                    onCountryChange={(value) => setFieldValue('country', value)}
                    onStateChange={(value) => setFieldValue('state', value)}
                    onDistrictChange={(value) => setFieldValue('district', value)}
                    countryError={errors.country as string}
                    stateError={errors.state as string}
                    districtError={errors.district as string}
                    countryTouched={touched.country as boolean}
                    stateTouched={touched.state as boolean}
                    districtTouched={touched.district as boolean}
                    countryRequired={true}
                    stateRequired={true}
                    districtRequired={true}
                  />
                </div>

                {/* Student Details (Optional) */}
                <StudentDetailsForm
                  values={values}
                  setFieldValue={setFieldValue}
                  errors={errors}
                  touched={touched}
                />

                {/* Status (Institute Admin only) */}
                {canEditStatus && (
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_active"
                      checked={values.is_active}
                      onCheckedChange={(checked) => setFieldValue('is_active', checked)}
                    />
                    <Label htmlFor="is_active">Student is active</Label>
                  </div>
                )}

                {/* Form Actions */}
                <div className="flex justify-end space-x-4 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    disabled={isUpdating || isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isUpdating || isSubmitting}
                    className="flex items-center gap-2"
                  >
                    {(isUpdating || isSubmitting) ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    {(isUpdating || isSubmitting) ? 'Updating...' : 'Update Student'}
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </CardContent>
      </Card>
    </div>
  )
}
