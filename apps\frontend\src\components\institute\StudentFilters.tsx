'use client'

import React, { useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Search, Filter, X, Calendar as CalendarIcon } from 'lucide-react'
import { useStudentStore } from '@/stores/institute/useStudentStore'
// useRoleStore removed - no role filtering needed for student management

interface StudentFiltersProps {
  onFiltersChange?: (filters: any) => void
  className?: string
}

export function StudentFilters({ onFiltersChange, className }: StudentFiltersProps) {
  const {
    availableBranches,
    filters,
    setFilters,
    clearFilters,
    fetchAvailableBranches
  } = useStudentStore()
  
  // Role store removed - no role filtering needed for student management

  useEffect(() => {
    fetchAvailableBranches()
    // fetchStudentRoles removed
  }, [fetchAvailableBranches])

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }

  const handleDateRangeChange = (field: 'from' | 'to', dateString: string) => {
    const date = dateString ? new Date(dateString) : null
    const newDateRange = { ...filters.dateRange, [field]: date }
    handleFilterChange('dateRange', newDateRange)
  }

  const handleClearFilters = () => {
    clearFilters()
    onFiltersChange?.({
      search: '',
      branch: 'all',  // Use 'branch' field name
      status: 'all',
      // role_id removed - no role filtering needed
      dateRange: { from: null, to: null }
    })
  }

  const hasActiveFilters = filters.search ||
                          (filters.branch && filters.branch !== 'all') ||
                          filters.status !== 'all' ||
                          // role_id filter removed
                          filters.dateRange.from ||
                          filters.dateRange.to

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
          {hasActiveFilters && (
            <Button variant="outline" size="sm" onClick={handleClearFilters}>
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search by name, email, or phone..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Branch Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Branch</label>
            <Select
              value={filters.branch}
              onValueChange={(value) => handleFilterChange('branch', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All branches" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All branches</SelectItem>
                {availableBranches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.id}>
                    {branch.name} ({branch.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Status</label>
            <Select
              value={filters.status}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All students</SelectItem>
                <SelectItem value="active">Active only</SelectItem>
                <SelectItem value="inactive">Inactive only</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Role Filter removed - all users in student management are students */}

          {/* Date Range Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Registration Date</label>
            <div className="flex gap-2">
              <div className="flex-1">
                <Input
                  type="date"
                  placeholder="From date"
                  value={filters.dateRange.from ? filters.dateRange.from.toISOString().split('T')[0] : ''}
                  onChange={(e) => handleDateRangeChange('from', e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex-1">
                <Input
                  type="date"
                  placeholder="To date"
                  value={filters.dateRange.to ? filters.dateRange.to.toISOString().split('T')[0] : ''}
                  onChange={(e) => handleDateRangeChange('to', e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-2 border-t border-gray-100">
            <div className="flex flex-wrap gap-2">
              {filters.search && (
                <div className="flex items-center gap-1 bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs">
                  Search: "{filters.search}"
                  <button 
                    onClick={() => handleFilterChange('search', '')}
                    className="ml-1 hover:text-blue-900"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              )}
              {filters.branch && filters.branch !== 'all' && (
                <div className="flex items-center gap-1 bg-green-100 text-green-700 px-2 py-1 rounded text-xs">
                  Branch: {availableBranches.find(b => b.id === filters.branch)?.name}
                  <button
                    onClick={() => handleFilterChange('branch', 'all')}
                    className="ml-1 hover:text-green-900"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              )}
              {filters.status !== 'all' && (
                <div className="flex items-center gap-1 bg-yellow-100 text-yellow-700 px-2 py-1 rounded text-xs">
                  Status: {filters.status}
                  <button 
                    onClick={() => handleFilterChange('status', 'all')}
                    className="ml-1 hover:text-yellow-900"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              )}
              {/* Role filter badge removed - no role filtering needed */}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
