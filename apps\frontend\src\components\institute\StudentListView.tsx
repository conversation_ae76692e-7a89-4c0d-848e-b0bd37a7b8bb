'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Grid, List, Plus, Users, Loader2, MoreHorizontal, Edit, Trash2, Eye, ChevronUp, ChevronDown } from 'lucide-react'
import { useStudentStore } from '@/stores/institute/useStudentStore'
import { StudentCard } from './StudentCard'
import { StudentFilters } from './StudentFilters'
import { StudentStatusToggle } from './StudentStatusToggle'
import { ViewStudentModal } from './ViewStudentModal'
import { DeleteStudentDialog } from './DeleteStudentDialog'

interface Student {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  address?: string
  is_active: boolean
  enrolledCourses?: number
  totalProgress?: number
  lastActivity?: string
  createdAt: string
  branch?: {
    id: string
    name: string
    code: string
  }
  role?: {
    id: string
    name: string
    permissions: string[]
  }
}

type ViewMode = 'table' | 'card'
type SortField = 'name' | 'email' | 'branch' | 'status' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface StudentListViewProps {
  onCreateStudent?: () => void
  onEditStudent?: (student: Student) => void
  onDeleteStudent?: (student: Student) => void
  onViewStudent?: (student: Student) => void
}

export function StudentListView({
  onCreateStudent,
  onEditStudent,
  onDeleteStudent,
  onViewStudent
}: StudentListViewProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('card')
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [sortField, setSortField] = useState<SortField>('name')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')

  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedStudentId, setSelectedStudentId] = useState<string | null>(null)
  const [studentToDelete, setStudentToDelete] = useState<Student | null>(null)

  const {
    students,
    pagination,
    isFetching,
    fetchStudents,
    bulkUpdateStudentStatus
  } = useStudentStore()

  useEffect(() => {
    fetchStudents()
  }, [fetchStudents])

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const sortedStudents = [...students].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortField) {
      case 'name':
        aValue = `${a.firstName} ${a.lastName}`.toLowerCase()
        bValue = `${b.firstName} ${b.lastName}`.toLowerCase()
        break
      case 'email':
        aValue = a.email.toLowerCase()
        bValue = b.email.toLowerCase()
        break
      case 'branch':
        aValue = a.branch?.name?.toLowerCase() || ''
        bValue = b.branch?.name?.toLowerCase() || ''
        break
      case 'status':
        aValue = a.is_active ? 'active' : 'inactive'
        bValue = b.is_active ? 'active' : 'inactive'
        break
      case 'createdAt':
        aValue = new Date(a.createdAt)
        bValue = new Date(b.createdAt)
        break
      default:
        return 0
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedStudents(students.map(student => student.id))
    } else {
      setSelectedStudents([])
    }
  }

  const handleSelectStudent = (studentId: string, checked: boolean) => {
    if (checked) {
      setSelectedStudents([...selectedStudents, studentId])
    } else {
      setSelectedStudents(selectedStudents.filter(id => id !== studentId))
    }
  }

  const handleBulkActivate = async () => {
    try {
      await bulkUpdateStudentStatus(selectedStudents, true)
      setSelectedStudents([])
    } catch (error) {
      // Error handling is done in the store
    }
  }

  const handleBulkDeactivate = async () => {
    try {
      await bulkUpdateStudentStatus(selectedStudents, false)
      setSelectedStudents([])
    } catch (error) {
      // Error handling is done in the store
    }
  }

  const handleLoadMore = () => {
    if (pagination.hasNextPage) {
      fetchStudents(pagination.page + 1)
    }
  }

  // Modal handlers
  const handleViewStudent = (student: Student) => {
    console.log('handleViewStudent called with student:', student)
    console.log('Setting selectedStudentId to:', student.id)
    setSelectedStudentId(student.id)
    setViewModalOpen(true)
    console.log('ViewModal should now be open')
    // Call parent handler if provided (optional)
    onViewStudent?.(student)
  }

  const handleEditStudent = (student: Student) => {
    onEditStudent?.(student)
  }

  const handleDeleteStudent = (student: Student) => {
    setStudentToDelete(student)
    setDeleteDialogOpen(true)
  }

  const handleCloseViewModal = () => {
    setViewModalOpen(false)
    setSelectedStudentId(null)
  }

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false)
    setStudentToDelete(null)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Students</h1>
          <p className="text-gray-600">
            Manage your institute's students
          </p>
        </div>
        <Button onClick={onCreateStudent} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Student
        </Button>
      </div>

      {/* Filters */}
      <StudentFilters />

      {/* Stats and View Toggle */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Users className="h-4 w-4" />
            <span>{pagination.totalDocs} total students</span>
          </div>
          {selectedStudents.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {selectedStudents.length} selected
              </span>
              <Button size="sm" onClick={handleBulkActivate}>
                Activate Selected
              </Button>
              <Button size="sm" variant="outline" onClick={handleBulkDeactivate}>
                Deactivate Selected
              </Button>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'table' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('table')}
          >
            <List className="h-4 w-4 mr-1" />
            Table
          </Button>
          <Button
            variant={viewMode === 'card' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('card')}
          >
            <Grid className="h-4 w-4 mr-1" />
            Cards
          </Button>
        </div>
      </div>

      {/* Loading State */}
      {isFetching && students.length === 0 && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">Loading students...</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!isFetching && students.length === 0 && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No students found</h3>
              <p className="text-gray-600 mb-4">
                Get started by adding your first student to the system.
              </p>
              <Button onClick={onCreateStudent}>
                <Plus className="h-4 w-4 mr-2" />
                Add Student
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Card View */}
      {viewMode === 'card' && students.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {sortedStudents.map((student) => (
            <StudentCard
              key={student.id}
              student={student}
              isSelected={selectedStudents.includes(student.id)}
              onSelect={(checked) => handleSelectStudent(student.id, checked)}
              onEdit={() => onEditStudent?.(student)}
              onDelete={() => onDeleteStudent?.(student)}
              onView={() => handleViewStudent(student)}
            />
          ))}
        </div>
      )}

      {/* Table View */}
      {viewMode === 'table' && students.length > 0 && (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedStudents.length === students.length && students.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Student</TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 select-none"
                    onClick={() => handleSort('email')}
                  >
                    <div className="flex items-center gap-1">
                      Email
                      {sortField === 'email' && (
                        sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 select-none"
                    onClick={() => handleSort('branch')}
                  >
                    <div className="flex items-center gap-1">
                      Branch
                      {sortField === 'branch' && (
                        sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 select-none"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center gap-1">
                      Status
                      {sortField === 'status' && (
                        sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 select-none"
                    onClick={() => handleSort('createdAt')}
                  >
                    <div className="flex items-center gap-1">
                      Joined
                      {sortField === 'createdAt' && (
                        sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="w-12">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedStudents.map((student) => (
                  <TableRow key={student.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedStudents.includes(student.id)}
                        onCheckedChange={(checked) => handleSelectStudent(student.id, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${student.firstName}${student.lastName}`} />
                          <AvatarFallback>
                            {student.firstName[0]}{student.lastName[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {student.firstName} {student.lastName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {student.phone}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{student.email}</TableCell>
                    <TableCell>
                      {student.branch ? (
                        <Badge variant="outline">
                          {student.branch.name} ({student.branch.code})
                        </Badge>
                      ) : (
                        <span className="text-gray-400">No branch</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {student.role ? (
                        <Badge variant="secondary">{student.role.name}</Badge>
                      ) : (
                        <span className="text-gray-400">No role</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <StudentStatusToggle student={student} size="sm" />
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(student.createdAt).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewStudent(student)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditStudent(student)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Student
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteStudent(student)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Remove Student
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Load More */}
      {students.length > 0 && pagination.hasNextPage && (
        <div className="flex justify-center">
          <Button 
            variant="outline" 
            onClick={handleLoadMore}
            disabled={isFetching}
          >
            {isFetching ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading...
              </>
            ) : (
              'Load More'
            )}
          </Button>
        </div>
      )}

      {/* Pagination Info */}
      {students.length > 0 && (
        <div className="text-center text-sm text-gray-600">
          Showing {students.length} of {pagination.totalDocs} students
          {pagination.totalPages > 1 && (
            <span> • Page {pagination.page} of {pagination.totalPages}</span>
          )}
        </div>
      )}

      {/* Modals */}
      <ViewStudentModal
        isOpen={viewModalOpen}
        onClose={handleCloseViewModal}
        studentId={selectedStudentId}
      />

      <DeleteStudentDialog
        isOpen={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        student={studentToDelete}
      />
    </div>
  )
}
