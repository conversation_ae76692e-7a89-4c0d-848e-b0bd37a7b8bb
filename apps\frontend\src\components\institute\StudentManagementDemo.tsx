'use client'

import React, { useState } from 'react'
import { StudentListView } from './StudentListView'
import { StudentCreateForm } from './StudentCreateForm'
import { StudentEditForm } from './StudentEditForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import { Badge } from '@/components/ui/badge'
import { CheckCircle, AlertCircle, Clock, Users } from 'lucide-react'



export function StudentManagementDemo() {
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<any>(null)

  const handleCreateStudent = () => {
    setShowCreateForm(true)
  }

  const handleEditStudent = (student: any) => {
    setSelectedStudent(student)
    setShowEditForm(true)
  }

  const handleDeleteStudent = (student: any) => {
    console.log('Delete student:', student)
    // Implementation would show confirmation dialog
  }

  // Note: View functionality is handled internally by StudentListView
  // const handleViewStudent = (student: any) => {
  //   console.log('View student:', student)
  //   // Implementation would show student details modal
  // }



  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'in-progress':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'complete':
        return <Badge className="bg-green-100 text-green-700">Complete</Badge>
      case 'in-progress':
        return <Badge className="bg-yellow-100 text-yellow-700">In Progress</Badge>
      default:
        return <Badge className="bg-red-100 text-red-700">Pending</Badge>
    }
  }

  return (
    <div className="space-y-6">


      {/* Student Management System */}
      <StudentListView
        onCreateStudent={handleCreateStudent}
        onEditStudent={handleEditStudent}
        onDeleteStudent={handleDeleteStudent}
        // onViewStudent handled internally by StudentListView
      />

      {/* Create Student Form */}
      <StudentCreateForm
        isOpen={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onSuccess={() => {
          console.log('Student created successfully')
          // In real implementation, this would refresh the student list
        }}
      />

      {/* Edit Student Form */}
      {selectedStudent && (
        <StudentEditForm
          student={selectedStudent}
          isOpen={showEditForm}
          onClose={() => {
            setShowEditForm(false)
            setSelectedStudent(null)
          }}
          onSuccess={() => {
            console.log('Student updated successfully')
            // In real implementation, this would refresh the student list
          }}
        />
      )}

    
    </div>
  )
}
