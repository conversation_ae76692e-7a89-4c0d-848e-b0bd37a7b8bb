'use client'

import React, { useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { useStudentStore } from '@/stores/institute/useStudentStore'
import { Loader2 } from 'lucide-react'

interface Student {
  id: number  // Changed from string to number
  firstName: string
  lastName: string
  is_active: boolean
}

interface StudentStatusToggleProps {
  student: Student
  onStatusChange?: (student: Student, newStatus: boolean) => void
  showLabel?: boolean
  size?: 'sm' | 'default'
}

export function StudentStatusToggle({ 
  student, 
  onStatusChange, 
  showLabel = true,
  size = 'default'
}: StudentStatusToggleProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [pendingStatus, setPendingStatus] = useState<boolean | null>(null)
  const [reason, setReason] = useState('')
  const { toggleStudentStatus, isUpdating } = useStudentStore()

  const handleStatusToggle = (newStatus: boolean) => {
    setPendingStatus(newStatus)
    setReason('')
    setShowConfirmDialog(true)
  }

  const confirmStatusChange = async () => {
    if (pendingStatus === null) return

    try {
      await toggleStudentStatus(student.id, pendingStatus)
      onStatusChange?.(student, pendingStatus)
      setShowConfirmDialog(false)
      setPendingStatus(null)
      setReason('')
    } catch (error) {
      // Error handling is done in the store
    }
  }

  const cancelStatusChange = () => {
    setShowConfirmDialog(false)
    setPendingStatus(null)
    setReason('')
  }

  const getConfirmationMessage = () => {
    const action = pendingStatus ? 'activate' : 'deactivate'
    const consequence = pendingStatus 
      ? 'The student will be able to access courses and submit assignments.'
      : 'The student will lose access to courses and cannot submit assignments.'
    
    return {
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Student`,
      description: `Are you sure you want to ${action} ${student.firstName} ${student.lastName}? ${consequence}`
    }
  }

  const { title, description } = getConfirmationMessage()

  return (
    <>
      <div className={`flex items-center ${size === 'sm' ? 'space-x-1' : 'space-x-2'}`}>
        <Switch
          checked={student.is_active}
          onCheckedChange={handleStatusToggle}
          disabled={isUpdating}
          size={size}
        />
        {showLabel && (
          <span className={`${size === 'sm' ? 'text-xs' : 'text-sm'} ${
            student.is_active ? 'text-green-600' : 'text-red-600'
          }`}>
            {student.is_active ? 'Active' : 'Inactive'}
          </span>
        )}
        {isUpdating && (
          <Loader2 className={`${size === 'sm' ? 'h-3 w-3' : 'h-4 w-4'} animate-spin text-gray-400`} />
        )}
      </div>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle>{title}</AlertDialogTitle>
            <AlertDialogDescription className="space-y-3">
              <p>{description}</p>
              
              <div className="space-y-2">
                <Label htmlFor="reason" className="text-sm font-medium">
                  Reason for status change (optional)
                </Label>
                <Textarea
                  id="reason"
                  placeholder="Enter reason for this status change..."
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  className="min-h-[80px]"
                />
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelStatusChange} disabled={isUpdating}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmStatusChange} 
              disabled={isUpdating}
              className={pendingStatus ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Processing...
                </>
              ) : (
                `Confirm ${pendingStatus ? 'Activation' : 'Deactivation'}`
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
