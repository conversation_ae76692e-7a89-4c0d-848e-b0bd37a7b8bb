'use client'

import React, { useEffect } from 'react'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Loader2, User, Mail, Phone, MapPin, Calendar, Building, Users, X } from 'lucide-react'
import { useStudentStore } from '@/stores/institute/useStudentStore'

interface ViewStudentModalProps {
  isOpen: boolean
  onClose: () => void
  studentId: string | null
}

export function ViewStudentModal({ isOpen, onClose, studentId }: ViewStudentModalProps) {
  const { currentStudent, isFetching, fetchStudentById, clearCurrentStudent } = useStudentStore()

  console.log('ViewStudentModal render:', { isOpen, studentId })

  useEffect(() => {
    console.log('ViewStudentModal useEffect:', { isOpen, studentId })
    if (isOpen && studentId) {
      console.log('Fetching student with ID:', studentId)
      fetchStudentById(studentId)
    }
  }, [isOpen, studentId, fetchStudentById])

  useEffect(() => {
    if (!isOpen) {
      clearCurrentStudent()
    }
  }, [isOpen, clearCurrentStudent])

  const handleClose = () => {
    clearCurrentStudent()
    onClose()
  }

  if (!isOpen) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Student Details
          </DialogTitle>
        </DialogHeader>

        {isFetching ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading student details...</span>
          </div>
        ) : currentStudent ? (
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Name:</span>
                      <span>{currentStudent.firstName} {currentStudent.lastName}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Email:</span>
                      <span>{currentStudent.email}</span>
                    </div>
                    {currentStudent.phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">Phone:</span>
                        <span>{currentStudent.phone}</span>
                      </div>
                    )}
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Branch:</span>
                      <span>{currentStudent.branch?.name || 'Not assigned'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Role:</span>
                      <span>{currentStudent.role?.name || 'Student'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Status:</span>
                      <Badge variant={currentStudent.is_active ? 'default' : 'secondary'}>
                        {currentStudent.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                </div>

                {currentStudent.address && (
                  <div className="pt-2">
                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-gray-500 mt-1" />
                      <div>
                        <span className="font-medium">Address:</span>
                        <p className="text-gray-600">{currentStudent.address}</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-2">
                  {currentStudent.dateOfBirth && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Date of Birth:</span>
                      <span>{new Date(currentStudent.dateOfBirth).toLocaleDateString()}</span>
                    </div>
                  )}
                  {currentStudent.gender && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Gender:</span>
                      <span className="capitalize">{currentStudent.gender}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Location Information */}
            {currentStudent.studentDetails?.country && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Location Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <span className="font-medium">Country:</span>
                      <p className="text-gray-600">{currentStudent.studentDetails.country.name}</p>
                    </div>
                    {currentStudent.studentDetails.state && (
                      <div>
                        <span className="font-medium">State:</span>
                        <p className="text-gray-600">{currentStudent.studentDetails.state.name}</p>
                      </div>
                    )}
                    {currentStudent.studentDetails.district && (
                      <div>
                        <span className="font-medium">District:</span>
                        <p className="text-gray-600">{currentStudent.studentDetails.district.name}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Education Information */}
            {currentStudent.studentDetails?.education && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Education Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {currentStudent.studentDetails.education.highestQualification && (
                      <div>
                        <span className="font-medium">Highest Qualification:</span>
                        <p className="text-gray-600 capitalize">
                          {currentStudent.studentDetails.education.highestQualification.replace('_', ' ')}
                        </p>
                      </div>
                    )}
                    {currentStudent.studentDetails.education.institution && (
                      <div>
                        <span className="font-medium">Institution:</span>
                        <p className="text-gray-600">{currentStudent.studentDetails.education.institution}</p>
                      </div>
                    )}
                    {currentStudent.studentDetails.education.fieldOfStudy && (
                      <div>
                        <span className="font-medium">Field of Study:</span>
                        <p className="text-gray-600">{currentStudent.studentDetails.education.fieldOfStudy}</p>
                      </div>
                    )}
                    {currentStudent.studentDetails.education.graduationYear && (
                      <div>
                        <span className="font-medium">Graduation Year:</span>
                        <p className="text-gray-600">{currentStudent.studentDetails.education.graduationYear}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Personal Information */}
            {currentStudent.studentDetails?.personalInfo && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Personal Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {currentStudent.studentDetails.personalInfo.fatherName && (
                      <div>
                        <span className="font-medium">Father's Name:</span>
                        <p className="text-gray-600">{currentStudent.studentDetails.personalInfo.fatherName}</p>
                      </div>
                    )}
                    {currentStudent.studentDetails.personalInfo.motherName && (
                      <div>
                        <span className="font-medium">Mother's Name:</span>
                        <p className="text-gray-600">{currentStudent.studentDetails.personalInfo.motherName}</p>
                      </div>
                    )}
                    {currentStudent.studentDetails.personalInfo.emergencyContact && (
                      <div>
                        <span className="font-medium">Emergency Contact:</span>
                        <p className="text-gray-600">{currentStudent.studentDetails.personalInfo.emergencyContact}</p>
                      </div>
                    )}
                    {currentStudent.studentDetails.personalInfo.bloodGroup && (
                      <div>
                        <span className="font-medium">Blood Group:</span>
                        <p className="text-gray-600 uppercase">
                          {currentStudent.studentDetails.personalInfo.bloodGroup.replace('_', '')}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Additional Information */}
            {currentStudent.studentDetails?.additionalInfo && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Additional Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {currentStudent.studentDetails.additionalInfo.hobbies && (
                    <div>
                      <span className="font-medium">Hobbies & Interests:</span>
                      <p className="text-gray-600 mt-1">{currentStudent.studentDetails.additionalInfo.hobbies}</p>
                    </div>
                  )}
                  {currentStudent.studentDetails.additionalInfo.skills && (
                    <div>
                      <span className="font-medium">Skills:</span>
                      <p className="text-gray-600 mt-1">{currentStudent.studentDetails.additionalInfo.skills}</p>
                    </div>
                  )}
                  {currentStudent.studentDetails.additionalInfo.goals && (
                    <div>
                      <span className="font-medium">Career Goals:</span>
                      <p className="text-gray-600 mt-1">{currentStudent.studentDetails.additionalInfo.goals}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Timestamps */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Account Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                  <div>
                    <span className="font-medium">Created:</span>
                    <p>{new Date(currentStudent.createdAt).toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="font-medium">Last Updated:</span>
                    <p>{new Date(currentStudent.updatedAt).toLocaleString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">Student not found or failed to load.</p>
          </div>
        )}

        <div className="flex justify-end pt-4">
          <Button onClick={handleClose} variant="outline">
            <X className="h-4 w-4 mr-2" />
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
