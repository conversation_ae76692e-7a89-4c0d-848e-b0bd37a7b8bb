'use client'

import { useState, useEffect } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'


import {
  Loader2,
  ChevronLeft,
  ChevronRight,
  Building2,
  MapPin,
  Receipt,
  Check
} from 'lucide-react'
import { useBranchStore } from '@/stores/institute/useBranchStore'
import { useLocationStore } from '@/stores/location/useLocationStore'

// Validation schemas for each step
const step1Schema = Yup.object({
  name: Yup.string()
    .required('Branch name is required')
    .min(2, 'Branch name must be at least 2 characters')
    .max(100, 'Branch name must be less than 100 characters'),
  code: Yup.string()
    .max(20, 'Branch code must be less than 20 characters'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  branchType: Yup.string()
    .oneOf(['physical', 'virtual', 'department'], 'Invalid branch type'),
  capacity: Yup.number()
    .min(1, 'Capacity must be at least 1')
    .max(10000, 'Capacity cannot exceed 10,000'),
})

const step2Schema = Yup.object({
  location: Yup.object({
    address: Yup.string()
      .required('Address is required')
      .min(10, 'Address must be at least 10 characters'),
    country: Yup.string().required('Country is required'),
    state: Yup.string().required('State is required'),
    district: Yup.string().required('District is required'),
    pincode: Yup.string()
      .matches(/^\d{6}$/, 'Pincode must be 6 digits'),
  }),
  contact: Yup.object({
    phone: Yup.string()
      .matches(/^[+]?[\d\s-()]+$/, 'Invalid phone number format'),
    email: Yup.string().email('Invalid email format'),
    website: Yup.string().url('Invalid website URL'),
  }),
})

const step3Schema = Yup.object({
  taxInformation: Yup.object({
    gstNumber: Yup.string()
      .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Invalid GST number format'),
    panNumber: Yup.string()
      .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Invalid PAN number format'),
  }),
  billingSettings: Yup.object({
    commissionRate: Yup.number()
      .min(0, 'Commission rate cannot be negative')
      .max(100, 'Commission rate cannot exceed 100%'),
    billingDay: Yup.number()
      .min(1, 'Billing day must be between 1-28')
      .max(28, 'Billing day must be between 1-28'),
  }),
})

const steps = [
  {
    id: 1,
    title: 'Basic Information',
    description: 'Branch details',
    icon: Building2,
    schema: step1Schema
  },
  {
    id: 2,
    title: 'Location & Contact',
    description: 'Location and contact info',
    icon: MapPin,
    schema: step2Schema
  },
  {
    id: 3,
    title: 'Tax & Billing',
    description: 'Tax and billing settings',
    icon: Receipt,
    schema: step3Schema
  }
]

interface BranchWizardModalProps {
  isOpen: boolean
  onClose: () => void
  editingBranch?: any
  mode?: 'create' | 'edit'
}

export function BranchWizardModal({ 
  isOpen, 
  onClose, 
  editingBranch, 
  mode = 'create' 
}: BranchWizardModalProps) {
  const { 
    isCreating, 
    isUpdating, 
    createBranch, 
    updateBranch 
  } = useBranchStore()
  
  const { 
    countries, 
    states, 
    districts, 
    fetchCountries,
    fetchStates, 
    fetchDistricts 
  } = useLocationStore()

  const [currentStep, setCurrentStep] = useState(1)
  const [selectedCountry, setSelectedCountry] = useState('')
  const [selectedState, setSelectedState] = useState('')
  const [completedSteps, setCompletedSteps] = useState<number[]>([])

  const formik = useFormik({
    initialValues: {
      // Step 1: Basic Information
      name: '',
      code: '',
      description: '',
      branchType: 'physical',
      capacity: 100,
      isHeadOffice: false,
      isActive: true,
      
      // Step 2: Location & Contact
      location: {
        address: '',
        country: '',
        state: '',
        district: '',
        pincode: '',
        coordinates: {
          latitude: undefined,
          longitude: undefined,
        },
      },
      contact: {
        phone: '',
        email: '',
        website: '',
      },
      
      // Step 3: Tax & Billing
      taxInformation: {
        gstNumber: '',
        panNumber: '',
        taxRegistrationNumber: '',
        isGstRegistered: false,
      },

      workingDays: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: false,
      },
      operatingHours: {
        monday: '09:00-18:00',
        tuesday: '09:00-18:00',
        wednesday: '09:00-18:00',
        thursday: '09:00-18:00',
        friday: '09:00-18:00',
        saturday: '09:00-18:00',
        sunday: 'closed',
      },
      customization: {
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
      }
    },
    validationSchema: steps[currentStep - 1]?.schema,
    onSubmit: async (values) => {
      // This should only be called for final submission
      // Navigation between steps is handled by handleNext function
      console.log('🚀 Wizard Form Submit:', { mode, values })

      try {
        if (mode === 'edit' && editingBranch) {
          await updateBranch(editingBranch.id, values)
        } else {
          await createBranch(values)
        }
        handleClose()
      } catch (error) {
        console.error('❌ Wizard Form Submit Error:', error)
      }
    },
  })

  // Load countries when modal opens
  useEffect(() => {
    if (isOpen && countries.length === 0) {
      fetchCountries()
    }
  }, [isOpen, countries.length, fetchCountries])

  // Populate form when editing
  useEffect(() => {
    if (editingBranch && mode === 'edit') {
      // Populate form with editing branch data
      formik.setValues({
        name: editingBranch.name || '',
        code: editingBranch.code || '',
        description: editingBranch.description || '',
        branchType: editingBranch.branchType || 'physical',
        capacity: editingBranch.capacity || 100,
        isHeadOffice: editingBranch.isHeadOffice || false,
        isActive: editingBranch.isActive !== false,
        location: {
          address: editingBranch.location?.address || '',
          country: typeof editingBranch.location?.country === 'object' 
            ? editingBranch.location.country.id 
            : editingBranch.location?.country || '',
          state: typeof editingBranch.location?.state === 'object' 
            ? editingBranch.location.state.id 
            : editingBranch.location?.state || '',
          district: typeof editingBranch.location?.district === 'object' 
            ? editingBranch.location.district.id 
            : editingBranch.location?.district || '',
          pincode: editingBranch.location?.pincode || '',
          coordinates: {
            latitude: editingBranch.location?.coordinates?.latitude,
            longitude: editingBranch.location?.coordinates?.longitude,
          },
        },
        contact: {
          phone: editingBranch.contact?.phone || '',
          email: editingBranch.contact?.email || '',
          website: editingBranch.contact?.website || '',
        },
        taxInformation: {
          gstNumber: editingBranch.taxInformation?.gstNumber || '',
          panNumber: editingBranch.taxInformation?.panNumber || '',
          taxRegistrationNumber: editingBranch.taxInformation?.taxRegistrationNumber || '',
          isGstRegistered: editingBranch.taxInformation?.isGstRegistered || false,
        },

        workingDays: {
          monday: editingBranch.workingDays?.monday ?? true,
          tuesday: editingBranch.workingDays?.tuesday ?? true,
          wednesday: editingBranch.workingDays?.wednesday ?? true,
          thursday: editingBranch.workingDays?.thursday ?? true,
          friday: editingBranch.workingDays?.friday ?? true,
          saturday: editingBranch.workingDays?.saturday ?? true,
          sunday: editingBranch.workingDays?.sunday ?? false,
        },
        operatingHours: editingBranch.operatingHours || {
          monday: '09:00-18:00',
          tuesday: '09:00-18:00',
          wednesday: '09:00-18:00',
          thursday: '09:00-18:00',
          friday: '09:00-18:00',
          saturday: '09:00-18:00',
          sunday: 'closed',
        },
        customization: {
          primaryColor: editingBranch.customization?.primaryColor || '#3b82f6',
          secondaryColor: editingBranch.customization?.secondaryColor || '#64748b',
        }
      })

      // Set selected values for dropdowns
      const countryId = typeof editingBranch.location?.country === 'object' 
        ? editingBranch.location.country.id 
        : editingBranch.location?.country || ''
      const stateId = typeof editingBranch.location?.state === 'object' 
        ? editingBranch.location.state.id 
        : editingBranch.location?.state || ''

      setSelectedCountry(countryId)
      setSelectedState(stateId)

      // Fetch states and districts if needed
      if (countryId) {
        fetchStates(countryId)
      }
      if (stateId) {
        fetchDistricts(stateId)
      }
    }
  }, [editingBranch, mode])

  const handleCountryChange = (countryId: string) => {
    setSelectedCountry(countryId)
    formik.setFieldValue('location.country', countryId)
    formik.setFieldValue('location.state', '')
    formik.setFieldValue('location.district', '')
    setSelectedState('')
    fetchStates(countryId)
  }

  const handleStateChange = (stateId: string) => {
    setSelectedState(stateId)
    formik.setFieldValue('location.state', stateId)
    formik.setFieldValue('location.district', '')
    fetchDistricts(stateId)
  }

  const handleClose = () => {
    setCurrentStep(1)
    setCompletedSteps([])
    setSelectedCountry('')
    setSelectedState('')
    formik.resetForm()
    onClose()
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleNext = async () => {
    // Validate current step
    const currentSchema = steps[currentStep - 1]?.schema
    if (currentSchema) {
      try {
        await currentSchema.validate(formik.values, { abortEarly: false })
        // Mark step as completed and move to next
        setCompletedSteps(prev => [...prev.filter(s => s !== currentStep), currentStep])
        setCurrentStep(currentStep + 1)
      } catch (error: any) {
        // Set formik errors
        if (error.inner) {
          const errors: any = {}
          error.inner.forEach((err: any) => {
            errors[err.path] = err.message
          })
          formik.setErrors(errors)
        }
      }
    }
  }

  const handleFinalSubmit = async () => {
    console.log('🎯 Final Submit Triggered:', { currentStep, values: formik.values })

    // Validate final step first
    const currentSchema = steps[currentStep - 1]?.schema
    if (currentSchema) {
      try {
        await currentSchema.validate(formik.values, { abortEarly: false })
        console.log('✅ Final validation passed, submitting form...')
        // If validation passes, submit the form
        formik.handleSubmit()
      } catch (error: any) {
        console.error('❌ Final validation failed:', error)
        // Set formik errors
        if (error.inner) {
          const errors: any = {}
          error.inner.forEach((err: any) => {
            errors[err.path] = err.message
          })
          formik.setErrors(errors)
        }
      }
    } else {
      console.log('⚠️ No validation schema, submitting directly...')
      formik.handleSubmit()
    }
  }



  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4">
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Building2 className="h-5 w-5" />
              <span>{mode === 'edit' ? 'Edit Branch' : 'Create New Branch'}</span>
            </div>
            <div className="text-sm text-muted-foreground">
              Step {currentStep} of {steps.length}
            </div>
          </DialogTitle>

          {/* Minimal Step Indicator */}
          <div className="flex items-center space-x-2">
            {steps.map((step, index) => {
              const isCompleted = completedSteps.includes(step.id)
              const isCurrent = currentStep === step.id

              return (
                <div key={step.id} className="flex items-center">
                  <div className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-colors
                    ${isCompleted
                      ? 'bg-primary text-primary-foreground'
                      : isCurrent
                        ? 'bg-primary/10 text-primary border-2 border-primary'
                        : 'bg-muted text-muted-foreground'
                    }
                  `}>
                    {isCompleted ? <Check className="h-4 w-4" /> : step.id}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-8 h-px mx-2 ${isCompleted ? 'bg-primary' : 'bg-muted'}`} />
                  )}
                </div>
              )
            })}
          </div>

          {/* Current Step Title */}
          <div>
            <h3 className="text-lg font-semibold">{steps[currentStep - 1]?.title}</h3>
            <p className="text-sm text-muted-foreground">{steps[currentStep - 1]?.description}</p>
          </div>
        </DialogHeader>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Branch Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="Enter branch name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.name && formik.errors.name}
                  />
                  {formik.touched.name && formik.errors.name && (
                    <p className="text-sm text-destructive">{formik.errors.name}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code">Branch Code</Label>
                  <Input
                    id="code"
                    name="code"
                    placeholder="Auto-generated if empty"
                    value={formik.values.code}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.code && formik.errors.code}
                  />
                  {formik.touched.code && formik.errors.code && (
                    <p className="text-sm text-destructive">{formik.errors.code}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="branchType">Branch Type</Label>
                  <Select
                    value={formik.values.branchType}
                    onValueChange={(value) => formik.setFieldValue('branchType', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select branch type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="physical">Physical Branch</SelectItem>
                      <SelectItem value="virtual">Virtual Branch</SelectItem>
                      <SelectItem value="department">Department</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="capacity">Student Capacity</Label>
                  <Input
                    id="capacity"
                    name="capacity"
                    type="number"
                    placeholder="100"
                    value={formik.values.capacity}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.capacity && formik.errors.capacity}
                  />
                  {formik.touched.capacity && formik.errors.capacity && (
                    <p className="text-sm text-destructive">{formik.errors.capacity}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={formik.values.isActive}
                    onCheckedChange={(checked) => formik.setFieldValue('isActive', checked)}
                  />
                  <Label htmlFor="isActive">Branch is active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isHeadOffice"
                    checked={formik.values.isHeadOffice}
                    onCheckedChange={(checked) => formik.setFieldValue('isHeadOffice', checked)}
                  />
                  <Label htmlFor="isHeadOffice">Head office</Label>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Location & Contact */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="location.address">Address *</Label>
                <Textarea
                  id="location.address"
                  name="location.address"
                  placeholder="Enter complete address"
                  value={formik.values.location.address}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.location?.address && formik.errors.location?.address}
                />
                {formik.touched.location?.address && formik.errors.location?.address && (
                  <p className="text-sm text-destructive">{formik.errors.location.address}</p>
                )}
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location.country">Country *</Label>
                  <Select value={selectedCountry} onValueChange={handleCountryChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map((country) => (
                        <SelectItem key={country.id} value={country.id}>
                          {country.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formik.touched.location?.country && formik.errors.location?.country && (
                    <p className="text-sm text-destructive">{formik.errors.location.country}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location.state">State *</Label>
                  <Select value={selectedState} onValueChange={handleStateChange} disabled={!selectedCountry}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select state" />
                    </SelectTrigger>
                    <SelectContent>
                      {states.map((state) => (
                        <SelectItem key={state.id} value={state.id}>
                          {state.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formik.touched.location?.state && formik.errors.location?.state && (
                    <p className="text-sm text-destructive">{formik.errors.location.state}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location.district">District *</Label>
                  <Select
                    value={formik.values.location.district}
                    onValueChange={(value) => formik.setFieldValue('location.district', value)}
                    disabled={!selectedState}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select district" />
                    </SelectTrigger>
                    <SelectContent>
                      {districts.map((district) => (
                        <SelectItem key={district.id} value={district.id}>
                          {district.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formik.touched.location?.district && formik.errors.location?.district && (
                    <p className="text-sm text-destructive">{formik.errors.location.district}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location.pincode">Pincode</Label>
                  <Input
                    id="location.pincode"
                    name="location.pincode"
                    placeholder="Enter 6-digit pincode"
                    value={formik.values.location.pincode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.location?.pincode && formik.errors.location?.pincode}
                  />
                  {formik.touched.location?.pincode && formik.errors.location?.pincode && (
                    <p className="text-sm text-destructive">{formik.errors.location.pincode}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact.phone">Phone</Label>
                  <Input
                    id="contact.phone"
                    name="contact.phone"
                    placeholder="Enter phone number"
                    value={formik.values.contact.phone}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.contact?.phone && formik.errors.contact?.phone}
                  />
                  {formik.touched.contact?.phone && formik.errors.contact?.phone && (
                    <p className="text-sm text-destructive">{formik.errors.contact.phone}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact.email">Email</Label>
                  <Input
                    id="contact.email"
                    name="contact.email"
                    type="email"
                    placeholder="Enter email address"
                    value={formik.values.contact.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.contact?.email && formik.errors.contact?.email}
                  />
                  {formik.touched.contact?.email && formik.errors.contact?.email && (
                    <p className="text-sm text-destructive">{formik.errors.contact.email}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Tax & Billing */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="taxInformation.isGstRegistered"
                  checked={formik.values.taxInformation.isGstRegistered}
                  onCheckedChange={(checked) => formik.setFieldValue('taxInformation.isGstRegistered', checked)}
                />
                <Label htmlFor="taxInformation.isGstRegistered">This branch is GST registered</Label>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="taxInformation.gstNumber">GST Number</Label>
                  <Input
                    id="taxInformation.gstNumber"
                    name="taxInformation.gstNumber"
                    placeholder="22**********1Z5"
                    value={formik.values.taxInformation.gstNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.taxInformation?.gstNumber && formik.errors.taxInformation?.gstNumber}
                    disabled={!formik.values.taxInformation.isGstRegistered}
                  />
                  {formik.touched.taxInformation?.gstNumber && formik.errors.taxInformation?.gstNumber && (
                    <p className="text-sm text-destructive">{formik.errors.taxInformation.gstNumber}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="taxInformation.panNumber">PAN Number</Label>
                  <Input
                    id="taxInformation.panNumber"
                    name="taxInformation.panNumber"
                    placeholder="**********"
                    value={formik.values.taxInformation.panNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.taxInformation?.panNumber && formik.errors.taxInformation?.panNumber}
                  />
                  {formik.touched.taxInformation?.panNumber && formik.errors.taxInformation?.panNumber && (
                    <p className="text-sm text-destructive">{formik.errors.taxInformation.panNumber}</p>
                  )}
                </div>
              </div>


            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={currentStep === 1 ? handleClose : handlePrevious}
              disabled={isCreating || isUpdating}
            >
              {currentStep === 1 ? (
                'Cancel'
              ) : (
                <>
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Previous
                </>
              )}
            </Button>

            <Button
              type="button"
              onClick={currentStep === steps.length ? handleFinalSubmit : handleNext}
              disabled={isCreating || isUpdating}
            >
              {isCreating || isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {mode === 'edit' ? 'Updating...' : 'Creating...'}
                </>
              ) : currentStep === steps.length ? (
                mode === 'edit' ? 'Update Branch' : 'Create Branch'
              ) : (
                <>
                  Next
                  <ChevronRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
