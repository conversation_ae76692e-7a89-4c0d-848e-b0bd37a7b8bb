'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useBranchStore } from '@/stores/institute/useBranchStore'

export function DeleteBranchModal() {
  const { 
    showDeleteConfirmModal, 
    deletingBranch,
    isDeleting, 
    setShowDeleteConfirmModal, 
    deleteBranch 
  } = useBranchStore()

  const handleDelete = async () => {
    if (deletingBranch) {
      await deleteBranch(deletingBranch.id)
    }
  }

  const handleClose = () => {
    setShowDeleteConfirmModal(false)
  }

  if (!deletingBranch) return null

  return (
    <Dialog open={showDeleteConfirmModal} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <DialogTitle>Delete Branch</DialogTitle>
          </div>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the branch and all associated data.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Branch Details */}
          <div className="rounded-lg border p-4 space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold">{deletingBranch.name}</h4>
              <Badge variant={deletingBranch.isActive ? 'default' : 'secondary'}>
                {deletingBranch.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            
            <div className="text-sm text-muted-foreground space-y-1">
              <p>
                <span className="font-medium">Code:</span> {deletingBranch.code}
              </p>
              <p>
                <span className="font-medium">Location:</span> {deletingBranch.location?.address}
              </p>
              {deletingBranch.location?.district && deletingBranch.location?.state && (
                <p>
                  <span className="font-medium">Area:</span> {deletingBranch.location.district.name}, {deletingBranch.location.state.name}
                </p>
              )}
              {deletingBranch.contact?.phone && (
                <p>
                  <span className="font-medium">Phone:</span> {deletingBranch.contact.phone}
                </p>
              )}
              {deletingBranch.contact?.email && (
                <p>
                  <span className="font-medium">Email:</span> {deletingBranch.contact.email}
                </p>
              )}
            </div>

            {deletingBranch.isHeadOffice && (
              <div className="flex items-center space-x-2 p-2 bg-amber-50 border border-amber-200 rounded">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
                <span className="text-sm text-amber-800 font-medium">
                  This is the head office branch
                </span>
              </div>
            )}
          </div>

          {/* Warning Message */}
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-destructive mb-1">Warning:</p>
                <ul className="text-destructive/80 space-y-1">
                  <li>• All students enrolled in this branch will be affected</li>
                  <li>• All staff assigned to this branch will need reassignment</li>
                  <li>• All courses and content associated with this branch will be removed</li>
                  <li>• All billing and financial records will be archived</li>
                  {deletingBranch.isHeadOffice && (
                    <li className="font-medium">• Deleting the head office may affect institute operations</li>
                  )}
                </ul>
              </div>
            </div>
          </div>

          {/* Confirmation Input */}
          <div className="space-y-2">
            <p className="text-sm font-medium">
              Type <span className="font-mono bg-muted px-1 rounded">DELETE</span> to confirm:
            </p>
            <input
              type="text"
              placeholder="Type DELETE to confirm"
              className="w-full px-3 py-2 border rounded-md text-sm"
              onChange={(e) => {
                const deleteButton = document.getElementById('delete-confirm-button') as HTMLButtonElement
                if (deleteButton) {
                  deleteButton.disabled = e.target.value !== 'DELETE' || isDeleting
                }
              }}
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-4">
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleClose}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button 
            id="delete-confirm-button"
            type="button" 
            variant="destructive" 
            onClick={handleDelete}
            disabled={true} // Initially disabled until user types DELETE
          >
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Delete Branch
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
