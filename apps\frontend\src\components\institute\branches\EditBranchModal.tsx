'use client'

import { useEffect, useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Loader2 } from 'lucide-react'
import { useBranchStore } from '@/stores/institute/useBranchStore'
import { useLocationStore } from '@/stores/location/useLocationStore'

const editBranchSchema = Yup.object({
  name: Yup.string()
    .required('Branch name is required')
    .min(2, 'Branch name must be at least 2 characters')
    .max(100, 'Branch name must be less than 100 characters'),
  code: Yup.string()
    .max(20, 'Branch code must be less than 20 characters'),
  location: Yup.object({
    address: Yup.string()
      .required('Address is required')
      .min(10, 'Address must be at least 10 characters'),
    country: Yup.string().required('Country is required'),
    state: Yup.string().required('State is required'),
    district: Yup.string().required('District is required'),
    pincode: Yup.string()
      .matches(/^\d{6}$/, 'Pincode must be 6 digits'),
  }),
  contact: Yup.object({
    phone: Yup.string()
      .matches(/^[+]?[\d\s-()]+$/, 'Invalid phone number format'),
    email: Yup.string().email('Invalid email format'),
    website: Yup.string().url('Invalid website URL'),
  }),
  taxInformation: Yup.object({
    gstNumber: Yup.string()
      .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Invalid GST number format'),
    panNumber: Yup.string()
      .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Invalid PAN number format'),
  }),
})

export function EditBranchModal() {
  const { 
    showEditBranchModal, 
    editingBranch,
    isUpdating, 
    setShowEditBranchModal, 
    updateBranch 
  } = useBranchStore()
  
  const {
    countries,
    states,
    districts,
    fetchCountries,
    fetchStates,
    fetchDistricts
  } = useLocationStore()

  const [selectedCountry, setSelectedCountry] = useState('')
  const [selectedState, setSelectedState] = useState('')

  // Load countries when modal opens
  useEffect(() => {
    if (showEditBranchModal && countries.length === 0) {
      fetchCountries()
    }
  }, [showEditBranchModal, countries.length, fetchCountries])

  const formik = useFormik({
    initialValues: {
      name: '',
      code: '',
      location: {
        address: '',
        country: '',
        state: '',
        district: '',
        pincode: '',
        coordinates: {
          latitude: undefined,
          longitude: undefined,
        },
      },
      contact: {
        phone: '',
        email: '',
        website: '',
      },
      taxInformation: {
        gstNumber: '',
        panNumber: '',
        taxRegistrationNumber: '',
        isGstRegistered: false,
      },
      billingSettings: {
        billingCycle: 'monthly' as const,
        billingDay: 1,
        currency: 'INR',
        commissionRate: 10,
      },
      isActive: true,
      isHeadOffice: false,
      workingDays: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: false,
      },
    },
    validationSchema: editBranchSchema,
    onSubmit: async (values) => {
      if (editingBranch) {
        await updateBranch(editingBranch.id, values)
      }
    },
  })

  // Populate form when editing branch changes
  useEffect(() => {
    if (editingBranch) {
      formik.setValues({
        name: editingBranch.name || '',
        code: editingBranch.code || '',
        location: {
          address: editingBranch.location?.address || '',
          country: typeof editingBranch.location?.country === 'object' 
            ? editingBranch.location.country.id 
            : editingBranch.location?.country || '',
          state: typeof editingBranch.location?.state === 'object' 
            ? editingBranch.location.state.id 
            : editingBranch.location?.state || '',
          district: typeof editingBranch.location?.district === 'object' 
            ? editingBranch.location.district.id 
            : editingBranch.location?.district || '',
          pincode: editingBranch.location?.pincode || '',
          coordinates: {
            latitude: editingBranch.location?.coordinates?.latitude,
            longitude: editingBranch.location?.coordinates?.longitude,
          },
        },
        contact: {
          phone: editingBranch.contact?.phone || '',
          email: editingBranch.contact?.email || '',
          website: editingBranch.contact?.website || '',
        },
        taxInformation: {
          gstNumber: editingBranch.taxInformation?.gstNumber || '',
          panNumber: editingBranch.taxInformation?.panNumber || '',
          taxRegistrationNumber: editingBranch.taxInformation?.taxRegistrationNumber || '',
          isGstRegistered: editingBranch.taxInformation?.isGstRegistered || false,
        },
        billingSettings: {
          billingCycle: editingBranch.billingSettings?.billingCycle || 'monthly',
          billingDay: editingBranch.billingSettings?.billingDay || 1,
          currency: editingBranch.billingSettings?.currency || 'INR',
          commissionRate: editingBranch.billingSettings?.commissionRate || 10,
        },
        isActive: editingBranch.isActive,
        isHeadOffice: editingBranch.isHeadOffice || false,
        workingDays: {
          monday: editingBranch.workingDays?.monday ?? true,
          tuesday: editingBranch.workingDays?.tuesday ?? true,
          wednesday: editingBranch.workingDays?.wednesday ?? true,
          thursday: editingBranch.workingDays?.thursday ?? true,
          friday: editingBranch.workingDays?.friday ?? true,
          saturday: editingBranch.workingDays?.saturday ?? true,
          sunday: editingBranch.workingDays?.sunday ?? false,
        },
      })

      // Set selected values for dropdowns
      const countryId = typeof editingBranch.location?.country === 'object' 
        ? editingBranch.location.country.id 
        : editingBranch.location?.country || ''
      const stateId = typeof editingBranch.location?.state === 'object' 
        ? editingBranch.location.state.id 
        : editingBranch.location?.state || ''

      setSelectedCountry(countryId)
      setSelectedState(stateId)

      // Fetch states and districts if needed
      if (countryId) {
        fetchStates(countryId)
      }
      if (stateId) {
        fetchDistricts(stateId)
      }
    }
  }, [editingBranch, fetchStates, fetchDistricts])

  const handleCountryChange = (countryId: string) => {
    setSelectedCountry(countryId)
    formik.setFieldValue('location.country', countryId)
    formik.setFieldValue('location.state', '')
    formik.setFieldValue('location.district', '')
    setSelectedState('')
    fetchStates(countryId)
  }

  const handleStateChange = (stateId: string) => {
    setSelectedState(stateId)
    formik.setFieldValue('location.state', stateId)
    formik.setFieldValue('location.district', '')
    fetchDistricts(stateId)
  }

  const handleClose = () => {
    setShowEditBranchModal(false)
    formik.resetForm()
    setSelectedCountry('')
    setSelectedState('')
  }

  return (
    <Dialog open={showEditBranchModal} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Branch</DialogTitle>
          <DialogDescription>
            Update the branch information
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
              <CardDescription>
                Update the basic details for the branch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Branch Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="Enter branch name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.name && formik.errors.name}
                  />
                  {formik.touched.name && formik.errors.name && (
                    <p className="text-sm text-destructive">{formik.errors.name}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code">Branch Code</Label>
                  <Input
                    id="code"
                    name="code"
                    placeholder="Branch code"
                    value={formik.values.code}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.code && formik.errors.code}
                  />
                  {formik.touched.code && formik.errors.code && (
                    <p className="text-sm text-destructive">{formik.errors.code}</p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={formik.values.isActive}
                    onCheckedChange={(checked) => formik.setFieldValue('isActive', checked)}
                  />
                  <Label htmlFor="isActive">Branch is active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isHeadOffice"
                    checked={formik.values.isHeadOffice}
                    onCheckedChange={(checked) => formik.setFieldValue('isHeadOffice', checked)}
                  />
                  <Label htmlFor="isHeadOffice">This is the head office</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Location Information</CardTitle>
              <CardDescription>
                Update the physical location of the branch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="location.address">Address *</Label>
                <Textarea
                  id="location.address"
                  name="location.address"
                  placeholder="Enter complete address"
                  value={formik.values.location.address}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.location?.address && formik.errors.location?.address}
                />
                {formik.touched.location?.address && formik.errors.location?.address && (
                  <p className="text-sm text-destructive">{formik.errors.location.address}</p>
                )}
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location.country">Country *</Label>
                  <Select value={selectedCountry} onValueChange={handleCountryChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map((country) => (
                        <SelectItem key={country.id} value={country.id}>
                          {country.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formik.touched.location?.country && formik.errors.location?.country && (
                    <p className="text-sm text-destructive">{formik.errors.location.country}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location.state">State *</Label>
                  <Select value={selectedState} onValueChange={handleStateChange} disabled={!selectedCountry}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select state" />
                    </SelectTrigger>
                    <SelectContent>
                      {states.map((state) => (
                        <SelectItem key={state.id} value={state.id}>
                          {state.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formik.touched.location?.state && formik.errors.location?.state && (
                    <p className="text-sm text-destructive">{formik.errors.location.state}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location.district">District *</Label>
                  <Select 
                    value={formik.values.location.district} 
                    onValueChange={(value) => formik.setFieldValue('location.district', value)}
                    disabled={!selectedState}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select district" />
                    </SelectTrigger>
                    <SelectContent>
                      {districts.map((district) => (
                        <SelectItem key={district.id} value={district.id}>
                          {district.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formik.touched.location?.district && formik.errors.location?.district && (
                    <p className="text-sm text-destructive">{formik.errors.location.district}</p>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="location.pincode">Pincode</Label>
                <Input
                  id="location.pincode"
                  name="location.pincode"
                  placeholder="Enter 6-digit pincode"
                  value={formik.values.location.pincode}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.location?.pincode && formik.errors.location?.pincode}
                />
                {formik.touched.location?.pincode && formik.errors.location?.pincode && (
                  <p className="text-sm text-destructive">{formik.errors.location.pincode}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Contact Information</CardTitle>
              <CardDescription>
                Update contact details for the branch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contact.phone">Phone</Label>
                  <Input
                    id="contact.phone"
                    name="contact.phone"
                    placeholder="Enter phone number"
                    value={formik.values.contact.phone}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.contact?.phone && formik.errors.contact?.phone}
                  />
                  {formik.touched.contact?.phone && formik.errors.contact?.phone && (
                    <p className="text-sm text-destructive">{formik.errors.contact.phone}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact.email">Email</Label>
                  <Input
                    id="contact.email"
                    name="contact.email"
                    type="email"
                    placeholder="Enter email address"
                    value={formik.values.contact.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.contact?.email && formik.errors.contact?.email}
                  />
                  {formik.touched.contact?.email && formik.errors.contact?.email && (
                    <p className="text-sm text-destructive">{formik.errors.contact.email}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact.website">Website</Label>
                  <Input
                    id="contact.website"
                    name="contact.website"
                    placeholder="Enter website URL"
                    value={formik.values.contact.website}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.contact?.website && formik.errors.contact?.website}
                  />
                  {formik.touched.contact?.website && formik.errors.contact?.website && (
                    <p className="text-sm text-destructive">{formik.errors.contact.website}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isUpdating}>
              {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Update Branch
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
