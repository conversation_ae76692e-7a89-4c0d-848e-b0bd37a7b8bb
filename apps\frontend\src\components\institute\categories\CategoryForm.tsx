'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Save, X, Palette, Type } from 'lucide-react'
import { toast } from 'sonner'

interface Category {
  id?: string
  name: string
  description: string
  slug?: string
  icon?: string
  color?: string
  orderIndex?: number
  isActive: boolean
  isPublic: boolean
  isGlobal?: boolean
  shareSettings?: {
    shareWithBranches: boolean
    shareWithMarketplace: boolean
    allowBranchCustomization: boolean
  }
}

interface CategoryFormProps {
  category?: Category | null
  onSubmit: (data: Partial<Category>) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

const ICON_OPTIONS = [
  { value: 'fas fa-university', label: 'University', icon: '🏛️' },
  { value: 'fas fa-graduation-cap', label: 'Graduation', icon: '🎓' },
  { value: 'fas fa-book', label: 'Book', icon: '📚' },
  { value: 'fas fa-calculator', label: 'Calculator', icon: '🧮' },
  { value: 'fas fa-laptop-code', label: 'Coding', icon: '💻' },
  { value: 'fas fa-briefcase', label: 'Business', icon: '💼' },
  { value: 'fas fa-stethoscope', label: 'Medical', icon: '🩺' },
  { value: 'fas fa-balance-scale', label: 'Law', icon: '⚖️' },
  { value: 'fas fa-cogs', label: 'Engineering', icon: '⚙️' },
  { value: 'fas fa-chart-line', label: 'Analytics', icon: '📈' },
  { value: 'fas fa-globe', label: 'Global', icon: '🌍' },
  { value: 'fas fa-users', label: 'Community', icon: '👥' }
]

const COLOR_OPTIONS = [
  '#3b82f6', '#ef4444', '#10b981', '#f59e0b',
  '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16',
  '#f97316', '#6366f1', '#14b8a6', '#f43f5e'
]

export default function CategoryForm({ category, onSubmit, onCancel, isLoading = false }: CategoryFormProps) {
  const [formData, setFormData] = useState<Partial<Category>>({
    name: '',
    description: '',
    icon: 'fas fa-graduation-cap',
    color: '#3b82f6',
    isActive: true,
    isPublic: true,
    shareSettings: {
      shareWithBranches: false,
      shareWithMarketplace: false,
      allowBranchCustomization: false
    }
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (category) {
      setFormData({
        ...category,
        shareSettings: category.shareSettings || {
          shareWithBranches: false,
          shareWithMarketplace: false,
          allowBranchCustomization: false
        }
      })
    }
  }, [category])

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name?.trim()) {
      newErrors.name = 'Category name is required'
    } else if (formData.name.length < 2) {
      newErrors.name = 'Category name must be at least 2 characters'
    } else if (formData.name.length > 100) {
      newErrors.name = 'Category name must be less than 100 characters'
    }

    if (!formData.description?.trim()) {
      newErrors.description = 'Description is required'
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters'
    } else if (formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters'
    }

    if (formData.color && !/^#[0-9A-Fa-f]{6}$/.test(formData.color)) {
      newErrors.color = 'Please select a valid color'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error('Please fix the form errors')
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
      toast.success(category ? 'Category updated successfully' : 'Category created successfully')
    } catch (error) {
      console.error('Error submitting category:', error)
      toast.error('Failed to save category')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof Category, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleShareSettingChange = (field: keyof NonNullable<Category['shareSettings']>, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      shareSettings: {
        ...prev.shareSettings,
        [field]: value
      }
    }))
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Type className="h-5 w-5" />
              <span>Basic Information</span>
            </CardTitle>
            <CardDescription>
              Enter the basic details for the category
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Category Name *</Label>
              <Input
                id="name"
                value={formData.name || ''}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., Government Exams"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name}</p>
              )}
            </div>

            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe what this category covers..."
                rows={3}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && (
                <p className="text-sm text-red-500 mt-1">{errors.description}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive || false}
                  onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isPublic"
                  checked={formData.isPublic || false}
                  onCheckedChange={(checked) => handleInputChange('isPublic', checked)}
                />
                <Label htmlFor="isPublic">Public</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Appearance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Palette className="h-5 w-5" />
              <span>Appearance</span>
            </CardTitle>
            <CardDescription>
              Customize the visual appearance of the category
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Icon</Label>
              <div className="grid grid-cols-4 gap-2 mt-2">
                {ICON_OPTIONS.map((option) => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => handleInputChange('icon', option.value)}
                    className={`p-2 border rounded-md text-center hover:bg-gray-50 ${
                      formData.icon === option.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                    }`}
                  >
                    <div className="text-lg">{option.icon}</div>
                    <div className="text-xs text-gray-600">{option.label}</div>
                  </button>
                ))}
              </div>
            </div>

            <div>
              <Label>Color</Label>
              <div className="grid grid-cols-6 gap-2 mt-2">
                {COLOR_OPTIONS.map((color) => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => handleInputChange('color', color)}
                    className={`w-8 h-8 rounded-md border-2 ${
                      formData.color === color ? 'border-gray-800' : 'border-gray-200'
                    }`}
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
              <Input
                value={formData.color || ''}
                onChange={(e) => handleInputChange('color', e.target.value)}
                placeholder="#3b82f6"
                className="mt-2"
              />
            </div>

            {/* Preview */}
            <div className="p-3 border rounded-md bg-gray-50">
              <Label className="text-sm text-gray-600">Preview</Label>
              <div className="flex items-center space-x-2 mt-1">
                {formData.icon && (
                  <span className={formData.icon} style={{ color: formData.color }} />
                )}
                <span className="font-medium">{formData.name || 'Category Name'}</span>
                {!formData.isActive && <Badge variant="destructive">Inactive</Badge>}
                {formData.isGlobal && <Badge variant="secondary">Global</Badge>}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sharing Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Sharing Settings</CardTitle>
          <CardDescription>
            Configure how this category can be shared with other branches and marketplace
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center space-x-2">
              <Switch
                id="shareWithBranches"
                checked={formData.shareSettings?.shareWithBranches || false}
                onCheckedChange={(checked) => handleShareSettingChange('shareWithBranches', checked)}
              />
              <div>
                <Label htmlFor="shareWithBranches">Share with Branches</Label>
                <p className="text-sm text-gray-600">Allow sharing with other branches</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="shareWithMarketplace"
                checked={formData.shareSettings?.shareWithMarketplace || false}
                onCheckedChange={(checked) => handleShareSettingChange('shareWithMarketplace', checked)}
              />
              <div>
                <Label htmlFor="shareWithMarketplace">Share with Marketplace</Label>
                <p className="text-sm text-gray-600">Allow sharing in public marketplace</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="allowBranchCustomization"
                checked={formData.shareSettings?.allowBranchCustomization || false}
                onCheckedChange={(checked) => handleShareSettingChange('allowBranchCustomization', checked)}
              />
              <div>
                <Label htmlFor="allowBranchCustomization">Allow Customization</Label>
                <p className="text-sm text-gray-600">Allow branches to customize this category</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting || isLoading}>
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {category ? 'Update Category' : 'Create Category'}
        </Button>
      </div>
    </form>
  )
}
