'use client'

import React from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import CategoryForm from './CategoryForm'

interface Category {
  id?: string
  name: string
  description: string
  slug?: string
  icon?: string
  color?: string
  orderIndex?: number
  isActive: boolean
  isPublic: boolean
  isGlobal?: boolean
  shareSettings?: {
    shareWithBranches: boolean
    shareWithMarketplace: boolean
    allowBranchCustomization: boolean
  }
}

interface CategoryFormModalProps {
  isOpen: boolean
  onClose: () => void
  category?: Category | null
  onSubmit: (data: Partial<Category>) => Promise<void>
  isLoading?: boolean
}

export default function CategoryFormModal({
  isOpen,
  onClose,
  category,
  onSubmit,
  isLoading = false
}: CategoryFormModalProps) {
  const handleSubmit = async (data: Partial<Category>) => {
    await onSubmit(data)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {category ? 'Edit Category' : 'Create New Category'}
          </DialogTitle>
          <DialogDescription>
            {category 
              ? 'Update the category information and settings.'
              : 'Create a new category to organize your courses and exam types.'
            }
          </DialogDescription>
        </DialogHeader>
        
        <CategoryForm
          category={category}
          onSubmit={handleSubmit}
          onCancel={onClose}
          isLoading={isLoading}
        />
      </DialogContent>
    </Dialog>
  )
}
