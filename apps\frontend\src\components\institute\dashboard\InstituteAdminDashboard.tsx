'use client'

import { useState, useEffect } from 'react'
import { 
  BookOpen, 
  Users, 
  GraduationCap,
  Video,
  FileText,
  TrendingUp,
  DollarSign,
  Calendar,
  Clock,
  Star,
  AlertCircle,
  CheckCircle,
  BarChart3,
  Activity,
  ShoppingBag,
  Award
} from 'lucide-react'
import { ResponsiveGrid, ResponsiveCard, ResponsiveSection } from '@/components/shared/layout/ResponsiveContainer'
import { useResponsive } from '@/hooks/useResponsive'

// Mock data for Institute Admin Dashboard
const mockInstituteData = {
  stats: {
    totalCourses: 45,
    activeCourses: 38,
    totalStudents: 1247,
    activeStudents: 892,
    totalInstructors: 23,
    activeInstructors: 18,
    monthlyRevenue: 45670,
    revenueGrowth: 15.2,
    liveClassesToday: 8,
    pendingAssignments: 156
  },
  recentActivity: [
    {
      id: 1,
      type: 'course_enrollment',
      message: '12 new students enrolled in "Advanced JavaScript"',
      timestamp: '5 minutes ago',
      status: 'success'
    },
    {
      id: 2,
      type: 'live_class',
      message: 'Live class "React Fundamentals" starting in 30 minutes',
      timestamp: '25 minutes ago',
      status: 'pending'
    },
    {
      id: 3,
      type: 'assignment_submission',
      message: '45 assignments submitted for "Database Design"',
      timestamp: '1 hour ago',
      status: 'info'
    },
    {
      id: 4,
      type: 'course_completion',
      message: '8 students completed "Python Basics" course',
      timestamp: '2 hours ago',
      status: 'success'
    }
  ],
  topCourses: [
    { name: 'Advanced JavaScript', students: 156, rating: 4.8, revenue: 8900 },
    { name: 'React Fundamentals', students: 134, rating: 4.7, revenue: 7650 },
    { name: 'Python for Beginners', students: 128, rating: 4.9, revenue: 6400 },
    { name: 'Database Design', students: 98, rating: 4.6, revenue: 5880 },
    { name: 'UI/UX Design', students: 87, rating: 4.8, revenue: 5220 }
  ],
  upcomingClasses: [
    { course: 'React Fundamentals', time: '2:30 PM', instructor: 'John Smith', students: 45 },
    { course: 'Python Basics', time: '4:00 PM', instructor: 'Sarah Johnson', students: 38 },
    { course: 'Database Design', time: '6:30 PM', instructor: 'Mike Wilson', students: 29 }
  ]
}

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  icon: React.ComponentType<any>
  color: string
  href?: string
}

function MetricCard({ title, value, change, icon: Icon, color, href }: MetricCardProps) {
  const { isMobile } = useResponsive()
  
  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      if (val >= 1000000) return `${(val / 1000000).toFixed(1)}M`
      if (val >= 1000) return `${(val / 1000).toFixed(1)}K`
      return val.toLocaleString()
    }
    return val
  }

  const card = (
    <ResponsiveCard className="hover:shadow-lg transition-shadow cursor-pointer">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-500 mb-1">
            {title}
          </div>
          <div className={`text-2xl font-bold text-gray-900 mb-2 ${isMobile ? 'text-xl' : ''}`}>
            {formatValue(value)}
          </div>
          {change !== undefined && (
            <div className={`flex items-center text-sm ${
              change >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              <TrendingUp className={`w-4 h-4 mr-1 ${change < 0 ? 'rotate-180' : ''}`} />
              <span>{Math.abs(change)}% from last month</span>
            </div>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </ResponsiveCard>
  )

  if (href) {
    return (
      <a href={href} className="block">
        {card}
      </a>
    )
  }

  return card
}

function ActivityItem({ activity }: { activity: any }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      default:
        return <Activity className="w-4 h-4 text-blue-500" />
    }
  }

  return (
    <div className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
      <div className="flex-shrink-0 mt-0.5">
        {getStatusIcon(activity.status)}
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-sm text-gray-900">{activity.message}</div>
        <div className="text-xs text-gray-500 mt-1">{activity.timestamp}</div>
      </div>
    </div>
  )
}

export function InstituteAdminDashboard() {
  const { isMobile } = useResponsive()
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d')

  const metrics = [
    {
      title: 'Total Courses',
      value: mockInstituteData.stats.totalCourses,
      change: 8.5,
      icon: BookOpen,
      color: 'bg-blue-600',
      href: '/institute-admin/courses'
    },
    {
      title: 'Active Students',
      value: mockInstituteData.stats.activeStudents,
      change: 12.3,
      icon: GraduationCap,
      color: 'bg-green-600',
      href: '/institute-admin/students'
    },
    {
      title: 'Monthly Revenue',
      value: `$${mockInstituteData.stats.monthlyRevenue.toLocaleString()}`,
      change: mockInstituteData.stats.revenueGrowth,
      icon: DollarSign,
      color: 'bg-purple-600',
      href: '/institute-admin/billing'
    },
    {
      title: 'Live Classes Today',
      value: mockInstituteData.stats.liveClassesToday,
      icon: Video,
      color: 'bg-orange-600',
      href: '/institute-admin/live-classes'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className={`font-bold text-gray-900 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
            Institute Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Welcome back! Here's what's happening at your institute.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          >
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <ResponsiveGrid
        mobileColumns={1}
        tabletColumns={2}
        desktopColumns={4}
        gap={6}
      >
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </ResponsiveGrid>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <ResponsiveCard>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
              <a href="/institute-admin/activity" className="text-sm text-blue-600 hover:text-blue-700">
                View all
              </a>
            </div>
            <div className="space-y-1">
              {mockInstituteData.recentActivity.map((activity) => (
                <ActivityItem key={activity.id} activity={activity} />
              ))}
            </div>
          </ResponsiveCard>
        </div>

        {/* Quick Actions */}
        <div>
          <ResponsiveCard>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <a
                href="/institute-admin/courses/create"
                className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <BookOpen className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">
                    Create New Course
                  </span>
                </div>
              </a>

              <a
                href="/institute-admin/live-classes/schedule"
                className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <Video className="w-5 h-5 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    Schedule Live Class
                  </span>
                </div>
              </a>

              <a
                href="/institute-admin/students/enrollment"
                className="flex items-center justify-between p-3 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <Users className="w-5 h-5 text-purple-600" />
                  <span className="text-sm font-medium text-purple-800">
                    Manage Enrollments
                  </span>
                </div>
              </a>

              <a
                href="/institute-admin/exams/create"
                className="flex items-center justify-between p-3 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <FileText className="w-5 h-5 text-orange-600" />
                  <span className="text-sm font-medium text-orange-800">
                    Create Exam
                  </span>
                </div>
              </a>
            </div>
          </ResponsiveCard>
        </div>
      </div>

      {/* Top Courses and Upcoming Classes */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Courses */}
        <ResponsiveCard>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Top Performing Courses</h3>
            <BarChart3 className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-3">
            {mockInstituteData.topCourses.map((course, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{course.name}</div>
                  <div className="flex items-center space-x-2 text-xs text-gray-500">
                    <span>{course.students} students</span>
                    <span>•</span>
                    <div className="flex items-center">
                      <Star className="w-3 h-3 text-yellow-500 mr-1" />
                      <span>{course.rating}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    ${course.revenue.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">Revenue</div>
                </div>
              </div>
            ))}
          </div>
        </ResponsiveCard>

        {/* Upcoming Live Classes */}
        <ResponsiveCard>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Upcoming Live Classes</h3>
            <Calendar className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-3">
            {mockInstituteData.upcomingClasses.map((classItem, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{classItem.course}</div>
                  <div className="text-xs text-gray-500">
                    by {classItem.instructor} • {classItem.students} students
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-blue-600">{classItem.time}</div>
                  <div className="text-xs text-gray-500">Today</div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 pt-3 border-t border-gray-200">
            <a
              href="/institute-admin/live-classes/schedule"
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              View full schedule →
            </a>
          </div>
        </ResponsiveCard>
      </div>
    </div>
  )
}

export default InstituteAdminDashboard
