'use client'

import { useState } from 'react'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useResponsive } from '@/hooks/useResponsive'
import { ResponsiveCard, ResponsiveGrid } from '@/components/shared/layout/ResponsiveContainer'
import { 
  BookOpen, 
  GraduationCap,
  UserCheck,
  Video,
  FileText,
  CreditCard,
  ShoppingBag,
  BarChart3,
  Settings,
  Building2,
  Users,
  Calendar,
  Award,
  Monitor,
  Tablet,
  Smartphone,
  Menu,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

export function InstituteAdminLayoutDemo() {
  const { isCollapsed, toggleSidebar, navigationItems } = useSidebarStore()
  const { isMobile, isTablet, isDesktop } = useResponsive()
  
  const [selectedSection, setSelectedSection] = useState('courses')

  // Institute Admin Navigation Sections (8 main sections)
  const navigationSections = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: BarChart3,
      description: 'Institute overview and analytics',
      color: 'bg-blue-600',
      features: ['Revenue tracking', 'Student analytics', 'Course performance', 'Live class schedule']
    },
    {
      id: 'courses',
      label: 'Course Management',
      icon: BookOpen,
      description: 'Manage courses and curriculum',
      color: 'bg-green-600',
      features: ['Course creation', 'Content management', 'Categories', 'Analytics']
    },
    {
      id: 'students',
      label: 'Student Management',
      icon: GraduationCap,
      description: 'Manage student accounts and enrollment',
      color: 'bg-purple-600',
      features: ['Student profiles', 'Enrollments', 'Progress tracking', 'Certificates']
    },
    {
      id: 'instructors',
      label: 'Instructor Management',
      icon: UserCheck,
      description: 'Manage instructors and teaching staff',
      color: 'bg-indigo-600',
      features: ['Instructor profiles', 'Schedules', 'Performance metrics', 'Assignments']
    },
    {
      id: 'live-classes',
      label: 'Live Classes',
      icon: Video,
      description: 'Manage live classes and sessions',
      color: 'bg-red-600',
      features: ['Class scheduling', 'Virtual rooms', 'Recordings', 'Attendance']
    },
    {
      id: 'exams',
      label: 'Exams & Assessments',
      icon: FileText,
      description: 'Manage exams and assessments',
      color: 'bg-orange-600',
      features: ['Exam creation', 'Question bank', 'Results', 'Analytics']
    },
    {
      id: 'billing',
      label: 'Billing & Payments',
      icon: CreditCard,
      description: 'Manage billing and payments',
      color: 'bg-pink-600',
      features: ['Revenue tracking', 'Transactions', 'Subscriptions', 'Reports']
    },
    {
      id: 'marketplace',
      label: 'Marketplace',
      icon: ShoppingBag,
      description: 'Manage course marketplace',
      color: 'bg-teal-600',
      features: ['Published courses', 'Orders', 'Reviews', 'Sales analytics']
    }
  ]

  const deviceTypes = [
    {
      type: 'desktop',
      label: 'Desktop',
      icon: Monitor,
      active: isDesktop,
      description: 'Full sidebar with all 8 sections'
    },
    {
      type: 'tablet',
      label: 'Tablet',
      icon: Tablet,
      active: isTablet,
      description: 'Collapsible sidebar with icons'
    },
    {
      type: 'mobile',
      label: 'Mobile',
      icon: Smartphone,
      active: isMobile,
      description: 'Bottom navigation + slide-out menu'
    }
  ]

  const layoutFeatures = [
    {
      title: 'Comprehensive Navigation',
      description: '8 main sections covering all institute management needs',
      icon: Menu,
      benefits: [
        'Course and curriculum management',
        'Student and instructor management',
        'Live classes and assessments',
        'Billing and marketplace integration'
      ]
    },
    {
      title: 'Responsive Design',
      description: 'Adapts seamlessly across all device types',
      icon: Monitor,
      benefits: [
        'Desktop persistent sidebar',
        'Tablet auto-collapse',
        'Mobile bottom navigation',
        'Touch-optimized interactions'
      ]
    },
    {
      title: 'Role-Based Access',
      description: 'Institute-specific permissions and features',
      icon: UserCheck,
      benefits: [
        'Institute admin permissions',
        'Feature-based access control',
        'Customizable navigation',
        'Secure authentication'
      ]
    }
  ]

  const selectedSectionData = navigationSections.find(s => s.id === selectedSection)

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Institute Admin Layout Demo
        </h1>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Explore the comprehensive Institute Admin Layout with 8 main navigation sections 
          designed specifically for educational institute management and operations.
        </p>
      </div>

      {/* Current Device Detection */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Current Device Detection</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={3} desktopColumns={3} gap={4}>
          {deviceTypes.map((device) => (
            <div
              key={device.type}
              className={`p-4 rounded-lg border-2 transition-colors ${
                device.active 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-3 mb-2">
                <device.icon className={`w-6 h-6 ${
                  device.active ? 'text-blue-600' : 'text-gray-400'
                }`} />
                <span className={`font-medium ${
                  device.active ? 'text-blue-900' : 'text-gray-600'
                }`}>
                  {device.label}
                </span>
                {device.active && (
                  <span className="px-2 py-1 text-xs bg-blue-600 text-white rounded-full">
                    Active
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-600">{device.description}</p>
            </div>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Navigation Sections Overview */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          8 Main Navigation Sections
        </h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={2} desktopColumns={4} gap={4}>
          {navigationSections.map((section) => (
            <button
              key={section.id}
              onClick={() => setSelectedSection(section.id)}
              className={`p-4 rounded-lg border-2 text-left transition-colors ${
                selectedSection === section.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${section.color}`}>
                  <section.icon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="font-medium text-gray-900">{section.label}</div>
                  {selectedSection === section.id && (
                    <div className="text-xs text-blue-600">Selected</div>
                  )}
                </div>
              </div>
              <p className="text-sm text-gray-600">{section.description}</p>
            </button>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Selected Section Details */}
      {selectedSectionData && (
        <ResponsiveCard>
          <div className="flex items-center space-x-3 mb-4">
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${selectedSectionData.color}`}>
              <selectedSectionData.icon className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {selectedSectionData.label}
              </h3>
              <p className="text-gray-600">{selectedSectionData.description}</p>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Key Features:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {selectedSectionData.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm text-gray-700">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </ResponsiveCard>
      )}

      {/* Interactive Sidebar Demo */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Interactive Sidebar Demo</h3>
        <div className="flex items-center space-x-4 mb-6">
          <button
            onClick={toggleSidebar}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {isCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
            <span>{isCollapsed ? 'Expand' : 'Collapse'} Sidebar</span>
          </button>
          <span className="text-sm text-gray-600">
            Current state: {isCollapsed ? 'Collapsed' : 'Expanded'}
          </span>
        </div>
        
        <div className="bg-gray-100 p-4 rounded-lg">
          <div className="text-sm text-gray-600 mb-2">
            Sidebar Preview ({isCollapsed ? 'Collapsed' : 'Expanded'} mode):
          </div>
          <div className={`bg-white border border-gray-200 rounded-lg transition-all duration-300 ${
            isCollapsed ? 'w-16' : 'w-64'
          } h-96 p-4`}>
            <div className="flex items-center justify-between mb-4">
              <div className={`font-bold text-gray-900 ${isCollapsed ? 'hidden' : 'block'}`}>
                Institute Admin
              </div>
              <button className="p-1 hover:bg-gray-100 rounded">
                {isCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
              </button>
            </div>
            
            <div className="space-y-2">
              {navigationSections.slice(0, 6).map((section, index) => (
                <div
                  key={section.id}
                  className={`p-2 rounded text-sm transition-colors ${
                    index === 0 ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'
                  } ${isCollapsed ? 'flex justify-center' : ''}`}
                >
                  {isCollapsed ? (
                    <section.icon className="w-4 h-4" />
                  ) : (
                    <div className="flex items-center space-x-2">
                      <section.icon className="w-4 h-4" />
                      <span>{section.label}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </ResponsiveCard>

      {/* Layout Features */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Layout Features</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={1} desktopColumns={3} gap={6}>
          {layoutFeatures.map((feature, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3 mb-3">
                <feature.icon className="w-6 h-6 text-blue-600" />
                <h4 className="font-medium text-gray-900">{feature.title}</h4>
              </div>
              <p className="text-sm text-gray-600 mb-3">{feature.description}</p>
              <ul className="space-y-1">
                {feature.benefits.map((benefit, benefitIndex) => (
                  <li key={benefitIndex} className="text-xs text-gray-500 flex items-center">
                    <div className="w-1 h-1 bg-blue-600 rounded-full mr-2"></div>
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Implementation Notes */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Implementation Highlights</h3>
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Institute-Specific Features</h4>
            <p className="text-sm text-blue-800">
              The layout includes specialized sections for educational institutes including course management, 
              live classes, student tracking, and marketplace integration for course sales.
            </p>
          </div>
          
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Comprehensive Management</h4>
            <p className="text-sm text-green-800">
              All aspects of institute operations are covered: academic (courses, students, instructors), 
              operational (live classes, exams), and business (billing, marketplace).
            </p>
          </div>
          
          <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 className="font-medium text-purple-900 mb-2">Scalable Architecture</h4>
            <p className="text-sm text-purple-800">
              The layout is designed to scale with institute growth, supporting multiple courses, 
              hundreds of students, and complex organizational structures.
            </p>
          </div>
        </div>
      </ResponsiveCard>
    </div>
  )
}

export default InstituteAdminLayoutDemo
