'use client'

import { useEffect, ReactNode } from 'react'
import { useSidebarStore, UserType } from '@/stores/sidebar/useSidebarStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { Sidebar } from './Sidebar'
import { Header } from './Header'
import { MobileSidebarOverlay } from './MobileSidebarOverlay'

interface AppLayoutProps {
  children: ReactNode
  userType: UserType
}

export function AppLayout({ children, userType }: AppLayoutProps) {
  const { 
    isCollapsed, 
    isMobileOpen, 
    setUserType, 
    initializeNavigation,
    setMobileSidebarOpen 
  } = useSidebarStore()
  
  const { user } = useAuthStore()

  // Initialize navigation based on user type
  useEffect(() => {
    setUserType(userType)
    initializeNavigation(userType)
  }, [userType, setUserType, initializeNavigation])

  // Close mobile sidebar when clicking outside
  const handleOverlayClick = () => {
    setMobileSidebarOpen(false)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Sidebar Overlay */}
      <MobileSidebarOverlay 
        isOpen={isMobileOpen} 
        onClose={handleOverlayClick}
      />

      {/* Sidebar */}
      <Sidebar userType={userType} />

      {/* Main Content Area */}
      <div 
        className={`transition-all duration-300 ease-in-out ${
          isCollapsed 
            ? 'lg:ml-16' 
            : 'lg:ml-64'
        }`}
      >
        {/* Header */}
        <Header userType={userType} />

        {/* Page Content */}
        <main className="p-4 lg:p-6">
          {children}
        </main>
      </div>
    </div>
  )
}

export default AppLayout
