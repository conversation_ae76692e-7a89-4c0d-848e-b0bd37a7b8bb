'use client'

import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'

interface BreadcrumbItem {
  label: string
  href?: string
  isActive?: boolean
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[]
}

export function Breadcrumbs({ items }: BreadcrumbsProps) {
  if (!items || items.length === 0) return null

  return (
    <nav className="flex items-center space-x-1 text-sm">
      {items.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && (
            <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
          )}
          
          {item.isActive ? (
            <span className="text-gray-900 font-medium">
              {item.label}
            </span>
          ) : (
            <Link
              href={item.href || '#'}
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              {index === 0 && (
                <Home className="w-4 h-4 inline mr-1" />
              )}
              {item.label}
            </Link>
          )}
        </div>
      ))}
    </nav>
  )
}

export default Breadcrumbs
