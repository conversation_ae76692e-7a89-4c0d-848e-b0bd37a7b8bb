'use client'

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { useSidebarStore, UserType } from '@/stores/sidebar/useSidebarStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useResponsive } from '@/hooks/useResponsive'
import { 
  Menu, 
  Search, 
  Bell, 
  User, 
  Settings, 
  LogOut, 
  ChevronDown,
  ChevronRight,
  Home,
  Command,
  X,
  Sun,
  Moon,
  Globe,
  HelpCircle,
  Maximize,
  Minimize
} from 'lucide-react'
import { Breadcrumbs } from '@/components/shared/navigation/Breadcrumbs'
import { NavigationSearch, GlobalSearchModal } from '@/components/shared/navigation/NavigationSearch'
import { NotificationDropdown } from './NotificationDropdown'
import { ProfileDropdown } from './ProfileDropdown'

interface EnhancedHeaderProps {
  userType: UserType
  showBreadcrumbs?: boolean
  showSearch?: boolean
  showNotifications?: boolean
  showProfile?: boolean
  showThemeToggle?: boolean
  showFullscreenToggle?: boolean
  className?: string
}

export function EnhancedHeader({ 
  userType,
  showBreadcrumbs = true,
  showSearch = true,
  showNotifications = true,
  showProfile = true,
  showThemeToggle = false,
  showFullscreenToggle = false,
  className = ''
}: EnhancedHeaderProps) {
  const pathname = usePathname()
  const { 
    isCollapsed,
    isMobileOpen,
    toggleSidebar,
    setMobileSidebarOpen,
    navigationItems,
    unreadCount
  } = useSidebarStore()
  
  const { user, logout } = useAuthStore()
  const { isMobile, isTablet } = useResponsive()
  
  const [showNotificationDropdown, setShowNotificationDropdown] = useState(false)
  const [showProfileDropdown, setShowProfileDropdown] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [currentTime, setCurrentTime] = useState(new Date())

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    return () => clearInterval(timer)
  }, [])

  // Handle fullscreen toggle
  const handleFullscreenToggle = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  // Handle theme toggle
  const handleThemeToggle = () => {
    setIsDarkMode(!isDarkMode)
    // In a real app, this would update the theme context/store
    document.documentElement.classList.toggle('dark')
  }

  const handleMobileMenuToggle = () => {
    if (isMobile) {
      setMobileSidebarOpen(!isMobileOpen)
    } else {
      toggleSidebar()
    }
  }

  const handleLogout = async () => {
    try {
      await logout()
      setShowProfileDropdown(false)
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const getUserDisplayName = () => {
    if (user?.personalInfo?.fullName) {
      return user.personalInfo.fullName
    }
    return user?.email?.split('@')[0] || 'User'
  }

  const getUserRole = () => {
  const rawRole = user?.role ?? user?.legacyRole ?? 'User';
  const roleString = String(rawRole);
  return roleString.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
}


  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    })
  }

  return (
    <header className={`bg-white border-b border-gray-200 sticky top-0 z-30 ${className}`}>
      <div className="flex items-center justify-between px-4 lg:px-6 h-16">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* Mobile Menu Button */}
          <button
            onClick={handleMobileMenuToggle}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors lg:hidden"
            aria-label="Toggle navigation menu"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>

          {/* Desktop Sidebar Toggle */}
          {!isMobile && (
            <button
              onClick={toggleSidebar}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors hidden lg:block"
              aria-label="Toggle sidebar"
            >
              <Menu className="w-5 h-5 text-gray-600" />
            </button>
          )}

          {/* Breadcrumbs */}
          {showBreadcrumbs && (
            <div className="hidden md:block">
              <Breadcrumbs 
                maxItems={4}
                showHomeIcon={true}
                className="text-sm"
              />
            </div>
          )}

          {/* Current Time (Desktop only) */}
          {!isMobile && (
            <div className="hidden xl:flex items-center space-x-2 text-sm text-gray-500">
              <span>{formatTime(currentTime)}</span>
            </div>
          )}
        </div>

        {/* Center Section - Search */}
        {showSearch && (
          <>
            <div className="flex-1 max-w-md mx-4 hidden md:block">
              <NavigationSearch 
                placeholder="Search navigation..."
                showShortcut={true}
              />
            </div>

            {/* Mobile Search */}
            <div className="md:hidden">
              <GlobalSearchModal />
            </div>
          </>
        )}

        {/* Right Section */}
        <div className="flex items-center space-x-2">
          {/* Theme Toggle */}
          {showThemeToggle && !isMobile && (
            <button
              onClick={handleThemeToggle}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Toggle theme"
            >
              {isDarkMode ? (
                <Sun className="w-5 h-5 text-gray-600" />
              ) : (
                <Moon className="w-5 h-5 text-gray-600" />
              )}
            </button>
          )}

          {/* Fullscreen Toggle */}
          {showFullscreenToggle && !isMobile && (
            <button
              onClick={handleFullscreenToggle}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Toggle fullscreen"
            >
              {isFullscreen ? (
                <Minimize className="w-5 h-5 text-gray-600" />
              ) : (
                <Maximize className="w-5 h-5 text-gray-600" />
              )}
            </button>
          )}

          {/* Help Button */}
          {!isMobile && (
            <button
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Help"
            >
              <HelpCircle className="w-5 h-5 text-gray-600" />
            </button>
          )}

          {/* Notifications */}
          {showNotifications && (
            <div className="relative">
              <button
                onClick={() => setShowNotificationDropdown(!showNotificationDropdown)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors relative"
                aria-label="Notifications"
              >
                <Bell className="w-5 h-5 text-gray-600" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </span>
                )}
              </button>

              {showNotificationDropdown && (
                <NotificationDropdown 
                  onClose={() => setShowNotificationDropdown(false)}
                />
              )}
            </div>
          )}

          {/* Profile Dropdown */}
          {showProfile && (
            <div className="relative">
              <button
                onClick={() => setShowProfileDropdown(!showProfileDropdown)}
                className="flex items-center space-x-2 p-2 hover:bg-gray-100 rounded-lg transition-colors"
                aria-label="User profile"
              >
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  {user?.personalInfo?.avatar ? (
                    <img 
                      src={user.personalInfo.avatar} 
                      alt={getUserDisplayName()}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-4 h-4 text-white" />
                  )}
                </div>
                {!isMobile && (
                  <>
                    <div className="text-left hidden lg:block">
                      <div className="text-sm font-medium text-gray-900 truncate max-w-32">
                        {getUserDisplayName()}
                      </div>
                      <div className="text-xs text-gray-500">
                        {getUserRole()}
                      </div>
                    </div>
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  </>
                )}
              </button>

              {showProfileDropdown && (
                <ProfileDropdown 
                  user={user}
                  onLogout={handleLogout}
                  onClose={() => setShowProfileDropdown(false)}
                />
              )}
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close dropdowns */}
      {(showProfileDropdown || showNotificationDropdown) && (
        <div 
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowProfileDropdown(false)
            setShowNotificationDropdown(false)
          }}
        />
      )}
    </header>
  )
}

export default EnhancedHeader
