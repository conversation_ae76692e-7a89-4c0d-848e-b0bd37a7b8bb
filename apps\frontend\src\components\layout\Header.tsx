'use client'

import { useState } from 'react'
import { usePathname } from 'next/navigation'
import { useSidebarStore, UserType } from '@/stores/sidebar/useSidebarStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import {
  Menu,
  Search,
  Bell,
  ChevronDown,
  ChevronRight,
  User,
  Settings,
  LogOut,
  HelpCircle
} from 'lucide-react'
import { useResponsive } from '@/hooks/useResponsive'
import { Breadcrumbs } from '@/components/shared/navigation/Breadcrumbs'
import { NavigationSearch, GlobalSearchModal } from '@/components/shared/navigation/NavigationSearch'
import { NotificationDropdown } from './NotificationDropdown'
import { ProfileDropdown } from './ProfileDropdown'
import { BranchSelectorCompact } from '@/components/institute/BranchSelectorCompact'

interface HeaderProps {
  userType: UserType
}

export function Header({ userType }: HeaderProps) {
  const pathname = usePathname()
  const {
    isCollapsed,
    isMobileOpen,
    toggleSidebar,
    setMobileSidebarOpen,
    navigationItems,
    notifications,
    unreadCount
  } = useSidebarStore()

  const { user, logout } = useAuthStore()
  const { isMobile } = useResponsive()
  const [showNotifications, setShowNotifications] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // Generate breadcrumbs from current path
  const generateBreadcrumbs = () => {
    const pathSegments = pathname.split('/').filter(Boolean)
    const breadcrumbs = []

    // Add home/dashboard
    const dashboardPath = `/${pathSegments[0] || ''}`
    const dashboardItem = navigationItems.find(item => item.href === dashboardPath)
    if (dashboardItem) {
      breadcrumbs.push({
        label: dashboardItem.label,
        href: dashboardItem.href,
        isActive: pathname === dashboardItem.href
      })
    }

    // Add subsequent segments
    for (let i = 1; i < pathSegments.length; i++) {
      const segmentPath = `/${pathSegments.slice(0, i + 1).join('/')}`
      const segmentItem = navigationItems.find(item => item.href === segmentPath)
      
      if (segmentItem) {
        breadcrumbs.push({
          label: segmentItem.label,
          href: segmentItem.href,
          isActive: pathname === segmentItem.href
        })
      } else {
        // Fallback for dynamic segments
        const label = pathSegments[i].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        breadcrumbs.push({
          label,
          href: segmentPath,
          isActive: i === pathSegments.length - 1
        })
      }
    }

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Implement search functionality
    console.log('Search query:', searchQuery)
  }

  const handleLogout = async () => {
    try {
      await logout()
      setShowProfile(false)
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const handleMobileMenuToggle = () => {
    if (isMobile) {
      setMobileSidebarOpen(!isMobileOpen)
    } else {
      toggleSidebar()
    }
  }



  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-30">
      <div className="flex items-center justify-between px-4 lg:px-6 h-16">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* Mobile Menu Button */}
          <button
            onClick={handleMobileMenuToggle}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors lg:hidden"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>

          {/* Breadcrumbs */}
          <div className="hidden md:block">
            <Breadcrumbs
              maxItems={4}
              showHomeIcon={true}
              className="text-sm"
            />
          </div>
        </div>

        {/* Center Section - Search */}
        <div className="flex-1 max-w-md mx-4 hidden md:block">
          <NavigationSearch
            placeholder="Search navigation..."
            showShortcut={true}
          />
        </div>

        {/* Mobile Search */}
        <div className="md:hidden">
          <GlobalSearchModal />
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2">
          {/* Branch Selector for Institute Admin */}
          {userType === 'institute_admin' && (
            <BranchSelectorCompact className="hidden lg:flex" />
          )}

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <Bell className="w-5 h-5 text-gray-600" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </button>

            {/* Notification Dropdown */}
            {showNotifications && (
              <NotificationDropdown
                notifications={notifications}
                onClose={() => setShowNotifications(false)}
              />
            )}
          </div>

          {/* Profile Dropdown */}
          <div className="relative">
            <button
              onClick={() => setShowProfile(!showProfile)}
              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                {user?.personalInfo?.avatar ? (
                  <img 
                    src={user.personalInfo.avatar} 
                    alt={user.personalInfo.fullName || user.email}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <User className="w-4 h-4 text-white" />
                )}
              </div>
              
              <div className="hidden md:block text-left">
                <div className="text-sm font-medium text-gray-900">
                  {user?.personalInfo?.fullName || user?.email || 'User'}
                </div>
                <div className="text-xs text-gray-500 capitalize">
                  {user?.role?.name || 'Admin'}
                </div>
              </div>
              
              <ChevronDown className="w-4 h-4 text-gray-400" />
            </button>

            {/* Profile Dropdown Menu */}
            {showProfile && (
              <ProfileDropdown
                user={user}
                userType={userType}
                onLogout={handleLogout}
                onClose={() => setShowProfile(false)}
              />
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
