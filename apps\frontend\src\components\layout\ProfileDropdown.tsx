'use client'

import { useEffect, useRef } from 'react'
import Link from 'next/link'
import { UserType } from '@/stores/sidebar/useSidebarStore'
import {
  User,
  Settings,
  HelpCircle,
  LogOut,
  CreditCard,
  Shield,
  Bell
} from 'lucide-react'
import { ProfileSettingsModal } from '@/components/modals/ProfileSettingsModal'
import { useProfileModal } from '@/hooks/useProfileModal'

interface ProfileDropdownProps {
  user: any // User type from auth store
  userType: UserType
  onLogout: () => void
  onClose: () => void
}

export function ProfileDropdown({ user, userType, onLogout, onClose }: ProfileDropdownProps) {
  const dropdownRef = useRef<HTMLDivElement>(null)
  const { isOpen, openModal, closeModal } = useProfileModal()

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [onClose])

  const getProfileLinks = () => {
    // Get the correct path prefix based on user type
    const getPathPrefix = () => {
      if (userType === 'super_admin') return 'super-admin'
      if (userType === 'institute_admin') return 'admin'
      if (userType === 'student') return 'student'
      return 'admin' // fallback
    }

    const pathPrefix = getPathPrefix()

    const baseLinks = [
      {
        icon: User,
        label: 'Profile',
        href: `/${pathPrefix}/profile`,
        description: 'Manage your profile information',
        isModal: true // Flag to indicate this should open a modal
      },

    ]

    // Add user-type specific links
    if (userType === 'super_admin') {
      baseLinks.push(
        {
          icon: Settings,
          label: 'Settings',
          href: `/${pathPrefix}/settings`,
          description: 'Account and preferences',
          isModal: false
        },
        {
          icon: Shield,
          label: 'System Settings',
          href: `/${pathPrefix}/settings/platform`,
          description: 'Platform configuration',
          isModal: false
        }
      )
    } else if (userType === 'institute_admin') {
      baseLinks.push(
        {
          icon: Settings,
          label: 'Settings',
          href: `/admin/settings`,
          description: 'Account and preferences',
          isModal: false
        },
        {
          icon: CreditCard,
          label: 'Billing',
          href: '/admin/billing',
          description: 'Manage billing and payments',
          isModal: false
        }
      )
    } else if (userType === 'student') {
      baseLinks.push(
        {
          icon: CreditCard,
          label: 'Payments',
          href: `/${pathPrefix}/payments`,
          description: 'Payment history and methods',
          isModal: false
        },
        {
          icon: Bell,
          label: 'Notifications',
          href: `/${pathPrefix}/notifications`,
          description: 'Notification preferences',
          isModal: false
        }
      )
    }

    baseLinks.push({
      icon: HelpCircle,
      label: 'Help & Support',
      href: `/${pathPrefix}/support`,
      description: 'Get help and contact support',
      isModal: false
    })

    return baseLinks
  }

  const profileLinks = getProfileLinks()

  const handleLinkClick = (link: any) => {
    if (link.isModal) {
      openModal()
    }
    onClose()
  }

  const handleLogoutClick = () => {
    onLogout()
    onClose()
  }

  return (
    <div 
      ref={dropdownRef}
      className="absolute right-0 top-full mt-2 w-72 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
    >
      {/* User Info Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
            {user?.personalInfo?.avatar ? (
              <img 
                src={user.personalInfo.avatar} 
                alt={user.personalInfo.fullName || user.email}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <User className="w-6 h-6 text-white" />
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-gray-900 truncate">
              {user?.personalInfo?.fullName || user?.email || 'User'}
            </div>
            <div className="text-sm text-gray-500 truncate">
              {user?.personalInfo?.email || user?.email}
            </div>
            <div className="text-xs text-gray-400 capitalize mt-1">
              {user?.role?.name || userType.replace('_', ' ')}
            </div>
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <div className="py-1">
        {profileLinks.map((link, index) => (
          link.isModal ? (
            <button
              key={index}
              onClick={() => handleLinkClick(link)}
              className="w-full flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors text-left"
            >
              <link.icon className="w-4 h-4 mr-3 text-gray-400" />
              <div className="flex-1">
                <div className="font-medium">{link.label}</div>
                <div className="text-xs text-gray-500">{link.description}</div>
              </div>
            </button>
          ) : (
            <Link
              key={index}
              href={link.href}
              onClick={() => handleLinkClick(link)}
              className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <link.icon className="w-4 h-4 mr-3 text-gray-400" />
              <div className="flex-1">
                <div className="font-medium">{link.label}</div>
                <div className="text-xs text-gray-500">{link.description}</div>
              </div>
            </Link>
          )
        ))}
      </div>

      {/* Logout */}
      <div className="border-t border-gray-200">
        <button
          onClick={handleLogoutClick}
          className="flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors"
        >
          <LogOut className="w-4 h-4 mr-3" />
          <div className="flex-1 text-left">
            <div className="font-medium">Sign Out</div>
            <div className="text-xs text-red-500">Sign out of your account</div>
          </div>
        </button>
      </div>

      {/* Profile Settings Modal */}
      <ProfileSettingsModal
        isOpen={isOpen}
        onClose={closeModal}
      />
    </div>
  )
}

export default ProfileDropdown
