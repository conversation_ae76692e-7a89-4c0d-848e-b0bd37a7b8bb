'use client'

import { ReactNode } from 'react'
import { SettingsSidebar } from './SettingsSidebar'

interface SettingsLayoutProps {
  children: ReactNode
}

export function SettingsLayout({ children }: SettingsLayoutProps) {
  return (
    <div className="flex h-screen bg-background">
      {/* Settings Sidebar */}
      <SettingsSidebar className="flex-shrink-0" />

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0 border-l">
        {/* Content Area */}
        <main className="flex-1 overflow-y-auto bg-background">
          <div className="h-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

export default SettingsLayout
