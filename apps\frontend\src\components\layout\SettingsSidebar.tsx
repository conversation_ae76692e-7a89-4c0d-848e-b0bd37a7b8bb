'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  Settings,
  Globe,
  Shield,
  Mail,
  Database,
  ChevronLeft,
  Search,
  Filter
} from 'lucide-react'

// Settings navigation items
const settingsNavigation = [
  {
    title: 'Platform',
    items: [
      {
        title: 'Platform Settings',
        href: '/super-admin/settings/platform',
        icon: Settings,
        description: 'Core platform configuration',
        badge: null
      },
      {
        title: 'Security Settings',
        href: '/super-admin/settings/security',
        icon: Shield,
        description: 'Authentication and security',
        badge: null
      }
    ]
  },
  {
    title: 'Communication',
    items: [
      {
        title: 'Email & SMTP',
        href: '/super-admin/settings/email',
        icon: Mail,
        description: 'Email configuration',
        badge: null
      }
    ]
  },
  {
    title: 'Infrastructure',
    items: [
      {
        title: 'Storage & CDN',
        href: '/super-admin/settings/storage',
        icon: Database,
        description: 'File storage settings',
        badge: null
      }
    ]
  }
]

interface SettingsSidebarProps {
  className?: string
}

export function SettingsSidebar({ className }: SettingsSidebarProps) {
  const pathname = usePathname()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  // Get all categories
  const categories = ['all', ...settingsNavigation.map(section => section.title.toLowerCase())]

  // Filter navigation items based on search and category
  const filteredNavigation = settingsNavigation
    .map(section => ({
      ...section,
      items: section.items.filter(item => {
        const matchesSearch = searchQuery === '' || 
          item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().includes(searchQuery.toLowerCase())
        
        const matchesCategory = selectedCategory === 'all' || 
          section.title.toLowerCase() === selectedCategory
        
        return matchesSearch && matchesCategory
      })
    }))
    .filter(section => section.items.length > 0)

  return (
    <div className={cn("flex h-full w-80 flex-col border-r bg-muted/30", className)}>
      {/* Header */}
      <div className="flex h-16 items-center px-6 border-b bg-background">
        <Link
          href="/super-admin"
          className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          <ChevronLeft className="h-4 w-4" />
          <span>Back to Dashboard</span>
        </Link>
      </div>

      {/* Settings Title */}
      <div className="px-6 py-6 bg-background">
        <h1 className="text-xl font-semibold">Settings</h1>
        <p className="text-sm text-muted-foreground mt-1">
          Configure platform settings
        </p>
      </div>

      {/* Search and Filter */}
      <div className="px-6 pb-4 space-y-3 bg-background">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search settings..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
          />
        </div>

        {/* Category Filter */}
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="flex-1 text-sm border rounded-md px-3 py-1.5 bg-background focus:outline-none focus:ring-2 focus:ring-ring"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>
        </div>
      </div>

      <Separator />
     

      {/* Navigation */}
      <ScrollArea className="flex-1 px-6">
        <div className="space-y-6 py-6">
          {filteredNavigation.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">No settings found</p>
              <p className="text-xs text-muted-foreground mt-1">
                Try adjusting your search or filter
              </p>
            </div>
          ) : (
            filteredNavigation.map((section) => (
              <div key={section.title}>
                <h3 className="mb-3 text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  {section.title}
                </h3>
                <div className="space-y-2">
                  {section.items.map((item) => {
                    const isActive = pathname === item.href
                    const Icon = item.icon

                    return (
                      <Link key={item.href} href={item.href}>
                        <Button
                          variant={isActive ? "secondary" : "ghost"}
                          className={cn(
                            "w-full justify-start h-auto p-3 text-left",
                            isActive && "bg-secondary"
                          )}
                        >
                          <div className="flex items-start space-x-3 w-full">
                            <Icon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <p className="text-sm font-medium truncate">
                                  {item.title}
                                </p>
                                {item.badge && (
                                  <Badge variant="secondary" className="ml-2 text-xs">
                                    {item.badge}
                                  </Badge>
                                )}
                              </div>
                              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </Button>
                      </Link>
                    )
                  })}
                </div>
              </div>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="border-t p-6 bg-background">
        <div className="text-xs text-muted-foreground">
          <p className="font-medium">Settings Management</p>
          <p className="mt-1">Configure platform-wide settings</p>
        </div>
      </div>
    </div>
  )
}

export default SettingsSidebar
