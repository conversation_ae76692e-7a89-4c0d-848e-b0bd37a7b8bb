/* Sidebar Scrolling Styles */
.sidebarScrollContainer {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

/* Webkit Scrollbar Styles */
.sidebarScrollContainer::-webkit-scrollbar {
  width: 6px;
}

.sidebarScrollContainer::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.sidebarScrollContainer::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.sidebarScrollContainer::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sidebarScrollContainer::-webkit-scrollbar {
    width: 4px;
  }
}

/* Ensure proper height calculation */
.sidebarContainer {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 40;
  height: 100vh;
  background: white;
  border-right: 1px solid #e5e7eb;
  transition: all 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
}

.sidebarHeader {
  flex-shrink: 0;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.sidebarSearch {
  flex-shrink: 0;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.sidebarFooter {
  flex-shrink: 0;
  border-top: 1px solid #e5e7eb;
}

/* Force scroll when content overflows */
.navigationContent {
  padding: 1rem 0;
  min-height: min-content;
}
