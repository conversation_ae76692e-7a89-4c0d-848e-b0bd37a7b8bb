'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSidebarStore, UserType } from '@/stores/sidebar/useSidebarStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useSidebarNavigation } from '@/hooks/useSidebarNavigation'
import {
  ChevronLeft,
  ChevronRight,
  Search,
  Star,
  Clock,
  LogOut,
  User,
  Settings,
  Menu,
  X,
  Home,
  Bookmark,
  History
} from 'lucide-react'
import * as Icons from 'lucide-react'
import { SidebarItem } from './SidebarItem'
import { UserProfile } from './UserProfile'
import { SidebarSearch } from './SidebarSearch'
import { isNavigationItemActive } from '@/utils/navigationUtils'
import { ProfileSettingsModal } from '@/components/modals/ProfileSettingsModal'
import { useProfileModal } from '@/hooks/useProfileModal'
import styles from './Sidebar.module.css'

interface SidebarProps {
  userType: UserType
}

export function Sidebar({ userType }: SidebarProps) {
  const pathname = usePathname()
  const {
    isCollapsed,
    isMobileOpen,
    favoriteItems,
    recentItems,
    toggleSidebar,
    setActiveItem,
    setMobileSidebarOpen
  } = useSidebarStore()

  const { user, logout } = useAuthStore()
  const { navigationItems, navigationStats } = useSidebarNavigation()
  const [showSearch, setShowSearch] = useState(false)
  const { isOpen, openModal, closeModal } = useProfileModal()

  // Safeguard: Ensure navigationItems is always an array
  const safeNavigationItems = Array.isArray(navigationItems) ? navigationItems : []

  // Show loading state if navigation items are not loaded yet
  const isNavigationLoading = safeNavigationItems.length === 0

  // Get favorite navigation items
  const favoriteNavItems = safeNavigationItems.filter(item =>
    item && favoriteItems.includes(item.id)
  )

  // Get recent navigation items
  const recentNavItems = safeNavigationItems.filter(item =>
    item && recentItems.includes(item.id)
  ).slice(0, 5)

  const handleItemClick = (itemId: string) => {
    // Find the clicked item
    const clickedItem = safeNavigationItems.find(item => item.id === itemId)

    // Handle modal items
    if (clickedItem?.isModal) {
      if (itemId === 'profile') {
        openModal()
      }
      // Close mobile sidebar when modal item is clicked
      if (isMobileOpen) {
        setMobileSidebarOpen(false)
      }
      return
    }

    setActiveItem(itemId)
    // Close mobile sidebar when item is clicked
    if (isMobileOpen) {
      setMobileSidebarOpen(false)
    }
  }

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  return (
    <>
      {/* Desktop Sidebar */}
      <div
        className={`${styles.sidebarContainer} sidebar-container ${
          isCollapsed ? 'w-16' : 'w-64'
        } hidden lg:block`}
      >
        {/* Sidebar Header */}
        <div className={`${styles.sidebarHeader} sidebar-fixed-section flex items-center justify-between`}>
          {!isCollapsed && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">LMS</span>
              </div>
              <span className="font-semibold text-gray-900">Groups Exam</span>
            </div>
          )}

          <button
            onClick={toggleSidebar}
            className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {isCollapsed ? (
              <ChevronRight className="w-4 h-4 text-gray-600" />
            ) : (
              <ChevronLeft className="w-4 h-4 text-gray-600" />
            )}
          </button>
        </div>

        {/* Search */}
        {!isCollapsed && (
          <div className={`${styles.sidebarSearch} sidebar-fixed-section`}>
            <SidebarSearch />
          </div>
        )}

        {/* Navigation - Scrollable Area */}
        <div className={`${styles.sidebarScrollContainer} sidebar-scroll-area`}>
          <div className={styles.navigationContent}>
            {/* Main Navigation */}
            <nav className="px-2 space-y-1">
              {safeNavigationItems.map((item) => (
                <SidebarItem
                  key={item.id}
                  item={item}
                  isActive={isNavigationItemActive(item, pathname)}
                  isCollapsed={isCollapsed}
                  onClick={() => handleItemClick(item.id)}
                />
              ))}
            </nav>

            {/* Favorites Section */}
            {!isCollapsed && favoriteNavItems.length > 0 && (
              <div className="mt-6 px-2">
                <div className="flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <Star className="w-3 h-3 mr-2" />
                  Favorites
                </div>
                <div className="space-y-1">
                  {favoriteNavItems.map((item) => (
                    <SidebarItem
                      key={`fav-${item.id}`}
                      item={item}
                      isActive={pathname === item.href}
                      isCollapsed={false}
                      onClick={() => handleItemClick(item.id)}
                      variant="compact"
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Recent Section */}
            {!isCollapsed && recentNavItems.length > 0 && (
              <div className="mt-6 px-2">
                <div className="flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <Clock className="w-3 h-3 mr-2" />
                  Recent
                </div>
                <div className="space-y-1">
                  {recentNavItems.map((item) => (
                    <SidebarItem
                      key={`recent-${item.id}`}
                      item={item}
                      isActive={pathname === item.href}
                      isCollapsed={false}
                      onClick={() => handleItemClick(item.id)}
                      variant="compact"
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Bottom Padding for Better Scrolling */}
            <div className="h-4"></div>
          </div>
        </div>

        {/* User Profile Section */}
        <div className={`${styles.sidebarFooter} sidebar-fixed-section`}>
          <UserProfile
            user={user}
            isCollapsed={isCollapsed}
            onLogout={handleLogout}
          />
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div
        className={`${styles.sidebarContainer} w-64 transform transition-transform duration-300 ease-in-out lg:hidden ${
          isMobileOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        style={{ zIndex: 50 }}
      >
        {/* Mobile Sidebar Header */}
        <div className={`${styles.sidebarHeader} flex items-center justify-between`}>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">LMS</span>
            </div>
            <span className="font-semibold text-gray-900">Groups Exam</span>
          </div>

          <button
            onClick={() => setMobileSidebarOpen(false)}
            className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <ChevronLeft className="w-4 h-4 text-gray-600" />
          </button>
        </div>

        {/* Mobile Search */}
        <div className={styles.sidebarSearch}>
          <SidebarSearch />
        </div>

        {/* Mobile Navigation - Scrollable Area */}
        <div className={styles.sidebarScrollContainer}>
          <div className={styles.navigationContent}>
            <nav className="px-2 space-y-1">
              {safeNavigationItems.map((item) => (
                <SidebarItem
                  key={item.id}
                  item={item}
                  isActive={isNavigationItemActive(item, pathname)}
                  isCollapsed={false}
                  onClick={() => handleItemClick(item.id)}
                />
              ))}
            </nav>

            {/* Mobile Favorites */}
            {favoriteNavItems.length > 0 && (
              <div className="mt-6 px-2">
                <div className="flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <Star className="w-3 h-3 mr-2" />
                  Favorites
                </div>
                <div className="space-y-1">
                  {favoriteNavItems.map((item) => (
                    <SidebarItem
                      key={`mobile-fav-${item.id}`}
                      item={item}
                      isActive={pathname === item.href}
                      isCollapsed={false}
                      onClick={() => handleItemClick(item.id)}
                      variant="compact"
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Bottom Padding for Better Mobile Scrolling */}
            <div className="h-4"></div>
          </div>
        </div>

        {/* Mobile User Profile */}
        <div className={styles.sidebarFooter}>
          <UserProfile
            user={user}
            isCollapsed={false}
            onLogout={handleLogout}
          />
        </div>
      </div>

      {/* Profile Settings Modal */}
      <ProfileSettingsModal
        isOpen={isOpen}
        onClose={closeModal}
      />
    </>
  )
}

export default Sidebar
