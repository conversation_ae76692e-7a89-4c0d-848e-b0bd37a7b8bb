'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { useSidebarStore, NavigationItem } from '@/stores/sidebar/useSidebarStore'
import { isNavigationItemActive, shouldExpandNavigationItem } from '@/utils/navigationUtils'
import {
  Star,
  StarOff,
  ChevronRight,
  ChevronDown
} from 'lucide-react'
import { LucideIcon } from 'lucide-react'
import * as Icons from 'lucide-react'

interface SidebarItemProps {
  item: NavigationItem
  isActive: boolean
  isCollapsed: boolean
  onClick: () => void
  variant?: 'default' | 'compact'
}

export function SidebarItem({
  item,
  isActive,
  isCollapsed,
  onClick,
  variant = 'default'
}: SidebarItemProps) {
  const {
    favoriteItems,
    addToFavorites,
    removeFromFavorites
  } = useSidebarStore()

  const pathname = usePathname()
  const [isExpanded, setIsExpanded] = useState(false)
  const isFavorite = favoriteItems.includes(item.id)
  const hasChildren = item.children && item.children.length > 0

  // Auto-expand if this item or any of its children are active
  useEffect(() => {
    if (shouldExpandNavigationItem(item, pathname)) {
      setIsExpanded(true)
    }
  }, [item, pathname])

  // Get the icon component
  const IconComponent = (Icons as any)[item.icon] as LucideIcon
  
  const handleFavoriteToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (isFavorite) {
      removeFromFavorites(item.id)
    } else {
      addToFavorites(item.id)
    }
  }

  const handleExpandToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsExpanded(!isExpanded)
  }

  const handleItemClick = () => {
    onClick()
    if (hasChildren) {
      setIsExpanded(!isExpanded)
    }
  }

  const baseClasses = `
    group relative flex items-center w-full text-left transition-all duration-200
    ${variant === 'compact' ? 'px-3 py-1.5' : 'px-3 py-2'}
    ${isActive 
      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' 
      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
    }
    rounded-lg mx-1
  `

  const content = (
    <>
      {/* Icon */}
      <div className={`flex-shrink-0 ${isCollapsed ? 'mx-auto' : 'mr-3'}`}>
        {IconComponent && (
          <IconComponent 
            className={`w-5 h-5 ${
              isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'
            }`} 
          />
        )}
      </div>

      {/* Label and Badge */}
      {!isCollapsed && (
        <>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <span className={`text-sm font-medium truncate ${
                variant === 'compact' ? 'text-xs' : ''
              }`}>
                {item.label}
              </span>
              
              {/* Badge */}
              {item.badge && item.badge > 0 && (
                <span className="ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
                  {item.badge > 99 ? '99+' : item.badge}
                </span>
              )}
            </div>
            
            {/* Description for non-compact variant */}
            {variant !== 'compact' && item.description && (
              <p className="text-xs text-gray-500 mt-0.5 truncate">
                {item.description}
              </p>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-1 ml-2">
            {/* Favorite Toggle */}
            <div
              onClick={handleFavoriteToggle}
              className="p-1 rounded opacity-0 group-hover:opacity-100 hover:bg-gray-200 transition-all cursor-pointer"
              title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  handleFavoriteToggle(e)
                }
              }}
            >
              {isFavorite ? (
                <Star className="w-3 h-3 text-yellow-500 fill-current" />
              ) : (
                <StarOff className="w-3 h-3 text-gray-400" />
              )}
            </div>

            {/* Expand Toggle for items with children */}
            {hasChildren && (
              <div
                onClick={handleExpandToggle}
                className="p-1 rounded hover:bg-gray-200 transition-colors cursor-pointer"
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    handleExpandToggle(e)
                  }
                }}
              >
                {isExpanded ? (
                  <ChevronDown className="w-3 h-3 text-gray-400" />
                ) : (
                  <ChevronRight className="w-3 h-3 text-gray-400" />
                )}
              </div>
            )}
          </div>
        </>
      )}

      {/* Tooltip for collapsed sidebar */}
      {isCollapsed && (
        <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
          {item.label}
          {item.badge && item.badge > 0 && (
            <span className="ml-1 px-1.5 py-0.5 text-xs bg-red-500 rounded-full">
              {item.badge > 99 ? '99+' : item.badge}
            </span>
          )}
        </div>
      )}
    </>
  )

  return (
    <div>
      {/* Main Item */}
      {hasChildren || item.isModal ? (
        <button
          onClick={handleItemClick}
          className={baseClasses}
        >
          {content}
        </button>
      ) : (
        <Link
          href={item.href}
          onClick={handleItemClick}
          className={baseClasses}
        >
          {content}
        </Link>
      )}

      {/* Children Items */}
      {hasChildren && isExpanded && !isCollapsed && (
        <div className="ml-6 mt-1 space-y-1">
          {item.children?.map((child) => (
            <SidebarItem
              key={child.id}
              item={child}
              isActive={isNavigationItemActive(child, pathname)}
              isCollapsed={false}
              onClick={() => onClick()}
              variant="compact"
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default SidebarItem
