'use client'

import React from 'react'
import styles from './Sidebar.module.css'

export function SidebarScrollTest() {
  // Generate test navigation items to test scrolling
  const testItems = Array.from({ length: 50 }, (_, i) => ({
    id: `test-item-${i}`,
    label: `Test Navigation Item ${i + 1}`,
    icon: 'Settings',
    href: `/test/${i}`,
    description: `This is test item ${i + 1} for scrolling verification`
  }))

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Sidebar Scroll Test</h2>
      
      {/* Test Sidebar Structure */}
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        <div 
          className={`${styles.sidebarContainer} sidebar-container w-64 relative`}
          style={{ height: '400px', position: 'relative' }}
        >
          {/* Fixed Header */}
          <div className={`${styles.sidebarHeader} sidebar-fixed-section flex items-center justify-between bg-blue-600 text-white`}>
            <span className="font-semibold">Test Sidebar</span>
            <button className="p-1 hover:bg-blue-700 rounded">
              ⚙️
            </button>
          </div>

          {/* Fixed Search */}
          <div className={`${styles.sidebarSearch} sidebar-fixed-section bg-gray-50 border-b`}>
            <input 
              type="text" 
              placeholder="Search..." 
              className="w-full p-2 text-sm border rounded"
            />
          </div>

          {/* Scrollable Navigation */}
          <div className={`${styles.sidebarScrollContainer} sidebar-scroll-area bg-white`}>
            <div className={styles.navigationContent}>
              <nav className="px-2 space-y-1">
                {testItems.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center p-2 text-sm text-gray-700 hover:bg-gray-100 rounded cursor-pointer"
                  >
                    <span className="mr-3">📄</span>
                    <div>
                      <div className="font-medium">{item.label}</div>
                      <div className="text-xs text-gray-500">{item.description}</div>
                    </div>
                  </div>
                ))}
              </nav>
              
              {/* Test Favorites */}
              <div className="mt-6 px-2">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
                  ⭐ Favorites
                </div>
                {testItems.slice(0, 5).map((item) => (
                  <div
                    key={`fav-${item.id}`}
                    className="flex items-center p-1 text-sm text-gray-600 hover:bg-gray-50 rounded cursor-pointer"
                  >
                    <span className="mr-2">⭐</span>
                    <span>{item.label}</span>
                  </div>
                ))}
              </div>

              {/* Test Recent */}
              <div className="mt-6 px-2">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
                  🕒 Recent
                </div>
                {testItems.slice(0, 3).map((item) => (
                  <div
                    key={`recent-${item.id}`}
                    className="flex items-center p-1 text-sm text-gray-600 hover:bg-gray-50 rounded cursor-pointer"
                  >
                    <span className="mr-2">🕒</span>
                    <span>{item.label}</span>
                  </div>
                ))}
              </div>

              <div className="h-4"></div>
            </div>
          </div>

          {/* Fixed Footer */}
          <div className={`${styles.sidebarFooter} sidebar-fixed-section bg-gray-100 border-t`}>
            <div className="p-3 flex items-center">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm mr-3">
                U
              </div>
              <div>
                <div className="text-sm font-medium">Test User</div>
                <div className="text-xs text-gray-500"><EMAIL></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-600">
        <p><strong>Test Instructions:</strong></p>
        <ul className="list-disc list-inside mt-2 space-y-1">
          <li>The header (blue), search bar, and footer should stay fixed</li>
          <li>The middle section with navigation items should scroll</li>
          <li>You should see 50 test items, favorites, and recent sections</li>
          <li>Scrollbar should be visible and functional</li>
          <li>Smooth scrolling should work when using scroll wheel</li>
        </ul>
      </div>
    </div>
  )
}

export default SidebarScrollTest
