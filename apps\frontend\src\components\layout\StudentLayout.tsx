'use client'

import { ReactNode, useEffect } from 'react'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useResponsive } from '@/hooks/useResponsive'
import { Sidebar } from './Sidebar'
import { Header } from './Header'
import { MobileNavigation } from '@/components/shared/navigation/MobileNavigation'
import { studentNavigationConfig } from '@/config/navigation/studentNavigation'

interface StudentLayoutProps {
  children: ReactNode
}

export function StudentLayout({ children }: StudentLayoutProps) {
  const { 
    isCollapsed, 
    isMobileOpen, 
    setNavigationItems, 
    setMobileSidebarOpen,
    setUserType 
  } = useSidebarStore()
  const { user } = useAuthStore()
  const { isMobile, isTablet } = useResponsive()

  // Initialize navigation for student
  useEffect(() => {
    setNavigationItems(studentNavigationConfig)
    setUserType('student')
  }, [setNavigationItems, setUserType])

  // Auto-collapse sidebar on mobile/tablet
  useEffect(() => {
    if (isMobile && isMobileOpen) {
      setMobileSidebarOpen(false)
    }
  }, [isMobile, isMobileOpen, setMobileSidebarOpen])

  // Verify user has student access
  if (!user || user.role !== 'student') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-red-500 text-lg font-medium mb-2">Access Denied</div>
          <div className="text-gray-600">You don't have permission to access this area.</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Navigation */}
      {isMobile && <MobileNavigation />}

      {/* Desktop/Tablet Layout */}
      {!isMobile && (
        <div className="flex h-screen">
          {/* Sidebar */}
          <div className={`
            ${isCollapsed ? 'w-16' : 'w-64'} 
            transition-all duration-300 ease-in-out
            flex-shrink-0
            ${isMobile ? 'hidden' : 'block'}
          `}>
            <Sidebar userType="student" />
          </div>

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header */}
            <Header userType="student" />

            {/* Page Content */}
            <main className="flex-1 overflow-y-auto">
              <div className="p-6">
                {children}
              </div>
            </main>
          </div>
        </div>
      )}

      {/* Mobile Layout */}
      {isMobile && (
        <div className="pb-20">
          {/* Mobile Header */}
          <div className="bg-white border-b border-gray-200 sticky top-0 z-30">
            <Header userType="student" />
          </div>

          {/* Mobile Content */}
          <main className="p-4">
            {children}
          </main>
        </div>
      )}

      {/* Mobile Sidebar Overlay */}
      {isMobile && isMobileOpen && (
        <div className="fixed inset-0 z-50">
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={() => setMobileSidebarOpen(false)}
          />

          {/* Sidebar */}
          <div className="fixed inset-y-0 left-0 w-64 bg-white shadow-xl">
            <Sidebar userType="student" />
          </div>
        </div>
      )}
    </div>
  )
}

export default StudentLayout
