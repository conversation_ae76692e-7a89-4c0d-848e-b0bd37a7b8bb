'use client'

import { useState } from 'react'
import { 
  User, 
  LogOut, 
  Setting<PERSON>, 
  ChevronUp,
  ChevronDown
} from 'lucide-react'

interface UserProfileProps {
  user: any // User type from auth store
  isCollapsed: boolean
  onLogout: () => void
}

export function UserProfile({ user, isCollapsed, onLogout }: UserProfileProps) {
  const [showMenu, setShowMenu] = useState(false)
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false)

  const handleLogoutClick = () => {
    setShowLogoutConfirm(true)
    setShowMenu(false)
  }

  const handleConfirmLogout = () => {
    onLogout()
    setShowLogoutConfirm(false)
  }

  const handleCancelLogout = () => {
    setShowLogoutConfirm(false)
  }

  if (isCollapsed) {
    return (
      <div className="p-2">
        <div className="relative">
          <button
            onClick={() => setShowMenu(!showMenu)}
            className="w-full p-2 rounded-lg hover:bg-gray-100 transition-colors group"
            title={user?.personalInfo?.fullName || user?.email || 'User Menu'}
          >
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
              {user?.personalInfo?.avatar ? (
                <img 
                  src={user.personalInfo.avatar} 
                  alt={user.personalInfo.fullName || user.email}
                  className="w-8 h-8 rounded-full object-cover"
                />
              ) : (
                <User className="w-4 h-4 text-white" />
              )}
            </div>
          </button>

          {/* Collapsed Menu */}
          {showMenu && (
            <div className="absolute bottom-full left-0 mb-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
              <div className="p-3 border-b border-gray-200">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    {user?.personalInfo?.avatar ? (
                      <img 
                        src={user.personalInfo.avatar} 
                        alt={user.personalInfo.fullName || user.email}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-4 h-4 text-white" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {user?.personalInfo?.fullName || user?.email || 'User'}
                    </div>
                    <div className="text-xs text-gray-500 capitalize">
                      {user?.role?.name || 'User'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="py-1">
                <button
                  onClick={() => {
                    setShowMenu(false)
                    // Navigate to settings
                  }}
                  className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Settings
                </button>
                <button
                  onClick={handleLogoutClick}
                  className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="p-4">
      {/* User Info */}
      <div className="relative">
        <button
          onClick={() => setShowMenu(!showMenu)}
          className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors group"
        >
          <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
            {user?.personalInfo?.avatar ? (
              <img 
                src={user.personalInfo.avatar} 
                alt={user.personalInfo.fullName || user.email}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <User className="w-5 h-5 text-white" />
            )}
          </div>
          
          <div className="flex-1 min-w-0 text-left">
            <div className="text-sm font-medium text-gray-900 truncate">
              {user?.personalInfo?.fullName || user?.email || 'User'}
            </div>
            <div className="text-xs text-gray-500 capitalize">
              {user?.role?.name || 'User'}
            </div>
          </div>
          
          {showMenu ? (
            <ChevronDown className="w-4 h-4 text-gray-400" />
          ) : (
            <ChevronUp className="w-4 h-4 text-gray-400" />
          )}
        </button>

        {/* Expanded Menu */}
        {showMenu && (
          <div className="absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
            <div className="py-1">
              <button
                onClick={() => {
                  setShowMenu(false)
                  // Navigate to settings
                }}
                className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </button>
              <button
                onClick={handleLogoutClick}
                className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Confirm Sign Out
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Are you sure you want to sign out? You'll need to sign in again to access your account.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={handleCancelLogout}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmLogout}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default UserProfile
