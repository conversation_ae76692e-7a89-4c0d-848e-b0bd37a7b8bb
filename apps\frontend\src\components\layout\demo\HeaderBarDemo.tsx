'use client'

import { useState } from 'react'
import { Header } from '@/components/layout/Header'
import { ResponsiveCard, ResponsiveGrid } from '@/components/shared/layout/ResponsiveContainer'
import { useResponsive } from '@/hooks/useResponsive'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { 
  Menu, 
  Search, 
  Bell, 
  User, 
  Settings,
  LogOut,
  ChevronDown,
  Home,
  ChevronRight,
  Monitor,
  Tablet,
  Smartphone,
  Navigation,
  Breadcrumbs as BreadcrumbsIcon,
  Command
} from 'lucide-react'

export function HeaderBarDemo() {
  const { isMobile, isTablet, isDesktop } = useResponsive()
  const { isCollapsed, toggleSidebar, unreadCount } = useSidebarStore()
  const { user } = useAuthStore()
  
  const [selectedDemo, setSelectedDemo] = useState<'overview' | 'breadcrumbs' | 'search' | 'notifications' | 'profile'>('overview')

  const headerFeatures = [
    {
      id: 'navigation',
      title: 'Navigation Control',
      description: 'Mobile menu toggle and sidebar collapse functionality',
      icon: Menu,
      features: [
        'Mobile hamburger menu',
        'Desktop sidebar toggle',
        'Responsive behavior',
        'Touch-friendly buttons'
      ]
    },
    {
      id: 'breadcrumbs',
      title: 'Smart Breadcrumbs',
      description: 'Automatic breadcrumb generation from navigation structure',
      icon: BreadcrumbsIcon,
      features: [
        'Auto-generated from URL',
        'Navigation-aware',
        'Overflow handling',
        'Mobile simplification'
      ]
    },
    {
      id: 'search',
      title: 'Global Search',
      description: 'Intelligent search across all navigation items',
      icon: Search,
      features: [
        'Keyboard shortcuts (Cmd+K)',
        'Real-time suggestions',
        'Recent items',
        'Mobile modal interface'
      ]
    },
    {
      id: 'notifications',
      title: 'Notification Center',
      description: 'Real-time notifications with badge counts',
      icon: Bell,
      features: [
        'Badge indicators',
        'Dropdown interface',
        'Mark as read',
        'Notification history'
      ]
    },
    {
      id: 'profile',
      title: 'User Profile',
      description: 'User information and account management',
      icon: User,
      features: [
        'User avatar display',
        'Role information',
        'Settings access',
        'Secure logout'
      ]
    }
  ]

  const deviceTypes = [
    {
      type: 'desktop',
      label: 'Desktop',
      icon: Monitor,
      active: isDesktop,
      description: 'Full header with all features'
    },
    {
      type: 'tablet',
      label: 'Tablet',
      icon: Tablet,
      active: isTablet,
      description: 'Optimized for touch interaction'
    },
    {
      type: 'mobile',
      label: 'Mobile',
      icon: Smartphone,
      active: isMobile,
      description: 'Simplified mobile interface'
    }
  ]

  const headerStats = {
    components: 5,
    features: 20,
    responsiveBreakpoints: 3,
    keyboardShortcuts: 3
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Header Bar Component Demo
        </h1>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Explore the comprehensive Header Bar Component with navigation controls, 
          breadcrumbs, search functionality, notifications, and user profile management.
        </p>
      </div>

      {/* Live Header Demo */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Live Header Demo</h3>
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <Header userType="super_admin" />
          <div className="p-4 bg-gray-50 text-sm text-gray-600">
            This is the actual Header component in action. Try interacting with the search, 
            notifications, and profile dropdown.
          </div>
        </div>
      </ResponsiveCard>

      {/* Device Detection */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Responsive Behavior</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={3} desktopColumns={3} gap={4}>
          {deviceTypes.map((device) => (
            <div
              key={device.type}
              className={`p-4 rounded-lg border-2 transition-colors ${
                device.active 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-3 mb-2">
                <device.icon className={`w-6 h-6 ${
                  device.active ? 'text-blue-600' : 'text-gray-400'
                }`} />
                <span className={`font-medium ${
                  device.active ? 'text-blue-900' : 'text-gray-600'
                }`}>
                  {device.label}
                </span>
                {device.active && (
                  <span className="px-2 py-1 text-xs bg-blue-600 text-white rounded-full">
                    Active
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-600">{device.description}</p>
            </div>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Header Features */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Header Features</h3>
        <ResponsiveGrid mobileColumns={1} tabletColumns={2} desktopColumns={3} gap={4}>
          {headerFeatures.map((feature) => (
            <div key={feature.id} className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3 mb-3">
                <feature.icon className="w-6 h-6 text-blue-600" />
                <h4 className="font-medium text-gray-900">{feature.title}</h4>
              </div>
              <p className="text-sm text-gray-600 mb-3">{feature.description}</p>
              <ul className="space-y-1">
                {feature.features.map((item, index) => (
                  <li key={index} className="text-xs text-gray-500 flex items-center">
                    <div className="w-1 h-1 bg-blue-600 rounded-full mr-2"></div>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* Header Statistics */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Component Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{headerStats.components}</div>
            <div className="text-sm text-blue-800">Components</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{headerStats.features}</div>
            <div className="text-sm text-green-800">Features</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{headerStats.responsiveBreakpoints}</div>
            <div className="text-sm text-purple-800">Breakpoints</div>
          </div>
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{headerStats.keyboardShortcuts}</div>
            <div className="text-sm text-orange-800">Shortcuts</div>
          </div>
        </div>
      </ResponsiveCard>

      {/* Interactive Demo */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Interactive Header Elements</h3>
        <div className="space-y-4">
          {/* Sidebar Toggle Demo */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Sidebar Toggle</h4>
            <div className="flex items-center space-x-4">
              <button
                onClick={toggleSidebar}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Menu className="w-4 h-4" />
                <span>{isCollapsed ? 'Expand' : 'Collapse'} Sidebar</span>
              </button>
              <span className="text-sm text-gray-600">
                Current state: {isCollapsed ? 'Collapsed' : 'Expanded'}
              </span>
            </div>
          </div>

          {/* Breadcrumb Demo */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Breadcrumb Navigation</h4>
            <div className="flex items-center space-x-2 text-sm">
              <Home className="w-4 h-4 text-gray-400" />
              <span className="text-gray-600">Dashboard</span>
              <ChevronRight className="w-4 h-4 text-gray-400" />
              <span className="text-gray-600">Staff Management</span>
              <ChevronRight className="w-4 h-4 text-gray-400" />
              <span className="text-gray-900 font-medium">Analytics</span>
            </div>
          </div>

          {/* Search Demo */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Global Search</h4>
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search navigation..."
                className="w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 text-xs text-gray-400">
                <Command className="w-3 h-3" />
                <span>K</span>
              </div>
            </div>
          </div>

          {/* Notification Demo */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Notifications</h4>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Bell className="w-6 h-6 text-gray-600" />
                <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {unreadCount || 3}
                </span>
              </div>
              <span className="text-sm text-gray-600">
                {unreadCount || 3} unread notifications
              </span>
            </div>
          </div>
        </div>
      </ResponsiveCard>

      {/* Implementation Notes */}
      <ResponsiveCard>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Implementation Highlights</h3>
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Responsive Design</h4>
            <p className="text-sm text-blue-800">
              The header automatically adapts to different screen sizes, showing/hiding elements 
              and adjusting layouts for optimal user experience on any device.
            </p>
          </div>
          
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Smart Navigation</h4>
            <p className="text-sm text-green-800">
              Breadcrumbs are automatically generated from the current route and navigation structure, 
              providing contextual navigation without manual configuration.
            </p>
          </div>
          
          <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 className="font-medium text-purple-900 mb-2">User Experience</h4>
            <p className="text-sm text-purple-800">
              Keyboard shortcuts, real-time search, notification badges, and intuitive interactions 
              create a modern, efficient user experience.
            </p>
          </div>
        </div>
      </ResponsiveCard>
    </div>
  )
}

export default HeaderBarDemo
