'use client'

import { useLocationStore } from '@/stores/location/useLocationStore'
import { CountryCard } from './CountryCard'
import { CountryListItem } from './CountryListItem'
import { LocationPagination } from './LocationPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Globe } from 'lucide-react'

export function CountriesList() {
  const {
    countries,
    viewMode,
    countriesPagination,
    isLoading,
    fetchCountries,
    setSelectedCountry
  } = useLocationStore()

  const handlePageChange = (page: number) => {
    fetchCountries(page)
  }

  const handleCountrySelect = (country: any) => {
    setSelectedCountry(country)
  }

  if (isLoading && countries.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (countries.length === 0) {
    return (
      <EmptyState
        icon={Globe}
        title="No countries found"
        description="No countries match your current filters. Try adjusting your search criteria."
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Countries Grid/List */}
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {countries.map((country) => (
            <CountryCard
              key={country.id}
              country={country}
              onSelect={handleCountrySelect}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {countries.map((country) => (
            <CountryListItem
              key={country.id}
              country={country}
              onSelect={handleCountrySelect}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      <LocationPagination
        pagination={countriesPagination}
        onPageChange={handlePageChange}
      />
    </div>
  )
}
