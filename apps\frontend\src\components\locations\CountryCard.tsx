'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Eye, Users, MapPin, Globe } from 'lucide-react'
import { CountryForm } from './CountryForm'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { toast } from 'sonner'

interface CountryCardProps {
  country: any
  onSelect: (country: any) => void
}

export function CountryCard({ country, onSelect }: CountryCardProps) {
  const { deleteCountry, fetchCountries } = useLocationStore()
  const [editDialogOpen, setEditDialogOpen] = useState(false)

  const handleViewStates = () => {
    onSelect(country)
  }

  const handleEdit = () => {
    setEditDialogOpen(true)
  }

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete ${country.name}? This action cannot be undone.`)) {
      try {
        await deleteCountry(country.id)
        await fetchCountries()
      } catch (error) {
        console.error('Delete error:', error)
      }
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  return (
    <>
    <Card className="hover:shadow-md transition-shadow cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {country.flag && (
              <img
                src={country.flag}
                alt={`${country.name} flag`}
                className="w-8 h-6 object-cover rounded"
              />
            )}
            <div>
              <h3 className="font-semibold text-lg">{country.name}</h3>
              <p className="text-sm text-gray-500">{country.code}</p>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleViewStates}>
                <Eye className="h-4 w-4 mr-2" />
                View States
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem className="text-destructive" onClick={handleDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Basic Info */}
        <div className="grid grid-cols-2 gap-2 text-sm">
          {country.details?.capital && (
            <div>
              <span className="text-gray-500">Capital:</span>
              <p className="font-medium">{country.details.capital}</p>
            </div>
          )}
          {country.details?.currency && (
            <div>
              <span className="text-gray-500">Currency:</span>
              <p className="font-medium">{country.details.currency}</p>
            </div>
          )}
        </div>

        {/* Statistics */}
        {(country.details?.population || country.details?.area) && (
          <div className="grid grid-cols-2 gap-2 text-sm">
            {country.details?.population && (
              <div className="flex items-center space-x-1">
                <Users className="h-3 w-3 text-gray-400" />
                <span>{formatNumber(country.details.population)}</span>
              </div>
            )}
            {country.details?.area && (
              <div className="flex items-center space-x-1">
                <MapPin className="h-3 w-3 text-gray-400" />
                <span>{formatNumber(country.details.area)} km²</span>
              </div>
            )}
          </div>
        )}

        {/* Status and Actions */}
        <div className="flex items-center justify-between pt-2">
          <Badge variant={country.isActive ? 'default' : 'secondary'}>
            {country.isActive ? 'Active' : 'Inactive'}
          </Badge>

          <Button
            variant="outline"
            size="sm"
            onClick={handleViewStates}
            className="flex items-center space-x-1"
          >
            <Globe className="h-3 w-3" />
            <span>View States</span>
          </Button>
        </div>
      </CardContent>
    </Card>

    {/* Edit Dialog */}
    <CountryForm
      mode="edit"
      country={country}
      open={editDialogOpen}
      onOpenChange={setEditDialogOpen}
      onSuccess={() => {
        fetchCountries()
        setEditDialogOpen(false)
      }}
      trigger={<div style={{ display: 'none' }} />}
    />
    </>
  )
}
