'use client'

import { useState, useEffect } from 'react'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Plus, Edit, Save, X } from 'lucide-react'
import { Formik, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { toast } from 'sonner'

// Validation schema using Yup
const validationSchema = Yup.object({
  name: Yup.string()
    .required('Country name is required')
    .min(2, 'Country name must be at least 2 characters')
    .max(100, 'Country name must be less than 100 characters'),
  code: Yup.string()
    .required('Country code is required')
    .min(2, 'Country code must be at least 2 characters')
    .max(3, 'Country code must be at most 3 characters')
    .matches(/^[A-Z]+$/, 'Country code must contain only uppercase letters'),
  capital: Yup.string()
    .max(100, 'Capital name must be less than 100 characters'),
  currency: Yup.string()
    .max(100, 'Currency name must be less than 100 characters'),
  currencyCode: Yup.string()
    .max(3, 'Currency code must be at most 3 characters')
    .matches(/^[A-Z]*$/, 'Currency code must contain only uppercase letters'),
  phoneCode: Yup.string()
    .matches(/^\+?[0-9]*$/, 'Phone code must contain only numbers and optional + prefix'),
  isActive: Yup.boolean(),
  priority: Yup.number()
    .min(0, 'Priority must be 0 or greater')
    .integer('Priority must be a whole number')
})

interface CountryFormProps {
  country?: any
  mode: 'create' | 'edit'
  trigger?: React.ReactNode
  onSuccess?: () => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function CountryForm({ country, mode, trigger, onSuccess, open: externalOpen, onOpenChange }: CountryFormProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const open = externalOpen !== undefined ? externalOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen
  const { createCountry, updateCountry, isLoading } = useLocationStore()

  // Initial values for Formik
  const initialValues = {
    name: country?.name || '',
    code: country?.code || '',
    capital: country?.details?.capital || '',
    currency: country?.details?.currency || '',
    currencyCode: country?.details?.currencyCode || '',
    phoneCode: country?.details?.phoneCode || '',
    isActive: country?.isActive ?? true,
    priority: country?.priority || 0
  }

  const handleSubmit = async (values: any, { setSubmitting, resetForm }: any) => {
    console.log('Country form submitting with values:', values)
    try {
      // Prepare data for submission
      const submitData = {
        name: values.name,
        code: values.code.toUpperCase(),
        details: {
          capital: values.capital,
          currency: values.currency,
          currencyCode: values.currencyCode.toUpperCase(),
          phoneCode: values.phoneCode
        },
        isActive: values.isActive,
        priority: values.priority
      }
      console.log('Country submit data:', submitData)

      if (mode === 'create') {
        await createCountry(submitData)
        toast.success('Country created successfully')
        resetForm()
      } else {
        await updateCountry(country.id, submitData)
        toast.success('Country updated successfully')
      }

      // Only close dialog on successful submission
      setOpen(false)
      onSuccess?.()
    } catch (error) {
      console.error('Form submission error:', error)
      toast.error('Failed to save country')
      // Don't close dialog on error - let user fix the issues
    } finally {
      setSubmitting(false)
    }
  }

  const defaultTrigger = (
    <Button size="sm" className="gap-2">
      {mode === 'create' ? <Plus className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
      {mode === 'create' ? 'Add Country' : 'Edit Country'}
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      // Only allow closing via Cancel button, not by clicking outside
      if (!newOpen) {
        // Don't auto-close
        return;
      }
      setOpen(newOpen);
    }} modal={true}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent
        className="max-w-2xl max-h-[90vh] overflow-y-auto"
        onPointerDownOutside={(e) => e.preventDefault()}
        onInteractOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
        onFocusOutside={(e) => e.preventDefault()}
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Add New Country' : `Edit ${country?.name}`}
          </DialogTitle>
        </DialogHeader>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ values, errors, touched, handleChange, isSubmitting, setSubmitting, resetForm }) => (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Country Name *</Label>
                      <Field
                        as={Input}
                        id="name"
                        name="name"
                        placeholder="Enter country name"
                        className={errors.name && touched.name ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="name" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="code">Country Code *</Label>
                      <Field
                        as={Input}
                        id="code"
                        name="code"
                        placeholder="US, IN, UK"
                        maxLength={3}
                        className={errors.code && touched.code ? 'border-red-500' : ''}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          handleChange({
                            target: {
                              name: 'code',
                              value: e.target.value.toUpperCase()
                            }
                          })
                        }}
                      />
                      <ErrorMessage name="code" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="capital">Capital</Label>
                      <Field
                        as={Input}
                        id="capital"
                        name="capital"
                        placeholder="Enter capital city"
                        className={errors.capital && touched.capital ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="capital" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="phoneCode">Phone Code</Label>
                      <Field
                        as={Input}
                        id="phoneCode"
                        name="phoneCode"
                        placeholder="+1, +91, +44"
                        className={errors.phoneCode && touched.phoneCode ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="phoneCode" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="currency">Currency</Label>
                      <Field
                        as={Input}
                        id="currency"
                        name="currency"
                        placeholder="US Dollar"
                        className={errors.currency && touched.currency ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="currency" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="currencyCode">Currency Code</Label>
                      <Field
                        as={Input}
                        id="currencyCode"
                        name="currencyCode"
                        placeholder="USD"
                        maxLength={3}
                        className={errors.currencyCode && touched.currencyCode ? 'border-red-500' : ''}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          handleChange({
                            target: {
                              name: 'currencyCode',
                              value: e.target.value.toUpperCase()
                            }
                          })
                        }}
                      />
                      <ErrorMessage name="currencyCode" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isActive">Active Status</Label>
                      <p className="text-sm text-muted-foreground">
                        Enable this country for selection
                      </p>
                    </div>
                    <Field name="isActive">
                      {({ field }: any) => (
                        <Switch
                          id="isActive"
                          checked={field.value}
                          onCheckedChange={(checked) => {
                            handleChange({
                              target: {
                                name: 'isActive',
                                value: checked
                              }
                            })
                          }}
                        />
                      )}
                    </Field>
                  </div>

                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Field
                      as={Input}
                      id="priority"
                      name="priority"
                      type="number"
                      placeholder="0"
                      className={errors.priority && touched.priority ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="priority" component="div" className="text-red-500 text-sm mt-1" />
                    <p className="text-sm text-muted-foreground mt-1">
                      Higher priority countries appear first in lists
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Form Actions */}
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="button"
                  disabled={isSubmitting}
                  onClick={() => handleSubmit(values, { setSubmitting, resetForm })}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSubmitting ? 'Saving...' : mode === 'create' ? 'Create Country' : 'Update Country'}
                </Button>
              </div>
            </div>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  )
}
