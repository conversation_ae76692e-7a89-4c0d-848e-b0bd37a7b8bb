'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Eye, Users, MapPin, Globe } from 'lucide-react'
import { CountryForm } from './CountryForm'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { toast } from 'sonner'

interface CountryListItemProps {
  country: any
  onSelect: (country: any) => void
}

export function CountryListItem({ country, onSelect }: CountryListItemProps) {
  const { fetchCountries, deleteCountry } = useLocationStore()
  const [editDialogOpen, setEditDialogOpen] = useState(false)

  const handleViewStates = () => {
    onSelect(country)
  }

  const handleEdit = () => {
    setEditDialogOpen(true)
  }

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this country?')) {
      try {
        await deleteCountry(country.id)
        toast.success('Country deleted successfully')
      } catch (error) {
        toast.error('Failed to delete country')
      }
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  return (
    <>
    <Card className="hover:shadow-sm transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Country Info */}
          <div className="flex items-center space-x-4 flex-1">
            {/* Flag and Name */}
            <div className="flex items-center space-x-3">
              {country.flag && (
                <img
                  src={country.flag}
                  alt={`${country.name} flag`}
                  className="w-8 h-6 object-cover rounded"
                />
              )}
              <div>
                <h3 className="font-semibold">{country.name}</h3>
                <p className="text-sm text-gray-500">{country.code}</p>
              </div>
            </div>

            {/* Details */}
            <div className="hidden md:flex items-center space-x-6 text-sm">
              {country.details?.capital && (
                <div>
                  <span className="text-gray-500">Capital: </span>
                  <span className="font-medium">{country.details.capital}</span>
                </div>
              )}
              {country.details?.currency && (
                <div>
                  <span className="text-gray-500">Currency: </span>
                  <span className="font-medium">{country.details.currency}</span>
                </div>
              )}
            </div>

            {/* Statistics */}
            <div className="hidden lg:flex items-center space-x-4 text-sm">
              {country.details?.population && (
                <div className="flex items-center space-x-1">
                  <Users className="h-3 w-3 text-gray-400" />
                  <span>{formatNumber(country.details.population)}</span>
                </div>
              )}
              {country.details?.area && (
                <div className="flex items-center space-x-1">
                  <MapPin className="h-3 w-3 text-gray-400" />
                  <span>{formatNumber(country.details.area)} km²</span>
                </div>
              )}
            </div>
          </div>

          {/* Status and Actions */}
          <div className="flex items-center space-x-3">
            <Badge variant={country.isActive ? 'default' : 'secondary'}>
              {country.isActive ? 'Active' : 'Inactive'}
            </Badge>

            <Button
              variant="outline"
              size="sm"
              onClick={handleViewStates}
              className="flex items-center space-x-1"
            >
              <Globe className="h-3 w-3" />
              <span className="hidden sm:inline">View States</span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleViewStates}>
                  <Eye className="h-4 w-4 mr-2" />
                  View States
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem className="text-destructive" onClick={handleDelete}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Mobile Details */}
        <div className="md:hidden mt-3 pt-3 border-t border-gray-100">
          <div className="grid grid-cols-2 gap-2 text-sm">
            {country.details?.capital && (
              <div>
                <span className="text-gray-500">Capital:</span>
                <p className="font-medium">{country.details.capital}</p>
              </div>
            )}
            {country.details?.currency && (
              <div>
                <span className="text-gray-500">Currency:</span>
                <p className="font-medium">{country.details.currency}</p>
              </div>
            )}
          </div>

          {(country.details?.population || country.details?.area) && (
            <div className="flex items-center space-x-4 mt-2 text-sm">
              {country.details?.population && (
                <div className="flex items-center space-x-1">
                  <Users className="h-3 w-3 text-gray-400" />
                  <span>{formatNumber(country.details.population)}</span>
                </div>
              )}
              {country.details?.area && (
                <div className="flex items-center space-x-1">
                  <MapPin className="h-3 w-3 text-gray-400" />
                  <span>{formatNumber(country.details.area)} km²</span>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>

    {/* Edit Dialog */}
    <CountryForm
      mode="edit"
      country={country}
      open={editDialogOpen}
      onOpenChange={setEditDialogOpen}
      onSuccess={() => {
        fetchCountries()
        setEditDialogOpen(false)
      }}
      trigger={<div style={{ display: 'none' }} />}
    />
    </>
  )
}
