'use client'

import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Eye, MapPin, Building } from 'lucide-react'
import { DistrictForm } from './DistrictForm'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { toast } from 'sonner'

interface DistrictCardProps {
  district: any
  onSelect: (district: any) => void
}

export function DistrictCard({ district, onSelect }: DistrictCardProps) {
  const { fetchDistricts, deleteDistrict } = useLocationStore()

  const handleViewDetails = () => {
    onSelect(district)
  }

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this district?')) {
      try {
        await deleteDistrict(district.id)
        toast.success('District deleted successfully')
      } catch (error) {
        toast.error('Failed to delete district')
      }
    }
  }



  const getTypeColor = (type: string) => {
    switch (type) {
      case 'district':
        return 'bg-blue-100 text-blue-800'
      case 'city':
        return 'bg-green-100 text-green-800'
      case 'municipality':
        return 'bg-purple-100 text-purple-800'
      case 'town':
        return 'bg-orange-100 text-orange-800'
      case 'village':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'city':
        return Building
      case 'district':
        return MapPin
      default:
        return Building
    }
  }

  const TypeIcon = getTypeIcon(district.details?.type)

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
              <TypeIcon className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-lg">{district.name}</h3>
              <div className="flex items-center space-x-2">
                {district.code && (
                  <p className="text-sm text-gray-500">{district.code}</p>
                )}
                <Badge 
                  variant="secondary" 
                  className={`text-xs capitalize ${getTypeColor(district.details?.type)}`}
                >
                  {district.details?.type || 'district'}
                </Badge>
              </div>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100" type="button">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleViewDetails}>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DistrictForm
                mode="edit"
                district={district}
                onSuccess={() => fetchDistricts()}
                trigger={
                  <DropdownMenuItem>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                }
              />
              <DropdownMenuItem className="text-destructive" onClick={handleDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Basic Info */}
        <div className="grid grid-cols-1 gap-2 text-sm">
          {typeof district.state === 'object' && district.state?.name && (
            <div>
              <span className="text-gray-500">State:</span>
              <p className="font-medium">{district.state.name}</p>
            </div>
          )}
          {district.details?.pincode && (
            <div>
              <span className="text-gray-500">Pincode:</span>
              <p className="font-medium font-mono">{district.details.pincode}</p>
            </div>
          )}
        </div>



        {/* Status and Actions */}
        <div className="flex items-center justify-between pt-2">
          <Badge variant={district.isActive ? 'default' : 'secondary'}>
            {district.isActive ? 'Active' : 'Inactive'}
          </Badge>

          <Button
            variant="outline"
            size="sm"
            onClick={handleViewDetails}
            className="flex items-center space-x-1"
          >
            <Eye className="h-3 w-3" />
            <span>Details</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
