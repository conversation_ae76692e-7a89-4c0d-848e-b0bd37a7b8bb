'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Eye, MapPin, Building, Hash } from 'lucide-react'
import { DistrictForm } from './DistrictForm'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { toast } from 'sonner'

interface DistrictListItemProps {
  district: any
  onSelect: (district: any) => void
}

export function DistrictListItem({ district, onSelect }: DistrictListItemProps) {
  const { fetchDistricts, deleteDistrict } = useLocationStore()
  const [editDialogOpen, setEditDialogOpen] = useState(false)

  const handleViewDetails = () => {
    onSelect(district)
  }

  const handleEdit = () => {
    setEditDialogOpen(true)
  }

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this district?')) {
      try {
        await deleteDistrict(district.id)
        toast.success('District deleted successfully')
      } catch (error) {
        toast.error('Failed to delete district')
      }
    }
  }



  const getTypeColor = (type: string) => {
    switch (type) {
      case 'district':
        return 'bg-blue-100 text-blue-800'
      case 'city':
        return 'bg-green-100 text-green-800'
      case 'municipality':
        return 'bg-purple-100 text-purple-800'
      case 'town':
        return 'bg-orange-100 text-orange-800'
      case 'village':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'city':
        return Building
      case 'district':
        return MapPin
      default:
        return Building
    }
  }

  const TypeIcon = getTypeIcon(district.details?.type)

  return (
    <>
    <Card className="hover:shadow-sm transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* District Info */}
          <div className="flex items-center space-x-4 flex-1">
            {/* Icon and Name */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                <TypeIcon className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold">{district.name}</h3>
                <div className="flex items-center space-x-2">
                  {district.code && (
                    <p className="text-sm text-gray-500">{district.code}</p>
                  )}
                  <Badge 
                    variant="secondary" 
                    className={`text-xs capitalize ${getTypeColor(district.details?.type)}`}
                  >
                    {district.details?.type || 'district'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Details */}
            <div className="hidden md:flex items-center space-x-6 text-sm">
              {typeof district.state === 'object' && district.state?.name && (
                <div>
                  <span className="text-gray-500">State: </span>
                  <span className="font-medium">{district.state.name}</span>
                </div>
              )}
              {district.details?.pincode && (
                <div className="flex items-center space-x-1">
                  <Hash className="h-3 w-3 text-gray-400" />
                  <span className="font-mono">{district.details.pincode}</span>
                </div>
              )}
            </div>

            {/* Statistics - Removed population and area as per requirements */}
          </div>

          {/* Status and Actions */}
          <div className="flex items-center space-x-3">
            <Badge variant={district.isActive ? 'default' : 'secondary'}>
              {district.isActive ? 'Active' : 'Inactive'}
            </Badge>

            <Button
              variant="outline"
              size="sm"
              onClick={handleViewDetails}
              className="flex items-center space-x-1"
            >
              <Eye className="h-3 w-3" />
              <span className="hidden sm:inline">Details</span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" type="button">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleViewDetails}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem className="text-destructive" onClick={handleDelete}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Mobile Details */}
        <div className="md:hidden mt-3 pt-3 border-t border-gray-100">
          <div className="grid grid-cols-2 gap-2 text-sm">
            {typeof district.state === 'object' && district.state?.name && (
              <div>
                <span className="text-gray-500">State:</span>
                <p className="font-medium">{district.state.name}</p>
              </div>
            )}
            {district.details?.pincode && (
              <div>
                <span className="text-gray-500">Pincode:</span>
                <p className="font-medium font-mono">{district.details.pincode}</p>
              </div>
            )}
          </div>


        </div>
      </CardContent>
    </Card>

    {/* Edit Dialog */}
    <DistrictForm
      mode="edit"
      district={district}
      open={editDialogOpen}
      onOpenChange={setEditDialogOpen}
      onSuccess={() => {
        fetchDistricts()
        setEditDialogOpen(false)
      }}
      trigger={<div style={{ display: 'none' }} />}
    />
    </>
  )
}
