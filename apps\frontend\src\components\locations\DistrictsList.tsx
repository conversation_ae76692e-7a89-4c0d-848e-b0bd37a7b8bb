'use client'

import { useLocationStore } from '@/stores/location/useLocationStore'
import { DistrictCard } from './DistrictCard'
import { DistrictListItem } from './DistrictListItem'
import { LocationPagination } from './LocationPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Building, AlertTriangle } from 'lucide-react'

export function DistrictsList() {
  const {
    districts,
    viewMode,
    districtsPagination,
    isLoading,
    selectedCountry,
    selectedState,
    fetchDistricts,
    setSelectedDistrict
  } = useLocationStore()

  const handlePageChange = (page: number) => {
    fetchDistricts(selectedState?.id, selectedCountry?.id, page)
  }

  const handleDistrictSelect = (district: any) => {
    setSelectedDistrict(district)
  }

  if (!selectedCountry) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Please select a country first to view districts and cities.
        </AlertDescription>
      </Alert>
    )
  }

  if (isLoading && districts.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-16 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (districts.length === 0) {
    const locationText = selectedState 
      ? `${selectedState.name}, ${selectedCountry.name}`
      : selectedCountry.name

    return (
      <EmptyState
        icon={Building}
        title="No districts found"
        description={`No districts found in ${locationText}. Try adjusting your search criteria or add new districts.`}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Districts Grid/List */}
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {districts.map((district) => (
            <DistrictCard
              key={district.id}
              district={district}
              onSelect={handleDistrictSelect}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {districts.map((district) => (
            <DistrictListItem
              key={district.id}
              district={district}
              onSelect={handleDistrictSelect}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      <LocationPagination
        pagination={districtsPagination}
        onPageChange={handlePageChange}
      />
    </div>
  )
}
