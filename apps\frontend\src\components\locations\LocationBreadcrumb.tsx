'use client'

import { useLocationStore } from '@/stores/location/useLocationStore'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { Globe, MapPin, Building, X } from 'lucide-react'

export function LocationBreadcrumb() {
  const {
    selectedCountry,
    selectedState,
    selectedDistrict,
    setSelectedCountry,
    setSelectedState,
    setSelectedDistrict
  } = useLocationStore()

  const handleClearCountry = () => {
    setSelectedCountry(null)
  }

  const handleClearState = () => {
    setSelectedState(null)
  }

  const handleClearDistrict = () => {
    setSelectedDistrict(null)
  }

  if (!selectedCountry && !selectedState && !selectedDistrict) {
    return null
  }

  return (
    <div className="flex items-center justify-between">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/super-admin/locations" className="flex items-center space-x-1">
              <Globe className="h-4 w-4" />
              <span>Locations</span>
            </BreadcrumbLink>
          </BreadcrumbItem>

          {selectedCountry && (
            <>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className="flex items-center space-x-1">
                  <Globe className="h-4 w-4" />
                  <span>{selectedCountry.name}</span>
                  <span className="text-xs text-gray-500">({selectedCountry.code})</span>
                </BreadcrumbPage>
              </BreadcrumbItem>
            </>
          )}

          {selectedState && (
            <>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4" />
                  <span>{selectedState.name}</span>
                  {selectedState.code && (
                    <span className="text-xs text-gray-500">({selectedState.code})</span>
                  )}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </>
          )}

          {selectedDistrict && (
            <>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className="flex items-center space-x-1">
                  <Building className="h-4 w-4" />
                  <span>{selectedDistrict.name}</span>
                  {selectedDistrict.details?.type && (
                    <span className="text-xs text-gray-500 capitalize">
                      ({selectedDistrict.details.type})
                    </span>
                  )}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </>
          )}
        </BreadcrumbList>
      </Breadcrumb>

      {/* Clear Selection Buttons */}
      <div className="flex items-center space-x-2">
        {selectedDistrict && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearDistrict}
            className="h-8 px-2"
          >
            <X className="h-3 w-3 mr-1" />
            Clear District
          </Button>
        )}
        
        {selectedState && !selectedDistrict && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearState}
            className="h-8 px-2"
          >
            <X className="h-3 w-3 mr-1" />
            Clear State
          </Button>
        )}
        
        {selectedCountry && !selectedState && !selectedDistrict && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearCountry}
            className="h-8 px-2"
          >
            <X className="h-3 w-3 mr-1" />
            Clear Country
          </Button>
        )}
      </div>
    </div>
  )
}
