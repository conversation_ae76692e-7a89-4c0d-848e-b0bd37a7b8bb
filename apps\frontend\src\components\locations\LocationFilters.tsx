'use client'

import { useState, useCallback } from 'react'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Search, Filter, X, LayoutGrid, List } from 'lucide-react'
import { debounce } from '@/utils/debounce'

interface LocationFiltersProps {
  activeTab: 'countries' | 'states' | 'districts'
}

export function LocationFilters({ activeTab }: LocationFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const {
    filters,
    viewMode,
    countries,
    states,
    selectedCountry,
    selectedState,
    setFilters,
    setViewMode,
    resetFilters,
    fetchCountries,
    fetchStates,
    fetchDistricts
  } = useLocationStore()

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchValue: string) => {
      setFilters({ search: searchValue })
      
      switch (activeTab) {
        case 'countries':
          fetchCountries()
          break
        case 'states':
          fetchStates(selectedCountry?.id)
          break
        case 'districts':
          fetchDistricts(selectedState?.id, selectedCountry?.id)
          break
      }
    }, 300),
    [activeTab, selectedCountry, selectedState]
  )

  const handleSearchChange = (value: string) => {
    debouncedSearch(value)
  }

  const handleStatusChange = (value: string) => {
    setFilters({ isActive: value as 'all' | 'true' | 'false' })

    switch (activeTab) {
      case 'countries':
        fetchCountries()
        break
      case 'states':
        fetchStates(selectedCountry?.id)
        break
      case 'districts':
        fetchDistricts(selectedState?.id, selectedCountry?.id)
        break
    }
  }

  const handleCountryFilter = (countryId: string) => {
    const actualCountryId = countryId === 'all' ? undefined : countryId
    setFilters({ countryId: actualCountryId })
    if (activeTab === 'states') {
      fetchStates(actualCountryId)
    } else if (activeTab === 'districts') {
      fetchDistricts(undefined, actualCountryId)
    }
  }

  const handleStateFilter = (stateId: string) => {
    const actualStateId = stateId === 'all' ? undefined : stateId
    setFilters({ stateId: actualStateId })
    if (activeTab === 'districts') {
      fetchDistricts(actualStateId)
    }
  }

  const handleTypeFilter = (type: string) => {
    const actualType = type === 'all' ? undefined : type
    setFilters({ type: actualType })
    fetchDistricts(selectedState?.id, selectedCountry?.id)
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.isActive !== 'true') count++
    if (filters.countryId) count++
    if (filters.stateId) count++
    if (filters.type) count++
    return count
  }

  const getPlaceholderText = () => {
    switch (activeTab) {
      case 'countries':
        return 'Search countries by name, code, or capital...'
      case 'states':
        return 'Search states by name, code, or capital...'
      case 'districts':
        return 'Search districts by name, code, or pincode...'
    }
  }

  return (
    <div className="space-y-4">
      {/* Basic Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="flex flex-1 gap-4 items-center">
          {/* Search */}
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={getPlaceholderText()}
              defaultValue={filters.search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Status Filter */}
          <Select value={filters.isActive} onValueChange={handleStatusChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="true">Active</SelectItem>
              <SelectItem value="false">Inactive</SelectItem>
            </SelectContent>
          </Select>

          {/* Advanced Filters Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="relative"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {getActiveFiltersCount() > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
              >
                {getActiveFiltersCount()}
              </Badge>
            )}
          </Button>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'card' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('card')}
          >
            <LayoutGrid className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="p-4 border rounded-lg bg-muted/50 space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Advanced Filters</h4>
            <Button variant="ghost" size="sm" onClick={resetFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Country Filter (for states and districts) */}
            {(activeTab === 'states' || activeTab === 'districts') && (
              <div>
                <label className="text-sm font-medium mb-2 block">Country</label>
                <Select
                  value={filters.countryId || 'all'}
                  onValueChange={handleCountryFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Countries</SelectItem>
                    {countries.map((country) => (
                      <SelectItem key={country.id} value={country.id}>
                        {country.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* State Filter (for districts) */}
            {activeTab === 'districts' && (
              <div>
                <label className="text-sm font-medium mb-2 block">State</label>
                <Select
                  value={filters.stateId || 'all'}
                  onValueChange={handleStateFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select state" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All States</SelectItem>
                    {states.map((state) => (
                      <SelectItem key={state.id} value={state.id}>
                        {state.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Type Filter (for districts) */}
            {activeTab === 'districts' && (
              <div>
                <label className="text-sm font-medium mb-2 block">Type</label>
                <Select
                  value={filters.type || 'all'}
                  onValueChange={handleTypeFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="district">District</SelectItem>
                    <SelectItem value="city">City</SelectItem>
                    <SelectItem value="municipality">Municipality</SelectItem>
                    <SelectItem value="town">Town</SelectItem>
                    <SelectItem value="village">Village</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
