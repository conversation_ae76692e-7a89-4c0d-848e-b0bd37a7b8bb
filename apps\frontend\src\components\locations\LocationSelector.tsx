'use client'

import { useState, useEffect } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertTriangle } from 'lucide-react'

interface LocationSelectorProps {
  value?: {
    countryId?: string
    stateId?: string
    districtId?: string
  }
  onChange?: (location: {
    countryId?: string
    stateId?: string
    districtId?: string
    country?: any
    state?: any
    district?: any
  }) => void
  required?: {
    country?: boolean
    state?: boolean
    district?: boolean
  }
  disabled?: boolean
  showLabels?: boolean
  layout?: 'horizontal' | 'vertical'
  size?: 'sm' | 'default' | 'lg'
}

export function LocationSelector({
  value = {},
  onChange,
  required = {},
  disabled = false,
  showLabels = true,
  layout = 'vertical',
  size = 'default'
}: LocationSelectorProps) {
  const [countries, setCountries] = useState<any[]>([])
  const [states, setStates] = useState<any[]>([])
  const [districts, setDistricts] = useState<any[]>([])
  const [loading, setLoading] = useState({
    countries: false,
    states: false,
    districts: false
  })
  const [error, setError] = useState<string | null>(null)

  // Load countries on mount
  useEffect(() => {
    loadCountries()
  }, [])

  // Load states when country changes
  useEffect(() => {
    if (value.countryId) {
      loadStates(value.countryId)
    } else {
      setStates([])
      setDistricts([])
    }
  }, [value.countryId])

  // Load districts when state changes
  useEffect(() => {
    if (value.stateId) {
      loadDistricts(value.stateId)
    } else {
      setDistricts([])
    }
  }, [value.stateId])

  const loadCountries = async () => {
    setLoading(prev => ({ ...prev, countries: true }))
    setError(null)
    try {
      const response = await fetch('/api/locations/countries?isActive=true&limit=300&sort=name')
      const data = await response.json()
      
      if (data.success) {
        setCountries(data.countries)
      } else {
        throw new Error(data.error || 'Failed to load countries')
      }
    } catch (err) {
      setError('Failed to load countries')
      console.error('Error loading countries:', err)
    } finally {
      setLoading(prev => ({ ...prev, countries: false }))
    }
  }

  const loadStates = async (countryId: string) => {
    setLoading(prev => ({ ...prev, states: true }))
    try {
      const response = await fetch(`/api/locations/states?countryId=${countryId}&isActive=true&limit=500&sort=name`)
      const data = await response.json()
      
      if (data.success) {
        setStates(data.states)
      } else {
        throw new Error(data.error || 'Failed to load states')
      }
    } catch (err) {
      console.error('Error loading states:', err)
      setStates([])
    } finally {
      setLoading(prev => ({ ...prev, states: false }))
    }
  }

  const loadDistricts = async (stateId: string) => {
    setLoading(prev => ({ ...prev, districts: true }))
    try {
      const response = await fetch(`/api/locations/districts?stateId=${stateId}&isActive=true&limit=1000&sort=name`)
      const data = await response.json()
      
      if (data.success) {
        setDistricts(data.districts)
      } else {
        throw new Error(data.error || 'Failed to load districts')
      }
    } catch (err) {
      console.error('Error loading districts:', err)
      setDistricts([])
    } finally {
      setLoading(prev => ({ ...prev, districts: false }))
    }
  }

  const handleCountryChange = (countryId: string) => {
    const country = countries.find(c => c.id === countryId)
    onChange?.({
      countryId,
      stateId: undefined,
      districtId: undefined,
      country,
      state: undefined,
      district: undefined
    })
  }

  const handleStateChange = (stateId: string) => {
    const state = states.find(s => s.id === stateId)
    onChange?.({
      countryId: value.countryId,
      stateId,
      districtId: undefined,
      country: countries.find(c => c.id === value.countryId),
      state,
      district: undefined
    })
  }

  const handleDistrictChange = (districtId: string) => {
    const district = districts.find(d => d.id === districtId)
    onChange?.({
      countryId: value.countryId,
      stateId: value.stateId,
      districtId,
      country: countries.find(c => c.id === value.countryId),
      state: states.find(s => s.id === value.stateId),
      district
    })
  }

  const containerClass = layout === 'horizontal' 
    ? 'flex flex-col sm:flex-row gap-4' 
    : 'space-y-4'

  const selectSize = size === 'sm' ? 'h-8' : size === 'lg' ? 'h-12' : 'h-10'

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className={containerClass}>
      {/* Country Selection */}
      <div className="flex-1">
        {showLabels && (
          <Label className="text-sm font-medium">
            Country {required.country && <span className="text-red-500">*</span>}
          </Label>
        )}
        <Select
          value={value.countryId || ''}
          onValueChange={handleCountryChange}
          disabled={disabled || loading.countries}
        >
          <SelectTrigger className={selectSize}>
            <SelectValue placeholder={
              loading.countries ? 'Loading countries...' : 'Select country'
            } />
            {loading.countries && <Loader2 className="h-4 w-4 animate-spin" />}
          </SelectTrigger>
          <SelectContent>
            {countries.map((country) => (
              <SelectItem key={country.id} value={country.id}>
                <div className="flex items-center space-x-2">
                  {country.flag && (
                    <img
                      src={country.flag}
                      alt=""
                      className="w-4 h-3 object-cover rounded"
                    />
                  )}
                  <span>{country.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* State Selection */}
      <div className="flex-1">
        {showLabels && (
          <Label className="text-sm font-medium">
            State/Province {required.state && <span className="text-red-500">*</span>}
          </Label>
        )}
        <Select
          value={value.stateId || ''}
          onValueChange={handleStateChange}
          disabled={disabled || !value.countryId || loading.states}
        >
          <SelectTrigger className={selectSize}>
            <SelectValue placeholder={
              !value.countryId 
                ? 'Select country first'
                : loading.states 
                ? 'Loading states...' 
                : 'Select state'
            } />
            {loading.states && <Loader2 className="h-4 w-4 animate-spin" />}
          </SelectTrigger>
          <SelectContent>
            {states.map((state) => (
              <SelectItem key={state.id} value={state.id}>
                {state.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* District Selection */}
      <div className="flex-1">
        {showLabels && (
          <Label className="text-sm font-medium">
            District/City {required.district && <span className="text-red-500">*</span>}
          </Label>
        )}
        <Select
          value={value.districtId || ''}
          onValueChange={handleDistrictChange}
          disabled={disabled || !value.stateId || loading.districts}
        >
          <SelectTrigger className={selectSize}>
            <SelectValue placeholder={
              !value.stateId 
                ? 'Select state first'
                : loading.districts 
                ? 'Loading districts...' 
                : 'Select district'
            } />
            {loading.districts && <Loader2 className="h-4 w-4 animate-spin" />}
          </SelectTrigger>
          <SelectContent>
            {districts.map((district) => (
              <SelectItem key={district.id} value={district.id}>
                <div className="flex items-center justify-between w-full">
                  <span>{district.name}</span>
                  <span className="text-xs text-gray-500 capitalize ml-2">
                    {district.details?.type}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
