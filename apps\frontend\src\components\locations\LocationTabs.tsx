'use client'

import { <PERSON><PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Globe, MapPin, Building } from 'lucide-react'

interface LocationTabsProps {
  activeTab: 'countries' | 'states' | 'districts'
  onTabChange: (tab: 'countries' | 'states' | 'districts') => void
  selectedCountry: any
  selectedState: any
}

export function LocationTabs({ 
  activeTab, 
  onTabChange, 
  selectedCountry, 
  selectedState 
}: LocationTabsProps) {
  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={(value) => onTabChange(value as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="countries" className="flex items-center space-x-2">
            <Globe className="h-4 w-4" />
            <span>Countries</span>
          </TabsTrigger>
          <TabsTrigger
            value="states"
            className="flex items-center space-x-2"
          >
            <MapPin className="h-4 w-4" />
            <span>States</span>
            {selectedCountry && (
              <Badge variant="secondary" className="ml-1 text-xs">
                {selectedCountry.name}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger
            value="districts"
            className="flex items-center space-x-2"
          >
            <Building className="h-4 w-4" />
            <span>Districts</span>
            {selectedState ? (
              <Badge variant="secondary" className="ml-1 text-xs">
                {selectedState.name}
              </Badge>
            ) : selectedCountry ? (
              <Badge variant="outline" className="ml-1 text-xs">
                All
              </Badge>
            ) : null}
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Selection Info */}
      {(selectedCountry || selectedState) && (
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <span>Current selection:</span>
          {selectedCountry && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <Globe className="h-3 w-3" />
              <span>{selectedCountry.name}</span>
            </Badge>
          )}
          {selectedState && (
            <>
              <span>→</span>
              <Badge variant="outline" className="flex items-center space-x-1">
                <MapPin className="h-3 w-3" />
                <span>{selectedState.name}</span>
              </Badge>
            </>
          )}
        </div>
      )}
    </div>
  )
}
