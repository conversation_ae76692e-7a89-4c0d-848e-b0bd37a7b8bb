'use client'

import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Eye, Users, MapPin, Building } from 'lucide-react'
import { StateForm } from './StateForm'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { toast } from 'sonner'

interface StateCardProps {
  state: any
  onSelect: (state: any) => void
}

export function StateCard({ state, onSelect }: StateCardProps) {
  const { fetchStates, deleteState } = useLocationStore()

  const handleViewDistricts = () => {
    onSelect(state)
  }

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this state?')) {
      try {
        await deleteState(state.id)
        toast.success('State deleted successfully')
      } catch (error) {
        toast.error('Failed to delete state')
      }
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'state':
        return 'bg-blue-100 text-blue-800'
      case 'province':
        return 'bg-green-100 text-green-800'
      case 'territory':
        return 'bg-purple-100 text-purple-800'
      case 'region':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <MapPin className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-lg">{state.name}</h3>
              <div className="flex items-center space-x-2">
                {state.code && (
                  <p className="text-sm text-gray-500">{state.code}</p>
                )}
                <Badge 
                  variant="secondary" 
                  className={`text-xs capitalize ${getTypeColor(state.details?.type)}`}
                >
                  {state.details?.type || 'state'}
                </Badge>
              </div>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleViewDistricts}>
                <Eye className="h-4 w-4 mr-2" />
                View Districts
              </DropdownMenuItem>
              <StateForm
                mode="edit"
                state={state}
                onSuccess={() => fetchStates()}
                trigger={
                  <DropdownMenuItem>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                }
              />
              <DropdownMenuItem className="text-destructive" onClick={handleDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Basic Info */}
        <div className="grid grid-cols-1 gap-2 text-sm">
          {state.details?.capital && (
            <div>
              <span className="text-gray-500">Capital:</span>
              <p className="font-medium">{state.details.capital}</p>
            </div>
          )}
          {typeof state.country === 'object' && state.country?.name && (
            <div>
              <span className="text-gray-500">Country:</span>
              <p className="font-medium">{state.country.name}</p>
            </div>
          )}
        </div>

        {/* Statistics */}
        {(state.details?.population || state.details?.area) && (
          <div className="grid grid-cols-2 gap-2 text-sm">
            {state.details?.population && (
              <div className="flex items-center space-x-1">
                <Users className="h-3 w-3 text-gray-400" />
                <span>{formatNumber(state.details.population)}</span>
              </div>
            )}
            {state.details?.area && (
              <div className="flex items-center space-x-1">
                <MapPin className="h-3 w-3 text-gray-400" />
                <span>{formatNumber(state.details.area)} km²</span>
              </div>
            )}
          </div>
        )}

        {/* Status and Actions */}
        <div className="flex items-center justify-between pt-2">
          <Badge variant={state.isActive ? 'default' : 'secondary'}>
            {state.isActive ? 'Active' : 'Inactive'}
          </Badge>

          <Button
            variant="outline"
            size="sm"
            onClick={handleViewDistricts}
            className="flex items-center space-x-1"
          >
            <Building className="h-3 w-3" />
            <span>View Districts</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
