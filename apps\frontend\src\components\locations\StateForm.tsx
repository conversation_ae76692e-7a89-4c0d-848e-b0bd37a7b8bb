'use client'

import { useState, useEffect } from 'react'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Plus, Edit, Save, X } from 'lucide-react'
import { Formik, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { toast } from 'sonner'

// Validation schema using Yup
const validationSchema = Yup.object({
  name: Yup.string()
    .required('State name is required')
    .min(2, 'State name must be at least 2 characters')
    .max(100, 'State name must be less than 100 characters'),
  code: Yup.string()
    .max(10, 'State code must be at most 10 characters')
    .matches(/^[A-Z0-9]*$/, 'State code must contain only uppercase letters and numbers'),
  country: Yup.string()
    .required('Country is required'),
  capital: Yup.string()
    .max(100, 'Capital name must be less than 100 characters'),
  type: Yup.string()
    .oneOf(['state', 'province', 'territory', 'region'], 'Invalid type'),
  isActive: Yup.boolean(),
  priority: Yup.number()
    .min(0, 'Priority must be 0 or greater')
    .integer('Priority must be a whole number')
})

interface StateFormProps {
  state?: any
  mode: 'create' | 'edit'
  trigger?: React.ReactNode
  onSuccess?: () => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function StateForm({ state, mode, trigger, onSuccess, open: externalOpen, onOpenChange }: StateFormProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const open = externalOpen !== undefined ? externalOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen
  const { countries, createState, updateState, fetchCountries } = useLocationStore()

  useEffect(() => {
    // Load countries when component mounts
    if (countries.length === 0) {
      console.log('Loading countries...')
      fetchCountries()
    } else {
      console.log('Countries already loaded:', countries.length)
    }
  }, [])

  useEffect(() => {
    console.log('Countries updated:', countries)
  }, [countries])

  // Initial values for Formik
  const initialValues = {
    name: state?.name || '',
    code: state?.code || '',
    country: typeof state?.country === 'string' ? state.country : state?.country?.id || '',
    capital: state?.details?.capital || '',
    type: state?.details?.type || 'state',
    isActive: state?.isActive ?? true,
    priority: state?.priority || 0
  }

  const handleSubmit = async (values: any, { setSubmitting, resetForm }: any) => {
    try {
      // Prepare data for submission
      const submitData = {
        name: values.name,
        code: values.code.toUpperCase(),
        country: values.country, // Keep as string for now
        details: {
          capital: values.capital,
          type: values.type
        },
        isActive: values.isActive,
        priority: values.priority
      }

      console.log('Submitting state data:', submitData)
      console.log('Country value type:', typeof values.country, 'value:', values.country)

      if (mode === 'create') {
        await createState(submitData)
        toast.success('State created successfully')
        resetForm()
      } else {
        await updateState(state.id, submitData)
        toast.success('State updated successfully')
      }

      // Only close dialog on successful submission
      setOpen(false)
      onSuccess?.()
    } catch (error) {
      console.error('Form submission error:', error)
      toast.error('Failed to save state')
      // Don't close dialog on error - let user fix the issues
    } finally {
      setSubmitting(false)
    }
  }

  const defaultTrigger = (
    <Button size="sm" className="gap-2">
      {mode === 'create' ? <Plus className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
      {mode === 'create' ? 'Add State' : 'Edit State'}
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      // Only allow closing via Cancel button, not by clicking outside
      if (!newOpen) {
        // Don't auto-close
        return;
      }
      setOpen(newOpen);
    }} modal={true}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent
        className="max-w-2xl max-h-[90vh] overflow-y-auto"
        onPointerDownOutside={(e) => e.preventDefault()}
        onInteractOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
        onFocusOutside={(e) => e.preventDefault()}
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Add New State' : `Edit ${state?.name}`}
          </DialogTitle>
        </DialogHeader>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ values, errors, touched, handleChange, isSubmitting, setSubmitting, resetForm }) => (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="country">Country *</Label>
                    <Field name="country">
                      {({ field, form }: any) => {
                        console.log('Country field value:', field.value, 'type:', typeof field.value)
                        console.log('Available countries:', countries.map(c => ({ id: c.id, name: c.name, idType: typeof c.id })))

                        return (
                          <Select
                            value={field.value ? String(field.value) : ''}
                            onValueChange={(value) => {
                              form.setFieldValue('country', value)
                              console.log('Selected country:', value) // Debug log
                            }}
                          >
                            <SelectTrigger className={errors.country && touched.country ? 'border-red-500' : ''}>
                              <SelectValue placeholder="Select a country" />
                            </SelectTrigger>
                            <SelectContent>
                              {countries.map((country) => (
                                <SelectItem key={country.id} value={String(country.id)}>
                                  {country.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )
                      }}
                    </Field>
                    <ErrorMessage name="country" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">State Name *</Label>
                      <Field
                        as={Input}
                        id="name"
                        name="name"
                        placeholder="Enter state name"
                        className={errors.name && touched.name ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="name" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="code">State Code</Label>
                      <Field
                        as={Input}
                        id="code"
                        name="code"
                        placeholder="CA, TX, MH"
                        maxLength={10}
                        className={errors.code && touched.code ? 'border-red-500' : ''}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          handleChange({
                            target: {
                              name: 'code',
                              value: e.target.value.toUpperCase()
                            }
                          })
                        }}
                      />
                      <ErrorMessage name="code" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="capital">Capital</Label>
                      <Field
                        as={Input}
                        id="capital"
                        name="capital"
                        placeholder="Enter capital city"
                        className={errors.capital && touched.capital ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="capital" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="type">Type</Label>
                      <Field name="type">
                        {({ field, form }: any) => (
                          <Select
                            value={field.value}
                            onValueChange={(value) => form.setFieldValue('type', value)}
                          >
                            <SelectTrigger className={errors.type && touched.type ? 'border-red-500' : ''}>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="state">State</SelectItem>
                              <SelectItem value="province">Province</SelectItem>
                              <SelectItem value="territory">Territory</SelectItem>
                              <SelectItem value="region">Region</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                      </Field>
                      <ErrorMessage name="type" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isActive">Active Status</Label>
                      <p className="text-sm text-muted-foreground">
                        Enable this state for selection
                      </p>
                    </div>
                    <Field name="isActive">
                      {({ field }: any) => (
                        <Switch
                          id="isActive"
                          checked={field.value}
                          onCheckedChange={(checked) => {
                            handleChange({
                              target: {
                                name: 'isActive',
                                value: checked
                              }
                            })
                          }}
                        />
                      )}
                    </Field>
                  </div>

                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Field
                      as={Input}
                      id="priority"
                      name="priority"
                      type="number"
                      placeholder="0"
                      className={errors.priority && touched.priority ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="priority" component="div" className="text-red-500 text-sm mt-1" />
                    <p className="text-sm text-muted-foreground mt-1">
                      Higher priority states appear first in lists
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Form Actions */}
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="button"
                  disabled={isSubmitting}
                  onClick={() => handleSubmit(values, { setSubmitting, resetForm })}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSubmitting ? 'Saving...' : mode === 'create' ? 'Create State' : 'Update State'}
                </Button>
              </div>
            </div>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  )
}
