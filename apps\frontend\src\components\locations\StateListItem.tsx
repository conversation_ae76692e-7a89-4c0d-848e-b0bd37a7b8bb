'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Eye, Users, MapPin, Building } from 'lucide-react'
import { StateForm } from './StateForm'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { toast } from 'sonner'

interface StateListItemProps {
  state: any
  onSelect: (state: any) => void
}

export function StateListItem({ state, onSelect }: StateListItemProps) {
  const { fetchStates, deleteState } = useLocationStore()
  const [editDialogOpen, setEditDialogOpen] = useState(false)

  const handleViewDistricts = () => {
    onSelect(state)
  }

  const handleEdit = () => {
    setEditDialogOpen(true)
  }

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this state?')) {
      try {
        await deleteState(state.id)
        toast.success('State deleted successfully')
      } catch (error) {
        toast.error('Failed to delete state')
      }
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'state':
        return 'bg-blue-100 text-blue-800'
      case 'province':
        return 'bg-green-100 text-green-800'
      case 'territory':
        return 'bg-purple-100 text-purple-800'
      case 'region':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <>
    <Card className="hover:shadow-sm transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* State Info */}
          <div className="flex items-center space-x-4 flex-1">
            {/* Icon and Name */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <MapPin className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold">{state.name}</h3>
                <div className="flex items-center space-x-2">
                  {state.code && (
                    <p className="text-sm text-gray-500">{state.code}</p>
                  )}
                  <Badge 
                    variant="secondary" 
                    className={`text-xs capitalize ${getTypeColor(state.details?.type)}`}
                  >
                    {state.details?.type || 'state'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Details */}
            <div className="hidden md:flex items-center space-x-6 text-sm">
              {state.details?.capital && (
                <div>
                  <span className="text-gray-500">Capital: </span>
                  <span className="font-medium">{state.details.capital}</span>
                </div>
              )}
              {typeof state.country === 'object' && state.country?.name && (
                <div>
                  <span className="text-gray-500">Country: </span>
                  <span className="font-medium">{state.country.name}</span>
                </div>
              )}
            </div>

            {/* Statistics */}
            <div className="hidden lg:flex items-center space-x-4 text-sm">
              {state.details?.population && (
                <div className="flex items-center space-x-1">
                  <Users className="h-3 w-3 text-gray-400" />
                  <span>{formatNumber(state.details.population)}</span>
                </div>
              )}
              {state.details?.area && (
                <div className="flex items-center space-x-1">
                  <MapPin className="h-3 w-3 text-gray-400" />
                  <span>{formatNumber(state.details.area)} km²</span>
                </div>
              )}
            </div>
          </div>

          {/* Status and Actions */}
          <div className="flex items-center space-x-3">
            <Badge variant={state.isActive ? 'default' : 'secondary'}>
              {state.isActive ? 'Active' : 'Inactive'}
            </Badge>

            <Button
              variant="outline"
              size="sm"
              onClick={handleViewDistricts}
              className="flex items-center space-x-1"
            >
              <Building className="h-3 w-3" />
              <span className="hidden sm:inline">View Districts</span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleViewDistricts}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Districts
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem className="text-destructive" onClick={handleDelete}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Mobile Details */}
        <div className="md:hidden mt-3 pt-3 border-t border-gray-100">
          <div className="grid grid-cols-2 gap-2 text-sm">
            {state.details?.capital && (
              <div>
                <span className="text-gray-500">Capital:</span>
                <p className="font-medium">{state.details.capital}</p>
              </div>
            )}
            {typeof state.country === 'object' && state.country?.name && (
              <div>
                <span className="text-gray-500">Country:</span>
                <p className="font-medium">{state.country.name}</p>
              </div>
            )}
          </div>

          {(state.details?.population || state.details?.area) && (
            <div className="flex items-center space-x-4 mt-2 text-sm">
              {state.details?.population && (
                <div className="flex items-center space-x-1">
                  <Users className="h-3 w-3 text-gray-400" />
                  <span>{formatNumber(state.details.population)}</span>
                </div>
              )}
              {state.details?.area && (
                <div className="flex items-center space-x-1">
                  <MapPin className="h-3 w-3 text-gray-400" />
                  <span>{formatNumber(state.details.area)} km²</span>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>

    {/* Edit Dialog */}
    <StateForm
      mode="edit"
      state={state}
      open={editDialogOpen}
      onOpenChange={setEditDialogOpen}
      onSuccess={() => {
        fetchStates()
        setEditDialogOpen(false)
      }}
      trigger={<div style={{ display: 'none' }} />}
    />
    </>
  )
}
