'use client'

import { useLocationStore } from '@/stores/location/useLocationStore'
import { StateCard } from './StateCard'
import { StateListItem } from './StateListItem'
import { LocationPagination } from './LocationPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { MapPin, AlertTriangle } from 'lucide-react'

export function StatesList() {
  const {
    states,
    viewMode,
    statesPagination,
    isLoading,
    selectedCountry,
    fetchStates,
    setSelectedState
  } = useLocationStore()

  const handlePageChange = (page: number) => {
    fetchStates(selectedCountry?.id, page)
  }

  const handleStateSelect = (state: any) => {
    setSelectedState(state)
  }

  if (!selectedCountry) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Please select a country first to view its states and provinces.
        </AlertDescription>
      </Alert>
    )
  }

  if (isLoading && states.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (states.length === 0) {
    return (
      <EmptyState
        icon={MapPin}
        title="No states found"
        description={`No states found in ${selectedCountry.name}. Try adjusting your search criteria or add new states.`}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* States Grid/List */}
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {states.map((state) => (
            <StateCard
              key={state.id}
              state={state}
              onSelect={handleStateSelect}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {states.map((state) => (
            <StateListItem
              key={state.id}
              state={state}
              onSelect={handleStateSelect}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      <LocationPagination
        pagination={statesPagination}
        onPageChange={handlePageChange}
      />
    </div>
  )
}
