'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Search, X, Clock, TrendingUp, Filter, ArrowRight } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface SearchSuggestion {
  id: string
  title: string
  slug: string
  thumbnail?: string
  price: number
  rating: number
  category: string
  type: 'course' | 'category' | 'instructor'
}

interface RecentSearch {
  query: string
  timestamp: number
}

interface AdvancedSearchProps {
  placeholder?: string
  showFilters?: boolean
  onSearch?: (query: string) => void
  className?: string
}

export default function AdvancedSearch({
  placeholder = "Search for courses, instructors, or topics...",
  showFilters = true,
  onSearch,
  className = ""
}: AdvancedSearchProps) {
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([])
  const [trendingSearches, setTrendingSearches] = useState<string[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setRecentSearches(parsed.slice(0, 5)) // Keep only last 5
      } catch (error) {
        console.error('Error parsing recent searches:', error)
      }
    }

    // Load trending searches
    setTrendingSearches([
      'UPSC Preparation',
      'Web Development',
      'Data Science',
      'Digital Marketing',
      'Python Programming',
      'Banking Exam',
      'SSC CGL'
    ])
  }, [])

  // Fetch suggestions from API
  useEffect(() => {
    if (query.length < 2) {
      setSuggestions([])
      return
    }

    const debounceTimer = setTimeout(async () => {
      setLoading(true)
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/courses/search?q=${encodeURIComponent(query)}&limit=8`,
          { credentials: 'include' }
        )

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            setSuggestions(data.suggestions || [])
          }
        }
      } catch (error) {
        console.error('Search suggestions error:', error)
      } finally {
        setLoading(false)
      }
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [query])

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return

    const totalItems = suggestions.length + recentSearches.length + trendingSearches.length

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => (prev < totalItems - 1 ? prev + 1 : -1))
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => (prev > -1 ? prev - 1 : totalItems - 1))
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0) {
          handleSelectItem(selectedIndex)
        } else if (query.trim()) {
          handleSearch(query)
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  const handleSelectItem = (index: number) => {
    let selectedQuery = ''
    
    if (index < suggestions.length) {
      // Course suggestion selected
      const suggestion = suggestions[index]
      router.push(`/courses/${suggestion.slug}`)
      return
    }
    
    index -= suggestions.length
    
    if (index < recentSearches.length) {
      // Recent search selected
      selectedQuery = recentSearches[index].query
    } else {
      // Trending search selected
      index -= recentSearches.length
      selectedQuery = trendingSearches[index]
    }

    if (selectedQuery) {
      setQuery(selectedQuery)
      handleSearch(selectedQuery)
    }
  }

  const handleSearch = (searchQuery: string) => {
    const trimmedQuery = searchQuery.trim()
    if (!trimmedQuery) return

    // Save to recent searches
    const newSearch: RecentSearch = {
      query: trimmedQuery,
      timestamp: Date.now()
    }

    const updatedRecent = [
      newSearch,
      ...recentSearches.filter(s => s.query !== trimmedQuery)
    ].slice(0, 5)

    setRecentSearches(updatedRecent)
    localStorage.setItem('recentSearches', JSON.stringify(updatedRecent))

    // Close dropdown
    setIsOpen(false)
    setSelectedIndex(-1)

    // Execute search
    if (onSearch) {
      onSearch(trimmedQuery)
    } else {
      router.push(`/courses?search=${encodeURIComponent(trimmedQuery)}`)
    }
  }

  const clearRecentSearches = () => {
    setRecentSearches([])
    localStorage.removeItem('recentSearches')
  }

  const removeRecentSearch = (queryToRemove: string) => {
    const updated = recentSearches.filter(s => s.query !== queryToRemove)
    setRecentSearches(updated)
    localStorage.setItem('recentSearches', JSON.stringify(updated))
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span
        key={i}
        className={`text-xs ${i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'}`}
      >
        ★
      </span>
    ))
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
        />
        
        {/* Clear Button */}
        {query && (
          <button
            onClick={() => {
              setQuery('')
              setSuggestions([])
              inputRef.current?.focus()
            }}
            className="absolute inset-y-0 right-8 flex items-center pr-3"
          >
            <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
          </button>
        )}

        {/* Filter Button */}
        {showFilters && (
          <button className="absolute inset-y-0 right-0 flex items-center pr-3">
            <Filter className="h-5 w-5 text-gray-400 hover:text-gray-600" />
          </button>
        )}

        {/* Loading Indicator */}
        {loading && (
          <div className="absolute inset-y-0 right-3 flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
          </div>
        )}
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {/* Course Suggestions */}
          {suggestions.length > 0 && (
            <div className="p-2">
              <div className="text-xs font-medium text-gray-500 px-3 py-2 uppercase tracking-wide">
                Courses
              </div>
              {suggestions.map((suggestion, index) => (
                <button
                  key={suggestion.id}
                  onClick={() => handleSelectItem(index)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left hover:bg-gray-50 transition-colors duration-200 ${
                    selectedIndex === index ? 'bg-green-50 border-l-2 border-green-500' : ''
                  }`}
                >
                  {/* Thumbnail */}
                  <div className="w-12 h-8 bg-gray-200 rounded overflow-hidden flex-shrink-0">
                    {suggestion.thumbnail ? (
                      <img
                        src={suggestion.thumbnail}
                        alt={suggestion.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Search className="h-3 w-3 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {suggestion.title}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs text-gray-500">
                        {suggestion.category.replace('_', ' ').toUpperCase()}
                      </span>
                      <div className="flex items-center">
                        {renderStars(suggestion.rating)}
                        <span className="text-xs text-gray-500 ml-1">
                          {suggestion.rating.toFixed(1)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Price */}
                  <div className="text-sm font-medium text-green-600">
                    {formatPrice(suggestion.price)}
                  </div>

                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </button>
              ))}
            </div>
          )}

          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <div className="border-t border-gray-100 p-2">
              <div className="flex items-center justify-between px-3 py-2">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Recent Searches
                </div>
                <button
                  onClick={clearRecentSearches}
                  className="text-xs text-gray-400 hover:text-gray-600"
                >
                  Clear all
                </button>
              </div>
              {recentSearches.map((recent, index) => {
                const actualIndex = suggestions.length + index
                return (
                  <button
                    key={recent.timestamp}
                    onClick={() => handleSelectItem(actualIndex)}
                    className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-left hover:bg-gray-50 transition-colors duration-200 group ${
                      selectedIndex === actualIndex ? 'bg-green-50 border-l-2 border-green-500' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-700">{recent.query}</span>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        removeRecentSearch(recent.query)
                      }}
                      className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 rounded transition-all duration-200"
                    >
                      <X className="h-3 w-3 text-gray-400" />
                    </button>
                  </button>
                )
              })}
            </div>
          )}

          {/* Trending Searches */}
          {trendingSearches.length > 0 && query.length < 2 && (
            <div className="border-t border-gray-100 p-2">
              <div className="text-xs font-medium text-gray-500 px-3 py-2 uppercase tracking-wide">
                Trending Searches
              </div>
              {trendingSearches.map((trending, index) => {
                const actualIndex = suggestions.length + recentSearches.length + index
                return (
                  <button
                    key={trending}
                    onClick={() => handleSelectItem(actualIndex)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left hover:bg-gray-50 transition-colors duration-200 ${
                      selectedIndex === actualIndex ? 'bg-green-50 border-l-2 border-green-500' : ''
                    }`}
                  >
                    <TrendingUp className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-700">{trending}</span>
                  </button>
                )
              })}
            </div>
          )}

          {/* No Results */}
          {query.length >= 2 && !loading && suggestions.length === 0 && (
            <div className="p-4 text-center">
              <Search className="h-8 w-8 text-gray-300 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No courses found for "{query}"</p>
              <button
                onClick={() => handleSearch(query)}
                className="mt-2 text-sm text-green-600 hover:text-green-700 underline"
              >
                Search anyway
              </button>
            </div>
          )}

          {/* Empty State */}
          {query.length < 2 && suggestions.length === 0 && recentSearches.length === 0 && (
            <div className="p-4 text-center">
              <Search className="h-8 w-8 text-gray-300 mx-auto mb-2" />
              <p className="text-sm text-gray-500">Start typing to search for courses</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
