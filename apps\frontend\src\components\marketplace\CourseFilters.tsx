'use client'

import React, { useState, useEffect } from 'react'
import { ChevronDown, ChevronUp, X, Star, Clock, DollarSign, Globe, Award, Filter } from 'lucide-react'

interface FilterOptions {
  category: string
  level: string
  priceRange: string
  duration: string
  rating: string
  language: string
  features: string[]
}

interface CourseFiltersProps {
  filters: FilterOptions
  onFiltersChange: (filters: FilterOptions) => void
  onClearFilters: () => void
  courseCount?: number
  showApplyButton?: boolean
}

export default function CourseFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  courseCount = 0,
  showApplyButton = false
}: CourseFiltersProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>([
    'category', 'level', 'price', 'rating'
  ])
  const [tempFilters, setTempFilters] = useState<FilterOptions>(filters)

  useEffect(() => {
    setTempFilters(filters)
  }, [filters])

  const toggleSection = (section: string) => {
    setExpandedSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    )
  }

  const handleFilterChange = (key: keyof FilterOptions, value: string | string[]) => {
    const newFilters = { ...tempFilters, [key]: value }
    setTempFilters(newFilters)
    
    if (!showApplyButton) {
      onFiltersChange(newFilters)
    }
  }

  const handleFeatureToggle = (feature: string) => {
    const currentFeatures = tempFilters.features || []
    const newFeatures = currentFeatures.includes(feature)
      ? currentFeatures.filter(f => f !== feature)
      : [...currentFeatures, feature]
    
    handleFilterChange('features', newFeatures)
  }

  const applyFilters = () => {
    onFiltersChange(tempFilters)
  }

  const clearAllFilters = () => {
    const emptyFilters: FilterOptions = {
      category: '',
      level: '',
      priceRange: '',
      duration: '',
      rating: '',
      language: '',
      features: []
    }
    setTempFilters(emptyFilters)
    onClearFilters()
  }

  const hasActiveFilters = Object.values(tempFilters).some(value => 
    Array.isArray(value) ? value.length > 0 : value !== ''
  )

  const categories = [
    'Programming & Development',
    'Business & Management',
    'Design & Creative',
    'Marketing & Sales',
    'Data Science & Analytics',
    'Personal Development',
    'Language Learning',
    'Health & Fitness',
    'Music & Arts',
    'Academic Subjects'
  ]

  const levels = [
    { value: 'beginner', label: 'Beginner' },
    { value: 'intermediate', label: 'Intermediate' },
    { value: 'advanced', label: 'Advanced' }
  ]

  const priceRanges = [
    { value: 'free', label: 'Free' },
    { value: '0-999', label: 'Under ₹1,000' },
    { value: '1000-2999', label: '₹1,000 - ₹2,999' },
    { value: '3000-4999', label: '₹3,000 - ₹4,999' },
    { value: '5000-9999', label: '₹5,000 - ₹9,999' },
    { value: '10000+', label: '₹10,000+' }
  ]

  const durations = [
    { value: '0-2', label: 'Under 2 hours' },
    { value: '2-6', label: '2-6 hours' },
    { value: '6-17', label: '6-17 hours' },
    { value: '17+', label: '17+ hours' }
  ]

  const ratings = [
    { value: '4.5', label: '4.5 & up' },
    { value: '4.0', label: '4.0 & up' },
    { value: '3.5', label: '3.5 & up' },
    { value: '3.0', label: '3.0 & up' }
  ]

  const languages = [
    'English',
    'Hindi',
    'Spanish',
    'French',
    'German',
    'Chinese',
    'Japanese',
    'Portuguese',
    'Russian',
    'Arabic'
  ]

  const features = [
    { value: 'certificate', label: 'Certificate of Completion', icon: Award },
    { value: 'subtitles', label: 'Subtitles Available', icon: Globe },
    { value: 'mobile_access', label: 'Mobile Access', icon: Globe },
    { value: 'lifetime_access', label: 'Lifetime Access', icon: Clock },
    { value: 'downloadable', label: 'Downloadable Content', icon: Globe },
    { value: 'assignments', label: 'Assignments & Projects', icon: Globe },
    { value: 'quizzes', label: 'Quizzes & Tests', icon: Globe },
    { value: 'live_sessions', label: 'Live Sessions', icon: Globe }
  ]

  const FilterSection = ({ 
    title, 
    section, 
    icon: Icon, 
    children 
  }: { 
    title: string
    section: string
    icon: any
    children: React.ReactNode 
  }) => {
    const isExpanded = expandedSections.includes(section)
    
    return (
      <div className="border-b border-gray-200 pb-4 mb-4">
        <button
          onClick={() => toggleSection(section)}
          className="flex items-center justify-between w-full text-left font-medium text-gray-900 hover:text-blue-600 transition-colors"
        >
          <div className="flex items-center gap-2">
            <Icon className="h-4 w-4" />
            {title}
          </div>
          {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </button>
        
        {isExpanded && (
          <div className="mt-3 space-y-2">
            {children}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-600" />
          <h3 className="font-semibold text-gray-900">Filters</h3>
          {courseCount > 0 && (
            <span className="text-sm text-gray-500">({courseCount} courses)</span>
          )}
        </div>
        
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            Clear All
          </button>
        )}
      </div>

      {/* Category Filter */}
      <FilterSection title="Category" section="category" icon={Filter}>
        <div className="space-y-2">
          {categories.map(category => (
            <label key={category} className="flex items-center">
              <input
                type="radio"
                name="category"
                value={category}
                checked={tempFilters.category === category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="mr-2 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">{category}</span>
            </label>
          ))}
        </div>
      </FilterSection>

      {/* Level Filter */}
      <FilterSection title="Level" section="level" icon={Star}>
        <div className="space-y-2">
          {levels.map(level => (
            <label key={level.value} className="flex items-center">
              <input
                type="radio"
                name="level"
                value={level.value}
                checked={tempFilters.level === level.value}
                onChange={(e) => handleFilterChange('level', e.target.value)}
                className="mr-2 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">{level.label}</span>
            </label>
          ))}
        </div>
      </FilterSection>

      {/* Price Filter */}
      <FilterSection title="Price" section="price" icon={DollarSign}>
        <div className="space-y-2">
          {priceRanges.map(range => (
            <label key={range.value} className="flex items-center">
              <input
                type="radio"
                name="priceRange"
                value={range.value}
                checked={tempFilters.priceRange === range.value}
                onChange={(e) => handleFilterChange('priceRange', e.target.value)}
                className="mr-2 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">{range.label}</span>
            </label>
          ))}
        </div>
      </FilterSection>

      {/* Duration Filter */}
      <FilterSection title="Duration" section="duration" icon={Clock}>
        <div className="space-y-2">
          {durations.map(duration => (
            <label key={duration.value} className="flex items-center">
              <input
                type="radio"
                name="duration"
                value={duration.value}
                checked={tempFilters.duration === duration.value}
                onChange={(e) => handleFilterChange('duration', e.target.value)}
                className="mr-2 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">{duration.label}</span>
            </label>
          ))}
        </div>
      </FilterSection>

      {/* Rating Filter */}
      <FilterSection title="Rating" section="rating" icon={Star}>
        <div className="space-y-2">
          {ratings.map(rating => (
            <label key={rating.value} className="flex items-center">
              <input
                type="radio"
                name="rating"
                value={rating.value}
                checked={tempFilters.rating === rating.value}
                onChange={(e) => handleFilterChange('rating', e.target.value)}
                className="mr-2 text-blue-600 focus:ring-blue-500"
              />
              <div className="flex items-center gap-1">
                <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                <span className="text-sm text-gray-700">{rating.label}</span>
              </div>
            </label>
          ))}
        </div>
      </FilterSection>

      {/* Language Filter */}
      <FilterSection title="Language" section="language" icon={Globe}>
        <div className="space-y-2">
          {languages.map(language => (
            <label key={language} className="flex items-center">
              <input
                type="radio"
                name="language"
                value={language}
                checked={tempFilters.language === language}
                onChange={(e) => handleFilterChange('language', e.target.value)}
                className="mr-2 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">{language}</span>
            </label>
          ))}
        </div>
      </FilterSection>

      {/* Features Filter */}
      <FilterSection title="Features" section="features" icon={Award}>
        <div className="space-y-2">
          {features.map(feature => (
            <label key={feature.value} className="flex items-center">
              <input
                type="checkbox"
                value={feature.value}
                checked={tempFilters.features.includes(feature.value)}
                onChange={() => handleFeatureToggle(feature.value)}
                className="mr-2 text-blue-600 focus:ring-blue-500"
              />
              <div className="flex items-center gap-2">
                <feature.icon className="h-3 w-3 text-gray-500" />
                <span className="text-sm text-gray-700">{feature.label}</span>
              </div>
            </label>
          ))}
        </div>
      </FilterSection>

      {/* Apply Button (if needed) */}
      {showApplyButton && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <button
            onClick={applyFilters}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Apply Filters
          </button>
        </div>
      )}
    </div>
  )
}
