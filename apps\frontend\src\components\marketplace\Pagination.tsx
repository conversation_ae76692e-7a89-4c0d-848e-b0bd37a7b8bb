'use client'

import React from 'react'
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react'

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  showPageNumbers?: boolean
  showFirstLast?: boolean
  showPrevNext?: boolean
  maxVisiblePages?: number
  className?: string
}

export default function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  showPageNumbers = true,
  showFirstLast = true,
  showPrevNext = true,
  maxVisiblePages = 7,
  className = ''
}: PaginationProps) {
  if (totalPages <= 1) return null

  const getVisiblePages = () => {
    const pages: (number | string)[] = []
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Calculate which pages to show
      const halfVisible = Math.floor(maxVisiblePages / 2)
      let startPage = Math.max(1, currentPage - halfVisible)
      let endPage = Math.min(totalPages, currentPage + halfVisible)

      // Adjust if we're near the beginning or end
      if (currentPage <= halfVisible) {
        endPage = Math.min(totalPages, maxVisiblePages)
      } else if (currentPage >= totalPages - halfVisible) {
        startPage = Math.max(1, totalPages - maxVisiblePages + 1)
      }

      // Add first page and ellipsis if needed
      if (startPage > 1) {
        pages.push(1)
        if (startPage > 2) {
          pages.push('...')
        }
      }

      // Add visible pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i)
      }

      // Add ellipsis and last page if needed
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push('...')
        }
        pages.push(totalPages)
      }
    }

    return pages
  }

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page)
    }
  }

  const visiblePages = getVisiblePages()

  const buttonBaseClass = "relative inline-flex items-center px-4 py-2 text-sm font-medium transition-colors duration-200"
  const activeButtonClass = "z-10 bg-blue-600 text-white border-blue-600 hover:bg-blue-700"
  const inactiveButtonClass = "bg-white text-gray-500 border-gray-300 hover:bg-gray-50 hover:text-gray-700"
  const disabledButtonClass = "bg-gray-100 text-gray-400 cursor-not-allowed"

  return (
    <nav className={`flex items-center justify-between ${className}`} aria-label="Pagination">
      {/* Mobile View */}
      <div className="flex flex-1 justify-between sm:hidden">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`${buttonBaseClass} rounded-md border ${
            currentPage === 1 ? disabledButtonClass : inactiveButtonClass
          }`}
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Previous
        </button>
        
        <span className="flex items-center text-sm text-gray-700">
          Page {currentPage} of {totalPages}
        </span>
        
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`${buttonBaseClass} rounded-md border ${
            currentPage === totalPages ? disabledButtonClass : inactiveButtonClass
          }`}
        >
          Next
          <ChevronRight className="h-4 w-4 ml-1" />
        </button>
      </div>

      {/* Desktop View */}
      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        {/* Results Info */}
        <div>
          <p className="text-sm text-gray-700">
            Page <span className="font-medium">{currentPage}</span> of{' '}
            <span className="font-medium">{totalPages}</span>
          </p>
        </div>

        {/* Pagination Controls */}
        <div className="flex items-center">
          <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
            {/* First Page Button */}
            {showFirstLast && currentPage > 1 && (
              <button
                onClick={() => handlePageChange(1)}
                className={`${buttonBaseClass} rounded-l-md border ${inactiveButtonClass}`}
                aria-label="Go to first page"
              >
                First
              </button>
            )}

            {/* Previous Button */}
            {showPrevNext && (
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`${buttonBaseClass} ${
                  !showFirstLast || currentPage === 1 ? 'rounded-l-md' : ''
                } border ${
                  currentPage === 1 ? disabledButtonClass : inactiveButtonClass
                }`}
                aria-label="Go to previous page"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
            )}

            {/* Page Numbers */}
            {showPageNumbers && visiblePages.map((page, index) => {
              if (page === '...') {
                return (
                  <span
                    key={`ellipsis-${index}`}
                    className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </span>
                )
              }

              const pageNumber = page as number
              const isActive = pageNumber === currentPage

              return (
                <button
                  key={pageNumber}
                  onClick={() => handlePageChange(pageNumber)}
                  className={`${buttonBaseClass} border ${
                    isActive ? activeButtonClass : inactiveButtonClass
                  }`}
                  aria-label={`Go to page ${pageNumber}`}
                  aria-current={isActive ? 'page' : undefined}
                >
                  {pageNumber}
                </button>
              )
            })}

            {/* Next Button */}
            {showPrevNext && (
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`${buttonBaseClass} ${
                  !showFirstLast || currentPage === totalPages ? 'rounded-r-md' : ''
                } border ${
                  currentPage === totalPages ? disabledButtonClass : inactiveButtonClass
                }`}
                aria-label="Go to next page"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            )}

            {/* Last Page Button */}
            {showFirstLast && currentPage < totalPages && (
              <button
                onClick={() => handlePageChange(totalPages)}
                className={`${buttonBaseClass} rounded-r-md border ${inactiveButtonClass}`}
                aria-label="Go to last page"
              >
                Last
              </button>
            )}
          </nav>
        </div>
      </div>
    </nav>
  )
}

// Quick pagination component for simple use cases
export function SimplePagination({
  currentPage,
  totalPages,
  onPageChange,
  className = ''
}: {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  className?: string
}) {
  return (
    <Pagination
      currentPage={currentPage}
      totalPages={totalPages}
      onPageChange={onPageChange}
      showPageNumbers={false}
      showFirstLast={false}
      className={className}
    />
  )
}

// Compact pagination for tight spaces
export function CompactPagination({
  currentPage,
  totalPages,
  onPageChange,
  className = ''
}: {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  className?: string
}) {
  return (
    <Pagination
      currentPage={currentPage}
      totalPages={totalPages}
      onPageChange={onPageChange}
      showFirstLast={false}
      maxVisiblePages={5}
      className={className}
    />
  )
}
