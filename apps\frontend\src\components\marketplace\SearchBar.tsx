'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Search, X, Filter, TrendingUp, Clock, Star } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface SearchSuggestion {
  id: string
  title: string
  type: 'course' | 'instructor' | 'category' | 'topic'
  category?: string
  rating?: number
  enrollments?: number
  thumbnail?: string
}

interface SearchBarProps {
  onSearch: (query: string) => void
  placeholder?: string
  showAdvancedSearch?: boolean
  showSuggestions?: boolean
  initialValue?: string
  className?: string
}

export default function SearchBar({
  onSearch,
  placeholder = "Search courses, instructors, topics...",
  showAdvancedSearch = false,
  showSuggestions = true,
  initialValue = '',
  className = ''
}: SearchBarProps) {
  const [query, setQuery] = useState(initialValue)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [showSuggestionsList, setShowSuggestionsList] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [popularSearches] = useState<string[]>([
    'React Development',
    'Python Programming',
    'Digital Marketing',
    'Data Science',
    'UI/UX Design',
    'Machine Learning',
    'JavaScript',
    'Business Analytics'
  ])

  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestionsList(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  useEffect(() => {
    if (query.length > 2 && showSuggestions) {
      const debounceTimer = setTimeout(() => {
        fetchSuggestions(query)
      }, 300)

      return () => clearTimeout(debounceTimer)
    } else {
      setSuggestions([])
    }
  }, [query, showSuggestions])

  const fetchSuggestions = async (searchQuery: string) => {
    setIsLoading(true)
    try {
      // Mock API call - replace with actual API
      const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(searchQuery)}`)
      if (response.ok) {
        const data = await response.json()
        setSuggestions(data.suggestions || [])
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error)
      // Mock suggestions for demo
      const mockSuggestions: SearchSuggestion[] = [
        {
          id: '1',
          title: `${searchQuery} for Beginners`,
          type: 'course',
          category: 'Programming',
          rating: 4.5,
          enrollments: 1200
        },
        {
          id: '2',
          title: `Advanced ${searchQuery}`,
          type: 'course',
          category: 'Programming',
          rating: 4.7,
          enrollments: 800
        },
        {
          id: '3',
          title: `${searchQuery} Instructor`,
          type: 'instructor',
          rating: 4.8
        },
        {
          id: '4',
          title: searchQuery,
          type: 'topic'
        }
      ]
      setSuggestions(mockSuggestions)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (searchQuery: string = query) => {
    if (searchQuery.trim()) {
      // Add to recent searches
      const updatedRecent = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5)
      setRecentSearches(updatedRecent)
      localStorage.setItem('recentSearches', JSON.stringify(updatedRecent))

      onSearch(searchQuery.trim())
      setShowSuggestionsList(false)
      
      // Update URL
      const params = new URLSearchParams(window.location.search)
      params.set('search', searchQuery.trim())
      router.push(`${window.location.pathname}?${params.toString()}`)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    } else if (e.key === 'Escape') {
      setShowSuggestionsList(false)
      inputRef.current?.blur()
    }
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    if (suggestion.type === 'course') {
      router.push(`/courses/${suggestion.id}`)
    } else if (suggestion.type === 'instructor') {
      router.push(`/instructors/${suggestion.id}`)
    } else {
      setQuery(suggestion.title)
      handleSearch(suggestion.title)
    }
  }

  const clearSearch = () => {
    setQuery('')
    setSuggestions([])
    setShowSuggestionsList(false)
    onSearch('')
    inputRef.current?.focus()
  }

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'course': return '📚'
      case 'instructor': return '👨‍🏫'
      case 'category': return '📁'
      case 'topic': return '🏷️'
      default: return '🔍'
    }
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyPress={handleKeyPress}
          onFocus={() => setShowSuggestionsList(true)}
          placeholder={placeholder}
          className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
        />

        <div className="absolute inset-y-0 right-0 flex items-center">
          {query && (
            <button
              onClick={clearSearch}
              className="p-2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
          
          {showAdvancedSearch && (
            <button
              onClick={() => router.push('/search/advanced')}
              className="p-2 text-gray-400 hover:text-gray-600 border-l border-gray-300"
              title="Advanced Search"
            >
              <Filter className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestionsList && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {/* Loading State */}
          {isLoading && (
            <div className="p-4 text-center text-gray-500">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <span className="text-sm mt-2">Searching...</span>
            </div>
          )}

          {/* Search Suggestions */}
          {!isLoading && suggestions.length > 0 && (
            <div className="py-2">
              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
                Suggestions
              </div>
              {suggestions.map((suggestion) => (
                <button
                  key={suggestion.id}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                >
                  <span className="text-lg">{getSuggestionIcon(suggestion.type)}</span>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{suggestion.title}</div>
                    {suggestion.category && (
                      <div className="text-xs text-gray-500">{suggestion.category}</div>
                    )}
                    {suggestion.rating && (
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        {typeof suggestion.rating === 'object'
                          ? suggestion.rating.average?.toFixed(1) || 0
                          : suggestion.rating
                        }
                        {suggestion.enrollments && (
                          <span className="ml-2">{suggestion.enrollments.toLocaleString()} students</span>
                        )}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* Recent Searches */}
          {!isLoading && query.length === 0 && recentSearches.length > 0 && (
            <div className="py-2 border-t border-gray-100">
              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide flex items-center gap-2">
                <Clock className="h-3 w-3" />
                Recent Searches
              </div>
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setQuery(search)
                    handleSearch(search)
                  }}
                  className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                >
                  <Search className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-700">{search}</span>
                </button>
              ))}
            </div>
          )}

          {/* Popular Searches */}
          {!isLoading && query.length === 0 && (
            <div className="py-2 border-t border-gray-100">
              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide flex items-center gap-2">
                <TrendingUp className="h-3 w-3" />
                Popular Searches
              </div>
              {popularSearches.slice(0, 5).map((search, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setQuery(search)
                    handleSearch(search)
                  }}
                  className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                >
                  <TrendingUp className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-700">{search}</span>
                </button>
              ))}
            </div>
          )}

          {/* No Results */}
          {!isLoading && query.length > 2 && suggestions.length === 0 && (
            <div className="p-4 text-center text-gray-500">
              <Search className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No suggestions found for "{query}"</p>
              <button
                onClick={() => handleSearch()}
                className="mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Search anyway
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
