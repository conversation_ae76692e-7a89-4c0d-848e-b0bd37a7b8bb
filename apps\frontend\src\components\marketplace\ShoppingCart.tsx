'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { useCartStore, CartItem } from '@/stores/cart/useCartStore'
import { X, Plus, Minus, ShoppingBag, CreditCard, Trash2 } from 'lucide-react'

interface ShoppingCartProps {
  isOpen?: boolean
  onClose?: () => void
  showCheckout?: boolean
}

export default function ShoppingCart({ 
  isOpen = false, 
  onClose, 
  showCheckout = true 
}: ShoppingCartProps) {
  const { 
    items, 
    removeItem, 
    updateQuantity, 
    clearCart, 
    getTotalItems, 
    getTotalPrice 
  } = useCartStore()
  
  const [isCheckingOut, setIsCheckingOut] = useState(false)

  const formatPrice = (price: number, currency: string) => {
    const formatter = new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    })
    return formatter.format(price)
  }

  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeItem(id)
    } else {
      updateQuantity(id, newQuantity)
    }
  }

  const handleCheckout = async () => {
    setIsCheckingOut(true)
    try {
      // Implement checkout logic here
      // This would typically involve:
      // 1. Validate cart items
      // 2. Create order
      // 3. Process payment
      // 4. Redirect to success page
      
      console.log('Processing checkout for items:', items)
      
      // Simulate checkout process
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // For demo purposes, just show success message
      alert('Checkout successful! (Demo)')
      clearCart()
      onClose?.()
    } catch (error) {
      console.error('Checkout error:', error)
      alert('Checkout failed. Please try again.')
    } finally {
      setIsCheckingOut(false)
    }
  }

  const CartItemComponent = ({ item }: { item: CartItem }) => (
    <div className="flex items-center space-x-4 p-4 border-b border-gray-200">
      {/* Thumbnail */}
      <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
        {item.thumbnail ? (
          <img
            src={item.thumbnail}
            alt={item.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <ShoppingBag className="h-6 w-6 text-gray-400" />
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 truncate">
          {item.title}
        </h4>
        <p className="text-sm text-gray-500 truncate">
          by {item.instructor}
        </p>
        <p className="text-sm text-gray-500">
          {item.institute}
        </p>
      </div>

      {/* Quantity Controls */}
      <div className="flex items-center space-x-2">
        <button
          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
          className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
        >
          <Minus className="h-4 w-4 text-gray-600" />
        </button>
        <span className="w-8 text-center text-sm font-medium">
          {item.quantity}
        </span>
        <button
          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
          className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
        >
          <Plus className="h-4 w-4 text-gray-600" />
        </button>
      </div>

      {/* Price */}
      <div className="text-right">
        <p className="text-sm font-medium text-gray-900">
          {formatPrice(item.price * item.quantity, item.currency)}
        </p>
        {item.originalPrice && item.originalPrice > item.price && (
          <p className="text-xs text-gray-500 line-through">
            {formatPrice(item.originalPrice * item.quantity, item.currency)}
          </p>
        )}
      </div>

      {/* Remove Button */}
      <button
        onClick={() => removeItem(item.id)}
        className="p-1 rounded-full hover:bg-red-100 transition-colors duration-200 text-gray-400 hover:text-red-600"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  )

  // Sidebar Cart (for mobile/overlay)
  if (isOpen) {
    return (
      <div className="fixed inset-0 z-50 overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
        <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                Shopping Cart ({getTotalItems()})
              </h2>
              <button
                onClick={onClose}
                className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
              >
                <X className="h-5 w-5 text-gray-600" />
              </button>
            </div>

            {/* Cart Items */}
            <div className="flex-1 overflow-y-auto">
              {items.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                  <ShoppingBag className="h-16 w-16 text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Your cart is empty
                  </h3>
                  <p className="text-gray-500 mb-4">
                    Add some courses to get started
                  </p>
                  <Link
                    href="/courses"
                    onClick={onClose}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
                  >
                    Browse Courses
                  </Link>
                </div>
              ) : (
                <div>
                  {items.map((item) => (
                    <CartItemComponent key={item.id} item={item} />
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {items.length > 0 && (
              <div className="border-t border-gray-200 p-4 space-y-4">
                {/* Total */}
                <div className="flex items-center justify-between">
                  <span className="text-lg font-semibold text-gray-900">Total:</span>
                  <span className="text-lg font-bold text-green-600">
                    {formatPrice(getTotalPrice(), items[0]?.currency || 'INR')}
                  </span>
                </div>

                {/* Actions */}
                <div className="space-y-2">
                  {showCheckout && (
                    <button
                      onClick={handleCheckout}
                      disabled={isCheckingOut}
                      className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                      <CreditCard className="h-5 w-5 mr-2" />
                      {isCheckingOut ? 'Processing...' : 'Proceed to Checkout'}
                    </button>
                  )}
                  
                  <div className="flex space-x-2">
                    <Link
                      href="/cart"
                      onClick={onClose}
                      className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:border-green-600 hover:text-green-600 transition-colors duration-200 text-center"
                    >
                      View Cart
                    </Link>
                    <button
                      onClick={clearCart}
                      className="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200 flex items-center"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Full Page Cart
  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Shopping Cart ({getTotalItems()} items)
        </h1>
        {items.length > 0 && (
          <button
            onClick={clearCart}
            className="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200 flex items-center"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear Cart
          </button>
        )}
      </div>

      {items.length === 0 ? (
        <div className="text-center py-16">
          <ShoppingBag className="h-24 w-24 text-gray-300 mx-auto mb-6" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            Your cart is empty
          </h2>
          <p className="text-gray-600 mb-8">
            Looks like you haven't added any courses to your cart yet.
          </p>
          <Link
            href="/courses"
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
          >
            <ShoppingBag className="h-5 w-5 mr-2" />
            Browse Courses
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">Course Items</h2>
              </div>
              <div>
                {items.map((item) => (
                  <CartItemComponent key={item.id} item={item} />
                ))}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>
              
              <div className="space-y-4 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal ({getTotalItems()} items)</span>
                  <span className="font-medium">
                    {formatPrice(getTotalPrice(), items[0]?.currency || 'INR')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Taxes</span>
                  <span className="font-medium">Calculated at checkout</span>
                </div>
                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between">
                    <span className="text-lg font-semibold text-gray-900">Total</span>
                    <span className="text-lg font-bold text-green-600">
                      {formatPrice(getTotalPrice(), items[0]?.currency || 'INR')}
                    </span>
                  </div>
                </div>
              </div>

              {showCheckout && (
                <button
                  onClick={handleCheckout}
                  disabled={isCheckingOut}
                  className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center mb-4"
                >
                  <CreditCard className="h-5 w-5 mr-2" />
                  {isCheckingOut ? 'Processing...' : 'Proceed to Checkout'}
                </button>
              )}

              <Link
                href="/courses"
                className="block w-full px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:border-green-600 hover:text-green-600 transition-colors duration-200 text-center"
              >
                Continue Shopping
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
