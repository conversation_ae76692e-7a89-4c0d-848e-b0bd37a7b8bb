'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { useWishlistStore, WishlistItem } from '@/stores/wishlist/useWishlistStore'
import { useCartStore } from '@/stores/cart/useCartStore'
import { Heart, ShoppingCart, X, Star, Clock, Users, Filter, Grid, List, Trash2 } from 'lucide-react'

interface WishlistProps {
  showHeader?: boolean
  viewMode?: 'grid' | 'list'
}

export default function Wishlist({ showHeader = true, viewMode: initialViewMode = 'grid' }: WishlistProps) {
  const { 
    items, 
    removeItem, 
    clearWishlist, 
    getTotalItems,
    getItemsByCategory,
    getItemsByPriceRange
  } = useWishlistStore()
  
  const { addItem: addToCart, isItemInCart } = useCartStore()
  
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(initialViewMode)
  const [sortBy, setSortBy] = useState('recent')
  const [filterCategory, setFilterCategory] = useState('')
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000])

  const formatPrice = (price: number, currency: string) => {
    const formatter = new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    })
    return formatter.format(price)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300'
        }`}
      />
    ))
  }

  const handleMoveToCart = (item: WishlistItem) => {
    addToCart({
      id: item.id,
      title: item.title,
      slug: item.slug,
      price: item.price,
      originalPrice: item.originalPrice,
      currency: item.currency,
      thumbnail: item.thumbnail,
      instructor: item.instructor,
      institute: item.institute
    })
    removeItem(item.id)
  }

  // Filter and sort items
  const getFilteredAndSortedItems = () => {
    let filteredItems = items

    // Filter by category
    if (filterCategory) {
      filteredItems = getItemsByCategory(filterCategory)
    }

    // Filter by price range
    filteredItems = filteredItems.filter(item => 
      item.price >= priceRange[0] && item.price <= priceRange[1]
    )

    // Sort items
    switch (sortBy) {
      case 'recent':
        filteredItems.sort((a, b) => new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime())
        break
      case 'price-low':
        filteredItems.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        filteredItems.sort((a, b) => b.price - a.price)
        break
      case 'rating':
        filteredItems.sort((a, b) => b.rating - a.rating)
        break
      case 'title':
        filteredItems.sort((a, b) => a.title.localeCompare(b.title))
        break
    }

    return filteredItems
  }

  const filteredItems = getFilteredAndSortedItems()
  const categories = [...new Set(items.map(item => item.category))]

  const WishlistItemComponent = ({ item }: { item: WishlistItem }) => {
    if (viewMode === 'list') {
      return (
        <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
          <div className="flex">
            {/* Thumbnail */}
            <div className="w-48 h-32 flex-shrink-0">
              <img
                src={item.thumbnail || '/images/course-placeholder.jpg'}
                alt={item.title}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Content */}
            <div className="flex-1 p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  {/* Category & Level */}
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">
                      {item.category.replace('_', ' ').toUpperCase()}
                    </span>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
                      {item.level.charAt(0).toUpperCase() + item.level.slice(1)}
                    </span>
                  </div>

                  {/* Title */}
                  <Link href={`/courses/${item.slug}`}>
                    <h3 className="text-lg font-bold text-gray-900 mb-2 hover:text-green-600 transition-colors duration-200 line-clamp-2">
                      {item.title}
                    </h3>
                  </Link>

                  {/* Instructor */}
                  <p className="text-sm text-gray-600 mb-2">by {item.instructor}</p>

                  {/* Rating */}
                  <div className="flex items-center mb-3">
                    <div className="flex items-center mr-2">
                      {renderStars(item.rating)}
                    </div>
                    <span className="text-sm text-gray-600">{item.rating.toFixed(1)}</span>
                  </div>
                </div>

                {/* Price and Actions */}
                <div className="ml-6 text-right">
                  {/* Price */}
                  <div className="mb-4">
                    <span className="text-xl font-bold text-gray-900">
                      {formatPrice(item.price, item.currency)}
                    </span>
                    {item.originalPrice && item.originalPrice > item.price && (
                      <span className="block text-sm text-gray-500 line-through">
                        {formatPrice(item.originalPrice, item.currency)}
                      </span>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="space-y-2">
                    <button
                      onClick={() => handleMoveToCart(item)}
                      disabled={isItemInCart(item.id)}
                      className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center text-sm"
                    >
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      {isItemInCart(item.id) ? 'In Cart' : 'Add to Cart'}
                    </button>
                    <button
                      onClick={() => removeItem(item.id)}
                      className="w-full px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200 flex items-center justify-center text-sm"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Remove
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }

    // Grid view
    return (
      <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group">
        {/* Thumbnail */}
        <div className="relative">
          <img
            src={item.thumbnail || '/images/course-placeholder.jpg'}
            alt={item.title}
            className="w-full h-48 object-cover"
          />
          
          {/* Remove Button */}
          <button
            onClick={() => removeItem(item.id)}
            className="absolute top-3 right-3 p-2 bg-white/80 rounded-full hover:bg-white transition-colors duration-200 opacity-0 group-hover:opacity-100"
          >
            <X className="h-4 w-4 text-red-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Category & Level */}
          <div className="flex items-center space-x-2 mb-2">
            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">
              {item.category.replace('_', ' ').toUpperCase()}
            </span>
            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
              {item.level.charAt(0).toUpperCase() + item.level.slice(1)}
            </span>
          </div>

          {/* Title */}
          <Link href={`/courses/${item.slug}`}>
            <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-green-600 transition-colors duration-200">
              {item.title}
            </h3>
          </Link>

          {/* Instructor */}
          <p className="text-sm text-gray-600 mb-3">by {item.instructor}</p>

          {/* Rating */}
          <div className="flex items-center mb-4">
            <div className="flex items-center mr-2">
              {renderStars(item.rating)}
            </div>
            <span className="text-sm text-gray-600">{item.rating.toFixed(1)}</span>
          </div>

          {/* Price */}
          <div className="mb-4">
            <span className="text-xl font-bold text-gray-900">
              {formatPrice(item.price, item.currency)}
            </span>
            {item.originalPrice && item.originalPrice > item.price && (
              <span className="block text-sm text-gray-500 line-through">
                {formatPrice(item.originalPrice, item.currency)}
              </span>
            )}
          </div>

          {/* Actions */}
          <div className="space-y-2">
            <button
              onClick={() => handleMoveToCart(item)}
              disabled={isItemInCart(item.id)}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {isItemInCart(item.id) ? 'In Cart' : 'Add to Cart'}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      {showHeader && (
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              My Wishlist ({getTotalItems()} items)
            </h1>
            <p className="text-gray-600">
              Courses you've saved for later
            </p>
          </div>

          {items.length > 0 && (
            <div className="mt-4 lg:mt-0 flex items-center space-x-4">
              {/* View Mode Toggle */}
              <div className="flex border border-gray-300 rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-green-600 text-white' : 'text-gray-600 hover:text-green-600'}`}
                >
                  <Grid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-green-600 text-white' : 'text-gray-600 hover:text-green-600'}`}
                >
                  <List className="h-5 w-5" />
                </button>
              </div>

              {/* Clear All */}
              <button
                onClick={clearWishlist}
                className="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200 flex items-center"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear All
              </button>
            </div>
          )}
        </div>
      )}

      {items.length === 0 ? (
        <div className="text-center py-16">
          <Heart className="h-24 w-24 text-gray-300 mx-auto mb-6" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            Your wishlist is empty
          </h2>
          <p className="text-gray-600 mb-8">
            Start adding courses you're interested in to your wishlist.
          </p>
          <Link
            href="/courses"
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
          >
            <Heart className="h-5 w-5 mr-2" />
            Browse Courses
          </Link>
        </div>
      ) : (
        <>
          {/* Filters and Sort */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              {/* Category Filter */}
              {categories.length > 1 && (
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category.replace('_', ' ').toUpperCase()}
                    </option>
                  ))}
                </select>
              )}
            </div>

            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {filteredItems.length} of {items.length} courses
              </span>
              
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="recent">Recently Added</option>
                <option value="title">Alphabetical</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Highest Rated</option>
              </select>
            </div>
          </div>

          {/* Wishlist Items */}
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-6'}>
            {filteredItems.map((item) => (
              <WishlistItemComponent key={item.id} item={item} />
            ))}
          </div>

          {/* Empty Filtered Results */}
          {filteredItems.length === 0 && items.length > 0 && (
            <div className="text-center py-12">
              <Filter className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No courses match your filters</h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your filter criteria
              </p>
              <button
                onClick={() => {
                  setFilterCategory('')
                  setPriceRange([0, 10000])
                }}
                className="text-green-600 hover:text-green-700 underline"
              >
                Clear filters
              </button>
            </div>
          )}
        </>
      )}
    </div>
  )
}
