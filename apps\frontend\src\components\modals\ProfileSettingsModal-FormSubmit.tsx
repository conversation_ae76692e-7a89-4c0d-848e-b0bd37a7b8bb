import React, { useState, useRef, useEffect } from 'react'
import { X, Upload, Trash2 } from 'lucide-react'
import { toast } from 'sonner'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useUserStore } from '@/stores/user/useUserStore'
import { uploadAvatar as uploadAvatarAPI, validateFile, getFileUrl } from '@/lib/api/file-upload'

interface ProfileSettingsModalProps {
  isOpen: boolean
  onClose: () => void
}

// Validation schema
const validationSchema = Yup.object({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  phone: Yup.string(),
  password: Yup.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: Yup.string().oneOf([Yup.ref('password')], 'Passwords must match')
})

const ProfileSettingsModalFormSubmit: React.FC<ProfileSettingsModalProps> = ({ isOpen, onClose }) => {
  const {
    user,
    isLoading,
    isUpdating,
    updateProfile,
    removeAvatar,
    fetchCurrentUser
  } = useUserStore()

  const [avatar, setAvatar] = useState<string | null>(user?.avatar || null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (isOpen && user) {
      setAvatar(user.avatar || null)
      setSelectedFile(null)
    }
  }, [isOpen, user])

  if (!isOpen) return null

  // Handle file selection (NO UPLOAD - just preview)
  const handleAvatarSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file before showing preview
    const validation = validateFile(file, 'avatar')
    if (!validation.valid) {
      toast.error(validation.message || 'Invalid file')
      return
    }

    // Store file and show preview
    setSelectedFile(file)
    const previewUrl = URL.createObjectURL(file)
    setAvatar(previewUrl)
    
    toast.info('Avatar selected. Click "Save Changes" to upload.')
  }

  const handleRemoveAvatar = async () => {
    setSelectedFile(null)
    setAvatar(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    toast.info('Avatar will be removed when you save changes.')
  }

  // Handle form submission (UPLOAD HAPPENS HERE)
  const handleSubmit = async (values: any) => {
    setIsUploading(true)
    try {
      let avatarMediaId = null

      // Upload avatar if file is selected
      if (selectedFile) {
        console.log('🚀 Uploading avatar during form submit...')
        
        const result = await uploadAvatarAPI(selectedFile)
        
        if (result.success && result.media) {
          avatarMediaId = result.media.id
          toast.success('Avatar uploaded successfully')
          
          console.log('📊 Avatar upload result:', {
            mediaId: result.media.id,
            url: result.media.url,
            sizes: Object.keys(result.media.sizes || {})
          })
        } else {
          throw new Error(result.message || 'Avatar upload failed')
        }
      }

      // Prepare profile data
      const profileData = {
        ...values,
        avatar: avatarMediaId || (avatar === null ? null : user?.avatar)
      }

      // Remove password fields if they're empty
      if (!values.password) {
        delete profileData.password
        delete profileData.confirmPassword
      }

      console.log('💾 Updating profile with data:', profileData)
      await updateProfile(profileData)
      
      toast.success('Profile updated successfully')
      onClose()
    } catch (error) {
      console.error('❌ Profile update error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update profile')
      // Reset avatar on error
      setAvatar(user?.avatar || null)
      setSelectedFile(null)
    } finally {
      setIsUploading(false)
    }
  }

  const getInitialValues = () => ({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    password: '',
    confirmPassword: ''
  })

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white rounded-xl shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Profile Settings</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          <Formik
            initialValues={getInitialValues()}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            enableReinitialize
          >
            {({ isSubmitting }) => (
              <Form className="space-y-6">
                {/* Avatar Section */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Profile Picture</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-6">
                      <Avatar className="h-20 w-20">
                        <AvatarImage src={avatar || undefined} alt="Profile" />
                        <AvatarFallback className="text-lg">
                          {user?.firstName?.[0]}{user?.lastName?.[0]}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1">
                        <div className="flex space-x-3">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => fileInputRef.current?.click()}
                            disabled={isUploading}
                          >
                            <Upload className="w-4 h-4 mr-2" />
                            {selectedFile ? 'Change' : 'Upload'}
                          </Button>
                          
                          {(avatar || selectedFile) && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={handleRemoveAvatar}
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Remove
                            </Button>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 mt-2">
                          JPG, PNG or GIF. Max size 5MB.
                          {selectedFile && (
                            <span className="block text-blue-600 font-medium">
                              Selected: {selectedFile.name}
                            </span>
                          )}
                        </p>
                      </div>
                      
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarSelect}
                        className="hidden"
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Personal Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Personal Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">First Name</Label>
                        <Field
                          as={Input}
                          id="firstName"
                          name="firstName"
                          placeholder="Enter first name"
                        />
                        <ErrorMessage name="firstName" component="div" className="text-red-500 text-sm mt-1" />
                      </div>
                      
                      <div>
                        <Label htmlFor="lastName">Last Name</Label>
                        <Field
                          as={Input}
                          id="lastName"
                          name="lastName"
                          placeholder="Enter last name"
                        />
                        <ErrorMessage name="lastName" component="div" className="text-red-500 text-sm mt-1" />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Field
                        as={Input}
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Enter email"
                      />
                      <ErrorMessage name="email" component="div" className="text-red-500 text-sm mt-1" />
                    </div>

                    <div>
                      <Label htmlFor="phone">Phone</Label>
                      <Field
                        as={Input}
                        id="phone"
                        name="phone"
                        placeholder="Enter phone number"
                      />
                      <ErrorMessage name="phone" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </CardContent>
                </Card>

                {/* Password Section */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Change Password</CardTitle>
                    <p className="text-sm text-gray-500">Leave blank to keep current password</p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="password">New Password</Label>
                      <Field
                        as={Input}
                        id="password"
                        name="password"
                        type="password"
                        placeholder="Enter new password"
                      />
                      <ErrorMessage name="password" component="div" className="text-red-500 text-sm mt-1" />
                    </div>

                    <div>
                      <Label htmlFor="confirmPassword">Confirm Password</Label>
                      <Field
                        as={Input}
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        placeholder="Confirm new password"
                      />
                      <ErrorMessage name="confirmPassword" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </CardContent>
                </Card>

                {/* Actions */}
                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    disabled={isSubmitting || isUploading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting || isUploading}
                  >
                    {isUploading ? 'Uploading...' : isSubmitting ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  )
}

export default ProfileSettingsModalFormSubmit
