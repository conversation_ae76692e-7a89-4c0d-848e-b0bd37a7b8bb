'use client'

import { useState, useRef, useEffect } from 'react'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { toast } from 'sonner'
import { X, Upload, Trash2, User, Eye, EyeOff, Loader2 } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useUserStore } from '@/stores/user/useUserStore'
import { uploadAvatar as uploadAvatarAPI, validateFile, getFileUrl } from '@/lib/api/file-upload'

interface ProfileSettingsModalProps {
  isOpen: boolean
  onClose: () => void
}

const profileSchema = Yup.object({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  phone: Yup.string(),
  password: Yup.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: Yup.string().oneOf([Yup.ref('password')], 'Passwords must match')
})

// Helper function to extract avatar URL from different formats
const getAvatarUrl = (avatar: any): string | null => {
  if (!avatar) return null

  if (typeof avatar === 'string') {
    // If avatar is already a URL string, use it directly
    return avatar
  } else if (typeof avatar === 'object' && avatar && 'url' in avatar) {
    // If avatar is a media object, extract the URL
    const avatarUrl = avatar.sizes?.avatar_medium?.url || avatar.url
    return getFileUrl(avatarUrl)
  } else {
    // If avatar is something else (like a media ID), we can't display it directly
    console.log('🖼️ Avatar format not supported for display:', avatar)
    return null
  }
}

export function ProfileSettingsModal({ isOpen, onClose }: ProfileSettingsModalProps) {

  const {
    user,
    isLoading,
    isUpdating,
    updateProfile,
    removeAvatar,
    fetchCurrentUser
  } = useUserStore()

  const [avatar, setAvatar] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Load user data when modal opens
  useEffect(() => {
    if (isOpen && !user) {
      fetchCurrentUser()
    }
  }, [isOpen, user, fetchCurrentUser])

  // Update avatar state when user data changes
  useEffect(() => {
    setAvatar(getAvatarUrl(user?.avatar))
  }, [user?.avatar])

  if (!isOpen) return null

  // Show loading state while fetching user data
  if (isLoading || !user) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div
          className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300"
          onClick={onClose}
        />
        <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-2xl transform transition-all duration-300 scale-100">
          <div className="flex items-center justify-center p-8">
            <div className="flex items-center space-x-3">
              <Loader2 className="w-6 h-6 animate-spin text-primary" />
              <span className="text-lg font-medium text-gray-700">Loading profile...</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file before upload
    const validation = validateFile(file, 'avatar')
    if (!validation.valid) {
      toast.error(validation.message || 'Invalid file')
      return
    }

    setIsUploading(true)
    try {
      // Create a preview URL for immediate feedback
      const previewUrl = URL.createObjectURL(file)
      setAvatar(previewUrl)

      console.log('🚀 Uploading avatar using new API...')

      // Upload the file using the new common API
      const result = await uploadAvatarAPI(file)

      if (result.success && result.media) {
        // Use the medium size avatar for display
        const avatarUrl = result.media.sizes?.avatar_medium?.url || result.media.url
        const fullAvatarUrl = getFileUrl(avatarUrl)
        setAvatar(fullAvatarUrl)

        // Update user store with new avatar info
        if (result.user) {
          // The API already updated the user's avatar field
          console.log('✅ User avatar updated:', result.user)
        }

        toast.success('Avatar uploaded successfully')
        console.log('📊 Avatar upload result:', {
          mediaId: result.media.id,
          url: result.media.url,
          sizes: Object.keys(result.media.sizes || {})
        })
      } else {
        throw new Error(result.message || 'Upload failed')
      }
    } catch (error) {
      console.error('❌ Avatar upload error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to upload avatar')
      // Reset to previous avatar on error
      setAvatar(getAvatarUrl(user?.avatar))
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveAvatar = async () => {
    // Show confirmation dialog
    const confirmed = window.confirm(
      'Are you sure you want to remove your avatar? This action cannot be undone.'
    )

    if (!confirmed) {
      return
    }

    try {
      console.log('🗑️ Starting avatar removal...')
      await removeAvatar()
      setAvatar(null)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
      console.log('✅ Avatar removal completed')
    } catch (error) {
      console.error('❌ Avatar removal failed:', error)
      // Error handling is done in the store (toast notification)
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      const profileData = {
        ...values,
        // Don't send avatar URL - the avatar upload already updated the user record
        // Only send avatar if it's null (for removal)
        ...(avatar === null && { avatar: null })
      }

      // Remove password fields if they're empty
      if (!values.password) {
        delete profileData.password
        delete profileData.confirmPassword
      }

      await updateProfile(profileData)
      onClose()
    } catch (error) {
      // Error handling is done in the store
      console.error('Profile update error:', error)
    }
  }

  const getInitialValues = () => ({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    password: '',
    confirmPassword: ''
  })

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden transform transition-all duration-300 scale-100">
        <div className="overflow-y-auto max-h-[90vh]">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100 bg-gray-50/50">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Profile Settings</h2>
            <p className="text-sm text-gray-600 mt-1">Manage your personal information and preferences</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200 text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 bg-white">
          <Formik
            initialValues={getInitialValues()}
            validationSchema={profileSchema}
            onSubmit={handleSubmit}
          >
            {({ errors, touched }) => (
              <Form className="space-y-6">
                {/* Avatar Section */}
                <Card className="border-0 shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
                      <User className="w-5 h-5 mr-2 text-blue-600" />
                      Profile Picture
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-6">
                      <Avatar className="h-20 w-20">
                        <AvatarImage src={avatar || undefined} />
                        <AvatarFallback className="text-lg">
                          <User className="w-8 h-8" />
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1">
                        <div className="flex space-x-3">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => fileInputRef.current?.click()}
                            disabled={isUploading}
                          >
                            <Upload className="w-4 h-4 mr-2" />
                            {isUploading ? 'Uploading...' : 'Upload'}
                          </Button>
                          
                          {avatar && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={handleRemoveAvatar}
                              disabled={isUpdating}
                            >
                              {isUpdating ? (
                                <>
                                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                  Removing...
                                </>
                              ) : (
                                <>
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Remove
                                </>
                              )}
                            </Button>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 mt-2">
                          JPG, PNG or GIF. Max size 5MB.
                        </p>
                      </div>
                      
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarUpload}
                        className="hidden"
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Personal Information */}
                <Card className="border-0 shadow-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-semibold text-gray-800">Personal Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="firstName">First Name</Label>
                        <Field
                          as={Input}
                          id="firstName"
                          name="firstName"
                          placeholder="Enter first name"
                        />
                        {errors.firstName && touched.firstName && (
                          <p className="text-sm text-destructive">{String(errors.firstName)}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="lastName">Last Name</Label>
                        <Field
                          as={Input}
                          id="lastName"
                          name="lastName"
                          placeholder="Enter last name"
                        />
                        {errors.lastName && touched.lastName && (
                          <p className="text-sm text-destructive">{String(errors.lastName)}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Field
                        as={Input}
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Enter email address"
                      />
                      {errors.email && touched.email && (
                        <p className="text-sm text-destructive">{String(errors.email)}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Field
                        as={Input}
                        id="phone"
                        name="phone"
                        placeholder="Enter phone number"
                      />
                      {errors.phone && touched.phone && (
                        <p className="text-sm text-destructive">{String(errors.phone)}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Password Section */}
                <Card className="border-0 shadow-sm bg-gradient-to-r from-amber-50 to-orange-50">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-semibold text-gray-800">Change Password</CardTitle>
                    <p className="text-sm text-amber-700 bg-amber-100 px-3 py-2 rounded-lg">
                      Leave blank to keep your current password
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="password">New Password</Label>
                      <div className="relative">
                        <Field
                          as={Input}
                          id="password"
                          name="password"
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Enter new password"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showPassword ? (
                            <EyeOff className="w-4 h-4 text-gray-400" />
                          ) : (
                            <Eye className="w-4 h-4 text-gray-400" />
                          )}
                        </button>
                      </div>
                      {errors.password && touched.password && (
                        <p className="text-sm text-destructive">{String(errors.password)}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword">Confirm Password</Label>
                      <div className="relative">
                        <Field
                          as={Input}
                          id="confirmPassword"
                          name="confirmPassword"
                          type={showConfirmPassword ? 'text' : 'password'}
                          placeholder="Confirm new password"
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="w-4 h-4 text-gray-400" />
                          ) : (
                            <Eye className="w-4 h-4 text-gray-400" />
                          )}
                        </button>
                      </div>
                      {errors.confirmPassword && touched.confirmPassword && (
                        <p className="text-sm text-destructive">{String(errors.confirmPassword)}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Footer */}
                <div className="flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-100 bg-gray-50/30 -mx-6 px-6 py-4 rounded-b-xl">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    className="px-6 py-2 border-gray-300 hover:bg-gray-50"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isUpdating}
                    className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
                  >
                    {isUpdating ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
        </div>
      </div>
    </div>
  )
}
