'use client'

import { useState, useEffect } from 'react'
import { useNotificationStore } from '@/stores/notifications/useNotificationStore'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { 
  Bell, 
  BellRing, 
  Check, 
  CheckCheck, 
  Archive, 
  Trash2, 
  Settings, 
  Filter,
  AlertCircle,
  Info,
  CheckCircle,
  AlertTriangle,
  X,
  MoreHorizontal
} from 'lucide-react'

export function NotificationCenter() {
  const [activeTab, setActiveTab] = useState('all')
  
  const {
    notifications,
    stats,
    preferences,
    isLoading,
    showOnlyUnread,
    filters,
    fetchNotifications,
    fetchNotificationStats,
    fetchPreferences,
    markAsRead,
    markAllAsRead,
    archiveNotification,
    deleteNotification,
    setShowOnlyUnread,
    setFilters,
    updatePreferences
  } = useNotificationStore()

  useEffect(() => {
    fetchNotifications()
    fetchNotificationStats()
    fetchPreferences()
  }, [fetchNotifications, fetchNotificationStats, fetchPreferences])

  const getNotificationIcon = (type: string, priority: string) => {
    if (priority === 'urgent') {
      return <AlertCircle className="h-4 w-4 text-red-500" />
    }
    
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      'system': 'System',
      'course': 'Course',
      'enrollment': 'Enrollment',
      'payment': 'Payment',
      'staff': 'Staff',
      'general': 'General'
    }
    return labels[category] || category
  }

  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'unread' && notification.isRead) return false
    if (activeTab === 'archived' && !notification.isArchived) return false
    if (activeTab === 'all' && notification.isArchived) return false
    
    if (filters.category && notification.category !== filters.category) return false
    if (filters.type && notification.type !== filters.type) return false
    if (filters.priority && notification.priority !== filters.priority) return false
    
    return true
  })

  const handleNotificationAction = async (notification: any, action: string) => {
    switch (action) {
      case 'read':
        if (!notification.isRead) {
          await markAsRead(notification.id)
        }
        break
      case 'archive':
        await archiveNotification(notification.id)
        break
      case 'delete':
        await deleteNotification(notification.id)
        break
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <BellRing className="h-6 w-6" />
            <h1 className="text-2xl font-bold">Notifications</h1>
          </div>
          
          {stats.unread > 0 && (
            <Badge variant="destructive" className="px-2 py-1">
              {stats.unread} unread
            </Badge>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={markAllAsRead}
            disabled={stats.unread === 0}
          >
            <CheckCheck className="h-4 w-4 mr-2" />
            Mark All Read
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <div className="p-4 space-y-4">
                <h3 className="font-medium">Notification Preferences</h3>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="email-notifications">Email Notifications</Label>
                    <Switch
                      id="email-notifications"
                      checked={preferences.email.enabled}
                      onCheckedChange={(checked) => 
                        updatePreferences({
                          email: { ...preferences.email, enabled: checked }
                        })
                      }
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="push-notifications">Push Notifications</Label>
                    <Switch
                      id="push-notifications"
                      checked={preferences.push.enabled}
                      onCheckedChange={(checked) => 
                        updatePreferences({
                          push: { ...preferences.push, enabled: checked }
                        })
                      }
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="in-app-notifications">In-App Notifications</Label>
                    <Switch
                      id="in-app-notifications"
                      checked={preferences.inApp.enabled}
                      onCheckedChange={(checked) => 
                        updatePreferences({
                          inApp: { ...preferences.inApp, enabled: checked }
                        })
                      }
                    />
                  </div>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Bell className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm font-medium">Total</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BellRing className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Unread</p>
                <p className="text-2xl font-bold">{stats.unread}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-sm font-medium">High Priority</p>
                <p className="text-2xl font-bold">{stats.byPriority.high || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <div>
                <p className="text-sm font-medium">Urgent</p>
                <p className="text-2xl font-bold">{stats.byPriority.urgent || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Recent Notifications</CardTitle>
            
            <div className="flex items-center space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setFilters({ category: undefined })}>
                    All Categories
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilters({ category: 'system' })}>
                    System
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilters({ category: 'course' })}>
                    Course
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilters({ category: 'enrollment' })}>
                    Enrollment
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilters({ category: 'payment' })}>
                    Payment
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="unread">
                Unread
                {stats.unread > 0 && (
                  <Badge variant="destructive" className="ml-2 px-1 py-0 text-xs">
                    {stats.unread}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="archived">Archived</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {isLoading ? (
                <div className="space-y-3">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className="h-16 bg-gray-200 rounded-lg"></div>
                    </div>
                  ))}
                </div>
              ) : filteredNotifications.length > 0 ? (
                <div className="space-y-3">
                  {filteredNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 border rounded-lg transition-colors ${
                        notification.isRead ? 'bg-gray-50' : 'bg-white border-blue-200'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type, notification.priority)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900">
                                {notification.title}
                              </h4>
                              <p className="text-sm text-gray-600 mt-1">
                                {notification.message}
                              </p>
                              
                              <div className="flex items-center space-x-2 mt-2">
                                <Badge variant="outline" className="text-xs">
                                  {getCategoryLabel(notification.category)}
                                </Badge>
                                
                                <Badge className={`text-xs ${getPriorityColor(notification.priority)}`}>
                                  {notification.priority}
                                </Badge>
                                
                                <span className="text-xs text-gray-500">
                                  {new Date(notification.createdAt).toLocaleString()}
                                </span>
                              </div>
                            </div>
                            
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                {!notification.isRead && (
                                  <DropdownMenuItem onClick={() => handleNotificationAction(notification, 'read')}>
                                    <Check className="h-4 w-4 mr-2" />
                                    Mark as Read
                                  </DropdownMenuItem>
                                )}
                                
                                <DropdownMenuItem onClick={() => handleNotificationAction(notification, 'archive')}>
                                  <Archive className="h-4 w-4 mr-2" />
                                  Archive
                                </DropdownMenuItem>
                                
                                <DropdownMenuItem 
                                  onClick={() => handleNotificationAction(notification, 'delete')}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <Bell className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium mb-2">No notifications</h3>
                  <p>You're all caught up! No notifications to show.</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
