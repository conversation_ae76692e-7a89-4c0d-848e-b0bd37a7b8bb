'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { LogoUpload, FaviconUpload } from '@/components/ui/file-upload'
import { ErrorDisplay, createErrorInfo } from '@/components/ui/error-display'
import { FileUploadErrorBoundary } from '@/components/ui/error-boundary'
import { useSettingsStore } from '@/stores/settings/useSettingsStore'
import { useRealTimeSync, useOptimisticPreview } from '@/hooks/useRealTimeSync'
import { toast } from 'sonner'
import {
  Upload,
  Trash2,
  Image,
  Palette,
  AlertCircle,
  CheckCircle2,
  Loader2,
  Info
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface BrandingUploadProps {
  className?: string
}

export function BrandingUpload({ className }: BrandingUploadProps) {
  const {
    platformBranding,
    isUploadingLogo,
    isUploadingFavicon,
    isRemovingLogo,
    isRemovingFavicon,
    uploadError,
    uploadPlatformLogo,
    uploadPlatformFavicon,
    removePlatformLogo,
    removePlatformFavicon,
    processFavicon,
    fetchPlatformBranding,
    clearUploadError
  } = useSettingsStore()

  const [showAdvancedFavicon, setShowAdvancedFavicon] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  // Real-time synchronization
  const {
    refreshBranding,
    optimisticBrandingUpdate,
    getUploadStatus
  } = useRealTimeSync({
    autoRefreshBranding: true,
    optimisticUpdates: true,
    onUploadComplete: (uploadId) => {
      console.log('✅ Upload completed:', uploadId)
      toast.success('Upload completed successfully!')
    },
    onUploadError: (uploadId, error) => {
      console.error('❌ Upload failed:', uploadId, error)
      toast.error(`Upload failed: ${error}`)
    },
    onProgressUpdate: (progress) => {
      console.log('📊 Upload progress:', progress)
    }
  })

  // Optimistic preview management
  const { createPreview, removePreview } = useOptimisticPreview()

  const handleValidationError = (errors: string[]) => {
    setValidationErrors(errors)
    toast.error(`Validation failed: ${errors[0]}`)
  }

  // Load branding on mount
  React.useEffect(() => {
    fetchPlatformBranding()
  }, [fetchPlatformBranding])

  const handleLogoUpload = async (file: File) => {
    try {
      clearUploadError()

      // Create optimistic preview
      optimisticBrandingUpdate('logo', file)
      const { previewId } = createPreview(file)

      const success = await uploadPlatformLogo(file)
      if (success) {
        toast.success('Platform logo uploaded successfully!')
        // Refresh branding to get the latest data
        setTimeout(() => refreshBranding(), 1000)
      } else {
        toast.error(uploadError || 'Failed to upload logo')
      }

      // Clean up preview
      removePreview(previewId)
    } catch (error) {
      console.error('Logo upload error:', error)
      toast.error('Failed to upload logo')
    }
  }

  const handleFaviconUpload = async (file: File) => {
    try {
      clearUploadError()

      // Create optimistic preview
      optimisticBrandingUpdate('favicon', file)
      const { previewId } = createPreview(file)

      const success = await uploadPlatformFavicon(file)
      if (success) {
        toast.success('Platform favicon uploaded successfully!')
        // Refresh branding to get the latest data
        setTimeout(() => refreshBranding(), 1000)
      } else {
        toast.error(uploadError || 'Failed to upload favicon')
      }

      // Clean up preview
      removePreview(previewId)
    } catch (error) {
      console.error('Favicon upload error:', error)
      toast.error('Failed to upload favicon')
    }
  }

  const handleAdvancedFaviconUpload = async (file: File) => {
    try {
      clearUploadError()

      // Create optimistic preview
      optimisticBrandingUpdate('favicon', file)
      const { previewId } = createPreview(file)

      const success = await processFavicon(file)
      if (success) {
        toast.success('Favicon processed and uploaded successfully! Multiple sizes generated.')
        // Refresh branding to get the latest data
        setTimeout(() => refreshBranding(), 2000) // Longer delay for processing
      } else {
        toast.error(uploadError || 'Failed to process favicon')
      }

      // Clean up preview
      removePreview(previewId)
    } catch (error) {
      console.error('Advanced favicon upload error:', error)
      toast.error('Failed to process favicon')
    }
  }

  const handleRemoveLogo = async () => {
    try {
      clearUploadError()
      const success = await removePlatformLogo()
      if (success) {
        toast.success('Platform logo removed successfully!')
      } else {
        toast.error(uploadError || 'Failed to remove logo')
      }
    } catch (error) {
      console.error('Remove logo error:', error)
      toast.error('Failed to remove logo')
    }
  }

  const handleRemoveFavicon = async () => {
    try {
      clearUploadError()
      const success = await removePlatformFavicon()
      if (success) {
        toast.success('Platform favicon removed successfully!')
      } else {
        toast.error(uploadError || 'Failed to remove favicon')
      }
    } catch (error) {
      console.error('Remove favicon error:', error)
      toast.error('Failed to remove favicon')
    }
  }

  // Get current upload status
  const uploadStatus = getUploadStatus()

  return (
    <FileUploadErrorBoundary>
      <div className={cn('space-y-6', className)}>
        {/* Real-time Upload Status */}
        {uploadStatus.hasActiveUploads && (
          <Alert className="border-blue-200 bg-blue-50">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              {uploadStatus.activeUploads.length} upload(s) in progress...
              {uploadStatus.activeUploads.map(upload => (
                <div key={upload.id} className="text-xs mt-1">
                  {upload.filename}: {upload.percentage}%
                </div>
              ))}
            </AlertDescription>
          </Alert>
        )}

        {/* Error Display */}
        {uploadError && (
          <ErrorDisplay
            error={createErrorInfo('upload', uploadError, {
              severity: 'medium',
              retryable: true
            })}
            onDismiss={clearUploadError}
            compact={true}
          />
        )}

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <ErrorDisplay
            error={createErrorInfo('validation', `File validation failed: ${validationErrors[0]}`, {
              severity: 'medium',
              details: validationErrors.join(', ')
            })}
            onDismiss={() => setValidationErrors([])}
            compact={true}
          />
        )}

      {/* Platform Logo Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Image className="h-5 w-5" />
            Platform Logo
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <LogoUpload
            onFileSelect={handleLogoUpload}
            onFileRemove={platformBranding?.logo ? handleRemoveLogo : undefined}
            onValidationError={handleValidationError}
            loading={isUploadingLogo || isRemovingLogo}
            error={uploadError}
            success={!isUploadingLogo && !uploadError && !!platformBranding?.logo}
            currentFile={platformBranding?.logo ? {
              name: platformBranding.logo.filename || 'Platform Logo',
              url: platformBranding.logo.url,
              size: platformBranding.logo.filesize
            } : undefined}
            disabled={isUploadingLogo || isRemovingLogo}
          />

          {platformBranding?.logo && (
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-800">Logo is active</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRemoveLogo}
                disabled={isRemovingLogo}
                className="text-red-600 hover:text-red-700"
              >
                {isRemovingLogo ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                Remove
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Platform Favicon Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Platform Favicon
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Basic Favicon Upload */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Basic Favicon</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvancedFavicon(!showAdvancedFavicon)}
                className="text-xs"
              >
                {showAdvancedFavicon ? 'Hide' : 'Show'} Advanced
              </Button>
            </div>
            
            <FaviconUpload
              onFileSelect={handleFaviconUpload}
              onFileRemove={platformBranding?.favicon ? handleRemoveFavicon : undefined}
              onValidationError={handleValidationError}
              loading={isUploadingFavicon || isRemovingFavicon}
              error={uploadError}
              success={!isUploadingFavicon && !uploadError && !!platformBranding?.favicon}
              currentFile={platformBranding?.favicon ? {
                name: platformBranding.favicon.filename || 'Platform Favicon',
                url: platformBranding.favicon.url,
                size: platformBranding.favicon.filesize
              } : undefined}
              disabled={isUploadingFavicon || isRemovingFavicon}
            />
          </div>

          {/* Advanced Favicon Processing */}
          {showAdvancedFavicon && (
            <div className="space-y-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-2">
                <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-blue-900">Advanced Favicon Processing</h4>
                  <p className="text-xs text-blue-700">
                    Upload any image and we'll automatically generate multiple favicon sizes 
                    (16x16, 32x32, 48x48, 64x64, 128x128, 256x256) optimized for different devices and browsers.
                  </p>
                </div>
              </div>
              
              <FaviconUpload
                onFileSelect={handleAdvancedFaviconUpload}
                onValidationError={handleValidationError}
                loading={isUploadingFavicon}
                error={uploadError}
                placeholder="Upload image for favicon processing"
                description="Any image format (will be converted to multiple PNG sizes)"
                disabled={isUploadingFavicon}
                showPreview={false}
              />
              
              <Badge variant="secondary" className="text-xs">
                Generates 6 optimized favicon sizes
              </Badge>
            </div>
          )}

          {platformBranding?.favicon && (
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-800">Favicon is active</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRemoveFavicon}
                disabled={isRemovingFavicon}
                className="text-red-600 hover:text-red-700"
              >
                {isRemovingFavicon ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                Remove
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Branding Guidelines</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div>
            <h5 className="font-medium text-gray-900">Logo Requirements:</h5>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li>Recommended: SVG format for scalability</li>
              <li>Alternative: PNG with transparent background</li>
              <li>Minimum width: 200px for clarity</li>
              <li>Maximum file size: 5MB</li>
            </ul>
          </div>
          
          <div>
            <h5 className="font-medium text-gray-900">Favicon Requirements:</h5>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li>Recommended: Square aspect ratio (1:1)</li>
              <li>Minimum size: 32x32 pixels</li>
              <li>Formats: ICO, PNG, or any image (for processing)</li>
              <li>Maximum file size: 2MB</li>
            </ul>
          </div>
        </CardContent>
      </Card>
      </div>
    </FileUploadErrorBoundary>
  )
}
