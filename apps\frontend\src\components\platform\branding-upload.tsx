'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { LogoUpload, FaviconUpload } from '@/components/ui/file-upload'
import { ErrorDisplay, createErrorInfo } from '@/components/ui/error-display'
import { FileUploadErrorBoundary } from '@/components/ui/error-boundary'
import { useSettingsStore } from '@/stores/settings/useSettingsStore'
import { toast } from 'sonner'
import {
  Upload,
  Trash2,
  Image,
  Palette,
  AlertCircle,
  CheckCircle2,
  Loader2,
  Info
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface BrandingUploadProps {
  className?: string
}

export function BrandingUpload({ className }: BrandingUploadProps) {
  const {
    platformBranding,
    isUploadingLogo,
    isUploadingFavicon,
    isRemovingLogo,
    isRemovingFavicon,
    uploadError,
    uploadPlatformLogo,
    uploadPlatformFavicon,
    removePlatformLogo,
    removePlatformFavicon,
    processFavicon,
    fetchPlatformBranding,
    clearUploadError
  } = useSettingsStore()

  const [showAdvancedFavicon, setShowAdvancedFavicon] = useState(false)

  // Load branding on mount
  React.useEffect(() => {
    fetchPlatformBranding()
  }, [fetchPlatformBranding])

  const handleLogoUpload = async (file: File) => {
    try {
      clearUploadError()
      const success = await uploadPlatformLogo(file)
      if (success) {
        toast.success('Platform logo uploaded successfully!')
      } else {
        toast.error(uploadError || 'Failed to upload logo')
      }
    } catch (error) {
      console.error('Logo upload error:', error)
      toast.error('Failed to upload logo')
    }
  }

  const handleFaviconUpload = async (file: File) => {
    try {
      clearUploadError()
      const success = await uploadPlatformFavicon(file)
      if (success) {
        toast.success('Platform favicon uploaded successfully!')
      } else {
        toast.error(uploadError || 'Failed to upload favicon')
      }
    } catch (error) {
      console.error('Favicon upload error:', error)
      toast.error('Failed to upload favicon')
    }
  }

  const handleAdvancedFaviconUpload = async (file: File) => {
    try {
      clearUploadError()
      const success = await processFavicon(file)
      if (success) {
        toast.success('Favicon processed and uploaded successfully! Multiple sizes generated.')
      } else {
        toast.error(uploadError || 'Failed to process favicon')
      }
    } catch (error) {
      console.error('Advanced favicon upload error:', error)
      toast.error('Failed to process favicon')
    }
  }

  const handleRemoveLogo = async () => {
    try {
      clearUploadError()
      const success = await removePlatformLogo()
      if (success) {
        toast.success('Platform logo removed successfully!')
      } else {
        toast.error(uploadError || 'Failed to remove logo')
      }
    } catch (error) {
      console.error('Remove logo error:', error)
      toast.error('Failed to remove logo')
    }
  }

  const handleRemoveFavicon = async () => {
    try {
      clearUploadError()
      const success = await removePlatformFavicon()
      if (success) {
        toast.success('Platform favicon removed successfully!')
      } else {
        toast.error(uploadError || 'Failed to remove favicon')
      }
    } catch (error) {
      console.error('Remove favicon error:', error)
      toast.error('Failed to remove favicon')
    }
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Error Alert */}
      {uploadError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{uploadError}</AlertDescription>
        </Alert>
      )}

      {/* Platform Logo Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Image className="h-5 w-5" />
            Platform Logo
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <LogoUpload
            onFileSelect={handleLogoUpload}
            onFileRemove={platformBranding?.logo ? handleRemoveLogo : undefined}
            loading={isUploadingLogo || isRemovingLogo}
            error={uploadError}
            success={!isUploadingLogo && !uploadError && !!platformBranding?.logo}
            currentFile={platformBranding?.logo ? {
              name: platformBranding.logo.filename || 'Platform Logo',
              url: platformBranding.logo.url,
              size: platformBranding.logo.filesize
            } : undefined}
            disabled={isUploadingLogo || isRemovingLogo}
          />

          {platformBranding?.logo && (
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-800">Logo is active</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRemoveLogo}
                disabled={isRemovingLogo}
                className="text-red-600 hover:text-red-700"
              >
                {isRemovingLogo ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                Remove
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Platform Favicon Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Platform Favicon
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Basic Favicon Upload */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Basic Favicon</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvancedFavicon(!showAdvancedFavicon)}
                className="text-xs"
              >
                {showAdvancedFavicon ? 'Hide' : 'Show'} Advanced
              </Button>
            </div>
            
            <FaviconUpload
              onFileSelect={handleFaviconUpload}
              onFileRemove={platformBranding?.favicon ? handleRemoveFavicon : undefined}
              loading={isUploadingFavicon || isRemovingFavicon}
              error={uploadError}
              success={!isUploadingFavicon && !uploadError && !!platformBranding?.favicon}
              currentFile={platformBranding?.favicon ? {
                name: platformBranding.favicon.filename || 'Platform Favicon',
                url: platformBranding.favicon.url,
                size: platformBranding.favicon.filesize
              } : undefined}
              disabled={isUploadingFavicon || isRemovingFavicon}
            />
          </div>

          {/* Advanced Favicon Processing */}
          {showAdvancedFavicon && (
            <div className="space-y-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-2">
                <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-blue-900">Advanced Favicon Processing</h4>
                  <p className="text-xs text-blue-700">
                    Upload any image and we'll automatically generate multiple favicon sizes 
                    (16x16, 32x32, 48x48, 64x64, 128x128, 256x256) optimized for different devices and browsers.
                  </p>
                </div>
              </div>
              
              <FaviconUpload
                onFileSelect={handleAdvancedFaviconUpload}
                loading={isUploadingFavicon}
                error={uploadError}
                placeholder="Upload image for favicon processing"
                description="Any image format (will be converted to multiple PNG sizes)"
                disabled={isUploadingFavicon}
                showPreview={false}
              />
              
              <Badge variant="secondary" className="text-xs">
                Generates 6 optimized favicon sizes
              </Badge>
            </div>
          )}

          {platformBranding?.favicon && (
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-800">Favicon is active</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRemoveFavicon}
                disabled={isRemovingFavicon}
                className="text-red-600 hover:text-red-700"
              >
                {isRemovingFavicon ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                Remove
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Branding Guidelines</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div>
            <h5 className="font-medium text-gray-900">Logo Requirements:</h5>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li>Recommended: SVG format for scalability</li>
              <li>Alternative: PNG with transparent background</li>
              <li>Minimum width: 200px for clarity</li>
              <li>Maximum file size: 5MB</li>
            </ul>
          </div>
          
          <div>
            <h5 className="font-medium text-gray-900">Favicon Requirements:</h5>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li>Recommended: Square aspect ratio (1:1)</li>
              <li>Minimum size: 32x32 pixels</li>
              <li>Formats: ICO, PNG, or any image (for processing)</li>
              <li>Maximum file size: 2MB</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
