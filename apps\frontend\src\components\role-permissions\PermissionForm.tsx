'use client'

import { useState } from 'react'
import { useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Plus, Edit, Save, X } from 'lucide-react'
import { Formik, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { toast } from 'sonner'

// Validation schema using Yup - Following Location Management patterns
const validationSchema = Yup.object({
  name: Yup.string()
    .required('Permission name is required')
    .min(2, 'Permission name must be at least 2 characters')
    .max(100, 'Permission name must be less than 100 characters'),
  code: Yup.string()
    .required('Permission code is required')
    .min(2, 'Permission code must be at least 2 characters')
    .max(50, 'Permission code must be at most 50 characters')
    .matches(/^[a-z_]+$/, 'Permission code must contain only lowercase letters and underscores'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  resource: Yup.string()
    .required('Resource is required'),
  action: Yup.string()
    .required('Action is required'),
  scope: Yup.string()
    .max(100, 'Scope must be less than 100 characters'),
  isActive: Yup.boolean(),
  priority: Yup.number()
    .min(0, 'Priority must be 0 or greater')
    .integer('Priority must be a whole number')
})

interface PermissionFormProps {
  permission?: any
  mode: 'create' | 'edit'
  trigger?: React.ReactNode
  onSuccess?: () => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function PermissionForm({ permission, mode, trigger, onSuccess, open: externalOpen, onOpenChange }: PermissionFormProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const open = externalOpen !== undefined ? externalOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen
  const { createPermission, updatePermission, isLoading } = useRolePermissionsStore()

  // Initial values for Formik - Following Location Management patterns
  const initialValues = {
    name: permission?.name || '',
    code: permission?.code || '',
    description: permission?.description || '',
    resource: permission?.resource || '',
    action: permission?.action || '',
    scope: permission?.scope || '',
    isActive: permission?.isActive ?? true,
    priority: permission?.priority || 0
  }

  const handleSubmit = async (values: any, { setSubmitting, resetForm }: any) => {
    try {
      // Prepare data for submission - Following Location Management patterns
      const submitData = {
        name: values.name,
        code: values.code.toLowerCase(),
        description: values.description,
        resource: values.resource,
        action: values.action,
        scope: values.scope,
        isActive: values.isActive,
        priority: values.priority
      }

      console.log('Submitting permission data:', submitData)

      if (mode === 'create') {
        await createPermission(submitData)
        toast.success('Permission created successfully')
        resetForm()
      } else {
        await updatePermission(permission.id, submitData)
        toast.success('Permission updated successfully')
      }

      // Only close dialog on successful submission
      setOpen(false)
      onSuccess?.()
    } catch (error) {
      console.error('Form submission error:', error)
      toast.error('Failed to save permission')
      // Don't close dialog on error - let user fix the issues
    } finally {
      setSubmitting(false)
    }
  }

  const defaultTrigger = (
    <Button size="sm" className="gap-2">
      {mode === 'create' ? <Plus className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
      {mode === 'create' ? 'Add Permission' : 'Edit Permission'}
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {mode === 'create' ? <Plus className="h-5 w-5" /> : <Edit className="h-5 w-5" />}
            {mode === 'create' ? 'Create New Permission' : 'Edit Permission'}
          </DialogTitle>
        </DialogHeader>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ values, errors, touched, handleChange, isSubmitting, setSubmitting, resetForm }) => (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="name">Permission Name *</Label>
                    <Field name="name">
                      {({ field }: any) => (
                        <Input
                          {...field}
                          id="name"
                          placeholder="Enter permission name"
                          className={errors.name && touched.name ? 'border-red-500' : ''}
                        />
                      )}
                    </Field>
                    <ErrorMessage name="name" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div>
                    <Label htmlFor="code">Permission Code *</Label>
                    <Field name="code">
                      {({ field }: any) => (
                        <Input
                          {...field}
                          id="code"
                          placeholder="Enter permission code (lowercase, underscores allowed)"
                          className={errors.code && touched.code ? 'border-red-500' : ''}
                        />
                      )}
                    </Field>
                    <ErrorMessage name="code" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Field name="description">
                      {({ field }: any) => (
                        <Textarea
                          {...field}
                          id="description"
                          placeholder="Enter permission description"
                          rows={3}
                          className={errors.description && touched.description ? 'border-red-500' : ''}
                        />
                      )}
                    </Field>
                    <ErrorMessage name="description" component="div" className="text-red-500 text-sm mt-1" />
                  </div>
                </CardContent>
              </Card>

              {/* Permission Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Permission Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="resource">Resource *</Label>
                    <Field name="resource">
                      {({ field, form }: any) => (
                        <Select
                          value={field.value}
                          onValueChange={(value) => form.setFieldValue('resource', value)}
                        >
                          <SelectTrigger className={errors.resource && touched.resource ? 'border-red-500' : ''}>
                            <SelectValue placeholder="Select resource" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="users">Users</SelectItem>
                            <SelectItem value="roles">Roles</SelectItem>
                            <SelectItem value="permissions">Permissions</SelectItem>
                            <SelectItem value="institutes">Institutes</SelectItem>
                            <SelectItem value="courses">Courses</SelectItem>
                            <SelectItem value="settings">Settings</SelectItem>
                            <SelectItem value="analytics">Analytics</SelectItem>
                            <SelectItem value="billing">Billing</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </Field>
                    <ErrorMessage name="resource" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div>
                    <Label htmlFor="action">Action *</Label>
                    <Field name="action">
                      {({ field, form }: any) => (
                        <Select
                          value={field.value}
                          onValueChange={(value) => form.setFieldValue('action', value)}
                        >
                          <SelectTrigger className={errors.action && touched.action ? 'border-red-500' : ''}>
                            <SelectValue placeholder="Select action" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="create">Create</SelectItem>
                            <SelectItem value="read">Read</SelectItem>
                            <SelectItem value="update">Update</SelectItem>
                            <SelectItem value="delete">Delete</SelectItem>
                            <SelectItem value="manage">Manage</SelectItem>
                            <SelectItem value="view">View</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </Field>
                    <ErrorMessage name="action" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div>
                    <Label htmlFor="scope">Scope</Label>
                    <Field name="scope">
                      {({ field }: any) => (
                        <Input
                          {...field}
                          id="scope"
                          placeholder="Enter permission scope (optional)"
                          className={errors.scope && touched.scope ? 'border-red-500' : ''}
                        />
                      )}
                    </Field>
                    <ErrorMessage name="scope" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Field name="priority">
                      {({ field }: any) => (
                        <Input
                          {...field}
                          id="priority"
                          type="number"
                          min="0"
                          placeholder="Enter priority (0 = highest)"
                          className={errors.priority && touched.priority ? 'border-red-500' : ''}
                        />
                      )}
                    </Field>
                    <ErrorMessage name="priority" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Field name="isActive">
                      {({ field, form }: any) => (
                        <Switch
                          id="isActive"
                          checked={field.value}
                          onCheckedChange={(checked) => form.setFieldValue('isActive', checked)}
                        />
                      )}
                    </Field>
                    <Label htmlFor="isActive">Active Permission</Label>
                  </div>
                </CardContent>
              </Card>

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="submit"
                  onClick={() => handleSubmit(values, { setSubmitting, resetForm })}
                  disabled={isSubmitting}
                  className="gap-2"
                >
                  <Save className="h-4 w-4" />
                  {isSubmitting ? 'Saving...' : (mode === 'create' ? 'Create Permission' : 'Update Permission')}
                </Button>
              </div>
            </div>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  )
}
