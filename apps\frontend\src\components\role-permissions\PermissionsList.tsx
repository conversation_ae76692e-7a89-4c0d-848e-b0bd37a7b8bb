'use client'

import { useEffect } from 'react'
import { useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'
import { PermissionCard } from './PermissionCard'
import { PermissionListItem } from './PermissionListItem'
import { RolePermissionsPagination } from './RolePermissionsPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle, Key } from 'lucide-react'

export function PermissionsList() {
  const {
    permissions,
    viewMode,
    permissionsPagination,
    isLoading,
    fetchPermissions,
    setSelectedPermission
  } = useRolePermissionsStore()

  useEffect(() => {
    // Load permissions when component mounts
    if (permissions.length === 0) {
      fetchPermissions()
    }
  }, [fetchPermissions, permissions.length])

  const handlePageChange = (page: number) => {
    fetchPermissions(page)
  }

  const handlePermissionSelect = (permission: any) => {
    setSelectedPermission(permission)
  }

  if (isLoading && permissions.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (permissions.length === 0) {
    return (
      <EmptyState
        icon={Key}
        title="No permissions found"
        description="No permissions found. Try adjusting your search criteria or add new permissions."
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Permissions Grid/List */}
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {permissions.map((permission) => (
            <PermissionCard
              key={permission.id}
              permission={permission}
              onSelect={handlePermissionSelect}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {permissions.map((permission) => (
            <PermissionListItem
              key={permission.id}
              permission={permission}
              onSelect={handlePermissionSelect}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      <RolePermissionsPagination
        pagination={permissionsPagination}
        onPageChange={handlePageChange}
      />
    </div>
  )
}
