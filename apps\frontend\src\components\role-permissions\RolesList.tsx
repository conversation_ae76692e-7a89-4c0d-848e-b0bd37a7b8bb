'use client'

import { useEffect } from 'react'
import { useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'
import { RoleCard } from './RoleCard'
import { RoleListItem } from './RoleListItem'
import { RolePermissionsPagination } from './RolePermissionsPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle, Shield } from 'lucide-react'

export function RolesList() {
  const {
    roles,
    viewMode,
    rolesPagination,
    isLoading,
    fetchRoles,
    setSelectedRole
  } = useRolePermissionsStore()

  useEffect(() => {
    // Load roles when component mounts
    if (roles.length === 0) {
      fetchRoles()
    }
  }, [fetchRoles, roles.length])

  const handlePageChange = (page: number) => {
    fetchRoles(page)
  }

  const handleRoleSelect = (role: any) => {
    setSelectedRole(role)
  }

  if (isLoading && roles.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (roles.length === 0) {
    return (
      <EmptyState
        icon={Shield}
        title="No roles found"
        description="No roles found. Try adjusting your search criteria or add new roles."
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Roles Grid/List */}
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {roles.map((role) => (
            <RoleCard
              key={role.id}
              role={role}
              onSelect={handleRoleSelect}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {roles.map((role) => (
            <RoleListItem
              key={role.id}
              role={role}
              onSelect={handleRoleSelect}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      <RolePermissionsPagination
        pagination={rolesPagination}
        onPageChange={handlePageChange}
      />
    </div>
  )
}
