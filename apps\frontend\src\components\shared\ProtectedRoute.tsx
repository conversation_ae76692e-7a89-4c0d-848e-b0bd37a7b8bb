'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles: string[]
  redirectTo: string
  authStore: any // We'll pass the appropriate auth store
}

export function ProtectedRoute({ 
  children, 
  allowedRoles, 
  redirectTo, 
  authStore 
}: ProtectedRouteProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthorized, setIsAuthorized] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { user, token, isAuthenticated } = authStore.getState()

        // Check if user is authenticated
        if (!isAuthenticated || !token || !user) {
          router.push(redirectTo)
          return
        }

        // Check if user has the required role using legacyRole
        if (!allowedRoles.includes(user.legacyRole)) {
          router.push(redirectTo)
          return
        }

        // Try to refresh token to ensure it's still valid
        try {
          await authStore.getState().refreshToken()
          setIsAuthorized(true)
        } catch (error) {
          // Token refresh failed, redirect to login
          router.push(redirectTo)
          return
        }

      } catch (error) {
        console.error('Auth check failed:', error)
        router.push(redirectTo)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [allowedRoles, redirectTo, router, authStore])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Verifying access...</p>
        </div>
      </div>
    )
  }

  if (!isAuthorized) {
    return null // Will redirect, so don't render anything
  }

  return <>{children}</>
}

// Specific protected route components for each user type
export function SuperAdminProtectedRoute({ children }: { children: React.ReactNode }) {
  const { useAuthStore } = require('@/stores/super-admin/useAuthStore')
  
  return (
    <ProtectedRoute
      allowedRoles={['super_admin', 'platform_staff']}
      redirectTo="/super-admin/auth/login"
      authStore={useAuthStore}
    >
      {children}
    </ProtectedRoute>
  )
}

export function InstituteAdminProtectedRoute({ children }: { children: React.ReactNode }) {
  const { useAuthStore } = require('@/stores/institute-admin/useAuthStore')
  
  return (
    <ProtectedRoute
      allowedRoles={['institute_admin', 'branch_manager', 'trainer', 'institute_staff']}
      redirectTo="/admin/auth/login"
      authStore={useAuthStore}
    >
      {children}
    </ProtectedRoute>
  )
}

export function StudentProtectedRoute({ children }: { children: React.ReactNode }) {
  const { useAuthStore } = require('@/stores/student/useAuthStore')
  
  return (
    <ProtectedRoute
      allowedRoles={['student']}
      redirectTo="/auth/user-login"
      authStore={useAuthStore}
    >
      {children}
    </ProtectedRoute>
  )
}
