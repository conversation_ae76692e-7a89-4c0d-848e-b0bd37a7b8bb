'use client'

import React from 'react'

// Import UI components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { BookOpen, Users, Award, Star, Phone, Mail, Globe, MapPin, Menu, X } from 'lucide-react'

interface InstituteData {
  id: number
  name: string
  slug: string
  tagline?: string
  description?: string
  email?: string
  phone?: string
  website?: string
  theme?: {
    id: number
    name: string
    slug: string
    colors?: {
      primary: string
      secondary: string
      accent: string
      background: string
      foreground: string
    }
    fonts?: {
      heading: string
      body: string
    }
  }
}

interface InstituteThemeRendererProps {
  institute: InstituteData
  theme?: InstituteData['theme']
}

// Simple institute components that don't require ThemeProvider
function InstituteHeader({ institute }: { institute: InstituteData }) {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false)

  return (
    <header className="bg-background border-b border-border sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Institute Name */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white font-bold">
              {institute.name.charAt(0)}
            </div>
            <div>
              <h1 className="text-xl font-bold text-foreground">
                {institute.name}
              </h1>
              {institute.tagline && (
                <p className="text-xs text-muted-foreground">
                  {institute.tagline}
                </p>
              )}
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#home" className="text-foreground hover:text-primary transition-colors">
              Home
            </a>
            <a href="#courses" className="text-foreground hover:text-primary transition-colors">
              Courses
            </a>
            <a href="#about" className="text-foreground hover:text-primary transition-colors">
              About
            </a>
            <a href="#contact" className="text-foreground hover:text-primary transition-colors">
              Contact
            </a>
          </nav>

          {/* CTA Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="outline" size="sm">
              Login
            </Button>
            <Button size="sm">
              Enroll Now
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <nav className="flex flex-col space-y-4">
              <a href="#home" className="text-foreground hover:text-primary transition-colors">
                Home
              </a>
              <a href="#courses" className="text-foreground hover:text-primary transition-colors">
                Courses
              </a>
              <a href="#about" className="text-foreground hover:text-primary transition-colors">
                About
              </a>
              <a href="#contact" className="text-foreground hover:text-primary transition-colors">
                Contact
              </a>
              <div className="flex flex-col space-y-2 pt-4">
                <Button variant="outline" size="sm">
                  Login
                </Button>
                <Button size="sm">
                  Enroll Now
                </Button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

function InstituteHero({ institute }: { institute: InstituteData }) {
  return (
    <section className="py-20 bg-primary text-white">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-4xl font-bold mb-6">
          Welcome to {institute.name}
        </h2>
        <p className="text-xl mb-8 max-w-2xl mx-auto">
          {institute.description || 'Providing quality education and training programs to help you achieve your goals.'}
        </p>

        <div className="flex flex-wrap justify-center gap-4 mb-8">
          <Button size="lg" variant="secondary">
            <BookOpen className="mr-2 h-5 w-5" />
            Browse Courses
          </Button>
          <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-primary">
            <Users className="mr-2 h-5 w-5" />
            Join Now
          </Button>
        </div>
      </div>
    </section>
  )
}

function FeaturedCourses({ institute }: { institute: InstituteData }) {
  const courses = [
    {
      title: "UPSC Civil Services Preparation",
      description: "Comprehensive course covering all aspects of UPSC examination",
      price: "₹15,000",
      duration: "12 months",
      students: 1250
    },
    {
      title: "Banking & Finance Mastery",
      description: "Complete preparation for banking sector examinations",
      price: "₹8,000",
      duration: "6 months",
      students: 890
    },
    {
      title: "SSC Combined Graduate Level",
      description: "Structured preparation for SSC CGL examination",
      price: "₹6,000",
      duration: "8 months",
      students: 650
    }
  ]

  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold mb-4">Featured Courses</h3>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Discover our most popular courses designed to help you achieve your career goals.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {courses.map((course, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">{course.title}</CardTitle>
                <CardDescription>{course.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold text-primary">{course.price}</span>
                    <Badge variant="secondary">{course.duration}</Badge>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Users className="h-4 w-4 mr-1" />
                    {course.students} students enrolled
                  </div>
                  <Button className="w-full">
                    Enroll Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

// Simple placeholder components for missing sections
function InstituteFeatures({ institute }: { institute: InstituteData }) {
  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold mb-4">Why Choose {institute.name}?</h3>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Discover what makes our institute the perfect choice for your educational journey.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <Card>
            <CardHeader>
              <BookOpen className="h-12 w-12 text-primary mb-4" />
              <CardTitle>Expert Faculty</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Learn from industry experts with years of experience in their respective fields.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Users className="h-12 w-12 text-primary mb-4" />
              <CardTitle>Interactive Learning</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Engage in hands-on learning with interactive sessions and practical projects.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Award className="h-12 w-12 text-primary mb-4" />
              <CardTitle>Certified Programs</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Earn recognized certifications that boost your career prospects.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}

function InstituteTestimonials({ institute }: { institute: InstituteData }) {
  const testimonials = [
    {
      name: "Priya Sharma",
      role: "Software Engineer",
      content: "The courses at {institute.name} transformed my career. The practical approach and expert guidance made all the difference.",
      rating: 5
    },
    {
      name: "Rajesh Kumar",
      role: "Data Analyst",
      content: "Excellent faculty and comprehensive curriculum. I highly recommend {institute.name} for anyone looking to upskill.",
      rating: 5
    },
    {
      name: "Anita Patel",
      role: "Project Manager",
      content: "The learning experience was exceptional. The institute provides great support throughout the journey.",
      rating: 5
    }
  ]

  return (
    <section className="py-16 bg-muted/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold mb-4">What Our Students Say</h3>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Hear from our successful graduates about their learning experience.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index}>
              <CardContent className="pt-6">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-4">
                  "{testimonial.content.replace('{institute.name}', institute.name)}"
                </p>
                <div>
                  <p className="font-semibold">{testimonial.name}</p>
                  <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

function InstituteContact({ institute }: { institute: InstituteData }) {
  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold mb-4">Get In Touch</h3>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Ready to start your learning journey? Contact us today!
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12">
          <div className="space-y-6">
            {institute.email && (
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-primary" />
                <span>{institute.email}</span>
              </div>
            )}

            {institute.phone && (
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-primary" />
                <span>{institute.phone}</span>
              </div>
            )}

            {institute.website && (
              <div className="flex items-center space-x-3">
                <Globe className="h-5 w-5 text-primary" />
                <a href={institute.website} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                  {institute.website}
                </a>
              </div>
            )}
          </div>

          <div className="text-center">
            <Button size="lg" className="px-8">
              Enroll Now
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}

function InstituteFooter({ institute }: { institute: InstituteData }) {
  return (
    <footer className="bg-muted py-12">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white font-bold">
                {institute.name.charAt(0)}
              </div>
              <h4 className="text-lg font-bold">{institute.name}</h4>
            </div>
            <p className="text-muted-foreground">
              {institute.tagline || 'Excellence in Education'}
            </p>
          </div>

          <div>
            <h5 className="font-semibold mb-4">Quick Links</h5>
            <ul className="space-y-2 text-muted-foreground">
              <li><a href="#" className="hover:text-primary">About Us</a></li>
              <li><a href="#" className="hover:text-primary">Courses</a></li>
              <li><a href="#" className="hover:text-primary">Instructors</a></li>
              <li><a href="#" className="hover:text-primary">Contact</a></li>
            </ul>
          </div>

          <div>
            <h5 className="font-semibold mb-4">Categories</h5>
            <ul className="space-y-2 text-muted-foreground">
              <li><a href="#" className="hover:text-primary">UPSC Preparation</a></li>
              <li><a href="#" className="hover:text-primary">Banking & Finance</a></li>
              <li><a href="#" className="hover:text-primary">IT & Software</a></li>
              <li><a href="#" className="hover:text-primary">SSC Preparation</a></li>
            </ul>
          </div>

          <div>
            <h5 className="font-semibold mb-4">Contact Info</h5>
            <div className="space-y-2 text-muted-foreground">
              {institute.email && <p>{institute.email}</p>}
              {institute.phone && <p>{institute.phone}</p>}
            </div>
          </div>
        </div>

        <div className="border-t border-border mt-8 pt-8 text-center text-muted-foreground">
          <p>&copy; 2024 {institute.name}. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}

export default function InstituteThemeRenderer({ institute, theme }: InstituteThemeRendererProps) {
  const themeSlug = theme?.slug || 'education-modern'

  console.log('🎨 Rendering institute theme:', {
    instituteName: institute.name,
    themeSlug,
    themeName: theme?.name
  })

  return (
    <>
      <InstituteHeader institute={institute} />
      <InstituteHero institute={institute} />
      <FeaturedCourses institute={institute} />
      <InstituteFeatures institute={institute} />
      <InstituteTestimonials institute={institute} />
      <InstituteContact institute={institute} />
      <InstituteFooter institute={institute} />
    </>
  )
}
