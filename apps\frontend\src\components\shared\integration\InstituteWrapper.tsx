'use client'

import React from 'react'

interface InstituteData {
  id: number
  name: string
  slug: string
  tagline?: string
  description?: string
  email?: string
  phone?: string
  website?: string
  theme?: {
    id: number
    name: string
    slug: string
    colors?: {
      primary: string
      secondary: string
      accent: string
      background: string
      foreground: string
    }
    fonts?: {
      heading: string
      body: string
    }
  }
}

interface InstituteWrapperProps {
  institute: InstituteData
  children: React.ReactNode
}

export default function InstituteWrapper({ institute, children }: InstituteWrapperProps) {
  return (
    <div className="institute-wrapper" data-institute-id={institute.id} data-theme-slug={institute.theme?.slug}>
      {/* Add any institute-specific global styles or context here */}
      <style jsx global>{`
        :root {
          --institute-name: "${institute.name}";
          --institute-tagline: "${institute.tagline || ''}";
        }
      `}</style>
      {children}
    </div>
  )
}
