'use client'

import React, { useEffect, useState } from 'react'
import { ThemeProvider } from '@/components/shared/theme/ThemeProvider'
import { MobileTabs } from '@/components/shared/mobile/MobileOptimized'
import { LazyComponent } from '@/components/shared/performance/LazyComponents'
import { 
  Home, 
  BookOpen, 
  ShoppingCart, 
  Heart, 
  User, 
  Settings,
  BarChart3,
  Palette,
  Users,
  Building
} from 'lucide-react'

interface PlatformWrapperProps {
  children: React.ReactNode
  userType: 'platform' | 'institute' | 'student'
  initialTheme?: any
  initialCustomizations?: any
}

export default function PlatformWrapper({
  children,
  userType,
  initialTheme,
  initialCustomizations
}: PlatformWrapperProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-48 mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-32"></div>
        </div>
      </div>
    )
  }

  return (
    <ThemeProvider
      initialTheme={initialTheme}
      initialCustomizations={initialCustomizations}
      userType={userType === 'student' ? 'institute' : userType}
    >
      <div className="min-h-screen bg-background text-text">
        {children}
      </div>
    </ThemeProvider>
  )
}

interface AdminLayoutProps {
  children: React.ReactNode
  userType: 'platform' | 'institute'
}

export function AdminLayout({ children, userType }: AdminLayoutProps) {
  const tabs = userType === 'platform' ? [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'themes',
      label: 'Themes',
      icon: Palette,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'institutes',
      label: 'Institutes',
      icon: Building,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      content: <LazyComponent>{children}</LazyComponent>
    }
  ] : [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'courses',
      label: 'Courses',
      icon: BookOpen,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'students',
      label: 'Students',
      icon: Users,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'themes',
      label: 'Themes',
      icon: Palette,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      content: <LazyComponent>{children}</LazyComponent>
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <MobileTabs tabs={tabs} defaultTab="dashboard" className="h-screen" />
    </div>
  )
}

interface StudentLayoutProps {
  children: React.ReactNode
}

export function StudentLayout({ children }: StudentLayoutProps) {
  const tabs = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'courses',
      label: 'Courses',
      icon: BookOpen,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'cart',
      label: 'Cart',
      icon: ShoppingCart,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'wishlist',
      label: 'Wishlist',
      icon: Heart,
      content: <LazyComponent>{children}</LazyComponent>
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: User,
      content: <LazyComponent>{children}</LazyComponent>
    }
  ]

  return (
    <div className="min-h-screen">
      <MobileTabs tabs={tabs} defaultTab="home" className="h-screen" />
    </div>
  )
}

interface MarketplaceWrapperProps {
  children: React.ReactNode
  instituteId?: string
  showHeader?: boolean
  showFooter?: boolean
}

export function MarketplaceWrapper({
  children,
  instituteId,
  showHeader = true,
  showFooter = true
}: MarketplaceWrapperProps) {
  return (
    <div className="min-h-screen flex flex-col">
      {showHeader && (
        <LazyComponent fallback={<div className="h-16 bg-white border-b border-gray-200" />}>
          <header className="sticky top-0 z-50">
            {/* Header content would be dynamically loaded based on institute */}
          </header>
        </LazyComponent>
      )}
      
      <main className="flex-1">
        <LazyComponent>
          {children}
        </LazyComponent>
      </main>
      
      {showFooter && (
        <LazyComponent fallback={<div className="h-32 bg-gray-900" />}>
          <footer>
            {/* Footer content */}
          </footer>
        </LazyComponent>
      )}
    </div>
  )
}

interface ThemePreviewWrapperProps {
  children: React.ReactNode
  theme: any
  customizations?: any
  device?: 'desktop' | 'tablet' | 'mobile'
}

export function ThemePreviewWrapper({
  children,
  theme,
  customizations,
  device = 'desktop'
}: ThemePreviewWrapperProps) {
  const deviceClasses = {
    desktop: 'w-full h-full',
    tablet: 'w-[768px] h-[1024px] mx-auto border border-gray-300 rounded-lg overflow-hidden',
    mobile: 'w-[375px] h-[667px] mx-auto border border-gray-300 rounded-lg overflow-hidden'
  }

  return (
    <div className="bg-gray-100 p-4 min-h-screen">
      <div className={deviceClasses[device]}>
        <ThemeProvider
          initialTheme={theme}
          initialCustomizations={customizations}
          userType={theme?.type || 'platform'}
        >
          <div className="w-full h-full overflow-auto">
            {children}
          </div>
        </ThemeProvider>
      </div>
    </div>
  )
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.props.onError?.(error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Something went wrong</h2>
            <p className="text-gray-600 mb-4">
              We're sorry, but something unexpected happened. Please try refreshing the page.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              Refresh Page
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

interface FeatureFlagWrapperProps {
  children: React.ReactNode
  feature: string
  fallback?: React.ReactNode
  userType?: 'platform' | 'institute' | 'student'
}

export function FeatureFlagWrapper({
  children,
  feature,
  fallback = null,
  userType
}: FeatureFlagWrapperProps) {
  const [isEnabled, setIsEnabled] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Mock feature flag check - in real app, this would call an API
    const checkFeatureFlag = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 100))
        
        // Mock feature flags
        const featureFlags: Record<string, boolean> = {
          'course-comparison': true,
          'advanced-search': true,
          'theme-customization': userType === 'platform' || userType === 'institute',
          'mobile-app': true,
          'analytics-dashboard': userType === 'platform' || userType === 'institute',
          'bulk-operations': userType === 'platform',
          'white-label': userType === 'platform'
        }
        
        setIsEnabled(featureFlags[feature] || false)
      } catch (error) {
        console.error('Failed to check feature flag:', error)
        setIsEnabled(false)
      } finally {
        setLoading(false)
      }
    }

    checkFeatureFlag()
  }, [feature, userType])

  if (loading) {
    return <div className="animate-pulse bg-gray-200 h-8 rounded"></div>
  }

  return isEnabled ? <>{children}</> : <>{fallback}</>
}

interface AccessControlWrapperProps {
  children: React.ReactNode
  requiredRole?: 'super_admin' | 'institute_admin' | 'student'
  requiredPermissions?: string[]
  fallback?: React.ReactNode
}

export function AccessControlWrapper({
  children,
  requiredRole,
  requiredPermissions = [],
  fallback = (
    <div className="text-center py-8">
      <p className="text-gray-600">You don't have permission to access this feature.</p>
    </div>
  )
}: AccessControlWrapperProps) {
  const [hasAccess, setHasAccess] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Mock access control check - in real app, this would check user permissions
    const checkAccess = async () => {
      try {
        // Simulate API call to check user permissions
        await new Promise(resolve => setTimeout(resolve, 100))
        
        // Mock user data - in real app, this would come from auth context
        const mockUser = {
          role: 'institute_admin',
          permissions: ['manage_courses', 'view_analytics', 'customize_theme']
        }
        
        let hasRoleAccess = true
        if (requiredRole) {
          hasRoleAccess = mockUser.role === requiredRole || mockUser.role === 'super_admin'
        }
        
        let hasPermissionAccess = true
        if (requiredPermissions.length > 0) {
          hasPermissionAccess = requiredPermissions.every(permission =>
            mockUser.permissions.includes(permission)
          )
        }
        
        setHasAccess(hasRoleAccess && hasPermissionAccess)
      } catch (error) {
        console.error('Failed to check access:', error)
        setHasAccess(false)
      } finally {
        setLoading(false)
      }
    }

    checkAccess()
  }, [requiredRole, requiredPermissions])

  if (loading) {
    return <div className="animate-pulse bg-gray-200 h-8 rounded"></div>
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>
}
