'use client'

import { ReactNode } from 'react'
import { useResponsive } from '@/hooks/useResponsive'

interface ResponsiveContainerProps {
  children: ReactNode
  className?: string
  mobileClassName?: string
  tabletClassName?: string
  desktopClassName?: string
  largeClassName?: string
}

export function ResponsiveContainer({
  children,
  className = '',
  mobileClassName = '',
  tabletClassName = '',
  desktopClassName = '',
  largeClassName = ''
}: ResponsiveContainerProps) {
  const { isMobile, isTablet, isDesktop, isLarge } = useResponsive()

  const getResponsiveClassName = () => {
    let classes = className

    if (isMobile && mobileClassName) {
      classes += ` ${mobileClassName}`
    } else if (isTablet && tabletClassName) {
      classes += ` ${tabletClassName}`
    } else if (isLarge && largeClassName) {
      classes += ` ${largeClassName}`
    } else if (isDesktop && desktopClassName) {
      classes += ` ${desktopClassName}`
    }

    return classes.trim()
  }

  return (
    <div className={getResponsiveClassName()}>
      {children}
    </div>
  )
}

// Responsive Grid Component
interface ResponsiveGridProps {
  children: ReactNode
  className?: string
  mobileColumns?: number
  tabletColumns?: number
  desktopColumns?: number
  largeColumns?: number
  gap?: number
}

export function ResponsiveGrid({
  children,
  className = '',
  mobileColumns = 1,
  tabletColumns = 2,
  desktopColumns = 3,
  largeColumns = 4,
  gap = 4
}: ResponsiveGridProps) {
  const { isMobile, isTablet, isDesktop, isLarge } = useResponsive()

  const getGridColumns = () => {
    if (isMobile) return mobileColumns
    if (isTablet) return tabletColumns
    if (isLarge) return largeColumns
    if (isDesktop) return desktopColumns
    return desktopColumns
  }

  const gridClasses = `grid grid-cols-${getGridColumns()} gap-${gap} ${className}`

  return (
    <div className={gridClasses}>
      {children}
    </div>
  )
}

// Responsive Stack Component
interface ResponsiveStackProps {
  children: ReactNode
  className?: string
  direction?: 'horizontal' | 'vertical' | 'responsive'
  spacing?: number
  align?: 'start' | 'center' | 'end' | 'stretch'
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'
}

export function ResponsiveStack({
  children,
  className = '',
  direction = 'responsive',
  spacing = 4,
  align = 'start',
  justify = 'start'
}: ResponsiveStackProps) {
  const { isMobile } = useResponsive()

  const getFlexDirection = () => {
    if (direction === 'horizontal') return 'flex-row'
    if (direction === 'vertical') return 'flex-col'
    return isMobile ? 'flex-col' : 'flex-row'
  }

  const getAlignItems = () => {
    const alignMap = {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch'
    }
    return alignMap[align]
  }

  const getJustifyContent = () => {
    const justifyMap = {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
      around: 'justify-around',
      evenly: 'justify-evenly'
    }
    return justifyMap[justify]
  }

  const stackClasses = `flex ${getFlexDirection()} ${getAlignItems()} ${getJustifyContent()} gap-${spacing} ${className}`

  return (
    <div className={stackClasses}>
      {children}
    </div>
  )
}

// Responsive Show/Hide Component
interface ResponsiveShowProps {
  children: ReactNode
  on?: ('mobile' | 'tablet' | 'desktop' | 'large')[]
  above?: 'mobile' | 'tablet' | 'desktop'
  below?: 'tablet' | 'desktop' | 'large'
}

export function ResponsiveShow({ children, on, above, below }: ResponsiveShowProps) {
  const { isMobile, isTablet, isDesktop, isLarge } = useResponsive()

  const shouldShow = () => {
    // Show on specific breakpoints
    if (on) {
      return on.some(breakpoint => {
        switch (breakpoint) {
          case 'mobile': return isMobile
          case 'tablet': return isTablet
          case 'desktop': return isDesktop
          case 'large': return isLarge
          default: return false
        }
      })
    }

    // Show above a certain breakpoint
    if (above) {
      switch (above) {
        case 'mobile': return !isMobile
        case 'tablet': return isDesktop || isLarge
        case 'desktop': return isLarge
        default: return true
      }
    }

    // Show below a certain breakpoint
    if (below) {
      switch (below) {
        case 'tablet': return isMobile
        case 'desktop': return isMobile || isTablet
        case 'large': return isMobile || isTablet || isDesktop
        default: return true
      }
    }

    return true
  }

  if (!shouldShow()) {
    return null
  }

  return <>{children}</>
}

// Responsive Text Component
interface ResponsiveTextProps {
  children: ReactNode
  className?: string
  mobileSize?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl'
  tabletSize?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl'
  desktopSize?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl'
  weight?: 'normal' | 'medium' | 'semibold' | 'bold'
}

export function ResponsiveText({
  children,
  className = '',
  mobileSize = 'base',
  tabletSize = 'base',
  desktopSize = 'base',
  weight = 'normal'
}: ResponsiveTextProps) {
  const { isMobile, isTablet } = useResponsive()

  const getTextSize = () => {
    if (isMobile) return `text-${mobileSize}`
    if (isTablet) return `text-${tabletSize}`
    return `text-${desktopSize}`
  }

  const getTextWeight = () => {
    const weightMap = {
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold'
    }
    return weightMap[weight]
  }

  const textClasses = `${getTextSize()} ${getTextWeight()} ${className}`

  return (
    <span className={textClasses}>
      {children}
    </span>
  )
}

// Responsive Spacing Component
interface ResponsiveSpacingProps {
  size?: number
  mobileSize?: number
  tabletSize?: number
  desktopSize?: number
  direction?: 'horizontal' | 'vertical' | 'both'
}

export function ResponsiveSpacing({
  size = 4,
  mobileSize,
  tabletSize,
  desktopSize,
  direction = 'vertical'
}: ResponsiveSpacingProps) {
  const { isMobile, isTablet } = useResponsive()

  const getSpacingSize = () => {
    if (isMobile && mobileSize !== undefined) return mobileSize
    if (isTablet && tabletSize !== undefined) return tabletSize
    if (desktopSize !== undefined) return desktopSize
    return size
  }

  const spacingSize = getSpacingSize()

  const getSpacingClasses = () => {
    switch (direction) {
      case 'horizontal':
        return `mx-${spacingSize}`
      case 'vertical':
        return `my-${spacingSize}`
      case 'both':
        return `m-${spacingSize}`
      default:
        return `my-${spacingSize}`
    }
  }

  return <div className={getSpacingClasses()} />
}

export default ResponsiveContainer
