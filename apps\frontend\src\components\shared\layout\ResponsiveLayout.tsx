'use client'

import { ReactNode } from 'react'
import { useResponsive } from '@/hooks/useResponsive'
import { AppLayout } from '@/components/layout/AppLayout'
import { MobileNavigation, MobileBottomNavigation } from '@/components/shared/navigation/MobileNavigation'
import { ResponsiveContainer } from './ResponsiveContainer'

interface ResponsiveLayoutProps {
  children: ReactNode
  title?: string
  subtitle?: string
  actions?: ReactNode
  showBackButton?: boolean
  onBack?: () => void
}

export function ResponsiveLayout({
  children,
  title,
  subtitle,
  actions,
  showBackButton = false,
  onBack
}: ResponsiveLayoutProps) {
  const { isMobile, isTablet } = useResponsive()

  if (isMobile) {
    return (
      <div className="min-h-screen bg-gray-50 pb-20">
        {/* Mobile Header */}
        <div className="bg-white border-b border-gray-200 sticky top-0 z-30">
          <div className="px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {showBackButton && (
                  <button
                    onClick={onBack}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                )}
                <div>
                  {title && (
                    <h1 className="text-lg font-semibold text-gray-900 truncate">
                      {title}
                    </h1>
                  )}
                  {subtitle && (
                    <p className="text-sm text-gray-600 truncate">
                      {subtitle}
                    </p>
                  )}
                </div>
              </div>
              {actions && (
                <div className="flex items-center space-x-2">
                  {actions}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Content */}
        <div className="px-4 py-4">
          {children}
        </div>

        {/* Mobile Navigation */}
        <MobileNavigation />
        <MobileBottomNavigation />
      </div>
    )
  }

  // Desktop/Tablet Layout
  return (
    <AppLayout>
      <ResponsiveContainer
        className="space-y-6"
        mobileClassName="space-y-4 px-4"
        tabletClassName="space-y-5 px-6"
        desktopClassName="space-y-6"
      >
        {/* Header */}
        {(title || subtitle || actions) && (
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <h1 className={`font-bold text-gray-900 ${
                  isMobile ? 'text-xl' : isTablet ? 'text-2xl' : 'text-3xl'
                }`}>
                  {title}
                </h1>
              )}
              {subtitle && (
                <p className={`text-gray-600 mt-1 ${
                  isMobile ? 'text-sm' : 'text-base'
                }`}>
                  {subtitle}
                </p>
              )}
            </div>
            {actions && (
              <div className="flex items-center space-x-3">
                {actions}
              </div>
            )}
          </div>
        )}

        {/* Content */}
        {children}
      </ResponsiveContainer>
    </AppLayout>
  )
}

// Responsive Page Container
interface ResponsivePageProps {
  children: ReactNode
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: boolean
}

export function ResponsivePage({ 
  children, 
  maxWidth = 'full', 
  padding = true 
}: ResponsivePageProps) {
  const { isMobile } = useResponsive()

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  }

  return (
    <div className={`mx-auto ${maxWidthClasses[maxWidth]} ${
      padding ? (isMobile ? 'px-4' : 'px-6') : ''
    }`}>
      {children}
    </div>
  )
}

// Responsive Card Component
interface ResponsiveCardProps {
  children: ReactNode
  className?: string
  padding?: boolean
  shadow?: boolean
  border?: boolean
}

export function ResponsiveCard({ 
  children, 
  className = '', 
  padding = true, 
  shadow = true, 
  border = true 
}: ResponsiveCardProps) {
  const { isMobile } = useResponsive()

  const cardClasses = `
    bg-white rounded-lg
    ${padding ? (isMobile ? 'p-4' : 'p-6') : ''}
    ${shadow ? 'shadow-sm hover:shadow-md transition-shadow' : ''}
    ${border ? 'border border-gray-200' : ''}
    ${className}
  `.trim()

  return (
    <div className={cardClasses}>
      {children}
    </div>
  )
}

// Responsive Section Component
interface ResponsiveSectionProps {
  children: ReactNode
  title?: string
  subtitle?: string
  actions?: ReactNode
  className?: string
}

export function ResponsiveSection({ 
  children, 
  title, 
  subtitle, 
  actions, 
  className = '' 
}: ResponsiveSectionProps) {
  const { isMobile } = useResponsive()

  return (
    <div className={`space-y-4 ${className}`}>
      {(title || subtitle || actions) && (
        <div className={`flex justify-between ${
          isMobile ? 'flex-col space-y-2' : 'items-center'
        }`}>
          <div>
            {title && (
              <h2 className={`font-semibold text-gray-900 ${
                isMobile ? 'text-lg' : 'text-xl'
              }`}>
                {title}
              </h2>
            )}
            {subtitle && (
              <p className={`text-gray-600 ${
                isMobile ? 'text-sm' : 'text-base'
              }`}>
                {subtitle}
              </p>
            )}
          </div>
          {actions && (
            <div className="flex items-center space-x-2">
              {actions}
            </div>
          )}
        </div>
      )}
      {children}
    </div>
  )
}

// Responsive Button Component
interface ResponsiveButtonProps {
  children: ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  disabled?: boolean
  className?: string
}

export function ResponsiveButton({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  disabled = false,
  className = ''
}: ResponsiveButtonProps) {
  const { isMobile } = useResponsive()

  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2'
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-blue-500'
  }

  const sizeClasses = {
    sm: isMobile ? 'px-3 py-2 text-sm' : 'px-3 py-1.5 text-sm',
    md: isMobile ? 'px-4 py-3 text-base' : 'px-4 py-2 text-sm',
    lg: isMobile ? 'px-6 py-4 text-lg' : 'px-6 py-3 text-base'
  }

  const buttonClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${sizeClasses[size]}
    ${fullWidth ? 'w-full' : ''}
    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
    ${className}
  `.trim()

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={buttonClasses}
    >
      {children}
    </button>
  )
}

export default ResponsiveLayout
