'use client'

import { Fragment } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useResponsive } from '@/hooks/useResponsive'
import { 
  ChevronRight, 
  Home, 
  MoreHorizontal,
  ArrowLeft
} from 'lucide-react'
import { navigationUtils } from '@/utils/navigationConfig'

interface BreadcrumbItem {
  label: string
  href: string
  isActive: boolean
  icon?: React.ComponentType<any>
}

interface BreadcrumbsProps {
  maxItems?: number
  showHomeIcon?: boolean
  showBackButton?: boolean
  onBack?: () => void
  className?: string
}

export function Breadcrumbs({ 
  maxItems = 4, 
  showHomeIcon = true, 
  showBackButton = false,
  onBack,
  className = '' 
}: BreadcrumbsProps) {
  const pathname = usePathname()
  const { navigationItems, userType } = useSidebarStore()
  const { isMobile } = useResponsive()

  // Generate breadcrumbs from current path and navigation structure
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = pathname.split('/').filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = []

    // Add home breadcrumb
    const homeHref = `/${userType?.replace('_', '-') || ''}`
    breadcrumbs.push({
      label: 'Dashboard',
      href: homeHref,
      isActive: pathname === homeHref,
      icon: showHomeIcon ? Home : undefined
    })

    // Build breadcrumbs from navigation structure
    let currentPath = ''
    for (let i = 0; i < pathSegments.length; i++) {
      currentPath += `/${pathSegments[i]}`
      
      // Skip the user type segment (e.g., 'super-admin', 'institute-admin', 'student')
      if (i === 0 && pathSegments[i].includes('admin')) continue
      if (i === 0 && pathSegments[i] === 'student') continue
      
      // Find matching navigation item
      const navItem = navigationUtils.findNavigationItemByHref(navigationItems, currentPath)
      
      if (navItem) {
        breadcrumbs.push({
          label: navItem.label,
          href: currentPath,
          isActive: i === pathSegments.length - 1
        })
      } else {
        // Fallback to formatted segment name
        const label = pathSegments[i]
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')
        
        breadcrumbs.push({
          label,
          href: currentPath,
          isActive: i === pathSegments.length - 1
        })
      }
    }

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  // Handle breadcrumb overflow
  const getDisplayBreadcrumbs = () => {
    if (breadcrumbs.length <= maxItems) {
      return breadcrumbs
    }

    const firstItem = breadcrumbs[0]
    const lastItems = breadcrumbs.slice(-2) // Always show last 2 items
    const middleItems = breadcrumbs.slice(1, -2)

    if (middleItems.length === 0) {
      return breadcrumbs
    }

    return [
      firstItem,
      { label: '...', href: '#', isActive: false },
      ...lastItems
    ]
  }

  const displayBreadcrumbs = getDisplayBreadcrumbs()

  // Mobile breadcrumbs (simplified)
  if (isMobile) {
    const currentItem = breadcrumbs[breadcrumbs.length - 1]
    const parentItem = breadcrumbs.length > 1 ? breadcrumbs[breadcrumbs.length - 2] : null

    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {showBackButton && onBack && (
          <button
            onClick={onBack}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-4 h-4 text-gray-600" />
          </button>
        )}
        
        {parentItem && (
          <>
            <Link
              href={parentItem.href}
              className="text-sm text-gray-600 hover:text-gray-900 transition-colors truncate max-w-24"
            >
              {parentItem.label}
            </Link>
            <ChevronRight className="w-3 h-3 text-gray-400 flex-shrink-0" />
          </>
        )}
        
        <span className="text-sm font-medium text-gray-900 truncate">
          {currentItem?.label}
        </span>
      </div>
    )
  }

  // Desktop breadcrumbs
  return (
    <nav className={`flex items-center space-x-1 ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {displayBreadcrumbs.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="w-4 h-4 text-gray-400 mx-2 flex-shrink-0" />
            )}
            
            {item.label === '...' ? (
              <div className="flex items-center space-x-1">
                <MoreHorizontal className="w-4 h-4 text-gray-400" />
              </div>
            ) : item.isActive ? (
              <span className="flex items-center space-x-1 text-sm font-medium text-gray-900">
                {item.icon && <item.icon className="w-4 h-4" />}
                <span>{item.label}</span>
              </span>
            ) : (
              <Link
                href={item.href}
                className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                {item.icon && <item.icon className="w-4 h-4" />}
                <span>{item.label}</span>
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

// Breadcrumb separator component
export function BreadcrumbSeparator({ className = '' }: { className?: string }) {
  return <ChevronRight className={`w-4 h-4 text-gray-400 ${className}`} />
}

// Custom breadcrumb component for manual breadcrumbs
interface CustomBreadcrumbsProps {
  items: Array<{
    label: string
    href?: string
    isActive?: boolean
    icon?: React.ComponentType<any>
  }>
  className?: string
}

export function CustomBreadcrumbs({ items, className = '' }: CustomBreadcrumbsProps) {
  const { isMobile } = useResponsive()

  if (isMobile && items.length > 2) {
    const currentItem = items[items.length - 1]
    const parentItem = items[items.length - 2]

    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {parentItem.href ? (
          <Link
            href={parentItem.href}
            className="text-sm text-gray-600 hover:text-gray-900 transition-colors truncate max-w-24"
          >
            {parentItem.label}
          </Link>
        ) : (
          <span className="text-sm text-gray-600 truncate max-w-24">
            {parentItem.label}
          </span>
        )}
        <ChevronRight className="w-3 h-3 text-gray-400 flex-shrink-0" />
        <span className="text-sm font-medium text-gray-900 truncate">
          {currentItem.label}
        </span>
      </div>
    )
  }

  return (
    <nav className={`flex items-center space-x-1 ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="w-4 h-4 text-gray-400 mx-2 flex-shrink-0" />
            )}
            
            {item.isActive || !item.href ? (
              <span className="flex items-center space-x-1 text-sm font-medium text-gray-900">
                {item.icon && <item.icon className="w-4 h-4" />}
                <span>{item.label}</span>
              </span>
            ) : (
              <Link
                href={item.href}
                className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                {item.icon && <item.icon className="w-4 h-4" />}
                <span>{item.label}</span>
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

export default Breadcrumbs
