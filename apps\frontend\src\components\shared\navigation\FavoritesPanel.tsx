'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useResponsive } from '@/hooks/useResponsive'
import { 
  Star, 
  StarOff, 
  MoreVertical,
  ExternalLink,
  Trash2,
  Grid3X3,
  List,
  Search,
  Plus
} from 'lucide-react'
import * as Icons from 'lucide-react'
import { navigationUtils } from '@/utils/navigationConfig'

interface FavoritesPanelProps {
  className?: string
  showHeader?: boolean
  maxItems?: number
}

export function FavoritesPanel({ 
  className = '', 
  showHeader = true, 
  maxItems 
}: FavoritesPanelProps) {
  const { 
    favoriteItems, 
    navigationItems, 
    removeFromFavorites,
    addToRecent 
  } = useSidebarStore()
  const { isMobile } = useResponsive()
  
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchQuery, setSearchQuery] = useState('')

  // Get favorite navigation items
  const favoriteNavItems = favoriteItems
    .map(id => navigationUtils.findNavigationItem(navigationItems, id))
    .filter(Boolean)
    .slice(0, maxItems)

  // Filter favorites based on search
  const filteredFavorites = favoriteNavItems.filter(item =>
    !searchQuery || 
    item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleItemClick = (item: any) => {
    addToRecent(item.id)
  }

  const handleRemoveFavorite = (itemId: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    removeFromFavorites(itemId)
  }

  if (favoriteItems.length === 0) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <Star className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Favorites Yet</h3>
        <p className="text-gray-600 mb-4">
          Add navigation items to your favorites for quick access
        </p>
        <div className="text-sm text-gray-500">
          Click the star icon next to any navigation item to add it to favorites
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Header */}
      {showHeader && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Star className="w-5 h-5 text-yellow-500" />
            <h3 className="text-lg font-medium text-gray-900">
              Favorites ({favoriteItems.length})
            </h3>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Search */}
            {favoriteNavItems.length > 4 && (
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search favorites..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-7 pr-3 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            )}
            
            {/* View Mode Toggle */}
            {!isMobile && (
              <div className="flex items-center border border-gray-300 rounded">
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-1 ${
                    viewMode === 'list' 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-600 hover:bg-gray-50'
                  } rounded-l transition-colors`}
                >
                  <List className="w-3 h-3" />
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-1 ${
                    viewMode === 'grid' 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-600 hover:bg-gray-50'
                  } rounded-r transition-colors`}
                >
                  <Grid3X3 className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Favorites List */}
      {filteredFavorites.length === 0 ? (
        <div className="text-center py-8">
          <Search className="w-8 h-8 text-gray-300 mx-auto mb-2" />
          <div className="text-sm text-gray-500">
            No favorites match your search
          </div>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? `grid gap-3 ${isMobile ? 'grid-cols-1' : 'grid-cols-2 lg:grid-cols-3'}`
            : 'space-y-2'
        }>
          {filteredFavorites.map((item) => (
            <FavoriteItem
              key={item.id}
              item={item}
              viewMode={viewMode}
              onClick={() => handleItemClick(item)}
              onRemove={(e) => handleRemoveFavorite(item.id, e)}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Individual favorite item component
interface FavoriteItemProps {
  item: any
  viewMode: 'grid' | 'list'
  onClick: () => void
  onRemove: (e: React.MouseEvent) => void
}

function FavoriteItem({ item, viewMode, onClick, onRemove }: FavoriteItemProps) {
  const [showMenu, setShowMenu] = useState(false)
  const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>

  if (viewMode === 'grid') {
    return (
      <Link
        href={item.href}
        onClick={onClick}
        className="group relative p-3 bg-white border border-gray-200 rounded-lg hover:shadow-md hover:border-gray-300 transition-all duration-200"
      >
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2 flex-1 min-w-0">
            {IconComponent && (
              <IconComponent className="w-4 h-4 text-blue-600 flex-shrink-0" />
            )}
            <div className="min-w-0">
              <div className="text-sm font-medium text-gray-900 truncate">
                {item.label}
              </div>
              {item.description && (
                <div className="text-xs text-gray-500 truncate">
                  {item.description}
                </div>
              )}
            </div>
          </div>
          
          <button
            onClick={onRemove}
            className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-100 rounded transition-all duration-200"
          >
            <StarOff className="w-3 h-3 text-gray-400 hover:text-red-500" />
          </button>
        </div>
        
        {item.badge && item.badge > 0 && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            {item.badge > 9 ? '9+' : item.badge}
          </div>
        )}
      </Link>
    )
  }

  return (
    <Link
      href={item.href}
      onClick={onClick}
      className="group flex items-center justify-between p-2 bg-white border border-gray-200 rounded hover:shadow-sm hover:border-gray-300 transition-all duration-200"
    >
      <div className="flex items-center space-x-2 flex-1 min-w-0">
        {IconComponent && (
          <IconComponent className="w-4 h-4 text-blue-600 flex-shrink-0" />
        )}
        <div className="min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate">
            {item.label}
          </div>
        </div>
        {item.badge && item.badge > 0 && (
          <span className="px-1.5 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
            {item.badge > 9 ? '9+' : item.badge}
          </span>
        )}
      </div>
      
      <button
        onClick={onRemove}
        className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-100 rounded transition-all duration-200"
      >
        <StarOff className="w-3 h-3 text-gray-400 hover:text-red-500" />
      </button>
    </Link>
  )
}

// Compact favorites for sidebar
export function CompactFavorites() {
  const { favoriteItems, navigationItems, addToRecent } = useSidebarStore()
  
  const favoriteNavItems = favoriteItems
    .map(id => navigationUtils.findNavigationItem(navigationItems, id))
    .filter(Boolean)
    .slice(0, 5) // Show max 5 in compact view

  if (favoriteItems.length === 0) {
    return null
  }

  return (
    <div className="px-3 py-2">
      <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
        Favorites
      </div>
      <div className="space-y-1">
        {favoriteNavItems.map((item) => {
          const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>
          
          return (
            <Link
              key={item.id}
              href={item.href}
              onClick={() => addToRecent(item.id)}
              className="flex items-center space-x-2 px-2 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors"
            >
              {IconComponent && (
                <IconComponent className="w-4 h-4 text-gray-400" />
              )}
              <span className="truncate">{item.label}</span>
              {item.badge && item.badge > 0 && (
                <span className="ml-auto px-1.5 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
                  {item.badge > 9 ? '9+' : item.badge}
                </span>
              )}
            </Link>
          )
        })}
      </div>
    </div>
  )
}

export default FavoritesPanel
