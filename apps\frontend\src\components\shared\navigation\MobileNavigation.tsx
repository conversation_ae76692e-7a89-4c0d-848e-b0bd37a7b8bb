'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSidebarStore, NavigationItem } from '@/stores/sidebar/useSidebarStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useResponsive } from '@/hooks/useResponsive'
import { 
  Home, 
  Menu, 
  X, 
  User, 
  Settings, 
  LogOut,
  ChevronRight
} from 'lucide-react'
import * as Icons from 'lucide-react'

export function MobileNavigation() {
  const pathname = usePathname()
  const { isMobile } = useResponsive()
  const { navigationItems } = useSidebarStore()
  const { user, logout } = useAuthStore()
  const [isOpen, setIsOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  if (!isMobile) return null

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const handleItemClick = () => {
    setIsOpen(false)
    setExpandedItems([])
  }

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>
    const isActive = pathname === item.href
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.id)

    return (
      <div key={item.id}>
        {hasChildren ? (
          <button
            onClick={() => toggleExpanded(item.id)}
            className={`w-full flex items-center justify-between px-4 py-3 text-left transition-colors ${
              level > 0 ? 'pl-8' : ''
            } ${
              isActive 
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' 
                : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center space-x-3">
              {IconComponent && (
                <IconComponent className={`w-5 h-5 ${
                  isActive ? 'text-blue-600' : 'text-gray-400'
                }`} />
              )}
              <span className="font-medium">{item.label}</span>
              {item.badge && item.badge > 0 && (
                <span className="ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
                  {item.badge > 9 ? '9+' : item.badge}
                </span>
              )}
            </div>
            <ChevronRight className={`w-4 h-4 text-gray-400 transition-transform ${
              isExpanded ? 'rotate-90' : ''
            }`} />
          </button>
        ) : (
          <Link
            href={item.href}
            onClick={handleItemClick}
            className={`flex items-center space-x-3 px-4 py-3 transition-colors ${
              level > 0 ? 'pl-8' : ''
            } ${
              isActive 
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' 
                : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            {IconComponent && (
              <IconComponent className={`w-5 h-5 ${
                isActive ? 'text-blue-600' : 'text-gray-400'
              }`} />
            )}
            <span className="font-medium">{item.label}</span>
            {item.badge && item.badge > 0 && (
              <span className="ml-auto px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
                {item.badge > 9 ? '9+' : item.badge}
              </span>
            )}
          </Link>
        )}

        {/* Children */}
        {hasChildren && isExpanded && (
          <div className="bg-gray-50">
            {item.children?.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      {/* Mobile Navigation Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 z-50 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors lg:hidden"
      >
        <Menu className="w-6 h-6" />
      </button>

      {/* Mobile Navigation Overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={() => setIsOpen(false)}
          />

          {/* Navigation Panel */}
          <div className="fixed inset-y-0 right-0 w-80 max-w-[85vw] bg-white shadow-xl">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Navigation</h2>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            {/* User Info */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  {user?.personalInfo?.avatar ? (
                    <img 
                      src={user.personalInfo.avatar} 
                      alt={user.personalInfo.fullName || user.email}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-5 h-5 text-white" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {user?.personalInfo?.fullName || user?.email || 'User'}
                  </div>
                  <div className="text-xs text-gray-500 capitalize">
                    {user?.role?.name || 'User'}
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Items */}
            <div className="flex-1 overflow-y-auto">
              <nav className="py-2">
                {navigationItems.map(item => renderNavigationItem(item))}
              </nav>
            </div>

            {/* Footer Actions */}
            <div className="border-t border-gray-200 p-4 space-y-2">
              <Link
                href="/settings"
                onClick={handleItemClick}
                className="flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
              >
                <Settings className="w-5 h-5 text-gray-400" />
                <span className="font-medium">Settings</span>
              </Link>
              <button
                onClick={() => {
                  logout()
                  handleItemClick()
                }}
                className="flex items-center space-x-3 w-full px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <LogOut className="w-5 h-5" />
                <span className="font-medium">Sign Out</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

// Bottom Navigation Bar for Mobile
export function MobileBottomNavigation() {
  const pathname = usePathname()
  const { isMobile } = useResponsive()
  const { navigationItems } = useSidebarStore()

  if (!isMobile) return null

  // Get the most important navigation items for bottom bar
  const bottomNavItems = navigationItems.slice(0, 4).map(item => ({
    ...item,
    isActive: pathname === item.href || pathname.startsWith(item.href + '/')
  }))

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-40 lg:hidden">
      <div className="grid grid-cols-4 h-16">
        {bottomNavItems.map(item => {
          const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>
          
          return (
            <Link
              key={item.id}
              href={item.href}
              className={`flex flex-col items-center justify-center space-y-1 transition-colors ${
                item.isActive 
                  ? 'text-blue-600 bg-blue-50' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <div className="relative">
                {IconComponent && <IconComponent className="w-5 h-5" />}
                {item.badge && item.badge > 0 && (
                  <span className="absolute -top-2 -right-2 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {item.badge > 9 ? '9+' : item.badge}
                  </span>
                )}
              </div>
              <span className="text-xs font-medium truncate max-w-full">
                {item.label}
              </span>
            </Link>
          )
        })}
      </div>
    </div>
  )
}

export default MobileNavigation
