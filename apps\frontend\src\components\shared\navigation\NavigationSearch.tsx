'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useResponsive } from '@/hooks/useResponsive'
import { 
  Search, 
  X, 
  ArrowRight,
  Clock,
  Star,
  Zap,
  Command
} from 'lucide-react'
import * as Icons from 'lucide-react'
import { navigationUtils } from '@/utils/navigationConfig'

interface NavigationSearchProps {
  placeholder?: string
  showShortcut?: boolean
  onItemSelect?: (item: any) => void
  className?: string
}

export function NavigationSearch({ 
  placeholder = "Search navigation...", 
  showShortcut = true,
  onItemSelect,
  className = '' 
}: NavigationSearchProps) {
  const { 
    navigationItems, 
    recentItems, 
    favoriteItems,
    addToRecent 
  } = useSidebarStore()
  const { isMobile } = useResponsive()
  
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [searchResults, setSearchResults] = useState<any[]>([])
  
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Search navigation items
  useEffect(() => {
    if (query.trim()) {
      const results = navigationUtils.searchNavigationItems(navigationItems, query)
      setSearchResults(results)
      setSelectedIndex(0)
    } else {
      setSearchResults([])
    }
  }, [query, navigationItems])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K to open search
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setIsOpen(true)
        setTimeout(() => inputRef.current?.focus(), 100)
      }
      
      // Escape to close
      if (e.key === 'Escape') {
        setIsOpen(false)
        setQuery('')
      }
      
      // Arrow navigation
      if (isOpen && searchResults.length > 0) {
        if (e.key === 'ArrowDown') {
          e.preventDefault()
          setSelectedIndex(prev => 
            prev < searchResults.length - 1 ? prev + 1 : 0
          )
        } else if (e.key === 'ArrowUp') {
          e.preventDefault()
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : searchResults.length - 1
          )
        } else if (e.key === 'Enter') {
          e.preventDefault()
          const selectedItem = searchResults[selectedIndex]
          if (selectedItem) {
            handleItemSelect(selectedItem)
          }
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, searchResults, selectedIndex])

  // Click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const handleItemSelect = (item: any) => {
    addToRecent(item)
    onItemSelect?.(item)
    setIsOpen(false)
    setQuery('')
  }

  const getQuickActions = () => {
    const actions = []
    
    // Recent items
    if (recentItems.length > 0) {
      actions.push({
        category: 'Recent',
        icon: Clock,
        items: recentItems.slice(0, 3)
      })
    }
    
    // Favorite items
    if (favoriteItems.length > 0) {
      const favoriteNavItems = favoriteItems
        .map(id => navigationUtils.findNavigationItem(navigationItems, id))
        .filter(Boolean)
        .slice(0, 3)
      
      if (favoriteNavItems.length > 0) {
        actions.push({
          category: 'Favorites',
          icon: Star,
          items: favoriteNavItems
        })
      }
    }
    
    // Quick access items
    const quickAccessItems = navigationItems
      .filter(item => item.badge && item.badge > 0)
      .slice(0, 3)
    
    if (quickAccessItems.length > 0) {
      actions.push({
        category: 'Quick Access',
        icon: Zap,
        items: quickAccessItems
      })
    }
    
    return actions
  }

  const quickActions = getQuickActions()

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="w-4 h-4 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className={`w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm ${
            isMobile ? 'text-base' : ''
          }`}
        />
        {showShortcut && !isMobile && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <div className="flex items-center space-x-1 text-xs text-gray-400">
              <Command className="w-3 h-3" />
              <span>K</span>
            </div>
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {query.trim() ? (
            // Search Results
            <div>
              {searchResults.length > 0 ? (
                <div className="p-2">
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wider px-2 py-1 mb-2">
                    Search Results ({searchResults.length})
                  </div>
                  {searchResults.map((item, index) => (
                    <SearchResultItem
                      key={item.id}
                      item={item}
                      isSelected={index === selectedIndex}
                      onClick={() => handleItemSelect(item)}
                    />
                  ))}
                </div>
              ) : (
                <div className="p-4 text-center text-gray-500">
                  <Search className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <div className="text-sm">No results found for "{query}"</div>
                </div>
              )}
            </div>
          ) : (
            // Quick Actions
            <div className="p-2">
              {quickActions.length > 0 ? (
                quickActions.map((section, sectionIndex) => (
                  <div key={sectionIndex} className="mb-4 last:mb-0">
                    <div className="flex items-center space-x-2 px-2 py-1 mb-2">
                      <section.icon className="w-4 h-4 text-gray-400" />
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {section.category}
                      </span>
                    </div>
                    {section.items.map((item: any, itemIndex: number) => (
                      <SearchResultItem
                        key={item.id}
                        item={item}
                        isSelected={false}
                        onClick={() => handleItemSelect(item)}
                      />
                    ))}
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-gray-500">
                  <div className="text-sm">Start typing to search navigation...</div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// Search result item component
interface SearchResultItemProps {
  item: any
  isSelected: boolean
  onClick: () => void
}

function SearchResultItem({ item, isSelected, onClick }: SearchResultItemProps) {
  const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>

  return (
    <button
      onClick={onClick}
      className={`w-full flex items-center space-x-3 px-2 py-2 rounded-lg text-left transition-colors ${
        isSelected ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'
      }`}
    >
      {/* Icon */}
      <div className={`flex-shrink-0 ${
        isSelected ? 'text-blue-600' : 'text-gray-400'
      }`}>
        {IconComponent && <IconComponent className="w-4 h-4" />}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-gray-900 truncate">
          {item.label}
        </div>
        {item.description && (
          <div className="text-xs text-gray-500 truncate">
            {item.description}
          </div>
        )}
      </div>

      {/* Badge */}
      {item.badge && item.badge > 0 && (
        <span className="flex-shrink-0 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
          {item.badge > 9 ? '9+' : item.badge}
        </span>
      )}

      {/* Arrow */}
      <ArrowRight className="w-3 h-3 text-gray-400 flex-shrink-0" />
    </button>
  )
}

// Global search modal for mobile
export function GlobalSearchModal() {
  const [isOpen, setIsOpen] = useState(false)
  const { isMobile } = useResponsive()

  if (!isMobile) return null

  return (
    <>
      {/* Search Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
      >
        <Search className="w-5 h-5 text-gray-600" />
      </button>

      {/* Modal */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
          <div className="bg-white h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Search</h2>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            {/* Search Content */}
            <div className="p-4">
              <NavigationSearch
                placeholder="Search navigation..."
                showShortcut={false}
                onItemSelect={() => setIsOpen(false)}
              />
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default NavigationSearch
