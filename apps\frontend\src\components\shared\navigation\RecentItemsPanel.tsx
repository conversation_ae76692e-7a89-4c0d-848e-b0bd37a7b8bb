'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useResponsive } from '@/hooks/useResponsive'
import { 
  Clock, 
  X,
  ExternalLink,
  Trash2,
  RotateCcw,
  Calendar,
  Search
} from 'lucide-react'
import * as Icons from 'lucide-react'
import { navigationUtils } from '@/utils/navigationConfig'

interface RecentItemsPanelProps {
  className?: string
  showHeader?: boolean
  maxItems?: number
}

export function RecentItemsPanel({ 
  className = '', 
  showHeader = true, 
  maxItems = 10 
}: RecentItemsPanelProps) {
  const { 
    recentItems, 
    navigationItems, 
    clearRecent,
    addToFavorites,
    favoriteItems
  } = useSidebarStore()
  const { isMobile } = useResponsive()
  
  const [searchQuery, setSearchQuery] = useState('')

  // Get recent navigation items with details
  const recentNavItems = recentItems
    .map(itemId => navigationUtils.findNavigationItem(navigationItems, itemId))
    .filter(Boolean)
    .slice(0, maxItems)

  // Filter recent items based on search
  const filteredRecent = recentNavItems.filter(item =>
    !searchQuery || 
    item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    
    return time.toLocaleDateString()
  }

  const handleAddToFavorites = (itemId: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    addToFavorites(itemId)
  }

  if (recentItems.length === 0) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <Clock className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Recent Items</h3>
        <p className="text-gray-600 mb-4">
          Your recently accessed navigation items will appear here
        </p>
        <div className="text-sm text-gray-500">
          Start navigating to see your recent activity
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Header */}
      {showHeader && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Clock className="w-5 h-5 text-blue-500" />
            <h3 className="text-lg font-medium text-gray-900">
              Recent Items ({recentItems.length})
            </h3>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Search */}
            {recentNavItems.length > 4 && (
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search recent..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-7 pr-3 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            )}
            
            {/* Clear All */}
            <button
              onClick={clearRecent}
              className="flex items-center space-x-1 px-2 py-1 text-xs text-red-600 hover:bg-red-50 rounded transition-colors"
            >
              <Trash2 className="w-3 h-3" />
              <span>Clear</span>
            </button>
          </div>
        </div>
      )}

      {/* Recent Items List */}
      {filteredRecent.length === 0 ? (
        <div className="text-center py-8">
          <Search className="w-8 h-8 text-gray-300 mx-auto mb-2" />
          <div className="text-sm text-gray-500">
            No recent items match your search
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          {filteredRecent.map((item, index) => (
            <RecentItem
              key={`${item.id}-${index}`}
              item={item}
              isFavorite={favoriteItems.includes(item.id)}
              onAddToFavorites={(e) => handleAddToFavorites(item.id, e)}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Individual recent item component
interface RecentItemProps {
  item: any
  isFavorite: boolean
  onAddToFavorites: (e: React.MouseEvent) => void
}

function RecentItem({ item, isFavorite, onAddToFavorites }: RecentItemProps) {
  const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>

  return (
    <Link
      href={item.href}
      className="group flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:shadow-sm hover:border-gray-300 transition-all duration-200"
    >
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        {/* Icon */}
        <div className="flex-shrink-0">
          {IconComponent && (
            <IconComponent className="w-4 h-4 text-blue-600" />
          )}
        </div>
        
        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <div className="text-sm font-medium text-gray-900 truncate">
              {item.label}
            </div>
            {item.badge && item.badge > 0 && (
              <span className="px-1.5 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
                {item.badge > 9 ? '9+' : item.badge}
              </span>
            )}
          </div>
          {item.description && (
            <div className="text-xs text-gray-500 truncate">
              {item.description}
            </div>
          )}
        </div>
        
        {/* Time */}
        <div className="flex-shrink-0 text-xs text-gray-400">
          {/* This would show actual timestamp in real implementation */}
          Recently
        </div>
      </div>
      
      {/* Actions */}
      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
        {!isFavorite && (
          <button
            onClick={onAddToFavorites}
            className="p-1 hover:bg-yellow-50 rounded transition-colors"
            title="Add to favorites"
          >
            <Clock className="w-3 h-3 text-gray-400 hover:text-yellow-500" />
          </button>
        )}
        <ExternalLink className="w-3 h-3 text-gray-400" />
      </div>
    </Link>
  )
}

// Compact recent items for sidebar
export function CompactRecentItems() {
  const { recentItems, navigationItems } = useSidebarStore()
  
  const recentNavItems = recentItems
    .map(itemId => navigationUtils.findNavigationItem(navigationItems, itemId))
    .filter(Boolean)
    .slice(0, 5) // Show max 5 in compact view

  if (recentItems.length === 0) {
    return null
  }

  return (
    <div className="px-3 py-2">
      <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
        Recent
      </div>
      <div className="space-y-1">
        {recentNavItems.map((item, index) => {
          const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>
          
          return (
            <Link
              key={`${item.id}-${index}`}
              href={item.href}
              className="flex items-center space-x-2 px-2 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors"
            >
              {IconComponent && (
                <IconComponent className="w-4 h-4 text-gray-400" />
              )}
              <span className="truncate">{item.label}</span>
              {item.badge && item.badge > 0 && (
                <span className="ml-auto px-1.5 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
                  {item.badge > 9 ? '9+' : item.badge}
                </span>
              )}
            </Link>
          )
        })}
      </div>
    </div>
  )
}

// Recent items dropdown for header
export function RecentItemsDropdown() {
  const [isOpen, setIsOpen] = useState(false)
  const { recentItems, navigationItems } = useSidebarStore()
  
  const recentNavItems = recentItems
    .map(itemId => navigationUtils.findNavigationItem(navigationItems, itemId))
    .filter(Boolean)
    .slice(0, 8)

  if (recentItems.length === 0) {
    return null
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 hover:bg-gray-100 rounded-lg transition-colors relative"
      >
        <Clock className="w-5 h-5 text-gray-600" />
        {recentItems.length > 0 && (
          <span className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center">
            {recentItems.length > 9 ? '9+' : recentItems.length}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 top-12 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-900">Recent Items</h3>
          </div>
          <div className="max-h-64 overflow-y-auto">
            {recentNavItems.map((item, index) => {
              const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>
              
              return (
                <Link
                  key={`${item.id}-${index}`}
                  href={item.href}
                  onClick={() => setIsOpen(false)}
                  className="flex items-center space-x-3 p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                >
                  {IconComponent && (
                    <IconComponent className="w-4 h-4 text-blue-600" />
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {item.label}
                    </div>
                    <div className="text-xs text-gray-500 truncate">
                      {item.description}
                    </div>
                  </div>
                  {item.badge && item.badge > 0 && (
                    <span className="px-1.5 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
                      {item.badge > 9 ? '9+' : item.badge}
                    </span>
                  )}
                </Link>
              )
            })}
          </div>
          <div className="p-3 border-t border-gray-200">
            <button
              onClick={() => {
                // clearRecent()
                setIsOpen(false)
              }}
              className="text-sm text-red-600 hover:text-red-700"
            >
              Clear recent items
            </button>
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export default RecentItemsPanel
