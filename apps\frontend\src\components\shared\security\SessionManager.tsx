'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  MapPin, 
  Clock, 
  Shield, 
  LogOut,
  AlertTriangle 
} from 'lucide-react'

interface ActiveSession {
  id: string
  deviceType: 'desktop' | 'mobile' | 'tablet'
  deviceName: string
  browser: string
  operatingSystem: string
  ipAddress: string
  location: {
    city: string
    country: string
    region: string
  }
  loginTime: string
  lastActivity: string
  isCurrent: boolean
  isSecure: boolean
}

interface SessionLimits {
  maxDesktopSessions: number
  maxMobileSessions: number
  maxTotalSessions: number
  sessionTimeout: number // in minutes
  requireSecureConnection: boolean
  allowConcurrentSessions: boolean
}

export function SessionManager({ userType }: { userType: 'super_admin' | 'institute_admin' | 'student' }) {
  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([])
  const [sessionLimits, setSessionLimits] = useState<SessionLimits | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchActiveSessions()
    fetchSessionLimits()
  }, [])

  const fetchActiveSessions = async () => {
    try {
      const response = await fetch('/api/auth/sessions')
      const data = await response.json()
      setActiveSessions(data.sessions || [])
    } catch (error) {
      toast.error('Failed to fetch active sessions')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchSessionLimits = async () => {
    try {
      const response = await fetch('/api/auth/session-limits')
      const data = await response.json()
      setSessionLimits(data.limits)
    } catch (error) {
      console.error('Failed to fetch session limits')
    }
  }

  const terminateSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/auth/sessions/${sessionId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success('Session Terminated', {
          description: 'The session has been successfully terminated.'
        })
        fetchActiveSessions()
      } else {
        throw new Error('Failed to terminate session')
      }
    } catch (error) {
      toast.error('Termination Failed', {
        description: 'Unable to terminate the session.'
      })
    }
  }

  const terminateAllOtherSessions = async () => {
    try {
      const response = await fetch('/api/auth/sessions/terminate-others', {
        method: 'POST'
      })

      if (response.ok) {
        toast.success('Sessions Terminated', {
          description: 'All other sessions have been terminated.'
        })
        fetchActiveSessions()
      } else {
        throw new Error('Failed to terminate sessions')
      }
    } catch (error) {
      toast.error('Termination Failed', {
        description: 'Unable to terminate other sessions.'
      })
    }
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'desktop':
        return <Monitor className="h-4 w-4" />
      case 'mobile':
        return <Smartphone className="h-4 w-4" />
      case 'tablet':
        return <Tablet className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  const getSessionStatus = (session: ActiveSession) => {
    if (session.isCurrent) {
      return <Badge variant="default">Current Session</Badge>
    }
    if (!session.isSecure) {
      return <Badge variant="destructive">Insecure</Badge>
    }
    return <Badge variant="secondary">Active</Badge>
  }

  const isSessionLimitExceeded = () => {
    if (!sessionLimits) return false
    
    const desktopSessions = activeSessions.filter(s => s.deviceType === 'desktop').length
    const mobileSessions = activeSessions.filter(s => s.deviceType === 'mobile').length
    const totalSessions = activeSessions.length

    return (
      desktopSessions > sessionLimits.maxDesktopSessions ||
      mobileSessions > sessionLimits.maxMobileSessions ||
      totalSessions > sessionLimits.maxTotalSessions
    )
  }

  if (isLoading) {
    return <div>Loading session information...</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Active Sessions</h2>
        <p className="text-gray-600">Manage your active login sessions across devices</p>
      </div>

      {/* Session Limits Warning */}
      {isSessionLimitExceeded() && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            You have exceeded the maximum number of allowed sessions. Please terminate some sessions to continue.
          </AlertDescription>
        </Alert>
      )}

      {/* Session Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sessions</p>
                <p className="text-2xl font-bold text-gray-900">{activeSessions.length}</p>
              </div>
              <Shield className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Desktop</p>
                <p className="text-2xl font-bold text-gray-900">
                  {activeSessions.filter(s => s.deviceType === 'desktop').length}
                </p>
              </div>
              <Monitor className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Mobile</p>
                <p className="text-2xl font-bold text-gray-900">
                  {activeSessions.filter(s => s.deviceType === 'mobile').length}
                </p>
              </div>
              <Smartphone className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Insecure</p>
                <p className="text-2xl font-bold text-red-600">
                  {activeSessions.filter(s => !s.isSecure).length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Session Limits Info */}
      {sessionLimits && (
        <Card>
          <CardHeader>
            <CardTitle>Session Limits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <Label>Desktop Sessions</Label>
                <p className="text-gray-600">
                  {activeSessions.filter(s => s.deviceType === 'desktop').length} / {sessionLimits.maxDesktopSessions}
                </p>
              </div>
              <div>
                <Label>Mobile Sessions</Label>
                <p className="text-gray-600">
                  {activeSessions.filter(s => s.deviceType === 'mobile').length} / {sessionLimits.maxMobileSessions}
                </p>
              </div>
              <div>
                <Label>Session Timeout</Label>
                <p className="text-gray-600">{sessionLimits.sessionTimeout} minutes</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Sessions Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Active Sessions</CardTitle>
            <Button 
              variant="outline" 
              onClick={terminateAllOtherSessions}
              disabled={activeSessions.length <= 1}
            >
              <LogOut className="h-4 w-4 mr-2" />
              Terminate All Others
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Device</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Login Time</TableHead>
                <TableHead>Last Activity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {activeSessions.map((session) => (
                <TableRow key={session.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      {getDeviceIcon(session.deviceType)}
                      <div>
                        <p className="font-medium">{session.deviceName}</p>
                        <p className="text-sm text-gray-500">
                          {session.browser} on {session.operatingSystem}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm">{session.location.city}, {session.location.country}</p>
                        <p className="text-xs text-gray-500">{session.ipAddress}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">
                        {new Date(session.loginTime).toLocaleString()}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">
                      {new Date(session.lastActivity).toLocaleString()}
                    </span>
                  </TableCell>
                  <TableCell>
                    {getSessionStatus(session)}
                  </TableCell>
                  <TableCell>
                    {!session.isCurrent && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => terminateSession(session.id)}
                      >
                        <LogOut className="h-4 w-4 mr-1" />
                        Terminate
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
