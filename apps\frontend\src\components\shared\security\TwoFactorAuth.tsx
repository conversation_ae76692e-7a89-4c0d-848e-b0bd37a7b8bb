'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { Shield, Smartphone, Key, CheckCircle, AlertTriangle } from 'lucide-react'

interface TwoFactorStatus {
  isEnabled: boolean
  backupCodes: string[]
  lastUsed?: string
  method: 'app' | 'sms' | null
}

export function TwoFactorAuth() {
  const [tfaStatus, setTfaStatus] = useState<TwoFactorStatus>({
    isEnabled: false,
    backupCodes: [],
    method: null
  })
  const [setupStep, setSetupStep] = useState<'initial' | 'qr' | 'verify' | 'backup'>('initial')
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('')
  const [verificationCode, setVerificationCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const initiate2FASetup = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/2fa/setup', {
        method: 'POST'
      })
      
      if (response.ok) {
        const data = await response.json()
        setQrCodeUrl(data.qrCodeUrl)
        setSetupStep('qr')
      } else {
        throw new Error('Failed to initiate 2FA setup')
      }
    } catch (error) {
      toast.error('Setup Failed', {
        description: 'Unable to initiate 2FA setup'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const verify2FASetup = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      toast.error('Invalid Code', {
        description: 'Please enter a 6-digit verification code'
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/2fa/verify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: verificationCode })
      })
      
      if (response.ok) {
        const data = await response.json()
        setTfaStatus({
          isEnabled: true,
          backupCodes: data.backupCodes,
          method: 'app'
        })
        setSetupStep('backup')
        toast.success('2FA Enabled', {
          description: 'Two-factor authentication has been successfully enabled'
        })
      } else {
        throw new Error('Invalid verification code')
      }
    } catch (error) {
      toast.error('Verification Failed', {
        description: 'Invalid verification code'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const disable2FA = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/2fa/disable', {
        method: 'POST'
      })
      
      if (response.ok) {
        setTfaStatus({
          isEnabled: false,
          backupCodes: [],
          method: null
        })
        setSetupStep('initial')
        toast.warning('2FA Disabled', {
          description: 'Two-factor authentication has been disabled'
        })
      } else {
        throw new Error('Failed to disable 2FA')
      }
    } catch (error) {
      toast.error('Disable Failed', {
        description: 'Unable to disable 2FA'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const generateNewBackupCodes = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/2fa/backup-codes', {
        method: 'POST'
      })
      
      if (response.ok) {
        const data = await response.json()
        setTfaStatus(prev => ({
          ...prev,
          backupCodes: data.backupCodes
        }))
        toast.success('Backup Codes Generated', {
          description: 'New backup codes have been generated'
        })
      } else {
        throw new Error('Failed to generate backup codes')
      }
    } catch (error) {
      toast.error('Generation Failed', {
        description: 'Unable to generate new backup codes'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Two-Factor Authentication</h2>
        <p className="text-gray-600">Add an extra layer of security to your account</p>
      </div>

      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Current Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {tfaStatus.isEnabled ? (
                <CheckCircle className="h-6 w-6 text-green-500" />
              ) : (
                <AlertTriangle className="h-6 w-6 text-yellow-500" />
              )}
              <div>
                <p className="font-medium">
                  {tfaStatus.isEnabled ? 'Two-Factor Authentication Enabled' : 'Two-Factor Authentication Disabled'}
                </p>
                <p className="text-sm text-gray-600">
                  {tfaStatus.isEnabled 
                    ? `Using ${tfaStatus.method === 'app' ? 'Authenticator App' : 'SMS'}`
                    : 'Your account is not protected by 2FA'
                  }
                </p>
              </div>
            </div>
            <Badge variant={tfaStatus.isEnabled ? 'default' : 'secondary'}>
              {tfaStatus.isEnabled ? 'Enabled' : 'Disabled'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Setup/Management */}
      {!tfaStatus.isEnabled ? (
        <Card>
          <CardHeader>
            <CardTitle>Enable Two-Factor Authentication</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {setupStep === 'initial' && (
              <div className="space-y-4">
                <Alert>
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    Two-factor authentication adds an extra layer of security to your account by requiring a verification code from your mobile device.
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Smartphone className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Download an Authenticator App</p>
                      <p className="text-sm text-gray-600">
                        Install Google Authenticator, Authy, or similar app on your mobile device
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Key className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Scan QR Code</p>
                      <p className="text-sm text-gray-600">
                        Use your authenticator app to scan the QR code we'll provide
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-purple-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Verify Setup</p>
                      <p className="text-sm text-gray-600">
                        Enter the verification code to complete setup
                      </p>
                    </div>
                  </div>
                </div>

                <Button onClick={initiate2FASetup} disabled={isLoading}>
                  {isLoading ? 'Setting up...' : 'Start Setup'}
                </Button>
              </div>
            )}

            {setupStep === 'qr' && (
              <div className="space-y-4">
                <div className="text-center">
                  <p className="font-medium mb-4">Scan this QR code with your authenticator app:</p>
                  <div className="inline-block p-4 bg-white border rounded-lg">
                    {/* QR Code would be rendered here */}
                    <div className="w-48 h-48 bg-gray-100 flex items-center justify-center">
                      QR Code Placeholder
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mt-4">
                    Can't scan? Enter this code manually: <code className="bg-gray-100 px-2 py-1 rounded">{qrCodeUrl}</code>
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="verificationCode">Enter verification code from your app:</Label>
                  <Input
                    id="verificationCode"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    placeholder="000000"
                    maxLength={6}
                    className="text-center text-lg tracking-widest"
                  />
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" onClick={() => setSetupStep('initial')}>
                    Back
                  </Button>
                  <Button onClick={verify2FASetup} disabled={isLoading || verificationCode.length !== 6}>
                    {isLoading ? 'Verifying...' : 'Verify & Enable'}
                  </Button>
                </div>
              </div>
            )}

            {setupStep === 'backup' && (
              <div className="space-y-4">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>2FA Successfully Enabled!</strong> Save these backup codes in a secure location. You can use them to access your account if you lose your authenticator device.
                  </AlertDescription>
                </Alert>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="font-medium mb-2">Backup Codes:</p>
                  <div className="grid grid-cols-2 gap-2 font-mono text-sm">
                    {tfaStatus.backupCodes.map((code, index) => (
                      <div key={index} className="bg-white p-2 rounded border">
                        {code}
                      </div>
                    ))}
                  </div>
                </div>

                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Important:</strong> Each backup code can only be used once. Store them securely and don't share them with anyone.
                  </AlertDescription>
                </Alert>

                <Button onClick={() => setSetupStep('initial')}>
                  Complete Setup
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* Backup Codes Management */}
          <Card>
            <CardHeader>
              <CardTitle>Backup Codes</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">
                You have {tfaStatus.backupCodes.length} unused backup codes remaining.
              </p>
              
              <div className="flex space-x-2">
                <Button variant="outline" onClick={generateNewBackupCodes} disabled={isLoading}>
                  Generate New Codes
                </Button>
                <Button variant="outline">
                  Download Codes
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Disable 2FA */}
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Disable Two-Factor Authentication</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Disabling 2FA will make your account less secure. Only disable if you're having issues with your authenticator app.
                </AlertDescription>
              </Alert>
              
              <Button variant="destructive" onClick={disable2FA} disabled={isLoading}>
                {isLoading ? 'Disabling...' : 'Disable 2FA'}
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
