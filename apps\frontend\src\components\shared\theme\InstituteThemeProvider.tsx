'use client'

import React, { createContext, useContext, useEffect } from 'react'

interface Institute {
  id: string
  name: string
  slug: string
  custom_domain?: string
  logo?: string
  settings?: any
}

interface Theme {
  id: string
  name: string
  slug: string
  type: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    text: string
  }
  fonts: {
    heading: string
    body: string
  }
  customizableElements?: any
}

interface InstituteThemeContextType {
  institute: Institute
  theme: Theme
  customDomain: string
  applyTheme: () => void
}

const InstituteThemeContext = createContext<InstituteThemeContextType | null>(null)

export function InstituteThemeProvider({
  children,
  institute,
  theme,
  customDomain
}: {
  children: React.ReactNode
  institute: Institute
  theme: Theme
  customDomain: string
}) {
  
  const applyTheme = () => {
    console.log('🎨 Applying institute theme:', theme.name)
    
    // Apply CSS custom properties
    const root = document.documentElement
    
    // Apply theme colors
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })
    
    // Apply theme fonts
    Object.entries(theme.fonts).forEach(([key, value]) => {
      root.style.setProperty(`--font-${key}`, value)
    })
    
    // Apply institute customizations if available
    if (theme.customizableElements) {
      Object.entries(theme.customizableElements.colors || {}).forEach(([key, value]) => {
        root.style.setProperty(`--color-${key}`, value as string)
      })
    }
    
    // Set institute logo
    if (institute.logo) {
      root.style.setProperty('--institute-logo', `url(${institute.logo})`)
    }
    
    // Set page title
    document.title = `${institute.name} - Online Learning Platform`
    
    // Set favicon if available
    if (institute.settings?.favicon) {
      const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
      if (favicon) {
        favicon.href = institute.settings.favicon
      } else {
        // Create favicon link if it doesn't exist
        const newFavicon = document.createElement('link')
        newFavicon.rel = 'icon'
        newFavicon.href = institute.settings.favicon
        document.head.appendChild(newFavicon)
      }
    }

    // Set meta description
    const metaDescription = document.querySelector('meta[name="description"]') as HTMLMetaElement
    if (metaDescription) {
      metaDescription.content = `Discover courses and enhance your skills with ${institute.name}`
    }

    // Set Open Graph meta tags
    const ogTitle = document.querySelector('meta[property="og:title"]') as HTMLMetaElement
    if (ogTitle) {
      ogTitle.content = `${institute.name} - Online Learning Platform`
    }

    const ogDescription = document.querySelector('meta[property="og:description"]') as HTMLMetaElement
    if (ogDescription) {
      ogDescription.content = `Discover courses and enhance your skills with ${institute.name}`
    }

    // Set canonical URL for custom domain
    if (customDomain) {
      const canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
      if (canonical) {
        canonical.href = `https://${customDomain}`
      } else {
        const newCanonical = document.createElement('link')
        newCanonical.rel = 'canonical'
        newCanonical.href = `https://${customDomain}`
        document.head.appendChild(newCanonical)
      }
    }
  }

  useEffect(() => {
    applyTheme()
  }, [theme, institute])

  const contextValue = {
    institute,
    theme,
    customDomain,
    applyTheme
  }

  return (
    <InstituteThemeContext.Provider value={contextValue}>
      {children}
    </InstituteThemeContext.Provider>
  )
}

export const useInstituteTheme = () => {
  const context = useContext(InstituteThemeContext)
  if (!context) {
    throw new Error('useInstituteTheme must be used within InstituteThemeProvider')
  }
  return context
}
