'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { onThemeChange, type ThemeChangeEvent } from '@/lib/theme-utils'

// Theme interfaces
export interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  background: string
  text: string
  muted?: string
  border?: string
  success?: string
  warning?: string
  error?: string
}

export interface ThemeFonts {
  heading: string
  body: string
  mono?: string
}

export interface Theme {
  id: string
  name: string
  slug: string
  type: 'platform' | 'institute'
  version: string
  description: string
  category: string
  colors: ThemeColors
  fonts: ThemeFonts
  features: string[]
  isActive: boolean
  isDefault: boolean
  rating?: {
    average: number
    count: number
  }
}

export interface ThemeCustomizations {
  colors?: Partial<ThemeColors>
  fonts?: Partial<ThemeFonts>
  logo?: string
  favicon?: string
  content?: Record<string, any>
  customCSS?: string
}

interface ThemeContextType {
  currentTheme: Theme | null
  setTheme: (theme: Theme) => void
  customizations: ThemeCustomizations
  updateCustomizations: (customizations: Partial<ThemeCustomizations>) => void
  isLoading: boolean
  error: string | null
  refreshTheme: () => Promise<void>
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

interface ThemeProviderProps {
  children: React.ReactNode
  initialTheme?: Theme
  initialCustomizations?: ThemeCustomizations
  userType?: 'platform' | 'institute'
}

export function ThemeProvider({
  children,
  initialTheme,
  initialCustomizations,
  userType = 'platform'
}: ThemeProviderProps) {
  const [currentTheme, setCurrentTheme] = useState<Theme | null>(initialTheme || null)
  const [customizations, setCustomizations] = useState<ThemeCustomizations>(
    initialCustomizations || {}
  )
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Apply theme CSS variables to document root
  const applyThemeVariables = (theme: Theme, customizations: ThemeCustomizations) => {
    const root = document.documentElement
    const colors = { ...theme.colors, ...customizations.colors }
    const fonts = { ...theme.fonts, ...customizations.fonts }

    // Apply color variables
    Object.entries(colors).forEach(([key, value]) => {
      if (value) {
        root.style.setProperty(`--color-${key}`, value)
      }
    })

    // Apply font variables
    Object.entries(fonts).forEach(([key, value]) => {
      if (value) {
        root.style.setProperty(`--font-${key}`, value)
      }
    })

    // Apply custom CSS if provided
    if (customizations.customCSS) {
      let customStyleElement = document.getElementById('theme-custom-css')
      if (!customStyleElement) {
        customStyleElement = document.createElement('style')
        customStyleElement.id = 'theme-custom-css'
        document.head.appendChild(customStyleElement)
      }
      customStyleElement.textContent = customizations.customCSS
    }

    // Update favicon if provided
    if (customizations.favicon) {
      const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
      if (favicon) {
        favicon.href = customizations.favicon
      }
    }
  }

  // Set theme and apply variables
  const setTheme = (theme: Theme) => {
    setCurrentTheme(theme)
    applyThemeVariables(theme, customizations)
    
    // Store theme preference
    localStorage.setItem('selectedTheme', JSON.stringify({
      id: theme.id,
      slug: theme.slug
    }))
  }

  // Update customizations and apply changes
  const updateCustomizations = (newCustomizations: Partial<ThemeCustomizations>) => {
    const updated = { ...customizations, ...newCustomizations }
    setCustomizations(updated)
    
    if (currentTheme) {
      applyThemeVariables(currentTheme, updated)
    }
    
    // Store customizations
    localStorage.setItem('themeCustomizations', JSON.stringify(updated))
  }

  // Fetch current theme from API
  const refreshTheme = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const endpoint = userType === 'platform'
        ? '/api/themes-platform-current'
        : '/api/institute-admin/themes/current'

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`, {
        credentials: 'include',
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch theme')
      }
      
      const data = await response.json()
      
      if (data.success && data.currentTheme) {
        setCurrentTheme(data.currentTheme)
        
        if (data.customizations) {
          setCustomizations(data.customizations)
        }
        
        applyThemeVariables(data.currentTheme, data.customizations || {})
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load theme')
      console.error('Theme fetch error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // Load theme on mount
  useEffect(() => {
    if (!initialTheme) {
      refreshTheme()
    } else if (currentTheme) {
      applyThemeVariables(currentTheme, customizations)
    }
  }, [])

  // Apply theme variables when theme or customizations change
  useEffect(() => {
    if (currentTheme) {
      applyThemeVariables(currentTheme, customizations)
    }
  }, [currentTheme, customizations])

  // Listen for theme change events from other components
  useEffect(() => {
    const handleThemeChanged = (event: ThemeChangeEvent) => {
      console.log('🎨 ThemeProvider: Received theme change event', event)
      // Refresh theme data when theme is changed from other components
      refreshTheme()
    }

    const cleanup = onThemeChange(handleThemeChanged)
    return cleanup
  }, [])

  const value: ThemeContextType = {
    currentTheme,
    setTheme,
    customizations,
    updateCustomizations,
    isLoading,
    error,
    refreshTheme
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

// Hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// Hook to get theme-aware styles
export const useThemeStyles = () => {
  const { currentTheme, customizations } = useTheme()
  
  const getColor = (colorKey: keyof ThemeColors) => {
    return customizations.colors?.[colorKey] || currentTheme?.colors[colorKey] || ''
  }
  
  const getFont = (fontKey: keyof ThemeFonts) => {
    return customizations.fonts?.[fontKey] || currentTheme?.fonts[fontKey] || ''
  }
  
  return {
    colors: currentTheme?.colors || {},
    fonts: currentTheme?.fonts || {},
    getColor,
    getFont,
    customizations
  }
}

// Default theme fallback
export const defaultTheme: Theme = {
  id: 'default',
  name: 'Default Theme',
  slug: 'default',
  type: 'platform',
  version: '1.0.0',
  description: 'Default platform theme',
  category: 'default',
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    accent: '#10b981',
    background: '#ffffff',
    text: '#1f2937',
    muted: '#6b7280',
    border: '#e5e7eb',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444'
  },
  fonts: {
    heading: 'Inter, sans-serif',
    body: 'Inter, sans-serif',
    mono: 'Fira Code, monospace'
  },
  features: ['responsive', 'accessible'],
  isActive: true,
  isDefault: true
}
