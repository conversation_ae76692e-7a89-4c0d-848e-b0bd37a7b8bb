'use client'

import { useState, useEffect, createContext, useContext, ReactNode } from 'react'
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'

// Toast types
export type ToastType = 'success' | 'error' | 'warning' | 'info'

export interface Toast {
  id: string
  type: ToastType
  title: string
  message?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

// Toast context
interface ToastContextType {
  toasts: Toast[]
  addToast: (toast: Omit<Toast, 'id'>) => void
  removeToast: (id: string) => void
  clearToasts: () => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

// Toast provider
interface ToastProviderProps {
  children: ReactNode
}

export function ToastProvider({ children }: ToastProviderProps) {
  const [toasts, setToasts] = useState<Toast[]>([])

  const addToast = (toast: Omit<Toast, 'id'>) => {
    const id = Date.now().toString()
    const newToast = { ...toast, id }

    setToasts(prev => [...prev, newToast])

    // Auto remove toast after duration
    const duration = toast.duration || 5000
    if (duration > 0) {
      setTimeout(() => {
        removeToast(id)
      }, duration)
    }
  }

  // Subscribe to global toast manager
  useEffect(() => {
    const unsubscribe = toastManager.subscribe(addToast)
    return unsubscribe
  }, [addToast])

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const clearToasts = () => {
    setToasts([])
  }

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast, clearToasts }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  )
}

// Hook to use toast
export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

// Toast container component
function ToastContainer() {
  const { toasts, removeToast } = useToast()

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map(toast => (
        <ToastItem
          key={toast.id}
          toast={toast}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </div>
  )
}

// Individual toast item
interface ToastItemProps {
  toast: Toast
  onClose: () => void
}

function ToastItem({ toast, onClose }: ToastItemProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isLeaving, setIsLeaving] = useState(false)

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 10)
    return () => clearTimeout(timer)
  }, [])

  const handleClose = () => {
    setIsLeaving(true)
    setTimeout(onClose, 300) // Wait for exit animation
  }

  const getToastStyles = () => {
    const baseStyles = "flex items-start p-4 rounded-lg shadow-lg border max-w-md transition-all duration-300 transform"
    
    const typeStyles = {
      success: "bg-green-50 border-green-200 text-green-800",
      error: "bg-red-50 border-red-200 text-red-800",
      warning: "bg-yellow-50 border-yellow-200 text-yellow-800",
      info: "bg-blue-50 border-blue-200 text-blue-800"
    }

    const animationStyles = isLeaving
      ? "translate-x-full opacity-0"
      : isVisible
      ? "translate-x-0 opacity-100"
      : "translate-x-full opacity-0"

    return `${baseStyles} ${typeStyles[toast.type]} ${animationStyles}`
  }

  const getIcon = () => {
    const iconProps = { className: "w-5 h-5 flex-shrink-0 mt-0.5" }
    
    switch (toast.type) {
      case 'success':
        return <CheckCircle {...iconProps} className="w-5 h-5 flex-shrink-0 mt-0.5 text-green-600" />
      case 'error':
        return <AlertCircle {...iconProps} className="w-5 h-5 flex-shrink-0 mt-0.5 text-red-600" />
      case 'warning':
        return <AlertTriangle {...iconProps} className="w-5 h-5 flex-shrink-0 mt-0.5 text-yellow-600" />
      case 'info':
        return <Info {...iconProps} className="w-5 h-5 flex-shrink-0 mt-0.5 text-blue-600" />
      default:
        return <Info {...iconProps} />
    }
  }

  return (
    <div className={getToastStyles()}>
      {/* Icon */}
      {getIcon()}

      {/* Content */}
      <div className="ml-3 flex-1">
        <div className="font-medium text-sm">
          {toast.title}
        </div>
        {toast.message && (
          <div className="text-sm opacity-90 mt-1">
            {toast.message}
          </div>
        )}
        {toast.action && (
          <button
            onClick={toast.action.onClick}
            className="text-sm font-medium underline mt-2 hover:no-underline"
          >
            {toast.action.label}
          </button>
        )}
      </div>

      {/* Close button */}
      <button
        onClick={handleClose}
        className="ml-3 flex-shrink-0 p-1 rounded-lg hover:bg-black hover:bg-opacity-10 transition-colors"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  )
}

// Utility functions for easy toast creation (use toast instead for better compatibility)
export const showToast = toast

// Helper hook for toast utilities
function useToastContext() {
  const context = useContext(ToastContext)
  if (!context) {
    // Fallback for when used outside provider
    return {
      addToast: (toast: Omit<Toast, 'id'>) => {
        console.warn('Toast used outside ToastProvider:', toast)
      }
    }
  }
  return context
}

// Global toast manager for use outside React components
class ToastManager {
  private listeners: Array<(toast: Omit<Toast, 'id'>) => void> = []

  subscribe(listener: (toast: Omit<Toast, 'id'>) => void) {
    this.listeners.push(listener)
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  private emit(toast: Omit<Toast, 'id'>) {
    this.listeners.forEach(listener => listener(toast))
  }

  success(title: string, message?: string, options?: Partial<Toast>) {
    this.emit({ type: 'success', title, message, ...options })
  }

  error(title: string, message?: string, options?: Partial<Toast>) {
    this.emit({ type: 'error', title, message, ...options })
  }

  warning(title: string, message?: string, options?: Partial<Toast>) {
    this.emit({ type: 'warning', title, message, ...options })
  }

  info(title: string, message?: string, options?: Partial<Toast>) {
    this.emit({ type: 'info', title, message, ...options })
  }
}

// Global toast instance
const toastManager = new ToastManager()

// Static toast functions that work without hooks
export const toast = {
  success: (title: string, message?: string, options?: Partial<Toast>) => {
    toastManager.success(title, message, options)
  },
  error: (title: string, message?: string, options?: Partial<Toast>) => {
    toastManager.error(title, message, options)
  },
  warning: (title: string, message?: string, options?: Partial<Toast>) => {
    toastManager.warning(title, message, options)
  },
  info: (title: string, message?: string, options?: Partial<Toast>) => {
    toastManager.info(title, message, options)
  }
}

// Export the manager for provider integration
export { toastManager }
