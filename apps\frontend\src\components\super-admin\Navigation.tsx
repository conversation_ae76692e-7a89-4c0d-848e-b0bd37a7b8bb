'use client'

import { useAuthStore } from '@/stores/auth/useAuthStore'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

export default function Navigation() {
  const { user, logout } = useAuthStore()
  const pathname = usePathname()

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/super-admin',
      icon: '📊'
    },
    {
      name: 'Institutes',
      href: '/super-admin/institutes',
      icon: '🏢'
    },
    {
      name: 'Users',
      href: '/super-admin/users',
      icon: '👥'
    },
    {
      name: 'Platform Blog',
      href: '/super-admin/platform-blog',
      icon: '📝'
    },
    {
      name: 'Themes',
      href: '/super-admin/themes',
      icon: '🎨'
    },
    {
      name: 'Settings',
      href: '/super-admin/settings',
      icon: '⚙️'
    },
    {
      name: 'Analytics',
      href: '/super-admin/analytics',
      icon: '📈'
    },
    {
      name: 'Billing',
      href: '/super-admin/billing',
      icon: '💳'
    }
  ]

  return (
    <nav className="bg-blue-900 text-white w-64 min-h-screen p-4">
      {/* Logo/Brand */}
      <div className="mb-8">
        <h1 className="text-xl font-bold">Super Admin</h1>
        <p className="text-blue-200 text-sm">Groups Exam LMS</p>
      </div>

      {/* User Info */}
      {user && (
        <div className="mb-6 p-3 bg-blue-800 rounded-lg">
          <p className="font-medium">{user.firstName} {user.lastName}</p>
          <p className="text-blue-200 text-sm">{user.email}</p>
          <p className="text-blue-300 text-xs capitalize">
            {typeof user.role === 'string' ? user.role.replace('_', ' ') : String(user.legacyRole || 'Unknown').replace('_', ' ')}
          </p>
        </div>
      )}

      {/* Navigation Items */}
      <ul className="space-y-2">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href
          return (
            <li key={item.href}>
              <Link
                href={item.href}
                className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                  isActive
                    ? 'bg-blue-700 text-white'
                    : 'text-blue-100 hover:bg-blue-800 hover:text-white'
                }`}
              >
                <span className="text-lg">{item.icon}</span>
                <span>{item.name}</span>
              </Link>
            </li>
          )
        })}
      </ul>

      {/* Logout Button */}
      <div className="mt-8 pt-4 border-t border-blue-700">
        <button
          onClick={logout}
          className="flex items-center space-x-3 p-3 rounded-lg text-blue-100 hover:bg-red-600 hover:text-white transition-colors w-full"
        >
          <span className="text-lg">🚪</span>
          <span>Logout</span>
        </button>
      </div>
    </nav>
  )
}
