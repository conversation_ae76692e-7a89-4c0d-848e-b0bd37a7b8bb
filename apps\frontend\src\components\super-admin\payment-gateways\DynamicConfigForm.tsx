'use client'

import React from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Eye, EyeOff, Info } from 'lucide-react'
import { useState } from 'react'

interface ConfigField {
  type: 'string' | 'number' | 'boolean' | 'textarea'
  label: string
  description?: string
  sensitive?: boolean
  required?: boolean
}

interface ConfigSchema {
  [key: string]: ConfigField
}

interface DynamicConfigFormProps {
  gatewayName: string
  requiredConfigFields: ConfigSchema
  optionalConfigFields: ConfigSchema
  onSubmit: (values: Record<string, any>) => void
  initialValues?: Record<string, any>
  isLoading?: boolean
}

export default function DynamicConfigForm({
  gatewayName,
  requiredConfigFields,
  optionalConfigFields,
  onSubmit,
  initialValues = {},
  isLoading = false
}: DynamicConfigFormProps) {
  const [showSensitiveFields, setShowSensitiveFields] = useState<Record<string, boolean>>({})

  // Create validation schema dynamically
  const createValidationSchema = () => {
    const schema: Record<string, any> = {}
    
    // Required fields validation
    Object.entries(requiredConfigFields || {}).forEach(([key, field]) => {
      if (field.type === 'string' || field.type === 'textarea') {
        schema[key] = Yup.string().required(`${field.label} is required`)
      } else if (field.type === 'number') {
        schema[key] = Yup.number().required(`${field.label} is required`)
      } else if (field.type === 'boolean') {
        schema[key] = Yup.boolean()
      }
    })
    
    // Optional fields validation
    Object.entries(optionalConfigFields || {}).forEach(([key, field]) => {
      if (field.type === 'string' || field.type === 'textarea') {
        schema[key] = Yup.string()
      } else if (field.type === 'number') {
        schema[key] = Yup.number()
      } else if (field.type === 'boolean') {
        schema[key] = Yup.boolean()
      }
    })
    
    return Yup.object().shape(schema)
  }

  // Create initial values dynamically
  const createInitialValues = () => {
    const values: Record<string, any> = {}
    
    // Set initial values for required fields
    Object.keys(requiredConfigFields || {}).forEach(key => {
      values[key] = initialValues[key] || ''
    })
    
    // Set initial values for optional fields
    Object.keys(optionalConfigFields || {}).forEach(key => {
      const field = optionalConfigFields[key]
      if (field.type === 'boolean') {
        values[key] = initialValues[key] || false
      } else {
        values[key] = initialValues[key] || ''
      }
    })
    
    return values
  }

  const formik = useFormik({
    initialValues: createInitialValues(),
    validationSchema: createValidationSchema(),
    onSubmit: (values) => {
      // Filter out empty optional fields
      const filteredValues = Object.entries(values).reduce((acc, [key, value]) => {
        if (requiredConfigFields[key] || (value !== '' && value !== false)) {
          acc[key] = value
        }
        return acc
      }, {} as Record<string, any>)
      
      onSubmit(filteredValues)
    }
  })

  const toggleSensitiveField = (fieldKey: string) => {
    setShowSensitiveFields(prev => ({
      ...prev,
      [fieldKey]: !prev[fieldKey]
    }))
  }

  const renderField = (key: string, field: ConfigField, isRequired: boolean) => {
    const error = formik.touched[key] && formik.errors[key]
    const isSensitive = field.sensitive && field.type === 'string'
    const showValue = !isSensitive || showSensitiveFields[key]

    return (
      <div key={key} className="space-y-2">
        <div className="flex items-center gap-2">
          <Label htmlFor={key} className="text-sm font-medium">
            {field.label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {isRequired ? (
            <Badge variant="destructive" className="text-xs">Required</Badge>
          ) : (
            <Badge variant="secondary" className="text-xs">Optional</Badge>
          )}
        </div>
        
        {field.description && (
          <div className="flex items-start gap-2 text-sm text-muted-foreground">
            <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <span>{field.description}</span>
          </div>
        )}
        
        <div className="relative">
          {field.type === 'textarea' ? (
            <Textarea
              id={key}
              name={key}
              placeholder={`Enter ${field.label.toLowerCase()}`}
              value={formik.values[key]}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              className={error ? 'border-red-500' : ''}
            />
          ) : field.type === 'boolean' ? (
            <div className="flex items-center space-x-2">
              <Checkbox
                id={key}
                checked={formik.values[key]}
                onCheckedChange={(checked) => formik.setFieldValue(key, checked)}
              />
              <Label htmlFor={key} className="text-sm">
                Enable {field.label}
              </Label>
            </div>
          ) : (
            <>
              <Input
                id={key}
                name={key}
                type={isSensitive && !showValue ? 'password' : field.type === 'number' ? 'number' : 'text'}
                placeholder={`Enter ${field.label.toLowerCase()}`}
                value={formik.values[key]}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className={error ? 'border-red-500' : ''}
              />
              {isSensitive && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                  onClick={() => toggleSensitiveField(key)}
                >
                  {showValue ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              )}
            </>
          )}
        </div>
        
        {error && (
          <p className="text-sm text-red-500">{error as string}</p>
        )}
      </div>
    )
  }

  return (
    <form onSubmit={formik.handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold">{gatewayName} Configuration</h3>
          <p className="text-sm text-muted-foreground">
            Configure the required and optional settings for {gatewayName} payment gateway.
          </p>
        </div>

        {/* Required Fields */}
        {Object.keys(requiredConfigFields || {}).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Required Configuration</CardTitle>
              <CardDescription>
                These fields are required for {gatewayName} to function properly.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(requiredConfigFields || {}).map(([key, field]) =>
                renderField(key, field, true)
              )}
            </CardContent>
          </Card>
        )}

        {/* Optional Fields */}
        {Object.keys(optionalConfigFields || {}).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Optional Configuration</CardTitle>
              <CardDescription>
                These fields are optional and can enhance {gatewayName} functionality.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(optionalConfigFields || {}).map(([key, field]) =>
                renderField(key, field, false)
              )}
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex justify-end gap-3">
        <Button type="submit" disabled={isLoading || !formik.isValid}>
          {isLoading ? 'Saving...' : 'Save Configuration'}
        </Button>
      </div>
    </form>
  )
}
