'use client'

import React, { useState } from 'react'
import DynamicConfigForm from './DynamicConfigForm'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON>ting<PERSON>, Eye } from 'lucide-react'

// Example gateway configurations
const gatewayConfigs = {
  'Razorpay': {
    required: {
      key_id: { 
        type: 'string' as const, 
        label: 'Key ID', 
        description: 'Razorpay Key ID from dashboard (starts with rzp_)' 
      },
      key_secret: { 
        type: 'string' as const, 
        label: 'Key Secret', 
        description: 'Razorpay Key Secret from dashboard', 
        sensitive: true 
      },
      webhook_secret: { 
        type: 'string' as const, 
        label: 'Webhook Secret', 
        description: 'Webhook secret for payment verification', 
        sensitive: true 
      }
    },
    optional: {
      theme_color: { 
        type: 'string' as const, 
        label: 'Theme Color', 
        description: 'Checkout theme color (hex code, e.g., #3399cc)' 
      }
    }
  },
  'Stripe': {
    required: {
      publishable_key: { 
        type: 'string' as const, 
        label: 'Publishable Key', 
        description: 'Stripe publishable key (starts with pk_)' 
      },
      secret_key: { 
        type: 'string' as const, 
        label: 'Secret Key', 
        description: 'Stripe secret key (starts with sk_)', 
        sensitive: true 
      },
      webhook_secret: { 
        type: 'string' as const, 
        label: 'Webhook Secret', 
        description: 'Webhook endpoint secret (starts with whsec_)', 
        sensitive: true 
      }
    },
    optional: {
      statement_descriptor: { 
        type: 'string' as const, 
        label: 'Statement Descriptor', 
        description: 'Text that appears on customer bank statements' 
      }
    }
  },
  'PayPal': {
    required: {
      client_id: { 
        type: 'string' as const, 
        label: 'Client ID', 
        description: 'PayPal Client ID from developer dashboard' 
      },
      client_secret: { 
        type: 'string' as const, 
        label: 'Client Secret', 
        description: 'PayPal Client Secret from developer dashboard', 
        sensitive: true 
      }
    },
    optional: {
      sandbox_mode: { 
        type: 'boolean' as const, 
        label: 'Sandbox Mode', 
        description: 'Enable for testing with PayPal sandbox' 
      }
    }
  },
  'PhonePe': {
    required: {
      merchant_id: { 
        type: 'string' as const, 
        label: 'Merchant ID', 
        description: 'PhonePe Merchant ID provided by PhonePe' 
      },
      salt_key: { 
        type: 'string' as const, 
        label: 'Salt Key', 
        description: 'PhonePe Salt Key for API authentication', 
        sensitive: true 
      },
      salt_index: { 
        type: 'string' as const, 
        label: 'Salt Index', 
        description: 'PhonePe Salt Index (usually 1)' 
      }
    },
    optional: {
      redirect_url: { 
        type: 'string' as const, 
        label: 'Redirect URL', 
        description: 'URL to redirect after payment completion' 
      }
    }
  }
}

export default function GatewayConfigExample() {
  const [selectedGateway, setSelectedGateway] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const handleConfigSubmit = async (gatewayName: string, values: Record<string, any>) => {
    setIsLoading(true)
    try {
      console.log(`Saving ${gatewayName} configuration:`, values)
      
      // Here you would make API call to save the configuration
      // await api.saveGatewayConfig(gatewayName, values)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      alert(`${gatewayName} configuration saved successfully!`)
      setSelectedGateway(null)
    } catch (error) {
      console.error('Error saving configuration:', error)
      alert('Error saving configuration')
    } finally {
      setIsLoading(false)
    }
  }

  if (selectedGateway) {
    const config = gatewayConfigs[selectedGateway as keyof typeof gatewayConfigs]
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            onClick={() => setSelectedGateway(null)}
          >
            ← Back to Gateways
          </Button>
        </div>
        
        <DynamicConfigForm
          gatewayName={selectedGateway}
          requiredConfigFields={config.required}
          optionalConfigFields={config.optional}
          onSubmit={(values) => handleConfigSubmit(selectedGateway, values)}
          isLoading={isLoading}
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Payment Gateway Configuration</h2>
        <p className="text-muted-foreground">
          Select a payment gateway to configure its settings. Each gateway has different required fields.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.entries(gatewayConfigs).map(([gatewayName, config]) => (
          <Card key={gatewayName} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{gatewayName}</CardTitle>
                <Badge variant="outline">
                  {Object.keys(config.required).length} required fields
                </Badge>
              </div>
              <CardDescription>
                Configure {gatewayName} payment gateway settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Required Fields:</h4>
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(config.required).map(([key, field]) => (
                      <Badge key={key} variant="secondary" className="text-xs">
                        {field.label}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {Object.keys(config.optional).length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">Optional Fields:</h4>
                    <div className="flex flex-wrap gap-1">
                      {Object.entries(config.optional).map(([key, field]) => (
                        <Badge key={key} variant="outline" className="text-xs">
                          {field.label}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                <Button 
                  className="w-full mt-4" 
                  onClick={() => setSelectedGateway(gatewayName)}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Configure {gatewayName}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-base text-blue-800">How it works</CardTitle>
        </CardHeader>
        <CardContent className="text-sm text-blue-700">
          <ul className="space-y-2">
            <li>• Each payment gateway has different configuration requirements</li>
            <li>• Required fields must be filled for the gateway to work</li>
            <li>• Optional fields can enhance functionality but aren't mandatory</li>
            <li>• Sensitive fields (like secrets) are masked for security</li>
            <li>• Configuration is validated before saving</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
