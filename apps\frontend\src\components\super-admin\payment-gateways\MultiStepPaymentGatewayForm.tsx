'use client'

import { useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { usePaymentGatewayStore, PaymentGateway } from '@/stores/super-admin/usePaymentGatewayStore'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Plus, X, ChevronLeft, ChevronRight, Check } from 'lucide-react'

// Configuration field types
interface ConfigField {
  key: string
  label: string
  type: 'text' | 'password' | 'url' | 'email' | 'number' | 'boolean'
  placeholder?: string
  description?: string
  isRequired: boolean
}

// Configuration Fields Step Component
function ConfigurationFieldsStep({ formik }: { formik: any }) {
  const [showAddRequired, setShowAddRequired] = useState(false)
  const [showAddOptional, setShowAddOptional] = useState(false)
  const [newField, setNewField] = useState<Partial<ConfigField>>({
    key: '',
    label: '',
    type: 'text',
    placeholder: '',
    description: '',
    isRequired: true
  })

  const fieldTypes = [
    { label: 'Text', value: 'text' },
    { label: 'Password', value: 'password' },
    { label: 'URL', value: 'url' },
    { label: 'Email', value: 'email' },
    { label: 'Number', value: 'number' },
    { label: 'Boolean', value: 'boolean' }
  ]

  const addField = (isRequired: boolean) => {
    if (!newField.key || !newField.label) return

    const field = {
      ...newField,
      isRequired,
      key: newField.key!.toLowerCase().replace(/[^a-z0-9]/g, '_'),
      placeholder: newField.placeholder || `Enter ${newField.label?.toLowerCase()}`
    }

    const fieldArray = isRequired ? 'requiredConfigFields' : 'optionalConfigFields'
    const currentFields = formik.values[fieldArray] || []

    // Check for duplicate keys
    if (currentFields.some((f: ConfigField) => f.key === field.key)) {
      alert('A field with this key already exists')
      return
    }

    formik.setFieldValue(fieldArray, [...currentFields, field])

    // Reset form
    setNewField({
      key: '',
      label: '',
      type: 'text',
      placeholder: '',
      description: '',
      isRequired: true
    })
    setShowAddRequired(false)
    setShowAddOptional(false)
  }

  const removeField = (index: number, isRequired: boolean) => {
    const fieldArray = isRequired ? 'requiredConfigFields' : 'optionalConfigFields'
    const currentFields = formik.values[fieldArray] || []
    formik.setFieldValue(fieldArray, currentFields.filter((_: any, i: number) => i !== index))
  }

  const renderFieldList = (fields: ConfigField[], isRequired: boolean, title: string) => (
    <div>
      <div className="flex items-center justify-between mb-4">
        <div>
          <Label className="text-sm font-medium">{title}</Label>
          <p className="text-sm text-muted-foreground">
            {isRequired ? 'These fields will be mandatory for institutes to configure' : 'These fields will be optional for institutes to configure'}
          </p>
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => isRequired ? setShowAddRequired(true) : setShowAddOptional(true)}
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Field
        </Button>
      </div>

      {fields.length > 0 ? (
        <div className="space-y-3">
          {fields.map((field: ConfigField, index: number) => (
            <div key={index} className="p-3 border rounded-lg">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="font-medium">{field.label}</div>
                  <div className="text-sm text-muted-foreground">
                    Key: {field.key} | Type: {field.type}
                  </div>
                  {field.placeholder && (
                    <div className="text-sm text-muted-foreground">
                      Placeholder: {field.placeholder}
                    </div>
                  )}
                  {field.description && (
                    <div className="text-sm text-muted-foreground mt-1">
                      {field.description}
                    </div>
                  )}
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeField(index, isRequired)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-muted-foreground border-2 border-dashed rounded-lg">
          No {isRequired ? 'required' : 'optional'} configuration fields defined yet.
          <br />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => isRequired ? setShowAddRequired(true) : setShowAddOptional(true)}
            className="mt-2"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add {isRequired ? 'Required' : 'Optional'} Field
          </Button>
        </div>
      )}
    </div>
  )

  const renderAddFieldForm = (isRequired: boolean, show: boolean, setShow: (show: boolean) => void) => {
    if (!show) return null

    return (
      <Card className="mt-4">
        <CardHeader>
          <CardTitle className="text-lg">Add {isRequired ? 'Required' : 'Optional'} Field</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="fieldKey">Field Key *</Label>
              <Input
                id="fieldKey"
                value={newField.key || ''}
                onChange={(e) => setNewField({ ...newField, key: e.target.value })}
                placeholder="e.g., api_key, secret_key"
              />
            </div>
            <div>
              <Label htmlFor="fieldLabel">Field Label *</Label>
              <Input
                id="fieldLabel"
                value={newField.label || ''}
                onChange={(e) => setNewField({ ...newField, label: e.target.value })}
                placeholder="e.g., API Key, Secret Key"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="fieldType">Field Type</Label>
              <select
                id="fieldType"
                value={newField.type || 'text'}
                onChange={(e) => setNewField({ ...newField, type: e.target.value as any })}
                className="w-full p-2 border rounded-md"
              >
                {fieldTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <Label htmlFor="fieldPlaceholder">Placeholder</Label>
              <Input
                id="fieldPlaceholder"
                value={newField.placeholder || ''}
                onChange={(e) => setNewField({ ...newField, placeholder: e.target.value })}
                placeholder="Enter placeholder text"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="fieldDescription">Description</Label>
            <Textarea
              id="fieldDescription"
              value={newField.description || ''}
              onChange={(e) => setNewField({ ...newField, description: e.target.value })}
              placeholder="Help text explaining this field"
              rows={2}
            />
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => setShow(false)}>
              Cancel
            </Button>
            <Button
              type="button"
              onClick={() => addField(isRequired)}
              disabled={!newField.key || !newField.label}
            >
              Add Field
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configuration Fields</CardTitle>
        <CardDescription>
          Define the configuration fields that institutes will need to provide
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Required Configuration Fields */}
        {renderFieldList(formik.values.requiredConfigFields || [], true, 'Required Configuration Fields')}
        {renderAddFieldForm(true, showAddRequired, setShowAddRequired)}

        {/* Optional Configuration Fields */}
        {renderFieldList(formik.values.optionalConfigFields || [], false, 'Optional Configuration Fields')}
        {renderAddFieldForm(false, showAddOptional, setShowAddOptional)}
      </CardContent>
    </Card>
  )
}

interface MultiStepPaymentGatewayFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  gateway?: PaymentGateway | null
}

// Form steps
const STEPS = [
  { id: 1, title: 'Basic Information', description: 'Gateway name and details' },
  { id: 2, title: 'Supported Currencies', description: 'Select supported currencies' },
  { id: 3, title: 'Payment Methods', description: 'Choose payment methods' },
  { id: 4, title: 'Supported Countries', description: 'Select supported countries' },
  { id: 5, title: 'Configuration Fields', description: 'Define required fields' },
]

const currencyOptions = [
  { label: 'Indian Rupee (INR)', value: 'INR' },
  { label: 'US Dollar (USD)', value: 'USD' },
  { label: 'Euro (EUR)', value: 'EUR' },
  { label: 'British Pound (GBP)', value: 'GBP' },
  { label: 'Canadian Dollar (CAD)', value: 'CAD' },
  { label: 'Australian Dollar (AUD)', value: 'AUD' },
  { label: 'Japanese Yen (JPY)', value: 'JPY' },
  { label: 'Singapore Dollar (SGD)', value: 'SGD' },
]

const paymentMethodOptions = [
  { label: 'Credit Card', value: 'credit_card' },
  { label: 'Debit Card', value: 'debit_card' },
  { label: 'UPI', value: 'upi' },
  { label: 'Net Banking', value: 'net_banking' },
  { label: 'Digital Wallet', value: 'wallet' },
  { label: 'Bank Transfer', value: 'bank_transfer' },
  { label: 'Apple Pay', value: 'apple_pay' },
  { label: 'Google Pay', value: 'google_pay' },
  { label: 'PayPal', value: 'paypal' },
  { label: 'Buy Now Pay Later (BNPL)', value: 'bnpl' },
]

const countryOptions = [
  { label: 'India (IN)', value: 'IN' },
  { label: 'United States (US)', value: 'US' },
  { label: 'United Kingdom (GB)', value: 'GB' },
  { label: 'Canada (CA)', value: 'CA' },
  { label: 'Australia (AU)', value: 'AU' },
  { label: 'Germany (DE)', value: 'DE' },
  { label: 'France (FR)', value: 'FR' },
  { label: 'Japan (JP)', value: 'JP' },
  { label: 'Singapore (SG)', value: 'SG' },
  { label: 'United Arab Emirates (AE)', value: 'AE' },
]

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Gateway name is required').max(100, 'Name too long'),
  description: Yup.string().max(500, 'Description too long'),
  supportedCurrencies: Yup.array().min(1, 'At least one currency is required'),
  supportedMethods: Yup.array(),
  supportedCountries: Yup.array(),
  documentationUrl: Yup.string().url('Must be a valid URL'),
  logoUrl: Yup.string().url('Must be a valid URL'),
  apiVersion: Yup.string().max(20, 'API version too long'),
  webhookSupport: Yup.boolean(),
  isActive: Yup.boolean(),
  isFeatured: Yup.boolean(),
  requiredConfigFields: Yup.array(),
  optionalConfigFields: Yup.array(),
})

export function MultiStepPaymentGatewayForm({ isOpen, onClose, onSuccess, gateway }: MultiStepPaymentGatewayFormProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [newCurrency, setNewCurrency] = useState('')
  const [newPaymentMethod, setNewPaymentMethod] = useState('')
  const [newCountry, setNewCountry] = useState('')
  const { createGateway, updateGateway } = usePaymentGatewayStore()

  const isEditing = !!gateway

  // Debug: Log the gateway data when it changes
  console.log('MultiStepPaymentGatewayForm - Gateway data:', gateway)
  console.log('MultiStepPaymentGatewayForm - Is editing:', isEditing)

  // Debug: Log the initial values
  const initialValues = {
    name: gateway?.name || '',
    description: gateway?.description || '',
    supportedCurrencies: gateway?.supportedCurrencies || ['INR'],
    supportedMethods: gateway?.supportedMethods || ['credit_card'],
    supportedCountries: gateway?.supportedCountries || ['IN'],
    documentationUrl: gateway?.documentationUrl || '',
    logoUrl: gateway?.logoUrl || '',
    apiVersion: gateway?.apiVersion || '1.0',
    webhookSupport: gateway?.webhookSupport ?? true,
    isActive: gateway?.isActive ?? true,
    isFeatured: gateway?.isFeatured ?? false,
    requiredConfigFields: gateway?.requiredConfigFields || [],
    optionalConfigFields: gateway?.optionalConfigFields || [],
  }
  
  console.log('MultiStepPaymentGatewayForm - Initial values:', initialValues)

  const formik = useFormik({
    initialValues,
    enableReinitialize: true, // This will reinitialize the form when gateway prop changes
    validationSchema,
    onSubmit: async (values) => {
      setIsSubmitting(true)
      try {
        // Transform and clean the data to match the expected API format
        const transformedValues = {
          name: values.name.trim(),
          description: values.description?.trim() || '',
          // Send as nested objects for API compatibility, backend will convert to simple arrays
          supportedCurrencies: values.supportedCurrencies.map(currency => ({ currency })),
          supportedMethods: values.supportedMethods.map(method => ({ method })),
          supportedCountries: values.supportedCountries.map(country => ({ country })),
          apiVersion: values.apiVersion?.trim() || '1.0',
          webhookSupport: values.webhookSupport,
          // Only include optional fields if they have values
          ...(values.documentationUrl?.trim() && { documentationUrl: values.documentationUrl.trim() }),
          ...(values.logoUrl?.trim() && { logoUrl: values.logoUrl.trim() }),
          isActive: values.isActive,
          isFeatured: values.isFeatured,
          requiredConfigFields: values.requiredConfigFields,
          optionalConfigFields: values.optionalConfigFields,
        }

        console.log('Submitting gateway data:', transformedValues)

        if (isEditing) {
          await updateGateway(gateway.id, transformedValues)
        } else {
          await createGateway(transformedValues)
        }
        onSuccess()
      } catch (error: any) {
        console.error('Form submission error:', error)
        
        // Handle validation errors from the backend
        if (error.response?.data?.validationErrors?.errors) {
          const backendErrors = error.response.data.validationErrors.errors
          const formikErrors: any = {}
          
          backendErrors.forEach((err: any) => {
            if (err.path === 'supportedCurrencies') {
              formikErrors.supportedCurrencies = err.message
            } else if (err.path === 'supportedMethods') {
              formikErrors.supportedMethods = err.message
            } else if (err.path === 'supportedCountries') {
              formikErrors.supportedCountries = err.message
            } else {
              formikErrors[err.path] = err.message
            }
          })
          
          formik.setErrors(formikErrors)
        }
        // Error toast is handled in store
      } finally {
        setIsSubmitting(false)
      }
    },
  })

  const nextStep = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1)
    }
  }



  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleClose = () => {
    setCurrentStep(1)
    formik.resetForm()
    onClose()
  }

  const progress = (currentStep / STEPS.length) * 100

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Payment Gateway' : 'Add New Payment Gateway'}
          </DialogTitle>
          <DialogDescription>
            Step {currentStep} of {STEPS.length}: {STEPS[currentStep - 1].description}
          </DialogDescription>
        </DialogHeader>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-muted-foreground">
            {STEPS.map((step) => (
              <span key={step.id} className={currentStep >= step.id ? 'text-primary' : ''}>
                {step.title}
              </span>
            ))}
          </div>
        </div>

        <div className="space-y-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Enter the basic details for the payment gateway
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="name">Gateway Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="e.g., Razorpay, Stripe, PayPal"
                  />
                  {formik.touched.name && formik.errors.name && (
                    <p className="text-sm text-red-600 mt-1">{formik.errors.name}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Brief description of the payment gateway"
                    rows={3}
                  />
                  {formik.touched.description && formik.errors.description && (
                    <p className="text-sm text-red-600 mt-1">{formik.errors.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="logoUrl">Logo URL</Label>
                    <Input
                      id="logoUrl"
                      name="logoUrl"
                      value={formik.values.logoUrl}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="https://example.com/logo.png"
                    />
                    {formik.touched.logoUrl && formik.errors.logoUrl && (
                      <p className="text-sm text-red-600 mt-1">{formik.errors.logoUrl}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="documentationUrl">Documentation URL</Label>
                    <Input
                      id="documentationUrl"
                      name="documentationUrl"
                      value={formik.values.documentationUrl}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="https://docs.example.com"
                    />
                    {formik.touched.documentationUrl && formik.errors.documentationUrl && (
                      <p className="text-sm text-red-600 mt-1">{formik.errors.documentationUrl}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="apiVersion">API Version</Label>
                    <Input
                      id="apiVersion"
                      name="apiVersion"
                      value={formik.values.apiVersion}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="1.0"
                    />
                    {formik.touched.apiVersion && formik.errors.apiVersion && (
                      <p className="text-sm text-red-600 mt-1">{formik.errors.apiVersion}</p>
                    )}
                  </div>

                  <div className="flex items-center justify-between pt-6">
                    <div>
                      <Label htmlFor="webhookSupport">Webhook Support</Label>
                      <p className="text-sm text-muted-foreground">
                        Whether this gateway supports webhooks
                      </p>
                    </div>
                    <Switch
                      id="webhookSupport"
                      checked={formik.values.webhookSupport}
                      onCheckedChange={(checked) => formik.setFieldValue('webhookSupport', checked)}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isActive"
                        checked={formik.values.isActive}
                        onCheckedChange={(checked) => formik.setFieldValue('isActive', checked)}
                      />
                      <Label htmlFor="isActive">Active</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isFeatured"
                        checked={formik.values.isFeatured}
                        onCheckedChange={(checked) => formik.setFieldValue('isFeatured', checked)}
                      />
                      <Label htmlFor="isFeatured">Featured</Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Supported Currencies */}
          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle>Supported Currencies</CardTitle>
                <CardDescription>
                  Select the currencies this gateway supports
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Predefined Currency Options */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Common Currencies</Label>
                  <div className="grid grid-cols-2 gap-4">
                    {currencyOptions.map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`currency-${option.value}`}
                          checked={formik.values.supportedCurrencies.includes(option.value)}
                          onCheckedChange={(checked) => {
                            const currentCurrencies = formik.values.supportedCurrencies
                            if (checked) {
                              formik.setFieldValue('supportedCurrencies', [...currentCurrencies, option.value])
                            } else {
                              formik.setFieldValue('supportedCurrencies', currentCurrencies.filter(c => c !== option.value))
                            }
                          }}
                        />
                        <Label
                          htmlFor={`currency-${option.value}`}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Add Custom Currency */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Add Custom Currency</Label>
                  <div className="flex gap-2">
                    <Input
                      value={newCurrency}
                      onChange={(e) => setNewCurrency(e.target.value.toUpperCase())}
                      placeholder="Enter currency code (e.g., AED, CHF)"
                      maxLength={3}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault()
                          if (newCurrency.trim() && !formik.values.supportedCurrencies.includes(newCurrency.trim())) {
                            formik.setFieldValue('supportedCurrencies', [...formik.values.supportedCurrencies, newCurrency.trim()])
                            setNewCurrency('')
                          }
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={() => {
                        if (newCurrency.trim() && !formik.values.supportedCurrencies.includes(newCurrency.trim())) {
                          formik.setFieldValue('supportedCurrencies', [...formik.values.supportedCurrencies, newCurrency.trim()])
                          setNewCurrency('')
                        }
                      }}
                      size="sm"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Selected Currencies Display */}
                {formik.values.supportedCurrencies.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Selected Currencies</Label>
                    <div className="flex flex-wrap gap-2">
                      {formik.values.supportedCurrencies.map((currency) => (
                        <Badge key={currency} variant="secondary" className="flex items-center gap-1">
                          {currency}
                          <X
                            className="w-3 h-3 cursor-pointer"
                            onClick={() => {
                              formik.setFieldValue('supportedCurrencies', formik.values.supportedCurrencies.filter(c => c !== currency))
                            }}
                          />
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {formik.touched.supportedCurrencies && formik.errors.supportedCurrencies && (
                  <p className="text-sm text-red-600">{formik.errors.supportedCurrencies}</p>
                )}
              </CardContent>
            </Card>
          )}

          {/* Step 3: Payment Methods */}
          {currentStep === 3 && (
            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>
                  Select the payment methods this gateway supports
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Predefined Payment Method Options */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Common Payment Methods</Label>
                  <div className="grid grid-cols-2 gap-4">
                    {paymentMethodOptions.map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`method-${option.value}`}
                          checked={formik.values.supportedMethods.includes(option.value)}
                          onCheckedChange={(checked) => {
                            const currentMethods = formik.values.supportedMethods
                            if (checked) {
                              formik.setFieldValue('supportedMethods', [...currentMethods, option.value])
                            } else {
                              formik.setFieldValue('supportedMethods', currentMethods.filter(m => m !== option.value))
                            }
                          }}
                        />
                        <Label
                          htmlFor={`method-${option.value}`}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Add Custom Payment Method */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Add Custom Payment Method</Label>
                  <div className="flex gap-2">
                    <Input
                      value={newPaymentMethod}
                      onChange={(e) => setNewPaymentMethod(e.target.value)}
                      placeholder="Enter payment method (e.g., crypto, bank_transfer)"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault()
                          if (newPaymentMethod.trim() && !formik.values.supportedMethods.includes(newPaymentMethod.trim())) {
                            formik.setFieldValue('supportedMethods', [...formik.values.supportedMethods, newPaymentMethod.trim()])
                            setNewPaymentMethod('')
                          }
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={() => {
                        if (newPaymentMethod.trim() && !formik.values.supportedMethods.includes(newPaymentMethod.trim())) {
                          formik.setFieldValue('supportedMethods', [...formik.values.supportedMethods, newPaymentMethod.trim()])
                          setNewPaymentMethod('')
                        }
                      }}
                      size="sm"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Selected Payment Methods Display */}
                {formik.values.supportedMethods.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Selected Payment Methods</Label>
                    <div className="flex flex-wrap gap-2">
                      {formik.values.supportedMethods.map((method) => (
                        <Badge key={method} variant="secondary" className="flex items-center gap-1">
                          {method.replace(/_/g, ' ')}
                          <X
                            className="w-3 h-3 cursor-pointer"
                            onClick={() => {
                              formik.setFieldValue('supportedMethods', formik.values.supportedMethods.filter(m => m !== method))
                            }}
                          />
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Step 4: Supported Countries */}
          {currentStep === 4 && (
            <Card>
              <CardHeader>
                <CardTitle>Supported Countries</CardTitle>
                <CardDescription>
                  Select the countries this gateway supports (optional)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Predefined Country Options */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Common Countries</Label>
                  <div className="grid grid-cols-2 gap-4">
                    {countryOptions.map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`country-${option.value}`}
                          checked={formik.values.supportedCountries.includes(option.value)}
                          onCheckedChange={(checked) => {
                            const currentCountries = formik.values.supportedCountries
                            if (checked) {
                              formik.setFieldValue('supportedCountries', [...currentCountries, option.value])
                            } else {
                              formik.setFieldValue('supportedCountries', currentCountries.filter(c => c !== option.value))
                            }
                          }}
                        />
                        <Label
                          htmlFor={`country-${option.value}`}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Add Custom Country */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Add Custom Country</Label>
                  <div className="flex gap-2">
                    <Input
                      value={newCountry}
                      onChange={(e) => setNewCountry(e.target.value.toUpperCase())}
                      placeholder="Enter country code (e.g., DE, FR)"
                      maxLength={2}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault()
                          if (newCountry.trim() && !formik.values.supportedCountries.includes(newCountry.trim())) {
                            formik.setFieldValue('supportedCountries', [...formik.values.supportedCountries, newCountry.trim()])
                            setNewCountry('')
                          }
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={() => {
                        if (newCountry.trim() && !formik.values.supportedCountries.includes(newCountry.trim())) {
                          formik.setFieldValue('supportedCountries', [...formik.values.supportedCountries, newCountry.trim()])
                          setNewCountry('')
                        }
                      }}
                      size="sm"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Selected Countries Display */}
                {formik.values.supportedCountries.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Selected Countries</Label>
                    <div className="flex flex-wrap gap-2">
                      {formik.values.supportedCountries.map((country) => {
                        // Find the label for predefined countries, or use the country code for custom ones
                        const predefinedCountry = countryOptions.find(option => option.value === country)
                        const displayName = predefinedCountry ? predefinedCountry.label : country

                        return (
                          <Badge key={country} variant="secondary" className="flex items-center gap-1">
                            {displayName}
                            <X
                              className="w-3 h-3 cursor-pointer"
                              onClick={() => {
                                formik.setFieldValue('supportedCountries', formik.values.supportedCountries.filter(c => c !== country))
                              }}
                            />
                          </Badge>
                        )
                      })}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Step 5: Configuration Fields */}
          {currentStep === 5 && (
            <ConfigurationFieldsStep
              formik={formik}
            />
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>

            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              
              {currentStep === STEPS.length ? (
                <Button
                  type="button"
                  disabled={isSubmitting}
                  onClick={() => formik.handleSubmit()}
                >
                  {isSubmitting ? 'Saving...' : (isEditing ? 'Update Gateway' : 'Create Gateway')}
                  <Check className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button type="button" onClick={nextStep}>
                  Next
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
