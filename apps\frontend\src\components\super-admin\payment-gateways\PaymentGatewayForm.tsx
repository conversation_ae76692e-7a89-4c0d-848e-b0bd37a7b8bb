'use client'

import { useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { usePaymentGatewayStore, PaymentGateway } from '@/stores/super-admin/usePaymentGatewayStore'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Plus, X, ChevronLeft, ChevronRight, Check } from 'lucide-react'

// Configuration field types
interface ConfigField {
  key: string
  label: string
  type: 'text' | 'password' | 'url' | 'email' | 'number' | 'boolean'
  placeholder?: string
  description?: string
  isRequired: boolean
}

interface PaymentGatewayFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  gateway?: PaymentGateway | null
}

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Gateway name is required').max(100, 'Name too long'),
  description: Yup.string().max(500, 'Description too long'),
  supportedCurrencies: Yup.array().min(1, 'At least one currency is required'),
  supportedMethods: Yup.array(),
  supportedCountries: Yup.array(),
  documentationUrl: Yup.string().url('Must be a valid URL'),
  logoUrl: Yup.string().url('Must be a valid URL'),
  apiVersion: Yup.string().max(20, 'API version too long'),
  webhookSupport: Yup.boolean(),
  isActive: Yup.boolean(),
  isFeatured: Yup.boolean()
})



const currencyOptions = [
  { label: 'Indian Rupee (INR)', value: 'INR' },
  { label: 'US Dollar (USD)', value: 'USD' },
  { label: 'Euro (EUR)', value: 'EUR' },
  { label: 'British Pound (GBP)', value: 'GBP' },
  { label: 'Canadian Dollar (CAD)', value: 'CAD' },
  { label: 'Australian Dollar (AUD)', value: 'AUD' },
  { label: 'Japanese Yen (JPY)', value: 'JPY' },
  { label: 'Swiss Franc (CHF)', value: 'CHF' },
  { label: 'Chinese Yuan (CNY)', value: 'CNY' },
  { label: 'Singapore Dollar (SGD)', value: 'SGD' },
]

const paymentMethodOptions = [
  { label: 'Credit Card', value: 'credit_card' },
  { label: 'Debit Card', value: 'debit_card' },
  { label: 'UPI', value: 'upi' },
  { label: 'Net Banking', value: 'net_banking' },
  { label: 'Digital Wallet', value: 'wallet' },
  { label: 'Bank Transfer', value: 'bank_transfer' },
  { label: 'Apple Pay', value: 'apple_pay' },
  { label: 'Google Pay', value: 'google_pay' },
  { label: 'PayPal', value: 'paypal' },
  { label: 'Buy Now Pay Later (BNPL)', value: 'bnpl' },
]

const countryOptions = [
  { label: 'India (IN)', value: 'IN' },
  { label: 'United States (US)', value: 'US' },
  { label: 'United Kingdom (GB)', value: 'GB' },
  { label: 'Canada (CA)', value: 'CA' },
  { label: 'Australia (AU)', value: 'AU' },
  { label: 'Germany (DE)', value: 'DE' },
  { label: 'France (FR)', value: 'FR' },
  { label: 'Japan (JP)', value: 'JP' },
  { label: 'Singapore (SG)', value: 'SG' },
  { label: 'United Arab Emirates (AE)', value: 'AE' },
]

export function PaymentGatewayForm({ isOpen, onClose, onSuccess, gateway }: PaymentGatewayFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [newCurrency, setNewCurrency] = useState('')
  const [newPaymentMethod, setNewPaymentMethod] = useState('')
  const [newCountry, setNewCountry] = useState('')
  const { createGateway, updateGateway } = usePaymentGatewayStore()

  const isEditing = !!gateway

  // Debug: Log the gateway data when it changes
  console.log('PaymentGatewayForm - Gateway data:', gateway)
  console.log('PaymentGatewayForm - Is editing:', isEditing)

  // Debug: Log the initial values
  const initialValues = {
    name: gateway?.name || '',
    description: gateway?.description || '',
    supportedCurrencies: gateway?.supportedCurrencies || ['INR'],
    supportedMethods: gateway?.supportedMethods || ['credit_card'],
    supportedCountries: gateway?.supportedCountries || ['IN'],
    documentationUrl: gateway?.documentationUrl || '',
    logoUrl: gateway?.logoUrl || '',
    apiVersion: gateway?.apiVersion || '1.0',
    webhookSupport: gateway?.webhookSupport ?? true,
    isActive: gateway?.isActive ?? true,
    isFeatured: gateway?.isFeatured ?? false,
  }

  console.log('PaymentGatewayForm - Initial values:', initialValues)

  const formik = useFormik({
    initialValues,
    enableReinitialize: true, // This will reinitialize the form when gateway prop changes
    validationSchema,
    onSubmit: async (values) => {
      setIsSubmitting(true)
      try {
        // Transform and clean the data to match the expected API format
        const transformedValues = {
          name: values.name.trim(),
          description: values.description?.trim() || '',
          // Send as nested objects for API compatibility, backend will convert to simple arrays
          supportedCurrencies: values.supportedCurrencies.map(currency => ({ currency })),
          supportedMethods: values.supportedMethods.map(method => ({ method })),
          supportedCountries: values.supportedCountries.map(country => ({ country })),
          apiVersion: values.apiVersion?.trim() || '1.0',
          webhookSupport: values.webhookSupport,
          // Only include optional fields if they have values
          ...(values.documentationUrl?.trim() && { documentationUrl: values.documentationUrl.trim() }),
          ...(values.logoUrl?.trim() && { logoUrl: values.logoUrl.trim() }),
          isActive: values.isActive,
          isFeatured: values.isFeatured
        }

        console.log('Submitting gateway data:', transformedValues)

        if (isEditing) {
          await updateGateway(gateway.id, transformedValues)
        } else {
          await createGateway(transformedValues)
        }
        onSuccess()
      } catch (error: any) {
        console.error('Form submission error:', error)

        // Handle validation errors from the backend
        if (error.response?.data?.validationErrors?.errors) {
          const backendErrors = error.response.data.validationErrors.errors
          const formikErrors: any = {}

          backendErrors.forEach((err: any) => {
            if (err.path === 'supportedCurrencies') {
              formikErrors.supportedCurrencies = err.message
            } else if (err.path === 'supportedMethods') {
              formikErrors.supportedMethods = err.message
            } else if (err.path === 'supportedCountries') {
              formikErrors.supportedCountries = err.message
            } else {
              formikErrors[err.path] = err.message
            }
          })

          formik.setErrors(formikErrors)
        }
        // Error toast is handled in store
      } finally {
        setIsSubmitting(false)
      }
    }
  })

  const handleCurrencyChange = (currency: string, checked: boolean) => {
    const currentCurrencies = formik.values.supportedCurrencies
    if (checked) {
      formik.setFieldValue('supportedCurrencies', [...currentCurrencies, currency])
    } else {
      formik.setFieldValue('supportedCurrencies', currentCurrencies.filter(c => c !== currency))
    }
  }

  const handlePaymentMethodChange = (method: string, checked: boolean) => {
    const currentMethods = formik.values.supportedMethods
    if (checked) {
      formik.setFieldValue('supportedMethods', [...currentMethods, method])
    } else {
      formik.setFieldValue('supportedMethods', currentMethods.filter(m => m !== method))
    }
  }

  const addCustomCurrency = () => {
    if (newCurrency.trim() && !formik.values.supportedCurrencies.includes(newCurrency.trim().toUpperCase())) {
      const currencyCode = newCurrency.trim().toUpperCase()
      formik.setFieldValue('supportedCurrencies', [...formik.values.supportedCurrencies, currencyCode])
      setNewCurrency('')
    }
  }

  const removeCustomCurrency = (currency: string) => {
    formik.setFieldValue('supportedCurrencies', formik.values.supportedCurrencies.filter(c => c !== currency))
  }

  const addCustomPaymentMethod = () => {
    if (newPaymentMethod.trim() && !formik.values.supportedMethods.includes(newPaymentMethod.trim().toLowerCase())) {
      const methodCode = newPaymentMethod.trim().toLowerCase().replace(/\s+/g, '_')
      formik.setFieldValue('supportedMethods', [...formik.values.supportedMethods, methodCode])
      setNewPaymentMethod('')
    }
  }

  const removeCustomPaymentMethod = (method: string) => {
    formik.setFieldValue('supportedMethods', formik.values.supportedMethods.filter(m => m !== method))
  }

  const handleCountryChange = (country: string, checked: boolean) => {
    const currentCountries = formik.values.supportedCountries
    if (checked) {
      formik.setFieldValue('supportedCountries', [...currentCountries, country])
    } else {
      formik.setFieldValue('supportedCountries', currentCountries.filter(c => c !== country))
    }
  }

  const addCustomCountry = () => {
    if (newCountry.trim() && !formik.values.supportedCountries.includes(newCountry.trim().toUpperCase())) {
      const countryCode = newCountry.trim().toUpperCase()
      formik.setFieldValue('supportedCountries', [...formik.values.supportedCountries, countryCode])
      setNewCountry('')
    }
  }

  const removeCustomCountry = (country: string) => {
    formik.setFieldValue('supportedCountries', formik.values.supportedCountries.filter(c => c !== country))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Payment Gateway' : 'Add Payment Gateway'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the payment gateway configuration.'
              : 'Configure a new payment gateway for institutes to use.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Gateway Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="e.g., Razorpay, Stripe, PayPal"
                />
                {formik.touched.name && formik.errors.name && (
                  <p className="text-sm text-red-600 mt-1">{formik.errors.name}</p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formik.values.description}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="Brief description of the payment gateway"
                  rows={3}
                />
                {formik.touched.description && formik.errors.description && (
                  <p className="text-sm text-red-600 mt-1">{formik.errors.description}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="logoUrl">Logo URL</Label>
                  <Input
                    id="logoUrl"
                    name="logoUrl"
                    value={formik.values.logoUrl}
                    onChange={formik.handleChange}
                    placeholder="https://example.com/logo.png"
                  />
                </div>

                <div>
                  <Label htmlFor="documentationUrl">Documentation URL</Label>
                  <Input
                    id="documentationUrl"
                    name="documentationUrl"
                    value={formik.values.documentationUrl}
                    onChange={formik.handleChange}
                    placeholder="https://docs.example.com"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="apiVersion">API Version</Label>
                  <Input
                    id="apiVersion"
                    name="apiVersion"
                    value={formik.values.apiVersion}
                    onChange={formik.handleChange}
                    placeholder="1.0"
                  />
                  {formik.touched.apiVersion && formik.errors.apiVersion && (
                    <p className="text-sm text-red-600 mt-1">{formik.errors.apiVersion}</p>
                  )}
                </div>

                <div className="flex items-center justify-between pt-6">
                  <div>
                    <Label htmlFor="webhookSupport">Webhook Support</Label>
                    <p className="text-sm text-muted-foreground">
                      Whether this gateway supports webhooks
                    </p>
                  </div>
                  <Switch
                    id="webhookSupport"
                    checked={formik.values.webhookSupport}
                    onCheckedChange={(checked) => formik.setFieldValue('webhookSupport', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Supported Currencies */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Supported Currencies *</CardTitle>
              <CardDescription>
                Select the currencies this gateway supports or add custom ones
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Predefined Currency Options */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Common Currencies</Label>
                <div className="grid grid-cols-2 gap-4">
                  {currencyOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`currency-${option.value}`}
                        checked={formik.values.supportedCurrencies.includes(option.value)}
                        onCheckedChange={(checked) =>
                          handleCurrencyChange(option.value, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`currency-${option.value}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Add Custom Currency */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Add Custom Currency</Label>
                <div className="flex gap-2">
                  <Input
                    value={newCurrency}
                    onChange={(e) => setNewCurrency(e.target.value)}
                    placeholder="Enter currency code (e.g., JPY, CAD)"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        addCustomCurrency()
                      }
                    }}
                  />
                  <Button type="button" onClick={addCustomCurrency} size="sm">
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Selected Currencies Display */}
              {formik.values.supportedCurrencies.length > 0 && (
                <div>
                  <Label className="text-sm font-medium mb-2 block">Selected Currencies</Label>
                  <div className="flex flex-wrap gap-2">
                    {formik.values.supportedCurrencies.map((currency) => (
                      <Badge key={currency} variant="secondary" className="flex items-center gap-1">
                        {currency}
                        <X
                          className="w-3 h-3 cursor-pointer"
                          onClick={() => removeCustomCurrency(currency)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {formik.touched.supportedCurrencies && formik.errors.supportedCurrencies && (
                <p className="text-sm text-red-600">{formik.errors.supportedCurrencies}</p>
              )}
            </CardContent>
          </Card>

          {/* Supported Payment Methods */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Supported Payment Methods</CardTitle>
              <CardDescription>
                Select the payment methods this gateway supports or add custom ones
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Predefined Payment Method Options */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Common Payment Methods</Label>
                <div className="grid grid-cols-2 gap-4">
                  {paymentMethodOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`method-${option.value}`}
                        checked={formik.values.supportedMethods.includes(option.value)}
                        onCheckedChange={(checked) =>
                          handlePaymentMethodChange(option.value, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`method-${option.value}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Add Custom Payment Method */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Add Custom Payment Method</Label>
                <div className="flex gap-2">
                  <Input
                    value={newPaymentMethod}
                    onChange={(e) => setNewPaymentMethod(e.target.value)}
                    placeholder="Enter payment method (e.g., Apple Pay, BNPL)"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        addCustomPaymentMethod()
                      }
                    }}
                  />
                  <Button type="button" onClick={addCustomPaymentMethod} size="sm">
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Selected Payment Methods Display */}
              {formik.values.supportedMethods.length > 0 && (
                <div>
                  <Label className="text-sm font-medium mb-2 block">Selected Payment Methods</Label>
                  <div className="flex flex-wrap gap-2">
                    {formik.values.supportedMethods.map((method) => {
                      // Find the label for predefined methods, or use the method code for custom ones
                      const predefinedMethod = paymentMethodOptions.find(option => option.value === method)
                      const displayName = predefinedMethod ? predefinedMethod.label : method.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())

                      return (
                        <Badge key={method} variant="secondary" className="flex items-center gap-1">
                          {displayName}
                          <X
                            className="w-3 h-3 cursor-pointer"
                            onClick={() => removeCustomPaymentMethod(method)}
                          />
                        </Badge>
                      )
                    })}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Supported Countries */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Supported Countries</CardTitle>
              <CardDescription>
                Select the countries this gateway supports (optional)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Predefined Country Options */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Common Countries</Label>
                <div className="grid grid-cols-2 gap-4">
                  {countryOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`country-${option.value}`}
                        checked={formik.values.supportedCountries.includes(option.value)}
                        onCheckedChange={(checked) =>
                          handleCountryChange(option.value, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`country-${option.value}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Add Custom Country */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Add Custom Country</Label>
                <div className="flex gap-2">
                  <Input
                    value={newCountry}
                    onChange={(e) => setNewCountry(e.target.value)}
                    placeholder="Enter country code (e.g., DE, FR)"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        addCustomCountry()
                      }
                    }}
                  />
                  <Button type="button" onClick={addCustomCountry} size="sm">
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Selected Countries Display */}
              {formik.values.supportedCountries.length > 0 && (
                <div>
                  <Label className="text-sm font-medium mb-2 block">Selected Countries</Label>
                  <div className="flex flex-wrap gap-2">
                    {formik.values.supportedCountries.map((country) => {
                      // Find the label for predefined countries, or use the country code for custom ones
                      const predefinedCountry = countryOptions.find(option => option.value === country)
                      const displayName = predefinedCountry ? predefinedCountry.label : country

                      return (
                        <Badge key={country} variant="secondary" className="flex items-center gap-1">
                          {displayName}
                          <X
                            className="w-3 h-3 cursor-pointer"
                            onClick={() => removeCustomCountry(country)}
                          />
                        </Badge>
                      )
                    })}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="isActive">Active</Label>
                  <p className="text-sm text-muted-foreground">
                    Make this gateway available to institutes
                  </p>
                </div>
                <Switch
                  id="isActive"
                  checked={formik.values.isActive}
                  onCheckedChange={(checked) => formik.setFieldValue('isActive', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="isFeatured">Featured</Label>
                  <p className="text-sm text-muted-foreground">
                    Show as featured gateway in institute selection
                  </p>
                </div>
                <Switch
                  id="isFeatured"
                  checked={formik.values.isFeatured}
                  onCheckedChange={(checked) => formik.setFieldValue('isFeatured', checked)}
                />
              </div>


            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : (isEditing ? 'Update Gateway' : 'Create Gateway')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
