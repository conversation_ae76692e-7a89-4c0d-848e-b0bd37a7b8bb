'use client'

import React from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, Save, X } from 'lucide-react'
import { Permission } from '@/stores/super-admin/useRolePermissionsStore'

interface PermissionFormProps {
  permission?: Permission | null
  onSubmit: (values: Partial<Permission>) => Promise<boolean>
  onCancel: () => void
  isLoading?: boolean
}

const permissionValidationSchema = Yup.object({
  name: Yup.string()
    .required('Permission name is required')
    .min(2, 'Permission name must be at least 2 characters')
    .max(100, 'Permission name must not exceed 100 characters'),
  
  description: Yup.string()
    .max(500, 'Description must not exceed 500 characters'),
  
  category: Yup.string()
    .required('Category is required'),
  
  resource: Yup.string()
    .required('Resource is required')
    .matches(/^[a-z_]+$/, 'Resource must be lowercase with underscores only'),
  
  action: Yup.string()
    .required('Action is required')
    .matches(/^[a-z_]+$/, 'Action must be lowercase with underscores only'),
  
  scope: Yup.string()
    .required('Scope is required'),
  
  requiredLevel: Yup.number()
    .required('Required level is required')
    .min(1, 'Level must be between 1 and 5')
    .max(5, 'Level must be between 1 and 5')
    .integer('Level must be a whole number'),
  
  isSystemPermission: Yup.boolean(),
})

const categoryOptions = [
  'user_management',
  'institute_management',
  'course_management',
  'billing_management',
  'system_administration',
  'analytics_reporting',
  'content_management',
  'communication',
]

const actionOptions = [
  'create',
  'read',
  'update',
  'delete',
  'manage',
  'view',
  'edit',
  'approve',
  'reject',
  'export',
  'import',
]

const scopeOptions = [
  'global',
  'institute',
  'department',
  'branch',
  'own',
  'assigned',
]

const levelOptions = [
  { value: 1, label: 'Level 1 - Executive/Director' },
  { value: 2, label: 'Level 2 - Manager/Head' },
  { value: 3, label: 'Level 3 - Senior Staff' },
  { value: 4, label: 'Level 4 - Staff' },
  { value: 5, label: 'Level 5 - Junior/Trainee' },
]

export default function PermissionForm({ permission, onSubmit, onCancel, isLoading = false }: PermissionFormProps) {
  const initialValues: Partial<Permission> = {
    name: permission?.name || '',
    description: permission?.description || '',
    category: permission?.category || '',
    resource: permission?.resource || '',
    action: permission?.action || '',
    scope: permission?.scope || '',
    requiredLevel: permission?.requiredLevel || 4,
    isSystemPermission: permission?.isSystemPermission || false,
  }

  const handleSubmit = async (values: Partial<Permission>) => {
    const success = await onSubmit(values)
    if (success) {
      onCancel() // Close form on success
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {permission ? 'Edit Permission' : 'Create New Permission'}
        </CardTitle>
        <CardDescription>
          {permission 
            ? 'Update the permission details and settings below.'
            : 'Create a new permission that can be assigned to roles.'
          }
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Formik
          initialValues={initialValues}
          validationSchema={permissionValidationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched, isSubmitting }) => (
            <Form className="space-y-6">
              {/* Permission Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  Permission Name *
                </Label>
                <Field
                  as={Input}
                  id="name"
                  name="name"
                  placeholder="Enter permission name (e.g., Manage Users)"
                  className={errors.name && touched.name ? 'border-red-500' : ''}
                />
                <ErrorMessage name="name" component="div" className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  <span>{errors.name}</span>
                </ErrorMessage>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium">
                  Description
                </Label>
                <Field
                  as={Textarea}
                  id="description"
                  name="description"
                  placeholder="Describe what this permission allows..."
                  rows={3}
                  className={errors.description && touched.description ? 'border-red-500' : ''}
                />
                <ErrorMessage name="description" component="div" className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  <span>{errors.description}</span>
                </ErrorMessage>
              </div>

              {/* Category */}
              <div className="space-y-2">
                <Label htmlFor="category" className="text-sm font-medium">
                  Category *
                </Label>
                <Select
                  value={values.category}
                  onValueChange={(value) => setFieldValue('category', value)}
                >
                  <SelectTrigger className={errors.category && touched.category ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryOptions.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <ErrorMessage name="category" component="div" className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  <span>{errors.category}</span>
                </ErrorMessage>
              </div>

              {/* Resource and Action */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="resource" className="text-sm font-medium">
                    Resource *
                  </Label>
                  <Field
                    as={Input}
                    id="resource"
                    name="resource"
                    placeholder="e.g., users, courses, institutes"
                    className={errors.resource && touched.resource ? 'border-red-500' : ''}
                  />
                  <ErrorMessage name="resource" component="div" className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.resource}</span>
                  </ErrorMessage>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="action" className="text-sm font-medium">
                    Action *
                  </Label>
                  <Select
                    value={values.action}
                    onValueChange={(value) => setFieldValue('action', value)}
                  >
                    <SelectTrigger className={errors.action && touched.action ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select action" />
                    </SelectTrigger>
                    <SelectContent>
                      {actionOptions.map((action) => (
                        <SelectItem key={action} value={action}>
                          {action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <ErrorMessage name="action" component="div" className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.action}</span>
                  </ErrorMessage>
                </div>
              </div>

              {/* Scope and Required Level */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="scope" className="text-sm font-medium">
                    Scope *
                  </Label>
                  <Select
                    value={values.scope}
                    onValueChange={(value) => setFieldValue('scope', value)}
                  >
                    <SelectTrigger className={errors.scope && touched.scope ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select scope" />
                    </SelectTrigger>
                    <SelectContent>
                      {scopeOptions.map((scope) => (
                        <SelectItem key={scope} value={scope}>
                          {scope.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <ErrorMessage name="scope" component="div" className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.scope}</span>
                  </ErrorMessage>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="requiredLevel" className="text-sm font-medium">
                    Required Level *
                  </Label>
                  <Select
                    value={values.requiredLevel?.toString()}
                    onValueChange={(value) => setFieldValue('requiredLevel', parseInt(value))}
                  >
                    <SelectTrigger className={errors.requiredLevel && touched.requiredLevel ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select level" />
                    </SelectTrigger>
                    <SelectContent>
                      {levelOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value.toString()}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <ErrorMessage name="requiredLevel" component="div" className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.requiredLevel}</span>
                  </ErrorMessage>
                </div>
              </div>

              {/* System Permission Switch */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <Label htmlFor="isSystemPermission" className="text-sm font-medium">
                    System Permission
                  </Label>
                  <p className="text-sm text-gray-500">
                    System permissions are protected and cannot be deleted by users
                  </p>
                </div>
                <Switch
                  id="isSystemPermission"
                  checked={values.isSystemPermission}
                  onCheckedChange={(checked) => setFieldValue('isSystemPermission', checked)}
                />
              </div>

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading || isSubmitting}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || isSubmitting}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {isLoading || isSubmitting ? 'Saving...' : (permission ? 'Update Permission' : 'Create Permission')}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
