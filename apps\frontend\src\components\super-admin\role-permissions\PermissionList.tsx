'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { 
  Search, 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Key, 
  Filter,
  Grid,
  List,
  AlertTriangle,
  Shield
} from 'lucide-react'
import { Permission, useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'

interface PermissionListProps {
  onCreatePermission: () => void
  onEditPermission: (permission: Permission) => void
  viewMode: 'list' | 'cards'
  onViewModeChange: (mode: 'list' | 'cards') => void
}

export default function PermissionList({ 
  onCreatePermission, 
  onEditPermission,
  viewMode,
  onViewModeChange 
}: PermissionListProps) {
  const {
    permissions,
    isLoading,
    filters,
    setFilters,
    deletePermission,
  } = useRolePermissionsStore()

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [permissionToDelete, setPermissionToDelete] = useState<Permission | null>(null)

  const handleDeletePermission = async () => {
    if (permissionToDelete) {
      await deletePermission(permissionToDelete.id)
      setDeleteDialogOpen(false)
      setPermissionToDelete(null)
    }
  }

  const openDeleteDialog = (permission: Permission) => {
    setPermissionToDelete(permission)
    setDeleteDialogOpen(true)
  }

  const getActionBadgeColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create': return 'bg-green-100 text-green-800'
      case 'read': case 'view': return 'bg-blue-100 text-blue-800'
      case 'update': case 'edit': return 'bg-yellow-100 text-yellow-800'
      case 'delete': return 'bg-red-100 text-red-800'
      case 'manage': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getScopeBadgeColor = (scope: string) => {
    switch (scope.toLowerCase()) {
      case 'global': return 'bg-red-100 text-red-800'
      case 'institute': return 'bg-blue-100 text-blue-800'
      case 'department': return 'bg-yellow-100 text-yellow-800'
      case 'branch': return 'bg-green-100 text-green-800'
      case 'own': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelBadgeColor = (level: number) => {
    switch (level) {
      case 1: return 'bg-red-100 text-red-800'
      case 2: return 'bg-orange-100 text-orange-800'
      case 3: return 'bg-yellow-100 text-yellow-800'
      case 4: return 'bg-blue-100 text-blue-800'
      case 5: return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get unique categories for filter
  const categories = Array.from(new Set(permissions.map(p => p.category)))

  const PermissionCard = ({ permission }: { permission: Permission }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg flex items-center gap-2">
              <Key className="h-4 w-4" />
              {permission.name}
            </CardTitle>
            <div className="flex items-center gap-2 flex-wrap">
              <Badge className={getActionBadgeColor(permission.action)}>
                {permission.action}
              </Badge>
              <Badge variant="outline">
                {permission.resource}
              </Badge>
              <Badge className={getScopeBadgeColor(permission.scope)}>
                {permission.scope}
              </Badge>
              <Badge className={getLevelBadgeColor(permission.requiredLevel)}>
                Level {permission.requiredLevel}+
              </Badge>
              {permission.isSystemPermission && (
                <Badge variant="outline" className="border-orange-300 text-orange-700">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  System
                </Badge>
              )}
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEditPermission(permission)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Permission
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => openDeleteDialog(permission)}
                className="text-red-600"
                disabled={permission.isSystemPermission}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Permission
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <CardDescription className="mb-3">
          {permission.description || 'No description provided'}
        </CardDescription>
        <div className="flex items-center justify-between text-sm text-gray-500">
          <Badge variant="secondary">
            {permission.category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Badge>
          <span>Created {new Date(permission.createdAt).toLocaleDateString()}</span>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Permission Management</h2>
          <p className="text-gray-600">Manage system permissions that can be assigned to roles</p>
        </div>
        <Button onClick={onCreatePermission} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Permission
        </Button>
      </div>

      {/* Filters and Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search permissions..."
                  value={filters.search || ''}
                  onChange={(e) => setFilters({ search: e.target.value })}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={filters.category || ''}
                onChange={(e) => setFilters({ category: e.target.value || undefined })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => onViewModeChange('list')}
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'cards' ? 'default' : 'outline'}
                size="sm"
                onClick={() => onViewModeChange('cards')}
              >
                <Grid className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      {isLoading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading permissions...</p>
        </div>
      ) : permissions.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No permissions found</h3>
            <p className="text-gray-600 mb-4">Get started by creating your first permission.</p>
            <Button onClick={onCreatePermission}>
              <Plus className="h-4 w-4 mr-2" />
              Create Permission
            </Button>
          </CardContent>
        </Card>
      ) : viewMode === 'cards' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {permissions.map((permission) => (
            <PermissionCard key={permission.id} permission={permission} />
          ))}
        </div>
      ) : (
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Permission Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Resource</TableHead>
                <TableHead>Scope</TableHead>
                <TableHead>Level</TableHead>
                <TableHead className="w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {permissions.map((permission) => (
                <TableRow key={permission.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium flex items-center gap-2">
                        <Key className="h-4 w-4" />
                        {permission.name}
                        {permission.isSystemPermission && (
                          <Badge variant="outline" className="border-orange-300 text-orange-700">
                            System
                          </Badge>
                        )}
                      </div>
                      {permission.description && (
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {permission.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {permission.category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getActionBadgeColor(permission.action)}>
                      {permission.action}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {permission.resource}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getScopeBadgeColor(permission.scope)}>
                      {permission.scope}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getLevelBadgeColor(permission.requiredLevel)}>
                      Level {permission.requiredLevel}+
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEditPermission(permission)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Permission
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => openDeleteDialog(permission)}
                          className="text-red-600"
                          disabled={permission.isSystemPermission}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Permission
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Permission</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the permission "{permissionToDelete?.name}"? 
              This action cannot be undone and will remove this permission from all roles.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeletePermission} className="bg-red-600 hover:bg-red-700">
              Delete Permission
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
