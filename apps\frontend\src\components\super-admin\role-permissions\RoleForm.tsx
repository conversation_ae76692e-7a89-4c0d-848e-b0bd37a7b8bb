'use client'

import React from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, Save, X } from 'lucide-react'
import { Role } from '@/stores/super-admin/useRolePermissionsStore'

interface RoleFormProps {
  role?: Role | null
  onSubmit: (values: Partial<Role>) => Promise<boolean>
  onCancel: () => void
  isLoading?: boolean
}

const roleValidationSchema = Yup.object({
  name: Yup.string()
    .required('Role name is required')
    .min(2, 'Role name must be at least 2 characters')
    .max(50, 'Role name must not exceed 50 characters')
    .matches(/^[a-zA-Z0-9\s_-]+$/, 'Role name can only contain letters, numbers, spaces, hyphens, and underscores'),
  
  description: Yup.string()
    .max(500, 'Description must not exceed 500 characters'),
  
  level: Yup.number()
    .required('Level is required')
    .min(1, 'Level must be between 1 and 5')
    .max(5, 'Level must be between 1 and 5')
    .integer('Level must be a whole number'),
  
  isActive: Yup.boolean(),
  isSystemRole: Yup.boolean(),
})

const levelOptions = [
  { value: 1, label: 'Level 1 - Executive/Director', description: 'Highest level with full access' },
  { value: 2, label: 'Level 2 - Manager/Head', description: 'Management level with departmental access' },
  { value: 3, label: 'Level 3 - Senior Staff', description: 'Senior level with extended permissions' },
  { value: 4, label: 'Level 4 - Staff', description: 'Standard staff level' },
  { value: 5, label: 'Level 5 - Junior/Trainee', description: 'Entry level with basic permissions' },
]

export default function RoleForm({ role, onSubmit, onCancel, isLoading = false }: RoleFormProps) {
  const initialValues: Partial<Role> = {
    name: role?.name || '',
    description: role?.description || '',
    level: role?.level || 4,
    isActive: role?.isActive ?? true,
    isSystemRole: role?.isSystemRole || false,
  }

  const handleSubmit = async (values: Partial<Role>) => {
    const success = await onSubmit(values)
    if (success) {
      onCancel() // Close form on success
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {role ? 'Edit Role' : 'Create New Role'}
        </CardTitle>
        <CardDescription>
          {role 
            ? 'Update the role information and settings below.'
            : 'Create a new role with specific permissions and access levels.'
          }
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Formik
          initialValues={initialValues}
          validationSchema={roleValidationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched, isSubmitting }) => (
            <Form className="space-y-6">
              {/* Role Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  Role Name *
                </Label>
                <Field
                  as={Input}
                  id="name"
                  name="name"
                  placeholder="Enter role name (e.g., Academic Coordinator)"
                  className={errors.name && touched.name ? 'border-red-500' : ''}
                />
                <ErrorMessage name="name" component="div" className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  <span>{errors.name}</span>
                </ErrorMessage>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium">
                  Description
                </Label>
                <Field
                  as={Textarea}
                  id="description"
                  name="description"
                  placeholder="Describe the role's responsibilities and purpose..."
                  rows={3}
                  className={errors.description && touched.description ? 'border-red-500' : ''}
                />
                <ErrorMessage name="description" component="div" className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  <span>{errors.description}</span>
                </ErrorMessage>
              </div>

              {/* Level */}
              <div className="space-y-2">
                <Label htmlFor="level" className="text-sm font-medium">
                  Role Level *
                </Label>
                <Select
                  value={values.level?.toString()}
                  onValueChange={(value) => setFieldValue('level', parseInt(value))}
                >
                  <SelectTrigger className={errors.level && touched.level ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select role level" />
                  </SelectTrigger>
                  <SelectContent>
                    {levelOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        <div className="flex flex-col">
                          <span className="font-medium">{option.label}</span>
                          <span className="text-sm text-gray-500">{option.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <ErrorMessage name="level" component="div" className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  <span>{errors.level}</span>
                </ErrorMessage>
              </div>

              {/* Status Switches */}
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label htmlFor="isActive" className="text-sm font-medium">
                      Active Status
                    </Label>
                    <p className="text-sm text-gray-500">
                      Whether this role is currently active and can be assigned to users
                    </p>
                  </div>
                  <Switch
                    id="isActive"
                    checked={values.isActive}
                    onCheckedChange={(checked) => setFieldValue('isActive', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label htmlFor="isSystemRole" className="text-sm font-medium">
                      System Role
                    </Label>
                    <p className="text-sm text-gray-500">
                      System roles are protected and cannot be deleted by users
                    </p>
                  </div>
                  <Switch
                    id="isSystemRole"
                    checked={values.isSystemRole}
                    onCheckedChange={(checked) => setFieldValue('isSystemRole', checked)}
                  />
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading || isSubmitting}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || isSubmitting}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {isLoading || isSubmitting ? 'Saving...' : (role ? 'Update Role' : 'Create Role')}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
