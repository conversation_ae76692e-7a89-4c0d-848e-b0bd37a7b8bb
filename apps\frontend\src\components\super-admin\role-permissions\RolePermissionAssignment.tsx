'use client'

import React, { useState, useEffect } from 'react'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Search, Save, X, Shield, Users, AlertCircle } from 'lucide-react'
import { Role, Permission, useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'

interface RolePermissionAssignmentProps {
  role: Role
  onCancel: () => void
}

const assignmentValidationSchema = Yup.object({
  selectedPermissions: Yup.array()
    .min(0, 'At least one permission must be selected')
})

export default function RolePermissionAssignment({ role, onCancel }: RolePermissionAssignmentProps) {
  const {
    permissions,
    selectedPermissions,
    isLoading,
    fetchPermissions,
    fetchRolePermissions,
    assignPermissionsToRole,
    setSelectedPermissions,
  } = useRolePermissionsStore()

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')

  useEffect(() => {
    fetchPermissions()
    fetchRolePermissions(role.id)
  }, [role.id, fetchPermissions, fetchRolePermissions])

  // Filter permissions based on search and category
  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = permission.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         permission.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         permission.resource.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesCategory = !selectedCategory || permission.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  // Group permissions by category
  const permissionsByCategory = filteredPermissions.reduce((acc, permission) => {
    const category = permission.category
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)

  // Get unique categories
  const categories = Array.from(new Set(permissions.map(p => p.category)))

  const handlePermissionToggle = (permissionId: string, checked: boolean) => {
    const newSelectedPermissions = checked
      ? [...selectedPermissions, permissionId]
      : selectedPermissions.filter(id => id !== permissionId)
    
    setSelectedPermissions(newSelectedPermissions)
  }

  const handleSelectAll = (categoryPermissions: Permission[], checked: boolean) => {
    const categoryPermissionIds = categoryPermissions.map(p => p.id)
    
    if (checked) {
      const newSelected = [...new Set([...selectedPermissions, ...categoryPermissionIds])]
      setSelectedPermissions(newSelected)
    } else {
      const newSelected = selectedPermissions.filter(id => !categoryPermissionIds.includes(id))
      setSelectedPermissions(newSelected)
    }
  }

  const handleSubmit = async () => {
    const success = await assignPermissionsToRole(role.id, selectedPermissions)
    if (success) {
      onCancel()
    }
  }

  const getCategoryPermissionCount = (categoryPermissions: Permission[]) => {
    const selectedCount = categoryPermissions.filter(p => selectedPermissions.includes(p.id)).length
    return `${selectedCount}/${categoryPermissions.length}`
  }

  const isAllCategorySelected = (categoryPermissions: Permission[]) => {
    return categoryPermissions.every(p => selectedPermissions.includes(p.id))
  }

  const isSomeCategorySelected = (categoryPermissions: Permission[]) => {
    return categoryPermissions.some(p => selectedPermissions.includes(p.id))
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Assign Permissions to Role: {role.name}
        </CardTitle>
        <CardDescription>
          Select the permissions that should be granted to users with the "{role.name}" role.
          Users with Level {role.level} access will inherit these permissions.
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Formik
          initialValues={{ selectedPermissions }}
          validationSchema={assignmentValidationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ isSubmitting }) => (
            <Form className="space-y-6">
              {/* Search and Filter Controls */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search permissions..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="sm:w-48">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Categories</option>
                    {categories.map(category => (
                      <option key={category} value={category}>
                        {category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Summary */}
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-900">
                    {selectedPermissions.length} permission{selectedPermissions.length !== 1 ? 's' : ''} selected
                  </span>
                </div>
                <Badge variant="secondary">
                  Level {role.level} Role
                </Badge>
              </div>

              {/* Permissions List */}
              <ScrollArea className="h-96 border rounded-lg">
                <div className="p-4 space-y-6">
                  {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                    <div key={category} className="space-y-3">
                      {/* Category Header */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Checkbox
                            id={`category-${category}`}
                            checked={isAllCategorySelected(categoryPermissions)}
                            onCheckedChange={(checked) => handleSelectAll(categoryPermissions, checked as boolean)}
                            className={isSomeCategorySelected(categoryPermissions) && !isAllCategorySelected(categoryPermissions) ? 'data-[state=checked]:bg-blue-600' : ''}
                          />
                          <Label htmlFor={`category-${category}`} className="text-lg font-semibold cursor-pointer">
                            {category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Label>
                          <Badge variant="outline">
                            {getCategoryPermissionCount(categoryPermissions)}
                          </Badge>
                        </div>
                      </div>

                      {/* Category Permissions */}
                      <div className="ml-6 space-y-2">
                        {categoryPermissions.map((permission) => (
                          <div key={permission.id} className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50">
                            <Checkbox
                              id={permission.id}
                              checked={selectedPermissions.includes(permission.id)}
                              onCheckedChange={(checked) => handlePermissionToggle(permission.id, checked as boolean)}
                            />
                            <div className="flex-1 min-w-0">
                              <Label htmlFor={permission.id} className="font-medium cursor-pointer">
                                {permission.name}
                              </Label>
                              {permission.description && (
                                <p className="text-sm text-gray-600 mt-1">
                                  {permission.description}
                                </p>
                              )}
                              <div className="flex items-center gap-2 mt-2">
                                <Badge variant="secondary" className="text-xs">
                                  {permission.action}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  {permission.resource}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  {permission.scope}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  Level {permission.requiredLevel}+
                                </Badge>
                                {permission.isSystemPermission && (
                                  <Badge variant="destructive" className="text-xs">
                                    System
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {category !== Object.keys(permissionsByCategory)[Object.keys(permissionsByCategory).length - 1] && (
                        <Separator className="my-4" />
                      )}
                    </div>
                  ))}

                  {Object.keys(permissionsByCategory).length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                      <p>No permissions found matching your search criteria.</p>
                    </div>
                  )}
                </div>
              </ScrollArea>

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading || isSubmitting}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isLoading || isSubmitting}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {isLoading || isSubmitting ? 'Saving...' : 'Save Permissions'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
