'use client'

import React, { useState, useEffect } from 'react'
import { 
  ShoppingCart, 
  Eye, 
  Settings, 
  Save, 
  Building,
  Users,
  CreditCard,
  Video,
  Filter,
  Search,
  CheckCircle,
  AlertCircle,
  Loader2,
  Globe
} from 'lucide-react'
import { InstituteTheme, Theme } from '@/types/themes'
import { useThemesStore } from '@/stores/super-admin/useThemesStore'
import { ThemeSelector } from './ThemeSelector'
import { toast } from 'sonner'

interface Institute {
  id: string
  name: string
  domain: string
  isVerified: boolean
  currentTheme?: string
}

export function InstituteThemeManager() {
  const {
    loading,
    applyTheme
  } = useThemesStore()

  const [selectedTheme, setSelectedTheme] = useState<Theme | null>(null)
  const [selectedInstitute, setSelectedInstitute] = useState<Institute | null>(null)
  const [institutes, setInstitutes] = useState<Institute[]>([])
  const [searchInstitute, setSearchInstitute] = useState('')
  const [applying, setApplying] = useState(false)

  // Mock institutes data (replace with actual API call)
  useEffect(() => {
    const mockInstitutes: Institute[] = [
      {
        id: '1',
        name: 'Tech Academy',
        domain: 'techacademy.com',
        isVerified: true,
        currentTheme: '3'
      },
      {
        id: '2',
        name: 'Business School',
        domain: 'businessschool.edu',
        isVerified: true,
        currentTheme: undefined
      },
      {
        id: '3',
        name: 'Art Institute',
        domain: 'artinstitute.org',
        isVerified: false,
        currentTheme: undefined
      }
    ]
    setInstitutes(mockInstitutes)
  }, [])

  const handleThemeSelect = (theme: Theme) => {
    setSelectedTheme(theme)
  }

  const handleInstituteSelect = (institute: Institute) => {
    setSelectedInstitute(institute)
  }

  const handleApplyTheme = async () => {
    if (!selectedTheme || !selectedInstitute) {
      toast.error('Please select both a theme and an institute')
      return
    }

    if (!selectedInstitute.isVerified) {
      toast.error('Cannot apply theme to unverified institute domain')
      return
    }

    setApplying(true)
    try {
      await applyTheme({
        themeId: selectedTheme.id,
        targetType: 'institute',
        targetId: selectedInstitute.id
      })
      
      // Update local state
      setInstitutes(prev => prev.map(inst => 
        inst.id === selectedInstitute.id 
          ? { ...inst, currentTheme: selectedTheme.id }
          : inst
      ))
      
      toast.success('Institute theme applied successfully', {
        description: `${selectedTheme.name} is now active for ${selectedInstitute.name}.`
      })
    } catch (error) {
      toast.error('Failed to apply theme')
    } finally {
      setApplying(false)
    }
  }

  const handlePreviewTheme = () => {
    if (!selectedTheme || !selectedInstitute) {
      toast.error('Please select both a theme and an institute to preview')
      return
    }
    
    // Open preview in new tab/window
    const previewUrl = `/preview/institute/${selectedInstitute.id}/${selectedTheme.id}`
    window.open(previewUrl, '_blank', 'width=1200,height=800')
  }

  const filteredInstitutes = institutes.filter(institute =>
    institute.name.toLowerCase().includes(searchInstitute.toLowerCase()) ||
    institute.domain.toLowerCase().includes(searchInstitute.toLowerCase())
  )

  const verifiedInstitutes = filteredInstitutes.filter(inst => inst.isVerified)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <ShoppingCart className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Institute Marketplace Themes
              </h2>
              <p className="text-sm text-gray-500">
                Assign e-commerce themes to institute domains for course marketplace functionality
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Preview Button */}
            <button
              onClick={handlePreviewTheme}
              disabled={!selectedTheme || !selectedInstitute}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </button>

            {/* Apply Button */}
            <button
              onClick={handleApplyTheme}
              disabled={!selectedTheme || !selectedInstitute || !selectedInstitute.isVerified || applying}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {applying ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Applying...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Apply Theme
                </>
              )}
            </button>
          </div>
        </div>

        {/* Selection Status */}
        {selectedInstitute && selectedTheme && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-blue-600" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Ready to Apply
                </h3>
                <div className="mt-1 text-sm text-blue-700">
                  <p>
                    Apply <strong>{selectedTheme.name}</strong> theme to <strong>{selectedInstitute.name}</strong> 
                    ({selectedInstitute.domain})
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Institute Selection */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Institute List */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="mb-4">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Select Institute
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Choose an institute to assign a marketplace theme. Only verified domains can have themes applied.
            </p>
            
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search institutes..."
                value={searchInstitute}
                onChange={(e) => setSearchInstitute(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>
          </div>

          {/* Institute List */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredInstitutes.map((institute) => (
              <div
                key={institute.id}
                onClick={() => institute.isVerified && handleInstituteSelect(institute)}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedInstitute?.id === institute.id
                    ? 'border-purple-500 bg-purple-50'
                    : institute.isVerified
                    ? 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    : 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      institute.isVerified ? 'bg-green-100' : 'bg-gray-100'
                    }`}>
                      <Building className={`w-4 h-4 ${
                        institute.isVerified ? 'text-green-600' : 'text-gray-400'
                      }`} />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {institute.name}
                      </h4>
                      <p className="text-xs text-gray-500">{institute.domain}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {institute.isVerified ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Verified
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                        Pending
                      </span>
                    )}
                    
                    {institute.currentTheme && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <Globe className="w-3 h-3 mr-1" />
                        Themed
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Stats */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Total Institutes:</span>
                <span className="ml-2 font-medium text-gray-900">{institutes.length}</span>
              </div>
              <div>
                <span className="text-gray-500">Verified:</span>
                <span className="ml-2 font-medium text-green-600">{verifiedInstitutes.length}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Theme Selection */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="mb-4">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Select Marketplace Theme
            </h3>
            <p className="text-sm text-gray-500">
              Choose an e-commerce theme designed for course marketplaces with shopping cart, 
              payment processing, and live class integration.
            </p>
          </div>

          <ThemeSelector
            type="institute"
            onThemeSelect={handleThemeSelect}
            selectedTheme={selectedTheme}
            showFilters={false}
          />
        </div>
      </div>

      {/* E-commerce Features Preview */}
      {selectedTheme && 'ecommerceFeatures' in selectedTheme && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            E-commerce Features
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { name: 'Shopping Cart', icon: ShoppingCart, enabled: true },
              { name: 'Payment Gateway', icon: CreditCard, enabled: true },
              { name: 'Live Classes', icon: Video, enabled: selectedTheme.liveClassIntegration?.enabled },
              { name: 'User Management', icon: Users, enabled: true }
            ].map((feature) => (
              <div
                key={feature.name}
                className={`p-4 border rounded-lg ${
                  feature.enabled 
                    ? 'border-green-200 bg-green-50' 
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <feature.icon className={`w-4 h-4 ${
                    feature.enabled ? 'text-green-600' : 'text-gray-400'
                  }`} />
                  <span className={`text-sm font-medium ${
                    feature.enabled ? 'text-green-900' : 'text-gray-500'
                  }`}>
                    {feature.name}
                  </span>
                </div>
                <div className="mt-1">
                  <span className={`text-xs ${
                    feature.enabled ? 'text-green-700' : 'text-gray-400'
                  }`}>
                    {feature.enabled ? 'Included' : 'Not available'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default InstituteThemeManager
