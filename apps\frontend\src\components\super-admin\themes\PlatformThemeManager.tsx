'use client'

import React, { useState, useEffect } from 'react'
import { 
  Monitor, 
  Eye, 
  Settings, 
  Save, 
  RotateCcw,
  Palette,
  Globe,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { PlatformTheme, Theme } from '@/types/themes'
import { useThemesStore } from '@/stores/super-admin/useThemesStore'
import { ThemeSelector } from './ThemeSelector'
import { toast } from 'sonner'

export function PlatformThemeManager() {
  const {
    currentPlatformTheme,
    loading,
    applyTheme,
    getCurrentPlatformTheme
  } = useThemesStore()

  const [selectedTheme, setSelectedTheme] = useState<Theme | null>(null)
  const [showPreview, setShowPreview] = useState(false)
  const [applying, setApplying] = useState(false)

  useEffect(() => {
    const current = getCurrentPlatformTheme()
    if (current) {
      setSelectedTheme(current)
    }
  }, [getCurrentPlatformTheme])

  const handleThemeSelect = (theme: Theme) => {
    setSelectedTheme(theme)
  }

  const handleApplyTheme = async () => {
    if (!selectedTheme) {
      toast.error('Please select a theme first')
      return
    }

    setApplying(true)
    try {
      await applyTheme({
        themeId: selectedTheme.id,
        targetType: 'platform'
      })
      
      toast.success('Platform theme applied successfully', {
        description: `${selectedTheme.name} is now active on the platform landing page.`
      })
    } catch (error) {
      toast.error('Failed to apply theme')
    } finally {
      setApplying(false)
    }
  }

  const handlePreviewTheme = () => {
    if (!selectedTheme) {
      toast.error('Please select a theme to preview')
      return
    }
    
    // Open preview in new tab/window
    const previewUrl = `/preview/platform/${selectedTheme.id}`
    window.open(previewUrl, '_blank', 'width=1200,height=800')
  }

  const isThemeChanged = selectedTheme?.id !== currentPlatformTheme?.id

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Monitor className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Platform Landing Page Theme
              </h2>
              <p className="text-sm text-gray-500">
                Configure the theme for the main LMS platform landing page
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Preview Button */}
            <button
              onClick={handlePreviewTheme}
              disabled={!selectedTheme}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </button>

            {/* Apply Button */}
            <button
              onClick={handleApplyTheme}
              disabled={!selectedTheme || !isThemeChanged || applying}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {applying ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Applying...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Apply Theme
                </>
              )}
            </button>
          </div>
        </div>

        {/* Current Theme Status */}
        {currentPlatformTheme && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">
                  Current Active Theme
                </h3>
                <div className="mt-1 text-sm text-green-700">
                  <p>
                    <strong>{currentPlatformTheme.name}</strong> is currently active on the platform landing page.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Theme Change Warning */}
        {isThemeChanged && selectedTheme && (
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-yellow-600" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  Theme Change Pending
                </h3>
                <div className="mt-1 text-sm text-yellow-700">
                  <p>
                    You have selected <strong>{selectedTheme.name}</strong>. 
                    Click "Apply Theme" to make it active on the platform.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Theme Selection */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Select Platform Theme
          </h3>
          <p className="text-sm text-gray-500">
            Choose from available platform themes. These themes are designed for the main LMS landing page 
            and include sections like hero, features, about, and contact.
          </p>
        </div>

        <ThemeSelector
          type="platform"
          onThemeSelect={handleThemeSelect}
          selectedTheme={selectedTheme}
          showFilters={true}
        />
      </div>

      {/* Theme Customization Preview */}
      {selectedTheme && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Theme Customization
            </h3>
            <button
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              onClick={() => {
                // Handle customization
                toast.info('Theme customization coming soon')
              }}
            >
              <Palette className="w-4 h-4 mr-2" />
              Customize
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Color Scheme */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Color Scheme</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Primary</span>
                  <div 
                    className="w-6 h-6 rounded border border-gray-200"
                    style={{ backgroundColor: selectedTheme.customization.colors.primary }}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Secondary</span>
                  <div 
                    className="w-6 h-6 rounded border border-gray-200"
                    style={{ backgroundColor: selectedTheme.customization.colors.secondary }}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Accent</span>
                  <div 
                    className="w-6 h-6 rounded border border-gray-200"
                    style={{ backgroundColor: selectedTheme.customization.colors.accent }}
                  />
                </div>
              </div>
            </div>

            {/* Typography */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Typography</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Heading Font</span>
                  <span className="text-sm font-medium text-gray-900">
                    {selectedTheme.customization.fonts.heading}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Body Font</span>
                  <span className="text-sm font-medium text-gray-900">
                    {selectedTheme.customization.fonts.body}
                  </span>
                </div>
              </div>
            </div>

            {/* Layout */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Layout</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Header Style</span>
                  <span className="text-sm font-medium text-gray-900 capitalize">
                    {selectedTheme.customization.layout.headerStyle}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Footer Style</span>
                  <span className="text-sm font-medium text-gray-900 capitalize">
                    {selectedTheme.customization.layout.footerStyle}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Theme Features */}
      {selectedTheme && 'landingPageSections' in selectedTheme && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Landing Page Sections
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { name: 'Hero Section', icon: Globe, enabled: true },
              { name: 'Features', icon: Settings, enabled: true },
              { name: 'About', icon: Monitor, enabled: true },
              { name: 'Contact', icon: Eye, enabled: true }
            ].map((section) => (
              <div
                key={section.name}
                className={`p-4 border rounded-lg ${
                  section.enabled 
                    ? 'border-green-200 bg-green-50' 
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <section.icon className={`w-4 h-4 ${
                    section.enabled ? 'text-green-600' : 'text-gray-400'
                  }`} />
                  <span className={`text-sm font-medium ${
                    section.enabled ? 'text-green-900' : 'text-gray-500'
                  }`}>
                    {section.name}
                  </span>
                </div>
                <div className="mt-1">
                  <span className={`text-xs ${
                    section.enabled ? 'text-green-700' : 'text-gray-400'
                  }`}>
                    {section.enabled ? 'Included' : 'Not available'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default PlatformThemeManager
