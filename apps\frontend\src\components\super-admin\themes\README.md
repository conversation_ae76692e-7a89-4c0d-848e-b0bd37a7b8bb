# Theme Management System

A comprehensive theme management system for the LMS super admin panel, enabling management of both platform landing page themes and institute marketplace themes with visual preview capabilities and real-time application.

## 🚀 Features

### Core Functionality
- ✅ **Platform Landing Page Theme Selection**: Visual theme selector for main LMS platform landing page
- ✅ **Institute Marketplace Theme Management**: E-commerce theme assignment for institute custom domains
- ✅ **Visual Theme Preview**: Thumbnail previews with detailed modal previews
- ✅ **Real-time Theme Application**: Apply themes instantly with live preview capabilities
- ✅ **Theme Customization**: Color schemes, typography, and layout configuration
- ✅ **Advanced Filtering**: Filter by category, status, features, and search functionality

### Platform Themes
- ✅ **Landing Page Sections**: Hero, features, about, contact sections
- ✅ **Responsive Design**: Mobile-first responsive themes
- ✅ **SEO Optimization**: SEO-friendly theme structure
- ✅ **Accessibility**: WCAG compliant themes with proper ARIA support
- ✅ **Customization Options**: Colors, fonts, layout configurations

### Institute Themes
- ✅ **E-commerce Features**: Shopping cart, payment gateway integration
- ✅ **Course Marketplace**: Course listings, filtering, search functionality
- ✅ **Live Class Integration**: Embedded live class booking and management
- ✅ **Payment Processing**: Multiple payment gateway support
- ✅ **User Management**: Student registration and profile management

### Technical Features
- ✅ **TypeScript**: Fully typed with comprehensive interfaces
- ✅ **State Management**: Zustand store following existing patterns
- ✅ **Responsive Design**: Works seamlessly on desktop and mobile
- ✅ **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- ✅ **Error Handling**: Comprehensive error boundaries and user-friendly messages
- ✅ **Toast Notifications**: Success/error notifications using Sonner

## 📁 File Structure

```
apps/frontend/src/components/super-admin/themes/
├── index.ts                      # Main exports
├── README.md                     # This documentation
├── ThemeManagement.tsx           # Main container component
├── PlatformThemeManager.tsx      # Platform theme management
├── InstituteThemeManager.tsx     # Institute theme management
├── ThemeSelector.tsx             # Theme selection component
├── ThemePreview.tsx              # Theme preview components
└── __tests__/                    # Test files (future)
```

## 🛠 Usage

### Basic Usage

```tsx
import { ThemeManagement } from '@/components/super-admin/themes'

export default function ThemesPage() {
  return <ThemeManagement />
}
```

### Platform Theme Management

```tsx
import { PlatformThemeManager } from '@/components/super-admin/themes'

export function PlatformThemes() {
  return <PlatformThemeManager />
}
```

### Institute Theme Management

```tsx
import { InstituteThemeManager } from '@/components/super-admin/themes'

export function InstituteThemes() {
  return <InstituteThemeManager />
}
```

### Theme Selection

```tsx
import { ThemeSelector, useThemesStore } from '@/components/super-admin/themes'

export function ThemeSelection() {
  const { applyTheme } = useThemesStore()
  
  const handleThemeSelect = async (theme) => {
    await applyTheme({
      themeId: theme.id,
      targetType: 'platform'
    })
  }
  
  return (
    <ThemeSelector
      type="platform"
      onThemeSelect={handleThemeSelect}
      showFilters={true}
    />
  )
}
```

## 🔧 Configuration

### Store Configuration

The themes store is configured in `@/stores/super-admin/useThemesStore.ts` and includes:

- **State Management**: Platform themes, institute themes, categories, and UI state
- **CRUD Operations**: Create, update, delete, and apply theme operations
- **Filtering**: Advanced filtering and search capabilities
- **Persistence**: View mode and filter preferences are persisted

### Type Definitions

All types are defined in `@/types/themes.ts`:

```typescript
interface Theme {
  id: string
  name: string
  description: string
  category: ThemeCategory
  type: ThemeType
  customization: ThemeCustomization
  // ... more properties
}

interface PlatformTheme extends Theme {
  type: 'platform'
  landingPageSections: LandingPageSection[]
  heroSection: HeroSectionConfig
  // ... platform-specific properties
}

interface InstituteTheme extends Theme {
  type: 'institute'
  ecommerceFeatures: EcommerceFeature[]
  marketplaceLayout: MarketplaceLayout
  // ... institute-specific properties
}
```

## 🎨 Styling

The components use Tailwind CSS with the following design principles:

- **Consistent Spacing**: Using standard Tailwind spacing scale
- **Color Scheme**: Blue for platform themes, purple for institute themes
- **Typography**: Clear hierarchy with proper font weights and sizes
- **Interactive States**: Hover, focus, and active states for all interactive elements

## ♿ Accessibility

The implementation includes comprehensive accessibility features:

- **ARIA Labels**: Descriptive labels for all interactive elements
- **Keyboard Navigation**: Full keyboard support with proper focus management
- **Screen Reader Support**: Semantic HTML and ARIA attributes
- **Color Contrast**: WCAG compliant color combinations
- **Focus Indicators**: Clear focus indicators for keyboard navigation

## 🔗 Integration

### Navigation Integration

The theme management is integrated with the super admin navigation:

```typescript
// In superAdminNavigation.ts
{
  id: 'themes',
  label: 'Theme Management',
  icon: 'Palette',
  href: '/super-admin/themes',
  description: 'Platform and institute themes'
}
```

### Domain Integration

Institute themes are connected with the domain management system:

- Themes can only be applied to verified institute domains
- Theme assignment is automatic when domains are verified
- Themes are applied at the custom domain level

### Route Structure

- `/super-admin/themes` - Main theme management page
- `/super-admin/themes/platform` - Platform theme management (future)
- `/super-admin/themes/institute` - Institute theme management (future)
- `/super-admin/themes/customization` - Theme customization (future)

## 🚀 Performance

The implementation includes several performance optimizations:

- **Lazy Loading**: Components are loaded on demand
- **Image Optimization**: Theme thumbnails are optimized and cached
- **Efficient Updates**: State updates are batched and optimized
- **Virtual Scrolling**: For large theme lists (future enhancement)

## 🔮 Future Enhancements

Planned improvements include:

- **Theme Builder**: Visual theme builder with drag-and-drop
- **Theme Marketplace**: Public theme marketplace for sharing
- **Advanced Customization**: CSS editor and component customization
- **Theme Analytics**: Usage analytics and performance metrics
- **Bulk Operations**: Apply themes to multiple institutes
- **Theme Versioning**: Version control for theme updates

## 📝 Contributing

When contributing to this module:

1. Follow the existing code patterns and conventions
2. Add comprehensive TypeScript types
3. Include accessibility features
4. Write tests for new functionality
5. Update documentation as needed

## 🐛 Troubleshooting

### Common Issues

1. **Store not updating**: Ensure you're using the store actions correctly
2. **Theme preview not loading**: Check image URLs and network connectivity
3. **Apply theme failing**: Verify institute domain verification status
4. **Styling issues**: Ensure Tailwind classes are properly applied

### Debug Mode

Enable debug mode by setting `NODE_ENV=development` to see:
- Detailed error messages
- Console logging
- Development-only UI elements
