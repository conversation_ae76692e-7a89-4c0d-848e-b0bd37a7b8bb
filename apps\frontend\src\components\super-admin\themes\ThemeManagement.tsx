'use client'

import React, { useState, useEffect } from 'react'
import { 
  Monitor, 
  ShoppingCart, 
  Palette, 
  Settings,
  Globe,
  Building,
  Eye,
  Download,
  Upload,
  Plus
} from 'lucide-react'
import { useThemesStore } from '@/stores/super-admin/useThemesStore'
import { PlatformThemeManager } from './PlatformThemeManager'
import { InstituteThemeManager } from './InstituteThemeManager'

export function ThemeManagement() {
  const {
    platformThemes,
    instituteThemes,
    currentPlatformTheme,
    loading,
    error,
    activeTab,
    setActiveTab,
    fetchPlatformThemes,
    fetchInstituteThemes,
    fetchCategories
  } = useThemesStore()

  const [showUploadModal, setShowUploadModal] = useState(false)

  useEffect(() => {
    const initializeData = async () => {
      try {
        await Promise.all([
          fetchPlatformThemes(),
          fetchInstituteThemes(),
          fetchCategories()
        ])
      } catch (error) {
        console.error('Failed to initialize theme data:', error)
      }
    }

    initializeData()
  }, [fetchPlatformThemes, fetchInstituteThemes, fetchCategories])

  const handleTabChange = (tab: 'platform' | 'institute') => {
    setActiveTab(tab)
  }

  // Error boundary
  if (error && !loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
          <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div className="mt-3 text-center">
            <h3 className="text-lg font-medium text-gray-900">Error Loading Themes</h3>
            <div className="mt-2 text-sm text-gray-500">
              <p>{error}</p>
            </div>
            <div className="mt-4">
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Reload Page
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Theme Management
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Manage platform and institute themes for landing pages and marketplace functionality
              </p>
            </div>
            
            {/* Action Buttons */}
            <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
              <button
                onClick={() => setShowUploadModal(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload Theme
              </button>
              
              <button
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Theme
              </button>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Monitor className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Platform Themes
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {platformThemes.length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ShoppingCart className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Institute Themes
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {instituteThemes.length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Globe className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Active Platform
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {currentPlatformTheme?.name || 'None'}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Building className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Themed Institutes
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      --
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => handleTabChange('platform')}
                className={`py-4 px-6 border-b-2 font-medium text-sm ${
                  activeTab === 'platform'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Monitor className="w-4 h-4" />
                  <span>Platform Themes</span>
                </div>
              </button>
              
              <button
                onClick={() => handleTabChange('institute')}
                className={`py-4 px-6 border-b-2 font-medium text-sm ${
                  activeTab === 'institute'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <ShoppingCart className="w-4 h-4" />
                  <span>Institute Themes</span>
                </div>
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'platform' && <PlatformThemeManager />}
            {activeTab === 'institute' && <InstituteThemeManager />}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Eye className="w-5 h-5 text-blue-600 mr-3" />
              <div className="text-left">
                <div className="text-sm font-medium text-gray-900">Preview All Themes</div>
                <div className="text-xs text-gray-500">View theme gallery</div>
              </div>
            </button>
            
            <button className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Download className="w-5 h-5 text-green-600 mr-3" />
              <div className="text-left">
                <div className="text-sm font-medium text-gray-900">Export Themes</div>
                <div className="text-xs text-gray-500">Download theme packages</div>
              </div>
            </button>
            
            <button className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Settings className="w-5 h-5 text-gray-600 mr-3" />
              <div className="text-left">
                <div className="text-sm font-medium text-gray-900">Theme Settings</div>
                <div className="text-xs text-gray-500">Configure global settings</div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ThemeManagement
