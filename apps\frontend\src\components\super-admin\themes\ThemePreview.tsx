'use client'

import React, { useState } from 'react'
import { 
  Eye, 
  Download, 
  Star, 
  Check, 
  <PERSON>tings, 
  Copy,
  ExternalLink,
  Palette,
  Monitor,
  Smartphone,
  Tablet,
  Crown,
  Shield
} from 'lucide-react'
import { Theme, ThemePreviewProps } from '@/types/themes'

export function ThemePreview({ 
  theme, 
  onSelect, 
  onPreview, 
  isSelected = false, 
  isActive = false,
  showActions = true 
}: ThemePreviewProps) {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handlePreview = (e: React.MouseEvent) => {
    e.stopPropagation()
    onPreview?.(theme)
  }

  const handleSelect = () => {
    onSelect?.(theme)
  }

  const handleImageLoad = () => {
    setImageLoaded(true)
  }

  const handleImageError = () => {
    setImageError(true)
    setImageLoaded(true)
  }

  return (
    <div
      className={`group relative bg-white rounded-lg border-2 transition-all duration-200 hover:shadow-lg cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
        isSelected
          ? 'border-blue-500 ring-2 ring-blue-200'
          : isActive
          ? 'border-green-500 ring-2 ring-green-200'
          : 'border-gray-200 hover:border-gray-300'
      }`}
      onClick={handleSelect}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          handleSelect()
        }
      }}
      aria-label={`${isSelected ? 'Selected' : 'Select'} ${theme.name} theme. ${theme.description}`}
      aria-pressed={isSelected}
    >
      {/* Status Badges */}
      <div className="absolute top-3 left-3 z-10 flex flex-wrap gap-1">
        {isActive && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <Check className="w-3 h-3 mr-1" />
            Active
          </span>
        )}
        {theme.isDefault && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Shield className="w-3 h-3 mr-1" />
            Default
          </span>
        )}
        {theme.isPremium && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Crown className="w-3 h-3 mr-1" />
            Premium
          </span>
        )}
      </div>

      {/* Action Buttons */}
      {showActions && (
        <div className="absolute top-3 right-3 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="flex space-x-1">
            <button
              onClick={handlePreview}
              className="p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              title="Preview theme"
              aria-label={`Preview ${theme.name} theme`}
            >
              <Eye className="w-4 h-4 text-gray-600" aria-hidden="true" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                // Handle customize action
              }}
              className="p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              title="Customize theme"
              aria-label={`Customize ${theme.name} theme`}
            >
              <Palette className="w-4 h-4 text-gray-600" aria-hidden="true" />
            </button>
          </div>
        </div>
      )}

      {/* Theme Thumbnail */}
      <div className="relative aspect-video bg-gray-100 rounded-t-lg overflow-hidden">
        {!imageLoaded && !imageError && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )}
        
        {imageError ? (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <Monitor className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">Preview not available</p>
            </div>
          </div>
        ) : (
          <img
            src={theme.thumbnail}
            alt={`${theme.name} theme preview`}
            className={`w-full h-full object-cover transition-opacity duration-200 ${
              imageLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        )}

        {/* Overlay on hover */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <button
              onClick={handlePreview}
              className="inline-flex items-center px-4 py-2 bg-white text-gray-900 rounded-lg shadow-lg hover:bg-gray-50 transition-colors"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </button>
          </div>
        </div>
      </div>

      {/* Theme Info */}
      <div className="p-4">
        {/* Header */}
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 truncate">
              {theme.name}
            </h3>
            <p className="text-sm text-gray-500 capitalize">
              {theme.type} • {theme.category.name}
            </p>
          </div>
          {theme.rating && (
            <div className="flex items-center ml-2">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span className="text-sm text-gray-600 ml-1">
                {typeof theme.rating === 'object'
                  ? `${theme.rating.average?.toFixed(1) || 0} (${theme.rating.count || 0})`
                  : theme.rating
                }
              </span>
            </div>
          )}
        </div>

        {/* Description */}
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
          {theme.description}
        </p>

        {/* Features/Tags */}
        <div className="flex flex-wrap gap-1 mb-3">
          {theme.tags.slice(0, 3).map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700"
            >
              {tag}
            </span>
          ))}
          {theme.tags.length > 3 && (
            <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-500">
              +{theme.tags.length - 3} more
            </span>
          )}
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
          <span>v{theme.version}</span>
          <span>by {theme.author}</span>
          {theme.downloadCount && (
            <span className="flex items-center">
              <Download className="w-3 h-3 mr-1" />
              {theme.downloadCount}
            </span>
          )}
        </div>

        {/* Color Palette Preview */}
        <div className="flex items-center space-x-1 mb-3">
          <span className="text-xs text-gray-500 mr-2">Colors:</span>
          <div 
            className="w-4 h-4 rounded-full border border-gray-200"
            style={{ backgroundColor: theme.customization.colors.primary }}
            title="Primary color"
          />
          <div 
            className="w-4 h-4 rounded-full border border-gray-200"
            style={{ backgroundColor: theme.customization.colors.secondary }}
            title="Secondary color"
          />
          <div 
            className="w-4 h-4 rounded-full border border-gray-200"
            style={{ backgroundColor: theme.customization.colors.accent }}
            title="Accent color"
          />
        </div>

        {/* Action Buttons */}
        {showActions && (
          <div className="flex space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onSelect?.(theme)
              }}
              className={`flex-1 inline-flex items-center justify-center px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
                isSelected
                  ? 'border-blue-300 bg-blue-50 text-blue-700'
                  : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              {isSelected ? (
                <>
                  <Check className="w-4 h-4 mr-1" />
                  Selected
                </>
              ) : (
                'Select'
              )}
            </button>
            
            <button
              onClick={handlePreview}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <Eye className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <div className="absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none">
          <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
            <Check className="w-3 h-3" />
          </div>
        </div>
      )}
    </div>
  )
}

// Detailed Theme Preview Modal
interface ThemePreviewModalProps {
  theme: Theme | null
  isOpen: boolean
  onClose: () => void
  onSelect?: (theme: Theme) => void
}

export function ThemePreviewModal({ theme, isOpen, onClose, onSelect }: ThemePreviewModalProps) {
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  if (!isOpen || !theme) return null

  const handleSelect = () => {
    onSelect?.(theme)
    onClose()
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) =>
      prev === theme.previewImages.length - 1 ? 0 : prev + 1
    )
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? theme.previewImages.length - 1 : prev - 1
    )
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
          {/* Header */}
          <div className="bg-white px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">{theme.name}</h3>
                <p className="text-sm text-gray-500">{theme.description}</p>
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <button
                    onClick={() => setViewMode('desktop')}
                    className={`p-2 ${viewMode === 'desktop' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
                    title="Desktop view"
                  >
                    <Monitor className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('tablet')}
                    className={`p-2 ${viewMode === 'tablet' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
                    title="Tablet view"
                  >
                    <Tablet className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('mobile')}
                    className={`p-2 ${viewMode === 'mobile' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
                    title="Mobile view"
                  >
                    <Smartphone className="w-4 h-4" />
                  </button>
                </div>

                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ExternalLink className="w-5 h-5 text-gray-500" />
                </button>
              </div>
            </div>
          </div>

          {/* Preview Content */}
          <div className="bg-gray-100 p-6">
            <div className={`mx-auto bg-white rounded-lg shadow-lg overflow-hidden ${
              viewMode === 'desktop' ? 'max-w-5xl' :
              viewMode === 'tablet' ? 'max-w-2xl' :
              'max-w-sm'
            }`}>
              {/* Image Carousel */}
              <div className="relative">
                <img
                  src={theme.previewImages[currentImageIndex] || theme.thumbnail}
                  alt={`${theme.name} preview ${currentImageIndex + 1}`}
                  className="w-full h-auto"
                />

                {theme.previewImages.length > 1 && (
                  <>
                    <button
                      onClick={prevImage}
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75"
                    >
                      ←
                    </button>
                    <button
                      onClick={nextImage}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75"
                    >
                      →
                    </button>

                    {/* Image indicators */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                      {theme.previewImages.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentImageIndex(index)}
                          className={`w-2 h-2 rounded-full ${
                            index === currentImageIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                          }`}
                        />
                      ))}
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse">
            <button
              onClick={handleSelect}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Select Theme
            </button>
            <button
              onClick={onClose}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ThemePreview
