'use client'

import React, { useEffect, useState } from 'react'
import { 
  Search, 
  Filter, 
  Grid, 
  List, 
  SlidersHorizontal,
  X,
  ChevronDown,
  Star,
  Crown,
  Shield
} from 'lucide-react'
import { Theme, ThemeType, ThemeSelectorProps, ThemeFilters } from '@/types/themes'
import { useThemesStore } from '@/stores/super-admin/useThemesStore'
import { ThemePreview, ThemePreviewModal } from './ThemePreview'

export function ThemeSelector({ 
  type, 
  onThemeSelect, 
  selectedTheme, 
  showFilters = true 
}: ThemeSelectorProps) {
  const {
    platformThemes,
    instituteThemes,
    categories,
    loading,
    error,
    filters,
    viewMode,
    setFilters,
    setViewMode,
    fetchPlatformThemes,
    fetchInstituteThemes,
    fetchCategories
  } = useThemesStore()

  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [previewTheme, setPreviewTheme] = useState<Theme | null>(null)
  const [showPreviewModal, setShowPreviewModal] = useState(false)

  const themes = type === 'platform' ? platformThemes : instituteThemes

  useEffect(() => {
    fetchCategories()
    if (type === 'platform') {
      fetchPlatformThemes()
    } else {
      fetchInstituteThemes()
    }
  }, [type, fetchPlatformThemes, fetchInstituteThemes, fetchCategories])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({ search: e.target.value, page: 1 })
  }

  const handleCategoryFilter = (categoryId: string) => {
    setFilters({ 
      category: categoryId === 'all' ? undefined : categoryId, 
      page: 1 
    })
  }

  const handleStatusFilter = (status: string) => {
    setFilters({ 
      status: status === 'all' ? undefined : status as 'active' | 'inactive',
      page: 1 
    })
  }

  const handleThemePreview = (theme: Theme) => {
    setPreviewTheme(theme)
    setShowPreviewModal(true)
  }

  const handleThemeSelect = (theme: Theme) => {
    onThemeSelect(theme)
  }

  const clearFilters = () => {
    setFilters({
      search: undefined,
      category: undefined,
      status: undefined,
      features: undefined,
      tags: undefined,
      page: 1
    })
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.category) count++
    if (filters.status) count++
    if (filters.features?.length) count++
    if (filters.tags?.length) count++
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading themes</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900 capitalize">
            {type} Themes
          </h3>
          <p className="text-sm text-gray-500">
            {type === 'platform' 
              ? 'Choose a theme for the main platform landing page'
              : 'Select themes for institute marketplace pages'
            }
          </p>
        </div>
        
        {/* View Mode Toggle */}
        <div className="flex items-center border border-gray-300 rounded-lg">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 ${
              viewMode === 'grid'
                ? 'bg-blue-100 text-blue-600'
                : 'text-gray-400 hover:text-gray-600'
            }`}
            title="Grid view"
          >
            <Grid className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 ${
              viewMode === 'list'
                ? 'bg-blue-100 text-blue-600'
                : 'text-gray-400 hover:text-gray-600'
            }`}
            title="List view"
          >
            <List className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search themes..."
                  value={filters.search || ''}
                  onChange={handleSearch}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                {filters.search && (
                  <button
                    onClick={() => setFilters({ search: undefined })}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Quick Filters */}
            <div className="flex items-center space-x-4">
              {/* Category Filter */}
              <select
                value={filters.category || 'all'}
                onChange={(e) => handleCategoryFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>

              {/* Status Filter */}
              <select
                value={filters.status || 'all'}
                onChange={(e) => handleStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>

              {/* Advanced Filters Toggle */}
              <button
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className={`inline-flex items-center px-3 py-2 border rounded-lg text-sm font-medium transition-colors ${
                  showAdvancedFilters || activeFiltersCount > 0
                    ? 'border-blue-300 bg-blue-50 text-blue-700'
                    : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <SlidersHorizontal className="w-4 h-4 mr-2" />
                Filters
                {activeFiltersCount > 0 && (
                  <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-blue-600 rounded-full">
                    {activeFiltersCount}
                  </span>
                )}
                <ChevronDown className={`w-4 h-4 ml-1 transition-transform ${showAdvancedFilters ? 'rotate-180' : ''}`} />
              </button>

              {/* Clear Filters */}
              {activeFiltersCount > 0 && (
                <button
                  onClick={clearFilters}
                  className="inline-flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
                >
                  <X className="w-4 h-4 mr-1" />
                  Clear
                </button>
              )}
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Feature Filters */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Features
                  </label>
                  <div className="space-y-2">
                    {['Responsive', 'Dark Mode', 'E-commerce', 'Live Classes'].map((feature) => (
                      <label key={feature} className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">{feature}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Premium Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Type
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 flex items-center">
                        <Crown className="w-3 h-3 mr-1 text-yellow-500" />
                        Premium Only
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 flex items-center">
                        <Shield className="w-3 h-3 mr-1 text-blue-500" />
                        Default Themes
                      </span>
                    </label>
                  </div>
                </div>

                {/* Sort Options */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort By
                  </label>
                  <select
                    value={`${filters.sortField}-${filters.sortOrder}`}
                    onChange={(e) => {
                      const [field, order] = e.target.value.split('-')
                      setFilters({ 
                        sortField: field as any, 
                        sortOrder: order as 'asc' | 'desc' 
                      })
                    }}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="name-asc">Name (A-Z)</option>
                    <option value="name-desc">Name (Z-A)</option>
                    <option value="createdAt-desc">Newest First</option>
                    <option value="createdAt-asc">Oldest First</option>
                    <option value="rating-desc">Highest Rated</option>
                    <option value="downloadCount-desc">Most Popular</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading themes...</span>
        </div>
      )}

      {/* Themes Grid/List */}
      {!loading && (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        }>
          {themes.map((theme) => (
            <ThemePreview
              key={theme.id}
              theme={theme}
              onSelect={handleThemeSelect}
              onPreview={handleThemePreview}
              isSelected={selectedTheme?.id === theme.id}
              isActive={theme.isActive}
              showActions={true}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && themes.length === 0 && (
        <div className="text-center py-12">
          <div className="mx-auto h-12 w-12 text-gray-400">
            <Filter className="h-12 w-12" />
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No themes found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {activeFiltersCount > 0
              ? 'Try adjusting your search or filter criteria.'
              : `No ${type} themes are available at the moment.`}
          </p>
          {activeFiltersCount > 0 && (
            <div className="mt-6">
              <button
                onClick={clearFilters}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Clear Filters
              </button>
            </div>
          )}
        </div>
      )}

      {/* Preview Modal */}
      <ThemePreviewModal
        theme={previewTheme}
        isOpen={showPreviewModal}
        onClose={() => setShowPreviewModal(false)}
        onSelect={handleThemeSelect}
      />
    </div>
  )
}

export default ThemeSelector
