/**
 * Theme Management System Components
 * 
 * This module provides a comprehensive theme management system for the LMS
 * super admin panel with the following features:
 * 
 * - Platform landing page theme selection and management
 * - Institute marketplace theme assignment and configuration
 * - Visual theme preview with real-time preview capabilities
 * - Theme customization and configuration options
 * - Integration with institute domain management
 * - Responsive design and accessibility features
 * - State management with Zustand
 * - Toast notifications and error handling
 */

// Main Components
export { ThemeManagement } from './ThemeManagement'
export { PlatformThemeManager } from './PlatformThemeManager'
export { InstituteThemeManager } from './InstituteThemeManager'
export { ThemeSelector } from './ThemeSelector'
export { ThemePreview, ThemePreviewModal } from './ThemePreview'

// Store
export { useThemesStore } from '@/stores/super-admin/useThemesStore'

// Types
export type {
  Theme,
  PlatformTheme,
  InstituteTheme,
  ThemeCategory,
  ThemeFeature,
  ThemeCustomization,
  SupportedFeature,
  ThemeType,
  ThemeStatus,
  ThemeSortField,
  SortOrder,
  LandingPageSection,
  SectionType,
  HeroSectionConfig,
  FeaturesSectionConfig,
  AboutSectionConfig,
  ContactSectionConfig,
  EcommerceFeature,
  CourseDisplayOption,
  MarketplaceLayout,
  CheckoutProcess,
  CheckoutStep,
  LiveClassIntegration,
  ThemeFilters,
  CreateThemeRequest,
  UpdateThemeRequest,
  ApplyThemeRequest,
  ThemesResponse,
  ThemeCategoriesResponse,
  ThemesState,
  ThemePreviewProps,
  ThemeSelectorProps,
  ThemeCustomizerProps
} from '@/types/themes'

// Constants
export {
  THEME_CATEGORIES,
  SUPPORTED_FEATURES,
  DEFAULT_PAGINATION
} from '@/types/themes'

/**
 * Usage Example:
 * 
 * ```tsx
 * import { ThemeManagement } from '@/components/super-admin/themes'
 * 
 * export default function ThemesPage() {
 *   return <ThemeManagement />
 * }
 * ```
 * 
 * For individual components:
 * 
 * ```tsx
 * import { 
 *   PlatformThemeManager, 
 *   InstituteThemeManager, 
 *   useThemesStore 
 * } from '@/components/super-admin/themes'
 * 
 * export function CustomThemePage() {
 *   const { activeTab, setActiveTab } = useThemesStore()
 *   
 *   return (
 *     <div>
 *       {activeTab === 'platform' && <PlatformThemeManager />}
 *       {activeTab === 'institute' && <InstituteThemeManager />}
 *     </div>
 *   )
 * }
 * ```
 * 
 * For theme selection:
 * 
 * ```tsx
 * import { ThemeSelector, useThemesStore } from '@/components/super-admin/themes'
 * 
 * export function ThemeSelectionPage() {
 *   const { applyTheme } = useThemesStore()
 *   
 *   const handleThemeSelect = async (theme) => {
 *     await applyTheme({
 *       themeId: theme.id,
 *       targetType: 'platform'
 *     })
 *   }
 *   
 *   return (
 *     <ThemeSelector
 *       type="platform"
 *       onThemeSelect={handleThemeSelect}
 *       showFilters={true}
 *     />
 *   )
 * }
 * ```
 */
