'use client'

import { useState } from 'react'
import { useTaxStore } from '@/stores/tax/useTaxStore'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { LocationSelector } from '@/components/locations/LocationSelector'
import { Calculator, RefreshCw, Eye } from 'lucide-react'

export function TaxCalculator() {
  const [calculationInput, setCalculationInput] = useState({
    amount: '',
    transactionType: 'course_purchase',
    customerType: 'individual',
    customerLocation: {},
    instituteLocation: {}
  })

  const {
    calculationResult,
    previewResults,
    isLoading,
    calculateTax,
    previewTaxCalculation
  } = useTaxStore()

  const handleInputChange = (field: string, value: any) => {
    setCalculationInput(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleLocationChange = (type: 'customer' | 'institute', location: any) => {
    setCalculationInput(prev => ({
      ...prev,
      [`${type}Location`]: {
        countryId: location.countryId,
        stateId: location.stateId,
        districtId: location.districtId
      }
    }))
  }

  const handleCalculate = async () => {
    if (!calculationInput.amount || !calculationInput.customerLocation.countryId || !calculationInput.instituteLocation.countryId) {
      return
    }

    await calculateTax({
      amount: parseFloat(calculationInput.amount),
      transactionType: calculationInput.transactionType,
      customerType: calculationInput.customerType,
      customerLocation: calculationInput.customerLocation,
      instituteLocation: calculationInput.instituteLocation
    })
  }

  const handlePreview = async () => {
    if (!calculationInput.amount || !calculationInput.customerLocation.countryId || !calculationInput.instituteLocation.countryId) {
      return
    }

    await previewTaxCalculation({
      amount: parseFloat(calculationInput.amount),
      transactionType: calculationInput.transactionType,
      customerType: calculationInput.customerType,
      customerLocation: calculationInput.customerLocation,
      instituteLocation: calculationInput.instituteLocation
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Input Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calculator className="h-5 w-5" />
            <span>Tax Calculation Input</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Amount */}
          <div>
            <Label htmlFor="amount">Transaction Amount</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={calculationInput.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
            />
          </div>

          {/* Transaction Type */}
          <div>
            <Label>Transaction Type</Label>
            <Select
              value={calculationInput.transactionType}
              onValueChange={(value) => handleInputChange('transactionType', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="course_purchase">Course Purchase</SelectItem>
                <SelectItem value="subscription">Subscription</SelectItem>
                <SelectItem value="certification">Certification</SelectItem>
                <SelectItem value="live_class">Live Class</SelectItem>
                <SelectItem value="exam_fee">Exam Fee</SelectItem>
                <SelectItem value="material_purchase">Material Purchase</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Customer Type */}
          <div>
            <Label>Customer Type</Label>
            <Select
              value={calculationInput.customerType}
              onValueChange={(value) => handleInputChange('customerType', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="individual">Individual</SelectItem>
                <SelectItem value="business">Business</SelectItem>
                <SelectItem value="educational">Educational Institution</SelectItem>
                <SelectItem value="government">Government</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Customer Location */}
          <div>
            <Label>Customer Location</Label>
            <LocationSelector
              value={calculationInput.customerLocation}
              onChange={(location) => handleLocationChange('customer', location)}
              required={{ country: true, state: true }}
              showLabels={false}
              layout="vertical"
              size="sm"
            />
          </div>

          {/* Institute Location */}
          <div>
            <Label>Institute Location</Label>
            <LocationSelector
              value={calculationInput.instituteLocation}
              onChange={(location) => handleLocationChange('institute', location)}
              required={{ country: true, state: true }}
              showLabels={false}
              layout="vertical"
              size="sm"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button 
              onClick={handleCalculate} 
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Calculator className="h-4 w-4 mr-2" />
              )}
              Calculate Tax
            </Button>
            <Button 
              variant="outline" 
              onClick={handlePreview}
              disabled={isLoading}
            >
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <div className="space-y-6">
        {/* Tax Calculation Result */}
        {calculationResult && (
          <Card>
            <CardHeader>
              <CardTitle>Tax Calculation Result</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span className="font-medium">{formatCurrency(calculationResult.subtotal)}</span>
                </div>
                
                {calculationResult.taxComponents.map((component, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {component.code}
                      </Badge>
                      <span>{component.name} ({component.rate}%)</span>
                    </span>
                    <span>{formatCurrency(component.amount)}</span>
                  </div>
                ))}
                
                <Separator />
                
                <div className="flex justify-between">
                  <span>Total Tax:</span>
                  <span className="font-medium text-primary">{formatCurrency(calculationResult.totalTax)}</span>
                </div>
                
                <div className="flex justify-between text-lg font-bold">
                  <span>Grand Total:</span>
                  <span>{formatCurrency(calculationResult.grandTotal)}</span>
                </div>
              </div>

              {/* Tax Scenario */}
              <div className="pt-2">
                <Badge variant="secondary" className="capitalize">
                  {calculationResult.scenario.replace('_', ' ')} Transaction
                </Badge>
              </div>

              {/* Applied Rules */}
              {calculationResult.appliedRules.length > 0 && (
                <div>
                  <Label className="text-sm font-medium">Applied Rules:</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {calculationResult.appliedRules.map((rule, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {rule}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Exemptions */}
              {calculationResult.exemptions.length > 0 && (
                <div>
                  <Label className="text-sm font-medium">Exemptions:</Label>
                  <div className="space-y-1 mt-1">
                    {calculationResult.exemptions.map((exemption, index) => (
                      <div key={index} className="text-sm flex justify-between">
                        <span className="capitalize">{exemption.condition.replace('_', ' ')}</span>
                        <span className="text-green-600">
                          -{formatCurrency(exemption.exemptedAmount)} ({exemption.exemptionPercentage}%)
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Preview Results */}
        {previewResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Scenario Preview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {previewResults.map((result, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">{result.description}</h4>
                    <Badge variant="outline" className="capitalize">
                      {result.scenario.replace('_', ' ')}
                    </Badge>
                  </div>
                  
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>{formatCurrency(result.calculation.subtotal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax:</span>
                      <span className="text-primary">{formatCurrency(result.calculation.totalTax)}</span>
                    </div>
                    <div className="flex justify-between font-medium">
                      <span>Total:</span>
                      <span>{formatCurrency(result.calculation.grandTotal)}</span>
                    </div>
                  </div>

                  {result.calculation.taxComponents.length > 0 && (
                    <div className="mt-2 pt-2 border-t">
                      <div className="flex flex-wrap gap-1">
                        {result.calculation.taxComponents.map((component, compIndex) => (
                          <Badge key={compIndex} variant="secondary" className="text-xs">
                            {component.code}: {component.rate}%
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
