'use client'

import { useState } from 'react'
import { useTaxStore } from '@/stores/tax/useTaxStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Plus, Edit, Save, X, Percent } from 'lucide-react'
import { Formik, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'


const validationSchema = Yup.object({
  name: Yup.string()
    .required('Tax component name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  rate: Yup.string()
    .required('Tax rate is required')
    .test('is-number', 'Rate must be a valid number', (value) => {
      if (!value) return false
      const num = parseFloat(value)
      return !isNaN(num) && num >= 0 && num <= 100
    }),
  calculationMethod: Yup.string()
    .required('Calculation method is required')
})

interface TaxComponentFormProps {
  component?: any
  mode: 'create' | 'edit'
  trigger?: React.ReactNode
  onSuccess?: () => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function TaxComponentForm({ component, mode, trigger, onSuccess, open: externalOpen, onOpenChange }: TaxComponentFormProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const open = externalOpen !== undefined ? externalOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen
  
  const { createTaxComponent, updateTaxComponent } = useTaxStore()

  const initialValues = {
    name: component?.name || '',
    rate: component?.rate !== undefined && component?.rate !== null ? component.rate.toString() : '',
    calculationMethod: component?.calculationMethod || 'percentage',
    isActive: component?.isActive ?? true
  }

  const handleSubmit = async (values: any, { setSubmitting, resetForm }: any) => {
    try {
      const submitData = {
        name: values.name,
        code: values.name.toUpperCase().replace(/\s+/g, '_'), // Auto-generate code from name
        description: '', // Default empty description
        type: 'custom', // Default type
        rate: values.rate ? parseFloat(values.rate) : 0,
        calculationMethod: values.calculationMethod,
        isActive: values.isActive,
        effectiveFrom: new Date().toISOString(), // Default to current date
        effectiveTo: undefined, // No end date by default
        priority: 0, // Default priority
        applicableRegions: []
      }

      if (mode === 'create') {
        await createTaxComponent(submitData)
        resetForm()
      } else {
        await updateTaxComponent(component.id, submitData)
      }

      setOpen(false)
      onSuccess?.()
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const calculationMethods = [
    { value: 'percentage', label: 'Percentage' },
    { value: 'fixed', label: 'Fixed Amount' },
    { value: 'tiered', label: 'Tiered' }
  ]

  return (
    <Dialog open={open} onOpenChange={setOpen} modal={true}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            {mode === 'create' ? <Plus className="h-5 w-5" /> : <Edit className="h-5 w-5" />}
            <span>{mode === 'create' ? 'Create Tax Component' : 'Edit Tax Component'}</span>
          </DialogTitle>
        </DialogHeader>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ values, errors, touched, isSubmitting, setSubmitting, resetForm, setFieldValue }) => (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Tax Component Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="name">Tax Component Name *</Label>
                    <Field
                      as={Input}
                      id="name"
                      name="name"
                      placeholder="Enter tax component name"
                      className={errors.name && touched.name ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="name" component="div" className="text-red-500 text-sm mt-1" />
                  </div>
                </CardContent>
              </Card>

              {/* Tax Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Tax Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="rate">Tax Rate (%) *</Label>
                      <div className="relative">
                        <Field
                          as={Input}
                          id="rate"
                          name="rate"
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          placeholder="0.00"
                          className={`pr-8 ${errors.rate && touched.rate ? 'border-red-500' : ''}`}
                        />
                        <Percent className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      </div>
                      <ErrorMessage name="rate" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="calculationMethod">Calculation Method *</Label>
                      <Field name="calculationMethod">
                        {({ field }: any) => (
                          <Select value={field.value} onValueChange={(value) => setFieldValue('calculationMethod', value)}>
                            <SelectTrigger className={errors.calculationMethod && touched.calculationMethod ? 'border-red-500' : ''}>
                              <SelectValue placeholder="Select method" />
                            </SelectTrigger>
                            <SelectContent>
                              {calculationMethods.map((method) => (
                                <SelectItem key={method.value} value={method.value}>
                                  {method.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </Field>
                      <ErrorMessage name="calculationMethod" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Field name="isActive">
                      {({ field }: any) => (
                        <Switch
                          checked={field.value}
                          onCheckedChange={(checked) => setFieldValue('isActive', checked)}
                        />
                      )}
                    </Field>
                    <Label htmlFor="isActive">Active</Label>
                  </div>
                </CardContent>
              </Card>

              {/* Form Actions */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="button"
                  disabled={isSubmitting}
                  onClick={() => handleSubmit(values, { setSubmitting, resetForm })}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSubmitting ? 'Saving...' : mode === 'create' ? 'Create Component' : 'Update Component'}
                </Button>
              </div>
            </div>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  )
}
