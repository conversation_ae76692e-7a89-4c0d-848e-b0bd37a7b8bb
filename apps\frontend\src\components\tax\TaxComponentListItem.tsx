'use client'

import { useState } from 'react'
import { useTaxStore } from '@/stores/tax/useTaxStore'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Eye, Percent, DollarSign, TrendingUp } from 'lucide-react'
import { TaxComponentForm } from './TaxComponentForm'
import { toast } from 'sonner'

interface TaxComponentListItemProps {
  component: any
}

export function TaxComponentListItem({ component }: TaxComponentListItemProps) {
  const { deleteTaxComponent, fetchTaxComponents } = useTaxStore()
  const [editDialogOpen, setEditDialogOpen] = useState(false)

  const handleEdit = () => {
    setEditDialogOpen(true)
  }

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this tax component?')) {
      try {
        await deleteTaxComponent(component.id)
        toast.success('Tax component deleted successfully')
      } catch (error) {
        toast.error('Failed to delete tax component')
      }
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'sgst':
        return 'bg-blue-100 text-blue-800'
      case 'cgst':
        return 'bg-green-100 text-green-800'
      case 'igst':
        return 'bg-purple-100 text-purple-800'
      case 'vat':
        return 'bg-orange-100 text-orange-800'
      case 'sales_tax':
        return 'bg-yellow-100 text-yellow-800'
      case 'income_tax':
        return 'bg-red-100 text-red-800'
      case 'service_tax':
        return 'bg-indigo-100 text-indigo-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (calculationMethod: string) => {
    switch (calculationMethod) {
      case 'percentage':
        return Percent
      case 'fixed':
        return DollarSign
      case 'tiered':
        return TrendingUp
      default:
        return Percent
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'sgst':
        return 'SGST'
      case 'cgst':
        return 'CGST'
      case 'igst':
        return 'IGST'
      case 'vat':
        return 'VAT'
      case 'sales_tax':
        return 'Sales Tax'
      case 'income_tax':
        return 'Income Tax'
      case 'service_tax':
        return 'Service Tax'
      case 'custom':
        return 'Custom'
      default:
        return type.toUpperCase()
    }
  }

  const MethodIcon = getTypeIcon(component.calculationMethod)

  return (
    <>
    <Card className="hover:shadow-sm transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Component Info */}
          <div className="flex items-center space-x-4 flex-1">
            {/* Icon and Name */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <MethodIcon className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold">{component.name}</h3>
                <div className="flex items-center space-x-2">
                  <p className="text-sm text-gray-500 font-mono">{component.code}</p>
                  <Badge 
                    variant="secondary" 
                    className={`text-xs ${getTypeColor(component.type)}`}
                  >
                    {getTypeLabel(component.type)}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Rate and Method */}
            <div className="hidden md:flex items-center space-x-6">
              <div className="text-center">
                <div className="text-lg font-bold text-primary">{component.rate}%</div>
                <div className="text-xs text-gray-500 capitalize">{component.calculationMethod}</div>
              </div>
            </div>

            {/* Description */}
            <div className="hidden lg:block flex-1 max-w-md">
              {component.description && (
                <p className="text-sm text-gray-600 line-clamp-2">
                  {component.description}
                </p>
              )}
            </div>

            {/* Regions */}
            <div className="hidden xl:block">
              {component.applicableRegions && component.applicableRegions.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {component.applicableRegions.slice(0, 2).map((region: any, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {typeof region.country === 'object' ? region.country.name : 'Country'}
                    </Badge>
                  ))}
                  {component.applicableRegions.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{component.applicableRegions.length - 2}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Status and Actions */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Badge variant={component.isActive ? 'default' : 'secondary'}>
                {component.isActive ? 'Active' : 'Inactive'}
              </Badge>
              {component.priority > 0 && (
                <Badge variant="outline" className="text-xs hidden sm:inline-flex">
                  P: {component.priority}
                </Badge>
              )}
            </div>

            <div className="hidden sm:block text-xs text-gray-500">
              {new Date(component.effectiveFrom).toLocaleDateString()}
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" type="button">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem className="text-destructive" onClick={handleDelete}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Mobile Details */}
        <div className="md:hidden mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-center">
                <div className="text-lg font-bold text-primary">{component.rate}%</div>
                <div className="text-xs text-gray-500 capitalize">{component.calculationMethod}</div>
              </div>
              
              <div className="text-xs text-gray-500">
                Effective: {new Date(component.effectiveFrom).toLocaleDateString()}
              </div>
            </div>

            {component.priority > 0 && (
              <Badge variant="outline" className="text-xs">
                Priority: {component.priority}
              </Badge>
            )}
          </div>

          {component.description && (
            <p className="text-sm text-gray-600 mt-2 line-clamp-2">
              {component.description}
            </p>
          )}

          {component.applicableRegions && component.applicableRegions.length > 0 && (
            <div className="mt-2">
              <span className="text-xs text-gray-500">Regions: </span>
              <div className="flex flex-wrap gap-1 mt-1">
                {component.applicableRegions.slice(0, 3).map((region: any, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {typeof region.country === 'object' ? region.country.name : 'Country'}
                  </Badge>
                ))}
                {component.applicableRegions.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{component.applicableRegions.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>

    {/* Edit Dialog */}
    <TaxComponentForm
      mode="edit"
      component={component}
      open={editDialogOpen}
      onOpenChange={setEditDialogOpen}
      onSuccess={() => {
        setEditDialogOpen(false)
        // Refresh the data after successful edit
        setTimeout(() => fetchTaxComponents(), 100)
      }}
      trigger={<div style={{ display: 'none' }} />}
    />
    </>
  )
}
