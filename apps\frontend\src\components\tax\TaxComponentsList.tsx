'use client'

import { useState } from 'react'
import { useTaxStore } from '@/stores/tax/useTaxStore'
import { TaxComponentCard } from './TaxComponentCard'
import { TaxComponentListItem } from './TaxComponentListItem'
import { TaxComponentForm } from './TaxComponentForm'
import { TaxPagination } from './TaxPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Settings } from 'lucide-react'

export function TaxComponentsList() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const {
    taxComponents,
    viewMode,
    componentsPagination,
    isLoading,
    fetchTaxComponents
  } = useTaxStore()

  const handlePageChange = (page: number) => {
    fetchTaxComponents(page)
  }

  if (isLoading && taxComponents.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (taxComponents.length === 0) {
    return (
      <>
        <EmptyState
          icon={Settings}
          title="No tax components found"
          description="No tax components match your current filters. Try adjusting your search criteria or create a new tax component."
          action={{
            label: "Create Tax Component",
            onClick: () => setCreateDialogOpen(true)
          }}
        />

        {/* Create Dialog */}
        <TaxComponentForm
          mode="create"
          open={createDialogOpen}
          onOpenChange={setCreateDialogOpen}
          onSuccess={() => {
            setCreateDialogOpen(false)
            // Let the parent component handle the refresh
          }}
          trigger={<div style={{ display: 'none' }} />}
        />
      </>
    )
  }

  return (
    <div className="space-y-6">
      {/* Components Grid/List */}
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {taxComponents.map((component) => (
            <TaxComponentCard
              key={component.id}
              component={component}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {taxComponents.map((component) => (
            <TaxComponentListItem
              key={component.id}
              component={component}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      <TaxPagination
        pagination={componentsPagination}
        onPageChange={handlePageChange}
      />

      {/* Create Dialog */}
      <TaxComponentForm
        mode="create"
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={() => {
          setCreateDialogOpen(false)
          // Let the parent handle the refresh
        }}
        trigger={<div style={{ display: 'none' }} />}
      />
    </div>
  )
}
