'use client'

import { useState, useCallback } from 'react'
import { useTaxStore } from '@/stores/tax/useTaxStore'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Search, Filter, X, LayoutGrid, List } from 'lucide-react'
import { debounce } from '@/utils/debounce'

interface TaxFiltersProps {
  type: 'components' | 'groups' | 'rules'
}

export function TaxFilters({ type }: TaxFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const {
    filters,
    viewMode,
    setFilters,
    setViewMode,
    resetFilters
  } = useTaxStore()





  // Debounced search function - only update filters, don't call APIs
  const debouncedSearch = useCallback(
    debounce((searchValue: string) => {
      setFilters({ search: searchValue })
      // Don't call API here - let parent handle it
    }, 300),
    [] // No dependencies to prevent recreation
  )

  const handleSearchChange = (value: string) => {
    debouncedSearch(value)
  }

  const handleStatusChange = (value: string) => {
    setFilters({ isActive: value as 'all' | 'true' | 'false' })
    // Don't call API here - let parent handle it
  }

  const handleTypeChange = (value: string) => {
    // Convert "all" to empty string for filtering logic
    const filterValue = value === 'all' ? '' : value
    setFilters({ type: filterValue })
    // Don't call API here - let parent handle it
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.isActive !== 'true') count++
    if (filters.type) count++
    return count
  }

  const getPlaceholderText = () => {
    switch (type) {
      case 'components':
        return 'Search tax components by name, code, or type...'
      case 'groups':
        return 'Search tax groups by name, code, or description...'
      case 'rules':
        return 'Search tax rules by name or description...'
    }
  }

  const getTypeOptions = () => {
    switch (type) {
      case 'components':
        return [
          { label: 'All Types', value: 'all' },
          { label: 'SGST (State GST)', value: 'sgst' },
          { label: 'CGST (Central GST)', value: 'cgst' },
          { label: 'IGST (Integrated GST)', value: 'igst' },
          { label: 'VAT (Value Added Tax)', value: 'vat' },
          { label: 'Sales Tax', value: 'sales_tax' },
          { label: 'Income Tax', value: 'income_tax' },
          { label: 'Service Tax', value: 'service_tax' },
          { label: 'Custom Tax', value: 'custom' },
        ]
      case 'groups':
        return [
          { label: 'All Scenarios', value: 'all' },
          { label: 'Intra-State', value: 'intra_state' },
          { label: 'Inter-State', value: 'inter_state' },
          { label: 'International', value: 'international' },
          { label: 'B2B Transaction', value: 'b2b' },
          { label: 'B2C Transaction', value: 'b2c' },
        ]
      case 'rules':
        return [
          { label: 'All Rule Types', value: 'all' },
          { label: 'Location Based', value: 'location' },
          { label: 'Transaction Type', value: 'transaction' },
          { label: 'Customer Type', value: 'customer' },
          { label: 'Amount Based', value: 'amount' },
        ]
      default:
        return []
    }
  }

  return (
    <div className="space-y-4">
      {/* Basic Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="flex flex-1 gap-4 items-center">
          {/* Search */}
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={getPlaceholderText()}
              defaultValue={filters.search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Status Filter */}
          <Select value={filters.isActive} onValueChange={handleStatusChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="true">Active</SelectItem>
              <SelectItem value="false">Inactive</SelectItem>
            </SelectContent>
          </Select>

          {/* Type Filter */}
          <Select value={filters.type || 'all'} onValueChange={handleTypeChange}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              {getTypeOptions().map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Advanced Filters Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="relative"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {getActiveFiltersCount() > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
              >
                {getActiveFiltersCount()}
              </Badge>
            )}
          </Button>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'card' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('card')}
          >
            <LayoutGrid className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="p-4 border rounded-lg bg-muted/50 space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Advanced Filters</h4>
            <Button variant="ghost" size="sm" onClick={resetFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {type === 'components' && (
              <>
                <div>
                  <label className="text-sm font-medium mb-2 block">Calculation Method</label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Methods</SelectItem>
                      <SelectItem value="percentage">Percentage</SelectItem>
                      <SelectItem value="fixed">Fixed Amount</SelectItem>
                      <SelectItem value="tiered">Tiered</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Rate Range</label>
                  <div className="flex space-x-2">
                    <Input placeholder="Min %" type="number" />
                    <Input placeholder="Max %" type="number" />
                  </div>
                </div>
              </>
            )}

            {type === 'groups' && (
              <>
                <div>
                  <label className="text-sm font-medium mb-2 block">Total Rate Range</label>
                  <div className="flex space-x-2">
                    <Input placeholder="Min %" type="number" />
                    <Input placeholder="Max %" type="number" />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Components Count</label>
                  <div className="flex space-x-2">
                    <Input placeholder="Min" type="number" />
                    <Input placeholder="Max" type="number" />
                  </div>
                </div>
              </>
            )}

            {type === 'rules' && (
              <>
                <div>
                  <label className="text-sm font-medium mb-2 block">Priority Range</label>
                  <div className="flex space-x-2">
                    <Input placeholder="Min" type="number" />
                    <Input placeholder="Max" type="number" />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Has Exemptions</label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Rules</SelectItem>
                      <SelectItem value="true">With Exemptions</SelectItem>
                      <SelectItem value="false">Without Exemptions</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}

            <div>
              <label className="text-sm font-medium mb-2 block">Date Range</label>
              <div className="flex space-x-2">
                <Input type="date" />
                <Input type="date" />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
