'use client'

import { useState, useEffect } from 'react'
import { useTaxStore } from '@/stores/tax/useTaxStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Save, X, Users, Trash2, Calculator } from 'lucide-react'
import { Formik, Field, ErrorMessage, FieldArray } from 'formik'
import * as Yup from 'yup'
import { toast } from 'sonner'

const validationSchema = Yup.object({
  name: Yup.string()
    .required('Tax group name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  code: Yup.string()
    .required('Tax group code is required')
    .matches(/^[A-Z0-9_]+$/, 'Code must contain only uppercase letters, numbers, and underscores')
    .min(2, 'Code must be at least 2 characters')
    .max(20, 'Code must be less than 20 characters'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  taxComponents: Yup.array()
    .min(1, 'At least one tax component is required')
    .of(
      Yup.object({
        component: Yup.string().required('Tax component is required'),
        customRate: Yup.string().test('is-valid-rate', 'Rate must be a valid number between 0 and 100', (value) => {
          if (!value || value.trim() === '') return true // Optional field
          const num = parseFloat(value)
          return !isNaN(num) && num >= 0 && num <= 100
        }),
        isOptional: Yup.boolean()
      })
    ),
  priority: Yup.number()
    .required('Priority is required')
    .min(0, 'Priority must be positive'),
  effectiveFrom: Yup.date()
    .required('Effective from date is required'),
  effectiveTo: Yup.date()
    .nullable()
    .min(Yup.ref('effectiveFrom'), 'Effective to date must be after effective from date')
})

interface TaxGroupFormProps {
  group?: any
  mode: 'create' | 'edit'
  trigger?: React.ReactNode
  onSuccess?: () => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function TaxGroupForm({ group, mode, trigger, onSuccess, open: externalOpen, onOpenChange }: TaxGroupFormProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const open = externalOpen !== undefined ? externalOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen
  
  const { taxComponents, createTaxGroup, updateTaxGroup, fetchTaxComponents } = useTaxStore()

  useEffect(() => {
    // Only fetch data when the form is actually opened to prevent unnecessary API calls
    if (open && taxComponents.length === 0) {
      fetchTaxComponents()
    }
  }, [open])

  const initialValues = {
    name: group?.name || '',
    code: group?.code || '',
    description: group?.description || '',
    taxComponents: group?.taxComponents?.map((tc: any) => ({
      component: typeof tc.component === 'string' ? tc.component : tc.component.id,
      customRate: tc.customRate !== undefined && tc.customRate !== null ? tc.customRate.toString() : '',
      isOptional: tc.isOptional || false
    })) || [{ component: '', customRate: '', isOptional: false }],
    isActive: group?.isActive ?? true,
    isDefault: group?.isDefault ?? false,
    effectiveFrom: group?.effectiveFrom ? new Date(group.effectiveFrom).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    effectiveTo: group?.effectiveTo ? new Date(group.effectiveTo).toISOString().split('T')[0] : '',
    priority: group?.priority || 0
  }

  const handleSubmit = async (values: any, { setSubmitting, resetForm }: any) => {
    try {
      const submitData = {
        name: values.name,
        code: values.code.toUpperCase(),
        description: values.description,
        taxComponents: values.taxComponents.filter((tc: any) => tc.component).map((tc: any) => ({
          component: tc.component,
          customRate: tc.customRate && tc.customRate.trim() !== '' ? parseFloat(tc.customRate) : null,
          isOptional: tc.isOptional
        })),
        isActive: values.isActive,
        isDefault: values.isDefault,
        effectiveFrom: new Date(values.effectiveFrom).toISOString(),
        effectiveTo: values.effectiveTo ? new Date(values.effectiveTo).toISOString() : null,
        priority: parseInt(values.priority),
        applicableScenarios: [] // Will be configured separately
      }

      if (mode === 'create') {
        await createTaxGroup(submitData)
        resetForm()
      } else {
        await updateTaxGroup(group.id, submitData)
      }

      setOpen(false)
      onSuccess?.()
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const calculateTotalRate = (taxComponentsArray: any[]) => {
    return taxComponentsArray.reduce((total, tc) => {
      if (!tc.component) return total
      
      const component = taxComponents.find(comp => comp.id === tc.component)
      if (!component) return total
      
      const rate = tc.customRate ? parseFloat(tc.customRate) : component.rate
      return total + (rate || 0)
    }, 0)
  }

  const scenarios = [
    { value: 'intra_state', label: 'Intra-State (Same State)' },
    { value: 'inter_state', label: 'Inter-State (Different States)' },
    { value: 'international', label: 'International' },
    { value: 'b2b', label: 'B2B Transaction' },
    { value: 'b2c', label: 'B2C Transaction' },
    { value: 'export', label: 'Export' },
    { value: 'import', label: 'Import' }
  ]

  return (
    <Dialog open={open} onOpenChange={setOpen} modal={true}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            {mode === 'create' ? <Plus className="h-5 w-5" /> : <Edit className="h-5 w-5" />}
            <span>{mode === 'create' ? 'Create Tax Group' : 'Edit Tax Group'}</span>
          </DialogTitle>
        </DialogHeader>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ values, errors, touched, handleChange, isSubmitting, setSubmitting, resetForm, setFieldValue }) => (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Tax Group Name *</Label>
                      <Field
                        as={Input}
                        id="name"
                        name="name"
                        placeholder="Enter tax group name"
                        className={errors.name && touched.name ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="name" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="code">Tax Group Code *</Label>
                      <Field
                        as={Input}
                        id="code"
                        name="code"
                        placeholder="Enter tax group code (e.g., GST_18)"
                        className={errors.code && touched.code ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="code" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Field
                      as={Textarea}
                      id="description"
                      name="description"
                      placeholder="Enter tax group description"
                      rows={3}
                      className={errors.description && touched.description ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="description" component="div" className="text-red-500 text-sm mt-1" />
                  </div>
                </CardContent>
              </Card>

              {/* Tax Components */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center justify-between">
                    <span>Tax Components</span>
                    <div className="flex items-center space-x-2">
                      <Calculator className="h-4 w-4" />
                      <span className="text-sm font-normal">
                        Total Rate: {calculateTotalRate(values.taxComponents).toFixed(2)}%
                      </span>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <FieldArray name="taxComponents">
                    {({ push, remove }) => (
                      <div className="space-y-4">
                        {values.taxComponents.map((_, index) => (
                          <div key={index} className="border rounded-lg p-4 space-y-4">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium">Component {index + 1}</h4>
                              {values.taxComponents.length > 1 && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => remove(index)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>

                            <div className="grid grid-cols-3 gap-4">
                              <div>
                                <Label>Tax Component *</Label>
                                <Field name={`taxComponents.${index}.component`}>
                                  {({ field }: any) => (
                                    <Select 
                                      value={field.value} 
                                      onValueChange={(value) => setFieldValue(`taxComponents.${index}.component`, value)}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select component" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {taxComponents.map((component) => (
                                          <SelectItem key={component.id} value={component.id}>
                                            {component.name} ({component.code}) - {component.rate}%
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  )}
                                </Field>
                              </div>
                              <div>
                                <Label>Custom Rate (%)</Label>
                                <Field
                                  as={Input}
                                  name={`taxComponents.${index}.customRate`}
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  max="100"
                                  placeholder="Override rate"
                                />
                              </div>
                              <div className="flex items-end">
                                <div className="flex items-center space-x-2">
                                  <Field name={`taxComponents.${index}.isOptional`}>
                                    {({ field }: any) => (
                                      <Switch
                                        checked={field.value}
                                        onCheckedChange={(checked) => setFieldValue(`taxComponents.${index}.isOptional`, checked)}
                                      />
                                    )}
                                  </Field>
                                  <Label>Optional</Label>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}

                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => push({ component: '', customRate: '', isOptional: false })}
                          className="w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Tax Component
                        </Button>
                      </div>
                    )}
                  </FieldArray>
                  <ErrorMessage name="taxComponents" component="div" className="text-red-500 text-sm mt-1" />
                </CardContent>
              </Card>

              {/* Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="effectiveFrom">Effective From *</Label>
                      <Field
                        as={Input}
                        id="effectiveFrom"
                        name="effectiveFrom"
                        type="date"
                        className={errors.effectiveFrom && touched.effectiveFrom ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="effectiveFrom" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="effectiveTo">Effective To</Label>
                      <Field
                        as={Input}
                        id="effectiveTo"
                        name="effectiveTo"
                        type="date"
                        className={errors.effectiveTo && touched.effectiveTo ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="effectiveTo" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="priority">Priority *</Label>
                      <Field
                        as={Input}
                        id="priority"
                        name="priority"
                        type="number"
                        min="0"
                        placeholder="0"
                        className={errors.priority && touched.priority ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="priority" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </div>

                  <div className="flex items-center space-x-6">
                    <div className="flex items-center space-x-2">
                      <Field name="isActive">
                        {({ field }: any) => (
                          <Switch
                            checked={field.value}
                            onCheckedChange={(checked) => setFieldValue('isActive', checked)}
                          />
                        )}
                      </Field>
                      <Label>Active</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Field name="isDefault">
                        {({ field }: any) => (
                          <Switch
                            checked={field.value}
                            onCheckedChange={(checked) => setFieldValue('isDefault', checked)}
                          />
                        )}
                      </Field>
                      <Label>Default Group</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Form Actions */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="button"
                  disabled={isSubmitting}
                  onClick={() => handleSubmit(values, { setSubmitting, resetForm })}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSubmitting ? 'Saving...' : mode === 'create' ? 'Create Group' : 'Update Group'}
                </Button>
              </div>
            </div>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  )
}
