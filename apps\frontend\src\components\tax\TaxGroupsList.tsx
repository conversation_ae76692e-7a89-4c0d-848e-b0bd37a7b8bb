'use client'

import { useState } from 'react'
import { useTaxStore } from '@/stores/tax/useTaxStore'
import { TaxGroupForm } from './TaxGroupForm'
import { TaxPagination } from './TaxPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Users, MoreHorizontal, Edit, Trash2, Eye } from 'lucide-react'
import { toast } from 'sonner'

export function TaxGroupsList() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedGroup, setSelectedGroup] = useState<any>(null)
  const {
    taxGroups,
    viewMode,
    groupsPagination,
    isLoading,
    fetchTaxGroups,
    deleteTaxGroup
  } = useTaxStore()

  const handlePageChange = (page: number) => {
    fetchTaxGroups(page)
  }

  const handleEdit = (group: any) => {
    setSelectedGroup(group)
    setEditDialogOpen(true)
  }

  const handleDelete = async (groupId: string) => {
    if (window.confirm('Are you sure you want to delete this tax group?')) {
      try {
        await deleteTaxGroup(groupId)
        toast.success('Tax group deleted successfully')
      } catch (error) {
        toast.error('Failed to delete tax group')
      }
    }
  }

  if (isLoading && taxGroups.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (taxGroups.length === 0) {
    return (
      <>
        <EmptyState
          icon={Users}
          title="No tax groups found"
          description="No tax groups match your current filters. Try adjusting your search criteria or create a new tax group."
          action={{
            label: "Create Tax Group",
            onClick: () => setCreateDialogOpen(true)
          }}
        />

        {/* Create Dialog */}
        <TaxGroupForm
          mode="create"
          open={createDialogOpen}
          onOpenChange={setCreateDialogOpen}
          onSuccess={() => {
            fetchTaxGroups()
            setCreateDialogOpen(false)
          }}
          trigger={<div style={{ display: 'none' }} />}
        />
      </>
    )
  }

  return (
    <div className="space-y-6">
      {/* Groups Grid/List */}
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {taxGroups.map((group) => (
            <Card key={group.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h3 className="font-semibold">{group.name}</h3>
                    <p className="text-sm text-gray-500 font-mono">{group.code}</p>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEdit(group)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-destructive" onClick={() => handleDelete(group.id)}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Total Rate:</span>
                    <span className="font-bold text-primary">{group.totalRate}%</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Components:</span>
                    <span className="text-sm">{group.taxComponents?.length || 0}</span>
                  </div>

                  {group.description && (
                    <p className="text-sm text-gray-600 line-clamp-2 mt-2">
                      {group.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between pt-2">
                    <Badge variant={group.isActive ? 'default' : 'secondary'}>
                      {group.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                    {group.isDefault && (
                      <Badge variant="outline" className="text-xs">
                        Default
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {taxGroups.map((group) => (
            <Card key={group.id} className="hover:shadow-sm transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <Users className="h-5 w-5 text-white" />
                    </div>
                    
                    <div>
                      <h3 className="font-semibold">{group.name}</h3>
                      <p className="text-sm text-gray-500 font-mono">{group.code}</p>
                    </div>

                    <div className="hidden md:flex items-center space-x-6">
                      <div className="text-center">
                        <div className="text-lg font-bold text-primary">{group.totalRate}%</div>
                        <div className="text-xs text-gray-500">Total Rate</div>
                      </div>
                      
                      <div className="text-center">
                        <div className="text-lg font-bold">{group.taxComponents?.length || 0}</div>
                        <div className="text-xs text-gray-500">Components</div>
                      </div>
                    </div>

                    {group.description && (
                      <div className="hidden lg:block flex-1 max-w-md">
                        <p className="text-sm text-gray-600 line-clamp-1">
                          {group.description}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <Badge variant={group.isActive ? 'default' : 'secondary'}>
                        {group.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      {group.isDefault && (
                        <Badge variant="outline" className="text-xs">
                          Default
                        </Badge>
                      )}
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(group)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive" onClick={() => handleDelete(group.id)}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Mobile Details */}
                <div className="md:hidden mt-3 pt-3 border-t border-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-primary">{group.totalRate}%</div>
                        <div className="text-xs text-gray-500">Total Rate</div>
                      </div>
                      
                      <div className="text-center">
                        <div className="text-lg font-bold">{group.taxComponents?.length || 0}</div>
                        <div className="text-xs text-gray-500">Components</div>
                      </div>
                    </div>
                  </div>

                  {group.description && (
                    <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                      {group.description}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      <TaxPagination
        pagination={groupsPagination}
        onPageChange={handlePageChange}
      />

      {/* Create Dialog */}
      <TaxGroupForm
        mode="create"
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={() => {
          setCreateDialogOpen(false)
          // Let the parent handle the refresh
        }}
        trigger={<div style={{ display: 'none' }} />}
      />

      {/* Edit Dialog */}
      {selectedGroup && (
        <TaxGroupForm
          mode="edit"
          group={selectedGroup}
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          onSuccess={() => {
            setEditDialogOpen(false)
            setSelectedGroup(null)
            // Refresh the data
            fetchTaxGroups()
          }}
          trigger={<div style={{ display: 'none' }} />}
        />
      )}
    </div>
  )
}
