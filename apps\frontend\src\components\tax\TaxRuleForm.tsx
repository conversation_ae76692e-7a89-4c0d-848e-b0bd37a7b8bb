'use client'

import { useState, useEffect } from 'react'
import { useTaxStore } from '@/stores/tax/useTaxStore'
import { useLocationStore } from '@/stores/location/useLocationStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Plus, Edit, Save, X, FileText, MapPin, Users, DollarSign } from 'lucide-react'
import { Formik, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { toast } from 'sonner'

const validationSchema = Yup.object({
  name: Yup.string()
    .required('Tax rule name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  ruleType: Yup.string()
    .required('Rule type is required'),
  taxGroup: Yup.string()
    .required('Tax group is required'),
  priority: Yup.number()
    .required('Priority is required')
    .min(0, 'Priority must be positive'),
  effectiveFrom: Yup.date()
    .required('Effective from date is required'),
  effectiveTo: Yup.date()
    .nullable()
    .min(Yup.ref('effectiveFrom'), 'Effective to date must be after effective from date')
})

interface TaxRuleFormProps {
  rule?: any
  mode: 'create' | 'edit'
  trigger?: React.ReactNode
  onSuccess?: () => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function TaxRuleForm({ rule, mode, trigger, onSuccess, open: externalOpen, onOpenChange }: TaxRuleFormProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const open = externalOpen !== undefined ? externalOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen
  
  const { taxGroups, createTaxRule, updateTaxRule, fetchTaxGroups } = useTaxStore()
  const { countries, states, fetchCountries, fetchStates } = useLocationStore()

  useEffect(() => {
    // Only fetch data when the form is actually opened to prevent unnecessary API calls
    if (open) {
      if (taxGroups.length === 0) {
        fetchTaxGroups()
      }
      if (countries.length === 0) {
        fetchCountries()
      }
    }
  }, [open])

  const initialValues = {
    name: rule?.name || '',
    description: rule?.description || '',
    ruleType: rule?.ruleType || 'location',
    taxGroup: typeof rule?.taxGroup === 'string' ? rule.taxGroup : rule?.taxGroup?.id || '',
    
    // Location conditions
    fromCountry: rule?.conditions?.fromCountry || '',
    fromState: rule?.conditions?.fromState || '',
    toCountry: rule?.conditions?.toCountry || '',
    toState: rule?.conditions?.toState || '',
    isSameState: rule?.conditions?.isSameState || false,
    isSameCountry: rule?.conditions?.isSameCountry || false,
    
    // Transaction conditions
    transactionType: rule?.conditions?.transactionType || '',
    
    // Entity conditions
    entityType: rule?.conditions?.entityType || '',
    
    // Amount conditions
    minAmount: rule?.conditions?.minAmount || '',
    maxAmount: rule?.conditions?.maxAmount || '',
    
    isActive: rule?.isActive ?? true,
    priority: rule?.priority || 0,
    effectiveFrom: rule?.effectiveFrom ? new Date(rule.effectiveFrom).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    effectiveTo: rule?.effectiveTo ? new Date(rule.effectiveTo).toISOString().split('T')[0] : ''
  }

  const handleSubmit = async (values: any, { setSubmitting, resetForm }: any) => {
    try {
      const conditions: any = {}
      
      // Build conditions based on rule type
      switch (values.ruleType) {
        case 'location':
          if (values.fromCountry) conditions.fromCountry = values.fromCountry
          if (values.fromState) conditions.fromState = values.fromState
          if (values.toCountry) conditions.toCountry = values.toCountry
          if (values.toState) conditions.toState = values.toState
          conditions.isSameState = values.isSameState
          conditions.isSameCountry = values.isSameCountry
          break
        case 'transaction':
          if (values.transactionType) conditions.transactionType = values.transactionType
          break
        case 'entity':
          if (values.entityType) conditions.entityType = values.entityType
          break
        case 'amount':
          if (values.minAmount) conditions.minAmount = parseFloat(values.minAmount)
          if (values.maxAmount) conditions.maxAmount = parseFloat(values.maxAmount)
          break
      }

      const submitData = {
        name: values.name,
        description: values.description,
        ruleType: values.ruleType,
        conditions,
        taxGroup: values.taxGroup,
        isActive: values.isActive,
        priority: parseInt(values.priority),
        effectiveFrom: new Date(values.effectiveFrom).toISOString(),
        effectiveTo: values.effectiveTo ? new Date(values.effectiveTo).toISOString() : null,
        exemptions: [] // Will be configured separately
      }

      if (mode === 'create') {
        await createTaxRule(submitData)
        resetForm()
      } else {
        await updateTaxRule(rule.id, submitData)
      }

      setOpen(false)
      onSuccess?.()
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const ruleTypes = [
    { value: 'location', label: 'Location Based', icon: MapPin },
    { value: 'transaction', label: 'Transaction Type', icon: FileText },
    { value: 'entity', label: 'Entity Type', icon: Users },
    { value: 'amount', label: 'Amount Based', icon: DollarSign }
  ]

  const transactionTypes = [
    { value: 'branch_bill', label: 'Branch Monthly Bill' },
    { value: 'course_purchase', label: 'Course Purchase' },
    { value: 'subscription', label: 'Subscription Payment' },
    { value: 'platform_fee', label: 'Platform Fee' },
    { value: 'refund', label: 'Refund' }
  ]

  const entityTypes = [
    { value: 'b2b', label: 'B2B (Business to Business)' },
    { value: 'b2c', label: 'B2C (Business to Consumer)' },
    { value: 'individual', label: 'Individual' },
    { value: 'corporate', label: 'Corporate' }
  ]

  return (
    <Dialog open={open} onOpenChange={setOpen} modal={true}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            {mode === 'create' ? <Plus className="h-5 w-5" /> : <Edit className="h-5 w-5" />}
            <span>{mode === 'create' ? 'Create Tax Rule' : 'Edit Tax Rule'}</span>
          </DialogTitle>
        </DialogHeader>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ values, errors, touched, handleChange, isSubmitting, setSubmitting, resetForm, setFieldValue }) => (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="name">Tax Rule Name *</Label>
                    <Field
                      as={Input}
                      id="name"
                      name="name"
                      placeholder="Enter tax rule name"
                      className={errors.name && touched.name ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="name" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Field
                      as={Textarea}
                      id="description"
                      name="description"
                      placeholder="Enter tax rule description"
                      rows={3}
                      className={errors.description && touched.description ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="description" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="ruleType">Rule Type *</Label>
                      <Field name="ruleType">
                        {({ field }: any) => (
                          <Select value={field.value} onValueChange={(value) => setFieldValue('ruleType', value)}>
                            <SelectTrigger className={errors.ruleType && touched.ruleType ? 'border-red-500' : ''}>
                              <SelectValue placeholder="Select rule type" />
                            </SelectTrigger>
                            <SelectContent>
                              {ruleTypes.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  <div className="flex items-center space-x-2">
                                    <type.icon className="h-4 w-4" />
                                    <span>{type.label}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </Field>
                      <ErrorMessage name="ruleType" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="taxGroup">Tax Group *</Label>
                      <Field name="taxGroup">
                        {({ field }: any) => (
                          <Select value={field.value} onValueChange={(value) => setFieldValue('taxGroup', value)}>
                            <SelectTrigger className={errors.taxGroup && touched.taxGroup ? 'border-red-500' : ''}>
                              <SelectValue placeholder="Select tax group" />
                            </SelectTrigger>
                            <SelectContent>
                              {taxGroups.map((group) => (
                                <SelectItem key={group.id} value={group.id}>
                                  {group.name} ({group.code}) - {group.totalRate}%
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </Field>
                      <ErrorMessage name="taxGroup" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Rule Conditions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Rule Conditions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Location Based Conditions */}
                  {values.ruleType === 'location' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>From Country</Label>
                          <Field name="fromCountry">
                            {({ field }: any) => (
                              <Select value={field.value} onValueChange={(value) => setFieldValue('fromCountry', value)}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select from country" />
                                </SelectTrigger>
                                <SelectContent>
                                  {countries.map((country) => (
                                    <SelectItem key={country.id} value={country.id}>
                                      {country.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )}
                          </Field>
                        </div>
                        <div>
                          <Label>To Country</Label>
                          <Field name="toCountry">
                            {({ field }: any) => (
                              <Select value={field.value} onValueChange={(value) => setFieldValue('toCountry', value)}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select to country" />
                                </SelectTrigger>
                                <SelectContent>
                                  {countries.map((country) => (
                                    <SelectItem key={country.id} value={country.id}>
                                      {country.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )}
                          </Field>
                        </div>
                      </div>

                      <div className="flex items-center space-x-6">
                        <div className="flex items-center space-x-2">
                          <Field name="isSameState">
                            {({ field }: any) => (
                              <Switch
                                checked={field.value}
                                onCheckedChange={(checked) => setFieldValue('isSameState', checked)}
                              />
                            )}
                          </Field>
                          <Label>Same State Transaction</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Field name="isSameCountry">
                            {({ field }: any) => (
                              <Switch
                                checked={field.value}
                                onCheckedChange={(checked) => setFieldValue('isSameCountry', checked)}
                              />
                            )}
                          </Field>
                          <Label>Same Country Transaction</Label>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Transaction Type Conditions */}
                  {values.ruleType === 'transaction' && (
                    <div>
                      <Label>Transaction Type</Label>
                      <Field name="transactionType">
                        {({ field }: any) => (
                          <Select value={field.value} onValueChange={(value) => setFieldValue('transactionType', value)}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select transaction type" />
                            </SelectTrigger>
                            <SelectContent>
                              {transactionTypes.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </Field>
                    </div>
                  )}

                  {/* Entity Type Conditions */}
                  {values.ruleType === 'entity' && (
                    <div>
                      <Label>Entity Type</Label>
                      <Field name="entityType">
                        {({ field }: any) => (
                          <Select value={field.value} onValueChange={(value) => setFieldValue('entityType', value)}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select entity type" />
                            </SelectTrigger>
                            <SelectContent>
                              {entityTypes.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </Field>
                    </div>
                  )}

                  {/* Amount Based Conditions */}
                  {values.ruleType === 'amount' && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Minimum Amount</Label>
                        <Field
                          as={Input}
                          name="minAmount"
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                        />
                      </div>
                      <div>
                        <Label>Maximum Amount</Label>
                        <Field
                          as={Input}
                          name="maxAmount"
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="No limit"
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="effectiveFrom">Effective From *</Label>
                      <Field
                        as={Input}
                        id="effectiveFrom"
                        name="effectiveFrom"
                        type="date"
                        className={errors.effectiveFrom && touched.effectiveFrom ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="effectiveFrom" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="effectiveTo">Effective To</Label>
                      <Field
                        as={Input}
                        id="effectiveTo"
                        name="effectiveTo"
                        type="date"
                        className={errors.effectiveTo && touched.effectiveTo ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="effectiveTo" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="priority">Priority *</Label>
                      <Field
                        as={Input}
                        id="priority"
                        name="priority"
                        type="number"
                        min="0"
                        placeholder="0"
                        className={errors.priority && touched.priority ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="priority" component="div" className="text-red-500 text-sm mt-1" />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Field name="isActive">
                      {({ field }: any) => (
                        <Switch
                          checked={field.value}
                          onCheckedChange={(checked) => setFieldValue('isActive', checked)}
                        />
                      )}
                    </Field>
                    <Label>Active</Label>
                  </div>
                </CardContent>
              </Card>

              {/* Form Actions */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="button"
                  disabled={isSubmitting}
                  onClick={() => handleSubmit(values, { setSubmitting, resetForm })}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSubmitting ? 'Saving...' : mode === 'create' ? 'Create Rule' : 'Update Rule'}
                </Button>
              </div>
            </div>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  )
}
