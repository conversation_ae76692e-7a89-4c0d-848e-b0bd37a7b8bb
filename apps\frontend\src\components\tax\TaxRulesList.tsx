'use client'

import React, { useState } from 'react'
import { useTaxStore } from '@/stores/tax/useTaxStore'
import { TaxRuleForm } from './TaxRuleForm'
import { TaxPagination } from './TaxPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { FileText, MoreHorizontal, Edit, Trash2, Eye, ArrowUp, ArrowDown } from 'lucide-react'
import { toast } from 'sonner'

export function TaxRulesList() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedRule, setSelectedRule] = useState<any>(null)
  const {
    taxRules,
    viewMode,
    rulesPagination,
    isLoading,
    fetchTaxRules,
    deleteTaxRule
  } = useTaxStore()

  const handlePageChange = (page: number) => {
    fetchTaxRules(page)
  }

  const handleEdit = (rule: any) => {
    setSelectedRule(rule)
    setEditDialogOpen(true)
  }

  const handleDelete = async (ruleId: string) => {
    if (window.confirm('Are you sure you want to delete this tax rule?')) {
      try {
        await deleteTaxRule(ruleId)
        toast.success('Tax rule deleted successfully')
      } catch (error) {
        toast.error('Failed to delete tax rule')
      }
    }
  }

  const getPriorityIcon = (priority: number) => {
    if (priority > 5) return ArrowUp
    if (priority < 0) return ArrowDown
    return null
  }

  const getPriorityColor = (priority: number) => {
    if (priority > 5) return 'text-red-600'
    if (priority < 0) return 'text-blue-600'
    return 'text-gray-600'
  }

  if (isLoading && taxRules.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (taxRules.length === 0) {
    return (
      <>
        <EmptyState
          icon={FileText}
          title="No tax rules found"
          description="No tax rules match your current filters. Try adjusting your search criteria or create a new tax rule."
          action={{
            label: "Create Tax Rule",
            onClick: () => setCreateDialogOpen(true)
          }}
        />

        {/* Create Dialog */}
        <TaxRuleForm
          mode="create"
          open={createDialogOpen}
          onOpenChange={setCreateDialogOpen}
          onSuccess={() => {
            setCreateDialogOpen(false)
            // Let the parent component handle the refresh
          }}
          trigger={<div style={{ display: 'none' }} />}
        />
      </>
    )
  }

  return (
    <div className="space-y-6">
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {taxRules.map((rule) => (
            <Card key={rule.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h3 className="font-semibold">{rule.name}</h3>
                    <p className="text-sm text-gray-500">
                      {typeof rule.taxGroup === 'object' ? rule.taxGroup.name : 'Tax Group'}
                    </p>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEdit(rule)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-destructive" onClick={() => handleDelete(rule.id)}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Priority:</span>
                    <div className="flex items-center space-x-1">
                      {(() => {
                        const PriorityIcon = getPriorityIcon(rule.priority)
                        return PriorityIcon ? <PriorityIcon className={`h-3 w-3 ${getPriorityColor(rule.priority)}`} /> : null
                      })()}
                      <span className={`font-medium ${getPriorityColor(rule.priority)}`}>
                        {rule.priority}
                      </span>
                    </div>
                  </div>

                  {rule.conditions?.transactionType && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Transaction:</span>
                      <Badge variant="outline" className="text-xs capitalize">
                        {rule.conditions.transactionType.replace('_', ' ')}
                      </Badge>
                    </div>
                  )}

                  {rule.exemptions && rule.exemptions.length > 0 && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Exemptions:</span>
                      <Badge variant="secondary" className="text-xs">
                        {rule.exemptions.length}
                      </Badge>
                    </div>
                  )}

                  {rule.description && (
                    <p className="text-sm text-gray-600 line-clamp-2 mt-2">
                      {rule.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between pt-2">
                    <Badge variant={rule.isActive ? 'default' : 'secondary'}>
                      {rule.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                    <div className="text-xs text-gray-500">
                      {new Date(rule.effectiveFrom).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {taxRules.map((rule) => (
            <Card key={rule.id} className="hover:shadow-sm transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                      <FileText className="h-5 w-5 text-white" />
                    </div>
                    
                    <div>
                      <h3 className="font-semibold">{rule.name}</h3>
                      <p className="text-sm text-gray-500">
                        {typeof rule.taxGroup === 'object' ? rule.taxGroup.name : 'Tax Group'}
                      </p>
                    </div>

                    <div className="hidden md:flex items-center space-x-6">
                      <div className="text-center">
                        <div className={`text-lg font-bold flex items-center space-x-1 ${getPriorityColor(rule.priority)}`}>
                          {(() => {
                            const PriorityIcon = getPriorityIcon(rule.priority)
                            return PriorityIcon ? <PriorityIcon className="h-4 w-4" /> : null
                          })()}
                          <span>{rule.priority}</span>
                        </div>
                        <div className="text-xs text-gray-500">Priority</div>
                      </div>
                      
                      {rule.exemptions && (
                        <div className="text-center">
                          <div className="text-lg font-bold">{rule.exemptions.length}</div>
                          <div className="text-xs text-gray-500">Exemptions</div>
                        </div>
                      )}
                    </div>

                    {rule.conditions?.transactionType && (
                      <div className="hidden lg:block">
                        <Badge variant="outline" className="text-xs capitalize">
                          {rule.conditions.transactionType.replace('_', ' ')}
                        </Badge>
                      </div>
                    )}

                    {rule.description && (
                      <div className="hidden xl:block flex-1 max-w-md">
                        <p className="text-sm text-gray-600 line-clamp-1">
                          {rule.description}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-3">
                    <Badge variant={rule.isActive ? 'default' : 'secondary'}>
                      {rule.isActive ? 'Active' : 'Inactive'}
                    </Badge>

                    <div className="hidden sm:block text-xs text-gray-500">
                      {new Date(rule.effectiveFrom).toLocaleDateString()}
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(rule)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive" onClick={() => handleDelete(rule.id)}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Mobile Details */}
                <div className="md:hidden mt-3 pt-3 border-t border-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <div className={`text-lg font-bold flex items-center space-x-1 ${getPriorityColor(rule.priority)}`}>
                          {(() => {
                            const PriorityIcon = getPriorityIcon(rule.priority)
                            return PriorityIcon ? <PriorityIcon className="h-4 w-4" /> : null
                          })()}
                          <span>{rule.priority}</span>
                        </div>
                        <div className="text-xs text-gray-500">Priority</div>
                      </div>
                      
                      {rule.exemptions && (
                        <div className="text-center">
                          <div className="text-lg font-bold">{rule.exemptions.length}</div>
                          <div className="text-xs text-gray-500">Exemptions</div>
                        </div>
                      )}
                    </div>

                    <div className="text-xs text-gray-500">
                      {new Date(rule.effectiveFrom).toLocaleDateString()}
                    </div>
                  </div>

                  {rule.conditions?.transactionType && (
                    <div className="mt-2">
                      <Badge variant="outline" className="text-xs capitalize">
                        {rule.conditions.transactionType.replace('_', ' ')}
                      </Badge>
                    </div>
                  )}

                  {rule.description && (
                    <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                      {rule.description}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      <TaxPagination
        pagination={rulesPagination}
        onPageChange={handlePageChange}
      />

      {/* Create Dialog */}
      <TaxRuleForm
        mode="create"
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={() => {
          setCreateDialogOpen(false)
          // Let the parent handle the refresh
        }}
        trigger={<div style={{ display: 'none' }} />}
      />

      {/* Edit Dialog */}
      {selectedRule && (
        <TaxRuleForm
          mode="edit"
          rule={selectedRule}
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          onSuccess={() => {
            setEditDialogOpen(false)
            setSelectedRule(null)
            // Let the parent component handle the refresh
          }}
          trigger={<div style={{ display: 'none' }} />}
        />
      )}
    </div>
  )
}
