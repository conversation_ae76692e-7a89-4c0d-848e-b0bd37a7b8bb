'use client'

import React, { useState, useEffect } from 'react'
import { X, ChevronDown, ChevronUp } from 'lucide-react'

interface FilterState {
  category: string
  level: string
  language: string
  priceRange: [number, number]
  rating: number
  isFree: boolean
}

interface CourseFiltersProps {
  filters: FilterState
  onFilterChange: (filters: FilterState) => void
  onClose?: () => void
}

const categories = [
  { value: 'upsc', label: 'UPSC Preparation' },
  { value: 'banking', label: 'Banking & Finance' },
  { value: 'ssc', label: 'SSC Preparation' },
  { value: 'it_software', label: 'IT & Software' },
  { value: 'languages', label: 'Languages' },
  { value: 'business', label: 'Business & Management' },
  { value: 'design', label: 'Design & Creative' },
  { value: 'marketing', label: 'Marketing & Sales' },
  { value: 'engineering', label: 'Engineering' },
  { value: 'medical', label: 'Medical & Healthcare' },
  { value: 'law', label: 'Law & Legal Studies' },
  { value: 'other', label: 'Other' }
]

const levels = [
  { value: 'beginner', label: 'Beginner' },
  { value: 'intermediate', label: 'Intermediate' },
  { value: 'advanced', label: 'Advanced' },
  { value: 'expert', label: 'Expert' }
]

const languages = [
  { value: 'english', label: 'English' },
  { value: 'hindi', label: 'Hindi' },
  { value: 'tamil', label: 'Tamil' },
  { value: 'telugu', label: 'Telugu' },
  { value: 'bengali', label: 'Bengali' },
  { value: 'marathi', label: 'Marathi' },
  { value: 'gujarati', label: 'Gujarati' },
  { value: 'kannada', label: 'Kannada' },
  { value: 'malayalam', label: 'Malayalam' },
  { value: 'punjabi', label: 'Punjabi' }
]

const ratings = [
  { value: 4.5, label: '4.5 & above' },
  { value: 4, label: '4.0 & above' },
  { value: 3.5, label: '3.5 & above' },
  { value: 3, label: '3.0 & above' }
]

export default function CourseFilters({ filters, onFilterChange, onClose }: CourseFiltersProps) {
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    level: true,
    language: false,
    price: true,
    rating: true,
    features: false
  })

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    onFilterChange({
      ...filters,
      [key]: value
    })
  }

  const clearAllFilters = () => {
    onFilterChange({
      category: '',
      level: '',
      language: '',
      priceRange: [0, 10000],
      rating: 0,
      isFree: false
    })
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.category) count++
    if (filters.level) count++
    if (filters.language) count++
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 10000) count++
    if (filters.rating > 0) count++
    if (filters.isFree) count++
    return count
  }

  const FilterSection = ({ 
    title, 
    sectionKey, 
    children 
  }: { 
    title: string
    sectionKey: keyof typeof expandedSections
    children: React.ReactNode 
  }) => (
    <div className="border-b border-gray-200 pb-4 mb-4">
      <button
        onClick={() => toggleSection(sectionKey)}
        className="flex items-center justify-between w-full text-left font-medium text-gray-900 mb-3"
      >
        {title}
        {expandedSections[sectionKey] ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </button>
      {expandedSections[sectionKey] && children}
    </div>
  )

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
          {getActiveFiltersCount() > 0 && (
            <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
              {getActiveFiltersCount()}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {getActiveFiltersCount() > 0 && (
            <button
              onClick={clearAllFilters}
              className="text-sm text-green-600 hover:text-green-700 underline"
            >
              Clear all
            </button>
          )}
          {onClose && (
            <button
              onClick={onClose}
              className="lg:hidden text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>

      {/* Category Filter */}
      <FilterSection title="Category" sectionKey="category">
        <div className="space-y-2">
          {categories.map((category) => (
            <label key={category.value} className="flex items-center">
              <input
                type="radio"
                name="category"
                value={category.value}
                checked={filters.category === category.value}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">{category.label}</span>
            </label>
          ))}
          {filters.category && (
            <button
              onClick={() => handleFilterChange('category', '')}
              className="text-sm text-green-600 hover:text-green-700 underline"
            >
              Clear category
            </button>
          )}
        </div>
      </FilterSection>

      {/* Level Filter */}
      <FilterSection title="Level" sectionKey="level">
        <div className="space-y-2">
          {levels.map((level) => (
            <label key={level.value} className="flex items-center">
              <input
                type="radio"
                name="level"
                value={level.value}
                checked={filters.level === level.value}
                onChange={(e) => handleFilterChange('level', e.target.value)}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">{level.label}</span>
            </label>
          ))}
          {filters.level && (
            <button
              onClick={() => handleFilterChange('level', '')}
              className="text-sm text-green-600 hover:text-green-700 underline"
            >
              Clear level
            </button>
          )}
        </div>
      </FilterSection>

      {/* Language Filter */}
      <FilterSection title="Language" sectionKey="language">
        <div className="space-y-2">
          {languages.map((language) => (
            <label key={language.value} className="flex items-center">
              <input
                type="radio"
                name="language"
                value={language.value}
                checked={filters.language === language.value}
                onChange={(e) => handleFilterChange('language', e.target.value)}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">{language.label}</span>
            </label>
          ))}
          {filters.language && (
            <button
              onClick={() => handleFilterChange('language', '')}
              className="text-sm text-green-600 hover:text-green-700 underline"
            >
              Clear language
            </button>
          )}
        </div>
      </FilterSection>

      {/* Price Filter */}
      <FilterSection title="Price" sectionKey="price">
        <div className="space-y-4">
          <div>
            <label className="flex items-center mb-3">
              <input
                type="checkbox"
                checked={filters.isFree}
                onChange={(e) => handleFilterChange('isFree', e.target.checked)}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Free courses only</span>
            </label>
          </div>
          
          {!filters.isFree && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Range: ₹{filters.priceRange[0]} - ₹{filters.priceRange[1]}
              </label>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="10000"
                  step="500"
                  value={filters.priceRange[0]}
                  onChange={(e) => handleFilterChange('priceRange', [Number(e.target.value), filters.priceRange[1]])}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <input
                  type="range"
                  min="0"
                  max="10000"
                  step="500"
                  value={filters.priceRange[1]}
                  onChange={(e) => handleFilterChange('priceRange', [filters.priceRange[0], Number(e.target.value)])}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>₹0</span>
                <span>₹10,000+</span>
              </div>
            </div>
          )}
        </div>
      </FilterSection>

      {/* Rating Filter */}
      <FilterSection title="Rating" sectionKey="rating">
        <div className="space-y-2">
          {ratings.map((rating) => (
            <label key={rating.value} className="flex items-center">
              <input
                type="radio"
                name="rating"
                value={rating.value}
                checked={filters.rating === rating.value}
                onChange={(e) => handleFilterChange('rating', Number(e.target.value))}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700 flex items-center">
                {rating.label}
                <div className="flex ml-2">
                  {Array.from({ length: 5 }, (_, i) => (
                    <svg
                      key={i}
                      className={`h-3 w-3 ${i < Math.floor(rating.value) ? 'text-yellow-400' : 'text-gray-300'}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
              </span>
            </label>
          ))}
          {filters.rating > 0 && (
            <button
              onClick={() => handleFilterChange('rating', 0)}
              className="text-sm text-green-600 hover:text-green-700 underline"
            >
              Clear rating
            </button>
          )}
        </div>
      </FilterSection>
    </div>
  )
}
