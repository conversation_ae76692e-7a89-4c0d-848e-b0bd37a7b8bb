'use client'

import React from 'react'
import { 
  BookOpen, 
  Users, 
  BarChart3, 
  Palette, 
  CreditCard, 
  Smartphone,
  Shield,
  Globe,
  Zap,
  Award,
  MessageSquare,
  Settings
} from 'lucide-react'

interface Feature {
  icon: React.ComponentType<any>
  title: string
  description: string
  benefits: string[]
}

export default function Features() {
  const features: Feature[] = [
    {
      icon: BookOpen,
      title: "Course Management",
      description: "Create, organize, and deliver engaging courses with our intuitive course builder and content management system.",
      benefits: [
        "Drag-and-drop course builder",
        "Video, audio, and document support",
        "Interactive quizzes and assignments",
        "Progress tracking and analytics"
      ]
    },
    {
      icon: Users,
      title: "Student Portal",
      description: "Give your students a seamless learning experience with our modern, mobile-friendly student portal.",
      benefits: [
        "Personalized learning dashboard",
        "Course progress tracking",
        "Discussion forums and chat",
        "Mobile app access"
      ]
    },
    {
      icon: BarChart3,
      title: "Analytics & Reports",
      description: "Track progress, measure success, and make data-driven decisions with comprehensive analytics.",
      benefits: [
        "Real-time performance metrics",
        "Student engagement analytics",
        "Revenue and enrollment reports",
        "Custom dashboard creation"
      ]
    },
    {
      icon: Palette,
      title: "Custom Themes",
      description: "Brand your institute with beautiful, customizable themes that reflect your unique identity.",
      benefits: [
        "Professional theme library",
        "Custom color schemes",
        "Logo and branding integration",
        "Mobile-responsive designs"
      ]
    },
    {
      icon: CreditCard,
      title: "Payment Integration",
      description: "Accept payments seamlessly with multiple payment gateway options and automated billing.",
      benefits: [
        "Multiple payment gateways",
        "Subscription management",
        "Automated invoicing",
        "Revenue tracking"
      ]
    },
    {
      icon: Smartphone,
      title: "Mobile Ready",
      description: "Your platform works perfectly on all devices - desktop, tablet, and mobile with native app support.",
      benefits: [
        "Responsive web design",
        "Native mobile apps",
        "Offline content access",
        "Push notifications"
      ]
    },
    {
      icon: Shield,
      title: "Security & Privacy",
      description: "Enterprise-grade security with data encryption, secure hosting, and GDPR compliance.",
      benefits: [
        "SSL encryption",
        "Data backup and recovery",
        "GDPR compliance",
        "Role-based access control"
      ]
    },
    {
      icon: Globe,
      title: "Multi-Language Support",
      description: "Reach a global audience with multi-language support and localization features.",
      benefits: [
        "Multiple language support",
        "RTL language compatibility",
        "Currency localization",
        "Regional customization"
      ]
    },
    {
      icon: Zap,
      title: "Performance & Speed",
      description: "Lightning-fast loading times with CDN delivery and optimized performance.",
      benefits: [
        "Global CDN delivery",
        "Optimized video streaming",
        "Fast page load times",
        "99.9% uptime guarantee"
      ]
    },
    {
      icon: Award,
      title: "Certification System",
      description: "Issue professional certificates and badges to recognize student achievements.",
      benefits: [
        "Custom certificate templates",
        "Digital badge system",
        "Blockchain verification",
        "LinkedIn integration"
      ]
    },
    {
      icon: MessageSquare,
      title: "Communication Tools",
      description: "Keep students engaged with built-in messaging, announcements, and discussion forums.",
      benefits: [
        "Real-time messaging",
        "Announcement system",
        "Discussion forums",
        "Email notifications"
      ]
    },
    {
      icon: Settings,
      title: "Easy Administration",
      description: "Manage your entire institute from one powerful admin dashboard with intuitive controls.",
      benefits: [
        "Centralized admin panel",
        "User management",
        "Content moderation",
        "System configuration"
      ]
    }
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Everything You Need to Run Your Institute
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From course creation to student management, our comprehensive LMS platform 
            provides all the tools you need to deliver exceptional online education.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <div 
              key={index} 
              className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900">{feature.title}</h3>
              </div>
              
              <p className="text-gray-600 mb-4 leading-relaxed">
                {feature.description}
              </p>
              
              <ul className="space-y-2">
                {feature.benefits.map((benefit, benefitIndex) => (
                  <li key={benefitIndex} className="flex items-center text-sm text-gray-600">
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3 flex-shrink-0"></div>
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Feature Highlights */}
        <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-green-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Quick Setup</h4>
              <p className="text-gray-600">
                Get your institute online in minutes with our easy setup wizard and pre-built templates.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-purple-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Enterprise Security</h4>
              <p className="text-gray-600">
                Bank-level security with encryption, secure hosting, and compliance with global standards.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-orange-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">24/7 Support</h4>
              <p className="text-gray-600">
                Our dedicated support team is available around the clock to help you succeed.
              </p>
            </div>
          </div>
        </div>

        {/* Integration Section */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">
            Integrates with Your Favorite Tools
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
            {[
              'Zoom', 'Google Meet', 'PayPal', 'Stripe', 'Mailchimp', 'Slack',
              'Google Analytics', 'YouTube', 'Vimeo', 'Dropbox', 'Google Drive', 'AWS'
            ].map((integration, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-gray-200 rounded-lg mx-auto mb-2"></div>
                <span className="text-sm text-gray-600">{integration}</span>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Transform Your Institute?
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Join thousands of institutes already using Groups Exam LMS to deliver 
              world-class online education.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/auth/register"
                className="inline-flex items-center justify-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                Start Free Trial
              </a>
              <a
                href="/demo"
                className="inline-flex items-center justify-center px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors duration-200"
              >
                Watch Demo
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
