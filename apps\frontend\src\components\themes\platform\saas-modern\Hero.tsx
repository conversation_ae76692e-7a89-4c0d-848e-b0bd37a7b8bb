'use client'

import React from 'react'
import Link from 'next/link'
import { useTheme } from '@/components/shared/theme/ThemeProvider'
import { ArrowRight, Play, CheckCircle } from 'lucide-react'

interface HeroProps {
  title?: string
  subtitle?: string
  description?: string
  primaryCTA?: {
    text: string
    href: string
  }
  secondaryCTA?: {
    text: string
    href: string
  }
  features?: string[]
  videoUrl?: string
  heroImage?: string
}

const defaultProps: HeroProps = {
  title: "Transform Education with",
  subtitle: "Groups Exam LMS",
  description: "The complete learning management system for institutes, coaching centers, and online academies. Create, manage, and sell courses with our powerful platform.",
  primaryCTA: {
    text: "Start Free Trial",
    href: "/auth/register"
  },
  secondaryCTA: {
    text: "Watch Demo",
    href: "#demo"
  },
  features: [
    "Course Management System",
    "Student Portal & Analytics",
    "Multiple Payment Gateways",
    "Custom Themes & Branding"
  ],
  heroImage: "/images/hero-dashboard.png"
}

export default function Hero(props: Partial<HeroProps> = {}) {
  const { currentTheme, customizations } = useTheme()
  const {
    title,
    subtitle,
    description,
    primaryCTA,
    secondaryCTA,
    features,
    videoUrl,
    heroImage
  } = { ...defaultProps, ...props, ...customizations.content?.hero }

  return (
    <section className="relative bg-gradient-to-br from-blue-50 via-white to-blue-50 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-left">
            <div className="space-y-6">
              {/* Badge */}
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
                Trusted by 1000+ Institutes
              </div>

              {/* Headline */}
              <div className="space-y-4">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  {title}
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-800">
                    {subtitle}
                  </span>
                </h1>
                
                <p className="text-xl text-gray-600 max-w-2xl">
                  {description}
                </p>
              </div>

              {/* CTAs */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                {primaryCTA && (
                  <Link
                    href={primaryCTA.href}
                    className="inline-flex items-center justify-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    {primaryCTA.text}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                )}
                
                {secondaryCTA && (
                  <Link
                    href={secondaryCTA.href}
                    className="inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-lg hover:border-blue-600 hover:text-blue-600 transition-all duration-200"
                  >
                    <Play className="mr-2 h-5 w-5" />
                    {secondaryCTA.text}
                  </Link>
                )}
              </div>

              {/* Features */}
              {features && features.length > 0 && (
                <div className="pt-8">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Social Proof */}
              <div className="pt-8 border-t border-gray-200">
                <p className="text-sm text-gray-500 mb-4">Trusted by leading institutions</p>
                <div className="flex items-center space-x-8 opacity-60">
                  {/* Placeholder logos - replace with actual client logos */}
                  <div className="h-8 w-20 bg-gray-300 rounded"></div>
                  <div className="h-8 w-20 bg-gray-300 rounded"></div>
                  <div className="h-8 w-20 bg-gray-300 rounded"></div>
                  <div className="h-8 w-20 bg-gray-300 rounded"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Hero Image/Video */}
          <div className="relative">
            <div className="relative z-10">
              {videoUrl ? (
                <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                  <video
                    className="w-full h-auto"
                    poster={heroImage}
                    controls
                    preload="metadata"
                  >
                    <source src={videoUrl} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
              ) : (
                <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white p-4">
                  {heroImage ? (
                    <img
                      src={heroImage}
                      alt="Platform Dashboard"
                      className="w-full h-auto rounded-lg"
                    />
                  ) : (
                    <div className="w-full h-96 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-blue-600 rounded-lg mx-auto mb-4 flex items-center justify-center">
                          <span className="text-white text-2xl font-bold">LMS</span>
                        </div>
                        <p className="text-blue-800 font-medium">Dashboard Preview</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Floating Elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-yellow-400 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-blue-400 rounded-full opacity-20 animate-pulse delay-1000"></div>
          </div>
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg
          className="w-full h-20 text-white"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
        >
          <path
            d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
            opacity=".25"
            fill="currentColor"
          ></path>
          <path
            d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
            opacity=".5"
            fill="currentColor"
          ></path>
          <path
            d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
            fill="currentColor"
          ></path>
        </svg>
      </div>
    </section>
  )
}
