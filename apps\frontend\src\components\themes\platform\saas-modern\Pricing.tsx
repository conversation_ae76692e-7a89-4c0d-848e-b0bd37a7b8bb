'use client'

import React, { useState } from 'react'
import { Check, X, Star, Zap, Crown, Building } from 'lucide-react'

interface PricingPlan {
  id: string
  name: string
  description: string
  icon: React.ComponentType<any>
  price: {
    monthly: number
    yearly: number
  }
  features: {
    included: string[]
    excluded?: string[]
  }
  limits: {
    students: string
    courses: string
    storage: string
    branches: string
  }
  popular?: boolean
  enterprise?: boolean
  color: string
}

export default function Pricing() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')

  const plans: PricingPlan[] = [
    {
      id: 'starter',
      name: 'Starter',
      description: 'Perfect for small institutes getting started',
      icon: Zap,
      price: {
        monthly: 2999,
        yearly: 29990
      },
      features: {
        included: [
          'Up to 100 students',
          'Up to 10 courses',
          'Basic analytics',
          'Email support',
          '5GB storage',
          'Mobile app access',
          'Basic themes',
          'Payment gateway integration'
        ],
        excluded: [
          'Live classes',
          'Advanced analytics',
          'Custom branding',
          'API access'
        ]
      },
      limits: {
        students: '100',
        courses: '10',
        storage: '5GB',
        branches: '1'
      },
      color: 'blue'
    },
    {
      id: 'professional',
      name: 'Professional',
      description: 'Best for growing institutes with advanced needs',
      icon: Star,
      price: {
        monthly: 5999,
        yearly: 59990
      },
      features: {
        included: [
          'Up to 500 students',
          'Up to 50 courses',
          'Advanced analytics',
          'Priority support',
          '25GB storage',
          'Live classes',
          'Custom branding',
          'Multiple payment gateways',
          'Bulk operations',
          'Certificate generation',
          'Discussion forums',
          'Assignment system'
        ]
      },
      limits: {
        students: '500',
        courses: '50',
        storage: '25GB',
        branches: '3'
      },
      popular: true,
      color: 'purple'
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'For large institutes with unlimited requirements',
      icon: Crown,
      price: {
        monthly: 12999,
        yearly: 129990
      },
      features: {
        included: [
          'Unlimited students',
          'Unlimited courses',
          'Advanced analytics & reports',
          '24/7 dedicated support',
          '100GB storage',
          'White-label solution',
          'Custom integrations',
          'API access',
          'Multi-branch management',
          'Advanced user roles',
          'Custom themes',
          'SSO integration',
          'Data export',
          'Priority feature requests'
        ]
      },
      limits: {
        students: 'Unlimited',
        courses: 'Unlimited',
        storage: '100GB',
        branches: 'Unlimited'
      },
      enterprise: true,
      color: 'gold'
    }
  ]

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const getYearlySavings = (monthly: number, yearly: number) => {
    const monthlyCost = monthly * 12
    const savings = monthlyCost - yearly
    const percentage = Math.round((savings / monthlyCost) * 100)
    return { amount: savings, percentage }
  }

  const getPlanColor = (color: string, type: 'bg' | 'text' | 'border') => {
    const colors = {
      blue: {
        bg: 'bg-blue-600',
        text: 'text-blue-600',
        border: 'border-blue-600'
      },
      purple: {
        bg: 'bg-purple-600',
        text: 'text-purple-600',
        border: 'border-purple-600'
      },
      gold: {
        bg: 'bg-yellow-600',
        text: 'text-yellow-600',
        border: 'border-yellow-600'
      }
    }
    return colors[color as keyof typeof colors]?.[type] || colors.blue[type]
  }

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Simple, Transparent Pricing
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Choose the perfect plan for your institute. All plans include our core features 
            with no hidden fees or setup costs.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center mb-8">
            <span className={`mr-3 ${billingCycle === 'monthly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Monthly
            </span>
            <button
              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                billingCycle === 'yearly' ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`ml-3 ${billingCycle === 'yearly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Yearly
            </span>
            {billingCycle === 'yearly' && (
              <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                Save up to 17%
              </span>
            )}
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan) => {
            const savings = getYearlySavings(plan.price.monthly, plan.price.yearly)
            const currentPrice = billingCycle === 'monthly' ? plan.price.monthly : plan.price.yearly

            return (
              <div
                key={plan.id}
                className={`relative bg-white rounded-2xl shadow-lg border-2 transition-all duration-200 hover:shadow-xl ${
                  plan.popular 
                    ? `${getPlanColor(plan.color, 'border')} scale-105` 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {/* Popular Badge */}
                {plan.popular && (
                  <div className={`absolute -top-4 left-1/2 transform -translate-x-1/2 px-4 py-1 ${getPlanColor(plan.color, 'bg')} text-white text-sm font-medium rounded-full`}>
                    Most Popular
                  </div>
                )}

                <div className="p-8">
                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <div className={`w-16 h-16 ${getPlanColor(plan.color, 'bg')} rounded-full flex items-center justify-center mx-auto mb-4`}>
                      <plan.icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                    <p className="text-gray-600">{plan.description}</p>
                  </div>

                  {/* Pricing */}
                  <div className="text-center mb-8">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-gray-900">
                        {formatPrice(currentPrice)}
                      </span>
                      <span className="text-gray-500 ml-2">
                        /{billingCycle === 'monthly' ? 'month' : 'year'}
                      </span>
                    </div>
                    {billingCycle === 'yearly' && (
                      <p className="text-sm text-green-600 mt-2">
                        Save {formatPrice(savings.amount)} ({savings.percentage}% off)
                      </p>
                    )}
                  </div>

                  {/* Features */}
                  <div className="mb-8">
                    <h4 className="font-semibold text-gray-900 mb-4">What's included:</h4>
                    <ul className="space-y-3">
                      {plan.features.included.map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                          <span className="text-gray-600">{feature}</span>
                        </li>
                      ))}
                      {plan.features.excluded?.map((feature, index) => (
                        <li key={index} className="flex items-center opacity-50">
                          <X className="h-4 w-4 text-gray-400 mr-3 flex-shrink-0" />
                          <span className="text-gray-400">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Limits */}
                  <div className="mb-8 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-3">Plan Limits:</h4>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <span className="text-gray-500">Students:</span>
                        <span className="font-medium text-gray-900 ml-1">{plan.limits.students}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Courses:</span>
                        <span className="font-medium text-gray-900 ml-1">{plan.limits.courses}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Storage:</span>
                        <span className="font-medium text-gray-900 ml-1">{plan.limits.storage}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Branches:</span>
                        <span className="font-medium text-gray-900 ml-1">{plan.limits.branches}</span>
                      </div>
                    </div>
                  </div>

                  {/* CTA Button */}
                  <button
                    className={`w-full py-3 px-6 rounded-lg font-semibold transition-colors ${
                      plan.popular
                        ? `${getPlanColor(plan.color, 'bg')} text-white hover:opacity-90`
                        : 'bg-gray-900 text-white hover:bg-gray-800'
                    }`}
                  >
                    {plan.enterprise ? 'Contact Sales' : 'Start Free Trial'}
                  </button>
                </div>
              </div>
            )
          })}
        </div>

        {/* FAQ Section */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Frequently Asked Questions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="text-left">
              <h4 className="font-semibold text-gray-900 mb-2">Can I change plans anytime?</h4>
              <p className="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
            </div>
            <div className="text-left">
              <h4 className="font-semibold text-gray-900 mb-2">Is there a free trial?</h4>
              <p className="text-gray-600">Yes, all plans come with a 14-day free trial. No credit card required to start.</p>
            </div>
            <div className="text-left">
              <h4 className="font-semibold text-gray-900 mb-2">What payment methods do you accept?</h4>
              <p className="text-gray-600">We accept all major credit cards, UPI, net banking, and digital wallets.</p>
            </div>
            <div className="text-left">
              <h4 className="font-semibold text-gray-900 mb-2">Do you offer custom plans?</h4>
              <p className="text-gray-600">Yes, we offer custom enterprise plans for large institutions with specific requirements.</p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="mt-16 text-center bg-gray-50 rounded-2xl p-8">
          <Building className="h-12 w-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-gray-900 mb-2">Need a Custom Solution?</h3>
          <p className="text-gray-600 mb-6">
            Contact our sales team for custom pricing and enterprise features tailored to your needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="px-6 py-3 bg-gray-900 text-white font-semibold rounded-lg hover:bg-gray-800"
            >
              Contact Sales
            </a>
            <a
              href="/demo"
              className="px-6 py-3 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50"
            >
              Schedule Demo
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}
