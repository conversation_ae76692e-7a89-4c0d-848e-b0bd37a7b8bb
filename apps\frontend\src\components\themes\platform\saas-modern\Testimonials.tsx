'use client'

import React, { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react'

interface Testimonial {
  id: string
  name: string
  role: string
  company: string
  avatar: string
  content: string
  rating: number
  metrics?: {
    label: string
    value: string
  }[]
}

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  const testimonials: Testimonial[] = [
    {
      id: '1',
      name: 'Dr. <PERSON><PERSON>',
      role: 'Director',
      company: 'TechEd Institute',
      avatar: '/images/testimonials/priya-sharma.jpg',
      content: 'Groups Exam LMS transformed our institute completely. We went from 200 to 2000+ students in just 6 months. The platform is incredibly user-friendly and our students love the mobile app.',
      rating: 5,
      metrics: [
        { label: 'Student Growth', value: '900%' },
        { label: 'Course Completion', value: '85%' }
      ]
    },
    {
      id: '2',
      name: '<PERSON><PERSON>',
      role: 'Founder & CEO',
      company: 'SkillUp Academy',
      avatar: '/images/testimonials/rajesh-kumar.jpg',
      content: 'The analytics and reporting features are outstanding. We can track every aspect of student engagement and course performance. Revenue increased by 300% after switching to Groups Exam LMS.',
      rating: 5,
      metrics: [
        { label: 'Revenue Growth', value: '300%' },
        { label: 'Student Satisfaction', value: '4.8/5' }
      ]
    },
    {
      id: '3',
      name: '<PERSON>',
      role: 'Academic Head',
      company: 'Global Learning Hub',
      avatar: '/images/testimonials/sarah-johnson.jpg',
      content: 'The multi-language support and custom branding options helped us expand internationally. We now serve students in 15+ countries with localized content.',
      rating: 5,
      metrics: [
        { label: 'Countries Served', value: '15+' },
        { label: 'Languages Supported', value: '8' }
      ]
    },
    {
      id: '4',
      name: 'Mohammed Ali',
      role: 'Training Manager',
      company: 'Professional Development Center',
      avatar: '/images/testimonials/mohammed-ali.jpg',
      content: 'The certificate generation and badge system motivated our students significantly. Course completion rates improved by 60% and student engagement is at an all-time high.',
      rating: 5,
      metrics: [
        { label: 'Completion Rate', value: '+60%' },
        { label: 'Engagement Score', value: '92%' }
      ]
    },
    {
      id: '5',
      name: 'Lisa Chen',
      role: 'Operations Director',
      company: 'Digital Skills Institute',
      avatar: '/images/testimonials/lisa-chen.jpg',
      content: 'The payment integration and automated billing saved us countless hours. We can now focus on creating great content instead of managing payments and invoices.',
      rating: 5,
      metrics: [
        { label: 'Time Saved', value: '20hrs/week' },
        { label: 'Payment Success', value: '99.2%' }
      ]
    },
    {
      id: '6',
      name: 'Amit Patel',
      role: 'Founder',
      company: 'CodeMaster Academy',
      avatar: '/images/testimonials/amit-patel.jpg',
      content: 'The live class integration with Zoom and Google Meet is seamless. Our instructors love the interactive features and students appreciate the recorded sessions.',
      rating: 5,
      metrics: [
        { label: 'Live Classes', value: '500+/month' },
        { label: 'Attendance Rate', value: '88%' }
      ]
    }
  ]

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      )
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, testimonials.length])

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000) // Resume auto-play after 10 seconds
  }

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const goToNext = () => {
    setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Trusted by 10,000+ Institutes Worldwide
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how institutes like yours are transforming education and growing their business 
            with Groups Exam LMS.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">10,000+</div>
            <div className="text-gray-600">Active Institutes</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2">2M+</div>
            <div className="text-gray-600">Students Enrolled</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-purple-600 mb-2">50K+</div>
            <div className="text-gray-600">Courses Created</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-orange-600 mb-2">4.9/5</div>
            <div className="text-gray-600">Average Rating</div>
          </div>
        </div>

        {/* Testimonial Carousel */}
        <div className="relative">
          <div className="bg-white rounded-2xl shadow-lg p-8 md:p-12">
            <div className="flex flex-col lg:flex-row items-center gap-8">
              {/* Avatar and Info */}
              <div className="flex-shrink-0 text-center lg:text-left">
                <div className="w-24 h-24 rounded-full bg-gray-200 mx-auto lg:mx-0 mb-4 overflow-hidden">
                  <img
                    src={testimonials[currentIndex].avatar}
                    alt={testimonials[currentIndex].name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(testimonials[currentIndex].name)}&background=3B82F6&color=fff&size=96`
                    }}
                  />
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-1">
                  {testimonials[currentIndex].name}
                </h4>
                <p className="text-gray-600 mb-2">
                  {testimonials[currentIndex].role}
                </p>
                <p className="text-blue-600 font-medium mb-4">
                  {testimonials[currentIndex].company}
                </p>
                
                {/* Rating */}
                <div className="flex justify-center lg:justify-start gap-1 mb-4">
                  {renderStars(testimonials[currentIndex].rating)}
                </div>

                {/* Metrics */}
                {testimonials[currentIndex].metrics && (
                  <div className="grid grid-cols-1 gap-3">
                    {testimonials[currentIndex].metrics!.map((metric, index) => (
                      <div key={index} className="text-center lg:text-left">
                        <div className="text-2xl font-bold text-blue-600">{metric.value}</div>
                        <div className="text-sm text-gray-600">{metric.label}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Testimonial Content */}
              <div className="flex-1">
                <Quote className="h-8 w-8 text-blue-600 mb-4" />
                <blockquote className="text-lg md:text-xl text-gray-700 leading-relaxed mb-6">
                  "{testimonials[currentIndex].content}"
                </blockquote>
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={goToPrevious}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors"
            aria-label="Previous testimonial"
          >
            <ChevronLeft className="h-6 w-6 text-gray-600" />
          </button>
          
          <button
            onClick={goToNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors"
            aria-label="Next testimonial"
          >
            <ChevronRight className="h-6 w-6 text-gray-600" />
          </button>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center mt-8 gap-2">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentIndex ? 'bg-blue-600' : 'bg-gray-300 hover:bg-gray-400'
              }`}
              aria-label={`Go to testimonial ${index + 1}`}
            />
          ))}
        </div>

        {/* Company Logos */}
        <div className="mt-16">
          <p className="text-center text-gray-600 mb-8">
            Trusted by leading educational institutions
          </p>
          <div className="grid grid-cols-3 md:grid-cols-6 gap-8 items-center opacity-60">
            {[
              'TechEd Institute',
              'SkillUp Academy', 
              'Global Learning Hub',
              'Professional Dev Center',
              'Digital Skills Institute',
              'CodeMaster Academy'
            ].map((company, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-2 flex items-center justify-center">
                  <span className="text-xs font-medium text-gray-500">
                    {company.split(' ').map(word => word[0]).join('')}
                  </span>
                </div>
                <span className="text-xs text-gray-500">{company}</span>
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Join Thousands of Successful Institutes
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Start your free trial today and see why institutes worldwide choose Groups Exam LMS 
              to transform their education delivery.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/auth/register"
                className="px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors"
              >
                Start Free Trial
              </a>
              <a
                href="/case-studies"
                className="px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
              >
                View Case Studies
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
