'use client'

import React from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  AlertCircle, 
  AlertTriangle, 
  XCircle, 
  Info, 
  RefreshCw, 
  X,
  FileX,
  Upload,
  Wifi,
  Server
} from 'lucide-react'
import { cn } from '@/lib/utils'

export type ErrorType = 'validation' | 'network' | 'server' | 'upload' | 'permission' | 'general'
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical'

export interface ErrorInfo {
  type: ErrorType
  severity: ErrorSeverity
  message: string
  details?: string
  code?: string | number
  field?: string
  timestamp?: Date
  retryable?: boolean
}

interface ErrorDisplayProps {
  error: ErrorInfo | string | null
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
  compact?: boolean
  showTimestamp?: boolean
  showCode?: boolean
}

export function ErrorDisplay({
  error,
  onRetry,
  onDismiss,
  className,
  compact = false,
  showTimestamp = false,
  showCode = false
}: ErrorDisplayProps) {
  if (!error) return null

  // Normalize error to ErrorInfo object
  const errorInfo: ErrorInfo = typeof error === 'string' 
    ? { type: 'general', severity: 'medium', message: error }
    : error

  const getErrorIcon = () => {
    switch (errorInfo.type) {
      case 'validation':
        return <AlertCircle className="h-4 w-4" />
      case 'network':
        return <Wifi className="h-4 w-4" />
      case 'server':
        return <Server className="h-4 w-4" />
      case 'upload':
        return <FileX className="h-4 w-4" />
      case 'permission':
        return <XCircle className="h-4 w-4" />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getErrorVariant = () => {
    switch (errorInfo.severity) {
      case 'critical':
      case 'high':
        return 'destructive'
      case 'medium':
        return 'default'
      case 'low':
        return 'default'
      default:
        return 'default'
    }
  }

  const getErrorColor = () => {
    switch (errorInfo.severity) {
      case 'critical':
        return 'text-red-600 border-red-200 bg-red-50'
      case 'high':
        return 'text-red-500 border-red-200 bg-red-50'
      case 'medium':
        return 'text-orange-600 border-orange-200 bg-orange-50'
      case 'low':
        return 'text-yellow-600 border-yellow-200 bg-yellow-50'
      default:
        return 'text-gray-600 border-gray-200 bg-gray-50'
    }
  }

  const getSeverityBadge = () => {
    const colors = {
      critical: 'bg-red-100 text-red-800',
      high: 'bg-red-100 text-red-700',
      medium: 'bg-orange-100 text-orange-700',
      low: 'bg-yellow-100 text-yellow-700'
    }

    return (
      <Badge variant="secondary" className={colors[errorInfo.severity]}>
        {errorInfo.severity.toUpperCase()}
      </Badge>
    )
  }

  if (compact) {
    return (
      <Alert variant={getErrorVariant()} className={cn('', className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getErrorIcon()}
            <AlertDescription className="mb-0">
              {errorInfo.message}
            </AlertDescription>
          </div>
          
          <div className="flex items-center space-x-1">
            {errorInfo.retryable && onRetry && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onRetry}
                className="h-6 w-6 p-0"
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
            )}
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </Alert>
    )
  }

  return (
    <Card className={cn('border-l-4', getErrorColor(), className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getErrorIcon()}
            <CardTitle className="text-sm font-medium">
              {errorInfo.type.charAt(0).toUpperCase() + errorInfo.type.slice(1)} Error
            </CardTitle>
            {getSeverityBadge()}
          </div>
          
          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="pt-0 space-y-3">
        <div>
          <p className="text-sm text-gray-900 font-medium">
            {errorInfo.message}
          </p>
          
          {errorInfo.details && (
            <p className="text-xs text-gray-600 mt-1">
              {errorInfo.details}
            </p>
          )}
        </div>

        {/* Additional info */}
        {(showCode || showTimestamp || errorInfo.field) && (
          <div className="flex flex-wrap gap-2 text-xs text-gray-500">
            {errorInfo.field && (
              <span className="bg-gray-100 px-2 py-1 rounded">
                Field: {errorInfo.field}
              </span>
            )}
            
            {showCode && errorInfo.code && (
              <span className="bg-gray-100 px-2 py-1 rounded">
                Code: {errorInfo.code}
              </span>
            )}
            
            {showTimestamp && errorInfo.timestamp && (
              <span className="bg-gray-100 px-2 py-1 rounded">
                {errorInfo.timestamp.toLocaleTimeString()}
              </span>
            )}
          </div>
        )}

        {/* Actions */}
        {(errorInfo.retryable && onRetry) && (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="h-8"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Try Again
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Specialized error display components
export function ValidationErrorDisplay({ 
  errors, 
  onDismiss,
  className 
}: { 
  errors: string[] | Record<string, string>
  onDismiss?: () => void
  className?: string 
}) {
  if (!errors || (Array.isArray(errors) && errors.length === 0) || 
      (typeof errors === 'object' && Object.keys(errors).length === 0)) {
    return null
  }

  const errorList = Array.isArray(errors) 
    ? errors 
    : Object.entries(errors).map(([field, message]) => `${field}: ${message}`)

  return (
    <Alert variant="destructive" className={className}>
      <AlertCircle className="h-4 w-4" />
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <AlertTitle>Validation Errors</AlertTitle>
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1 mt-2">
              {errorList.map((error, index) => (
                <li key={index} className="text-sm">{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </div>
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="h-6 w-6 p-0 ml-2"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    </Alert>
  )
}

export function NetworkErrorDisplay({ 
  onRetry, 
  className 
}: { 
  onRetry?: () => void
  className?: string 
}) {
  return (
    <ErrorDisplay
      error={{
        type: 'network',
        severity: 'high',
        message: 'Network connection failed',
        details: 'Please check your internet connection and try again.',
        retryable: true
      }}
      onRetry={onRetry}
      className={className}
    />
  )
}

export function UploadErrorDisplay({ 
  message, 
  onRetry, 
  onDismiss,
  className 
}: { 
  message: string
  onRetry?: () => void
  onDismiss?: () => void
  className?: string 
}) {
  return (
    <ErrorDisplay
      error={{
        type: 'upload',
        severity: 'medium',
        message,
        retryable: !!onRetry
      }}
      onRetry={onRetry}
      onDismiss={onDismiss}
      className={className}
    />
  )
}

// Utility function to create error info objects
export function createErrorInfo(
  type: ErrorType,
  message: string,
  options: Partial<Omit<ErrorInfo, 'type' | 'message'>> = {}
): ErrorInfo {
  return {
    type,
    message,
    severity: 'medium',
    timestamp: new Date(),
    retryable: type === 'network' || type === 'server',
    ...options
  }
}
