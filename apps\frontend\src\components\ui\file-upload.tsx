'use client'

import React, { useCallback, useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent } from '@/components/ui/card'
import { Upload, X, Image, FileText, AlertCircle, CheckCircle2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { validateFileClientSide, formatFileSize } from '@/lib/validation/file-upload'
import { ErrorDisplay, ValidationErrorDisplay } from '@/components/ui/error-display'

export interface FileUploadProps {
  onFileSelect: (file: File) => void
  onFileRemove?: () => void
  accept?: Record<string, string[]>
  maxSize?: number
  multiple?: boolean
  disabled?: boolean
  loading?: boolean
  progress?: number
  error?: string | null
  success?: boolean
  currentFile?: {
    name: string
    url?: string
    size?: number
  }
  placeholder?: string
  description?: string
  className?: string
  showPreview?: boolean
  previewClassName?: string
  // Validation options
  allowedTypes?: string[]
  allowedExtensions?: string[]
  validateOnSelect?: boolean
  showValidationErrors?: boolean
  onValidationError?: (errors: string[]) => void
}

export function FileUpload({
  onFileSelect,
  onFileRemove,
  accept = {
    'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp', '.svg']
  },
  maxSize = 5 * 1024 * 1024, // 5MB
  multiple = false,
  disabled = false,
  loading = false,
  progress,
  error,
  success = false,
  currentFile,
  placeholder = 'Click to upload or drag and drop',
  description = 'SVG, PNG, JPG or GIF (max. 5MB)',
  className,
  showPreview = true,
  previewClassName
}: FileUploadProps) {
  const [dragActive, setDragActive] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (files && files.length > 0) {
      const file = files[0]

      // Clear previous validation errors
      setValidationErrors([])

      // Client-side validation
      if (validateOnSelect) {
        const validation = validateFileClientSide(file, {
          maxSize,
          allowedTypes,
          allowedExtensions
        })

        if (!validation.valid) {
          setValidationErrors(validation.errors)
          if (onValidationError) {
            onValidationError(validation.errors)
          }
          return
        }
      } else {
        // Basic validation (legacy)
        const errors: string[] = []

        // Validate file size
        if (file.size > maxSize) {
          errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(maxSize)})`)
        }

        // Validate file type
        const acceptedTypes = Object.values(accept).flat()
        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
        const isAccepted = acceptedTypes.length === 0 || acceptedTypes.includes(fileExtension) ||
                          Object.keys(accept).some(type => file.type.startsWith(type.replace('/*', '')))

        if (!isAccepted) {
          errors.push(`File type (${file.type}) is not allowed`)
        }

        if (errors.length > 0) {
          setValidationErrors(errors)
          if (onValidationError) {
            onValidationError(errors)
          }
          return
        }
      }

      onFileSelect(file)
    }
  }, [onFileSelect, accept, maxSize, allowedTypes, allowedExtensions, validateOnSelect, onValidationError])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!dragActive) setDragActive(true)
  }, [dragActive])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (disabled || loading) return

    const files = e.dataTransfer.files
    handleFileSelect(files)
  }, [disabled, loading, handleFileSelect])

  const handleClick = useCallback(() => {
    if (disabled || loading) return
    fileInputRef.current?.click()
  }, [disabled, loading])

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files)
  }, [handleFileSelect])

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const isImage = (filename: string) => {
    return /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(filename)
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Current File Preview */}
      {currentFile && showPreview && (
        <Card className={cn('relative', previewClassName)}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              {currentFile.url && isImage(currentFile.name) ? (
                <div className="relative">
                  <img
                    src={currentFile.url}
                    alt={currentFile.name}
                    className="h-16 w-16 rounded-lg object-cover border"
                  />
                  {success && (
                    <div className="absolute -top-1 -right-1 bg-green-500 rounded-full p-1">
                      <CheckCircle2 className="h-3 w-3 text-white" />
                    </div>
                  )}
                </div>
              ) : (
                <div className="h-16 w-16 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                  <FileText className="h-6 w-6 text-gray-400" />
                </div>
              )}
              
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {currentFile.name}
                </p>
                {currentFile.size && (
                  <p className="text-xs text-gray-500">
                    {formatFileSize(currentFile.size)}
                  </p>
                )}
                {loading && progress !== undefined && (
                  <div className="mt-2">
                    <Progress value={progress} className="h-1" />
                    <p className="text-xs text-gray-500 mt-1">{progress}% uploaded</p>
                  </div>
                )}
              </div>

              {onFileRemove && !loading && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={onFileRemove}
                  className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Area */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
        className={cn(
          'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
          {
            'border-blue-300 bg-blue-50': dragActive,
            'border-gray-300 hover:border-gray-400': !dragActive && !disabled && !loading,
            'border-gray-200 bg-gray-50 cursor-not-allowed': disabled || loading,
            'border-red-300 bg-red-50': error,
            'border-green-300 bg-green-50': success && !error,
          }
        )}
      >
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleInputChange}
          accept={Object.keys(accept).join(',')}
          multiple={multiple}
          disabled={disabled || loading}
          className="hidden"
        />
        
        <div className="space-y-2">
          {loading ? (
            <>
              <div className="mx-auto h-12 w-12 text-blue-500 animate-pulse">
                <Upload className="h-full w-full" />
              </div>
              <p className="text-sm text-blue-600">Uploading...</p>
            </>
          ) : success && !error ? (
            <>
              <div className="mx-auto h-12 w-12 text-green-500">
                <CheckCircle2 className="h-full w-full" />
              </div>
              <p className="text-sm text-green-600">Upload successful!</p>
            </>
          ) : error ? (
            <>
              <div className="mx-auto h-12 w-12 text-red-500">
                <AlertCircle className="h-full w-full" />
              </div>
              <p className="text-sm text-red-600">Upload failed</p>
            </>
          ) : (
            <>
              <div className="mx-auto h-12 w-12 text-gray-400">
                {currentFile && isImage(currentFile.name) ? (
                  <Image className="h-full w-full" />
                ) : (
                  <Upload className="h-full w-full" />
                )}
              </div>
              <div>
                <p className="text-sm text-gray-600">{placeholder}</p>
                <p className="text-xs text-gray-500">{description}</p>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Validation Errors */}
      {showValidationErrors && validationErrors.length > 0 && (
        <ValidationErrorDisplay
          errors={validationErrors}
          onDismiss={() => setValidationErrors([])}
        />
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Success Message */}
      {success && !error && validationErrors.length === 0 && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            File uploaded successfully!
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

// Specialized components for different upload types
export function LogoUpload(props: Omit<FileUploadProps, 'accept' | 'description' | 'allowedTypes' | 'validateOnSelect'>) {
  return (
    <FileUpload
      {...props}
      accept={{
        'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp', '.svg']
      }}
      allowedTypes={['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']}
      description="SVG, PNG, JPG, GIF or WebP (max. 5MB)"
      placeholder="Upload platform logo"
      validateOnSelect={true}
      showValidationErrors={true}
    />
  )
}

export function FaviconUpload(props: Omit<FileUploadProps, 'accept' | 'description' | 'maxSize' | 'allowedTypes' | 'validateOnSelect'>) {
  return (
    <FileUpload
      {...props}
      accept={{
        'image/*': ['.png', '.ico', '.gif', '.jpg', '.jpeg']
      }}
      allowedTypes={['image/x-icon', 'image/png', 'image/gif', 'image/jpeg', 'image/jpg']}
      maxSize={2 * 1024 * 1024} // 2MB
      description="ICO, PNG, GIF or JPG (max. 2MB, square recommended)"
      placeholder="Upload platform favicon"
      validateOnSelect={true}
      showValidationErrors={true}
    />
  )
}

export function AvatarUpload(props: Omit<FileUploadProps, 'accept' | 'description' | 'allowedTypes' | 'validateOnSelect'>) {
  return (
    <FileUpload
      {...props}
      accept={{
        'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
      }}
      allowedTypes={['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']}
      description="JPG, PNG, GIF or WebP (max. 5MB, square recommended)"
      placeholder="Upload avatar"
      previewClassName="max-w-xs"
      validateOnSelect={true}
      showValidationErrors={true}
    />
  )
}
