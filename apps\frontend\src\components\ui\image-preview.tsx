'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  X, 
  Download, 
  ExternalLink, 
  Eye, 
  EyeOff, 
  Maximize2,
  Image as ImageIcon,
  FileText
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ImagePreviewProps {
  src: string
  alt: string
  filename?: string
  filesize?: number
  mimeType?: string
  sizes?: Record<string, {
    url: string
    width: number
    height: number
  }>
  onRemove?: () => void
  onDownload?: () => void
  className?: string
  showSizes?: boolean
  showActions?: boolean
  compact?: boolean
}

export function ImagePreview({
  src,
  alt,
  filename,
  filesize,
  mimeType,
  sizes,
  onRemove,
  onDownload,
  className,
  showSizes = false,
  showActions = true,
  compact = false
}: ImagePreviewProps) {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const [showFullSize, setShowFullSize] = useState(false)

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const isImage = (type?: string) => {
    return type?.startsWith('image/') || /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(filename || '')
  }

  const handleImageLoad = () => {
    setImageLoaded(true)
    setImageError(false)
  }

  const handleImageError = () => {
    setImageError(true)
    setImageLoaded(false)
  }

  const handleDownload = () => {
    if (onDownload) {
      onDownload()
    } else {
      // Default download behavior
      const link = document.createElement('a')
      link.href = src
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  if (compact) {
    return (
      <div className={cn('flex items-center space-x-3 p-2 border rounded-lg', className)}>
        {isImage(mimeType) && !imageError ? (
          <img
            src={src}
            alt={alt}
            onLoad={handleImageLoad}
            onError={handleImageError}
            className="h-12 w-12 rounded object-cover border"
          />
        ) : (
          <div className="h-12 w-12 rounded border-2 border-dashed border-gray-300 flex items-center justify-center">
            <FileText className="h-6 w-6 text-gray-400" />
          </div>
        )}
        
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">
            {filename || alt}
          </p>
          {filesize && (
            <p className="text-xs text-gray-500">
              {formatFileSize(filesize)}
            </p>
          )}
        </div>

        {showActions && (
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open(src, '_blank')}
              className="h-8 w-8 p-0"
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
            {onRemove && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onRemove}
                className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <Card className={cn('overflow-hidden', className)}>
      <CardContent className="p-0">
        {/* Image Display */}
        <div className="relative">
          {isImage(mimeType) && !imageError ? (
            <div className="relative">
              <img
                src={src}
                alt={alt}
                onLoad={handleImageLoad}
                onError={handleImageError}
                className={cn(
                  'w-full h-auto max-h-64 object-contain bg-gray-50',
                  !imageLoaded && 'animate-pulse bg-gray-200'
                )}
              />
              
              {/* Overlay Actions */}
              {showActions && imageLoaded && (
                <div className="absolute top-2 right-2 flex space-x-1">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => setShowFullSize(!showFullSize)}
                    className="h-8 w-8 p-0 bg-white/80 hover:bg-white"
                  >
                    {showFullSize ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => window.open(src, '_blank')}
                    className="h-8 w-8 p-0 bg-white/80 hover:bg-white"
                  >
                    <Maximize2 className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="h-32 bg-gray-50 flex items-center justify-center border-b">
              <div className="text-center">
                <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">File preview not available</p>
              </div>
            </div>
          )}
        </div>

        {/* File Information */}
        <div className="p-4 space-y-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-gray-900 truncate">
                {filename || alt}
              </h4>
              <div className="flex items-center space-x-2 mt-1">
                {mimeType && (
                  <Badge variant="secondary" className="text-xs">
                    {mimeType}
                  </Badge>
                )}
                {filesize && (
                  <span className="text-xs text-gray-500">
                    {formatFileSize(filesize)}
                  </span>
                )}
              </div>
            </div>

            {showActions && (
              <div className="flex items-center space-x-1 ml-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownload}
                  className="h-8"
                >
                  <Download className="h-3 w-3 mr-1" />
                  Download
                </Button>
                {onRemove && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onRemove}
                    className="h-8 text-red-600 hover:text-red-700"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Remove
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Image Sizes */}
          {showSizes && sizes && Object.keys(sizes).length > 0 && (
            <div className="space-y-2">
              <h5 className="text-xs font-medium text-gray-700">Available Sizes:</h5>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(sizes).map(([sizeName, sizeInfo]) => (
                  <div
                    key={sizeName}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs"
                  >
                    <span className="font-medium">{sizeName}</span>
                    <span className="text-gray-500">
                      {sizeInfo.width}×{sizeInfo.height}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Full Size Toggle */}
          {showFullSize && isImage(mimeType) && !imageError && (
            <div className="border-t pt-3">
              <img
                src={src}
                alt={alt}
                className="w-full h-auto rounded border"
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Specialized preview components
export function LogoPreview(props: Omit<ImagePreviewProps, 'showSizes'>) {
  return <ImagePreview {...props} showSizes={true} />
}

export function FaviconPreview(props: Omit<ImagePreviewProps, 'showSizes'>) {
  return <ImagePreview {...props} showSizes={true} />
}

export function AvatarPreview(props: Omit<ImagePreviewProps, 'compact'>) {
  return <ImagePreview {...props} compact={true} />
}
