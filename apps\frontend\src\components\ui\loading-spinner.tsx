'use client'

import React from 'react'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  text?: string
  variant?: 'default' | 'overlay' | 'inline'
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
}

const textSizeClasses = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl'
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className,
  text,
  variant = 'default'
}) => {
  const spinnerClasses = cn(
    'animate-spin',
    sizeClasses[size],
    className
  )

  const textClasses = cn(
    'text-gray-600',
    textSizeClasses[size]
  )

  if (variant === 'overlay') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 flex flex-col items-center gap-4">
          <Loader2 className={spinnerClasses} />
          {text && <p className={textClasses}>{text}</p>}
        </div>
      </div>
    )
  }

  if (variant === 'inline') {
    return (
      <div className="flex items-center gap-2">
        <Loader2 className={spinnerClasses} />
        {text && <span className={textClasses}>{text}</span>}
      </div>
    )
  }

  // Default variant
  return (
    <div className="flex flex-col items-center justify-center gap-4 p-8">
      <Loader2 className={spinnerClasses} />
      {text && <p className={textClasses}>{text}</p>}
    </div>
  )
}

// Additional spinner variants for specific use cases
export const PageLoadingSpinner: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <div className="flex items-center justify-center min-h-[400px]">
    <LoadingSpinner size="lg" text={text} />
  </div>
)

export const ButtonLoadingSpinner: React.FC<{ className?: string }> = ({ className }) => (
  <Loader2 className={cn('h-4 w-4 animate-spin', className)} />
)

export const TableLoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center py-12">
    <LoadingSpinner size="md" text="Loading data..." />
  </div>
)

export const CardLoadingSpinner: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <div className="flex items-center justify-center py-8">
    <LoadingSpinner size="md" text={text} variant="inline" />
  </div>
)

// Skeleton loading components for better UX
export const SkeletonCard: React.FC = () => (
  <div className="animate-pulse">
    <div className="bg-gray-200 rounded-lg h-48 mb-4"></div>
    <div className="space-y-2">
      <div className="bg-gray-200 rounded h-4 w-3/4"></div>
      <div className="bg-gray-200 rounded h-4 w-1/2"></div>
      <div className="bg-gray-200 rounded h-4 w-2/3"></div>
    </div>
  </div>
)

export const SkeletonTable: React.FC<{ rows?: number; cols?: number }> = ({ 
  rows = 5, 
  cols = 4 
}) => (
  <div className="animate-pulse space-y-4">
    {/* Table header */}
    <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}>
      {Array.from({ length: cols }).map((_, i) => (
        <div key={i} className="bg-gray-200 rounded h-4"></div>
      ))}
    </div>
    
    {/* Table rows */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}>
        {Array.from({ length: cols }).map((_, colIndex) => (
          <div key={colIndex} className="bg-gray-200 rounded h-4"></div>
        ))}
      </div>
    ))}
  </div>
)

export const SkeletonList: React.FC<{ items?: number }> = ({ items = 5 }) => (
  <div className="animate-pulse space-y-4">
    {Array.from({ length: items }).map((_, index) => (
      <div key={index} className="flex items-center space-x-4">
        <div className="bg-gray-200 rounded-full h-10 w-10"></div>
        <div className="flex-1 space-y-2">
          <div className="bg-gray-200 rounded h-4 w-3/4"></div>
          <div className="bg-gray-200 rounded h-4 w-1/2"></div>
        </div>
      </div>
    ))}
  </div>
)

export const SkeletonForm: React.FC = () => (
  <div className="animate-pulse space-y-6">
    <div className="space-y-2">
      <div className="bg-gray-200 rounded h-4 w-24"></div>
      <div className="bg-gray-200 rounded h-10 w-full"></div>
    </div>
    <div className="space-y-2">
      <div className="bg-gray-200 rounded h-4 w-32"></div>
      <div className="bg-gray-200 rounded h-24 w-full"></div>
    </div>
    <div className="grid grid-cols-2 gap-4">
      <div className="space-y-2">
        <div className="bg-gray-200 rounded h-4 w-20"></div>
        <div className="bg-gray-200 rounded h-10 w-full"></div>
      </div>
      <div className="space-y-2">
        <div className="bg-gray-200 rounded h-4 w-28"></div>
        <div className="bg-gray-200 rounded h-10 w-full"></div>
      </div>
    </div>
    <div className="flex justify-end space-x-2">
      <div className="bg-gray-200 rounded h-10 w-20"></div>
      <div className="bg-gray-200 rounded h-10 w-24"></div>
    </div>
  </div>
)

// Loading states for specific course builder components
export const CourseCardSkeleton: React.FC = () => (
  <div className="animate-pulse border rounded-lg p-6">
    <div className="bg-gray-200 rounded h-32 mb-4"></div>
    <div className="space-y-3">
      <div className="bg-gray-200 rounded h-6 w-3/4"></div>
      <div className="bg-gray-200 rounded h-4 w-full"></div>
      <div className="bg-gray-200 rounded h-4 w-2/3"></div>
      <div className="flex justify-between items-center mt-4">
        <div className="bg-gray-200 rounded h-6 w-20"></div>
        <div className="bg-gray-200 rounded h-8 w-24"></div>
      </div>
    </div>
  </div>
)

export const LessonListSkeleton: React.FC = () => (
  <div className="animate-pulse space-y-4">
    {Array.from({ length: 6 }).map((_, index) => (
      <div key={index} className="border rounded-lg p-4">
        <div className="flex items-center gap-4">
          <div className="bg-gray-200 rounded h-6 w-6"></div>
          <div className="bg-gray-200 rounded h-8 w-8"></div>
          <div className="flex-1 space-y-2">
            <div className="bg-gray-200 rounded h-5 w-2/3"></div>
            <div className="bg-gray-200 rounded h-4 w-1/2"></div>
          </div>
          <div className="bg-gray-200 rounded h-6 w-16"></div>
          <div className="bg-gray-200 rounded h-8 w-8"></div>
        </div>
      </div>
    ))}
  </div>
)

export default LoadingSpinner
