'use client'

import React, { useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered, 
  Link, 
  Quote,
  Heading1,
  Heading2,
  Heading3,
  Code,
  Eye,
  Edit
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  rows?: number
}

export function RichTextEditor({ 
  value, 
  onChange, 
  placeholder = "Write your content...", 
  className,
  rows = 10 
}: RichTextEditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [isPreview, setIsPreview] = useState(false)

  const insertText = (before: string, after: string = '') => {
    const textarea = textareaRef.current
    if (!textarea) return

    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = value.substring(start, end)
    
    const newText = value.substring(0, start) + before + selectedText + after + value.substring(end)
    onChange(newText)

    // Restore cursor position
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length)
    }, 0)
  }

  const formatButtons = [
    {
      icon: Bold,
      label: 'Bold',
      action: () => insertText('**', '**'),
    },
    {
      icon: Italic,
      label: 'Italic',
      action: () => insertText('*', '*'),
    },
    {
      icon: Underline,
      label: 'Underline',
      action: () => insertText('<u>', '</u>'),
    },
    {
      icon: Heading1,
      label: 'Heading 1',
      action: () => insertText('# '),
    },
    {
      icon: Heading2,
      label: 'Heading 2',
      action: () => insertText('## '),
    },
    {
      icon: Heading3,
      label: 'Heading 3',
      action: () => insertText('### '),
    },
    {
      icon: List,
      label: 'Bullet List',
      action: () => insertText('- '),
    },
    {
      icon: ListOrdered,
      label: 'Numbered List',
      action: () => insertText('1. '),
    },
    {
      icon: Quote,
      label: 'Quote',
      action: () => insertText('> '),
    },
    {
      icon: Code,
      label: 'Code',
      action: () => insertText('`', '`'),
    },
    {
      icon: Link,
      label: 'Link',
      action: () => insertText('[', '](url)'),
    },
  ]

  const convertToHtml = (markdown: string) => {
    return markdown
      // Headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Bold
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      // Code
      .replace(/`(.*)`/gim, '<code>$1</code>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')
      // Line breaks
      .replace(/\n/gim, '<br>')
      // Lists
      .replace(/^- (.*$)/gim, '<li>$1</li>')
      .replace(/^1\. (.*$)/gim, '<li>$1</li>')
      // Quotes
      .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
  }

  return (
    <div className={cn("border rounded-lg", className)}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-2 border-b bg-muted/50">
        <div className="flex items-center gap-1 flex-wrap">
          {formatButtons.map((button, index) => (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              onClick={button.action}
              title={button.label}
              type="button"
            >
              <button.icon className="h-4 w-4" />
            </Button>
          ))}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={isPreview ? "ghost" : "default"}
            size="sm"
            onClick={() => setIsPreview(false)}
            type="button"
          >
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
          <Button
            variant={isPreview ? "default" : "ghost"}
            size="sm"
            onClick={() => setIsPreview(true)}
            type="button"
          >
            <Eye className="h-4 w-4 mr-1" />
            Preview
          </Button>
        </div>
      </div>

      {/* Content Area */}
      <div className="p-4">
        {isPreview ? (
          <div
            className="prose prose-sm max-w-none min-h-[200px] p-4 border rounded bg-background"
            style={{
              lineHeight: '1.6',
            }}
            dangerouslySetInnerHTML={{ __html: convertToHtml(value) }}
          />
        ) : (
          <Textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            rows={rows}
            className="border-0 resize-none focus-visible:ring-0 p-0 font-mono text-sm"
            style={{
              lineHeight: '1.6',
              fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
            }}
          />
        )}
      </div>

      {/* Help Text */}
      <div className="px-4 pb-2 text-xs text-muted-foreground">
        Use **bold**, *italic*, # headers, - lists, > quotes, `code`, [links](url)
      </div>
    </div>
  )
}
