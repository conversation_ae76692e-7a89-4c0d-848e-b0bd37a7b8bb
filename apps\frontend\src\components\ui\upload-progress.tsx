'use client'

import React from 'react'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Upload, 
  CheckCircle2, 
  XCircle, 
  AlertCircle, 
  X, 
  Loader2,
  FileText,
  Image
} from 'lucide-react'
import { cn } from '@/lib/utils'

export type UploadStatus = 'idle' | 'uploading' | 'processing' | 'success' | 'error' | 'cancelled'

interface UploadProgressProps {
  status: UploadStatus
  progress?: number
  filename?: string
  filesize?: number
  error?: string | null
  onCancel?: () => void
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
  showDetails?: boolean
}

export function UploadProgress({
  status,
  progress = 0,
  filename,
  filesize,
  error,
  onCancel,
  onRetry,
  onDismiss,
  className,
  showDetails = true
}: UploadProgressProps) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'uploading':
        return <Upload className="h-4 w-4 text-blue-500 animate-pulse" />
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case 'success':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'cancelled':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      default:
        return <FileText className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'uploading':
        return `Uploading... ${progress}%`
      case 'processing':
        return 'Processing...'
      case 'success':
        return 'Upload complete'
      case 'error':
        return 'Upload failed'
      case 'cancelled':
        return 'Upload cancelled'
      default:
        return 'Ready to upload'
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return 'text-blue-600'
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'cancelled':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  const isActive = status === 'uploading' || status === 'processing'
  const isComplete = status === 'success'
  const hasError = status === 'error'
  const isCancelled = status === 'cancelled'

  if (status === 'idle') {
    return null
  }

  return (
    <Card className={cn('overflow-hidden', className)}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className={cn('text-sm font-medium', getStatusColor())}>
                {getStatusText()}
              </span>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-1">
              {isActive && onCancel && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onCancel}
                  className="h-6 w-6 p-0 text-gray-400 hover:text-red-500"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
              
              {hasError && onRetry && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRetry}
                  className="h-6 text-xs px-2"
                >
                  Retry
                </Button>
              )}

              {(isComplete || hasError || isCancelled) && onDismiss && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onDismiss}
                  className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>

          {/* Progress Bar */}
          {(isActive || hasError) && (
            <div className="space-y-1">
              <Progress 
                value={hasError ? 100 : progress} 
                className={cn('h-2', {
                  'bg-red-100': hasError,
                  'bg-blue-100': isActive
                })}
              />
              {isActive && (
                <div className="flex justify-between text-xs text-gray-500">
                  <span>{progress}%</span>
                  {filesize && (
                    <span>{formatFileSize(filesize)}</span>
                  )}
                </div>
              )}
            </div>
          )}

          {/* File Details */}
          {showDetails && filename && (
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              {filename.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i) ? (
                <Image className="h-3 w-3" />
              ) : (
                <FileText className="h-3 w-3" />
              )}
              <span className="truncate">{filename}</span>
            </div>
          )}

          {/* Error Message */}
          {hasError && error && (
            <div className="p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
              {error}
            </div>
          )}

          {/* Success Message */}
          {isComplete && (
            <div className="p-2 bg-green-50 border border-green-200 rounded text-xs text-green-700">
              File uploaded successfully!
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Multiple upload progress component
interface MultiUploadProgressProps {
  uploads: Array<{
    id: string
    filename: string
    status: UploadStatus
    progress?: number
    error?: string
    filesize?: number
  }>
  onCancel?: (id: string) => void
  onRetry?: (id: string) => void
  onDismiss?: (id: string) => void
  className?: string
}

export function MultiUploadProgress({
  uploads,
  onCancel,
  onRetry,
  onDismiss,
  className
}: MultiUploadProgressProps) {
  if (uploads.length === 0) {
    return null
  }

  const activeUploads = uploads.filter(upload => 
    upload.status === 'uploading' || upload.status === 'processing'
  )
  const completedUploads = uploads.filter(upload => upload.status === 'success')
  const failedUploads = uploads.filter(upload => upload.status === 'error')

  return (
    <div className={cn('space-y-2', className)}>
      {/* Summary */}
      {uploads.length > 1 && (
        <div className="text-xs text-gray-500 mb-3">
          {activeUploads.length > 0 && (
            <span>{activeUploads.length} uploading</span>
          )}
          {completedUploads.length > 0 && (
            <span className="text-green-600 ml-2">
              {completedUploads.length} completed
            </span>
          )}
          {failedUploads.length > 0 && (
            <span className="text-red-600 ml-2">
              {failedUploads.length} failed
            </span>
          )}
        </div>
      )}

      {/* Individual Upload Progress */}
      {uploads.map((upload) => (
        <UploadProgress
          key={upload.id}
          status={upload.status}
          progress={upload.progress}
          filename={upload.filename}
          filesize={upload.filesize}
          error={upload.error}
          onCancel={onCancel ? () => onCancel(upload.id) : undefined}
          onRetry={onRetry ? () => onRetry(upload.id) : undefined}
          onDismiss={onDismiss ? () => onDismiss(upload.id) : undefined}
          showDetails={uploads.length === 1}
        />
      ))}
    </div>
  )
}
