import { NavigationItem } from '@/stores/sidebar/useSidebarStore'

export const instituteAdminNavigationConfig: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'LayoutDashboard',
    href: '/admin',
    description: 'Institute overview and analytics',
    badge: 0
  },

  {
    id: 'students',
    label: 'Student Management',
    icon: 'GraduationCap',
    href: '/admin/students',
    description: 'Manage student accounts and enrollment',
    badge: 0,
    children: [
      {
        id: 'students-list',
        label: 'All Students',
        icon: 'Users',
        href: '/institute-admin/students',
        description: 'View all students'
      },
      {
        id: 'students-enrollment',
        label: 'Enrollments',
        icon: 'UserPlus',
        href: '/institute-admin/students/enrollment',
        description: 'Manage course enrollments'
      },
      {
        id: 'students-progress',
        label: 'Progress Tracking',
        icon: 'TrendingUp',
        href: '/institute-admin/students/progress',
        description: 'Track student progress'
      },
      {
        id: 'students-certificates',
        label: 'Certificates',
        icon: 'Award',
        href: '/institute-admin/students/certificates',
        description: 'Manage certificates'
      }
    ]
  },
  {
    id: 'staff',
    label: 'Staff Management',
    icon: 'Users',
    href: '/admin/staff',
    description: 'Manage institute staff members',
    badge: 0,
    permissions: ['institute_admin', 'branch_manager'],

  },

  {
    id: 'courses',
    label: 'Course Management',
    icon: 'BookOpen',
    href: '/admin/courses',
    description: 'Create and manage courses',
    badge: 0,
    children: [
      {
        id: 'courses-list',
        label: 'All Courses',
        icon: 'BookOpen',
        href: '/admin/courses',
        description: 'View and manage all courses'
      },
      {
        id: 'course-builder',
        label: 'Course Builder',
        icon: 'Edit3',
        href: '/admin/course-builder',
        description: 'Build course content'
      },
      {
        id: 'courses-analytics',
        label: 'Course Analytics',
        icon: 'BarChart3',
        href: '/admin/courses/analytics',
        description: 'Course performance metrics'
      },
      {
        id: 'courses-categories',
        label: 'Categories',
        icon: 'FolderOpen',
        href: '/admin/courses/categories',
        description: 'Manage course categories'
      }
    ]
  },

  {
    id: 'live-classes',
    label: 'Live Classes',
    icon: 'Video',
    href: '/institute-admin/live-classes',
    description: 'Manage live classes and sessions',
    badge: 2,
    children: [
      {
        id: 'live-classes-schedule',
        label: 'Class Schedule',
        icon: 'Calendar',
        href: '/institute-admin/live-classes/schedule',
        description: 'Manage class schedules'
      },
      {
        id: 'live-classes-rooms',
        label: 'Virtual Rooms',
        icon: 'Monitor',
        href: '/institute-admin/live-classes/rooms',
        description: 'Manage virtual classrooms'
      },
      {
        id: 'live-classes-recordings',
        label: 'Recordings',
        icon: 'PlayCircle',
        href: '/institute-admin/live-classes/recordings',
        description: 'Class recordings'
      }
    ]
  },
  {
    id: 'exams',
    label: 'Exams & Assessments',
    icon: 'FileText',
    href: '/institute-admin/exams',
    description: 'Manage exams and assessments',
    badge: 0,
    children: [
      {
        id: 'exams-list',
        label: 'All Exams',
        icon: 'List',
        href: '/institute-admin/exams',
        description: 'View all exams'
      },
      {
        id: 'exams-create',
        label: 'Create Exam',
        icon: 'Plus',
        href: '/institute-admin/exams/create',
        description: 'Create new exam'
      },
      {
        id: 'exams-results',
        label: 'Results',
        icon: 'BarChart3',
        href: '/institute-admin/exams/results',
        description: 'Exam results and analytics'
      },
      {
        id: 'exams-question-bank',
        label: 'Question Bank',
        icon: 'HelpCircle',
        href: '/institute-admin/exams/questions',
        description: 'Manage question bank'
      }
    ]
  },
  {
    id: 'billing',
    label: 'Billing & Payments',
    icon: 'CreditCard',
    href: '/institute-admin/billing',
    description: 'Manage billing and payments',
    badge: 0,

    children: [
      {
        id: 'billing-overview',
        label: 'Billing Overview',
        icon: 'DollarSign',
        href: '/institute-admin/billing',
        description: 'Revenue and billing overview'
      },
      {
        id: 'billing-transactions',
        label: 'Transactions',
        icon: 'Receipt',
        href: '/institute-admin/billing/transactions',
        description: 'Payment transactions'
      },
      {
        id: 'billing-subscriptions',
        label: 'Subscriptions',
        icon: 'CreditCard',
        href: '/institute-admin/billing/subscriptions',
        description: 'Student subscriptions'
      },
      {
        id: 'billing-reports',
        label: 'Financial Reports',
        icon: 'FileText',
        href: '/institute-admin/billing/reports',
        description: 'Financial reports'
      }
    ]
  },
  {
    id: 'marketplace',
    label: 'Marketplace',
    icon: 'ShoppingBag',
    href: '/institute-admin/marketplace',
    description: 'Manage course marketplace',
    badge: 0,

    children: [
      {
        id: 'marketplace-courses',
        label: 'Published Courses',
        icon: 'BookOpen',
        href: '/institute-admin/marketplace/courses',
        description: 'Courses in marketplace'
      },
      {
        id: 'marketplace-orders',
        label: 'Orders',
        icon: 'ShoppingCart',
        href: '/institute-admin/marketplace/orders',
        description: 'Course orders'
      },
      {
        id: 'marketplace-reviews',
        label: 'Reviews & Ratings',
        icon: 'Star',
        href: '/institute-admin/marketplace/reviews',
        description: 'Course reviews'
      },
      {
        id: 'marketplace-analytics',
        label: 'Sales Analytics',
        icon: 'TrendingUp',
        href: '/institute-admin/marketplace/analytics',
        description: 'Sales performance'
      }
    ]
  },
  {
    id: 'blog',
    label: 'Blog Management',
    icon: 'PenTool',
    href: '/admin/blog',
    description: 'Manage institute blog and content',
    badge: 0,

    permissions: ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'],
    children: [
      {
        id: 'blog-dashboard',
        label: 'Blog Dashboard',
        icon: 'BarChart3',
        href: '/admin/blog',
        description: 'Blog analytics and overview'
      },
      {
        id: 'blog-posts',
        label: 'All Posts',
        icon: 'FileText',
        href: '/admin/blog/posts',
        description: 'Manage all blog posts'
      },
      {
        id: 'blog-create',
        label: 'Create Post',
        icon: 'Plus',
        href: '/admin/blog/posts/new',
        description: 'Write a new blog post'
      },
      {
        id: 'blog-categories',
        label: 'Categories',
        icon: 'FolderOpen',
        href: '/admin/blog/categories',
        description: 'Organize content categories'
      },
      {
        id: 'blog-drafts',
        label: 'Drafts',
        icon: 'Archive',
        href: '/admin/blog/drafts',
        description: 'Draft posts'
      },
      {
        id: 'blog-scheduled',
        label: 'Scheduled',
        icon: 'Calendar',
        href: '/admin/blog/scheduled',
        description: 'Scheduled posts'
      },
      {
        id: 'blog-analytics',
        label: 'Blog Analytics',
        icon: 'TrendingUp',
        href: '/admin/blog/analytics',
        description: 'Detailed blog analytics'
      },
      {
        id: 'blog-settings',
        label: 'Blog Settings',
        icon: 'Settings',
        href: '/admin/blog/settings',
        description: 'Blog configuration'
      }
    ]
  },
  {
    id: 'analytics',
    label: 'Analytics & Reports',
    icon: 'BarChart3',
    href: '/institute-admin/analytics',
    description: 'Institute analytics and insights',
    badge: 0,

    children: [
      {
        id: 'analytics-overview',
        label: 'Overview',
        icon: 'BarChart3',
        href: '/institute-admin/analytics',
        description: 'Institute overview metrics'
      },
      {
        id: 'analytics-students',
        label: 'Student Analytics',
        icon: 'Users',
        href: '/institute-admin/analytics/students',
        description: 'Student engagement metrics'
      },
      {
        id: 'analytics-courses',
        label: 'Course Analytics',
        icon: 'BookOpen',
        href: '/institute-admin/analytics/courses',
        description: 'Course performance metrics'
      },
      {
        id: 'analytics-revenue',
        label: 'Revenue Analytics',
        icon: 'DollarSign',
        href: '/institute-admin/analytics/revenue',
        description: 'Revenue and financial metrics'
      }
    ]
  },
  {
    id: 'settings',
    label: 'Institute Settings',
    icon: 'Settings',
    href: '/admin/settings',
    description: 'Institute configuration and settings',
    badge: 0,

    permissions: ['institute_admin', 'branch_manager', 'institute_staff'],
    children: [
      {
        id: 'settings-general',
        label: 'General Settings',
        icon: 'Settings',
        href: '/institute-admin/settings/general',
        description: 'Basic institute settings'
      },
      {
        id: 'settings-domain',
        label: 'Domain Settings',
        icon: 'Globe',
        href: '/institute-admin/settings/domain',
        description: 'Custom domain configuration'
      },
      {
        id: 'settings-theme',
        label: 'Theme & Branding',
        icon: 'Palette',
        href: '/institute-admin/settings/theme',
        description: 'Customize appearance'
      },
      {
        id: 'settings-payment',
        label: 'Payment Gateways',
        icon: 'CreditCard',
        href: '/admin/settings/payment-gateways',
        description: 'Configure payment gateways',
        permissions: ['institute_admin', 'branch_manager']
      },
      {
        id: 'settings-notifications',
        label: 'Notifications',
        icon: 'Bell',
        href: '/institute-admin/settings/notifications',
        description: 'Notification preferences'
      },
      {
        id: 'settings-integrations',
        label: 'Integrations',
        icon: 'Plug',
        href: '/institute-admin/settings/integrations',
        description: 'Third-party integrations'
      }
    ]
  }
]

// Quick access items for institute admin
export const instituteAdminQuickAccess = [
  {
    id: 'quick-live-classes',
    label: 'Live Classes Today',
    icon: 'Video',
    href: '/institute-admin/live-classes/schedule',
    count: 2
  },
  {
    id: 'quick-new-enrollments',
    label: 'New Enrollments',
    icon: 'UserPlus',
    href: '/institute-admin/students/enrollment',
    count: 8
  },
  {
    id: 'quick-pending-exams',
    label: 'Pending Exams',
    icon: 'FileText',
    href: '/institute-admin/exams',
    count: 3
  },
  {
    id: 'quick-revenue',
    label: 'Today\'s Revenue',
    icon: 'DollarSign',
    href: '/institute-admin/billing',
    value: '$1,250'
  }
]

// Favorite items for institute admin
export const instituteAdminFavorites = [
  'courses',
  'students',
  'live-classes',
  'analytics'
]

// Recent items for institute admin (this would be dynamic)
export const instituteAdminRecentItems = [
  {
    id: 'recent-1',
    label: 'Course Analytics',
    href: '/institute-admin/courses/analytics',
    timestamp: new Date().toISOString()
  },
  {
    id: 'recent-2',
    label: 'Student Progress',
    href: '/institute-admin/students/progress',
    timestamp: new Date(Date.now() - 1800000).toISOString()
  },
  {
    id: 'recent-3',
    label: 'Live Class Schedule',
    href: '/institute-admin/live-classes/schedule',
    timestamp: new Date(Date.now() - 3600000).toISOString()
  }
]

export default instituteAdminNavigationConfig
