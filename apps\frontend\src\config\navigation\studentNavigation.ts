import { NavigationItem } from '@/stores/sidebar/useSidebarStore'

export const studentNavigationConfig: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'LayoutDashboard',
    href: '/student',
    description: 'Your learning overview',
    badge: 0,
    section: 'main'
  },
  {
    id: 'my-courses',
    label: 'My Courses',
    icon: 'BookOpen',
    href: '/student/courses',
    description: 'Your enrolled courses',
    badge: 2,
    section: 'main',
    children: [
      {
        id: 'courses-active',
        label: 'Active Courses',
        icon: 'Play',
        href: '/student/courses/active',
        description: 'Currently enrolled courses'
      },
      {
        id: 'courses-completed',
        label: 'Completed Courses',
        icon: 'CheckCircle',
        href: '/student/courses/completed',
        description: 'Finished courses'
      },
      {
        id: 'courses-favorites',
        label: 'Favorites',
        icon: 'Heart',
        href: '/student/courses/favorites',
        description: 'Your favorite courses'
      },
      {
        id: 'courses-downloads',
        label: 'Downloads',
        icon: 'Download',
        href: '/student/courses/downloads',
        description: 'Downloaded content'
      }
    ]
  },
  {
    id: 'marketplace',
    label: 'Course Marketplace',
    icon: 'ShoppingBag',
    href: '/student/marketplace',
    description: 'Browse and purchase courses',
    badge: 0,
    section: 'main',
    children: [
      {
        id: 'marketplace-browse',
        label: 'Browse Courses',
        icon: 'Search',
        href: '/student/marketplace',
        description: 'Explore available courses'
      },
      {
        id: 'marketplace-categories',
        label: 'Categories',
        icon: 'Tag',
        href: '/student/marketplace/categories',
        description: 'Browse by category'
      },
      {
        id: 'marketplace-wishlist',
        label: 'Wishlist',
        icon: 'Heart',
        href: '/student/marketplace/wishlist',
        description: 'Your course wishlist'
      },
      {
        id: 'marketplace-cart',
        label: 'Shopping Cart',
        icon: 'ShoppingCart',
        href: '/student/marketplace/cart',
        description: 'Items in your cart',
        badge: 1
      }
    ]
  },
  {
    id: 'live-classes',
    label: 'Live Classes',
    icon: 'Video',
    href: '/student/live-classes',
    description: 'Attend live sessions',
    badge: 1,
    section: 'main',
    children: [
      {
        id: 'live-classes-schedule',
        label: 'Class Schedule',
        icon: 'Calendar',
        href: '/student/live-classes/schedule',
        description: 'Your class schedule'
      },
      {
        id: 'live-classes-upcoming',
        label: 'Upcoming Classes',
        icon: 'Clock',
        href: '/student/live-classes/upcoming',
        description: 'Classes starting soon',
        badge: 1
      },
      {
        id: 'live-classes-recordings',
        label: 'Recorded Classes',
        icon: 'PlayCircle',
        href: '/student/live-classes/recordings',
        description: 'Watch recorded sessions'
      }
    ]
  },
  {
    id: 'assignments',
    label: 'Assignments & Exams',
    icon: 'FileText',
    href: '/student/assignments',
    description: 'Your assignments and tests',
    badge: 3,
    section: 'main',
    children: [
      {
        id: 'assignments-pending',
        label: 'Pending Assignments',
        icon: 'Clock',
        href: '/student/assignments/pending',
        description: 'Assignments to complete',
        badge: 2
      },
      {
        id: 'assignments-submitted',
        label: 'Submitted',
        icon: 'CheckCircle',
        href: '/student/assignments/submitted',
        description: 'Completed assignments'
      },
      {
        id: 'assignments-exams',
        label: 'Exams',
        icon: 'FileText',
        href: '/student/assignments/exams',
        description: 'Scheduled exams',
        badge: 1
      },
      {
        id: 'assignments-results',
        label: 'Results',
        icon: 'BarChart3',
        href: '/student/assignments/results',
        description: 'Your exam results'
      }
    ]
  },
  {
    id: 'progress',
    label: 'Progress & Analytics',
    icon: 'TrendingUp',
    href: '/student/progress',
    description: 'Track your learning progress',
    badge: 0,
    section: 'main',
    children: [
      {
        id: 'progress-overview',
        label: 'Learning Overview',
        icon: 'BarChart3',
        href: '/student/progress',
        description: 'Your learning statistics'
      },
      {
        id: 'progress-achievements',
        label: 'Achievements',
        icon: 'Award',
        href: '/student/progress/achievements',
        description: 'Badges and achievements'
      },
      {
        id: 'progress-certificates',
        label: 'Certificates',
        icon: 'Award',
        href: '/student/progress/certificates',
        description: 'Your certificates'
      },
      {
        id: 'progress-goals',
        label: 'Learning Goals',
        icon: 'Target',
        href: '/student/progress/goals',
        description: 'Set and track goals'
      }
    ]
  },
  {
    id: 'discussions',
    label: 'Discussions',
    icon: 'MessageSquare',
    href: '/student/discussions',
    description: 'Course discussions and forums',
    badge: 5,
    section: 'main',
    children: [
      {
        id: 'discussions-forums',
        label: 'Course Forums',
        icon: 'MessageSquare',
        href: '/student/discussions/forums',
        description: 'Course discussion forums'
      },
      {
        id: 'discussions-qa',
        label: 'Q&A',
        icon: 'HelpCircle',
        href: '/student/discussions/qa',
        description: 'Ask questions'
      },
      {
        id: 'discussions-study-groups',
        label: 'Study Groups',
        icon: 'Users',
        href: '/student/discussions/study-groups',
        description: 'Join study groups'
      }
    ]
  },
  {
    id: 'library',
    label: 'Digital Library',
    icon: 'Library',
    href: '/student/library',
    description: 'Access learning resources',
    badge: 0,
    section: 'main',
    children: [
      {
        id: 'library-books',
        label: 'E-Books',
        icon: 'Book',
        href: '/student/library/books',
        description: 'Digital textbooks'
      },
      {
        id: 'library-articles',
        label: 'Articles',
        icon: 'FileText',
        href: '/student/library/articles',
        description: 'Research articles'
      },
      {
        id: 'library-videos',
        label: 'Video Library',
        icon: 'PlayCircle',
        href: '/student/library/videos',
        description: 'Educational videos'
      },
      {
        id: 'library-notes',
        label: 'My Notes',
        icon: 'StickyNote',
        href: '/student/library/notes',
        description: 'Your personal notes'
      }
    ]
  },
  {
    id: 'billing',
    label: 'Billing & Payments',
    icon: 'CreditCard',
    href: '/student/billing',
    description: 'Manage payments and subscriptions',
    badge: 0,
    section: 'main',
    children: [
      {
        id: 'billing-overview',
        label: 'Payment History',
        icon: 'Receipt',
        href: '/student/billing',
        description: 'Your payment history'
      },
      {
        id: 'billing-subscriptions',
        label: 'Subscriptions',
        icon: 'CreditCard',
        href: '/student/billing/subscriptions',
        description: 'Active subscriptions'
      },
      {
        id: 'billing-invoices',
        label: 'Invoices',
        icon: 'FileText',
        href: '/student/billing/invoices',
        description: 'Download invoices'
      },
      {
        id: 'billing-payment-methods',
        label: 'Payment Methods',
        icon: 'Wallet',
        href: '/student/billing/payment-methods',
        description: 'Manage payment methods'
      }
    ]
  },
  {
    id: 'settings',
    label: 'Account Settings',
    icon: 'Settings',
    href: '/student/settings',
    description: 'Manage your account',
    badge: 0,
    section: 'settings',
    children: [
      {
        id: 'settings-profile',
        label: 'Profile Settings',
        icon: 'User',
        href: '/student/settings/profile',
        description: 'Update your profile'
      },
      {
        id: 'settings-preferences',
        label: 'Learning Preferences',
        icon: 'Settings',
        href: '/student/settings/preferences',
        description: 'Customize your experience'
      },
      {
        id: 'settings-notifications',
        label: 'Notifications',
        icon: 'Bell',
        href: '/student/settings/notifications',
        description: 'Notification preferences'
      },
      {
        id: 'settings-privacy',
        label: 'Privacy & Security',
        icon: 'Shield',
        href: '/student/settings/privacy',
        description: 'Privacy settings'
      },
      {
        id: 'settings-downloads',
        label: 'Download Settings',
        icon: 'Download',
        href: '/student/settings/downloads',
        description: 'Offline content settings'
      }
    ]
  }
]

// Quick access items for students
export const studentQuickAccess = [
  {
    id: 'quick-upcoming-class',
    label: 'Next Live Class',
    icon: 'Video',
    href: '/student/live-classes/upcoming',
    time: '2:30 PM'
  },
  {
    id: 'quick-pending-assignments',
    label: 'Pending Assignments',
    icon: 'FileText',
    href: '/student/assignments/pending',
    count: 2
  },
  {
    id: 'quick-new-messages',
    label: 'New Messages',
    icon: 'MessageSquare',
    href: '/student/discussions',
    count: 5
  },
  {
    id: 'quick-progress',
    label: 'Course Progress',
    icon: 'TrendingUp',
    href: '/student/progress',
    value: '78%'
  }
]

// Favorite items for students
export const studentFavorites = [
  'my-courses',
  'live-classes',
  'assignments',
  'progress'
]

// Recent items for students (this would be dynamic)
export const studentRecentItems = [
  {
    id: 'recent-1',
    label: 'JavaScript Fundamentals',
    href: '/student/courses/javascript-fundamentals',
    timestamp: new Date().toISOString()
  },
  {
    id: 'recent-2',
    label: 'Assignment: React Components',
    href: '/student/assignments/react-components',
    timestamp: new Date(Date.now() - 1800000).toISOString()
  },
  {
    id: 'recent-3',
    label: 'Live Class: Advanced CSS',
    href: '/student/live-classes/advanced-css',
    timestamp: new Date(Date.now() - 3600000).toISOString()
  }
]

export default studentNavigationConfig
