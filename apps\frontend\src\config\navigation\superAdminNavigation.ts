import { NavigationItem } from '@/stores/sidebar/useSidebarStore'

export const superAdminNavigationConfig: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'LayoutDashboard',
    href: '/super-admin',
    description: 'Overview and analytics'
  },
  {
    id: 'institutes',
    label: 'Institute Management',
    icon: 'Building2',
    href: '/super-admin/institutes',
    description: 'Manage institutes and verification'
  },
  {
    id: 'users',
    label: 'User Management',
    icon: 'Users',
    href: '/super-admin/users',
    description: 'Manage all platform users'
  },
  {
    id: 'themes',
    label: 'Theme Management',
    icon: 'Palette',
    href: '/super-admin/themes',
    description: 'Platform and institute themes'
  },
  {
    id: 'gateway-management',
    label: 'Gateway Management',
    icon: 'CreditCard',
    href: '/super-admin/gateway-management',
    description: 'Manage payment gateway providers and configurations'
  },
  {
    id: 'platform-blog',
    label: 'Platform Blog',
    icon: 'FileText',
    href: '/super-admin/platform-blog',
    description: 'Manage platform-wide blog posts and announcements',
    children: [
      {
        id: 'platform-blog-posts',
        label: 'All Posts',
        icon: 'FileText',
        href: '/super-admin/platform-blog',
        description: 'View and manage all platform blog posts'
      },
      {
        id: 'platform-blog-create',
        label: 'Create Post',
        icon: 'Plus',
        href: '/super-admin/platform-blog/create',
        description: 'Create a new platform blog post'
      },
      {
        id: 'platform-blog-categories',
        label: 'Categories',
        icon: 'Tag',
        href: '/super-admin/platform-blog/categories',
        description: 'Manage blog post categories'
      },
      {
        id: 'platform-blog-analytics',
        label: 'Analytics',
        icon: 'BarChart3',
        href: '/super-admin/platform-blog/analytics',
        description: 'View blog performance analytics'
      }
    ]
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: 'BarChart3',
    href: '/super-admin/analytics',
    description: 'Platform analytics and insights'
  },
  {
    id: 'role-permissions',
    label: 'Roles & Permissions',
    icon: 'Shield',
    href: '/super-admin/role-permissions',
    description: 'Manage user roles and permissions'
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: 'Settings',
    href: '/super-admin/settings',
    description: 'Platform configuration and settings',
    children: [
      {
        id: 'settings-platform',
        label: 'Platform Settings',
        icon: 'Settings',
        href: '/super-admin/settings/platform',
        description: 'General platform configuration'
      },
      {
        id: 'settings-domains',
        label: 'Domain Management',
        icon: 'Globe',
        href: '/super-admin/settings/domains',
        description: 'Manage custom domain requests and DNS'
      }
    ]
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: 'User',
    href: '#',
    description: 'Manage your profile settings',
    isModal: true
  }
]