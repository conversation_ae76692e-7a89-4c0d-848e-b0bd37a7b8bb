'use client'

import { useState, useCallback } from 'react'

export interface Toast {
  id: string
  title: string
  description?: string
  variant?: 'default' | 'destructive' | 'success'
  duration?: number
}

interface ToastState {
  toasts: Toast[]
}

let toastCount = 0

export function useToast() {
  const [state, setState] = useState<ToastState>({ toasts: [] })

  const toast = useCallback(
    ({ title, description, variant = 'default', duration = 5000 }: Omit<Toast, 'id'>) => {
      const id = (++toastCount).toString()
      const newToast: Toast = {
        id,
        title,
        description,
        variant,
        duration
      }

      setState((prevState) => ({
        toasts: [...prevState.toasts, newToast]
      }))

      // Auto-dismiss after duration
      if (duration > 0) {
        setTimeout(() => {
          dismiss(id)
        }, duration)
      }

      return {
        id,
        dismiss: () => dismiss(id),
        update: (updates: Partial<Toast>) => update(id, updates)
      }
    },
    []
  )

  const dismiss = useCallback((toastId: string) => {
    setState((prevState) => ({
      toasts: prevState.toasts.filter((toast) => toast.id !== toastId)
    }))
  }, [])

  const update = useCallback((toastId: string, updates: Partial<Toast>) => {
    setState((prevState) => ({
      toasts: prevState.toasts.map((toast) =>
        toast.id === toastId ? { ...toast, ...updates } : toast
      )
    }))
  }, [])

  const dismissAll = useCallback(() => {
    setState({ toasts: [] })
  }, [])

  return {
    toast,
    dismiss,
    update,
    dismissAll,
    toasts: state.toasts
  }
}

// Toast component for rendering
export function ToastProvider({ children }: { children: React.ReactNode }) {
  return children
}

export default useToast
