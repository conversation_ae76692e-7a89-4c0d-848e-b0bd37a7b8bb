import { useState, useCallback } from 'react'
import { api } from '@/lib/api'
import { useToast } from '@/hooks/use-toast'

export interface UploadedFile {
  id: string
  originalName: string
  filename: string
  mimeType: string
  size: number
  category: string
  url: string
  cdnUrl?: string
  path: string
  uploadedAt: Date
}

export interface UploadOptions {
  generateThumbnail?: boolean
  compressImage?: boolean
  maxWidth?: number
  maxHeight?: number
  quality?: number
  preserveOriginal?: boolean
  onProgress?: (progress: number) => void
  signal?: AbortSignal
}

export interface UploadResult {
  success: boolean
  files?: UploadedFile[]
  error?: string
}

export interface UploadUrlResult {
  success: boolean
  uploadUrl?: string
  file?: UploadedFile
  error?: string
}

/**
 * Custom hook for file upload operations
 * Provides methods for uploading files with progress tracking and error handling
 */
export const useFileUpload = () => {
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const { toast } = useToast()

  /**
   * Generate upload URL for direct client uploads
   */
  const generateUploadUrl = useCallback(async (
    filename: string,
    mimeType: string,
    size: number,
    options: Omit<UploadOptions, 'onProgress' | 'signal'> = {}
  ): Promise<UploadUrlResult> => {
    try {
      const response = await api.post('/admin/files/upload-url', {
        filename,
        mimeType,
        size,
        options
      })

      if (response.data.success) {
        return {
          success: true,
          uploadUrl: response.data.data.uploadUrl,
          file: response.data.data.file
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Failed to generate upload URL'
        }
      }
    } catch (error: any) {
      console.error('Error generating upload URL:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to generate upload URL'
      }
    }
  }, [])

  /**
   * Upload files directly to the server
   */
  const uploadFiles = useCallback(async (
    files: File[],
    options: UploadOptions = {}
  ): Promise<UploadResult> => {
    if (files.length === 0) {
      return {
        success: false,
        error: 'No files provided'
      }
    }

    setUploading(true)
    setProgress(0)

    try {
      const formData = new FormData()
      
      files.forEach((file) => {
        formData.append('files', file)
      })

      formData.append('options', JSON.stringify(options))

      const response = await api.post('/admin/files/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            setProgress(percentCompleted)
            options.onProgress?.(percentCompleted)
          }
        },
        signal: options.signal
      })

      if (response.data.success) {
        return {
          success: true,
          files: response.data.data.uploaded
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Upload failed'
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return {
          success: false,
          error: 'Upload cancelled'
        }
      }

      console.error('Error uploading files:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Upload failed'
      }
    } finally {
      setUploading(false)
      setProgress(0)
    }
  }, [])

  /**
   * Upload single file with progress tracking
   */
  const uploadFile = useCallback(async (
    file: File,
    options: UploadOptions = {}
  ): Promise<UploadResult> => {
    const result = await uploadFiles([file], options)
    return {
      ...result,
      files: result.files ? [result.files[0]] : undefined
    }
  }, [uploadFiles])

  /**
   * Generate download URL for a file
   */
  const generateDownloadUrl = useCallback(async (
    filePath: string,
    expiresIn: number = 3600
  ): Promise<{ success: boolean; url?: string; error?: string }> => {
    try {
      const response = await api.post('/admin/files/download-url', {
        filePath,
        expiresIn
      })

      if (response.data.success) {
        return {
          success: true,
          url: response.data.data.downloadUrl
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Failed to generate download URL'
        }
      }
    } catch (error: any) {
      console.error('Error generating download URL:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to generate download URL'
      }
    }
  }, [])

  /**
   * Delete a file
   */
  const deleteFile = useCallback(async (
    filePath: string
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const encodedPath = encodeURIComponent(filePath)
      const response = await api.delete(`/admin/files/${encodedPath}`)

      if (response.data.success) {
        toast({
          title: 'Success',
          description: 'File deleted successfully'
        })
        return { success: true }
      } else {
        return {
          success: false,
          error: response.data.error || 'Failed to delete file'
        }
      }
    } catch (error: any) {
      console.error('Error deleting file:', error)
      const errorMessage = error.response?.data?.error || 'Failed to delete file'
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      })
      return {
        success: false,
        error: errorMessage
      }
    }
  }, [toast])

  /**
   * Get file metadata
   */
  const getFileMetadata = useCallback(async (
    filePath: string
  ): Promise<{ success: boolean; metadata?: any; error?: string }> => {
    try {
      const response = await api.post('/admin/files/metadata', {
        filePath
      })

      if (response.data.success) {
        return {
          success: true,
          metadata: response.data.data
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Failed to get file metadata'
        }
      }
    } catch (error: any) {
      console.error('Error getting file metadata:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get file metadata'
      }
    }
  }, [])

  /**
   * Bulk delete files
   */
  const bulkDeleteFiles = useCallback(async (
    filePaths: string[]
  ): Promise<{ success: boolean; summary?: any; error?: string }> => {
    try {
      const response = await api.post('/admin/files/bulk', {
        operation: 'delete',
        filePaths
      })

      if (response.data.success) {
        toast({
          title: 'Success',
          description: `${response.data.data.summary.successful} files deleted successfully`
        })
        return {
          success: true,
          summary: response.data.data.summary
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Bulk delete failed'
        }
      }
    } catch (error: any) {
      console.error('Error bulk deleting files:', error)
      const errorMessage = error.response?.data?.error || 'Bulk delete failed'
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      })
      return {
        success: false,
        error: errorMessage
      }
    }
  }, [toast])

  /**
   * Check storage health
   */
  const checkStorageHealth = useCallback(async (): Promise<{
    success: boolean
    data?: any
    error?: string
  }> => {
    try {
      const response = await api.get('/admin/files/health')

      return {
        success: response.data.success,
        data: response.data.data,
        error: response.data.error
      }
    } catch (error: any) {
      console.error('Error checking storage health:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to check storage health'
      }
    }
  }, [])

  /**
   * Validate file before upload
   */
  const validateFile = useCallback((
    file: File,
    maxSize: number = 100 * 1024 * 1024, // 100MB
    allowedTypes: string[] = ['image/*', 'video/*', 'audio/*', 'application/pdf']
  ): { valid: boolean; error?: string } => {
    // Check file size
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size exceeds maximum allowed size of ${Math.round(maxSize / 1024 / 1024)}MB`
      }
    }

    // Check file type
    const isAllowed = allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        return file.type.startsWith(type.slice(0, -1))
      }
      return file.type === type
    })

    if (!isAllowed) {
      return {
        valid: false,
        error: 'File type not allowed'
      }
    }

    return { valid: true }
  }, [])

  return {
    // State
    uploading,
    progress,

    // Methods
    generateUploadUrl,
    uploadFiles,
    uploadFile,
    generateDownloadUrl,
    deleteFile,
    getFileMetadata,
    bulkDeleteFiles,
    checkStorageHealth,
    validateFile
  }
}

export default useFileUpload
