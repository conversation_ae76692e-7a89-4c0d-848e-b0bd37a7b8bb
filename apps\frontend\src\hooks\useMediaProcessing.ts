import { useState, useCallback } from 'react'
import { api } from '@/lib/api'
import { useToast } from '@/hooks/use-toast'

export interface ProcessingOptions {
  generateThumbnail?: boolean
  optimizeImage?: boolean
  transcodeVideo?: boolean
  extractAudio?: boolean
  generatePreview?: boolean
  quality?: number
  maxWidth?: number
  maxHeight?: number
  videoFormats?: string[]
  audioFormats?: string[]
}

export interface ProcessedMedia {
  original: {
    url: string
    path: string
    size: number
    dimensions?: { width: number; height: number }
    duration?: number
  }
  optimized?: {
    url: string
    path: string
    size: number
    dimensions?: { width: number; height: number }
    quality: number
  }
  thumbnail?: {
    url: string
    path: string
    size: number
    dimensions: { width: number; height: number }
  }
  transcoded?: Array<{
    format: string
    url: string
    path: string
    size: number
    bitrate?: number
    resolution?: string
  }>
  preview?: {
    url: string
    path: string
    duration: number
  }
  metadata: {
    format: string
    codec?: string
    bitrate?: number
    sampleRate?: number
    channels?: number
    duration?: number
    frameRate?: number
  }
}

export interface ProcessingResult {
  success: boolean
  processed?: ProcessedMedia
  error?: string
  jobId?: string
}

export interface JobStatusResult {
  success: boolean
  data?: {
    jobId: string
    status: string
    progress: number
  }
  error?: string
}

/**
 * Custom hook for media processing operations
 * Provides methods for processing images, videos, and audio files
 */
export const useMediaProcessing = () => {
  const [processing, setProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const { toast } = useToast()

  /**
   * Process media file with specified options
   */
  const processMedia = useCallback(async (
    filePath: string,
    mimeType: string,
    options: ProcessingOptions = {}
  ): Promise<ProcessingResult> => {
    setProcessing(true)
    setProgress(0)

    try {
      const response = await api.post('/admin/media/process', {
        filePath,
        mimeType,
        options
      })

      if (response.data.success) {
        return {
          success: true,
          processed: response.data.data.processed,
          jobId: response.data.data.jobId
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Processing failed'
        }
      }
    } catch (error: any) {
      console.error('Error processing media:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Processing failed'
      }
    } finally {
      setProcessing(false)
    }
  }, [])

  /**
   * Get processing job status
   */
  const getJobStatus = useCallback(async (
    jobId: string
  ): Promise<JobStatusResult> => {
    try {
      const response = await api.get(`/admin/media/job/${jobId}`)

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Failed to get job status'
        }
      }
    } catch (error: any) {
      console.error('Error getting job status:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get job status'
      }
    }
  }, [])

  /**
   * Batch process multiple media files
   */
  const batchProcessMedia = useCallback(async (
    files: Array<{ filePath: string; mimeType: string; options?: ProcessingOptions }>,
    globalOptions: ProcessingOptions = {}
  ): Promise<{
    success: boolean
    results?: any[]
    summary?: { total: number; successful: number; failed: number }
    error?: string
  }> => {
    setProcessing(true)
    setProgress(0)

    try {
      const response = await api.post('/admin/media/batch-process', {
        files,
        options: globalOptions
      })

      if (response.data.success) {
        return {
          success: true,
          results: response.data.data.results,
          summary: response.data.data.summary
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Batch processing failed'
        }
      }
    } catch (error: any) {
      console.error('Error batch processing media:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Batch processing failed'
      }
    } finally {
      setProcessing(false)
    }
  }, [])

  /**
   * Generate thumbnail for media file
   */
  const generateThumbnail = useCallback(async (
    filePath: string,
    mimeType: string,
    options: ProcessingOptions = {}
  ): Promise<{
    success: boolean
    thumbnail?: any
    jobId?: string
    error?: string
  }> => {
    try {
      const response = await api.post('/admin/media/thumbnail', {
        filePath,
        mimeType,
        options
      })

      if (response.data.success) {
        toast({
          title: 'Thumbnail generated',
          description: 'Thumbnail has been generated successfully'
        })
        return {
          success: true,
          thumbnail: response.data.data.thumbnail,
          jobId: response.data.data.jobId
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Thumbnail generation failed'
        }
      }
    } catch (error: any) {
      console.error('Error generating thumbnail:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Thumbnail generation failed'
      }
    }
  }, [toast])

  /**
   * Optimize image
   */
  const optimizeImage = useCallback(async (
    filePath: string,
    options: ProcessingOptions = {}
  ): Promise<{
    success: boolean
    optimized?: any
    jobId?: string
    error?: string
  }> => {
    try {
      const response = await api.post('/admin/media/optimize-image', {
        filePath,
        options
      })

      if (response.data.success) {
        toast({
          title: 'Image optimized',
          description: 'Image has been optimized successfully'
        })
        return {
          success: true,
          optimized: response.data.data.optimized,
          jobId: response.data.data.jobId
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Image optimization failed'
        }
      }
    } catch (error: any) {
      console.error('Error optimizing image:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Image optimization failed'
      }
    }
  }, [toast])

  /**
   * Get media metadata
   */
  const getMediaMetadata = useCallback(async (
    filePath: string,
    mimeType: string
  ): Promise<{
    success: boolean
    metadata?: any
    error?: string
  }> => {
    try {
      const response = await api.post('/admin/media/metadata', {
        filePath,
        mimeType
      })

      if (response.data.success) {
        return {
          success: true,
          metadata: response.data.data
        }
      } else {
        return {
          success: false,
          error: response.data.error || 'Failed to get media metadata'
        }
      }
    } catch (error: any) {
      console.error('Error getting media metadata:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get media metadata'
      }
    }
  }, [])

  /**
   * Check media processing health
   */
  const checkProcessingHealth = useCallback(async (): Promise<{
    success: boolean
    data?: any
    error?: string
  }> => {
    try {
      const response = await api.get('/admin/media/health')

      return {
        success: response.data.success,
        data: response.data.data,
        error: response.data.error
      }
    } catch (error: any) {
      console.error('Error checking processing health:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to check processing health'
      }
    }
  }, [])

  /**
   * Poll job status with automatic updates
   */
  const pollJobStatus = useCallback(async (
    jobId: string,
    onProgress?: (progress: number) => void,
    onComplete?: (result: any) => void,
    onError?: (error: string) => void,
    interval: number = 1000,
    timeout: number = 5 * 60 * 1000 // 5 minutes
  ): Promise<void> => {
    const startTime = Date.now()
    
    const poll = async () => {
      try {
        const status = await getJobStatus(jobId)
        
        if (status.success && status.data) {
          onProgress?.(status.data.progress)
          setProgress(status.data.progress)
          
          if (status.data.status === 'completed') {
            onComplete?.(status.data)
            return
          } else if (status.data.status === 'error') {
            onError?.('Processing failed')
            return
          }
        }
        
        // Check timeout
        if (Date.now() - startTime > timeout) {
          onError?.('Processing timeout')
          return
        }
        
        // Continue polling
        setTimeout(poll, interval)
      } catch (error) {
        onError?.(error instanceof Error ? error.message : 'Polling failed')
      }
    }
    
    poll()
  }, [getJobStatus])

  /**
   * Validate media file for processing
   */
  const validateMediaFile = useCallback((
    mimeType: string,
    size: number,
    maxSize: number = 500 * 1024 * 1024 // 500MB
  ): { valid: boolean; error?: string } => {
    // Check file size
    if (size > maxSize) {
      return {
        valid: false,
        error: `File size exceeds maximum allowed size of ${Math.round(maxSize / 1024 / 1024)}MB`
      }
    }

    // Check if media type is supported for processing
    const supportedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/webm', 'video/quicktime', 'video/x-msvideo',
      'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4'
    ]

    if (!supportedTypes.includes(mimeType)) {
      return {
        valid: false,
        error: 'File type not supported for processing'
      }
    }

    return { valid: true }
  }, [])

  return {
    // State
    processing,
    progress,

    // Methods
    processMedia,
    getJobStatus,
    batchProcessMedia,
    generateThumbnail,
    optimizeImage,
    getMediaMetadata,
    checkProcessingHealth,
    pollJobStatus,
    validateMediaFile
  }
}

export default useMediaProcessing
