import { useMemo } from 'react'
import { useAuthStore } from '@/stores/auth/authStore'

/**
 * Permission constants for Course Builder System
 * These should match the backend Permission enum
 */
export enum Permission {
  // Course Management
  COURSES_CREATE = 'courses:create',
  COURSES_READ = 'courses:read',
  COURSES_UPDATE = 'courses:update',
  COURSES_DELETE = 'courses:delete',
  COURSES_PUBLISH = 'courses:publish',
  COURSES_ARCHIVE = 'courses:archive',

  // Lesson Management
  LESSONS_CREATE = 'lessons:create',
  LESSONS_READ = 'lessons:read',
  LESSONS_UPDATE = 'lessons:update',
  LESSONS_DELETE = 'lessons:delete',
  LESSONS_REORDER = 'lessons:reorder',

  // Content Management
  CONTENT_CREATE = 'content:create',
  CONTENT_READ = 'content:read',
  CONTENT_UPDATE = 'content:update',
  CONTENT_DELETE = 'content:delete',
  CONTENT_UPLOAD = 'content:upload',

  // Assessment Management
  ASSESSMENTS_CREATE = 'assessments:create',
  ASSESSMENTS_READ = 'assessments:read',
  ASSESSMENTS_UPDATE = 'assessments:update',
  ASSESSMENTS_DELETE = 'assessments:delete',
  ASSESSMENTS_GRADE = 'assessments:grade',

  // Analytics & Reports
  ANALYTICS_VIEW = 'analytics:view',
  ANALYTICS_EXPORT = 'analytics:export',
  REPORTS_GENERATE = 'reports:generate',

  // User Management
  USERS_CREATE = 'users:create',
  USERS_READ = 'users:read',
  USERS_UPDATE = 'users:update',
  USERS_DELETE = 'users:delete',

  // Institute Management
  INSTITUTE_MANAGE = 'institute:manage',
  BRANCH_MANAGE = 'branch:manage',

  // System Administration
  SYSTEM_ADMIN = 'system:admin',
  SYSTEM_CONFIG = 'system:config'
}

/**
 * User roles enum
 */
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  INSTITUTE_ADMIN = 'institute_admin',
  BRANCH_MANAGER = 'branch_manager',
  TRAINER = 'trainer',
  STAFF = 'staff',
  STUDENT = 'student',
  LEVEL_4 = 'level_4'
}

/**
 * Role-Permission Matrix (matches backend)
 */
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.SUPER_ADMIN]: Object.values(Permission),

  [UserRole.INSTITUTE_ADMIN]: [
    Permission.COURSES_CREATE,
    Permission.COURSES_READ,
    Permission.COURSES_UPDATE,
    Permission.COURSES_DELETE,
    Permission.COURSES_PUBLISH,
    Permission.COURSES_ARCHIVE,
    Permission.LESSONS_CREATE,
    Permission.LESSONS_READ,
    Permission.LESSONS_UPDATE,
    Permission.LESSONS_DELETE,
    Permission.LESSONS_REORDER,
    Permission.CONTENT_CREATE,
    Permission.CONTENT_READ,
    Permission.CONTENT_UPDATE,
    Permission.CONTENT_DELETE,
    Permission.CONTENT_UPLOAD,
    Permission.ASSESSMENTS_CREATE,
    Permission.ASSESSMENTS_READ,
    Permission.ASSESSMENTS_UPDATE,
    Permission.ASSESSMENTS_DELETE,
    Permission.ASSESSMENTS_GRADE,
    Permission.ANALYTICS_VIEW,
    Permission.ANALYTICS_EXPORT,
    Permission.REPORTS_GENERATE,
    Permission.USERS_CREATE,
    Permission.USERS_READ,
    Permission.USERS_UPDATE,
    Permission.USERS_DELETE,
    Permission.INSTITUTE_MANAGE,
    Permission.BRANCH_MANAGE
  ],

  [UserRole.BRANCH_MANAGER]: [
    Permission.COURSES_CREATE,
    Permission.COURSES_READ,
    Permission.COURSES_UPDATE,
    Permission.COURSES_DELETE,
    Permission.COURSES_PUBLISH,
    Permission.LESSONS_CREATE,
    Permission.LESSONS_READ,
    Permission.LESSONS_UPDATE,
    Permission.LESSONS_DELETE,
    Permission.LESSONS_REORDER,
    Permission.CONTENT_CREATE,
    Permission.CONTENT_READ,
    Permission.CONTENT_UPDATE,
    Permission.CONTENT_DELETE,
    Permission.CONTENT_UPLOAD,
    Permission.ASSESSMENTS_CREATE,
    Permission.ASSESSMENTS_READ,
    Permission.ASSESSMENTS_UPDATE,
    Permission.ASSESSMENTS_DELETE,
    Permission.ASSESSMENTS_GRADE,
    Permission.ANALYTICS_VIEW,
    Permission.REPORTS_GENERATE,
    Permission.USERS_READ,
    Permission.USERS_UPDATE
  ],

  [UserRole.TRAINER]: [
    Permission.COURSES_CREATE,
    Permission.COURSES_READ,
    Permission.COURSES_UPDATE,
    Permission.LESSONS_CREATE,
    Permission.LESSONS_READ,
    Permission.LESSONS_UPDATE,
    Permission.LESSONS_DELETE,
    Permission.LESSONS_REORDER,
    Permission.CONTENT_CREATE,
    Permission.CONTENT_READ,
    Permission.CONTENT_UPDATE,
    Permission.CONTENT_DELETE,
    Permission.CONTENT_UPLOAD,
    Permission.ASSESSMENTS_CREATE,
    Permission.ASSESSMENTS_READ,
    Permission.ASSESSMENTS_UPDATE,
    Permission.ASSESSMENTS_DELETE,
    Permission.ASSESSMENTS_GRADE,
    Permission.ANALYTICS_VIEW
  ],

  [UserRole.STAFF]: [
    Permission.COURSES_READ,
    Permission.LESSONS_READ,
    Permission.CONTENT_READ,
    Permission.ASSESSMENTS_READ,
    Permission.ASSESSMENTS_GRADE,
    Permission.ANALYTICS_VIEW
  ],

  [UserRole.STUDENT]: [
    Permission.COURSES_READ,
    Permission.LESSONS_READ,
    Permission.CONTENT_READ,
    Permission.ASSESSMENTS_READ
  ],

  [UserRole.LEVEL_4]: [
    Permission.COURSES_READ,
    Permission.LESSONS_READ,
    Permission.CONTENT_READ,
    Permission.ASSESSMENTS_READ
  ]
}

/**
 * Custom hook for permission management
 */
export const usePermissions = () => {
  const { user } = useAuthStore()

  const permissions = useMemo(() => {
    if (!user) return []

    // Super admin has all permissions
    if (user.legacyRole === UserRole.SUPER_ADMIN || user.role === UserRole.SUPER_ADMIN) {
      return Object.values(Permission)
    }

    const userRole = (user.legacyRole || user.role) as UserRole
    const rolePermissions = ROLE_PERMISSIONS[userRole] || []
    const userSpecificPermissions = user.permissions || []

    // Combine role permissions with user-specific permissions
    const allPermissions = [...rolePermissions, ...userSpecificPermissions]
    
    // Remove duplicates
    return [...new Set(allPermissions)] as Permission[]
  }, [user])

  const hasPermission = (permission: Permission): boolean => {
    return permissions.includes(permission)
  }

  const hasAnyPermission = (permissionList: Permission[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissionList: Permission[]): boolean => {
    return permissionList.every(permission => hasPermission(permission))
  }

  const canManageCourses = (): boolean => {
    return hasAnyPermission([
      Permission.COURSES_CREATE,
      Permission.COURSES_UPDATE,
      Permission.COURSES_DELETE
    ])
  }

  const canManageLessons = (): boolean => {
    return hasAnyPermission([
      Permission.LESSONS_CREATE,
      Permission.LESSONS_UPDATE,
      Permission.LESSONS_DELETE
    ])
  }

  const canManageContent = (): boolean => {
    return hasAnyPermission([
      Permission.CONTENT_CREATE,
      Permission.CONTENT_UPDATE,
      Permission.CONTENT_DELETE
    ])
  }

  const canManageAssessments = (): boolean => {
    return hasAnyPermission([
      Permission.ASSESSMENTS_CREATE,
      Permission.ASSESSMENTS_UPDATE,
      Permission.ASSESSMENTS_DELETE
    ])
  }

  const canViewAnalytics = (): boolean => {
    return hasPermission(Permission.ANALYTICS_VIEW)
  }

  const canManageUsers = (): boolean => {
    return hasAnyPermission([
      Permission.USERS_CREATE,
      Permission.USERS_UPDATE,
      Permission.USERS_DELETE
    ])
  }

  const isInstituteAdmin = (): boolean => {
    return user?.legacyRole === UserRole.INSTITUTE_ADMIN || 
           user?.role === UserRole.INSTITUTE_ADMIN ||
           user?.legacyRole === UserRole.SUPER_ADMIN ||
           user?.role === UserRole.SUPER_ADMIN
  }

  const isBranchManager = (): boolean => {
    return user?.legacyRole === UserRole.BRANCH_MANAGER || 
           user?.role === UserRole.BRANCH_MANAGER ||
           isInstituteAdmin()
  }

  const isTrainer = (): boolean => {
    return user?.legacyRole === UserRole.TRAINER || 
           user?.role === UserRole.TRAINER ||
           isBranchManager()
  }

  const isStudent = (): boolean => {
    return user?.legacyRole === UserRole.STUDENT || 
           user?.role === UserRole.STUDENT ||
           user?.legacyRole === UserRole.LEVEL_4 ||
           user?.role === UserRole.LEVEL_4
  }

  const getUserRole = (): UserRole | null => {
    if (!user) return null
    return (user.legacyRole || user.role) as UserRole
  }

  const getRoleDisplayName = (role?: UserRole): string => {
    const roleNames = {
      [UserRole.SUPER_ADMIN]: 'Super Administrator',
      [UserRole.INSTITUTE_ADMIN]: 'Institute Administrator',
      [UserRole.BRANCH_MANAGER]: 'Branch Manager',
      [UserRole.TRAINER]: 'Trainer',
      [UserRole.STAFF]: 'Staff',
      [UserRole.STUDENT]: 'Student',
      [UserRole.LEVEL_4]: 'Student'
    }

    const currentRole = role || getUserRole()
    return currentRole ? roleNames[currentRole] : 'Unknown'
  }

  return {
    permissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canManageCourses,
    canManageLessons,
    canManageContent,
    canManageAssessments,
    canViewAnalytics,
    canManageUsers,
    isInstituteAdmin,
    isBranchManager,
    isTrainer,
    isStudent,
    getUserRole,
    getRoleDisplayName,
    user
  }
}

/**
 * Hook for checking specific course permissions
 */
export const useCoursePermissions = (courseId?: string) => {
  const { hasPermission, user } = usePermissions()

  const canViewCourse = (): boolean => {
    return hasPermission(Permission.COURSES_READ)
  }

  const canEditCourse = (): boolean => {
    return hasPermission(Permission.COURSES_UPDATE)
  }

  const canDeleteCourse = (): boolean => {
    return hasPermission(Permission.COURSES_DELETE)
  }

  const canPublishCourse = (): boolean => {
    return hasPermission(Permission.COURSES_PUBLISH)
  }

  const canCreateLesson = (): boolean => {
    return hasPermission(Permission.LESSONS_CREATE)
  }

  const canUploadContent = (): boolean => {
    return hasPermission(Permission.CONTENT_UPLOAD)
  }

  return {
    canViewCourse,
    canEditCourse,
    canDeleteCourse,
    canPublishCourse,
    canCreateLesson,
    canUploadContent
  }
}

export default usePermissions
