'use client'

import { create } from 'zustand'

interface ProfileModalState {
  isOpen: boolean
  openModal: () => void
  closeModal: () => void
}

const useProfileModalStore = create<ProfileModalState>((set) => ({
  isOpen: false,
  openModal: () => set({ isOpen: true }),
  closeModal: () => set({ isOpen: false }),
}))

export function useProfileModal() {
  const { isOpen, openModal, closeModal } = useProfileModalStore()

  return {
    isOpen,
    openModal,
    closeModal
  }
}
