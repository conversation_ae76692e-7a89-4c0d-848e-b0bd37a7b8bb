/**
 * Real-time State Synchronization Hook
 * Provides real-time updates and optimistic UI updates for file operations
 */

import { useEffect, useCallback, useRef } from 'react'
import { useSettingsStore } from '@/stores/settings/useSettingsStore'
import { uploadProgressTracker, type UploadProgress } from '@/lib/utils/upload-progress'

export interface RealTimeSyncOptions {
  /**
   * Enable automatic refresh of platform branding after uploads
   */
  autoRefreshBranding?: boolean
  
  /**
   * Interval for checking upload progress (ms)
   */
  progressInterval?: number
  
  /**
   * Enable optimistic updates
   */
  optimisticUpdates?: boolean
  
  /**
   * Callback for upload progress updates
   */
  onProgressUpdate?: (progress: UploadProgress) => void
  
  /**
   * Callback for upload completion
   */
  onUploadComplete?: (uploadId: string) => void
  
  /**
   * Callback for upload errors
   */
  onUploadError?: (uploadId: string, error: string) => void
}

/**
 * Hook for real-time synchronization of platform settings
 */
export function useRealTimeSync(options: RealTimeSyncOptions = {}) {
  const {
    autoRefreshBranding = true,
    progressInterval = 500,
    optimisticUpdates = true,
    onProgressUpdate,
    onUploadComplete,
    onUploadError
  } = options

  const {
    fetchSettings,
    settings,
    isUploadingLogo,
    isUploadingFavicon,
    uploadError
  } = useSettingsStore()

  const progressIntervalRef = useRef<NodeJS.Timeout>()
  const activeUploadsRef = useRef<Set<string>>(new Set())

  /**
   * Start monitoring upload progress
   */
  const startProgressMonitoring = useCallback(() => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current)
    }

    progressIntervalRef.current = setInterval(() => {
      const allProgress = uploadProgressTracker.getAllProgress()
      const activeUploads = allProgress.filter(p => 
        p.status === 'uploading' || p.status === 'processing'
      )

      // Update progress for active uploads
      activeUploads.forEach(progress => {
        onProgressUpdate?.(progress)
        activeUploadsRef.current.add(progress.id)
      })

      // Check for completed uploads
      allProgress.forEach(progress => {
        if (activeUploadsRef.current.has(progress.id)) {
          if (progress.status === 'completed') {
            activeUploadsRef.current.delete(progress.id)
            onUploadComplete?.(progress.id)
            
            // Auto-refresh settings if enabled
            if (autoRefreshBranding) {
              setTimeout(() => {
                fetchSettings()
              }, 1000) // Small delay to ensure backend processing is complete
            }
          } else if (progress.status === 'error') {
            activeUploadsRef.current.delete(progress.id)
            onUploadError?.(progress.id, progress.error || 'Upload failed')
          }
        }
      })

      // Stop monitoring if no active uploads
      if (activeUploads.length === 0 && activeUploadsRef.current.size === 0) {
        if (progressIntervalRef.current) {
          clearInterval(progressIntervalRef.current)
          progressIntervalRef.current = undefined
        }
      }
    }, progressInterval)
  }, [progressInterval, onProgressUpdate, onUploadComplete, onUploadError, autoRefreshBranding, fetchPlatformBranding])

  /**
   * Stop monitoring upload progress
   */
  const stopProgressMonitoring = useCallback(() => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current)
      progressIntervalRef.current = undefined
    }
    activeUploadsRef.current.clear()
  }, [])

  /**
   * Trigger optimistic update for branding
   */
  const optimisticBrandingUpdate = useCallback((
    type: 'logo' | 'favicon',
    file: File
  ) => {
    if (!optimisticUpdates) return

    // Create optimistic preview URL
    const previewUrl = URL.createObjectURL(file)
    
    // This would ideally update the store with optimistic data
    // For now, we'll just log it as the store structure would need modification
    console.log('🔮 Optimistic update:', {
      type,
      filename: file.name,
      previewUrl,
      size: file.size
    })

    // Clean up the preview URL after a delay
    setTimeout(() => {
      URL.revokeObjectURL(previewUrl)
    }, 30000) // 30 seconds
  }, [optimisticUpdates])

  /**
   * Refresh platform branding data
   */
  const refreshBranding = useCallback(async () => {
    try {
      await fetchPlatformBranding()
    } catch (error) {
      console.error('Failed to refresh platform branding:', error)
    }
  }, [fetchPlatformBranding])

  /**
   * Get current upload status
   */
  const getUploadStatus = useCallback(() => {
    const allProgress = uploadProgressTracker.getAllProgress()
    const activeUploads = allProgress.filter(p => 
      p.status === 'uploading' || p.status === 'processing'
    )
    
    return {
      hasActiveUploads: activeUploads.length > 0,
      activeUploads,
      totalUploads: allProgress.length,
      completedUploads: allProgress.filter(p => p.status === 'completed').length,
      failedUploads: allProgress.filter(p => p.status === 'error').length
    }
  }, [])

  // Start monitoring when uploads are active
  useEffect(() => {
    if (isUploadingLogo || isUploadingFavicon) {
      startProgressMonitoring()
    }

    return () => {
      stopProgressMonitoring()
    }
  }, [isUploadingLogo, isUploadingFavicon, startProgressMonitoring, stopProgressMonitoring])

  // Auto-refresh on error resolution
  useEffect(() => {
    if (!uploadError && autoRefreshBranding) {
      // Refresh branding when errors are cleared (might indicate successful retry)
      const timer = setTimeout(() => {
        refreshBranding()
      }, 2000)

      return () => clearTimeout(timer)
    }
  }, [uploadError, autoRefreshBranding, refreshBranding])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopProgressMonitoring()
      // Clean up any remaining object URLs
      uploadProgressTracker.getAllProgress().forEach(progress => {
        if (progress.id.startsWith('blob:')) {
          URL.revokeObjectURL(progress.id)
        }
      })
    }
  }, [stopProgressMonitoring])

  return {
    // State
    platformBranding,
    isUploadingLogo,
    isUploadingFavicon,
    uploadError,
    
    // Actions
    startProgressMonitoring,
    stopProgressMonitoring,
    optimisticBrandingUpdate,
    refreshBranding,
    getUploadStatus,
    
    // Utilities
    isMonitoring: !!progressIntervalRef.current,
    activeUploads: Array.from(activeUploadsRef.current)
  }
}

/**
 * Hook for optimistic file preview
 */
export function useOptimisticPreview() {
  const previewUrls = useRef<Map<string, string>>(new Map())

  const createPreview = useCallback((file: File, id?: string) => {
    const previewId = id || `preview_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const url = URL.createObjectURL(file)
    
    previewUrls.current.set(previewId, url)
    
    // Auto-cleanup after 5 minutes
    setTimeout(() => {
      const storedUrl = previewUrls.current.get(previewId)
      if (storedUrl) {
        URL.revokeObjectURL(storedUrl)
        previewUrls.current.delete(previewId)
      }
    }, 5 * 60 * 1000)
    
    return { previewId, url }
  }, [])

  const removePreview = useCallback((previewId: string) => {
    const url = previewUrls.current.get(previewId)
    if (url) {
      URL.revokeObjectURL(url)
      previewUrls.current.delete(previewId)
    }
  }, [])

  const getPreview = useCallback((previewId: string) => {
    return previewUrls.current.get(previewId) || null
  }, [])

  // Cleanup all previews on unmount
  useEffect(() => {
    return () => {
      previewUrls.current.forEach((url) => {
        URL.revokeObjectURL(url)
      })
      previewUrls.current.clear()
    }
  }, [])

  return {
    createPreview,
    removePreview,
    getPreview,
    previewCount: previewUrls.current.size
  }
}

/**
 * Hook for automatic retry logic
 */
export function useAutoRetry(maxRetries: number = 3, retryDelay: number = 2000) {
  const retryCountRef = useRef<Map<string, number>>(new Map())

  const shouldRetry = useCallback((uploadId: string, error: string) => {
    const currentRetries = retryCountRef.current.get(uploadId) || 0
    
    // Don't retry validation errors or client-side errors
    if (error.includes('validation') || error.includes('file type') || error.includes('file size')) {
      return false
    }
    
    return currentRetries < maxRetries
  }, [maxRetries])

  const incrementRetry = useCallback((uploadId: string) => {
    const currentRetries = retryCountRef.current.get(uploadId) || 0
    retryCountRef.current.set(uploadId, currentRetries + 1)
    return currentRetries + 1
  }, [])

  const resetRetry = useCallback((uploadId: string) => {
    retryCountRef.current.delete(uploadId)
  }, [])

  const getRetryCount = useCallback((uploadId: string) => {
    return retryCountRef.current.get(uploadId) || 0
  }, [])

  const scheduleRetry = useCallback((
    uploadId: string,
    retryFn: () => Promise<void>
  ) => {
    const retryCount = incrementRetry(uploadId)
    const delay = retryDelay * Math.pow(2, retryCount - 1) // Exponential backoff
    
    setTimeout(async () => {
      try {
        await retryFn()
        resetRetry(uploadId)
      } catch (error) {
        console.error(`Retry ${retryCount} failed for ${uploadId}:`, error)
      }
    }, delay)
  }, [retryDelay, incrementRetry, resetRetry])

  return {
    shouldRetry,
    incrementRetry,
    resetRetry,
    getRetryCount,
    scheduleRetry
  }
}
