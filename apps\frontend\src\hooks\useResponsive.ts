import { useState, useEffect } from 'react'

// Breakpoint definitions (matching Tailwind CSS)
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
} as const

export type Breakpoint = keyof typeof breakpoints

// Hook to detect current screen size
export function useResponsive() {
  const [screenSize, setScreenSize] = useState<{
    width: number
    height: number
    isMobile: boolean
    isTablet: boolean
    isDesktop: boolean
    isLarge: boolean
    currentBreakpoint: Breakpoint | 'xs'
  }>({
    width: 0,
    height: 0,
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isLarge: false,
    currentBreakpoint: 'xs'
  })

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth
      const height = window.innerHeight

      // Determine current breakpoint
      let currentBreakpoint: Breakpoint | 'xs' = 'xs'
      if (width >= breakpoints['2xl']) currentBreakpoint = '2xl'
      else if (width >= breakpoints.xl) currentBreakpoint = 'xl'
      else if (width >= breakpoints.lg) currentBreakpoint = 'lg'
      else if (width >= breakpoints.md) currentBreakpoint = 'md'
      else if (width >= breakpoints.sm) currentBreakpoint = 'sm'

      setScreenSize({
        width,
        height,
        isMobile: width < breakpoints.md,
        isTablet: width >= breakpoints.md && width < breakpoints.lg,
        isDesktop: width >= breakpoints.lg,
        isLarge: width >= breakpoints.xl,
        currentBreakpoint
      })
    }

    // Initial call
    updateScreenSize()

    // Add event listener
    window.addEventListener('resize', updateScreenSize)

    // Cleanup
    return () => window.removeEventListener('resize', updateScreenSize)
  }, [])

  return screenSize
}

// Hook to check if screen is at least a certain breakpoint
export function useBreakpoint(breakpoint: Breakpoint) {
  const { width } = useResponsive()
  return width >= breakpoints[breakpoint]
}

// Hook for media queries
export function useMediaQuery(query: string) {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia(query)
    setMatches(mediaQuery.matches)

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    mediaQuery.addEventListener('change', handler)
    return () => mediaQuery.removeEventListener('change', handler)
  }, [query])

  return matches
}

// Utility functions for responsive design
export const responsive = {
  // Check if current screen is mobile
  isMobile: () => window.innerWidth < breakpoints.md,
  
  // Check if current screen is tablet
  isTablet: () => window.innerWidth >= breakpoints.md && window.innerWidth < breakpoints.lg,
  
  // Check if current screen is desktop
  isDesktop: () => window.innerWidth >= breakpoints.lg,
  
  // Get current breakpoint
  getCurrentBreakpoint: (): Breakpoint | 'xs' => {
    const width = window.innerWidth
    if (width >= breakpoints['2xl']) return '2xl'
    if (width >= breakpoints.xl) return 'xl'
    if (width >= breakpoints.lg) return 'lg'
    if (width >= breakpoints.md) return 'md'
    if (width >= breakpoints.sm) return 'sm'
    return 'xs'
  },
  
  // Get responsive grid columns
  getGridColumns: (mobile: number = 1, tablet: number = 2, desktop: number = 3, large: number = 4) => {
    const width = window.innerWidth
    if (width >= breakpoints.xl) return large
    if (width >= breakpoints.lg) return desktop
    if (width >= breakpoints.md) return tablet
    return mobile
  },
  
  // Get responsive items per page
  getItemsPerPage: (mobile: number = 5, tablet: number = 10, desktop: number = 20) => {
    const width = window.innerWidth
    if (width >= breakpoints.lg) return desktop
    if (width >= breakpoints.md) return tablet
    return mobile
  }
}

// Hook for responsive grid columns
export function useResponsiveGrid(
  mobile: number = 1,
  tablet: number = 2,
  desktop: number = 3,
  large: number = 4
) {
  const { isMobile, isTablet, isDesktop, isLarge } = useResponsive()
  
  if (isLarge) return large
  if (isDesktop) return desktop
  if (isTablet) return tablet
  return mobile
}

// Hook for responsive items per page
export function useResponsiveItemsPerPage(
  mobile: number = 5,
  tablet: number = 10,
  desktop: number = 20
) {
  const { isMobile, isTablet } = useResponsive()
  
  if (isMobile) return mobile
  if (isTablet) return tablet
  return desktop
}

// Hook for responsive sidebar behavior
export function useResponsiveSidebar() {
  const { isMobile, isTablet } = useResponsive()
  
  return {
    shouldCollapse: isMobile || isTablet,
    shouldOverlay: isMobile,
    defaultCollapsed: isMobile || isTablet
  }
}

// Hook for responsive table behavior
export function useResponsiveTable() {
  const { isMobile, isTablet } = useResponsive()
  
  return {
    shouldUseCards: isMobile,
    shouldHideColumns: isMobile || isTablet,
    shouldScroll: isMobile || isTablet
  }
}

// Hook for responsive form layout
export function useResponsiveForm() {
  const { isMobile, isTablet } = useResponsive()
  
  return {
    columns: isMobile ? 1 : isTablet ? 2 : 3,
    shouldStack: isMobile,
    shouldUseFullWidth: isMobile
  }
}

// Hook for responsive modal behavior
export function useResponsiveModal() {
  const { isMobile } = useResponsive()
  
  return {
    shouldUseFullScreen: isMobile,
    maxWidth: isMobile ? '100%' : '90%',
    padding: isMobile ? '1rem' : '2rem'
  }
}

export default useResponsive
