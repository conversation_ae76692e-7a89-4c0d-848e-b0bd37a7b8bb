import { useEffect, useMemo } from 'react'
import { useSidebarStore, NavigationItem } from '@/stores/sidebar/useSidebarStore'
import { usePermissions } from '@/contexts/PermissionContext'
import { useAuthStore } from '@/stores/auth/useAuthStore'

/**
 * Hook that integrates sidebar store with permission-aware navigation
 * This hook ensures that navigation items are properly filtered based on user permissions
 * and that the sidebar store is kept in sync with the permission system
 */
export function useSidebarNavigation() {
  const { user } = useAuthStore()
  const { userPermissions, filterNavigationByPermissions } = usePermissions()
  const {
    navigationItems,
    userType,
    setNavigationItems,
    filterNavigationByPermissions: sidebarFilterPermissions,
    initializeNavigation
  } = useSidebarStore()

  // Filter navigation items based on permissions
  const filteredNavigationItems = useMemo(() => {
    if (!Array.isArray(navigationItems) || navigationItems.length === 0) {
      return []
    }

    // For super admin, return all items without filtering
    if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {
      return navigationItems
    }

    // Use the permission context filtering for complex permission checks
    return filterNavigationByPermissions(navigationItems)
  }, [navigationItems, userPermissions.role, filterNavigationByPermissions])

  // Helper function to check if a navigation item is accessible
  const isNavigationItemAccessible = (item: NavigationItem): boolean => {
    // Super admin has access to everything
    if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {
      return true
    }

    // Check item permissions
    if (item.permissions && item.permissions.length > 0) {
      // Check if user role is in the permissions array
      if (item.permissions.includes(userPermissions.role)) {
        return true
      }
      
      // Check if user has any of the required permissions
      return item.permissions.some(permission => 
        userPermissions.permissions.some(userPerm => 
          userPerm.code === permission || userPerm.name === permission
        )
      )
    }

    // If no permissions specified, allow access
    return true
  }

  // Helper function to filter navigation items recursively
  const filterNavigationItemsRecursively = (items: NavigationItem[]): NavigationItem[] => {
    return items.map(item => {
      let filteredItem = { ...item }

      // Filter children if they exist
      if (item.children && item.children.length > 0) {
        const filteredChildren = filterNavigationItemsRecursively(item.children)
          .filter(child => isNavigationItemAccessible(child))
        
        filteredItem.children = filteredChildren
      }

      return filteredItem
    }).filter(item => isNavigationItemAccessible(item))
  }

  // Enhanced filtered navigation with recursive filtering
  const enhancedFilteredNavigation = useMemo(() => {
    if (!Array.isArray(navigationItems) || navigationItems.length === 0) {
      return []
    }

    // For super admin, return all items
    if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {
      return navigationItems
    }

    return filterNavigationItemsRecursively(navigationItems)
  }, [navigationItems, userPermissions.role, userPermissions.permissions])

  // Update sidebar store when user permissions change
  useEffect(() => {
    if (user && userPermissions.role) {
      // Extract permission codes for sidebar filtering
      const permissionCodes = userPermissions.permissions.map(p => p.code || p.name)
      sidebarFilterPermissions(permissionCodes)
    }
  }, [user, userPermissions.permissions, sidebarFilterPermissions])

  // Get navigation items by section
  const getNavigationBySection = (sectionName: string): NavigationItem[] => {
    return enhancedFilteredNavigation.filter(item => 
      item.href.includes(sectionName.toLowerCase())
    )
  }

  // Check if specific navigation item is accessible
  const isNavigationAccessible = (href: string): boolean => {
    const findItemByHref = (items: NavigationItem[]): NavigationItem | undefined => {
      for (const item of items) {
        if (item.href === href) {
          return item
        }
        if (item.children) {
          const found = findItemByHref(item.children)
          if (found) return found
        }
      }
      return undefined
    }

    const item = findItemByHref(enhancedFilteredNavigation)
    return !!item
  }

  // Get accessible navigation count
  const getNavigationStats = () => {
    const countItems = (items: NavigationItem[]): number => {
      return items.reduce((count, item) => {
        let itemCount = 1
        if (item.children) {
          itemCount += countItems(item.children)
        }
        return count + itemCount
      }, 0)
    }

    const accessibleCount = countItems(enhancedFilteredNavigation)
    const totalCount = countItems(navigationItems)

    return {
      accessible: accessibleCount,
      total: totalCount,
      restricted: totalCount - accessibleCount
    }
  }

  // Find navigation item by ID
  const findNavigationItem = (id: string): NavigationItem | undefined => {
    const findById = (items: NavigationItem[]): NavigationItem | undefined => {
      for (const item of items) {
        if (item.id === id) {
          return item
        }
        if (item.children) {
          const found = findById(item.children)
          if (found) return found
        }
      }
      return undefined
    }

    return findById(enhancedFilteredNavigation)
  }

  return {
    // Navigation items
    navigationItems: enhancedFilteredNavigation,
    rawNavigationItems: navigationItems,
    
    // Permission checks
    isNavigationItemAccessible,
    isNavigationAccessible,
    
    // Utility functions
    getNavigationBySection,
    getNavigationStats,
    findNavigationItem,
    
    // Store actions
    setNavigationItems,
    initializeNavigation,
    
    // User info
    userType,
    userRole: userPermissions.role,
    
    // Stats
    navigationStats: getNavigationStats()
  }
}

/**
 * Hook for checking specific navigation permissions
 */
export function useNavigationAccess(navigationIds: string[]) {
  const { isNavigationAccessible } = useSidebarNavigation()

  const accessMap = useMemo(() => {
    return navigationIds.reduce((acc, id) => {
      acc[id] = isNavigationAccessible(`/${id.replace(/-/g, '/')}`)
      return acc
    }, {} as Record<string, boolean>)
  }, [navigationIds, isNavigationAccessible])

  const accessibleIds = navigationIds.filter(id => accessMap[id])
  const restrictedIds = navigationIds.filter(id => !accessMap[id])

  return {
    accessMap,
    accessibleIds,
    restrictedIds,
    hasAccessToAny: accessibleIds.length > 0,
    hasAccessToAll: restrictedIds.length === 0
  }
}

/**
 * Hook for payment gateway navigation access
 */
export function usePaymentGatewayNavigation() {
  const { isNavigationAccessible, userRole } = useSidebarNavigation()

  const superAdminAccess = isNavigationAccessible('/super-admin/gateway-management')
  const instituteAdminAccess = isNavigationAccessible('/admin/settings/payment-gateways')

  return {
    canAccessSuperAdminGateways: superAdminAccess,
    canAccessInstituteAdminGateways: instituteAdminAccess,
    canAccessAnyGateways: superAdminAccess || instituteAdminAccess,
    userRole,
    isSuperAdmin: userRole === 'super_admin',
    isInstituteAdmin: userRole === 'institute_admin'
  }
}
