import { useCallback } from 'react'
import { toast } from '@/components/shared/ui/Toast'

// Custom hook for toast notifications with predefined messages
export function useToastNotifications() {
  // Staff Management Notifications - REMOVED
  // const staffNotifications = useCallback(() => ({
  //   createSuccess: (name: string) =>
  //     toast.success('Staff Created', `${name} has been successfully added to the team.`),
  //
  //   createError: (error?: string) =>
  //     toast.error('Failed to Create Staff', error || 'An error occurred while creating the staff member.'),
  //
  //   updateSuccess: (name: string) =>
  //     toast.success('Staff Updated', `${name}'s information has been successfully updated.`),
  //
  //   updateError: (error?: string) =>
  //     toast.error('Failed to Update Staff', error || 'An error occurred while updating the staff member.'),
  // }), [])

  // Role Management Notifications
  const roleNotifications = useCallback(() => ({
    createSuccess: (name: string) => 
      toast.success('Role Created', `${name} role has been successfully created.`),
    
    createError: (error?: string) => 
      toast.error('Failed to Create Role', error || 'An error occurred while creating the role.'),
    
    updateSuccess: (name: string) => 
      toast.success('Role Updated', `${name} role has been successfully updated.`),
    
    updateError: (error?: string) => 
      toast.error('Failed to Update Role', error || 'An error occurred while updating the role.'),
    
    deleteSuccess: (name: string) => 
      toast.success('Role Deleted', `${name} role has been removed.`),
    
    deleteError: (error?: string) => 
      toast.error('Failed to Delete Role', error || 'An error occurred while deleting the role.'),
    
    deleteWarning: (userCount: number) =>
      toast.warning('Role In Use', `This role is assigned to ${userCount} user${userCount > 1 ? 's' : ''}. Please reassign them first.`),
    
    permissionUpdateSuccess: () => 
      toast.success('Permissions Updated', 'Role permissions have been successfully updated.'),
    
    duplicateName: (name: string) => 
      toast.warning('Role Name Exists', `A role with the name "${name}" already exists in this department.`)
  }), [])

  // Permission Management Notifications
  const permissionNotifications = useCallback(() => ({
    updateSuccess: () => 
      toast.success('Permissions Updated', 'User permissions have been successfully updated.'),
    
    updateError: (error?: string) => 
      toast.error('Failed to Update Permissions', error || 'An error occurred while updating permissions.'),
    
    accessDenied: (action: string) => 
      toast.warning('Access Denied', `You don't have permission to ${action}.`),
    
    insufficientLevel: (requiredLevel: number) => 
      toast.warning('Insufficient Access Level', `This action requires level ${requiredLevel} or higher access.`)
  }), [])

  // Authentication Notifications
  const authNotifications = useCallback(() => ({
    loginSuccess: (name: string) => 
      toast.success('Welcome Back', `Hello ${name}, you have successfully logged in.`),
    
    loginError: (error?: string) => 
      toast.error('Login Failed', error || 'Invalid credentials. Please try again.'),
    
    logoutSuccess: () => 
      toast.info('Logged Out', 'You have been successfully logged out.'),
    
    sessionExpired: () => 
      toast.warning('Session Expired', 'Your session has expired. Please log in again.'),
    
    passwordChangeSuccess: () => 
      toast.success('Password Changed', 'Your password has been successfully updated.'),
    
    passwordChangeError: (error?: string) => 
      toast.error('Password Change Failed', error || 'Failed to update password. Please try again.'),
    
    profileUpdateSuccess: () => 
      toast.success('Profile Updated', 'Your profile information has been successfully updated.'),
    
    profileUpdateError: (error?: string) => 
      toast.error('Profile Update Failed', error || 'Failed to update profile. Please try again.')
  }), [])

  // System Notifications
  const systemNotifications = useCallback(() => ({
    saveSuccess: () => 
      toast.success('Changes Saved', 'Your changes have been successfully saved.'),
    
    saveError: (error?: string) => 
      toast.error('Save Failed', error || 'Failed to save changes. Please try again.'),
    
    networkError: () => 
      toast.error('Network Error', 'Unable to connect to the server. Please check your internet connection.'),
    
    serverError: () => 
      toast.error('Server Error', 'A server error occurred. Please try again later.'),
    
    maintenanceMode: () => 
      toast.warning('Maintenance Mode', 'The system is currently under maintenance. Some features may be unavailable.'),
    
    featureComingSoon: (feature: string) => 
      toast.info('Coming Soon', `${feature} feature is coming soon!`),
    
    unsavedChanges: () => 
      toast.warning('Unsaved Changes', 'You have unsaved changes. Please save before leaving.'),
    
    autoSaveSuccess: () => 
      toast.info('Auto-saved', 'Your changes have been automatically saved.', { duration: 2000 }),
    
    copySuccess: (item: string) => 
      toast.success('Copied', `${item} has been copied to clipboard.`, { duration: 2000 }),
    
    downloadSuccess: (filename: string) => 
      toast.success('Download Complete', `${filename} has been downloaded successfully.`),
    
    uploadSuccess: (filename: string) => 
      toast.success('Upload Complete', `${filename} has been uploaded successfully.`),
    
    uploadError: (error?: string) => 
      toast.error('Upload Failed', error || 'Failed to upload file. Please try again.')
  }), [])

  // Form Validation Notifications
  const validationNotifications = useCallback(() => ({
    requiredField: (field: string) => 
      toast.warning('Required Field', `${field} is required.`),
    
    invalidEmail: () => 
      toast.warning('Invalid Email', 'Please enter a valid email address.'),
    
    invalidPhone: () => 
      toast.warning('Invalid Phone', 'Please enter a valid phone number.'),
    
    passwordTooWeak: () => 
      toast.warning('Weak Password', 'Password must be at least 8 characters with uppercase, lowercase, and numbers.'),
    
    passwordMismatch: () => 
      toast.warning('Password Mismatch', 'Passwords do not match.'),
    
    invalidDate: () => 
      toast.warning('Invalid Date', 'Please enter a valid date.'),
    
    fileTooLarge: (maxSize: string) => 
      toast.warning('File Too Large', `File size must be less than ${maxSize}.`),
    
    invalidFileType: (allowedTypes: string) => 
      toast.warning('Invalid File Type', `Only ${allowedTypes} files are allowed.`),
    
    formErrors: (count: number) => 
      toast.warning('Form Errors', `Please fix ${count} error${count > 1 ? 's' : ''} before submitting.`)
  }), [])

  // Data Operations Notifications
  const dataNotifications = useCallback(() => ({
    loadingData: (type: string) => 
      toast.info('Loading', `Loading ${type}...`, { duration: 0 }),
    
    dataLoaded: (type: string, count: number) => 
      toast.success('Data Loaded', `Loaded ${count} ${type} record${count > 1 ? 's' : ''}.`, { duration: 2000 }),
    
    noDataFound: (type: string) => 
      toast.info('No Data', `No ${type} records found.`),
    
    filterApplied: (count: number) => 
      toast.info('Filter Applied', `Showing ${count} filtered result${count > 1 ? 's' : ''}.`, { duration: 2000 }),
    
    searchResults: (count: number, query: string) => 
      toast.info('Search Results', `Found ${count} result${count > 1 ? 's' : ''} for "${query}".`, { duration: 3000 }),
    
    refreshSuccess: () => 
      toast.success('Refreshed', 'Data has been refreshed.', { duration: 2000 }),
    
    cacheCleared: () => 
      toast.info('Cache Cleared', 'Application cache has been cleared.', { duration: 2000 })
  }), [])

  return {
    // staff: staffNotifications(), // REMOVED
    role: roleNotifications(),
    permission: permissionNotifications(),
    auth: authNotifications(),
    system: systemNotifications(),
    validation: validationNotifications(),
    data: dataNotifications(),
    
    // Direct toast methods
    success: toast.success,
    error: toast.error,
    warning: toast.warning,
    info: toast.info
  }
}

// Utility function for API error handling
export function handleApiError(error: any, context: string = 'operation') {
  console.error(`API Error in ${context}:`, error)
  
  if (error?.response?.status === 401) {
    toast.error('Authentication Required', 'Please log in to continue.')
  } else if (error?.response?.status === 403) {
    toast.error('Access Denied', 'You do not have permission to perform this action.')
  } else if (error?.response?.status === 404) {
    toast.error('Not Found', 'The requested resource was not found.')
  } else if (error?.response?.status === 422) {
    toast.error('Validation Error', error?.response?.data?.message || 'Please check your input and try again.')
  } else if (error?.response?.status >= 500) {
    toast.error('Server Error', 'A server error occurred. Please try again later.')
  } else if (error?.code === 'NETWORK_ERROR') {
    toast.error('Network Error', 'Unable to connect to the server. Please check your internet connection.')
  } else {
    toast.error('Error', error?.message || `An error occurred during ${context}.`)
  }
}

// Utility function for success operations
export function handleApiSuccess(message: string, details?: string) {
  toast.success(message, details)
}

export default useToastNotifications
