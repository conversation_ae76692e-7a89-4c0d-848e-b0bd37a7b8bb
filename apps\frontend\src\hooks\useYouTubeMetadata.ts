'use client'

import { useState, useCallback } from 'react'
import { YouTubeVideoData, PlaylistData } from '@/components/admin/content-management/YouTubeIntegration'

interface UseYouTubeMetadataReturn {
  extractVideoMetadata: (url: string) => Promise<YouTubeVideoData | null>
  extractPlaylistMetadata: (url: string, maxVideos?: number) => Promise<PlaylistData | null>
  isLoading: boolean
  error: string | null
}

export const useYouTubeMetadata = (): UseYouTubeMetadataReturn => {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Extract video ID from various YouTube URL formats
  const extractVideoId = (url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ]
    
    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) return match[1]
    }
    
    return null
  }

  // Extract playlist ID from YouTube URL
  const extractPlaylistId = (url: string): string | null => {
    const patterns = [
      /[?&]list=([^&\n?#]+)/,
      /youtube\.com\/playlist\?list=([^&\n?#]+)/
    ]
    
    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) return match[1]
    }
    
    return null
  }

  // Generate embed URL for video
  const generateEmbedUrl = (videoId: string): string => {
    return `https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0`
  }

  // Format duration from ISO 8601 to seconds
  const parseDuration = (duration: string): number => {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
    if (!match) return 0
    
    const hours = parseInt(match[1] || '0', 10)
    const minutes = parseInt(match[2] || '0', 10)
    const seconds = parseInt(match[3] || '0', 10)
    
    return hours * 3600 + minutes * 60 + seconds
  }

  // Mock YouTube API response (in production, use actual YouTube Data API v3)
  const mockYouTubeAPI = {
    getVideoDetails: async (videoId: string): Promise<any> => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock response based on video ID
      return {
        items: [{
          id: videoId,
          snippet: {
            title: `Sample Video Title - ${videoId}`,
            description: `This is a sample description for video ${videoId}. In a real implementation, this would come from the YouTube Data API v3.`,
            channelTitle: 'Sample Channel',
            publishedAt: new Date().toISOString(),
            thumbnails: {
              high: {
                url: `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`
              }
            }
          },
          contentDetails: {
            duration: 'PT5M30S' // 5 minutes 30 seconds
          },
          statistics: {
            viewCount: Math.floor(Math.random() * 1000000).toString()
          }
        }]
      }
    },

    getPlaylistDetails: async (playlistId: string): Promise<any> => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Mock playlist response
      return {
        items: [{
          id: playlistId,
          snippet: {
            title: `Sample Playlist - ${playlistId}`,
            description: `This is a sample playlist description for ${playlistId}.`,
            channelTitle: 'Sample Channel',
            thumbnails: {
              high: {
                url: `https://img.youtube.com/vi/sample/hqdefault.jpg`
              }
            }
          },
          contentDetails: {
            itemCount: 10
          }
        }]
      }
    },

    getPlaylistItems: async (playlistId: string, maxResults: number = 50): Promise<any> => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Generate mock playlist items
      const items = Array.from({ length: Math.min(maxResults, 10) }, (_, index) => ({
        snippet: {
          resourceId: {
            videoId: `video${index + 1}_${playlistId}`
          },
          title: `Playlist Video ${index + 1}`,
          description: `Description for playlist video ${index + 1}`,
          channelTitle: 'Sample Channel',
          publishedAt: new Date().toISOString(),
          thumbnails: {
            default: {
              url: `https://img.youtube.com/vi/video${index + 1}_${playlistId}/default.jpg`
            }
          }
        }
      }))
      
      return { items }
    }
  }

  const extractVideoMetadata = useCallback(async (url: string): Promise<YouTubeVideoData | null> => {
    setIsLoading(true)
    setError(null)
    
    try {
      const videoId = extractVideoId(url)
      if (!videoId) {
        throw new Error('Invalid YouTube video URL')
      }

      // In production, replace with actual YouTube Data API v3 call
      const response = await mockYouTubeAPI.getVideoDetails(videoId)
      
      if (!response.items || response.items.length === 0) {
        throw new Error('Video not found')
      }

      const video = response.items[0]
      const snippet = video.snippet
      const contentDetails = video.contentDetails
      const statistics = video.statistics

      const videoData: YouTubeVideoData = {
        id: videoId,
        title: snippet.title,
        description: snippet.description,
        thumbnail: snippet.thumbnails.high.url,
        duration: parseDuration(contentDetails.duration),
        publishedAt: snippet.publishedAt,
        channelTitle: snippet.channelTitle,
        viewCount: parseInt(statistics.viewCount, 10),
        embedUrl: generateEmbedUrl(videoId),
        originalUrl: url
      }

      return videoData
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to extract video metadata'
      setError(errorMessage)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [])

  const extractPlaylistMetadata = useCallback(async (url: string, maxVideos: number = 50): Promise<PlaylistData | null> => {
    setIsLoading(true)
    setError(null)
    
    try {
      const playlistId = extractPlaylistId(url)
      if (!playlistId) {
        throw new Error('Invalid YouTube playlist URL')
      }

      // Get playlist details
      const playlistResponse = await mockYouTubeAPI.getPlaylistDetails(playlistId)
      
      if (!playlistResponse.items || playlistResponse.items.length === 0) {
        throw new Error('Playlist not found')
      }

      const playlist = playlistResponse.items[0]
      const snippet = playlist.snippet

      // Get playlist items (videos)
      const itemsResponse = await mockYouTubeAPI.getPlaylistItems(playlistId, maxVideos)
      
      const videos: YouTubeVideoData[] = await Promise.all(
        itemsResponse.items.map(async (item: any) => {
          const videoId = item.snippet.resourceId.videoId
          
          // For each video, we would normally get detailed info
          // For this mock, we'll create simplified video data
          return {
            id: videoId,
            title: item.snippet.title,
            description: item.snippet.description,
            thumbnail: item.snippet.thumbnails.default.url,
            duration: 300, // Mock 5 minutes
            publishedAt: item.snippet.publishedAt,
            channelTitle: item.snippet.channelTitle,
            viewCount: Math.floor(Math.random() * 100000),
            embedUrl: generateEmbedUrl(videoId),
            originalUrl: `https://www.youtube.com/watch?v=${videoId}`
          }
        })
      )

      const playlistData: PlaylistData = {
        id: playlistId,
        title: snippet.title,
        description: snippet.description,
        videoCount: playlist.contentDetails.itemCount,
        videos,
        thumbnail: snippet.thumbnails.high.url
      }

      return playlistData
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to extract playlist metadata'
      setError(errorMessage)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    extractVideoMetadata,
    extractPlaylistMetadata,
    isLoading,
    error
  }
}

// Utility functions for YouTube URL validation and processing
export const youTubeUtils = {
  validateUrl: (url: string): boolean => {
    const patterns = [
      /^https?:\/\/(www\.)?youtube\.com\/watch\?v=[\w-]+/,
      /^https?:\/\/youtu\.be\/[\w-]+/,
      /^https?:\/\/(www\.)?youtube\.com\/playlist\?list=[\w-]+/,
      /^https?:\/\/(www\.)?youtube\.com\/embed\/[\w-]+/
    ]
    return patterns.some(pattern => pattern.test(url))
  },

  extractVideoId: (url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ]
    
    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) return match[1]
    }
    
    return null
  },

  extractPlaylistId: (url: string): string | null => {
    const patterns = [
      /[?&]list=([^&\n?#]+)/,
      /youtube\.com\/playlist\?list=([^&\n?#]+)/
    ]
    
    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) return match[1]
    }
    
    return null
  },

  generateThumbnailUrl: (videoId: string, quality: 'default' | 'medium' | 'high' | 'standard' | 'maxres' = 'high'): string => {
    const qualityMap = {
      default: 'default',
      medium: 'mqdefault', 
      high: 'hqdefault',
      standard: 'sddefault',
      maxres: 'maxresdefault'
    }
    
    return `https://img.youtube.com/vi/${videoId}/${qualityMap[quality]}.jpg`
  }
}
