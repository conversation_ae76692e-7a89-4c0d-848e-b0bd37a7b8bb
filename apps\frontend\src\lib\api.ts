/**
 * API utility functions for making requests to the backend
 */

// Get the API base URL from environment variables
const getApiBaseUrl = (): string => {
  // Always use the full backend URL for API calls
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'
}

/**
 * Create a full API URL from a relative path
 */
export const createApiUrl = (path: string): string => {
  const baseUrl = getApiBaseUrl()

  // Remove leading slash from path if present
  const cleanPath = path.startsWith('/') ? path.slice(1) : path

  // Combine base URL with path
  return `${baseUrl}/${cleanPath}`
}

/**
 * Get the auth token from localStorage or Zustand storage
 */
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    // First try direct auth_token
    let token = localStorage.getItem('auth_token')

    // If not found, try Zustand auth storage
    if (!token) {
      try {
        const authStorage = localStorage.getItem('auth-storage')
        if (authStorage) {
          const parsed = JSON.parse(authStorage)
          token = parsed?.state?.token || null
        }
      } catch (error) {
        console.error('Failed to parse auth storage:', error)
      }
    }

    console.log('🔍 getAuthToken:', {
      hasToken: !!token,
      tokenLength: token?.length,
      tokenPreview: token ? `${token.substring(0, 10)}...${token.substring(token.length - 10)}` : 'null',
      directToken: !!localStorage.getItem('auth_token'),
      zustandToken: !!localStorage.getItem('auth-storage')
    })
    return token
  }
  return null
}

/**
 * Make an API request with proper error handling
 */
export const apiRequest = async (
  path: string,
  options: RequestInit = {}
): Promise<Response> => {
  const url = createApiUrl(path)
  const token = getAuthToken()

  const defaultOptions: RequestInit = {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
  }



  const finalOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  }

  console.log('🔍 API Request:', {
    url,
    method: finalOptions.method || 'GET',
    hasAuth: !!(finalOptions.headers as any)?.['Authorization'],
    authHeader: (finalOptions.headers as any)?.['Authorization'],
    allHeaders: finalOptions.headers,
    token: token ? `${token.substring(0, 10)}...${token.substring(token.length - 10)}` : 'NO_TOKEN'
  })

  try {
    const response = await fetch(url, finalOptions)
    console.log('✅ API Response:', {
      url,
      status: response.status,
      ok: response.ok
    })
    return response
  } catch (error) {
    console.error('❌ API request failed:', { url, error })
    throw error
  }
}

/**
 * Make a GET request to the API
 */
export const apiGet = async (path: string, params?: Record<string, string>): Promise<Response> => {
  let url = path
  
  if (params) {
    const searchParams = new URLSearchParams(params)
    url = `${path}?${searchParams.toString()}`
  }
  
  return apiRequest(url, { method: 'GET' })
}

/**
 * Make a POST request to the API
 */
export const apiPost = async (path: string, data?: any): Promise<Response> => {
  return apiRequest(path, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * Make a PUT request to the API
 */
export const apiPut = async (path: string, data?: any): Promise<Response> => {
  return apiRequest(path, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * Make a PATCH request to the API
 */
export const apiPatch = async (path: string, data?: any): Promise<Response> => {
  return apiRequest(path, {
    method: 'PATCH',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * Make a DELETE request to the API
 */
export const apiDelete = async (path: string): Promise<Response> => {
  return apiRequest(path, { method: 'DELETE' })
}

/**
 * Make a DELETE request to the API with request body
 */
export const apiDeleteWithBody = async (path: string, data?: any): Promise<Response> => {
  const options: RequestInit = {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  }

  if (data) {
    options.body = JSON.stringify(data)
  }

  return apiRequest(path, options)
}

/**
 * Make a POST request with FormData (for file uploads)
 */
export const apiPostFormData = async (path: string, formData: FormData): Promise<Response> => {
  const token = getAuthToken()
  if (!token) {
    throw new Error('No authentication token found')
  }

  const url = createApiUrl(path)

  console.log('🌐 API FormData Request:', {
    method: 'POST',
    url,
    hasToken: !!token,
    formDataKeys: Array.from(formData.keys())
  })

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        // Don't set Content-Type for FormData - let browser set it with boundary
      },
      body: formData,
    })

    console.log('📡 API FormData Response:', {
      url,
      status: response.status,
      ok: response.ok
    })

    return response
  } catch (error) {
    console.error('❌ API FormData request failed:', { url, error })
    throw error
  }
}

/**
 * Handle API response and extract JSON data
 */
export const handleApiResponse = async <T = any>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}))

    // Handle specific authorization errors
    if (response.status === 401) {
      console.error('🔒 Authorization failed - token may be invalid or expired')

      // Only clear localStorage if the error specifically indicates token issues
      // Don't automatically clear on every 401 - let the auth store handle it
      const errorMessage = errorData.message || errorData.error || ''
      if (errorMessage.includes('token') || errorMessage.includes('expired') || errorMessage.includes('invalid')) {
        console.warn('🗑️ Clearing potentially invalid auth tokens')
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_token')
          localStorage.removeItem('auth-storage')
        }
      } else {
        console.warn('⚠️ 401 error but not clearing tokens - may be permission issue')
      }
    }

    if (response.status === 403) {
      console.error('🚫 Access forbidden - insufficient permissions')
    }

    throw new Error(errorData.message || errorData.error || `HTTP ${response.status}`)
  }

  return response.json()
}

/**
 * Make a complete API call with error handling and JSON parsing
 */
export const apiCall = async <T = any>(
  path: string,
  options: RequestInit = {}
): Promise<T> => {
  const response = await apiRequest(path, options)
  return handleApiResponse<T>(response)
}

/**
 * Verify if authorization token is available and valid format
 */
export const verifyAuthToken = (): { hasToken: boolean; tokenInfo: string } => {
  const token = getAuthToken()
  return {
    hasToken: !!token,
    tokenInfo: token ? `${token.substring(0, 10)}...${token.substring(token.length - 10)}` : 'No token'
  }
}

/**
 * Debug function to check current auth state
 */
export const debugAuthState = () => {
  if (typeof window === 'undefined') return { error: 'Not in browser' }

  const directToken = localStorage.getItem('auth_token')
  const authStorage = localStorage.getItem('auth-storage')
  const userData = localStorage.getItem('user_data')

  let zustandToken = null
  try {
    if (authStorage) {
      const parsed = JSON.parse(authStorage)
      zustandToken = parsed?.state?.token
    }
  } catch (e) {
    // ignore
  }

  return {
    directToken: directToken ? `${directToken.substring(0, 10)}...` : null,
    zustandToken: zustandToken ? `${zustandToken.substring(0, 10)}...` : null,
    hasUserData: !!userData,
    authStorageKeys: authStorage ? Object.keys(JSON.parse(authStorage)) : [],
    currentToken: getAuthToken()
  }
}

/**
 * Convenience methods for common API operations
 */
export const api = {
  get: async <T = any>(path: string, params?: Record<string, string>): Promise<T> => {
    const response = await apiGet(path, params)
    return handleApiResponse<T>(response)
  },

  post: async <T = any>(path: string, data?: any): Promise<T> => {
    const response = await apiPost(path, data)
    return handleApiResponse<T>(response)
  },

  postFormData: async <T = any>(path: string, formData: FormData): Promise<T> => {
    const response = await apiPostFormData(path, formData)
    return handleApiResponse<T>(response)
  },

  put: async <T = any>(path: string, data?: any): Promise<T> => {
    const response = await apiPut(path, data)
    return handleApiResponse<T>(response)
  },

  patch: async <T = any>(path: string, data?: any): Promise<T> => {
    const response = await apiPatch(path, data)
    return handleApiResponse<T>(response)
  },

  delete: async <T = any>(path: string): Promise<T> => {
    const response = await apiDelete(path)
    return handleApiResponse<T>(response)
  },

  deleteWithBody: async <T = any>(path: string, data?: any): Promise<T> => {
    const response = await apiDeleteWithBody(path, data)
    return handleApiResponse<T>(response)
  },

  // Utility method to check auth status
  checkAuth: () => verifyAuthToken(),

  // Utility method to get auth token
  getToken: () => getAuthToken(),
}
