import { api } from '@/lib/api'

export interface Course {
  id: string
  title: string
  description?: string
  instructor: string
  status: 'draft' | 'published' | 'archived'
  thumbnail?: string
  createdAt: string
  updatedAt: string
}

export interface PaginatedCourses {
  docs: Course[]
  totalDocs: number
  limit: number
  totalPages: number
  page: number
  pagingCounter: number
  hasPrevPage: boolean
  hasNextPage: boolean
  prevPage: number | null
  nextPage: number | null
}

export const coursesApi = {
  getAllCourses: async (): Promise<PaginatedCourses> => {
    const response = await api.get('/courses')
    return response.data
  },
  createCourse: async (courseData: { title: string; description?: string }): Promise<Course> => {
    const response = await api.post('/courses', courseData)
    return response.data
  },
}