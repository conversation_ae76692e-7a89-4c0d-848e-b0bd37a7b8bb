// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    // First try direct auth_token
    let token = localStorage.getItem('auth_token')

    // If not found, try Zustand auth storage
    if (!token) {
      try {
        const authStorage = localStorage.getItem('auth-storage')
        if (authStorage) {
          const parsed = JSON.parse(authStorage)
          token = parsed?.state?.token || null
        }
      } catch (error) {
        console.error('Failed to parse auth storage:', error)
      }
    }

    return token
  }
  return null
}

// File upload types
export type UploadType = 'avatar' | 'course_thumbnail' | 'institute_logo' | 'platform_logo' | 'platform_favicon' | 'document' | 'general'

// Upload options
export interface UploadOptions {
  uploadType: UploadType
  updateUserField?: string // e.g., 'avatar' to update user.avatar field
  folder?: string
  onProgress?: (progress: number) => void
}

// Upload result
export interface UploadResult {
  success: boolean
  message: string
  media?: {
    id: string
    filename: string
    url: string
    sizes?: Record<string, {
      url: string
      width: number
      height: number
    }>
    alt: string
    mediaType: string
  }
  user?: any // Updated user object if updateUserField was used
  uploadType: string
}

// File info
export interface FileInfo {
  id: string
  filename: string
  url: string
  mediaType: string
  filesize: number
  mimeType: string
  alt?: string
  createdAt: string
  updatedAt: string
  sizes?: Record<string, any>
}

// Files response
export interface FilesResponse {
  success: boolean
  files: FileInfo[]
  totalDocs: number
  totalPages: number
  page: number
  limit: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

class FileUploadAPI {
  private baseUrl: string

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'
  }

  /**
   * Upload a file using the common upload API
   */
  async uploadFile(file: File, options: UploadOptions): Promise<UploadResult> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('uploadType', options.uploadType)
      
      if (options.updateUserField) {
        formData.append('updateUserField', options.updateUserField)
      }
      
      if (options.folder) {
        formData.append('folder', options.folder)
      }

      console.log('📤 Uploading file:', {
        name: file.name,
        size: file.size,
        type: file.type,
        uploadType: options.uploadType,
        updateUserField: options.updateUserField
      })

      const token = getAuthToken()
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`${this.baseUrl}/api/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: UploadResult = await response.json()
      
      console.log('📦 Upload result:', result)
      
      return result
    } catch (error) {
      console.error('❌ Upload error:', error)
      throw error
    }
  }

  /**
   * Upload profile avatar (convenience method)
   */
  async uploadAvatar(file: File): Promise<UploadResult> {
    return this.uploadFile(file, {
      uploadType: 'avatar',
      updateUserField: 'avatar',
      folder: 'avatars'
    })
  }

  /**
   * Upload course thumbnail (convenience method)
   */
  async uploadCourseThumbnail(file: File): Promise<UploadResult> {
    return this.uploadFile(file, {
      uploadType: 'course_thumbnail',
      folder: 'courses'
    })
  }

  /**
   * Upload institute logo (convenience method)
   */
  async uploadInstituteLogo(file: File): Promise<UploadResult> {
    return this.uploadFile(file, {
      uploadType: 'institute_logo',
      folder: 'institutes'
    })
  }

  /**
   * Upload document (convenience method)
   */
  async uploadDocument(file: File): Promise<UploadResult> {
    return this.uploadFile(file, {
      uploadType: 'document',
      folder: 'documents'
    })
  }

  /**
   * Upload platform logo (convenience method)
   */
  async uploadPlatformLogo(file: File): Promise<UploadResult> {
    return this.uploadFile(file, {
      uploadType: 'platform_logo',
      folder: 'platform'
    })
  }

  /**
   * Upload platform favicon (convenience method)
   */
  async uploadPlatformFavicon(file: File): Promise<UploadResult> {
    return this.uploadFile(file, {
      uploadType: 'platform_favicon',
      folder: 'platform'
    })
  }

  /**
   * Get user's uploaded files
   */
  async getMyFiles(options: {
    mediaType?: string
    page?: number
    limit?: number
  } = {}): Promise<FilesResponse> {
    try {
      const params = new URLSearchParams()
      
      if (options.mediaType) {
        params.append('mediaType', options.mediaType)
      }
      if (options.page) {
        params.append('page', options.page.toString())
      }
      if (options.limit) {
        params.append('limit', options.limit.toString())
      }

      const url = `${this.baseUrl}/api/upload/my-files${params.toString() ? '?' + params.toString() : ''}`
      
      const token = getAuthToken()
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: FilesResponse = await response.json()
      
      console.log('📂 My files:', result)
      
      return result
    } catch (error) {
      console.error('❌ Get files error:', error)
      throw error
    }
  }

  /**
   * Get user's avatars only
   */
  async getMyAvatars(): Promise<FilesResponse> {
    return this.getMyFiles({ mediaType: 'user_avatar' })
  }

  /**
   * Delete a file
   */
  async deleteFile(fileId: string): Promise<{ success: boolean; message: string }> {
    try {
      const token = getAuthToken()
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`${this.baseUrl}/api/upload/${fileId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      console.log('🗑️ Delete result:', result)
      
      return result
    } catch (error) {
      console.error('❌ Delete error:', error)
      throw error
    }
  }

  /**
   * Get full file URL
   */
  getFileUrl(path: string): string {
    if (path.startsWith('http')) {
      return path // Already a full URL (S3)
    }

    // For local storage, use the Next.js media route handler on port 3001
    // This uses the /media/[...path]/route.ts handler we created
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'
    return `${backendUrl}${path}` // Direct backend URL with media route handler
  }

  /**
   * Get avatar URL for specific size
   */
  getAvatarUrl(media: FileInfo, size: 'small' | 'medium' | 'large' | 'profile' = 'medium'): string {
    const sizeKey = `avatar_${size}`
    
    if (media.sizes && media.sizes[sizeKey]) {
      return this.getFileUrl(media.sizes[sizeKey].url)
    }
    
    // Fallback to original image
    return this.getFileUrl(media.url)
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File, uploadType: UploadType): { valid: boolean; message?: string } {
    const validations: Record<UploadType, { maxSize: number; allowedTypes: string[] }> = {
      avatar: {
        maxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
      },
      course_thumbnail: {
        maxSize: 10 * 1024 * 1024, // 10MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
      },
      institute_logo: {
        maxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      },
      platform_logo: {
        maxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      },
      platform_favicon: {
        maxSize: 2 * 1024 * 1024, // 2MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/x-icon', 'image/vnd.microsoft.icon']
      },
      document: {
        maxSize: 50 * 1024 * 1024, // 50MB
        allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
      },
      general: {
        maxSize: 25 * 1024 * 1024, // 25MB
        allowedTypes: [] // Allow all types
      }
    }

    const config = validations[uploadType]
    
    // Check file size
    if (file.size > config.maxSize) {
      return {
        valid: false,
        message: `File size must be less than ${Math.round(config.maxSize / 1024 / 1024)}MB`
      }
    }

    // Check file type (if restrictions exist)
    if (config.allowedTypes.length > 0 && !config.allowedTypes.includes(file.type)) {
      return {
        valid: false,
        message: `File type ${file.type} is not allowed for ${uploadType}`
      }
    }

    return { valid: true }
  }
}

// Export singleton instance
export const fileUploadAPI = new FileUploadAPI()

// Export convenience functions
export const uploadAvatar = (file: File) => fileUploadAPI.uploadAvatar(file)
export const uploadCourseThumbnail = (file: File) => fileUploadAPI.uploadCourseThumbnail(file)
export const uploadInstituteLogo = (file: File) => fileUploadAPI.uploadInstituteLogo(file)
export const uploadPlatformLogo = (file: File) => fileUploadAPI.uploadPlatformLogo(file)
export const uploadPlatformFavicon = (file: File) => fileUploadAPI.uploadPlatformFavicon(file)
export const uploadDocument = (file: File) => fileUploadAPI.uploadDocument(file)
export const getMyFiles = (options?: Parameters<typeof fileUploadAPI.getMyFiles>[0]) => fileUploadAPI.getMyFiles(options)
export const getMyAvatars = () => fileUploadAPI.getMyAvatars()
export const deleteFile = (fileId: string) => fileUploadAPI.deleteFile(fileId)
export const getFileUrl = (path: string) => fileUploadAPI.getFileUrl(path)
export const getAvatarUrl = (media: FileInfo, size?: Parameters<typeof fileUploadAPI.getAvatarUrl>[1]) => fileUploadAPI.getAvatarUrl(media, size)
export const validateFile = (file: File, uploadType: UploadType) => fileUploadAPI.validateFile(file, uploadType)
