import { api } from '@/lib/api'

export interface ProcessingJob {
  id: string
  userId: string
  instituteId: string
  branchId?: string
  type: 'image' | 'video' | 'audio' | 'batch'
  filePath: string
  mimeType: string
  options: any
  priority: 'low' | 'normal' | 'high' | 'urgent'
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  result?: any
  error?: string
  createdAt: string
  startedAt?: string
  completedAt?: string
  retryCount: number
  maxRetries: number
  metadata?: Record<string, any>
  additionalDetails?: {
    videoProcessing?: {
      status: string
      progress: number
      result?: any
    }
  }
}

export interface QueueStats {
  total: number
  pending: number
  processing: number
  completed: number
  failed: number
  cancelled: number
  averageProcessingTime: number
  throughput: number
}

export interface SystemHealth {
  videoProcessingService: {
    status: 'healthy' | 'degraded' | 'down'
    activeJobs: number
    queueLength: number
    averageProcessingTime: number
  }
  documentProcessingService: {
    status: 'healthy' | 'degraded' | 'down'
    activeJobs: number
    queueLength: number
    averageProcessingTime: number
  }
}

export interface PerformanceMetrics {
  totalJobsToday: number
  completedJobsToday: number
  failedJobsToday: number
  averageProcessingTime: number
  throughput: number
  successRate: number
}

export interface DashboardOverview {
  queueStats: QueueStats
  systemHealth: SystemHealth
  performanceMetrics: PerformanceMetrics
  recentActivity: ProcessingJob[]
}

export interface JobsResponse {
  jobs: ProcessingJob[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

class MediaDashboardAPI {
  /**
   * Get dashboard overview with statistics and recent activity
   */
  async getDashboardOverview(): Promise<DashboardOverview> {
    const response = await api.get('/admin/media-dashboard/overview')
    return response.data.data
  }

  /**
   * Get processing jobs with filtering and pagination
   */
  async getProcessingJobs(params?: {
    page?: number
    limit?: number
    status?: ProcessingJob['status']
    type?: ProcessingJob['type']
    priority?: ProcessingJob['priority']
    search?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }): Promise<JobsResponse> {
    const searchParams = new URLSearchParams()
    
    if (params?.page) searchParams.set('page', params.page.toString())
    if (params?.limit) searchParams.set('limit', params.limit.toString())
    if (params?.status) searchParams.set('status', params.status)
    if (params?.type) searchParams.set('type', params.type)
    if (params?.priority) searchParams.set('priority', params.priority)
    if (params?.search) searchParams.set('search', params.search)
    if (params?.sortBy) searchParams.set('sortBy', params.sortBy)
    if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder)

    const response = await api.get(`/admin/media-dashboard/jobs?${searchParams.toString()}`)
    return response.data.data
  }

  /**
   * Get detailed job information
   */
  async getJobDetails(jobId: string): Promise<ProcessingJob> {
    const response = await api.get(`/admin/media-dashboard/jobs/${jobId}`)
    return response.data.data
  }

  /**
   * Retry a failed job
   */
  async retryJob(jobId: string) {
    const response = await api.post(`/admin/media-dashboard/jobs/${jobId}/retry`)
    return response.data
  }

  /**
   * Cancel a pending or processing job
   */
  async cancelJob(jobId: string) {
    const response = await api.post(`/admin/media-dashboard/jobs/${jobId}/cancel`)
    return response.data
  }

  /**
   * Update job priority
   */
  async updateJobPriority(jobId: string, priority: ProcessingJob['priority']) {
    const response = await api.put(`/admin/media-dashboard/jobs/${jobId}/priority`, { priority })
    return response.data
  }

  /**
   * Get job status options for filtering
   */
  getJobStatusOptions() {
    return [
      { value: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
      { value: 'processing', label: 'Processing', color: 'bg-blue-100 text-blue-800' },
      { value: 'completed', label: 'Completed', color: 'bg-green-100 text-green-800' },
      { value: 'failed', label: 'Failed', color: 'bg-red-100 text-red-800' },
      { value: 'cancelled', label: 'Cancelled', color: 'bg-gray-100 text-gray-800' }
    ]
  }

  /**
   * Get job type options for filtering
   */
  getJobTypeOptions() {
    return [
      { value: 'video', label: 'Video', icon: '🎥' },
      { value: 'image', label: 'Image', icon: '🖼️' },
      { value: 'audio', label: 'Audio', icon: '🎵' },
      { value: 'batch', label: 'Batch', icon: '📦' }
    ]
  }

  /**
   * Get priority options for filtering and updating
   */
  getPriorityOptions() {
    return [
      { value: 'low', label: 'Low', color: 'bg-gray-100 text-gray-800' },
      { value: 'normal', label: 'Normal', color: 'bg-blue-100 text-blue-800' },
      { value: 'high', label: 'High', color: 'bg-orange-100 text-orange-800' },
      { value: 'urgent', label: 'Urgent', color: 'bg-red-100 text-red-800' }
    ]
  }

  /**
   * Format processing time for display
   */
  formatProcessingTime(milliseconds: number): string {
    if (milliseconds < 1000) {
      return `${milliseconds}ms`
    }
    
    const seconds = Math.floor(milliseconds / 1000)
    if (seconds < 60) {
      return `${seconds}s`
    }
    
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    if (minutes < 60) {
      return `${minutes}m ${remainingSeconds}s`
    }
    
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours}h ${remainingMinutes}m`
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * Get status color class for UI
   */
  getStatusColor(status: ProcessingJob['status']): string {
    const statusColors = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      processing: 'bg-blue-100 text-blue-800 border-blue-200',
      completed: 'bg-green-100 text-green-800 border-green-200',
      failed: 'bg-red-100 text-red-800 border-red-200',
      cancelled: 'bg-gray-100 text-gray-800 border-gray-200'
    }
    return statusColors[status] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  /**
   * Get priority color class for UI
   */
  getPriorityColor(priority: ProcessingJob['priority']): string {
    const priorityColors = {
      low: 'bg-gray-100 text-gray-800 border-gray-200',
      normal: 'bg-blue-100 text-blue-800 border-blue-200',
      high: 'bg-orange-100 text-orange-800 border-orange-200',
      urgent: 'bg-red-100 text-red-800 border-red-200'
    }
    return priorityColors[priority] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  /**
   * Calculate success rate percentage
   */
  calculateSuccessRate(completed: number, total: number): number {
    if (total === 0) return 0
    return Math.round((completed / total) * 100)
  }

  /**
   * Get system health status color
   */
  getHealthStatusColor(status: 'healthy' | 'degraded' | 'down'): string {
    const healthColors = {
      healthy: 'text-green-600',
      degraded: 'text-yellow-600',
      down: 'text-red-600'
    }
    return healthColors[status] || 'text-gray-600'
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string): string {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    
    return date.toLocaleDateString()
  }

  /**
   * Get progress bar color based on status
   */
  getProgressBarColor(status: ProcessingJob['status']): string {
    const progressColors = {
      pending: 'bg-yellow-500',
      processing: 'bg-blue-500',
      completed: 'bg-green-500',
      failed: 'bg-red-500',
      cancelled: 'bg-gray-500'
    }
    return progressColors[status] || 'bg-gray-500'
  }
}

export const mediaDashboardAPI = new MediaDashboardAPI()
