/**
 * Platform Settings API Client
 * Comprehensive API client for platform settings management
 */

import { api } from '@/lib/api'

export interface PlatformBranding {
  logo: MediaFile | null
  favicon: MediaFile | null
}

export interface MediaFile {
  id: string
  filename: string
  url: string
  filesize: number
  mimeType: string
  alt?: string
  sizes?: Record<string, {
    url: string
    width: number
    height: number
  }>
}

export interface StorageConfig {
  provider: 'local' | 's3'
  local?: {
    uploadDir: string
    baseUrl: string
    publicPath: string
  }
  s3?: {
    bucket: string
    region: string
    endpoint?: string
    publicUrl?: string
    cdnUrl?: string
    hasAccessKey: boolean
    hasSecretKey: boolean
  }
}

export interface StorageSummary {
  provider: string
  localConfigured: boolean
  s3Configured: boolean
  brandingConfigured: boolean
}

export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
}

export interface UploadResponse {
  success: boolean
  message: string
  data: {
    upload: {
      id: string
      filename: string
      url: string
      size: number
    }
    media: MediaFile
  }
}

class PlatformSettingsAPI {

  /**
   * Get platform settings (including branding)
   */
  async getPlatformBranding(): Promise<ApiResponse<PlatformBranding>> {
    return api.get('/api/platform/settings')
  }

  /**
   * Upload platform logo
   */
  async uploadPlatformLogo(file: File): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('uploadType', 'platform_logo')

    console.log('📤 Uploading platform logo:', {
      name: file.name,
      size: file.size,
      type: file.type
    })

    return api.postFormData('/api/file-upload', formData)
  }

  /**
   * Upload platform favicon
   */
  async uploadPlatformFavicon(file: File): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('uploadType', 'platform_favicon')

    console.log('📤 Uploading platform favicon:', {
      name: file.name,
      size: file.size,
      type: file.type
    })

    return api.postFormData('/api/file-upload', formData)
  }

  /**
   * Remove platform logo
   */
  async removePlatformLogo(): Promise<ApiResponse> {
    return api.post('/api/platform/settings/bulk', [
      { key: 'platform_logo', value: '', category: 'platform' }
    ])
  }

  /**
   * Remove platform favicon
   */
  async removePlatformFavicon(): Promise<ApiResponse> {
    return api.post('/api/platform/settings/bulk', [
      { key: 'platform_favicon', value: '', category: 'platform' }
    ])
  }

  /**
   * Process favicon from image (generates multiple sizes)
   */
  async processFavicon(file: File): Promise<ApiResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('uploadType', 'platform_favicon')
    formData.append('processFavicon', 'true')

    console.log('🔖 Processing favicon:', {
      name: file.name,
      size: file.size,
      type: file.type
    })

    return api.postFormData('/api/file-upload', formData)
  }

  /**
   * Get storage configuration
   */
  async getStorageConfig(): Promise<ApiResponse<StorageConfig>> {
    return api.get('/api/platform/storage/config')
  }

  /**
   * Update storage provider
   */
  async updateStorageProvider(provider: 'local' | 's3'): Promise<ApiResponse<{ provider: string }>> {
    return api.post('/api/platform/storage/provider', { provider })
  }

  /**
   * Update S3 configuration
   */
  async updateS3Config(config: {
    bucket: string
    region: string
    accessKeyId: string
    secretAccessKey: string
    endpoint?: string
    publicUrl?: string
    cdnUrl?: string
  }): Promise<ApiResponse> {
    return api.post('/api/platform/storage/s3', config)
  }

  /**
   * Test storage configuration
   */
  async testStorageConfig(): Promise<ApiResponse<{
    provider: string
    healthy: boolean
    validation: boolean
  }>> {
    return api.post('/api/platform/storage/test', {})
  }

  /**
   * Clear storage configuration cache
   */
  async clearStorageCache(): Promise<ApiResponse> {
    return api.post('/api/platform/storage/cache/clear', {})
  }

  /**
   * Initialize storage settings
   */
  async initStorageSettings(): Promise<ApiResponse<{
    summary: StorageSummary
    validation: {
      valid: boolean
      issues: string[]
    }
  }>> {
    return api.post('/api/platform/storage/init', {})
  }

  /**
   * Get storage settings summary
   */
  async getStorageSummary(): Promise<ApiResponse<{
    summary: StorageSummary
    validation: {
      valid: boolean
      issues: string[]
    }
  }>> {
    return api.get('/api/platform/storage/summary')
  }

  /**
   * Health check for upload system
   */
  async uploadHealthCheck(): Promise<ApiResponse<{
    healthy: boolean
    details: any
  }>> {
    return api.get('/api/platform/upload/health')
  }
}

// Create singleton instance
export const platformSettingsAPI = new PlatformSettingsAPI()

// Export convenience functions
export const getPlatformBranding = () => platformSettingsAPI.getPlatformBranding()
export const uploadPlatformLogo = (file: File) => platformSettingsAPI.uploadPlatformLogo(file)
export const uploadPlatformFavicon = (file: File) => platformSettingsAPI.uploadPlatformFavicon(file)
export const removePlatformLogo = () => platformSettingsAPI.removePlatformLogo()
export const removePlatformFavicon = () => platformSettingsAPI.removePlatformFavicon()
export const processFavicon = (file: File) => platformSettingsAPI.processFavicon(file)
export const getStorageConfig = () => platformSettingsAPI.getStorageConfig()
export const updateStorageProvider = (provider: 'local' | 's3') => platformSettingsAPI.updateStorageProvider(provider)
export const updateS3Config = (config: Parameters<typeof platformSettingsAPI.updateS3Config>[0]) => platformSettingsAPI.updateS3Config(config)
export const testStorageConfig = () => platformSettingsAPI.testStorageConfig()
export const clearStorageCache = () => platformSettingsAPI.clearStorageCache()
export const initStorageSettings = () => platformSettingsAPI.initStorageSettings()
export const getStorageSummary = () => platformSettingsAPI.getStorageSummary()
export const uploadHealthCheck = () => platformSettingsAPI.uploadHealthCheck()

export default platformSettingsAPI
