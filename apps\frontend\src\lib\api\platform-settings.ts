/**
 * Platform Settings API Client
 * Comprehensive API client for platform settings management
 */

import { getAuthToken } from '@/lib/auth'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'

export interface PlatformBranding {
  logo: MediaFile | null
  favicon: MediaFile | null
}

export interface MediaFile {
  id: string
  filename: string
  url: string
  filesize: number
  mimeType: string
  alt?: string
  sizes?: Record<string, {
    url: string
    width: number
    height: number
  }>
}

export interface StorageConfig {
  provider: 'local' | 's3'
  local?: {
    uploadDir: string
    baseUrl: string
    publicPath: string
  }
  s3?: {
    bucket: string
    region: string
    endpoint?: string
    publicUrl?: string
    cdnUrl?: string
    hasAccessKey: boolean
    hasSecretKey: boolean
  }
}

export interface StorageSummary {
  provider: string
  localConfigured: boolean
  s3Configured: boolean
  brandingConfigured: boolean
}

export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
}

export interface UploadResponse {
  success: boolean
  message: string
  data: {
    upload: {
      id: string
      filename: string
      url: string
      size: number
    }
    media: MediaFile
  }
}

class PlatformSettingsAPI {
  private baseUrl: string

  constructor() {
    this.baseUrl = API_BASE_URL
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    const url = `${this.baseUrl}${endpoint}`
    const config: RequestInit = {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    }

    console.log(`🌐 API Request: ${options.method || 'GET'} ${url}`)

    const response = await fetch(url, config)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      const errorMessage = errorData.message || `HTTP error! status: ${response.status}`
      console.error(`❌ API Error: ${errorMessage}`)
      throw new Error(errorMessage)
    }

    const result = await response.json()
    console.log(`✅ API Response:`, result)
    return result
  }

  /**
   * Get platform branding assets
   */
  async getPlatformBranding(): Promise<ApiResponse<PlatformBranding>> {
    return this.makeRequest<ApiResponse<PlatformBranding>>('/api/platform/settings/branding')
  }

  /**
   * Upload platform logo
   */
  async uploadPlatformLogo(file: File): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('file', file)

    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    console.log('📤 Uploading platform logo:', {
      name: file.name,
      size: file.size,
      type: file.type
    })

    const response = await fetch(`${this.baseUrl}/api/platform/settings/logo`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ Logo upload result:', result)
    return result
  }

  /**
   * Upload platform favicon
   */
  async uploadPlatformFavicon(file: File): Promise<UploadResponse> {
    const formData = new FormData()
    formData.append('file', file)

    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    console.log('📤 Uploading platform favicon:', {
      name: file.name,
      size: file.size,
      type: file.type
    })

    const response = await fetch(`${this.baseUrl}/api/platform/settings/favicon`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ Favicon upload result:', result)
    return result
  }

  /**
   * Remove platform logo
   */
  async removePlatformLogo(): Promise<ApiResponse> {
    return this.makeRequest<ApiResponse>('/api/platform/settings/logo', {
      method: 'DELETE'
    })
  }

  /**
   * Remove platform favicon
   */
  async removePlatformFavicon(): Promise<ApiResponse> {
    return this.makeRequest<ApiResponse>('/api/platform/settings/favicon', {
      method: 'DELETE'
    })
  }

  /**
   * Process favicon from image (generates multiple sizes)
   */
  async processFavicon(file: File): Promise<ApiResponse> {
    const formData = new FormData()
    formData.append('file', file)

    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    console.log('🔖 Processing favicon:', {
      name: file.name,
      size: file.size,
      type: file.type
    })

    const response = await fetch(`${this.baseUrl}/api/platform/favicon/process`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ Favicon processing result:', result)
    return result
  }

  /**
   * Get storage configuration
   */
  async getStorageConfig(): Promise<ApiResponse<StorageConfig>> {
    return this.makeRequest<ApiResponse<StorageConfig>>('/api/platform/storage/config')
  }

  /**
   * Update storage provider
   */
  async updateStorageProvider(provider: 'local' | 's3'): Promise<ApiResponse<{ provider: string }>> {
    return this.makeRequest<ApiResponse<{ provider: string }>>('/api/platform/storage/provider', {
      method: 'POST',
      body: JSON.stringify({ provider })
    })
  }

  /**
   * Update S3 configuration
   */
  async updateS3Config(config: {
    bucket: string
    region: string
    accessKeyId: string
    secretAccessKey: string
    endpoint?: string
    publicUrl?: string
    cdnUrl?: string
  }): Promise<ApiResponse> {
    return this.makeRequest<ApiResponse>('/api/platform/storage/s3', {
      method: 'POST',
      body: JSON.stringify(config)
    })
  }

  /**
   * Test storage configuration
   */
  async testStorageConfig(): Promise<ApiResponse<{
    provider: string
    healthy: boolean
    validation: boolean
  }>> {
    return this.makeRequest<ApiResponse<{
      provider: string
      healthy: boolean
      validation: boolean
    }>>('/api/platform/storage/test', {
      method: 'POST'
    })
  }

  /**
   * Clear storage configuration cache
   */
  async clearStorageCache(): Promise<ApiResponse> {
    return this.makeRequest<ApiResponse>('/api/platform/storage/cache/clear', {
      method: 'POST'
    })
  }

  /**
   * Initialize storage settings
   */
  async initStorageSettings(): Promise<ApiResponse<{
    summary: StorageSummary
    validation: {
      valid: boolean
      issues: string[]
    }
  }>> {
    return this.makeRequest<ApiResponse<{
      summary: StorageSummary
      validation: {
        valid: boolean
        issues: string[]
      }
    }>>('/api/platform/storage/init', {
      method: 'POST'
    })
  }

  /**
   * Get storage settings summary
   */
  async getStorageSummary(): Promise<ApiResponse<{
    summary: StorageSummary
    validation: {
      valid: boolean
      issues: string[]
    }
  }>> {
    return this.makeRequest<ApiResponse<{
      summary: StorageSummary
      validation: {
        valid: boolean
        issues: string[]
      }
    }>>('/api/platform/storage/summary')
  }

  /**
   * Health check for upload system
   */
  async uploadHealthCheck(): Promise<ApiResponse<{
    healthy: boolean
    details: any
  }>> {
    return this.makeRequest<ApiResponse<{
      healthy: boolean
      details: any
    }>>('/api/platform/upload/health')
  }
}

// Create singleton instance
export const platformSettingsAPI = new PlatformSettingsAPI()

// Export convenience functions
export const getPlatformBranding = () => platformSettingsAPI.getPlatformBranding()
export const uploadPlatformLogo = (file: File) => platformSettingsAPI.uploadPlatformLogo(file)
export const uploadPlatformFavicon = (file: File) => platformSettingsAPI.uploadPlatformFavicon(file)
export const removePlatformLogo = () => platformSettingsAPI.removePlatformLogo()
export const removePlatformFavicon = () => platformSettingsAPI.removePlatformFavicon()
export const processFavicon = (file: File) => platformSettingsAPI.processFavicon(file)
export const getStorageConfig = () => platformSettingsAPI.getStorageConfig()
export const updateStorageProvider = (provider: 'local' | 's3') => platformSettingsAPI.updateStorageProvider(provider)
export const updateS3Config = (config: Parameters<typeof platformSettingsAPI.updateS3Config>[0]) => platformSettingsAPI.updateS3Config(config)
export const testStorageConfig = () => platformSettingsAPI.testStorageConfig()
export const clearStorageCache = () => platformSettingsAPI.clearStorageCache()
export const initStorageSettings = () => platformSettingsAPI.initStorageSettings()
export const getStorageSummary = () => platformSettingsAPI.getStorageSummary()
export const uploadHealthCheck = () => platformSettingsAPI.uploadHealthCheck()

export default platformSettingsAPI
