/**
 * Settings API Client
 * 
 * API functions for managing platform settings
 */

import { api } from '../api'

// Types
export type SettingType = 'string' | 'number' | 'boolean' | 'json' | 'url' | 'email' | 'textarea' | 'upload' | 'media'
export type SettingCategory = 
  | 'platform' 
  | 'email' 
  | 'security' 
  | 'storage' 
  | 'payment' 
  | 'notification' 
  | 'integration' 
  | 'feature'

export interface ValidationRules {
  min_length?: number
  max_length?: number
  min_value?: number
  max_value?: number
  pattern?: string
}

export interface Setting {
  id: string
  key: string
  value: string
  description?: string
  category: SettingCategory
  type: SettingType
  is_public: boolean
  is_required?: boolean
  validation_rules?: ValidationRules
  upload?: string | { id: string; url: string; filename: string } // For file uploads
  mediaRef?: { id: string; url: string; filename: string } // For media type settings
  mediaData?: { id: string; url: string; filename: string } // Populated media data
  createdAt: string
  updatedAt: string
}

export interface SettingCreationData {
  key: string
  value: string
  description?: string
  category: SettingCategory
  type: SettingType
  is_public?: boolean
  is_required?: boolean
  validation_rules?: ValidationRules
  mediaRef?: string | number // For media type settings
}

export interface SettingUpdateData {
  key?: string
  value?: string
  description?: string
  category?: SettingCategory
  type?: SettingType
  is_public?: boolean
  is_required?: boolean
  validation_rules?: ValidationRules
}

export interface SettingsFilters {
  category?: SettingCategory
  type?: SettingType
  is_public?: boolean
  search?: string
}

export interface SettingsResponse {
  success: boolean
  settings: Setting[]
  totalDocs: number
  limit?: number
  totalPages?: number
  page?: number
  category?: string
}

export interface BulkUpdateSettingsRequest {
  settings: SettingCreationData[]
}

export interface BulkUpdateSettingsResponse {
  results: Array<{
    key: string
    operation: 'created' | 'updated'
    id: string
  }>
  errors: Array<{
    key: string
    error: string
  }>
  success: number
  failed: number
}

/**
 * Settings API functions
 */
export const settingsApi = {
  /**
   * Get all settings with optional filtering
   */
  async getSettings(filters?: SettingsFilters): Promise<SettingsResponse> {
    const params = new URLSearchParams()
    
    if (filters?.category) params.append('category', filters.category)
    if (filters?.type) params.append('type', filters.type)
    if (filters?.is_public !== undefined) params.append('is_public', filters.is_public.toString())
    if (filters?.search) params.append('search', filters.search)
    
    const queryString = params.toString()
    const url =  `/api/platform/settings?${queryString}` 

    return api.get(url)
  },

  /**
   * Get a specific setting by key
   */
  async getSettingByKey(key: string): Promise<Setting> {
    return api.get(`/api/platform/settings/${key}`)
  },

  /**
   * Get settings by category
   */
  async getSettingsByCategory(category: SettingCategory): Promise<SettingsResponse> {
    return api.get(`/api/platform/settings/category/${category}`)
  },

  /**
   * Create a new setting
   */
  async createSetting(data: SettingCreationData): Promise<Setting> {
    return api.post('/api/platform/settings', data)
  },

  /**
   * Update a setting by key
   */
  async updateSetting(key: string, data: SettingUpdateData): Promise<Setting> {
    return api.put(`/api/platform/settings/${key}`, data)
  },

  /**
   * Delete a setting by key
   */
  async deleteSetting(key: string): Promise<{ message: string }> {
    return api.delete(`/api/platform/settings/${key}`)
  },

  /**
   * Bulk update multiple settings
   */
  async bulkUpdateSettings(settings: SettingCreationData[]): Promise<BulkUpdateSettingsResponse> {
    return api.post('/api/platform/settings/bulk', { settings })
  },

  /**
   * Get public settings (no authentication required)
   */
  async getPublicSettings(): Promise<SettingsResponse> {
    return api.get('/api/platform/settings?is_public=true')
  },

  /**
   * Get platform configuration (commonly used settings)
   */
  async getPlatformConfig(): Promise<Record<string, string>> {
    const response = await this.getSettingsByCategory('platform')
    const config: Record<string, string> = {}
    
    response.settings.forEach(setting => {
      config[setting.key] = setting.value
    })
    
    return config
  },

  /**
   * Update platform configuration
   */
  async updatePlatformConfig(config: Record<string, string>): Promise<BulkUpdateSettingsResponse> {
    const settings: SettingCreationData[] = Object.entries(config).map(([key, value]) => ({
      key,
      value,
      category: 'platform',
      type: 'string',
    }))

    return this.bulkUpdateSettings(settings)
  },

  /**
   * Create or update a single setting (handles both creation and updates)
   */
  async createOrUpdateSetting(key: string, value: string, options: {
    category?: SettingCategory
    type?: SettingType
    description?: string
    is_public?: boolean
  } = {}): Promise<BulkUpdateSettingsResponse> {
    const setting: SettingCreationData = {
      key,
      value,
      category: options.category || 'platform',
      type: options.type || 'string',
      description: options.description,
      is_public: options.is_public !== undefined ? options.is_public : true
    }

    return this.bulkUpdateSettings([setting])
  },


}

/**
 * Helper functions for common setting operations
 */
export const settingsHelpers = {
  /**
   * Convert setting value to appropriate type
   */
  convertValue(value: string, type: SettingType): any {
    switch (type) {
      case 'number':
        return Number(value)
      case 'boolean':
        return value.toLowerCase() === 'true' || value === '1'
      case 'json':
        try {
          return JSON.parse(value)
        } catch {
          return null
        }
      default:
        return value
    }
  },

  /**
   * Validate setting value based on type
   */
  validateValue(value: string, type: SettingType): boolean {
    try {
      switch (type) {
        case 'number':
          return !isNaN(Number(value))
        case 'boolean':
          return ['true', 'false', '1', '0'].includes(value.toLowerCase())
        case 'json':
          JSON.parse(value)
          return true
        case 'email':
          return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
        case 'url':
          new URL(value)
          return true
        default:
          return true
      }
    } catch {
      return false
    }
  },
}

export default settingsApi
