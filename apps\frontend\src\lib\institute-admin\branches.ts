/**
 * Institute Admin API functions for branch management
 */

import { api, verifyAuthToken } from '../api'

// Types
export interface Branch {
  id: string
  name: string
  code: string
  institute: string
  location: {
    address: string
    country: {
      id: string
      name: string
    }
    state: {
      id: string
      name: string
    }
    district: {
      id: string
      name: string
    }
    pincode?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  contact?: {
    phone?: string
    email?: string
    website?: string
  }
  taxInformation?: {
    gstNumber?: string
    panNumber?: string
    taxRegistrationNumber?: string
    isGstRegistered?: boolean
  }

  isActive: boolean
  isHeadOffice?: boolean
  workingDays?: {
    monday?: boolean
    tuesday?: boolean
    wednesday?: boolean
    thursday?: boolean
    friday?: boolean
    saturday?: boolean
    sunday?: boolean
  }
  createdAt: string
  updatedAt: string
}

export interface CreateBranchData {
  name: string
  code?: string
  location: {
    address: string
    country: string
    state: string
    district: string
    pincode?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  contact?: {
    phone?: string
    email?: string
    website?: string
  }
  taxInformation?: {
    gstNumber?: string
    panNumber?: string
    taxRegistrationNumber?: string
    isGstRegistered?: boolean
  }

  isHeadOffice?: boolean
  workingDays?: {
    monday?: boolean
    tuesday?: boolean
    wednesday?: boolean
    thursday?: boolean
    friday?: boolean
    saturday?: boolean
    sunday?: boolean
  }

  // Operating hours fields (matching database columns)
  operatingHours?: {
    openTime?: string
    closeTime?: string
  }
}

export interface UpdateBranchData {
  name?: string
  code?: string
  location?: {
    address?: string
    country?: string
    state?: string
    district?: string
    pincode?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  contact?: {
    phone?: string
    email?: string
    website?: string
  }
  taxInformation?: {
    gstNumber?: string
    panNumber?: string
    taxRegistrationNumber?: string
    isGstRegistered?: boolean
  }

  isActive?: boolean
  isHeadOffice?: boolean
  workingDays?: {
    monday?: boolean
    tuesday?: boolean
    wednesday?: boolean
    thursday?: boolean
    friday?: boolean
    saturday?: boolean
    sunday?: boolean
  }
  // Soft delete fields
  isDeleted?: boolean
  deletedAt?: string | null
}

export interface BranchesResponse {
  success: boolean
  data: Branch[]
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalDocs: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export interface BranchParams {
  page?: number
  limit?: number
  search?: string
}

// API Functions
export const branchesApi = {
  /**
   * Get all branches for the institute
   */
  getAll: async (params?: BranchParams): Promise<BranchesResponse> => {
    const queryParams: Record<string, string> = {}
    
    if (params?.page) queryParams.page = params.page.toString()
    if (params?.limit) queryParams.limit = params.limit.toString()
    if (params?.search) queryParams.search = params.search
    
    return api.get('/api/institute-admin/branches', queryParams)
  },

  /**
   * Get branch details by ID
   */
  getById: async (id: string): Promise<{ success: boolean; data: Branch }> => {
    return api.get(`/api/institute-admin/branches/${id}`)
  },

  /**
   * Create a new branch
   */
  create: async (data: CreateBranchData): Promise<{ success: boolean; data: Branch; message: string }> => {
    console.log('📤 Frontend sending branch data:', JSON.stringify(data, null, 2))

    // Debug: Check auth state before API call
    const authState = verifyAuthToken()
    console.log('🔐 Auth state before API call:', authState)

    // Check if user is logged in
    if (!authState.hasToken) {
      console.error('❌ No auth token found! User might not be logged in.')
      throw new Error('Authentication required. Please login again.')
    }

    const result = await api.post('/api/institute-admin/branches', data)
    console.log('📥 Frontend received response:', result)
    return result
  },

  /**
   * Update branch details
   */
  update: async (id: string, data: UpdateBranchData): Promise<{ success: boolean; data: Branch; message: string }> => {
    return api.put(`/api/institute-admin/branches/${id}`, data)
  },

  /**
   * Delete a branch
   */
  delete: async (id: string): Promise<{ success: boolean; message: string }> => {
    return api.delete(`/api/institute-admin/branches/${id}`)
  },

  /**
   * Toggle branch status (active/inactive)
   */
  toggleStatus: async (id: string): Promise<{ success: boolean; data: Branch; message: string }> => {
    return api.put(`/api/institute-admin/branches/${id}/toggle-status`)
  },

  /**
   * Get branch statistics
   */
  getStats: async (branchId?: string): Promise<{
    success: boolean;
    data: {
      totalStudents: number
      totalStaff: number
      totalCourses: number
      activeEnrollments: number
      monthlyRevenue: number
      recentActivities: any[]
    }
  }> => {
    const url = branchId
      ? `/api/institute-admin/branches/${branchId}/stats`
      : '/api/institute-admin/branches/stats'
    return api.get(url)
  },

  /**
   * Get branch students
   */
  getStudents: async (branchId: string, params?: BranchParams): Promise<{
    success: boolean
    data: any[]
    pagination: {
      page: number
      limit: number
      totalPages: number
      totalDocs: number
      hasNextPage: boolean
      hasPrevPage: boolean
    }
  }> => {
    const queryParams: Record<string, string> = {}

    if (params?.page) queryParams.page = params.page.toString()
    if (params?.limit) queryParams.limit = params.limit.toString()
    if (params?.search) queryParams.search = params.search

    return api.get(`/api/institute-admin/branches/${branchId}/students`, queryParams)
  },

  /**
   * Get branch staff
   */
  getStaff: async (branchId: string, params?: BranchParams): Promise<{
    success: boolean
    data: any[]
    pagination: {
      page: number
      limit: number
      totalPages: number
      totalDocs: number
      hasNextPage: boolean
      hasPrevPage: boolean
    }
  }> => {
    const queryParams: Record<string, string> = {}

    if (params?.page) queryParams.page = params.page.toString()
    if (params?.limit) queryParams.limit = params.limit.toString()
    if (params?.search) queryParams.search = params.search

    return api.get(`/api/institute-admin/branches/${branchId}/staff`, queryParams)
  },

  /**
   * Check authorization status
   */
  checkAuth: () => verifyAuthToken()
}
