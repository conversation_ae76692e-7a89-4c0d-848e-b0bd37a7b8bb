/**
 * Institute Admin API functions for domain management
 */

import { api } from '../api'

// Types
export interface DomainRequest {
  id: string
  domainName: string
  status: 'pending' | 'reviewing' | 'approved' | 'rejected' | 'active'
  requestedAt: string
  reviewedAt?: string
  reviewedBy?: any
  rejectionReason?: string
  notes?: string
  dnsConfiguration?: {
    aRecord?: string
    cnameRecord?: string
    txtRecord?: string
  }
  sslStatus?: 'not_configured' | 'pending' | 'active' | 'failed'
}

export interface CreateDomainRequestData {
  domainName: string
  purpose?: string
}

// API Functions
export const domainApi = {
  /**
   * Create a new domain request
   */
  createRequest: async (data: CreateDomainRequestData): Promise<{ 
    success: boolean
    data: DomainRequest
    message: string 
  }> => {
    return api.post('/api/institute-admin/domain-request', data)
  },

  /**
   * Get domain request status
   */
  getStatus: async (): Promise<{ 
    success: boolean
    data: DomainRequest | null
    message?: string 
  }> => {
    return api.get('/api/institute-admin/domain-request/status')
  }
}
