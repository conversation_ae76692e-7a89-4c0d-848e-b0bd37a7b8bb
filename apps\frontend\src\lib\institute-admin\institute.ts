/**
 * Institute Admin API functions for institute management
 */

import { api } from '../api'

// Types
export interface Institute {
  id: string
  name: string
  tagline?: string
  email: string
  phone?: string
  website?: string
  description?: string
  customDomain?: string
  domainVerified?: boolean
  logo?: any
  favicon?: any
  createdAt: string
  updatedAt: string
}

export interface InstituteStats {
  totalStudents: number
  totalBranches: number
  totalCourses: number
  recentStudents: number
  growthRate: number
}

export interface UpdateInstituteData {
  name?: string
  tagline?: string
  email?: string
  phone?: string
  website?: string
  description?: string
  customDomain?: string
}

// API Functions
export const instituteApi = {
  /**
   * Get institute details
   */
  getDetails: async (): Promise<{ success: boolean; data: Institute }> => {
    return api.get('/api/institute-admin/institute')
  },

  /**
   * Update institute details
   */
  updateDetails: async (data: UpdateInstituteData): Promise<{ success: boolean; data: Institute; message: string }> => {
    return api.put('/api/institute-admin/institute', data)
  },

  /**
   * Get institute statistics
   */
  getStats: async (branch?: string): Promise<{ success: boolean; data: InstituteStats }> => {
    const params = branch ? { branch } : undefined
    return api.get('/api/institute-admin/institute/stats', params)
  },

  /**
   * Upload institute logo
   */
  uploadLogo: async (file: File): Promise<{ success: boolean; data: any; message: string }> => {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await fetch('/api/institute-admin/institute/logo', {
      method: 'POST',
      credentials: 'include',
      body: formData
    })
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || errorData.error || `HTTP ${response.status}`)
    }
    
    return response.json()
  },

  /**
   * Upload institute favicon
   */
  uploadFavicon: async (file: File): Promise<{ success: boolean; data: any; message: string }> => {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await fetch('/api/institute-admin/institute/favicon', {
      method: 'POST',
      credentials: 'include',
      body: formData
    })
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || errorData.error || `HTTP ${response.status}`)
    }
    
    return response.json()
  }
}
