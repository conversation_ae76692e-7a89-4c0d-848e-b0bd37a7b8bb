/**
 * Institute Admin Theme API functions
 */

import { api } from '../api'

// Types
export interface Theme {
  id: string
  name: string
  slug: string
  description?: string
  category?: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    text: string
  }
  fonts: {
    heading: string
    body: string
  }
  features: string[]
  preview?: string
  screenshots?: string[]
  customizableElements?: any
  usageCount?: number
  rating?: {
    average: number
    count: number
  }
  demoUrl?: string
}

export interface ThemeCustomizations {
  colors?: {
    primary?: string
    secondary?: string
    accent?: string
    background?: string
    text?: string
  }
  fonts?: {
    heading?: string
    body?: string
  }
  content?: {
    heroTitle?: string
    heroSubtitle?: string
    aboutText?: string
  }
}

export interface InstituteTheme {
  id: string
  institute: string
  theme: Theme
  isActive: boolean
  customizations: ThemeCustomizations
  appliedAt: string
  appliedBy?: {
    id: string
    name: string
  }
  notes?: string
}

export interface ThemeHistory {
  id: string
  theme: {
    id: string
    name: string
    slug: string
    preview?: string
  }
  isActive: boolean
  appliedAt: string
  appliedBy?: {
    id: string
    name: string
  }
  customizations: ThemeCustomizations
  notes?: string
}

export interface ApplyThemeData {
  themeId: string
  customizations?: ThemeCustomizations
}

// API Functions
export const themeApi = {
  /**
   * Get all available institute themes
   */
  getAll: async (): Promise<{ 
    success: boolean
    themes: Theme[]
    pagination: any
  }> => {
    return api.get('/api/institute-admin/themes')
  },

  /**
   * Get current institute's selected theme
   */
  getCurrent: async (): Promise<{ 
    success: boolean
    currentTheme: Theme | null
    themeCustomizations: ThemeCustomizations
    appliedAt?: string
  }> => {
    return api.get('/api/institute-admin/themes/current')
  },

  /**
   * Apply theme to institute
   */
  apply: async (data: ApplyThemeData): Promise<{ 
    success: boolean
    message: string
    instituteTheme: any
  }> => {
    return api.post('/api/institute-admin/themes/apply', data)
  },

  /**
   * Preview theme details
   */
  preview: async (themeId: string): Promise<{ 
    success: boolean
    theme: Theme
  }> => {
    return api.get(`/api/institute-admin/themes/${themeId}/preview`)
  },

  /**
   * Get theme application history
   */
  getHistory: async (): Promise<{ 
    success: boolean
    themeHistory: ThemeHistory[]
    pagination: any
  }> => {
    return api.get('/api/institute-admin/themes/history')
  },

  /**
   * Update theme customizations
   */
  updateCustomizations: async (customizations: ThemeCustomizations): Promise<{ 
    success: boolean
    message: string
    customizations: ThemeCustomizations
  }> => {
    return api.put('/api/institute-admin/themes/customizations', { customizations })
  }
}
