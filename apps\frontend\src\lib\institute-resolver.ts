// Institute and theme resolution utilities

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'

export async function getInstituteData(instituteId: string | null) {
  if (!instituteId) {
    console.log('❌ No institute ID provided')
    return null
  }

  try {
    const response = await fetch(`${API_URL}/api/institutes/${instituteId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add cache control for better performance
      next: { revalidate: 3600 } // Cache for 1 hour
    })

    if (!response.ok) {
      console.log(`❌ Institute API response not ok: ${response.status}`)
      return null
    }

    const data = await response.json()
    
    if (!data.success || !data.institute) {
      console.log(`❌ No institute data in response`)
      return null
    }

    console.log('✅ Institute data fetched:', {
      id: data.institute.id,
      name: data.institute.name,
      slug: data.institute.slug
    })

    return data.institute

  } catch (error) {
    console.error('❌ Error fetching institute data:', error)
    return null
  }
}

export async function getThemeData(themeId: string | null, instituteId: string | null) {
  if (!themeId) {
    console.log('❌ No theme ID provided, using default theme')
    return getDefaultTheme()
  }

  try {
    const response = await fetch(`${API_URL}/api/themes/${themeId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add cache control for better performance
      next: { revalidate: 3600 } // Cache for 1 hour
    })

    if (!response.ok) {
      console.log(`❌ Theme API response not ok: ${response.status}`)
      return getDefaultTheme()
    }

    const data = await response.json()
    
    if (!data.success || !data.theme) {
      console.log(`❌ No theme data in response`)
      return getDefaultTheme()
    }

    console.log('✅ Theme data fetched:', {
      id: data.theme.id,
      name: data.theme.name,
      type: data.theme.type
    })

    return data.theme

  } catch (error) {
    console.error('❌ Error fetching theme data:', error)
    return getDefaultTheme()
  }
}

export function getDefaultTheme() {
  return {
    id: 'education-modern',
    name: 'Education Modern',
    slug: 'education-modern',
    type: 'institute',
    colors: {
      primary: '#059669',
      secondary: '#6b7280',
      accent: '#f59e0b',
      background: '#ffffff',
      text: '#1f2937'
    },
    fonts: {
      heading: 'Poppins',
      body: 'Inter'
    },
    features: ['responsive', 'course_marketplace', 'customizable_branding']
  }
}

export async function getInstituteCourses(instituteId: string, options: {
  page?: number
  limit?: number
  category?: string
  featured?: boolean
} = {}) {
  const { page = 1, limit = 12, category, featured } = options

  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      institute: instituteId,
      status: 'published'
    })

    if (category) params.append('category', category)
    if (featured) params.append('featured', 'true')

    const response = await fetch(`${API_URL}/api/courses/marketplace?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 1800 } // Cache for 30 minutes
    })

    if (!response.ok) {
      console.log(`❌ Courses API response not ok: ${response.status}`)
      return { courses: [], pagination: null }
    }

    const data = await response.json()
    
    if (!data.success) {
      console.log(`❌ Courses API returned error`)
      return { courses: [], pagination: null }
    }

    return {
      courses: data.courses || [],
      pagination: data.pagination || null
    }

  } catch (error) {
    console.error('❌ Error fetching institute courses:', error)
    return { courses: [], pagination: null }
  }
}

export async function getFeaturedCourses(instituteId: string, limit: number = 6) {
  return getInstituteCourses(instituteId, { limit, featured: true })
}

// Utility function to resolve institute by domain (used by middleware)
export async function resolveInstituteByDomain(domain: string) {
  try {
    const response = await fetch(`${API_URL}/api/public/domain/resolve?domain=${domain}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    
    if (!data.success || !data.institute) {
      return null
    }

    return data.institute

  } catch (error) {
    console.error('❌ Error resolving institute by domain:', error)
    return null
  }
}
