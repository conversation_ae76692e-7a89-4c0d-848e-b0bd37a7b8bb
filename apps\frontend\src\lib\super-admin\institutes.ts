/**
 * Super Admin API functions for institute management
 */

import { api } from '../api'

// Types
export interface Institute {
  id: string | number
  name: string
  slug: string
  email?: string
  phone?: string
  website?: string
  tagline?: string
  logo?: any
  addressStreet?: string
  cityId?: string
  stateId?: string
  countryId?: string
  districtId?: string
  zipCode?: string
  customDomain?: string
  domainVerified: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string
}

export interface InstituteFormData {
  // Institute data
  name: string
  email?: string
  phone?: string
  website?: string
  tagline?: string
  logo?: string
  addressStreet?: string
  cityId?: string
  stateId?: string
  countryId?: string
  districtId?: string
  zipCode?: string
  customDomain?: string
  // Admin user data
  adminFirstName: string
  adminLastName: string
  adminEmail: string
  adminPassword: string
}

export interface InstituteFilters {
  search?: string
  isActive?: boolean
  domainVerified?: boolean
}

export interface InstituteStatistics {
  total: number
  active: number
  inactive: number
  verifiedDomains: number
  recentlyCreated: number
}

export interface InstitutesResponse {
  success: boolean
  docs: Institute[]
  totalDocs: number
  limit: number
  page: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// API Functions
export const superAdminInstitutesApi = {
  /**
   * Get all institutes with pagination and filters
   */
  getAll: async (page = 1, filters?: InstituteFilters): Promise<InstitutesResponse> => {
    const queryParams: Record<string, string> = {
      page: page.toString(),
      limit: '20'
    }

    if (filters?.search) queryParams.search = filters.search
    if (filters?.isActive !== undefined) queryParams.isActive = filters.isActive.toString()
    if (filters?.domainVerified !== undefined) queryParams.domainVerified = filters.domainVerified.toString()

    return api.get('/api/institute-management/institutes', queryParams)
  },

  /**
   * Get institute statistics
   */
  getStatistics: async (): Promise<{ success: boolean; data: InstituteStatistics }> => {
    return api.get('/api/institute-management/statistics')
  },

  /**
   * Create a new institute with admin user
   */
  create: async (data: InstituteFormData): Promise<{ success: boolean; data: Institute; message: string }> => {
    return api.post('/api/institute-management/institutes', data)
  },

  /**
   * Update institute details
   */
  update: async (id: string, data: Partial<Institute>): Promise<{ success: boolean; data: Institute; message: string }> => {
    return api.put(`/api/institute-management/institutes/${id}`, data)
  },

  /**
   * Delete an institute
   */
  delete: async (id: string): Promise<{ success: boolean; message: string }> => {
    return api.delete(`/api/institute-management/institutes/${id}`)
  },

  /**
   * Verify institute domain
   */
  verifyDomain: async (id: string): Promise<{ success: boolean; message: string }> => {
    return api.post(`/api/institute-management/institutes/${id}/verify-domain`)
  },

  /**
   * Toggle institute status (active/inactive)
   */
  toggleStatus: async (id: string): Promise<{ success: boolean; data: Institute; message: string }> => {
    return api.put(`/api/institute-management/institutes/${id}/toggle-status`)
  }
}
