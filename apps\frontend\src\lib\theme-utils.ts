/**
 * Theme utility functions for handling theme changes across the application
 */

export interface ThemeChangeEvent {
  themeId?: string
  theme?: any
  source?: string
}

/**
 * Dispatch a theme change event to notify all components about theme updates
 */
export function notifyThemeChange(data: ThemeChangeEvent) {
  if (typeof window !== 'undefined') {
    console.log('🎨 Dispatching theme change event:', data)
    
    window.dispatchEvent(new CustomEvent('themeChanged', {
      detail: data
    }))
  }
}

/**
 * Listen for theme change events
 */
export function onThemeChange(callback: (event: ThemeChangeEvent) => void) {
  if (typeof window === 'undefined') return () => {}

  const handleThemeChanged = (event: CustomEvent) => {
    callback(event.detail)
  }

  window.addEventListener('themeChanged', handleThemeChanged as EventListener)
  
  return () => {
    window.removeEventListener('themeChanged', handleThemeChanged as EventListener)
  }
}

/**
 * Refresh theme data across all components
 */
export function refreshAllThemes() {
  notifyThemeChange({
    source: 'global-refresh'
  })
}

/**
 * Apply CSS variables for theme colors
 */
export function applyThemeVariables(theme: any, customizations?: any) {
  if (typeof document === 'undefined') return

  const root = document.documentElement
  const colors = { ...theme.colors, ...customizations?.colors }
  const fonts = { ...theme.fonts, ...customizations?.fonts }

  // Apply color variables
  if (colors) {
    Object.entries(colors).forEach(([key, value]) => {
      if (typeof value === 'string') {
        root.style.setProperty(`--theme-${key}`, value)
      }
    })
  }

  // Apply font variables
  if (fonts) {
    Object.entries(fonts).forEach(([key, value]) => {
      if (typeof value === 'string') {
        root.style.setProperty(`--theme-font-${key}`, value)
      }
    })
  }

  console.log('✅ Theme variables applied:', { colors, fonts })
}

/**
 * Remove theme variables from CSS
 */
export function removeThemeVariables() {
  if (typeof document === 'undefined') return

  const root = document.documentElement
  const styles = root.style

  // Remove theme-related CSS variables
  for (let i = styles.length - 1; i >= 0; i--) {
    const property = styles[i]
    if (property.startsWith('--theme-')) {
      root.style.removeProperty(property)
    }
  }

  console.log('🧹 Theme variables removed')
}

/**
 * Get current theme from localStorage (fallback)
 */
export function getCurrentThemeFromStorage(): any | null {
  if (typeof window === 'undefined') return null

  try {
    const stored = localStorage.getItem('current-theme')
    return stored ? JSON.parse(stored) : null
  } catch (error) {
    console.error('Failed to get theme from storage:', error)
    return null
  }
}

/**
 * Save current theme to localStorage (fallback)
 */
export function saveCurrentThemeToStorage(theme: any) {
  if (typeof window === 'undefined') return

  try {
    localStorage.setItem('current-theme', JSON.stringify(theme))
    console.log('💾 Theme saved to storage:', theme.name)
  } catch (error) {
    console.error('Failed to save theme to storage:', error)
  }
}

/**
 * Clear theme from localStorage
 */
export function clearThemeFromStorage() {
  if (typeof window === 'undefined') return

  try {
    localStorage.removeItem('current-theme')
    console.log('🗑️ Theme cleared from storage')
  } catch (error) {
    console.error('Failed to clear theme from storage:', error)
  }
}
