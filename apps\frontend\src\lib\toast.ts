// Simple toast implementation without external dependencies
export const showToast = {
  success: (message: string, description?: string) => {
    console.log('✅ SUCCESS:', message, description)
    alert(`✅ ${message}${description ? '\n' + description : ''}`)
  },

  error: (message: string, description?: string) => {
    console.log('❌ ERROR:', message, description)
    alert(`❌ ${message}${description ? '\n' + description : ''}`)
  },

  info: (message: string, description?: string) => {
    console.log('ℹ️ INFO:', message, description)
    alert(`ℹ️ ${message}${description ? '\n' + description : ''}`)
  },

  loading: (message: string) => {
    console.log('⏳ LOADING:', message)
    return message
  },

  dismiss: (toastId?: string | number) => {
    console.log('🚫 DISMISS:', toastId)
  },

  // Authentication specific toasts
  loginSuccess: (description?: string) => {
    const message = 'Login successful!'
    const desc = description || 'Welcome back. Redirecting to dashboard...'
    console.log('✅ LOGIN SUCCESS:', message, desc)
    alert(`✅ ${message}\n${desc}`)
  },

  loginError: (message?: string) => {
    const msg = 'Login failed'
    const desc = message || 'Invalid email or password. Please try again.'
    console.log('❌ LOGIN ERROR:', msg, desc)
    alert(`❌ ${msg}\n${desc}`)
  },

  registerSuccess: (description?: string) => {
    const message = 'Registration successful!'
    const desc = description || 'Please check your email for verification instructions.'
    console.log('✅ REGISTER SUCCESS:', message, desc)
    alert(`✅ ${message}\n${desc}`)
  },

  registerError: (message?: string) => {
    const msg = 'Registration failed'
    const desc = message || 'Please check your information and try again.'
    console.log('❌ REGISTER ERROR:', msg, desc)
    alert(`❌ ${msg}\n${desc}`)
  }
}
