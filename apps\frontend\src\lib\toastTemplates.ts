import { toast } from 'sonner'

export const studentToastMessages = {
  // Create operations
  createSuccess: (studentName: string) => 
    toast.success('Student Created', {
      description: `${studentName} has been successfully added to the system.`,
      duration: 4000
    }),
  
  createError: (error: string) =>
    toast.error('Failed to Create Student', {
      description: error || 'An unexpected error occurred while creating the student.',
      duration: 5000
    }),
  
  // Update operations
  updateSuccess: (studentName: string) =>
    toast.success('Student Updated', {
      description: `${studentName}'s information has been successfully updated.`,
      duration: 4000
    }),
  
  updateError: (error: string) =>
    toast.error('Failed to Update Student', {
      description: error || 'An unexpected error occurred while updating the student.',
      duration: 5000
    }),
  
  // Status change operations
  activateSuccess: (studentName: string) =>
    toast.success('Student Activated', {
      description: `${studentName} has been activated and can now access courses.`,
      duration: 4000
    }),
  
  deactivateSuccess: (studentName: string) =>
    toast.success('Student Deactivated', {
      description: `${studentName} has been deactivated and cannot access courses.`,
      duration: 4000
    }),
  
  statusChangeError: (error: string) =>
    toast.error('Failed to Change Status', {
      description: error || 'An unexpected error occurred while changing student status.',
      duration: 5000
    }),
  
  // Delete operations
  deleteSuccess: (studentName: string) =>
    toast.success('Student Removed', {
      description: `${studentName} has been successfully removed from the system.`,
      duration: 4000
    }),
  
  deleteError: (error: string) =>
    toast.error('Failed to Remove Student', {
      description: error || 'An unexpected error occurred while removing the student.',
      duration: 5000
    }),
  
  // Bulk operations
  bulkActivateSuccess: (count: number) =>
    toast.success('Students Activated', {
      description: `${count} student${count > 1 ? 's' : ''} have been successfully activated.`,
      duration: 4000
    }),
  
  bulkDeactivateSuccess: (count: number) =>
    toast.success('Students Deactivated', {
      description: `${count} student${count > 1 ? 's' : ''} have been successfully deactivated.`,
      duration: 4000
    }),
  
  bulkOperationError: (operation: string, error: string) =>
    toast.error(`Failed to ${operation} Students`, {
      description: error || `An unexpected error occurred during the ${operation} operation.`,
      duration: 5000
    }),
  
  // Loading states
  createLoading: () =>
    toast.loading('Creating student...', {
      description: 'Please wait while we add the new student to the system.'
    }),
  
  updateLoading: () =>
    toast.loading('Updating student...', {
      description: 'Please wait while we save the changes.'
    }),
  
  statusChangeLoading: (action: string) =>
    toast.loading(`${action} student...`, {
      description: `Please wait while we ${action.toLowerCase()} the student.`
    }),
  
  deleteLoading: () =>
    toast.loading('Removing student...', {
      description: 'Please wait while we remove the student from the system.'
    }),
  
  // Validation errors
  validationError: (field: string, message: string) =>
    toast.error('Validation Error', {
      description: `${field}: ${message}`,
      duration: 4000
    }),
  
  // Permission errors
  permissionError: (action: string) =>
    toast.error('Permission Denied', {
      description: `You don't have permission to ${action} students.`,
      duration: 5000
    }),
  
  // Network errors
  networkError: () =>
    toast.error('Network Error', {
      description: 'Please check your internet connection and try again.',
      duration: 5000
    })
}

// Usage hook for components
export const useStudentToasts = () => {
  return studentToastMessages
}

// Specific error handling for common scenarios
export const handleStudentApiError = (error: any, operation: string) => {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error'
  
  if (errorMessage.includes('email')) {
    studentToastMessages.validationError('Email', 'This email address is already in use')
  } else if (errorMessage.includes('permission')) {
    studentToastMessages.permissionError(operation)
  } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
    studentToastMessages.networkError()
  } else {
    switch (operation) {
      case 'create':
        studentToastMessages.createError(errorMessage)
        break
      case 'update':
        studentToastMessages.updateError(errorMessage)
        break
      case 'delete':
        studentToastMessages.deleteError(errorMessage)
        break
      case 'status':
        studentToastMessages.statusChangeError(errorMessage)
        break
      default:
        toast.error('Operation Failed', {
          description: errorMessage,
          duration: 5000
        })
    }
  }
}
