/**
 * Upload Progress Tracking Utilities
 * Comprehensive progress tracking for file uploads
 */

export interface UploadProgress {
  id: string
  filename: string
  filesize: number
  loaded: number
  total: number
  percentage: number
  speed: number // bytes per second
  timeRemaining: number // seconds
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error' | 'cancelled'
  error?: string
  startTime: number
  endTime?: number
}

export interface UploadProgressOptions {
  onProgress?: (progress: UploadProgress) => void
  onComplete?: (progress: UploadProgress) => void
  onError?: (progress: UploadProgress, error: Error) => void
  onCancel?: (progress: UploadProgress) => void
}

class UploadProgressTracker {
  private uploads = new Map<string, UploadProgress>()
  private intervals = new Map<string, NodeJS.Timeout>()

  /**
   * Start tracking upload progress
   */
  startTracking(
    id: string,
    file: File,
    options: UploadProgressOptions = {}
  ): UploadProgress {
    const progress: UploadProgress = {
      id,
      filename: file.name,
      filesize: file.size,
      loaded: 0,
      total: file.size,
      percentage: 0,
      speed: 0,
      timeRemaining: 0,
      status: 'pending',
      startTime: Date.now()
    }

    this.uploads.set(id, progress)

    // Start progress monitoring
    const interval = setInterval(() => {
      const currentProgress = this.uploads.get(id)
      if (currentProgress && currentProgress.status === 'uploading') {
        this.calculateSpeed(id)
        options.onProgress?.(currentProgress)
      }
    }, 1000)

    this.intervals.set(id, interval)

    return progress
  }

  /**
   * Update upload progress
   */
  updateProgress(
    id: string,
    loaded: number,
    options: UploadProgressOptions = {}
  ): UploadProgress | null {
    const progress = this.uploads.get(id)
    if (!progress) return null

    const now = Date.now()
    const elapsed = (now - progress.startTime) / 1000 // seconds
    
    progress.loaded = loaded
    progress.percentage = Math.round((loaded / progress.total) * 100)
    progress.status = loaded >= progress.total ? 'processing' : 'uploading'

    // Calculate speed (bytes per second)
    if (elapsed > 0) {
      progress.speed = loaded / elapsed
    }

    // Calculate time remaining
    if (progress.speed > 0) {
      const remaining = progress.total - loaded
      progress.timeRemaining = remaining / progress.speed
    }

    this.uploads.set(id, progress)
    options.onProgress?.(progress)

    return progress
  }

  /**
   * Mark upload as completed
   */
  completeUpload(
    id: string,
    options: UploadProgressOptions = {}
  ): UploadProgress | null {
    const progress = this.uploads.get(id)
    if (!progress) return null

    progress.status = 'completed'
    progress.endTime = Date.now()
    progress.percentage = 100
    progress.loaded = progress.total

    this.clearInterval(id)
    this.uploads.set(id, progress)
    
    options.onComplete?.(progress)
    return progress
  }

  /**
   * Mark upload as failed
   */
  failUpload(
    id: string,
    error: Error,
    options: UploadProgressOptions = {}
  ): UploadProgress | null {
    const progress = this.uploads.get(id)
    if (!progress) return null

    progress.status = 'error'
    progress.error = error.message
    progress.endTime = Date.now()

    this.clearInterval(id)
    this.uploads.set(id, progress)
    
    options.onError?.(progress, error)
    return progress
  }

  /**
   * Cancel upload
   */
  cancelUpload(
    id: string,
    options: UploadProgressOptions = {}
  ): UploadProgress | null {
    const progress = this.uploads.get(id)
    if (!progress) return null

    progress.status = 'cancelled'
    progress.endTime = Date.now()

    this.clearInterval(id)
    this.uploads.set(id, progress)
    
    options.onCancel?.(progress)
    return progress
  }

  /**
   * Get upload progress by ID
   */
  getProgress(id: string): UploadProgress | null {
    return this.uploads.get(id) || null
  }

  /**
   * Get all upload progress
   */
  getAllProgress(): UploadProgress[] {
    return Array.from(this.uploads.values())
  }

  /**
   * Remove upload from tracking
   */
  removeUpload(id: string): void {
    this.clearInterval(id)
    this.uploads.delete(id)
  }

  /**
   * Clear all uploads
   */
  clearAll(): void {
    this.intervals.forEach((interval) => clearInterval(interval))
    this.intervals.clear()
    this.uploads.clear()
  }

  /**
   * Calculate upload speed
   */
  private calculateSpeed(id: string): void {
    const progress = this.uploads.get(id)
    if (!progress) return

    const now = Date.now()
    const elapsed = (now - progress.startTime) / 1000

    if (elapsed > 0) {
      progress.speed = progress.loaded / elapsed
      
      if (progress.speed > 0) {
        const remaining = progress.total - progress.loaded
        progress.timeRemaining = remaining / progress.speed
      }
    }
  }

  /**
   * Clear interval for upload
   */
  private clearInterval(id: string): void {
    const interval = this.intervals.get(id)
    if (interval) {
      clearInterval(interval)
      this.intervals.delete(id)
    }
  }
}

// Create singleton instance
export const uploadProgressTracker = new UploadProgressTracker()

/**
 * Format upload speed for display
 */
export function formatUploadSpeed(bytesPerSecond: number): string {
  if (bytesPerSecond === 0) return '0 B/s'
  
  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  const k = 1024
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
  
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(2)) + ' ' + units[i]
}

/**
 * Format time remaining for display
 */
export function formatTimeRemaining(seconds: number): string {
  if (seconds === 0 || !isFinite(seconds)) return 'Unknown'
  
  if (seconds < 60) {
    return `${Math.round(seconds)}s`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.round(seconds % 60)
    return `${minutes}m ${remainingSeconds}s`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }
}

/**
 * Create XMLHttpRequest with progress tracking
 */
export function createProgressXHR(
  uploadId: string,
  options: UploadProgressOptions = {}
): XMLHttpRequest {
  const xhr = new XMLHttpRequest()

  xhr.upload.addEventListener('progress', (event) => {
    if (event.lengthComputable) {
      uploadProgressTracker.updateProgress(uploadId, event.loaded, options)
    }
  })

  xhr.upload.addEventListener('load', () => {
    uploadProgressTracker.completeUpload(uploadId, options)
  })

  xhr.upload.addEventListener('error', () => {
    uploadProgressTracker.failUpload(
      uploadId, 
      new Error('Upload failed'), 
      options
    )
  })

  xhr.upload.addEventListener('abort', () => {
    uploadProgressTracker.cancelUpload(uploadId, options)
  })

  return xhr
}

/**
 * Create fetch with progress tracking (using ReadableStream)
 */
export async function fetchWithProgress(
  url: string,
  options: RequestInit & {
    uploadId?: string
    file?: File
    progressOptions?: UploadProgressOptions
  } = {}
): Promise<Response> {
  const { uploadId, file, progressOptions, ...fetchOptions } = options

  if (uploadId && file) {
    // Start tracking
    uploadProgressTracker.startTracking(uploadId, file, progressOptions)
    
    // For now, we'll use a simple approach since fetch doesn't support upload progress natively
    // In a real implementation, you might want to use XMLHttpRequest for upload progress
    try {
      const response = await fetch(url, fetchOptions)
      
      if (response.ok) {
        uploadProgressTracker.completeUpload(uploadId, progressOptions)
      } else {
        uploadProgressTracker.failUpload(
          uploadId,
          new Error(`HTTP ${response.status}: ${response.statusText}`),
          progressOptions
        )
      }
      
      return response
    } catch (error) {
      uploadProgressTracker.failUpload(
        uploadId,
        error instanceof Error ? error : new Error('Upload failed'),
        progressOptions
      )
      throw error
    }
  }

  return fetch(url, fetchOptions)
}

/**
 * Generate unique upload ID
 */
export function generateUploadId(): string {
  return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

export default uploadProgressTracker
