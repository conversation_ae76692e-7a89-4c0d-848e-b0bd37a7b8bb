import * as Yup from 'yup'

/**
 * File Upload Validation Schemas
 * Comprehensive validation for different file upload types
 */

// File size constants (in bytes)
export const FILE_SIZE_LIMITS = {
  AVATAR: 5 * 1024 * 1024, // 5MB
  LOGO: 5 * 1024 * 1024, // 5MB
  FAVICON: 2 * 1024 * 1024, // 2MB
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  COURSE_THUMBNAIL: 10 * 1024 * 1024, // 10MB
  GENERAL: 5 * 1024 * 1024, // 5MB
} as const

// Supported file types
export const SUPPORTED_FILE_TYPES = {
  IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
  FAVICON: ['image/x-icon', 'image/png', 'image/gif', 'image/jpeg', 'image/jpg'],
  DOCUMENTS: ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  ALL_IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/x-icon'],
} as const

// File extension mappings
export const FILE_EXTENSIONS = {
  IMAGES: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
  FAVICON: ['.ico', '.png', '.gif', '.jpg', '.jpeg'],
  DOCUMENTS: ['.pdf', '.txt', '.doc', '.docx'],
} as const

/**
 * Custom Yup validation methods for files
 */
export const fileValidation = {
  /**
   * Validate file size
   */
  fileSize: (maxSize: number, message?: string) =>
    Yup.mixed().test(
      'fileSize',
      message || `File size must be less than ${formatFileSize(maxSize)}`,
      (value: any) => {
        if (!value) return true // Allow empty files (handled by required validation)
        if (value instanceof File) {
          return value.size <= maxSize
        }
        return true
      }
    ),

  /**
   * Validate file type by MIME type
   */
  fileType: (allowedTypes: string[], message?: string) =>
    Yup.mixed().test(
      'fileType',
      message || `File type not supported. Allowed types: ${allowedTypes.join(', ')}`,
      (value: any) => {
        if (!value) return true // Allow empty files (handled by required validation)
        if (value instanceof File) {
          return allowedTypes.includes(value.type)
        }
        return true
      }
    ),

  /**
   * Validate file extension
   */
  fileExtension: (allowedExtensions: string[], message?: string) =>
    Yup.mixed().test(
      'fileExtension',
      message || `File extension not supported. Allowed extensions: ${allowedExtensions.join(', ')}`,
      (value: any) => {
        if (!value) return true // Allow empty files (handled by required validation)
        if (value instanceof File) {
          const extension = '.' + value.name.split('.').pop()?.toLowerCase()
          return allowedExtensions.includes(extension)
        }
        return true
      }
    ),

  /**
   * Validate image dimensions (requires reading the file)
   */
  imageDimensions: (
    minWidth?: number,
    minHeight?: number,
    maxWidth?: number,
    maxHeight?: number,
    message?: string
  ) =>
    Yup.mixed().test(
      'imageDimensions',
      message || 'Image dimensions are not valid',
      async (value: any) => {
        if (!value) return true // Allow empty files
        if (!(value instanceof File)) return true
        if (!value.type.startsWith('image/')) return true

        return new Promise((resolve) => {
          const img = new Image()
          const url = URL.createObjectURL(value)

          img.onload = () => {
            URL.revokeObjectURL(url)
            
            let valid = true
            if (minWidth && img.width < minWidth) valid = false
            if (minHeight && img.height < minHeight) valid = false
            if (maxWidth && img.width > maxWidth) valid = false
            if (maxHeight && img.height > maxHeight) valid = false
            
            resolve(valid)
          }

          img.onerror = () => {
            URL.revokeObjectURL(url)
            resolve(false)
          }

          img.src = url
        })
      }
    ),

  /**
   * Validate aspect ratio
   */
  aspectRatio: (expectedRatio: number, tolerance: number = 0.1, message?: string) =>
    Yup.mixed().test(
      'aspectRatio',
      message || `Image aspect ratio should be approximately ${expectedRatio}:1`,
      async (value: any) => {
        if (!value) return true // Allow empty files
        if (!(value instanceof File)) return true
        if (!value.type.startsWith('image/')) return true

        return new Promise((resolve) => {
          const img = new Image()
          const url = URL.createObjectURL(value)

          img.onload = () => {
            URL.revokeObjectURL(url)
            const actualRatio = img.width / img.height
            const diff = Math.abs(actualRatio - expectedRatio)
            resolve(diff <= tolerance)
          }

          img.onerror = () => {
            URL.revokeObjectURL(url)
            resolve(false)
          }

          img.src = url
        })
      }
    ),
}

/**
 * Pre-built validation schemas for common upload types
 */
export const uploadValidationSchemas = {
  /**
   * Avatar upload validation
   */
  avatar: Yup.object({
    file: Yup.mixed()
      .required('Avatar image is required')
      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.AVATAR))
      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.IMAGES))
      .concat(fileValidation.imageDimensions(50, 50, 2048, 2048, 'Avatar should be between 50x50 and 2048x2048 pixels'))
  }),

  /**
   * Logo upload validation
   */
  logo: Yup.object({
    file: Yup.mixed()
      .required('Logo image is required')
      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.LOGO))
      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.IMAGES))
      .concat(fileValidation.imageDimensions(100, 50, 2048, 1024, 'Logo should be between 100x50 and 2048x1024 pixels'))
  }),

  /**
   * Favicon upload validation
   */
  favicon: Yup.object({
    file: Yup.mixed()
      .required('Favicon is required')
      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.FAVICON))
      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.FAVICON))
      .concat(fileValidation.imageDimensions(16, 16, 512, 512, 'Favicon should be between 16x16 and 512x512 pixels'))
  }),

  /**
   * Course thumbnail validation
   */
  courseThumbnail: Yup.object({
    file: Yup.mixed()
      .required('Course thumbnail is required')
      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.COURSE_THUMBNAIL))
      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.IMAGES))
      .concat(fileValidation.imageDimensions(300, 200, 1920, 1080, 'Thumbnail should be between 300x200 and 1920x1080 pixels'))
      .concat(fileValidation.aspectRatio(16/9, 0.2, 'Thumbnail should have approximately 16:9 aspect ratio'))
  }),

  /**
   * Document upload validation
   */
  document: Yup.object({
    file: Yup.mixed()
      .required('Document is required')
      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.DOCUMENT))
      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.DOCUMENTS))
  }),

  /**
   * General image upload validation
   */
  image: Yup.object({
    file: Yup.mixed()
      .required('Image is required')
      .concat(fileValidation.fileSize(FILE_SIZE_LIMITS.GENERAL))
      .concat(fileValidation.fileType(SUPPORTED_FILE_TYPES.IMAGES))
  }),
}

/**
 * Utility functions
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function getFileExtension(filename: string): string {
  return '.' + filename.split('.').pop()?.toLowerCase() || ''
}

export function isImageFile(file: File): boolean {
  return SUPPORTED_FILE_TYPES.ALL_IMAGES.includes(file.type)
}

export function validateFileClientSide(
  file: File,
  options: {
    maxSize?: number
    allowedTypes?: string[]
    allowedExtensions?: string[]
  } = {}
): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // Check file size
  if (options.maxSize && file.size > options.maxSize) {
    errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(options.maxSize)})`)
  }

  // Check file type
  if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
    errors.push(`File type (${file.type}) is not allowed. Allowed types: ${options.allowedTypes.join(', ')}`)
  }

  // Check file extension
  if (options.allowedExtensions) {
    const extension = getFileExtension(file.name)
    if (!options.allowedExtensions.includes(extension)) {
      errors.push(`File extension (${extension}) is not allowed. Allowed extensions: ${options.allowedExtensions.join(', ')}`)
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}
