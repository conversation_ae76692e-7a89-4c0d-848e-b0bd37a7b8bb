import * as Yup from 'yup'

// Staff creation validation schema
export const staffCreateValidationSchema = Yup.object({
  firstName: Yup.string()
    .required('First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'First name can only contain letters and spaces'),

  lastName: Yup.string()
    .required('Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Last name can only contain letters and spaces'),

  email: Yup.string()
    .required('Email is required')
    .email('Please enter a valid email address')
    .max(100, 'Email must be less than 100 characters'),

  phone: Yup.string()
    .matches(/^[+]?[\d\s\-\(\)]+$/, 'Please enter a valid phone number')
    .min(10, 'Phone number must be at least 10 digits')
    .max(15, 'Phone number must be less than 15 digits'),

  password: Yup.string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    ),

  legacyRole: Yup.string()
    .required('Role is required')
    .notOneOf(['select-role'], 'Please select a valid role'),

  branch_id: Yup.string()
    .notOneOf(['select-branch'], 'Please select a valid branch'),

  isActive: Yup.boolean()
    .default(true)
})

// Staff update validation schema (password is optional)
export const staffUpdateValidationSchema = Yup.object({
  firstName: Yup.string()
    .required('First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'First name can only contain letters and spaces'),

  lastName: Yup.string()
    .required('Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Last name can only contain letters and spaces'),

  email: Yup.string()
    .required('Email is required')
    .email('Please enter a valid email address')
    .max(100, 'Email must be less than 100 characters'),

  phone: Yup.string()
    .matches(/^[+]?[\d\s\-\(\)]+$/, 'Please enter a valid phone number')
    .min(10, 'Phone number must be at least 10 digits')
    .max(15, 'Phone number must be less than 15 digits'),

  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    ),

  legacyRole: Yup.string()
    .required('Role is required')
    .notOneOf(['select-role'], 'Please select a valid role'),

  branch_id: Yup.string()
    .notOneOf(['select-branch'], 'Please select a valid branch'),

  isActive: Yup.boolean()
})

// Staff filter validation schema
export const staffFilterValidationSchema = Yup.object({
  search: Yup.string()
    .max(100, 'Search term must be less than 100 characters'),

  role: Yup.string()
    .oneOf(['all', 'institute_staff', 'trainer', 'branch_manager'], 'Invalid role filter'),

  branch_id: Yup.string(),

  status: Yup.string()
    .oneOf(['all', 'active', 'inactive'], 'Invalid status filter')
})

// Type definitions for form data
export interface StaffCreateFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password: string
  legacyRole: string
  branch_id?: string
  isActive: boolean
}

export interface StaffUpdateFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password?: string
  legacyRole: string
  branch_id?: string
  isActive: boolean
}

export interface StaffFilterFormData {
  search: string
  role: string
  branch_id: string
  status: 'all' | 'active' | 'inactive'
}

// Default values for forms
export const defaultStaffCreateValues: StaffCreateFormData = {
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  password: '',
  legacyRole: '',
  branch_id: '',
  isActive: true
}

export const defaultStaffUpdateValues: StaffUpdateFormData = {
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  password: '',
  legacyRole: '',
  branch_id: '',
  isActive: true
}

export const defaultStaffFilterValues: StaffFilterFormData = {
  search: '',
  role: 'all',
  branch_id: 'all',
  status: 'all'
}
