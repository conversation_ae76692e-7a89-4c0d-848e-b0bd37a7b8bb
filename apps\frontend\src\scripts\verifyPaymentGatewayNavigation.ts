#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify Payment Gateway Navigation configuration
 * This script checks that the navigation items are properly configured
 * and that the permissions are correctly set up.
 */

import { superAdminNavigationConfig } from '../config/navigation/superAdminNavigation'
import { instituteAdminNavigationConfig } from '../config/navigation/instituteAdminNavigation'
import { NAVIGATION_PERMISSIONS } from '../utils/permissions'

console.log('🔍 Verifying Payment Gateway Navigation Configuration...\n')

// Test Super Admin Navigation
console.log('📋 Super Admin Navigation:')
const gatewayManagementItem = superAdminNavigationConfig.find(item => item.id === 'gateway-management')

if (gatewayManagementItem) {
  console.log('✅ Gateway Management section found')
  console.log(`   - Label: ${gatewayManagementItem.label}`)
  console.log(`   - Icon: ${gatewayManagementItem.icon}`)
  console.log(`   - Href: ${gatewayManagementItem.href}`)
  console.log(`   - Description: ${gatewayManagementItem.description}`)
} else {
  console.log('❌ Gateway Management section NOT found')
}

// Test Super Admin Permissions
console.log('\n🔐 Super Admin Permissions:')
const superAdminNavId = 'super-admin-gateway-management'
const superAdminPermissions = NAVIGATION_PERMISSIONS[superAdminNavId]

if (superAdminPermissions) {
  console.log('✅ Navigation permissions found for super admin gateway management')
  console.log(`   - Permissions: ${superAdminPermissions.join(', ')}`)
} else {
  console.log('❌ Navigation permissions NOT found for super admin gateway management')
}

// Test Institute Admin Navigation
console.log('\n📋 Institute Admin Navigation:')
const instituteAdminSettings = instituteAdminNavigationConfig.find(item => item.id === 'settings')

if (instituteAdminSettings) {
  console.log('✅ Settings section found')
  console.log(`   - Permissions: ${instituteAdminSettings.permissions?.join(', ') || 'None'}`)
  
  if (instituteAdminSettings.children) {
    const paymentGatewayItem = instituteAdminSettings.children.find(
      child => child.id === 'settings-payment'
    )
    
    if (paymentGatewayItem) {
      console.log('✅ Payment Gateways item found in settings children')
      console.log(`   - Label: ${paymentGatewayItem.label}`)
      console.log(`   - Icon: ${paymentGatewayItem.icon}`)
      console.log(`   - Href: ${paymentGatewayItem.href}`)
      console.log(`   - Description: ${paymentGatewayItem.description}`)
      console.log(`   - Permissions: ${paymentGatewayItem.permissions?.join(', ') || 'None'}`)
    } else {
      console.log('❌ Payment Gateways item NOT found in settings children')
    }
  } else {
    console.log('❌ Settings section has no children')
  }
} else {
  console.log('❌ Settings section NOT found')
}

// Test Institute Admin Permissions
console.log('\n🔐 Institute Admin Permissions:')
const instituteAdminNavId = 'admin-settings-payment-gateways'
const instituteAdminPermissions = NAVIGATION_PERMISSIONS[instituteAdminNavId]

if (instituteAdminPermissions) {
  console.log('✅ Navigation permissions found for institute admin payment gateways')
  console.log(`   - Permissions: ${instituteAdminPermissions.join(', ')}`)
} else {
  console.log('❌ Navigation permissions NOT found for institute admin payment gateways')
}

// Summary
console.log('\n📊 Summary:')
const superAdminOk = gatewayManagementItem && superAdminPermissions
const instituteAdminOk = instituteAdminSettings?.children?.some(child => child.id === 'settings-payment') && instituteAdminPermissions

if (superAdminOk && instituteAdminOk) {
  console.log('🎉 All payment gateway navigation configurations are properly set up!')
} else {
  console.log('⚠️  Some navigation configurations are missing or incomplete:')
  if (!superAdminOk) {
    console.log('   - Super Admin gateway management navigation needs attention')
  }
  if (!instituteAdminOk) {
    console.log('   - Institute Admin payment gateway navigation needs attention')
  }
}

console.log('\n✨ Verification complete!')
