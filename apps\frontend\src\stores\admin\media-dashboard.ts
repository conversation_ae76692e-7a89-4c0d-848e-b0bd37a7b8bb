import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { 
  mediaDashboardAPI, 
  ProcessingJob, 
  DashboardOverview, 
  JobsResponse 
} from '@/lib/api/media-dashboard'
import { toast } from 'sonner'

interface MediaDashboardState {
  // Data
  overview: DashboardOverview | null
  jobs: ProcessingJob[]
  currentJob: ProcessingJob | null
  
  // UI State
  loading: boolean
  refreshing: boolean
  jobsLoading: boolean
  
  // Pagination
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
  
  // Filters
  filters: {
    status?: ProcessingJob['status']
    type?: ProcessingJob['type']
    priority?: ProcessingJob['priority']
    search: string
    sortBy: string
    sortOrder: 'asc' | 'desc'
  }
  
  // Real-time updates
  autoRefresh: boolean
  refreshInterval: number
  
  // Actions
  fetchOverview: () => Promise<void>
  fetchJobs: () => Promise<void>
  fetchJobDetails: (jobId: string) => Promise<void>
  retryJob: (jobId: string) => Promise<boolean>
  cancelJob: (jobId: string) => Promise<boolean>
  updateJobPriority: (jobId: string, priority: ProcessingJob['priority']) => Promise<boolean>
  
  // Filter actions
  setFilters: (filters: Partial<MediaDashboardState['filters']>) => void
  setPagination: (pagination: Partial<MediaDashboardState['pagination']>) => void
  resetFilters: () => void
  
  // Real-time actions
  startAutoRefresh: () => void
  stopAutoRefresh: () => void
  setAutoRefresh: (enabled: boolean) => void
  
  // UI actions
  setCurrentJob: (job: ProcessingJob | null) => void
  refreshData: () => Promise<void>
}

const initialFilters = {
  search: '',
  sortBy: 'createdAt',
  sortOrder: 'desc' as const
}

const initialPagination = {
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false
}

let refreshIntervalId: NodeJS.Timeout | null = null

export const useMediaDashboardStore = create<MediaDashboardState>()(
  devtools(
    (set, get) => ({
      // Initial state
      overview: null,
      jobs: [],
      currentJob: null,
      loading: false,
      refreshing: false,
      jobsLoading: false,
      pagination: initialPagination,
      filters: initialFilters,
      autoRefresh: false,
      refreshInterval: 30000, // 30 seconds

      // Fetch dashboard overview
      fetchOverview: async () => {
        set({ loading: true })
        try {
          const overview = await mediaDashboardAPI.getDashboardOverview()
          set({ overview, loading: false })
        } catch (error: any) {
          console.error('Error fetching dashboard overview:', error)
          toast.error(error.response?.data?.error || 'Failed to fetch dashboard overview')
          set({ loading: false })
        }
      },

      // Fetch jobs with current filters and pagination
      fetchJobs: async () => {
        set({ jobsLoading: true })
        try {
          const { filters, pagination } = get()
          const response = await mediaDashboardAPI.getProcessingJobs({
            page: pagination.page,
            limit: pagination.limit,
            status: filters.status,
            type: filters.type,
            priority: filters.priority,
            search: filters.search || undefined,
            sortBy: filters.sortBy,
            sortOrder: filters.sortOrder
          })

          set({
            jobs: response.jobs,
            pagination: response.pagination,
            jobsLoading: false
          })
        } catch (error: any) {
          console.error('Error fetching jobs:', error)
          toast.error(error.response?.data?.error || 'Failed to fetch processing jobs')
          set({ jobsLoading: false })
        }
      },

      // Fetch detailed job information
      fetchJobDetails: async (jobId: string) => {
        set({ loading: true })
        try {
          const job = await mediaDashboardAPI.getJobDetails(jobId)
          set({ currentJob: job, loading: false })
        } catch (error: any) {
          console.error('Error fetching job details:', error)
          toast.error(error.response?.data?.error || 'Failed to fetch job details')
          set({ loading: false })
        }
      },

      // Retry failed job
      retryJob: async (jobId: string) => {
        try {
          await mediaDashboardAPI.retryJob(jobId)
          toast.success('Job retry initiated')
          
          // Refresh data
          get().refreshData()
          
          return true
        } catch (error: any) {
          console.error('Error retrying job:', error)
          toast.error(error.response?.data?.error || 'Failed to retry job')
          return false
        }
      },

      // Cancel job
      cancelJob: async (jobId: string) => {
        try {
          await mediaDashboardAPI.cancelJob(jobId)
          toast.success('Job cancelled successfully')
          
          // Refresh data
          get().refreshData()
          
          return true
        } catch (error: any) {
          console.error('Error cancelling job:', error)
          toast.error(error.response?.data?.error || 'Failed to cancel job')
          return false
        }
      },

      // Update job priority
      updateJobPriority: async (jobId: string, priority: ProcessingJob['priority']) => {
        try {
          await mediaDashboardAPI.updateJobPriority(jobId, priority)
          toast.success('Job priority updated successfully')
          
          // Update job in current list
          const { jobs, currentJob } = get()
          const updatedJobs = jobs.map(job => 
            job.id === jobId ? { ...job, priority } : job
          )
          
          set({ 
            jobs: updatedJobs,
            currentJob: currentJob?.id === jobId ? { ...currentJob, priority } : currentJob
          })
          
          return true
        } catch (error: any) {
          console.error('Error updating job priority:', error)
          toast.error(error.response?.data?.error || 'Failed to update job priority')
          return false
        }
      },

      // Filter actions
      setFilters: (newFilters) => {
        set((state) => ({
          filters: { ...state.filters, ...newFilters },
          pagination: { ...state.pagination, page: 1 } // Reset to first page
        }))
        // Auto-fetch with new filters
        setTimeout(() => get().fetchJobs(), 0)
      },

      setPagination: (newPagination) => {
        set((state) => ({
          pagination: { ...state.pagination, ...newPagination }
        }))
        // Auto-fetch with new pagination
        setTimeout(() => get().fetchJobs(), 0)
      },

      resetFilters: () => {
        set({
          filters: initialFilters,
          pagination: initialPagination
        })
        // Auto-fetch with reset filters
        setTimeout(() => get().fetchJobs(), 0)
      },

      // Real-time actions
      startAutoRefresh: () => {
        const { refreshInterval } = get()
        
        if (refreshIntervalId) {
          clearInterval(refreshIntervalId)
        }
        
        refreshIntervalId = setInterval(() => {
          get().refreshData()
        }, refreshInterval)
        
        set({ autoRefresh: true })
      },

      stopAutoRefresh: () => {
        if (refreshIntervalId) {
          clearInterval(refreshIntervalId)
          refreshIntervalId = null
        }
        set({ autoRefresh: false })
      },

      setAutoRefresh: (enabled: boolean) => {
        if (enabled) {
          get().startAutoRefresh()
        } else {
          get().stopAutoRefresh()
        }
      },

      // UI actions
      setCurrentJob: (job) => {
        set({ currentJob: job })
      },

      // Refresh all data
      refreshData: async () => {
        set({ refreshing: true })
        try {
          await Promise.all([
            get().fetchOverview(),
            get().fetchJobs()
          ])
        } finally {
          set({ refreshing: false })
        }
      }
    }),
    {
      name: 'media-dashboard-store'
    }
  )
)

// Cleanup interval on store destruction
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    if (refreshIntervalId) {
      clearInterval(refreshIntervalId)
    }
  })
}
