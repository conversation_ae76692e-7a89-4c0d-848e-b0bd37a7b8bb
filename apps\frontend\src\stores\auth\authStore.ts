import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: string
  email: string
  name: string
  role: {
    id: string
    name: string
    permissions: Array<{
      id: string
      code: string
      name: string
    }>
  }
  institute?: {
    id: string
    name: string
    slug: string
  }
  legacyRole?: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  setUser: (user: User) => void
  setToken: (token: string) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (email: string, password: string) => {
        set({ isLoading: true })
        try {
          // Mock login for development
          const mockUser: User = {
            id: '1',
            email,
            name: 'Test User',
            role: {
              id: '1',
              name: 'Institute Admin',
              permissions: [
                { id: '1', code: 'content.create', name: 'Create Content' },
                { id: '2', code: 'content.edit', name: 'Edit Content' },
                { id: '3', code: 'content.delete', name: 'Delete Content' },
                { id: '4', code: 'content.view', name: 'View Content' }
              ]
            },
            institute: {
              id: '1',
              name: 'Test Institute',
              slug: 'test-institute'
            },
            legacyRole: 'institute_admin'
          }

          const mockToken = 'mock-jwt-token'

          set({
            user: mockUser,
            token: mockToken,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false
        })
      },

      setUser: (user: User) => {
        set({ user, isAuthenticated: true })
      },

      setToken: (token: string) => {
        set({ token })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
