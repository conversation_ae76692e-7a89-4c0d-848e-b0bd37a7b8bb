import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface Bill {
  id: string
  billNumber: string
  branch: any
  billingPeriod: {
    startDate: string
    endDate: string
    month: number
    year: number
  }
  amounts: {
    baseFee: number
    commissionAmount: number
    subtotal: number
    taxAmount: number
    totalAmount: number
    currency: string
  }
  taxDetails: {
    taxScenario: string
    taxComponents: Array<{
      componentName: string
      componentCode: string
      rate: number
      amount: number
    }>
  }
  commissionDetails: Array<{
    studentPurchase: string
    courseTitle: string
    studentName: string
    purchaseAmount: number
    commissionRate: number
    commissionAmount: number
    purchaseDate: string
  }>
  status: 'pending' | 'sent' | 'viewed' | 'paid' | 'overdue' | 'cancelled'
  dates: {
    generatedDate: string
    sentDate?: string
    dueDate: string
    paidDate?: string
    viewedDate?: string
  }
  paymentDetails?: {
    paymentMethod: string
    transactionId: string
    paidBy: any
  }
  createdAt: string
  updatedAt: string
}

// Course functionality has been removed from the system

interface DashboardData {
  summary: {
    currentMonthTotal: number
    pendingTotal: number
    overdueTotal: number
    billsCount: {
      current: number
      pending: number
      overdue: number
    }
  }
  currentMonthBills: Bill[]
  pendingBills: Bill[]
  overdueBills: Bill[]
  recentPurchases: CoursePurchase[]
  commissionSummary: {
    totalCommission: number
    totalRevenue: number
    purchaseCount: number
  }
}

interface BillingFilters {
  status?: string
  month?: number
  year?: number
  branchId?: string
}

interface Pagination {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface BillingState {
  // Data
  dashboardData: DashboardData | null
  bills: Bill[]
  coursePurchases: CoursePurchase[]
  selectedBill: Bill | null

  // UI State
  isLoading: boolean
  error: string | null

  // Filters
  filters: BillingFilters

  // Pagination
  billsPagination: Pagination
  purchasesPagination: Pagination

  // Actions
  setFilters: (filters: Partial<BillingFilters>) => void
  setSelectedBill: (bill: Bill | null) => void

  // API Actions
  fetchDashboardData: () => Promise<void>
  fetchBills: (page?: number) => Promise<void>
  fetchCoursePurchases: (page?: number) => Promise<void>
  generateBill: (branchId: string, month: number, year: number, baseFee?: number) => Promise<void>
  generateBulkBills: (month: number, year: number) => Promise<void>
  updateBillStatus: (billId: string, status: string, paymentDetails?: any) => Promise<void>

  // Utility Actions
  clearError: () => void
  resetFilters: () => void
}

const initialFilters: BillingFilters = {}

const initialPagination: Pagination = {
  page: 1,
  limit: 20,
  totalPages: 1,
  totalDocs: 0,
  hasNextPage: false,
  hasPrevPage: false
}

export const useBillingStore = create<BillingState>()(
  devtools(
    (set, get) => ({
      // Initial State
      dashboardData: null,
      bills: [],
      coursePurchases: [],
      selectedBill: null,
      isLoading: false,
      error: null,
      filters: initialFilters,
      billsPagination: initialPagination,
      purchasesPagination: initialPagination,

      // UI Actions
      setFilters: (newFilters) => set((state) => ({
        filters: { ...state.filters, ...newFilters }
      })),

      setSelectedBill: (bill) => set({ selectedBill: bill }),

      // API Actions
      fetchDashboardData: async () => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/billing/dashboard', {
            credentials: 'include'
          })
          const data = await response.json()

          if (data.success) {
            set({
              dashboardData: data.data,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch dashboard data')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch dashboard data')
        }
      },

      fetchBills: async (page = 1) => {
        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            ...(filters.status && { status: filters.status }),
            ...(filters.month && { month: filters.month.toString() }),
            ...(filters.year && { year: filters.year.toString() }),
            ...(filters.branchId && { branchId: filters.branchId })
          })

          const response = await fetch(`/api/billing/bills?${params}`, {
            credentials: 'include'
          })
          const data = await response.json()

          if (data.success) {
            set({
              bills: data.bills,
              billsPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch bills')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch bills')
        }
      },

      fetchCoursePurchases: async (page = 1) => {
        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '50',
            ...(filters.month && { month: filters.month.toString() }),
            ...(filters.year && { year: filters.year.toString() }),
            ...(filters.branchId && { branchId: filters.branchId })
          })

          const response = await fetch(`/api/billing/commission?${params}`, {
            credentials: 'include'
          })
          const data = await response.json()

          if (data.success) {
            set({
              coursePurchases: data.purchases,
              purchasesPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch course purchases')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch course purchases')
        }
      },

      generateBill: async (branchId, month, year, baseFee) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/billing/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ branchId, month, year, baseFee })
          })

          const data = await response.json()

          if (data.success) {
            await get().fetchBills()
            await get().fetchDashboardData()
            toast.success('Bill Generated', {
              description: `Bill ${data.bill.billNumber} has been generated successfully.`
            })
            set({ isLoading: false })
          } else {
            throw new Error(data.error || 'Failed to generate bill')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to generate bill', {
            description: errorMessage
          })
          throw error
        }
      },

      generateBulkBills: async (month, year) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/billing/generate-bulk', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ month, year })
          })

          const data = await response.json()

          if (data.success) {
            await get().fetchBills()
            await get().fetchDashboardData()
            toast.success('Bulk Bills Generated', {
              description: `${data.generated} bills have been generated successfully.`
            })
            set({ isLoading: false })
          } else {
            throw new Error(data.error || 'Failed to generate bulk bills')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to generate bulk bills', {
            description: errorMessage
          })
          throw error
        }
      },

      updateBillStatus: async (billId, status, paymentDetails) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/billing/bills/${billId}/status`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ status, paymentDetails })
          })

          const data = await response.json()

          if (data.success) {
            // Update bill in local state
            set((state) => ({
              bills: state.bills.map(bill => 
                bill.id === billId ? { ...bill, ...data.bill } : bill
              ),
              selectedBill: state.selectedBill?.id === billId 
                ? { ...state.selectedBill, ...data.bill } 
                : state.selectedBill,
              isLoading: false
            }))

            await get().fetchDashboardData()
            
            const statusMessages = {
              sent: 'Bill has been sent successfully',
              viewed: 'Bill status updated to viewed',
              paid: 'Payment has been recorded successfully',
              overdue: 'Bill marked as overdue',
              cancelled: 'Bill has been cancelled'
            }

            toast.success('Bill Status Updated', {
              description: statusMessages[status as keyof typeof statusMessages] || 'Bill status updated'
            })
          } else {
            throw new Error(data.error || 'Failed to update bill status')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to update bill status', {
            description: errorMessage
          })
          throw error
        }
      },

      // Utility Actions
      clearError: () => set({ error: null }),

      resetFilters: () => set({
        filters: initialFilters
      })
    }),
    {
      name: 'billing-store'
    }
  )
)
