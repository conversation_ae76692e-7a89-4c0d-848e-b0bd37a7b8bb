import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: string
  featuredImage?: {
    id: string
    url: string
    alt: string
  }
  status: 'draft' | 'scheduled' | 'published' | 'archived'
  publishedAt?: string
  scheduledFor?: string
  category?: {
    id: string
    name: string
    slug: string
    color?: string
  }
  tags: Array<{ tag: string }>
  seo: {
    title?: string
    description?: string
    keywords?: Array<{ keyword: string }>
  }
  settings: {
    allowComments: boolean
    isFeatured: boolean
    isSticky: boolean
  }
  analytics: {
    viewCount: number
    likeCount: number
    commentCount: number
    readingTime?: number
  }
  author: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
  lastEditedBy?: {
    id: string
    firstName: string
    lastName: string
  }
  createdAt: string
  updatedAt: string
}

export interface BlogCategory {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  icon?: string
  parentCategory?: {
    id: string
    name: string
  }
  displayOrder: number
  isActive: boolean
  postCount?: number
  createdBy: {
    id: string
    firstName: string
    lastName: string
  }
  createdAt: string
}

interface BlogState {
  // Posts
  posts: BlogPost[]
  currentPost: BlogPost | null
  postsLoading: boolean

  // Categories
  categories: BlogCategory[]
  categoriesLoading: boolean

  // Search & Filters
  searchQuery: string
  selectedCategory: string | null
  selectedStatus: string | null
  selectedTags: string[]

  // Analytics
  analytics: {
    totalViews: number
    totalUniqueViews: number
    totalLikes: number
    totalComments: number
    totalShares: number
  }
  trendingPosts: BlogPost[]

  // UI State
  error: string | null

  // Actions
  fetchPosts: (params?: any) => Promise<void>
  fetchPost: (id: string) => Promise<void>
  createPost: (postData: Partial<BlogPost>) => Promise<void>
  updatePost: (id: string, postData: Partial<BlogPost>) => Promise<void>
  deletePost: (id: string) => Promise<void>
  publishPost: (id: string) => Promise<void>
  schedulePost: (id: string, scheduledFor: string) => Promise<void>

  // Categories
  fetchCategories: () => Promise<void>
  createCategory: (categoryData: Partial<BlogCategory>) => Promise<void>
  updateCategory: (id: string, categoryData: Partial<BlogCategory>) => Promise<void>
  deleteCategory: (id: string) => Promise<void>

  // Search
  searchPosts: (query: string, filters?: any) => Promise<void>
  setSearchQuery: (query: string) => void
  setFilters: (filters: any) => void
  clearFilters: () => void

  // Validation
  checkSlugUniqueness: (slug: string, excludeId?: string) => Promise<boolean>

  // Analytics
  fetchAnalytics: (period?: string) => Promise<void>
  fetchTrendingPosts: (period?: string) => Promise<void>
}

export const useBlogStore = create<BlogState>()(
  devtools(
    (set, get) => ({
      // Initial state
      posts: [],
      currentPost: null,
      postsLoading: false,
      categories: [],
      categoriesLoading: false,
      searchQuery: '',
      selectedCategory: null,
      selectedStatus: null,
      selectedTags: [],
      analytics: {
        totalViews: 0,
        totalUniqueViews: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0
      },
      trendingPosts: [],
      error: null,

      // Posts actions
      fetchPosts: async (params = {}) => {
        set({ postsLoading: true, error: null })
        try {
          const response = await api.get('/api/institute-admin/blog/posts', params)

          if (!response || !response) {
            throw new Error('No response received from server')
          }

          if (response.success === false) {
            throw new Error(response.data.error || 'Failed to fetch posts')
          }

          set({
            posts: response.posts || [],
            postsLoading: false
          })
        } catch (error: any) {
          set({
            error: error.message || error.response?.data?.error || 'Failed to fetch posts',
            postsLoading: false
          })
          toast.error('Failed to fetch blog posts')
        }
      },

      fetchPost: async (id) => {
        set({ postsLoading: true, error: null })
        try {
          const response = await api.get(`/api/institute-admin/blog/posts/${id}`)
          // Convert richText content back to plain text for editing
          const post = response.post
          if (post.content && typeof post.content === 'object' && post.content.root) {
            // Extract text from Lexical richText format
            const extractTextFromLexical = (node: any): string => {
              if (node.type === 'text') {
                return node.text || ''
              }
              if (node.children && Array.isArray(node.children)) {
                return node.children.map(extractTextFromLexical).join('')
              }
              return ''
            }

            post.content = extractTextFromLexical(post.content.root)
          }

          set({
            currentPost: post,
            postsLoading: false
          })
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to fetch post',
            postsLoading: false
          })
          toast.error('Failed to fetch blog post')
        }
      },

      createPost: async (postData) => {
        set({ postsLoading: true, error: null })
        try {
          const response = await api.post('/api/institute-admin/blog/posts', postData)

          if (!response || !response) {
            throw new Error('No response received from server')
          }

          if (response.success === false) {
            throw new Error(response.error || 'Failed to create post')
          }

          set(state => ({
            posts: [response.post, ...state.posts],
            postsLoading: false
          }))

          toast.success('Blog post created successfully')
        } catch (error: any) {
          set({
            error: error.message || error.response?.data?.error || 'Failed to create post',
            postsLoading: false
          })
          toast.error(`Failed to create blog post: ${error.message || error}`)
          throw error
        }
      },

      updatePost: async (id, postData) => {
        set({ postsLoading: true, error: null })
        try {
          const response = await api.patch(`/api/institute-admin/blog/posts/${id}`, postData)

          set(state => ({
            posts: state.posts.map(post =>
              post.id === id ? response.data.post : post
            ),
            currentPost: state.currentPost?.id === id ? response.data.post : state.currentPost,
            postsLoading: false
          }))

          toast.success('Blog post updated successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to update post',
            postsLoading: false
          })
          toast.error('Failed to update blog post')
          throw error
        }
      },

      deletePost: async (id) => {
        set({ postsLoading: true, error: null })
        try {
          await api.delete(`/api/institute-admin/blog/posts/${id}`)

          set(state => ({
            posts: state.posts.filter(post => post.id !== id),
            currentPost: state.currentPost?.id === id ? null : state.currentPost,
            postsLoading: false
          }))

          toast.success('Blog post deleted successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to delete post',
            postsLoading: false
          })
          toast.error('Failed to delete blog post')
        }
      },

      publishPost: async (id) => {
        try {
          await get().updatePost(id, {
            status: 'published',
            publishedAt: new Date().toISOString()
          })
          toast.success('Blog post published successfully')
        } catch (error) {
          // Error handling is done in updatePost
        }
      },

      schedulePost: async (id, scheduledFor) => {
        try {
          await get().updatePost(id, {
            status: 'scheduled',
            scheduledFor
          })
          toast.success('Blog post scheduled successfully')
        } catch (error) {
          // Error handling is done in updatePost
        }
      },

      // Categories actions
      fetchCategories: async () => {
        set({ categoriesLoading: true, error: null })
        try {
          const response = await api.get('/api/institute-admin/blog/categories')

          if (!response || !response) {
            throw new Error('No response received from server')
          }

          if (response.success === false) {
            throw new Error(response.data.error || 'Failed to fetch categories')
          }

          set({
            categories: response.categories || [],
            categoriesLoading: false
          })
        } catch (error: any) {
          set({
            error: error.message || error.response?.data?.error || 'Failed to fetch categories',
            categoriesLoading: false
          })
          toast.error('Failed to fetch blog categories')
        }
      },

      createCategory: async (categoryData) => {
        set({ categoriesLoading: true, error: null })
        try {
          const response = await api.post('/api/institute-admin/blog/categories', categoryData)

          if (!response || !response) {
            throw new Error('No response received from server')
          }

          if (response.success === false) {
            throw new Error(response.data.error || 'Failed to create category')
          }

          set(state => ({
            categories: [...state.categories, response.category],
            categoriesLoading: false
          }))

          toast.success('Blog category created successfully')
        } catch (error: any) {
          set({
            error: error.message || error.response?.data?.error || 'Failed to create category',
            categoriesLoading: false
          })
          toast.error(`Failed to create blog category: ${error.message || error}`)
          throw error
        }
      },

      updateCategory: async (id, categoryData) => {
        set({ categoriesLoading: true, error: null })
        try {
          const response = await api.patch(`/api/institute-admin/blog/categories/${id}`, categoryData)

          set(state => ({
            categories: state.categories.map(category =>
              category.id === id ? response.category : category
            ),
            categoriesLoading: false
          }))

          toast.success('Blog category updated successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to update category',
            categoriesLoading: false
          })
          toast.error('Failed to update blog category')
          throw error
        }
      },

      deleteCategory: async (id) => {
        set({ categoriesLoading: true, error: null })
        try {
          await api.delete(`/api/institute-admin/blog/categories/${id}`)

          set(state => ({
            categories: state.categories.filter(category => category.id !== id),
            categoriesLoading: false
          }))

          toast.success('Blog category deleted successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to delete category',
            categoriesLoading: false
          })
          toast.error('Failed to delete blog category')
        }
      },

      // Search actions
      searchPosts: async (query, filters = {}) => {
        set({ postsLoading: true, error: null })
        try {
          const params = {
            q: query,
            ...filters
          }

          const response = await api.get('/api/institute-admin/blog/search', params)

          set({
            posts: response.data.posts,
            searchQuery: query,
            postsLoading: false
          })
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Search failed',
            postsLoading: false
          })
          toast.error('Blog search failed')
        }
      },

      setSearchQuery: (query) => {
        set({ searchQuery: query })
      },

      setFilters: (filters) => {
        set({
          selectedCategory: filters.category || null,
          selectedStatus: filters.status || null,
          selectedTags: filters.tags || []
        })
      },

      clearFilters: () => {
        set({
          searchQuery: '',
          selectedCategory: null,
          selectedStatus: null,
          selectedTags: []
        })
      },

      // Analytics actions
      fetchAnalytics: async (period = '30d') => {
        try {
          const response = await api.get('/api/institute-admin/blog/analytics', { period })

          if (!response || !response) {
            throw new Error('No response received from server')
          }

          if (response.success === false) {
            throw new Error(response.data.error || 'Failed to fetch analytics')
          }

          set({ analytics: response.analytics || {} })
        } catch (error: any) {
          console.error('Analytics error:', error)
          toast.error(`Failed to fetch blog analytics: ${error.message || error}`)
        }
      },

      fetchTrendingPosts: async (period = '7d') => {
        try {
          const response = await api.get('/api/institute-admin/blog/trending', { period })

          if (!response || !response) {
            throw new Error('No response received from server')
          }

          if (response.success === false) {
            throw new Error(response.data.error || 'Failed to fetch trending posts')
          }

          set({ trendingPosts: response.posts || [] })
        } catch (error: any) {
          console.error('Trending posts error:', error)
          toast.error(`Failed to fetch trending posts: ${error.message || error}`)
        }
      },

      // Validation functions
      checkSlugUniqueness: async (slug: string, excludeId?: string) => {
        try {
          const response = await api.get('/api/institute-admin/blog/posts', {
            slug: slug,
            limit: '1'
          })

          if (!response || !response.data) {
            return true // Assume unique if can't check
          }

          if (response.data.success === false) {
            return true // Assume unique if error
          }

          const posts = response.data.posts || []

          // If no posts found with this slug, it's unique
          if (posts.length === 0) {
            return true
          }

          // If we're editing a post, exclude it from the check
          if (excludeId) {
            const conflictingPosts = posts.filter((post: any) => post.id !== excludeId)
            return conflictingPosts.length === 0
          }

          // If we found posts with this slug, it's not unique
          return false
        } catch (error: any) {
          console.error('Slug uniqueness check error:', error)
          return true // Assume unique if error checking
        }
      }
    }),
    {
      name: 'blog-store'
    }
  )
)
