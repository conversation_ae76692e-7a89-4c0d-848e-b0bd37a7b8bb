import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

// Types and Interfaces
export interface Course {
  id: string
  title: string
  description: string
  thumbnail?: {
    id: string
    url: string
    alt?: string
  } | null
  pricing_type: 'free' | 'one_time'
  price_amount?: number | null
  discount_percentage?: number | null
  final_price?: number | null
  status: 'draft' | 'published' | 'archived'
  institute: {
    id: string
    name: string
  }
  branch: {
    id: string
    name: string
    code: string
  }
  created_by: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
  createdAt: string
  updatedAt: string
}

export interface CourseCreationData {
  title: string
  description: string
  pricing_type: 'free' | 'one_time'
  price_amount?: number
  discount_percentage?: number
  thumbnail?: string
}

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface CourseFilters {
  search?: string
  status?: string
  pricing_type?: string
}

interface CourseStore {
  // State
  courses: Course[]
  loading: boolean
  error: string | null
  pagination: PaginationInfo | null
  filters: CourseFilters

  // Actions
  fetchCourses: (page?: number, limit?: number) => Promise<void>
  createCourse: (data: CourseCreationData) => Promise<Course | null>
  setFilters: (filters: Partial<CourseFilters>) => void
  clearFilters: () => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  reset: () => void
}

const initialState = {
  courses: [],
  loading: false,
  error: null,
  pagination: null,
  filters: {}
}

export const useCourseStore = create<CourseStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      fetchCourses: async (page = 1, limit = 10) => {
        try {
          set({ loading: true, error: null })

          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
            ...filters
          })

          const response = await api.get(`/api/institute-admin/courses?${params}`)

          if (response.success) {
            set({
              courses: response.data.courses,
              pagination: response.data.pagination,
              loading: false
            })
          } else {
            throw new Error(response.error || 'Failed to fetch courses')
          }
        } catch (error) {
          console.error('Error fetching courses:', error)
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch courses'
          set({ error: errorMessage, loading: false })
          toast.error(errorMessage)
        }
      },

      createCourse: async (data: CourseCreationData) => {
        try {
          set({ loading: true, error: null })

          const response = await api.post('/api/institute-admin/courses', data)

          if (response.success) {
            // Refresh the course list
            await get().fetchCourses()
            
            set({ loading: false })
            toast.success('Course created successfully!')
            return response.data
          } else {
            throw new Error(response.error || 'Failed to create course')
          }
        } catch (error) {
          console.error('Error creating course:', error)
          const errorMessage = error instanceof Error ? error.message : 'Failed to create course'
          set({ error: errorMessage, loading: false })
          toast.error(errorMessage)
          return null
        }
      },

      setFilters: (newFilters: Partial<CourseFilters>) => {
        const { filters } = get()
        const updatedFilters = { ...filters, ...newFilters }
        set({ filters: updatedFilters })
        
        // Auto-fetch with new filters
        get().fetchCourses(1)
      },

      clearFilters: () => {
        set({ filters: {} })
        get().fetchCourses(1)
      },

      setLoading: (loading: boolean) => set({ loading }),
      setError: (error: string | null) => set({ error }),
      reset: () => set(initialState)
    }),
    {
      name: 'course-store'
    }
  )
)
