import { create } from 'zustand'
import { api } from '@/lib/api'
import { toast } from 'sonner'

export interface ConfigField {
  key: string
  label: string
  type: 'text' | 'password' | 'url' | 'email' | 'number' | 'boolean'
  placeholder?: string
  description?: string
  isRequired: boolean
}

export interface PaymentGateway {
  id: string
  name: string
  description?: string
  supportedCurrencies: string[]
  supportedMethods: string[]
  supportedCountries: string[]
  requiredConfigFields: ConfigField[]
  optionalConfigFields: ConfigField[]
  logoUrl?: string
  documentationUrl?: string
  isActive: boolean
  isFeatured: boolean
}

export interface InstituteGatewayConfig {
  id: string
  institute: string
  gateway: PaymentGateway
  configuration: Record<string, any>
  isActive: boolean
  testMode: boolean
  isPrimary: boolean
  configuredBy?: string
  lastTestedAt?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

interface InstituteGatewayState {
  // Available gateways (from super admin)
  availableGateways: PaymentGateway[]
  availableGatewaysLoading: boolean
  
  // Institute's configured gateways
  configuredGateways: InstituteGatewayConfig[]
  configuredGatewaysLoading: boolean
  
  // Current gateway being configured
  currentGateway: PaymentGateway | null
  currentConfig: InstituteGatewayConfig | null
  
  error: string | null
  
  // Actions
  fetchAvailableGateways: () => Promise<void>
  fetchConfiguredGateways: () => Promise<void>
  getGatewayConfig: (gatewayId: string) => Promise<InstituteGatewayConfig | null>
  saveGatewayConfig: (gatewayId: string, configuration: Record<string, any>, options?: { isActive?: boolean, testMode?: boolean, isPrimary?: boolean, notes?: string }) => Promise<void>
  deleteGatewayConfig: (configId: string) => Promise<void>
  testGatewayConfig: (configId: string) => Promise<boolean>
  setCurrentGateway: (gateway: PaymentGateway | null) => void
  setCurrentConfig: (config: InstituteGatewayConfig | null) => void
  clearError: () => void
}

export const useInstituteGatewayStore = create<InstituteGatewayState>((set, get) => ({
  // Initial state
  availableGateways: [],
  availableGatewaysLoading: false,
  configuredGateways: [],
  configuredGatewaysLoading: false,
  currentGateway: null,
  currentConfig: null,
  error: null,

  // Fetch available gateways from super admin
  fetchAvailableGateways: async () => {
    set({ availableGatewaysLoading: true, error: null })
    try {
      const response = await api.get('/api/institute-admin/available-gateways')
      set({ 
        availableGateways: response.gateways || [],
        availableGatewaysLoading: false 
      })
    } catch (error: any) {
      console.error('Failed to fetch available gateways:', error)
      set({ 
        error: error.response?.data?.message || 'Failed to fetch available gateways',
        availableGatewaysLoading: false 
      })
      toast.error('Failed to fetch available gateways')
    }
  },

  // Fetch institute's configured gateways
  fetchConfiguredGateways: async () => {
    set({ configuredGatewaysLoading: true, error: null })
    try {
      const response = await api.get('/api/institute-admin/gateway-configs')
      set({ 
        configuredGateways: response.configs || [],
        configuredGatewaysLoading: false 
      })
    } catch (error: any) {
      console.error('Failed to fetch configured gateways:', error)
      set({ 
        error: error.response?.data?.message || 'Failed to fetch configured gateways',
        configuredGatewaysLoading: false 
      })
      toast.error('Failed to fetch configured gateways')
    }
  },

  // Get specific gateway configuration
  getGatewayConfig: async (gatewayId: string) => {
    try {
      const response = await api.get(`/api/institute-admin/gateway-configs/${gatewayId}`)
      const config = response.config
      set({ currentConfig: config })
      return config
    } catch (error: any) {
      console.error('Failed to fetch gateway config:', error)
      if (error.response?.status !== 404) {
        set({ error: error.response?.data?.message || 'Failed to fetch gateway configuration' })
        toast.error('Failed to fetch gateway configuration')
      }
      return null
    }
  },

  // Save gateway configuration
  saveGatewayConfig: async (gatewayId: string, configuration: Record<string, any>, options = {}) => {
    try {
      const payload = {
        gatewayId,
        configuration,
        isActive: options.isActive ?? false,
        testMode: options.testMode ?? true,
        isPrimary: options.isPrimary ?? false,
        notes: options.notes || ''
      }

      const response = await api.post('/api/institute-admin/gateway-configs', payload)
      
      // Update the configured gateways list
      const { configuredGateways } = get()
      const existingIndex = configuredGateways.findIndex(config => config.gateway.id === gatewayId)
      
      if (existingIndex >= 0) {
        // Update existing configuration
        const updatedConfigs = [...configuredGateways]
        updatedConfigs[existingIndex] = response.config
        set({ configuredGateways: updatedConfigs })
      } else {
        // Add new configuration
        set({ configuredGateways: [...configuredGateways, response.config] })
      }

      set({ currentConfig: response.config })
      toast.success('Gateway configuration saved successfully')
    } catch (error: any) {
      console.error('Failed to save gateway config:', error)
      set({ error: error.response?.data?.message || 'Failed to save gateway configuration' })
      toast.error('Failed to save gateway configuration')
      throw error
    }
  },

  // Delete gateway configuration
  deleteGatewayConfig: async (configId: string) => {
    try {
      await api.delete(`/api/institute-admin/gateway-configs/${configId}`)
      
      // Remove from configured gateways list
      const { configuredGateways } = get()
      set({ 
        configuredGateways: configuredGateways.filter(config => config.id !== configId),
        currentConfig: null
      })
      
      toast.success('Gateway configuration deleted successfully')
    } catch (error: any) {
      console.error('Failed to delete gateway config:', error)
      set({ error: error.response?.data?.message || 'Failed to delete gateway configuration' })
      toast.error('Failed to delete gateway configuration')
      throw error
    }
  },

  // Test gateway configuration
  testGatewayConfig: async (configId: string) => {
    try {
      const response = await api.post(`/api/institute-admin/gateway-configs/${configId}/test`)
      
      if (response.data.success) {
        toast.success('Gateway configuration test successful')
        
        // Update lastTestedAt
        const { configuredGateways } = get()
        const updatedConfigs = configuredGateways.map(config => 
          config.id === configId 
            ? { ...config, lastTestedAt: new Date().toISOString() }
            : config
        )
        set({ configuredGateways: updatedConfigs })
        
        return true
      } else {
        toast.error(`Gateway test failed: ${response.data.message}`)
        return false
      }
    } catch (error: any) {
      console.error('Failed to test gateway config:', error)
      set({ error: error.response?.data?.message || 'Failed to test gateway configuration' })
      toast.error('Failed to test gateway configuration')
      return false
    }
  },

  // Set current gateway
  setCurrentGateway: (gateway: PaymentGateway | null) => {
    set({ currentGateway: gateway })
  },

  // Set current config
  setCurrentConfig: (config: InstituteGatewayConfig | null) => {
    set({ currentConfig: config })
  },

  // Clear error
  clearError: () => {
    set({ error: null })
  },
}))
