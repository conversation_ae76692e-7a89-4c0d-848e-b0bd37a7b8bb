import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import {
  themeApi,
  type Theme,
  type ThemeCustomizations,
  type ThemeHistory,
  type ApplyThemeData
} from '@/lib/institute-admin/themes'

// Re-export types for convenience
export type { Theme, ThemeCustomizations, ThemeHistory, ApplyThemeData }

interface InstituteThemeState {
  // State
  themes: Theme[]
  currentTheme: Theme | null
  themeCustomizations: ThemeCustomizations
  themeHistory: ThemeHistory[]
  isLoading: boolean
  error: string | null
  previewTheme: Theme | null

  // Actions
  fetchThemes: () => Promise<void>
  getCurrentTheme: () => Promise<void>
  applyTheme: (themeId: string, customizations?: ThemeCustomizations) => Promise<boolean>
  previewThemeById: (themeId: string) => Promise<void>
  fetchThemeHistory: () => Promise<void>
  updateCustomizations: (customizations: ThemeCustomizations) => Promise<boolean>
  clearPreview: () => void
  clearError: () => void
}

export const useInstituteThemeStore = create<InstituteThemeState>()(
  devtools(
    (set, get) => ({
      // Initial state
      themes: [],
      currentTheme: null,
      themeCustomizations: {},
      themeHistory: [],
      isLoading: false,
      error: null,
      previewTheme: null,

      // Fetch all available institute themes
      fetchThemes: async () => {
        set({ isLoading: true, error: null })

        try {
          console.log('🎨 Fetching institute themes...')

          const response = await themeApi.getAll()

          if (response.success) {
            set({
              themes: response.themes,
              isLoading: false
            })
            console.log(`✅ Loaded ${response.themes.length} themes`)
            toast.success(`Loaded ${response.themes.length} themes`)
          } else {
            throw new Error('Failed to fetch themes')
          }
        } catch (error: any) {
          console.error('❌ Fetch themes error:', error)
          const errorMessage = error.message || 'Failed to fetch themes'
          set({
            error: errorMessage,
            isLoading: false
          })
          toast.error(errorMessage)
        }
      },

      // Get current institute's selected theme
      getCurrentTheme: async () => {
        set({ isLoading: true, error: null })

        try {
          console.log('🎨 Getting current institute theme...')

          const response = await themeApi.getCurrent()

          if (response.success) {
            set({
              currentTheme: response.currentTheme,
              themeCustomizations: response.themeCustomizations || {},
              isLoading: false
            })
            console.log('✅ Current theme loaded:', response.currentTheme?.name || 'None')
          } else {
            throw new Error('Failed to get current theme')
          }
        } catch (error: any) {
          console.error('❌ Get current theme error:', error)
          const errorMessage = error.message || 'Failed to get current theme'
          set({
            error: errorMessage,
            isLoading: false
          })
          toast.error(errorMessage)
        }
      },

      // Apply theme to institute
      applyTheme: async (themeId: string, customizations?: ThemeCustomizations): Promise<boolean> => {
        set({ isLoading: true, error: null })

        try {
          console.log('🎨 Applying theme:', themeId)

          const response = await themeApi.apply({
            themeId,
            customizations: customizations || get().themeCustomizations
          })

          if (response.success) {
            // Find the applied theme in the themes list
            const appliedTheme = get().themes.find(theme => theme.id === themeId)

            set({
              currentTheme: appliedTheme || null,
              themeCustomizations: customizations || get().themeCustomizations,
              isLoading: false,
              previewTheme: null // Clear preview after applying
            })

            console.log('✅ Theme applied successfully:', appliedTheme?.name)
            toast.success(`Theme "${appliedTheme?.name}" applied successfully!`)

            // Refresh current theme to get latest data
            await get().getCurrentTheme()

            return true
          } else {
            throw new Error('Failed to apply theme')
          }
        } catch (error: any) {
          console.error('❌ Apply theme error:', error)
          const errorMessage = error.message || 'Failed to apply theme'
          set({
            error: errorMessage,
            isLoading: false
          })
          toast.error(errorMessage)
          return false
        }
      },

      // Preview theme without applying
      previewThemeById: async (themeId: string) => {
        set({ isLoading: true, error: null })

        try {
          console.log('🎨 Previewing theme:', themeId)

          const response = await themeApi.preview(themeId)

          if (response.success) {
            set({
              previewTheme: response.theme,
              isLoading: false
            })
            console.log('✅ Theme preview loaded:', response.theme.name)
            toast.success(`Preview loaded for "${response.theme.name}"`)
          } else {
            throw new Error('Failed to preview theme')
          }
        } catch (error: any) {
          console.error('❌ Preview theme error:', error)
          const errorMessage = error.message || 'Failed to preview theme'
          set({
            error: errorMessage,
            isLoading: false
          })
          toast.error(errorMessage)
        }
      },

      // Fetch theme history
      fetchThemeHistory: async () => {
        set({ isLoading: true, error: null })

        try {
          console.log('🎨 Fetching theme history...')

          const response = await themeApi.getHistory()

          if (response.success) {
            set({
              themeHistory: response.themeHistory,
              isLoading: false
            })
            console.log(`✅ Loaded ${response.themeHistory.length} theme history entries`)
          } else {
            throw new Error('Failed to fetch theme history')
          }
        } catch (error: any) {
          console.error('❌ Fetch theme history error:', error)
          const errorMessage = error.message || 'Failed to fetch theme history'
          set({
            error: errorMessage,
            isLoading: false
          })
          toast.error(errorMessage)
        }
      },

      // Update theme customizations
      updateCustomizations: async (customizations: ThemeCustomizations): Promise<boolean> => {
        set({ isLoading: true, error: null })

        try {
          console.log('🎨 Updating theme customizations...')

          const response = await themeApi.updateCustomizations(customizations)

          if (response.success) {
            set({
              themeCustomizations: response.customizations,
              isLoading: false
            })
            console.log('✅ Theme customizations updated')
            toast.success('Theme customizations updated successfully!')
            return true
          } else {
            throw new Error('Failed to update customizations')
          }
        } catch (error: any) {
          console.error('❌ Update customizations error:', error)
          const errorMessage = error.message || 'Failed to update customizations'
          set({
            error: errorMessage,
            isLoading: false
          })
          toast.error(errorMessage)
          return false
        }
      },

      // Clear preview
      clearPreview: () => {
        set({ previewTheme: null })
        console.log('🎨 Theme preview cleared')
      },

      // Clear error
      clearError: () => {
        set({ error: null })
      }
    }),
    {
      name: 'institute-theme-store'
    }
  )
)
