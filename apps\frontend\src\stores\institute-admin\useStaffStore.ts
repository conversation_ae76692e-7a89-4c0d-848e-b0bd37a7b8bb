import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

// Types and Interfaces
export interface StaffMember {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  legacyRole: string
  institute: {
    id: string
    name: string
  }
  branch?: {
    id: string
    name: string
    code: string
  } | null
  isActive: boolean
  emailVerified: boolean
  lastLogin?: string
  createdAt: string
  updatedAt: string
}

interface Role {
  id: string
  name: string
  description: string
  isActive: boolean
}

interface Branch {
  id: string
  name: string
  code: string
  isActive: boolean
}

interface StaffCreationData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password: string
  legacyRole: string
  branch_id?: string
  isActive: boolean
}

interface StaffUpdateData {
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  legacyRole?: string
  branch_id?: string
  isActive?: boolean
}

interface StaffFilters {
  search: string
  role: string
  branch_id: string
  status: 'all' | 'active' | 'inactive'
}

interface Pagination {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface StaffStore {
  // State
  staffMembers: StaffMember[]
  selectedStaff: StaffMember | null
  availableBranches: Branch[]
  availableRoles: Role[]

  // Loading States
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean
  isFetchingRoles: boolean
  isFetchingBranches: boolean
  error: string | null

  // Pagination
  pagination: Pagination

  // Filters
  filters: StaffFilters

  // Actions
  fetchStaff: () => Promise<void>
  fetchAvailableRoles: () => Promise<void>
  fetchAvailableBranches: () => Promise<void>
  createStaff: (data: StaffCreationData) => Promise<void>
  updateStaff: (id: string, data: StaffUpdateData) => Promise<void>
  deleteStaff: (id: string) => Promise<void>
  toggleStaffStatus: (id: string) => Promise<void>
  setFilters: (filters: Partial<StaffFilters>) => void
  setSelectedStaff: (staff: StaffMember | null) => void
  clearError: () => void
  setPage: (page: number) => void
}

// Initial state
const initialFilters: StaffFilters = {
  search: '',
  role: 'all',
  branch_id: 'all',
  status: 'all'
}

const initialPagination: Pagination = {
  page: 1,
  limit: 20,
  totalPages: 0,
  totalDocs: 0,
  hasNextPage: false,
  hasPrevPage: false
}

export const useStaffStore = create<StaffStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      staffMembers: [],
      selectedStaff: null,
      availableBranches: [],
      availableRoles: [],
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isFetchingRoles: false,
      isFetchingBranches: false,
      error: null,
      pagination: initialPagination,
      filters: initialFilters,

      // Actions
      setSelectedStaff: (staff) => set({ selectedStaff: staff }),
      
      setFilters: (newFilters) => {
        set(state => ({
          filters: { ...state.filters, ...newFilters },
          pagination: { ...state.pagination, page: 1 } // Reset to first page when filtering
        }))
        // Automatically fetch staff with new filters
        get().fetchStaff()
      },

      setPage: (page) => {
        set(state => ({
          pagination: { ...state.pagination, page }
        }))
        get().fetchStaff()
      },

      clearError: () => set({ error: null }),

      // Fetch staff members
      fetchStaff: async () => {
        set({ isLoading: true, error: null })
        try {
          const { filters, pagination } = get()
          const params: Record<string, string> = {
            page: pagination.page.toString(),
            limit: pagination.limit.toString()
          }

          if (filters.search) params.search = filters.search
          if (filters.role && filters.role !== 'all') params.role = filters.role
          if (filters.branch_id && filters.branch_id !== 'all') params.branch_id = filters.branch_id
          if (filters.status !== 'all') params.status = filters.status

          const response = await api.get('/api/institute-admin/staff', params)

          if (response.success) {
            set({
              staffMembers: response.data,
              pagination: response.pagination,
              isLoading: false
            })
          } else {
            throw new Error(response.error || 'Failed to fetch staff members')
          }
        } catch (error: any) {
          console.error('Error fetching staff:', error)
          set({
            error: error.message || 'Failed to fetch staff members',
            isLoading: false
          })
          toast.error('Failed to fetch staff members')
        }
      },

      // Fetch available roles
      fetchAvailableRoles: async () => {
        set({ isFetchingRoles: true })
        try {
          const response = await api.get('/api/institute-admin/roles')
          
          if (response.success) {
            set({ 
              availableRoles: response.data,
              isFetchingRoles: false 
            })
          } else {
            throw new Error(response.error || 'Failed to fetch roles')
          }
        } catch (error: any) {
          console.error('Error fetching roles:', error)
          set({ isFetchingRoles: false })
          toast.error('Failed to fetch roles')
        }
      },

      // Fetch available branches
      fetchAvailableBranches: async () => {
        set({ isFetchingBranches: true })
        try {
          const response = await api.get('/api/institute-admin/branches')
          
          if (response.success) {
            set({ 
              availableBranches: response.data,
              isFetchingBranches: false 
            })
          } else {
            throw new Error(response.error || 'Failed to fetch branches')
          }
        } catch (error: any) {
          console.error('Error fetching branches:', error)
          set({ isFetchingBranches: false })
          toast.error('Failed to fetch branches')
        }
      },

      // Create staff member
      createStaff: async (data: StaffCreationData) => {
        set({ isCreating: true, error: null })
        
        const loadingToast = toast.loading('Creating staff member...')
        
        try {
          const response = await api.post('/api/institute-admin/staff', data)
          
          if (response.success) {
            toast.dismiss(loadingToast)
            toast.success(`${data.firstName} ${data.lastName} created successfully`)
            
            // Refresh staff list
            await get().fetchStaff()
            
            set({ isCreating: false })
          } else {
            throw new Error(response.error || 'Failed to create staff member')
          }
        } catch (error: any) {
          toast.dismiss(loadingToast)
          
          const errorMessage = error.message || 'Failed to create staff member'
          set({ error: errorMessage, isCreating: false })
          
          toast.error(errorMessage)
          throw error
        }
      },

      // Update staff member
      updateStaff: async (id: string, data: StaffUpdateData) => {
        set({ isUpdating: true, error: null })
        
        const loadingToast = toast.loading('Updating staff member...')
        
        try {
          const response = await api.patch(`/api/institute-admin/staff/${id}`, data)
          
          if (response.success) {
            toast.dismiss(loadingToast)
            toast.success('Staff member updated successfully')
            
            // Refresh staff list
            await get().fetchStaff()
            
            set({ isUpdating: false })
          } else {
            throw new Error(response.error || 'Failed to update staff member')
          }
        } catch (error: any) {
          toast.dismiss(loadingToast)
          
          const errorMessage = error.message || 'Failed to update staff member'
          set({ error: errorMessage, isUpdating: false })
          
          toast.error(errorMessage)
          throw error
        }
      },

      // Delete staff member
      deleteStaff: async (id: string) => {
        set({ isDeleting: true, error: null })
        
        const loadingToast = toast.loading('Deleting staff member...')
        
        try {
          const response = await api.delete(`/api/institute-admin/staff/${id}`)
          
          if (response.success) {
            toast.dismiss(loadingToast)
            toast.success('Staff member deleted successfully')
            
            // Refresh staff list
            await get().fetchStaff()
            
            set({ isDeleting: false })
          } else {
            throw new Error(response.error || 'Failed to delete staff member')
          }
        } catch (error: any) {
          toast.dismiss(loadingToast)
          
          const errorMessage = error.message || 'Failed to delete staff member'
          set({ error: errorMessage, isDeleting: false })
          
          toast.error(errorMessage)
          throw error
        }
      },

      // Toggle staff status
      toggleStaffStatus: async (id: string) => {
        set({ error: null })
        
        const loadingToast = toast.loading('Updating staff status...')
        
        try {
          const response = await api.patch(`/api/institute-admin/staff/${id}/status`)
          
          if (response.success) {
            toast.dismiss(loadingToast)
            toast.success(response.message || 'Staff status updated successfully')
            
            // Refresh staff list
            await get().fetchStaff()
          } else {
            throw new Error(response.error || 'Failed to update staff status')
          }
        } catch (error: any) {
          toast.dismiss(loadingToast)
          
          const errorMessage = error.message || 'Failed to update staff status'
          set({ error: errorMessage })
          
          toast.error(errorMessage)
          throw error
        }
      }
    }),
    {
      name: 'staff-store'
    }
  )
)
