import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { branchesApi, type Branch, type CreateBranchData, type UpdateBranchData } from '@/lib/institute-admin'

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface BranchState {
  // Data state
  branches: Branch[]
  selectedBranch: Branch | null
  pagination: PaginationInfo

  // Loading states
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean

  // UI state
  showCreateBranchModal: boolean
  showEditBranchModal: boolean
  showDeleteConfirmModal: boolean
  editingBranch: Branch | null
  deletingBranch: Branch | null

  // Filters and search
  searchQuery: string
  statusFilter: 'all' | 'active' | 'inactive'

  // Actions - Data operations
  fetchBranches: (params?: { page?: number; limit?: number; search?: string }) => Promise<void>
  createBranch: (data: CreateBranchData) => Promise<void>
  updateBranch: (id: string, data: UpdateBranchData) => Promise<void>
  deleteBranch: (id: string) => Promise<void>
  toggleBranchStatus: (id: string) => Promise<void>

  // Actions - UI state
  setSelectedBranch: (branch: Branch | null) => void
  setShowCreateBranchModal: (show: boolean) => void
  setShowEditBranchModal: (show: boolean) => void
  setShowDeleteConfirmModal: (show: boolean) => void
  setEditingBranch: (branch: Branch | null) => void
  setDeletingBranch: (branch: Branch | null) => void
  setSearchQuery: (query: string) => void
  setStatusFilter: (filter: 'all' | 'active' | 'inactive') => void

  // Utility functions
  clearSelection: () => void
  isAllBranchesView: () => boolean
  getSelectedBranchId: () => string | null
  getBranchById: (id: string) => Branch | undefined
  getActiveBranches: () => Branch[]
  getInactiveBranches: () => Branch[]
  resetFilters: () => void
}

export const useBranchStore = create<BranchState>()(
  devtools(
    (set, get) => ({
      // Initial state
      branches: [],
      selectedBranch: null,
      pagination: {
        page: 1,
        limit: 20,
        totalPages: 0,
        totalDocs: 0,
        hasNextPage: false,
        hasPrevPage: false,
      },

      // Loading states
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,

      // UI state
      showCreateBranchModal: false,
      showEditBranchModal: false,
      showDeleteConfirmModal: false,
      editingBranch: null,
      deletingBranch: null,

      // Filters
      searchQuery: '',
      statusFilter: 'all',

      // Data operations
      fetchBranches: async (params) => {
        set({ isLoading: true })
        try {
          const response = await branchesApi.getAll({
            page: params?.page || get().pagination.page,
            limit: params?.limit || get().pagination.limit,
            search: params?.search || get().searchQuery,
          })

          if (response.success) {
            set({
              branches: response.data,
              pagination: response.pagination,
              isLoading: false,
            })
          } else {
            throw new Error('Failed to fetch branches')
          }
        } catch (error) {
          console.error('Error fetching branches:', error)
          toast.error('Failed to load branches')
          set({ isLoading: false })
        }
      },

      createBranch: async (data) => {
        set({ isCreating: true })
        try {
          // Debug: Check authorization before making request
          const authCheck = branchesApi.checkAuth?.() || { hasToken: false, tokenInfo: 'No auth check available' }
          console.log('🔐 Branch Creation Auth Check:', authCheck)

          // Additional debug: Check full auth state
          if (typeof window !== 'undefined') {
            console.log('🔍 Full Auth State:', {
              directToken: localStorage.getItem('auth_token') ? 'EXISTS' : 'MISSING',
              authStorage: localStorage.getItem('auth-storage') ? 'EXISTS' : 'MISSING',
              userData: localStorage.getItem('user_data') ? 'EXISTS' : 'MISSING'
            })
          }

          const response = await branchesApi.create(data)

          if (response.success) {
            // Add new branch to the list
            set((state) => ({
              branches: [response.data, ...state.branches],
              isCreating: false,
              showCreateBranchModal: false,
            }))

            toast.success(response.message || 'Branch created successfully!')

            // Refresh the list to get updated data
            get().fetchBranches()
          } else {
            throw new Error('Failed to create branch')
          }
        } catch (error: any) {
          console.error('Error creating branch:', error)

          // Provide specific error messages for auth issues
          if (error.message?.includes('401') || error.message?.includes('Authorization')) {
            toast.error('Authentication failed. Please login again.')
          } else if (error.message?.includes('403') || error.message?.includes('forbidden')) {
            toast.error('Access denied. Insufficient permissions.')
          } else {
            toast.error(error.message || 'Failed to create branch')
          }

          set({ isCreating: false })
        }
      },

      updateBranch: async (id, data) => {
        set({ isUpdating: true })
        try {
          const response = await branchesApi.update(id, data)

          if (response.success) {
            // Update branch in the list
            set((state) => ({
              branches: state.branches.map((branch) =>
                branch.id === id ? { ...branch, ...response.data } : branch
              ),
              isUpdating: false,
              showEditBranchModal: false,
              editingBranch: null,
            }))

            // Update selected branch if it's the one being edited
            const currentSelected = get().selectedBranch
            if (currentSelected && currentSelected.id === id) {
              set({ selectedBranch: { ...currentSelected, ...response.data } })
            }

            toast.success(response.message || 'Branch updated successfully!')
          } else {
            throw new Error('Failed to update branch')
          }
        } catch (error) {
          console.error('Error updating branch:', error)
          toast.error('Failed to update branch')
          set({ isUpdating: false })
        }
      },

      deleteBranch: async (id) => {
        set({ isDeleting: true })
        try {
          const response = await branchesApi.delete(id)

          if (response.success) {
            // Remove branch from the list
            set((state) => ({
              branches: state.branches.filter((branch) => branch.id !== id),
              isDeleting: false,
              showDeleteConfirmModal: false,
              deletingBranch: null,
            }))

            // Clear selection if deleted branch was selected
            const currentSelected = get().selectedBranch
            if (currentSelected && currentSelected.id === id) {
              get().clearSelection()
            }

            toast.success(response.message || 'Branch deleted successfully!')
          } else {
            throw new Error('Failed to delete branch')
          }
        } catch (error) {
          console.error('Error deleting branch:', error)
          toast.error('Failed to delete branch')
          set({ isDeleting: false })
        }
      },

      // Soft delete branch (set is_deleted flag)
      softDeleteBranch: async (id: string) => {
        set({ isDeleting: true })
        try {
          // Update branch with is_deleted flag
          const response = await branchesApi.update(id, {
            isActive: false,
            isDeleted: true,
            deletedAt: new Date().toISOString()
          })

          if (response.success) {
            // Remove from local state
            set((state) => ({
              branches: state.branches.filter((branch) => branch.id !== id),
              isDeleting: false,
              showDeleteConfirmModal: false,
              deletingBranch: null,
            }))

            // Clear selection if deleted branch was selected
            const currentSelected = get().selectedBranch
            if (currentSelected && currentSelected.id === id) {
              get().clearSelection()
            }

            toast.success('Branch moved to trash')
          } else {
            throw new Error('Failed to delete branch')
          }
        } catch (error) {
          console.error('Error soft deleting branch:', error)
          toast.error('Failed to delete branch')
          set({ isDeleting: false })
        }
      },

      // Restore soft deleted branch
      restoreBranch: async (id: string) => {
        set({ isUpdating: true })
        try {
          const response = await branchesApi.update(id, {
            isActive: true,
            isDeleted: false,
            deletedAt: null
          })

          if (response.success) {
            // Refresh branches list
            await get().fetchBranches()
            toast.success('Branch restored successfully')
          } else {
            throw new Error('Failed to restore branch')
          }
        } catch (error) {
          console.error('Error restoring branch:', error)
          toast.error('Failed to restore branch')
          set({ isUpdating: false })
        }
      },

      toggleBranchStatus: async (id) => {
        try {
          const response = await branchesApi.toggleStatus(id)

          if (response.success) {
            // Update branch status in the list
            set((state) => ({
              branches: state.branches.map((branch) =>
                branch.id === id ? { ...branch, isActive: !branch.isActive } : branch
              ),
            }))

            // Update selected branch if it's the one being toggled
            const currentSelected = get().selectedBranch
            if (currentSelected && currentSelected.id === id) {
              set({ selectedBranch: { ...currentSelected, isActive: !currentSelected.isActive } })
            }

            toast.success(response.message || 'Branch status updated successfully!')
          } else {
            throw new Error('Failed to toggle branch status')
          }
        } catch (error) {
          console.error('Error toggling branch status:', error)
          toast.error('Failed to update branch status')
        }
      },

      // UI Actions
      setSelectedBranch: (branch) => {
        set({ selectedBranch: branch })

        // Store in localStorage for persistence
        if (branch) {
          localStorage.setItem('selectedBranch', JSON.stringify(branch))
        } else {
          localStorage.removeItem('selectedBranch')
        }
      },

      setShowCreateBranchModal: (show) => set({ showCreateBranchModal: show }),

      setShowEditBranchModal: (show) => set({
        showEditBranchModal: show,
        editingBranch: show ? get().editingBranch : null
      }),

      setShowDeleteConfirmModal: (show) => set({
        showDeleteConfirmModal: show,
        deletingBranch: show ? get().deletingBranch : null
      }),

      setEditingBranch: (branch) => set({ editingBranch: branch }),
      setDeletingBranch: (branch) => set({ deletingBranch: branch }),
      setSearchQuery: (query) => set({ searchQuery: query }),
      setStatusFilter: (filter) => set({ statusFilter: filter }),

      // Utility functions
      clearSelection: () => {
        set({ selectedBranch: null })
        localStorage.removeItem('selectedBranch')
      },

      isAllBranchesView: () => get().selectedBranch === null,

      getSelectedBranchId: () => get().selectedBranch?.id || null,

      getBranchById: (id) => get().branches.find((branch) => branch.id === id),

      getActiveBranches: () => get().branches.filter((branch) => branch.isActive),

      getInactiveBranches: () => get().branches.filter((branch) => !branch.isActive),

      resetFilters: () => set({ searchQuery: '', statusFilter: 'all' }),
    }),
    {
      name: 'branch-store',
    }
  )
)

// Initialize from localStorage on app start
if (typeof window !== 'undefined') {
  const storedBranch = localStorage.getItem('selectedBranch')
  if (storedBranch) {
    try {
      const branch = JSON.parse(storedBranch)
      useBranchStore.getState().setSelectedBranch(branch)
    } catch (error) {
      console.error('Failed to parse stored branch:', error)
      localStorage.removeItem('selectedBranch')
    }
  }
}
