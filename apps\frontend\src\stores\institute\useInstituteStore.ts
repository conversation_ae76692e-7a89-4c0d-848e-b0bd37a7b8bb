import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { toast } from 'sonner'
import { useBranchStore } from './useBranchStore'
import { instituteApi, branchesApi, type Institute as ApiInstitute, type UpdateInstituteData, type Branch as ApiBranch } from '@/lib/institute-admin'
import { api } from '@/lib/api'

// Use the API Branch type
type Branch = ApiBranch

interface Student {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  branch: any
  lastActivity: string
  status: 'active' | 'inactive' | 'suspended'
  createdAt: string
}

interface InstituteStats {
  totalStudents: number
  activeStudents: number
  totalCourses: number
  publishedCourses: number
  totalBranches: number
  activeBranches: number
  monthlyRevenue: number
  totalRevenue: number
  enrollmentsThisMonth: number
  completionRate: number
}

interface InstituteState {
  // Data
  institute: any | null
  branches: Branch[]
  students: Student[]
  instituteStats: InstituteStats
  selectedBranch: Branch | null
  selectedStudent: Student | null

  // UI State
  isLoading: boolean
  error: string | null

  // Filters
  studentFilters: {
    search?: string
    branch?: string
    status?: string
  }
  branchFilters: {
    search?: string
    status?: string
  }

  // Actions
  setSelectedBranch: (branch: Branch | null) => void
  setSelectedStudent: (student: Student | null) => void
  setStudentFilters: (filters: Partial<typeof studentFilters>) => void
  setBranchFilters: (filters: Partial<typeof branchFilters>) => void

  // API Actions
  fetchInstituteData: (force?: boolean) => Promise<void>
  fetchBranches: () => Promise<void>
  fetchStudents: (branchId?: string) => Promise<void>
  fetchInstituteStats: (branchId?: string) => Promise<void>
  
  createBranch: (branchData: any) => Promise<void>
  updateBranch: (id: string, branchData: any) => Promise<void>
  deleteBranch: (id: string) => Promise<void>
  
  createStudent: (studentData: Partial<Student>) => Promise<void>
  updateStudent: (id: string, studentData: Partial<Student>) => Promise<void>
  deleteStudent: (id: string) => Promise<void>
  
  updateInstituteProfile: (profileData: any) => Promise<void>

  // Utility Actions
  clearError: () => void
}

const initialStats: InstituteStats = {
  totalStudents: 0,
  activeStudents: 0,
  totalCourses: 0,
  publishedCourses: 0,
  totalBranches: 0,
  activeBranches: 0,
  monthlyRevenue: 0,
  totalRevenue: 0,
  enrollmentsThisMonth: 0,
  completionRate: 0
}

export const useInstituteStore = create<InstituteState>()(
  devtools(
    persist(
      (set, get) => ({
      // Initial State
      institute: null,
      branches: [],
      students: [],
      instituteStats: initialStats,
      selectedBranch: null,
      selectedStudent: null,
      isLoading: false,
      error: null,
      studentFilters: {},
      branchFilters: {},

      // UI Actions
      setSelectedBranch: (branch) => set({ selectedBranch: branch }),
      setSelectedStudent: (student) => set({ selectedStudent: student }),
      setStudentFilters: (filters) => set((state) => ({
        studentFilters: { ...state.studentFilters, ...filters }
      })),
      setBranchFilters: (filters) => set((state) => ({
        branchFilters: { ...state.branchFilters, ...filters }
      })),

      // API Actions
      fetchInstituteData: async (force = false) => {
        const { institute } = get()

        // Skip fetch if data already exists and not forced
        if (institute && !force) {
          console.log('📦 Institute data already cached, skipping API call')
          return
        }

        try {
          console.log('🔄 Fetching institute data from API...')
          const response = await instituteApi.getDetails()

          if (response.success) {
            set({ institute: response.data })
            console.log('✅ Institute data fetched and cached')
          } else {
            throw new Error('Failed to fetch institute data')
          }
        } catch (error) {
          console.error('Failed to fetch institute data:', error)
          set({ error: error instanceof Error ? error.message : 'Failed to fetch institute data' })
        }
      },

      fetchBranches: async () => {
        set({ isLoading: true, error: null })
        try {
          const data = await branchesApi.getAll()

          if (data.success) {
            set({
              branches: data.data,
              isLoading: false
            })
          } else {
            throw new Error('Failed to fetch branches')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch branches')
        }
      },

      fetchStudents: async (branchId?: string) => {
        set({ isLoading: true, error: null })
        try {
          const { studentFilters } = get()
          const params = new URLSearchParams({
            ...(studentFilters.search && { search: studentFilters.search }),
            ...(branchId && { branch: branchId }),
            ...(studentFilters.status && { status: studentFilters.status })
          })

          const data = await api.get(`/api/institute-admin/students?${params}`)

          if (data.success) {
            set({
              students: data.data,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch students')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch students')
        }
      },

      fetchInstituteStats: async (branchId?: string) => {
        try {
          const params = new URLSearchParams()
          if (branchId) {
            params.append('branch', branchId)
          }

          const data = await instituteApi.getStats(branchId)

          if (data.success) {
            set({ instituteStats: data.data })
          } else {
            throw new Error('Failed to fetch institute stats')
          }
        } catch (error) {
          console.error('Failed to fetch institute stats:', error)
        }
      },

      createBranch: async (branchData) => {
        set({ isLoading: true, error: null })
        try {
          const data = await branchesApi.create(branchData)

          if (data.success) {
            await get().fetchBranches()
            await get().fetchInstituteStats()
            toast.success('Branch Created', {
              description: `Branch "${data.data.name}" has been created successfully.`
            })
            set({ isLoading: false })
          } else {
            throw new Error('Failed to create branch')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to create branch', {
            description: errorMessage
          })
          throw error
        }
      },

      updateBranch: async (id, branchData) => {
        set({ isLoading: true, error: null })
        try {
          const data = await branchesApi.update(id, branchData)

          if (data.success) {
            // Update branch in local state
            set((state) => ({
              branches: state.branches.map(branch =>
                branch.id === id ? { ...branch, ...data.data } : branch
              ),
              selectedBranch: state.selectedBranch?.id === id
                ? { ...state.selectedBranch, ...data.data }
                : state.selectedBranch,
              isLoading: false
            }))

            toast.success('Branch Updated', {
              description: `Branch "${data.data.name}" has been updated successfully.`
            })
          } else {
            throw new Error('Failed to update branch')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to update branch', {
            description: errorMessage
          })
          throw error
        }
      },

      deleteBranch: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const data = await branchesApi.delete(id)

          if (data.success) {
            // Remove branch from local state
            set((state) => ({
              branches: state.branches.filter(branch => branch.id !== id),
              selectedBranch: state.selectedBranch?.id === id ? null : state.selectedBranch,
              isLoading: false
            }))

            await get().fetchInstituteStats()
            toast.success('Branch Deleted', {
              description: 'Branch has been deleted successfully.'
            })
          } else {
            throw new Error('Failed to delete branch')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to delete branch', {
            description: errorMessage
          })
          throw error
        }
      },

      createStudent: async (studentData) => {
        set({ isLoading: true, error: null })
        try {
          const data = await api.post('/api/institute-admin/students', studentData)

          if (data.success) {
            await get().fetchStudents()
            await get().fetchInstituteStats()
            toast.success('Student Created', {
              description: `Student "${data.data.firstName} ${data.data.lastName}" has been created successfully.`
            })
            set({ isLoading: false })
          } else {
            throw new Error('Failed to create student')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to create student', {
            description: errorMessage
          })
          throw error
        }
      },

      updateStudent: async (id, studentData) => {
        set({ isLoading: true, error: null })
        try {
          const data = await api.put(`/api/institute-admin/students/${id}`, studentData)

          if (data.success) {
            // Update student in local state
            set((state) => ({
              students: state.students.map(student =>
                student.id === id ? { ...student, ...data.data } : student
              ),
              selectedStudent: state.selectedStudent?.id === id
                ? { ...state.selectedStudent, ...data.data }
                : state.selectedStudent,
              isLoading: false
            }))

            toast.success('Student Updated', {
              description: `Student has been updated successfully.`
            })
          } else {
            throw new Error('Failed to update student')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to update student', {
            description: errorMessage
          })
          throw error
        }
      },

      deleteStudent: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const data = await api.delete(`/api/institute-admin/students/${id}`)

          if (data.success) {
            // Remove student from local state
            set((state) => ({
              students: state.students.filter(student => student.id !== id),
              selectedStudent: state.selectedStudent?.id === id ? null : state.selectedStudent,
              isLoading: false
            }))

            await get().fetchInstituteStats()
            toast.success('Student Deleted', {
              description: 'Student has been deleted successfully.'
            })
          } else {
            throw new Error('Failed to delete student')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to delete student', {
            description: errorMessage
          })
          throw error
        }
      },

      updateInstituteProfile: async (profileData: UpdateInstituteData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await instituteApi.updateDetails(profileData)

          if (response.success) {
            set({
              institute: { ...get().institute, ...response.data },
              isLoading: false
            })

            toast.success('Profile Updated', {
              description: response.message || 'Institute profile has been updated successfully.'
            })
          } else {
            throw new Error('Failed to update profile')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to update profile', {
            description: errorMessage
          })
          throw error
        }
      },

      // Utility Actions
      clearError: () => set({ error: null })
    }),
    {
      name: 'institute-storage',
      // Only persist essential data, not loading states
      partialize: (state) => ({
        institute: state.institute,
        branches: state.branches,
        students: state.students,
        instituteStats: state.instituteStats
      })
    }
  ),
  {
    name: 'institute-store'
  }
))
