import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  isActive: boolean
  applicableToStudents: boolean
  createdAt: Date
}

interface RoleStore {
  // State
  studentRoles: Role[]
  allRoles: Role[]
  isLoading: boolean
  isFetchingStudentRoles: boolean
  error: string | null
  
  // Actions
  fetchStudentRoles: () => Promise<void>
  fetchAllRoles: () => Promise<void>
  clearError: () => void
}

export const useRoleStore = create<RoleStore>()(
  devtools((set, get) => ({
    // Initial state
    studentRoles: [],
    allRoles: [],
    isLoading: false,
    isFetchingStudentRoles: false,
    error: null,
    
    // Fetch student-specific roles
    fetchStudentRoles: async () => {
      set({ isFetchingStudentRoles: true, error: null })
      try {
        const response = await api.get('/api/institute-admin/roles')
        if (response.success) {
          set({ 
            studentRoles: response.data, 
            isFetchingStudentRoles: false 
          })
        } else {
          throw new Error(response.error || 'Failed to fetch student roles')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        set({ 
          error: errorMessage, 
          isFetchingStudentRoles: false 
        })
        toast.error('Failed to load student roles', {
          description: errorMessage
        })
      }
    },
    
    // Fetch all roles (for admin purposes)
    fetchAllRoles: async () => {
      set({ isLoading: true, error: null })
      try {
        const response = await api.get('/api/institute-admin/roles')
        if (response.success) {
          set({ 
            allRoles: response.data, 
            isLoading: false 
          })
        } else {
          throw new Error(response.error || 'Failed to fetch roles')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        set({ 
          error: errorMessage, 
          isLoading: false 
        })
        toast.error('Failed to load roles', {
          description: errorMessage
        })
      }
    },
    
    clearError: () => set({ error: null })
  }))
)

// Helper function to get role permissions
export const getRolePermissions = (role: Role, institutePermissions: string[] = []) => {
  const baseStudentPermissions = [
    'view_courses',
    'submit_assignments',
    'view_progress',
    'access_materials'
  ]
  
  // Combine base permissions with role-specific permissions
  const rolePermissions = role.permissions || []
  
  // Filter institute permissions that apply to students
  const applicableInstitutePermissions = institutePermissions.filter(permission =>
    permission.startsWith('student_') || permission.includes('course_access')
  )
  
  return [
    ...baseStudentPermissions,
    ...rolePermissions,
    ...applicableInstitutePermissions
  ].filter((permission, index, array) => array.indexOf(permission) === index) // Remove duplicates
}

// Helper function to check if user can assign a specific role
export const canAssignRole = (userRole: string, targetRole: Role): boolean => {
  switch (userRole) {
    case 'institute_admin':
      // Institute admin can assign any student role
      return targetRole.applicableToStudents
    
    case 'institute_staff':
      // Staff can assign basic student roles (not admin roles)
      return targetRole.applicableToStudents && 
             !targetRole.permissions.some(p => p.includes('admin'))
    
    case 'branch_manager':
      // Branch manager can assign basic student roles
      return targetRole.applicableToStudents && 
             !targetRole.permissions.some(p => p.includes('admin') || p.includes('staff'))
    
    default:
      return false
  }
}
