import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'
import { studentToastMessages, handleStudentApiError } from '@/lib/toastTemplates'

// Types and Interfaces
export interface Student {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  legacyRole: 'student'
  institute_id: string
  branch_id?: string
  role_id?: string
  address?: string
  dateOfBirth?: Date
  gender?: 'male' | 'female' | 'other'
  is_active: boolean
  emailVerified: boolean
  lastLogin?: Date
  enrolledCourses?: number
  totalProgress?: number
  lastActivity?: string
  createdAt: Date
  updatedAt: Date
  
  // Populated relationships
  branch?: {
    id: string
    name: string
    code: string
  }
  role?: {
    id: string
    name: string
    permissions: string[]
  }
}

interface Branch {
  id: string
  name: string
  code: string
  isActive: boolean
}

interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  isActive: boolean
}

interface StudentCreationData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password: string
  branch?: string  // Use 'branch' field name (maps to branch_id in database)
  // role_id removed - automatically assigned as student role
  address?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other' | 'select-gender'
  // Location fields (will be stored in StudentDetails)
  country?: string
  state?: string
  district?: string
  // StudentDetails fields (optional)
  studentDetails?: {
    education?: {
      highestQualification?: string
      institution?: string
      fieldOfStudy?: string
      graduationYear?: number
      percentage?: number
    }
    personalInfo?: {
      fatherName?: string
      motherName?: string
      guardianName?: string
      emergencyContact?: string
      bloodGroup?: string
      nationality?: string
      religion?: string
      caste?: string
    }
    documents?: {
      aadharNumber?: string
      panNumber?: string
      passportNumber?: string
      drivingLicense?: string
    }
    additionalInfo?: {
      hobbies?: string
      skills?: string
      experience?: string
      goals?: string
      notes?: string
    }
  }
  is_active: boolean
}

interface StudentUpdateFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password?: string
  branch?: string  // Use 'branch' field name (maps to branch_id in database)
  // role_id removed - students always keep student role
  address?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other' | 'select-gender'
  // Location fields (will be stored in StudentDetails)
  country?: string
  state?: string
  district?: string
  // StudentDetails fields (optional)
  studentDetails?: {
    education?: {
      highestQualification?: string
      institution?: string
      fieldOfStudy?: string
      graduationYear?: number
      percentage?: number
    }
    personalInfo?: {
      fatherName?: string
      motherName?: string
      guardianName?: string
      emergencyContact?: string
      bloodGroup?: string
      nationality?: string
      religion?: string
      caste?: string
    }
    documents?: {
      aadharNumber?: string
      panNumber?: string
      passportNumber?: string
      drivingLicense?: string
    }
    additionalInfo?: {
      hobbies?: string
      skills?: string
      experience?: string
      goals?: string
      notes?: string
    }
  }
  is_active: boolean
}

interface StudentFilters {
  search: string
  branch: string  // Use 'branch' field name (maps to branch_id in database)
  status: 'all' | 'active' | 'inactive'
  // role_id removed - all users in student management are students
  dateRange: {
    from: Date | null
    to: Date | null
  }
}

interface StudentStore {
  // State
  students: Student[]
  currentStudent: Student | null
  availableBranches: Branch[]
  availableRoles: Role[]
  filters: StudentFilters
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalDocs: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
  
  // Loading states
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  isFetching: boolean
  isFetchingBranches: boolean
  isFetchingRoles: boolean
  
  // Error state
  error: string | null
  
  // Actions
  fetchStudents: (page?: number) => Promise<void>
  fetchStudentById: (id: string | number) => Promise<Student | null>
  fetchAvailableBranches: () => Promise<void>
  // fetchAvailableRoles removed - not needed for student management
  createStudent: (data: StudentCreationData) => Promise<void>
  updateStudent: (id: string, data: StudentUpdateFormData) => Promise<void>
  toggleStudentStatus: (id: string, is_active: boolean) => Promise<void>
  deleteStudent: (id: string, reason?: string) => Promise<void>
  bulkUpdateStudentStatus: (studentIds: string[], is_active: boolean) => Promise<void>
  setFilters: (filters: Partial<StudentFilters>) => void
  clearFilters: () => void
  clearError: () => void
  clearCurrentStudent: () => void
}

const initialFilters: StudentFilters = {
  search: '',
  branch: 'all',  // Use 'branch' field name
  status: 'all',
  // role_id removed - all users are students
  dateRange: { from: null, to: null }
}

export const useStudentStore = create<StudentStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        students: [],
        currentStudent: null,
        availableBranches: [],
        availableRoles: [],
        filters: initialFilters,
        pagination: {
          page: 1,
          limit: 20,
          totalPages: 0,
          totalDocs: 0,
          hasNextPage: false,
          hasPrevPage: false
        },
        
        // Loading states
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isFetching: false,
        isFetchingBranches: false,
        isFetchingRoles: false,
        
        // Error state
        error: null,
        
        // Fetch students with filtering and pagination
        fetchStudents: async (page = 1) => {
          set({ isFetching: true, error: null })
          try {
            const { filters } = get()
            const params = new URLSearchParams({
              page: page.toString(),
              limit: get().pagination.limit.toString(),
              ...(filters.search && { search: filters.search }),
              ...(filters.branch_id && { branch_id: filters.branch_id }),
              ...(filters.status !== 'all' && { status: filters.status }),
              ...(filters.role_id && { role_id: filters.role_id }),
              ...(filters.dateRange.from && { date_from: filters.dateRange.from.toISOString() }),
              ...(filters.dateRange.to && { date_to: filters.dateRange.to.toISOString() })
            })
            
            const response = await api.get(`/api/institute-admin/students?${params}`)
            
            if (response.success) {
              set({
                students: response.data,
                pagination: response.pagination,
                isFetching: false
              })
            } else {
              throw new Error(response.error || 'Failed to fetch students')
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            set({ error: errorMessage, isFetching: false })
            handleStudentApiError(error, 'fetch')
          }
        },
        
        // Fetch available branches based on user permissions
        fetchAvailableBranches: async () => {
          set({ isFetchingBranches: true })
          try {
            const response = await api.get('/api/institute-admin/branches')
            if (response.success) {
              set({ availableBranches: response.data, isFetchingBranches: false })
            } else {
              throw new Error(response.error || 'Failed to fetch branches')
            }
          } catch (error) {
            set({ isFetchingBranches: false })
            console.error('Failed to fetch branches:', error)
            toast.error('Failed to load branches')
          }
        },

        // Fetch single student by ID
        fetchStudentById: async (id: string | number) => {
          // Convert ID to string and validate
          const studentId = String(id)
          console.log('fetchStudentById: Received ID:', id, 'Type:', typeof id, 'Converted:', studentId)

          if (!id || studentId.trim() === '' || studentId === 'undefined' || studentId === 'null') {
            console.error('fetchStudentById: Invalid student ID provided:', { originalId: id, convertedId: studentId, type: typeof id })
            set({ isFetching: false, error: 'Invalid student ID' })
            toast.error('Invalid student ID. Please select a valid student.')
            return null
          }

          console.log('fetchStudentById: Fetching student with ID:', studentId)
          set({ isFetching: true, error: null })
          try {
            const response = await api.get(`/api/institute-admin/students/${studentId}`)
            if (response.success) {
              console.log('fetchStudentById: Successfully fetched student data')
              set({ currentStudent: response.data, isFetching: false })
              return response.data
            } else {
              throw new Error(response.error || 'Failed to fetch student')
            }
          } catch (error) {
            console.error('fetchStudentById: Error fetching student:', error)
            set({ isFetching: false, error: (error as Error).message })

            // More specific error messages
            if ((error as any)?.response?.status === 404) {
              toast.error('Student not found. The student may have been deleted.')
            } else if ((error as any)?.response?.status === 401) {
              toast.error('You are not authorized to view this student.')
            } else {
              toast.error('Failed to load student details. Please try again.')
            }
            return null
          }
        },

        // fetchAvailableRoles removed - not needed for student management
        
        // Create student
        createStudent: async (data: StudentCreationData) => {
          set({ isCreating: true, error: null })
          
          const loadingToast = studentToastMessages.createLoading()
          
          try {
            const response = await api.post('/api/institute-admin/students', data)
            
            if (response.success) {
              toast.dismiss(loadingToast)
              studentToastMessages.createSuccess(`${data.firstName} ${data.lastName}`)
              
              // Refresh student list
              await get().fetchStudents()
              
              set({ isCreating: false })
            } else {
              throw new Error(response.error || 'Failed to create student')
            }
          } catch (error) {
            toast.dismiss(loadingToast)
            
            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            set({ error: errorMessage, isCreating: false })
            
            handleStudentApiError(error, 'create')
            throw error
          }
        },

        // Update student
        updateStudent: async (id: string, data: StudentUpdateFormData) => {
          set({ isUpdating: true, error: null })

          const loadingToast = studentToastMessages.updateLoading()

          try {
            const response = await api.put(`/api/institute-admin/students/${id}`, data)

            if (response.success) {
              toast.dismiss(loadingToast)
              studentToastMessages.updateSuccess(`${data.firstName} ${data.lastName}`)

              // Update student in local state
              set(state => ({
                students: state.students.map(student =>
                  student.id === id ? { ...student, ...response.data } : student
                ),
                isUpdating: false
              }))
            } else {
              throw new Error(response.error || 'Failed to update student')
            }
          } catch (error) {
            toast.dismiss(loadingToast)

            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            set({ error: errorMessage, isUpdating: false })

            handleStudentApiError(error, 'update')
            throw error
          }
        },

        // Toggle student status
        toggleStudentStatus: async (id: string, is_active: boolean) => {
          set({ isUpdating: true, error: null })

          const action = is_active ? 'Activating' : 'Deactivating'
          const loadingToast = studentToastMessages.statusChangeLoading(action)

          try {
            const response = await api.patch(`/api/institute-admin/students/${id}/status`, { is_active })

            if (response.success) {
              toast.dismiss(loadingToast)

              const student = get().students.find(s => s.id === id)
              const studentName = student ? `${student.firstName} ${student.lastName}` : 'Student'

              if (is_active) {
                studentToastMessages.activateSuccess(studentName)
              } else {
                studentToastMessages.deactivateSuccess(studentName)
              }

              // Update student in local state
              set(state => ({
                students: state.students.map(student =>
                  student.id === id ? { ...student, is_active } : student
                ),
                isUpdating: false
              }))
            } else {
              throw new Error(response.error || 'Failed to update student status')
            }
          } catch (error) {
            toast.dismiss(loadingToast)

            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            set({ error: errorMessage, isUpdating: false })

            handleStudentApiError(error, 'status')
            throw error
          }
        },

        // Delete student (soft delete)
        deleteStudent: async (id: string, reason?: string) => {
          set({ isUpdating: true, error: null })

          const loadingToast = studentToastMessages.deleteLoading()

          try {
            const response = await api.delete(`/api/institute-admin/students/${id}`, { reason })

            if (response.success) {
              toast.dismiss(loadingToast)

              const student = get().students.find(s => s.id === id)
              const studentName = student ? `${student.firstName} ${student.lastName}` : 'Student'

              studentToastMessages.deleteSuccess(studentName)

              // Remove student from local state
              set(state => ({
                students: state.students.filter(student => student.id !== id),
                isUpdating: false
              }))
            } else {
              throw new Error(response.error || 'Failed to remove student')
            }
          } catch (error) {
            toast.dismiss(loadingToast)

            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            set({ error: errorMessage, isUpdating: false })

            handleStudentApiError(error, 'delete')
            throw error
          }
        },

        // Bulk update student status
        bulkUpdateStudentStatus: async (studentIds: string[], is_active: boolean) => {
          set({ isUpdating: true, error: null })

          const action = is_active ? 'activate' : 'deactivate'
          const loadingToast = toast.loading(`${action}ing ${studentIds.length} students...`)

          try {
            const promises = studentIds.map(id =>
              api.patch(`/api/institute-admin/students/${id}/status`, { is_active })
            )

            const results = await Promise.allSettled(promises)
            const successful = results.filter(result => result.status === 'fulfilled').length
            const failed = results.length - successful

            toast.dismiss(loadingToast)

            if (successful > 0) {
              if (is_active) {
                studentToastMessages.bulkActivateSuccess(successful)
              } else {
                studentToastMessages.bulkDeactivateSuccess(successful)
              }

              // Update students in local state
              set(state => ({
                students: state.students.map(student =>
                  studentIds.includes(student.id) ? { ...student, is_active } : student
                ),
                isUpdating: false
              }))
            }

            if (failed > 0) {
              studentToastMessages.bulkOperationError(action, `${failed} students could not be ${action}d`)
            }
          } catch (error) {
            toast.dismiss(loadingToast)

            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            set({ error: errorMessage, isUpdating: false })

            studentToastMessages.bulkOperationError(action, errorMessage)
            throw error
          }
        },

        // Set filters
        setFilters: (newFilters: Partial<StudentFilters>) => {
          set(state => ({
            filters: { ...state.filters, ...newFilters }
          }))
          // Auto-refresh students when filters change
          get().fetchStudents(1)
        },

        // Clear filters
        clearFilters: () => {
          set({ filters: initialFilters })
          get().fetchStudents(1)
        },

        clearError: () => set({ error: null }),

        clearCurrentStudent: () => set({ currentStudent: null })
      }),
      {
        name: 'student-store',
        partialize: (state) => ({
          filters: state.filters,
          pagination: { ...state.pagination, page: 1 } // Reset page on persist
        })
      }
    )
  )
)
