import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

interface Country {
  id: string
  name: string
  code: string
  flag?: string
  details: {
    capital?: string
    currency?: string
    currencyCode?: string
    language?: string
    timezone?: string
    population?: number
    area?: number
  }
  coordinates?: {
    latitude?: number
    longitude?: number
  }
  isActive: boolean
  priority: number
  createdAt: string
  updatedAt: string
}

interface State {
  id: string
  name: string
  code?: string
  country: string | Country
  details: {
    capital?: string
    population?: number
    area?: number
    type: 'state' | 'province' | 'territory' | 'region'
  }
  coordinates?: {
    latitude?: number
    longitude?: number
  }
  isActive: boolean
  priority: number
  createdAt: string
  updatedAt: string
}

interface District {
  id: string
  name: string
  code?: string
  state: string | State
  details: {
    type: 'district' | 'city' | 'municipality' | 'town' | 'village'
    population?: number
    area?: number
    pincode?: string
  }
  coordinates?: {
    latitude?: number
    longitude?: number
  }
  isActive: boolean
  priority: number
  createdAt: string
  updatedAt: string
}

interface LocationFilters {
  search: string
  isActive: 'all' | 'true' | 'false'
  countryId?: string
  stateId?: string
  type?: string
}

interface Pagination {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface LocationState {
  // Data
  countries: Country[]
  states: State[]
  districts: District[]

  // UI State
  viewMode: 'list' | 'card'
  isLoading: boolean
  error: string | null

  // Filters
  filters: LocationFilters

  // Pagination
  countriesPagination: Pagination
  statesPagination: Pagination
  districtsPagination: Pagination

  // Selected items
  selectedCountry: Country | null
  selectedState: State | null
  selectedDistrict: District | null

  // Actions
  setViewMode: (mode: 'list' | 'card') => void
  setFilters: (filters: Partial<LocationFilters>) => void
  setSelectedCountry: (country: Country | null) => void
  setSelectedState: (state: State | null) => void
  setSelectedDistrict: (district: District | null) => void

  // API Actions
  fetchCountries: (page?: number) => Promise<void>
  fetchStates: (countryId?: string, page?: number) => Promise<void>
  fetchDistricts: (stateId?: string, countryId?: string, page?: number) => Promise<void>
  fetchLocationHierarchy: (countryId: string) => Promise<void>

  // CRUD Actions
  createCountry: (data: Partial<Country>) => Promise<void>
  updateCountry: (id: string, data: Partial<Country>) => Promise<void>
  deleteCountry: (id: string) => Promise<void>

  createState: (data: Partial<State>) => Promise<void>
  updateState: (id: string, data: Partial<State>) => Promise<void>
  deleteState: (id: string) => Promise<void>

  createDistrict: (data: Partial<District>) => Promise<void>
  updateDistrict: (id: string, data: Partial<District>) => Promise<void>
  deleteDistrict: (id: string) => Promise<void>

  // Utility Actions
  clearError: () => void
  resetFilters: () => void
}

const initialFilters: LocationFilters = {
  search: '',
  isActive: 'true'
}

const initialPagination: Pagination = {
  page: 1,
  limit: 20,
  totalPages: 1,
  totalDocs: 0,
  hasNextPage: false,
  hasPrevPage: false
}

export const useLocationStore = create<LocationState>()(
  devtools(
    (set, get) => ({
      // Initial State
      countries: [],
      states: [],
      districts: [],
      viewMode: 'list',
      isLoading: false,
      error: null,
      filters: initialFilters,
      countriesPagination: initialPagination,
      statesPagination: initialPagination,
      districtsPagination: initialPagination,
      selectedCountry: null,
      selectedState: null,
      selectedDistrict: null,

      // UI Actions
      setViewMode: (mode) => set({ viewMode: mode }),

      setFilters: (newFilters) => set((state) => ({
        filters: { ...state.filters, ...newFilters }
      })),

      setSelectedCountry: (country) => set({
        selectedCountry: country,
        selectedState: null,
        selectedDistrict: null,
        states: [],
        districts: []
      }),

      setSelectedState: (state) => set({
        selectedState: state,
        selectedDistrict: null,
        districts: []
      }),

      setSelectedDistrict: (district) => set({ selectedDistrict: district }),

      // API Actions
      fetchCountries: async (page = 1) => {
        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = {
            page: page.toString(),
            limit: '20',
            search: filters.search,
            isActive: filters.isActive,
            sort: 'name'
          }

          const data = await api.get('/api/locations/countries', params)

          if (data.success) {
            set({
              countries: data.countries,
              countriesPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch countries')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch countries')
        }
      },

      fetchStates: async (countryId, page = 1) => {
        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '50',
            search: filters.search,
            isActive: filters.isActive,
            sort: 'name'
          })

          if (countryId) {
            params.append('countryId', countryId)
          }

          const data = await api.get('/api/locations/states', Object.fromEntries(params))

          if (data.success) {
            set({
              states: data.states,
              statesPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch states')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch states')
        }
      },

      fetchDistricts: async (stateId, countryId, page = 1) => {
        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '100',
            search: filters.search,
            isActive: filters.isActive,
            sort: 'name'
          })

          if (stateId) {
            params.append('stateId', stateId)
          }
          if (countryId) {
            params.append('countryId', countryId)
          }
          if (filters.type) {
            params.append('type', filters.type)
          }

          const data = await api.get('/api/locations/districts', Object.fromEntries(params))

          if (data.success) {
            set({
              districts: data.districts,
              districtsPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch districts')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch districts')
        }
      },

      fetchLocationHierarchy: async (countryId) => {
        set({ isLoading: true, error: null })
        try {
          const data = await api.get(`/api/locations/hierarchy/${countryId}`)

          if (data.success) {
            set({
              selectedCountry: data.country,
              states: data.states,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch location hierarchy')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch location hierarchy')
        }
      },

      // CRUD Actions with API calls and toast notifications
      createCountry: async (data) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.post('/api/locations/countries', data)

          if (result.success) {
            await get().fetchCountries()
            toast.success('Country created successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to create country')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      updateCountry: async (id, data) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.put(`/api/locations/countries/${id}`, data)

          if (result.success) {
            await get().fetchCountries()
            toast.success('Country updated successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to update country')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      deleteCountry: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.delete(`/api/locations/countries/${id}`)

          if (result.success) {
            await get().fetchCountries()
            toast.success('Country deleted successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to delete country')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      createState: async (data) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.post('/api/locations/states', data)

          if (result.success) {
            const countryId = typeof data.country === 'string' ? data.country : data.country?.id
            await get().fetchStates(countryId)
            toast.success('State created successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to create state')
          }
        } catch (error) {
          console.error('State creation error:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      updateState: async (id, data) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.put(`/api/locations/states/${id}`, data)

          if (result.success) {
            await get().fetchStates(get().selectedCountry?.id)
            toast.success('State updated successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to update state')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      deleteState: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.delete(`/api/locations/states/${id}`)

          if (result.success) {
            await get().fetchStates(get().selectedCountry?.id)
            toast.success('State deleted successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to delete state')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      createDistrict: async (data) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.post('/api/locations/districts', data)

          if (result.success) {
            const stateId = typeof data.state === 'string' ? data.state : data.state?.id
            await get().fetchDistricts(stateId, get().selectedCountry?.id)
            toast.success('District created successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to create district')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      updateDistrict: async (id, data) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.put(`/api/locations/districts/${id}`, data)

          if (result.success) {
            await get().fetchDistricts(get().selectedState?.id, get().selectedCountry?.id)
            toast.success('District updated successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to update district')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      deleteDistrict: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.delete(`/api/locations/districts/${id}`)

          if (result.success) {
            await get().fetchDistricts(get().selectedState?.id, get().selectedCountry?.id)
            toast.success('District deleted successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to delete district')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },



      // Utility Actions
      clearError: () => set({ error: null }),

      resetFilters: () => set({
        filters: initialFilters,
        selectedCountry: null,
        selectedState: null,
        selectedDistrict: null
      })
    }),
    {
      name: 'location-store'
    }
  )
)
