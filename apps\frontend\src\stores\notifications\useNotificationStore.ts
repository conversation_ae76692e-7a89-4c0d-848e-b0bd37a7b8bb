import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  category: 'system' | 'course' | 'enrollment' | 'payment' | 'staff' | 'general'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  isRead: boolean
  isArchived: boolean
  actionUrl?: string
  actionLabel?: string
  metadata?: Record<string, any>
  recipientType: 'super_admin' | 'institute_admin' | 'student' | 'staff' | 'all'
  recipientId?: string
  createdAt: string
  readAt?: string
  expiresAt?: string
}

interface NotificationPreferences {
  email: {
    enabled: boolean
    categories: string[]
    frequency: 'immediate' | 'daily' | 'weekly'
  }
  push: {
    enabled: boolean
    categories: string[]
  }
  inApp: {
    enabled: boolean
    categories: string[]
  }
}

interface NotificationFilters {
  category?: string
  type?: string
  priority?: string
  isRead?: boolean
  isArchived?: boolean
}

interface NotificationStats {
  total: number
  unread: number
  byCategory: Record<string, number>
  byType: Record<string, number>
  byPriority: Record<string, number>
}

interface NotificationState {
  // Data
  notifications: Notification[]
  preferences: NotificationPreferences
  stats: NotificationStats
  
  // UI State
  isLoading: boolean
  error: string | null
  filters: NotificationFilters
  showOnlyUnread: boolean

  // Actions
  setFilters: (filters: Partial<NotificationFilters>) => void
  setShowOnlyUnread: (show: boolean) => void

  // API Actions
  fetchNotifications: () => Promise<void>
  fetchNotificationStats: () => Promise<void>
  fetchPreferences: () => Promise<void>
  
  markAsRead: (notificationId: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  archiveNotification: (notificationId: string) => Promise<void>
  deleteNotification: (notificationId: string) => Promise<void>
  
  updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>
  
  // Real-time notifications
  addNotification: (notification: Notification) => void
  removeNotification: (notificationId: string) => void

  // Utility Actions
  clearError: () => void
}

const initialPreferences: NotificationPreferences = {
  email: {
    enabled: true,
    categories: ['system', 'course', 'enrollment', 'payment'],
    frequency: 'immediate'
  },
  push: {
    enabled: true,
    categories: ['system', 'course', 'enrollment']
  },
  inApp: {
    enabled: true,
    categories: ['system', 'course', 'enrollment', 'payment', 'staff', 'general']
  }
}

const initialStats: NotificationStats = {
  total: 0,
  unread: 0,
  byCategory: {},
  byType: {},
  byPriority: {}
}

export const useNotificationStore = create<NotificationState>()(
  devtools(
    (set, get) => ({
      // Initial State
      notifications: [],
      preferences: initialPreferences,
      stats: initialStats,
      isLoading: false,
      error: null,
      filters: {},
      showOnlyUnread: false,

      // UI Actions
      setFilters: (newFilters) => set((state) => ({
        filters: { ...state.filters, ...newFilters }
      })),

      setShowOnlyUnread: (show) => set({ showOnlyUnread: show }),

      // API Actions
      fetchNotifications: async () => {
        set({ isLoading: true, error: null })
        try {
          const { filters, showOnlyUnread } = get()
          const params = new URLSearchParams({
            ...(filters.category && { category: filters.category }),
            ...(filters.type && { type: filters.type }),
            ...(filters.priority && { priority: filters.priority }),
            ...(filters.isRead !== undefined && { isRead: filters.isRead.toString() }),
            ...(filters.isArchived !== undefined && { isArchived: filters.isArchived.toString() }),
            ...(showOnlyUnread && { isRead: 'false' })
          })

          const response = await fetch(`/api/notifications?${params}`, {
            credentials: 'include'
          })
          const data = await response.json()

          if (data.success) {
            set({
              notifications: data.docs,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch notifications')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch notifications')
        }
      },

      fetchNotificationStats: async () => {
        try {
          const response = await fetch('/api/notifications/stats', {
            credentials: 'include'
          })
          const data = await response.json()

          if (data.success) {
            set({ stats: data.stats })
          } else {
            throw new Error(data.error || 'Failed to fetch notification stats')
          }
        } catch (error) {
          console.error('Failed to fetch notification stats:', error)
        }
      },

      fetchPreferences: async () => {
        try {
          const response = await fetch('/api/notifications/preferences', {
            credentials: 'include'
          })
          const data = await response.json()

          if (data.success) {
            set({ preferences: data.preferences })
          } else {
            throw new Error(data.error || 'Failed to fetch preferences')
          }
        } catch (error) {
          console.error('Failed to fetch preferences:', error)
        }
      },

      markAsRead: async (notificationId) => {
        try {
          const response = await fetch(`/api/notifications/${notificationId}/read`, {
            method: 'PATCH',
            credentials: 'include'
          })

          const data = await response.json()

          if (data.success) {
            // Update notification in local state
            set((state) => ({
              notifications: state.notifications.map(notification =>
                notification.id === notificationId
                  ? { ...notification, isRead: true, readAt: new Date().toISOString() }
                  : notification
              )
            }))

            // Update stats
            await get().fetchNotificationStats()
          } else {
            throw new Error(data.error || 'Failed to mark notification as read')
          }
        } catch (error) {
          toast.error('Failed to mark notification as read')
        }
      },

      markAllAsRead: async () => {
        try {
          const response = await fetch('/api/notifications/mark-all-read', {
            method: 'PATCH',
            credentials: 'include'
          })

          const data = await response.json()

          if (data.success) {
            // Update all notifications in local state
            set((state) => ({
              notifications: state.notifications.map(notification => ({
                ...notification,
                isRead: true,
                readAt: new Date().toISOString()
              }))
            }))

            // Update stats
            await get().fetchNotificationStats()
            toast.success('All notifications marked as read')
          } else {
            throw new Error(data.error || 'Failed to mark all notifications as read')
          }
        } catch (error) {
          toast.error('Failed to mark all notifications as read')
        }
      },

      archiveNotification: async (notificationId) => {
        try {
          const response = await fetch(`/api/notifications/${notificationId}/archive`, {
            method: 'PATCH',
            credentials: 'include'
          })

          const data = await response.json()

          if (data.success) {
            // Remove notification from local state or mark as archived
            set((state) => ({
              notifications: state.notifications.map(notification =>
                notification.id === notificationId
                  ? { ...notification, isArchived: true }
                  : notification
              )
            }))

            toast.success('Notification archived')
          } else {
            throw new Error(data.error || 'Failed to archive notification')
          }
        } catch (error) {
          toast.error('Failed to archive notification')
        }
      },

      deleteNotification: async (notificationId) => {
        try {
          const response = await fetch(`/api/notifications/${notificationId}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          const data = await response.json()

          if (data.success) {
            // Remove notification from local state
            set((state) => ({
              notifications: state.notifications.filter(notification => notification.id !== notificationId)
            }))

            // Update stats
            await get().fetchNotificationStats()
            toast.success('Notification deleted')
          } else {
            throw new Error(data.error || 'Failed to delete notification')
          }
        } catch (error) {
          toast.error('Failed to delete notification')
        }
      },

      updatePreferences: async (newPreferences) => {
        try {
          const response = await fetch('/api/notifications/preferences', {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify(newPreferences)
          })

          const data = await response.json()

          if (data.success) {
            set((state) => ({
              preferences: { ...state.preferences, ...newPreferences }
            }))

            toast.success('Notification preferences updated')
          } else {
            throw new Error(data.error || 'Failed to update preferences')
          }
        } catch (error) {
          toast.error('Failed to update notification preferences')
        }
      },

      // Real-time notifications
      addNotification: (notification) => {
        set((state) => ({
          notifications: [notification, ...state.notifications]
        }))

        // Show toast for new notifications
        if (notification.priority === 'urgent') {
          toast.error(notification.title, {
            description: notification.message
          })
        } else if (notification.type === 'success') {
          toast.success(notification.title, {
            description: notification.message
          })
        } else if (notification.type === 'warning') {
          toast.warning(notification.title, {
            description: notification.message
          })
        } else {
          toast.info(notification.title, {
            description: notification.message
          })
        }

        // Update stats
        get().fetchNotificationStats()
      },

      removeNotification: (notificationId) => {
        set((state) => ({
          notifications: state.notifications.filter(notification => notification.id !== notificationId)
        }))
      },

      // Utility Actions
      clearError: () => set({ error: null })
    }),
    {
      name: 'notification-store'
    }
  )
)
