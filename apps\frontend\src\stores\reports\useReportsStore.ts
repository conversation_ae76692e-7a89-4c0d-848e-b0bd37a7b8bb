import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface ReportData {
  id: string
  name: string
  description: string
  type: 'enrollment' | 'revenue' | 'course_performance' | 'student_progress' | 'staff_performance' | 'system_usage'
  category: 'academic' | 'financial' | 'operational' | 'administrative'
  data: any[]
  metadata: {
    totalRecords: number
    dateRange: {
      start: string
      end: string
    }
    filters: Record<string, any>
    generatedAt: string
    generatedBy: any
  }
  format: 'table' | 'chart' | 'summary'
  isScheduled: boolean
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
    recipients: string[]
    nextRun: string
  }
}

interface ReportTemplate {
  id: string
  name: string
  description: string
  type: string
  category: string
  fields: Array<{
    key: string
    label: string
    type: 'string' | 'number' | 'date' | 'boolean'
    required: boolean
  }>
  filters: Array<{
    key: string
    label: string
    type: 'select' | 'date' | 'text' | 'number'
    options?: Array<{ value: string; label: string }>
  }>
  defaultFilters: Record<string, any>
  isActive: boolean
}

interface ReportFilters {
  dateRange: {
    start: string
    end: string
  }
  institute?: string
  branch?: string
  course?: string
  student?: string
  staff?: string
  status?: string
}

interface ReportsState {
  // Data
  reports: ReportData[]
  templates: ReportTemplate[]
  currentReport: ReportData | null
  
  // UI State
  isLoading: boolean
  isGenerating: boolean
  error: string | null
  filters: ReportFilters

  // Actions
  setCurrentReport: (report: ReportData | null) => void
  setFilters: (filters: Partial<ReportFilters>) => void

  // API Actions
  fetchReports: () => Promise<void>
  fetchTemplates: () => Promise<void>
  generateReport: (templateId: string, filters: ReportFilters) => Promise<ReportData>
  exportReport: (reportId: string, format: 'csv' | 'pdf' | 'excel') => Promise<void>
  scheduleReport: (templateId: string, schedule: any) => Promise<void>
  deleteReport: (reportId: string) => Promise<void>

  // Utility Actions
  clearError: () => void
}

const initialFilters: ReportFilters = {
  dateRange: {
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    end: new Date().toISOString().split('T')[0] // today
  }
}

export const useReportsStore = create<ReportsState>()(
  devtools(
    (set, get) => ({
      // Initial State
      reports: [],
      templates: [],
      currentReport: null,
      isLoading: false,
      isGenerating: false,
      error: null,
      filters: initialFilters,

      // UI Actions
      setCurrentReport: (report) => set({ currentReport: report }),
      setFilters: (newFilters) => set((state) => ({
        filters: { ...state.filters, ...newFilters }
      })),

      // API Actions
      fetchReports: async () => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/reports', {
            credentials: 'include'
          })
          const data = await response.json()

          if (data.success) {
            set({
              reports: data.docs,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch reports')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch reports')
        }
      },

      fetchTemplates: async () => {
        try {
          const response = await fetch('/api/reports/templates', {
            credentials: 'include'
          })
          const data = await response.json()

          if (data.success) {
            set({ templates: data.templates })
          } else {
            throw new Error(data.error || 'Failed to fetch report templates')
          }
        } catch (error) {
          console.error('Failed to fetch report templates:', error)
        }
      },

      generateReport: async (templateId, filters) => {
        set({ isGenerating: true, error: null })
        try {
          const response = await fetch('/api/reports/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({
              templateId,
              filters
            })
          })

          const data = await response.json()

          if (data.success) {
            const newReport = data.report
            
            // Add report to local state
            set((state) => ({
              reports: [newReport, ...state.reports],
              currentReport: newReport,
              isGenerating: false
            }))

            toast.success('Report Generated', {
              description: `Report "${newReport.name}" has been generated successfully.`
            })

            return newReport
          } else {
            throw new Error(data.error || 'Failed to generate report')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isGenerating: false })
          toast.error('Failed to generate report', {
            description: errorMessage
          })
          throw error
        }
      },

      exportReport: async (reportId, format) => {
        try {
          const response = await fetch(`/api/reports/${reportId}/export?format=${format}`, {
            credentials: 'include'
          })

          if (response.ok) {
            // Handle file download
            const blob = await response.blob()
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `report-${reportId}.${format}`
            document.body.appendChild(a)
            a.click()
            window.URL.revokeObjectURL(url)
            document.body.removeChild(a)

            toast.success('Report Exported', {
              description: `Report has been exported as ${format.toUpperCase()}.`
            })
          } else {
            throw new Error('Failed to export report')
          }
        } catch (error) {
          toast.error('Failed to export report')
        }
      },

      scheduleReport: async (templateId, schedule) => {
        try {
          const response = await fetch('/api/reports/schedule', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({
              templateId,
              schedule
            })
          })

          const data = await response.json()

          if (data.success) {
            toast.success('Report Scheduled', {
              description: 'Report has been scheduled successfully.'
            })
          } else {
            throw new Error(data.error || 'Failed to schedule report')
          }
        } catch (error) {
          toast.error('Failed to schedule report')
        }
      },

      deleteReport: async (reportId) => {
        try {
          const response = await fetch(`/api/reports/${reportId}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          const data = await response.json()

          if (data.success) {
            // Remove report from local state
            set((state) => ({
              reports: state.reports.filter(report => report.id !== reportId),
              currentReport: state.currentReport?.id === reportId ? null : state.currentReport
            }))

            toast.success('Report Deleted', {
              description: 'Report has been deleted successfully.'
            })
          } else {
            throw new Error(data.error || 'Failed to delete report')
          }
        } catch (error) {
          toast.error('Failed to delete report')
        }
      },

      // Utility Actions
      clearError: () => set({ error: null })
    }),
    {
      name: 'reports-store'
    }
  )
)
