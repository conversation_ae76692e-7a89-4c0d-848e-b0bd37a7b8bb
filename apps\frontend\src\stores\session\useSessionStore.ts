import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface ActiveSession {
  id: string
  deviceType: 'desktop' | 'mobile' | 'tablet'
  deviceName: string
  browser: string
  operatingSystem: string
  ipAddress: string
  location: {
    city: string
    country: string
    region: string
  }
  loginTime: string
  lastActivity: string
  isCurrent: boolean
  isSecure: boolean
}

interface SessionLimits {
  maxDesktopSessions: number
  maxMobileSessions: number
  maxTotalSessions: number
  sessionTimeout: number
  requireSecureConnection: boolean
  allowConcurrentSessions: boolean
}

interface SessionState {
  // Data
  activeSessions: ActiveSession[]
  sessionLimits: SessionLimits | null
  isLoading: boolean
  error: string | null

  // Actions
  fetchActiveSessions: () => Promise<void>
  fetchSessionLimits: () => Promise<void>
  terminateSession: (sessionId: string) => Promise<void>
  terminateAllOtherSessions: () => Promise<void>
  createSession: (sessionData: any) => Promise<void>
  updateSessionActivity: (sessionId: string) => Promise<void>
  getSessionStats: () => {
    total: number
    desktop: number
    mobile: number
    tablet: number
    insecure: number
  }
  isSessionLimitExceeded: () => boolean
  clearError: () => void
}

export const useSessionStore = create<SessionState>()(
  devtools(
    (set, get) => ({
      // Initial state
      activeSessions: [],
      sessionLimits: null,
      isLoading: false,
      error: null,

      // Fetch active sessions
      fetchActiveSessions: async () => {
        set({ isLoading: true })
        try {
          const response = await fetch('/api/auth/sessions', {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch sessions')
          }

          const data = await response.json()
          set({
            activeSessions: data.sessions || [],
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: (error as Error).message,
          })
          toast.error('Failed to fetch active sessions')
        }
      },

      // Fetch session limits
      fetchSessionLimits: async () => {
        try {
          const response = await fetch('/api/auth/session-limits', {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch session limits')
          }

          const data = await response.json()
          set({
            sessionLimits: data.limits,
            error: null,
          })
        } catch (error) {
          set({ error: (error as Error).message })
          console.error('Failed to fetch session limits:', error)
        }
      },

      // Terminate specific session
      terminateSession: async (sessionId) => {
        try {
          const response = await fetch(`/api/auth/sessions/${sessionId}`, {
            method: 'DELETE',
            credentials: 'include',
          })

          if (!response.ok) {
            throw new Error('Failed to terminate session')
          }

          // Remove session from local state
          const currentSessions = get().activeSessions
          const updatedSessions = currentSessions.filter(session => session.id !== sessionId)
          
          set({ activeSessions: updatedSessions })
          
          toast.success('Session Terminated', {
            description: 'The session has been successfully terminated.'
          })
        } catch (error) {
          set({ error: (error as Error).message })
          toast.error('Termination Failed', {
            description: 'Unable to terminate the session.'
          })
          throw error
        }
      },

      // Terminate all other sessions
      terminateAllOtherSessions: async () => {
        try {
          const response = await fetch('/api/auth/sessions/terminate-others', {
            method: 'POST',
            credentials: 'include',
          })

          if (!response.ok) {
            throw new Error('Failed to terminate other sessions')
          }

          const data = await response.json()
          
          // Keep only current session
          const currentSessions = get().activeSessions
          const currentSession = currentSessions.find(session => session.isCurrent)
          
          set({ 
            activeSessions: currentSession ? [currentSession] : []
          })
          
          toast.success('Sessions Terminated', {
            description: 'All other sessions have been terminated.'
          })
        } catch (error) {
          set({ error: (error as Error).message })
          toast.error('Termination Failed', {
            description: 'Unable to terminate other sessions.'
          })
          throw error
        }
      },

      // Create new session
      createSession: async (sessionData) => {
        try {
          const response = await fetch('/api/auth/sessions/create', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify(sessionData),
          })

          if (!response.ok) {
            throw new Error('Failed to create session')
          }

          const data = await response.json()
          
          // Refresh sessions
          await get().fetchActiveSessions()
          
        } catch (error) {
          set({ error: (error as Error).message })
          console.error('Failed to create session:', error)
          throw error
        }
      },

      // Update session activity
      updateSessionActivity: async (sessionId) => {
        try {
          const response = await fetch(`/api/auth/sessions/${sessionId}/activity`, {
            method: 'PUT',
            credentials: 'include',
          })

          if (!response.ok) {
            throw new Error('Failed to update session activity')
          }

          // Update local session activity time
          const currentSessions = get().activeSessions
          const updatedSessions = currentSessions.map(session => 
            session.id === sessionId 
              ? { ...session, lastActivity: new Date().toISOString() }
              : session
          )
          
          set({ activeSessions: updatedSessions })
          
        } catch (error) {
          console.error('Failed to update session activity:', error)
        }
      },

      // Get session statistics
      getSessionStats: () => {
        const sessions = get().activeSessions
        return {
          total: sessions.length,
          desktop: sessions.filter(s => s.deviceType === 'desktop').length,
          mobile: sessions.filter(s => s.deviceType === 'mobile').length,
          tablet: sessions.filter(s => s.deviceType === 'tablet').length,
          insecure: sessions.filter(s => !s.isSecure).length,
        }
      },

      // Check if session limits are exceeded
      isSessionLimitExceeded: () => {
        const { activeSessions, sessionLimits } = get()
        if (!sessionLimits) return false
        
        const stats = get().getSessionStats()
        
        return (
          stats.desktop > sessionLimits.maxDesktopSessions ||
          stats.mobile > sessionLimits.maxMobileSessions ||
          stats.total > sessionLimits.maxTotalSessions
        )
      },

      // Clear error
      clearError: () => {
        set({ error: null })
      },
    }),
    {
      name: 'session-store',
    }
  )
)

// Helper hooks for specific session operations
export const useCurrentSession = () => {
  const activeSessions = useSessionStore(state => state.activeSessions)
  return activeSessions.find(session => session.isCurrent) || null
}

export const useSessionStats = () => {
  const getSessionStats = useSessionStore(state => state.getSessionStats)
  return getSessionStats()
}

export const useSessionSecurity = () => {
  const { activeSessions, sessionLimits, isSessionLimitExceeded } = useSessionStore()
  
  const insecureSessions = activeSessions.filter(session => !session.isSecure)
  const hasInsecureSessions = insecureSessions.length > 0
  const limitsExceeded = isSessionLimitExceeded()
  
  return {
    hasInsecureSessions,
    insecureSessions,
    limitsExceeded,
    sessionLimits,
    securityScore: hasInsecureSessions || limitsExceeded ? 'warning' : 'good',
  }
}

// Auto-refresh sessions periodically
export const useSessionAutoRefresh = (intervalMs: number = 60000) => {
  const fetchActiveSessions = useSessionStore(state => state.fetchActiveSessions)
  
  // This would typically be used in a useEffect hook in components
  return {
    startAutoRefresh: () => {
      const interval = setInterval(fetchActiveSessions, intervalMs)
      return () => clearInterval(interval)
    }
  }
}
