import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import {
  settingsApi,
  type Setting,
  type SettingCreationData,
  type SettingUpdateData,
  type SettingsFilters,
  type SettingCategory
} from '@/lib/api/settings'

interface SettingsState {
  // State
  settings: Setting[]
  settingsByCategory: Record<SettingCategory, Setting[]>
  currentSetting: Setting | null
  filters: SettingsFilters

  // Loading states
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean
  isBulkUpdating: boolean

  // Error state
  error: string | null

  // Actions
  fetchSettings: (filters?: SettingsFilters) => Promise<void>
  fetchSettingByKey: (key: string) => Promise<Setting | null>
  fetchSettingsByCategory: (category: SettingCategory) => Promise<void>
  createSetting: (data: SettingCreationData) => Promise<void>
  updateSetting: (key: string, data: SettingUpdateData) => Promise<void>
  deleteSetting: (key: string) => Promise<void>
  bulkUpdateSettings: (settings: SettingCreationData[]) => Promise<void>

  // Utility actions
  setFilters: (filters: Partial<SettingsFilters>) => void
  clearFilters: () => void
  clearError: () => void
  clearCurrentSetting: () => void

  // Getters
  getSettingValue: (key: string) => string | null
  getSettingsByCategory: (category: SettingCategory) => Setting[]
  getPublicSettings: () => Setting[]
}

export const useSettingsStore = create<SettingsState>()(
  devtools(
    (set, get) => ({
      // Initial state
      settings: [],
      settingsByCategory: {},
      currentSetting: null,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isBulkUpdating: false,
      error: null,
      filters: {
        category: '',
        scope: '',
        search: '',
      },

      // Fetch settings
      fetchSettings: async (filters = {}) => {
        set({ isLoading: true })
        try {
          const currentFilters = { ...get().filters, ...filters }
          const response = await settingsApi.getSettings(currentFilters)

          // Group settings by category
          const settingsByCategory = response.settings.reduce((acc: Record<string, Setting[]>, setting: Setting) => {
            if (!acc[setting.category]) {
              acc[setting.category] = []
            }
            acc[setting.category].push(setting)
            return acc
          }, {})

          set({
            settings: response.settings,
            settingsByCategory,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: (error as Error).message,
          })
          toast.error('Failed to fetch settings')
        }
      },

      // Fetch setting by key
      fetchSettingByKey: async (key) => {
        try {
          const setting = await settingsApi.getSettingByKey(key)

          // Update current setting
          set({ currentSetting: setting })

          return setting
        } catch (error) {
          set({ error: (error as Error).message })
          toast.error('Failed to fetch setting')
          return null
        }
      },

      // Fetch settings by category
      fetchSettingsByCategory: async (category) => {
        set({ isLoading: true })
        try {
          const response = await settingsApi.getSettingsByCategory(category)

          // Update category-specific settings
          set(state => ({
            settingsByCategory: {
              ...state.settingsByCategory,
              [category]: response.settings
            },
            isLoading: false,
            error: null
          }))
        } catch (error) {
          set({
            isLoading: false,
            error: (error as Error).message,
          })
          toast.error('Failed to fetch settings by category')
        }
      },

      // Create setting
      createSetting: async (settingData) => {
        try {
          await settingsApi.createSetting(settingData)

          // Refresh settings
          await get().fetchSettings()

          toast.success('Setting Created', {
            description: 'Setting has been created successfully.'
          })
        } catch (error) {
          set({ error: (error as Error).message })
          toast.error('Failed to create setting')
          throw error
        }
      },

      // Update setting
      updateSetting: async (id, updateData) => {
        try {
          const response = await fetch('/api/settings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({ ...updateData, id }),
          })

          if (!response.ok) {
            throw new Error('Failed to update setting')
          }

          // Refresh settings
          await get().fetchSettings()
          
          toast.success('Setting Updated', {
            description: 'Setting has been updated successfully.'
          })
        } catch (error) {
          set({ error: (error as Error).message })
          toast.error('Failed to update setting')
          throw error
        }
      },

      // Delete setting
      deleteSetting: async (id) => {
        try {
          const response = await fetch(`/api/settings/${id}`, {
            method: 'DELETE',
            credentials: 'include',
          })

          if (!response.ok) {
            throw new Error('Failed to delete setting')
          }

          // Remove from local state
          const currentSettings = get().settings
          const updatedSettings = currentSettings.filter(setting => setting.id !== id)
          
          // Regroup by category
          const settingsByCategory = updatedSettings.reduce((acc: Record<string, Setting[]>, setting: Setting) => {
            if (!acc[setting.category]) {
              acc[setting.category] = []
            }
            acc[setting.category].push(setting)
            return acc
          }, {})

          set({
            settings: updatedSettings,
            settingsByCategory,
          })
          
          toast.success('Setting Deleted', {
            description: 'Setting has been deleted successfully.'
          })
        } catch (error) {
          set({ error: (error as Error).message })
          toast.error('Failed to delete setting')
          throw error
        }
      },

      // Bulk update settings
      bulkUpdateSettings: async (settings) => {
        set({ isBulkUpdating: true })
        try {
          const data = await settingsApi.bulkUpdateSettings(settings)

          // Refresh settings
          await get().fetchSettings()

          toast.success('Settings Updated', {
            description: `${data.success || settings.length} settings updated successfully.`
          })
        } catch (error) {
          set({ error: (error as Error).message })
          toast.error('Failed to update settings')
          throw error
        } finally {
          set({ isBulkUpdating: false })
        }
      },

      // Utility actions
      setFilters: (filters: Partial<SettingsFilters>) => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }))
      },

      clearFilters: () => {
        set({ filters: {} })
      },

      clearError: () => {
        set({ error: null })
      },

      clearCurrentSetting: () => {
        set({ currentSetting: null })
      },

      // Getters
      getSettingValue: (key: string) => {
        const setting = get().settings.find(s => s.key === key)
        return setting ? setting.value : null
      },

      getSettingsByCategory: (category: SettingCategory) => {
        return get().settingsByCategory[category] || []
      },

      getPublicSettings: () => {
        return get().settings.filter(setting => setting.is_public)
      },


    }),
    {
      name: 'settings-store',
    }
  )
)

// Helper hooks for specific setting categories
export const usePlatformSettings = () => {
  const { settingsByCategory, fetchSettings } = useSettingsStore()
  
  return {
    platformSettings: settingsByCategory.platform || [],
    fetchPlatformSettings: () => fetchSettings({ category: 'platform' }),
  }
}

export const useSecuritySettings = () => {
  const { settingsByCategory, fetchSettings } = useSettingsStore()
  
  return {
    securitySettings: settingsByCategory.security || [],
    fetchSecuritySettings: () => fetchSettings({ category: 'security' }),
  }
}

// Helper function to get setting values
export const useSettingValue = (key: string): string | null => {
  const getSettingValue = useSettingsStore(state => state.getSettingValue)
  return getSettingValue(key)
}

// Helper function to check if a setting exists
export const useHasSetting = (key: string): boolean => {
  const settings = useSettingsStore(state => state.settings)
  return settings.some(setting => setting.key === key)
}
