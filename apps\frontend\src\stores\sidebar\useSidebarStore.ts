import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// Types
export type UserType = 'super_admin' | 'institute_admin' | 'student'

export interface NavigationItem {
  id: string
  label: string
  icon: string
  href: string
  badge?: number
  isActive?: boolean
  children?: NavigationItem[]
  permissions?: string[]
  description?: string
  isModal?: boolean
}

export interface SidebarSection {
  id: string
  title: string
  items: NavigationItem[]
  isCollapsed?: boolean
}

export interface BreadcrumbItem {
  label: string
  href?: string
  isActive?: boolean
}

export interface SidebarState {
  // Layout State
  isCollapsed: boolean
  isMobileOpen: boolean
  userType: UserType
  
  // Navigation State
  activeItem: string
  activeSection: string
  navigationItems: NavigationItem[]
  sections: SidebarSection[]
  breadcrumbs: BreadcrumbItem[]
  
  // Search State
  searchQuery: string
  searchResults: NavigationItem[]
  isSearching: boolean
  
  // Favorites & Recent
  favoriteItems: string[]
  recentItems: string[]
  
  // Notifications
  notifications: Array<{
    id: string
    title: string
    message: string
    type: 'info' | 'warning' | 'error' | 'success'
    timestamp: string
    isRead: boolean
  }>
  unreadCount: number
}

interface SidebarStore extends SidebarState {
  // Layout Actions
  toggleSidebar: () => void
  setSidebarCollapsed: (collapsed: boolean) => void
  toggleMobileSidebar: () => void
  setMobileSidebarOpen: (open: boolean) => void
  setUserType: (userType: UserType) => void
  
  // Navigation Actions
  setActiveItem: (itemId: string) => void
  setActiveSection: (sectionId: string) => void
  setNavigationItems: (items: NavigationItem[]) => void
  setSections: (sections: SidebarSection[]) => void
  setBreadcrumbs: (breadcrumbs: BreadcrumbItem[]) => void
  updateItemBadge: (itemId: string, badge: number) => void
  
  // Search Actions
  setSearchQuery: (query: string) => void
  performSearch: (query: string) => void
  clearSearch: () => void
  
  // Favorites & Recent Actions
  addToFavorites: (itemId: string) => void
  removeFromFavorites: (itemId: string) => void
  addToRecent: (itemId: string) => void
  clearRecent: () => void
  
  // Notification Actions
  addNotification: (notification: Omit<SidebarState['notifications'][0], 'id' | 'timestamp' | 'isRead'>) => void
  markNotificationAsRead: (notificationId: string) => void
  markAllNotificationsAsRead: () => void
  removeNotification: (notificationId: string) => void
  clearNotifications: () => void
  
  // Utility Actions
  initializeNavigation: (userType: UserType) => void
  filterNavigationByPermissions: (userPermissions?: string[]) => void
  resetState: () => void
}

// Helper function to filter navigation items based on user permissions
const filterNavigationByRole = (items: NavigationItem[], userType: UserType, userPermissions?: string[]): NavigationItem[] => {
  return items.map(item => {
    // For super admin, return all items without filtering
    if (userType === 'super_admin') {
      return item
    }

    // Filter children if they exist
    let filteredChildren = item.children
    if (item.children && item.children.length > 0) {
      filteredChildren = item.children.filter(child => {
        // If child has permissions, check if user role is included
        if (child.permissions && child.permissions.length > 0) {
          return child.permissions.includes(userType) ||
                 (userPermissions && child.permissions.some(perm => userPermissions.includes(perm)))
        }
        // If no permissions specified, allow access
        return true
      })
    }

    // Return item with filtered children
    return {
      ...item,
      children: filteredChildren
    }
  }).filter(item => {
    // Filter parent items based on permissions
    if (item.permissions && item.permissions.length > 0) {
      return item.permissions.includes(userType) ||
             (userPermissions && item.permissions.some(perm => userPermissions.includes(perm)))
    }
    // If no permissions specified, allow access
    return true
  })
}

// Default navigation items for each user type
const getDefaultNavigation = (userType: UserType): NavigationItem[] => {
  switch (userType) {
    case 'super_admin':
      return [
        {
          id: 'dashboard',
          label: 'Dashboard',
          icon: 'LayoutDashboard',
          href: '/super-admin',
          description: 'Overview and analytics'
        },
        {
          id: 'institutes',
          label: 'Institute Management',
          icon: 'Building2',
          href: '/super-admin/institutes',
          description: 'Manage institutes and verification'
        },
        {
          id: 'users',
          label: 'User Management',
          icon: 'Users',
          href: '/super-admin/users',
          description: 'Manage all platform users'
        },
        {
          id: 'billing',
          label: 'Billing & Finance',
          icon: 'CreditCard',
          href: '/super-admin/billing',
          description: 'Financial management and billing'
        },
        {
          id: 'platform-blog',
          label: 'Platform Blog',
          icon: 'FileText',
          href: '/super-admin/platform-blog',
          description: 'Manage platform-wide blog posts and announcements'
        },
        {
          id: 'themes',
          label: 'Theme Management',
          icon: 'Palette',
          href: '/super-admin/themes',
          description: 'Platform and institute themes'
        },
        {
          id: 'analytics',
          label: 'Analytics & Reports',
          icon: 'BarChart3',
          href: '/super-admin/analytics',
          description: 'Platform analytics and reports'
        },
        {
          id: 'locations',
          label: 'Location Management',
          icon: 'MapPin',
          href: '/super-admin/locations',
          description: 'Manage countries, states, and districts'
        },
       
        {
          id: 'tax-management',
          label: 'Tax Management',
          icon: 'Calculator',
          href: '/super-admin/tax-management',
          description: 'Manage tax components, groups, and rules'
        },
        {
          id: 'role-permissions',
          label: 'Roles & Permissions',
          icon: 'Shield',
          href: '/super-admin/role-permissions',
          description: 'Manage user roles and permissions'
        },
        {
          id: 'gateway-management',
          label: 'Gateway Management',
          icon: 'CreditCard',
          href: '/super-admin/gateway-management',
          description: 'Manage payment gateway providers and configurations'          
        },
        {
          id: 'settings',
          label: 'System Settings',
          icon: 'Settings',
          href: '/super-admin/settings',
          description: 'System configuration and settings',          
        }
      ]
    
    case 'institute_admin':
      return [
        {
          id: 'dashboard',
          label: 'Dashboard',
          icon: 'LayoutDashboard',
          href: '/admin',
          description: 'Institute overview and analytics'
        },
        {
          id: 'courses',
          label: 'Course Management',
          icon: 'BookOpen',
          href: '/admin/courses',
          description: 'Manage courses and curriculum'
        },
        {
          id: 'students',
          label: 'Student Management',
          icon: 'GraduationCap',
          href: '/admin/students',
          description: 'Manage student enrollments and progress'
        },
        {
          id: 'staff',
          label: 'Staff Management',
          icon: 'Users',
          href: '/admin/staff',
          description: 'Manage staff members',
          permissions: ['institute_admin', 'branch_manager']
        },
        {
          id: 'branches',
          label: 'Branch Management',
          icon: 'MapPin',
          href: '/admin/branches',
          description: 'Manage institute branches and locations'
        },
        {
          id: 'billing',
          label: 'Billing & Payments',
          icon: 'CreditCard',
          href: '/admin/billing',
          description: 'Student billing and payment management'
        },
        {
          id: 'analytics',
          label: 'Analytics & Reports',
          icon: 'BarChart3',
          href: '/admin/analytics',
          description: 'Institute analytics and performance reports'
        },
        {
          id: 'blog',
          label: 'Blog Management',
          icon: 'PenTool',
          href: '/admin/blog',
          description: 'Manage institute blog and content'         
        },       
        {
          id: 'settings',
          label: 'Institute Settings',
          icon: 'Settings',
          href: '/admin/settings',
          description: 'Institute configuration and preferences'          
        }
      ]
    
    case 'student':
      return [
        {
          id: 'dashboard',
          label: 'Dashboard',
          icon: 'LayoutDashboard',
          href: '/student',
          description: 'Your learning dashboard'
        },
        {
          id: 'my-courses',
          label: 'My Courses',
          icon: 'BookOpen',
          href: '/student/courses',
          description: 'Your enrolled courses and progress'
        },
        {
          id: 'marketplace',
          label: 'Course Marketplace',
          icon: 'ShoppingCart',
          href: '/student/marketplace',
          description: 'Browse and purchase new courses'
        },
        {
          id: 'assignments',
          label: 'Assignments & Exams',
          icon: 'FileText',
          href: '/student/assignments',
          description: 'View and submit assignments'
        },
        {
          id: 'live-classes',
          label: 'Live Classes',
          icon: 'Video',
          href: '/student/live-classes',
          description: 'Join live classes and webinars'
        },
        {
          id: 'progress',
          label: 'Progress & Analytics',
          icon: 'TrendingUp',
          href: '/student/progress',
          description: 'Track your learning progress'
        },
        {
          id: 'community',
          label: 'Community',
          icon: 'MessageCircle',
          href: '/student/community',
          description: 'Connect with peers and instructors'
        },
        {
          id: 'payments',
          label: 'Payments & Billing',
          icon: 'CreditCard',
          href: '/student/payments',
          description: 'Manage payments and billing history'
        },
        {
          id: 'account',
          label: 'Account Settings',
          icon: 'User',
          href: '/student/account',
          description: 'Manage your account and preferences'
        },
        {
          id: 'support',
          label: 'Support & Help',
          icon: 'HelpCircle',
          href: '/student/support',
          description: 'Get help and contact support'
        }
      ]
    
    default:
      return []
  }
}

const initialState: SidebarState = {
  isCollapsed: false,
  isMobileOpen: false,
  userType: 'super_admin',
  activeItem: '',
  activeSection: '',
  navigationItems: [],
  sections: [],
  breadcrumbs: [],
  searchQuery: '',
  searchResults: [],
  isSearching: false,
  favoriteItems: [],
  recentItems: [],
  notifications: [],
  unreadCount: 0
}

export const useSidebarStore = create<SidebarStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Layout Actions
      toggleSidebar: () => set((state) => ({ isCollapsed: !state.isCollapsed })),
      setSidebarCollapsed: (collapsed) => set({ isCollapsed: collapsed }),
      toggleMobileSidebar: () => set((state) => ({ isMobileOpen: !state.isMobileOpen })),
      setMobileSidebarOpen: (open) => set({ isMobileOpen: open }),
      setUserType: (userType) => {
        set({ userType })
        get().initializeNavigation(userType)
      },

      // Navigation Actions
      setActiveItem: (itemId) => {
        set({ activeItem: itemId })
        get().addToRecent(itemId)
      },
      setActiveSection: (sectionId) => set({ activeSection: sectionId }),
      setNavigationItems: (items) => set({ navigationItems: items }),
      setSections: (sections) => set({ sections }),
      setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),
      updateItemBadge: (itemId, badge) => {
        set((state) => ({
          navigationItems: state.navigationItems.map(item =>
            item.id === itemId ? { ...item, badge } : item
          )
        }))
      },

      // Search Actions
      setSearchQuery: (query) => set({ searchQuery: query }),
      performSearch: (query) => {
        set({ isSearching: true, searchQuery: query })
        
        const { navigationItems } = get()
        const results = navigationItems.filter(item =>
          item.label.toLowerCase().includes(query.toLowerCase()) ||
          item.description?.toLowerCase().includes(query.toLowerCase())
        )
        
        set({ searchResults: results, isSearching: false })
      },
      clearSearch: () => set({ searchQuery: '', searchResults: [], isSearching: false }),

      // Favorites & Recent Actions
      addToFavorites: (itemId) => {
        set((state) => ({
          favoriteItems: state.favoriteItems.includes(itemId)
            ? state.favoriteItems
            : [...state.favoriteItems, itemId]
        }))
      },
      removeFromFavorites: (itemId) => {
        set((state) => ({
          favoriteItems: state.favoriteItems.filter(id => id !== itemId)
        }))
      },
      addToRecent: (itemId) => {
        set((state) => {
          const filtered = state.recentItems.filter(id => id !== itemId)
          return {
            recentItems: [itemId, ...filtered].slice(0, 10) // Keep only last 10
          }
        })
      },
      clearRecent: () => set({ recentItems: [] }),

      // Notification Actions
      addNotification: (notification) => {
        const newNotification = {
          ...notification,
          id: Date.now().toString(),
          timestamp: new Date().toISOString(),
          isRead: false
        }
        
        set((state) => ({
          notifications: [newNotification, ...state.notifications],
          unreadCount: state.unreadCount + 1
        }))
      },
      markNotificationAsRead: (notificationId) => {
        set((state) => ({
          notifications: state.notifications.map(notification =>
            notification.id === notificationId
              ? { ...notification, isRead: true }
              : notification
          ),
          unreadCount: Math.max(0, state.unreadCount - 1)
        }))
      },
      markAllNotificationsAsRead: () => {
        set((state) => ({
          notifications: state.notifications.map(notification => ({
            ...notification,
            isRead: true
          })),
          unreadCount: 0
        }))
      },
      removeNotification: (notificationId) => {
        set((state) => {
          const notification = state.notifications.find(n => n.id === notificationId)
          return {
            notifications: state.notifications.filter(n => n.id !== notificationId),
            unreadCount: notification && !notification.isRead 
              ? Math.max(0, state.unreadCount - 1)
              : state.unreadCount
          }
        })
      },
      clearNotifications: () => set({ notifications: [], unreadCount: 0 }),

      // Utility Actions
      initializeNavigation: (userType) => {
        const navigationItems = getDefaultNavigation(userType)
        set({ navigationItems, userType })
      },
      filterNavigationByPermissions: (userPermissions) => {
        const { navigationItems, userType } = get()
        const filteredItems = filterNavigationByRole(navigationItems, userType, userPermissions)
        set({ navigationItems: filteredItems })
      },
      resetState: () => set(initialState)
    }),
    {
      name: 'sidebar-store',
      partialize: (state) => ({
        isCollapsed: state.isCollapsed,
        userType: state.userType,
        favoriteItems: state.favoriteItems,
        recentItems: state.recentItems,
        notifications: state.notifications,
        unreadCount: state.unreadCount
      })
    }
  )
)
