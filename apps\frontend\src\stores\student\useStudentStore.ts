import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Course functionality has been removed from the system

interface StudentDashboardStats {
  totalStudyTime: number // in minutes
  streakDays: number
  lastActivity: string
}

interface StudentState {
  // Dashboard Data
  dashboardStats: StudentDashboardStats

  // UI State
  isLoading: boolean
  error: string | null

  // API Actions
  fetchDashboardStats: () => Promise<void>

  // Utility Actions
  clearError: () => void
}

const initialStats: StudentDashboardStats = {
  totalStudyTime: 0,
  streakDays: 0,
  lastActivity: ''
}

export const useStudentStore = create<StudentState>()(
  devtools(
    (set) => ({
      // Initial State
      dashboardStats: initialStats,
      isLoading: false,
      error: null,

      // API Actions

      fetchDashboardStats: async () => {
        try {
          const response = await fetch('/api/student/dashboard-stats', {
            credentials: 'include'
          })
          const data = await response.json()

          if (data.success) {
            set({ dashboardStats: data.stats })
          } else {
            throw new Error(data.error || 'Failed to fetch dashboard stats')
          }
        } catch (error) {
          console.error('Failed to fetch dashboard stats:', error)
        }
      },



      // Utility Actions
      clearError: () => set({ error: null })
    }),
    {
      name: 'student-store'
    }
  )
)
