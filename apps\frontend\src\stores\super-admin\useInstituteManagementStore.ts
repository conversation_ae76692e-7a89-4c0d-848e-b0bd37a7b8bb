import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import {
  superAdminInstitutesApi,
  type Institute,
  type InstituteFormData,
  type InstituteFilters,
  type InstituteStatistics
} from '@/lib/super-admin'

// Re-export types for convenience
export type { Institute, InstituteFormData, InstituteFilters, InstituteStatistics }

interface Pagination {
  page: number
  limit: number
  totalDocs: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface Filters {
  search: string
  isActive?: boolean
  domainVerified?: boolean
}

// Use the InstituteStatistics type from the API library
type Statistics = InstituteStatistics



interface InstituteManagementStore {
  // State
  institutes: Institute[]
  selectedInstitute: Institute | null
  isLoading: boolean
  error: string | null
  pagination: Pagination
  filters: Filters
  statistics: Statistics | null



  // UI State
  showCreateForm: boolean
  showEditForm: boolean
  viewMode: 'list' | 'cards'

  // Actions
  setSelectedInstitute: (institute: Institute | null) => void
  setFilters: (filters: Partial<Filters>) => void
  setViewMode: (mode: 'list' | 'cards') => void
  setShowCreateForm: (show: boolean) => void
  setShowEditForm: (show: boolean) => void
  clearError: () => void

  // Data Actions
  fetchInstitutes: (page?: number, filters?: Partial<Filters>) => Promise<void>
  fetchStatistics: () => Promise<void>
  createInstitute: (data: InstituteFormData) => Promise<boolean>
  updateInstitute: (id: string, data: Partial<Institute>) => Promise<boolean>
  deleteInstitute: (id: string) => Promise<boolean>
  verifyDomain: (id: string) => Promise<boolean>


}

export const useInstituteManagementStore = create<InstituteManagementStore>()(
  devtools(
    (set, get) => ({
      // Initial State
      institutes: [],
      selectedInstitute: null,
      isLoading: false,
      error: null,
      pagination: {
        page: 1,
        limit: 20,
        totalDocs: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false
      },
      filters: {
        search: '',
        isActive: true,
        domainVerified: undefined
      },
      statistics: null,
      showCreateForm: false,
      showEditForm: false,
      viewMode: 'list',

      // UI Actions
      setSelectedInstitute: (institute) => set({ selectedInstitute: institute }),
      setFilters: (newFilters) => set(state => ({
        filters: { ...state.filters, ...newFilters }
      })),
      setViewMode: (mode) => set({ viewMode: mode }),
      setShowCreateForm: (show) => set({ showCreateForm: show }),
      setShowEditForm: (show) => set({ showEditForm: show }),
      clearError: () => set({ error: null }),

      // Fetch Institutes
      fetchInstitutes: async (page = 1, filters) => {
        set({ isLoading: true, error: null })
        try {
          const currentFilters = filters || get().filters
          const response = await superAdminInstitutesApi.getAll(page, currentFilters)

          if (response.success) {
            set({
              institutes: response.docs,
              pagination: {
                page: response.page,
                limit: response.limit,
                totalDocs: response.totalDocs,
                totalPages: response.totalPages,
                hasNextPage: response.hasNextPage,
                hasPrevPage: response.hasPrevPage,
              },
              isLoading: false,
            })
          } else {
            throw new Error('Failed to fetch institutes')
          }
        } catch (error: any) {
          console.error('Error fetching institutes:', error)
          set({
            error: error.message || 'Failed to fetch institutes',
            isLoading: false
          })
          toast.error('Failed to fetch institutes')
        }
      },

      // Fetch Statistics
      fetchStatistics: async () => {
        try {
          const response = await superAdminInstitutesApi.getStatistics()

          if (response.success) {
            set({ statistics: response.data })
          }
        } catch (error: any) {
          console.error('Error fetching statistics:', error)
        }
      },

      // Create Institute
      createInstitute: async (instituteData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await superAdminInstitutesApi.create(instituteData)

          if (response.success) {
            await get().fetchInstitutes()
            await get().fetchStatistics()
            set({ showCreateForm: false })
            toast.success(response.message || 'Institute created successfully')
            return true
          } else {
            throw new Error('Failed to create institute')
          }
        } catch (error: any) {
          console.error('Error creating institute:', error)
          set({
            error: error.message || 'Failed to create institute',
            isLoading: false
          })
          toast.error('Failed to create institute')
          return false
        }
      },

      // Update Institute
      updateInstitute: async (id, updateData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await superAdminInstitutesApi.update(id, updateData)

          if (response.success) {
            await get().fetchInstitutes()
            await get().fetchStatistics()
            set({ showEditForm: false, selectedInstitute: null })
            toast.success(response.message || 'Institute updated successfully')
            return true
          } else {
            throw new Error('Failed to update institute')
          }
        } catch (error: any) {
          console.error('Error updating institute:', error)
          set({
            error: error.message || 'Failed to update institute',
            isLoading: false
          })
          toast.error('Failed to update institute')
          return false
        }
      },

      // Delete Institute
      deleteInstitute: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await superAdminInstitutesApi.delete(id)

          if (response.success) {
            await get().fetchInstitutes()
            await get().fetchStatistics()
            toast.success(response.message || 'Institute deleted successfully')
            return true
          } else {
            throw new Error('Failed to delete institute')
          }
        } catch (error: any) {
          console.error('Error deleting institute:', error)
          set({
            error: error.message || 'Failed to delete institute',
            isLoading: false
          })
          toast.error('Failed to delete institute')
          return false
        }
      },

      // Verify Domain
      verifyDomain: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await superAdminInstitutesApi.verifyDomain(id)

          if (response.success) {
            await get().fetchInstitutes()
            toast.success(response.message || 'Domain verified successfully')
            return true
          } else {
            throw new Error('Failed to verify domain')
          }
        } catch (error: any) {
          console.error('Error verifying domain:', error)
          set({
            error: error.message || 'Failed to verify domain',
            isLoading: false
          })
          toast.error('Failed to verify domain')
          return false
        }
      },
    }),
    {
      name: 'institute-management-store',
    }
  )
)
