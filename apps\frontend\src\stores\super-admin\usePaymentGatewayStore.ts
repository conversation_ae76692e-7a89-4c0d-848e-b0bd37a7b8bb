import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

export interface PaymentGateway {
  id: string
  name: string
  description?: string
  supportedCurrencies: string[] // Simple array: ["INR", "USD", "EUR"]
  supportedMethods?: string[] // Simple array: ["credit_card", "debit_card", "upi"]
  supportedCountries?: string[] // Simple array: ["IN", "US", "GB"]
  requiredConfigFields?: Record<string, any>
  optionalConfigFields?: Record<string, any>
  documentationUrl?: string
  apiVersion?: string
  webhookSupport?: boolean
  logoUrl?: string
  isActive: boolean
  isFeatured: boolean
  createdBy?: string
  createdAt: string
  updatedAt: string
}

interface PaymentGatewayState {
  gateways: PaymentGateway[]
  loading: boolean
  error: string | null

  // Actions
  fetchGateways: () => Promise<void>
  createGateway: (gatewayData: any) => Promise<void> // Accept any format for API compatibility
  updateGateway: (id: string, gatewayData: any) => Promise<void> // Accept any format for API compatibility
  deleteGateway: (id: string) => Promise<void>
  toggleGatewayStatus: (id: string, isActive: boolean) => Promise<void>
}

export const usePaymentGatewayStore = create<PaymentGatewayState>()(
  devtools(
    (set, get) => ({
      gateways: [],
      loading: false,
      error: null,

      fetchGateways: async () => {
        set({ loading: true, error: null })
        try {
          const response = await api.get('/api/super-admin/payment-gateways')
          set({
            gateways: response.gateways,
            loading: false
          })
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to fetch gateways',
            loading: false
          })
          toast.error('Failed to fetch payment gateways')
        }
      },

      createGateway: async (gatewayData) => {
        set({ loading: true, error: null })
        try {
          const response = await api.post('/api/super-admin/payment-gateways', gatewayData)

          set(state => ({
            gateways: [...state.gateways, response.gateway],
            loading: false
          }))

          toast.success('Payment gateway created successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to create gateway',
            loading: false
          })
          toast.error('Failed to create payment gateway')
          throw error
        }
      },

      updateGateway: async (id, gatewayData) => {
        set({ loading: true, error: null })
        try {
          const response = await api.patch(`/api/super-admin/payment-gateways/${id}`, gatewayData)

          set(state => ({
            gateways: state.gateways.map(gateway =>
              gateway.id === id ? (response.gateway || response.data?.gateway) : gateway
            ),
            loading: false
          }))

          toast.success('Payment gateway updated successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to update gateway',
            loading: false
          })
          toast.error('Failed to update payment gateway')
          throw error
        }
      },

      deleteGateway: async (id) => {
        set({ loading: true, error: null })
        try {
          await api.delete(`/api/super-admin/payment-gateways/${id}`)

          set(state => ({
            gateways: state.gateways.filter(gateway => gateway.id !== id),
            loading: false
          }))

          toast.success('Payment gateway deleted successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to delete gateway',
            loading: false
          })
          toast.error('Failed to delete payment gateway')
          throw error
        }
      },

      toggleGatewayStatus: async (id, isActive) => {
        try {
          await get().updateGateway(id, { isActive })
        } catch (error) {
          // Error handling is done in updateGateway
        }
      }
    }),
    {
      name: 'payment-gateway-store'
    }
  )
)
