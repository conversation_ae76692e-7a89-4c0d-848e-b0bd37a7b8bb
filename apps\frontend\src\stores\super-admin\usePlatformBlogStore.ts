import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

// Types
export interface PlatformBlogPost {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: string
  featuredImage?: any
  status: 'draft' | 'scheduled' | 'published' | 'archived'
  publishedAt?: string
  scheduledFor?: string
  category?: PlatformBlogCategory
  tags?: Array<{ tag: string }>
  isAnnouncement: boolean
  announcementPriority?: 'low' | 'medium' | 'high' | 'critical'
  targetAudience: string[]
  seo?: {
    title?: string
    description?: string
    keywords?: Array<{ keyword: string }>
    canonicalUrl?: string
  }
  analytics?: {
    viewCount: number
    uniqueViewCount: number
    likeCount: number
    shareCount: number
    readingTime: number
    instituteAdminViews: number
    studentViews: number
    staffViews: number
    publicViews: number
  }
  settings?: {
    allowComments: boolean
    isFeatured: boolean
    isSticky: boolean
    showOnDashboard: boolean
  }
  author: any
  lastEditedBy?: any
  createdAt: string
  updatedAt: string
}

export interface PlatformBlogCategory {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  icon?: string
  isActive: boolean
}

export interface PlatformBlogAnalytics {
  totalViews: number
  totalUniqueViews: number
  totalLikes: number
  totalShares: number
  instituteAdminViews: number
  studentViews: number
  staffViews: number
  publicViews: number
  totalPosts: number
  draftPosts: number
  publishedPosts: number
  scheduledPosts: number
  announcementPosts: number
  period: string
}

interface PlatformBlogState {
  // Data
  posts: PlatformBlogPost[]
  currentPost: PlatformBlogPost | null
  categories: PlatformBlogCategory[]
  analytics: PlatformBlogAnalytics

  // Loading states
  postsLoading: boolean
  categoriesLoading: boolean
  analyticsLoading: boolean

  // Filters
  selectedStatus: string | null
  selectedCategory: string | null
  selectedTargetAudience: string | null
  showAnnouncementsOnly: boolean
  viewMode: 'list' | 'card'

  // UI State
  error: string | null

  // Actions
  fetchPosts: (params?: any) => Promise<void>
  fetchPost: (id: string) => Promise<void>
  createPost: (postData: Partial<PlatformBlogPost>) => Promise<void>
  updatePost: (id: string, postData: Partial<PlatformBlogPost>) => Promise<void>
  deletePost: (id: string) => Promise<void>
  publishPost: (id: string) => Promise<void>
  schedulePost: (id: string, scheduledFor: string) => Promise<void>

  // Categories
  fetchCategories: () => Promise<void>
  createCategory: (categoryData: Partial<PlatformBlogCategory>) => Promise<void>
  updateCategory: (id: string, categoryData: Partial<PlatformBlogCategory>) => Promise<void>
  deleteCategory: (id: string) => Promise<void>

  // Filters
  setFilters: (filters: any) => void
  clearFilters: () => void
  setViewMode: (mode: 'list' | 'card') => void

  // Analytics
  fetchAnalytics: (period?: string) => Promise<void>
}

export const usePlatformBlogStore = create<PlatformBlogState>()(
  devtools(
    (set, get) => ({
      // Initial state
      posts: [],
      currentPost: null,
      postsLoading: false,
      categories: [],
      categoriesLoading: false,
      analyticsLoading: false,
      selectedStatus: null,
      selectedCategory: null,
      selectedTargetAudience: null,
      showAnnouncementsOnly: false,
      viewMode: 'list',
      analytics: {
        totalViews: 0,
        totalUniqueViews: 0,
        totalLikes: 0,
        totalShares: 0,
        instituteAdminViews: 0,
        studentViews: 0,
        staffViews: 0,
        publicViews: 0,
        totalPosts: 0,
        draftPosts: 0,
        publishedPosts: 0,
        scheduledPosts: 0,
        announcementPosts: 0,
        period: '30d'
      },
      error: null,

      // Posts actions
      fetchPosts: async (params = {}) => {
        set({ postsLoading: true, error: null })
        try {
          const state = get()
          const queryParams = {
            ...params,
            status: state.selectedStatus,
            category: state.selectedCategory,
            targetAudience: state.selectedTargetAudience,
            isAnnouncement: state.showAnnouncementsOnly ? 'true' : undefined,
          }

          // Remove null/undefined values and convert to string
          const cleanParams: Record<string, string> = {}
          Object.keys(queryParams).forEach(key => {
            if (queryParams[key] !== null && queryParams[key] !== undefined && queryParams[key] !== '') {
              cleanParams[key] = String(queryParams[key])
            }
          })

          const response = await api.get('/api/super-admin/platform-blogs/posts', cleanParams)
          set({
            posts: response.posts,
            postsLoading: false
          })
        } catch (error: any) {
          console.error('Fetch posts error:', error)
          set({ 
            error: error.message || 'Failed to fetch posts',
            postsLoading: false 
          })
          toast.error('Failed to fetch posts')
        }
      },

      fetchPost: async (id: string) => {
        set({ postsLoading: true, error: null })
        try {
          const response = await api.get(`/api/super-admin/platform-blogs/posts/${id}`)
          set({
            currentPost: response.post,
            postsLoading: false
          })
        } catch (error: any) {
          console.error('Fetch post error:', error)
          set({ 
            error: error.message || 'Failed to fetch post',
            postsLoading: false 
          })
          toast.error('Failed to fetch post')
        }
      },

      createPost: async (postData: Partial<PlatformBlogPost>) => {
        try {
          const response = await api.post('/api/super-admin/platform-blogs/posts', postData)
          
          // Refresh posts list
          await get().fetchPosts()
          
          toast.success('Platform blog post created successfully')
        } catch (error: any) {
          console.error('Create post error:', error)
          set({ error: error.message || 'Failed to create post' })
          toast.error('Failed to create post')
          throw error
        }
      },

      updatePost: async (id: string, postData: Partial<PlatformBlogPost>) => {
        try {
          const response = await api.put(`/api/super-admin/platform-blogs/posts/${id}`, postData)
          
          // Update current post if it's the one being edited
          const state = get()
          if (state.currentPost?.id === id) {
            set({ currentPost: response.post })
          }
          
          // Refresh posts list
          await get().fetchPosts()
          
          toast.success('Platform blog post updated successfully')
        } catch (error: any) {
          console.error('Update post error:', error)
          set({ error: error.message || 'Failed to update post' })
          toast.error('Failed to update post')
          throw error
        }
      },

      deletePost: async (id: string) => {
        try {
          await api.delete(`/api/super-admin/platform-blogs/posts/${id}`)
          
          // Remove from posts list
          set(state => ({
            posts: state.posts.filter(post => post.id !== id),
            currentPost: state.currentPost?.id === id ? null : state.currentPost
          }))
          
          toast.success('Platform blog post deleted successfully')
        } catch (error: any) {
          console.error('Delete post error:', error)
          set({ error: error.message || 'Failed to delete post' })
          toast.error('Failed to delete post')
          throw error
        }
      },

      publishPost: async (id: string) => {
        try {
          const response = await api.post(`/api/super-admin/platform-blogs/posts/${id}/publish`)
          
          // Update post in list
          set(state => ({
            posts: state.posts.map(post => 
              post.id === id ? { ...post, status: 'published', publishedAt: new Date().toISOString() } : post
            ),
            currentPost: state.currentPost?.id === id 
              ? { ...state.currentPost, status: 'published', publishedAt: new Date().toISOString() }
              : state.currentPost
          }))
          
          toast.success('Platform blog post published successfully')
        } catch (error: any) {
          console.error('Publish post error:', error)
          set({ error: error.message || 'Failed to publish post' })
          toast.error('Failed to publish post')
          throw error
        }
      },

      schedulePost: async (id: string, scheduledFor: string) => {
        try {
          const response = await api.post(`/api/super-admin/platform-blogs/posts/${id}/schedule`, { scheduledFor })
          
          // Update post in list
          set(state => ({
            posts: state.posts.map(post => 
              post.id === id ? { ...post, status: 'scheduled', scheduledFor } : post
            ),
            currentPost: state.currentPost?.id === id 
              ? { ...state.currentPost, status: 'scheduled', scheduledFor }
              : state.currentPost
          }))
          
          toast.success('Platform blog post scheduled successfully')
        } catch (error: any) {
          console.error('Schedule post error:', error)
          set({ error: error.message || 'Failed to schedule post' })
          toast.error('Failed to schedule post')
          throw error
        }
      },

      // Categories actions
      fetchCategories: async () => {
        set({ categoriesLoading: true, error: null })
        try {
          const response = await api.get('/api/super-admin/platform-blogs/categories')
          set({
            categories: response.categories,
            categoriesLoading: false
          })
        } catch (error: any) {
          console.error('Fetch categories error:', error)
          set({
            error: error.message || 'Failed to fetch categories',
            categoriesLoading: false
          })
          toast.error('Failed to fetch categories')
        }
      },

      createCategory: async (categoryData: Partial<PlatformBlogCategory>) => {
        try {
          const response = await api.post('/api/super-admin/platform-blogs/categories', categoryData)

          // Add to categories list
          set(state => ({
            categories: [...state.categories, response.category]
          }))

          toast.success('Platform blog category created successfully')
        } catch (error: any) {
          console.error('Create category error:', error)
          set({ error: error.message || 'Failed to create category' })
          toast.error('Failed to create category')
          throw error
        }
      },

      updateCategory: async (id: string, categoryData: Partial<PlatformBlogCategory>) => {
        try {
          const response = await api.put(`/api/super-admin/platform-blogs/categories/${id}`, categoryData)

          // Update in categories list
          set(state => ({
            categories: state.categories.map(category =>
              category.id === id ? response.category : category
            )
          }))

          toast.success('Platform blog category updated successfully')
        } catch (error: any) {
          console.error('Update category error:', error)
          set({ error: error.message || 'Failed to update category' })
          toast.error('Failed to update category')
          throw error
        }
      },

      deleteCategory: async (id: string) => {
        try {
          await api.delete(`/api/super-admin/platform-blogs/categories/${id}`)

          // Remove from categories list
          set(state => ({
            categories: state.categories.filter(category => category.id !== id)
          }))

          toast.success('Platform blog category deleted successfully')
        } catch (error: any) {
          console.error('Delete category error:', error)
          set({ error: error.message || 'Failed to delete category' })
          toast.error('Failed to delete category')
          throw error
        }
      },

      // Filter actions
      setFilters: (filters: any) => {
        set(state => ({
          selectedStatus: filters.status !== undefined ? filters.status : state.selectedStatus,
          selectedCategory: filters.category !== undefined ? filters.category : state.selectedCategory,
          selectedTargetAudience: filters.targetAudience !== undefined ? filters.targetAudience : state.selectedTargetAudience,
          showAnnouncementsOnly: filters.showAnnouncementsOnly !== undefined ? filters.showAnnouncementsOnly : state.showAnnouncementsOnly,
        }))
      },

      clearFilters: () => {
        set({
          selectedStatus: null,
          selectedCategory: null,
          selectedTargetAudience: null,
          showAnnouncementsOnly: false,
        })
      },

      setViewMode: (mode: 'list' | 'card') => {
        set({ viewMode: mode })
      },

      // Analytics actions
      fetchAnalytics: async (period = '30d') => {
        set({ analyticsLoading: true, error: null })
        try {
          const response = await api.get('/api/super-admin/platform-blogs/analytics', { period })
          set({
            analytics: response.analytics,
            analyticsLoading: false
          })
        } catch (error: any) {
          console.error('Fetch analytics error:', error)
          set({
            error: error.message || 'Failed to fetch analytics',
            analyticsLoading: false
          })
          toast.error('Failed to fetch analytics')
        }
      },
    }),
    {
      name: 'platform-blog-store',
    }
  )
)
