import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { toast } from 'sonner'
import {
  Theme,
  PlatformTheme,
  InstituteTheme,
  ThemeCategory,
  ThemesState,
  ThemeFilters,
  CreateThemeRequest,
  UpdateThemeRequest,
  ApplyThemeRequest,
  ThemeType,
  DEFAULT_PAGINATION
} from '@/types/themes'

interface ThemesStore extends ThemesState {
  // Actions
  fetchPlatformThemes: () => Promise<void>
  fetchInstituteThemes: () => Promise<void>
  fetchCategories: () => Promise<void>
  createTheme: (data: CreateThemeRequest) => Promise<void>
  updateTheme: (data: UpdateThemeRequest) => Promise<void>
  deleteTheme: (id: string) => Promise<void>
  applyTheme: (data: ApplyThemeRequest) => Promise<void>
  duplicateTheme: (id: string) => Promise<void>
  
  // UI Actions
  setSelectedTheme: (theme: Theme | null) => void
  setPreviewTheme: (theme: Theme | null) => void
  setViewMode: (mode: 'grid' | 'list') => void
  setActiveTab: (tab: 'platform' | 'institute') => void
  setShowPreview: (show: boolean) => void
  setFilters: (filters: Partial<ThemeFilters>) => void
  clearFilters: () => void
  
  // Utility Actions
  getThemeById: (id: string) => Theme | undefined
  getActiveThemes: () => Theme[]
  getCurrentPlatformTheme: () => PlatformTheme | null
  resetState: () => void
}

const initialState: ThemesState = {
  platformThemes: [],
  instituteThemes: [],
  categories: [],
  currentPlatformTheme: null,
  loading: false,
  error: null,
  selectedTheme: null,
  previewTheme: null,
  filters: {
    page: 1,
    limit: 12,
    sortField: 'name',
    sortOrder: 'asc'
  },
  pagination: DEFAULT_PAGINATION,
  viewMode: 'grid',
  showPreview: false,
  activeTab: 'platform'
}

// Mock API functions (replace with actual API calls)
const mockAPI = {
  async fetchPlatformThemes(filters: ThemeFilters) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockPlatformThemes: PlatformTheme[] = [
      {
        id: '1',
        name: 'Modern Education',
        description: 'A sleek, modern theme perfect for educational platforms',
        category: { id: '1', name: 'Modern', description: 'Modern designs', icon: 'Sparkles', order: 1 },
        type: 'platform',
        version: '1.0.0',
        author: 'LMS Team',
        thumbnail: '/themes/modern-education/thumbnail.jpg',
        previewImages: ['/themes/modern-education/preview1.jpg', '/themes/modern-education/preview2.jpg'],
        features: [],
        isActive: true,
        isDefault: true,
        isPremium: false,
        supportedFeatures: [],
        customization: {
          colors: { primary: '#3B82F6', secondary: '#10B981', accent: '#F59E0B', background: '#FFFFFF', text: '#1F2937' },
          fonts: { heading: 'Inter', body: 'Inter' },
          layout: { headerStyle: 'fixed', footerStyle: 'detailed', sidebarPosition: 'none' },
          components: {}
        },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        tags: ['modern', 'education', 'responsive'],
        landingPageSections: [],
        heroSection: {
          title: 'Welcome to Modern Education',
          subtitle: 'Transform your learning experience',
          backgroundImage: '/themes/modern-education/hero-bg.jpg',
          ctaButton: { text: 'Get Started', link: '/register', style: 'primary' },
          layout: 'centered'
        },
        featuresSection: {
          title: 'Why Choose Us',
          subtitle: 'Discover our amazing features',
          features: [],
          layout: 'grid'
        },
        aboutSection: {
          title: 'About Us',
          content: 'We are dedicated to providing the best education platform.',
          image: '/themes/modern-education/about.jpg',
          layout: 'side-by-side'
        },
        contactSection: {
          title: 'Contact Us',
          subtitle: 'Get in touch with our team',
          showForm: true,
          showMap: false,
          contactInfo: { email: '<EMAIL>', phone: '+1234567890', address: '123 Education St' }
        }
      },
      {
        id: '2',
        name: 'Classic Academic',
        description: 'Traditional academic theme with professional styling',
        category: { id: '2', name: 'Classic', description: 'Classic designs', icon: 'BookOpen', order: 2 },
        type: 'platform',
        version: '1.0.0',
        author: 'LMS Team',
        thumbnail: '/themes/classic-academic/thumbnail.jpg',
        previewImages: ['/themes/classic-academic/preview1.jpg'],
        features: [],
        isActive: false,
        isDefault: false,
        isPremium: false,
        supportedFeatures: [],
        customization: {
          colors: { primary: '#1E40AF', secondary: '#059669', accent: '#DC2626', background: '#F9FAFB', text: '#111827' },
          fonts: { heading: 'Georgia', body: 'Georgia' },
          layout: { headerStyle: 'static', footerStyle: 'detailed', sidebarPosition: 'none' },
          components: {}
        },
        createdAt: '2024-01-02T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
        tags: ['classic', 'academic', 'professional'],
        landingPageSections: [],
        heroSection: {
          title: 'Classic Academic Excellence',
          subtitle: 'Traditional learning, modern results',
          backgroundImage: '/themes/classic-academic/hero-bg.jpg',
          ctaButton: { text: 'Learn More', link: '/about', style: 'primary' },
          layout: 'left-aligned'
        },
        featuresSection: {
          title: 'Our Features',
          subtitle: 'Excellence in education',
          features: [],
          layout: 'list'
        },
        aboutSection: {
          title: 'Our Mission',
          content: 'Providing quality education with traditional values.',
          image: '/themes/classic-academic/about.jpg',
          layout: 'stacked'
        },
        contactSection: {
          title: 'Get in Touch',
          subtitle: 'We are here to help',
          showForm: true,
          showMap: true,
          contactInfo: { email: '<EMAIL>', phone: '+1234567890', address: '456 Academic Ave' }
        }
      }
    ]
    
    return {
      themes: mockPlatformThemes,
      total: mockPlatformThemes.length,
      page: filters.page || 1,
      limit: filters.limit || 12,
      totalPages: Math.ceil(mockPlatformThemes.length / (filters.limit || 12))
    }
  },

  async fetchInstituteThemes(filters: ThemeFilters) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockInstituteThemes: InstituteTheme[] = [
      {
        id: '3',
        name: 'E-Learning Marketplace',
        description: 'Complete e-commerce theme for course marketplaces',
        category: { id: '3', name: 'E-commerce', description: 'E-commerce designs', icon: 'ShoppingCart', order: 3 },
        type: 'institute',
        version: '1.0.0',
        author: 'LMS Team',
        thumbnail: '/themes/elearning-marketplace/thumbnail.jpg',
        previewImages: ['/themes/elearning-marketplace/preview1.jpg', '/themes/elearning-marketplace/preview2.jpg'],
        features: [],
        isActive: true,
        isDefault: true,
        isPremium: false,
        supportedFeatures: [],
        customization: {
          colors: { primary: '#7C3AED', secondary: '#10B981', accent: '#F59E0B', background: '#FFFFFF', text: '#1F2937' },
          fonts: { heading: 'Poppins', body: 'Inter' },
          layout: { headerStyle: 'fixed', footerStyle: 'minimal', sidebarPosition: 'left' },
          components: {}
        },
        createdAt: '2024-01-03T00:00:00Z',
        updatedAt: '2024-01-03T00:00:00Z',
        tags: ['ecommerce', 'marketplace', 'courses'],
        ecommerceFeatures: [
          {
            id: '1',
            name: 'Shopping Cart',
            description: 'Advanced shopping cart functionality',
            enabled: true,
            config: {}
          },
          {
            id: '2',
            name: 'Wishlist',
            description: 'Course wishlist feature',
            enabled: true,
            config: {}
          }
        ],
        courseDisplayOptions: [
          {
            id: '1',
            name: 'Grid View',
            layout: 'grid',
            itemsPerPage: 12,
            showFilters: true,
            showSorting: true,
            showSearch: true
          }
        ],
        marketplaceLayout: {
          headerStyle: 'detailed',
          categoryNavigation: 'sidebar',
          productGrid: 'detailed',
          filterPosition: 'left'
        },
        checkoutProcess: {
          steps: [
            { id: '1', name: 'Cart Review', required: true, order: 1 },
            { id: '2', name: 'User Information', required: true, order: 2 },
            { id: '3', name: 'Payment', required: true, order: 3 }
          ],
          paymentMethods: ['credit_card', 'paypal', 'stripe'],
          guestCheckout: false,
          socialLogin: true
        },
        liveClassIntegration: {
          enabled: true,
          displayStyle: 'embedded',
          bookingSystem: true,
          calendarIntegration: true
        }
      }
    ]
    
    return {
      themes: mockInstituteThemes,
      total: mockInstituteThemes.length,
      page: filters.page || 1,
      limit: filters.limit || 12,
      totalPages: Math.ceil(mockInstituteThemes.length / (filters.limit || 12))
    }
  },

  async fetchCategories() {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockCategories: ThemeCategory[] = [
      { id: '1', name: 'Modern', description: 'Modern and contemporary designs', icon: 'Sparkles', order: 1 },
      { id: '2', name: 'Classic', description: 'Traditional and timeless designs', icon: 'BookOpen', order: 2 },
      { id: '3', name: 'E-commerce', description: 'Marketplace and e-commerce focused', icon: 'ShoppingCart', order: 3 },
      { id: '4', name: 'Minimal', description: 'Clean and minimalist designs', icon: 'Minus', order: 4 },
      { id: '5', name: 'Corporate', description: 'Professional business themes', icon: 'Building', order: 5 }
    ]
    
    return { categories: mockCategories }
  },

  async createTheme(data: CreateThemeRequest) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    return { success: true }
  },

  async updateTheme(data: UpdateThemeRequest) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    return { success: true }
  },

  async deleteTheme(id: string) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    return { success: true }
  },

  async applyTheme(data: ApplyThemeRequest) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    return { success: true }
  }
}

export const useThemesStore = create<ThemesStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Fetch Actions
        fetchPlatformThemes: async () => {
          set({ loading: true, error: null })
          try {
            const response = await mockAPI.fetchPlatformThemes(get().filters)
            set({
              platformThemes: response.themes as PlatformTheme[],
              pagination: {
                page: response.page,
                limit: response.limit,
                total: response.total,
                totalPages: response.totalPages
              },
              loading: false
            })
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to fetch platform themes',
              loading: false 
            })
            toast.error('Failed to fetch platform themes')
          }
        },

        fetchInstituteThemes: async () => {
          set({ loading: true, error: null })
          try {
            const response = await mockAPI.fetchInstituteThemes(get().filters)
            set({
              instituteThemes: response.themes as InstituteTheme[],
              pagination: {
                page: response.page,
                limit: response.limit,
                total: response.total,
                totalPages: response.totalPages
              },
              loading: false
            })
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to fetch institute themes',
              loading: false 
            })
            toast.error('Failed to fetch institute themes')
          }
        },

        fetchCategories: async () => {
          try {
            const response = await mockAPI.fetchCategories()
            set({ categories: response.categories })
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to fetch categories' })
            toast.error('Failed to fetch theme categories')
          }
        },

        // CRUD Actions
        createTheme: async (data: CreateThemeRequest) => {
          set({ loading: true, error: null })
          try {
            await mockAPI.createTheme(data)
            set({ loading: false })
            toast.success('Theme created successfully')
            // Refresh themes based on type
            if (data.type === 'platform') {
              get().fetchPlatformThemes()
            } else {
              get().fetchInstituteThemes()
            }
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to create theme',
              loading: false 
            })
            toast.error('Failed to create theme')
          }
        },

        updateTheme: async (data: UpdateThemeRequest) => {
          set({ loading: true, error: null })
          try {
            await mockAPI.updateTheme(data)
            set({ loading: false })
            toast.success('Theme updated successfully')
            // Refresh themes
            get().fetchPlatformThemes()
            get().fetchInstituteThemes()
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to update theme',
              loading: false 
            })
            toast.error('Failed to update theme')
          }
        },

        deleteTheme: async (id: string) => {
          set({ loading: true, error: null })
          try {
            await mockAPI.deleteTheme(id)
            set(state => ({
              platformThemes: state.platformThemes.filter(theme => theme.id !== id),
              instituteThemes: state.instituteThemes.filter(theme => theme.id !== id),
              loading: false
            }))
            toast.success('Theme deleted successfully')
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to delete theme',
              loading: false 
            })
            toast.error('Failed to delete theme')
          }
        },

        applyTheme: async (data: ApplyThemeRequest) => {
          set({ loading: true, error: null })
          try {
            await mockAPI.applyTheme(data)
            
            // Update current platform theme if applying platform theme
            if (data.targetType === 'platform') {
              const theme = get().getThemeById(data.themeId) as PlatformTheme
              if (theme) {
                set({ currentPlatformTheme: theme })
              }
            }
            
            set({ loading: false })
            toast.success(`Theme applied successfully to ${data.targetType}`)
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to apply theme',
              loading: false 
            })
            toast.error('Failed to apply theme')
          }
        },

        duplicateTheme: async (id: string) => {
          const theme = get().getThemeById(id)
          if (!theme) return

          try {
            const duplicateData: CreateThemeRequest = {
              name: `${theme.name} (Copy)`,
              description: theme.description,
              category: theme.category.id,
              type: theme.type,
              features: theme.features.map(f => f.id),
              customization: theme.customization,
              tags: theme.tags
            }
            
            await get().createTheme(duplicateData)
            toast.success('Theme duplicated successfully')
          } catch (error) {
            toast.error('Failed to duplicate theme')
          }
        },

        // UI Actions
        setSelectedTheme: (theme) => set({ selectedTheme: theme }),
        setPreviewTheme: (theme) => set({ previewTheme: theme }),
        setViewMode: (mode) => set({ viewMode: mode }),
        setActiveTab: (tab) => set({ activeTab: tab }),
        setShowPreview: (show) => set({ showPreview: show }),
        setFilters: (filters) => set(state => ({ 
          filters: { ...state.filters, ...filters }
        })),
        clearFilters: () => set({ 
          filters: { 
            page: 1, 
            limit: 12, 
            sortField: 'name', 
            sortOrder: 'asc' 
          } 
        }),

        // Utility Actions
        getThemeById: (id) => {
          const { platformThemes, instituteThemes } = get()
          return [...platformThemes, ...instituteThemes].find(theme => theme.id === id)
        },
        
        getActiveThemes: () => {
          const { platformThemes, instituteThemes } = get()
          return [...platformThemes, ...instituteThemes].filter(theme => theme.isActive)
        },

        getCurrentPlatformTheme: () => get().currentPlatformTheme,

        resetState: () => set(initialState)
      }),
      {
        name: 'themes-store',
        partialize: (state) => ({
          viewMode: state.viewMode,
          activeTab: state.activeTab,
          filters: state.filters
        })
      }
    ),
    { name: 'themes-store' }
  )
)
