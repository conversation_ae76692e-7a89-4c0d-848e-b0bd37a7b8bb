import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

interface TaxComponent {
  id: string
  name: string
  code: string
  type: string
  rate: number
  calculationMethod: string
  applicableRegions: any[]
  isActive: boolean
  effectiveFrom: string
  effectiveTo?: string
  priority: number
  createdAt: string
  updatedAt: string
}

interface TaxGroup {
  id: string
  name: string
  code: string
  description?: string
  taxComponents: Array<{
    component: TaxComponent
    customRate?: number
    isOptional: boolean
  }>
  totalRate: number
  applicableScenarios: any[]
  isActive: boolean
  isDefault: boolean
  effectiveFrom: string
  effectiveTo?: string
  priority: number
  createdAt: string
  updatedAt: string
}

interface TaxRule {
  id: string
  name: string
  description?: string
  taxGroup: TaxGroup
  conditions: any
  exemptions: any[]
  isActive: boolean
  priority: number
  effectiveFrom: string
  effectiveTo?: string
  createdAt: string
  updatedAt: string
}

interface Branch {
  id: string
  name: string
  code: string
  institute: any
  location: {
    address: string
    country: any
    state: any
    district: any
    pincode?: string
  }
  contact: any
  taxInformation: any
  billingSettings: any
  isActive: boolean
  isHeadOffice: boolean
}

interface TaxCalculationResult {
  subtotal: number
  totalTax: number
  grandTotal: number
  taxComponents: Array<{
    id: string
    name: string
    code: string
    type: string
    rate: number
    amount: number
  }>
  appliedRules: string[]
  exemptions: Array<{
    condition: string
    exemptionPercentage: number
    exemptedAmount: number
  }>
  scenario: string
  breakdown: {
    sgst?: number
    cgst?: number
    igst?: number
    vat?: number
    other?: number
  }
}

interface TaxFilters {
  search: string
  type?: string
  isActive: 'all' | 'true' | 'false'
}

interface Pagination {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface TaxState {
  // Data
  taxComponents: TaxComponent[]
  taxGroups: TaxGroup[]
  taxRules: TaxRule[]
  branches: Branch[]

  // UI State
  viewMode: 'list' | 'card'
  isLoading: boolean
  error: string | null

  // Filters
  filters: TaxFilters

  // Pagination
  componentsPagination: Pagination
  groupsPagination: Pagination
  rulesPagination: Pagination

  // Tax Calculation
  calculationResult: TaxCalculationResult | null
  previewResults: Array<{
    scenario: string
    description: string
    calculation: TaxCalculationResult
  }>

  // Actions
  setViewMode: (mode: 'list' | 'card') => void
  setFilters: (filters: Partial<TaxFilters>) => void

  // API Actions
  fetchTaxComponents: (page?: number) => Promise<void>
  fetchTaxGroups: (page?: number) => Promise<void>
  fetchTaxRules: (page?: number) => Promise<void>
  fetchBranches: (instituteId?: string) => Promise<void>

  // CRUD Actions
  createTaxComponent: (data: Partial<TaxComponent>) => Promise<void>
  updateTaxComponent: (id: string, data: Partial<TaxComponent>) => Promise<void>
  deleteTaxComponent: (id: string) => Promise<void>

  createTaxGroup: (data: Partial<TaxGroup>) => Promise<void>
  updateTaxGroup: (id: string, data: Partial<TaxGroup>) => Promise<void>
  deleteTaxGroup: (id: string) => Promise<void>

  createTaxRule: (data: Partial<TaxRule>) => Promise<void>
  updateTaxRule: (id: string, data: Partial<TaxRule>) => Promise<void>
  deleteTaxRule: (id: string) => Promise<void>

  // Tax Calculation Actions
  calculateTax: (input: any) => Promise<void>
  previewTaxCalculation: (input: any) => Promise<void>

  // Utility Actions
  clearError: () => void
  resetFilters: () => void
}

const initialFilters: TaxFilters = {
  search: '',
  isActive: 'true'
}

const initialPagination: Pagination = {
  page: 1,
  limit: 20,
  totalPages: 1,
  totalDocs: 0,
  hasNextPage: false,
  hasPrevPage: false
}

export const useTaxStore = create<TaxState>()(
  devtools(
    (set, get) => ({
      // Initial State
      taxComponents: [],
      taxGroups: [],
      taxRules: [],
      branches: [],
      viewMode: 'list',
      isLoading: false,
      error: null,
      filters: initialFilters,
      componentsPagination: initialPagination,
      groupsPagination: initialPagination,
      rulesPagination: initialPagination,
      calculationResult: null,
      previewResults: [],

      // UI Actions
      setViewMode: (mode) => set({ viewMode: mode }),

      setFilters: (newFilters) => set((state) => ({
        filters: { ...state.filters, ...newFilters }
      })),

      // API Actions
      fetchTaxComponents: async (page = 1) => {
        const currentState = get()
        // Prevent duplicate calls if already loading
        if (currentState.isLoading) return

        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            search: filters.search,
            isActive: filters.isActive,
            ...(filters.type && { type: filters.type })
          })

          const data = await api.get('/api/tax/components', Object.fromEntries(params))

          if (data.success) {
            set({
              taxComponents: data.components,
              componentsPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch tax components')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch tax components')
        }
      },

      fetchTaxGroups: async (page = 1) => {
        const currentState = get()
        // Prevent duplicate calls if already loading
        if (currentState.isLoading) return

        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            search: filters.search,
            isActive: filters.isActive
          })

          const data = await api.get('/api/tax/groups', Object.fromEntries(params))

          if (data.success) {
            set({
              taxGroups: data.groups,
              groupsPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch tax groups')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch tax groups')
        }
      },

      fetchTaxRules: async (page = 1) => {
        const currentState = get()
        // Prevent duplicate calls if already loading
        if (currentState.isLoading) return

        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            search: filters.search,
            isActive: filters.isActive
          })

          const data = await api.get('/api/tax/rules', Object.fromEntries(params))

          if (data.success) {
            set({
              taxRules: data.rules,
              rulesPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch tax rules')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch tax rules')
        }
      },

      fetchBranches: async (instituteId) => {
        set({ isLoading: true, error: null })
        try {
          const params = new URLSearchParams()
          if (instituteId) {
            params.append('instituteId', instituteId)
          }

          const response = await fetch(`/api/tax/branches?${params}`, {
            credentials: 'include'
          })
          const data = await response.json()

          if (data.success) {
            set({
              branches: data.branches,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch branches')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to fetch branches')
        }
      },

      // CRUD Actions
      createTaxComponent: async (data) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.post('/api/tax/components', data)

          if (result.success) {
            await get().fetchTaxComponents()
            toast.success('Tax Component Created', {
              description: 'Tax component has been created successfully.'
            })
            set({ isLoading: false })
          } else {
            throw new Error(result.message || 'Failed to create tax component')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to create tax component', {
            description: errorMessage
          })
          throw error
        }
      },

      updateTaxComponent: async (id, data) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.put(`/api/tax/components/${id}`, data)

          if (result.success) {
            await get().fetchTaxComponents()
            toast.success('Tax Component Updated', {
              description: 'Tax component has been updated successfully.'
            })
            set({ isLoading: false })
          } else {
            throw new Error(result.message || 'Failed to update tax component')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to update tax component', {
            description: errorMessage
          })
          throw error
        }
      },

      deleteTaxComponent: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.delete(`/api/tax/components/${id}`)

          if (result.success) {
            await get().fetchTaxComponents()
            toast.success('Tax Component Deleted', {
              description: 'Tax component has been deleted successfully.'
            })
            set({ isLoading: false })
          } else {
            throw new Error(result.message || 'Failed to delete tax component')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to delete tax component', {
            description: errorMessage
          })
          throw error
        }
      },

      createTaxGroup: async (data) => {
        set({ isLoading: true, error: null })
        try {
          const result = await api.post('/api/tax-groups', data)

          if (result.success) {
            await get().fetchTaxGroups()
            toast.success('Tax Group Created', {
              description: 'Tax group has been created successfully.'
            })
            set({ isLoading: false })
          } else {
            throw new Error(result.message || 'Failed to create tax group')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to create tax group', {
            description: errorMessage
          })
          throw error
        }
      },

      updateTaxGroup: async (id, data) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/tax-groups/${id}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          const result = await response.json()

          if (response.ok) {
            await get().fetchTaxGroups()
            toast.success('Tax Group Updated', {
              description: 'Tax group has been updated successfully.'
            })
            set({ isLoading: false })
          } else {
            throw new Error(result.message || 'Failed to update tax group')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to update tax group', {
            description: errorMessage
          })
          throw error
        }
      },

      deleteTaxGroup: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/tax-groups/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (response.ok) {
            await get().fetchTaxGroups()
            toast.success('Tax Group Deleted', {
              description: 'Tax group has been deleted successfully.'
            })
            set({ isLoading: false })
          } else {
            const result = await response.json()
            throw new Error(result.message || 'Failed to delete tax group')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to delete tax group', {
            description: errorMessage
          })
          throw error
        }
      },

      createTaxRule: async (data) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/tax-rules', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          const result = await response.json()

          if (response.ok) {
            await get().fetchTaxRules()
            toast.success('Tax Rule Created', {
              description: 'Tax rule has been created successfully.'
            })
            set({ isLoading: false })
          } else {
            throw new Error(result.message || 'Failed to create tax rule')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to create tax rule', {
            description: errorMessage
          })
          throw error
        }
      },

      updateTaxRule: async (id, data) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/tax-rules/${id}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          const result = await response.json()

          if (response.ok) {
            await get().fetchTaxRules()
            toast.success('Tax Rule Updated', {
              description: 'Tax rule has been updated successfully.'
            })
            set({ isLoading: false })
          } else {
            throw new Error(result.message || 'Failed to update tax rule')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to update tax rule', {
            description: errorMessage
          })
          throw error
        }
      },

      deleteTaxRule: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/tax-rules/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (response.ok) {
            await get().fetchTaxRules()
            toast.success('Tax Rule Deleted', {
              description: 'Tax rule has been deleted successfully.'
            })
            set({ isLoading: false })
          } else {
            const result = await response.json()
            throw new Error(result.message || 'Failed to delete tax rule')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to delete tax rule', {
            description: errorMessage
          })
          throw error
        }
      },

      // Tax Calculation Actions
      calculateTax: async (input) => {
        set({ isLoading: true, error: null })
        try {
          const data = await api.post('/api/tax/calculate', input)

          if (data.success) {
            set({
              calculationResult: data.calculation,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to calculate tax')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to calculate tax')
        }
      },

      previewTaxCalculation: async (input) => {
        set({ isLoading: true, error: null })
        try {
          const data = await api.post('/api/tax/preview', input)

          if (data.success) {
            set({
              previewResults: data.preview.scenarios,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to preview tax calculation')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
          toast.error('Failed to preview tax calculation')
        }
      },

      // Utility Actions
      clearError: () => set({ error: null }),

      resetFilters: () => set({
        filters: initialFilters
      })
    }),
    {
      name: 'tax-store'
    }
  )
)
