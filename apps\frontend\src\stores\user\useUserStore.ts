import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

// Types
export interface MediaObject {
  id: number
  url: string
  filename?: string
  mimeType?: string
  filesize?: number
  width?: number
  height?: number
  sizes?: {
    avatar_small?: { url: string; width: number; height: number }
    avatar_medium?: { url: string; width: number; height: number }
    avatar_large?: { url: string; width: number; height: number }
    [key: string]: any
  }
}

interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  avatar?: string | number | MediaObject | null
  role?: any
  createdAt: string
  updatedAt: string
}

export interface UserProfileData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password?: string
  confirmPassword?: string
  avatar?: string | File
}

interface UserState {
  // State
  user: User | null
  isLoading: boolean
  isUpdating: boolean
  error: string | null

  // Actions
  fetchCurrentUser: () => Promise<void>
  updateProfile: (data: UserProfileData) => Promise<void>
  uploadAvatar: (file: File) => Promise<string>
  removeAvatar: () => Promise<void>
  clearError: () => void
  setUser: (user: User | null) => void
}

export const useUserStore = create<UserState>()(
  devtools(
    (set, get) => ({
      // Initial state
      user: null,
      isLoading: false,
      isUpdating: false,
      error: null,

      // Actions
      fetchCurrentUser: async () => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.get('/api/super-admin/user/me')
          // Handle both response formats: { user: ... } or direct user object
          const userData = response.user || response
          set({ user: userData, isLoading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user'
          set({ error: errorMessage, isLoading: false })
          toast.error('Failed to load profile')
        }
      },

      updateProfile: async (data: UserProfileData) => {
        set({ isUpdating: true, error: null })
        try {
          // Prepare the update data
          const updateData: any = {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            phone: data.phone
          }

          // Handle avatar if it's a file
          if (data.avatar instanceof File) {
            await get().uploadAvatar(data.avatar)
            // Don't include avatar in updateData - the upload already updated the user record
            console.log('🖼️ Avatar uploaded, user record already updated')
          } else if (data.avatar === null) {
            // Only send avatar if it's explicitly null (for removal)
            updateData.avatar = null
          } else if (typeof data.avatar === 'number') {
            // If avatar is a media ID, use it directly
            updateData.avatar = data.avatar
          }
          // Don't send avatar URLs - they should be media IDs

          // Handle password update if provided
          if (data.password && data.password.trim() !== '') {
            updateData.password = data.password
          }

          // Remove confirmPassword from update data
          delete updateData.confirmPassword

          const response = await api.put('/api/super-admin/user/me', updateData)

          // Handle response format: { success: true, user: ... }
          const userData = response.user || response

          set({
            user: userData,
            isUpdating: false,
            error: null
          })
          
          toast.success('Profile updated successfully')
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update profile'
          set({ error: errorMessage, isUpdating: false })
          toast.error('Failed to update profile')
          throw error
        }
      },

      uploadAvatar: async (file: File): Promise<string> => {
        try {
          // Validate file
          if (file.size > 5 * 1024 * 1024) {
            throw new Error('File too large. Please select an image smaller than 5MB')
          }

          if (!file.type.startsWith('image/')) {
            throw new Error('Please select a valid image file')
          }

          // For now, create a local URL for the file
          // In a real implementation, you would upload to your storage service
          // TODO: Implement actual file upload to your storage service
          const localUrl = URL.createObjectURL(file)

          // Simulate upload delay
          await new Promise(resolve => setTimeout(resolve, 1000))

          return localUrl
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to upload avatar'
          toast.error(errorMessage)
          throw error
        }
      },

      removeAvatar: async () => {
        set({ isUpdating: true, error: null })
        try {
          console.log('🗑️ Removing avatar via new API...')

          // Use the new api.deleteWithBody method for DELETE with request body
          const result = await api.deleteWithBody('/api/remove-avatar', {
            removeFromUser: true // Remove avatar reference from user profile
          })

          console.log('📦 Remove avatar response:', result)

          // Handle response format: { success: true, deletedMedia: ..., userUpdated: ... }
          if (result.success) {
            // Fetch updated user data to get the latest state
            await get().fetchCurrentUser()

            toast.success('Avatar removed successfully')
          } else {
            throw new Error(result.message || 'Failed to remove avatar')
          }

          set({ isUpdating: false, error: null })
        } catch (error) {
          console.error('❌ Remove avatar error:', error)
          const errorMessage = error instanceof Error ? error.message : 'Failed to remove avatar'
          set({ error: errorMessage, isUpdating: false })
          toast.error('Failed to remove avatar')
          throw error
        }
      },

      clearError: () => {
        set({ error: null })
      },

      setUser: (user: User | null) => {
        set({ user })
      }
    }),
    {
      name: 'user-store',
    }
  )
)
