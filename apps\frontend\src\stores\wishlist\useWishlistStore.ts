import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface WishlistItem {
  id: string
  title: string
  slug: string
  price: number
  originalPrice?: number
  currency: string
  thumbnail?: string
  instructor: string
  institute: string
  rating: number
  level: string
  category: string
  addedAt: string
}

interface WishlistState {
  items: WishlistItem[]
  
  // Actions
  addItem: (item: Omit<WishlistItem, 'addedAt'>) => void
  removeItem: (id: string) => void
  clearWishlist: () => void
  moveToCart: (id: string) => void
  
  // Getters
  getTotalItems: () => number
  getItemById: (id: string) => WishlistItem | undefined
  isItemInWishlist: (id: string) => boolean
  getItemsByCategory: (category: string) => WishlistItem[]
  getItemsByPriceRange: (min: number, max: number) => WishlistItem[]
}

export const useWishlistStore = create<WishlistState>()(
  persist(
    (set, get) => ({
      items: [],

      addItem: (newItem) => {
        const items = get().items
        const existingItem = items.find(item => item.id === newItem.id)

        if (!existingItem) {
          set({
            items: [
              ...items,
              {
                ...newItem,
                addedAt: new Date().toISOString()
              }
            ]
          })
        }
      },

      removeItem: (id) => {
        set({
          items: get().items.filter(item => item.id !== id)
        })
      },

      clearWishlist: () => {
        set({ items: [] })
      },

      moveToCart: (id) => {
        // This would typically integrate with the cart store
        // For now, we'll just remove from wishlist
        get().removeItem(id)
      },

      getTotalItems: () => {
        return get().items.length
      },

      getItemById: (id) => {
        return get().items.find(item => item.id === id)
      },

      isItemInWishlist: (id) => {
        return get().items.some(item => item.id === id)
      },

      getItemsByCategory: (category) => {
        return get().items.filter(item => item.category === category)
      },

      getItemsByPriceRange: (min, max) => {
        return get().items.filter(item => item.price >= min && item.price <= max)
      }
    }),
    {
      name: 'wishlist-storage',
      partialize: (state) => ({ items: state.items })
    }
  )
)
