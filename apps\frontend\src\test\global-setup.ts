// Global setup for Course Builder System tests
export default async function globalSetup() {
  console.log('🚀 Setting up Course Builder System tests...')
  
  // Set test environment variables
  process.env.NODE_ENV = 'test'
  process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3001'
  process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000'
  
  // Mock external services
  process.env.MOCK_EXTERNAL_SERVICES = 'true'
  
  // Database setup for integration tests (if needed)
  if (process.env.RUN_INTEGRATION_TESTS === 'true') {
    console.log('📊 Setting up test database...')
    // Add database setup logic here if needed
  }
  
  console.log('✅ Course Builder System test setup complete')
}
