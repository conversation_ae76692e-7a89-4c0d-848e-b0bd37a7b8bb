import { render, RenderOptions } from '@testing-library/react'
import React, { ReactElement } from 'react'
import { Permission, UserRole } from '@/hooks/usePermissions'

/**
 * Permission Testing Utilities for Course Builder System
 * Provides comprehensive testing tools for permission-based components
 */

// Mock user data for different roles
export const mockUsers = {
  superAdmin: {
    id: 'super-admin-1',
    email: '<EMAIL>',
    role: UserRole.SUPER_ADMIN,
    legacyRole: UserRole.SUPER_ADMIN,
    institute: 'institute-1',
    branch: 'branch-1',
    permissions: Object.values(Permission),
    isActive: true,
    firstName: 'Super',
    lastName: 'Admin'
  },

  instituteAdmin: {
    id: 'institute-admin-1',
    email: '<EMAIL>',
    role: UserRole.INSTITUTE_ADMIN,
    legacyRole: UserRole.INSTITUTE_ADMIN,
    institute: 'institute-1',
    branch: null,
    permissions: [
      Permission.COURSES_CREATE,
      Permission.COURSES_READ,
      Permission.COURSES_UPDATE,
      Permission.COURSES_DELETE,
      Permission.COURSES_PUBLISH,
      Permission.LESSONS_CREATE,
      Permission.LESSONS_READ,
      Permission.LESSONS_UPDATE,
      Permission.LESSONS_DELETE,
      Permission.CONTENT_CREATE,
      Permission.CONTENT_READ,
      Permission.CONTENT_UPDATE,
      Permission.CONTENT_DELETE,
      Permission.ANALYTICS_VIEW,
      Permission.USERS_CREATE,
      Permission.USERS_READ,
      Permission.USERS_UPDATE
    ],
    isActive: true,
    firstName: 'Institute',
    lastName: 'Admin'
  },

  branchManager: {
    id: 'branch-manager-1',
    email: '<EMAIL>',
    role: UserRole.BRANCH_MANAGER,
    legacyRole: UserRole.BRANCH_MANAGER,
    institute: 'institute-1',
    branch: 'branch-1',
    permissions: [
      Permission.COURSES_CREATE,
      Permission.COURSES_READ,
      Permission.COURSES_UPDATE,
      Permission.COURSES_DELETE,
      Permission.LESSONS_CREATE,
      Permission.LESSONS_READ,
      Permission.LESSONS_UPDATE,
      Permission.LESSONS_DELETE,
      Permission.CONTENT_CREATE,
      Permission.CONTENT_READ,
      Permission.CONTENT_UPDATE,
      Permission.CONTENT_DELETE,
      Permission.ANALYTICS_VIEW
    ],
    isActive: true,
    firstName: 'Branch',
    lastName: 'Manager'
  },

  trainer: {
    id: 'trainer-1',
    email: '<EMAIL>',
    role: UserRole.TRAINER,
    legacyRole: UserRole.TRAINER,
    institute: 'institute-1',
    branch: 'branch-1',
    permissions: [
      Permission.COURSES_CREATE,
      Permission.COURSES_READ,
      Permission.COURSES_UPDATE,
      Permission.LESSONS_CREATE,
      Permission.LESSONS_READ,
      Permission.LESSONS_UPDATE,
      Permission.LESSONS_DELETE,
      Permission.CONTENT_CREATE,
      Permission.CONTENT_READ,
      Permission.CONTENT_UPDATE,
      Permission.CONTENT_DELETE,
      Permission.ASSESSMENTS_CREATE,
      Permission.ASSESSMENTS_READ,
      Permission.ASSESSMENTS_UPDATE,
      Permission.ASSESSMENTS_GRADE
    ],
    isActive: true,
    firstName: 'Course',
    lastName: 'Trainer'
  },

  staff: {
    id: 'staff-1',
    email: '<EMAIL>',
    role: UserRole.STAFF,
    legacyRole: UserRole.STAFF,
    institute: 'institute-1',
    branch: 'branch-1',
    permissions: [
      Permission.COURSES_READ,
      Permission.LESSONS_READ,
      Permission.CONTENT_READ,
      Permission.ASSESSMENTS_READ,
      Permission.ASSESSMENTS_GRADE
    ],
    isActive: true,
    firstName: 'Staff',
    lastName: 'Member'
  },

  student: {
    id: 'student-1',
    email: '<EMAIL>',
    role: UserRole.STUDENT,
    legacyRole: UserRole.STUDENT,
    institute: 'institute-1',
    branch: 'branch-1',
    permissions: [
      Permission.COURSES_READ,
      Permission.LESSONS_READ,
      Permission.CONTENT_READ,
      Permission.ASSESSMENTS_READ
    ],
    isActive: true,
    firstName: 'Test',
    lastName: 'Student'
  }
}

/**
 * Mock auth store for testing
 */
export const createMockAuthStore = (user: any = null) => ({
  user,
  isAuthenticated: !!user,
  loading: false,
  login: jest.fn(),
  logout: jest.fn(),
  refreshToken: jest.fn(),
  updateUser: jest.fn()
})

/**
 * Permission test scenarios
 */
export const permissionTestScenarios = [
  {
    name: 'Super Admin',
    user: mockUsers.superAdmin,
    expectedPermissions: Object.values(Permission),
    canManageCourses: true,
    canManageLessons: true,
    canManageContent: true,
    canManageAssessments: true,
    canViewAnalytics: true,
    canManageUsers: true
  },
  {
    name: 'Institute Admin',
    user: mockUsers.instituteAdmin,
    expectedPermissions: mockUsers.instituteAdmin.permissions,
    canManageCourses: true,
    canManageLessons: true,
    canManageContent: true,
    canManageAssessments: false,
    canViewAnalytics: true,
    canManageUsers: true
  },
  {
    name: 'Branch Manager',
    user: mockUsers.branchManager,
    expectedPermissions: mockUsers.branchManager.permissions,
    canManageCourses: true,
    canManageLessons: true,
    canManageContent: true,
    canManageAssessments: false,
    canViewAnalytics: true,
    canManageUsers: false
  },
  {
    name: 'Trainer',
    user: mockUsers.trainer,
    expectedPermissions: mockUsers.trainer.permissions,
    canManageCourses: true,
    canManageLessons: true,
    canManageContent: true,
    canManageAssessments: true,
    canViewAnalytics: false,
    canManageUsers: false
  },
  {
    name: 'Staff',
    user: mockUsers.staff,
    expectedPermissions: mockUsers.staff.permissions,
    canManageCourses: false,
    canManageLessons: false,
    canManageContent: false,
    canManageAssessments: false,
    canViewAnalytics: false,
    canManageUsers: false
  },
  {
    name: 'Student',
    user: mockUsers.student,
    expectedPermissions: mockUsers.student.permissions,
    canManageCourses: false,
    canManageLessons: false,
    canManageContent: false,
    canManageAssessments: false,
    canViewAnalytics: false,
    canManageUsers: false
  }
]

/**
 * Test helper for permission-based component rendering
 */
export const renderWithPermissions = (
  ui: ReactElement,
  user: any = null,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  // Mock the useAuthStore hook
  jest.mock('@/stores/auth/authStore', () => ({
    useAuthStore: () => createMockAuthStore(user)
  }))

  return render(ui, options)
}

/**
 * Test helper for checking permission gates
 */
export const testPermissionGate = (
  Component: React.ComponentType,
  permission: Permission | Permission[],
  testCases: Array<{
    user: any
    shouldRender: boolean
    description: string
  }>
) => {
  testCases.forEach(({ user, shouldRender, description }) => {
    it(description, () => {
      const { container } = renderWithPermissions(
        React.createElement(Component),
        user
      )
      
      if (shouldRender) {
        expect(container.firstChild).not.toBeNull()
      } else {
        expect(container.firstChild).toBeNull()
      }
    })
  })
}

/**
 * Test helper for role-based access
 */
export const testRoleAccess = (
  Component: React.ComponentType,
  allowedRoles: UserRole[],
  testUsers: any[] = Object.values(mockUsers)
) => {
  testUsers.forEach(user => {
    const shouldHaveAccess = allowedRoles.includes(user.role)
    
    it(`${shouldHaveAccess ? 'allows' : 'denies'} access for ${user.role}`, () => {
      const { container } = renderWithPermissions(
        React.createElement(Component),
        user
      )
      
      if (shouldHaveAccess) {
        expect(container.firstChild).not.toBeNull()
      } else {
        expect(container.firstChild).toBeNull()
      }
    })
  })
}

/**
 * Test helper for tenant isolation
 */
export const testTenantIsolation = (
  Component: React.ComponentType,
  testCases: Array<{
    user: any
    institute?: string
    branch?: string
    shouldRender: boolean
    description: string
  }>
) => {
  testCases.forEach(({ user, institute, branch, shouldRender, description }) => {
    it(description, () => {
      const ComponentWithProps = () => React.createElement(Component, { institute, branch })
      
      const { container } = renderWithPermissions(
        React.createElement(ComponentWithProps),
        user
      )
      
      if (shouldRender) {
        expect(container.firstChild).not.toBeNull()
      } else {
        expect(container.firstChild).toBeNull()
      }
    })
  })
}

/**
 * Permission matrix test generator
 */
export const generatePermissionTests = (
  permissions: Permission[],
  roles: UserRole[]
) => {
  const tests: Array<{
    permission: Permission
    role: UserRole
    expected: boolean
  }> = []

  permissions.forEach(permission => {
    roles.forEach(role => {
      const user = mockUsers[role.toLowerCase() as keyof typeof mockUsers]
      const expected = user?.permissions?.includes(permission) || false
      
      tests.push({
        permission,
        role,
        expected
      })
    })
  })

  return tests
}

/**
 * Bulk permission testing utility
 */
export const testPermissionMatrix = (
  permissions: Permission[],
  roles: UserRole[]
) => {
  const tests = generatePermissionTests(permissions, roles)
  
  describe('Permission Matrix Tests', () => {
    tests.forEach(({ permission, role, expected }) => {
      it(`${role} ${expected ? 'should have' : 'should not have'} ${permission}`, () => {
        const user = mockUsers[role.toLowerCase() as keyof typeof mockUsers]
        const hasPermission = user?.permissions?.includes(permission) || false
        expect(hasPermission).toBe(expected)
      })
    })
  })
}

/**
 * Course-specific permission tests
 */
export const coursePermissionTests = {
  create: [
    { user: mockUsers.superAdmin, expected: true },
    { user: mockUsers.instituteAdmin, expected: true },
    { user: mockUsers.branchManager, expected: true },
    { user: mockUsers.trainer, expected: true },
    { user: mockUsers.staff, expected: false },
    { user: mockUsers.student, expected: false }
  ],
  
  read: [
    { user: mockUsers.superAdmin, expected: true },
    { user: mockUsers.instituteAdmin, expected: true },
    { user: mockUsers.branchManager, expected: true },
    { user: mockUsers.trainer, expected: true },
    { user: mockUsers.staff, expected: true },
    { user: mockUsers.student, expected: true }
  ],
  
  update: [
    { user: mockUsers.superAdmin, expected: true },
    { user: mockUsers.instituteAdmin, expected: true },
    { user: mockUsers.branchManager, expected: true },
    { user: mockUsers.trainer, expected: true },
    { user: mockUsers.staff, expected: false },
    { user: mockUsers.student, expected: false }
  ],
  
  delete: [
    { user: mockUsers.superAdmin, expected: true },
    { user: mockUsers.instituteAdmin, expected: true },
    { user: mockUsers.branchManager, expected: true },
    { user: mockUsers.trainer, expected: false },
    { user: mockUsers.staff, expected: false },
    { user: mockUsers.student, expected: false }
  ],
  
  publish: [
    { user: mockUsers.superAdmin, expected: true },
    { user: mockUsers.instituteAdmin, expected: true },
    { user: mockUsers.branchManager, expected: true },
    { user: mockUsers.trainer, expected: false },
    { user: mockUsers.staff, expected: false },
    { user: mockUsers.student, expected: false }
  ]
}

export default {
  mockUsers,
  createMockAuthStore,
  permissionTestScenarios,
  renderWithPermissions,
  testPermissionGate,
  testRoleAccess,
  testTenantIsolation,
  generatePermissionTests,
  testPermissionMatrix,
  coursePermissionTests
}
