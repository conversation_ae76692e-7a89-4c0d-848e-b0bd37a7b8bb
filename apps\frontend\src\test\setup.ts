// Jest setup file for Course Builder System testing
import '@testing-library/jest-dom'
import { TextEncoder, TextDecoder } from 'util'

// Polyfills for Node.js environment
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/admin/course-builder',
  useSearchParams: () => new URLSearchParams(),
}))

// Mock Next.js image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />
  },
}))

// <PERSON><PERSON> stores
jest.mock('@/stores/admin/course-builder', () => ({
  useCourseBuilderStore: () => ({
    courses: [],
    currentCourse: null,
    loading: false,
    pagination: null,
    lessons: [],
    currentLesson: null,
    lessonsLoading: false,
    lessonContent: [],
    contentLoading: false,
    fetchCourses: jest.fn(),
    fetchCourse: jest.fn(),
    createCourse: jest.fn(),
    updateCourse: jest.fn(),
    deleteCourse: jest.fn(),
    fetchLessons: jest.fn(),
    fetchLesson: jest.fn(),
    createLesson: jest.fn(),
    updateLesson: jest.fn(),
    deleteLesson: jest.fn(),
    reorderLessons: jest.fn(),
    fetchLessonContent: jest.fn(),
    createLessonContent: jest.fn(),
    updateLessonContent: jest.fn(),
    deleteLessonContent: jest.fn(),
    reorderLessonContent: jest.fn(),
    clearCurrentCourse: jest.fn(),
    clearCurrentLesson: jest.fn(),
    setLoading: jest.fn(),
  }),
}))

// Mock API client
jest.mock('@/lib/api', () => ({
  api: {
    get: jest.fn(),
    post: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}))

// Mock toast notifications
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}))

// Mock file upload
global.File = class MockFile {
  name: string
  size: number
  type: string
  lastModified: number

  constructor(parts: any[], filename: string, properties?: any) {
    this.name = filename
    this.size = parts.reduce((acc, part) => acc + (part.length || 0), 0)
    this.type = properties?.type || 'text/plain'
    this.lastModified = Date.now()
  }
} as any

global.FileReader = class MockFileReader {
  result: string | ArrayBuffer | null = null
  error: any = null
  readyState: number = 0
  onload: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null
  onerror: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null

  readAsDataURL(file: File) {
    this.readyState = 2
    this.result = `data:${file.type};base64,mock-base64-data`
    if (this.onload) {
      this.onload({} as ProgressEvent<FileReader>)
    }
  }

  readAsText(file: File) {
    this.readyState = 2
    this.result = 'mock file content'
    if (this.onload) {
      this.onload({} as ProgressEvent<FileReader>)
    }
  }
} as any

// Mock IntersectionObserver
global.IntersectionObserver = class MockIntersectionObserver {
  constructor(callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {}
  observe(target: Element) {}
  unobserve(target: Element) {}
  disconnect() {}
} as any

// Mock ResizeObserver
global.ResizeObserver = class MockResizeObserver {
  constructor(callback: ResizeObserverCallback) {}
  observe(target: Element) {}
  unobserve(target: Element) {}
  disconnect() {}
} as any

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
})

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
})

// Mock URL.createObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  writable: true,
  value: jest.fn(() => 'mock-object-url'),
})

Object.defineProperty(URL, 'revokeObjectURL', {
  writable: true,
  value: jest.fn(),
})

// Console error suppression for known issues
const originalError = console.error
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is no longer supported') ||
        args[0].includes('Warning: An invalid form control'))
    ) {
      return
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
})

// Global test utilities
export const mockCourse = {
  id: 'test-course-1',
  title: 'Test Course',
  slug: 'test-course',
  description: 'A test course for unit testing',
  short_description: 'Test course description',
  difficulty_level: 'beginner' as const,
  estimated_duration: 10,
  duration_unit: 'hours' as const,
  pricing_type: 'free' as const,
  price_amount: 0,
  currency: 'USD',
  visibility: 'institute' as const,
  status: 'draft' as const,
  institute_id: 'test-institute',
  instructor_id: 'test-instructor',
  prerequisites: [],
  learning_outcomes: ['Learn testing', 'Understand mocking'],
  tags: ['test', 'mock'],
  version: 1,
  createdAt: '2025-01-08T00:00:00Z',
  updatedAt: '2025-01-08T00:00:00Z',
  stats: {
    lessonsCount: 5,
    enrollmentsCount: 10,
  },
}

export const mockLesson = {
  id: 'test-lesson-1',
  course_id: 'test-course-1',
  title: 'Test Lesson',
  description: 'A test lesson',
  order_index: 0,
  estimated_duration: 60,
  learning_objectives: ['Understand testing'],
  is_mandatory: true,
  completion_criteria: {
    type: 'manual' as const,
  },
  createdAt: '2025-01-08T00:00:00Z',
  updatedAt: '2025-01-08T00:00:00Z',
  contentCount: 3,
}

export const mockLessonContent = {
  id: 'test-content-1',
  lesson_id: 'test-lesson-1',
  content_type: 'text' as const,
  title: 'Test Content',
  description: 'Test content description',
  order_index: 0,
  content_data: { text_content: 'This is test content' },
  duration: 0,
  is_downloadable: false,
  createdAt: '2025-01-08T00:00:00Z',
  updatedAt: '2025-01-08T00:00:00Z',
}
