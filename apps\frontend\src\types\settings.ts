/**
 * Settings Types - Frontend
 * 
 * Type definitions for the platform settings system (frontend)
 */

// Setting data types
export type SettingType = 'string' | 'number' | 'boolean' | 'json' | 'url' | 'email' | 'textarea' | 'upload'

// Setting categories
export type SettingCategory = 
  | 'platform' 
  | 'email' 
  | 'security' 
  | 'storage' 
  | 'payment' 
  | 'notification' 
  | 'integration' 
  | 'feature'

// Validation rules for settings
export interface ValidationRules {
  min_length?: number
  max_length?: number
  min_value?: number
  max_value?: number
  pattern?: string
}

// Setting model
export interface Setting {
  id: string
  key: string
  value: string
  description?: string
  category: SettingCategory
  type: SettingType
  is_public: boolean
  is_required?: boolean
  validation_rules?: ValidationRules
  upload?: string | { id: string; url: string; filename: string } // For file uploads
  createdAt: string
  updatedAt: string
}

// Setting creation data
export interface SettingCreationData {
  key: string
  value: string
  description?: string
  category: SettingCategory
  type: SettingType
  is_public?: boolean
  is_required?: boolean
  validation_rules?: ValidationRules
}

// Setting update data
export interface SettingUpdateData {
  value?: string
  description?: string
  category?: SettingCategory
  type?: SettingType
  is_public?: boolean
  is_required?: boolean
  validation_rules?: ValidationRules
}

// Settings filters
export interface SettingsFilters {
  category?: SettingCategory
  type?: SettingType
  is_public?: boolean
  search?: string
}

// Settings response with pagination
export interface SettingsResponse {
  docs: Setting[]
  totalDocs: number
  limit: number
  totalPages: number
  page: number
  pagingCounter: number
  hasPrevPage: boolean
  hasNextPage: boolean
  prevPage: number | null
  nextPage: number | null
}

// Bulk update settings request
export interface BulkUpdateSettingsRequest {
  settings: SettingCreationData[]
}

// Bulk update settings response
export interface BulkUpdateSettingsResponse {
  results: Array<{
    key: string
    operation: 'created' | 'updated'
    id: string
  }>
  errors: Array<{
    key: string
    error: string
  }>
  success: number
  failed: number
}

// Form data for platform settings
export interface PlatformSettingsFormData {
  platform_name: string
  platform_url: string
  support_email: string
  platform_address: string
  platform_tagline: string
  platform_logo: File | null
  platform_favicon: File | null
  maintenance_mode: boolean
  allow_registration: boolean
  require_email_verification: boolean
  max_institutes_per_plan: number
}

// Setting field configuration for dynamic forms
export interface SettingFieldConfig {
  key: string
  label: string
  description?: string
  type: SettingType
  category: SettingCategory
  required?: boolean
  placeholder?: string
  validation?: ValidationRules
  options?: Array<{ label: string; value: string }> // For select fields
}

// Platform configuration object
export interface PlatformConfig {
  platform_name: string
  platform_url: string
  support_email: string
  maintenance_mode: boolean
  allow_registration: boolean
  require_email_verification: boolean
  max_institutes_per_plan: number
  [key: string]: string | number | boolean
}

// Setting categories with metadata
export interface SettingCategoryInfo {
  key: SettingCategory
  label: string
  description: string
  icon?: string
  color?: string
}

// Default setting categories
export const SETTING_CATEGORIES: SettingCategoryInfo[] = [
  {
    key: 'platform',
    label: 'Platform',
    description: 'Core platform settings and configuration',
    icon: 'Settings',
    color: 'blue',
  },
  {
    key: 'email',
    label: 'Email',
    description: 'Email and SMTP configuration',
    icon: 'Mail',
    color: 'green',
  },
  {
    key: 'security',
    label: 'Security',
    description: 'Security and authentication settings',
    icon: 'Shield',
    color: 'red',
  },
  {
    key: 'storage',
    label: 'Storage',
    description: 'File storage and CDN configuration',
    icon: 'Database',
    color: 'purple',
  },
  {
    key: 'payment',
    label: 'Payment',
    description: 'Payment gateway and billing settings',
    icon: 'CreditCard',
    color: 'yellow',
  },
  {
    key: 'notification',
    label: 'Notification',
    description: 'Notification and messaging settings',
    icon: 'Bell',
    color: 'orange',
  },
  {
    key: 'integration',
    label: 'Integration',
    description: 'Third-party integrations and APIs',
    icon: 'Link',
    color: 'indigo',
  },
  {
    key: 'feature',
    label: 'Feature',
    description: 'Feature flags and experimental settings',
    icon: 'Flag',
    color: 'pink',
  },
]

// Setting types with metadata
export interface SettingTypeInfo {
  key: SettingType
  label: string
  description: string
  validation?: (value: string) => boolean
}

export const SETTING_TYPES: SettingTypeInfo[] = [
  {
    key: 'string',
    label: 'Text',
    description: 'Plain text value',
  },
  {
    key: 'number',
    label: 'Number',
    description: 'Numeric value',
    validation: (value) => !isNaN(Number(value)),
  },
  {
    key: 'boolean',
    label: 'Boolean',
    description: 'True/false value',
    validation: (value) => ['true', 'false', '1', '0'].includes(value.toLowerCase()),
  },
  {
    key: 'json',
    label: 'JSON',
    description: 'JSON object or array',
    validation: (value) => {
      try {
        JSON.parse(value)
        return true
      } catch {
        return false
      }
    },
  },
  {
    key: 'url',
    label: 'URL',
    description: 'Valid URL',
    validation: (value) => {
      try {
        new URL(value)
        return true
      } catch {
        return false
      }
    },
  },
  {
    key: 'email',
    label: 'Email',
    description: 'Valid email address',
    validation: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
  },
  {
    key: 'textarea',
    label: 'Textarea',
    description: 'Multi-line text value',
  },
  {
    key: 'upload',
    label: 'File Upload',
    description: 'File upload (image, document, etc.)',
  },
]

// Helper functions
export const settingsUtils = {
  /**
   * Convert setting value to appropriate type
   */
  convertValue(value: string, type: SettingType): any {
    switch (type) {
      case 'number':
        return Number(value)
      case 'boolean':
        return value.toLowerCase() === 'true' || value === '1'
      case 'json':
        try {
          return JSON.parse(value)
        } catch {
          return null
        }
      default:
        return value
    }
  },

  /**
   * Get category info by key
   */
  getCategoryInfo(category: SettingCategory): SettingCategoryInfo | undefined {
    return SETTING_CATEGORIES.find(cat => cat.key === category)
  },

  /**
   * Get type info by key
   */
  getTypeInfo(type: SettingType): SettingTypeInfo | undefined {
    return SETTING_TYPES.find(t => t.key === type)
  },

  /**
   * Validate setting value based on type
   */
  validateValue(value: string, type: SettingType): boolean {
    const typeInfo = this.getTypeInfo(type)
    return typeInfo?.validation ? typeInfo.validation(value) : true
  },

  /**
   * Format setting value for display
   */
  formatValue(value: string, type: SettingType): string {
    switch (type) {
      case 'boolean':
        return value.toLowerCase() === 'true' || value === '1' ? 'Yes' : 'No'
      case 'json':
        try {
          return JSON.stringify(JSON.parse(value), null, 2)
        } catch {
          return value
        }
      default:
        return value
    }
  },
}
