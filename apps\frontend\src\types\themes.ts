// Theme Management Types for LMS System

export interface Theme {
  id: string
  name: string
  description: string
  category: ThemeCategory
  type: ThemeType
  version: string
  author: string
  thumbnail: string
  previewImages: string[]
  features: ThemeFeature[]
  isActive: boolean
  isDefault: boolean
  isPremium: boolean
  supportedFeatures: SupportedFeature[]
  customization: ThemeCustomization
  createdAt: string
  updatedAt: string
  downloadCount?: number
  rating?: {
    average: number
    count: number
  }
  tags: string[]
}

export interface ThemeCategory {
  id: string
  name: string
  description: string
  icon: string
  order: number
}

export interface ThemeFeature {
  id: string
  name: string
  description: string
  enabled: boolean
  configurable: boolean
  config?: Record<string, any>
}

export interface ThemeCustomization {
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    text: string
    [key: string]: string
  }
  fonts: {
    heading: string
    body: string
    [key: string]: string
  }
  layout: {
    headerStyle: 'fixed' | 'static' | 'transparent'
    footerStyle: 'minimal' | 'detailed' | 'hidden'
    sidebarPosition: 'left' | 'right' | 'none'
    [key: string]: any
  }
  components: Record<string, any>
}

export interface SupportedFeature {
  id: string
  name: string
  description: string
  required: boolean
}

export type ThemeType = 'platform' | 'institute'

export type ThemeStatus = 'active' | 'inactive' | 'draft'

export type ThemeSortField = 'name' | 'createdAt' | 'updatedAt' | 'downloadCount' | 'rating'

export type SortOrder = 'asc' | 'desc'

// Platform Theme Types
export interface PlatformTheme extends Theme {
  type: 'platform'
  landingPageSections: LandingPageSection[]
  heroSection: HeroSectionConfig
  featuresSection: FeaturesSectionConfig
  aboutSection: AboutSectionConfig
  contactSection: ContactSectionConfig
}

export interface LandingPageSection {
  id: string
  name: string
  type: SectionType
  order: number
  enabled: boolean
  config: Record<string, any>
}

export type SectionType = 
  | 'hero' 
  | 'features' 
  | 'about' 
  | 'testimonials' 
  | 'pricing' 
  | 'contact' 
  | 'cta' 
  | 'stats'

export interface HeroSectionConfig {
  title: string
  subtitle: string
  backgroundImage: string
  ctaButton: {
    text: string
    link: string
    style: 'primary' | 'secondary'
  }
  layout: 'centered' | 'left-aligned' | 'right-aligned'
}

export interface FeaturesSectionConfig {
  title: string
  subtitle: string
  features: Array<{
    icon: string
    title: string
    description: string
  }>
  layout: 'grid' | 'list' | 'carousel'
}

export interface AboutSectionConfig {
  title: string
  content: string
  image: string
  layout: 'side-by-side' | 'stacked'
}

export interface ContactSectionConfig {
  title: string
  subtitle: string
  showForm: boolean
  showMap: boolean
  contactInfo: {
    email: string
    phone: string
    address: string
  }
}

// Institute Theme Types
export interface InstituteTheme extends Theme {
  type: 'institute'
  ecommerceFeatures: EcommerceFeature[]
  courseDisplayOptions: CourseDisplayOption[]
  marketplaceLayout: MarketplaceLayout
  checkoutProcess: CheckoutProcess
  liveClassIntegration: LiveClassIntegration
}

export interface EcommerceFeature {
  id: string
  name: string
  description: string
  enabled: boolean
  config: Record<string, any>
}

export interface CourseDisplayOption {
  id: string
  name: string
  layout: 'grid' | 'list' | 'carousel'
  itemsPerPage: number
  showFilters: boolean
  showSorting: boolean
  showSearch: boolean
}

export interface MarketplaceLayout {
  headerStyle: 'minimal' | 'detailed' | 'mega-menu'
  categoryNavigation: 'sidebar' | 'top-bar' | 'dropdown'
  productGrid: 'compact' | 'detailed' | 'card'
  filterPosition: 'left' | 'right' | 'top'
}

export interface CheckoutProcess {
  steps: CheckoutStep[]
  paymentMethods: string[]
  guestCheckout: boolean
  socialLogin: boolean
}

export interface CheckoutStep {
  id: string
  name: string
  required: boolean
  order: number
}

export interface LiveClassIntegration {
  enabled: boolean
  displayStyle: 'embedded' | 'popup' | 'redirect'
  bookingSystem: boolean
  calendarIntegration: boolean
}

// Form Types
export interface ThemeFilters {
  search?: string
  category?: string
  type?: ThemeType
  status?: ThemeStatus
  features?: string[]
  tags?: string[]
  sortField?: ThemeSortField
  sortOrder?: SortOrder
  page?: number
  limit?: number
}

export interface CreateThemeRequest {
  name: string
  description: string
  category: string
  type: ThemeType
  features: string[]
  customization: Partial<ThemeCustomization>
  tags: string[]
}

export interface UpdateThemeRequest {
  id: string
  name?: string
  description?: string
  category?: string
  features?: string[]
  customization?: Partial<ThemeCustomization>
  tags?: string[]
  isActive?: boolean
}

export interface ApplyThemeRequest {
  themeId: string
  targetType: 'platform' | 'institute'
  targetId?: string // Institute ID for institute themes
  customization?: Partial<ThemeCustomization>
}

// API Response Types
export interface ThemesResponse {
  themes: Theme[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface ThemeCategoriesResponse {
  categories: ThemeCategory[]
}

// Store State Types
export interface ThemesState {
  // Data
  platformThemes: PlatformTheme[]
  instituteThemes: InstituteTheme[]
  categories: ThemeCategory[]
  currentPlatformTheme: PlatformTheme | null
  
  // UI State
  loading: boolean
  error: string | null
  selectedTheme: Theme | null
  previewTheme: Theme | null
  
  // Filters and Pagination
  filters: ThemeFilters
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  
  // View State
  viewMode: 'grid' | 'list'
  showPreview: boolean
  activeTab: 'platform' | 'institute'
}

// Component Props Types
export interface ThemePreviewProps {
  theme: Theme
  onSelect?: (theme: Theme) => void
  onPreview?: (theme: Theme) => void
  isSelected?: boolean
  isActive?: boolean
  showActions?: boolean
}

export interface ThemeSelectorProps {
  type: ThemeType
  onThemeSelect: (theme: Theme) => void
  selectedTheme?: Theme
  showFilters?: boolean
}

export interface ThemeCustomizerProps {
  theme: Theme
  customization: Partial<ThemeCustomization>
  onChange: (customization: Partial<ThemeCustomization>) => void
}

// Constants
export const THEME_CATEGORIES = {
  MODERN: 'modern',
  CLASSIC: 'classic',
  MINIMAL: 'minimal',
  CORPORATE: 'corporate',
  CREATIVE: 'creative',
  ECOMMERCE: 'ecommerce'
} as const

export const SUPPORTED_FEATURES = {
  RESPONSIVE: 'responsive',
  DARK_MODE: 'dark_mode',
  RTL_SUPPORT: 'rtl_support',
  MULTI_LANGUAGE: 'multi_language',
  SEO_OPTIMIZED: 'seo_optimized',
  ACCESSIBILITY: 'accessibility',
  COURSE_MARKETPLACE: 'course_marketplace',
  LIVE_CLASSES: 'live_classes',
  PAYMENT_GATEWAY: 'payment_gateway',
  SOCIAL_LOGIN: 'social_login'
} as const

export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 12,
  total: 0,
  totalPages: 0
} as const
