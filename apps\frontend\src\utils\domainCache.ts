/**
 * Domain Resolution Caching Utility
 * 
 * Implements localStorage-based caching for domain resolution API calls
 * with automatic expiration and background processing capabilities.
 */

interface CachedDomainData {
  domain: string
  timestamp: number
  data: {
    success: boolean
    institute?: {
      id: number
      name: string
      slug: string
      tagline?: string
      description?: string
      email?: string
      phone?: string
      website?: string
      theme?: {
        id: number
        name: string
        slug: string
        colors?: {
          primary: string
          secondary: string
          accent: string
          background: string
          foreground: string
        }
        fonts?: {
          heading: string
          body: string
        }
      }
    }
    error?: string
  }
}

// Cache configuration
const CACHE_EXPIRY_TIME = 3 * 60 * 60 * 1000 // 3 hours in milliseconds (10,800,000 ms)
const CACHE_KEY_PREFIX = 'domain_cache_'

/**
 * Generates a cache key for a specific domain
 */
function getCacheKey(domain: string): string {
  return `${CACHE_KEY_PREFIX}${domain}`
}

/**
 * Checks if cached data is still valid (not expired)
 */
function isCacheValid(cachedData: CachedDomainData): boolean {
  const now = Date.now()
  const age = now - cachedData.timestamp
  return age < CACHE_EXPIRY_TIME
}

/**
 * Validates the structure of cached data
 */
function isCacheDataValid(data: any): data is CachedDomainData {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.domain === 'string' &&
    typeof data.timestamp === 'number' &&
    data.data &&
    typeof data.data === 'object' &&
    typeof data.data.success === 'boolean'
  )
}

/**
 * Retrieves cached domain data from localStorage
 */
function getCachedDomainData(domain: string): CachedDomainData | null {
  try {
    const cacheKey = getCacheKey(domain)
    const cachedString = localStorage.getItem(cacheKey)
    
    if (!cachedString) {
      console.log(`🔍 No cache found for domain: ${domain}`)
      return null
    }

    const cachedData = JSON.parse(cachedString)
    
    // Validate cache data structure
    if (!isCacheDataValid(cachedData)) {
      console.warn(`⚠️ Invalid cache data structure for domain: ${domain}`)
      removeCachedDomainData(domain) // Clean up corrupted cache
      return null
    }

    // Check if cache is expired
    if (!isCacheValid(cachedData)) {
      console.log(`⏰ Cache expired for domain: ${domain}`)
      removeCachedDomainData(domain) // Clean up expired cache
      return null
    }

    console.log(`✅ Valid cache found for domain: ${domain}`)
    return cachedData
  } catch (error) {
    console.error(`❌ Error reading cache for domain ${domain}:`, error)
    removeCachedDomainData(domain) // Clean up corrupted cache
    return null
  }
}

/**
 * Stores domain data in localStorage cache
 */
function setCachedDomainData(domain: string, data: CachedDomainData['data']): void {
  try {
    const cacheKey = getCacheKey(domain)
    const cacheData: CachedDomainData = {
      domain,
      timestamp: Date.now(),
      data
    }
    
    localStorage.setItem(cacheKey, JSON.stringify(cacheData))
    console.log(`💾 Cached domain data for: ${domain}`)
  } catch (error) {
    console.error(`❌ Error caching domain data for ${domain}:`, error)
  }
}

/**
 * Removes cached domain data from localStorage
 */
function removeCachedDomainData(domain: string): void {
  try {
    const cacheKey = getCacheKey(domain)
    localStorage.removeItem(cacheKey)
    console.log(`🗑️ Removed cache for domain: ${domain}`)
  } catch (error) {
    console.error(`❌ Error removing cache for domain ${domain}:`, error)
  }
}

/**
 * Cleans up all expired cache entries
 */
function cleanupExpiredCache(): void {
  try {
    const keysToRemove: string[] = []
    
    // Iterate through all localStorage keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(CACHE_KEY_PREFIX)) {
        try {
          const cachedString = localStorage.getItem(key)
          if (cachedString) {
            const cachedData = JSON.parse(cachedString)
            if (isCacheDataValid(cachedData) && !isCacheValid(cachedData)) {
              keysToRemove.push(key)
            }
          }
        } catch (error) {
          // If we can't parse the data, mark it for removal
          keysToRemove.push(key)
        }
      }
    }
    
    // Remove expired/corrupted entries
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      console.log(`🧹 Cleaned up expired cache: ${key}`)
    })
    
    if (keysToRemove.length > 0) {
      console.log(`✨ Cache cleanup completed: ${keysToRemove.length} entries removed`)
    }
  } catch (error) {
    console.error('❌ Error during cache cleanup:', error)
  }
}

/**
 * Fetches domain data from API
 */
async function fetchDomainDataFromAPI(domain: string): Promise<CachedDomainData['data']> {
  console.log(`🌐 Fetching domain data from API: ${domain}`)
  
  const response = await fetch(`/api/public/domain/resolve?domain=${domain}`)
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status} ${response.statusText}`)
  }
  
  const data = await response.json()
  console.log(`📡 API response received for domain: ${domain}`)
  
  return data
}

/**
 * Main function to get domain data with caching
 * This function implements the complete caching strategy
 */
export async function getDomainData(domain: string): Promise<CachedDomainData['data']> {
  console.log(`🔍 Getting domain data for: ${domain}`)
  
  // Step 1: Check for valid cached data
  const cachedData = getCachedDomainData(domain)
  if (cachedData) {
    console.log(`⚡ Using cached data for domain: ${domain}`)
    return cachedData.data
  }
  
  // Step 2: No valid cache found, fetch from API
  try {
    const apiData = await fetchDomainDataFromAPI(domain)
    
    // Step 3: Cache the API response
    setCachedDomainData(domain, apiData)
    
    return apiData
  } catch (error) {
    console.error(`❌ Failed to fetch domain data for ${domain}:`, error)
    
    // Return error response
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch domain data'
    }
  }
}

/**
 * Background cache management
 * Runs cleanup and maintenance tasks without blocking UI
 */
export function initializeCacheManagement(): void {
  // Run initial cleanup
  setTimeout(() => {
    cleanupExpiredCache()
  }, 1000) // Delay to avoid blocking initial page load
  
  // Schedule periodic cleanup (every hour)
  setInterval(() => {
    cleanupExpiredCache()
  }, 60 * 60 * 1000) // 1 hour
  
  console.log('🔧 Domain cache management initialized')
}

/**
 * Utility function to manually clear all domain cache
 * Useful for debugging or forced refresh
 */
export function clearAllDomainCache(): void {
  try {
    const keysToRemove: string[] = []
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(CACHE_KEY_PREFIX)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    console.log(`🧹 Cleared all domain cache: ${keysToRemove.length} entries removed`)
  } catch (error) {
    console.error('❌ Error clearing domain cache:', error)
  }
}

/**
 * Get cache statistics for debugging
 */
export function getCacheStats(): { totalEntries: number; validEntries: number; expiredEntries: number } {
  let totalEntries = 0
  let validEntries = 0
  let expiredEntries = 0
  
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(CACHE_KEY_PREFIX)) {
        totalEntries++
        try {
          const cachedString = localStorage.getItem(key)
          if (cachedString) {
            const cachedData = JSON.parse(cachedString)
            if (isCacheDataValid(cachedData)) {
              if (isCacheValid(cachedData)) {
                validEntries++
              } else {
                expiredEntries++
              }
            }
          }
        } catch (error) {
          expiredEntries++
        }
      }
    }
  } catch (error) {
    console.error('❌ Error getting cache stats:', error)
  }
  
  return { totalEntries, validEntries, expiredEntries }
}
