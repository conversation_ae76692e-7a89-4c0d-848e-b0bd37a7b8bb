import { UserType } from '@/stores/sidebar/useSidebarStore'

// Header configuration types
export interface HeaderConfig {
  showBreadcrumbs: boolean
  showSearch: boolean
  showNotifications: boolean
  showProfile: boolean
  showThemeToggle: boolean
  showFullscreenToggle: boolean
  showTimeDisplay: boolean
  showHelpButton: boolean
}

// Default header configurations for different user types
export const defaultHeaderConfigs: Record<UserType, HeaderConfig> = {
  super_admin: {
    showBreadcrumbs: true,
    showSearch: true,
    showNotifications: true,
    showProfile: true,
    showThemeToggle: true,
    showFullscreenToggle: true,
    showTimeDisplay: true,
    showHelpButton: true
  },
  institute_admin: {
    showBreadcrumbs: true,
    showSearch: true,
    showNotifications: true,
    showProfile: true,
    showThemeToggle: false,
    showFullscreenToggle: false,
    showTimeDisplay: false,
    showHelpButton: true
  },
  student: {
    showBreadcrumbs: false,
    showSearch: true,
    showNotifications: true,
    showProfile: true,
    showThemeToggle: false,
    showFullscreenToggle: false,
    showTimeDisplay: false,
    showHelpButton: true
  }
}

// Header theme configurations
export interface HeaderTheme {
  backgroundColor: string
  textColor: string
  borderColor: string
  hoverColor: string
  activeColor: string
}

export const headerThemes: Record<string, HeaderTheme> = {
  light: {
    backgroundColor: 'bg-white',
    textColor: 'text-gray-900',
    borderColor: 'border-gray-200',
    hoverColor: 'hover:bg-gray-100',
    activeColor: 'bg-blue-50 text-blue-700'
  },
  dark: {
    backgroundColor: 'bg-gray-900',
    textColor: 'text-white',
    borderColor: 'border-gray-700',
    hoverColor: 'hover:bg-gray-800',
    activeColor: 'bg-blue-900 text-blue-300'
  },
  blue: {
    backgroundColor: 'bg-blue-600',
    textColor: 'text-white',
    borderColor: 'border-blue-500',
    hoverColor: 'hover:bg-blue-700',
    activeColor: 'bg-blue-500 text-white'
  }
}

// Notification types and configurations
export interface NotificationConfig {
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
  actionUrl?: string
  actionLabel?: string
}

export const mockNotifications: NotificationConfig[] = [
  {
    type: 'info',
    title: 'New Feature Available',
    message: 'Check out the new analytics dashboard',
    timestamp: '2 minutes ago',
    read: false,
    actionUrl: '/analytics',
    actionLabel: 'View'
  },
  {
    type: 'success',
    title: 'Backup Completed',
    message: 'Daily backup completed successfully',
    timestamp: '1 hour ago',
    read: false
  },
  {
    type: 'warning',
    title: 'Storage Warning',
    message: 'Storage is 85% full',
    timestamp: '3 hours ago',
    read: true,
    actionUrl: '/settings/storage',
    actionLabel: 'Manage'
  },
  {
    type: 'error',
    title: 'Failed Login Attempt',
    message: 'Suspicious login attempt detected',
    timestamp: '1 day ago',
    read: true,
    actionUrl: '/security',
    actionLabel: 'Review'
  }
]

// User profile menu items
export interface ProfileMenuItem {
  id: string
  label: string
  icon: string
  href?: string
  action?: () => void
  divider?: boolean
  danger?: boolean
}

export const getProfileMenuItems = (userType: UserType): ProfileMenuItem[] => {
  const baseItems: ProfileMenuItem[] = [
    {
      id: 'profile',
      label: 'Profile Settings',
      icon: 'User',
      href: `/${userType.replace('_', '-')}/profile`
    },
    {
      id: 'preferences',
      label: 'Preferences',
      icon: 'Settings',
      href: `/${userType.replace('_', '-')}/settings/preferences`
    },
    {
      id: 'help',
      label: 'Help & Support',
      icon: 'HelpCircle',
      href: '/help'
    }
  ]

  // Add user-type specific items
  if (userType === 'super_admin') {
    baseItems.splice(2, 0, {
      id: 'system-settings',
      label: 'System Settings',
      icon: 'Settings',
      href: '/super-admin/settings'
    })
  }

  // Add logout item
  baseItems.push(
    { id: 'divider', label: '', icon: '', divider: true },
    {
      id: 'logout',
      label: 'Sign Out',
      icon: 'LogOut',
      danger: true
    }
  )

  return baseItems
}

// Keyboard shortcuts
export interface KeyboardShortcut {
  key: string
  description: string
  action: () => void
}

export const headerKeyboardShortcuts: KeyboardShortcut[] = [
  {
    key: 'Cmd+K',
    description: 'Open search',
    action: () => {
      // This would be implemented to open search modal
      console.log('Opening search...')
    }
  },
  {
    key: 'Cmd+/',
    description: 'Show keyboard shortcuts',
    action: () => {
      // This would be implemented to show shortcuts modal
      console.log('Showing shortcuts...')
    }
  },
  {
    key: 'Cmd+Shift+N',
    description: 'Open notifications',
    action: () => {
      // This would be implemented to open notifications
      console.log('Opening notifications...')
    }
  }
]

// Utility functions
export const formatTimeAgo = (timestamp: string): string => {
  const now = new Date()
  const time = new Date(timestamp)
  const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  
  return time.toLocaleDateString()
}

export const formatCurrentTime = (date: Date, format: '12h' | '24h' = '12h'): string => {
  if (format === '24h') {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }
  
  return date.toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: true 
  })
}

export const getNotificationIcon = (type: NotificationConfig['type']): string => {
  switch (type) {
    case 'success':
      return 'CheckCircle'
    case 'warning':
      return 'AlertTriangle'
    case 'error':
      return 'AlertCircle'
    default:
      return 'Info'
  }
}

export const getNotificationColor = (type: NotificationConfig['type']): string => {
  switch (type) {
    case 'success':
      return 'text-green-600'
    case 'warning':
      return 'text-yellow-600'
    case 'error':
      return 'text-red-600'
    default:
      return 'text-blue-600'
  }
}

// Header analytics
export interface HeaderAnalytics {
  searchQueries: number
  notificationClicks: number
  profileViews: number
  menuToggles: number
  breadcrumbClicks: number
}

export const trackHeaderAction = (action: keyof HeaderAnalytics): void => {
  // In a real app, this would send analytics data
  console.log(`Header action tracked: ${action}`)
}

// Responsive breakpoints for header
export const headerBreakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280
}

export const getHeaderLayout = (screenWidth: number): 'mobile' | 'tablet' | 'desktop' => {
  if (screenWidth < headerBreakpoints.mobile) return 'mobile'
  if (screenWidth < headerBreakpoints.tablet) return 'tablet'
  return 'desktop'
}

// Header accessibility helpers
export const headerA11yLabels = {
  menuToggle: 'Toggle navigation menu',
  search: 'Search navigation',
  notifications: 'View notifications',
  profile: 'User profile menu',
  themeToggle: 'Toggle theme',
  fullscreenToggle: 'Toggle fullscreen mode',
  help: 'Help and support'
}

export const getA11yLabel = (element: keyof typeof headerA11yLabels): string => {
  return headerA11yLabels[element]
}

export default {
  defaultHeaderConfigs,
  headerThemes,
  mockNotifications,
  getProfileMenuItems,
  headerKeyboardShortcuts,
  formatTimeAgo,
  formatCurrentTime,
  getNotificationIcon,
  getNotificationColor,
  trackHeaderAction,
  getHeaderLayout,
  getA11yLabel
}
