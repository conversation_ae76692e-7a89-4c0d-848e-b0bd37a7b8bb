export interface CachedInstituteData {
  institute: {
    id: string
    name: string
    slug: string
    tagline?: string
    description?: string
    email?: string
    phone?: string
    website?: string
    customDomain?: string
    logo?: any
  }
  theme: {
    id: string
    name: string
    colors: {
      primary: string
      secondary: string
      accent: string
      background: string
      text: string
      muted?: string
      border?: string
    }
    fonts: {
      heading: string
      body: string
      mono?: string
    }
    customizations?: any
  } | null
  timestamp: number
  domain: string
}

// Cache expiration time: 1 hour (3600000 ms)
const CACHE_EXPIRATION_TIME = 60 * 60 * 1000

export class InstituteCache {
  private static getCacheKey(domain: string): string {
    return `institute-data-${domain}`
  }

  /**
   * Store institute data in localStorage with timestamp
   */
  static store(domain: string, data: Omit<CachedInstituteData, 'timestamp' | 'domain'>): void {
    try {
      const cacheData: CachedInstituteData = {
        ...data,
        timestamp: Date.now(),
        domain
      }
      
      const cacheKey = this.getCacheKey(domain)
      localStorage.setItem(cacheKey, JSON.stringify(cacheData))
      
      console.log('📦 Cached institute data for domain:', domain)
    } catch (error) {
      console.warn('⚠️ Failed to cache institute data:', error)
    }
  }

  /**
   * Retrieve institute data from localStorage
   */
  static retrieve(domain: string): CachedInstituteData | null {
    try {
      const cacheKey = this.getCacheKey(domain)
      const cachedData = localStorage.getItem(cacheKey)
      
      if (!cachedData) {
        console.log('📭 No cached data found for domain:', domain)
        return null
      }
      
      const data: CachedInstituteData = JSON.parse(cachedData)
      console.log('📦 Retrieved cached data for domain:', domain)
      return data
    } catch (error) {
      console.warn('⚠️ Failed to retrieve cached data:', error)
      return null
    }
  }

  /**
   * Check if cached data is still valid (not expired)
   */
  static isValid(data: CachedInstituteData): boolean {
    const now = Date.now()
    const age = now - data.timestamp
    const isValid = age < CACHE_EXPIRATION_TIME
    
    console.log(`🕒 Cache age: ${Math.round(age / 1000)}s, Valid: ${isValid}`)
    return isValid
  }

  /**
   * Get fresh cached data if available and valid
   */
  static getFresh(domain: string): CachedInstituteData | null {
    const cached = this.retrieve(domain)
    
    if (!cached) {
      return null
    }
    
    if (!this.isValid(cached)) {
      console.log('🗑️ Cached data expired, removing...')
      this.remove(domain)
      return null
    }
    
    console.log('✅ Fresh cached data available')
    return cached
  }

  /**
   * Remove cached data for a domain
   */
  static remove(domain: string): void {
    try {
      const cacheKey = this.getCacheKey(domain)
      localStorage.removeItem(cacheKey)
      console.log('🗑️ Removed cached data for domain:', domain)
    } catch (error) {
      console.warn('⚠️ Failed to remove cached data:', error)
    }
  }

  /**
   * Clear all cached institute data
   */
  static clearAll(): void {
    try {
      const keys = Object.keys(localStorage)
      const instituteKeys = keys.filter(key => key.startsWith('institute-data-'))
      
      instituteKeys.forEach(key => {
        localStorage.removeItem(key)
      })
      
      console.log(`🗑️ Cleared ${instituteKeys.length} cached institute entries`)
    } catch (error) {
      console.warn('⚠️ Failed to clear cached data:', error)
    }
  }

  /**
   * Get cache statistics
   */
  static getStats(): { totalEntries: number; domains: string[] } {
    try {
      const keys = Object.keys(localStorage)
      const instituteKeys = keys.filter(key => key.startsWith('institute-data-'))
      const domains = instituteKeys.map(key => key.replace('institute-data-', ''))
      
      return {
        totalEntries: instituteKeys.length,
        domains
      }
    } catch (error) {
      console.warn('⚠️ Failed to get cache stats:', error)
      return { totalEntries: 0, domains: [] }
    }
  }
}
