import { NavigationItem, UserType } from '@/stores/sidebar/useSidebarStore'
import { superAdminNavigationConfig } from '@/config/navigation/superAdminNavigation'
import { instituteAdminNavigationConfig } from '@/config/navigation/instituteAdminNavigation'
import { studentNavigationConfig } from '@/config/navigation/studentNavigation'

// Navigation configuration mapping
export const navigationConfigs: Record<UserType, NavigationItem[]> = {
  super_admin: superAdminNavigationConfig,
  institute_admin: instituteAdminNavigationConfig,
  student: studentNavigationConfig
}

// Get navigation items for a specific user type
export function getNavigationForUserType(userType: UserType): NavigationItem[] {
  return navigationConfigs[userType] || []
}

// Get navigation item by ID (recursive search)
export function findNavigationItem(
  items: NavigationItem[], 
  itemId: string
): NavigationItem | null {
  for (const item of items) {
    if (item.id === itemId) {
      return item
    }
    if (item.children) {
      const found = findNavigationItem(item.children, itemId)
      if (found) return found
    }
  }
  return null
}

// Get navigation item by href (recursive search)
export function findNavigationItemByHref(
  items: NavigationItem[], 
  href: string
): NavigationItem | null {
  for (const item of items) {
    if (item.href === href) {
      return item
    }
    if (item.children) {
      const found = findNavigationItemByHref(item.children, href)
      if (found) return found
    }
  }
  return null
}

// Get all navigation items as flat array (for search)
export function getFlatNavigationItems(items: NavigationItem[]): NavigationItem[] {
  const flatItems: NavigationItem[] = []
  
  function flatten(items: NavigationItem[]) {
    for (const item of items) {
      flatItems.push(item)
      if (item.children) {
        flatten(item.children)
      }
    }
  }
  
  flatten(items)
  return flatItems
}

// Search navigation items
export function searchNavigationItems(
  items: NavigationItem[], 
  query: string
): NavigationItem[] {
  const flatItems = getFlatNavigationItems(items)
  const searchTerm = query.toLowerCase().trim()
  
  if (!searchTerm) return []
  
  return flatItems.filter(item => 
    item.label.toLowerCase().includes(searchTerm) ||
    item.description?.toLowerCase().includes(searchTerm) ||
    item.href.toLowerCase().includes(searchTerm)
  )
}

// Get navigation breadcrumbs for a given path
export function getNavigationBreadcrumbs(
  items: NavigationItem[], 
  currentPath: string
): NavigationItem[] {
  const breadcrumbs: NavigationItem[] = []
  
  function findPath(items: NavigationItem[], path: NavigationItem[]): boolean {
    for (const item of items) {
      const currentPath = [...path, item]
      
      if (item.href === currentPath || currentPath.startsWith(item.href + '/')) {
        breadcrumbs.push(...currentPath)
        return true
      }
      
      if (item.children && findPath(item.children, currentPath)) {
        return true
      }
    }
    return false
  }
  
  findPath(items, [])
  return breadcrumbs
}

// Get navigation items by section
export function getNavigationBySection(
  items: NavigationItem[], 
  section: string
): NavigationItem[] {
  return items.filter(item => item.section === section)
}

// Get navigation items with badges
export function getNavigationItemsWithBadges(items: NavigationItem[]): NavigationItem[] {
  const flatItems = getFlatNavigationItems(items)
  return flatItems.filter(item => item.badge && item.badge > 0)
}

// Calculate total badge count
export function getTotalBadgeCount(items: NavigationItem[]): number {
  const flatItems = getFlatNavigationItems(items)
  return flatItems.reduce((total, item) => total + (item.badge || 0), 0)
}

// Get parent navigation item
export function getParentNavigationItem(
  items: NavigationItem[], 
  childId: string
): NavigationItem | null {
  for (const item of items) {
    if (item.children) {
      const found = item.children.find(child => child.id === childId)
      if (found) return item
      
      const parentInChildren = getParentNavigationItem(item.children, childId)
      if (parentInChildren) return parentInChildren
    }
  }
  return null
}

// Check if navigation item is active (including children)
export function isNavigationItemActive(
  item: NavigationItem, 
  currentPath: string
): boolean {
  if (currentPath === item.href || currentPath.startsWith(item.href + '/')) {
    return true
  }
  
  if (item.children) {
    return item.children.some(child => isNavigationItemActive(child, currentPath))
  }
  
  return false
}

// Get navigation item depth
export function getNavigationItemDepth(
  items: NavigationItem[], 
  itemId: string, 
  currentDepth: number = 0
): number {
  for (const item of items) {
    if (item.id === itemId) {
      return currentDepth
    }
    if (item.children) {
      const depth = getNavigationItemDepth(item.children, itemId, currentDepth + 1)
      if (depth !== -1) return depth
    }
  }
  return -1
}

// Validate navigation structure
export function validateNavigationStructure(items: NavigationItem[]): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  const seenIds = new Set<string>()
  const seenHrefs = new Set<string>()
  
  function validate(items: NavigationItem[], path: string = '') {
    for (const item of items) {
      const currentPath = path ? `${path} > ${item.label}` : item.label
      
      // Check for duplicate IDs
      if (seenIds.has(item.id)) {
        errors.push(`Duplicate ID "${item.id}" found at ${currentPath}`)
      }
      seenIds.add(item.id)
      
      // Check for duplicate hrefs
      if (seenHrefs.has(item.href)) {
        errors.push(`Duplicate href "${item.href}" found at ${currentPath}`)
      }
      seenHrefs.add(item.href)
      
      // Check required fields
      if (!item.label) {
        errors.push(`Missing label at ${currentPath}`)
      }
      if (!item.href) {
        errors.push(`Missing href at ${currentPath}`)
      }
      if (!item.icon) {
        errors.push(`Missing icon at ${currentPath}`)
      }
      
      // Validate children
      if (item.children) {
        validate(item.children, currentPath)
      }
    }
  }
  
  validate(items)
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Get navigation statistics
export function getNavigationStatistics(items: NavigationItem[]) {
  const flatItems = getFlatNavigationItems(items)
  const sections = new Set(items.map(item => item.section).filter(Boolean))
  const maxDepth = Math.max(...flatItems.map(item => 
    getNavigationItemDepth(items, item.id)
  ))
  const itemsWithBadges = getNavigationItemsWithBadges(items)
  const totalBadgeCount = getTotalBadgeCount(items)
  
  return {
    totalItems: flatItems.length,
    topLevelItems: items.length,
    sections: sections.size,
    maxDepth: maxDepth + 1, // Convert 0-based to 1-based
    itemsWithBadges: itemsWithBadges.length,
    totalBadgeCount,
    averageChildrenPerItem: items.reduce((sum, item) => 
      sum + (item.children?.length || 0), 0
    ) / items.length
  }
}

// Export utility functions
export const navigationUtils = {
  getNavigationForUserType,
  findNavigationItem,
  findNavigationItemByHref,
  getFlatNavigationItems,
  searchNavigationItems,
  getNavigationBreadcrumbs,
  getNavigationBySection,
  getNavigationItemsWithBadges,
  getTotalBadgeCount,
  getParentNavigationItem,
  isNavigationItemActive,
  getNavigationItemDepth,
  validateNavigationStructure,
  getNavigationStatistics
}

export default navigationUtils
