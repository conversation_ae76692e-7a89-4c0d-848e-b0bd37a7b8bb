import { NavigationItem } from '@/stores/sidebar/useSidebarStore'

/**
 * Check if a navigation item should be considered active based on the current pathname
 * This includes checking if the current path matches the item's href or any of its children's hrefs
 */
export function isNavigationItemActive(item: NavigationItem, pathname: string): boolean {
  // Direct match
  if (pathname === item.href) {
    return true
  }

  // Check if current path starts with the item's href (for nested routes)
  // But only if the item's href is not just a root path like '/'
  if (item.href !== '/' && pathname.startsWith(item.href)) {
    return true
  }

  // Check children recursively
  if (item.children && item.children.length > 0) {
    return item.children.some(child => isNavigationItemActive(child, pathname))
  }

  return false
}

/**
 * Check if a navigation item's children contain the active path
 * This is useful for determining if a parent item should be expanded
 */
export function hasActiveChild(item: NavigationItem, pathname: string): boolean {
  if (!item.children || item.children.length === 0) {
    return false
  }

  return item.children.some(child => isNavigationItemActive(child, pathname))
}

/**
 * Get the active child item for a navigation item
 */
export function getActiveChild(item: NavigationItem, pathname: string): NavigationItem | null {
  if (!item.children || item.children.length === 0) {
    return null
  }

  for (const child of item.children) {
    if (isNavigationItemActive(child, pathname)) {
      return child
    }
  }

  return null
}

/**
 * Check if a navigation item should be expanded based on the current pathname
 */
export function shouldExpandNavigationItem(item: NavigationItem, pathname: string): boolean {
  // Expand if the item itself is active
  if (pathname === item.href) {
    return true
  }

  // Expand if any child is active
  return hasActiveChild(item, pathname)
}

/**
 * Get breadcrumb items for the current pathname based on navigation structure
 */
export function getBreadcrumbsFromNavigation(
  navigationItems: NavigationItem[], 
  pathname: string
): Array<{ label: string; href: string }> {
  const breadcrumbs: Array<{ label: string; href: string }> = []

  function findPath(items: NavigationItem[], currentPath: Array<{ label: string; href: string }>): boolean {
    for (const item of items) {
      const newPath = [...currentPath, { label: item.label, href: item.href }]

      if (isNavigationItemActive(item, pathname)) {
        breadcrumbs.push(...newPath)
        return true
      }

      if (item.children && findPath(item.children, newPath)) {
        return true
      }
    }
    return false
  }

  findPath(navigationItems, [])
  return breadcrumbs
}
