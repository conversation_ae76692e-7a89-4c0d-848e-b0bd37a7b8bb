import React from 'react'
import { UserType } from '@/stores/sidebar/useSidebarStore'

// Permission types
export interface Permission {
  id: string
  name: string
  code: string // Add code field for permission checking
  category: string
  resource: string
  action: string
  scope: 'global' | 'institute' | 'department' | 'own'
  requiredLevel: 1 | 2 | 3 | 4 | 5
  level?: string // Add level field from backend
  isActive?: boolean // Add isActive field from backend
}

export interface UserPermissions {
  role: string
  level: number
  permissions: Permission[]
  department?: string
  instituteId?: string
}

// Default permissions for each user type
export const DEFAULT_PERMISSIONS = {
  super_admin: {
    level: 1,
    permissions: [
      // Dashboard
      'view_dashboard',
      'manage_dashboard',

      // User & Role Management
      'manage_users',
      'manage_roles',
      'manage_permissions',
      'view_users',
      'create_users',
      'update_users',
      'delete_users',
      'view_roles',
      'create_roles',
      'update_roles',
      'delete_roles',
      'view_permissions',
      'create_permissions',
      'update_permissions',
      'delete_permissions',

      // Institute Management
      'manage_institutes',
      'view_institutes',
      'create_institutes',
      'update_institutes',
      'delete_institutes',

      // Location Management
      'manage_locations',
      'manage_countries',
      'manage_states',
      'manage_districts',
      'view_locations',
      'view_countries',
      'view_states',
      'view_districts',
      'create_countries',
      'create_states',
      'create_districts',
      'update_countries',
      'update_states',
      'update_districts',
      'delete_countries',
      'delete_states',
      'delete_districts',

      // Tax Management
      'manage_taxes',
      'view_taxes',
      'create_taxes',
      'update_taxes',
      'delete_taxes',
      'calculate_taxes',

      // Theme Management
      'manage_themes',
      'view_themes',
      'create_themes',
      'update_themes',
      'delete_themes',
      'apply_themes',

      // System Management
      'manage_settings',
      'view_settings',
      'update_settings',
      'manage_billing',
      'view_billing',
      'view_analytics',
      'view_reports',
      'export_reports',

      // Payment Gateway Management
      'manage_payment_gateways',
      'view_payment_gateways',
      'create_payment_gateways',
      'update_payment_gateways',
      'delete_payment_gateways',

      // Course & Content Management
      'manage_courses',
      'view_courses',
      'create_courses',
      'update_courses',
      'delete_courses',
      'manage_content',
      'view_content',
      'create_content',
      'update_content',
      'delete_content',

      // Student Management
      'manage_students',
      'view_students',
      'create_students',
      'update_students',
      'delete_students',

      // Branch Management
      'manage_branches',
      'view_branches',
      'create_branches',
      'update_branches',
      'delete_branches',

      // Live Classes & Assignments
      'manage_live_classes',
      'view_live_classes',
      'create_live_classes',
      'update_live_classes',
      'delete_live_classes',
      'join_live_classes',
      'view_assignments',
      'create_assignments',
      'update_assignments',
      'delete_assignments',
      'grade_assignments',
      'submit_assignments',

      // Communication & Website
      'manage_website',
      'view_website',
      'update_website',
      'send_notifications',
      'manage_communication',

      // System Administration
      'system_admin',
      'access_all'
    ]
  },
  institute_admin: {
    level: 2,
    permissions: [
      'manage_courses',
      'manage_students',
      'manage_branches',
      'manage_institute_billing',
      'view_institute_analytics',
      'manage_website',
      'manage_institute_settings',
      'manage_institute_payment_gateways',
      'configure_payment_gateways'
    ]
  },
  student: {
    level: 5,
    permissions: [
      'view_courses',
      'enroll_courses',
      'view_assignments',
      'submit_assignments',
      'join_live_classes',
      'view_progress',
      'manage_account',
      'access_community'
    ]
  }
}

// Navigation permissions mapping - Updated for new permission structure
export const NAVIGATION_PERMISSIONS = {
  // Super Admin Dashboard
  'super-admin': ['view_dashboard', 'manage_dashboard'],
  'super-admin-dashboard': ['view_dashboard', 'manage_dashboard'],

  // User & Role Management
  'super-admin-users': ['manage_users', 'view_users'],
  'super-admin-role-permissions': ['manage_roles', 'manage_permissions', 'view_roles', 'view_permissions'],

  // Institute Management
  'super-admin-institutes': ['manage_institutes', 'view_institutes'],

  // Location Management
  'super-admin-locations': ['manage_locations', 'view_locations'],
  'super-admin-countries': ['manage_countries', 'view_countries'],
  'super-admin-states': ['manage_states', 'view_states'],
  'super-admin-districts': ['manage_districts', 'view_districts'],

  // Tax Management
  'super-admin-tax': ['manage_taxes', 'view_taxes'],
  'super-admin-tax-management': ['manage_taxes', 'view_taxes', 'calculate_taxes'],

  // Theme Management
  'super-admin-themes': ['manage_themes', 'view_themes', 'apply_themes'],

  // Billing & Finance
  'super-admin-billing': ['manage_billing', 'view_billing'],

  // Payment Gateway Management
  'super-admin-gateway-management': ['manage_payment_gateways', 'view_payment_gateways'],

  // Analytics & Reports
  'super-admin-analytics': ['view_analytics', 'view_reports', 'export_reports'],

  // System Settings
  'super-admin-settings': ['manage_settings', 'view_settings', 'update_settings'],

  // Website Management
  'super-admin-website': ['manage_website', 'view_website', 'update_website'],

  // Institute Admin
  'institute-admin-dashboard': [],
  'institute-admin-courses': ['manage_courses'],
  'institute-admin-students': ['manage_students'],
  'institute-admin-branches': ['manage_branches'],
  'institute-admin-billing': ['manage_institute_billing'],
  'institute-admin-analytics': ['view_institute_analytics'],
  'institute-admin-website': ['manage_website'],
  'institute-admin-settings': ['manage_institute_settings'],

  // Institute Admin Payment Gateway Configuration
  'admin-settings-payment-gateways': ['manage_institute_payment_gateways', 'configure_payment_gateways'],

  // Student
  'student-dashboard': [],
  'student-courses': ['view_courses'],
  'student-marketplace': ['view_courses'],
  'student-assignments': ['view_assignments'],
  'student-live-classes': ['join_live_classes'],
  'student-progress': ['view_progress'],
  'student-community': ['access_community'],
  'student-payments': ['manage_account'],
  'student-account': ['manage_account'],
  'student-support': []
}

/**
 * Check if user has a specific permission using the new structure
 */
export function hasPermission(
  userPermissions: UserPermissions,
  requiredPermission: string,
  resource?: string,
  scope?: 'global' | 'institute' | 'department' | 'own'
): boolean {
  console.log(`🔍 hasPermission check: ${requiredPermission}`, {
    userRole: userPermissions.role,
    permissionCount: userPermissions.permissions.length,
    isSuperAdmin: userPermissions.role === 'super_admin'
  })

  // Super admin and platform staff have all permissions
  if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {
    console.log(`✅ Permission granted (${userPermissions.role} has all permissions)`)
    return true
  }

  // Check if user has the specific permission by code, name, or id
  const hasDirectPermission = userPermissions.permissions.some(
    permission =>
      permission.code === requiredPermission ||
      permission.name === requiredPermission ||
      permission.id === requiredPermission ||
      permission.name.toLowerCase() === requiredPermission.toLowerCase() ||
      permission.code?.toLowerCase() === requiredPermission.toLowerCase()
  )

  console.log(`🔍 Direct permission check result: ${hasDirectPermission}`)

  if (!hasDirectPermission) {
    // Log available permissions for debugging
    const availablePermissions = userPermissions.permissions.map(p => p.code || p.name).slice(0, 10)
    console.log(`❌ Permission "${requiredPermission}" not found. Available permissions (first 10):`, availablePermissions)
    return false
  }

  // If scope is specified, check scope restrictions
  if (scope) {
    const permission = userPermissions.permissions.find(
      p => p.code === requiredPermission || p.name === requiredPermission || p.id === requiredPermission
    )

    if (!permission) return false

    // Check scope hierarchy: global > institute > department > own
    const scopeHierarchy = ['global', 'institute', 'department', 'own']
    const userScopeIndex = scopeHierarchy.indexOf(permission.scope)
    const requiredScopeIndex = scopeHierarchy.indexOf(scope)

    const scopeAllowed = userScopeIndex <= requiredScopeIndex
    console.log(`🔍 Scope check: ${permission.scope} vs ${scope} = ${scopeAllowed}`)
    return scopeAllowed
  }

  console.log(`✅ Permission "${requiredPermission}" granted`)
  return true
}

/**
 * Check if user can access a navigation item using new permission structure
 */
export function canAccessNavigation(
  userPermissions: UserPermissions,
  navigationId: string
): boolean {
  console.log(`🔍 canAccessNavigation check: ${navigationId}`, {
    userRole: userPermissions.role,
    isSuperAdmin: userPermissions.role === 'super_admin'
  })

  // Super admin can access everything
  if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {
    console.log(`✅ Navigation access granted (${userPermissions.role} has all access)`)
    return true
  }

  const requiredPermissions = NAVIGATION_PERMISSIONS[navigationId as keyof typeof NAVIGATION_PERMISSIONS]

  // If no permissions required, allow access
  if (!requiredPermissions || requiredPermissions.length === 0) {
    console.log(`✅ Navigation access granted (no permissions required for ${navigationId})`)
    return true
  }

  // Check if user has any of the required permissions
  const hasAccess = requiredPermissions.some(permission =>
    hasPermission(userPermissions, permission)
  )

  console.log(`🔍 Navigation access result for ${navigationId}: ${hasAccess}`)
  console.log(`   Required permissions: ${requiredPermissions.join(', ')}`)

  return hasAccess
}

/**
 * Filter navigation items based on user permissions
 */
export function filterNavigationByPermissions(
  navigationItems: any[],
  userPermissions: UserPermissions
): any[] {
  // Super admin has access to all navigation items
  if (userPermissions.role === 'super_admin') {
    return navigationItems
  }

  // Prevent infinite loops by checking if navigationItems is valid
  if (!Array.isArray(navigationItems) || navigationItems.length === 0) {
    return []
  }

  return navigationItems.filter(item => {
    // Ensure item has required properties
    if (!item || !item.href) {
      return false
    }

    // Generate navigation ID from href
    const navigationId = item.href.replace('/', '').replace(/\//g, '-')

    // Check if user can access this navigation item
    const canAccess = canAccessNavigation(userPermissions, navigationId)

    // If item has children, filter them recursively
    if (item.children && Array.isArray(item.children) && item.children.length > 0) {
      const filteredChildren = filterNavigationByPermissions(item.children, userPermissions)

      // If no children are accessible, hide the parent too
      if (filteredChildren.length === 0 && !canAccess) {
        return false
      }

      // Update children with filtered list (avoid mutation)
      item = { ...item, children: filteredChildren }
    }

    return canAccess
  })
}

/**
 * Check if user has minimum required level
 */
export function hasMinimumLevel(
  userLevel: number,
  requiredLevel: number
): boolean {
  // Lower numbers = higher level (1 = highest, 5 = lowest)
  return userLevel <= requiredLevel
}

/**
 * Get user permissions from user object with new structure
 */
export function getUserPermissions(user: any): UserPermissions {
  if (!user) {
    return {
      role: 'guest',
      level: 5,
      permissions: [],
      department: undefined,
      instituteId: undefined
    }
  }

  console.log('🔍 getUserPermissions - Processing user:', {
    email: user.email,
    legacyRole: user.legacyRole,
    hasRole: !!user.role,
    roleType: typeof user.role,
    hasRolePermissions: !!(user.role && user.role.permissions),
    rolePermissionsCount: user.role?.permissions?.length || 0
  })

  // Use legacyRole for role checking (consistent with auth system)
  const role = user.legacyRole || 'student'

  // Get level from role relationship or default
  let level = 5
  if (user.role && typeof user.role === 'object' && user.role.level) {
    level = parseInt(user.role.level) || 5
  } else {
    level = DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS]?.level || 5
  }

  // Extract permissions from user.role.permissions array (new structure)
  let permissions: Permission[] = []

  if (user.role && typeof user.role === 'object' && Array.isArray(user.role.permissions)) {
    console.log(`🔍 Found ${user.role.permissions.length} permissions in user.role.permissions`)

    // Convert backend permission objects to frontend format
    permissions = user.role.permissions
      .filter((perm: any) => perm && perm.isActive !== false) // Only include active permissions
      .map((perm: any) => ({
        id: perm.id || perm.code,
        name: perm.name || perm.code,
        code: perm.code, // Include the code field for permission checking
        category: perm.category || 'general',
        resource: perm.resource || 'general',
        action: perm.action || 'access',
        scope: 'global' as const,
        requiredLevel: level,
        level: perm.level || '1',
        isActive: perm.isActive !== false
      }))

    console.log(`✅ Processed ${permissions.length} active permissions`)

    // Log sample permissions for debugging
    if (permissions.length > 0) {
      console.log('📝 Sample permissions:', permissions.slice(0, 5).map(p => ({
        name: p.name,
        code: p.code,
        category: p.category,
        resource: p.resource,
        action: p.action
      })))
    }
  } else if (user.permissions && Array.isArray(user.permissions)) {
    // Fallback: Handle legacy simple permission strings
    console.log('🔄 Using legacy permissions format')
    permissions = user.permissions.map((perm: string) => ({
      id: perm,
      name: perm,
      code: perm,
      category: 'general',
      resource: 'general',
      action: 'access',
      scope: 'global' as const,
      requiredLevel: level,
      level: '1',
      isActive: true
    }))
  } else {
    console.log('⚠️ No permissions found in user object')
  }

  const department = user.employment?.department || user.department
  const instituteId = user.institute?.id || user.institute

  const result = {
    role,
    level,
    permissions,
    department,
    instituteId
  }

  console.log('✅ getUserPermissions result:', {
    role: result.role,
    level: result.level,
    permissionCount: result.permissions.length,
    department: result.department,
    instituteId: result.instituteId
  })

  return result
}

/**
 * Check if user can perform CRUD operations
 */
export function canPerformAction(
  userPermissions: UserPermissions,
  action: 'create' | 'read' | 'update' | 'delete',
  resource: string,
  scope?: 'global' | 'institute' | 'department' | 'own'
): boolean {
  const permissionName = `${action}_${resource}`
  return hasPermission(userPermissions, permissionName, resource, scope)
}

/**
 * Get allowed actions for a resource
 */
export function getAllowedActions(
  userPermissions: UserPermissions,
  resource: string
): string[] {
  const actions = ['create', 'read', 'update', 'delete']
  
  return actions.filter(action =>
    canPerformAction(userPermissions, action as any, resource)
  )
}

/**
 * Check if user is in same department
 */
export function isSameDepartment(
  userDepartment: string | undefined,
  targetDepartment: string | undefined
): boolean {
  if (!userDepartment || !targetDepartment) return false
  return userDepartment === targetDepartment
}

/**
 * Check if user is in same institute
 */
export function isSameInstitute(
  userInstituteId: string | undefined,
  targetInstituteId: string | undefined
): boolean {
  if (!userInstituteId || !targetInstituteId) return false
  return userInstituteId === targetInstituteId
}

/**
 * Permission-based component wrapper
 */
export function withPermission<T extends object>(
  Component: React.ComponentType<T>,
  requiredPermission: string,
  fallback?: React.ComponentType<T>
) {
  return function PermissionWrapper(props: T) {
    // This would be used with a permission context or hook
    // For now, returning the component as-is
    return <Component {...props} />
  }
}

/**
 * Generate permission key for caching
 */
export function generatePermissionKey(
  userId: string,
  permission: string,
  resource?: string,
  scope?: string
): string {
  return `${userId}:${permission}:${resource || 'any'}:${scope || 'any'}`
}

/**
 * Validate permission structure
 */
export function validatePermission(permission: any): permission is Permission {
  return (
    permission &&
    typeof permission.id === 'string' &&
    typeof permission.name === 'string' &&
    typeof permission.category === 'string' &&
    typeof permission.resource === 'string' &&
    typeof permission.action === 'string' &&
    ['global', 'institute', 'department', 'own'].includes(permission.scope) &&
    [1, 2, 3, 4, 5].includes(permission.requiredLevel)
  )
}

/**
 * Debug function to test super admin permissions with new structure
 */
export function debugSuperAdminPermissions(user: any) {
  console.log('=== SUPER ADMIN PERMISSION DEBUG ===')
  console.log('User object:', {
    email: user.email,
    legacyRole: user.legacyRole,
    hasRole: !!user.role,
    roleType: typeof user.role,
    roleName: user.role?.name,
    roleCode: user.role?.code,
    rolePermissionsCount: user.role?.permissions?.length || 0
  })

  const userPermissions = getUserPermissions(user)
  console.log('Processed user permissions:', {
    role: userPermissions.role,
    level: userPermissions.level,
    permissionCount: userPermissions.permissions.length,
    department: userPermissions.department,
    instituteId: userPermissions.instituteId
  })

  // Test key permissions with new codes
  const testPermissions = [
    'manage_users',
    'manage_roles',
    'manage_permissions',
    'view_users',
    'view_dashboard',
    'manage_taxes',
    'manage_locations',
    'manage_themes',
    'system_admin',
    'access_all'
  ]

  console.log('Permission test results:')
  testPermissions.forEach(permission => {
    const hasAccess = hasPermission(userPermissions, permission)
    console.log(`  ${permission}: ${hasAccess}`)
  })

  // Test navigation access with new navigation IDs
  const testNavigation = [
    'super-admin',
    'super-admin-dashboard',
    'super-admin-role-permissions',
    'super-admin-users',
    'super-admin-institutes',
    'super-admin-tax',
    'super-admin-locations',
    'super-admin-themes',
    'super-admin-settings'
  ]

  console.log('Navigation access test results:')
  testNavigation.forEach(navId => {
    const hasAccess = canAccessNavigation(userPermissions, navId)
    console.log(`  ${navId}: ${hasAccess}`)
  })

  // Show sample permissions
  if (userPermissions.permissions.length > 0) {
    console.log('Sample permissions (first 10):')
    userPermissions.permissions.slice(0, 10).forEach((perm, index) => {
      console.log(`  ${index + 1}. ${perm.name} (${perm.code}) - ${perm.category}/${perm.resource}`)
    })
  }

  console.log('=== END DEBUG ===')

  return userPermissions
}
