/**
 * Test utility for domain caching system
 * This file can be used to test the caching functionality
 */

import { getDomainData, initializeCacheManagement, clearAllDomainCache, getCacheStats } from './domainCache'

// Test function to verify caching works
export async function testDomainCaching() {
  console.log('🧪 Testing domain caching system...')
  
  // Initialize cache management
  initializeCacheManagement()
  
  // Clear any existing cache
  clearAllDomainCache()
  
  // Test domain
  const testDomain = 'hello.local:3000'
  
  console.log('📊 Initial cache stats:', getCacheStats())
  
  // First call - should make API request
  console.log('🔄 First call (should make API request)...')
  const start1 = Date.now()
  const result1 = await getDomainData(testDomain)
  const time1 = Date.now() - start1
  console.log('⏱️ First call took:', time1, 'ms')
  console.log('📋 First result:', result1)
  
  console.log('📊 Cache stats after first call:', getCacheStats())
  
  // Second call - should use cache
  console.log('🔄 Second call (should use cache)...')
  const start2 = Date.now()
  const result2 = await getDomainData(testDomain)
  const time2 = Date.now() - start2
  console.log('⏱️ Second call took:', time2, 'ms')
  console.log('📋 Second result:', result2)
  
  console.log('📊 Final cache stats:', getCacheStats())
  
  // Verify results are the same
  const resultsMatch = JSON.stringify(result1) === JSON.stringify(result2)
  console.log('✅ Results match:', resultsMatch)
  console.log('⚡ Performance improvement:', time1 > time2 ? `${time1 - time2}ms faster` : 'No improvement')
  
  return {
    firstCallTime: time1,
    secondCallTime: time2,
    resultsMatch,
    performanceImprovement: time1 - time2
  }
}

// Function to test cache expiration
export function testCacheExpiration() {
  console.log('🧪 Testing cache expiration...')
  
  // This would require mocking the timestamp or waiting 3 hours
  // For now, we'll just log the concept
  console.log('⏰ Cache expiration test would require waiting 3 hours or mocking timestamps')
  console.log('💡 In production, expired cache entries are automatically cleaned up')
}

// Function to test cache corruption handling
export function testCacheCorruption() {
  console.log('🧪 Testing cache corruption handling...')
  
  try {
    // Manually corrupt cache data
    const corruptKey = 'domain_cache_test.corrupt'
    localStorage.setItem(corruptKey, 'invalid json data')
    
    console.log('💥 Inserted corrupted cache data')
    
    // Try to read it - should handle gracefully
    const cachedData = localStorage.getItem(corruptKey)
    console.log('📖 Corrupted data:', cachedData)
    
    // Clean up
    localStorage.removeItem(corruptKey)
    console.log('🧹 Cleaned up corrupted data')
    
    return true
  } catch (error) {
    console.error('❌ Error in corruption test:', error)
    return false
  }
}

// Export test runner
export function runAllCacheTests() {
  console.log('🚀 Running all domain cache tests...')
  
  // Test basic caching
  testDomainCaching()
  
  // Test corruption handling
  testCacheCorruption()
  
  // Test expiration (conceptual)
  testCacheExpiration()
  
  console.log('✅ All cache tests completed')
}
