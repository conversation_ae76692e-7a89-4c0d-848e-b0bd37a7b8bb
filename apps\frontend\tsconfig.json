{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/components/admin/*": ["./src/components/admin/*"], "@/components/admin/course-builder/*": ["./src/components/admin/course-builder/*"], "@/stores/admin/*": ["./src/stores/admin/*"], "@/lib/course-builder/*": ["./src/lib/course-builder/*"], "@/types/admin/*": ["./src/types/admin/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}