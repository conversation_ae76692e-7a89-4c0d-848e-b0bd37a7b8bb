{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXT_PUBLIC_API_URL": "@next_public_api_url", "NEXT_PUBLIC_APP_URL": "@next_public_app_url"}, "build": {"env": {"NEXT_PUBLIC_API_URL": "@next_public_api_url", "NEXT_PUBLIC_APP_URL": "@next_public_app_url"}}, "functions": {"app/**": {"maxDuration": 30}}, "regions": ["bom1"], "rewrites": [{"source": "/api/:path*", "destination": "$NEXT_PUBLIC_API_URL/api/:path*"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}