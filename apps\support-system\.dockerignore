# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Next.js
.next/
out/

# Production
build

# Misc
.DS_Store
*.tsbuildinfo

# Debug
*.log

# Local env files
.env*.local
.env.development
.env.test

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Testing
coverage/
.nyc_output

# IDEs
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Git
.git
.gitignore

# Documentation
README.md
*.md

# Development files
init-scripts/
uploads/
