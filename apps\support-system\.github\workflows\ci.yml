name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/support-system/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/support-system/**'

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Job 1: Code Quality and Testing
  test:
    name: Test & Lint
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: support_system_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    defaults:
      run:
        working-directory: ./apps/support-system

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          cache-dependency-path: './apps/support-system/pnpm-lock.yaml'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Create test environment file
        run: |
          cat > .env.test << EOF
          NODE_ENV=test
          POSTGRES_URL=postgres://postgres:postgres@localhost:5432/support_system_test
          DATABASE_URL=postgres://postgres:postgres@localhost:5432/support_system_test
          REDIS_URL=redis://localhost:6379
          PAYLOAD_SECRET=test-secret-key
          PAYLOAD_CONFIG_PATH=src/payload.config.ts
          NEXTAUTH_SECRET=test-nextauth-secret
          NEXTAUTH_URL=http://localhost:3000
          EOF

      - name: Run linting
        run: pnpm run lint

      - name: Run type checking
        run: pnpm run build --dry-run || npx tsc --noEmit

      - name: Generate Payload types
        run: pnpm run generate:types
        env:
          NODE_ENV: test
          POSTGRES_URL: postgres://postgres:postgres@localhost:5432/support_system_test

      - name: Run tests
        run: pnpm test
        if: false # Disable until tests are implemented
        env:
          NODE_ENV: test
          POSTGRES_URL: postgres://postgres:postgres@localhost:5432/support_system_test

  # Job 2: Build and Docker
  build:
    name: Build & Docker
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push'

    defaults:
      run:
        working-directory: ./apps/support-system

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          cache-dependency-path: './apps/support-system/pnpm-lock.yaml'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build application
        run: pnpm run build
        env:
          NODE_ENV: production
          POSTGRES_URL: postgres://dummy:dummy@localhost:5432/dummy
          PAYLOAD_SECRET: build-secret

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./apps/support-system
          file: ./apps/support-system/Dockerfile
          push: false
          tags: support-system:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Job 3: Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test

    defaults:
      run:
        working-directory: ./apps/support-system

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run security audit
        run: pnpm audit --audit-level moderate

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        continue-on-error: true
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
