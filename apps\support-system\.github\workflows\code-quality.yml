name: Code Quality

on:
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/support-system/**'

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Job 1: Code Quality Analysis
  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./apps/support-system

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch full history for better analysis

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          cache-dependency-path: './apps/support-system/pnpm-lock.yaml'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run ESLint with annotations
        run: |
          pnpm run lint --format=@microsoft/eslint-formatter-sarif --output-file=eslint-results.sarif
        continue-on-error: true

      - name: Upload ESLint results to GitHub
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: ./apps/support-system/eslint-results.sarif
          wait-for-processing: true

      - name: Run Prettier check
        run: |
          npx prettier --check "src/**/*.{ts,tsx,js,jsx,json,css,md}"

      - name: TypeScript compilation check
        run: |
          npx tsc --noEmit

      - name: Check for TODO/FIXME comments
        run: |
          echo "Checking for TODO/FIXME comments..."
          if grep -r "TODO\|FIXME" src/ --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx"; then
            echo "⚠️ Found TODO/FIXME comments. Consider addressing them."
          else
            echo "✅ No TODO/FIXME comments found."
          fi

  # Job 2: Bundle Analysis
  bundle-analysis:
    name: Bundle Analysis
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./apps/support-system

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build for analysis
        run: |
          pnpm run build
        env:
          ANALYZE: true
          NODE_ENV: production
          POSTGRES_URL: postgres://dummy:dummy@localhost:5432/dummy
          PAYLOAD_SECRET: build-secret

      - name: Analyze bundle size
        run: |
          echo "Bundle analysis completed"
          # Add bundle size analysis tools here
          # Example: npx @next/bundle-analyzer

  # Job 3: Performance Testing
  performance:
    name: Performance Testing
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./apps/support-system

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build application
        run: pnpm run build
        env:
          NODE_ENV: production
          POSTGRES_URL: postgres://dummy:dummy@localhost:5432/dummy
          PAYLOAD_SECRET: build-secret

      - name: Run Lighthouse CI
        run: |
          echo "Lighthouse CI would run here"
          # npm install -g @lhci/cli
          # lhci autorun
