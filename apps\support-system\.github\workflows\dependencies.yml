name: Dependency Management

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Job 1: Update Dependencies
  update-dependencies:
    name: Update Dependencies
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: ./apps/support-system

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          cache-dependency-path: './apps/support-system/pnpm-lock.yaml'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Update dependencies
        run: |
          pnpm update --latest
          pnpm audit fix

      - name: Run tests after update
        run: |
          pnpm run lint
          # pnpm test (when tests are implemented)

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update dependencies'
          title: 'chore: automated dependency updates'
          body: |
            This PR contains automated dependency updates.
            
            Please review the changes and ensure all tests pass before merging.
            
            - Updated to latest compatible versions
            - Fixed security vulnerabilities (if any)
            - Ran linting and tests
          branch: chore/dependency-updates
          delete-branch: true
          path: ./apps/support-system

  # Job 2: Security Audit
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: ./apps/support-system

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run security audit
        run: |
          pnpm audit --audit-level moderate
          echo "Security audit completed"

      - name: Create security issue if vulnerabilities found
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: 'Security vulnerabilities detected in dependencies',
              body: 'Automated security audit found vulnerabilities in dependencies. Please review and update.',
              labels: ['security', 'dependencies']
            })
