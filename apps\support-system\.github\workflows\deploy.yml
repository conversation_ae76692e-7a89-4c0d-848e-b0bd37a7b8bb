name: Deploy to Production

on:
  push:
    branches: [ main ]
    paths:
      - 'apps/support-system/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'
  REGISTRY: ghcr.io
  IMAGE_NAME: support-system

jobs:
  # Job 1: Build and Push Docker Image
  build-and-push:
    name: Build & Push Docker Image
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    defaults:
      run:
        working-directory: ./apps/support-system

    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: ./apps/support-system
          file: ./apps/support-system/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # Job 2: Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    environment:
      name: staging
      url: https://support-staging.your-domain.com

    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
          # Add your staging deployment commands here
          # Examples:
          # - Deploy to Kubernetes
          # - Deploy to Docker Swarm
          # - Deploy to cloud provider (AWS ECS, Azure Container Instances, etc.)
          # - Update docker-compose on staging server

      - name: Run health check
        run: |
          echo "Running health check on staging..."
          # curl -f https://support-staging.your-domain.com/api/health

  # Job 3: Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-and-push, deploy-staging]
    if: github.ref == 'refs/heads/main' && github.event.inputs.environment == 'production'
    environment:
      name: production
      url: https://support.your-domain.com

    steps:
      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
          # Add your production deployment commands here

      - name: Run health check
        run: |
          echo "Running health check on production..."
          # curl -f https://support.your-domain.com/api/health

      - name: Notify deployment success
        if: success()
        run: |
          echo "Production deployment successful!"
          # Add notification logic (Slack, Discord, email, etc.)

      - name: Notify deployment failure
        if: failure()
        run: |
          echo "Production deployment failed!"
          # Add failure notification logic
