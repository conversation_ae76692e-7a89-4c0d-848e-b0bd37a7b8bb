name: Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/support-system/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/support-system/**'

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  test:
    name: Unit Tests
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./apps/support-system

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          cache-dependency-path: './apps/support-system/pnpm-lock.yaml'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tests
        run: pnpm run test:ci
        env:
          NODE_ENV: test

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        if: always()
        with:
          file: ./apps/support-system/coverage/lcov.info
          flags: support-system
          name: support-system-coverage
          fail_ci_if_error: false

  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: support_system_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    defaults:
      run:
        working-directory: ./apps/support-system

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          cache-dependency-path: './apps/support-system/pnpm-lock.yaml'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Create test environment file
        run: |
          cat > .env.test << EOF
          NODE_ENV=test
          POSTGRES_URL=postgres://postgres:postgres@localhost:5432/support_system_test
          DATABASE_URL=postgres://postgres:postgres@localhost:5432/support_system_test
          REDIS_URL=redis://localhost:6379
          PAYLOAD_SECRET=test-secret-key
          PAYLOAD_CONFIG_PATH=src/payload.config.ts
          NEXTAUTH_SECRET=test-nextauth-secret
          NEXTAUTH_URL=http://localhost:3000
          EOF

      - name: Run integration tests
        run: pnpm run test -- --testPathPattern=integration
        env:
          NODE_ENV: test
          POSTGRES_URL: postgres://postgres:postgres@localhost:5432/support_system_test
