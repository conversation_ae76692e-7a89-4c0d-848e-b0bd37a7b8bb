# Dependencies
node_modules/
pnpm-lock.yaml
package-lock.json
yarn.lock

# Build outputs
.next/
out/
build/
dist/

# Generated files
*.tsbuildinfo
next-env.d.ts
payload-types.ts

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env*

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Docker
Dockerfile*
.dockerignore

# Git
.git/
.gitignore

# Coverage
coverage/

# Uploads
uploads/

# Documentation that should maintain formatting
CHANGELOG.md
LICENSE
