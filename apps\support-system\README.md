# Support Ticket System

A comprehensive support ticket management system built with Next.js, Payload CMS, and PostgreSQL.

## Prerequisites

- Node.js 18+ and pnpm
- Docker and Docker Compose (for databases)
- PostgreSQL 15+ (if not using Docker)
- Redis 7+ (if not using Docker)

## Quick Start

### 1. Install Dependencies

```bash
pnpm install
```

### 2. Setup Databases (Docker - Recommended)

**Install Docker Desktop first if not already installed:**
- Download from: https://www.docker.com/products/docker-desktop/
- Follow installation instructions for your OS

**Start development databases:**

```bash
# Start PostgreSQL and Redis for development
docker compose -f docker-compose.dev.yml up -d

# Check if services are running
docker compose -f docker-compose.dev.yml ps
```

### 3. Setup Environment Variables

```bash
# Copy the example environment file
cp .env.example .env.local

# Edit .env.local with your configuration
# The default values should work with the Docker setup
```

### 4. Initialize Database

```bash
# Generate Payload types
pnpm run generate:types

# Start the development server
pnpm run dev
```

### 5. Access the Application

- **Frontend**: http://localhost:3000
- **Admin Panel**: http://localhost:3000/admin
- **API**: http://localhost:3000/api

## Database Configuration

### Development (Docker)
- **PostgreSQL**: localhost:5433
- **Redis**: localhost:6380
- **Database**: support_system_dev

### Production (Docker)
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **Database**: support_system

## Manual Database Setup (Alternative to Docker)

If you prefer not to use Docker:

### PostgreSQL Setup
1. Install PostgreSQL 15+
2. Create database: `CREATE DATABASE support_system_dev;`
3. Update POSTGRES_URL in .env.local

### Redis Setup
1. Install Redis 7+
2. Start Redis server
3. Update REDIS_URL in .env.local

## Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint
- `pnpm payload` - Run Payload CLI commands
- `pnpm generate:types` - Generate TypeScript types
- `pnpm generate:schema` - Generate GraphQL schema

## Docker Commands

```bash
# Development
docker compose -f docker-compose.dev.yml up -d    # Start dev databases
docker compose -f docker-compose.dev.yml down     # Stop dev databases
docker compose -f docker-compose.dev.yml logs     # View logs

# Production
docker compose up -d                               # Start all services
docker compose down                                # Stop all services
docker compose logs                                # View logs
```

## Project Structure

```
apps/support-system/
├── src/
│   ├── app/                    # Next.js App Router
│   ├── payload.config.ts       # Payload CMS configuration
│   └── payload-config.ts       # Config alias
├── docker-compose.yml          # Production Docker setup
├── docker-compose.dev.yml      # Development Docker setup
├── init-scripts/               # Database initialization
└── uploads/                    # File uploads (created automatically)
```

## Next Steps

1. Install Docker Desktop if not already installed
2. Start the development databases
3. Configure your environment variables
4. Begin development with `pnpm run dev`
