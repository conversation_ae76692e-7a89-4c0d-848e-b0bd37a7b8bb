version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres-dev:
    image: postgres:15-alpine
    container_name: support-system-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: support_system_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - support-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d support_system_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Development
  redis-dev:
    image: redis:7-alpine
    container_name: support-system-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_dev_data:/data
    networks:
      - support-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  support-dev-network:
    driver: bridge
