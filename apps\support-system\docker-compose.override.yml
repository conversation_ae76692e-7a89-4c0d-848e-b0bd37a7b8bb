# Docker Compose Override for Development
# This file is automatically loaded by docker-compose for development overrides

version: '3.8'

services:
  # Development overrides for the app service
  app:
    build:
      target: deps  # Use deps stage for development
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - POSTGRES_URL=******************************************/support_system_dev
      - DATABASE_URL=******************************************/support_system_dev
    command: pnpm run dev
    ports:
      - "3000:3000"
      - "9229:9229"  # Node.js debugging port

  # Development database with different name
  postgres:
    environment:
      POSTGRES_DB: support_system_dev
