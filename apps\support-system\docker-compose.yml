version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: support-system-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: support_system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - support-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d support_system"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for sessions and caching
  redis:
    image: redis:7-alpine
    container_name: support-system-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - support-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes

  # Support System Application (for production)
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: support-system-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - POSTGRES_URL=******************************************/support_system
      - DATABASE_URL=******************************************/support_system
      - REDIS_URL=redis://redis:6379
      - PAYLOAD_SECRET=your-secret-key-change-this-in-production
      - PAYLOAD_CONFIG_PATH=src/payload.config.ts
      - NEXTAUTH_SECRET=your-nextauth-secret-change-this-in-production
      - NEXTAUTH_URL=http://localhost:3000
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - support-network
    volumes:
      - app_uploads:/app/uploads
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local

networks:
  support-network:
    driver: bridge
