# Support System Database Schema Design

## Overview

This document outlines the comprehensive database schema design for the multi-tenant support ticket system. The schema builds upon our existing authentication and multi-tenant infrastructure, incorporating ITIL best practices and preparing for AI-powered features.

## Design Principles

### 1. Multi-Tenancy Strategy
- **Shared Schema with Tenant ID**: All support-related tables include `instituteId` for data isolation
- **Row-Level Security**: PostgreSQL RLS enforced for additional security
- **Branch-Level Granularity**: Support for institute → branch hierarchy

### 2. ITIL Compliance
- Support for Incident, Problem, Change, and Service Request ticket types
- Comprehensive status and workflow tracking
- SLA management and tracking
- Audit trails for all operations

### 3. Scalability Considerations
- Proper indexing strategy for multi-tenant queries
- Partitioning strategy for high-volume tables
- Optimized for PostgreSQL-specific features

### 4. AI-Ready Architecture
- Flexible metadata storage for AI analysis results
- Audit trails for AI recommendations and actions
- Extensible schema for future AI features

## Core Entities and Relationships

```
Institute (1) ──── (N) Branch
    │                   │
    │                   │
    └─── (N) User ──────┘
            │
            │
    ┌───────┴───────┐
    │               │
    ▼               ▼
SupportTicket ── TicketMessage
    │               │
    ├── TicketAttachment
    ├── TicketNote
    ├── TicketAnalytics
    └── TicketHistory

SupportCategory ── SupportTicket
TicketTemplate ── SupportTicket
```

## Detailed Schema Design

### 1. Support Categories

```sql
CREATE TABLE support_categories (
    id VARCHAR(30) PRIMARY KEY DEFAULT generate_cuid(),
    institute_id VARCHAR(30) NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50), -- Icon identifier
    
    -- SLA Configuration
    response_time_hours INTEGER DEFAULT 24,
    resolution_time_hours INTEGER DEFAULT 72,
    
    -- Routing Rules
    auto_assign_to VARCHAR(30) REFERENCES users(id),
    escalation_rules JSONB,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    
    -- Audit
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by VARCHAR(30) REFERENCES users(id),
    
    UNIQUE(institute_id, name)
);
```

### 2. Ticket Templates

```sql
CREATE TABLE ticket_templates (
    id VARCHAR(30) PRIMARY KEY DEFAULT generate_cuid(),
    institute_id VARCHAR(30) NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    category_id VARCHAR(30) REFERENCES support_categories(id),
    
    name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- Template Content
    title_template VARCHAR(255),
    content_template TEXT,
    variables JSONB, -- Template variables and defaults
    
    -- Configuration
    default_priority ticket_priority DEFAULT 'MEDIUM',
    default_status ticket_status DEFAULT 'OPEN',
    auto_assign_to VARCHAR(30) REFERENCES users(id),
    
    -- Usage Tracking
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Audit
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by VARCHAR(30) REFERENCES users(id),
    
    UNIQUE(institute_id, name)
);
```

### 3. Support Tickets (Core Entity)

```sql
CREATE TYPE ticket_status AS ENUM (
    'OPEN', 'IN_PROGRESS', 'PENDING_CUSTOMER', 'PENDING_VENDOR', 
    'RESOLVED', 'CLOSED', 'CANCELLED'
);

CREATE TYPE ticket_priority AS ENUM (
    'LOW', 'MEDIUM', 'HIGH', 'URGENT', 'CRITICAL'
);

CREATE TYPE ticket_type AS ENUM (
    'INCIDENT', 'PROBLEM', 'CHANGE_REQUEST', 'SERVICE_REQUEST'
);

CREATE TABLE support_tickets (
    id VARCHAR(30) PRIMARY KEY DEFAULT generate_cuid(),
    ticket_number VARCHAR(20) UNIQUE NOT NULL, -- Auto-generated: INST-YYYY-NNNNNN
    
    -- Multi-tenant fields
    institute_id VARCHAR(30) NOT NULL REFERENCES institutes(id) ON DELETE CASCADE,
    branch_id VARCHAR(30) REFERENCES branches(id),
    
    -- Core ticket information
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    status ticket_status DEFAULT 'OPEN',
    priority ticket_priority DEFAULT 'MEDIUM',
    type ticket_type DEFAULT 'INCIDENT',
    
    -- Categorization
    category_id VARCHAR(30) REFERENCES support_categories(id),
    tags TEXT[], -- Array of tags for flexible categorization
    
    -- Assignment and ownership
    created_by VARCHAR(30) NOT NULL REFERENCES users(id),
    assigned_to VARCHAR(30) REFERENCES users(id),
    assigned_team VARCHAR(100), -- Team/department assignment
    
    -- Customer information (if different from created_by)
    customer_name VARCHAR(100),
    customer_email VARCHAR(255),
    customer_phone VARCHAR(20),
    
    -- SLA tracking
    sla_response_due TIMESTAMP,
    sla_resolution_due TIMESTAMP,
    first_response_at TIMESTAMP,
    resolved_at TIMESTAMP,
    closed_at TIMESTAMP,
    
    -- Source tracking
    source VARCHAR(50) DEFAULT 'WEB', -- WEB, EMAIL, PHONE, CHAT, API
    source_reference VARCHAR(255), -- External reference ID
    
    -- AI and automation
    ai_analysis JSONB, -- AI-generated insights and suggestions
    automation_rules_applied TEXT[],
    
    -- Custom fields (flexible metadata)
    custom_fields JSONB,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_assignment CHECK (
        assigned_to IS NULL OR 
        (SELECT institute_id FROM users WHERE id = assigned_to) = institute_id
    )
);
```

### 4. Ticket Messages (Conversations)

```sql
CREATE TYPE message_type AS ENUM (
    'CUSTOMER_REPLY', 'AGENT_REPLY', 'INTERNAL_NOTE', 'SYSTEM_MESSAGE'
);

CREATE TYPE message_visibility AS ENUM (
    'PUBLIC', 'INTERNAL', 'PRIVATE'
);

CREATE TABLE ticket_messages (
    id VARCHAR(30) PRIMARY KEY DEFAULT generate_cuid(),
    ticket_id VARCHAR(30) NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE,
    
    -- Message content
    content TEXT NOT NULL,
    content_type VARCHAR(20) DEFAULT 'text', -- text, html, markdown
    message_type message_type NOT NULL,
    visibility message_visibility DEFAULT 'PUBLIC',
    
    -- Author information
    author_id VARCHAR(30) REFERENCES users(id),
    author_name VARCHAR(100), -- For external/anonymous messages
    author_email VARCHAR(255),
    
    -- Threading
    parent_message_id VARCHAR(30) REFERENCES ticket_messages(id),
    thread_position INTEGER DEFAULT 0,
    
    -- Source and metadata
    source VARCHAR(50) DEFAULT 'WEB', -- WEB, EMAIL, PHONE, CHAT, API
    source_message_id VARCHAR(255), -- External message ID (email, etc.)
    
    -- AI processing
    ai_sentiment DECIMAL(3,2), -- -1.0 to 1.0
    ai_language VARCHAR(10),
    ai_summary TEXT,
    
    -- Status
    is_edited BOOLEAN DEFAULT false,
    edited_at TIMESTAMP,
    edited_by VARCHAR(30) REFERENCES users(id),
    
    -- Audit
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 5. Ticket Attachments

```sql
CREATE TABLE ticket_attachments (
    id VARCHAR(30) PRIMARY KEY DEFAULT generate_cuid(),
    ticket_id VARCHAR(30) NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE,
    message_id VARCHAR(30) REFERENCES ticket_messages(id) ON DELETE CASCADE,
    
    -- File information
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64), -- SHA-256 hash for deduplication
    
    -- Upload information
    uploaded_by VARCHAR(30) REFERENCES users(id),
    upload_source VARCHAR(50) DEFAULT 'WEB',
    
    -- Access control
    visibility message_visibility DEFAULT 'PUBLIC',
    download_count INTEGER DEFAULT 0,
    
    -- Virus scanning
    scan_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, CLEAN, INFECTED, ERROR
    scan_result JSONB,
    
    -- Audit
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 6. Ticket Notes (Internal)

```sql
CREATE TABLE ticket_notes (
    id VARCHAR(30) PRIMARY KEY DEFAULT generate_cuid(),
    ticket_id VARCHAR(30) NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE,
    
    -- Note content
    content TEXT NOT NULL,
    note_type VARCHAR(50) DEFAULT 'GENERAL', -- GENERAL, ESCALATION, RESOLUTION, FOLLOWUP
    
    -- Author
    author_id VARCHAR(30) NOT NULL REFERENCES users(id),
    
    -- Visibility (internal notes only)
    visibility VARCHAR(20) DEFAULT 'TEAM', -- TEAM, DEPARTMENT, ADMIN_ONLY
    
    -- Pinning and importance
    is_pinned BOOLEAN DEFAULT false,
    importance VARCHAR(20) DEFAULT 'NORMAL', -- LOW, NORMAL, HIGH
    
    -- Audit
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 7. Ticket Analytics

```sql
CREATE TABLE ticket_analytics (
    id VARCHAR(30) PRIMARY KEY DEFAULT generate_cuid(),
    ticket_id VARCHAR(30) NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE,
    
    -- Response time metrics (in minutes)
    first_response_time INTEGER,
    avg_response_time INTEGER,
    total_response_time INTEGER,
    response_count INTEGER DEFAULT 0,
    
    -- Resolution metrics (in minutes)
    resolution_time INTEGER,
    time_to_close INTEGER,
    
    -- Activity metrics
    message_count INTEGER DEFAULT 0,
    internal_note_count INTEGER DEFAULT 0,
    attachment_count INTEGER DEFAULT 0,
    assignment_changes INTEGER DEFAULT 0,
    status_changes INTEGER DEFAULT 0,
    
    -- Customer satisfaction
    satisfaction_score INTEGER, -- 1-5 scale
    satisfaction_comment TEXT,
    satisfaction_date TIMESTAMP,
    
    -- SLA compliance
    sla_response_met BOOLEAN,
    sla_resolution_met BOOLEAN,
    sla_response_breach_time INTEGER, -- Minutes over SLA
    sla_resolution_breach_time INTEGER,
    
    -- Agent performance
    agent_id VARCHAR(30) REFERENCES users(id),
    agent_workload_score DECIMAL(5,2), -- Calculated workload metric
    
    -- AI insights
    ai_complexity_score DECIMAL(3,2), -- 0.0 to 1.0
    ai_escalation_risk DECIMAL(3,2), -- 0.0 to 1.0
    ai_resolution_prediction INTEGER, -- Predicted resolution time in minutes
    
    -- Audit
    calculated_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## Indexing Strategy

### Primary Indexes
```sql
-- Multi-tenant queries
CREATE INDEX idx_support_tickets_institute_status ON support_tickets(institute_id, status);
CREATE INDEX idx_support_tickets_institute_assigned ON support_tickets(institute_id, assigned_to);
CREATE INDEX idx_support_tickets_branch_status ON support_tickets(branch_id, status) WHERE branch_id IS NOT NULL;

-- Performance critical queries
CREATE INDEX idx_support_tickets_created_at ON support_tickets(created_at DESC);
CREATE INDEX idx_support_tickets_updated_at ON support_tickets(updated_at DESC);
CREATE INDEX idx_support_tickets_number ON support_tickets(ticket_number);

-- Message queries
CREATE INDEX idx_ticket_messages_ticket_created ON ticket_messages(ticket_id, created_at);
CREATE INDEX idx_ticket_messages_author ON ticket_messages(author_id, created_at);

-- Analytics queries
CREATE INDEX idx_ticket_analytics_agent_date ON ticket_analytics(agent_id, calculated_at);
CREATE INDEX idx_ticket_analytics_institute_sla ON ticket_analytics(ticket_id) 
    INCLUDE (sla_response_met, sla_resolution_met);

-- Search indexes
CREATE INDEX idx_support_tickets_search ON support_tickets 
    USING gin(to_tsvector('english', title || ' ' || description));
CREATE INDEX idx_ticket_messages_search ON ticket_messages 
    USING gin(to_tsvector('english', content));
```

### Composite Indexes for Common Queries
```sql
-- Dashboard queries
CREATE INDEX idx_tickets_dashboard ON support_tickets(institute_id, status, priority, created_at DESC);
CREATE INDEX idx_tickets_assigned_dashboard ON support_tickets(assigned_to, status, priority, updated_at DESC);

-- Reporting queries
CREATE INDEX idx_tickets_reporting ON support_tickets(institute_id, created_at, status, category_id);
CREATE INDEX idx_analytics_reporting ON ticket_analytics(ticket_id) 
    INCLUDE (resolution_time, satisfaction_score, sla_resolution_met);
```

## Row-Level Security (RLS)

```sql
-- Enable RLS on all support tables
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_templates ENABLE ROW LEVEL SECURITY;

-- Example RLS policy for support_tickets
CREATE POLICY support_tickets_institute_isolation ON support_tickets
    FOR ALL TO authenticated_users
    USING (institute_id = current_setting('app.current_institute_id'));

-- Branch-level isolation for support staff
CREATE POLICY support_tickets_branch_isolation ON support_tickets
    FOR ALL TO support_staff_role
    USING (
        institute_id = current_setting('app.current_institute_id') AND
        (branch_id = current_setting('app.current_branch_id') OR 
         current_setting('app.user_role') = 'INSTITUTE_ADMIN')
    );
```

## Next Steps

1. **Implement Core Tables**: Start with SupportTickets and SupportCategories
2. **Add Payload CMS Collections**: Configure collections for each table
3. **Implement Indexing**: Add performance-critical indexes
4. **Set up RLS**: Configure row-level security policies
5. **Create Seed Data**: Add initial categories and templates
6. **Test Performance**: Validate query performance with realistic data volumes

This schema design provides a solid foundation for a scalable, secure, and feature-rich support ticket system while maintaining compatibility with our existing multi-tenant authentication infrastructure.
