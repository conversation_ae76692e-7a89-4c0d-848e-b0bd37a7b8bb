-- Initialize Support System Database
-- This script runs when PostgreSQL container starts for the first time

-- Create extensions that might be useful
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create a dedicated user for the application (optional, for production)
-- CREATE USER support_app WITH PASSWORD 'app_password';
-- GRANT ALL PRIVILEGES ON DATABASE support_system TO support_app;

-- Set timezone
SET timezone = 'UTC';

-- Create initial schema (Payload will handle table creation)
-- This is just for any custom setup we might need

-- Log initialization
INSERT INTO pg_stat_statements_info (dealloc) VALUES (0) ON CONFLICT DO NOTHING;
