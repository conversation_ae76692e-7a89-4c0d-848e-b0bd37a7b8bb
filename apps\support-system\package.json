{"name": "support-system", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts next dev --turbopack", "build": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts next build", "start": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "code-quality": "pnpm run lint && pnpm run format:check && pnpm run type-check", "code-fix": "pnpm run lint:fix && pnpm run format", "payload": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload", "generate:types": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:types", "generate:schema": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:graphql-schema", "prepare": "husky install", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:lms": "npx tsx scripts/test-lms-integration.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@payloadcms/db-postgres": "^3.45.0", "@payloadcms/next": "3.43.0", "@payloadcms/richtext-lexical": "3.43.0", "@payloadcms/ui": "3.43.0", "@prisma/client": "^6.11.1", "@upstash/redis": "^1.35.1", "bcryptjs": "^3.0.2", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "ioredis": "^5.6.1", "next": "15.3.5", "next-auth": "^4.24.11", "payload": "^3.45.0", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "redis": "^5.5.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^3.0.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "husky": "^9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}