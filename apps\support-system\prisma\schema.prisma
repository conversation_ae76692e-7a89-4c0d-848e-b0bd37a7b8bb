// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?   // For credentials provider

  // LMS Integration fields
  lmsUserId     String?   @unique // Reference to LMS user ID
  role          UserRole  @default(SUPPORT_STAFF)
  instituteId   String?   // Institute association
  branchId      String?   // Branch association

  // Audit fields
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastLoginAt   DateTime?
  isActive      Boolean   @default(true)

  accounts Account[]
  sessions Session[]

  // Relations
  institute Institute? @relation(fields: [instituteId], references: [id], onDelete: SetNull)
  branch    Branch?    @relation(fields: [branchId], references: [id], onDelete: SetNull)

  // Support ticket relations
  createdTickets  SupportTicket[] @relation("TicketCreator")
  assignedTickets SupportTicket[] @relation("TicketAssignee")

  // Message relations
  messages TicketMessage[]

  // Attachment relations
  uploadedAttachments TicketAttachment[]

  // Note relations
  authoredNotes           TicketNote[] @relation("NoteAuthor")
  followUpAssignedNotes   TicketNote[] @relation("NoteFollowUpAssigned")
  escalatedToNotes        TicketNote[] @relation("NoteEscalatedTo")
  previousAssigneeNotes   TicketNote[] @relation("NotePreviousAssignee")

  // Performance indexes
  @@index([instituteId, role, isActive])
  @@index([branchId, role, isActive])
  @@index([role, isActive])
  @@index([lastLoginAt])
  @@index([createdAt])
  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@index([expires])
  @@map("verification_tokens")
}

// User roles enum
enum UserRole {
  SUPER_ADMIN
  INSTITUTE_ADMIN
  SUPPORT_STAFF
  STUDENT
}

// Support ticket enums
enum TicketStatus {
  OPEN
  IN_PROGRESS
  PENDING_CUSTOMER
  PENDING_VENDOR
  RESOLVED
  CLOSED
  CANCELLED
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
  CRITICAL
}

enum TicketType {
  INCIDENT
  PROBLEM
  CHANGE_REQUEST
  SERVICE_REQUEST
}

enum MessageType {
  CUSTOMER_REPLY
  AGENT_REPLY
  INTERNAL_NOTE
  SYSTEM_MESSAGE
}

enum MessageVisibility {
  PUBLIC
  INTERNAL
  PRIVATE
}

// Institute model for data isolation
model Institute {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  email       String?
  phone       String?
  website     String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  branches    Branch[]
  users       User[]

  // Support system relations
  supportCategories SupportCategory[]
  ticketTemplates   TicketTemplate[]
  supportTickets    SupportTicket[]

  // Performance indexes
  @@index([isActive])
  @@index([createdAt])
  @@map("institutes")
}

// Branch model for sub-institute organization
model Branch {
  id          String    @id @default(cuid())
  name        String
  code        String    @unique
  instituteId String
  address     String?
  phone       String?
  email       String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  institute   Institute @relation(fields: [instituteId], references: [id], onDelete: Cascade)
  users       User[]

  // Support system relations
  supportTickets SupportTicket[]

  // Performance indexes
  @@index([instituteId, isActive])
  @@index([isActive])
  @@index([createdAt])
  @@map("branches")
}

// Support Categories for ticket classification
model SupportCategory {
  id          String   @id @default(cuid())
  instituteId String
  name        String
  description String?
  color       String?  // Hex color code
  icon        String?  // Icon identifier

  // SLA Configuration
  responseTimeHours   Int @default(24)
  resolutionTimeHours Int @default(72)

  // Routing Rules
  autoAssignTo    String? // User ID for auto-assignment
  escalationRules Json?   // JSON rules for escalation

  // Status and ordering
  isActive   Boolean @default(true)
  sortOrder  Int     @default(0)

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?  // User ID who created this category

  // Relations
  institute Institute @relation(fields: [instituteId], references: [id], onDelete: Cascade)
  tickets   SupportTicket[]
  templates TicketTemplate[]

  // Constraints
  @@unique([instituteId, name])
  // Performance indexes
  @@index([instituteId, isActive, sortOrder])
  @@index([isActive, sortOrder])
  @@index([autoAssignTo])
  @@index([createdAt])
  @@map("support_categories")
}

// Ticket Templates for quick ticket creation
model TicketTemplate {
  id          String  @id @default(cuid())
  instituteId String
  categoryId  String?
  name        String
  description String?

  // Template Content
  titleTemplate   String?
  contentTemplate String?
  variables       Json?   // Template variables and defaults

  // Default Configuration
  defaultPriority TicketPriority @default(MEDIUM)
  defaultStatus   TicketStatus   @default(OPEN)
  autoAssignTo    String?        // User ID for auto-assignment

  // Usage Tracking
  usageCount  Int       @default(0)
  lastUsedAt  DateTime?

  // Status
  isActive Boolean @default(true)

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?  // User ID who created this template

  // Relations
  institute Institute        @relation(fields: [instituteId], references: [id], onDelete: Cascade)
  category  SupportCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  tickets   SupportTicket[]

  // Constraints
  @@unique([instituteId, name])
  // Performance indexes
  @@index([instituteId, isActive])
  @@index([categoryId, isActive])
  @@index([isActive, usageCount])
  @@index([lastUsedAt])
  @@index([autoAssignTo])
  @@index([createdAt])
  @@map("ticket_templates")
}

// Core Support Ticket model
model SupportTicket {
  id           String @id @default(cuid())
  ticketNumber String @unique // Auto-generated: INST-YYYY-NNNNNN

  // Multi-tenant fields
  instituteId String
  branchId    String?

  // Core ticket information
  title       String
  description String
  status      TicketStatus   @default(OPEN)
  priority    TicketPriority @default(MEDIUM)
  type        TicketType     @default(INCIDENT)

  // Categorization
  categoryId String?
  tags       String[] // Array of tags for flexible categorization

  // Assignment and ownership
  createdBy    String
  assignedTo   String? // User ID of assigned agent
  assignedTeam String? // Team/department assignment
  templateId   String? // Template used to create this ticket

  // Customer information (if different from created_by)
  customerName  String?
  customerEmail String?
  customerPhone String?

  // SLA tracking
  slaResponseDue   DateTime?
  slaResolutionDue DateTime?
  firstResponseAt  DateTime?
  resolvedAt       DateTime?
  closedAt         DateTime?

  // Source tracking
  source          String @default("WEB") // WEB, EMAIL, PHONE, CHAT, API
  sourceReference String? // External reference ID

  // AI and automation
  aiAnalysis             Json?    // AI-generated insights and suggestions
  automationRulesApplied String[] // Array of automation rule IDs applied

  // Custom fields (flexible metadata)
  customFields Json?

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  institute Institute         @relation(fields: [instituteId], references: [id], onDelete: Cascade)
  branch    Branch?           @relation(fields: [branchId], references: [id], onDelete: SetNull)
  category  SupportCategory?  @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  template  TicketTemplate?   @relation(fields: [templateId], references: [id], onDelete: SetNull)
  creator   User              @relation("TicketCreator", fields: [createdBy], references: [id], onDelete: Restrict)
  assignee  User?             @relation("TicketAssignee", fields: [assignedTo], references: [id], onDelete: SetNull)

  // Related collections
  messages    TicketMessage[]
  attachments TicketAttachment[]
  notes       TicketNote[]
  analytics   TicketAnalytics?

  // Performance indexes - Multi-tenant queries (most critical)
  @@index([instituteId, status])
  @@index([instituteId, assignedTo, status])
  @@index([instituteId, priority, status])
  @@index([instituteId, createdAt])
  @@index([branchId, status])
  @@index([branchId, assignedTo, status])

  // Assignment and workflow indexes
  @@index([assignedTo, status])
  @@index([createdBy, status])
  @@index([status, priority])
  @@index([status, updatedAt])

  // SLA and time-based indexes
  @@index([slaResponseDue])
  @@index([slaResolutionDue])
  @@index([firstResponseAt])
  @@index([resolvedAt])
  @@index([closedAt])

  // Categorization indexes
  @@index([categoryId, status])
  @@index([templateId])
  @@index([type, status])

  // Time-based queries
  @@index([createdAt])
  @@index([updatedAt])

  // Source tracking
  @@index([source])
  @@index([sourceReference])

  @@map("support_tickets")
}

// Ticket Messages for conversations
model TicketMessage {
  id        String @id @default(cuid())
  ticketId  String

  // Message content
  content     String
  contentType String @default("text") // text, html, markdown
  messageType MessageType
  visibility  MessageVisibility @default(PUBLIC)

  // Author information
  authorId    String? // User ID for internal users
  authorName  String? // Name for external/anonymous messages
  authorEmail String? // Email for external/anonymous messages

  // Threading
  parentMessageId String? // For reply threading
  threadPosition  Int     @default(0)

  // AI Analysis (for future use)
  sentiment     String? // POSITIVE, NEUTRAL, NEGATIVE
  urgencyScore  Float?  // 0-10 AI-calculated urgency
  aiCategories  String[] // AI-detected categories

  // Delivery tracking
  deliveryStatus String    @default("DELIVERED") // PENDING, DELIVERED, FAILED, BOUNCED
  deliveredAt    DateTime?
  readAt         DateTime?

  // Multi-tenant fields
  instituteId String
  branchId    String?

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ticket        SupportTicket  @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  author        User?          @relation(fields: [authorId], references: [id], onDelete: SetNull)
  parentMessage TicketMessage? @relation("MessageReplies", fields: [parentMessageId], references: [id], onDelete: SetNull)
  replies       TicketMessage[] @relation("MessageReplies")
  attachments   TicketAttachment[]

  // Performance indexes
  @@index([ticketId, createdAt])
  @@index([ticketId, messageType, createdAt])
  @@index([authorId, createdAt])
  @@index([instituteId, messageType])
  @@index([parentMessageId])
  @@index([messageType, visibility])
  @@index([deliveryStatus])
  @@index([createdAt])
  @@index([updatedAt])
  @@map("ticket_messages")
}

// Ticket Attachments for file uploads
model TicketAttachment {
  id        String  @id @default(cuid())
  ticketId  String
  messageId String? // Optional - attachment can belong to a specific message

  // File information
  filename         String // Generated filename
  originalFilename String // Original filename from upload
  filePath         String // Path to file in storage
  fileSize         BigInt // File size in bytes
  mimeType         String // MIME type
  fileHash         String? // SHA-256 hash for deduplication
  description      String? // Optional description

  // Upload information
  uploadedBy   String? // User ID who uploaded
  uploadSource String  @default("WEB") // WEB, EMAIL, API, MOBILE

  // Access control
  visibility       MessageVisibility @default(PUBLIC)
  downloadCount    Int               @default(0)
  lastDownloadedAt DateTime?

  // Security information
  virusScanStatus String  @default("PENDING") // PENDING, CLEAN, INFECTED, ERROR, SKIPPED
  scanResults     Json? // Detailed scan results
  quarantined     Boolean @default(false)

  // Multi-tenant fields
  instituteId String
  branchId    String?

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ticket     SupportTicket  @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  message    TicketMessage? @relation(fields: [messageId], references: [id], onDelete: Cascade)
  uploadedByUser User?       @relation(fields: [uploadedBy], references: [id], onDelete: SetNull)

  // Performance indexes
  @@index([ticketId, createdAt])
  @@index([messageId, createdAt])
  @@index([uploadedBy, createdAt])
  @@index([instituteId, uploadSource])
  @@index([virusScanStatus])
  @@index([quarantined])
  @@index([visibility])
  @@index([fileHash]) // For deduplication
  @@index([mimeType])
  @@index([createdAt])
  @@map("ticket_attachments")
}

// Ticket Notes for internal documentation
model TicketNote {
  id       String @id @default(cuid())
  ticketId String

  // Note content
  content  String
  noteType String @default("GENERAL") // GENERAL, ESCALATION, RESOLUTION, FOLLOWUP, INVESTIGATION, CUSTOMER_CONTACT, TECHNICAL

  // Author
  authorId String

  // Visibility and importance
  visibility String @default("TEAM") // TEAM, DEPARTMENT, ADMIN_ONLY, PERSONAL
  importance String @default("NORMAL") // LOW, NORMAL, HIGH, CRITICAL
  isPinned   Boolean @default(false)

  // Tags for organization
  tags String[] // Array of tags

  // Follow-up information (when noteType = FOLLOWUP)
  followUpDate   DateTime?
  followUpAssignedTo String? // User ID
  followUpCompleted  Boolean @default(false)
  followUpCompletedAt DateTime?

  // Escalation information (when noteType = ESCALATION)
  escalatedTo       String? // User ID
  escalationReason  String? // SLA_BREACH, TECHNICAL_COMPLEXITY, CUSTOMER_REQUEST, etc.
  previousAssignee  String? // User ID

  // Resolution information (when noteType = RESOLUTION)
  resolutionType String? // FIXED, WORKAROUND, DUPLICATE, CANNOT_REPRODUCE, etc.
  timeSpent      Int? // Time spent in minutes
  relatedTickets String[] // Array of related ticket IDs

  // Multi-tenant fields
  instituteId String
  branchId    String?

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ticket               SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  author               User          @relation("NoteAuthor", fields: [authorId], references: [id], onDelete: Restrict)
  followUpAssignedUser User?         @relation("NoteFollowUpAssigned", fields: [followUpAssignedTo], references: [id], onDelete: SetNull)
  escalatedToUser      User?         @relation("NoteEscalatedTo", fields: [escalatedTo], references: [id], onDelete: SetNull)
  previousAssigneeUser User?         @relation("NotePreviousAssignee", fields: [previousAssignee], references: [id], onDelete: SetNull)

  // Performance indexes
  @@index([ticketId, createdAt])
  @@index([ticketId, noteType, createdAt])
  @@index([authorId, createdAt])
  @@index([instituteId, noteType])
  @@index([noteType, importance])
  @@index([visibility, isPinned])
  @@index([followUpDate])
  @@index([followUpAssignedTo, followUpCompleted])
  @@index([escalatedTo])
  @@index([importance, isPinned])
  @@index([createdAt])
  @@map("ticket_notes")
}

// Ticket Analytics for performance metrics and reporting
model TicketAnalytics {
  id       String @id @default(cuid())
  ticketId String @unique

  // Response Time Metrics
  firstResponseTime     Int? // minutes
  averageResponseTime   Int? // minutes
  slaResponseMet        Boolean @default(false)
  responseTimeBreaches  Int     @default(0)

  // Resolution Time Metrics
  resolutionTime    Int? // minutes
  activeWorkTime    Int? // minutes
  slaResolutionMet  Boolean @default(false)
  escalationCount   Int     @default(0)
  reopenCount       Int     @default(0)

  // Customer Satisfaction Metrics
  satisfactionScore    Int? // 1-5 scale
  satisfactionFeedback String?
  npsScore            Int? // 0-10 scale
  surveyCompleted     Boolean @default(false)

  // Agent Performance Metrics
  assignedAgents      Json? // Array of agent assignments with time tracking
  totalAgentSwitches  Int   @default(0)

  // Communication Metrics
  totalMessages    Int @default(0)
  customerMessages Int @default(0)
  agentMessages    Int @default(0)
  internalNotes    Int @default(0)
  attachmentCount  Int @default(0)

  // AI Analysis Metrics
  sentimentAnalysis    Json? // AI sentiment analysis results
  complexityScore      Float? // 0-10 AI-calculated complexity
  urgencyScore         Float? // 0-10 AI-calculated urgency
  categoryPredictions  Json? // AI category predictions with confidence
  resolutionPrediction Json? // AI resolution time and approach predictions

  // Business Impact Metrics
  impactLevel     String? // LOW, MEDIUM, HIGH, CRITICAL
  affectedUsers   Int?
  estimatedCost   Float?
  revenueImpact   Float?

  // Multi-tenant fields
  instituteId String
  branchId    String?

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ticket SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  // Performance indexes for analytics and reporting
  @@index([instituteId, createdAt])
  @@index([instituteId, slaResponseMet])
  @@index([instituteId, slaResolutionMet])
  @@index([satisfactionScore])
  @@index([complexityScore])
  @@index([urgencyScore])
  @@index([impactLevel])
  @@index([escalationCount])
  @@index([reopenCount])
  @@index([createdAt])
  @@index([updatedAt])
  @@map("ticket_analytics")
}
