# Authentication and Authorization Test Suite

This directory contains comprehensive tests for the support system's authentication and authorization infrastructure.

## Test Structure

### Unit Tests (`lib/__tests__/`)
- **`payload-auth.test.ts`** - Tests for Payload CMS RBAC functions (11 tests)
- **`rbac-functions.test.ts`** - Isolated RBAC function tests (11 tests)  
- **`redis.test.ts`** - Redis service tests (15 tests)
- **`token-refresh.test.ts`** - Token refresh mechanism tests (13 tests)
- **`data-isolation.test.ts`** - Data isolation service tests (23 tests)

### Integration Tests (`__tests__/integration/`)
- **`auth-system.test.ts`** - End-to-end authentication system tests (14 tests)

## Test Coverage

### Authentication Flow
✅ User login and session creation  
✅ Session validation and refresh  
✅ Token expiry handling  
✅ Automatic token refresh  
✅ Session invalidation  
✅ Multi-device session management  

### Role-Based Access Control (RBAC)
✅ Super Admin permissions (full access)  
✅ Institute Admin permissions (institute-scoped)  
✅ Support Staff permissions (branch-scoped)  
✅ Student permissions (minimal access)  
✅ Permission inheritance and escalation prevention  

### Data Isolation
✅ Cross-institute data access prevention  
✅ Query filtering by user context  
✅ Resource ownership validation  
✅ Multi-tenant data segregation  
✅ Branch-level access control  

### Security Features
✅ SQL injection prevention  
✅ Cross-tenant data leakage prevention  
✅ Privilege escalation prevention  
✅ Session fixation attack prevention  
✅ Rate limiting and brute force protection  
✅ Malicious input handling  

### Performance
✅ Large dataset filtering (1000+ items)  
✅ Concurrent request handling  
✅ Cache performance optimization  
✅ Memory pressure handling  
✅ Resource cleanup  

### Error Handling
✅ Database connection failures  
✅ Redis connection failures  
✅ Network timeouts  
✅ Malformed session data  
✅ Null/undefined value handling  

## Running Tests

### All Tests
```bash
pnpm test
```

### Specific Test Suites
```bash
# Unit tests only
pnpm test src/lib/__tests__/

# Integration tests only  
pnpm test src/__tests__/integration/

# Specific test file
pnpm test src/__tests__/integration/auth-system.test.ts
```

### Test with Coverage
```bash
pnpm test --coverage
```

## Test Data

### Mock Users
- **Super Admin**: Full system access
- **Institute Admin**: Institute-scoped access (inst-1)
- **Support Staff**: Branch-scoped access (inst-1, branch-1)
- **Student**: Minimal access (inst-1, branch-1)
- **Other Institute User**: Different institute (inst-2)

### Mock Resources
- Users with different roles and institutes
- Support tickets with various ownership patterns
- Media files with public/private access levels
- Branches and institutes for multi-tenant testing

## Security Test Scenarios

### Cross-Tenant Access Prevention
- Institute admin cannot access inst-2 data
- Support staff cannot access other branches
- Students cannot access other users' data

### Privilege Escalation Prevention
- Students cannot perform admin actions
- Support staff cannot delete users
- Institute admins cannot access super admin functions

### Data Leakage Prevention
- Query filters prevent unauthorized data access
- Resource ownership is validated
- Cross-institute queries return empty results

## Performance Benchmarks

### Data Filtering Performance
- 1000 items filtered in < 50ms
- Large dataset handling without memory issues
- Efficient query generation

### Token Refresh Performance
- Concurrent refresh requests handled properly
- Timeout handling (100ms+ responses)
- Retry logic with exponential backoff

### Cache Performance
- 1000 cache operations in < 1000ms
- High-frequency operations support
- Efficient cleanup and invalidation

## Test Utilities

### Mock Services
- Simplified data isolation service
- Mock Redis implementations
- Mock NextAuth.js sessions
- Mock Prisma database calls

### Test Helpers
- User context creation
- Resource data generation
- Performance measurement utilities
- Error simulation helpers

## Continuous Integration

Tests are designed to run in CI/CD environments with:
- No external dependencies (Redis, Database)
- Deterministic results
- Fast execution (< 30 seconds total)
- Clear failure reporting

## Test Maintenance

### Adding New Tests
1. Follow existing naming conventions
2. Use appropriate mock implementations
3. Include both positive and negative test cases
4. Add performance tests for new features

### Updating Tests
1. Update mock data when schemas change
2. Maintain test isolation
3. Update documentation for new test scenarios
4. Ensure backward compatibility

## Known Limitations

### Module Resolution
- Some complex mocking scenarios require Jest configuration updates
- Path aliases may need adjustment for new test files

### Performance Tests
- Performance benchmarks are environment-dependent
- CI environments may have different performance characteristics

### Integration Tests
- Limited to unit-level integration
- Full end-to-end tests require separate test environment

## Future Enhancements

### Planned Test Additions
- API endpoint integration tests
- WebSocket authentication tests
- File upload security tests
- Audit logging validation tests

### Test Infrastructure Improvements
- Test database setup automation
- Performance regression detection
- Security vulnerability scanning
- Load testing integration
