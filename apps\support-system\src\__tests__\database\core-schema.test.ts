/**
 * Tests for core support system database schema
 * Validates the SupportTickets, SupportCategories, and TicketTemplates models
 */

import { PrismaClient } from '@prisma/client';

// Mock Prisma for testing
const mockPrisma = {
  institute: {
    create: jest.fn(),
    findUnique: jest.fn(),
  },
  user: {
    create: jest.fn(),
    findUnique: jest.fn(),
  },
  supportCategory: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  ticketTemplate: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  supportTicket: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
} as unknown as PrismaClient;

describe('Core Support System Schema Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('SupportCategory Model', () => {
    it('should create a support category with required fields', async () => {
      const mockCategory = {
        id: 'cat-1',
        instituteId: 'inst-1',
        name: 'Technical Issues',
        description: 'Hardware and software problems',
        color: '#FF5733',
        icon: 'bug',
        responseTimeHours: 24,
        resolutionTimeHours: 72,
        isActive: true,
        sortOrder: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'user-1',
      };

      mockPrisma.supportCategory.create = jest.fn().mockResolvedValue(mockCategory);

      const result = await mockPrisma.supportCategory.create({
        data: {
          instituteId: 'inst-1',
          name: 'Technical Issues',
          description: 'Hardware and software problems',
          color: '#FF5733',
          icon: 'bug',
          responseTimeHours: 24,
          resolutionTimeHours: 72,
          createdBy: 'user-1',
        },
      });

      expect(result).toEqual(mockCategory);
      expect(mockPrisma.supportCategory.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          name: 'Technical Issues',
          instituteId: 'inst-1',
          responseTimeHours: 24,
          resolutionTimeHours: 72,
        }),
      });
    });

    it('should enforce unique name per institute constraint', async () => {
      const duplicateCategory = {
        instituteId: 'inst-1',
        name: 'Technical Issues', // Duplicate name
        responseTimeHours: 24,
        resolutionTimeHours: 72,
      };

      mockPrisma.supportCategory.create = jest.fn().mockRejectedValue(
        new Error('Unique constraint failed on the fields: (`instituteId`,`name`)')
      );

      await expect(
        mockPrisma.supportCategory.create({ data: duplicateCategory })
      ).rejects.toThrow('Unique constraint failed');
    });

    it('should validate SLA time ranges', () => {
      const validSLA = {
        responseTimeHours: 24,
        resolutionTimeHours: 72,
      };

      const invalidSLA = {
        responseTimeHours: 0, // Invalid: should be >= 1
        resolutionTimeHours: 800, // Invalid: should be <= 720
      };

      // Valid SLA should pass
      expect(validSLA.responseTimeHours).toBeGreaterThanOrEqual(1);
      expect(validSLA.resolutionTimeHours).toBeLessThanOrEqual(720);

      // Invalid SLA should fail
      expect(invalidSLA.responseTimeHours).toBeLessThan(1);
      expect(invalidSLA.resolutionTimeHours).toBeGreaterThan(720);
    });
  });

  describe('TicketTemplate Model', () => {
    it('should create a ticket template with all fields', async () => {
      const mockTemplate = {
        id: 'template-1',
        instituteId: 'inst-1',
        categoryId: 'cat-1',
        name: 'Password Reset Request',
        description: 'Template for password reset requests',
        titleTemplate: 'Password Reset for {{username}}',
        contentTemplate: 'User {{username}} is requesting a password reset for {{system}}.',
        variables: { username: '', system: '' },
        defaultPriority: 'MEDIUM',
        defaultStatus: 'OPEN',
        autoAssignTo: 'user-1',
        usageCount: 0,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'user-1',
      };

      mockPrisma.ticketTemplate.create = jest.fn().mockResolvedValue(mockTemplate);

      const result = await mockPrisma.ticketTemplate.create({
        data: {
          instituteId: 'inst-1',
          categoryId: 'cat-1',
          name: 'Password Reset Request',
          description: 'Template for password reset requests',
          titleTemplate: 'Password Reset for {{username}}',
          contentTemplate: 'User {{username}} is requesting a password reset for {{system}}.',
          variables: { username: '', system: '' },
          defaultPriority: 'MEDIUM',
          defaultStatus: 'OPEN',
          autoAssignTo: 'user-1',
          createdBy: 'user-1',
        },
      });

      expect(result).toEqual(mockTemplate);
      expect(result.variables).toEqual({ username: '', system: '' });
    });

    it('should validate template variables format', () => {
      const validVariables = {
        username: { type: 'string', default: '', required: true },
        system: { type: 'string', default: 'LMS', required: false },
      };

      const invalidVariables = 'not-a-json-object';

      expect(typeof validVariables).toBe('object');
      expect(validVariables.username).toHaveProperty('type');
      expect(typeof invalidVariables).toBe('string');
    });

    it('should track template usage statistics', async () => {
      const templateId = 'template-1';
      const updatedTemplate = {
        id: templateId,
        usageCount: 5,
        lastUsedAt: new Date(),
      };

      mockPrisma.ticketTemplate.update = jest.fn().mockResolvedValue(updatedTemplate);

      const result = await mockPrisma.ticketTemplate.update({
        where: { id: templateId },
        data: {
          usageCount: { increment: 1 },
          lastUsedAt: new Date(),
        },
      });

      expect(result.usageCount).toBe(5);
      expect(result.lastUsedAt).toBeInstanceOf(Date);
    });
  });

  describe('SupportTicket Model', () => {
    it('should create a support ticket with all required fields', async () => {
      const mockTicket = {
        id: 'ticket-1',
        ticketNumber: 'INST-2024-000001',
        instituteId: 'inst-1',
        branchId: 'branch-1',
        title: 'Login Issues',
        description: 'Unable to login to the system',
        status: 'OPEN',
        priority: 'MEDIUM',
        type: 'INCIDENT',
        categoryId: 'cat-1',
        tags: ['login', 'authentication'],
        createdBy: 'user-1',
        assignedTo: 'user-2',
        source: 'WEB',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.supportTicket.create = jest.fn().mockResolvedValue(mockTicket);

      const result = await mockPrisma.supportTicket.create({
        data: {
          ticketNumber: 'INST-2024-000001',
          instituteId: 'inst-1',
          branchId: 'branch-1',
          title: 'Login Issues',
          description: 'Unable to login to the system',
          categoryId: 'cat-1',
          tags: ['login', 'authentication'],
          createdBy: 'user-1',
          assignedTo: 'user-2',
        },
      });

      expect(result).toEqual(mockTicket);
      expect(result.ticketNumber).toMatch(/^INST-\d{4}-\d{6}$/);
      expect(result.tags).toContain('login');
      expect(result.status).toBe('OPEN');
      expect(result.priority).toBe('MEDIUM');
    });

    it('should validate ticket number format', () => {
      const validTicketNumbers = [
        'INST-2024-000001',
        'ABC-2024-123456',
        'XYZ-2023-999999',
      ];

      const invalidTicketNumbers = [
        'INST-24-001', // Wrong year format
        'INST-2024-1', // Wrong number format
        'inst-2024-000001', // Wrong case
        '2024-000001', // Missing institute code
      ];

      const ticketNumberRegex = /^[A-Z]+-\d{4}-\d{6}$/;

      validTicketNumbers.forEach(number => {
        expect(number).toMatch(ticketNumberRegex);
      });

      invalidTicketNumbers.forEach(number => {
        expect(number).not.toMatch(ticketNumberRegex);
      });
    });

    it('should handle SLA tracking fields', async () => {
      const now = new Date();
      const responseTime = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours
      const resolutionTime = new Date(now.getTime() + 72 * 60 * 60 * 1000); // 72 hours

      const ticketWithSLA = {
        id: 'ticket-1',
        slaResponseDue: responseTime,
        slaResolutionDue: resolutionTime,
        firstResponseAt: null,
        resolvedAt: null,
        closedAt: null,
      };

      mockPrisma.supportTicket.update = jest.fn().mockResolvedValue({
        ...ticketWithSLA,
        firstResponseAt: now,
      });

      const result = await mockPrisma.supportTicket.update({
        where: { id: 'ticket-1' },
        data: { firstResponseAt: now },
      });

      expect(result.firstResponseAt).toEqual(now);
      expect(result.slaResponseDue).toEqual(responseTime);
      expect(result.slaResolutionDue).toEqual(resolutionTime);
    });

    it('should support custom fields as JSON', () => {
      const customFields = {
        affectedUsers: 150,
        systemVersion: '2.1.4',
        errorCode: 'AUTH_001',
        reproductionSteps: [
          'Navigate to login page',
          'Enter credentials',
          'Click login button',
          'Observe error message',
        ],
      };

      expect(typeof customFields).toBe('object');
      expect(customFields.affectedUsers).toBe(150);
      expect(Array.isArray(customFields.reproductionSteps)).toBe(true);
    });

    it('should validate enum values', () => {
      const validStatuses = ['OPEN', 'IN_PROGRESS', 'PENDING_CUSTOMER', 'PENDING_VENDOR', 'RESOLVED', 'CLOSED', 'CANCELLED'];
      const validPriorities = ['LOW', 'MEDIUM', 'HIGH', 'URGENT', 'CRITICAL'];
      const validTypes = ['INCIDENT', 'PROBLEM', 'CHANGE_REQUEST', 'SERVICE_REQUEST'];

      expect(validStatuses).toContain('OPEN');
      expect(validStatuses).toContain('RESOLVED');
      expect(validPriorities).toContain('HIGH');
      expect(validTypes).toContain('INCIDENT');

      // Invalid values should not be in the arrays
      expect(validStatuses).not.toContain('INVALID_STATUS');
      expect(validPriorities).not.toContain('SUPER_HIGH');
      expect(validTypes).not.toContain('UNKNOWN_TYPE');
    });
  });

  describe('Multi-Tenant Data Isolation', () => {
    it('should filter support categories by institute', async () => {
      const institute1Categories = [
        { id: 'cat-1', instituteId: 'inst-1', name: 'Technical' },
        { id: 'cat-2', instituteId: 'inst-1', name: 'Billing' },
      ];

      mockPrisma.supportCategory.findMany = jest.fn().mockResolvedValue(institute1Categories);

      const result = await mockPrisma.supportCategory.findMany({
        where: { instituteId: 'inst-1' },
      });

      expect(result).toHaveLength(2);
      expect(result.every(cat => cat.instituteId === 'inst-1')).toBe(true);
    });

    it('should filter support tickets by institute and branch', async () => {
      const branchTickets = [
        { id: 'ticket-1', instituteId: 'inst-1', branchId: 'branch-1', title: 'Issue 1' },
        { id: 'ticket-2', instituteId: 'inst-1', branchId: 'branch-1', title: 'Issue 2' },
      ];

      mockPrisma.supportTicket.findMany = jest.fn().mockResolvedValue(branchTickets);

      const result = await mockPrisma.supportTicket.findMany({
        where: {
          instituteId: 'inst-1',
          branchId: 'branch-1',
        },
      });

      expect(result).toHaveLength(2);
      expect(result.every(ticket => 
        ticket.instituteId === 'inst-1' && ticket.branchId === 'branch-1'
      )).toBe(true);
    });

    it('should prevent cross-institute data access', async () => {
      // Simulate trying to access another institute's data
      mockPrisma.supportTicket.findMany = jest.fn().mockResolvedValue([]);

      const result = await mockPrisma.supportTicket.findMany({
        where: {
          instituteId: 'inst-2', // Different institute
          createdBy: 'user-from-inst-1', // User from inst-1
        },
      });

      expect(result).toHaveLength(0);
    });
  });

  describe('Relationships and Foreign Keys', () => {
    it('should maintain referential integrity', async () => {
      // Test that deleting an institute cascades to related records
      const cascadeError = new Error('Foreign key constraint failed');
      
      mockPrisma.supportTicket.create = jest.fn().mockRejectedValue(cascadeError);

      await expect(
        mockPrisma.supportTicket.create({
          data: {
            ticketNumber: 'TEST-2024-000001',
            instituteId: 'non-existent-institute',
            title: 'Test Ticket',
            description: 'Test Description',
            createdBy: 'user-1',
          },
        })
      ).rejects.toThrow('Foreign key constraint failed');
    });

    it('should handle optional relationships correctly', async () => {
      const ticketWithoutCategory = {
        id: 'ticket-1',
        categoryId: null, // Optional relationship
        assignedTo: null, // Optional relationship
        branchId: null, // Optional relationship
      };

      mockPrisma.supportTicket.create = jest.fn().mockResolvedValue(ticketWithoutCategory);

      const result = await mockPrisma.supportTicket.create({
        data: {
          ticketNumber: 'TEST-2024-000001',
          instituteId: 'inst-1',
          title: 'Test Ticket',
          description: 'Test Description',
          createdBy: 'user-1',
          // categoryId, assignedTo, branchId are optional
        },
      });

      expect(result.categoryId).toBeNull();
      expect(result.assignedTo).toBeNull();
      expect(result.branchId).toBeNull();
    });
  });
});
