/**
 * Integration tests for API authentication and authorization
 * Tests middleware, rate limiting, and data isolation in API endpoints
 */

import { NextRequest } from 'next/server';
import { GET as healthCheck } from '@/app/api/cache/health/route';
import { GET as refreshStatus, POST as refreshToken } from '@/app/api/auth/refresh/route';
import { GET as testIsolation } from '@/app/api/test-isolation/route';
import { UserRole } from '@prisma/client';

// Mock dependencies
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  },
}));

jest.mock('@/lib/redis', () => ({
  sessionCache: {
    addActiveSession: jest.fn(),
    removeActiveSession: jest.fn(),
    setSessionData: jest.fn(),
    getSessionData: jest.fn().mockResolvedValue({
      lastActivity: Date.now(),
      userAgent: 'test-agent',
      ipAddress: '127.0.0.1',
    }),
    getActiveSessions: jest.fn().mockResolvedValue(['session-1', 'session-2']),
    invalidateUserSessions: jest.fn(),
  },
  cacheService: {
    ping: jest.fn().mockResolvedValue('PONG'),
    info: jest.fn().mockResolvedValue(`redis_version:7.0.0\r\nuptime_in_seconds:3600\r\nconnected_clients:5\r\nused_memory_human:1.5M\r\ntotal_commands_processed:1000`),
  },
  rateLimiter: {
    checkLimit: jest.fn().mockResolvedValue({
      allowed: true,
      remaining: 10,
      resetTime: Date.now() + 60000,
    }),
  },
}));

import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';

describe('API Authentication Integration Tests', () => {
  const mockUsers = {
    superAdmin: {
      id: 'super-1',
      email: '<EMAIL>',
      name: 'Super Admin',
      role: UserRole.SUPER_ADMIN,
      instituteId: null,
      branchId: null,
      lmsUserId: null,
      isActive: true,
    },
    instituteAdmin: {
      id: 'admin-1',
      email: '<EMAIL>',
      name: 'Institute Admin',
      role: UserRole.INSTITUTE_ADMIN,
      instituteId: 'inst-1',
      branchId: 'branch-1',
      lmsUserId: 'lms-admin-1',
      isActive: true,
    },
    supportStaff: {
      id: 'staff-1',
      email: '<EMAIL>',
      name: 'Support Staff',
      role: UserRole.SUPPORT_STAFF,
      instituteId: 'inst-1',
      branchId: 'branch-1',
      lmsUserId: 'lms-staff-1',
      isActive: true,
    },
    student: {
      id: 'student-1',
      email: '<EMAIL>',
      name: 'Student User',
      role: UserRole.STUDENT,
      instituteId: 'inst-1',
      branchId: 'branch-1',
      lmsUserId: 'lms-student-1',
      isActive: true,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Cache Health Endpoint', () => {
    it('should allow super admin to check cache health', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue({
        user: mockUsers.superAdmin,
      } as any);

      const request = new NextRequest('http://localhost:3000/api/cache/health');
      const response = await healthCheck(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.status).toBe('healthy');
      expect(data.ping).toBe('PONG');
      expect(data.redis).toBeDefined();
    });

    it('should deny non-super admin access to cache health', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue({
        user: mockUsers.instituteAdmin,
      } as any);

      const request = new NextRequest('http://localhost:3000/api/cache/health');
      const response = await healthCheck(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Unauthorized');
    });

    it('should deny unauthenticated access to cache health', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/cache/health');
      const response = await healthCheck(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Unauthorized');
    });
  });

  describe('Token Refresh Endpoint', () => {
    it('should refresh token for authenticated user', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      const mockPrismaUser = prisma.user as any;

      mockGetServerSession.mockResolvedValue({
        user: mockUsers.instituteAdmin,
      } as any);

      mockPrismaUser.findUnique.mockResolvedValue(mockUsers.instituteAdmin);
      mockPrismaUser.update.mockResolvedValue(mockUsers.instituteAdmin);

      const request = new NextRequest('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
        headers: {
          'user-agent': 'test-agent',
          'x-forwarded-for': '127.0.0.1',
        },
      });

      const response = await refreshToken(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Token refreshed successfully');
      expect(data.user.id).toBe('admin-1');
      expect(data.user.role).toBe(UserRole.INSTITUTE_ADMIN);
    });

    it('should reject refresh for inactive user', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      const mockPrismaUser = prisma.user as any;

      mockGetServerSession.mockResolvedValue({
        user: { id: 'inactive-1' },
      } as any);

      mockPrismaUser.findUnique.mockResolvedValue({
        ...mockUsers.supportStaff,
        isActive: false,
      });

      const request = new NextRequest('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
      });

      const response = await refreshToken(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('User account is inactive or not found');
    });

    it('should get token status for authenticated user', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

      mockGetServerSession.mockResolvedValue({
        user: mockUsers.instituteAdmin,
        lastRefresh: Date.now() - 60000, // 1 minute ago
        tokenExp: Math.floor((Date.now() + 30 * 60 * 1000) / 1000), // 30 minutes from now
      } as any);

      const request = new NextRequest('http://localhost:3000/api/auth/refresh');
      const response = await refreshStatus(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.status).toBe('active');
      expect(data.user.id).toBe('admin-1');
      expect(data.token.isValid).toBe(true);
      expect(data.session.activeSessions).toBe(2);
    });

    it('should deny unauthenticated access to token status', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/auth/refresh');
      const response = await refreshStatus(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('No active session found');
    });
  });

  describe('Data Isolation Test Endpoint', () => {
    it('should filter data correctly for institute admin', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue({
        user: mockUsers.instituteAdmin,
      } as any);

      const request = new NextRequest('http://localhost:3000/api/test-isolation?resource=users&test=filter');
      const response = await testIsolation(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.context.role).toBe(UserRole.INSTITUTE_ADMIN);
      expect(data.context.instituteId).toBe('inst-1');
      expect(data.result.filteredItems).toBeLessThanOrEqual(data.result.totalItems);
    });

    it('should filter data correctly for support staff', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue({
        user: mockUsers.supportStaff,
      } as any);

      const request = new NextRequest('http://localhost:3000/api/test-isolation?resource=users&test=access');
      const response = await testIsolation(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.context.role).toBe(UserRole.SUPPORT_STAFF);
      expect(data.result.summary.accessible).toBeLessThanOrEqual(data.result.summary.total);
    });

    it('should filter data correctly for students', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue({
        user: mockUsers.student,
      } as any);

      const request = new NextRequest('http://localhost:3000/api/test-isolation?resource=support-tickets&test=modify');
      const response = await testIsolation(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.context.role).toBe(UserRole.STUDENT);
      expect(data.result.summary.create.allowed).toBeGreaterThan(0);
      expect(data.result.summary.update.allowed).toBe(0); // Students can't update tickets
    });

    it('should deny unauthenticated access to test endpoint', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/test-isolation');
      const response = await testIsolation(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Authentication required');
    });
  });

  describe('Rate Limiting Integration', () => {
    it('should apply rate limiting to auth endpoints', async () => {
      const { rateLimiter } = require('@/lib/redis');
      
      // Mock rate limit exceeded
      rateLimiter.checkLimit.mockResolvedValue({
        allowed: false,
        remaining: 0,
        resetTime: Date.now() + 60000,
      });

      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue({
        user: mockUsers.instituteAdmin,
      } as any);

      const request = new NextRequest('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
      });

      const response = await refreshToken(request);
      const data = await response.json();

      expect(response.status).toBe(429);
      expect(data.error).toBe('Too many requests');
    });

    it('should include rate limit headers', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      const mockPrismaUser = prisma.user as any;

      mockGetServerSession.mockResolvedValue({
        user: mockUsers.instituteAdmin,
      } as any);

      mockPrismaUser.findUnique.mockResolvedValue(mockUsers.instituteAdmin);
      mockPrismaUser.update.mockResolvedValue(mockUsers.instituteAdmin);

      const request = new NextRequest('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
      });

      const response = await refreshToken(request);

      // Rate limit headers should be present (added by middleware)
      expect(response.status).toBe(200);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      const mockPrismaUser = prisma.user as any;

      mockGetServerSession.mockResolvedValue({
        user: mockUsers.instituteAdmin,
      } as any);

      // Mock database error
      mockPrismaUser.findUnique.mockRejectedValue(new Error('Database connection failed'));

      const request = new NextRequest('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
      });

      const response = await refreshToken(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Internal server error during token refresh');
    });

    it('should handle Redis errors gracefully', async () => {
      const { cacheService } = require('@/lib/redis');
      
      // Mock Redis error
      cacheService.ping.mockRejectedValue(new Error('Redis connection failed'));

      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue({
        user: mockUsers.superAdmin,
      } as any);

      const request = new NextRequest('http://localhost:3000/api/cache/health');
      const response = await healthCheck(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.status).toBe('unhealthy');
      expect(data.error).toBe('Redis connection failed');
    });
  });
});
