/**
 * Integration tests for authentication and authorization flows
 * Tests the complete auth system including NextAuth.js, RBAC, data isolation, and token refresh
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { DataIsolationService } from '@/lib/data-isolation';
import { tokenRefreshService } from '@/lib/token-refresh';
import { apiClient } from '@/lib/api-interceptor';
import { UserRole } from '@prisma/client';

// Mock dependencies
jest.mock('next-auth');
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  },
}));

jest.mock('@/lib/redis', () => ({
  sessionCache: {
    addActiveSession: jest.fn(),
    removeActiveSession: jest.fn(),
    setSessionData: jest.fn(),
    getSessionData: jest.fn(),
    invalidateUserSessions: jest.fn(),
  },
  cacheService: {
    ping: jest.fn().mockResolvedValue('PONG'),
  },
  rateLimiter: {
    checkLimit: jest.fn().mockResolvedValue({
      allowed: true,
      remaining: 10,
      resetTime: Date.now() + 60000,
    }),
  },
}));

// Mock fetch for API tests
global.fetch = jest.fn();

describe('Authentication and Authorization Integration Tests', () => {
  const mockUsers = {
    superAdmin: {
      id: 'super-1',
      email: '<EMAIL>',
      name: 'Super Admin',
      role: UserRole.SUPER_ADMIN,
      instituteId: null,
      branchId: null,
      lmsUserId: null,
      isActive: true,
    },
    instituteAdmin: {
      id: 'admin-1',
      email: '<EMAIL>',
      name: 'Institute Admin',
      role: UserRole.INSTITUTE_ADMIN,
      instituteId: 'inst-1',
      branchId: 'branch-1',
      lmsUserId: 'lms-admin-1',
      isActive: true,
    },
    supportStaff: {
      id: 'staff-1',
      email: '<EMAIL>',
      name: 'Support Staff',
      role: UserRole.SUPPORT_STAFF,
      instituteId: 'inst-1',
      branchId: 'branch-1',
      lmsUserId: 'lms-staff-1',
      isActive: true,
    },
    student: {
      id: 'student-1',
      email: '<EMAIL>',
      name: 'Student User',
      role: UserRole.STUDENT,
      instituteId: 'inst-1',
      branchId: 'branch-1',
      lmsUserId: 'lms-student-1',
      isActive: true,
    },
    inactiveUser: {
      id: 'inactive-1',
      email: '<EMAIL>',
      name: 'Inactive User',
      role: UserRole.SUPPORT_STAFF,
      instituteId: 'inst-1',
      branchId: 'branch-1',
      lmsUserId: null,
      isActive: false,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Authentication Flow', () => {
    it('should authenticate valid user and create session', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      
      // Mock successful authentication
      mockGetServerSession.mockResolvedValue({
        user: mockUsers.instituteAdmin,
        expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        lastRefresh: Date.now(),
        tokenExp: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
      } as any);

      const session = await getServerSession(authOptions);

      expect(session).toBeTruthy();
      expect(session?.user.id).toBe('admin-1');
      expect(session?.user.role).toBe(UserRole.INSTITUTE_ADMIN);
      expect(session?.user.instituteId).toBe('inst-1');
    });

    it('should reject authentication for inactive user', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      
      // Mock authentication with inactive user
      mockGetServerSession.mockResolvedValue(null);

      const session = await getServerSession(authOptions);

      expect(session).toBeNull();
    });

    it('should handle session refresh correctly', async () => {
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      
      // Mock session that needs refresh
      const oldSession = {
        user: mockUsers.instituteAdmin,
        expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        lastRefresh: Date.now() - 25 * 60 * 60 * 1000, // 25 hours ago
        tokenExp: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
      };

      mockGetServerSession.mockResolvedValue(oldSession as any);

      const session = await getServerSession(authOptions);

      expect(session).toBeTruthy();
      expect(session?.user.id).toBe('admin-1');
    });
  });

  describe('Role-Based Access Control (RBAC)', () => {
    it('should enforce super admin permissions', () => {
      const context = DataIsolationService.createContext(mockUsers.superAdmin);

      // Super admin should have access to everything
      expect(DataIsolationService.canAccessResource(context, 'user', mockUsers.instituteAdmin)).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'institute', { id: 'any-institute' })).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'user', mockUsers.student, 'delete')).toBe(true);
    });

    it('should enforce institute admin permissions', () => {
      const context = DataIsolationService.createContext(mockUsers.instituteAdmin);

      // Can access users in same institute
      expect(DataIsolationService.canAccessResource(context, 'user', mockUsers.supportStaff)).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'user', mockUsers.student)).toBe(true);

      // Cannot access users in different institute
      const otherInstituteUser = { ...mockUsers.student, instituteId: 'inst-2' };
      expect(DataIsolationService.canAccessResource(context, 'user', otherInstituteUser)).toBe(false);

      // Can create users but not delete them
      expect(DataIsolationService.canModifyResource(context, 'user', mockUsers.student, 'create')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'user', mockUsers.student, 'delete')).toBe(false);
    });

    it('should enforce support staff permissions', () => {
      const context = DataIsolationService.createContext(mockUsers.supportStaff);

      // Can only access own profile
      expect(DataIsolationService.canAccessResource(context, 'user', mockUsers.supportStaff)).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'user', mockUsers.student)).toBe(false);

      // Can create and update tickets
      const ticket = { id: 'ticket-1', createdBy: 'staff-1', instituteId: 'inst-1', branchId: 'branch-1' };
      expect(DataIsolationService.canModifyResource(context, 'support-ticket', ticket, 'create')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'support-ticket', ticket, 'update')).toBe(true);
    });

    it('should enforce student permissions', () => {
      const context = DataIsolationService.createContext(mockUsers.student);

      // Can only access own profile
      expect(DataIsolationService.canAccessResource(context, 'user', mockUsers.student)).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'user', mockUsers.supportStaff)).toBe(false);

      // Can create tickets but not update them
      const ownTicket = { id: 'ticket-1', createdBy: 'student-1', instituteId: 'inst-1' };
      expect(DataIsolationService.canModifyResource(context, 'support-ticket', ownTicket, 'create')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'support-ticket', ownTicket, 'update')).toBe(false);
    });
  });

  describe('Data Isolation', () => {
    it('should apply correct filters for different user roles', () => {
      // Super admin - no filters
      const superAdminContext = DataIsolationService.createContext(mockUsers.superAdmin);
      const superAdminFilter = DataIsolationService.applyIsolationFilter(superAdminContext, {}, 'users');
      expect(superAdminFilter).toEqual({});

      // Institute admin - institute filter
      const adminContext = DataIsolationService.createContext(mockUsers.instituteAdmin);
      const adminFilter = DataIsolationService.applyIsolationFilter(adminContext, {}, 'users');
      expect(adminFilter).toHaveProperty('and');

      // Support staff - own profile only
      const staffContext = DataIsolationService.createContext(mockUsers.supportStaff);
      const staffFilter = DataIsolationService.applyIsolationFilter(staffContext, {}, 'users');
      expect(staffFilter).toEqual({
        and: [
          {},
          { id: { equals: 'staff-1' } },
        ],
      });
    });

    it('should prevent cross-institute data access', () => {
      const context = DataIsolationService.createContext(mockUsers.instituteAdmin);

      // Same institute - allowed
      const sameInstituteData = { id: 'data-1', instituteId: 'inst-1' };
      expect(DataIsolationService.canAccessResource(context, 'branch', sameInstituteData)).toBe(true);

      // Different institute - denied
      const differentInstituteData = { id: 'data-2', instituteId: 'inst-2' };
      expect(DataIsolationService.canAccessResource(context, 'branch', differentInstituteData)).toBe(false);
    });
  });

  describe('Token Refresh Integration', () => {
    it('should handle token refresh flow', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      
      // Mock successful refresh response
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          user: mockUsers.instituteAdmin,
          refreshedAt: new Date().toISOString(),
        }),
      } as Response);

      const result = await tokenRefreshService.refreshToken();

      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith('/api/auth/session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ trigger: 'update' }),
      });
    });

    it('should handle token refresh failure', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      
      // Mock failed refresh response
      mockFetch.mockResolvedValue({
        ok: false,
        status: 401,
      } as Response);

      const result = await tokenRefreshService.refreshToken();

      expect(result).toBe(false);
    });
  });

  describe('API Integration with Authentication', () => {
    it('should include auth headers in API requests', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      
      // Mock session
      jest.doMock('next-auth/react', () => ({
        getSession: jest.fn().mockResolvedValue({
          user: mockUsers.instituteAdmin,
        }),
      }));

      // Mock successful API response
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ data: 'test' }),
        status: 200,
        statusText: 'OK',
        headers: new Headers(),
      } as Response);

      const response = await apiClient.get('/test-endpoint');

      expect(response.data).toEqual({ data: 'test' });
      expect(mockFetch).toHaveBeenCalledWith('/api/test-endpoint', expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
        }),
      }));
    });

    it('should handle 401 errors with token refresh', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      
      // First call returns 401, second call succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 401,
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ data: 'success after refresh' }),
          status: 200,
          statusText: 'OK',
          headers: new Headers(),
        } as Response);

      // Mock token refresh success
      jest.spyOn(tokenRefreshService, 'refreshToken').mockResolvedValue(true);

      const response = await apiClient.get('/protected-endpoint');

      expect(response.data).toEqual({ data: 'success after refresh' });
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('End-to-End Authentication Scenarios', () => {
    it('should handle complete user journey', async () => {
      // 1. User logs in
      const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
      mockGetServerSession.mockResolvedValue({
        user: mockUsers.instituteAdmin,
        expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      } as any);

      const session = await getServerSession(authOptions);
      expect(session?.user.role).toBe(UserRole.INSTITUTE_ADMIN);

      // 2. User makes API request with proper isolation
      const context = DataIsolationService.createContext(session!.user);
      const canAccessOwnInstitute = DataIsolationService.canAccessResource(
        context,
        'branch',
        { id: 'branch-1', instituteId: 'inst-1' }
      );
      expect(canAccessOwnInstitute).toBe(true);

      // 3. User cannot access other institute data
      const cannotAccessOtherInstitute = DataIsolationService.canAccessResource(
        context,
        'branch',
        { id: 'branch-2', instituteId: 'inst-2' }
      );
      expect(cannotAccessOtherInstitute).toBe(false);

      // 4. Token refresh works
      const refreshResult = await tokenRefreshService.checkAndRefreshToken();
      expect(typeof refreshResult).toBe('boolean');
    });

    it('should handle security violations correctly', () => {
      const context = DataIsolationService.createContext(mockUsers.student);

      // Student tries to access admin functions
      const canDeleteUser = DataIsolationService.canModifyResource(
        context,
        'user',
        mockUsers.supportStaff,
        'delete'
      );
      expect(canDeleteUser).toBe(false);

      // Student tries to access other institute data
      const canAccessOtherInstitute = DataIsolationService.canAccessResource(
        context,
        'institute',
        { id: 'inst-2' }
      );
      expect(canAccessOtherInstitute).toBe(false);
    });
  });
});
