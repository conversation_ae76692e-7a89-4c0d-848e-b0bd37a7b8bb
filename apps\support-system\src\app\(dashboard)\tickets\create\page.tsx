'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { TicketCreateForm } from '@/components/tickets/TicketCreateForm';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export default function CreateTicketPage() {
  const router = useRouter();

  const handleSuccess = (ticket: any) => {
    // Redirect to the created ticket
    router.push(`/tickets/${ticket.id}`);
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="container mx-auto py-6 px-4">
      {/* Header */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Tickets
        </Button>
        
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create New Ticket</h1>
          <p className="text-gray-600 mt-2">
            Create a new support ticket to track and resolve customer issues.
          </p>
        </div>
      </div>

      {/* Form */}
      <TicketCreateForm
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
