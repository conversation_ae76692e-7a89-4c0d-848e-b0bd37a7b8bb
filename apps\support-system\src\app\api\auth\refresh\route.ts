import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { sessionCache } from '@/lib/redis';
import { prisma } from '@/lib/prisma';
import { withRateLimit, rateLimitConfigs } from '@/lib/rate-limit-middleware';

/**
 * Manual token refresh endpoint
 * POST /api/auth/refresh
 */
export const POST = withRateLimit(
  rateLimitConfigs.auth,
  async (req: NextRequest) => {
    try {
      // Get current session
      const session = await getServerSession(authOptions);

      if (!session || !session.user) {
        return NextResponse.json(
          { error: 'No active session found' },
          { status: 401 }
        );
      }

      const userId = session.user.id;

      // Fetch fresh user data from database
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          instituteId: true,
          branchId: true,
          lmsUserId: true,
          isActive: true,
          lastLoginAt: true,
        },
      });

      if (!user || !user.isActive) {
        // User is inactive or deleted
        await sessionCache.invalidateUserSessions(userId);
        
        return NextResponse.json(
          { error: 'User account is inactive or not found' },
          { status: 403 }
        );
      }

      // Update last login time
      await prisma.user.update({
        where: { id: userId },
        data: { lastLoginAt: new Date() },
      });

      // Update session activity in Redis
      await sessionCache.setSessionData(userId, {
        lastActivity: Date.now(),
        refreshedAt: Date.now(),
        userAgent: req.headers.get('user-agent') || '',
        ipAddress: req.headers.get('x-forwarded-for') || req.ip || '',
      }, 30 * 24 * 60 * 60); // 30 days

      // Return refreshed user data
      return NextResponse.json({
        message: 'Token refreshed successfully',
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          instituteId: user.instituteId,
          branchId: user.branchId,
          lmsUserId: user.lmsUserId,
        },
        refreshedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      
      return NextResponse.json(
        { error: 'Internal server error during token refresh' },
        { status: 500 }
      );
    }
  }
);

/**
 * Get token status
 * GET /api/auth/refresh
 */
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'No active session found' },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get session data from Redis
    const sessionData = await sessionCache.getSessionData(userId);
    
    // Get active sessions
    const activeSessions = await sessionCache.getActiveSessions(userId);

    // Calculate token expiry information
    const now = Date.now();
    const lastRefresh = (session as any).lastRefresh || 0;
    const tokenExp = (session as any).tokenExp || 0;
    const expiresAt = tokenExp ? tokenExp * 1000 : now + (30 * 24 * 60 * 60 * 1000);
    const timeUntilExpiry = expiresAt - now;
    const needsRefresh = timeUntilExpiry < (5 * 60 * 1000); // 5 minutes

    return NextResponse.json({
      status: 'active',
      user: {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: session.user.role,
        instituteId: session.user.instituteId,
        branchId: session.user.branchId,
        lmsUserId: session.user.lmsUserId,
      },
      token: {
        expiresAt,
        timeUntilExpiry,
        lastRefresh,
        needsRefresh,
        isValid: timeUntilExpiry > 0,
      },
      session: {
        activeSessions: activeSessions.length,
        lastActivity: sessionData?.lastActivity || lastRefresh,
        sessionData,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Get token status error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
