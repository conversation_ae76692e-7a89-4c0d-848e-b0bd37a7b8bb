import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { cacheService } from '@/lib/redis';
import { UserRole } from '@prisma/client';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Only super admins can clear cache
    if (!session || session.user.role !== UserRole.SUPER_ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { pattern, keys } = body;

    let deletedCount = 0;

    if (pattern) {
      // Delete keys matching pattern
      deletedCount = await cacheService.deletePattern(pattern);
    } else if (keys && Array.isArray(keys)) {
      // Delete specific keys
      for (const key of keys) {
        await cacheService.del(key);
        deletedCount++;
      }
    } else {
      return NextResponse.json(
        { error: 'Either pattern or keys array is required' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      message: 'Cache cleared successfully',
      deletedCount,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
    
    return NextResponse.json(
      { error: 'Failed to clear cache' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Only super admins can view cache keys
    if (!session || session.user.role !== UserRole.SUPER_ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const pattern = searchParams.get('pattern') || '*';
    const limit = parseInt(searchParams.get('limit') || '100');

    // Get keys matching pattern
    const allKeys = await cacheService.keys(pattern);
    const keys = allKeys.slice(0, limit);

    // Group keys by prefix for better organization
    const groupedKeys: Record<string, string[]> = {};
    
    keys.forEach(key => {
      const prefix = key.split(':')[0];
      if (!groupedKeys[prefix]) {
        groupedKeys[prefix] = [];
      }
      groupedKeys[prefix].push(key);
    });

    return NextResponse.json({
      totalKeys: allKeys.length,
      returnedKeys: keys.length,
      pattern,
      groupedKeys,
      keys,
    });
  } catch (error) {
    console.error('Error getting cache keys:', error);
    
    return NextResponse.json(
      { error: 'Failed to get cache keys' },
      { status: 500 }
    );
  }
}
