import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { cacheService } from '@/lib/redis';
import { UserRole } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Only super admins can check cache health
    if (!session || session.user.role !== UserRole.SUPER_ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Test Redis connection
    const startTime = Date.now();
    const pingResult = await cacheService.ping();
    const responseTime = Date.now() - startTime;

    // Get Redis info
    const info = await cacheService.info();
    const infoLines = info.split('\r\n');
    
    const redisInfo = {
      version: infoLines.find(line => line.startsWith('redis_version:'))?.split(':')[1],
      uptime: infoLines.find(line => line.startsWith('uptime_in_seconds:'))?.split(':')[1],
      connected_clients: infoLines.find(line => line.startsWith('connected_clients:'))?.split(':')[1],
      used_memory: infoLines.find(line => line.startsWith('used_memory_human:'))?.split(':')[1],
      total_commands_processed: infoLines.find(line => line.startsWith('total_commands_processed:'))?.split(':')[1],
    };

    return NextResponse.json({
      status: 'healthy',
      ping: pingResult,
      responseTime: `${responseTime}ms`,
      timestamp: new Date().toISOString(),
      redis: redisInfo,
    });
  } catch (error) {
    console.error('Redis health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: 'Redis connection failed',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
