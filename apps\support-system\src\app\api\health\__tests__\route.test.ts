import { GET } from '../route';

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    json: jest.fn((data, options) => ({
      json: async () => data,
      status: options?.status || 200,
    })),
  },
}));

describe('/api/health', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return health check data with status 200', async () => {
    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('status', 'ok');
    expect(data).toHaveProperty('timestamp');
    expect(data).toHaveProperty('uptime');
    expect(data).toHaveProperty('environment');
    expect(data).toHaveProperty('version');
  });

  it('should return current timestamp', async () => {
    const beforeTime = Date.now();
    const response = await GET();
    const data = await response.json();
    const afterTime = Date.now();
    const timestampTime = new Date(data.timestamp).getTime();

    expect(timestampTime).toBeGreaterThanOrEqual(beforeTime);
    expect(timestampTime).toBeLessThanOrEqual(afterTime);
  });

  it('should return process uptime', async () => {
    const response = await GET();
    const data = await response.json();

    expect(typeof data.uptime).toBe('number');
    expect(data.uptime).toBeGreaterThanOrEqual(0);
  });

  it('should return environment information', async () => {
    const response = await GET();
    const data = await response.json();

    expect(data.environment).toBeDefined();
    expect(typeof data.environment).toBe('string');
  });

  it('should handle errors gracefully', async () => {
    // Mock process.uptime to throw an error
    const originalUptime = process.uptime;
    process.uptime = jest.fn(() => {
      throw new Error('Test error');
    });

    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data).toHaveProperty('status', 'error');
    expect(data).toHaveProperty('timestamp');
    expect(data).toHaveProperty('error', 'Health check failed');

    // Restore original function
    process.uptime = originalUptime;
  });
});
