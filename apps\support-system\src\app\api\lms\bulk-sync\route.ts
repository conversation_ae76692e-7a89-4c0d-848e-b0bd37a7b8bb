import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { syncUserFromLMS, fetchUserFromLMS, syncInstituteFromLMS, syncBranchFromLMS } from '@/lib/lms-integration';
import { UserRole } from '@prisma/client';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Only super admins can perform bulk sync
    if (!session || session.user.role !== UserRole.SUPER_ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { lmsUserIds, instituteId } = body;

    if (!lmsUserIds || !Array.isArray(lmsUserIds)) {
      return NextResponse.json(
        { error: 'LMS User IDs array is required' },
        { status: 400 }
      );
    }

    const results = {
      success: [],
      failed: [],
      total: lmsUserIds.length,
    };

    // Process users in batches to avoid overwhelming the system
    const batchSize = 10;
    for (let i = 0; i < lmsUserIds.length; i += batchSize) {
      const batch = lmsUserIds.slice(i, i + batchSize);
      
      await Promise.allSettled(
        batch.map(async (lmsUserId: string) => {
          try {
            // Fetch user from LMS
            const lmsUser = await fetchUserFromLMS(lmsUserId);

            if (!lmsUser) {
              results.failed.push({
                lmsUserId,
                error: 'User not found in LMS',
              });
              return;
            }

            // Filter by institute if specified
            if (instituteId && lmsUser.instituteId !== instituteId) {
              results.failed.push({
                lmsUserId,
                error: 'User does not belong to specified institute',
              });
              return;
            }

            // Sync institute if provided
            if (lmsUser.instituteId) {
              await syncInstituteFromLMS(lmsUser.instituteId);
            }

            // Sync branch if provided
            if (lmsUser.branchId) {
              await syncBranchFromLMS(lmsUser.branchId);
            }

            // Sync user
            const syncedUser = await syncUserFromLMS(lmsUser);

            results.success.push({
              lmsUserId,
              userId: syncedUser.id,
              email: syncedUser.email,
              role: syncedUser.role,
            });
          } catch (error) {
            console.error(`Error syncing user ${lmsUserId}:`, error);
            results.failed.push({
              lmsUserId,
              error: error instanceof Error ? error.message : 'Unknown error',
            });
          }
        })
      );
    }

    return NextResponse.json({
      message: 'Bulk sync completed',
      results,
    });
  } catch (error) {
    console.error('Error in bulk sync:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
