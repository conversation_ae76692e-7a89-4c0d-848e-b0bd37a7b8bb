import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { syncUserFromLMS, fetchUserFromLMS, syncInstituteFromLMS, syncBranchFromLMS } from '@/lib/lms-integration';
import { UserRole } from '@prisma/client';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Only super admins can sync users
    if (!session || session.user.role !== UserRole.SUPER_ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { lmsUserId } = body;

    if (!lmsUserId) {
      return NextResponse.json(
        { error: 'LMS User ID is required' },
        { status: 400 }
      );
    }

    // Fetch user from LMS
    const lmsUser = await fetchUserFromLMS(lmsUserId);

    if (!lmsUser) {
      return NextResponse.json(
        { error: 'User not found in LMS' },
        { status: 404 }
      );
    }

    // Sync institute if provided
    if (lmsUser.instituteId) {
      await syncInstituteFromLMS(lmsUser.instituteId);
    }

    // Sync branch if provided
    if (lmsUser.branchId) {
      await syncBranchFromLMS(lmsUser.branchId);
    }

    // Sync user
    const syncedUser = await syncUserFromLMS(lmsUser);

    return NextResponse.json({
      message: 'User synced successfully',
      user: {
        id: syncedUser.id,
        email: syncedUser.email,
        name: syncedUser.name,
        role: syncedUser.role,
        instituteId: syncedUser.instituteId,
        branchId: syncedUser.branchId,
        lmsUserId: syncedUser.lmsUserId,
        isActive: syncedUser.isActive,
      },
    });
  } catch (error) {
    console.error('Error syncing user from LMS:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Only authenticated users can check sync status
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const lmsUserId = searchParams.get('lmsUserId');

    if (!lmsUserId) {
      return NextResponse.json(
        { error: 'LMS User ID is required' },
        { status: 400 }
      );
    }

    // Fetch user from LMS to check if it exists
    const lmsUser = await fetchUserFromLMS(lmsUserId);

    if (!lmsUser) {
      return NextResponse.json(
        { error: 'User not found in LMS' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      lmsUser: {
        id: lmsUser.id,
        email: lmsUser.email,
        name: lmsUser.name,
        role: lmsUser.role,
        instituteId: lmsUser.instituteId,
        branchId: lmsUser.branchId,
        isActive: lmsUser.isActive,
      },
    });
  } catch (error) {
    console.error('Error fetching user from LMS:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
