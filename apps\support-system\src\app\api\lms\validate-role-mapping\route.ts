import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { mapLMSRoleToSupportRole, fetchUserFromLMS } from '@/lib/lms-integration';
import { UserRole } from '@prisma/client';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Only super admins and institute admins can validate role mappings
    if (!session || (session.user.role !== UserRole.SUPER_ADMIN && session.user.role !== UserRole.INSTITUTE_ADMIN)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { lmsUserId, expectedRole } = body;

    if (!lmsUserId) {
      return NextResponse.json(
        { error: 'LMS User ID is required' },
        { status: 400 }
      );
    }

    // Fetch user from LMS
    const lmsUser = await fetchUserFromLMS(lmsUserId);

    if (!lmsUser) {
      return NextResponse.json(
        { error: 'User not found in LMS' },
        { status: 404 }
      );
    }

    // Map the role
    const mappedRole = mapLMSRoleToSupportRole(lmsUser);

    // Check if mapping matches expected role
    const isCorrect = expectedRole ? mappedRole === expectedRole : true;

    return NextResponse.json({
      lmsUser: {
        id: lmsUser.id,
        email: lmsUser.email,
        name: lmsUser.name,
        legacyRole: lmsUser.legacyRole,
        permissions: lmsUser.role.permissions.map(p => p.code),
      },
      mapping: {
        mappedRole,
        expectedRole,
        isCorrect,
      },
      roleHierarchy: {
        [UserRole.SUPER_ADMIN]: 4,
        [UserRole.INSTITUTE_ADMIN]: 3,
        [UserRole.SUPPORT_STAFF]: 2,
        [UserRole.STUDENT]: 1,
      },
    });
  } catch (error) {
    console.error('Error validating role mapping:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Return role mapping documentation
    return NextResponse.json({
      roleMappingRules: {
        [UserRole.SUPER_ADMIN]: {
          description: 'System-wide administrative access',
          lmsConditions: [
            'legacyRole === "super_admin"',
            'permissions includes "manage_system"',
            'permissions includes "manage_all_institutes"',
            'permissions includes "super_admin_access"',
          ],
        },
        [UserRole.INSTITUTE_ADMIN]: {
          description: 'Institute-level administrative access',
          lmsConditions: [
            'permissions includes "manage_institute"',
            'permissions includes "manage_institute_users"',
            'permissions includes "institute_admin_access"',
            'permissions includes "manage_branches"',
          ],
        },
        [UserRole.SUPPORT_STAFF]: {
          description: 'Support ticket management access',
          lmsConditions: [
            'permissions includes "manage_tickets"',
            'permissions includes "support_staff_access"',
            'permissions includes "handle_support_requests"',
            'permissions includes "tutor_access"',
            'permissions includes "trainer_access"',
          ],
        },
        [UserRole.STUDENT]: {
          description: 'Default role for all other users',
          lmsConditions: [
            'Default when no other conditions match',
          ],
        },
      },
      permissionCodes: {
        system: [
          'manage_system',
          'manage_all_institutes',
          'super_admin_access',
        ],
        institute: [
          'manage_institute',
          'manage_institute_users',
          'institute_admin_access',
          'manage_branches',
        ],
        support: [
          'manage_tickets',
          'support_staff_access',
          'handle_support_requests',
          'tutor_access',
          'trainer_access',
        ],
      },
    });
  } catch (error) {
    console.error('Error getting role mapping info:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
