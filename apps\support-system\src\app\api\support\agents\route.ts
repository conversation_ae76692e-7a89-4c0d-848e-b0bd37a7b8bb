import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const skills = searchParams.get('skills')?.split(',') || [];
    const department = searchParams.get('department');
    const status = searchParams.get('status');

    // Build where clause based on user permissions
    const where: any = {
      role: {
        permissions: {
          some: {
            code: {
              in: ['SUPPORT_AGENT', 'SUPPORT_MANAGER', 'INSTITUTE_ADMIN']
            }
          }
        }
      }
    };

    // Multi-tenant filtering
    if (session.user.role !== 'SUPER_ADMIN') {
      where.instituteId = session.user.instituteId;
    }

    // Apply filters
    if (department) {
      where.department = department;
    }

    if (status) {
      where.status = status;
    }

    // Get agents with their current ticket counts
    const agents = await prisma.user.findMany({
      where,
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        department: true,
        role: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            assignedTickets: {
              where: {
                status: {
                  in: ['OPEN', 'IN_PROGRESS']
                }
              }
            }
          }
        }
      },
      orderBy: { name: 'asc' },
    });

    // Transform the data to include availability info
    const agentsWithAvailability = agents.map(agent => ({
      id: agent.id,
      name: agent.name || 'Unknown',
      email: agent.email,
      avatar: agent.avatar,
      role: agent.role?.name || 'Support Agent',
      department: agent.department || 'Support',
      status: 'ONLINE', // In a real app, this would come from a presence system
      currentTickets: agent._count.assignedTickets,
      maxTickets: 10, // This could be configurable per agent
      skills: ['General Support'], // This could come from a skills table
    }));

    // Filter by skills if provided
    let filteredAgents = agentsWithAvailability;
    if (skills.length > 0) {
      filteredAgents = agentsWithAvailability.filter(agent =>
        skills.some(skill => agent.skills.includes(skill))
      );
    }

    return NextResponse.json({
      agents: filteredAgents,
    });
  } catch (error) {
    console.error('Error fetching agents:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
