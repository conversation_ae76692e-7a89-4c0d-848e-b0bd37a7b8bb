import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const instituteId = searchParams.get('instituteId');
    const branchId = searchParams.get('branchId');

    // Build where clause based on user permissions
    const where: any = {};

    // Multi-tenant filtering
    if (session.user.role !== 'SUPER_ADMIN') {
      where.instituteId = session.user.instituteId;
      
      // Branch-level filtering for support staff
      if (session.user.role === 'SUPPORT_STAFF' && session.user.branchId) {
        where.branchId = session.user.branchId;
      }
    } else {
      // Super admin can filter by specific institute/branch
      if (instituteId) where.instituteId = instituteId;
      if (branchId) where.branchId = branchId;
    }

    // Date range filter
    const periodDays = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - periodDays);

    where.createdAt = {
      gte: startDate,
    };

    // Get ticket statistics
    const [
      totalTickets,
      openTickets,
      inProgressTickets,
      resolvedTickets,
      closedTickets,
      avgResponseTime,
      avgResolutionTime,
      slaBreaches,
      satisfactionStats,
      categoryStats,
      priorityStats,
    ] = await Promise.all([
      // Total tickets
      prisma.supportTicket.count({ where }),
      
      // Open tickets
      prisma.supportTicket.count({
        where: { ...where, status: 'OPEN' },
      }),
      
      // In progress tickets
      prisma.supportTicket.count({
        where: { ...where, status: 'IN_PROGRESS' },
      }),
      
      // Resolved tickets
      prisma.supportTicket.count({
        where: { ...where, status: 'RESOLVED' },
      }),
      
      // Closed tickets
      prisma.supportTicket.count({
        where: { ...where, status: 'CLOSED' },
      }),
      
      // Average response time
      prisma.ticketAnalytics.aggregate({
        where: {
          ...where,
          firstResponseTime: { not: null },
        },
        _avg: {
          firstResponseTime: true,
        },
      }),
      
      // Average resolution time
      prisma.ticketAnalytics.aggregate({
        where: {
          ...where,
          resolutionTime: { not: null },
        },
        _avg: {
          resolutionTime: true,
        },
      }),
      
      // SLA breaches
      prisma.ticketAnalytics.count({
        where: {
          ...where,
          OR: [
            { slaResponseMet: false },
            { slaResolutionMet: false },
          ],
        },
      }),
      
      // Satisfaction statistics
      prisma.ticketAnalytics.aggregate({
        where: {
          ...where,
          satisfactionScore: { not: null },
        },
        _avg: {
          satisfactionScore: true,
        },
        _count: {
          satisfactionScore: true,
        },
      }),
      
      // Category statistics
      prisma.supportTicket.groupBy({
        by: ['categoryId'],
        where,
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
        take: 10,
      }),
      
      // Priority statistics
      prisma.supportTicket.groupBy({
        by: ['priority'],
        where,
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
      }),
    ]);

    // Get category names for category stats
    const categoryIds = categoryStats.map(stat => stat.categoryId).filter(Boolean);
    const categories = await prisma.supportCategory.findMany({
      where: { id: { in: categoryIds } },
      select: { id: true, name: true },
    });

    const categoryStatsWithNames = categoryStats.map(stat => ({
      ...stat,
      categoryName: categories.find(cat => cat.id === stat.categoryId)?.name || 'Uncategorized',
    }));

    // Calculate additional metrics
    const totalResolved = resolvedTickets + closedTickets;
    const resolutionRate = totalTickets > 0 ? (totalResolved / totalTickets) * 100 : 0;
    const avgSatisfaction = satisfactionStats._avg.satisfactionScore || 0;

    // Get daily ticket creation trend
    const dailyTrend = await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM support_tickets 
      WHERE created_at >= ${startDate}
        ${session.user.role !== 'SUPER_ADMIN' ? prisma.$queryRaw`AND institute_id = ${session.user.instituteId}` : prisma.$queryRaw``}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `;

    const analytics = {
      summary: {
        totalTickets,
        openTickets,
        inProgressTickets,
        resolvedTickets,
        closedTickets,
        resolutionRate: Math.round(resolutionRate * 100) / 100,
        avgResponseTime: Math.round((avgResponseTime._avg.firstResponseTime || 0) * 100) / 100,
        avgResolutionTime: Math.round((avgResolutionTime._avg.resolutionTime || 0) * 100) / 100,
        slaBreaches,
        avgSatisfaction: Math.round((avgSatisfaction || 0) * 100) / 100,
        satisfactionResponses: satisfactionStats._count.satisfactionScore,
      },
      trends: {
        daily: dailyTrend,
      },
      breakdowns: {
        byCategory: categoryStatsWithNames,
        byPriority: priorityStats,
      },
      period: {
        days: periodDays,
        startDate,
        endDate: new Date(),
      },
    };

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
