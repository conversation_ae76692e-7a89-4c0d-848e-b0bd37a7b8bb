import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Build where clause based on user permissions
    const where: any = {};

    // Multi-tenant filtering
    if (session.user.role !== 'SUPER_ADMIN') {
      where.instituteId = session.user.instituteId;
    }

    // Get categories
    const categories = await prisma.supportCategory.findMany({
      where,
      select: {
        id: true,
        name: true,
        description: true,
        color: true,
        isActive: true,
      },
      orderBy: { name: 'asc' },
    });

    return NextResponse.json({
      categories,
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
