import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const ticketId = params.id;
    const body = await request.json();
    const { assignedTo, note } = body;

    // Verify ticket exists and user has access
    const ticket = await prisma.supportTicket.findFirst({
      where: {
        id: ticketId,
        ...(session.user.role !== 'SUPER_ADMIN' && {
          instituteId: session.user.instituteId,
        }),
      },
      include: {
        assignee: {
          select: { id: true, name: true, email: true },
        },
      },
    });

    if (!ticket) {
      return NextResponse.json(
        { error: 'Ticket not found or access denied' },
        { status: 404 }
      );
    }

    // If assigning to someone, verify the agent exists and has access
    if (assignedTo) {
      const agent = await prisma.user.findFirst({
        where: {
          id: assignedTo,
          ...(session.user.role !== 'SUPER_ADMIN' && {
            instituteId: session.user.instituteId,
          }),
        },
      });

      if (!agent) {
        return NextResponse.json(
          { error: 'Agent not found or access denied' },
          { status: 404 }
        );
      }
    }

    // Update ticket assignment
    const updatedTicket = await prisma.supportTicket.update({
      where: { id: ticketId },
      data: {
        assignedTo: assignedTo || null,
        updatedBy: session.user.id,
        updatedAt: new Date(),
      },
      include: {
        assignee: {
          select: { id: true, name: true, email: true },
        },
        category: {
          select: { id: true, name: true },
        },
      },
    });

    // Create assignment history/note
    const action = !ticket.assignedTo && assignedTo 
      ? 'ASSIGNED'
      : ticket.assignedTo && !assignedTo
      ? 'UNASSIGNED'
      : 'REASSIGNED';

    await prisma.ticketMessage.create({
      data: {
        content: note || `Ticket ${action.toLowerCase()}${assignedTo ? ` to ${updatedTicket.assignee?.name}` : ''}`,
        messageType: 'NOTE',
        ticketId: ticketId,
        authorId: session.user.id,
        isInternal: true,
        metadata: {
          action,
          previousAssignee: ticket.assignee?.id,
          newAssignee: assignedTo,
        },
      },
    });

    // Update ticket status if needed
    if (assignedTo && ticket.status === 'OPEN') {
      await prisma.supportTicket.update({
        where: { id: ticketId },
        data: { status: 'IN_PROGRESS' },
      });
    }

    return NextResponse.json(updatedTicket);
  } catch (error) {
    console.error('Error assigning ticket:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
