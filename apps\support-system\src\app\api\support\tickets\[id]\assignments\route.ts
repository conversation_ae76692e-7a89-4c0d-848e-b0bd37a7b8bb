import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const ticketId = params.id;

    // Verify ticket exists and user has access
    const ticket = await prisma.supportTicket.findFirst({
      where: {
        id: ticketId,
        ...(session.user.role !== 'SUPER_ADMIN' && {
          instituteId: session.user.instituteId,
        }),
      },
    });

    if (!ticket) {
      return NextResponse.json(
        { error: 'Ticket not found or access denied' },
        { status: 404 }
      );
    }

    // Get assignment history from ticket messages
    const assignmentMessages = await prisma.ticketMessage.findMany({
      where: {
        ticketId: ticketId,
        messageType: 'NOTE',
        isInternal: true,
        metadata: {
          path: ['action'],
          in: ['ASSIGNED', 'UNASSIGNED', 'REASSIGNED'],
        },
      },
      include: {
        author: {
          select: { id: true, name: true, email: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Transform messages to assignment history format
    const assignments = await Promise.all(
      assignmentMessages.map(async (message) => {
        const metadata = message.metadata as any;
        let assignedTo = null;

        if (metadata?.newAssignee) {
          const agent = await prisma.user.findUnique({
            where: { id: metadata.newAssignee },
            select: { name: true, email: true },
          });
          assignedTo = agent;
        }

        return {
          id: message.id,
          assignedTo,
          assignedBy: {
            name: message.author.name || 'Unknown',
            email: message.author.email,
          },
          assignedAt: message.createdAt.toISOString(),
          note: message.content,
          action: metadata?.action || 'ASSIGNED',
        };
      })
    );

    return NextResponse.json({
      assignments,
    });
  } catch (error) {
    console.error('Error fetching assignment history:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
