import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { validateInstituteAccess } from '@/lib/payload-auth';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const ticketId = params.id;

    // Build where clause based on user permissions
    const where: any = { id: ticketId };

    // Multi-tenant filtering
    if (session.user.role !== 'SUPER_ADMIN') {
      where.instituteId = session.user.instituteId;
      
      // Branch-level filtering for support staff
      if (session.user.role === 'SUPPORT_STAFF' && session.user.branchId) {
        where.branchId = session.user.branchId;
      }
    }

    const ticket = await prisma.supportTicket.findFirst({
      where,
      include: {
        category: true,
        template: true,
        creator: {
          select: { id: true, name: true, email: true },
        },
        assignee: {
          select: { id: true, name: true, email: true },
        },
        messages: {
          include: {
            author: {
              select: { id: true, name: true, email: true },
            },
            attachments: true,
          },
          orderBy: { createdAt: 'asc' },
        },
        attachments: {
          include: {
            uploadedByUser: {
              select: { id: true, name: true, email: true },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        notes: {
          include: {
            author: {
              select: { id: true, name: true, email: true },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        analytics: true,
      },
    });

    if (!ticket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    return NextResponse.json(ticket);
  } catch (error) {
    console.error('Error fetching ticket:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const ticketId = params.id;
    const body = await request.json();

    // Check if ticket exists and user has access
    const existingTicket = await prisma.supportTicket.findFirst({
      where: {
        id: ticketId,
        ...(session.user.role !== 'SUPER_ADMIN' && {
          instituteId: session.user.instituteId,
        }),
      },
    });

    if (!existingTicket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    // Validate institute access
    if (!validateInstituteAccess(session.user, existingTicket.instituteId)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Prepare update data
    const updateData: any = {};
    const allowedFields = [
      'title',
      'description',
      'status',
      'priority',
      'type',
      'categoryId',
      'assignedTo',
      'assignedTeam',
      'customerName',
      'customerEmail',
      'customerPhone',
      'tags',
    ];

    // Only update allowed fields
    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    });

    // Handle status change timestamps
    const now = new Date();
    if (body.status && body.status !== existingTicket.status) {
      if (body.status === 'IN_PROGRESS' && !existingTicket.firstResponseAt) {
        updateData.firstResponseAt = now;
      }
      if (body.status === 'RESOLVED' && !existingTicket.resolvedAt) {
        updateData.resolvedAt = now;
      }
      if (body.status === 'CLOSED' && !existingTicket.closedAt) {
        updateData.closedAt = now;
      }
    }

    // Update ticket
    const updatedTicket = await prisma.supportTicket.update({
      where: { id: ticketId },
      data: updateData,
      include: {
        category: true,
        template: true,
        creator: {
          select: { id: true, name: true, email: true },
        },
        assignee: {
          select: { id: true, name: true, email: true },
        },
        analytics: true,
      },
    });

    // Update analytics if ticket is completed
    if (body.status === 'RESOLVED' || body.status === 'CLOSED') {
      const analytics = await prisma.ticketAnalytics.findUnique({
        where: { ticketId },
      });

      if (analytics) {
        const resolutionTime = updatedTicket.resolvedAt
          ? Math.round((updatedTicket.resolvedAt.getTime() - updatedTicket.createdAt.getTime()) / (1000 * 60))
          : null;

        const firstResponseTime = updatedTicket.firstResponseAt
          ? Math.round((updatedTicket.firstResponseAt.getTime() - updatedTicket.createdAt.getTime()) / (1000 * 60))
          : null;

        await prisma.ticketAnalytics.update({
          where: { ticketId },
          data: {
            resolutionTime,
            firstResponseTime,
            slaResolutionMet: updatedTicket.slaResolutionDue
              ? (updatedTicket.resolvedAt || now) <= updatedTicket.slaResolutionDue
              : null,
            slaResponseMet: updatedTicket.slaResponseDue
              ? (updatedTicket.firstResponseAt || now) <= updatedTicket.slaResponseDue
              : null,
          },
        });
      }
    }

    return NextResponse.json(updatedTicket);
  } catch (error) {
    console.error('Error updating ticket:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only super admin and institute admin can delete tickets
    if (!['SUPER_ADMIN', 'INSTITUTE_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const ticketId = params.id;

    // Check if ticket exists and user has access
    const existingTicket = await prisma.supportTicket.findFirst({
      where: {
        id: ticketId,
        ...(session.user.role !== 'SUPER_ADMIN' && {
          instituteId: session.user.instituteId,
        }),
      },
    });

    if (!existingTicket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    // Validate institute access
    if (!validateInstituteAccess(session.user, existingTicket.instituteId)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Delete ticket (cascade will handle related records)
    await prisma.supportTicket.delete({
      where: { id: ticketId },
    });

    return NextResponse.json({ message: 'Ticket deleted successfully' });
  } catch (error) {
    console.error('Error deleting ticket:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
