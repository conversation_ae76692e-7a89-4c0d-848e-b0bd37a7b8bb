import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { ticketIds, assignedTo, note } = body;

    if (!Array.isArray(ticketIds) || ticketIds.length === 0) {
      return NextResponse.json(
        { error: 'Ticket IDs array is required' },
        { status: 400 }
      );
    }

    // Verify all tickets exist and user has access
    const tickets = await prisma.supportTicket.findMany({
      where: {
        id: { in: ticketIds },
        ...(session.user.role !== 'SUPER_ADMIN' && {
          instituteId: session.user.instituteId,
        }),
      },
      include: {
        assignee: {
          select: { id: true, name: true },
        },
      },
    });

    if (tickets.length !== ticketIds.length) {
      return NextResponse.json(
        { error: 'Some tickets not found or access denied' },
        { status: 404 }
      );
    }

    // If assigning to someone, verify the agent exists and has access
    if (assignedTo) {
      const agent = await prisma.user.findFirst({
        where: {
          id: assignedTo,
          ...(session.user.role !== 'SUPER_ADMIN' && {
            instituteId: session.user.instituteId,
          }),
        },
      });

      if (!agent) {
        return NextResponse.json(
          { error: 'Agent not found or access denied' },
          { status: 404 }
        );
      }
    }

    // Perform bulk assignment
    const updatePromises = tickets.map(async (ticket) => {
      // Determine action type
      const action = !ticket.assignedTo && assignedTo 
        ? 'ASSIGNED'
        : ticket.assignedTo && !assignedTo
        ? 'UNASSIGNED'
        : 'REASSIGNED';

      // Update ticket
      const updatedTicket = await prisma.supportTicket.update({
        where: { id: ticket.id },
        data: {
          assignedTo: assignedTo || null,
          updatedBy: session.user.id,
          updatedAt: new Date(),
          // Update status if assigning to someone and ticket is open
          ...(assignedTo && ticket.status === 'OPEN' && { status: 'IN_PROGRESS' }),
        },
      });

      // Create assignment note
      await prisma.ticketMessage.create({
        data: {
          content: note || `Ticket ${action.toLowerCase()} via bulk operation${assignedTo ? ` to agent` : ''}`,
          messageType: 'NOTE',
          ticketId: ticket.id,
          authorId: session.user.id,
          isInternal: true,
          metadata: {
            action,
            bulkOperation: true,
            previousAssignee: ticket.assignee?.id,
            newAssignee: assignedTo,
          },
        },
      });

      return updatedTicket;
    });

    const updatedTickets = await Promise.all(updatePromises);

    return NextResponse.json({
      assignedCount: updatedTickets.length,
      tickets: updatedTickets.map(ticket => ({
        id: ticket.id,
        ticketNumber: ticket.ticketNumber,
        assignedTo: ticket.assignedTo,
      })),
    });
  } catch (error) {
    console.error('Error in bulk assignment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
