import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { validateInstituteAccess } from '@/lib/payload-auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const assignedTo = searchParams.get('assignedTo');
    const priority = searchParams.get('priority');
    const categoryId = searchParams.get('categoryId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');

    // Build where clause based on user permissions
    const where: any = {};

    // Multi-tenant filtering
    if (session.user.role !== 'SUPER_ADMIN') {
      where.instituteId = session.user.instituteId;
      
      // Branch-level filtering for support staff
      if (session.user.role === 'SUPPORT_STAFF' && session.user.branchId) {
        where.branchId = session.user.branchId;
      }
    }

    // Apply filters
    if (status) where.status = status;
    if (assignedTo) where.assignedTo = assignedTo;
    if (priority) where.priority = priority;
    if (categoryId) where.categoryId = categoryId;

    // Search functionality
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { ticketNumber: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get tickets with pagination
    const [tickets, total] = await Promise.all([
      prisma.supportTicket.findMany({
        where,
        include: {
          category: true,
          template: true,
          creator: {
            select: { id: true, name: true, email: true },
          },
          assignee: {
            select: { id: true, name: true, email: true },
          },
          analytics: true,
          _count: {
            select: {
              messages: true,
              attachments: true,
              notes: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.supportTicket.count({ where }),
    ]);

    return NextResponse.json({
      tickets,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching tickets:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      title,
      description,
      priority = 'MEDIUM',
      type = 'INCIDENT',
      categoryId,
      templateId,
      assignedTo,
      customerName,
      customerEmail,
      customerPhone,
      tags = [],
    } = body;

    // Validate required fields
    if (!title || !description) {
      return NextResponse.json(
        { error: 'Title and description are required' },
        { status: 400 }
      );
    }

    // Validate institute access
    if (!validateInstituteAccess(session.user, session.user.instituteId)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Generate ticket number
    const year = new Date().getFullYear();
    const instituteCode = session.user.instituteId?.slice(-4) || 'UNKN';
    const randomNum = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
    const ticketNumber = `${instituteCode}-${year}-${randomNum}`;

    // Calculate SLA due dates (would fetch from category in real implementation)
    const now = new Date();
    const responseHours = 24; // Default or from category
    const resolutionHours = 72; // Default or from category
    
    const slaResponseDue = new Date(now.getTime() + responseHours * 60 * 60 * 1000);
    const slaResolutionDue = new Date(now.getTime() + resolutionHours * 60 * 60 * 1000);

    // Create ticket
    const ticket = await prisma.supportTicket.create({
      data: {
        ticketNumber,
        title,
        description,
        priority,
        type,
        status: 'OPEN',
        categoryId,
        templateId,
        assignedTo,
        customerName,
        customerEmail,
        customerPhone,
        tags,
        slaResponseDue,
        slaResolutionDue,
        instituteId: session.user.instituteId!,
        branchId: session.user.branchId,
        createdBy: session.user.id,
        source: 'WEB',
      },
      include: {
        category: true,
        template: true,
        creator: {
          select: { id: true, name: true, email: true },
        },
        assignee: {
          select: { id: true, name: true, email: true },
        },
      },
    });

    // Create initial analytics record
    await prisma.ticketAnalytics.create({
      data: {
        ticketId: ticket.id,
        instituteId: session.user.instituteId!,
        branchId: session.user.branchId,
      },
    });

    return NextResponse.json(ticket, { status: 201 });
  } catch (error) {
    console.error('Error creating ticket:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
