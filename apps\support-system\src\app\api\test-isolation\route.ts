import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { DataIsolationService } from '@/lib/data-isolation';
import { withDataIsolation, applyIsolationToQuery } from '@/lib/isolation-middleware';
import { UserRole } from '@prisma/client';

// Mock data for testing isolation
const mockUsers = [
  { id: 'user1', email: '<EMAIL>', role: 'INSTITUTE_ADMIN', instituteId: 'inst1', branchId: 'branch1' },
  { id: 'user2', email: '<EMAIL>', role: 'SUPPORT_STAFF', instituteId: 'inst1', branchId: 'branch1' },
  { id: 'user3', email: '<EMAIL>', role: 'STUDENT', instituteId: 'inst1', branchId: 'branch1' },
  { id: 'user4', email: '<EMAIL>', role: 'INSTITUTE_ADMIN', instituteId: 'inst2', branchId: 'branch2' },
  { id: 'user5', email: '<EMAIL>', role: 'SUPER_ADMIN', instituteId: null, branchId: null },
];

const mockTickets = [
  { id: 'ticket1', title: 'Issue 1', createdBy: 'user1', assignedTo: 'user2', instituteId: 'inst1', branchId: 'branch1' },
  { id: 'ticket2', title: 'Issue 2', createdBy: 'user3', assignedTo: 'user2', instituteId: 'inst1', branchId: 'branch1' },
  { id: 'ticket3', title: 'Issue 3', createdBy: 'user4', assignedTo: null, instituteId: 'inst2', branchId: 'branch2' },
];

const mockMedia = [
  { id: 'media1', filename: 'doc1.pdf', uploadedBy: 'user1', instituteId: 'inst1', isPublic: true },
  { id: 'media2', filename: 'doc2.pdf', uploadedBy: 'user2', instituteId: 'inst1', isPublic: false },
  { id: 'media3', filename: 'doc3.pdf', uploadedBy: 'user4', instituteId: 'inst2', isPublic: true },
];

// Test endpoint without isolation middleware
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const resourceType = searchParams.get('resource') || 'users';
    const testType = searchParams.get('test') || 'filter';

    // Create isolation context
    const context = DataIsolationService.createContext(session.user);

    let result: any = {};

    switch (testType) {
      case 'filter':
        result = await testFilteringLogic(context, resourceType);
        break;
      
      case 'access':
        result = await testAccessValidation(context, resourceType);
        break;
      
      case 'modify':
        result = await testModificationPermissions(context, resourceType);
        break;
      
      default:
        result = { error: 'Invalid test type' };
    }

    return NextResponse.json({
      context: {
        userId: context.userId,
        role: context.role,
        instituteId: context.instituteId,
        branchId: context.branchId,
      },
      resourceType,
      testType,
      result,
    });
  } catch (error) {
    console.error('Test isolation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function testFilteringLogic(context: any, resourceType: string) {
  let mockData: any[] = [];
  
  switch (resourceType) {
    case 'users':
      mockData = mockUsers;
      break;
    case 'support-tickets':
      mockData = mockTickets;
      break;
    case 'media':
      mockData = mockMedia;
      break;
    default:
      return { error: 'Unknown resource type' };
  }

  // Apply isolation filter
  const baseQuery = {};
  const isolatedQuery = DataIsolationService.applyIsolationFilter(
    context,
    baseQuery,
    resourceType
  );

  // Simulate filtering (in real app, this would be done by the database)
  const filteredData = mockData.filter(item => {
    return DataIsolationService.canAccessResource(context, resourceType.slice(0, -1), item);
  });

  return {
    totalItems: mockData.length,
    filteredItems: filteredData.length,
    isolatedQuery,
    data: filteredData,
  };
}

async function testAccessValidation(context: any, resourceType: string) {
  let mockData: any[] = [];
  
  switch (resourceType) {
    case 'users':
      mockData = mockUsers;
      break;
    case 'support-tickets':
      mockData = mockTickets;
      break;
    case 'media':
      mockData = mockMedia;
      break;
    default:
      return { error: 'Unknown resource type' };
  }

  const accessResults = mockData.map(item => ({
    id: item.id,
    canAccess: DataIsolationService.canAccessResource(
      context,
      resourceType.slice(0, -1),
      item
    ),
    item,
  }));

  return {
    accessResults,
    summary: {
      total: accessResults.length,
      accessible: accessResults.filter(r => r.canAccess).length,
      restricted: accessResults.filter(r => !r.canAccess).length,
    },
  };
}

async function testModificationPermissions(context: any, resourceType: string) {
  let mockData: any[] = [];
  
  switch (resourceType) {
    case 'users':
      mockData = mockUsers;
      break;
    case 'support-tickets':
      mockData = mockTickets;
      break;
    case 'media':
      mockData = mockMedia;
      break;
    default:
      return { error: 'Unknown resource type' };
  }

  const operations = ['create', 'update', 'delete'] as const;
  const resourceTypeSingular = resourceType.slice(0, -1);

  const permissionResults = mockData.map(item => {
    const permissions: any = { id: item.id };
    
    operations.forEach(operation => {
      permissions[operation] = DataIsolationService.canModifyResource(
        context,
        resourceTypeSingular,
        item,
        operation
      );
    });

    return permissions;
  });

  return {
    permissionResults,
    summary: operations.reduce((acc, operation) => {
      acc[operation] = {
        allowed: permissionResults.filter(r => r[operation]).length,
        denied: permissionResults.filter(r => !r[operation]).length,
      };
      return acc;
    }, {} as any),
  };
}

// Test endpoint with isolation middleware
export const POST = withDataIsolation(
  { resourceType: 'test', enforceOnRead: true, enforceOnWrite: true },
  async (req: NextRequest, context) => {
    const body = await req.json();
    const { action, resourceType, resourceData } = body;

    let result: any = {};

    switch (action) {
      case 'validate_access':
        result = {
          canAccess: DataIsolationService.canAccessResource(
            context,
            resourceType,
            resourceData
          ),
        };
        break;
      
      case 'validate_modify':
        const operations = ['create', 'update', 'delete'] as const;
        result = operations.reduce((acc, operation) => {
          acc[operation] = DataIsolationService.canModifyResource(
            context,
            resourceType,
            resourceData,
            operation
          );
          return acc;
        }, {} as any);
        break;
      
      case 'apply_filter':
        result = {
          filter: DataIsolationService.applyIsolationFilter(
            context,
            body.baseQuery || {},
            resourceType
          ),
        };
        break;
      
      default:
        result = { error: 'Unknown action' };
    }

    return NextResponse.json({
      context: {
        userId: context.userId,
        role: context.role,
        instituteId: context.instituteId,
        branchId: context.branchId,
      },
      action,
      resourceType,
      result,
    });
  }
);
