import { CollectionConfig } from 'payload';
import { UserRole } from '@prisma/client';

const Branches: CollectionConfig = {
  slug: 'branches',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'code', 'institute', 'isActive'],
  },
  access: {
    // Super admins can see all branches, others only their institute's branches
    read: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      // Institute admins can see all branches in their institute
      if (user.role === UserRole.INSTITUTE_ADMIN) {
        return {
          institute: {
            equals: user.instituteId,
          },
        };
      }
      
      // Support staff can see all branches in their institute
      if (user.role === UserRole.SUPPORT_STAFF) {
        return {
          institute: {
            equals: user.instituteId,
          },
        };
      }
      
      return false;
    },
    
    // Super admins and institute admins can create branches
    create: ({ req: { user } }) => {
      return [UserRole.SUPER_ADMIN, UserRole.INSTITUTE_ADMIN].includes(user?.role);
    },
    
    // Super admins can update all, institute admins can update their institute's branches
    update: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      if (user.role === UserRole.INSTITUTE_ADMIN) {
        return {
          institute: {
            equals: user.instituteId,
          },
        };
      }
      
      return false;
    },
    
    // Super admins and institute admins can delete branches
    delete: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      if (user.role === UserRole.INSTITUTE_ADMIN) {
        return {
          institute: {
            equals: user.instituteId,
          },
        };
      }
      
      return false;
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'Unique branch code (e.g., MAIN, NORTH, SOUTH)',
      },
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      access: {
        // Institute admins can only assign to their own institute
        update: ({ req: { user } }) => {
          if (user?.role === UserRole.SUPER_ADMIN) {
            return true;
          }
          
          if (user?.role === UserRole.INSTITUTE_ADMIN) {
            // This would need additional validation in hooks
            return true;
          }
          
          return false;
        },
      },
    },
    {
      name: 'address',
      type: 'textarea',
      required: false,
    },
    {
      name: 'phone',
      type: 'text',
      required: false,
    },
    {
      name: 'email',
      type: 'email',
      required: false,
    },
    {
      name: 'manager',
      type: 'relationship',
      relationTo: 'users',
      required: false,
      admin: {
        description: 'Branch manager',
      },
      filterOptions: ({ user }) => {
        // Only show users from the same institute
        if (user?.role === UserRole.SUPER_ADMIN) {
          return {};
        }
        
        return {
          instituteId: {
            equals: user?.instituteId,
          },
          role: {
            in: [UserRole.INSTITUTE_ADMIN, UserRole.SUPPORT_STAFF],
          },
        };
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      access: {
        // Only super admins and institute admins can change active status
        update: ({ req: { user } }) => {
          return [UserRole.SUPER_ADMIN, UserRole.INSTITUTE_ADMIN].includes(user?.role);
        },
      },
    },
    {
      name: 'settings',
      type: 'group',
      fields: [
        {
          name: 'operatingHours',
          type: 'group',
          fields: [
            {
              name: 'monday',
              type: 'text',
              admin: {
                placeholder: '9:00 AM - 5:00 PM',
              },
            },
            {
              name: 'tuesday',
              type: 'text',
              admin: {
                placeholder: '9:00 AM - 5:00 PM',
              },
            },
            {
              name: 'wednesday',
              type: 'text',
              admin: {
                placeholder: '9:00 AM - 5:00 PM',
              },
            },
            {
              name: 'thursday',
              type: 'text',
              admin: {
                placeholder: '9:00 AM - 5:00 PM',
              },
            },
            {
              name: 'friday',
              type: 'text',
              admin: {
                placeholder: '9:00 AM - 5:00 PM',
              },
            },
            {
              name: 'saturday',
              type: 'text',
              admin: {
                placeholder: 'Closed',
              },
            },
            {
              name: 'sunday',
              type: 'text',
              admin: {
                placeholder: 'Closed',
              },
            },
          ],
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        // Validate institute assignment for institute admins
        if (operation === 'create' && req.user?.role === UserRole.INSTITUTE_ADMIN) {
          if (data.institute !== req.user.instituteId) {
            throw new Error('Cannot create branch for a different institute');
          }
        }
        
        if (operation === 'update' && req.user?.role === UserRole.INSTITUTE_ADMIN) {
          if (data.institute && data.institute !== req.user.instituteId) {
            throw new Error('Cannot assign branch to a different institute');
          }
        }
        
        return data;
      },
    ],
  },
};

export default Branches;
