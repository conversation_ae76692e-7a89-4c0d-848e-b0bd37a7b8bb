import { CollectionConfig } from 'payload';
import { UserRole } from '@prisma/client';

const Institutes: CollectionConfig = {
  slug: 'institutes',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'slug', 'email', 'isActive'],
  },
  access: {
    // Super admins can see all institutes, others only their own
    read: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      // Institute admins and staff can only see their own institute
      if ([UserRole.INSTITUTE_ADMIN, UserRole.SUPPORT_STAFF].includes(user.role)) {
        return {
          id: {
            equals: user.instituteId,
          },
        };
      }
      
      return false;
    },
    
    // Only super admins can create institutes
    create: ({ req: { user } }) => {
      return user?.role === UserRole.SUPER_ADMIN;
    },
    
    // Super admins can update all, institute admins can update their own
    update: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      if (user.role === UserRole.INSTITUTE_ADMIN) {
        return {
          id: {
            equals: user.instituteId,
          },
        };
      }
      
      return false;
    },
    
    // Only super admins can delete institutes
    delete: ({ req: { user } }) => {
      return user?.role === UserRole.SUPER_ADMIN;
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'URL-friendly identifier',
      },
    },
    {
      name: 'email',
      type: 'email',
      required: false,
    },
    {
      name: 'phone',
      type: 'text',
      required: false,
    },
    {
      name: 'website',
      type: 'text',
      required: false,
      admin: {
        description: 'Institute website URL',
      },
    },
    {
      name: 'address',
      type: 'textarea',
      required: false,
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      access: {
        // Only super admins can change active status
        update: ({ req: { user } }) => {
          return user?.role === UserRole.SUPER_ADMIN;
        },
      },
    },
    {
      name: 'settings',
      type: 'group',
      fields: [
        {
          name: 'allowTicketCreation',
          type: 'checkbox',
          defaultValue: true,
          admin: {
            description: 'Allow users to create support tickets',
          },
        },
        {
          name: 'autoAssignTickets',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Automatically assign tickets to available staff',
          },
        },
        {
          name: 'maxTicketsPerUser',
          type: 'number',
          defaultValue: 10,
          admin: {
            description: 'Maximum open tickets per user (0 = unlimited)',
          },
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        // Auto-generate slug from name if not provided
        if (operation === 'create' && !data.slug && data.name) {
          data.slug = data.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '');
        }
        
        return data;
      },
    ],
  },
};

export default Institutes;
