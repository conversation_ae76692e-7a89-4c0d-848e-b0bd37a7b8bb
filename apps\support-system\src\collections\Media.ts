import { CollectionConfig } from 'payload';
import { UserRole } from '@prisma/client';

const Media: CollectionConfig = {
  slug: 'media',
  upload: {
    staticDir: 'uploads',
    imageSizes: [
      {
        name: 'thumbnail',
        width: 400,
        height: 300,
        position: 'centre',
      },
      {
        name: 'card',
        width: 768,
        height: 1024,
        position: 'centre',
      },
    ],
    adminThumbnail: 'thumbnail',
    mimeTypes: [
      'image/*',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
    ],
  },
  admin: {
    useAsTitle: 'filename',
    defaultColumns: ['filename', 'alt', 'mimeType', 'filesize'],
  },
  access: {
    // Users can see media they uploaded or media in their institute
    read: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      // Institute admins can see all media in their institute
      if (user.role === UserRole.INSTITUTE_ADMIN) {
        return {
          or: [
            { uploadedBy: { equals: user.id } },
            { institute: { equals: user.instituteId } },
          ],
        };
      }
      
      // Support staff can see media they uploaded or public media in their institute
      if (user.role === UserRole.SUPPORT_STAFF) {
        return {
          or: [
            { uploadedBy: { equals: user.id } },
            {
              and: [
                { institute: { equals: user.instituteId } },
                { isPublic: { equals: true } },
              ],
            },
          ],
        };
      }
      
      // Students can only see their own uploads or public media
      return {
        or: [
          { uploadedBy: { equals: user.id } },
          { isPublic: { equals: true } },
        ],
      };
    },
    
    // All authenticated users can upload media
    create: ({ req: { user } }) => {
      return !!user;
    },
    
    // Users can update their own uploads, admins can update institute media
    update: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      if (user.role === UserRole.INSTITUTE_ADMIN) {
        return {
          or: [
            { uploadedBy: { equals: user.id } },
            { institute: { equals: user.instituteId } },
          ],
        };
      }
      
      // Others can only update their own uploads
      return {
        uploadedBy: {
          equals: user.id,
        },
      };
    },
    
    // Users can delete their own uploads, admins can delete institute media
    delete: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      if (user.role === UserRole.INSTITUTE_ADMIN) {
        return {
          or: [
            { uploadedBy: { equals: user.id } },
            { institute: { equals: user.instituteId } },
          ],
        };
      }
      
      // Others can only delete their own uploads
      return {
        uploadedBy: {
          equals: user.id,
        },
      };
    },
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      required: false,
      admin: {
        description: 'Alternative text for accessibility',
      },
    },
    {
      name: 'caption',
      type: 'text',
      required: false,
    },
    {
      name: 'uploadedBy',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: false,
      admin: {
        description: 'Institute this media belongs to',
      },
    },
    {
      name: 'isPublic',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Make this file publicly accessible within the institute',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for organizing media files',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        // Set uploadedBy to current user on create
        if (operation === 'create' && req.user) {
          data.uploadedBy = req.user.id;
          
          // Auto-assign institute for non-super admins
          if (req.user.role !== UserRole.SUPER_ADMIN && req.user.instituteId) {
            data.institute = req.user.instituteId;
          }
        }
        
        return data;
      },
    ],
  },
};

export default Media;
