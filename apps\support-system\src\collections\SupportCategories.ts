import { CollectionConfig } from 'payload/types';
import { hasPermission, filterByAccess } from '../lib/payload-auth';

const SupportCategories: CollectionConfig = {
  slug: 'support-categories',
  labels: {
    singular: 'Support Category',
    plural: 'Support Categories',
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'description', 'responseTimeHours', 'resolutionTimeHours', 'isActive'],
    group: 'Support System',
  },
  access: {
    create: ({ req: { user } }) => hasPermission(user, 'create', 'support-categories'),
    read: ({ req: { user } }) => {
      if (!hasPermission(user, 'read', 'support-categories')) return false;
      return filterByAccess(user, {}, 'support-categories');
    },
    update: ({ req: { user } }) => {
      if (!hasPermission(user, 'update', 'support-categories')) return false;
      return filterByAccess(user, {}, 'support-categories');
    },
    delete: ({ req: { user } }) => {
      if (!hasPermission(user, 'delete', 'support-categories')) return false;
      return filterByAccess(user, {}, 'support-categories');
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      label: 'Category Name',
      required: true,
      maxLength: 100,
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
    },
    {
      name: 'color',
      type: 'text',
      label: 'Color',
      admin: {
        description: 'Hex color code (e.g., #FF5733)',
        placeholder: '#FF5733',
      },
      validate: (val) => {
        if (val && !/^#[0-9A-F]{6}$/i.test(val)) {
          return 'Please enter a valid hex color code (e.g., #FF5733)';
        }
        return true;
      },
    },
    {
      name: 'icon',
      type: 'text',
      label: 'Icon',
      admin: {
        description: 'Icon identifier (e.g., bug, question, gear)',
        placeholder: 'bug',
      },
    },
    // SLA Configuration Group
    {
      name: 'slaConfig',
      type: 'group',
      label: 'SLA Configuration',
      fields: [
        {
          name: 'responseTimeHours',
          type: 'number',
          label: 'Response Time (Hours)',
          required: true,
          defaultValue: 24,
          min: 1,
          max: 168, // 1 week
          admin: {
            description: 'Time limit for first response in hours',
          },
        },
        {
          name: 'resolutionTimeHours',
          type: 'number',
          label: 'Resolution Time (Hours)',
          required: true,
          defaultValue: 72,
          min: 1,
          max: 720, // 30 days
          admin: {
            description: 'Time limit for ticket resolution in hours',
          },
        },
      ],
    },
    // Routing Rules Group
    {
      name: 'routingRules',
      type: 'group',
      label: 'Routing Rules',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN' || user?.role === 'INSTITUTE_ADMIN',
      },
      fields: [
        {
          name: 'autoAssignTo',
          type: 'relationship',
          label: 'Auto Assign To',
          relationTo: 'users',
          admin: {
            description: 'Automatically assign new tickets to this user',
          },
          filterOptions: ({ user }) => {
            if (!user?.instituteId) return false;
            return {
              instituteId: { equals: user.instituteId },
              role: { in: ['INSTITUTE_ADMIN', 'SUPPORT_STAFF'] },
              isActive: { equals: true },
            };
          },
        },
        {
          name: 'escalationRules',
          type: 'json',
          label: 'Escalation Rules',
          admin: {
            description: 'JSON configuration for automatic escalation rules',
          },
        },
      ],
    },
    // Status and Ordering
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active',
      defaultValue: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'sortOrder',
      type: 'number',
      label: 'Sort Order',
      defaultValue: 0,
      admin: {
        position: 'sidebar',
        description: 'Lower numbers appear first',
      },
    },
    // Hidden fields for multi-tenancy
    {
      name: 'instituteId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.instituteId,
        ],
      },
    },
    {
      name: 'createdBy',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.id,
        ],
      },
    },
  ],
  hooks: {
    beforeValidate: [
      ({ operation, data, req }) => {
        // Ensure unique name per institute
        if (operation === 'create' || operation === 'update') {
          data.instituteId = req.user?.instituteId;
        }
        return data;
      },
    ],
  },
  timestamps: true,
};

export default SupportCategories;
