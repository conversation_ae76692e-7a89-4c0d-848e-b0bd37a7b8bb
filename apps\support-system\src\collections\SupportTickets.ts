import { CollectionConfig } from 'payload/types';
import { hasPermission, filterByAccess } from '../lib/payload-auth';

const SupportTickets: CollectionConfig = {
  slug: 'support-tickets',
  labels: {
    singular: 'Support Ticket',
    plural: 'Support Tickets',
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['ticketNumber', 'title', 'status', 'priority', 'assignedTo', 'createdAt'],
    group: 'Support System',
  },
  access: {
    create: ({ req: { user } }) => hasPermission(user, 'create', 'support-tickets'),
    read: ({ req: { user } }) => {
      if (!hasPermission(user, 'read', 'support-tickets')) return false;
      return filterByAccess(user, {}, 'support-tickets');
    },
    update: ({ req: { user } }) => {
      if (!hasPermission(user, 'update', 'support-tickets')) return false;
      return filterByAccess(user, {}, 'support-tickets');
    },
    delete: ({ req: { user } }) => {
      if (!hasPermission(user, 'delete', 'support-tickets')) return false;
      return filterByAccess(user, {}, 'support-tickets');
    },
  },
  fields: [
    {
      name: 'ticketNumber',
      type: 'text',
      label: 'Ticket Number',
      unique: true,
      admin: {
        readOnly: true,
        position: 'sidebar',
      },
      hooks: {
        beforeChange: [
          ({ operation, data, req }) => {
            if (operation === 'create') {
              // Generate ticket number: INST-YYYY-NNNNNN
              const year = new Date().getFullYear();
              const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
              const instituteCode = req.user?.institute?.slug?.toUpperCase() || 'INST';
              return `${instituteCode}-${year}-${random}`;
            }
            return data.ticketNumber;
          },
        ],
      },
    },
    {
      name: 'title',
      type: 'text',
      label: 'Title',
      required: true,
      maxLength: 255,
    },
    {
      name: 'description',
      type: 'richText',
      label: 'Description',
      required: true,
    },
    {
      name: 'status',
      type: 'select',
      label: 'Status',
      required: true,
      defaultValue: 'OPEN',
      options: [
        { label: 'Open', value: 'OPEN' },
        { label: 'In Progress', value: 'IN_PROGRESS' },
        { label: 'Pending Customer', value: 'PENDING_CUSTOMER' },
        { label: 'Pending Vendor', value: 'PENDING_VENDOR' },
        { label: 'Resolved', value: 'RESOLVED' },
        { label: 'Closed', value: 'CLOSED' },
        { label: 'Cancelled', value: 'CANCELLED' },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'priority',
      type: 'select',
      label: 'Priority',
      required: true,
      defaultValue: 'MEDIUM',
      options: [
        { label: 'Low', value: 'LOW' },
        { label: 'Medium', value: 'MEDIUM' },
        { label: 'High', value: 'HIGH' },
        { label: 'Urgent', value: 'URGENT' },
        { label: 'Critical', value: 'CRITICAL' },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'type',
      type: 'select',
      label: 'Type',
      required: true,
      defaultValue: 'INCIDENT',
      options: [
        { label: 'Incident', value: 'INCIDENT' },
        { label: 'Problem', value: 'PROBLEM' },
        { label: 'Change Request', value: 'CHANGE_REQUEST' },
        { label: 'Service Request', value: 'SERVICE_REQUEST' },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'category',
      type: 'relationship',
      label: 'Category',
      relationTo: 'support-categories',
      admin: {
        position: 'sidebar',
      },
      filterOptions: ({ user }) => {
        if (!user?.instituteId) return false;
        return {
          instituteId: { equals: user.instituteId },
          isActive: { equals: true },
        };
      },
    },
    {
      name: 'tags',
      type: 'array',
      label: 'Tags',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        components: {
          RowLabel: ({ data }) => data?.tag || 'Tag',
        },
      },
    },
    {
      name: 'assignedTo',
      type: 'relationship',
      label: 'Assigned To',
      relationTo: 'users',
      admin: {
        position: 'sidebar',
      },
      filterOptions: ({ user }) => {
        if (!user?.instituteId) return false;
        return {
          instituteId: { equals: user.instituteId },
          role: { in: ['INSTITUTE_ADMIN', 'SUPPORT_STAFF'] },
          isActive: { equals: true },
        };
      },
    },
    {
      name: 'assignedTeam',
      type: 'text',
      label: 'Assigned Team',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'template',
      type: 'relationship',
      label: 'Template Used',
      relationTo: 'ticket-templates',
      admin: {
        readOnly: true,
        position: 'sidebar',
      },
    },
    // Customer Information Group
    {
      name: 'customerInfo',
      type: 'group',
      label: 'Customer Information',
      fields: [
        {
          name: 'customerName',
          type: 'text',
          label: 'Customer Name',
        },
        {
          name: 'customerEmail',
          type: 'email',
          label: 'Customer Email',
        },
        {
          name: 'customerPhone',
          type: 'text',
          label: 'Customer Phone',
        },
      ],
      admin: {
        condition: (data) => data.createdBy !== data.customerEmail,
      },
    },
    // SLA Tracking Group
    {
      name: 'slaTracking',
      type: 'group',
      label: 'SLA Tracking',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN' || user?.role === 'INSTITUTE_ADMIN',
      },
      fields: [
        {
          name: 'slaResponseDue',
          type: 'date',
          label: 'Response Due',
          admin: {
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
        {
          name: 'slaResolutionDue',
          type: 'date',
          label: 'Resolution Due',
          admin: {
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
        {
          name: 'firstResponseAt',
          type: 'date',
          label: 'First Response At',
          admin: {
            readOnly: true,
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
        {
          name: 'resolvedAt',
          type: 'date',
          label: 'Resolved At',
          admin: {
            readOnly: true,
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
        {
          name: 'closedAt',
          type: 'date',
          label: 'Closed At',
          admin: {
            readOnly: true,
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
      ],
    },
    // Source Information Group
    {
      name: 'sourceInfo',
      type: 'group',
      label: 'Source Information',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN' || user?.role === 'INSTITUTE_ADMIN',
      },
      fields: [
        {
          name: 'source',
          type: 'select',
          label: 'Source',
          defaultValue: 'WEB',
          options: [
            { label: 'Web Portal', value: 'WEB' },
            { label: 'Email', value: 'EMAIL' },
            { label: 'Phone', value: 'PHONE' },
            { label: 'Chat', value: 'CHAT' },
            { label: 'API', value: 'API' },
          ],
        },
        {
          name: 'sourceReference',
          type: 'text',
          label: 'Source Reference',
          admin: {
            description: 'External reference ID (email message ID, etc.)',
          },
        },
      ],
    },
    // AI Analysis (for future use)
    {
      name: 'aiAnalysis',
      type: 'json',
      label: 'AI Analysis',
      admin: {
        readOnly: true,
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN',
      },
    },
    // Custom Fields
    {
      name: 'customFields',
      type: 'json',
      label: 'Custom Fields',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN' || user?.role === 'INSTITUTE_ADMIN',
      },
    },
    // Hidden fields for multi-tenancy
    {
      name: 'instituteId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.instituteId,
        ],
      },
    },
    {
      name: 'branchId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.branchId,
        ],
      },
    },
    {
      name: 'createdBy',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.id,
        ],
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ operation, data, req, originalDoc }) => {
        // Auto-set SLA due dates based on category
        if (operation === 'create' && data.categoryId) {
          const now = new Date();
          // These would be fetched from the category in a real implementation
          const responseHours = 24; // Default or from category
          const resolutionHours = 72; // Default or from category

          data.slaResponseDue = new Date(now.getTime() + responseHours * 60 * 60 * 1000);
          data.slaResolutionDue = new Date(now.getTime() + resolutionHours * 60 * 60 * 1000);
        }

        // Track first response time
        if (operation === 'update' && !originalDoc?.firstResponseAt) {
          if (data.status === 'IN_PROGRESS' || data.assignedTo) {
            data.firstResponseAt = new Date();
          }
        }

        // Auto-set timestamps based on status changes
        if (operation === 'update') {
          const now = new Date();

          if (data.status === 'RESOLVED' && !originalDoc?.resolvedAt) {
            data.resolvedAt = now;
          }

          if (data.status === 'CLOSED' && !originalDoc?.closedAt) {
            data.closedAt = now;
          }
        }

        return data;
      },
    ],
    afterChange: [
      ({ operation, doc, req, previousDoc }) => {
        // Log ticket creation
        if (operation === 'create') {
          console.log(`Created ticket ${doc.ticketNumber} for institute ${doc.instituteId}`);
        }

        // Log status changes
        if (operation === 'update' && previousDoc?.status !== doc.status) {
          console.log(`Ticket ${doc.ticketNumber} status changed: ${previousDoc?.status} → ${doc.status}`);
        }

        // Trigger analytics update for completed tickets
        if (operation === 'update' && (doc.status === 'RESOLVED' || doc.status === 'CLOSED')) {
          console.log(`Ticket ${doc.ticketNumber} completed - analytics update triggered`);
        }
      },
    ],
  },
  timestamps: true,
};

export default SupportTickets;
