import { CollectionConfig } from 'payload/types';
import { hasPermission, filterByAccess } from '../lib/payload-auth';

const TicketAnalytics: CollectionConfig = {
  slug: 'ticket-analytics',
  labels: {
    singular: 'Ticket Analytics',
    plural: 'Ticket Analytics',
  },
  admin: {
    useAsTitle: 'ticket',
    defaultColumns: ['ticket', 'responseTime', 'resolutionTime', 'satisfactionScore', 'createdAt'],
    group: 'Support System',
    description: 'Analytics and performance metrics for support tickets',
  },
  access: {
    create: ({ req: { user } }) => hasPermission(user, 'create', 'ticket-analytics'),
    read: ({ req: { user } }) => {
      if (!hasPermission(user, 'read', 'ticket-analytics')) return false;
      return filterByAccess(user, {}, 'ticket-analytics');
    },
    update: ({ req: { user } }) => {
      if (!hasPermission(user, 'update', 'ticket-analytics')) return false;
      return filterByAccess(user, {}, 'ticket-analytics');
    },
    delete: ({ req: { user } }) => {
      if (!hasPermission(user, 'delete', 'ticket-analytics')) return false;
      return filterByAccess(user, {}, 'ticket-analytics');
    },
  },
  fields: [
    {
      name: 'ticket',
      type: 'relationship',
      label: 'Support Ticket',
      relationTo: 'support-tickets',
      required: true,
      unique: true,
      admin: {
        position: 'sidebar',
      },
      filterOptions: ({ user }) => {
        if (!user?.instituteId) return false;
        return {
          instituteId: { equals: user.instituteId },
        };
      },
    },
    // Response Time Metrics
    {
      name: 'responseMetrics',
      type: 'group',
      label: 'Response Time Metrics',
      fields: [
        {
          name: 'firstResponseTime',
          type: 'number',
          label: 'First Response Time (minutes)',
          admin: {
            description: 'Time from ticket creation to first agent response',
          },
        },
        {
          name: 'averageResponseTime',
          type: 'number',
          label: 'Average Response Time (minutes)',
          admin: {
            description: 'Average time between customer messages and agent responses',
          },
        },
        {
          name: 'slaResponseMet',
          type: 'checkbox',
          label: 'SLA Response Time Met',
          admin: {
            description: 'Whether the response SLA was met',
          },
        },
        {
          name: 'responseTimeBreaches',
          type: 'number',
          label: 'Response Time Breaches',
          defaultValue: 0,
          admin: {
            description: 'Number of times response SLA was breached',
          },
        },
      ],
    },
    // Resolution Time Metrics
    {
      name: 'resolutionMetrics',
      type: 'group',
      label: 'Resolution Time Metrics',
      fields: [
        {
          name: 'resolutionTime',
          type: 'number',
          label: 'Resolution Time (minutes)',
          admin: {
            description: 'Total time from ticket creation to resolution',
          },
        },
        {
          name: 'activeWorkTime',
          type: 'number',
          label: 'Active Work Time (minutes)',
          admin: {
            description: 'Time spent actively working on the ticket',
          },
        },
        {
          name: 'slaResolutionMet',
          type: 'checkbox',
          label: 'SLA Resolution Time Met',
          admin: {
            description: 'Whether the resolution SLA was met',
          },
        },
        {
          name: 'escalationCount',
          type: 'number',
          label: 'Escalation Count',
          defaultValue: 0,
          admin: {
            description: 'Number of times the ticket was escalated',
          },
        },
        {
          name: 'reopenCount',
          type: 'number',
          label: 'Reopen Count',
          defaultValue: 0,
          admin: {
            description: 'Number of times the ticket was reopened',
          },
        },
      ],
    },
    // Customer Satisfaction Metrics
    {
      name: 'satisfactionMetrics',
      type: 'group',
      label: 'Customer Satisfaction Metrics',
      fields: [
        {
          name: 'satisfactionScore',
          type: 'number',
          label: 'Satisfaction Score',
          min: 1,
          max: 5,
          admin: {
            description: 'Customer satisfaction rating (1-5 scale)',
          },
        },
        {
          name: 'satisfactionFeedback',
          type: 'textarea',
          label: 'Satisfaction Feedback',
          admin: {
            description: 'Customer feedback comments',
          },
        },
        {
          name: 'npsScore',
          type: 'number',
          label: 'NPS Score',
          min: 0,
          max: 10,
          admin: {
            description: 'Net Promoter Score (0-10 scale)',
          },
        },
        {
          name: 'surveyCompleted',
          type: 'checkbox',
          label: 'Survey Completed',
          admin: {
            description: 'Whether customer completed satisfaction survey',
          },
        },
      ],
    },
    // Agent Performance Metrics
    {
      name: 'agentMetrics',
      type: 'group',
      label: 'Agent Performance Metrics',
      fields: [
        {
          name: 'assignedAgents',
          type: 'array',
          label: 'Assigned Agents',
          fields: [
            {
              name: 'agent',
              type: 'relationship',
              relationTo: 'users',
              required: true,
            },
            {
              name: 'timeSpent',
              type: 'number',
              label: 'Time Spent (minutes)',
            },
            {
              name: 'assignedAt',
              type: 'date',
              admin: {
                date: {
                  pickerAppearance: 'dayAndTime',
                },
              },
            },
            {
              name: 'unassignedAt',
              type: 'date',
              admin: {
                date: {
                  pickerAppearance: 'dayAndTime',
                },
              },
            },
          ],
        },
        {
          name: 'totalAgentSwitches',
          type: 'number',
          label: 'Total Agent Switches',
          defaultValue: 0,
          admin: {
            description: 'Number of times ticket was reassigned',
          },
        },
      ],
    },
    // Communication Metrics
    {
      name: 'communicationMetrics',
      type: 'group',
      label: 'Communication Metrics',
      fields: [
        {
          name: 'totalMessages',
          type: 'number',
          label: 'Total Messages',
          defaultValue: 0,
          admin: {
            description: 'Total number of messages in the ticket',
          },
        },
        {
          name: 'customerMessages',
          type: 'number',
          label: 'Customer Messages',
          defaultValue: 0,
          admin: {
            description: 'Number of messages from customer',
          },
        },
        {
          name: 'agentMessages',
          type: 'number',
          label: 'Agent Messages',
          defaultValue: 0,
          admin: {
            description: 'Number of messages from agents',
          },
        },
        {
          name: 'internalNotes',
          type: 'number',
          label: 'Internal Notes',
          defaultValue: 0,
          admin: {
            description: 'Number of internal notes added',
          },
        },
        {
          name: 'attachmentCount',
          type: 'number',
          label: 'Attachment Count',
          defaultValue: 0,
          admin: {
            description: 'Number of files attached to the ticket',
          },
        },
      ],
    },
    // AI Analysis Metrics
    {
      name: 'aiMetrics',
      type: 'group',
      label: 'AI Analysis Metrics',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN',
      },
      fields: [
        {
          name: 'sentimentAnalysis',
          type: 'json',
          label: 'Sentiment Analysis',
          admin: {
            description: 'AI-generated sentiment analysis results',
          },
        },
        {
          name: 'complexityScore',
          type: 'number',
          label: 'Complexity Score',
          min: 0,
          max: 10,
          admin: {
            description: 'AI-calculated ticket complexity (0-10)',
          },
        },
        {
          name: 'urgencyScore',
          type: 'number',
          label: 'Urgency Score',
          min: 0,
          max: 10,
          admin: {
            description: 'AI-calculated urgency score (0-10)',
          },
        },
        {
          name: 'categoryPredictions',
          type: 'array',
          label: 'Category Predictions',
          fields: [
            {
              name: 'category',
              type: 'text',
              required: true,
            },
            {
              name: 'confidence',
              type: 'number',
              min: 0,
              max: 1,
            },
          ],
          admin: {
            description: 'AI-predicted categories with confidence scores',
          },
        },
        {
          name: 'resolutionPrediction',
          type: 'json',
          label: 'Resolution Prediction',
          admin: {
            description: 'AI prediction for resolution time and approach',
          },
        },
      ],
    },
    // Business Impact Metrics
    {
      name: 'businessMetrics',
      type: 'group',
      label: 'Business Impact Metrics',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN' || user?.role === 'INSTITUTE_ADMIN',
      },
      fields: [
        {
          name: 'impactLevel',
          type: 'select',
          label: 'Business Impact Level',
          options: [
            { label: 'Low', value: 'LOW' },
            { label: 'Medium', value: 'MEDIUM' },
            { label: 'High', value: 'HIGH' },
            { label: 'Critical', value: 'CRITICAL' },
          ],
        },
        {
          name: 'affectedUsers',
          type: 'number',
          label: 'Affected Users',
          admin: {
            description: 'Number of users affected by this issue',
          },
        },
        {
          name: 'estimatedCost',
          type: 'number',
          label: 'Estimated Cost Impact',
          admin: {
            description: 'Estimated cost impact in currency units',
          },
        },
        {
          name: 'revenueImpact',
          type: 'number',
          label: 'Revenue Impact',
          admin: {
            description: 'Estimated revenue impact',
          },
        },
      ],
    },
    // Hidden fields for multi-tenancy
    {
      name: 'instituteId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.instituteId,
        ],
      },
    },
    {
      name: 'branchId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.branchId,
        ],
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ operation, data, req }) => {
        // Auto-calculate derived metrics
        if (data.communicationMetrics) {
          const { customerMessages = 0, agentMessages = 0, internalNotes = 0 } = data.communicationMetrics;
          data.communicationMetrics.totalMessages = customerMessages + agentMessages + internalNotes;
        }
        
        return data;
      },
    ],
    afterChange: [
      ({ operation, doc, req }) => {
        // Trigger analytics aggregation updates
        if (operation === 'create' || operation === 'update') {
          // This would trigger background jobs to update aggregate analytics
          // Implementation would depend on your analytics requirements
        }
      },
    ],
  },
  timestamps: true,
};

export default TicketAnalytics;
