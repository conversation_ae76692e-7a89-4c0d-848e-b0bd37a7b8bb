import { CollectionConfig } from 'payload/types';
import { hasPermission, filterByAccess } from '../lib/payload-auth';

const TicketAttachments: CollectionConfig = {
  slug: 'ticket-attachments',
  labels: {
    singular: 'Ticket Attachment',
    plural: 'Ticket Attachments',
  },
  admin: {
    useAsTitle: 'originalFilename',
    defaultColumns: ['originalFilename', 'ticket', 'message', 'fileSize', 'uploadedBy', 'createdAt'],
    group: 'Support System',
    description: 'File attachments for support tickets and messages',
  },
  access: {
    create: ({ req: { user } }) => hasPermission(user, 'create', 'ticket-attachments'),
    read: ({ req: { user } }) => {
      if (!hasPermission(user, 'read', 'ticket-attachments')) return false;
      return filterByAccess(user, {}, 'ticket-attachments');
    },
    update: ({ req: { user } }) => {
      if (!hasPermission(user, 'update', 'ticket-attachments')) return false;
      return filterByAccess(user, {}, 'ticket-attachments');
    },
    delete: ({ req: { user } }) => {
      if (!hasPermission(user, 'delete', 'ticket-attachments')) return false;
      return filterByAccess(user, {}, 'ticket-attachments');
    },
  },
  upload: {
    staticURL: '/uploads',
    staticDir: 'uploads',
    adminThumbnail: 'thumbnail',
    mimeTypes: [
      'image/*',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv',
      'application/zip',
      'application/x-zip-compressed',
    ],
    imageSizes: [
      {
        name: 'thumbnail',
        width: 150,
        height: 150,
        position: 'centre',
      },
      {
        name: 'preview',
        width: 400,
        height: 300,
        position: 'centre',
      },
    ],
  },
  fields: [
    {
      name: 'ticket',
      type: 'relationship',
      label: 'Support Ticket',
      relationTo: 'support-tickets',
      required: true,
      admin: {
        position: 'sidebar',
      },
      filterOptions: ({ user }) => {
        if (!user?.instituteId) return false;
        return {
          instituteId: { equals: user.instituteId },
        };
      },
    },
    {
      name: 'message',
      type: 'relationship',
      label: 'Related Message',
      relationTo: 'ticket-messages',
      admin: {
        position: 'sidebar',
        description: 'Message this attachment belongs to (optional)',
      },
      filterOptions: ({ user }) => {
        if (!user?.instituteId) return false;
        return {
          instituteId: { equals: user.instituteId },
        };
      },
    },
    {
      name: 'originalFilename',
      type: 'text',
      label: 'Original Filename',
      required: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
      admin: {
        description: 'Optional description of the attachment',
      },
    },
    {
      name: 'fileInfo',
      type: 'group',
      label: 'File Information',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN' || user?.role === 'INSTITUTE_ADMIN',
      },
      fields: [
        {
          name: 'fileSize',
          type: 'number',
          label: 'File Size (bytes)',
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'mimeType',
          type: 'text',
          label: 'MIME Type',
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'fileHash',
          type: 'text',
          label: 'File Hash (SHA-256)',
          admin: {
            readOnly: true,
            description: 'Used for deduplication and integrity checking',
          },
        },
      ],
    },
    {
      name: 'uploadInfo',
      type: 'group',
      label: 'Upload Information',
      fields: [
        {
          name: 'uploadedBy',
          type: 'relationship',
          label: 'Uploaded By',
          relationTo: 'users',
          admin: {
            readOnly: true,
          },
          filterOptions: ({ user }) => {
            if (!user?.instituteId) return false;
            return {
              instituteId: { equals: user.instituteId },
              isActive: { equals: true },
            };
          },
        },
        {
          name: 'uploadSource',
          type: 'select',
          label: 'Upload Source',
          defaultValue: 'WEB',
          options: [
            { label: 'Web Portal', value: 'WEB' },
            { label: 'Email', value: 'EMAIL' },
            { label: 'API', value: 'API' },
            { label: 'Mobile App', value: 'MOBILE' },
          ],
          admin: {
            position: 'sidebar',
          },
        },
      ],
    },
    {
      name: 'accessControl',
      type: 'group',
      label: 'Access Control',
      fields: [
        {
          name: 'visibility',
          type: 'select',
          label: 'Visibility',
          defaultValue: 'PUBLIC',
          options: [
            { label: 'Public', value: 'PUBLIC' },
            { label: 'Internal', value: 'INTERNAL' },
            { label: 'Private', value: 'PRIVATE' },
          ],
          admin: {
            description: 'Who can access this attachment',
          },
        },
        {
          name: 'downloadCount',
          type: 'number',
          label: 'Download Count',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'lastDownloadedAt',
          type: 'date',
          label: 'Last Downloaded At',
          admin: {
            readOnly: true,
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
      ],
    },
    {
      name: 'securityInfo',
      type: 'group',
      label: 'Security Information',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN',
      },
      fields: [
        {
          name: 'virusScanStatus',
          type: 'select',
          label: 'Virus Scan Status',
          defaultValue: 'PENDING',
          options: [
            { label: 'Pending', value: 'PENDING' },
            { label: 'Clean', value: 'CLEAN' },
            { label: 'Infected', value: 'INFECTED' },
            { label: 'Error', value: 'ERROR' },
            { label: 'Skipped', value: 'SKIPPED' },
          ],
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'scanResults',
          type: 'json',
          label: 'Scan Results',
          admin: {
            readOnly: true,
            description: 'Detailed virus scan results',
          },
        },
        {
          name: 'quarantined',
          type: 'checkbox',
          label: 'Quarantined',
          defaultValue: false,
          admin: {
            readOnly: true,
          },
        },
      ],
    },
    // Hidden fields for multi-tenancy
    {
      name: 'instituteId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.instituteId,
        ],
      },
    },
    {
      name: 'branchId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.branchId,
        ],
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ operation, data, req }) => {
        // Auto-set upload information
        if (operation === 'create') {
          data.uploadInfo = {
            ...data.uploadInfo,
            uploadedBy: req.user?.id,
            uploadSource: data.uploadInfo?.uploadSource || 'WEB',
          };
          
          // Initialize security scan status
          data.securityInfo = {
            ...data.securityInfo,
            virusScanStatus: 'PENDING',
            quarantined: false,
          };
        }
        
        return data;
      },
    ],
    afterChange: [
      ({ operation, doc, req }) => {
        // Trigger virus scan for new uploads
        if (operation === 'create') {
          // This would trigger an async virus scan
          // Implementation would depend on your security requirements
        }
      },
    ],
    beforeDelete: [
      ({ doc }) => {
        // Clean up physical file when attachment is deleted
        // Implementation would depend on your file storage setup
      },
    ],
  },
  timestamps: true,
};

export default TicketAttachments;
