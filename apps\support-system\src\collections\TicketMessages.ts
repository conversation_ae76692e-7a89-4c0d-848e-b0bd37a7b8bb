import { CollectionConfig } from 'payload/types';
import { hasPermission, filterByAccess } from '../lib/payload-auth';

const TicketMessages: CollectionConfig = {
  slug: 'ticket-messages',
  labels: {
    singular: 'Ticket Message',
    plural: 'Ticket Messages',
  },
  admin: {
    useAsTitle: 'content',
    defaultColumns: ['ticket', 'messageType', 'authorName', 'visibility', 'createdAt'],
    group: 'Support System',
    description: 'Messages and conversations within support tickets',
  },
  access: {
    create: ({ req: { user } }) => hasPermission(user, 'create', 'ticket-messages'),
    read: ({ req: { user } }) => {
      if (!hasPermission(user, 'read', 'ticket-messages')) return false;
      return filterByAccess(user, {}, 'ticket-messages');
    },
    update: ({ req: { user } }) => {
      if (!hasPermission(user, 'update', 'ticket-messages')) return false;
      return filterByAccess(user, {}, 'ticket-messages');
    },
    delete: ({ req: { user } }) => {
      if (!hasPermission(user, 'delete', 'ticket-messages')) return false;
      return filterByAccess(user, {}, 'ticket-messages');
    },
  },
  fields: [
    {
      name: 'ticket',
      type: 'relationship',
      label: 'Support Ticket',
      relationTo: 'support-tickets',
      required: true,
      admin: {
        position: 'sidebar',
      },
      filterOptions: ({ user }) => {
        if (!user?.instituteId) return false;
        return {
          instituteId: { equals: user.instituteId },
        };
      },
    },
    {
      name: 'content',
      type: 'richText',
      label: 'Message Content',
      required: true,
      admin: {
        description: 'The main content of the message',
      },
    },
    {
      name: 'contentType',
      type: 'select',
      label: 'Content Type',
      defaultValue: 'text',
      options: [
        { label: 'Plain Text', value: 'text' },
        { label: 'HTML', value: 'html' },
        { label: 'Markdown', value: 'markdown' },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'messageType',
      type: 'select',
      label: 'Message Type',
      required: true,
      options: [
        { label: 'Customer Reply', value: 'CUSTOMER_REPLY' },
        { label: 'Agent Reply', value: 'AGENT_REPLY' },
        { label: 'Internal Note', value: 'INTERNAL_NOTE' },
        { label: 'System Message', value: 'SYSTEM_MESSAGE' },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'visibility',
      type: 'select',
      label: 'Visibility',
      defaultValue: 'PUBLIC',
      options: [
        { label: 'Public', value: 'PUBLIC' },
        { label: 'Internal', value: 'INTERNAL' },
        { label: 'Private', value: 'PRIVATE' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Who can see this message',
      },
    },
    // Author Information Group
    {
      name: 'authorInfo',
      type: 'group',
      label: 'Author Information',
      fields: [
        {
          name: 'author',
          type: 'relationship',
          label: 'Author (User)',
          relationTo: 'users',
          admin: {
            description: 'Internal user who wrote this message',
          },
          filterOptions: ({ user }) => {
            if (!user?.instituteId) return false;
            return {
              instituteId: { equals: user.instituteId },
              isActive: { equals: true },
            };
          },
        },
        {
          name: 'authorName',
          type: 'text',
          label: 'Author Name',
          admin: {
            description: 'Name for external/anonymous messages',
          },
        },
        {
          name: 'authorEmail',
          type: 'email',
          label: 'Author Email',
          admin: {
            description: 'Email for external/anonymous messages',
          },
        },
      ],
    },
    // Threading Group
    {
      name: 'threading',
      type: 'group',
      label: 'Message Threading',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN' || user?.role === 'INSTITUTE_ADMIN',
      },
      fields: [
        {
          name: 'parentMessage',
          type: 'relationship',
          label: 'Parent Message',
          relationTo: 'ticket-messages',
          admin: {
            description: 'Reply to this message',
          },
        },
        {
          name: 'threadPosition',
          type: 'number',
          label: 'Thread Position',
          defaultValue: 0,
          admin: {
            description: 'Position in the conversation thread',
          },
        },
      ],
    },
    // AI Analysis Group
    {
      name: 'aiAnalysis',
      type: 'group',
      label: 'AI Analysis',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN',
      },
      fields: [
        {
          name: 'sentiment',
          type: 'select',
          label: 'Sentiment',
          options: [
            { label: 'Positive', value: 'POSITIVE' },
            { label: 'Neutral', value: 'NEUTRAL' },
            { label: 'Negative', value: 'NEGATIVE' },
          ],
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'urgencyScore',
          type: 'number',
          label: 'Urgency Score',
          min: 0,
          max: 10,
          admin: {
            readOnly: true,
            description: 'AI-calculated urgency score (0-10)',
          },
        },
        {
          name: 'categories',
          type: 'array',
          label: 'AI Categories',
          fields: [
            {
              name: 'category',
              type: 'text',
              required: true,
            },
          ],
          admin: {
            readOnly: true,
            description: 'AI-detected categories',
          },
        },
      ],
    },
    // Delivery Information Group
    {
      name: 'deliveryInfo',
      type: 'group',
      label: 'Delivery Information',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN' || user?.role === 'INSTITUTE_ADMIN',
      },
      fields: [
        {
          name: 'deliveryStatus',
          type: 'select',
          label: 'Delivery Status',
          defaultValue: 'DELIVERED',
          options: [
            { label: 'Pending', value: 'PENDING' },
            { label: 'Delivered', value: 'DELIVERED' },
            { label: 'Failed', value: 'FAILED' },
            { label: 'Bounced', value: 'BOUNCED' },
          ],
        },
        {
          name: 'deliveredAt',
          type: 'date',
          label: 'Delivered At',
          admin: {
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
        {
          name: 'readAt',
          type: 'date',
          label: 'Read At',
          admin: {
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
      ],
    },
    // Hidden fields for multi-tenancy and tracking
    {
      name: 'instituteId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.instituteId,
        ],
      },
    },
    {
      name: 'branchId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.branchId,
        ],
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ operation, data, req }) => {
        // Auto-set author information for authenticated users
        if (operation === 'create' && req.user && !data.authorInfo?.author) {
          data.authorInfo = {
            ...data.authorInfo,
            author: req.user.id,
            authorName: req.user.name,
            authorEmail: req.user.email,
          };
        }
        
        // Auto-set delivery status and timestamp
        if (operation === 'create') {
          data.deliveryInfo = {
            ...data.deliveryInfo,
            deliveryStatus: 'DELIVERED',
            deliveredAt: new Date(),
          };
        }
        
        return data;
      },
    ],
    afterChange: [
      ({ operation, doc, req }) => {
        // Update ticket's first response time if this is the first agent reply
        if (operation === 'create' && doc.messageType === 'AGENT_REPLY') {
          // This would trigger an update to the parent ticket's firstResponseAt
          // Implementation would depend on your specific requirements
        }
      },
    ],
  },
  timestamps: true,
};

export default TicketMessages;
