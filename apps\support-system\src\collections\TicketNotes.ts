import { CollectionConfig } from 'payload/types';
import { hasPermission, filterByAccess } from '../lib/payload-auth';

const TicketNotes: CollectionConfig = {
  slug: 'ticket-notes',
  labels: {
    singular: 'Ticket Note',
    plural: 'Ticket Notes',
  },
  admin: {
    useAsTitle: 'content',
    defaultColumns: ['ticket', 'noteType', 'author', 'visibility', 'isPinned', 'createdAt'],
    group: 'Support System',
    description: 'Internal notes and documentation for support tickets',
  },
  access: {
    create: ({ req: { user } }) => hasPermission(user, 'create', 'ticket-notes'),
    read: ({ req: { user } }) => {
      if (!hasPermission(user, 'read', 'ticket-notes')) return false;
      return filterByAccess(user, {}, 'ticket-notes');
    },
    update: ({ req: { user } }) => {
      if (!hasPermission(user, 'update', 'ticket-notes')) return false;
      return filterByAccess(user, {}, 'ticket-notes');
    },
    delete: ({ req: { user } }) => {
      if (!hasPermission(user, 'delete', 'ticket-notes')) return false;
      return filterByAccess(user, {}, 'ticket-notes');
    },
  },
  fields: [
    {
      name: 'ticket',
      type: 'relationship',
      label: 'Support Ticket',
      relationTo: 'support-tickets',
      required: true,
      admin: {
        position: 'sidebar',
      },
      filterOptions: ({ user }) => {
        if (!user?.instituteId) return false;
        return {
          instituteId: { equals: user.instituteId },
        };
      },
    },
    {
      name: 'content',
      type: 'richText',
      label: 'Note Content',
      required: true,
      admin: {
        description: 'Internal note content - not visible to customers',
      },
    },
    {
      name: 'noteType',
      type: 'select',
      label: 'Note Type',
      defaultValue: 'GENERAL',
      options: [
        { label: 'General', value: 'GENERAL' },
        { label: 'Escalation', value: 'ESCALATION' },
        { label: 'Resolution', value: 'RESOLUTION' },
        { label: 'Follow-up', value: 'FOLLOWUP' },
        { label: 'Investigation', value: 'INVESTIGATION' },
        { label: 'Customer Contact', value: 'CUSTOMER_CONTACT' },
        { label: 'Technical Details', value: 'TECHNICAL' },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'author',
      type: 'relationship',
      label: 'Author',
      relationTo: 'users',
      required: true,
      admin: {
        readOnly: true,
        position: 'sidebar',
      },
      filterOptions: ({ user }) => {
        if (!user?.instituteId) return false;
        return {
          instituteId: { equals: user.instituteId },
          isActive: { equals: true },
        };
      },
    },
    {
      name: 'visibility',
      type: 'select',
      label: 'Visibility',
      defaultValue: 'TEAM',
      options: [
        { label: 'Team', value: 'TEAM' },
        { label: 'Department', value: 'DEPARTMENT' },
        { label: 'Admin Only', value: 'ADMIN_ONLY' },
        { label: 'Personal', value: 'PERSONAL' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Who can see this note within the organization',
      },
    },
    {
      name: 'importance',
      type: 'select',
      label: 'Importance',
      defaultValue: 'NORMAL',
      options: [
        { label: 'Low', value: 'LOW' },
        { label: 'Normal', value: 'NORMAL' },
        { label: 'High', value: 'HIGH' },
        { label: 'Critical', value: 'CRITICAL' },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'isPinned',
      type: 'checkbox',
      label: 'Pinned',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        description: 'Pin this note to the top of the ticket',
      },
    },
    {
      name: 'tags',
      type: 'array',
      label: 'Tags',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        components: {
          RowLabel: ({ data }) => data?.tag || 'Tag',
        },
        description: 'Tags for organizing and searching notes',
      },
    },
    {
      name: 'followUpInfo',
      type: 'group',
      label: 'Follow-up Information',
      admin: {
        condition: (data) => data.noteType === 'FOLLOWUP',
      },
      fields: [
        {
          name: 'followUpDate',
          type: 'date',
          label: 'Follow-up Date',
          admin: {
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
        {
          name: 'assignedTo',
          type: 'relationship',
          label: 'Assigned To',
          relationTo: 'users',
          filterOptions: ({ user }) => {
            if (!user?.instituteId) return false;
            return {
              instituteId: { equals: user.instituteId },
              role: { in: ['INSTITUTE_ADMIN', 'SUPPORT_STAFF'] },
              isActive: { equals: true },
            };
          },
        },
        {
          name: 'completed',
          type: 'checkbox',
          label: 'Completed',
          defaultValue: false,
        },
        {
          name: 'completedAt',
          type: 'date',
          label: 'Completed At',
          admin: {
            readOnly: true,
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
      ],
    },
    {
      name: 'escalationInfo',
      type: 'group',
      label: 'Escalation Information',
      admin: {
        condition: (data) => data.noteType === 'ESCALATION',
      },
      fields: [
        {
          name: 'escalatedTo',
          type: 'relationship',
          label: 'Escalated To',
          relationTo: 'users',
          filterOptions: ({ user }) => {
            if (!user?.instituteId) return false;
            return {
              instituteId: { equals: user.instituteId },
              role: { in: ['INSTITUTE_ADMIN', 'SUPPORT_STAFF'] },
              isActive: { equals: true },
            };
          },
        },
        {
          name: 'escalationReason',
          type: 'select',
          label: 'Escalation Reason',
          options: [
            { label: 'SLA Breach', value: 'SLA_BREACH' },
            { label: 'Technical Complexity', value: 'TECHNICAL_COMPLEXITY' },
            { label: 'Customer Request', value: 'CUSTOMER_REQUEST' },
            { label: 'Policy Exception', value: 'POLICY_EXCEPTION' },
            { label: 'Management Review', value: 'MANAGEMENT_REVIEW' },
          ],
        },
        {
          name: 'previousAssignee',
          type: 'relationship',
          label: 'Previous Assignee',
          relationTo: 'users',
          admin: {
            readOnly: true,
          },
        },
      ],
    },
    {
      name: 'resolutionInfo',
      type: 'group',
      label: 'Resolution Information',
      admin: {
        condition: (data) => data.noteType === 'RESOLUTION',
      },
      fields: [
        {
          name: 'resolutionType',
          type: 'select',
          label: 'Resolution Type',
          options: [
            { label: 'Fixed', value: 'FIXED' },
            { label: 'Workaround', value: 'WORKAROUND' },
            { label: 'Duplicate', value: 'DUPLICATE' },
            { label: 'Cannot Reproduce', value: 'CANNOT_REPRODUCE' },
            { label: 'By Design', value: 'BY_DESIGN' },
            { label: 'Customer Error', value: 'CUSTOMER_ERROR' },
          ],
        },
        {
          name: 'timeSpent',
          type: 'number',
          label: 'Time Spent (minutes)',
          admin: {
            description: 'Time spent on resolution',
          },
        },
        {
          name: 'relatedTickets',
          type: 'array',
          label: 'Related Tickets',
          fields: [
            {
              name: 'ticket',
              type: 'relationship',
              relationTo: 'support-tickets',
              required: true,
            },
          ],
        },
      ],
    },
    // Hidden fields for multi-tenancy
    {
      name: 'instituteId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.instituteId,
        ],
      },
    },
    {
      name: 'branchId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.branchId,
        ],
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ operation, data, req }) => {
        // Auto-set author for new notes
        if (operation === 'create') {
          data.author = req.user?.id;
        }
        
        // Auto-set completion timestamp for follow-up notes
        if (data.followUpInfo?.completed && !data.followUpInfo?.completedAt) {
          data.followUpInfo.completedAt = new Date();
        }
        
        return data;
      },
    ],
    afterChange: [
      ({ operation, doc, req }) => {
        // Send notifications for high importance or escalation notes
        if (operation === 'create' && (doc.importance === 'HIGH' || doc.importance === 'CRITICAL' || doc.noteType === 'ESCALATION')) {
          // This would trigger notification logic
          // Implementation would depend on your notification system
        }
      },
    ],
  },
  timestamps: true,
};

export default TicketNotes;
