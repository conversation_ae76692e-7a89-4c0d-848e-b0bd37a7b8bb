import { CollectionConfig } from 'payload/types';
import { hasPermission, filterByAccess } from '../lib/payload-auth';

const TicketTemplates: CollectionConfig = {
  slug: 'ticket-templates',
  labels: {
    singular: 'Ticket Template',
    plural: 'Ticket Templates',
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'category', 'defaultPriority', 'usageCount', 'isActive'],
    group: 'Support System',
  },
  access: {
    create: ({ req: { user } }) => hasPermission(user, 'create', 'ticket-templates'),
    read: ({ req: { user } }) => {
      if (!hasPermission(user, 'read', 'ticket-templates')) return false;
      return filterByAccess(user, {}, 'ticket-templates');
    },
    update: ({ req: { user } }) => {
      if (!hasPermission(user, 'update', 'ticket-templates')) return false;
      return filterByAccess(user, {}, 'ticket-templates');
    },
    delete: ({ req: { user } }) => {
      if (!hasPermission(user, 'delete', 'ticket-templates')) return false;
      return filterByAccess(user, {}, 'ticket-templates');
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      label: 'Template Name',
      required: true,
      maxLength: 100,
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
      admin: {
        description: 'Brief description of when to use this template',
      },
    },
    {
      name: 'category',
      type: 'relationship',
      label: 'Category',
      relationTo: 'support-categories',
      admin: {
        position: 'sidebar',
      },
      filterOptions: ({ user }) => {
        if (!user?.instituteId) return false;
        return {
          instituteId: { equals: user.instituteId },
          isActive: { equals: true },
        };
      },
    },
    // Template Content Group
    {
      name: 'templateContent',
      type: 'group',
      label: 'Template Content',
      fields: [
        {
          name: 'titleTemplate',
          type: 'text',
          label: 'Title Template',
          admin: {
            description: 'Use {{variable}} for dynamic content (e.g., "Issue with {{system}}")',
            placeholder: 'Issue with {{system}}',
          },
        },
        {
          name: 'contentTemplate',
          type: 'richText',
          label: 'Content Template',
          admin: {
            description: 'Use {{variable}} for dynamic content',
          },
        },
        {
          name: 'variables',
          type: 'json',
          label: 'Template Variables',
          admin: {
            description: 'JSON object defining available variables and their default values',
          },
        },
      ],
    },
    // Default Configuration Group
    {
      name: 'defaultConfig',
      type: 'group',
      label: 'Default Configuration',
      fields: [
        {
          name: 'defaultPriority',
          type: 'select',
          label: 'Default Priority',
          required: true,
          defaultValue: 'MEDIUM',
          options: [
            { label: 'Low', value: 'LOW' },
            { label: 'Medium', value: 'MEDIUM' },
            { label: 'High', value: 'HIGH' },
            { label: 'Urgent', value: 'URGENT' },
            { label: 'Critical', value: 'CRITICAL' },
          ],
        },
        {
          name: 'defaultStatus',
          type: 'select',
          label: 'Default Status',
          required: true,
          defaultValue: 'OPEN',
          options: [
            { label: 'Open', value: 'OPEN' },
            { label: 'In Progress', value: 'IN_PROGRESS' },
            { label: 'Pending Customer', value: 'PENDING_CUSTOMER' },
            { label: 'Pending Vendor', value: 'PENDING_VENDOR' },
          ],
        },
        {
          name: 'autoAssignTo',
          type: 'relationship',
          label: 'Auto Assign To',
          relationTo: 'users',
          admin: {
            description: 'Automatically assign tickets created from this template',
          },
          filterOptions: ({ user }) => {
            if (!user?.instituteId) return false;
            return {
              instituteId: { equals: user.instituteId },
              role: { in: ['INSTITUTE_ADMIN', 'SUPPORT_STAFF'] },
              isActive: { equals: true },
            };
          },
        },
      ],
    },
    // Usage Statistics Group
    {
      name: 'usageStats',
      type: 'group',
      label: 'Usage Statistics',
      admin: {
        condition: (data, siblingData, { user }) => 
          user?.role === 'SUPER_ADMIN' || user?.role === 'INSTITUTE_ADMIN',
      },
      fields: [
        {
          name: 'usageCount',
          type: 'number',
          label: 'Usage Count',
          defaultValue: 0,
          admin: {
            readOnly: true,
            description: 'Number of times this template has been used',
          },
        },
        {
          name: 'lastUsedAt',
          type: 'date',
          label: 'Last Used At',
          admin: {
            readOnly: true,
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
      ],
    },
    // Status
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active',
      defaultValue: true,
      admin: {
        position: 'sidebar',
      },
    },
    // Hidden fields for multi-tenancy
    {
      name: 'instituteId',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.instituteId,
        ],
      },
    },
    {
      name: 'createdBy',
      type: 'text',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          ({ req }) => req.user?.id,
        ],
      },
    },
  ],
  hooks: {
    beforeValidate: [
      ({ operation, data, req }) => {
        // Ensure unique name per institute
        if (operation === 'create' || operation === 'update') {
          data.instituteId = req.user?.instituteId;
        }
        return data;
      },
    ],
    afterChange: [
      ({ operation, doc, req }) => {
        // Update usage statistics when template is used
        if (operation === 'update' && doc.usageCount !== undefined) {
          // This would be triggered when a ticket is created from this template
          // Implementation would update lastUsedAt timestamp
        }
      },
    ],
  },
  timestamps: true,
};

export default TicketTemplates;
