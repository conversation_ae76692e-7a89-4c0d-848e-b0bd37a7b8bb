import { CollectionConfig } from 'payload';
import { UserRole } from '@prisma/client';

const Users: CollectionConfig = {
  slug: 'users',
  auth: {
    // Disable Payload's built-in auth since we're using NextAuth.js
    disableLocalStrategy: true,
  },
  admin: {
    useAsTitle: 'email',
    defaultColumns: ['email', 'name', 'role', 'isActive'],
  },
  access: {
    // Only super admins can read all users
    read: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      // Institute admins can only see users in their institute
      if (user.role === UserRole.INSTITUTE_ADMIN) {
        return {
          instituteId: {
            equals: user.instituteId,
          },
        };
      }
      
      // Support staff can only see their own profile
      if (user.role === UserRole.SUPPORT_STAFF) {
        return {
          id: {
            equals: user.id,
          },
        };
      }
      
      // Students can only see their own profile
      return {
        id: {
          equals: user.id,
        },
      };
    },
    
    // Only super admins and institute admins can create users
    create: ({ req: { user } }) => {
      if (!user) return false;
      return [UserRole.SUPER_ADMIN, UserRole.INSTITUTE_ADMIN].includes(user.role);
    },
    
    // Users can update their own profile, admins can update users in their scope
    update: ({ req: { user }, id }) => {
      if (!user) return false;
      
      if (user.role === UserRole.SUPER_ADMIN) {
        return true;
      }
      
      if (user.role === UserRole.INSTITUTE_ADMIN) {
        return {
          or: [
            { id: { equals: user.id } }, // Own profile
            { instituteId: { equals: user.instituteId } }, // Same institute
          ],
        };
      }
      
      // Others can only update their own profile
      return {
        id: {
          equals: user.id,
        },
      };
    },
    
    // Only super admins can delete users
    delete: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === UserRole.SUPER_ADMIN;
    },
  },
  fields: [
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true,
      admin: {
        readOnly: true, // Email should not be changed in CMS
      },
    },
    {
      name: 'name',
      type: 'text',
      required: false,
    },
    {
      name: 'role',
      type: 'select',
      required: true,
      options: [
        {
          label: 'Super Admin',
          value: UserRole.SUPER_ADMIN,
        },
        {
          label: 'Institute Admin',
          value: UserRole.INSTITUTE_ADMIN,
        },
        {
          label: 'Support Staff',
          value: UserRole.SUPPORT_STAFF,
        },
        {
          label: 'Student',
          value: UserRole.STUDENT,
        },
      ],
      access: {
        // Only super admins can change roles
        update: ({ req: { user } }) => {
          return user?.role === UserRole.SUPER_ADMIN;
        },
      },
    },
    {
      name: 'instituteId',
      type: 'text',
      required: false,
      admin: {
        description: 'Institute ID from LMS',
      },
      access: {
        // Only super admins can change institute assignment
        update: ({ req: { user } }) => {
          return user?.role === UserRole.SUPER_ADMIN;
        },
      },
    },
    {
      name: 'branchId',
      type: 'text',
      required: false,
      admin: {
        description: 'Branch ID from LMS',
      },
      access: {
        // Only super admins and institute admins can change branch assignment
        update: ({ req: { user } }) => {
          return [UserRole.SUPER_ADMIN, UserRole.INSTITUTE_ADMIN].includes(user?.role);
        },
      },
    },
    {
      name: 'lmsUserId',
      type: 'text',
      required: false,
      unique: true,
      admin: {
        description: 'User ID from LMS',
        readOnly: true,
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      access: {
        // Only super admins and institute admins can change active status
        update: ({ req: { user } }) => {
          return [UserRole.SUPER_ADMIN, UserRole.INSTITUTE_ADMIN].includes(user?.role);
        },
      },
    },
    {
      name: 'lastLoginAt',
      type: 'date',
      admin: {
        readOnly: true,
        description: 'Last login timestamp',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        // Prevent role escalation
        if (operation === 'update' && req.user) {
          const currentUserRole = req.user.role;
          const targetRole = data.role;
          
          // Non-super admins cannot assign super admin role
          if (currentUserRole !== UserRole.SUPER_ADMIN && targetRole === UserRole.SUPER_ADMIN) {
            throw new Error('Insufficient permissions to assign super admin role');
          }
          
          // Institute admins cannot assign institute admin role to users outside their institute
          if (
            currentUserRole === UserRole.INSTITUTE_ADMIN &&
            targetRole === UserRole.INSTITUTE_ADMIN &&
            data.instituteId !== req.user.instituteId
          ) {
            throw new Error('Cannot assign institute admin role outside your institute');
          }
        }
        
        return data;
      },
    ],
  },
};

export default Users;
