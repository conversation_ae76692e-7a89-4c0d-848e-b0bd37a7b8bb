import React from 'react';

interface AnalyticsPreviewProps {
  analytics: {
    firstResponseTime?: number;
    resolutionTime?: number;
    satisfactionScore?: number;
    escalationCount?: number;
    reopenCount?: number;
    slaResponseMet?: boolean;
    slaResolutionMet?: boolean;
  };
  className?: string;
}

export const AnalyticsPreview: React.FC<AnalyticsPreviewProps> = ({
  analytics,
  className = '',
}) => {
  const formatTime = (minutes?: number) => {
    if (!minutes) return 'N/A';
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  const formatSatisfaction = (score?: number) => {
    if (!score) return 'N/A';
    const stars = '★'.repeat(score) + '☆'.repeat(5 - score);
    return `${stars} (${score}/5)`;
  };

  const getSLAIcon = (met?: boolean) => {
    if (met === undefined) return '❓';
    return met ? '✅' : '❌';
  };

  const getSLAColor = (met?: boolean) => {
    if (met === undefined) return 'text-gray-500';
    return met ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className={`bg-gray-50 rounded-lg p-4 space-y-3 ${className}`}>
      <h4 className="text-sm font-semibold text-gray-700 mb-3">Ticket Analytics</h4>
      
      <div className="grid grid-cols-2 gap-4">
        {/* Response Time */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">First Response:</span>
          <span className="text-sm font-medium">{formatTime(analytics.firstResponseTime)}</span>
        </div>

        {/* Resolution Time */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">Resolution:</span>
          <span className="text-sm font-medium">{formatTime(analytics.resolutionTime)}</span>
        </div>

        {/* SLA Response */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">Response SLA:</span>
          <span className={`text-sm font-medium ${getSLAColor(analytics.slaResponseMet)}`}>
            {getSLAIcon(analytics.slaResponseMet)}
          </span>
        </div>

        {/* SLA Resolution */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">Resolution SLA:</span>
          <span className={`text-sm font-medium ${getSLAColor(analytics.slaResolutionMet)}`}>
            {getSLAIcon(analytics.slaResolutionMet)}
          </span>
        </div>
      </div>

      {/* Satisfaction Score */}
      {analytics.satisfactionScore && (
        <div className="pt-2 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Satisfaction:</span>
            <span className="text-sm">{formatSatisfaction(analytics.satisfactionScore)}</span>
          </div>
        </div>
      )}

      {/* Escalations and Reopens */}
      {(analytics.escalationCount || analytics.reopenCount) && (
        <div className="pt-2 border-t border-gray-200">
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">
              Escalations: <span className="font-medium">{analytics.escalationCount || 0}</span>
            </span>
            <span className="text-gray-600">
              Reopens: <span className="font-medium">{analytics.reopenCount || 0}</span>
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsPreview;
