import React from 'react';

interface MessageTypeIndicatorProps {
  messageType: string;
  visibility?: string;
  className?: string;
}

const messageTypeConfig = {
  CUSTOMER_REPLY: {
    label: 'Customer',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: '👤',
  },
  AGENT_REPLY: {
    label: 'Agent',
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: '🎧',
  },
  INTERNAL_NOTE: {
    label: 'Internal',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: '📝',
  },
  SYSTEM_MESSAGE: {
    label: 'System',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: '🤖',
  },
};

const visibilityConfig = {
  PUBLIC: {
    label: 'Public',
    color: 'text-green-600',
    icon: '🌐',
  },
  INTERNAL: {
    label: 'Internal',
    color: 'text-yellow-600',
    icon: '🏢',
  },
  PRIVATE: {
    label: 'Private',
    color: 'text-red-600',
    icon: '🔒',
  },
};

export const MessageTypeIndicator: React.FC<MessageTypeIndicatorProps> = ({
  messageType,
  visibility,
  className = '',
}) => {
  const typeConfig = messageTypeConfig[messageType as keyof typeof messageTypeConfig] || {
    label: messageType,
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: '❓',
  };

  const visConfig = visibility ? visibilityConfig[visibility as keyof typeof visibilityConfig] : null;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span
        className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md border ${typeConfig.color}`}
      >
        <span>{typeConfig.icon}</span>
        <span>{typeConfig.label}</span>
      </span>
      
      {visConfig && (
        <span className={`inline-flex items-center gap-1 text-xs ${visConfig.color}`}>
          <span>{visConfig.icon}</span>
          <span>{visConfig.label}</span>
        </span>
      )}
    </div>
  );
};

export default MessageTypeIndicator;
