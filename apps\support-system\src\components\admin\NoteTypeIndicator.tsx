import React from 'react';

interface NoteTypeIndicatorProps {
  noteType: string;
  importance?: string;
  isPinned?: boolean;
  className?: string;
}

const noteTypeConfig = {
  GENERAL: {
    label: 'General',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: '📄',
  },
  ESCALATION: {
    label: 'Escalation',
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: '⬆️',
  },
  RESOLUTION: {
    label: 'Resolution',
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: '✅',
  },
  FOLLOWUP: {
    label: 'Follow-up',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: '📅',
  },
  INVESTIGATION: {
    label: 'Investigation',
    color: 'bg-purple-100 text-purple-800 border-purple-200',
    icon: '🔍',
  },
  CUSTOMER_CONTACT: {
    label: 'Customer Contact',
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: '📞',
  },
  TECHNICAL: {
    label: 'Technical',
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    icon: '⚙️',
  },
};

const importanceConfig = {
  LOW: {
    label: 'Low',
    color: 'text-gray-500',
    icon: '⬇️',
  },
  NORMAL: {
    label: 'Normal',
    color: 'text-blue-500',
    icon: '➡️',
  },
  HIGH: {
    label: 'High',
    color: 'text-orange-500',
    icon: '⬆️',
  },
  CRITICAL: {
    label: 'Critical',
    color: 'text-red-500',
    icon: '🚨',
  },
};

export const NoteTypeIndicator: React.FC<NoteTypeIndicatorProps> = ({
  noteType,
  importance,
  isPinned,
  className = '',
}) => {
  const typeConfig = noteTypeConfig[noteType as keyof typeof noteTypeConfig] || {
    label: noteType,
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: '❓',
  };

  const importanceConf = importance ? importanceConfig[importance as keyof typeof importanceConfig] : null;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span
        className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md border ${typeConfig.color}`}
      >
        <span>{typeConfig.icon}</span>
        <span>{typeConfig.label}</span>
      </span>
      
      {importanceConf && importance !== 'NORMAL' && (
        <span className={`inline-flex items-center gap-1 text-xs ${importanceConf.color}`}>
          <span>{importanceConf.icon}</span>
          <span>{importanceConf.label}</span>
        </span>
      )}

      {isPinned && (
        <span className="inline-flex items-center gap-1 text-xs text-yellow-600">
          <span>📌</span>
          <span>Pinned</span>
        </span>
      )}
    </div>
  );
};

export default NoteTypeIndicator;
