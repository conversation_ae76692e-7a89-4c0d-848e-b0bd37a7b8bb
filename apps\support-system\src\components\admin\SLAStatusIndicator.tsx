import React from 'react';

interface SLAStatusIndicatorProps {
  dueDate?: string | Date;
  completedDate?: string | Date;
  type: 'response' | 'resolution';
  className?: string;
}

export const SLAStatusIndicator: React.FC<SLAStatusIndicatorProps> = ({
  dueDate,
  completedDate,
  type,
  className = '',
}) => {
  if (!dueDate) {
    return (
      <span className={`text-gray-500 text-sm ${className}`}>
        No SLA set
      </span>
    );
  }

  const due = new Date(dueDate);
  const completed = completedDate ? new Date(completedDate) : null;
  const now = new Date();

  let status: 'met' | 'breached' | 'at-risk' | 'pending';
  let timeInfo: string;
  let colorClass: string;
  let icon: string;

  if (completed) {
    // SLA is complete
    if (completed <= due) {
      status = 'met';
      colorClass = 'text-green-700 bg-green-50 border-green-200';
      icon = '✅';
      timeInfo = `Met (${Math.round((due.getTime() - completed.getTime()) / (1000 * 60 * 60))}h early)`;
    } else {
      status = 'breached';
      colorClass = 'text-red-700 bg-red-50 border-red-200';
      icon = '❌';
      timeInfo = `Breached (${Math.round((completed.getTime() - due.getTime()) / (1000 * 60 * 60))}h late)`;
    }
  } else {
    // SLA is still pending
    const hoursRemaining = Math.round((due.getTime() - now.getTime()) / (1000 * 60 * 60));
    
    if (now > due) {
      status = 'breached';
      colorClass = 'text-red-700 bg-red-50 border-red-200';
      icon = '🚨';
      timeInfo = `Breached (${Math.abs(hoursRemaining)}h overdue)`;
    } else if (hoursRemaining <= 2) {
      status = 'at-risk';
      colorClass = 'text-orange-700 bg-orange-50 border-orange-200';
      icon = '⚠️';
      timeInfo = `At risk (${hoursRemaining}h remaining)`;
    } else {
      status = 'pending';
      colorClass = 'text-blue-700 bg-blue-50 border-blue-200';
      icon = '⏱️';
      timeInfo = `${hoursRemaining}h remaining`;
    }
  }

  const typeLabel = type === 'response' ? 'Response' : 'Resolution';

  return (
    <div className={`inline-flex items-center gap-2 px-3 py-1 text-sm rounded-md border ${colorClass} ${className}`}>
      <span>{icon}</span>
      <div className="flex flex-col">
        <span className="font-medium">{typeLabel} SLA</span>
        <span className="text-xs opacity-75">{timeInfo}</span>
      </div>
    </div>
  );
};

export default SLAStatusIndicator;
