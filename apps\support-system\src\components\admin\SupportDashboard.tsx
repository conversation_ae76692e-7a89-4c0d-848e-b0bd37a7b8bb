import React from 'react';

interface SupportDashboardProps {
  user: {
    id: string;
    role: string;
    instituteId?: string;
    branchId?: string;
  };
}

interface DashboardStats {
  totalTickets: number;
  openTickets: number;
  inProgressTickets: number;
  resolvedToday: number;
  avgResponseTime: number;
  slaBreaches: number;
}

export const SupportDashboard: React.FC<SupportDashboardProps> = ({ user }) => {
  // In a real implementation, these would be fetched from the API
  const stats: DashboardStats = {
    totalTickets: 156,
    openTickets: 23,
    inProgressTickets: 12,
    resolvedToday: 8,
    avgResponseTime: 2.5, // hours
    slaBreaches: 3,
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    subtitle?: string;
    color?: string;
    icon?: string;
  }> = ({ title, value, subtitle, color = 'blue', icon }) => (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-bold text-${color}-600`}>{value}</p>
          {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
        </div>
        {icon && <span className="text-2xl">{icon}</span>}
      </div>
    </div>
  );

  const QuickActions = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
      <div className="space-y-3">
        <button className="w-full text-left px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-md border border-blue-200 text-blue-700 font-medium">
          📝 Create New Ticket
        </button>
        <button className="w-full text-left px-4 py-2 bg-green-50 hover:bg-green-100 rounded-md border border-green-200 text-green-700 font-medium">
          📊 View Analytics
        </button>
        <button className="w-full text-left px-4 py-2 bg-purple-50 hover:bg-purple-100 rounded-md border border-purple-200 text-purple-700 font-medium">
          ⚙️ Manage Categories
        </button>
        <button className="w-full text-left px-4 py-2 bg-orange-50 hover:bg-orange-100 rounded-md border border-orange-200 text-orange-700 font-medium">
          📋 Ticket Templates
        </button>
      </div>
    </div>
  );

  const RecentActivity = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
      <div className="space-y-3">
        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-md">
          <span className="text-green-600">✅</span>
          <div className="flex-1">
            <p className="text-sm font-medium">Ticket #INST-2025-001234 resolved</p>
            <p className="text-xs text-gray-500">2 minutes ago</p>
          </div>
        </div>
        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-md">
          <span className="text-blue-600">📝</span>
          <div className="flex-1">
            <p className="text-sm font-medium">New ticket created by John Doe</p>
            <p className="text-xs text-gray-500">15 minutes ago</p>
          </div>
        </div>
        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-md">
          <span className="text-yellow-600">⚠️</span>
          <div className="flex-1">
            <p className="text-sm font-medium">SLA breach warning for ticket #INST-2025-001230</p>
            <p className="text-xs text-gray-500">1 hour ago</p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Support Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Welcome back! Here's what's happening with your support system.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          <StatCard
            title="Total Tickets"
            value={stats.totalTickets}
            icon="🎫"
            color="blue"
          />
          <StatCard
            title="Open Tickets"
            value={stats.openTickets}
            icon="🔵"
            color="blue"
          />
          <StatCard
            title="In Progress"
            value={stats.inProgressTickets}
            icon="🟡"
            color="yellow"
          />
          <StatCard
            title="Resolved Today"
            value={stats.resolvedToday}
            icon="✅"
            color="green"
          />
          <StatCard
            title="Avg Response"
            value={`${stats.avgResponseTime}h`}
            subtitle="Response time"
            icon="⏱️"
            color="purple"
          />
          <StatCard
            title="SLA Breaches"
            value={stats.slaBreaches}
            icon="🚨"
            color="red"
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Quick Actions */}
          <div className="lg:col-span-1">
            <QuickActions />
          </div>

          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <RecentActivity />
          </div>
        </div>

        {/* Role-specific information */}
        {user.role === 'SUPER_ADMIN' && (
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900">Super Admin View</h4>
            <p className="text-blue-700 text-sm">
              You have access to all institutes and advanced system settings.
            </p>
          </div>
        )}

        {user.role === 'INSTITUTE_ADMIN' && (
          <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-900">Institute Admin View</h4>
            <p className="text-green-700 text-sm">
              Managing support for your institute. You can access all branches and staff.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SupportDashboard;
