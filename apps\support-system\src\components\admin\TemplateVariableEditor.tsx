import React, { useState, useEffect } from 'react';

interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  required: boolean;
  defaultValue?: string;
  description?: string;
}

interface TemplateVariableEditorProps {
  value?: Record<string, TemplateVariable>;
  onChange: (value: Record<string, TemplateVariable>) => void;
  className?: string;
}

export const TemplateVariableEditor: React.FC<TemplateVariableEditorProps> = ({
  value = {},
  onChange,
  className = '',
}) => {
  const [variables, setVariables] = useState<Record<string, TemplateVariable>>(value);
  const [newVariableName, setNewVariableName] = useState('');

  useEffect(() => {
    setVariables(value);
  }, [value]);

  const addVariable = () => {
    if (!newVariableName.trim()) return;
    
    const newVariable: TemplateVariable = {
      name: newVariableName,
      type: 'string',
      required: false,
      defaultValue: '',
      description: '',
    };

    const updated = {
      ...variables,
      [newVariableName]: newVariable,
    };

    setVariables(updated);
    onChange(updated);
    setNewVariableName('');
  };

  const updateVariable = (name: string, updates: Partial<TemplateVariable>) => {
    const updated = {
      ...variables,
      [name]: {
        ...variables[name],
        ...updates,
      },
    };

    setVariables(updated);
    onChange(updated);
  };

  const removeVariable = (name: string) => {
    const updated = { ...variables };
    delete updated[name];
    
    setVariables(updated);
    onChange(updated);
  };

  const variableEntries = Object.entries(variables);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-gray-700 mb-3">Template Variables</h4>
        <p className="text-xs text-gray-600 mb-4">
          Define variables that can be used in your template with {`{{variableName}}`} syntax.
        </p>

        {/* Add new variable */}
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            placeholder="Variable name (e.g., customerName)"
            value={newVariableName}
            onChange={(e) => setNewVariableName(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
            onKeyPress={(e) => e.key === 'Enter' && addVariable()}
          />
          <button
            type="button"
            onClick={addVariable}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
          >
            Add Variable
          </button>
        </div>

        {/* Variable list */}
        {variableEntries.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p className="text-sm">No variables defined yet.</p>
            <p className="text-xs">Add variables to make your templates dynamic.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {variableEntries.map(([name, variable]) => (
              <div key={name} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <code className="px-2 py-1 bg-gray-100 rounded text-sm font-mono">
                      {`{{${name}}}`}
                    </code>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      variable.required 
                        ? 'bg-red-100 text-red-700' 
                        : 'bg-gray-100 text-gray-700'
                    }`}>
                      {variable.required ? 'Required' : 'Optional'}
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeVariable(name)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Type */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Type
                    </label>
                    <select
                      value={variable.type}
                      onChange={(e) => updateVariable(name, { type: e.target.value as TemplateVariable['type'] })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="string">Text</option>
                      <option value="number">Number</option>
                      <option value="date">Date</option>
                      <option value="boolean">Yes/No</option>
                    </select>
                  </div>

                  {/* Required */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Required
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={variable.required}
                        onChange={(e) => updateVariable(name, { required: e.target.checked })}
                        className="mr-2"
                      />
                      <span className="text-sm">This variable is required</span>
                    </label>
                  </div>

                  {/* Default Value */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Default Value
                    </label>
                    <input
                      type="text"
                      value={variable.defaultValue || ''}
                      onChange={(e) => updateVariable(name, { defaultValue: e.target.value })}
                      placeholder="Optional default value"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    />
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <input
                      type="text"
                      value={variable.description || ''}
                      onChange={(e) => updateVariable(name, { description: e.target.value })}
                      placeholder="What this variable represents"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateVariableEditor;
