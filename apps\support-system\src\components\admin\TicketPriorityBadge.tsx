import React from 'react';

interface TicketPriorityBadgeProps {
  priority: string;
  className?: string;
}

const priorityConfig = {
  LOW: {
    label: 'Low',
    color: 'bg-gray-100 text-gray-700 border-gray-300',
    icon: '⬇️',
  },
  MEDIUM: {
    label: 'Medium',
    color: 'bg-blue-100 text-blue-700 border-blue-300',
    icon: '➡️',
  },
  HIGH: {
    label: 'High',
    color: 'bg-orange-100 text-orange-700 border-orange-300',
    icon: '⬆️',
  },
  URGENT: {
    label: 'Urgent',
    color: 'bg-red-100 text-red-700 border-red-300',
    icon: '🔺',
  },
  CRITICAL: {
    label: 'Critical',
    color: 'bg-red-200 text-red-900 border-red-400',
    icon: '🚨',
  },
};

export const TicketPriorityBadge: React.FC<TicketPriorityBadgeProps> = ({ 
  priority, 
  className = '' 
}) => {
  const config = priorityConfig[priority as keyof typeof priorityConfig] || {
    label: priority,
    color: 'bg-gray-100 text-gray-700 border-gray-300',
    icon: '❓',
  };

  return (
    <span
      className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md border ${config.color} ${className}`}
    >
      <span>{config.icon}</span>
      <span>{config.label}</span>
    </span>
  );
};

export default TicketPriorityBadge;
