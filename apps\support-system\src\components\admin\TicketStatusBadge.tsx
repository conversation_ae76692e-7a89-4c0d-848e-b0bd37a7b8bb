import React from 'react';

interface TicketStatusBadgeProps {
  status: string;
  className?: string;
}

const statusConfig = {
  OPEN: {
    label: 'Open',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: '🔵',
  },
  IN_PROGRESS: {
    label: 'In Progress',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: '🟡',
  },
  PENDING_CUSTOMER: {
    label: 'Pending Customer',
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: '🟠',
  },
  PENDING_VENDOR: {
    label: 'Pending Vendor',
    color: 'bg-purple-100 text-purple-800 border-purple-200',
    icon: '🟣',
  },
  RESOLVED: {
    label: 'Resolved',
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: '✅',
  },
  CLOSED: {
    label: 'Closed',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: '⚫',
  },
  CANCELLED: {
    label: 'Cancelled',
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: '❌',
  },
};

export const TicketStatusBadge: React.FC<TicketStatusBadgeProps> = ({ 
  status, 
  className = '' 
}) => {
  const config = statusConfig[status as keyof typeof statusConfig] || {
    label: status,
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: '❓',
  };

  return (
    <span
      className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md border ${config.color} ${className}`}
    >
      <span>{config.icon}</span>
      <span>{config.label}</span>
    </span>
  );
};

export default TicketStatusBadge;
