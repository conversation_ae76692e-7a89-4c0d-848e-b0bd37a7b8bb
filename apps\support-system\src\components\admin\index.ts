// Admin UI Components for Payload CMS
export { default as TicketStatusBadge } from './TicketStatusBadge';
export { default as TicketPriorityBadge } from './TicketPriorityBadge';
export { default as SLAStatusIndicator } from './SLAStatusIndicator';
export { default as AnalyticsPreview } from './AnalyticsPreview';
export { default as MessageTypeIndicator } from './MessageTypeIndicator';
export { default as NoteTypeIndicator } from './NoteTypeIndicator';
export { default as SupportDashboard } from './SupportDashboard';
export { default as TemplateVariableEditor } from './TemplateVariableEditor';

// Re-export types for convenience
export type { default as TicketStatusBadgeProps } from './TicketStatusBadge';
export type { default as TicketPriorityBadgeProps } from './TicketPriorityBadge';
export type { default as SLAStatusIndicatorProps } from './SLAStatusIndicator';
export type { default as AnalyticsPreviewProps } from './AnalyticsPreview';
export type { default as MessageTypeIndicatorProps } from './MessageTypeIndicator';
export type { default as NoteTypeIndicatorProps } from './NoteTypeIndicator';
export type { default as SupportDashboardProps } from './SupportDashboard';
export type { default as TemplateVariableEditorProps } from './TemplateVariableEditor';
