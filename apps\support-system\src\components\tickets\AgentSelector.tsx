'use client';

import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { Search, User, Clock, CheckCircle, AlertCircle, Users } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Agent {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'ONLINE' | 'OFFLINE' | 'BUSY' | 'AWAY';
  currentTickets: number;
  maxTickets: number;
  avatar?: string;
  department?: string;
  skills?: string[];
}

interface AgentSelectorProps {
  selectedAgentId?: string;
  onAgentSelect: (agentId: string | null) => void;
  disabled?: boolean;
  showAvailability?: boolean;
  filterBySkills?: string[];
  className?: string;
  placeholder?: string;
}

export const AgentSelector: React.FC<AgentSelectorProps> = ({
  selectedAgentId,
  onAgentSelect,
  disabled = false,
  showAvailability = true,
  filterBySkills = [],
  className,
  placeholder = "Select an agent...",
}) => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadAgents();
  }, [filterBySkills]);

  const loadAgents = async () => {
    try {
      const params = new URLSearchParams();
      if (filterBySkills.length > 0) {
        params.append('skills', filterBySkills.join(','));
      }
      
      const response = await fetch(`/api/support/agents?${params}`);
      if (!response.ok) {
        throw new Error('Failed to load agents');
      }

      const data = await response.json();
      setAgents(data.agents || []);
    } catch (error) {
      console.error('Error loading agents:', error);
      toast({
        title: 'Error',
        description: 'Failed to load agents',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ONLINE':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'BUSY':
        return <AlertCircle className="h-3 w-3 text-yellow-500" />;
      case 'AWAY':
        return <Clock className="h-3 w-3 text-orange-500" />;
      default:
        return <div className="h-3 w-3 rounded-full bg-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ONLINE':
        return 'text-green-600 bg-green-50';
      case 'BUSY':
        return 'text-yellow-600 bg-yellow-50';
      case 'AWAY':
        return 'text-orange-600 bg-orange-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getAvailabilityText = (agent: Agent) => {
    const available = agent.maxTickets - agent.currentTickets;
    if (available <= 0) return 'Full capacity';
    return `${available} slots available`;
  };

  const isAgentAvailable = (agent: Agent) => {
    return agent.status === 'ONLINE' && agent.currentTickets < agent.maxTickets;
  };

  const filteredAgents = agents.filter(agent =>
    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.department?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedAgent = agents.find(agent => agent.id === selectedAgentId);

  if (loading) {
    return (
      <div className={cn('space-y-2', className)}>
        <Skeleton className="h-10 w-full" />
        {showAvailability && <Skeleton className="h-4 w-32" />}
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <div className="relative">
        <Select
          value={selectedAgentId || ''}
          onValueChange={(value) => onAgentSelect(value || null)}
          disabled={disabled}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder={placeholder}>
              {selectedAgent && (
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={selectedAgent.avatar} />
                    <AvatarFallback className="text-xs">
                      {selectedAgent.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <span>{selectedAgent.name}</span>
                  {showAvailability && getStatusIcon(selectedAgent.status)}
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <div className="p-2">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search agents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            
            {/* Unassign option */}
            <SelectItem value="">
              <div className="flex items-center gap-2">
                <div className="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center">
                  <User className="h-3 w-3 text-gray-500" />
                </div>
                <span>Unassigned</span>
              </div>
            </SelectItem>

            {filteredAgents.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                <Users className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p>No agents found</p>
              </div>
            ) : (
              filteredAgents.map((agent) => (
                <SelectItem 
                  key={agent.id} 
                  value={agent.id}
                  disabled={!isAgentAvailable(agent) && showAvailability}
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={agent.avatar} />
                        <AvatarFallback className="text-xs">
                          {agent.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="font-medium">{agent.name}</span>
                        <span className="text-xs text-gray-500">{agent.department}</span>
                      </div>
                    </div>
                    
                    {showAvailability && (
                      <div className="flex items-center gap-2">
                        <Badge className={cn('text-xs', getStatusColor(agent.status))}>
                          {agent.status}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {agent.currentTickets}/{agent.maxTickets}
                        </span>
                      </div>
                    )}
                  </div>
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      </div>

      {/* Agent Details */}
      {selectedAgent && showAvailability && (
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={selectedAgent.avatar} />
                  <AvatarFallback>
                    {selectedAgent.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-sm">{selectedAgent.name}</p>
                  <p className="text-xs text-gray-500">{selectedAgent.email}</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="flex items-center gap-1 mb-1">
                  {getStatusIcon(selectedAgent.status)}
                  <span className="text-xs font-medium">{selectedAgent.status}</span>
                </div>
                <p className="text-xs text-gray-500">
                  {getAvailabilityText(selectedAgent)}
                </p>
              </div>
            </div>
            
            {selectedAgent.skills && selectedAgent.skills.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                {selectedAgent.skills.map((skill) => (
                  <Badge key={skill} variant="outline" className="text-xs">
                    {skill}
                  </Badge>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
