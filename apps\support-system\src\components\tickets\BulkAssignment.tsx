'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { AgentSelector } from './AgentSelector';
import { Users, UserCheck, Loader2, CheckSquare } from 'lucide-react';

interface Ticket {
  id: string;
  ticketNumber: string;
  title: string;
  priority: string;
  status: string;
  assignee?: {
    name: string;
  };
}

interface BulkAssignmentProps {
  tickets: Ticket[];
  onAssignmentComplete?: () => void;
  onClose?: () => void;
}

export const BulkAssignment: React.FC<BulkAssignmentProps> = ({
  tickets,
  onAssignmentComplete,
  onClose,
}) => {
  const [selectedTickets, setSelectedTickets] = useState<string[]>([]);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [assignmentNote, setAssignmentNote] = useState('');
  const [isAssigning, setIsAssigning] = useState(false);
  const { toast } = useToast();

  const handleTicketSelection = (ticketId: string, checked: boolean) => {
    if (checked) {
      setSelectedTickets(prev => [...prev, ticketId]);
    } else {
      setSelectedTickets(prev => prev.filter(id => id !== ticketId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTickets(tickets.map(ticket => ticket.id));
    } else {
      setSelectedTickets([]);
    }
  };

  const handleBulkAssignment = async () => {
    if (selectedTickets.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select at least one ticket to assign',
        variant: 'destructive',
      });
      return;
    }

    setIsAssigning(true);
    try {
      const response = await fetch('/api/support/tickets/bulk-assign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ticketIds: selectedTickets,
          assignedTo: selectedAgentId,
          note: assignmentNote.trim() || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to assign tickets');
      }

      const result = await response.json();
      
      toast({
        title: 'Success',
        description: `${result.assignedCount} tickets ${selectedAgentId ? 'assigned' : 'unassigned'} successfully`,
      });

      setSelectedTickets([]);
      setSelectedAgentId(null);
      setAssignmentNote('');
      onAssignmentComplete?.();
    } catch (error) {
      console.error('Error in bulk assignment:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to assign tickets',
        variant: 'destructive',
      });
    } finally {
      setIsAssigning(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-gray-100 text-gray-800';
      case 'MEDIUM':
        return 'bg-blue-100 text-blue-800';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800';
      case 'URGENT':
        return 'bg-red-100 text-red-800';
      case 'CRITICAL':
        return 'bg-red-200 text-red-900';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const allSelected = selectedTickets.length === tickets.length;
  const someSelected = selectedTickets.length > 0 && selectedTickets.length < tickets.length;

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Bulk Assignment ({tickets.length} tickets)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Agent Selection */}
        <div>
          <Label htmlFor="bulk-agent-selector">Assign to Agent</Label>
          <AgentSelector
            selectedAgentId={selectedAgentId}
            onAgentSelect={setSelectedAgentId}
            showAvailability={true}
            placeholder="Select an agent or leave unassigned..."
          />
        </div>

        {/* Assignment Note */}
        <div>
          <Label htmlFor="bulk-assignment-note">Assignment Note (Optional)</Label>
          <Textarea
            id="bulk-assignment-note"
            placeholder="Add a note about this bulk assignment..."
            value={assignmentNote}
            onChange={(e) => setAssignmentNote(e.target.value)}
            rows={3}
          />
        </div>

        {/* Ticket Selection */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <Label className="text-base font-medium">Select Tickets</Label>
            <div className="flex items-center gap-2">
              <Checkbox
                id="select-all"
                checked={allSelected}
                ref={(el) => {
                  if (el) el.indeterminate = someSelected;
                }}
                onCheckedChange={handleSelectAll}
              />
              <Label htmlFor="select-all" className="text-sm">
                Select All ({selectedTickets.length}/{tickets.length})
              </Label>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto space-y-2 border rounded-lg p-4">
            {tickets.map((ticket) => (
              <div
                key={ticket.id}
                className={`flex items-center gap-3 p-3 rounded-lg border transition-colors ${
                  selectedTickets.includes(ticket.id)
                    ? 'bg-blue-50 border-blue-200'
                    : 'bg-white hover:bg-gray-50'
                }`}
              >
                <Checkbox
                  id={`ticket-${ticket.id}`}
                  checked={selectedTickets.includes(ticket.id)}
                  onCheckedChange={(checked) => 
                    handleTicketSelection(ticket.id, checked as boolean)
                  }
                />
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-mono text-gray-500">
                      #{ticket.ticketNumber}
                    </span>
                    <Badge className={getPriorityColor(ticket.priority)}>
                      {ticket.priority}
                    </Badge>
                    <Badge variant="outline">{ticket.status}</Badge>
                  </div>
                  
                  <h4 className="font-medium text-sm truncate">{ticket.title}</h4>
                  
                  {ticket.assignee && (
                    <p className="text-xs text-gray-500 mt-1">
                      Currently assigned to: {ticket.assignee.name}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Summary */}
        {selectedTickets.length > 0 && (
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <CheckSquare className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900">
                {selectedTickets.length} ticket{selectedTickets.length !== 1 ? 's' : ''} selected
              </span>
            </div>
            <p className="text-sm text-blue-700">
              {selectedAgentId 
                ? 'These tickets will be assigned to the selected agent'
                : 'These tickets will be unassigned'
              }
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-4 pt-4">
          {onClose && (
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
          )}
          <Button
            onClick={handleBulkAssignment}
            disabled={selectedTickets.length === 0 || isAssigning}
          >
            {isAssigning && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {selectedAgentId ? 'Assign' : 'Unassign'} {selectedTickets.length} Ticket{selectedTickets.length !== 1 ? 's' : ''}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
