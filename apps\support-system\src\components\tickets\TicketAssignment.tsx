'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { AgentSelector } from './AgentSelector';
import { UserCheck, UserX, MessageSquare, Clock, Loader2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface TicketAssignmentProps {
  ticketId: string;
  currentAssignee?: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  onAssignmentChange?: (assigneeId: string | null) => void;
  disabled?: boolean;
  showHistory?: boolean;
}

interface AssignmentHistory {
  id: string;
  assignedTo?: {
    name: string;
    email: string;
  };
  assignedBy: {
    name: string;
    email: string;
  };
  assignedAt: string;
  note?: string;
  action: 'ASSIGNED' | 'UNASSIGNED' | 'REASSIGNED';
}

export const TicketAssignment: React.FC<TicketAssignmentProps> = ({
  ticketId,
  currentAssignee,
  onAssignmentChange,
  disabled = false,
  showHistory = true,
}) => {
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(currentAssignee?.id || null);
  const [assignmentNote, setAssignmentNote] = useState('');
  const [isAssigning, setIsAssigning] = useState(false);
  const [assignmentHistory, setAssignmentHistory] = useState<AssignmentHistory[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const { toast } = useToast();

  React.useEffect(() => {
    if (showHistory) {
      loadAssignmentHistory();
    }
  }, [ticketId, showHistory]);

  const loadAssignmentHistory = async () => {
    setLoadingHistory(true);
    try {
      const response = await fetch(`/api/support/tickets/${ticketId}/assignments`);
      if (response.ok) {
        const data = await response.json();
        setAssignmentHistory(data.assignments || []);
      }
    } catch (error) {
      console.error('Error loading assignment history:', error);
    } finally {
      setLoadingHistory(false);
    }
  };

  const handleAssignment = async () => {
    if (selectedAgentId === currentAssignee?.id) {
      toast({
        title: 'No Change',
        description: 'Agent is already assigned to this ticket',
      });
      return;
    }

    setIsAssigning(true);
    try {
      const response = await fetch(`/api/support/tickets/${ticketId}/assign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          assignedTo: selectedAgentId,
          note: assignmentNote.trim() || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to assign ticket');
      }

      const updatedTicket = await response.json();
      
      toast({
        title: 'Success',
        description: selectedAgentId 
          ? `Ticket assigned successfully`
          : 'Ticket unassigned successfully',
      });

      onAssignmentChange?.(selectedAgentId);
      setAssignmentNote('');
      
      if (showHistory) {
        loadAssignmentHistory();
      }
    } catch (error) {
      console.error('Error assigning ticket:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to assign ticket',
        variant: 'destructive',
      });
    } finally {
      setIsAssigning(false);
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'ASSIGNED':
        return <UserCheck className="h-4 w-4 text-green-600" />;
      case 'UNASSIGNED':
        return <UserX className="h-4 w-4 text-red-600" />;
      case 'REASSIGNED':
        return <UserCheck className="h-4 w-4 text-blue-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActionText = (assignment: AssignmentHistory) => {
    switch (assignment.action) {
      case 'ASSIGNED':
        return `Assigned to ${assignment.assignedTo?.name}`;
      case 'UNASSIGNED':
        return 'Unassigned';
      case 'REASSIGNED':
        return `Reassigned to ${assignment.assignedTo?.name}`;
      default:
        return 'Assignment changed';
    }
  };

  const hasChanges = selectedAgentId !== currentAssignee?.id;

  return (
    <div className="space-y-4">
      {/* Current Assignment */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Ticket Assignment
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Assignee Display */}
          {currentAssignee && (
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={currentAssignee.avatar} />
                  <AvatarFallback>
                    {currentAssignee.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-sm">Currently assigned to:</p>
                  <p className="text-sm text-gray-600">{currentAssignee.name}</p>
                  <p className="text-xs text-gray-500">{currentAssignee.email}</p>
                </div>
              </div>
            </div>
          )}

          {!currentAssignee && (
            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
              <div className="flex items-center gap-2 text-gray-600">
                <UserX className="h-4 w-4" />
                <span className="text-sm">This ticket is not assigned to anyone</span>
              </div>
            </div>
          )}

          {/* Agent Selector */}
          <div>
            <Label htmlFor="agent-selector">Assign to Agent</Label>
            <AgentSelector
              selectedAgentId={selectedAgentId}
              onAgentSelect={setSelectedAgentId}
              disabled={disabled}
              showAvailability={true}
              placeholder="Select an agent to assign..."
            />
          </div>

          {/* Assignment Note */}
          <div>
            <Label htmlFor="assignment-note">Assignment Note (Optional)</Label>
            <Textarea
              id="assignment-note"
              placeholder="Add a note about this assignment..."
              value={assignmentNote}
              onChange={(e) => setAssignmentNote(e.target.value)}
              disabled={disabled}
              rows={3}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleAssignment}
              disabled={!hasChanges || isAssigning || disabled}
              className="flex-1"
            >
              {isAssigning && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {selectedAgentId ? 'Assign Ticket' : 'Unassign Ticket'}
            </Button>
            
            {hasChanges && (
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedAgentId(currentAssignee?.id || null);
                  setAssignmentNote('');
                }}
                disabled={isAssigning || disabled}
              >
                Cancel
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Assignment History */}
      {showHistory && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Assignment History
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loadingHistory ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center gap-3">
                    <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse" />
                    <div className="flex-1 space-y-1">
                      <div className="h-4 bg-gray-200 rounded animate-pulse" />
                      <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse" />
                    </div>
                  </div>
                ))}
              </div>
            ) : assignmentHistory.length === 0 ? (
              <div className="text-center py-6 text-gray-500">
                <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p>No assignment history available</p>
              </div>
            ) : (
              <div className="space-y-3">
                {assignmentHistory.map((assignment) => (
                  <div key={assignment.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="mt-1">
                      {getActionIcon(assignment.action)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">
                        {getActionText(assignment)}
                      </p>
                      <p className="text-xs text-gray-500">
                        by {assignment.assignedBy.name} • {formatDistanceToNow(new Date(assignment.assignedAt), { addSuffix: true })}
                      </p>
                      {assignment.note && (
                        <p className="text-xs text-gray-600 mt-1 italic">
                          "{assignment.note}"
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
