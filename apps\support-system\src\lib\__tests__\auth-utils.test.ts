import { hashPassword, verifyPassword, hasRole, canAccessInstitute, canAccessBranch, getUserPermissions } from '../auth-utils';
import { UserRole } from '@prisma/client';

describe('Auth Utils', () => {
  describe('Password hashing', () => {
    it('should hash a password', async () => {
      const password = 'testpassword123';
      const hash = await hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(50);
    });

    it('should verify a correct password', async () => {
      const password = 'testpassword123';
      const hash = await hashPassword(password);
      const isValid = await verifyPassword(password, hash);
      
      expect(isValid).toBe(true);
    });

    it('should reject an incorrect password', async () => {
      const password = 'testpassword123';
      const wrongPassword = 'wrongpassword';
      const hash = await hashPassword(password);
      const isValid = await verifyPassword(wrongPassword, hash);
      
      expect(isValid).toBe(false);
    });
  });

  describe('Role hierarchy', () => {
    it('should allow super admin to access all roles', () => {
      expect(hasRole(UserRole.SUPER_ADMIN, UserRole.SUPER_ADMIN)).toBe(true);
      expect(hasRole(UserRole.SUPER_ADMIN, UserRole.INSTITUTE_ADMIN)).toBe(true);
      expect(hasRole(UserRole.SUPER_ADMIN, UserRole.SUPPORT_STAFF)).toBe(true);
      expect(hasRole(UserRole.SUPER_ADMIN, UserRole.STUDENT)).toBe(true);
    });

    it('should allow institute admin to access lower roles', () => {
      expect(hasRole(UserRole.INSTITUTE_ADMIN, UserRole.SUPER_ADMIN)).toBe(false);
      expect(hasRole(UserRole.INSTITUTE_ADMIN, UserRole.INSTITUTE_ADMIN)).toBe(true);
      expect(hasRole(UserRole.INSTITUTE_ADMIN, UserRole.SUPPORT_STAFF)).toBe(true);
      expect(hasRole(UserRole.INSTITUTE_ADMIN, UserRole.STUDENT)).toBe(true);
    });

    it('should restrict support staff access', () => {
      expect(hasRole(UserRole.SUPPORT_STAFF, UserRole.SUPER_ADMIN)).toBe(false);
      expect(hasRole(UserRole.SUPPORT_STAFF, UserRole.INSTITUTE_ADMIN)).toBe(false);
      expect(hasRole(UserRole.SUPPORT_STAFF, UserRole.SUPPORT_STAFF)).toBe(true);
      expect(hasRole(UserRole.SUPPORT_STAFF, UserRole.STUDENT)).toBe(true);
    });

    it('should restrict student access', () => {
      expect(hasRole(UserRole.STUDENT, UserRole.SUPER_ADMIN)).toBe(false);
      expect(hasRole(UserRole.STUDENT, UserRole.INSTITUTE_ADMIN)).toBe(false);
      expect(hasRole(UserRole.STUDENT, UserRole.SUPPORT_STAFF)).toBe(false);
      expect(hasRole(UserRole.STUDENT, UserRole.STUDENT)).toBe(true);
    });
  });

  describe('Institute access control', () => {
    it('should allow super admin to access any institute', () => {
      expect(canAccessInstitute(UserRole.SUPER_ADMIN, null, 'institute1')).toBe(true);
      expect(canAccessInstitute(UserRole.SUPER_ADMIN, 'institute2', 'institute1')).toBe(true);
    });

    it('should restrict other roles to their own institute', () => {
      expect(canAccessInstitute(UserRole.INSTITUTE_ADMIN, 'institute1', 'institute1')).toBe(true);
      expect(canAccessInstitute(UserRole.INSTITUTE_ADMIN, 'institute1', 'institute2')).toBe(false);
      expect(canAccessInstitute(UserRole.SUPPORT_STAFF, 'institute1', 'institute1')).toBe(true);
      expect(canAccessInstitute(UserRole.SUPPORT_STAFF, 'institute1', 'institute2')).toBe(false);
    });
  });

  describe('Branch access control', () => {
    it('should allow super admin to access any branch', () => {
      expect(canAccessBranch(UserRole.SUPER_ADMIN, null, null, 'institute1', 'branch1')).toBe(true);
    });

    it('should allow institute admin to access all branches in their institute', () => {
      expect(canAccessBranch(UserRole.INSTITUTE_ADMIN, 'institute1', 'branch1', 'institute1', 'branch2')).toBe(true);
      expect(canAccessBranch(UserRole.INSTITUTE_ADMIN, 'institute1', 'branch1', 'institute2', 'branch1')).toBe(false);
    });

    it('should restrict other roles to their own branch', () => {
      expect(canAccessBranch(UserRole.SUPPORT_STAFF, 'institute1', 'branch1', 'institute1', 'branch1')).toBe(true);
      expect(canAccessBranch(UserRole.SUPPORT_STAFF, 'institute1', 'branch1', 'institute1', 'branch2')).toBe(false);
    });
  });

  describe('User permissions', () => {
    it('should give super admin all permissions', () => {
      const permissions = getUserPermissions(UserRole.SUPER_ADMIN);
      expect(permissions.canManageUsers).toBe(true);
      expect(permissions.canManageInstitutes).toBe(true);
      expect(permissions.canManageBranches).toBe(true);
      expect(permissions.canViewAllTickets).toBe(true);
      expect(permissions.canManageTickets).toBe(true);
      expect(permissions.canViewReports).toBe(true);
      expect(permissions.canManageSystem).toBe(true);
    });

    it('should give institute admin limited permissions', () => {
      const permissions = getUserPermissions(UserRole.INSTITUTE_ADMIN);
      expect(permissions.canManageUsers).toBe(true);
      expect(permissions.canManageInstitutes).toBe(false);
      expect(permissions.canManageBranches).toBe(true);
      expect(permissions.canViewAllTickets).toBe(true);
      expect(permissions.canManageTickets).toBe(true);
      expect(permissions.canViewReports).toBe(true);
      expect(permissions.canManageSystem).toBe(false);
    });

    it('should give support staff minimal permissions', () => {
      const permissions = getUserPermissions(UserRole.SUPPORT_STAFF);
      expect(permissions.canManageUsers).toBe(false);
      expect(permissions.canManageInstitutes).toBe(false);
      expect(permissions.canManageBranches).toBe(false);
      expect(permissions.canViewAllTickets).toBe(false);
      expect(permissions.canManageTickets).toBe(true);
      expect(permissions.canViewReports).toBe(false);
      expect(permissions.canManageSystem).toBe(false);
    });

    it('should give students no permissions', () => {
      const permissions = getUserPermissions(UserRole.STUDENT);
      expect(permissions.canManageUsers).toBe(false);
      expect(permissions.canManageInstitutes).toBe(false);
      expect(permissions.canManageBranches).toBe(false);
      expect(permissions.canViewAllTickets).toBe(false);
      expect(permissions.canManageTickets).toBe(false);
      expect(permissions.canViewReports).toBe(false);
      expect(permissions.canManageSystem).toBe(false);
    });
  });
});
