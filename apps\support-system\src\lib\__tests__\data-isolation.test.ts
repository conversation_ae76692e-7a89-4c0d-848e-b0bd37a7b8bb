import { DataIsolationService, IsolationContext } from '../data-isolation';
import { UserRole } from '@prisma/client';

describe('DataIsolationService', () => {
  const mockContexts: Record<string, IsolationContext> = {
    superAdmin: {
      userId: 'super-1',
      role: UserRole.SUPER_ADMIN,
    },
    instituteAdmin: {
      userId: 'admin-1',
      role: UserRole.INSTITUTE_ADMIN,
      instituteId: 'inst-1',
      branchId: 'branch-1',
    },
    supportStaff: {
      userId: 'staff-1',
      role: UserRole.SUPPORT_STAFF,
      instituteId: 'inst-1',
      branchId: 'branch-1',
    },
    student: {
      userId: 'student-1',
      role: UserRole.STUDENT,
      instituteId: 'inst-1',
      branchId: 'branch-1',
    },
    otherInstituteAdmin: {
      userId: 'admin-2',
      role: UserRole.INSTITUTE_ADMIN,
      instituteId: 'inst-2',
      branchId: 'branch-2',
    },
  };

  const mockData = {
    users: [
      { id: 'user-1', instituteId: 'inst-1', branchId: 'branch-1' },
      { id: 'user-2', instituteId: 'inst-2', branchId: 'branch-2' },
    ],
    institutes: [
      { id: 'inst-1', name: 'Institute 1' },
      { id: 'inst-2', name: 'Institute 2' },
    ],
    branches: [
      { id: 'branch-1', instituteId: 'inst-1' },
      { id: 'branch-2', instituteId: 'inst-2' },
    ],
    tickets: [
      { id: 'ticket-1', createdBy: 'student-1', assignedTo: 'staff-1', instituteId: 'inst-1', branchId: 'branch-1' },
      { id: 'ticket-2', createdBy: 'student-2', assignedTo: null, instituteId: 'inst-2', branchId: 'branch-2' },
    ],
    media: [
      { id: 'media-1', uploadedBy: 'staff-1', instituteId: 'inst-1', isPublic: true },
      { id: 'media-2', uploadedBy: 'staff-2', instituteId: 'inst-2', isPublic: false },
    ],
  };

  describe('applyIsolationFilter', () => {
    it('should allow super admin to access everything', () => {
      const context = mockContexts.superAdmin;
      const filter = DataIsolationService.applyIsolationFilter(context, {}, 'users');
      
      expect(filter).toEqual({});
    });

    it('should filter users by institute for institute admin', () => {
      const context = mockContexts.instituteAdmin;
      const filter = DataIsolationService.applyIsolationFilter(context, {}, 'users');
      
      expect(filter).toEqual({
        and: [
          {},
          {
            or: [
              { id: { equals: 'admin-1' } },
              { instituteId: { equals: 'inst-1' } },
            ],
          },
        ],
      });
    });

    it('should restrict support staff to own profile', () => {
      const context = mockContexts.supportStaff;
      const filter = DataIsolationService.applyIsolationFilter(context, {}, 'users');
      
      expect(filter).toEqual({
        and: [
          {},
          { id: { equals: 'staff-1' } },
        ],
      });
    });

    it('should filter institutes to user own institute', () => {
      const context = mockContexts.instituteAdmin;
      const filter = DataIsolationService.applyIsolationFilter(context, {}, 'institutes');
      
      expect(filter).toEqual({
        and: [
          {},
          { id: { equals: 'inst-1' } },
        ],
      });
    });

    it('should filter branches by institute', () => {
      const context = mockContexts.instituteAdmin;
      const filter = DataIsolationService.applyIsolationFilter(context, {}, 'branches');
      
      expect(filter).toEqual({
        and: [
          {},
          { instituteId: { equals: 'inst-1' } },
        ],
      });
    });

    it('should filter tickets for support staff', () => {
      const context = mockContexts.supportStaff;
      const filter = DataIsolationService.applyIsolationFilter(context, {}, 'support-tickets');
      
      expect(filter).toEqual({
        and: [
          {},
          {
            or: [
              { assignedTo: { equals: 'staff-1' } },
              { createdBy: { equals: 'staff-1' } },
              {
                and: [
                  { instituteId: { equals: 'inst-1' } },
                  { branchId: { equals: 'branch-1' } },
                ],
              },
            ],
          },
        ],
      });
    });

    it('should filter media for students', () => {
      const context = mockContexts.student;
      const filter = DataIsolationService.applyIsolationFilter(context, {}, 'media');
      
      expect(filter).toEqual({
        and: [
          {},
          {
            or: [
              { uploadedBy: { equals: 'student-1' } },
              {
                and: [
                  { instituteId: { equals: 'inst-1' } },
                  { isPublic: { equals: true } },
                ],
              },
            ],
          },
        ],
      });
    });
  });

  describe('canAccessResource', () => {
    it('should allow super admin to access any resource', () => {
      const context = mockContexts.superAdmin;
      
      expect(DataIsolationService.canAccessResource(context, 'user', mockData.users[0])).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'user', mockData.users[1])).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'institute', mockData.institutes[0])).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'institute', mockData.institutes[1])).toBe(true);
    });

    it('should allow institute admin to access users in their institute', () => {
      const context = mockContexts.instituteAdmin;
      
      expect(DataIsolationService.canAccessResource(context, 'user', mockData.users[0])).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'user', mockData.users[1])).toBe(false);
    });

    it('should allow users to access their own profile', () => {
      const context = mockContexts.supportStaff;
      const ownProfile = { id: 'staff-1', instituteId: 'inst-1' };
      const otherProfile = { id: 'other-user', instituteId: 'inst-1' };
      
      expect(DataIsolationService.canAccessResource(context, 'user', ownProfile)).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'user', otherProfile)).toBe(false);
    });

    it('should restrict institute access to own institute', () => {
      const context = mockContexts.instituteAdmin;
      
      expect(DataIsolationService.canAccessResource(context, 'institute', mockData.institutes[0])).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'institute', mockData.institutes[1])).toBe(false);
    });

    it('should allow access to branches in same institute', () => {
      const context = mockContexts.instituteAdmin;
      
      expect(DataIsolationService.canAccessResource(context, 'branch', mockData.branches[0])).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'branch', mockData.branches[1])).toBe(false);
    });

    it('should allow support staff to access assigned tickets', () => {
      const context = mockContexts.supportStaff;
      
      expect(DataIsolationService.canAccessResource(context, 'support-ticket', mockData.tickets[0])).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'support-ticket', mockData.tickets[1])).toBe(false);
    });

    it('should allow students to access their own tickets', () => {
      const context = mockContexts.student;
      const ownTicket = { id: 'ticket-1', createdBy: 'student-1', instituteId: 'inst-1' };
      const otherTicket = { id: 'ticket-2', createdBy: 'other-student', instituteId: 'inst-1' };
      
      expect(DataIsolationService.canAccessResource(context, 'support-ticket', ownTicket)).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'support-ticket', otherTicket)).toBe(false);
    });

    it('should allow access to public media in same institute', () => {
      const context = mockContexts.student;
      
      expect(DataIsolationService.canAccessResource(context, 'media', mockData.media[0])).toBe(true);
      expect(DataIsolationService.canAccessResource(context, 'media', mockData.media[1])).toBe(false);
    });
  });

  describe('canModifyResource', () => {
    it('should allow super admin to modify everything', () => {
      const context = mockContexts.superAdmin;
      
      expect(DataIsolationService.canModifyResource(context, 'user', mockData.users[0], 'create')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'user', mockData.users[0], 'update')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'user', mockData.users[0], 'delete')).toBe(true);
    });

    it('should allow institute admin to create users', () => {
      const context = mockContexts.instituteAdmin;
      
      expect(DataIsolationService.canModifyResource(context, 'user', mockData.users[0], 'create')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'user', mockData.users[0], 'update')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'user', mockData.users[0], 'delete')).toBe(false);
    });

    it('should allow support staff to create tickets', () => {
      const context = mockContexts.supportStaff;
      
      expect(DataIsolationService.canModifyResource(context, 'support-ticket', mockData.tickets[0], 'create')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'support-ticket', mockData.tickets[0], 'update')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'support-ticket', mockData.tickets[0], 'delete')).toBe(false);
    });

    it('should allow students to create tickets and media', () => {
      const context = mockContexts.student;
      
      expect(DataIsolationService.canModifyResource(context, 'support-ticket', mockData.tickets[0], 'create')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'support-ticket', mockData.tickets[0], 'update')).toBe(false);
      expect(DataIsolationService.canModifyResource(context, 'media', mockData.media[0], 'create')).toBe(true);
    });

    it('should allow users to update their own profile', () => {
      const context = mockContexts.supportStaff;
      const ownProfile = { id: 'staff-1', instituteId: 'inst-1' };
      
      expect(DataIsolationService.canModifyResource(context, 'user', ownProfile, 'update')).toBe(true);
    });

    it('should allow users to delete their own media uploads', () => {
      const context = mockContexts.supportStaff;
      const ownMedia = { id: 'media-1', uploadedBy: 'staff-1', instituteId: 'inst-1' };
      const otherMedia = { id: 'media-2', uploadedBy: 'other-user', instituteId: 'inst-1' };
      
      expect(DataIsolationService.canModifyResource(context, 'media', ownMedia, 'delete')).toBe(true);
      expect(DataIsolationService.canModifyResource(context, 'media', otherMedia, 'delete')).toBe(false);
    });
  });

  describe('createContext', () => {
    it('should create isolation context from user object', () => {
      const user = {
        id: 'user-123',
        role: UserRole.INSTITUTE_ADMIN,
        instituteId: 'inst-456',
        branchId: 'branch-789',
        lmsUserId: 'lms-user-123',
      };

      const context = DataIsolationService.createContext(user);

      expect(context).toEqual({
        userId: 'user-123',
        role: UserRole.INSTITUTE_ADMIN,
        instituteId: 'inst-456',
        branchId: 'branch-789',
        lmsUserId: 'lms-user-123',
      });
    });

    it('should handle undefined optional fields', () => {
      const user = {
        id: 'user-123',
        role: UserRole.STUDENT,
        instituteId: null,
        branchId: null,
        lmsUserId: null,
      };

      const context = DataIsolationService.createContext(user);

      expect(context).toEqual({
        userId: 'user-123',
        role: UserRole.STUDENT,
        instituteId: null,
        branchId: null,
        lmsUserId: null,
      });
    });
  });
});
