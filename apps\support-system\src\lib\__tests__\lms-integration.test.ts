import { mapLMSRoleToSupportRole, fetchUserFromLMS } from '../lms-integration';
import { UserRole } from '@prisma/client';

describe('LMS Integration', () => {
  describe('Role Mapping', () => {
    it('should map legacy super admin role', () => {
      const lmsUser = {
        id: 'test1',
        email: '<EMAIL>',
        legacyRole: 'super_admin',
        role: { permissions: [] },
        isActive: true,
      };

      const mappedRole = mapLMSRoleToSupportRole(lmsUser);
      expect(mappedRole).toBe(UserRole.SUPER_ADMIN);
    });

    it('should map super admin by permissions', () => {
      const lmsUser = {
        id: 'test2',
        email: '<EMAIL>',
        role: {
          permissions: [
            { code: 'manage_system' },
            { code: 'manage_all_institutes' },
          ],
        },
        isActive: true,
      };

      const mappedRole = mapLMSRoleToSupportRole(lmsUser);
      expect(mappedRole).toBe(UserRole.SUPER_ADMIN);
    });

    it('should map institute admin by permissions', () => {
      const lmsUser = {
        id: 'test3',
        email: '<EMAIL>',
        role: {
          permissions: [
            { code: 'manage_institute' },
            { code: 'manage_institute_users' },
          ],
        },
        isActive: true,
      };

      const mappedRole = mapLMSRoleToSupportRole(lmsUser);
      expect(mappedRole).toBe(UserRole.INSTITUTE_ADMIN);
    });

    it('should map support staff by permissions', () => {
      const lmsUser = {
        id: 'test4',
        email: '<EMAIL>',
        role: {
          permissions: [
            { code: 'manage_tickets' },
            { code: 'support_staff_access' },
          ],
        },
        isActive: true,
      };

      const mappedRole = mapLMSRoleToSupportRole(lmsUser);
      expect(mappedRole).toBe(UserRole.SUPPORT_STAFF);
    });

    it('should map tutor/trainer to support staff', () => {
      const lmsUser = {
        id: 'test5',
        email: '<EMAIL>',
        role: {
          permissions: [
            { code: 'tutor_access' },
            { code: 'trainer_access' },
          ],
        },
        isActive: true,
      };

      const mappedRole = mapLMSRoleToSupportRole(lmsUser);
      expect(mappedRole).toBe(UserRole.SUPPORT_STAFF);
    });

    it('should default to student for unknown permissions', () => {
      const lmsUser = {
        id: 'test6',
        email: '<EMAIL>',
        role: {
          permissions: [
            { code: 'view_courses' },
            { code: 'submit_assignments' },
          ],
        },
        isActive: true,
      };

      const mappedRole = mapLMSRoleToSupportRole(lmsUser);
      expect(mappedRole).toBe(UserRole.STUDENT);
    });

    it('should prioritize legacy role over permissions', () => {
      const lmsUser = {
        id: 'test7',
        email: '<EMAIL>',
        legacyRole: 'super_admin',
        role: {
          permissions: [
            { code: 'manage_tickets' }, // Would normally map to support staff
          ],
        },
        isActive: true,
      };

      const mappedRole = mapLMSRoleToSupportRole(lmsUser);
      expect(mappedRole).toBe(UserRole.SUPER_ADMIN);
    });
  });

  describe('User Fetching', () => {
    it('should fetch mock institute admin user', async () => {
      const user = await fetchUserFromLMS('lms_user_1');
      
      expect(user).toBeDefined();
      expect(user?.email).toBe('<EMAIL>');
      expect(user?.name).toBe('Institute Admin');
      expect(user?.instituteId).toBe('inst_1');
      expect(user?.branchId).toBe('branch_1');
      expect(user?.isActive).toBe(true);
    });

    it('should fetch mock support staff user', async () => {
      const user = await fetchUserFromLMS('lms_user_2');
      
      expect(user).toBeDefined();
      expect(user?.email).toBe('<EMAIL>');
      expect(user?.name).toBe('Support Staff');
      expect(user?.role.permissions).toContainEqual({ code: 'manage_tickets' });
    });

    it('should fetch mock super admin user', async () => {
      const user = await fetchUserFromLMS('lms_user_3');
      
      expect(user).toBeDefined();
      expect(user?.email).toBe('<EMAIL>');
      expect(user?.legacyRole).toBe('super_admin');
      expect(user?.instituteId).toBeUndefined();
      expect(user?.branchId).toBeUndefined();
    });

    it('should return null for non-existent user', async () => {
      const user = await fetchUserFromLMS('non_existent_user');
      expect(user).toBeNull();
    });
  });

  describe('Role Mapping Integration', () => {
    it('should correctly map fetched institute admin', async () => {
      const user = await fetchUserFromLMS('lms_user_1');
      expect(user).toBeDefined();
      
      if (user) {
        const mappedRole = mapLMSRoleToSupportRole(user);
        expect(mappedRole).toBe(UserRole.INSTITUTE_ADMIN);
      }
    });

    it('should correctly map fetched support staff', async () => {
      const user = await fetchUserFromLMS('lms_user_2');
      expect(user).toBeDefined();
      
      if (user) {
        const mappedRole = mapLMSRoleToSupportRole(user);
        expect(mappedRole).toBe(UserRole.SUPPORT_STAFF);
      }
    });

    it('should correctly map fetched super admin', async () => {
      const user = await fetchUserFromLMS('lms_user_3');
      expect(user).toBeDefined();
      
      if (user) {
        const mappedRole = mapLMSRoleToSupportRole(user);
        expect(mappedRole).toBe(UserRole.SUPER_ADMIN);
      }
    });
  });
});
