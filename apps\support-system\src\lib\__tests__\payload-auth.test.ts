// Mock NextAuth.js to avoid import issues in tests
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

jest.mock('../auth', () => ({
  authOptions: {},
}));

jest.mock('../prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
  },
}));

import {
  hasPermission,
  filterByAccess,
  validateInstituteAccess,
  getAccessibleInstitutes,
  getAccessibleBranches,
} from '../payload-auth';
import { UserRole } from '@prisma/client';

describe('Payload RBAC', () => {
  const mockUsers = {
    superAdmin: {
      id: 'super-1',
      email: '<EMAIL>',
      role: UserRole.SUPER_ADMIN,
      isActive: true,
    },
    instituteAdmin: {
      id: 'inst-1',
      email: '<EMAIL>',
      role: UserRole.INSTITUTE_ADMIN,
      instituteId: 'institute-1',
      branchId: 'branch-1',
      isActive: true,
    },
    supportStaff: {
      id: 'support-1',
      email: '<EMAIL>',
      role: UserRole.SUPPORT_STAFF,
      instituteId: 'institute-1',
      branchId: 'branch-1',
      isActive: true,
    },
    student: {
      id: 'student-1',
      email: '<EMAIL>',
      role: UserRole.STUDENT,
      instituteId: 'institute-1',
      branchId: 'branch-1',
      isActive: true,
    },
    inactiveUser: {
      id: 'inactive-1',
      email: '<EMAIL>',
      role: UserRole.SUPPORT_STAFF,
      instituteId: 'institute-1',
      isActive: false,
    },
  };

  describe('hasPermission', () => {
    it('should grant all permissions to super admin', () => {
      const user = mockUsers.superAdmin;
      
      expect(hasPermission(user, 'create', 'users')).toBe(true);
      expect(hasPermission(user, 'read', 'institutes')).toBe(true);
      expect(hasPermission(user, 'update', 'branches')).toBe(true);
      expect(hasPermission(user, 'delete', 'media')).toBe(true);
    });

    it('should grant appropriate permissions to institute admin', () => {
      const user = mockUsers.instituteAdmin;
      
      expect(hasPermission(user, 'create', 'users')).toBe(true);
      expect(hasPermission(user, 'read', 'institutes')).toBe(true);
      expect(hasPermission(user, 'update', 'branches')).toBe(true);
      expect(hasPermission(user, 'delete', 'support-tickets')).toBe(true);
      
      // Should not have delete permission for users
      expect(hasPermission(user, 'delete', 'users')).toBe(false);
    });

    it('should grant limited permissions to support staff', () => {
      const user = mockUsers.supportStaff;
      
      expect(hasPermission(user, 'read', 'users')).toBe(true);
      expect(hasPermission(user, 'create', 'support-tickets')).toBe(true);
      expect(hasPermission(user, 'update', 'support-tickets')).toBe(true);
      
      // Should not have create permission for users
      expect(hasPermission(user, 'create', 'users')).toBe(false);
      expect(hasPermission(user, 'delete', 'branches')).toBe(false);
    });

    it('should grant minimal permissions to students', () => {
      const user = mockUsers.student;
      
      expect(hasPermission(user, 'read', 'users')).toBe(true);
      expect(hasPermission(user, 'create', 'support-tickets')).toBe(true);
      expect(hasPermission(user, 'create', 'media')).toBe(true);
      
      // Should not have update permission for support tickets
      expect(hasPermission(user, 'update', 'support-tickets')).toBe(false);
      expect(hasPermission(user, 'create', 'users')).toBe(false);
    });

    it('should deny all permissions to inactive users', () => {
      const user = mockUsers.inactiveUser;
      
      expect(hasPermission(user, 'read', 'users')).toBe(false);
      expect(hasPermission(user, 'create', 'support-tickets')).toBe(false);
    });

    it('should deny permissions to null user', () => {
      expect(hasPermission(null, 'read', 'users')).toBe(false);
      expect(hasPermission(undefined, 'create', 'support-tickets')).toBe(false);
    });
  });

  describe('filterByAccess', () => {
    it('should return base query for super admin', () => {
      const user = mockUsers.superAdmin;
      const baseQuery = { status: 'active' };
      
      const result = filterByAccess(user, baseQuery);
      expect(result).toEqual(baseQuery);
    });

    it('should add institute filter for institute admin', () => {
      const user = mockUsers.instituteAdmin;
      const baseQuery = { status: 'active' };
      
      const result = filterByAccess(user, baseQuery);
      expect(result).toEqual({
        status: 'active',
        and: [
          baseQuery,
          {
            or: [
              { institute: { equals: 'institute-1' } },
              { instituteId: { equals: 'institute-1' } },
            ],
          },
        ],
      });
    });

    it('should return impossible condition for inactive user', () => {
      const user = mockUsers.inactiveUser;
      
      const result = filterByAccess(user);
      expect(result).toEqual({ id: { equals: 'never-match' } });
    });

    it('should return impossible condition for null user', () => {
      const result = filterByAccess(null);
      expect(result).toEqual({ id: { equals: 'never-match' } });
    });
  });

  describe('validateInstituteAccess', () => {
    it('should allow super admin to access any institute', () => {
      const user = mockUsers.superAdmin;
      
      expect(validateInstituteAccess(user, 'any-institute')).toBe(true);
      expect(validateInstituteAccess(user, 'another-institute', 'any-branch')).toBe(true);
    });

    it('should allow institute admin to access their institute', () => {
      const user = mockUsers.instituteAdmin;
      
      expect(validateInstituteAccess(user, 'institute-1')).toBe(true);
      expect(validateInstituteAccess(user, 'institute-1', 'any-branch')).toBe(true);
      expect(validateInstituteAccess(user, 'different-institute')).toBe(false);
    });

    it('should restrict support staff to their branch', () => {
      const user = mockUsers.supportStaff;
      
      expect(validateInstituteAccess(user, 'institute-1', 'branch-1')).toBe(true);
      expect(validateInstituteAccess(user, 'institute-1', 'different-branch')).toBe(false);
      expect(validateInstituteAccess(user, 'different-institute')).toBe(false);
    });

    it('should deny access to inactive users', () => {
      const user = mockUsers.inactiveUser;
      
      expect(validateInstituteAccess(user, 'institute-1')).toBe(false);
    });
  });

  describe('getAccessibleInstitutes', () => {
    it('should return all institutes marker for super admin', () => {
      const user = mockUsers.superAdmin;
      
      const result = getAccessibleInstitutes(user);
      expect(result).toEqual(['*']);
    });

    it('should return user institute for institute admin', () => {
      const user = mockUsers.instituteAdmin;
      
      const result = getAccessibleInstitutes(user);
      expect(result).toEqual(['institute-1']);
    });

    it('should return empty array for inactive user', () => {
      const user = mockUsers.inactiveUser;
      
      const result = getAccessibleInstitutes(user);
      expect(result).toEqual([]);
    });

    it('should return empty array for user without institute', () => {
      const user = { ...mockUsers.supportStaff, instituteId: null };
      
      const result = getAccessibleInstitutes(user);
      expect(result).toEqual([]);
    });
  });

  describe('getAccessibleBranches', () => {
    it('should return all branches marker for super admin', () => {
      const user = mockUsers.superAdmin;
      
      const result = getAccessibleBranches(user);
      expect(result).toEqual(['*']);
    });

    it('should return all branches marker for institute admin', () => {
      const user = mockUsers.instituteAdmin;
      
      const result = getAccessibleBranches(user);
      expect(result).toEqual(['*']);
    });

    it('should return user branch for support staff', () => {
      const user = mockUsers.supportStaff;
      
      const result = getAccessibleBranches(user);
      expect(result).toEqual(['branch-1']);
    });

    it('should return empty array for user without branch', () => {
      const user = { ...mockUsers.supportStaff, branchId: null };
      
      const result = getAccessibleBranches(user);
      expect(result).toEqual([]);
    });
  });
});
