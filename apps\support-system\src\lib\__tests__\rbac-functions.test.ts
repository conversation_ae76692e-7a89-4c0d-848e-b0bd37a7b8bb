import { UserRole } from '@prisma/client';

// Isolated RBAC functions for testing (without NextAuth.js dependencies)

/**
 * Check if user has permission for a specific action
 */
function hasPermission(
  user: any,
  action: 'create' | 'read' | 'update' | 'delete',
  resource: string,
  resourceData?: any
): boolean {
  if (!user || !user.isActive) {
    return false;
  }

  const userRole = user.role as UserRole;

  // Super admin has all permissions
  if (userRole === UserRole.SUPER_ADMIN) {
    return true;
  }

  // Define permissions by role and resource
  const permissions = {
    [UserRole.INSTITUTE_ADMIN]: {
      users: ['create', 'read', 'update'],
      institutes: ['read', 'update'],
      branches: ['create', 'read', 'update', 'delete'],
      media: ['create', 'read', 'update', 'delete'],
      'support-tickets': ['create', 'read', 'update', 'delete'],
    },
    [UserRole.SUPPORT_STAFF]: {
      users: ['read'], // Own profile only
      institutes: ['read'],
      branches: ['read'],
      media: ['create', 'read', 'update', 'delete'], // Own uploads only
      'support-tickets': ['create', 'read', 'update'],
    },
    [UserRole.STUDENT]: {
      users: ['read'], // Own profile only
      institutes: ['read'],
      branches: ['read'],
      media: ['create', 'read'], // Own uploads only
      'support-tickets': ['create', 'read'],
    },
  };

  const rolePermissions = permissions[userRole];
  if (!rolePermissions || !rolePermissions[resource]) {
    return false;
  }

  return rolePermissions[resource].includes(action);
}

/**
 * Filter query based on user's institute/branch access
 */
function filterByAccess(user: any, baseQuery: any = {}) {
  if (!user || !user.isActive) {
    return { id: { equals: 'never-match' } }; // Return impossible condition
  }

  const userRole = user.role as UserRole;

  // Super admin can access everything
  if (userRole === UserRole.SUPER_ADMIN) {
    return baseQuery;
  }

  // Institute-level filtering
  if ([UserRole.INSTITUTE_ADMIN, UserRole.SUPPORT_STAFF].includes(userRole)) {
    if (user.instituteId) {
      return {
        ...baseQuery,
        and: [
          baseQuery,
          {
            or: [
              { institute: { equals: user.instituteId } },
              { instituteId: { equals: user.instituteId } },
            ],
          },
        ],
      };
    }
  }

  // Default: no access
  return { id: { equals: 'never-match' } };
}

/**
 * Validate institute/branch access for data operations
 */
function validateInstituteAccess(
  user: any,
  targetInstituteId: string,
  targetBranchId?: string
): boolean {
  if (!user || !user.isActive) {
    return false;
  }

  const userRole = user.role as UserRole;

  // Super admin can access everything
  if (userRole === UserRole.SUPER_ADMIN) {
    return true;
  }

  // Check institute access
  if (user.instituteId !== targetInstituteId) {
    return false;
  }

  // Institute admin can access all branches in their institute
  if (userRole === UserRole.INSTITUTE_ADMIN) {
    return true;
  }

  // Support staff and students need branch-level access
  if (targetBranchId && user.branchId !== targetBranchId) {
    return false;
  }

  return true;
}

/**
 * Get user's accessible institute IDs
 */
function getAccessibleInstitutes(user: any): string[] {
  if (!user || !user.isActive) {
    return [];
  }

  const userRole = user.role as UserRole;

  // Super admin can access all institutes
  if (userRole === UserRole.SUPER_ADMIN) {
    return ['*']; // Special marker for all institutes
  }

  // Others can only access their own institute
  if (user.instituteId) {
    return [user.instituteId];
  }

  return [];
}

/**
 * Get user's accessible branch IDs
 */
function getAccessibleBranches(user: any): string[] {
  if (!user || !user.isActive) {
    return [];
  }

  const userRole = user.role as UserRole;

  // Super admin can access all branches
  if (userRole === UserRole.SUPER_ADMIN) {
    return ['*']; // Special marker for all branches
  }

  // Institute admin can access all branches in their institute
  if (userRole === UserRole.INSTITUTE_ADMIN) {
    return ['*']; // Will be filtered by institute in queries
  }

  // Others can only access their own branch
  if (user.branchId) {
    return [user.branchId];
  }

  return [];
}

describe('RBAC Functions', () => {
  const mockUsers = {
    superAdmin: {
      id: 'super-1',
      email: '<EMAIL>',
      role: UserRole.SUPER_ADMIN,
      isActive: true,
    },
    instituteAdmin: {
      id: 'inst-1',
      email: '<EMAIL>',
      role: UserRole.INSTITUTE_ADMIN,
      instituteId: 'institute-1',
      branchId: 'branch-1',
      isActive: true,
    },
    supportStaff: {
      id: 'support-1',
      email: '<EMAIL>',
      role: UserRole.SUPPORT_STAFF,
      instituteId: 'institute-1',
      branchId: 'branch-1',
      isActive: true,
    },
    student: {
      id: 'student-1',
      email: '<EMAIL>',
      role: UserRole.STUDENT,
      instituteId: 'institute-1',
      branchId: 'branch-1',
      isActive: true,
    },
    inactiveUser: {
      id: 'inactive-1',
      email: '<EMAIL>',
      role: UserRole.SUPPORT_STAFF,
      instituteId: 'institute-1',
      isActive: false,
    },
  };

  describe('hasPermission', () => {
    it('should grant all permissions to super admin', () => {
      const user = mockUsers.superAdmin;
      
      expect(hasPermission(user, 'create', 'users')).toBe(true);
      expect(hasPermission(user, 'read', 'institutes')).toBe(true);
      expect(hasPermission(user, 'update', 'branches')).toBe(true);
      expect(hasPermission(user, 'delete', 'media')).toBe(true);
    });

    it('should grant appropriate permissions to institute admin', () => {
      const user = mockUsers.instituteAdmin;
      
      expect(hasPermission(user, 'create', 'users')).toBe(true);
      expect(hasPermission(user, 'read', 'institutes')).toBe(true);
      expect(hasPermission(user, 'update', 'branches')).toBe(true);
      expect(hasPermission(user, 'delete', 'support-tickets')).toBe(true);
      
      // Should not have delete permission for users
      expect(hasPermission(user, 'delete', 'users')).toBe(false);
    });

    it('should grant limited permissions to support staff', () => {
      const user = mockUsers.supportStaff;
      
      expect(hasPermission(user, 'read', 'users')).toBe(true);
      expect(hasPermission(user, 'create', 'support-tickets')).toBe(true);
      expect(hasPermission(user, 'update', 'support-tickets')).toBe(true);
      
      // Should not have create permission for users
      expect(hasPermission(user, 'create', 'users')).toBe(false);
      expect(hasPermission(user, 'delete', 'branches')).toBe(false);
    });

    it('should grant minimal permissions to students', () => {
      const user = mockUsers.student;
      
      expect(hasPermission(user, 'read', 'users')).toBe(true);
      expect(hasPermission(user, 'create', 'support-tickets')).toBe(true);
      expect(hasPermission(user, 'create', 'media')).toBe(true);
      
      // Should not have update permission for support tickets
      expect(hasPermission(user, 'update', 'support-tickets')).toBe(false);
      expect(hasPermission(user, 'create', 'users')).toBe(false);
    });

    it('should deny all permissions to inactive users', () => {
      const user = mockUsers.inactiveUser;
      
      expect(hasPermission(user, 'read', 'users')).toBe(false);
      expect(hasPermission(user, 'create', 'support-tickets')).toBe(false);
    });
  });

  describe('validateInstituteAccess', () => {
    it('should allow super admin to access any institute', () => {
      const user = mockUsers.superAdmin;
      
      expect(validateInstituteAccess(user, 'any-institute')).toBe(true);
      expect(validateInstituteAccess(user, 'another-institute', 'any-branch')).toBe(true);
    });

    it('should allow institute admin to access their institute', () => {
      const user = mockUsers.instituteAdmin;
      
      expect(validateInstituteAccess(user, 'institute-1')).toBe(true);
      expect(validateInstituteAccess(user, 'institute-1', 'any-branch')).toBe(true);
      expect(validateInstituteAccess(user, 'different-institute')).toBe(false);
    });

    it('should restrict support staff to their branch', () => {
      const user = mockUsers.supportStaff;
      
      expect(validateInstituteAccess(user, 'institute-1', 'branch-1')).toBe(true);
      expect(validateInstituteAccess(user, 'institute-1', 'different-branch')).toBe(false);
      expect(validateInstituteAccess(user, 'different-institute')).toBe(false);
    });
  });

  describe('getAccessibleInstitutes', () => {
    it('should return all institutes marker for super admin', () => {
      const user = mockUsers.superAdmin;
      
      const result = getAccessibleInstitutes(user);
      expect(result).toEqual(['*']);
    });

    it('should return user institute for institute admin', () => {
      const user = mockUsers.instituteAdmin;
      
      const result = getAccessibleInstitutes(user);
      expect(result).toEqual(['institute-1']);
    });

    it('should return empty array for inactive user', () => {
      const user = mockUsers.inactiveUser;
      
      const result = getAccessibleInstitutes(user);
      expect(result).toEqual([]);
    });
  });
});
