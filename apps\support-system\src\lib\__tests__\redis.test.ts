// Mock <PERSON>is to avoid connection issues in tests
jest.mock('ioredis', () => {
  const mockRedis = {
    get: jest.fn(),
    set: jest.fn(),
    setex: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    mget: jest.fn(),
    mset: jest.fn(),
    incr: jest.fn(),
    sadd: jest.fn(),
    smembers: jest.fn(),
    sismember: jest.fn(),
    srem: jest.fn(),
    keys: jest.fn(),
    info: jest.fn(),
    ping: jest.fn(),
    pipeline: jest.fn(() => ({
      zremrangebyscore: jest.fn(),
      zcard: jest.fn(),
      zadd: jest.fn(),
      expire: jest.fn(),
      incr: jest.fn(),
      exec: jest.fn(),
    })),
    on: jest.fn(),
    disconnect: jest.fn(),
  };

  return jest.fn(() => mockRedis);
});

import { CacheService, RateLimiter, SessionCache } from '../redis';

describe('Redis Services', () => {
  let cacheService: CacheService;
  let rateLimiter: RateLimiter;
  let sessionCache: SessionCache;

  beforeEach(() => {
    jest.clearAllMocks();
    cacheService = new CacheService();
    rateLimiter = new RateLimiter();
    sessionCache = new SessionCache();
  });

  describe('CacheService', () => {
    it('should set and get values', async () => {
      const mockRedis = (cacheService as any).redis;
      mockRedis.get.mockResolvedValue(JSON.stringify({ test: 'data' }));

      const result = await cacheService.get('test-key');
      expect(result).toEqual({ test: 'data' });
      expect(mockRedis.get).toHaveBeenCalledWith('test-key');
    });

    it('should set values with TTL', async () => {
      const mockRedis = (cacheService as any).redis;
      
      await cacheService.set('test-key', { test: 'data' }, 300);
      
      expect(mockRedis.setex).toHaveBeenCalledWith(
        'test-key',
        300,
        JSON.stringify({ test: 'data' })
      );
    });

    it('should set values without TTL', async () => {
      const mockRedis = (cacheService as any).redis;
      
      await cacheService.set('test-key', { test: 'data' });
      
      expect(mockRedis.set).toHaveBeenCalledWith(
        'test-key',
        JSON.stringify({ test: 'data' })
      );
    });

    it('should delete keys', async () => {
      const mockRedis = (cacheService as any).redis;
      
      await cacheService.del('test-key');
      
      expect(mockRedis.del).toHaveBeenCalledWith('test-key');
    });

    it('should check if key exists', async () => {
      const mockRedis = (cacheService as any).redis;
      mockRedis.exists.mockResolvedValue(1);
      
      const result = await cacheService.exists('test-key');
      
      expect(result).toBe(true);
      expect(mockRedis.exists).toHaveBeenCalledWith('test-key');
    });

    it('should increment counters', async () => {
      const mockRedis = (cacheService as any).redis;
      mockRedis.incr.mockResolvedValue(5);
      
      const result = await cacheService.incr('counter-key');
      
      expect(result).toBe(5);
      expect(mockRedis.incr).toHaveBeenCalledWith('counter-key');
    });

    it('should handle set operations', async () => {
      const mockRedis = (cacheService as any).redis;
      mockRedis.sadd.mockResolvedValue(1);
      mockRedis.smembers.mockResolvedValue(['member1', 'member2']);
      mockRedis.sismember.mockResolvedValue(1);
      
      await cacheService.sadd('set-key', 'member1');
      const members = await cacheService.smembers('set-key');
      const isMember = await cacheService.sismember('set-key', 'member1');
      
      expect(mockRedis.sadd).toHaveBeenCalledWith('set-key', 'member1');
      expect(members).toEqual(['member1', 'member2']);
      expect(isMember).toBe(true);
    });

    it('should return null for non-existent keys', async () => {
      const mockRedis = (cacheService as any).redis;
      mockRedis.get.mockResolvedValue(null);
      
      const result = await cacheService.get('non-existent');
      
      expect(result).toBeNull();
    });

    it('should handle JSON parsing errors gracefully', async () => {
      const mockRedis = (cacheService as any).redis;
      mockRedis.get.mockResolvedValue('invalid-json');
      
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const result = await cacheService.get('test-key');
      
      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Error parsing cached value:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('RateLimiter', () => {
    it('should allow requests within limit', async () => {
      const mockRedis = (rateLimiter as any).redis;
      const mockPipeline = {
        zremrangebyscore: jest.fn(),
        zcard: jest.fn(),
        zadd: jest.fn(),
        expire: jest.fn(),
        exec: jest.fn().mockResolvedValue([
          [null, 0], // zremrangebyscore result
          [null, 2], // zcard result (current count)
          [null, 1], // zadd result
          [null, 1], // expire result
        ]),
      };
      
      mockRedis.pipeline.mockReturnValue(mockPipeline);
      
      const result = await rateLimiter.checkLimit('test-key', 5, 60);
      
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(2); // 5 - 2 - 1 = 2
      expect(typeof result.resetTime).toBe('number');
    });

    it('should deny requests over limit', async () => {
      const mockRedis = (rateLimiter as any).redis;
      const mockPipeline = {
        zremrangebyscore: jest.fn(),
        zcard: jest.fn(),
        zadd: jest.fn(),
        expire: jest.fn(),
        exec: jest.fn().mockResolvedValue([
          [null, 0], // zremrangebyscore result
          [null, 5], // zcard result (current count at limit)
          [null, 1], // zadd result
          [null, 1], // expire result
        ]),
      };
      
      mockRedis.pipeline.mockReturnValue(mockPipeline);
      
      const result = await rateLimiter.checkLimit('test-key', 5, 60);
      
      expect(result.allowed).toBe(false);
      expect(result.remaining).toBe(0);
    });

    it('should reset rate limits', async () => {
      const mockRedis = (rateLimiter as any).redis;
      
      await rateLimiter.resetLimit('test-key');
      
      expect(mockRedis.del).toHaveBeenCalledWith('rate_limit:test-key');
    });
  });

  describe('SessionCache', () => {
    it('should store and retrieve session data', async () => {
      const mockCache = (sessionCache as any).cache;
      mockCache.set = jest.fn();
      mockCache.get = jest.fn().mockResolvedValue({ userId: '123' });
      
      await sessionCache.setSessionData('session-123', { userId: '123' });
      const result = await sessionCache.getSessionData('session-123');
      
      expect(mockCache.set).toHaveBeenCalledWith('session:session-123', { userId: '123' }, 3600);
      expect(result).toEqual({ userId: '123' });
    });

    it('should manage active sessions', async () => {
      const mockCache = (sessionCache as any).cache;
      mockCache.sadd = jest.fn();
      mockCache.expire = jest.fn();
      mockCache.smembers = jest.fn().mockResolvedValue(['session-1', 'session-2']);
      mockCache.srem = jest.fn();
      
      await sessionCache.addActiveSession('user-123', 'session-1');
      const sessions = await sessionCache.getActiveSessions('user-123');
      await sessionCache.removeActiveSession('user-123', 'session-1');
      
      expect(mockCache.sadd).toHaveBeenCalledWith('user_sessions:user-123', 'session-1');
      expect(sessions).toEqual(['session-1', 'session-2']);
      expect(mockCache.srem).toHaveBeenCalledWith('user_sessions:user-123', 'session-1');
    });

    it('should invalidate all user sessions', async () => {
      const mockCache = (sessionCache as any).cache;
      mockCache.smembers = jest.fn().mockResolvedValue(['session-1', 'session-2']);
      mockCache.del = jest.fn();
      
      await sessionCache.invalidateUserSessions('user-123');
      
      expect(mockCache.del).toHaveBeenCalledWith('session:session-1');
      expect(mockCache.del).toHaveBeenCalledWith('session:session-2');
      expect(mockCache.del).toHaveBeenCalledWith('user_sessions:user-123');
    });
  });
});
