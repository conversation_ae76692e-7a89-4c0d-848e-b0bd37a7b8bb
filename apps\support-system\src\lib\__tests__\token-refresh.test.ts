// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  getSession: jest.fn(),
  signOut: jest.fn(),
}));

// Mock Redis
jest.mock('../redis', () => ({
  sessionCache: {
    setSessionData: jest.fn(),
    getSessionData: jest.fn(),
    removeActiveSession: jest.fn(),
    addActiveSession: jest.fn(),
    invalidateUserSessions: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

import { TokenRefreshService, defaultRefreshConfig } from '../token-refresh';
import { getSession, signOut } from 'next-auth/react';

describe('TokenRefreshService', () => {
  let tokenRefreshService: TokenRefreshService;
  const mockGetSession = getSession as jest.MockedFunction<typeof getSession>;
  const mockSignOut = signOut as jest.MockedFunction<typeof signOut>;
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    jest.clearAllMocks();
    tokenRefreshService = TokenRefreshService.getInstance();
  });

  afterEach(() => {
    tokenRefreshService.stopRefreshMonitoring();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = TokenRefreshService.getInstance();
      const instance2 = TokenRefreshService.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('checkAndRefreshToken', () => {
    it('should return false if no session exists', async () => {
      mockGetSession.mockResolvedValue(null);
      
      const result = await tokenRefreshService.checkAndRefreshToken();
      
      expect(result).toBe(false);
      expect(mockGetSession).toHaveBeenCalled();
    });

    it('should return true if token does not need refresh', async () => {
      const mockSession = {
        user: { id: 'user-1', email: '<EMAIL>' },
        tokenExp: Math.floor((Date.now() + 30 * 60 * 1000) / 1000), // 30 minutes from now
        lastRefresh: Date.now() - 60 * 1000, // 1 minute ago
      };
      
      mockGetSession.mockResolvedValue(mockSession as any);
      
      const result = await tokenRefreshService.checkAndRefreshToken();
      
      expect(result).toBe(true);
    });

    it('should refresh token if close to expiry', async () => {
      const mockSession = {
        user: { id: 'user-1', email: '<EMAIL>' },
        tokenExp: Math.floor((Date.now() + 2 * 60 * 1000) / 1000), // 2 minutes from now
        lastRefresh: Date.now() - 60 * 1000, // 1 minute ago
      };
      
      mockGetSession.mockResolvedValue(mockSession as any);
      
      // Mock successful refresh
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ user: mockSession.user }),
      } as Response);
      
      const result = await tokenRefreshService.checkAndRefreshToken();
      
      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith('/api/auth/session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ trigger: 'update' }),
      });
    });

    it('should refresh token if last refresh was over 24 hours ago', async () => {
      const mockSession = {
        user: { id: 'user-1', email: '<EMAIL>' },
        tokenExp: Math.floor((Date.now() + 30 * 60 * 1000) / 1000), // 30 minutes from now
        lastRefresh: Date.now() - 25 * 60 * 60 * 1000, // 25 hours ago
      };
      
      mockGetSession.mockResolvedValue(mockSession as any);
      
      // Mock successful refresh
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ user: mockSession.user }),
      } as Response);
      
      const result = await tokenRefreshService.checkAndRefreshToken();
      
      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalled();
    });
  });

  describe('refreshToken', () => {
    it('should successfully refresh token', async () => {
      const mockUpdatedSession = {
        user: { id: 'user-1', email: '<EMAIL>' },
        lastRefresh: Date.now(),
      };
      
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockUpdatedSession),
      } as Response);
      
      const result = await tokenRefreshService.refreshToken();
      
      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith('/api/auth/session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ trigger: 'update' }),
      });
    });

    it('should handle refresh failure', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 401,
      } as Response);

      mockGetSession.mockResolvedValue({
        user: { id: 'user-1' }
      } as any);

      const result = await tokenRefreshService.refreshToken();

      expect(result).toBe(false);
      expect(mockSignOut).toHaveBeenCalledWith({
        callbackUrl: '/auth/signin?error=SessionExpired',
        redirect: true,
      });
    }, 10000);

    it('should retry on failure', async () => {
      // First call fails
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
      } as Response);
      
      // Second call succeeds
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ user: { id: 'user-1' } }),
      } as Response);
      
      const result = await tokenRefreshService.refreshToken();
      
      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('should sign out after max retries', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
      } as Response);

      mockGetSession.mockResolvedValue({
        user: { id: 'user-1' }
      } as any);

      const result = await tokenRefreshService.refreshToken();

      expect(result).toBe(false);
      expect(mockFetch).toHaveBeenCalledTimes(defaultRefreshConfig.maxRetries + 1);
      expect(mockSignOut).toHaveBeenCalled();
    }, 10000);
  });

  describe('getSessionInfo', () => {
    it('should return null if no session exists', async () => {
      mockGetSession.mockResolvedValue(null);
      
      const result = await tokenRefreshService.getSessionInfo();
      
      expect(result).toBeNull();
    });

    it('should return session information', async () => {
      const now = Date.now();
      const tokenExp = Math.floor((now + 30 * 60 * 1000) / 1000); // 30 minutes from now
      const expiresAt = tokenExp * 1000;
      const lastRefresh = now - 60 * 1000; // 1 minute ago

      const mockSession = {
        user: { id: 'user-1', email: '<EMAIL>' },
        tokenExp,
        lastRefresh,
      };

      mockGetSession.mockResolvedValue(mockSession as any);

      const result = await tokenRefreshService.getSessionInfo();

      expect(result).toEqual({
        isValid: true,
        expiresAt,
        lastRefresh,
        timeUntilExpiry: expect.any(Number),
        needsRefresh: false,
      });
    });

    it('should indicate when refresh is needed', async () => {
      const now = Date.now();
      const expiresAt = now + 2 * 60 * 1000; // 2 minutes from now (less than 5 minute threshold)
      
      const mockSession = {
        user: { id: 'user-1', email: '<EMAIL>' },
        tokenExp: Math.floor(expiresAt / 1000),
        lastRefresh: now - 60 * 1000,
      };
      
      mockGetSession.mockResolvedValue(mockSession as any);
      
      const result = await tokenRefreshService.getSessionInfo();
      
      expect(result?.needsRefresh).toBe(true);
    });
  });

  describe('validateSession', () => {
    it('should return false if no session exists', async () => {
      mockGetSession.mockResolvedValue(null);
      
      const result = await tokenRefreshService.validateSession();
      
      expect(result).toBe(false);
    });

    it('should return true for valid session', async () => {
      const mockSession = {
        user: { id: 'user-1', email: '<EMAIL>' },
        tokenExp: Math.floor((Date.now() + 30 * 60 * 1000) / 1000), // 30 minutes from now
        lastRefresh: Date.now() - 60 * 1000, // 1 minute ago
      };
      
      mockGetSession.mockResolvedValue(mockSession as any);
      
      const result = await tokenRefreshService.validateSession();
      
      expect(result).toBe(true);
    });

    it('should handle expired session', async () => {
      const mockSession = {
        user: { id: 'user-1', email: '<EMAIL>' },
        tokenExp: Math.floor((Date.now() - 60 * 1000) / 1000), // 1 minute ago (expired)
        lastRefresh: Date.now() - 2 * 60 * 1000, // 2 minutes ago
      };
      
      mockGetSession.mockResolvedValue(mockSession as any);
      
      const result = await tokenRefreshService.validateSession();
      
      expect(result).toBe(false);
      expect(mockSignOut).toHaveBeenCalled();
    });
  });

  describe('monitoring', () => {
    it('should start and stop monitoring', async () => {
      const mockSession = {
        user: { id: 'user-1', email: '<EMAIL>' },
        tokenExp: Math.floor((Date.now() + 30 * 60 * 1000) / 1000),
        lastRefresh: Date.now() - 60 * 1000,
      };
      
      mockGetSession.mockResolvedValue(mockSession as any);
      
      await tokenRefreshService.startRefreshMonitoring();
      
      // Verify initial check was called
      expect(mockGetSession).toHaveBeenCalled();
      
      tokenRefreshService.stopRefreshMonitoring();
      
      // Should not throw any errors
    });
  });
});
