import { NextRequest, NextResponse } from 'next/server';
import { cacheService } from './redis';

export interface CacheConfig {
  ttl: number; // Time to live in seconds
  keyPrefix?: string;
  keyGenerator?: (req: NextRequest) => string;
  shouldCache?: (req: NextRequest, res: NextResponse) => boolean;
  varyBy?: string[]; // Headers to vary cache by
}

/**
 * Generate cache key from request
 */
function generateCacheKey(req: NextRequest, config: CacheConfig): string {
  if (config.keyGenerator) {
    return config.keyGenerator(req);
  }

  const url = new URL(req.url);
  const baseKey = `${req.method}:${url.pathname}${url.search}`;
  
  // Add vary headers to key
  if (config.varyBy) {
    const varyValues = config.varyBy
      .map(header => `${header}:${req.headers.get(header) || ''}`)
      .join('|');
    return `${config.keyPrefix || 'api'}:${baseKey}:${varyValues}`;
  }
  
  return `${config.keyPrefix || 'api'}:${baseKey}`;
}

/**
 * Cache middleware for API responses
 */
export function withCache<T extends any[]>(
  config: CacheConfig,
  handler: (req: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    // Only cache GET requests by default
    if (req.method !== 'GET') {
      return handler(req, ...args);
    }

    const cacheKey = generateCacheKey(req, config);

    try {
      // Try to get cached response
      const cached = await cacheService.get<{
        status: number;
        headers: Record<string, string>;
        body: any;
        timestamp: number;
      }>(cacheKey);

      if (cached) {
        // Return cached response
        const response = NextResponse.json(cached.body, {
          status: cached.status,
          headers: {
            ...cached.headers,
            'X-Cache': 'HIT',
            'X-Cache-Date': new Date(cached.timestamp).toISOString(),
          },
        });
        
        return response;
      }

      // Execute handler
      const response = await handler(req, ...args);

      // Check if we should cache this response
      if (config.shouldCache && !config.shouldCache(req, response)) {
        return response;
      }

      // Only cache successful responses
      if (response.status >= 200 && response.status < 300) {
        try {
          // Clone response to read body
          const responseClone = response.clone();
          const body = await responseClone.json();

          // Prepare cache data
          const cacheData = {
            status: response.status,
            headers: Object.fromEntries(response.headers.entries()),
            body,
            timestamp: Date.now(),
          };

          // Cache the response
          await cacheService.set(cacheKey, cacheData, config.ttl);

          // Add cache headers
          response.headers.set('X-Cache', 'MISS');
          response.headers.set('X-Cache-TTL', config.ttl.toString());
        } catch (error) {
          console.error('Error caching response:', error);
        }
      }

      return response;
    } catch (error) {
      console.error('Cache middleware error:', error);
      // If caching fails, continue with normal request
      return handler(req, ...args);
    }
  };
}

/**
 * Invalidate cache entries by pattern
 */
export async function invalidateCache(pattern: string): Promise<number> {
  try {
    return await cacheService.deletePattern(pattern);
  } catch (error) {
    console.error('Error invalidating cache:', error);
    return 0;
  }
}

/**
 * Invalidate cache for specific key
 */
export async function invalidateCacheKey(key: string): Promise<void> {
  try {
    await cacheService.del(key);
  } catch (error) {
    console.error('Error invalidating cache key:', error);
  }
}

/**
 * Cache configurations for different types of data
 */
export const cacheConfigs = {
  // User profile data (5 minutes)
  userProfile: {
    ttl: 300,
    keyPrefix: 'user_profile',
    keyGenerator: (req: NextRequest) => {
      const userId = req.headers.get('x-user-id');
      return `user_profile:${userId}`;
    },
  },

  // Institute data (15 minutes)
  institute: {
    ttl: 900,
    keyPrefix: 'institute',
    varyBy: ['x-user-institute-id'],
  },

  // Branch data (15 minutes)
  branch: {
    ttl: 900,
    keyPrefix: 'branch',
    varyBy: ['x-user-institute-id', 'x-user-branch-id'],
  },

  // Support tickets list (2 minutes)
  ticketsList: {
    ttl: 120,
    keyPrefix: 'tickets_list',
    varyBy: ['x-user-id', 'x-user-role', 'x-user-institute-id'],
  },

  // Support ticket details (5 minutes)
  ticketDetails: {
    ttl: 300,
    keyPrefix: 'ticket_details',
    keyGenerator: (req: NextRequest) => {
      const url = new URL(req.url);
      const ticketId = url.pathname.split('/').pop();
      const userId = req.headers.get('x-user-id');
      return `ticket_details:${ticketId}:user:${userId}`;
    },
  },

  // Static data (1 hour)
  static: {
    ttl: 3600,
    keyPrefix: 'static',
  },

  // Search results (10 minutes)
  search: {
    ttl: 600,
    keyPrefix: 'search',
    varyBy: ['x-user-id', 'x-user-role'],
  },

  // Reports (30 minutes)
  reports: {
    ttl: 1800,
    keyPrefix: 'reports',
    varyBy: ['x-user-id', 'x-user-role', 'x-user-institute-id'],
  },

  // Short-lived cache (1 minute)
  shortLived: {
    ttl: 60,
    keyPrefix: 'short',
  },

  // Long-lived cache (24 hours)
  longLived: {
    ttl: 86400,
    keyPrefix: 'long',
  },
};

/**
 * Cache invalidation patterns
 */
export const cacheInvalidationPatterns = {
  userProfile: (userId: string) => `user_profile:${userId}*`,
  institute: (instituteId: string) => `institute:*institute-id:${instituteId}*`,
  branch: (branchId: string) => `branch:*branch-id:${branchId}*`,
  tickets: (userId?: string) => userId ? `tickets_*:*user-id:${userId}*` : 'tickets_*',
  search: () => 'search:*',
  reports: (instituteId?: string) => 
    instituteId ? `reports:*institute-id:${instituteId}*` : 'reports:*',
  all: () => '*',
};

/**
 * Utility to warm up cache
 */
export async function warmUpCache(
  key: string,
  data: any,
  ttl: number
): Promise<void> {
  try {
    await cacheService.set(key, data, ttl);
  } catch (error) {
    console.error('Error warming up cache:', error);
  }
}

/**
 * Utility to get cache statistics
 */
export async function getCacheStats(): Promise<{
  totalKeys: number;
  keysByPrefix: Record<string, number>;
}> {
  try {
    const allKeys = await cacheService.keys('*');
    const keysByPrefix: Record<string, number> = {};

    allKeys.forEach(key => {
      const prefix = key.split(':')[0];
      keysByPrefix[prefix] = (keysByPrefix[prefix] || 0) + 1;
    });

    return {
      totalKeys: allKeys.length,
      keysByPrefix,
    };
  } catch (error) {
    console.error('Error getting cache stats:', error);
    return {
      totalKeys: 0,
      keysByPrefix: {},
    };
  }
}
