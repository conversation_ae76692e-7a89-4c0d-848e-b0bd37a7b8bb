import { getSession, signOut } from 'next-auth/react';
import { tokenRefreshService } from './token-refresh';

export interface ApiRequestConfig {
  url: string;
  method?: string;
  headers?: Record<string, string>;
  body?: any;
  retries?: number;
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

/**
 * API Client with automatic token refresh
 */
export class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL = '', defaultHeaders: Record<string, string> = {}) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders,
    };
  }

  /**
   * Make an API request with automatic token refresh
   */
  async request<T = any>(config: ApiRequestConfig): Promise<ApiResponse<T>> {
    const { url, method = 'GET', headers = {}, body, retries = 1 } = config;
    
    // Get current session
    const session = await getSession();
    
    // Prepare headers
    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers,
    };

    // Add authorization header if session exists
    if (session?.user) {
      requestHeaders['Authorization'] = `Bearer ${session.user.id}`;
      requestHeaders['X-User-ID'] = session.user.id;
      requestHeaders['X-User-Role'] = session.user.role;
      
      if (session.user.instituteId) {
        requestHeaders['X-User-Institute-ID'] = session.user.instituteId;
      }
      
      if (session.user.branchId) {
        requestHeaders['X-User-Branch-ID'] = session.user.branchId;
      }
    }

    // Prepare request
    const requestConfig: RequestInit = {
      method,
      headers: requestHeaders,
    };

    if (body && method !== 'GET') {
      requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body);
    }

    try {
      // Make the request
      const response = await fetch(`${this.baseURL}${url}`, requestConfig);

      // Handle 401 Unauthorized - token might be expired
      if (response.status === 401 && retries > 0) {
        console.log('Received 401, attempting token refresh...');
        
        // Try to refresh the token
        const refreshSuccess = await tokenRefreshService.refreshToken();
        
        if (refreshSuccess) {
          // Retry the request with refreshed token
          return this.request({ ...config, retries: retries - 1 });
        } else {
          // Refresh failed, redirect to login
          await signOut({ callbackUrl: '/auth/signin?error=TokenExpired' });
          throw new Error('Authentication failed');
        }
      }

      // Handle other error statuses
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData
        );
      }

      // Parse response
      const data = await response.json();

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Network or other errors
      throw new ApiError(
        error instanceof Error ? error.message : 'Network error',
        0,
        { originalError: error }
      );
    }
  }

  /**
   * GET request
   */
  async get<T = any>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'GET', headers });
  }

  /**
   * POST request
   */
  async post<T = any>(
    url: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'POST', body, headers });
  }

  /**
   * PUT request
   */
  async put<T = any>(
    url: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'PUT', body, headers });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(
    url: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'PATCH', body, headers });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'DELETE', headers });
  }
}

/**
 * Custom API Error class
 */
export class ApiError extends Error {
  public status: number;
  public data: any;

  constructor(message: string, status: number, data: any = {}) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

/**
 * Request wrapper function for backward compatibility
 */
export async function apiRequest<T = any>(
  url: string,
  options: RequestInit & { retries?: number } = {}
): Promise<T> {
  const client = new ApiClient();
  const { retries = 1, ...requestOptions } = options;
  
  const response = await client.request<T>({
    url,
    method: requestOptions.method || 'GET',
    headers: requestOptions.headers as Record<string, string>,
    body: requestOptions.body,
    retries,
  });
  
  return response.data;
}

/**
 * Fetch wrapper with automatic token refresh
 */
export async function fetchWithAuth(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const session = await getSession();
  
  // Add auth headers
  const headers = new Headers(options.headers);
  
  if (session?.user) {
    headers.set('Authorization', `Bearer ${session.user.id}`);
    headers.set('X-User-ID', session.user.id);
    headers.set('X-User-Role', session.user.role);
    
    if (session.user.instituteId) {
      headers.set('X-User-Institute-ID', session.user.instituteId);
    }
    
    if (session.user.branchId) {
      headers.set('X-User-Branch-ID', session.user.branchId);
    }
  }

  const response = await fetch(url, {
    ...options,
    headers,
  });

  // Handle 401 - try to refresh token
  if (response.status === 401) {
    const refreshSuccess = await tokenRefreshService.refreshToken();
    
    if (refreshSuccess) {
      // Retry with refreshed session
      const newSession = await getSession();
      
      if (newSession?.user) {
        headers.set('Authorization', `Bearer ${newSession.user.id}`);
        headers.set('X-User-ID', newSession.user.id);
        headers.set('X-User-Role', newSession.user.role);
        
        if (newSession.user.instituteId) {
          headers.set('X-User-Institute-ID', newSession.user.instituteId);
        }
        
        if (newSession.user.branchId) {
          headers.set('X-User-Branch-ID', newSession.user.branchId);
        }
      }
      
      return fetch(url, { ...options, headers });
    } else {
      // Refresh failed, redirect to login
      await signOut({ callbackUrl: '/auth/signin?error=TokenExpired' });
      throw new Error('Authentication failed');
    }
  }

  return response;
}

/**
 * Create an API client instance
 */
export function createApiClient(baseURL?: string, defaultHeaders?: Record<string, string>) {
  return new ApiClient(baseURL, defaultHeaders);
}

// Default API client instance
export const apiClient = new ApiClient('/api');

// Export commonly used methods
export const { get, post, put, patch, delete: del } = apiClient;
