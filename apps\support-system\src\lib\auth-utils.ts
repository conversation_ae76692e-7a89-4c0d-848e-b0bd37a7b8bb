import bcrypt from 'bcryptjs';
import { UserRole } from '@prisma/client';
import { prisma } from './prisma';

/**
 * Hash a password using bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

/**
 * Verify a password against a hash
 */
export async function verifyPassword(
  password: string,
  hash: string
): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

/**
 * Create a new user with hashed password
 */
export async function createUser(data: {
  email: string;
  password: string;
  name?: string;
  role?: UserRole;
  instituteId?: string;
  branchId?: string;
  lmsUserId?: string;
}) {
  const hashedPassword = await hashPassword(data.password);

  return prisma.user.create({
    data: {
      ...data,
      password: hashedPassword,
      role: data.role || UserRole.SUPPORT_STAFF,
    },
  });
}

/**
 * Check if user has required role
 */
export function hasRole(userRole: UserRole, requiredRole: UserRole): boolean {
  const roleHierarchy = {
    [UserRole.SUPER_ADMIN]: 4,
    [UserRole.INSTITUTE_ADMIN]: 3,
    [UserRole.SUPPORT_STAFF]: 2,
    [UserRole.STUDENT]: 1,
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
}

/**
 * Check if user can access institute data
 */
export function canAccessInstitute(
  userRole: UserRole,
  userInstituteId: string | null,
  targetInstituteId: string
): boolean {
  // Super admin can access all institutes
  if (userRole === UserRole.SUPER_ADMIN) {
    return true;
  }

  // Other roles can only access their own institute
  return userInstituteId === targetInstituteId;
}

/**
 * Check if user can access branch data
 */
export function canAccessBranch(
  userRole: UserRole,
  userInstituteId: string | null,
  userBranchId: string | null,
  targetInstituteId: string,
  targetBranchId?: string
): boolean {
  // Super admin can access all branches
  if (userRole === UserRole.SUPER_ADMIN) {
    return true;
  }

  // Must be in the same institute
  if (userInstituteId !== targetInstituteId) {
    return false;
  }

  // Institute admin can access all branches in their institute
  if (userRole === UserRole.INSTITUTE_ADMIN) {
    return true;
  }

  // Other roles can only access their own branch
  return userBranchId === targetBranchId;
}

/**
 * Get user permissions based on role
 */
export function getUserPermissions(role: UserRole) {
  const permissions = {
    canManageUsers: false,
    canManageInstitutes: false,
    canManageBranches: false,
    canViewAllTickets: false,
    canManageTickets: false,
    canViewReports: false,
    canManageSystem: false,
  };

  switch (role) {
    case UserRole.SUPER_ADMIN:
      return {
        ...permissions,
        canManageUsers: true,
        canManageInstitutes: true,
        canManageBranches: true,
        canViewAllTickets: true,
        canManageTickets: true,
        canViewReports: true,
        canManageSystem: true,
      };

    case UserRole.INSTITUTE_ADMIN:
      return {
        ...permissions,
        canManageUsers: true, // Within institute
        canManageBranches: true, // Within institute
        canViewAllTickets: true, // Within institute
        canManageTickets: true,
        canViewReports: true, // Within institute
      };

    case UserRole.SUPPORT_STAFF:
      return {
        ...permissions,
        canManageTickets: true,
        canViewReports: false, // Limited reports
      };

    case UserRole.STUDENT:
      return {
        ...permissions,
        // Students have minimal permissions
      };

    default:
      return permissions;
  }
}
