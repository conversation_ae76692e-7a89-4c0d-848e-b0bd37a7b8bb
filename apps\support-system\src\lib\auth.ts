import { NextAuthOptions } from 'next-auth';
import { PrismaAdapter } from '@auth/prisma-adapter';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
import { prisma } from './prisma';
import { UserRole } from '@prisma/client';
import { sessionCache } from './redis';

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email,
          },
        });

        if (!user || !user.password) {
          return null;
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        );

        if (!isPasswordValid) {
          return null;
        }

        if (!user.isActive) {
          return null;
        }

        // Update last login
        await prisma.user.update({
          where: { id: user.id },
          data: { lastLoginAt: new Date() },
        });

        return {
          id: user.id,
          email: user.email,
          name: user.name || undefined,
          role: user.role,
          instituteId: user.instituteId || undefined,
          branchId: user.branchId || undefined,
          lmsUserId: user.lmsUserId || undefined,
        };
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours - update session if older than this
  },
  jwt: {
    secret: process.env.NEXTAUTH_SECRET,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user, trigger }) {
      // Initial sign in
      if (user) {
        token.role = user.role;
        token.instituteId = user.instituteId;
        token.branchId = user.branchId;
        token.lmsUserId = user.lmsUserId;
        token.lastRefresh = Date.now();

        // Store session in Redis for tracking
        await sessionCache.addActiveSession(user.id, token.sub!);
      }

      // Check if token needs refresh (every 24 hours)
      const shouldRefresh = trigger === 'update' ||
        (token.lastRefresh && Date.now() - (token.lastRefresh as number) > 24 * 60 * 60 * 1000);

      if (shouldRefresh && token.sub) {
        try {
          // Fetch fresh user data from database
          const freshUser = await prisma.user.findUnique({
            where: { id: token.sub },
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
              instituteId: true,
              branchId: true,
              lmsUserId: true,
              isActive: true,
              lastLoginAt: true,
            },
          });

          if (freshUser && freshUser.isActive) {
            // Update token with fresh data
            token.role = freshUser.role;
            token.instituteId = freshUser.instituteId || undefined;
            token.branchId = freshUser.branchId || undefined;
            token.lmsUserId = freshUser.lmsUserId || undefined;
            token.lastRefresh = Date.now();

            // Update last login time
            await prisma.user.update({
              where: { id: token.sub },
              data: { lastLoginAt: new Date() },
            });
          } else {
            // User is inactive or deleted, mark token as invalid
            token.isValid = false;
          }
        } catch (error) {
          console.error('Error refreshing token:', error);
          // Return existing token on error to avoid breaking the session
        }
      }

      return token;
    },
    async session({ session, token }) {
      if (token && token.isValid !== false) {
        session.user.id = token.sub!;
        session.user.role = token.role as UserRole;
        session.user.instituteId = token.instituteId as string;
        session.user.branchId = token.branchId as string;
        session.user.lmsUserId = token.lmsUserId as string;

        // Add token metadata to session (extend session type)
        (session as any).lastRefresh = token.lastRefresh as number;
        (session as any).tokenExp = token.exp;

        return session;
      } else {
        // Token is invalid, force re-authentication by throwing error
        throw new Error('Invalid token');
      }
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  events: {
    async signIn({ user }) {
      console.log(`User ${user.email} signed in`);
    },
    async signOut({ token }) {
      console.log(`User ${token?.email} signed out`);

      // Clean up session from Redis
      if (token?.sub) {
        await sessionCache.removeActiveSession(token.sub, token.sub);
      }
    },
    async session({ token }) {
      // Update session activity in Redis
      if (token?.sub) {
        await sessionCache.setSessionData(token.sub, {
          lastActivity: Date.now(),
          userAgent: '', // Would be set from request
          ipAddress: '', // Would be set from request
        }, 30 * 24 * 60 * 60); // 30 days
      }
    },
  },
};
