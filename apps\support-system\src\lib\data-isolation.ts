import { UserRole } from '@prisma/client';
import { NextRequest } from 'next/server';

export interface IsolationContext {
  userId: string;
  role: UserRole;
  instituteId?: string;
  branchId?: string;
  lmsUserId?: string;
}

export interface QueryFilter {
  [key: string]: any;
}

/**
 * Data Isolation Service
 * Ensures users can only access data they're authorized to see
 */
export class DataIsolationService {
  /**
   * Apply data isolation filters to a query
   */
  static applyIsolationFilter(
    context: IsolationContext,
    baseQuery: QueryFilter = {},
    resourceType: string
  ): QueryFilter {
    const { role, instituteId, branchId, userId } = context;

    // Super admin can access everything
    if (role === UserRole.SUPER_ADMIN) {
      return baseQuery;
    }

    // Apply filters based on resource type and user role
    switch (resourceType) {
      case 'users':
        return this.applyUserIsolation(context, baseQuery);
      
      case 'institutes':
        return this.applyInstituteIsolation(context, baseQuery);
      
      case 'branches':
        return this.applyBranchIsolation(context, baseQuery);
      
      case 'support-tickets':
        return this.applyTicketIsolation(context, baseQuery);
      
      case 'media':
        return this.applyMediaIsolation(context, baseQuery);
      
      default:
        return this.applyDefaultIsolation(context, baseQuery);
    }
  }

  /**
   * Apply user data isolation
   */
  private static applyUserIsolation(
    context: IsolationContext,
    baseQuery: QueryFilter
  ): QueryFilter {
    const { role, instituteId, userId } = context;

    if (role === UserRole.INSTITUTE_ADMIN) {
      // Institute admins can see users in their institute
      return {
        ...baseQuery,
        and: [
          baseQuery,
          {
            or: [
              { id: { equals: userId } }, // Own profile
              { instituteId: { equals: instituteId } }, // Same institute
            ],
          },
        ],
      };
    }

    // Support staff and students can only see their own profile
    return {
      ...baseQuery,
      and: [
        baseQuery,
        { id: { equals: userId } },
      ],
    };
  }

  /**
   * Apply institute data isolation
   */
  private static applyInstituteIsolation(
    context: IsolationContext,
    baseQuery: QueryFilter
  ): QueryFilter {
    const { instituteId } = context;

    if (!instituteId) {
      // Users without institute can't see any institutes
      return { id: { equals: 'never-match' } };
    }

    // Users can only see their own institute
    return {
      ...baseQuery,
      and: [
        baseQuery,
        { id: { equals: instituteId } },
      ],
    };
  }

  /**
   * Apply branch data isolation
   */
  private static applyBranchIsolation(
    context: IsolationContext,
    baseQuery: QueryFilter
  ): QueryFilter {
    const { role, instituteId, branchId } = context;

    if (!instituteId) {
      return { id: { equals: 'never-match' } };
    }

    if (role === UserRole.INSTITUTE_ADMIN) {
      // Institute admins can see all branches in their institute
      return {
        ...baseQuery,
        and: [
          baseQuery,
          { instituteId: { equals: instituteId } },
        ],
      };
    }

    // Support staff and students can see branches in their institute
    return {
      ...baseQuery,
      and: [
        baseQuery,
        { instituteId: { equals: instituteId } },
      ],
    };
  }

  /**
   * Apply support ticket data isolation
   */
  private static applyTicketIsolation(
    context: IsolationContext,
    baseQuery: QueryFilter
  ): QueryFilter {
    const { role, instituteId, branchId, userId } = context;

    if (!instituteId) {
      return { id: { equals: 'never-match' } };
    }

    if (role === UserRole.INSTITUTE_ADMIN) {
      // Institute admins can see all tickets in their institute
      return {
        ...baseQuery,
        and: [
          baseQuery,
          { instituteId: { equals: instituteId } },
        ],
      };
    }

    if (role === UserRole.SUPPORT_STAFF) {
      // Support staff can see tickets assigned to them or in their branch
      return {
        ...baseQuery,
        and: [
          baseQuery,
          {
            or: [
              { assignedTo: { equals: userId } },
              { createdBy: { equals: userId } },
              {
                and: [
                  { instituteId: { equals: instituteId } },
                  { branchId: { equals: branchId } },
                ],
              },
            ],
          },
        ],
      };
    }

    // Students can only see their own tickets
    return {
      ...baseQuery,
      and: [
        baseQuery,
        { createdBy: { equals: userId } },
      ],
    };
  }

  /**
   * Apply media data isolation
   */
  private static applyMediaIsolation(
    context: IsolationContext,
    baseQuery: QueryFilter
  ): QueryFilter {
    const { role, instituteId, userId } = context;

    if (role === UserRole.INSTITUTE_ADMIN) {
      // Institute admins can see all media in their institute
      return {
        ...baseQuery,
        and: [
          baseQuery,
          {
            or: [
              { uploadedBy: { equals: userId } },
              { instituteId: { equals: instituteId } },
            ],
          },
        ],
      };
    }

    // Others can see their own uploads or public media in their institute
    return {
      ...baseQuery,
      and: [
        baseQuery,
        {
          or: [
            { uploadedBy: { equals: userId } },
            {
              and: [
                { instituteId: { equals: instituteId } },
                { isPublic: { equals: true } },
              ],
            },
          ],
        },
      ],
    };
  }

  /**
   * Apply default isolation (institute-based)
   */
  private static applyDefaultIsolation(
    context: IsolationContext,
    baseQuery: QueryFilter
  ): QueryFilter {
    const { instituteId } = context;

    if (!instituteId) {
      return { id: { equals: 'never-match' } };
    }

    return {
      ...baseQuery,
      and: [
        baseQuery,
        { instituteId: { equals: instituteId } },
      ],
    };
  }

  /**
   * Validate if user can access specific resource
   */
  static canAccessResource(
    context: IsolationContext,
    resourceType: string,
    resourceData: any
  ): boolean {
    const { role, instituteId, branchId, userId } = context;

    // Super admin can access everything
    if (role === UserRole.SUPER_ADMIN) {
      return true;
    }

    switch (resourceType) {
      case 'user':
        return this.canAccessUser(context, resourceData);
      
      case 'institute':
        return resourceData.id === instituteId;
      
      case 'branch':
        return this.canAccessBranch(context, resourceData);
      
      case 'support-ticket':
        return this.canAccessTicket(context, resourceData);
      
      case 'media':
        return this.canAccessMedia(context, resourceData);
      
      default:
        // Default: check institute ownership
        return resourceData.instituteId === instituteId;
    }
  }

  /**
   * Check if user can access another user's data
   */
  private static canAccessUser(
    context: IsolationContext,
    userData: any
  ): boolean {
    const { role, instituteId, userId } = context;

    // Users can always access their own data
    if (userData.id === userId) {
      return true;
    }

    // Institute admins can access users in their institute
    if (role === UserRole.INSTITUTE_ADMIN) {
      return userData.instituteId === instituteId;
    }

    return false;
  }

  /**
   * Check if user can access branch data
   */
  private static canAccessBranch(
    context: IsolationContext,
    branchData: any
  ): boolean {
    const { instituteId } = context;
    return branchData.instituteId === instituteId;
  }

  /**
   * Check if user can access ticket data
   */
  private static canAccessTicket(
    context: IsolationContext,
    ticketData: any
  ): boolean {
    const { role, instituteId, branchId, userId } = context;

    // Institute admins can access all tickets in their institute
    if (role === UserRole.INSTITUTE_ADMIN) {
      return ticketData.instituteId === instituteId;
    }

    // Support staff can access tickets assigned to them or in their branch
    if (role === UserRole.SUPPORT_STAFF) {
      return (
        ticketData.assignedTo === userId ||
        ticketData.createdBy === userId ||
        (ticketData.instituteId === instituteId && ticketData.branchId === branchId)
      );
    }

    // Students can only access their own tickets
    return ticketData.createdBy === userId;
  }

  /**
   * Check if user can access media
   */
  private static canAccessMedia(
    context: IsolationContext,
    mediaData: any
  ): boolean {
    const { role, instituteId, userId } = context;

    // Users can always access their own uploads
    if (mediaData.uploadedBy === userId) {
      return true;
    }

    // Institute admins can access all media in their institute
    if (role === UserRole.INSTITUTE_ADMIN) {
      return mediaData.instituteId === instituteId;
    }

    // Others can access public media in their institute
    return (
      mediaData.instituteId === instituteId &&
      mediaData.isPublic === true
    );
  }

  /**
   * Extract isolation context from request headers
   */
  static extractContextFromRequest(req: NextRequest): IsolationContext | null {
    const userId = req.headers.get('x-user-id');
    const role = req.headers.get('x-user-role') as UserRole;
    const instituteId = req.headers.get('x-user-institute-id');
    const branchId = req.headers.get('x-user-branch-id');
    const lmsUserId = req.headers.get('x-user-lms-id');

    if (!userId || !role) {
      return null;
    }

    return {
      userId,
      role,
      instituteId: instituteId || undefined,
      branchId: branchId || undefined,
      lmsUserId: lmsUserId || undefined,
    };
  }

  /**
   * Create isolation context from user object
   */
  static createContext(user: any): IsolationContext {
    return {
      userId: user.id,
      role: user.role,
      instituteId: user.instituteId,
      branchId: user.branchId,
      lmsUserId: user.lmsUserId,
    };
  }

  /**
   * Validate data modification permissions
   */
  static canModifyResource(
    context: IsolationContext,
    resourceType: string,
    resourceData: any,
    operation: 'create' | 'update' | 'delete'
  ): boolean {
    const { role } = context;

    // Super admin can modify everything
    if (role === UserRole.SUPER_ADMIN) {
      return true;
    }

    // Check if user can access the resource first
    if (!this.canAccessResource(context, resourceType, resourceData)) {
      return false;
    }

    // Apply operation-specific rules
    switch (operation) {
      case 'create':
        return this.canCreateResource(context, resourceType, resourceData);
      
      case 'update':
        return this.canUpdateResource(context, resourceType, resourceData);
      
      case 'delete':
        return this.canDeleteResource(context, resourceType, resourceData);
      
      default:
        return false;
    }
  }

  /**
   * Check create permissions
   */
  private static canCreateResource(
    context: IsolationContext,
    resourceType: string,
    resourceData: any
  ): boolean {
    const { role } = context;

    const createPermissions = {
      [UserRole.INSTITUTE_ADMIN]: ['user', 'branch', 'support-ticket', 'media'],
      [UserRole.SUPPORT_STAFF]: ['support-ticket', 'media'],
      [UserRole.STUDENT]: ['support-ticket', 'media'],
    };

    return createPermissions[role]?.includes(resourceType) || false;
  }

  /**
   * Check update permissions
   */
  private static canUpdateResource(
    context: IsolationContext,
    resourceType: string,
    resourceData: any
  ): boolean {
    const { role, userId } = context;

    // Users can always update their own profile
    if (resourceType === 'user' && resourceData.id === userId) {
      return true;
    }

    const updatePermissions = {
      [UserRole.INSTITUTE_ADMIN]: ['user', 'institute', 'branch', 'support-ticket', 'media'],
      [UserRole.SUPPORT_STAFF]: ['support-ticket', 'media'],
      [UserRole.STUDENT]: ['media'], // Only their own uploads
    };

    return updatePermissions[role]?.includes(resourceType) || false;
  }

  /**
   * Check delete permissions
   */
  private static canDeleteResource(
    context: IsolationContext,
    resourceType: string,
    resourceData: any
  ): boolean {
    const { role, userId } = context;

    // Users can delete their own uploads
    if (resourceType === 'media' && resourceData.uploadedBy === userId) {
      return true;
    }

    const deletePermissions = {
      [UserRole.INSTITUTE_ADMIN]: ['branch', 'support-ticket', 'media'],
      [UserRole.SUPPORT_STAFF]: ['media'], // Only their own uploads
      [UserRole.STUDENT]: ['media'], // Only their own uploads
    };

    return deletePermissions[role]?.includes(resourceType) || false;
  }
}
