import { NextRequest, NextResponse } from 'next/server';
import { DataIsolationService, IsolationContext } from './data-isolation';

export interface IsolationConfig {
  resourceType: string;
  enforceOnRead?: boolean;
  enforceOnWrite?: boolean;
  allowSuperAdminBypass?: boolean;
}

/**
 * Middleware to enforce data isolation on API routes
 */
export function withDataIsolation<T extends any[]>(
  config: IsolationConfig,
  handler: (req: NextRequest, context: IsolationContext, ...args: T) => Promise<NextResponse>
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      // Extract isolation context from request headers
      const context = DataIsolationService.extractContextFromRequest(req);

      if (!context) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Check if operation should be enforced
      const isReadOperation = req.method === 'GET';
      const isWriteOperation = ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method);

      const shouldEnforce = 
        (isReadOperation && config.enforceOnRead !== false) ||
        (isWriteOperation && config.enforceOnWrite !== false);

      if (!shouldEnforce) {
        return handler(req, context, ...args);
      }

      // Super admin bypass
      if (config.allowSuperAdminBypass !== false && context.role === 'SUPER_ADMIN') {
        return handler(req, context, ...args);
      }

      // Add isolation context to request for use in handler
      const requestWithContext = new Request(req.url, {
        method: req.method,
        headers: {
          ...Object.fromEntries(req.headers.entries()),
          'x-isolation-context': JSON.stringify(context),
        },
        body: req.body,
      });

      return handler(requestWithContext as NextRequest, context, ...args);
    } catch (error) {
      console.error('Data isolation middleware error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Utility to apply isolation filter to query parameters
 */
export function applyIsolationToQuery(
  context: IsolationContext,
  resourceType: string,
  baseQuery: any = {}
): any {
  return DataIsolationService.applyIsolationFilter(context, baseQuery, resourceType);
}

/**
 * Utility to validate resource access
 */
export function validateResourceAccess(
  context: IsolationContext,
  resourceType: string,
  resourceData: any
): boolean {
  return DataIsolationService.canAccessResource(context, resourceType, resourceData);
}

/**
 * Utility to validate modification permissions
 */
export function validateModificationPermission(
  context: IsolationContext,
  resourceType: string,
  resourceData: any,
  operation: 'create' | 'update' | 'delete'
): boolean {
  return DataIsolationService.canModifyResource(context, resourceType, resourceData, operation);
}

/**
 * Middleware decorator for class methods
 */
export function DataIsolation(config: IsolationConfig) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const req = args[0] as NextRequest;
      
      // Extract context
      const context = DataIsolationService.extractContextFromRequest(req);
      
      if (!context) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Add context as second parameter
      args.splice(1, 0, context);
      
      return method.apply(this, args);
    };
  };
}

/**
 * Utility to filter array of resources based on isolation rules
 */
export function filterResourcesByIsolation<T>(
  context: IsolationContext,
  resources: T[],
  resourceType: string,
  getResourceData: (resource: T) => any = (r) => r
): T[] {
  return resources.filter(resource => {
    const resourceData = getResourceData(resource);
    return DataIsolationService.canAccessResource(context, resourceType, resourceData);
  });
}

/**
 * Utility to validate bulk operations
 */
export function validateBulkOperation(
  context: IsolationContext,
  resourceType: string,
  resources: any[],
  operation: 'create' | 'update' | 'delete'
): { valid: any[]; invalid: any[] } {
  const valid: any[] = [];
  const invalid: any[] = [];

  resources.forEach(resource => {
    if (DataIsolationService.canModifyResource(context, resourceType, resource, operation)) {
      valid.push(resource);
    } else {
      invalid.push(resource);
    }
  });

  return { valid, invalid };
}

/**
 * Predefined isolation configurations
 */
export const isolationConfigs = {
  users: {
    resourceType: 'users',
    enforceOnRead: true,
    enforceOnWrite: true,
  },
  
  institutes: {
    resourceType: 'institutes',
    enforceOnRead: true,
    enforceOnWrite: true,
  },
  
  branches: {
    resourceType: 'branches',
    enforceOnRead: true,
    enforceOnWrite: true,
  },
  
  supportTickets: {
    resourceType: 'support-tickets',
    enforceOnRead: true,
    enforceOnWrite: true,
  },
  
  media: {
    resourceType: 'media',
    enforceOnRead: true,
    enforceOnWrite: true,
  },
  
  // Lenient config for public data
  public: {
    resourceType: 'public',
    enforceOnRead: false,
    enforceOnWrite: true,
    allowSuperAdminBypass: true,
  },
  
  // Strict config for sensitive data
  strict: {
    resourceType: 'sensitive',
    enforceOnRead: true,
    enforceOnWrite: true,
    allowSuperAdminBypass: false,
  },
};

/**
 * Helper to create isolation-aware API response
 */
export function createIsolatedResponse(
  context: IsolationContext,
  data: any,
  resourceType: string,
  options: {
    filterArrays?: boolean;
    includeMetadata?: boolean;
  } = {}
): any {
  const { filterArrays = true, includeMetadata = false } = options;

  // Filter arrays if requested
  if (filterArrays && Array.isArray(data)) {
    data = filterResourcesByIsolation(context, data, resourceType);
  }

  // Add metadata if requested
  if (includeMetadata) {
    return {
      data,
      metadata: {
        userId: context.userId,
        role: context.role,
        instituteId: context.instituteId,
        branchId: context.branchId,
        resourceType,
        filteredAt: new Date().toISOString(),
      },
    };
  }

  return data;
}

/**
 * Utility to log isolation violations
 */
export function logIsolationViolation(
  context: IsolationContext,
  resourceType: string,
  resourceData: any,
  operation: string,
  reason: string
): void {
  console.warn('Data isolation violation:', {
    userId: context.userId,
    role: context.role,
    instituteId: context.instituteId,
    branchId: context.branchId,
    resourceType,
    resourceId: resourceData?.id,
    operation,
    reason,
    timestamp: new Date().toISOString(),
  });
}

/**
 * Utility to create audit log entry
 */
export function createAuditLogEntry(
  context: IsolationContext,
  action: string,
  resourceType: string,
  resourceId: string,
  details?: any
): any {
  return {
    userId: context.userId,
    userRole: context.role,
    instituteId: context.instituteId,
    branchId: context.branchId,
    action,
    resourceType,
    resourceId,
    details,
    timestamp: new Date().toISOString(),
    ipAddress: null, // Would be set by calling code
    userAgent: null, // Would be set by calling code
  };
}
