import { UserRole } from '@prisma/client';
import { prisma } from './prisma';

/**
 * LMS User Role Mapping
 * Maps LMS roles to support system roles
 */
export interface LMSUser {
  id: string;
  email: string;
  name?: string;
  role: {
    permissions: Array<{ code: string }>;
  };
  legacyRole?: string;
  instituteId?: string;
  branchId?: string;
  isActive: boolean;
}

/**
 * Map LMS role to support system role
 */
export function mapLMSRoleToSupportRole(lmsUser: LMSUser): UserRole {
  // Check for legacy super admin role first
  if (lmsUser.legacyRole === 'super_admin') {
    return UserRole.SUPER_ADMIN;
  }

  // Check permissions for role mapping
  const permissions = lmsUser.role.permissions.map(p => p.code);

  // Super Admin: Has system-wide permissions
  if (
    permissions.includes('manage_system') ||
    permissions.includes('manage_all_institutes') ||
    permissions.includes('super_admin_access')
  ) {
    return UserRole.SUPER_ADMIN;
  }

  // Institute Admin: Has institute-level permissions
  if (
    permissions.includes('manage_institute') ||
    permissions.includes('manage_institute_users') ||
    permissions.includes('institute_admin_access') ||
    permissions.includes('manage_branches')
  ) {
    return UserRole.INSTITUTE_ADMIN;
  }

  // Support Staff: Has support-related permissions
  if (
    permissions.includes('manage_tickets') ||
    permissions.includes('support_staff_access') ||
    permissions.includes('handle_support_requests') ||
    permissions.includes('tutor_access') ||
    permissions.includes('trainer_access')
  ) {
    return UserRole.SUPPORT_STAFF;
  }

  // Default to student for all other users
  return UserRole.STUDENT;
}

/**
 * Sync user from LMS to support system
 */
export async function syncUserFromLMS(lmsUser: LMSUser): Promise<any> {
  const supportRole = mapLMSRoleToSupportRole(lmsUser);

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { lmsUserId: lmsUser.id },
  });

  const userData = {
    email: lmsUser.email,
    name: lmsUser.name,
    role: supportRole,
    instituteId: lmsUser.instituteId,
    branchId: lmsUser.branchId,
    isActive: lmsUser.isActive,
    lmsUserId: lmsUser.id,
  };

  if (existingUser) {
    // Update existing user
    return prisma.user.update({
      where: { id: existingUser.id },
      data: userData,
    });
  } else {
    // Create new user (without password - will be set on first login)
    return prisma.user.create({
      data: userData,
    });
  }
}

/**
 * Fetch user from LMS API
 * This would integrate with your actual LMS API
 */
export async function fetchUserFromLMS(lmsUserId: string): Promise<LMSUser | null> {
  // TODO: Replace with actual LMS API integration
  // This is a mock implementation
  
  try {
    // Example API call to LMS
    // const response = await fetch(`${process.env.LMS_API_URL}/users/${lmsUserId}`, {
    //   headers: {
    //     'Authorization': `Bearer ${process.env.LMS_API_TOKEN}`,
    //     'Content-Type': 'application/json',
    //   },
    // });
    
    // if (!response.ok) {
    //   return null;
    // }
    
    // return response.json();

    // Mock data for development
    const mockUsers: Record<string, LMSUser> = {
      'lms_user_1': {
        id: 'lms_user_1',
        email: '<EMAIL>',
        name: 'Institute Admin',
        role: {
          permissions: [
            { code: 'manage_institute' },
            { code: 'manage_institute_users' },
            { code: 'manage_branches' },
          ],
        },
        instituteId: 'inst_1',
        branchId: 'branch_1',
        isActive: true,
      },
      'lms_user_2': {
        id: 'lms_user_2',
        email: '<EMAIL>',
        name: 'Support Staff',
        role: {
          permissions: [
            { code: 'manage_tickets' },
            { code: 'support_staff_access' },
          ],
        },
        instituteId: 'inst_1',
        branchId: 'branch_1',
        isActive: true,
      },
      'lms_user_3': {
        id: 'lms_user_3',
        email: '<EMAIL>',
        name: 'Super Admin',
        legacyRole: 'super_admin',
        role: {
          permissions: [
            { code: 'manage_system' },
            { code: 'manage_all_institutes' },
          ],
        },
        isActive: true,
      },
    };

    return mockUsers[lmsUserId] || null;
  } catch (error) {
    console.error('Error fetching user from LMS:', error);
    return null;
  }
}

/**
 * Validate LMS token and get user info
 */
export async function validateLMSToken(token: string): Promise<LMSUser | null> {
  // TODO: Replace with actual LMS token validation
  // This would validate the token with your LMS and return user info
  
  try {
    // Example API call to validate token
    // const response = await fetch(`${process.env.LMS_API_URL}/auth/validate`, {
    //   headers: {
    //     'Authorization': `Bearer ${token}`,
    //     'Content-Type': 'application/json',
    //   },
    // });
    
    // if (!response.ok) {
    //   return null;
    // }
    
    // const userData = await response.json();
    // return userData;

    // Mock validation for development
    if (token === 'valid_lms_token') {
      return await fetchUserFromLMS('lms_user_1');
    }

    return null;
  } catch (error) {
    console.error('Error validating LMS token:', error);
    return null;
  }
}

/**
 * Get institute and branch information from LMS
 */
export async function fetchInstituteFromLMS(instituteId: string) {
  // TODO: Replace with actual LMS API integration
  
  // Mock data for development
  const mockInstitutes: Record<string, any> = {
    'inst_1': {
      id: 'inst_1',
      name: 'Sample Institute',
      slug: 'sample-institute',
      email: '<EMAIL>',
      phone: '+1234567890',
      website: 'https://institute.com',
      isActive: true,
    },
  };

  return mockInstitutes[instituteId] || null;
}

/**
 * Get branch information from LMS
 */
export async function fetchBranchFromLMS(branchId: string) {
  // TODO: Replace with actual LMS API integration
  
  // Mock data for development
  const mockBranches: Record<string, any> = {
    'branch_1': {
      id: 'branch_1',
      name: 'Main Branch',
      code: 'MAIN',
      instituteId: 'inst_1',
      address: '123 Main St, City, State',
      phone: '+1234567890',
      email: '<EMAIL>',
      isActive: true,
    },
  };

  return mockBranches[branchId] || null;
}

/**
 * Sync institute data from LMS
 */
export async function syncInstituteFromLMS(instituteId: string) {
  const lmsInstitute = await fetchInstituteFromLMS(instituteId);
  
  if (!lmsInstitute) {
    return null;
  }

  return prisma.institute.upsert({
    where: { id: instituteId },
    update: {
      name: lmsInstitute.name,
      slug: lmsInstitute.slug,
      email: lmsInstitute.email,
      phone: lmsInstitute.phone,
      website: lmsInstitute.website,
      isActive: lmsInstitute.isActive,
    },
    create: {
      id: instituteId,
      name: lmsInstitute.name,
      slug: lmsInstitute.slug,
      email: lmsInstitute.email,
      phone: lmsInstitute.phone,
      website: lmsInstitute.website,
      isActive: lmsInstitute.isActive,
    },
  });
}

/**
 * Sync branch data from LMS
 */
export async function syncBranchFromLMS(branchId: string) {
  const lmsBranch = await fetchBranchFromLMS(branchId);
  
  if (!lmsBranch) {
    return null;
  }

  return prisma.branch.upsert({
    where: { id: branchId },
    update: {
      name: lmsBranch.name,
      code: lmsBranch.code,
      address: lmsBranch.address,
      phone: lmsBranch.phone,
      email: lmsBranch.email,
      isActive: lmsBranch.isActive,
    },
    create: {
      id: branchId,
      name: lmsBranch.name,
      code: lmsBranch.code,
      instituteId: lmsBranch.instituteId,
      address: lmsBranch.address,
      phone: lmsBranch.phone,
      email: lmsBranch.email,
      isActive: lmsBranch.isActive,
    },
  });
}
