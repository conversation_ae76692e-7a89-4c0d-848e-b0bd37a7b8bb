import { getServerSession } from 'next-auth';
import { authOptions } from './auth';
import { prisma } from './prisma';
import { UserRole } from '@prisma/client';

/**
 * Custom authentication strategy for Payload CMS
 * Integrates with NextAuth.js sessions
 */
export async function payloadAuth(req: any) {
  try {
    // Get NextAuth.js session
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return null;
    }

    // Get full user data from database
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        instituteId: true,
        branchId: true,
        lmsUserId: true,
        isActive: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user || !user.isActive) {
      return null;
    }

    // Return user in format expected by Payload CMS
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      instituteId: user.instituteId,
      branchId: user.branchId,
      lmsUserId: user.lmsUserId,
      isActive: user.isActive,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      collection: 'users',
    };
  } catch (error) {
    console.error('Error in Payload auth:', error);
    return null;
  }
}

/**
 * Middleware to inject user into Payload requests
 */
export function injectPayloadUser() {
  return async (req: any, res: any, next: any) => {
    try {
      const user = await payloadAuth(req);
      req.user = user;
      next();
    } catch (error) {
      console.error('Error injecting Payload user:', error);
      req.user = null;
      next();
    }
  };
}

/**
 * Check if user has permission for a specific action
 */
export function hasPermission(
  user: any,
  action: 'create' | 'read' | 'update' | 'delete',
  resource: string,
  resourceData?: any
): boolean {
  if (!user || !user.isActive) {
    return false;
  }

  const userRole = user.role as UserRole;

  // Super admin has all permissions
  if (userRole === UserRole.SUPER_ADMIN) {
    return true;
  }

  // Define permissions by role and resource
  const permissions = {
    [UserRole.INSTITUTE_ADMIN]: {
      users: ['create', 'read', 'update'],
      institutes: ['read', 'update'],
      branches: ['create', 'read', 'update', 'delete'],
      media: ['create', 'read', 'update', 'delete'],
      'support-tickets': ['create', 'read', 'update', 'delete'],
      'support-categories': ['create', 'read', 'update', 'delete'],
      'ticket-templates': ['create', 'read', 'update', 'delete'],
      'ticket-messages': ['create', 'read', 'update', 'delete'],
      'ticket-attachments': ['create', 'read', 'update', 'delete'],
      'ticket-notes': ['create', 'read', 'update', 'delete'],
      'ticket-analytics': ['read', 'update'],
    },
    [UserRole.SUPPORT_STAFF]: {
      users: ['read'], // Own profile only
      institutes: ['read'],
      branches: ['read'],
      media: ['create', 'read', 'update', 'delete'], // Own uploads only
      'support-tickets': ['create', 'read', 'update'],
      'support-categories': ['read'],
      'ticket-templates': ['read'],
      'ticket-messages': ['create', 'read', 'update'],
      'ticket-attachments': ['create', 'read', 'update'],
      'ticket-notes': ['create', 'read', 'update'],
      'ticket-analytics': ['read'],
    },
    [UserRole.STUDENT]: {
      users: ['read'], // Own profile only
      institutes: ['read'],
      branches: ['read'],
      media: ['create', 'read'], // Own uploads only
      'support-tickets': ['create', 'read'],
      'support-categories': ['read'],
      'ticket-templates': ['read'],
      'ticket-messages': ['create', 'read'],
      'ticket-attachments': ['create', 'read'],
      'ticket-notes': ['read'], // Can only read notes, not create
      'ticket-analytics': [], // No access to analytics
    },
  };

  const rolePermissions = permissions[userRole];
  if (!rolePermissions || !rolePermissions[resource]) {
    return false;
  }

  return rolePermissions[resource].includes(action);
}

/**
 * Filter query based on user's institute/branch access
 */
export function filterByAccess(user: any, baseQuery: any = {}) {
  if (!user || !user.isActive) {
    return { id: { equals: 'never-match' } }; // Return impossible condition
  }

  const userRole = user.role as UserRole;

  // Super admin can access everything
  if (userRole === UserRole.SUPER_ADMIN) {
    return baseQuery;
  }

  // Institute-level filtering
  if ([UserRole.INSTITUTE_ADMIN, UserRole.SUPPORT_STAFF].includes(userRole)) {
    if (user.instituteId) {
      return {
        ...baseQuery,
        and: [
          baseQuery,
          {
            or: [
              { institute: { equals: user.instituteId } },
              { instituteId: { equals: user.instituteId } },
            ],
          },
        ],
      };
    }
  }

  // Default: no access
  return { id: { equals: 'never-match' } };
}

/**
 * Validate institute/branch access for data operations
 */
export function validateInstituteAccess(
  user: any,
  targetInstituteId: string,
  targetBranchId?: string
): boolean {
  if (!user || !user.isActive) {
    return false;
  }

  const userRole = user.role as UserRole;

  // Super admin can access everything
  if (userRole === UserRole.SUPER_ADMIN) {
    return true;
  }

  // Check institute access
  if (user.instituteId !== targetInstituteId) {
    return false;
  }

  // Institute admin can access all branches in their institute
  if (userRole === UserRole.INSTITUTE_ADMIN) {
    return true;
  }

  // Support staff and students need branch-level access
  if (targetBranchId && user.branchId !== targetBranchId) {
    return false;
  }

  return true;
}

/**
 * Get user's accessible institute IDs
 */
export function getAccessibleInstitutes(user: any): string[] {
  if (!user || !user.isActive) {
    return [];
  }

  const userRole = user.role as UserRole;

  // Super admin can access all institutes
  if (userRole === UserRole.SUPER_ADMIN) {
    return ['*']; // Special marker for all institutes
  }

  // Others can only access their own institute
  if (user.instituteId) {
    return [user.instituteId];
  }

  return [];
}

/**
 * Get user's accessible branch IDs
 */
export function getAccessibleBranches(user: any): string[] {
  if (!user || !user.isActive) {
    return [];
  }

  const userRole = user.role as UserRole;

  // Super admin can access all branches
  if (userRole === UserRole.SUPER_ADMIN) {
    return ['*']; // Special marker for all branches
  }

  // Institute admin can access all branches in their institute
  if (userRole === UserRole.INSTITUTE_ADMIN) {
    return ['*']; // Will be filtered by institute in queries
  }

  // Others can only access their own branch
  if (user.branchId) {
    return [user.branchId];
  }

  return [];
}
