import { NextRequest, NextResponse } from 'next/server';
import { rateLimiter } from './redis';

export interface RateLimitConfig {
  requests: number;
  windowSeconds: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: NextRequest) => string;
}

/**
 * Rate limiting middleware for API routes
 */
export function createRateLimitMiddleware(config: RateLimitConfig) {
  return async (req: NextRequest): Promise<NextResponse | null> => {
    try {
      // Generate rate limit key
      const key = config.keyGenerator 
        ? config.keyGenerator(req)
        : getDefaultKey(req);

      // Check rate limit
      const result = await rateLimiter.checkLimit(
        key,
        config.requests,
        config.windowSeconds
      );

      // Add rate limit headers
      const headers = new Headers();
      headers.set('X-RateLimit-Limit', config.requests.toString());
      headers.set('X-RateLimit-Remaining', result.remaining.toString());
      headers.set('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000).toString());

      if (!result.allowed) {
        return NextResponse.json(
          {
            error: 'Too many requests',
            message: `Rate limit exceeded. Try again in ${Math.ceil((result.resetTime - Date.now()) / 1000)} seconds.`,
          },
          {
            status: 429,
            headers,
          }
        );
      }

      // Rate limit passed, continue with request
      return null;
    } catch (error) {
      console.error('Rate limiting error:', error);
      // If Redis is down, allow the request to continue
      return null;
    }
  };
}

/**
 * Default key generator using IP address
 */
function getDefaultKey(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : req.ip || 'unknown';
  return `ip:${ip}`;
}

/**
 * User-based key generator
 */
export function userKeyGenerator(userId: string) {
  return (req: NextRequest): string => {
    return `user:${userId}`;
  };
}

/**
 * API endpoint-based key generator
 */
export function endpointKeyGenerator(endpoint: string) {
  return (req: NextRequest): string => {
    const forwarded = req.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : req.ip || 'unknown';
    return `endpoint:${endpoint}:ip:${ip}`;
  };
}

/**
 * Combined user and IP key generator
 */
export function combinedKeyGenerator(userId?: string) {
  return (req: NextRequest): string => {
    const forwarded = req.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : req.ip || 'unknown';
    
    if (userId) {
      return `user:${userId}:ip:${ip}`;
    }
    
    return `ip:${ip}`;
  };
}

// Predefined rate limit configurations
export const rateLimitConfigs = {
  // General API rate limit
  api: {
    requests: 100,
    windowSeconds: 60, // 100 requests per minute
  },
  
  // Authentication endpoints
  auth: {
    requests: 5,
    windowSeconds: 60, // 5 login attempts per minute
    keyGenerator: endpointKeyGenerator('auth'),
  },
  
  // File upload endpoints
  upload: {
    requests: 10,
    windowSeconds: 60, // 10 uploads per minute
  },
  
  // Search endpoints
  search: {
    requests: 30,
    windowSeconds: 60, // 30 searches per minute
  },
  
  // Support ticket creation
  ticketCreation: {
    requests: 5,
    windowSeconds: 300, // 5 tickets per 5 minutes
  },
  
  // Password reset
  passwordReset: {
    requests: 3,
    windowSeconds: 3600, // 3 password resets per hour
    keyGenerator: endpointKeyGenerator('password-reset'),
  },
  
  // Email sending
  email: {
    requests: 10,
    windowSeconds: 3600, // 10 emails per hour
  },
  
  // Strict rate limit for sensitive operations
  strict: {
    requests: 10,
    windowSeconds: 60, // 10 requests per minute
  },
  
  // Lenient rate limit for read operations
  lenient: {
    requests: 200,
    windowSeconds: 60, // 200 requests per minute
  },
};

/**
 * Apply rate limiting to an API route handler
 */
export function withRateLimit<T extends any[]>(
  config: RateLimitConfig,
  handler: (req: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    // Apply rate limiting
    const rateLimitResponse = await createRateLimitMiddleware(config)(req);
    
    if (rateLimitResponse) {
      return rateLimitResponse;
    }
    
    // Continue with original handler
    return handler(req, ...args);
  };
}

/**
 * Rate limit decorator for class methods
 */
export function RateLimit(config: RateLimitConfig) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const req = args[0] as NextRequest;
      
      // Apply rate limiting
      const rateLimitResponse = await createRateLimitMiddleware(config)(req);
      
      if (rateLimitResponse) {
        return rateLimitResponse;
      }
      
      // Continue with original method
      return method.apply(this, args);
    };
  };
}

/**
 * Utility to check if user is rate limited without incrementing counter
 */
export async function checkRateLimit(
  key: string,
  config: RateLimitConfig
): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
  return rateLimiter.checkLimit(key, config.requests, config.windowSeconds);
}

/**
 * Utility to reset rate limit for a specific key
 */
export async function resetRateLimit(key: string): Promise<void> {
  return rateLimiter.resetLimit(key);
}
