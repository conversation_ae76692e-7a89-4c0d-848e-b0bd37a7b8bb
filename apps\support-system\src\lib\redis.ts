import Redis from 'ioredis';

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

// Create Redis client instance
let redis: Redis | null = null;

export function getRedisClient(): Redis {
  if (!redis) {
    // Use Redis URL if provided, otherwise use individual config
    if (process.env.REDIS_URL) {
      redis = new Redis(process.env.REDIS_URL, {
        retryDelayOnFailover: redisConfig.retryDelayOnFailover,
        maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
        lazyConnect: redisConfig.lazyConnect,
        keepAlive: redisConfig.keepAlive,
        connectTimeout: redisConfig.connectTimeout,
        commandTimeout: redisConfig.commandTimeout,
      });
    } else {
      redis = new Redis(redisConfig);
    }

    // Handle connection events
    redis.on('connect', () => {
      console.log('✅ Redis connected successfully');
    });

    redis.on('error', (error) => {
      console.error('❌ Redis connection error:', error);
    });

    redis.on('close', () => {
      console.log('🔌 Redis connection closed');
    });

    redis.on('reconnecting', () => {
      console.log('🔄 Redis reconnecting...');
    });
  }

  return redis;
}

// Cache utilities
export class CacheService {
  private redis: Redis;

  constructor() {
    this.redis = getRedisClient();
  }

  /**
   * Set a value in cache with optional TTL
   */
  async set(key: string, value: any, ttlSeconds?: number): Promise<void> {
    const serializedValue = JSON.stringify(value);
    
    if (ttlSeconds) {
      await this.redis.setex(key, ttlSeconds, serializedValue);
    } else {
      await this.redis.set(key, serializedValue);
    }
  }

  /**
   * Get a value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    const value = await this.redis.get(key);
    
    if (!value) {
      return null;
    }

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      console.error('Error parsing cached value:', error);
      return null;
    }
  }

  /**
   * Delete a key from cache
   */
  async del(key: string): Promise<void> {
    await this.redis.del(key);
  }

  /**
   * Check if a key exists
   */
  async exists(key: string): Promise<boolean> {
    const result = await this.redis.exists(key);
    return result === 1;
  }

  /**
   * Set expiration for a key
   */
  async expire(key: string, ttlSeconds: number): Promise<void> {
    await this.redis.expire(key, ttlSeconds);
  }

  /**
   * Get multiple keys
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    const values = await this.redis.mget(...keys);
    
    return values.map(value => {
      if (!value) return null;
      
      try {
        return JSON.parse(value) as T;
      } catch (error) {
        console.error('Error parsing cached value:', error);
        return null;
      }
    });
  }

  /**
   * Set multiple key-value pairs
   */
  async mset(keyValuePairs: Record<string, any>): Promise<void> {
    const serializedPairs: string[] = [];
    
    for (const [key, value] of Object.entries(keyValuePairs)) {
      serializedPairs.push(key, JSON.stringify(value));
    }
    
    await this.redis.mset(...serializedPairs);
  }

  /**
   * Increment a counter
   */
  async incr(key: string): Promise<number> {
    return await this.redis.incr(key);
  }

  /**
   * Increment a counter with TTL
   */
  async incrWithTTL(key: string, ttlSeconds: number): Promise<number> {
    const pipeline = this.redis.pipeline();
    pipeline.incr(key);
    pipeline.expire(key, ttlSeconds);
    
    const results = await pipeline.exec();
    return results?.[0]?.[1] as number || 0;
  }

  /**
   * Add to a set
   */
  async sadd(key: string, ...members: string[]): Promise<number> {
    return await this.redis.sadd(key, ...members);
  }

  /**
   * Get all members of a set
   */
  async smembers(key: string): Promise<string[]> {
    return await this.redis.smembers(key);
  }

  /**
   * Check if member exists in set
   */
  async sismember(key: string, member: string): Promise<boolean> {
    const result = await this.redis.sismember(key, member);
    return result === 1;
  }

  /**
   * Remove from set
   */
  async srem(key: string, ...members: string[]): Promise<number> {
    return await this.redis.srem(key, ...members);
  }

  /**
   * Get keys matching pattern
   */
  async keys(pattern: string): Promise<string[]> {
    return await this.redis.keys(pattern);
  }

  /**
   * Delete keys matching pattern
   */
  async deletePattern(pattern: string): Promise<number> {
    const keys = await this.keys(pattern);
    
    if (keys.length === 0) {
      return 0;
    }
    
    return await this.redis.del(...keys);
  }

  /**
   * Get Redis info
   */
  async info(): Promise<string> {
    return await this.redis.info();
  }

  /**
   * Ping Redis
   */
  async ping(): Promise<string> {
    return await this.redis.ping();
  }

  /**
   * Close Redis connection
   */
  async disconnect(): Promise<void> {
    await this.redis.disconnect();
  }
}

// Rate limiting utilities
export class RateLimiter {
  private redis: Redis;

  constructor() {
    this.redis = getRedisClient();
  }

  /**
   * Check if request is rate limited
   * @param key - Unique identifier for the rate limit (e.g., user ID, IP)
   * @param limit - Maximum number of requests
   * @param windowSeconds - Time window in seconds
   * @returns Object with allowed status and remaining requests
   */
  async checkLimit(
    key: string,
    limit: number,
    windowSeconds: number
  ): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const redisKey = `rate_limit:${key}`;
    const now = Date.now();
    const windowStart = now - (windowSeconds * 1000);

    // Use Redis pipeline for atomic operations
    const pipeline = this.redis.pipeline();
    
    // Remove expired entries
    pipeline.zremrangebyscore(redisKey, 0, windowStart);
    
    // Count current requests in window
    pipeline.zcard(redisKey);
    
    // Add current request
    pipeline.zadd(redisKey, now, `${now}-${Math.random()}`);
    
    // Set expiration
    pipeline.expire(redisKey, windowSeconds);
    
    const results = await pipeline.exec();
    const currentCount = (results?.[1]?.[1] as number) || 0;
    
    const allowed = currentCount < limit;
    const remaining = Math.max(0, limit - currentCount - 1);
    const resetTime = now + (windowSeconds * 1000);

    return {
      allowed,
      remaining,
      resetTime,
    };
  }

  /**
   * Reset rate limit for a key
   */
  async resetLimit(key: string): Promise<void> {
    const redisKey = `rate_limit:${key}`;
    await this.redis.del(redisKey);
  }
}

// Session utilities (for additional session data, not primary session storage)
export class SessionCache {
  private cache: CacheService;

  constructor() {
    this.cache = new CacheService();
  }

  /**
   * Store additional session data
   */
  async setSessionData(sessionId: string, data: any, ttlSeconds = 3600): Promise<void> {
    const key = `session:${sessionId}`;
    await this.cache.set(key, data, ttlSeconds);
  }

  /**
   * Get additional session data
   */
  async getSessionData<T>(sessionId: string): Promise<T | null> {
    const key = `session:${sessionId}`;
    return await this.cache.get<T>(key);
  }

  /**
   * Delete session data
   */
  async deleteSessionData(sessionId: string): Promise<void> {
    const key = `session:${sessionId}`;
    await this.cache.del(key);
  }

  /**
   * Store user's active sessions
   */
  async addActiveSession(userId: string, sessionId: string): Promise<void> {
    const key = `user_sessions:${userId}`;
    await this.cache.sadd(key, sessionId);
    await this.cache.expire(key, 86400); // 24 hours
  }

  /**
   * Remove user's active session
   */
  async removeActiveSession(userId: string, sessionId: string): Promise<void> {
    const key = `user_sessions:${userId}`;
    await this.cache.srem(key, sessionId);
  }

  /**
   * Get user's active sessions
   */
  async getActiveSessions(userId: string): Promise<string[]> {
    const key = `user_sessions:${userId}`;
    return await this.cache.smembers(key);
  }

  /**
   * Invalidate all user sessions
   */
  async invalidateUserSessions(userId: string): Promise<void> {
    const key = `user_sessions:${userId}`;
    const sessions = await this.cache.smembers(key);
    
    // Delete all session data
    for (const sessionId of sessions) {
      await this.deleteSessionData(sessionId);
    }
    
    // Clear the user sessions set
    await this.cache.del(key);
  }
}

// Export singleton instances
export const cacheService = new CacheService();
export const rateLimiter = new RateLimiter();
export const sessionCache = new SessionCache();
