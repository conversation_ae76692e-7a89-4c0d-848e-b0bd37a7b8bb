import React from 'react';
import { getSession, signOut } from 'next-auth/react';
import { sessionCache } from './redis';

export interface TokenRefreshConfig {
  refreshThreshold: number; // Time in milliseconds before expiry to refresh
  maxRetries: number;
  retryDelay: number;
}

export const defaultRefreshConfig: TokenRefreshConfig = {
  refreshThreshold: 5 * 60 * 1000, // 5 minutes before expiry
  maxRetries: 3,
  retryDelay: 1000, // 1 second
};

/**
 * Token Refresh Service
 * Handles automatic token refresh and session management
 */
export class TokenRefreshService {
  private static instance: TokenRefreshService;
  private refreshTimer: NodeJS.Timeout | null = null;
  private isRefreshing = false;
  private config: TokenRefreshConfig;

  private constructor(config: TokenRefreshConfig = defaultRefreshConfig) {
    this.config = config;
  }

  static getInstance(config?: TokenRefreshConfig): TokenRefreshService {
    if (!TokenRefreshService.instance) {
      TokenRefreshService.instance = new TokenRefreshService(config);
    }
    return TokenRefreshService.instance;
  }

  /**
   * Start automatic token refresh monitoring
   */
  async startRefreshMonitoring(): Promise<void> {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }

    // Check every minute
    this.refreshTimer = setInterval(async () => {
      await this.checkAndRefreshToken();
    }, 60 * 1000);

    // Initial check
    await this.checkAndRefreshToken();
  }

  /**
   * Stop automatic token refresh monitoring
   */
  stopRefreshMonitoring(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Check if token needs refresh and refresh if necessary
   */
  async checkAndRefreshToken(): Promise<boolean> {
    if (this.isRefreshing) {
      return false;
    }

    try {
      const session = await getSession();
      
      if (!session) {
        return false;
      }

      const now = Date.now();
      const expiresAt = (session as any).tokenExp ? (session as any).tokenExp * 1000 : 0;
      const lastRefresh = (session as any).lastRefresh || 0;

      // Check if token is close to expiry or hasn't been refreshed in 24 hours
      const needsRefresh = 
        (expiresAt > 0 && (expiresAt - now) < this.config.refreshThreshold) ||
        (now - lastRefresh > 24 * 60 * 60 * 1000);

      if (needsRefresh) {
        return await this.refreshToken();
      }

      return true;
    } catch (error) {
      console.error('Error checking token refresh:', error);
      return false;
    }
  }

  /**
   * Force token refresh
   */
  async refreshToken(retryCount = 0): Promise<boolean> {
    if (this.isRefreshing && retryCount === 0) {
      return false;
    }

    this.isRefreshing = true;

    try {
      // Trigger NextAuth.js token refresh by updating the session
      const response = await fetch('/api/auth/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ trigger: 'update' }),
      });

      if (response.ok) {
        const updatedSession = await response.json();
        
        if (updatedSession) {
          console.log('Token refreshed successfully');
          return true;
        } else {
          throw new Error('Session update returned null');
        }
      } else {
        throw new Error(`Token refresh failed: ${response.status}`);
      }
    } catch (error) {
      console.error('Token refresh error:', error);

      // Retry logic
      if (retryCount < this.config.maxRetries) {
        await this.delay(this.config.retryDelay * (retryCount + 1));
        return this.refreshToken(retryCount + 1);
      }

      // If all retries failed, sign out the user
      console.error('Token refresh failed after all retries, signing out user');
      await this.handleRefreshFailure();
      return false;
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Handle token refresh failure
   */
  private async handleRefreshFailure(): Promise<void> {
    try {
      // Clear session cache
      const session = await getSession();
      if (session?.user?.id) {
        await sessionCache.invalidateUserSessions(session.user.id);
      }

      // Sign out user
      await signOut({ 
        callbackUrl: '/auth/signin?error=SessionExpired',
        redirect: true 
      });
    } catch (error) {
      console.error('Error handling refresh failure:', error);
      // Force page reload as fallback
      window.location.href = '/auth/signin?error=SessionExpired';
    }
  }

  /**
   * Get session expiry information
   */
  async getSessionInfo(): Promise<{
    isValid: boolean;
    expiresAt: number;
    lastRefresh: number;
    timeUntilExpiry: number;
    needsRefresh: boolean;
  } | null> {
    try {
      const session = await getSession();
      
      if (!session) {
        return null;
      }

      const now = Date.now();
      const expiresAt = (session as any).tokenExp ? (session as any).tokenExp * 1000 : 0;
      const lastRefresh = (session as any).lastRefresh || 0;
      const timeUntilExpiry = expiresAt - now;
      const needsRefresh = timeUntilExpiry < this.config.refreshThreshold;

      return {
        isValid: timeUntilExpiry > 0,
        expiresAt,
        lastRefresh,
        timeUntilExpiry,
        needsRefresh,
      };
    } catch (error) {
      console.error('Error getting session info:', error);
      return null;
    }
  }

  /**
   * Validate current session
   */
  async validateSession(): Promise<boolean> {
    try {
      const session = await getSession();
      
      if (!session) {
        return false;
      }

      // Check if session is still valid
      const sessionInfo = await this.getSessionInfo();
      
      if (!sessionInfo || !sessionInfo.isValid) {
        await this.handleRefreshFailure();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error validating session:', error);
      return false;
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    this.stopRefreshMonitoring();
    TokenRefreshService.instance = null as any;
  }
}

/**
 * React hook for token refresh
 */
export function useTokenRefresh(config?: TokenRefreshConfig) {
  const refreshService = TokenRefreshService.getInstance(config);

  const startMonitoring = () => refreshService.startRefreshMonitoring();
  const stopMonitoring = () => refreshService.stopRefreshMonitoring();
  const refreshToken = () => refreshService.refreshToken();
  const validateSession = () => refreshService.validateSession();
  const getSessionInfo = () => refreshService.getSessionInfo();

  return {
    startMonitoring,
    stopMonitoring,
    refreshToken,
    validateSession,
    getSessionInfo,
  };
}

/**
 * Higher-order component for automatic token refresh
 */
export function withTokenRefresh<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  config?: TokenRefreshConfig
) {
  return function TokenRefreshWrapper(props: P) {
    const { startMonitoring, stopMonitoring } = useTokenRefresh(config);

    React.useEffect(() => {
      startMonitoring();
      
      return () => {
        stopMonitoring();
      };
    }, [startMonitoring, stopMonitoring]);

    return React.createElement(WrappedComponent, props);
  };
}

// Export singleton instance
export const tokenRefreshService = TokenRefreshService.getInstance();
