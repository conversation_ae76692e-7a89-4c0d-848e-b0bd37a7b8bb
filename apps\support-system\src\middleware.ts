import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';
import { UserRole } from '@prisma/client';

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token;
    const { pathname } = req.nextUrl;

    // Allow access to auth pages
    if (pathname.startsWith('/auth/')) {
      return NextResponse.next();
    }

    // Protect admin routes
    if (pathname.startsWith('/admin')) {
      if (!token) {
        return NextResponse.redirect(new URL('/auth/signin', req.url));
      }

      // Super admin and institute admin can access admin panel
      if (
        token.role !== UserRole.SUPER_ADMIN &&
        token.role !== UserRole.INSTITUTE_ADMIN
      ) {
        return NextResponse.redirect(new URL('/unauthorized', req.url));
      }
    }

    // Protect API routes
    if (pathname.startsWith('/api/')) {
      // Skip auth API routes
      if (pathname.startsWith('/api/auth/')) {
        return NextResponse.next();
      }

      // Skip health check
      if (pathname === '/api/health') {
        return NextResponse.next();
      }

      // Require authentication for other API routes
      if (!token) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Add user info to headers for API routes
      const requestHeaders = new Headers(req.headers);
      requestHeaders.set('x-user-id', token.sub!);
      requestHeaders.set('x-user-role', token.role);
      requestHeaders.set('x-user-email', token.email || '');
      if (token.instituteId) {
        requestHeaders.set('x-user-institute-id', token.instituteId);
      }
      if (token.branchId) {
        requestHeaders.set('x-user-branch-id', token.branchId);
      }
      if (token.lmsUserId) {
        requestHeaders.set('x-user-lms-id', token.lmsUserId);
      }

      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      });
    }

    // Protect dashboard routes
    if (pathname.startsWith('/dashboard')) {
      if (!token) {
        return NextResponse.redirect(new URL('/auth/signin', req.url));
      }
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to public routes
        const { pathname } = req.nextUrl;
        
        if (
          pathname === '/' ||
          pathname.startsWith('/auth/') ||
          pathname.startsWith('/_next/') ||
          pathname.startsWith('/favicon.ico')
        ) {
          return true;
        }

        // Require token for protected routes
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
