import { buildConfig } from 'payload';
import { postgresAdapter } from '@payloadcms/db-postgres';
import { lexicalEditor } from '@payloadcms/richtext-lexical';
import path from 'path';

// Import collections
import Users from './collections/Users';
import Institutes from './collections/Institutes';
import Branches from './collections/Branches';
import Media from './collections/Media';

// Import support system collections
import SupportCategories from './collections/SupportCategories';
import TicketTemplates from './collections/TicketTemplates';
import SupportTickets from './collections/SupportTickets';
import TicketMessages from './collections/TicketMessages';
import TicketAttachments from './collections/TicketAttachments';
import TicketNotes from './collections/TicketNotes';
import TicketAnalytics from './collections/TicketAnalytics';

// Authentication will be handled in API routes

export default buildConfig({
  admin: {
    user: 'users',
    meta: {
      titleSuffix: '- Support System',
    },
  },
  collections: [
    Users,
    Institutes,
    Branches,
    Media,
    // Support System Collections
    SupportCategories,
    TicketTemplates,
    SupportTickets,
    TicketMessages,
    TicketAttachments,
    TicketNotes,
    TicketAnalytics,
  ],
  editor: lexicalEditor({}),
  secret: process.env.PAYLOAD_SECRET || 'your-secret-here',
  typescript: {
    outputFile: path.resolve(__dirname, 'payload-types.ts'),
  },
  db: postgresAdapter({
    pool: {
      connectionString: process.env.POSTGRES_URL || process.env.DATABASE_URL,
    },
  }),
  cors: [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://localhost:3000',
    'https://localhost:3001',
  ],
  csrf: [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://localhost:3000',
    'https://localhost:3001',
  ],
  // Note: Express middleware will be configured in the Next.js API route
  upload: {
    limits: {
      fileSize: 5000000, // 5MB
    },
  },
});
