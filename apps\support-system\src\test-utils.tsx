import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';

// Add providers here as needed (e.g., Theme, Router, etc.)
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <div data-testid="test-wrapper">
      {children}
    </div>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Re-export everything
export * from '@testing-library/react';

// Override render method
export { customRender as render };

// Common test utilities
export const createMockUser = (overrides = {}) => ({
  id: '1',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'user',
  ...overrides,
});

export const createMockTicket = (overrides = {}) => ({
  id: '1',
  title: 'Test Ticket',
  description: 'Test description',
  status: 'open',
  priority: 'medium',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

// Mock API responses
export const mockApiResponse = (data: any, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  json: async () => data,
  text: async () => JSON.stringify(data),
});

// Wait for async operations
export const waitFor = (ms: number) =>
  new Promise(resolve => setTimeout(resolve, ms));

// Mock fetch
export const mockFetch = (response: any, status = 200) => {
  global.fetch = jest.fn(() =>
    Promise.resolve(mockApiResponse(response, status))
  ) as jest.Mock;
};

// Reset mocks
export const resetMocks = () => {
  jest.clearAllMocks();
  if (global.fetch && jest.isMockFunction(global.fetch)) {
    (global.fetch as jest.Mock).mockClear();
  }
};
