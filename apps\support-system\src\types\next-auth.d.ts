import { UserRole } from '@prisma/client';
import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name?: string;
      image?: string;
      role: UserRole;
      instituteId?: string;
      branchId?: string;
      lmsUserId?: string;
    };
  }

  interface User {
    id: string;
    email: string;
    name?: string;
    role: UserRole;
    instituteId?: string;
    branchId?: string;
    lmsUserId?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole;
    instituteId?: string;
    branchId?: string;
    lmsUserId?: string;
  }
}
