@echo off
echo Cleaning up ports 3000, 3001, 3002...

REM Kill processes using port 3000
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3000"') do (
    echo Killing process %%a on port 3000
    taskkill /PID %%a /F >nul 2>&1
)

REM Kill processes using port 3001
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3001"') do (
    echo Killing process %%a on port 3001
    taskkill /PID %%a /F >nul 2>&1
)

REM Kill processes using port 3002
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3002"') do (
    echo Killing process %%a on port 3002
    taskkill /PID %%a /F >nul 2>&1
)

echo Ports cleaned up!
echo You can now run: npm run dev
pause
