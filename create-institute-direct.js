// Script to create institute directly using Payload's local API

import payload from 'payload'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

async function createTestInstitute() {
  try {
    console.log('🏫 Creating test institute with custom domain hello.local:3000...')

    // Initialize Payload
    await payload.init({
      secret: process.env.PAYLOAD_SECRET || 'your-secret-here',
      local: true,
      configPath: path.resolve(__dirname, 'apps/api/src/payload.config.ts'),
    })

    // Create the institute
    const institute = await payload.create({
      collection: 'institutes',
      data: {
        name: 'Hello Academy',
        slug: 'hello-academy',
        email: '<EMAIL>',
        phone: '******-0123',
        website: 'https://hello.local',
        tagline: 'Learn, Grow, Succeed',
        customDomain: 'hello.local:3000',
        domainVerified: true, // Mark as verified for testing
        isActive: true
      }
    })

    console.log('✅ Institute created successfully!')
    console.log('Institute ID:', institute.id)
    console.log('Institute Name:', institute.name)
    console.log('Custom Domain:', institute.customDomain)
    console.log('Domain Verified:', institute.domainVerified)

    // Test the domain resolution
    console.log('🔍 Testing domain resolution...')
    const foundInstitute = await payload.find({
      collection: 'institutes',
      where: {
        customDomain: {
          equals: 'hello.local:3000'
        }
      },
      limit: 1
    })

    if (foundInstitute.docs.length > 0) {
      console.log('✅ Domain resolution working!')
      console.log('Found institute:', foundInstitute.docs[0].name)
    } else {
      console.log('⚠️ Domain resolution test failed')
    }

    process.exit(0)

  } catch (error) {
    console.error('❌ Error creating test institute:', error)
    process.exit(1)
  }
}

// Run the script
createTestInstitute()
