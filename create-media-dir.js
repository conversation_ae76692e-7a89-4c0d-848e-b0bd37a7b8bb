const fs = require('fs')
const path = require('path')

console.log('🚀 Creating media directories...')

// Get the current working directory
const cwd = process.cwd()
console.log('📁 Current working directory:', cwd)

// Define media directories to create
const mediaDirs = [
  'media',
  'media/avatars',
  'media/courses', 
  'media/institutes',
  'media/documents',
  'media/uploads'
]

// Create directories
mediaDirs.forEach(dir => {
  const fullPath = path.join(cwd, dir)
  
  try {
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true })
      console.log('✅ Created directory:', fullPath)
    } else {
      console.log('📂 Directory already exists:', fullPath)
    }
  } catch (error) {
    console.error('❌ Failed to create directory:', fullPath, error.message)
  }
})

// Test write permissions
const testFile = path.join(cwd, 'media', 'test-write.txt')
try {
  fs.writeFileSync(testFile, 'Test write permissions')
  console.log('✅ Write test successful')
  
  // Clean up test file
  fs.unlinkSync(testFile)
  console.log('🧹 Test file cleaned up')
} catch (error) {
  console.error('❌ Write test failed:', error.message)
}

console.log('🎉 Media directory setup complete!')
