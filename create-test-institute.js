// <PERSON>ript to create a test institute with custom domain hello.local:3000

const API_URL = 'http://localhost:3001'

async function createTestInstitute() {
  try {
    console.log('🏫 Creating test institute with custom domain hello.local:3000...')

    // First, authenticate as super admin
    console.log('🔐 Authenticating as super admin...')
    const loginResponse = await fetch(`${API_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'SuperAdmin@123'
      })
    })

    const loginResult = await loginResponse.json()
    console.log('Login response:', loginResult)

    if (!loginResponse.ok) {
      throw new Error('Failed to authenticate: ' + (loginResult.message || loginResult.error || 'Unknown error'))
    }

    const token = loginResult.token || loginResult.user?.token
    if (!token) {
      throw new Error('No token received from login')
    }

    console.log('✅ Authenticated successfully!')

    const instituteData = {
      name: 'Hello Academy',
      slug: 'hello-academy',
      email: '<EMAIL>',
      phone: '******-0123',
      website: 'https://hello.local',
      description: 'A test academy for demonstrating institute landing pages',
      customDomain: 'hello.local:3000',
      domainVerified: true,
      isActive: true
    }

    const response = await fetch(`${API_URL}/api/institutes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(instituteData)
    })

    const result = await response.json()
    
    if (response.ok && result.success) {
      console.log('✅ Institute created successfully!')
      console.log('Institute ID:', result.institute.id)
      console.log('Institute Name:', result.institute.name)
      console.log('Custom Domain:', result.institute.customDomain)
      
      // Now verify the domain
      if (result.institute.id) {
        console.log('🔍 Verifying domain...')
        const verifyResponse = await fetch(`${API_URL}/api/institute-management/institutes/${result.institute.id}/verify-domain`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          }
        })
        
        const verifyResult = await verifyResponse.json()
        if (verifyResponse.ok && verifyResult.success) {
          console.log('✅ Domain verified successfully!')
        } else {
          console.log('⚠️ Domain verification failed:', verifyResult.error)
        }
      }
      
    } else {
      console.error('❌ Failed to create institute:', result.error || result.message)
      console.log('Response status:', response.status)
      console.log('Full response:', result)
    }

  } catch (error) {
    console.error('❌ Error creating test institute:', error)
  }
}

// Run the script
createTestInstitute()
