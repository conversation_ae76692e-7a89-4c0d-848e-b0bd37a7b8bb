-- Row Level Security (RLS) Policies for Course Builder Multi-Tenant System
-- These policies ensure complete data isolation between institutes

-- Enable RLS on all tenant-aware tables
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE lessons ENABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE question_banks ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_bundles ENABLE ROW LEVEL SECURITY;
ALTER TABLE bundle_courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_progress ENABLE ROW LEVEL SECURITY;

-- <PERSON>reate function to get current user's institute ID
CREATE OR R<PERSON>LACE FUNCTION get_current_institute_id()
<PERSON><PERSON><PERSON>NS UUID AS $$
BEGIN
  -- Get institute_id from current session
  -- This should be set by the application when user authenticates
  RETURN current_setting('app.current_institute_id', true)::UUID;
EXCEPTION
  WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get current user's branch ID
CREATE OR REPLACE FUNCTION get_current_branch_id()
RETURNS UUID AS $$
BEGIN
  -- Get branch_id from current session
  RETURN current_setting('app.current_branch_id', true)::UUID;
EXCEPTION
  WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user is super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if current user is super admin
  RETURN current_setting('app.is_super_admin', true)::BOOLEAN;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user is institute admin
CREATE OR REPLACE FUNCTION is_institute_admin()
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if current user is institute admin
  RETURN current_setting('app.is_institute_admin', true)::BOOLEAN;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Courses RLS Policies
CREATE POLICY courses_tenant_isolation ON courses
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR 
    institute_id = get_current_institute_id()
  );

CREATE POLICY courses_branch_isolation ON courses
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR 
    is_institute_admin() OR
    branch_id = get_current_branch_id() OR
    branch_id IS NULL
  );

-- Lessons RLS Policies
CREATE POLICY lessons_tenant_isolation ON lessons
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR
    EXISTS (
      SELECT 1 FROM courses 
      WHERE courses.id = lessons.course_id 
      AND courses.institute_id = get_current_institute_id()
    )
  );

-- Lesson Content RLS Policies
CREATE POLICY lesson_content_tenant_isolation ON lesson_content
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR
    EXISTS (
      SELECT 1 FROM lessons 
      JOIN courses ON courses.id = lessons.course_id
      WHERE lessons.id = lesson_content.lesson_id 
      AND courses.institute_id = get_current_institute_id()
    )
  );

-- Tests RLS Policies
CREATE POLICY tests_tenant_isolation ON tests
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR
    (course_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM courses 
      WHERE courses.id = tests.course_id 
      AND courses.institute_id = get_current_institute_id()
    )) OR
    (lesson_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM lessons 
      JOIN courses ON courses.id = lessons.course_id
      WHERE lessons.id = tests.lesson_id 
      AND courses.institute_id = get_current_institute_id()
    )) OR
    institute_id = get_current_institute_id()
  );

-- Question Banks RLS Policies
CREATE POLICY question_banks_tenant_isolation ON question_banks
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR 
    institute_id = get_current_institute_id() OR
    is_public = true
  );

-- Questions RLS Policies
CREATE POLICY questions_tenant_isolation ON questions
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR
    EXISTS (
      SELECT 1 FROM question_banks 
      WHERE question_banks.id = questions.question_bank_id 
      AND (
        question_banks.institute_id = get_current_institute_id() OR
        question_banks.is_public = true
      )
    )
  );

-- Test Questions RLS Policies
CREATE POLICY test_questions_tenant_isolation ON test_questions
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR
    EXISTS (
      SELECT 1 FROM tests 
      WHERE tests.id = test_questions.test_id 
      AND (
        (tests.course_id IS NOT NULL AND EXISTS (
          SELECT 1 FROM courses 
          WHERE courses.id = tests.course_id 
          AND courses.institute_id = get_current_institute_id()
        )) OR
        (tests.lesson_id IS NOT NULL AND EXISTS (
          SELECT 1 FROM lessons 
          JOIN courses ON courses.id = lessons.course_id
          WHERE lessons.id = tests.lesson_id 
          AND courses.institute_id = get_current_institute_id()
        )) OR
        tests.institute_id = get_current_institute_id()
      )
    )
  );

-- Test Attempts RLS Policies
CREATE POLICY test_attempts_tenant_isolation ON test_attempts
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR
    EXISTS (
      SELECT 1 FROM tests 
      WHERE tests.id = test_attempts.test_id 
      AND (
        (tests.course_id IS NOT NULL AND EXISTS (
          SELECT 1 FROM courses 
          WHERE courses.id = tests.course_id 
          AND courses.institute_id = get_current_institute_id()
        )) OR
        (tests.lesson_id IS NOT NULL AND EXISTS (
          SELECT 1 FROM lessons 
          JOIN courses ON courses.id = lessons.course_id
          WHERE lessons.id = tests.lesson_id 
          AND courses.institute_id = get_current_institute_id()
        )) OR
        tests.institute_id = get_current_institute_id()
      )
    )
  );

-- Course Bundles RLS Policies
CREATE POLICY course_bundles_tenant_isolation ON course_bundles
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR 
    institute_id = get_current_institute_id()
  );

-- Bundle Courses RLS Policies
CREATE POLICY bundle_courses_tenant_isolation ON bundle_courses
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR
    EXISTS (
      SELECT 1 FROM course_bundles 
      WHERE course_bundles.id = bundle_courses.bundle_id 
      AND course_bundles.institute_id = get_current_institute_id()
    )
  );

-- Course Enrollments RLS Policies
CREATE POLICY course_enrollments_tenant_isolation ON course_enrollments
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR
    EXISTS (
      SELECT 1 FROM courses 
      WHERE courses.id = course_enrollments.course_id 
      AND courses.institute_id = get_current_institute_id()
    )
  );

-- Lesson Progress RLS Policies
CREATE POLICY lesson_progress_tenant_isolation ON lesson_progress
  FOR ALL
  TO authenticated
  USING (
    is_super_admin() OR
    EXISTS (
      SELECT 1 FROM lessons 
      JOIN courses ON courses.id = lessons.course_id
      WHERE lessons.id = lesson_progress.lesson_id 
      AND courses.institute_id = get_current_institute_id()
    )
  );

-- Create indexes to support RLS policies efficiently
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_courses_institute_id ON courses(institute_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_courses_branch_id ON courses(branch_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tests_institute_id ON tests(institute_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tests_course_id ON tests(course_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tests_lesson_id ON tests(lesson_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_question_banks_institute_id ON question_banks(institute_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_question_banks_is_public ON question_banks(is_public);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_course_bundles_institute_id ON course_bundles(institute_id);

-- Create function to set session variables for tenant isolation
CREATE OR REPLACE FUNCTION set_tenant_context(
  p_institute_id UUID,
  p_branch_id UUID DEFAULT NULL,
  p_is_super_admin BOOLEAN DEFAULT FALSE,
  p_is_institute_admin BOOLEAN DEFAULT FALSE
)
RETURNS VOID AS $$
BEGIN
  -- Set session variables for RLS policies
  PERFORM set_config('app.current_institute_id', p_institute_id::TEXT, false);
  
  IF p_branch_id IS NOT NULL THEN
    PERFORM set_config('app.current_branch_id', p_branch_id::TEXT, false);
  END IF;
  
  PERFORM set_config('app.is_super_admin', p_is_super_admin::TEXT, false);
  PERFORM set_config('app.is_institute_admin', p_is_institute_admin::TEXT, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clear tenant context
CREATE OR REPLACE FUNCTION clear_tenant_context()
RETURNS VOID AS $$
BEGIN
  -- Clear session variables
  PERFORM set_config('app.current_institute_id', '', false);
  PERFORM set_config('app.current_branch_id', '', false);
  PERFORM set_config('app.is_super_admin', 'false', false);
  PERFORM set_config('app.is_institute_admin', 'false', false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_current_institute_id() TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_branch_id() TO authenticated;
GRANT EXECUTE ON FUNCTION is_super_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION is_institute_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION set_tenant_context(UUID, UUID, BOOLEAN, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION clear_tenant_context() TO authenticated;

-- Create role for application connections
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'course_builder_app') THEN
    CREATE ROLE course_builder_app;
  END IF;
END
$$;

-- Grant permissions to application role
GRANT authenticated TO course_builder_app;
GRANT USAGE ON SCHEMA public TO course_builder_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO course_builder_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO course_builder_app;
