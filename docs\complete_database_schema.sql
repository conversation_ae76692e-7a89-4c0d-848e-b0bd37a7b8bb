-- =====================================================
-- GROUPS EXAM LMS SAAS - COMPLETE DATABASE SCHEMA
-- Based on all documentation in docs/ directory
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. CORE PLATFORM TABLES
-- =====================================================

-- Subscription Plans (Commission-based model)
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL, -- 'Starter', 'Growth', 'Professional', 'Enterprise'
    description TEXT,

    -- First month platform cost (one-time setup fee)
    setup_fee DECIMAL(10,2) NOT NULL, -- Platform cost + SSL activation

    -- Commission-based pricing (from 2nd month onwards)
    commission_percentage DECIMAL(5,2) NOT NULL, -- Percentage of student course purchases

    -- Plan Limits
    max_students INTEGER, -- -1 for unlimited
    max_trainers INTEGER, -- -1 for unlimited
    max_branches INTEGER, -- -1 for unlimited
    max_courses INTEGER, -- -1 for unlimited
    storage_limit_gb INTEGER, -- Local storage limit

    -- Features (what's included in the plan)
    features JSONB DEFAULT '{}', -- {"courses": true, "marketplace": true, "live_classes": true, "online_exams": true, "blog": true, "analytics": true}

    -- Plan settings
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- 2. INSTITUTE MANAGEMENT
-- =====================================================

-- Institutes (Tenants)
CREATE TABLE institutes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL, -- for subdomain
    description TEXT,
    logo_url VARCHAR(500),
    primary_color VARCHAR(7), -- hex color
    secondary_color VARCHAR(7),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    address TEXT,
    website_url VARCHAR(500),
    social_media JSONB DEFAULT '{}', -- {facebook, twitter, linkedin, instagram}
    
    -- Subscription details
    subscription_plan_id UUID REFERENCES subscription_plans(id),
    subscription_status VARCHAR(20) DEFAULT 'setup', -- 'setup', 'active', 'suspended', 'cancelled'
    subscription_start_date DATE,
    setup_fee_paid BOOLEAN DEFAULT false,
    setup_fee_paid_date DATE,
    commission_model_active BOOLEAN DEFAULT false, -- Activated after setup fee payment
    
    -- Settings
    timezone VARCHAR(50) DEFAULT 'UTC',
    default_language VARCHAR(10) DEFAULT 'en',
    marketplace_enabled BOOLEAN DEFAULT false,
    student_registration_enabled BOOLEAN DEFAULT false,
    website_status VARCHAR(20) DEFAULT 'setup', -- 'setup', 'pending', 'live'
    
    -- Metadata
    establishment_year INTEGER,
    accreditations TEXT[],
    specializations TEXT[],
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Institute Domains
CREATE TABLE institute_domains (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    domain_name VARCHAR(255) NOT NULL,
    domain_type VARCHAR(20) DEFAULT 'custom', -- 'subdomain', 'custom'
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'verified', 'active', 'failed'
    dns_records JSONB, -- DNS configuration details
    ssl_certificate_id VARCHAR(255),
    verification_token VARCHAR(255),
    verified_at TIMESTAMP,
    activated_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(domain_name)
);

-- =====================================================
-- 3. BRANCH MANAGEMENT
-- =====================================================

-- Institute Branches
CREATE TABLE branches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(10) NOT NULL, -- 'DLC', 'NYC', etc.
    slug VARCHAR(100) NOT NULL, -- for URL routing
    description TEXT,
    branch_type VARCHAR(50), -- 'physical', 'virtual', 'department'
    
    -- Location details
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(255),
    
    -- Operational details
    operating_hours JSONB, -- {monday: "9:00-18:00", ...}
    capacity INTEGER,
    
    -- Customization
    primary_color VARCHAR(7), -- branch-specific color
    secondary_color VARCHAR(7),
    
    -- Settings
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(institute_id, slug),
    UNIQUE(institute_id, code)
);

-- =====================================================
-- 4. USER MANAGEMENT
-- =====================================================

-- Users (All user types including Super Admins)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    profile_picture_url VARCHAR(500),

    -- User type and scope
    user_type VARCHAR(30) NOT NULL, -- 'super_admin', 'platform_staff', 'platform_accountant', 'technical_admin', 'institute_admin', 'trainer', 'student', 'branch_manager', 'staff'
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE, -- NULL for super admins and platform staff

    -- Super Admin specific fields
    platform_permissions JSONB DEFAULT '{}', -- For super admin and platform staff permissions

    -- Authentication
    email_verified BOOLEAN DEFAULT false,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP,
    last_login TIMESTAMP,

    -- Settings
    preferred_language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50),
    notification_preferences JSONB DEFAULT '{}',

    -- Status
    is_active BOOLEAN DEFAULT true,
    is_suspended BOOLEAN DEFAULT false,
    suspension_reason TEXT,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    -- Constraints
    CONSTRAINT check_super_admin_no_institute CHECK (
        (user_type IN ('super_admin', 'platform_staff', 'platform_accountant', 'technical_admin') AND institute_id IS NULL) OR
        (user_type NOT IN ('super_admin', 'platform_staff', 'platform_accountant', 'technical_admin') AND institute_id IS NOT NULL)
    )
);

-- User Roles and Permissions
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL, -- 'admin', 'trainer', 'mentor', 'educator', 'student', 'branch_manager'
    scope VARCHAR(20) DEFAULT 'institute', -- 'institute', 'branch'
    scope_id UUID, -- branch_id if scope is 'branch'
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    assigned_at TIMESTAMP DEFAULT NOW(),
    assigned_by UUID REFERENCES users(id)
);

-- User Branch Assignments
CREATE TABLE user_branch_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
    assignment_type VARCHAR(20) DEFAULT 'assigned', -- 'primary', 'secondary', 'assigned'
    permissions JSONB DEFAULT '{}',
    assigned_at TIMESTAMP DEFAULT NOW(),
    assigned_by UUID REFERENCES users(id),
    
    UNIQUE(user_id, branch_id)
);

-- Student Academic Information
CREATE TABLE student_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    target_exam VARCHAR(100), -- 'UPSC', 'Banking', 'SSC', etc.
    educational_background VARCHAR(255),
    study_goals TEXT,
    enrollment_date DATE DEFAULT CURRENT_DATE,
    student_id VARCHAR(50), -- institute-specific student ID
    
    -- Academic details
    current_level VARCHAR(50), -- 'beginner', 'intermediate', 'advanced'
    preferred_subjects TEXT[],
    study_schedule JSONB, -- preferred study times
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- Trainer Profiles
CREATE TABLE trainer_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    qualifications TEXT[],
    experience_years INTEGER,
    specializations TEXT[],
    bio TEXT,
    
    -- Teaching preferences
    teaching_subjects TEXT[],
    exam_types TEXT[],
    languages TEXT[],
    
    -- Referral system
    referral_code VARCHAR(20) UNIQUE,
    commission_rate DECIMAL(5,2) DEFAULT 10.00, -- percentage
    
    -- Performance metrics
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_students INTEGER DEFAULT 0,
    total_courses INTEGER DEFAULT 0,
    
    is_verified BOOLEAN DEFAULT false,
    verification_date TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- =====================================================
-- 5. THEME MANAGEMENT
-- =====================================================

-- Platform Themes (for groups-exam.com)
CREATE TABLE platform_themes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50), -- 'saas_modern', 'saas_corporate', 'saas_startup'
    version VARCHAR(20) DEFAULT '1.0.0',
    preview_image_url VARCHAR(500),
    demo_image_url VARCHAR(500),
    
    -- Theme configuration
    theme_config JSONB DEFAULT '{}',
    customizable_elements JSONB DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Institute Themes (for abc-institute.com)
CREATE TABLE institute_themes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50), -- 'education', 'corporate', 'modern', 'classic'
    version VARCHAR(20) DEFAULT '1.0.0',
    preview_image_url VARCHAR(500),
    demo_image_url VARCHAR(500),
    
    -- Theme configuration
    theme_config JSONB DEFAULT '{}',
    customizable_elements JSONB DEFAULT '{}',
    suitable_for TEXT[], -- ['coaching_centers', 'online_academies']
    features TEXT[], -- ['mobile_responsive', 'seo_optimized']
    
    -- Usage statistics
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Institute Theme Selections
CREATE TABLE institute_theme_selections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    theme_id UUID REFERENCES institute_themes(id),
    customizations JSONB DEFAULT '{}', -- colors, fonts, content customizations
    is_active BOOLEAN DEFAULT true,
    applied_at TIMESTAMP DEFAULT NOW(),
    applied_by UUID REFERENCES users(id),

    UNIQUE(institute_id, is_active) WHERE is_active = true
);

-- =====================================================
-- 6. COURSE MANAGEMENT
-- =====================================================

-- Exam Types and Categories
CREATE TABLE exam_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL, -- 'UPSC', 'Banking', 'SSC', 'NEET', 'JEE'
    description TEXT,
    category VARCHAR(50), -- 'government', 'entrance', 'competitive'
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Subjects
CREATE TABLE subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL, -- 'History', 'Mathematics', 'Physics'
    description TEXT,
    exam_type_id UUID REFERENCES exam_types(id),
    parent_subject_id UUID REFERENCES subjects(id), -- for sub-subjects
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Courses
CREATE TABLE courses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),

    -- Course details
    exam_type_id UUID REFERENCES exam_types(id),
    duration_months INTEGER,
    difficulty_level VARCHAR(20), -- 'beginner', 'intermediate', 'advanced'
    prerequisites TEXT,
    learning_outcomes TEXT[],

    -- Pricing
    price DECIMAL(10,2),
    original_price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    is_free BOOLEAN DEFAULT false,

    -- Content organization
    total_lessons INTEGER DEFAULT 0,
    total_duration_minutes INTEGER DEFAULT 0,

    -- Media
    thumbnail_url VARCHAR(500),
    preview_video_url VARCHAR(500),

    -- Status and workflow
    status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'review', 'verified', 'approved', 'published', 'archived'
    workflow_stage VARCHAR(20) DEFAULT 'creation', -- 'creation', 'verification', 'approval'

    -- Instructor
    created_by UUID REFERENCES users(id),
    instructor_id UUID REFERENCES users(id),

    -- Publishing
    published_at TIMESTAMP,
    published_by UUID REFERENCES users(id),

    -- Settings
    is_featured BOOLEAN DEFAULT false,
    allow_preview BOOLEAN DEFAULT true,
    certificate_enabled BOOLEAN DEFAULT true,

    -- Metadata
    tags TEXT[],
    language VARCHAR(10) DEFAULT 'en',

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(institute_id, slug)
);

-- Course Subjects (Many-to-Many)
CREATE TABLE course_subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    subject_id UUID REFERENCES subjects(id),
    order_index INTEGER DEFAULT 0,

    UNIQUE(course_id, subject_id)
);

-- Course Chapters
CREATE TABLE course_chapters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    subject_id UUID REFERENCES subjects(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    order_index INTEGER DEFAULT 0,

    -- Settings
    is_free BOOLEAN DEFAULT false,
    is_locked BOOLEAN DEFAULT false,
    unlock_after_chapter_id UUID REFERENCES course_chapters(id),

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Course Lessons
CREATE TABLE course_lessons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    chapter_id UUID REFERENCES course_chapters(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    content_type VARCHAR(20) NOT NULL, -- 'video', 'document', 'quiz', 'assignment'
    order_index INTEGER DEFAULT 0,

    -- Content details
    video_url VARCHAR(500),
    video_duration_seconds INTEGER,
    document_url VARCHAR(500),
    content_data JSONB, -- for quiz questions, assignments, etc.

    -- Settings
    is_free BOOLEAN DEFAULT false,
    is_mandatory BOOLEAN DEFAULT true,
    estimated_duration_minutes INTEGER,

    -- Status
    status VARCHAR(20) DEFAULT 'draft',

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Course Reviews and Ratings
CREATE TABLE course_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_verified BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(course_id, user_id)
);

-- =====================================================
-- 7. ENROLLMENT AND PROGRESS TRACKING
-- =====================================================

-- Course Enrollments
CREATE TABLE course_enrollments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Enrollment details
    enrollment_date TIMESTAMP DEFAULT NOW(),
    enrollment_type VARCHAR(20) DEFAULT 'paid', -- 'paid', 'free', 'referral', 'bulk'
    payment_id UUID, -- reference to payment record

    -- Progress tracking
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    completed_lessons INTEGER DEFAULT 0,
    total_lessons INTEGER DEFAULT 0,
    last_accessed TIMESTAMP,

    -- Completion
    is_completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP,
    certificate_issued BOOLEAN DEFAULT false,
    certificate_url VARCHAR(500),

    -- Status
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'paused', 'cancelled', 'completed'

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(course_id, user_id)
);

-- Lesson Progress
CREATE TABLE lesson_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    enrollment_id UUID REFERENCES course_enrollments(id) ON DELETE CASCADE,
    lesson_id UUID REFERENCES course_lessons(id) ON DELETE CASCADE,

    -- Progress details
    status VARCHAR(20) DEFAULT 'not_started', -- 'not_started', 'in_progress', 'completed'
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    time_spent_seconds INTEGER DEFAULT 0,

    -- Video progress (if applicable)
    video_progress_seconds INTEGER DEFAULT 0,
    video_completed BOOLEAN DEFAULT false,

    -- Completion
    completed_at TIMESTAMP,

    -- Tracking
    first_accessed TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT NOW(),
    access_count INTEGER DEFAULT 1,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(enrollment_id, lesson_id)
);

-- =====================================================
-- 8. ASSESSMENT AND EXAMINATION
-- =====================================================

-- Exams/Assessments
CREATE TABLE exams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,

    -- Exam configuration
    exam_type VARCHAR(20) NOT NULL, -- 'quiz', 'mock_test', 'practice', 'final', 'live'
    question_count INTEGER NOT NULL,
    duration_minutes INTEGER NOT NULL,
    passing_score DECIMAL(5,2) DEFAULT 60.00,

    -- Scheduling
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    is_scheduled BOOLEAN DEFAULT false,

    -- Settings
    allow_retakes BOOLEAN DEFAULT true,
    max_attempts INTEGER DEFAULT 3,
    show_results_immediately BOOLEAN DEFAULT true,
    randomize_questions BOOLEAN DEFAULT true,

    -- Status
    status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'published', 'active', 'completed', 'archived'

    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Exam Questions
CREATE TABLE exam_questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    exam_id UUID REFERENCES exams(id) ON DELETE CASCADE,
    question_text TEXT NOT NULL,
    question_type VARCHAR(20) DEFAULT 'multiple_choice', -- 'multiple_choice', 'true_false', 'short_answer', 'essay'

    -- Options (for multiple choice)
    options JSONB, -- [{"text": "Option A", "is_correct": false}, ...]
    correct_answer TEXT,
    explanation TEXT,

    -- Scoring
    points DECIMAL(5,2) DEFAULT 1.00,
    difficulty_level VARCHAR(20) DEFAULT 'medium', -- 'easy', 'medium', 'hard'

    -- Metadata
    subject_id UUID REFERENCES subjects(id),
    tags TEXT[],

    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Exam Attempts
CREATE TABLE exam_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    exam_id UUID REFERENCES exams(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Attempt details
    attempt_number INTEGER DEFAULT 1,
    started_at TIMESTAMP DEFAULT NOW(),
    submitted_at TIMESTAMP,
    duration_seconds INTEGER,

    -- Scoring
    total_questions INTEGER,
    answered_questions INTEGER DEFAULT 0,
    correct_answers INTEGER DEFAULT 0,
    score DECIMAL(5,2) DEFAULT 0.00,
    percentage DECIMAL(5,2) DEFAULT 0.00,

    -- Status
    status VARCHAR(20) DEFAULT 'in_progress', -- 'in_progress', 'submitted', 'auto_submitted', 'cancelled'
    is_passed BOOLEAN DEFAULT false,

    -- Responses
    responses JSONB DEFAULT '{}', -- question_id -> answer mapping

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- 9. LIVE CLASSES AND SESSIONS
-- =====================================================

-- Live Classes
CREATE TABLE live_classes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    instructor_id UUID REFERENCES users(id),

    -- Class details
    title VARCHAR(255) NOT NULL,
    description TEXT,
    subject_id UUID REFERENCES subjects(id),

    -- Scheduling
    scheduled_start TIMESTAMP NOT NULL,
    scheduled_end TIMESTAMP NOT NULL,
    actual_start TIMESTAMP,
    actual_end TIMESTAMP,
    timezone VARCHAR(50) DEFAULT 'UTC',

    -- Platform integration
    platform VARCHAR(20) DEFAULT 'zoom', -- 'zoom', 'youtube', 'custom'
    meeting_id VARCHAR(255),
    meeting_url VARCHAR(500),
    meeting_password VARCHAR(100),
    recording_url VARCHAR(500),

    -- Settings
    max_participants INTEGER,
    is_recorded BOOLEAN DEFAULT true,
    allow_chat BOOLEAN DEFAULT true,
    require_registration BOOLEAN DEFAULT false,

    -- Status
    status VARCHAR(20) DEFAULT 'scheduled', -- 'scheduled', 'live', 'completed', 'cancelled'

    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Live Class Participants
CREATE TABLE live_class_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    live_class_id UUID REFERENCES live_classes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Participation details
    joined_at TIMESTAMP,
    left_at TIMESTAMP,
    duration_seconds INTEGER DEFAULT 0,

    -- Engagement
    questions_asked INTEGER DEFAULT 0,
    chat_messages INTEGER DEFAULT 0,

    -- Status
    registration_status VARCHAR(20) DEFAULT 'registered', -- 'registered', 'attended', 'absent'

    registered_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(live_class_id, user_id)
);

-- =====================================================
-- 10. PAYMENT AND BILLING
-- =====================================================

-- Payment Gateway List (Super Admin manages available gateways)
CREATE TABLE payment_gateways (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL, -- 'stripe', 'razorpay', 'paypal', 'phonepe', 'paytm'
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    logo_url VARCHAR(500),

    -- Gateway details
    supported_currencies TEXT[] DEFAULT ARRAY['USD'],
    supported_countries TEXT[] DEFAULT ARRAY['US'],

    -- Integration details
    documentation_url VARCHAR(500),
    api_version VARCHAR(20),
    webhook_support BOOLEAN DEFAULT true,

    -- Configuration fields that institutes need to provide
    required_config_fields JSONB DEFAULT '{}', -- {"api_key": "string", "secret_key": "string", "webhook_secret": "string"}
    optional_config_fields JSONB DEFAULT '{}',

    -- Status
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,

    -- Metadata
    created_by UUID REFERENCES users(id), -- Super Admin who added this gateway
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Institute Payment Configurations (Institute Admin configures their keys)
CREATE TABLE institute_payment_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    payment_gateway_id UUID REFERENCES payment_gateways(id),

    -- Institute-specific configuration
    config_data JSONB NOT NULL, -- {"api_key": "sk_live_...", "secret_key": "...", "webhook_secret": "..."}

    -- Settings
    is_enabled BOOLEAN DEFAULT false,
    is_test_mode BOOLEAN DEFAULT true,

    -- Status
    status VARCHAR(20) DEFAULT 'inactive', -- 'inactive', 'testing', 'active', 'suspended'
    last_tested TIMESTAMP,
    test_result JSONB, -- Test transaction results

    -- Metadata
    configured_by UUID REFERENCES users(id), -- Institute Admin who configured
    configured_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(institute_id, payment_gateway_id)
);

-- Payments
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Payment details
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    institute_payment_config_id UUID REFERENCES institute_payment_configs(id), -- Which gateway config was used

    -- External payment details
    external_payment_id VARCHAR(255), -- Stripe/Razorpay payment ID
    external_transaction_id VARCHAR(255),
    gateway_response JSONB, -- Full gateway response for debugging

    -- Payment purpose
    payment_type VARCHAR(20) NOT NULL, -- 'course_purchase', 'setup_fee', 'refund'
    reference_id UUID, -- course_id, institute_id, etc.
    reference_type VARCHAR(20), -- 'course', 'setup_fee'

    -- Commission tracking (for course purchases)
    platform_commission_percentage DECIMAL(5,2), -- Commission rate at time of purchase
    platform_commission_amount DECIMAL(10,2), -- Calculated commission amount
    institute_revenue_amount DECIMAL(10,2), -- Amount after commission deduction

    -- Status
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'completed', 'failed', 'refunded', 'cancelled'

    -- Timestamps
    payment_date TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP,
    failed_at TIMESTAMP,
    failure_reason TEXT,

    -- Metadata
    metadata JSONB DEFAULT '{}',

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Platform Commission Tracking
CREATE TABLE platform_commissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    payment_id UUID REFERENCES payments(id) ON DELETE CASCADE,

    -- Commission details
    transaction_amount DECIMAL(10,2) NOT NULL,
    commission_percentage DECIMAL(5,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    institute_revenue DECIMAL(10,2) NOT NULL,

    -- Commission period
    commission_month INTEGER NOT NULL, -- 1-12
    commission_year INTEGER NOT NULL,

    -- Status
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'calculated', 'billed', 'paid', 'overdue'
    calculated_at TIMESTAMP,
    billed_at TIMESTAMP,
    paid_at TIMESTAMP,

    -- Metadata
    course_id UUID REFERENCES courses(id),
    student_id UUID REFERENCES users(id),

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Monthly Commission Bills (Automatic bill generation)
CREATE TABLE monthly_commission_bills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,

    -- Bill period
    bill_month INTEGER NOT NULL, -- 1-12
    bill_year INTEGER NOT NULL,

    -- Bill details
    bill_number VARCHAR(50) UNIQUE NOT NULL, -- INV-2024-03-ABC001
    total_course_sales DECIMAL(10,2) NOT NULL,
    total_commission_amount DECIMAL(10,2) NOT NULL,
    commission_percentage DECIMAL(5,2) NOT NULL,

    -- Bill dates
    bill_generated_date DATE NOT NULL,
    bill_due_date DATE NOT NULL,

    -- Payment details
    payment_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'paid', 'overdue', 'cancelled'
    payment_date DATE,
    payment_method VARCHAR(50), -- 'bank_transfer', 'online', 'cheque'
    payment_reference VARCHAR(100),

    -- Late fees
    late_fee_amount DECIMAL(10,2) DEFAULT 0.00,
    late_fee_applied_date DATE,

    -- Bill metadata
    total_transactions INTEGER DEFAULT 0,
    bill_pdf_url VARCHAR(500),
    notes TEXT,

    -- Audit
    generated_by UUID REFERENCES users(id), -- System or admin who generated
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(institute_id, bill_month, bill_year)
);

-- Commission Bill Line Items (Detailed breakdown)
CREATE TABLE commission_bill_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bill_id UUID REFERENCES monthly_commission_bills(id) ON DELETE CASCADE,
    commission_id UUID REFERENCES platform_commissions(id),

    -- Item details
    course_name VARCHAR(255),
    student_name VARCHAR(255),
    transaction_date DATE,
    course_price DECIMAL(10,2),
    commission_rate DECIMAL(5,2),
    commission_amount DECIMAL(10,2),

    created_at TIMESTAMP DEFAULT NOW()
);

-- Referral System
CREATE TABLE referrals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    referrer_id UUID REFERENCES users(id), -- trainer who referred
    referred_user_id UUID REFERENCES users(id), -- student who was referred
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,

    -- Referral details
    referral_code VARCHAR(20) NOT NULL,
    referral_source VARCHAR(50), -- 'social_media', 'email', 'direct'

    -- Conversion tracking
    is_converted BOOLEAN DEFAULT false,
    converted_at TIMESTAMP,
    conversion_value DECIMAL(10,2) DEFAULT 0.00,

    -- Commission
    commission_rate DECIMAL(5,2), -- percentage
    commission_amount DECIMAL(10,2) DEFAULT 0.00,
    commission_paid BOOLEAN DEFAULT false,
    commission_paid_at TIMESTAMP,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- 11. NOTIFICATIONS AND COMMUNICATIONS
-- =====================================================

-- Notification Templates
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'email', 'sms', 'push', 'whatsapp'
    subject VARCHAR(255),
    content TEXT NOT NULL,
    variables JSONB DEFAULT '{}', -- template variables
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Notification details
    type VARCHAR(50) NOT NULL, -- 'course_enrollment', 'payment_success', 'class_reminder'
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,

    -- Delivery
    delivery_method VARCHAR(20) NOT NULL, -- 'email', 'sms', 'push', 'in_app'
    delivery_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'failed'
    delivered_at TIMESTAMP,

    -- Interaction
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP,

    -- Metadata
    metadata JSONB DEFAULT '{}',

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- 12. ANALYTICS AND REPORTING
-- =====================================================

-- User Activity Logs
CREATE TABLE user_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,

    -- Activity details
    activity_type VARCHAR(50) NOT NULL, -- 'login', 'course_access', 'lesson_complete', 'exam_attempt'
    activity_description TEXT,

    -- Context
    resource_type VARCHAR(50), -- 'course', 'lesson', 'exam'
    resource_id UUID,

    -- Session info
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,

    -- Metadata
    metadata JSONB DEFAULT '{}',

    created_at TIMESTAMP DEFAULT NOW()
);

-- Platform Analytics (Aggregated Data)
CREATE TABLE platform_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    metric_type VARCHAR(50) NOT NULL, -- 'daily_active_users', 'course_enrollments', 'revenue'

    -- Scope
    scope_type VARCHAR(20) DEFAULT 'platform', -- 'platform', 'institute', 'branch'
    scope_id UUID, -- institute_id or branch_id

    -- Metrics
    metric_value DECIMAL(15,2) NOT NULL,
    metric_count INTEGER DEFAULT 0,

    -- Metadata
    metadata JSONB DEFAULT '{}',

    created_at TIMESTAMP DEFAULT NOW(),

    UNIQUE(date, metric_type, scope_type, scope_id)
);

-- =====================================================
-- 13. CONTENT MANAGEMENT
-- =====================================================

-- File Uploads and Media
CREATE TABLE media_files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    uploaded_by UUID REFERENCES users(id),

    -- File details
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size_bytes BIGINT,
    mime_type VARCHAR(100),
    file_type VARCHAR(20), -- 'image', 'video', 'document', 'audio'

    -- Processing status (for videos)
    processing_status VARCHAR(20) DEFAULT 'completed', -- 'pending', 'processing', 'completed', 'failed'

    -- Usage tracking
    usage_type VARCHAR(50), -- 'course_content', 'profile_picture', 'institute_logo'
    reference_id UUID,

    -- CDN and optimization
    cdn_url VARCHAR(500),
    thumbnail_url VARCHAR(500),

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- 14. SYSTEM CONFIGURATION
-- =====================================================

-- System Settings
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    data_type VARCHAR(20) DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
    is_public BOOLEAN DEFAULT false,
    updated_by UUID REFERENCES users(id), -- References users table (super admin)
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Audit Logs
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id), -- References users table (any user type)
    user_type VARCHAR(30), -- 'super_admin', 'platform_staff', 'institute_admin', 'trainer', 'student'
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE, -- NULL for platform-level actions

    -- Action details
    action VARCHAR(100) NOT NULL, -- 'create_course', 'update_user', 'delete_branch'
    resource_type VARCHAR(50), -- 'course', 'user', 'branch', 'payment'
    resource_id UUID,

    -- Changes
    old_values JSONB,
    new_values JSONB,

    -- Context
    ip_address INET,
    user_agent TEXT,

    created_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- 15. INDEXES FOR PERFORMANCE
-- =====================================================

-- User indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_institute_id ON users(institute_id);
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_is_active ON users(is_active);

-- Course indexes
CREATE INDEX idx_courses_institute_id ON courses(institute_id);
CREATE INDEX idx_courses_status ON courses(status);
CREATE INDEX idx_courses_exam_type_id ON courses(exam_type_id);
CREATE INDEX idx_courses_created_by ON courses(created_by);
CREATE INDEX idx_courses_published_at ON courses(published_at);

-- Enrollment indexes
CREATE INDEX idx_course_enrollments_user_id ON course_enrollments(user_id);
CREATE INDEX idx_course_enrollments_course_id ON course_enrollments(course_id);
CREATE INDEX idx_course_enrollments_status ON course_enrollments(status);
CREATE INDEX idx_course_enrollments_enrollment_date ON course_enrollments(enrollment_date);

-- Payment indexes
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_institute_id ON payments(institute_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_payment_date ON payments(payment_date);
CREATE INDEX idx_payments_institute_payment_config_id ON payments(institute_payment_config_id);

-- Payment gateway indexes
CREATE INDEX idx_payment_gateways_is_active ON payment_gateways(is_active);
CREATE INDEX idx_payment_gateways_name ON payment_gateways(name);
CREATE INDEX idx_institute_payment_configs_institute_id ON institute_payment_configs(institute_id);
CREATE INDEX idx_institute_payment_configs_status ON institute_payment_configs(status);

-- Commission tracking indexes
CREATE INDEX idx_platform_commissions_institute_id ON platform_commissions(institute_id);
CREATE INDEX idx_platform_commissions_status ON platform_commissions(status);
CREATE INDEX idx_platform_commissions_month_year ON platform_commissions(commission_year, commission_month);
CREATE INDEX idx_platform_commissions_payment_id ON platform_commissions(payment_id);

-- Monthly billing indexes
CREATE INDEX idx_monthly_commission_bills_institute_id ON monthly_commission_bills(institute_id);
CREATE INDEX idx_monthly_commission_bills_status ON monthly_commission_bills(payment_status);
CREATE INDEX idx_monthly_commission_bills_month_year ON monthly_commission_bills(bill_year, bill_month);
CREATE INDEX idx_monthly_commission_bills_due_date ON monthly_commission_bills(bill_due_date);
CREATE INDEX idx_commission_bill_items_bill_id ON commission_bill_items(bill_id);

-- Activity log indexes
CREATE INDEX idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX idx_user_activity_logs_institute_id ON user_activity_logs(institute_id);
CREATE INDEX idx_user_activity_logs_activity_type ON user_activity_logs(activity_type);
CREATE INDEX idx_user_activity_logs_created_at ON user_activity_logs(created_at);

-- Branch indexes
CREATE INDEX idx_branches_institute_id ON branches(institute_id);
CREATE INDEX idx_branches_is_active ON branches(is_active);

-- Domain indexes
CREATE INDEX idx_institute_domains_institute_id ON institute_domains(institute_id);
CREATE INDEX idx_institute_domains_status ON institute_domains(status);
CREATE INDEX idx_institute_domains_domain_name ON institute_domains(domain_name);

-- =====================================================
-- 16. TRIGGERS AND FUNCTIONS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at trigger to relevant tables
CREATE TRIGGER update_institutes_updated_at BEFORE UPDATE ON institutes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_enrollments_updated_at BEFORE UPDATE ON course_enrollments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_branches_updated_at BEFORE UPDATE ON branches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically activate institute website when domain is verified
CREATE OR REPLACE FUNCTION activate_institute_website()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'active' AND OLD.status != 'active' THEN
        -- Enable institute website features
        UPDATE institutes
        SET marketplace_enabled = true,
            student_registration_enabled = true,
            website_status = 'live'
        WHERE id = NEW.institute_id;

        -- Apply selected theme if any
        UPDATE institute_theme_selections
        SET is_active = true
        WHERE institute_id = NEW.institute_id AND is_active = false;

        -- Create notification
        INSERT INTO notifications (institute_id, user_id, type, title, message, delivery_method)
        SELECT NEW.institute_id, u.id, 'domain_activated',
               'Domain Activated Successfully',
               'Your website is now live at ' || NEW.domain_name,
               'email'
        FROM users u
        WHERE u.institute_id = NEW.institute_id AND u.user_type = 'institute_admin';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER domain_activation_trigger
    AFTER UPDATE ON institute_domains
    FOR EACH ROW
    EXECUTE FUNCTION activate_institute_website();

-- Function to update course statistics
CREATE OR REPLACE FUNCTION update_course_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Update enrollment count
        UPDATE courses
        SET total_enrollments = (
            SELECT COUNT(*) FROM course_enrollments
            WHERE course_id = NEW.course_id AND status = 'active'
        )
        WHERE id = NEW.course_id;
    ELSIF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        -- Update enrollment count when status changes
        UPDATE courses
        SET total_enrollments = (
            SELECT COUNT(*) FROM course_enrollments
            WHERE course_id = NEW.course_id AND status = 'active'
        )
        WHERE id = NEW.course_id;
    END IF;
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Add total_enrollments column to courses table
ALTER TABLE courses ADD COLUMN total_enrollments INTEGER DEFAULT 0;

CREATE TRIGGER update_course_enrollment_stats
    AFTER INSERT OR UPDATE ON course_enrollments
    FOR EACH ROW
    EXECUTE FUNCTION update_course_stats();

-- Function to calculate platform commission on course purchases
CREATE OR REPLACE FUNCTION calculate_platform_commission()
RETURNS TRIGGER AS $$
DECLARE
    institute_plan_commission DECIMAL(5,2);
    commission_amt DECIMAL(10,2);
    institute_revenue DECIMAL(10,2);
BEGIN
    -- Only process course purchases that are completed
    IF NEW.payment_type = 'course_purchase' AND NEW.status = 'completed' AND OLD.status != 'completed' THEN
        -- Get commission percentage from institute's plan
        SELECT sp.commission_percentage INTO institute_plan_commission
        FROM institutes i
        JOIN subscription_plans sp ON i.subscription_plan_id = sp.id
        WHERE i.id = NEW.institute_id;

        -- Calculate commission amounts
        commission_amt := NEW.amount * (institute_plan_commission / 100);
        institute_revenue := NEW.amount - commission_amt;

        -- Update payment record with commission details
        UPDATE payments
        SET platform_commission_percentage = institute_plan_commission,
            platform_commission_amount = commission_amt,
            institute_revenue_amount = institute_revenue
        WHERE id = NEW.id;

        -- Create commission tracking record
        INSERT INTO platform_commissions (
            institute_id, payment_id, transaction_amount, commission_percentage,
            commission_amount, institute_revenue, commission_month, commission_year,
            course_id, student_id, status
        ) VALUES (
            NEW.institute_id, NEW.id, NEW.amount, institute_plan_commission,
            commission_amt, institute_revenue,
            EXTRACT(MONTH FROM NEW.completed_at), EXTRACT(YEAR FROM NEW.completed_at),
            NEW.reference_id, NEW.user_id, 'calculated'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_commission_trigger
    AFTER UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION calculate_platform_commission();

-- Function to generate monthly commission bills automatically
CREATE OR REPLACE FUNCTION generate_monthly_commission_bills(target_month INTEGER, target_year INTEGER)
RETURNS INTEGER AS $$
DECLARE
    institute_record RECORD;
    bill_record RECORD;
    commission_total DECIMAL(10,2);
    sales_total DECIMAL(10,2);
    transaction_count INTEGER;
    bill_number VARCHAR(50);
    bills_generated INTEGER := 0;
BEGIN
    -- Loop through all institutes that have commission activity
    FOR institute_record IN
        SELECT DISTINCT i.id, i.name, i.slug, sp.commission_percentage
        FROM institutes i
        JOIN subscription_plans sp ON i.subscription_plan_id = sp.id
        WHERE i.commission_model_active = true
        AND EXISTS (
            SELECT 1 FROM platform_commissions pc
            WHERE pc.institute_id = i.id
            AND pc.commission_month = target_month
            AND pc.commission_year = target_year
            AND pc.status = 'calculated'
        )
    LOOP
        -- Calculate totals for this institute
        SELECT
            COALESCE(SUM(commission_amount), 0),
            COALESCE(SUM(transaction_amount), 0),
            COUNT(*)
        INTO commission_total, sales_total, transaction_count
        FROM platform_commissions
        WHERE institute_id = institute_record.id
        AND commission_month = target_month
        AND commission_year = target_year
        AND status = 'calculated';

        -- Skip if no commissions to bill
        IF commission_total <= 0 THEN
            CONTINUE;
        END IF;

        -- Generate bill number
        bill_number := 'INV-' || target_year || '-' || LPAD(target_month::TEXT, 2, '0') || '-' || UPPER(LEFT(institute_record.slug, 6)) || LPAD((SELECT COUNT(*) + 1 FROM monthly_commission_bills WHERE bill_year = target_year AND bill_month = target_month)::TEXT, 3, '0');

        -- Create monthly bill
        INSERT INTO monthly_commission_bills (
            institute_id, bill_month, bill_year, bill_number,
            total_course_sales, total_commission_amount, commission_percentage,
            bill_generated_date, bill_due_date, total_transactions,
            generated_by
        ) VALUES (
            institute_record.id, target_month, target_year, bill_number,
            sales_total, commission_total, institute_record.commission_percentage,
            CURRENT_DATE, CURRENT_DATE + INTERVAL '30 days', transaction_count,
            (SELECT id FROM users WHERE user_type = 'super_admin' LIMIT 1)
        ) RETURNING id INTO bill_record;

        -- Create bill line items
        INSERT INTO commission_bill_items (
            bill_id, commission_id, course_name, student_name, transaction_date,
            course_price, commission_rate, commission_amount
        )
        SELECT
            bill_record.id, pc.id, c.title, u.full_name, p.completed_at::DATE,
            pc.transaction_amount, pc.commission_percentage, pc.commission_amount
        FROM platform_commissions pc
        LEFT JOIN courses c ON pc.course_id = c.id
        LEFT JOIN users u ON pc.student_id = u.id
        LEFT JOIN payments p ON pc.payment_id = p.id
        WHERE pc.institute_id = institute_record.id
        AND pc.commission_month = target_month
        AND pc.commission_year = target_year
        AND pc.status = 'calculated';

        -- Update commission status to billed
        UPDATE platform_commissions
        SET status = 'billed', billed_at = NOW()
        WHERE institute_id = institute_record.id
        AND commission_month = target_month
        AND commission_year = target_year
        AND status = 'calculated';

        bills_generated := bills_generated + 1;
    END LOOP;

    RETURN bills_generated;
END;
$$ LANGUAGE plpgsql;

-- Function to generate referral codes
CREATE OR REPLACE FUNCTION generate_referral_code(trainer_name TEXT)
RETURNS TEXT AS $$
DECLARE
    base_code TEXT;
    final_code TEXT;
    counter INTEGER := 1;
BEGIN
    -- Create base code from trainer name
    base_code := UPPER(REGEXP_REPLACE(trainer_name, '[^A-Za-z]', '', 'g'));
    base_code := LEFT(base_code, 8);

    -- Ensure uniqueness
    final_code := base_code;
    WHILE EXISTS (SELECT 1 FROM trainer_profiles WHERE referral_code = final_code) LOOP
        final_code := base_code || counter::TEXT;
        counter := counter + 1;
    END LOOP;

    RETURN final_code;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically generate bills on the 1st of each month
CREATE OR REPLACE FUNCTION auto_generate_monthly_bills()
RETURNS INTEGER AS $$
DECLARE
    prev_month INTEGER;
    prev_year INTEGER;
    bills_count INTEGER;
BEGIN
    -- Calculate previous month and year
    IF EXTRACT(MONTH FROM CURRENT_DATE) = 1 THEN
        prev_month := 12;
        prev_year := EXTRACT(YEAR FROM CURRENT_DATE) - 1;
    ELSE
        prev_month := EXTRACT(MONTH FROM CURRENT_DATE) - 1;
        prev_year := EXTRACT(YEAR FROM CURRENT_DATE);
    END IF;

    -- Generate bills for previous month
    SELECT generate_monthly_commission_bills(prev_month, prev_year) INTO bills_count;

    -- Log the bill generation
    INSERT INTO system_settings (key, value, description, updated_at)
    VALUES (
        'last_bill_generation',
        CURRENT_DATE::TEXT,
        'Last automatic bill generation date - ' || bills_count || ' bills generated',
        NOW()
    )
    ON CONFLICT (key) DO UPDATE SET
        value = EXCLUDED.value,
        description = EXCLUDED.description,
        updated_at = EXCLUDED.updated_at;

    RETURN bills_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 17. INITIAL DATA SETUP
-- =====================================================

-- Insert default subscription plans (Commission-based model)
INSERT INTO subscription_plans (name, description, setup_fee, commission_percentage, max_students, max_trainers, max_branches, max_courses, storage_limit_gb, features, is_featured, sort_order) VALUES

('Starter', 'Perfect for new institutes getting started', 99.00, 15.00, 100, 5, 1, 10, 5,
 '{"courses": true, "marketplace": false, "live_classes": false, "online_exams": true, "blog": false, "analytics": "basic", "storage": "5GB"}',
 false, 1),

('Growth', 'Great for growing institutes', 199.00, 12.00, 500, 15, 3, 50, 25,
 '{"courses": true, "marketplace": true, "live_classes": true, "online_exams": true, "blog": true, "analytics": "advanced", "storage": "25GB", "custom_domain": true}',
 true, 2),

('Professional', 'For established educational organizations', 399.00, 10.00, 2000, 50, 10, 200, 100,
 '{"courses": true, "marketplace": true, "live_classes": true, "online_exams": true, "blog": true, "analytics": "premium", "storage": "100GB", "custom_domain": true, "api_access": true, "white_label": true}',
 false, 3),

('Enterprise', 'For large educational networks', 799.00, 8.00, -1, -1, -1, -1, 500,
 '{"courses": true, "marketplace": true, "live_classes": true, "online_exams": true, "blog": true, "analytics": "enterprise", "storage": "500GB", "custom_domain": true, "api_access": true, "white_label": true, "priority_support": true, "custom_integrations": true}',
 false, 4);

-- Insert default exam types
INSERT INTO exam_types (name, description, category) VALUES
('UPSC', 'Union Public Service Commission', 'government'),
('Banking', 'Banking Sector Examinations', 'government'),
('SSC', 'Staff Selection Commission', 'government'),
('NEET', 'National Eligibility cum Entrance Test', 'entrance'),
('JEE', 'Joint Entrance Examination', 'entrance'),
('CAT', 'Common Admission Test', 'entrance'),
('GATE', 'Graduate Aptitude Test in Engineering', 'entrance');

-- Insert default subjects
INSERT INTO subjects (name, description, exam_type_id) VALUES
('History', 'Ancient, Medieval and Modern History', (SELECT id FROM exam_types WHERE name = 'UPSC')),
('Geography', 'Physical and Human Geography', (SELECT id FROM exam_types WHERE name = 'UPSC')),
('Polity', 'Indian Constitution and Governance', (SELECT id FROM exam_types WHERE name = 'UPSC')),
('Economics', 'Indian Economy and Economic Development', (SELECT id FROM exam_types WHERE name = 'UPSC')),
('Mathematics', 'Quantitative Aptitude', (SELECT id FROM exam_types WHERE name = 'Banking')),
('Reasoning', 'Logical and Analytical Reasoning', (SELECT id FROM exam_types WHERE name = 'Banking')),
('English', 'English Language and Comprehension', (SELECT id FROM exam_types WHERE name = 'Banking'));

-- Insert default payment gateways (Super Admin managed)
INSERT INTO payment_gateways (name, display_name, description, supported_currencies, supported_countries, required_config_fields, optional_config_fields, documentation_url, is_active, is_featured) VALUES
('stripe', 'Stripe', 'Global payment processing platform with support for 135+ currencies',
 ARRAY['USD', 'EUR', 'GBP', 'INR', 'AUD', 'CAD'], ARRAY['US', 'GB', 'IN', 'AU', 'CA', 'SG'],
 '{"publishable_key": "string", "secret_key": "string", "webhook_secret": "string"}',
 '{"statement_descriptor": "string", "capture_method": "automatic"}',
 'https://stripe.com/docs/api', true, true),

('razorpay', 'Razorpay', 'Leading payment gateway for Indian businesses',
 ARRAY['INR'], ARRAY['IN'],
 '{"key_id": "string", "key_secret": "string", "webhook_secret": "string"}',
 '{"theme_color": "string", "company_name": "string"}',
 'https://razorpay.com/docs/api/', true, true),

('paypal', 'PayPal', 'Trusted global payment solution',
 ARRAY['USD', 'EUR', 'GBP', 'AUD', 'CAD'], ARRAY['US', 'GB', 'AU', 'CA', 'DE', 'FR'],
 '{"client_id": "string", "client_secret": "string"}',
 '{"sandbox_mode": "boolean", "brand_name": "string"}',
 'https://developer.paypal.com/docs/api/', true, false),

('phonepe', 'PhonePe', 'Digital payment platform for India',
 ARRAY['INR'], ARRAY['IN'],
 '{"merchant_id": "string", "salt_key": "string", "salt_index": "string"}',
 '{"callback_url": "string", "redirect_url": "string"}',
 'https://developer.phonepe.com/docs/', true, false),

('paytm', 'Paytm', 'Indian digital payment and financial services company',
 ARRAY['INR'], ARRAY['IN'],
 '{"merchant_id": "string", "merchant_key": "string", "website": "string"}',
 '{"industry_type": "string", "channel_id": "string"}',
 'https://developer.paytm.com/docs/', true, false);

-- Insert default notification templates
INSERT INTO notification_templates (name, type, subject, content) VALUES
('welcome_email', 'email', 'Welcome to {{institute_name}}!', 'Dear {{user_name}}, welcome to our learning platform...'),
('course_enrollment', 'email', 'Course Enrollment Confirmation', 'You have successfully enrolled in {{course_name}}...'),
('payment_success', 'email', 'Payment Successful', 'Your payment of {{amount}} has been processed successfully...'),
('class_reminder', 'email', 'Live Class Reminder', 'Your class {{class_name}} is starting in 30 minutes...');

-- Insert system settings
INSERT INTO system_settings (key, value, description, data_type) VALUES
('platform_name', 'Groups Exam LMS', 'Platform display name', 'string'),
('default_timezone', 'UTC', 'Default timezone for the platform', 'string'),
('max_file_upload_size', '100', 'Maximum file upload size in MB', 'number'),
('enable_referral_system', 'true', 'Enable referral system platform-wide', 'boolean'),
('default_commission_rate', '10.0', 'Default commission rate for referrals', 'number');

-- Insert initial super admin user (password should be hashed in real implementation)
INSERT INTO users (email, password_hash, full_name, user_type, platform_permissions, email_verified, is_active) VALUES
('<EMAIL>', '$2b$12$example_hashed_password', 'Super Administrator', 'super_admin',
 '{"platform_management": true, "institute_management": true, "user_management": true, "billing_management": true, "system_settings": true}',
 true, true);

-- =====================================================
-- END OF SCHEMA
-- =====================================================

-- Create a view for institute dashboard statistics
CREATE VIEW institute_dashboard_stats AS
SELECT
    i.id as institute_id,
    i.name as institute_name,
    COUNT(DISTINCT u.id) FILTER (WHERE u.user_type = 'student') as total_students,
    COUNT(DISTINCT u.id) FILTER (WHERE u.user_type = 'trainer') as total_trainers,
    COUNT(DISTINCT c.id) as total_courses,
    COUNT(DISTINCT ce.id) as total_enrollments,
    COUNT(DISTINCT b.id) as total_branches,
    COALESCE(SUM(p.amount) FILTER (WHERE p.status = 'completed'), 0) as total_revenue
FROM institutes i
LEFT JOIN users u ON i.id = u.institute_id AND u.is_active = true
LEFT JOIN courses c ON i.id = c.institute_id AND c.status = 'published'
LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.status = 'active'
LEFT JOIN branches b ON i.id = b.institute_id AND b.is_active = true
LEFT JOIN payments p ON i.id = p.institute_id AND p.payment_type = 'course_purchase'
GROUP BY i.id, i.name;

COMMENT ON DATABASE postgres IS 'Groups Exam LMS SaaS - Complete Database Schema';
COMMENT ON SCHEMA public IS 'Main schema for Groups Exam LMS SaaS platform with multi-tenant architecture';
