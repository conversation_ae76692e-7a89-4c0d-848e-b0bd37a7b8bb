# 🗄️ Complete Database Schema Overview - Groups Exam LMS SaaS

## 📋 Overview
This document provides an overview of the complete database schema for the Groups Exam LMS SaaS platform, designed based on all requirements from the documentation files.

## 🏗️ Database Architecture

### **Multi-Tenant SaaS Design**
- **Platform Level**: Super Admin management of the entire SaaS platform
- **Institute Level**: Individual educational institutes as tenants
- **Branch Level**: Multiple branches within each institute
- **User Level**: Role-based access control across all levels

## 📊 Database Statistics
- **Total Tables**: 35 core tables (optimized with single users table)
- **User Types**: 9 distinct user roles (including platform staff)
- **Billing Model**: Commission-based revenue sharing with setup fees
- **Authentication Routes**: Platform and institute-specific login systems
- **Multi-Tenant**: Complete isolation between institutes
- **Scalability**: Designed for thousands of institutes and millions of users

## 🗂️ Table Categories

### **1. Core Platform Tables (3 tables)**
- `subscription_plans` - SaaS pricing plans and features
- `system_settings` - Platform-wide configuration
- `audit_logs` - Complete audit trail for all actions

### **2. Institute Management (3 tables)**
- `institutes` - Educational institutes (tenants)
- `institute_domains` - Custom domain management and verification
- `branches` - Multi-branch support within institutes

### **3. User Management (5 tables)**
- `users` - **ALL user types including Super Admins** with role-based access
- `user_roles` - Flexible role and permission system
- `user_branch_assignments` - Branch-specific user assignments
- `student_profiles` - Student-specific academic information
- `trainer_profiles` - Trainer qualifications and referral system

### **4. Theme Management (4 tables)**
- `platform_themes` - Themes for main SaaS website
- `institute_themes` - Themes for institute landing pages
- `institute_theme_selections` - Applied themes with customizations
- Theme separation: Landing pages vs Course marketplace

### **5. Course Management (8 tables)**
- `exam_types` - UPSC, Banking, SSC, NEET, JEE, etc.
- `subjects` - Subject hierarchy and organization
- `courses` - Complete course management with workflow
- `course_subjects` - Many-to-many course-subject relationships
- `course_chapters` - Course content organization
- `course_lessons` - Individual lessons with multiple content types
- `course_reviews` - Student ratings and reviews
- Content workflow: Draft → Review → Verified → Approved → Published

### **6. Enrollment & Progress (3 tables)**
- `course_enrollments` - Student course enrollments
- `lesson_progress` - Detailed lesson-level progress tracking
- Progress analytics and completion certificates

### **7. Assessment System (3 tables)**
- `exams` - Quizzes, mock tests, practice exams, live exams
- `exam_questions` - Question bank with multiple question types
- `exam_attempts` - Student exam attempts and scoring

### **8. Live Classes (2 tables)**
- `live_classes` - Zoom/YouTube integration for live sessions
- `live_class_participants` - Attendance and engagement tracking

### **9. Payment & Billing (7 tables)**
- `payment_gateways` - Super Admin managed gateway list with configurations
- `institute_payment_configs` - Institute-specific API keys and settings
- `payments` - Complete payment processing with commission tracking
- `platform_commissions` - Commission calculation and tracking for revenue sharing
- `monthly_commission_bills` - Automated monthly bill generation for institutes
- `commission_bill_items` - Detailed line items for commission bills
- `referrals` - Trainer referral system with commission tracking

### **10. Communications (2 tables)**
- `notification_templates` - Email, SMS, WhatsApp templates
- `notifications` - Multi-channel notification delivery

### **11. Analytics & Reporting (2 tables)**
- `user_activity_logs` - Detailed user activity tracking
- `platform_analytics` - Aggregated metrics and KPIs

### **12. Content Management (1 table)**
- `media_files` - File uploads, CDN integration, media processing

## 🔐 Authentication System

### **Login Routes Structure**
```sql
-- Platform Level (groups-exam.com)
/auth/register        -- Institute registration (one-time)
/auth/admin/login     -- Super Admin & Platform Staff

-- Institute Level (abc-institute.com)  
/auth/login           -- Institute Admin, Staff, Trainers
/auth/user-register   -- Student registration
/auth/user-login      -- Student login
```

### **User Types & Access (All in Single Users Table)**
- **Super Admin**: Platform-wide control (institute_id = NULL)
- **Platform Staff**: Limited platform functions (institute_id = NULL)
- **Platform Accountant**: Billing management (institute_id = NULL)
- **Technical Admin**: System monitoring (institute_id = NULL)
- **Institute Admin**: Full institute management
- **Branch Manager**: Branch-specific management
- **Trainer**: Content creation and student management
- **Student**: Course access and learning
- **Staff**: Administrative support roles

## 🎨 Theme Management

### **Dual Theme System**
```sql
-- Platform Themes (for groups-exam.com)
platform_themes → SaaS marketing pages

-- Institute Themes (for abc-institute.com)
institute_themes → Institute landing pages
institute_theme_selections → Applied customizations

-- Standard Functionality (same for all)
Course marketplace, student dashboard, functional pages
```

## 🏢 Multi-Branch Architecture

### **Branch Support**
- **Geographic Branches**: Multiple physical locations
- **Virtual Branches**: Online divisions or departments
- **Branch Isolation**: Separate user assignments and analytics
- **Unified Management**: Institute admin controls all branches

## 💰 Commission-Based Billing System

### **Revenue Sharing Model**
```sql
Billing Structure:
├── Month 1: Setup Fee (Platform Cost + SSL Activation)
│   ├── One-time payment to platform
│   ├── Includes SSL certificate and domain setup
│   └── Activates institute website and features
│
├── Month 2+: Commission-Based Revenue Sharing
│   ├── Platform takes percentage of student course purchases
│   ├── Institute keeps remaining revenue
│   └── No monthly subscription fees

Commission Rates by Plan:
├── Starter Plan: 15% commission (Setup: $99)
├── Growth Plan: 12% commission (Setup: $199)
├── Professional Plan: 10% commission (Setup: $399)
└── Enterprise Plan: 8% commission (Setup: $799)
```

### **Plan Features and Limits**
```sql
Plan Comparison:
┌─────────────────────────────────────────────────────────────┐
│ Feature         │ Starter │ Growth │ Professional │ Enterprise │
├─────────────────────────────────────────────────────────────┤
│ Setup Fee       │ $99     │ $199   │ $399         │ $799       │
│ Commission      │ 15%     │ 12%    │ 10%          │ 8%         │
│ Max Students    │ 100     │ 500    │ 2,000        │ Unlimited  │
│ Max Trainers    │ 5       │ 15     │ 50           │ Unlimited  │
│ Max Branches    │ 1       │ 3      │ 10           │ Unlimited  │
│ Max Courses     │ 10      │ 50     │ 200          │ Unlimited  │
│ Storage         │ 5GB     │ 25GB   │ 100GB        │ 500GB      │
│ Marketplace     │ ❌      │ ✅     │ ✅           │ ✅         │
│ Live Classes    │ ❌      │ ✅     │ ✅           │ ✅         │
│ Online Exams    │ ✅      │ ✅     │ ✅           │ ✅         │
│ Blog            │ ❌      │ ✅     │ ✅           │ ✅         │
│ Custom Domain   │ ❌      │ ✅     │ ✅           │ ✅         │
│ API Access      │ ❌      │ ❌     │ ✅           │ ✅         │
│ White Label     │ ❌      │ ❌     │ ✅           │ ✅         │
└─────────────────────────────────────────────────────────────┘
```

### **Payment Gateway Management**
```sql
Super Admin Level:
├── payment_gateways (Available gateway list)
│   ├── Stripe, Razorpay, PayPal, PhonePe, Paytm
│   ├── Configuration requirements
│   └── Documentation and support details

Institute Admin Level:
├── institute_payment_configs (Their API keys)
│   ├── Select from available gateways
│   ├── Configure their own API keys
│   ├── Test mode and live mode
│   └── Enable/disable specific gateways

Payment Processing:
├── Uses institute-specific configurations
├── Automatic commission calculation
├── Revenue split tracking
└── Complete audit trail and tracking
```

### **Commission Calculation & Monthly Billing Process**
```sql
Student Course Purchase Flow:
1. Student pays ₹1000 for course
2. Payment processed via institute's gateway
3. Institute receives ₹880 immediately (88%)
4. Platform commission (₹120) tracked for monthly billing
5. Commission recorded in platform_commissions table

Monthly Billing Automation:
1. 1st of every month: Auto-generate bills for previous month
2. Calculate total commissions per institute
3. Create detailed bill with line items
4. Send email notification with PDF bill
5. Set 30-day payment due date
6. Track payment status and send reminders
```

### **Monthly Billing System**
```sql
Automated Bill Generation:
┌─────────────────────────────────────────────────────────────┐
│ monthly_commission_bills                                    │
│ ├── Bill number: INV-2024-03-ABC001                       │
│ ├── Total sales: $18,750                                  │
│ ├── Commission: $2,250 (12%)                              │
│ ├── Due date: 30 days from generation                     │
│ └── Payment status tracking                               │
│                                                             │
│ commission_bill_items (detailed breakdown)                 │
│ ├── Course name and student details                       │
│ ├── Individual transaction amounts                        │
│ ├── Commission rate and amount per transaction            │
│ └── Transaction dates for transparency                    │
└─────────────────────────────────────────────────────────────┘

Payment Status Flow:
pending → paid | overdue → paid (with late fees)
```

### **Referral System**
- Trainer-specific referral codes
- Commission tracking and payouts
- Conversion analytics

## 📈 Analytics & Reporting

### **Multi-Level Analytics**
- **Platform Level**: Super Admin insights across all institutes
- **Institute Level**: Complete institute performance metrics
- **Branch Level**: Branch-specific analytics and comparisons
- **User Level**: Individual progress and engagement tracking

## 🔧 Advanced Features

### **Automated Workflows**
- Domain verification triggers automatic website activation
- Course approval workflow with multiple stages
- Automatic progress tracking and certificate generation

### **Performance Optimizations**
- Comprehensive indexing strategy
- Efficient queries for multi-tenant data
- Optimized for high-volume operations

### **Data Integrity**
- Foreign key constraints for referential integrity
- Triggers for automatic data updates
- Audit logging for all critical operations

## 🚀 Scalability Features

### **Horizontal Scaling**
- Multi-tenant architecture supports unlimited institutes
- Branch system allows institute expansion
- User role system supports complex organizational structures

### **Performance Scaling**
- Indexed for fast queries across large datasets
- Optimized for real-time analytics
- Efficient storage of user activity and progress data

## 📋 Key Business Logic

### **Institute Onboarding Flow**
1. Institute registers on platform
2. Super Admin creates institute record
3. Institute admin sets up courses and users
4. Custom domain verification and activation
5. Automatic website launch with selected theme

### **Course Creation Workflow**
1. Trainer creates course content
2. Institute admin reviews and approves
3. Course goes through verification stages
4. Published course available for student enrollment

### **Student Learning Journey**
1. Student registers and enrolls in courses
2. Progress tracked at lesson level
3. Participates in live classes and exams
4. Receives certificates upon completion

This database schema provides a solid foundation for a scalable, multi-tenant LMS SaaS platform with comprehensive features for educational institutes, trainers, and students.
