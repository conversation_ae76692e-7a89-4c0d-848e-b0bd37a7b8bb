# Field Mapping Solution - Database vs API Field Names

## 🔧 **Issue Resolved**

**Problem**: Database has `branch_id_id` and `role_id_id` columns, but API should accept/return `branch_id` and `role_id`.

**Error**: `column "branch_id" does not exist` - Database hint: "Perhaps you meant to reference the column "users.branch_id_id"`

## ✅ **Solution: Field Mapping Layer**

Instead of changing the database schema (which could break existing data), implemented a field mapping layer in the API.

### **Architecture:**
```
API Layer (branch_id, role_id) 
    ↕️ Field Mapping
Database Layer (branch_id_id, role_id_id)
```

## 🎯 **Implementation Details**

### **1. Schema Reverted to Match Database**
```typescript
// Users.ts - Matches existing database structure
{
  name: 'branch_id',
  type: 'relationship',  // Creates branch_id_id in database
  relationTo: 'branches'
}

{
  name: 'role_id', 
  type: 'relationship',  // Creates role_id_id in database
  relationTo: 'roles'
}
```

### **2. API Input Mapping (CREATE/UPDATE)**
```typescript
// API accepts: { branch_id: "2", role_id: "7" }
// Maps to database fields automatically via Payload

const userData = {
  firstName,
  lastName,
  email: email.toLowerCase(),
  password,
  legacyRole: 'student',
  institute: req.instituteId,
  branch_id,  // Payload maps this to branch_id_id in database
  role_id,    // Payload maps this to role_id_id in database
  is_active
}
```

### **3. API Output Mapping (GET)**
```typescript
// Database returns: { branch_id_id: "2", role_id_id: "7" }
// Transform to API format: { branch_id: "2", role_id: "7" }

const transformedStudents = students.docs.map((student: any) => ({
  ...student,
  // Map database field names to API field names
  branch_id: student.branch_id_id || student.branch_id,
  role_id: student.role_id_id || student.role_id,
  // Remove database field names from response
  branch_id_id: undefined,
  role_id_id: undefined
}))
```

## 📋 **API Behavior**

### **Your Request (Works Now):**
```json
POST /api/institute-admin/students
{
  "firstName": "Vadi",
  "lastName": "Velan", 
  "email": "<EMAIL>",
  "phone": "09655008990",
  "password": "123456",
  "branch_id": "2",      // ✅ API accepts branch_id
  "role_id": "7",        // ✅ API accepts role_id
  "address": "253/4, Pattalamman Street\nAdiyur Post",
  "dateOfBirth": "2025-07-16",
  "gender": "male",
  "is_active": true
}
```

### **Database Storage:**
```sql
-- Payload automatically maps to database columns
INSERT INTO users (
  first_name, last_name, email, phone, password,
  legacy_role, institute_id, 
  branch_id_id,  -- API branch_id maps here
  role_id_id,    -- API role_id maps here
  address, date_of_birth, gender, is_active
) VALUES (...)
```

### **API Response:**
```json
GET /api/institute-admin/students
{
  "success": true,
  "data": [
    {
      "id": "student-id",
      "firstName": "Vadi",
      "lastName": "Velan",
      "email": "<EMAIL>",
      "branch_id": "2",    // ✅ API returns branch_id (mapped from branch_id_id)
      "role_id": "7",      // ✅ API returns role_id (mapped from role_id_id)
      // branch_id_id and role_id_id are hidden from response
    }
  ]
}
```

## 🔄 **Field Mapping Flow**

### **CREATE Student:**
1. **API Input**: `{ branch_id: "2", role_id: "7" }`
2. **Payload Processing**: Maps to `{ branch_id_id: "2", role_id_id: "7" }` in database
3. **Database Storage**: Stores in `branch_id_id` and `role_id_id` columns
4. **API Response**: Returns success with clean field names

### **GET Students:**
1. **Database Query**: Retrieves data with `branch_id_id` and `role_id_id`
2. **Response Transform**: Maps `branch_id_id` → `branch_id`, `role_id_id` → `role_id`
3. **API Response**: Returns clean field names to frontend

### **UPDATE Student:**
1. **API Input**: `{ branch_id: "3", role_id: "8" }`
2. **Payload Processing**: Maps to database columns automatically
3. **Database Update**: Updates `branch_id_id` and `role_id_id` columns
4. **API Response**: Returns success with clean field names

## ✅ **Benefits of This Approach**

### **1. Backward Compatibility**
- ✅ **Existing Data**: No database migration needed
- ✅ **Existing Queries**: Database structure unchanged
- ✅ **Zero Downtime**: No schema changes required

### **2. Clean API Interface**
- ✅ **Consistent Naming**: API uses `branch_id`, `role_id`
- ✅ **Frontend Friendly**: No confusing `_id` suffixes
- ✅ **Developer Experience**: Intuitive field names

### **3. Transparent Mapping**
- ✅ **Automatic**: Payload handles input mapping
- ✅ **Response Transform**: API layer handles output mapping
- ✅ **Seamless**: Frontend doesn't know about database field names

## 🎯 **Status: WORKING**

### **✅ All Operations Now Work:**
- ✅ **CREATE Student**: Accepts `branch_id`, `role_id` in request
- ✅ **GET Students**: Returns `branch_id`, `role_id` in response
- ✅ **GET Student Details**: Returns clean field names
- ✅ **UPDATE Student**: Accepts clean field names
- ✅ **Database Compatibility**: Works with existing `branch_id_id`, `role_id_id` columns

### **✅ Your Request Now Works:**
```json
{
  "firstName": "Vadi",
  "lastName": "Velan", 
  "email": "<EMAIL>",
  "branch_id": "2",  // ✅ Works perfectly
  "role_id": "7"     // ✅ Works perfectly
}
```

**Tamil Summary**: "API-la branch_id, role_id use pannalam, database-la branch_id_id, role_id_id-aa store aagum. Mapping automatically handle aagum!" 🎉
