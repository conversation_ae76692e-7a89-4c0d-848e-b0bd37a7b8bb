# Flexible Payload Format - Multiple Field Name Support

## ✅ **Both Formats Now Supported**

The student creation endpoint now accepts both field naming conventions:

### **Format 1: Underscore Format**
```json
{
  "firstName": "test",
  "lastName": "Eagelminds",
  "email": "<EMAIL>",
  "phone": "09655008990",
  "password": "123456",
  "branch_id": "4",        // ✅ Supported
  "role_id": "5",          // ✅ Supported
  "address": "253/4, Pattalamman Street\nAdiyur Post",
  "gender": "male",
  "is_active": true
}
```

### **Format 2: Simple Format (Your Preference)**
```json
{
  "firstName": "test",
  "lastName": "Eagelminds",
  "email": "<EMAIL>",
  "phone": "09655008990",
  "password": "123456",
  "branch": "4",           // ✅ Also supported
  "role": "5",             // ✅ Also supported
  "address": "253/4, Pattalamman Street\nAdiyur Post",
  "gender": "male",
  "is_active": true
}
```

## 🔧 **Implementation Logic**

```typescript
// Endpoint accepts both field names
const {
  branch_id,
  role_id,
  branch,    // Alternative field name
  role,      // Alternative field name
  // ... other fields
} = body

// Use whichever is provided
const branchId = branch_id || branch
const roleId = role_id || role

// Store in database with standard field names
if (branchId) userData.branch_id = branchId
if (roleId) userData.role_id = roleId
```

## 🎯 **Database Storage**

Both formats result in the same database storage:

```sql
INSERT INTO users (
  first_name, last_name, email, phone, password,
  legacy_role, institute_id,
  branch_id,              -- Always stored as branch_id
  role_id,                -- Always stored as role_id
  "isActive"
) VALUES (
  'test', 'Eagelminds', '<EMAIL>', '09655008990', 'hashed_password',
  'student', 'institute-id',
  '4',                    -- From branch: "4" or branch_id: "4"
  '5',                    -- From role: "5" or role_id: "5"
  true
)
```

## ✅ **Your Preferred Format Works**

```json
{
  "firstName": "test",
  "lastName": "Eagelminds",
  "email": "<EMAIL>",
  "phone": "09655008990",
  "password": "123456",
  "branch": "4",           // ✅ Perfect!
  "role": "5",             // ✅ Perfect!
  "address": "253/4, Pattalamman Street\nAdiyur Post",
  "gender": "male",
  "is_active": true
}
```

**This should work perfectly now!** 🎉
