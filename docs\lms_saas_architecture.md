# 🏗️ Overall Architecture – LMS SaaS Web Application

## ⚙️ 1. High-Level System Architecture

```
                +--------------------+
                |   Client Devices   |
                | (Web, Mobile, TV)  |
                +---------+----------+
                          |
                          v
           +-------------------------------+
           |         Frontend (Next.js)    |
           | TailwindCSS + Shadcn + SSR    |
           +-------------------------------+
                          |
                          v
           +-------------------------------+
           |       API Layer (Payload CMS) |
           |  Custom Express Routes / Auth |
           +-------------------------------+
                          |
                          v
           +-------------------------------+
           |        Database Layer         |
           | MongoDB / PostgreSQL (Payload)|
           +-------------------------------+
                          |
                          v
           +-------------------------------+
           |      External Services        |
           | Zoom, Stripe, WhatsApp, Email|
           +-------------------------------+
```

## 🧩 2. Key Modules Breakdown

| Module | Responsibility |
| ------ | -------------- |
|        |                |

| **Authentication**       | JWT-based login (1 device policy), role/plan validation                |
| ------------------------ | ---------------------------------------------------------------------- |
| **Institute Setup**      | Super Admin creates Institute with domain and limits                   |
| **User Management**      | Admin creates Trainers, Students, Sub-admins                           |
| **Course Engine**        | Create → Verify → Approve content; supports files, video, multilingual |
| **Exam Engine**          | Mock, Live, OMR, Aptitude, Descriptive Exam logic                      |
| **Live Class**           | Zoom/Youtube API integration, countdown, chat, watermark               |
| **Marketplace**          | Buy/request content from others with role-restricted preview           |
| **Payment Module**       | Commission-based billing: Setup fee + revenue sharing model            |
| **Referral System**      | Track trainer codes, commission %, discount application                |
| **Platform Landing Pages** | SaaS product marketing themes for main platform (groups-exam.com)     |
| **Institute Landing Pages** | Institute marketing themes with customization (abc-institute.com)     |
| **Course Marketplace**   | Standard Amazon-style course browsing, filtering, purchase (all institutes) |
| **Theme Management**     | Dual theme system: Platform themes + Institute themes with previews    |
| **Branch Management**    | Multi-location support, institute branding with branch customization, user assignment |
| **Notification System**  | Email, SMS, WhatsApp, Push, Telegram APIs                              |
| **Analytics**            | Course usage, referral stats, quiz scores, mock ranking, branch metrics|
| **Search & AI Layer**    | Keyword & AI-enhanced content/discussion/question search               |

## 🧱 3. Tech Stack

| Layer        | Stack                                                 |
| ------------ | ----------------------------------------------------- |
| Frontend     | **Next.js** (SSR/SSG), TailwindCSS, shadcn/ui         |
| Backend CMS  | **Payload CMS** (Headless CMS over Node.js/Express)   |
| Database     | PostgreSQL (with Payload PostgreSQL adapter)          |
| Auth         | JWT + custom Payload auth strategy                    |
| File Storage | Payload upload plugin (to Cloud, S3, or local)        |
| Deployment   | Institute Custom Domains, VPS / Railway (Payload)     |
| Messaging    | Twilio / WhatsApp Cloud API / Telegram Bot            |
| Payments     | Stripe, Razorpay                                      |
| Video        | Zoom API, YouTube Embed                               |
| PDF/Docs     | jsPDF, Watermark plugin for secure previews           |
| Real-time    | Optional: Socket.io or Webhooks for live exam updates |

## 🏢 4. Multi-Tenant + Role Structure + Branch Management

| Role            | Scope                          | Isolation                        | Branch Access                    |
| --------------- | ------------------------------ | -------------------------------- | -------------------------------- |
| Super Admin     | Global (all tenants)           | Full access                      | All institutes and branches      |
| Institute Admin | Specific institute/tenant      | Scoped to domain or subdomain    | All branches within institute    |
| Branch Manager  | Specific branch within institute| Branch-scoped permissions       | Single branch only               |
| Trainer         | Scoped to institute/branch     | Can only manage assigned content | Assigned branches only           |
| Student         | Scoped to their plan/institute | No create access                 | Enrolled branch(es) only         |

### Multi-Branch Architecture
- **Institute Level**: Each institute can have multiple physical/virtual branches
- **Branch Isolation**: Each branch can operate semi-independently with:
  - Branch-specific URL: `institute.com/downtown` or `downtown.institute.com`
  - Consistent institute branding (same logo, custom branch colors, local contact info)
  - Dedicated user base (trainers, students, staff)
  - Branch-specific courses and content access
  - Local analytics and reporting

### Tenant Isolation Levels
1. **Platform Level**: Each institute can operate on:
   - Subdomain: `institute1.groups-exam.com`
   - Custom Domain: `www.myinstitute.com`

2. **Branch Level**: Each branch within an institute can have:
   - Path-based routing: `www.myinstitute.com/downtown`
   - Subdomain routing: `downtown.myinstitute.com`
   - Dedicated landing pages with institute branding (consistent logo, custom colors)

- Super Admin can manage tenant themes, roles, billing, and quotas globally.
- Institute Admin can manage all branches within their institute.
- Branch Managers have limited control over their specific branch only.

## 🔐 6. Authentication & Login Structure

### Platform-Level Authentication Routes
```
Main Platform (groups-exam.com):
├── /auth/register (Institute Registration - First time only)
├── /auth/admin/login (Super Admin & Staff Login)
└── Public pages (marketing, features, pricing)

Institute Domain (abc-institute.com):
├── /auth/login (Institute Admin, Staff, Tutors)
├── /auth/user-register (Student Registration)
├── /auth/user-login (Student Login)
└── Public pages (landing, courses, about)
```

### User Access Patterns
```
Super Admin Level (groups-exam.com):
├── Super Admin → /auth/admin/login
├── Platform Staff → /auth/admin/login
├── Platform Accountant → /auth/admin/login
└── New Institute → /auth/register (one-time)

Institute Level (abc-institute.com):
├── Institute Admin → /auth/login
├── Institute Staff → /auth/login
├── Tutors/Trainers → /auth/login
└── Students → /auth/user-register → /auth/user-login
```

### Authentication Flow
```
Registration Flow:
1. Institute Registration: groups-exam.com/auth/register
2. Student Registration: abc-institute.com/auth/user-register

Login Flow:
1. Super Admin: groups-exam.com/auth/admin/login
2. Institute Users: abc-institute.com/auth/login
3. Students: abc-institute.com/auth/user-login
```

## 🎨 7. Dual Theme Management Architecture

### Theme System Overview
```
Platform Theme Flow:
[Super Admin] → Creates Platform Themes → [Platform Theme Library]
                                            ↓
[Super Admin] → Selects Platform Theme → [groups-exam.com]
                                            ↓
[Visitors] → Views SaaS Product Landing Page

Institute Theme & Domain Activation Flow:
[Super Admin] → Creates Institute Themes → [Institute Theme Library]
                                            ↓
[Institute Admin] → Browses Gallery → Selects Theme → Customizes
                                            ↓
[Institute Admin] → Requests Custom Domain → [Super Admin Verification]
                                            ↓
[Super Admin] → Verifies Domain → Activates → [Automatic Website Launch]
                                            ↓
[Live Website] → Theme Applied + Course Marketplace + Student Registration
                                            ↓
[Students/Public] → Register/Login → Browse/Purchase Courses → Access Content
```

### Theme Storage Structure
```
/public/themes/
├── /education-modern/
│   ├── preview.jpg (400x300px thumbnail)
│   ├── demo.jpg (1200x800px full preview)
│   ├── components/
│   │   ├── Header.tsx
│   │   ├── Hero.tsx
│   │   ├── About.tsx
│   │   ├── Courses.tsx
│   │   ├── Testimonials.tsx
│   │   └── Footer.tsx
│   ├── styles/
│   │   ├── globals.css
│   │   ├── components.css
│   │   └── responsive.css
│   └── theme.json (metadata)
├── /corporate-professional/
└── /coaching-academy/
```

### Theme Selection Database Schema
```sql
-- Themes table (managed by Super Admin)
CREATE TABLE themes (
  id UUID PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  category VARCHAR(50),
  preview_image VARCHAR(255),
  demo_image VARCHAR(255),
  version VARCHAR(20),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- Institute theme selections
CREATE TABLE institute_themes (
  id UUID PRIMARY KEY,
  institute_id UUID REFERENCES institutes(id),
  theme_id UUID REFERENCES themes(id),
  customizations JSONB, -- colors, fonts, content
  is_active BOOLEAN DEFAULT true,
  applied_at TIMESTAMP
);

-- Domain management and activation
CREATE TABLE institute_domains (
  id UUID PRIMARY KEY,
  institute_id UUID REFERENCES institutes(id),
  domain_name VARCHAR(255) NOT NULL,
  domain_type VARCHAR(20) DEFAULT 'custom', -- 'subdomain' or 'custom'
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'verified', 'active', 'failed'
  dns_records JSONB, -- DNS configuration details
  ssl_certificate_id VARCHAR(255),
  verified_at TIMESTAMP,
  activated_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Domain activation triggers automatic website launch
CREATE OR REPLACE FUNCTION activate_institute_website()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'active' AND OLD.status != 'active' THEN
    -- Automatically apply selected theme
    UPDATE institute_themes
    SET is_active = true
    WHERE institute_id = NEW.institute_id;

    -- Enable course marketplace
    UPDATE institutes
    SET marketplace_enabled = true,
        student_registration_enabled = true,
        website_status = 'live'
    WHERE id = NEW.institute_id;

    -- Send activation notification
    INSERT INTO notifications (institute_id, type, message)
    VALUES (NEW.institute_id, 'domain_activated',
            'Your website is now live at ' || NEW.domain_name);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER domain_activation_trigger
  AFTER UPDATE ON institute_domains
  FOR EACH ROW
  EXECUTE FUNCTION activate_institute_website();
```

## 🔁 5. Workflow Overview (Simplified)

```
[Super Admin]
   ↓ creates
[Institute]
   ↓ invites
[Admin] → [Trainers] → [Students]

→ Admin/Trainer creates Course
→ Passes through Verification Workflow
→ Course goes Live
→ Students Enroll via Plan / Referral
→ Attend Live Classes / Mock Exams
→ Analytics tracked
→ Trainer gets referral commission
→ Platform earns commission percentage
```

## 💰 7. Commission-Based Billing Architecture

### Revenue Sharing Model
```
Traditional SaaS vs Commission Model:
┌─────────────────────────────────────────────────────────────┐
│ Traditional SaaS         │ Our Commission Model             │
├─────────────────────────────────────────────────────────────┤
│ Monthly subscription fees│ One-time setup fee              │
│ Fixed costs regardless  │ Revenue-based costs              │
│ High churn risk         │ Growth-aligned partnership       │
│ Payment pressure        │ Pay only when earning            │
└─────────────────────────────────────────────────────────────┘
```

### Billing Flow Architecture
```
Institute Registration & Billing Flow:
┌─────────────────────────────────────────────────────────────┐
│ Step 1: Institute Registration                              │
│ ├── Register on platform (no plan selection required)      │
│ ├── Basic setup and configuration                          │
│ └── Preview mode activation                                │
│                                                             │
│ Step 2: Plan Selection & Setup Fee                         │
│ ├── Choose plan when ready to go live                      │
│ ├── Pay setup fee ($99-$799)                              │
│ ├── SSL certificate & domain activation                    │
│ └── Website goes live with plan features                   │
│                                                             │
│ Step 3: Commission-Based Revenue Sharing                   │
│ ├── Students purchase courses                              │
│ ├── Platform deducts commission (8-15%)                    │
│ ├── Institute receives remaining revenue (85-92%)          │
│ └── No monthly fees ever                                   │
└─────────────────────────────────────────────────────────────┘
```

### Plan Structure & Features
```
Commission-Based Plans:
┌─────────────────────────────────────────────────────────────┐
│ Plan         │ Setup │ Commission │ Limits        │ Features │
├─────────────────────────────────────────────────────────────┤
│ Starter      │ $99   │ 15%        │ 100 students │ Basic    │
│ Growth       │ $199  │ 12%        │ 500 students │ Standard │
│ Professional │ $399  │ 10%        │ 2K students  │ Advanced │
│ Enterprise   │ $799  │ 8%         │ Unlimited    │ Premium  │
└─────────────────────────────────────────────────────────────┘

Feature Matrix:
├── Courses: All plans
├── Marketplace: Growth+
├── Live Classes: Growth+
├── Online Exams: All plans
├── Blog: Growth+
├── Custom Domain: Growth+
├── API Access: Professional+
└── White Label: Professional+
```

### Technical Implementation
```
Database Schema for Commission & Billing:
├── subscription_plans (setup_fee, commission_percentage, limits)
├── institutes (setup_fee_paid, commission_model_active)
├── payments (commission tracking fields)
├── platform_commissions (detailed commission records)
├── monthly_commission_bills (automated bill generation)
├── commission_bill_items (detailed bill line items)
└── Automated triggers & functions for billing automation
```

### Monthly Billing Automation
```
Automated Monthly Billing Process:
┌─────────────────────────────────────────────────────────────┐
│ Daily: Course Purchase Processing                           │
│ ├── Student purchases course                               │
│ ├── Institute receives 85-92% immediately                  │
│ ├── Commission (8-15%) tracked in platform_commissions     │
│ └── Status: 'calculated' for monthly billing               │
│                                                             │
│ Monthly: 1st of Every Month (Automated)                    │
│ ├── System runs generate_monthly_commission_bills()        │
│ ├── Calculates total commissions per institute             │
│ ├── Creates bills with detailed line items                 │
│ ├── Generates unique bill numbers (INV-YYYY-MM-INST###)    │
│ ├── Sets 30-day payment due dates                         │
│ ├── Sends email notifications with PDF bills              │
│ └── Updates commission status to 'billed'                 │
│                                                             │
│ Payment Tracking:                                          │
│ ├── Payment status monitoring (pending/paid/overdue)       │
│ ├── Late fee calculation and application                   │
│ ├── Automated reminder notifications                       │
│ └── Account suspension for chronic non-payment             │
└─────────────────────────────────────────────────────────────┘
```

### Bill Generation Logic
```sql
-- Automated function runs on 1st of each month
SELECT generate_monthly_commission_bills(
    EXTRACT(MONTH FROM CURRENT_DATE - INTERVAL '1 month'),
    EXTRACT(YEAR FROM CURRENT_DATE - INTERVAL '1 month')
);

-- Creates bills for all institutes with commission activity
-- Includes detailed breakdown of each transaction
-- Sends notifications and sets payment due dates
```

## 📊 6. Optional Visual Diagrams

- **ER Diagram**: Tables for User, Course, Lesson, Quiz, Institute, Exam, Payment, etc.
- **Sequence Diagrams**:
  - Course publishing
  - Live class creation
  - Student subscription via referral
- **Tenant Flow Diagram**
- **Marketplace Purchase Flow**

> This architecture offers scalability, modularity, and SaaS best practices. Perfect for B2B institute onboarding, internal training portals, or exam coaching platforms.

