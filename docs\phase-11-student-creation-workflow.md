# Phase 11: Student Management System - Complete CRUD Implementation Guide

## Overview

This document provides comprehensive specifications for implementing a complete student management system with full CRUD operations in the LMS. The implementation uses the existing `users` table (no separate student table) and follows existing codebase patterns, specifically referencing the institute_admin registration workflow. Students are created as users with `legacyRole: 'student'` and assigned to institutes, branches, and roles.

**Key Features:**
- Complete CRUD operations (Create, Read, Update, Delete)
- Role-based permissions and field visibility
- Student status management (active/inactive)
- Role management integration
- Advanced filtering and search capabilities
- Bulk operations support
- Comprehensive toast notifications
- List and card view options

## 1. Codebase Analysis

### 1.1 Existing Store Patterns

Based on analysis of `apps/frontend/src/stores/institute/useInstituteStore.ts`:

**Key Patterns Identified:**
- **Zustand Store Structure**: Uses `create()` with `devtools()` and `persist()` middleware
- **State Management**: Separates data, UI state, and filters
- **API Integration**: Uses `lib/api` utility functions (`api.get()`, `api.post()`)
- **Error Handling**: Consistent error state management with toast notifications
- **Loading States**: Granular loading states for different operations
- **Toast Integration**: Uses `toast` from 'sonner' for user feedback

**Enhanced Student Interface (Complete CRUD support):**
```typescript
interface Student {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  legacyRole: 'student'
  institute_id: string // Institute ID reference
  branch_id?: string // Branch ID reference (NEW COLUMN)
  role_id?: string // Role ID reference for role management integration
  address?: string
  dateOfBirth?: Date
  gender?: 'male' | 'female' | 'other'
  is_active: boolean // Student status (active/inactive) - NEW FIELD
  emailVerified: boolean
  lastLogin?: Date
  enrolledCourses?: number
  totalProgress?: number
  lastActivity?: string
  createdAt: Date
  updatedAt: Date

  // Populated relationships
  branch?: {
    id: string
    name: string
    code: string
  }
  role?: {
    id: string
    name: string
    permissions: string[]
  }
}
```

### 1.2 Authentication Middleware Patterns

From `apps/api/src/middleware/auth.ts`:

**Key Components:**
- **JWT Token Validation**: Uses `jwt.verify()` with `process.env.JWT_SECRET`
- **User Context Extraction**: Fetches user with depth 3 to include relationships
- **Role-Based Access**: Uses `legacyRole` field for authorization
- **Institute/Branch Context**: Available via `user.institute` and `user.branch`

**AuthenticatedUser Interface:**
```typescript
interface AuthenticatedUser {
  id: string
  email: string
  role: string
  legacyRole: string
  institute?: any
  branch?: any
  permissions?: string[]
  isActive: boolean
}
```

### 1.3 Form Patterns Analysis

From existing forms in `apps/frontend/src/components/`:

**Standard Form Structure:**
- **Formik + Yup**: All forms use Formik for form handling and Yup for validation
- **Validation Schema**: Comprehensive validation with conditional rules
- **Error Display**: Uses `ErrorMessage` component with consistent styling
- **Loading States**: Form submission loading states with disabled buttons
- **Toast Feedback**: Success/error notifications after form submission

**Common Validation Patterns:**
```typescript
const validationSchema = Yup.object({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  phone: Yup.string(),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required')
})
```

### 1.4 API Endpoint Patterns

From `apps/api/src/endpoints/institute-admin/students.ts`:

**Existing Student Endpoints:**
- `GET /api/institute-admin/students` - List students with filtering
- `POST /api/institute-admin/students` - Create student (already exists)
- `PATCH /api/institute-admin/students/:id` - Update student
- `DELETE /api/institute-admin/students/:id` - Delete student

**Current Implementation Issues:**
- Missing role-based branch assignment logic
- No permission-based branch filtering
- Limited validation for branch access

## 2. API Endpoint Specifications

### 2.1 Enhanced Student Creation Endpoint

**Endpoint:** `POST /api/institute/students`

**Authentication:** JWT Bearer token required

**Request Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Payload:**
```typescript
interface CreateStudentRequest {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password: string
  branch_id?: string // Optional for Institute Admin/Staff, auto-assigned for Branch Manager
  address?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
}
```

**Response Payload:**
```typescript
interface CreateStudentResponse {
  success: boolean
  data?: {
    id: string
    firstName: string
    lastName: string
    email: string
    phone?: string
    branch: {
      id: string
      name: string
      code: string // Branch code field
    }
    institute: {
      id: string
      name: string
    }
    createdAt: string
  }
  message?: string
  error?: string
}
```

**Error Responses:**
- `400` - Validation errors, missing required fields
- `401` - Authentication required
- `403` - Insufficient permissions or invalid branch access
- `409` - Email already exists
- `500` - Internal server error

### 2.2 Branch List Endpoint

**Endpoint:** `GET /api/institute/branches`

**Query Parameters:**
```typescript
interface BranchListParams {
  accessible_only?: boolean // Filter branches based on user permissions
}
```

**Response:**
```typescript
interface BranchListResponse {
  success: boolean
  data: Array<{
    id: string
    name: string
    address: string
    isActive: boolean
  }>
}
```

## 3. Branch Assignment Logic

### 3.1 Role-Based Branch Assignment

**Institute Admin (level 2):**
- Can assign students to any branch within their institute
- Branch dropdown shows all active branches in their institute
- Validation: Ensure selected branch belongs to user's institute

**Staff:**
- Can assign students to branches they have access to
- Branch dropdown filtered by user's branch permissions
- Validation: Check user has permission for selected branch

**Branch Manager:**
- Students auto-assigned to their specific branch
- No branch selection dropdown shown
- Branch ID extracted from authenticated user context

### 3.2 Implementation Logic

```typescript
// Backend branch assignment logic
const assignBranch = (user: AuthenticatedUser, requestedBranchId?: string) => {
  switch (user.legacyRole) {
    case 'institute_admin':
      // Can assign to any branch in their institute
      return requestedBranchId || getDefaultBranch(user.institute)

    case 'institute_staff':
      // Can only assign to branches they have access to
      if (requestedBranchId && hasAccessToBranch(user, requestedBranchId)) {
        return requestedBranchId
      }
      throw new Error('No access to specified branch')

    case 'branch_manager':
      // Auto-assign to their branch (use branch_id field)
      return user.branch_id || user.branch // Fallback to legacy branch field

    default:
      throw new Error('Invalid role for student creation')
  }
}
```

## 4. Backend Permission Checks

### 4.1 Authentication Middleware Integration

**File:** `apps/api/src/endpoints/institute/students.ts`

```typescript
import { requireAuth } from '../../middleware/auth'

export const createStudentEndpoint: Endpoint = {
  path: '/institute/students',
  method: 'post',
  handler: async (req: any) => {
    // Apply authentication middleware
    const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'institute_staff'])
    const authResult = await authMiddleware(req)

    if (authResult) {
      return authResult // Return error response
    }

    const user = req.user
    const { branch_id, ...studentData } = await req.json()

    // Role-based branch assignment
    const assignedBranchId = assignBranch(user, branch_id)

    // Validate required fields (following institute_admin register pattern)
    if (!studentData.firstName || !studentData.lastName || !studentData.email || !studentData.password) {
      return Response.json({
        success: false,
        error: 'First name, last name, email, and password are required'
      }, { status: 400 })
    }

    // Check if email already exists (following existing pattern)
    const existingUser = await req.payload.find({
      collection: 'users',
      where: {
        email: { equals: studentData.email.toLowerCase() }
      },
      limit: 1
    })

    if (existingUser.docs.length > 0) {
      return Response.json({
        success: false,
        error: 'Email already exists'
      }, { status: 409 })
    }

    // Create student user (following institute_admin register pattern)
    const student = await req.payload.create({
      collection: 'users',
      data: {
        ...studentData,
        email: studentData.email.toLowerCase(),
        legacyRole: 'student',
        institute: user.institute,
        branch_id: assignedBranchId, // Use new branch_id field
        isActive: true,
        emailVerified: false
      }
    })

    return Response.json({
      success: true,
      data: student,
      message: 'Student created successfully'
    })
  }
}
```

### 4.2 Permission Validation

**Branch Access Validation:**
```typescript
const validateBranchAccess = async (user: AuthenticatedUser, branchId: string) => {
  // Institute admin can access any branch in their institute
  if (user.legacyRole === 'institute_admin') {
    const branch = await payload.findByID({
      collection: 'branches',
      id: branchId
    })
    return branch.institute === user.institute
  }
  
  // Staff can only access branches they're assigned to
  if (user.legacyRole === 'institute_staff') {
    return user.permissions?.includes(`branch_access_${branchId}`)
  }
  
  // Branch manager can only access their branch
  if (user.legacyRole === 'branch_manager') {
    return (user.branch_id || user.branch) === branchId
  }
  
  return false
}
```

## 5. Frontend Implementation Specifications

### 5.1 Student Creation Store

**File:** `apps/frontend/src/stores/institute/useStudentStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

interface StudentCreationData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password: string
  branch_id?: string // Updated to use branch_id
  address?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
}

interface StudentStore {
  // State
  students: Student[]
  availableBranches: Branch[]
  isLoading: boolean
  isCreating: boolean
  error: string | null
  
  // Actions
  fetchStudents: () => Promise<void>
  fetchAvailableBranches: () => Promise<void>
  createStudent: (data: StudentCreationData) => Promise<void>
  clearError: () => void
}

export const useStudentStore = create<StudentStore>()(
  devtools((set, get) => ({
    // Initial state
    students: [],
    availableBranches: [],
    isLoading: false,
    isCreating: false,
    error: null,
    
    // Fetch available branches based on user permissions
    fetchAvailableBranches: async () => {
      try {
        const response = await api.get('/api/institute/branches?accessible_only=true')
        if (response.success) {
          set({ availableBranches: response.data })
        }
      } catch (error) {
        console.error('Failed to fetch branches:', error)
        toast.error('Failed to load branches')
      }
    },
    
    // Create student
    createStudent: async (data: StudentCreationData) => {
      set({ isCreating: true, error: null })
      try {
        const response = await api.post('/api/institute/students', data)
        
        if (response.success) {
          // Refresh student list
          await get().fetchStudents()
          
          toast.success('Student Created', {
            description: `${data.firstName} ${data.lastName} has been created successfully.`
          })
          
          set({ isCreating: false })
        } else {
          throw new Error(response.error || 'Failed to create student')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        set({ error: errorMessage, isCreating: false })
        
        toast.error('Failed to create student', {
          description: errorMessage
        })
        throw error
      }
    },
    
    clearError: () => set({ error: null })
  }))
)
```

### 5.2 Student Creation Form Component

**File:** `apps/frontend/src/components/institute/StudentCreationForm.tsx`

```typescript
'use client'

import React, { useEffect } from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, Save, X } from 'lucide-react'
import { useStudentStore } from '@/stores/institute/useStudentStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'

const studentValidationSchema = Yup.object({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email format').required('Email is required'),
  phone: Yup.string(),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
  branch_id: Yup.string().when('$userRole', {
    is: (role: string) => ['institute_admin', 'institute_staff'].includes(role),
    then: (schema) => schema.required('Branch selection is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  address: Yup.string(),
  dateOfBirth: Yup.date(),
  gender: Yup.string().oneOf(['male', 'female', 'other'])
})

interface StudentCreationFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

export function StudentCreationForm({ isOpen, onClose, onSuccess }: StudentCreationFormProps) {
  const { user } = useAuthStore()
  const {
    availableBranches,
    isCreating,
    createStudent,
    fetchAvailableBranches,
    clearError
  } = useStudentStore()

  // Fetch available branches on component mount
  useEffect(() => {
    if (isOpen) {
      fetchAvailableBranches()
      clearError()
    }
  }, [isOpen, fetchAvailableBranches, clearError])

  const initialValues = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    branch_id: user?.legacyRole === 'branch_manager' ? (user.branch_id || user.branch) : '',
    address: '',
    dateOfBirth: '',
    gender: ''
  }

  const handleSubmit = async (values: any, { resetForm }: any) => {
    try {
      await createStudent(values)
      resetForm()
      onSuccess?.()
      onClose()
    } catch (error) {
      // Error handling is done in the store
    }
  }

  const showBranchSelection = user?.legacyRole !== 'branch_manager'

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Create New Student</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          <Formik
            initialValues={initialValues}
            validationSchema={studentValidationSchema}
            onSubmit={handleSubmit}
            context={{ userRole: user?.legacyRole }}
            enableReinitialize
          >
            {({ values, setFieldValue, errors, touched, isSubmitting }) => (
              <Form className="space-y-6">
                {/* Personal Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <Field
                      as={Input}
                      id="firstName"
                      name="firstName"
                      placeholder="Enter first name"
                      className={errors.firstName && touched.firstName ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="firstName" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.firstName}</span>
                    </ErrorMessage>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Field
                      as={Input}
                      id="lastName"
                      name="lastName"
                      placeholder="Enter last name"
                      className={errors.lastName && touched.lastName ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="lastName" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.lastName}</span>
                    </ErrorMessage>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Field
                      as={Input}
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Enter email address"
                      className={errors.email && touched.email ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="email" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.email}</span>
                    </ErrorMessage>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Field
                      as={Input}
                      id="phone"
                      name="phone"
                      placeholder="Enter phone number"
                      className={errors.phone && touched.phone ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="phone" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.phone}</span>
                    </ErrorMessage>
                  </div>
                </div>

                {/* Password */}
                <div className="space-y-2">
                  <Label htmlFor="password">Password *</Label>
                  <Field
                    as={Input}
                    id="password"
                    name="password"
                    type="password"
                    placeholder="Enter password (minimum 6 characters)"
                    className={errors.password && touched.password ? 'border-red-500' : ''}
                  />
                  <ErrorMessage name="password" component="div" className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.password}</span>
                  </ErrorMessage>
                </div>

                {/* Branch Selection (conditional) */}
                {showBranchSelection && (
                  <div className="space-y-2">
                    <Label htmlFor="branch_id">Branch *</Label>
                    <Select
                      value={values.branch_id}
                      onValueChange={(value) => setFieldValue('branch_id', value)}
                    >
                      <SelectTrigger className={errors.branch_id && touched.branch_id ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select a branch" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableBranches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id}>
                            {branch.name} ({branch.code})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <ErrorMessage name="branch_id" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.branch_id}</span>
                    </ErrorMessage>
                  </div>
                )}

                {/* Additional Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth">Date of Birth</Label>
                    <Field
                      as={Input}
                      id="dateOfBirth"
                      name="dateOfBirth"
                      type="date"
                      className={errors.dateOfBirth && touched.dateOfBirth ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="dateOfBirth" component="div" className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      <span>{errors.dateOfBirth}</span>
                    </ErrorMessage>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="gender">Gender</Label>
                    <Select
                      value={values.gender}
                      onValueChange={(value) => setFieldValue('gender', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Address */}
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Field
                    as={Input}
                    id="address"
                    name="address"
                    placeholder="Enter address"
                    className={errors.address && touched.address ? 'border-red-500' : ''}
                  />
                  <ErrorMessage name="address" component="div" className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.address}</span>
                  </ErrorMessage>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-4 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    disabled={isCreating}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isCreating}
                    className="flex items-center gap-2"
                  >
                    <Save className="h-4 w-4" />
                    {isCreating ? 'Creating...' : 'Create Student'}
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </CardContent>
      </Card>
    </div>
  )
}
```

## 6. Database Schema Requirements

### 6.1 Users Table Structure (No Separate Student Table)

Based on analysis of existing collections and following institute_admin register pattern:

**Users Collection Schema Updates Required:**
```typescript
interface User {
  id: string
  email: string
  password: string // Hashed by Payload
  firstName: string
  lastName: string
  phone?: string
  legacyRole: string // 'student', 'institute_admin', etc.
  institute: string // Institute ID reference
  branch?: string // Legacy branch field (to be deprecated)
  branch_id?: string // NEW: Branch ID reference (replaces branch field)
  address?: string
  dateOfBirth?: Date
  gender?: 'male' | 'female' | 'other'
  isActive: boolean
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
}
```

**Required Database Migration:**
```sql
-- Add new branch_id column to users table
ALTER TABLE users ADD COLUMN branch_id UUID REFERENCES branches(id);

-- Create index for performance
CREATE INDEX idx_users_branch_id ON users(branch_id);

-- Migrate existing branch data (if needed)
-- UPDATE users SET branch_id = branch WHERE branch IS NOT NULL;
```

### 6.2 Branch Collection Schema Updates

**Branches Collection (ensure code field exists):**
```typescript
interface Branch {
  id: string
  name: string
  code: string // Branch code field (e.g., 'DLC', 'NYC', 'MUM')
  institute: string // Institute ID reference
  location: {
    address: string
    country: string
    state: string
    district: string
    pincode?: string
  }
  contact?: {
    phone?: string
    email?: string
  }
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}
```

**Required Indexes:**
```sql
-- Performance optimization indexes
CREATE INDEX idx_users_institute_role ON users(institute, legacyRole);
CREATE INDEX idx_users_branch_id_role ON users(branch_id, legacyRole); -- Updated index
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(isActive);
CREATE INDEX idx_branches_code ON branches(code); -- Index for branch code
CREATE INDEX idx_branches_institute ON branches(institute);
```

### 6.3 Foreign Key Relationships

**Institute Relationship:**
- `users.institute` → `institutes.id`
- Cascade delete when institute is deleted
- Required for all non-super-admin users

**Branch Relationship:**
- `users.branch_id` → `branches.id` (NEW)
- `users.branch` → Legacy field (to be deprecated)
- Optional for institute admins, required for branch-specific roles
- Validates branch belongs to user's institute

**Branch Code Usage:**
- `branches.code` field used for display and identification
- Unique within each institute
- Examples: 'DLC' (Delhi Center), 'MUM' (Mumbai Center), 'BLR' (Bangalore Center)

### 6.3 Permission Requirements

**Role-Based Permissions:**
```typescript
const STUDENT_MANAGEMENT_PERMISSIONS = {
  'institute_admin': [
    'create_students',
    'view_all_institute_students',
    'update_institute_students',
    'delete_institute_students'
  ],
  'institute_staff': [
    'create_students',
    'view_assigned_branch_students',
    'update_assigned_branch_students'
  ],
  'branch_manager': [
    'create_students',
    'view_branch_students',
    'update_branch_students'
  ]
}
```

## 7. Technical Integration Points

### 7.1 API Integration Pattern

**Using lib/api instead of direct fetch:**
```typescript
// ❌ Avoid direct fetch calls
const response = await fetch('/api/institute/students', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` },
  body: JSON.stringify(data)
})

// ✅ Use lib/api utility
const response = await api.post('/api/institute/students', data)
```

### 7.2 Error Handling Pattern

**Consistent Error Handling:**
```typescript
try {
  const response = await api.post('/api/institute/students', data)

  if (response.success) {
    toast.success('Student Created', {
      description: `${data.firstName} ${data.lastName} has been created successfully.`
    })
  } else {
    throw new Error(response.error || 'Failed to create student')
  }
} catch (error) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error'

  toast.error('Failed to create student', {
    description: errorMessage
  })

  throw error // Re-throw for form handling
}
```

### 7.3 Loading State Management

**Granular Loading States:**
```typescript
interface StudentStore {
  isLoading: boolean        // General loading
  isCreating: boolean       // Creating student
  isFetching: boolean       // Fetching students
  isFetchingBranches: boolean // Fetching branches
}
```

### 7.4 Toast Notification Integration

**Consistent Toast Patterns:**
```typescript
// Success notifications
toast.success('Student Created', {
  description: `${firstName} ${lastName} has been created successfully.`
})

// Error notifications
toast.error('Failed to create student', {
  description: errorMessage
})

// Loading notifications (if needed)
const loadingToast = toast.loading('Creating student...')
// Later: toast.dismiss(loadingToast)
```

## 8. Implementation Checklist

### 8.1 Backend Implementation

- [ ] **Database Migration**: Add `branch_id` column to users table
- [ ] **Update Users Collection**: Add `branch_id` field to Payload Users collection config
- [ ] **Create enhanced `/api/institute/students` endpoint** following institute_admin register pattern
- [ ] **Implement role-based branch assignment logic** using `branch_id` field
- [ ] **Add branch access validation middleware** with `branch_id` support
- [ ] **Update authentication middleware** for institute context extraction
- [ ] **Add comprehensive error handling and validation** following existing patterns
- [ ] **Create branch list endpoint** with permission filtering and branch code display
- [ ] **Add database indexes** for performance (`branch_id`, `code` fields)
- [ ] **Write unit tests** for permission logic and branch assignment

### 8.2 Frontend Implementation

- [ ] **Create `useStudentStore` Zustand store** following existing store patterns
- [ ] **Implement `StudentCreationForm` component** with `branch_id` field support
- [ ] **Add form validation with Yup schema** using `branch_id` validation
- [ ] **Integrate with existing toast notification system** for success/error feedback
- [ ] **Add loading states and error handling** following existing patterns
- [ ] **Implement role-based UI rendering** (conditional branch selection)
- [ ] **Add branch selection dropdown** with filtering and branch code display
- [ ] **Create responsive form design** with Shadcn UI components
- [ ] **Update existing student interfaces** to use `branch_id` instead of `branch`
- [ ] **Write component tests** for all user roles and scenarios

### 8.3 Integration Testing

- [ ] **Test Institute Admin student creation flow** with branch selection
- [ ] **Test Staff student creation** with branch restrictions using `branch_id`
- [ ] **Test Branch Manager auto-assignment** using `branch_id` field
- [ ] **Test form validation and error handling** for all required fields
- [ ] **Test API error responses** and frontend handling (email exists, validation errors)
- [ ] **Test permission-based branch filtering** with branch code display
- [ ] **Test toast notifications** for all success/error scenarios
- [ ] **Test responsive design** on different screen sizes
- [ ] **Test database migration** from `branch` to `branch_id` field
- [ ] **Test backward compatibility** during migration period

## 9. File Structure

```
apps/
├── api/src/
│   ├── endpoints/institute/
│   │   └── students.ts (Enhanced endpoint)
│   ├── middleware/
│   │   └── auth.ts (Updated with institute context)
│   └── lib/
│       └── permissions.ts (Branch access validation)
├── frontend/src/
│   ├── stores/institute/
│   │   └── useStudentStore.ts (New store)
│   ├── components/institute/
│   │   └── StudentCreationForm.tsx (New component)
│   ├── app/admin/students/
│   │   └── page.tsx (Integration page)
│   └── lib/
│       └── api.ts (Existing utility)
└── docs/
    └── phase-11-student-creation-workflow.md (This document)
```

## 10. Migration Strategy

### 10.1 Database Migration Steps

1. **Add `branch_id` column** to users table with foreign key constraint
2. **Migrate existing data** from `branch` field to `branch_id` field
3. **Update application code** to use `branch_id` instead of `branch`
4. **Test thoroughly** in staging environment
5. **Deploy to production** with rollback plan
6. **Deprecate `branch` field** after successful migration

### 10.2 Code Migration Pattern

```typescript
// Before (legacy)
user.branch

// After (new)
user.branch_id || user.branch // Fallback during migration
```

## 11. Next Steps

1. **Database Migration**: Add `branch_id` column and create migration script
2. **Backend Development**: Implement enhanced API endpoints following institute_admin register pattern
3. **Frontend Development**: Create student store and form components with `branch_id` support
4. **Integration**: Connect frontend components with backend APIs
5. **Testing**: Comprehensive testing of all user roles and migration scenarios
6. **Documentation**: Update API documentation and user guides
7. **Deployment**: Deploy with migration strategy and monitor in staging environment

This implementation guide ensures consistency with existing codebase patterns (specifically following the institute_admin registration workflow) while providing comprehensive role-based student management functionality using the existing users table structure.

---

# COMPREHENSIVE CRUD OPERATIONS

## 12. Student Management Forms

### 12.1 Enhanced Create Student Form

**File:** `apps/frontend/src/components/institute/StudentCreateForm.tsx`

```typescript
const studentCreateValidationSchema = Yup.object({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email format').required('Email is required'),
  phone: Yup.string(),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
  branch_id: Yup.string().when('$userRole', {
    is: (role: string) => ['institute_admin', 'institute_staff'].includes(role),
    then: (schema) => schema.required('Branch selection is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  role_id: Yup.string().required('Role selection is required'),
  address: Yup.string(),
  dateOfBirth: Yup.date(),
  gender: Yup.string().oneOf(['male', 'female', 'other']),
  is_active: Yup.boolean().default(true)
})

interface StudentCreateFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password: string
  branch_id?: string
  role_id: string
  address?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
  is_active: boolean
}
```

### 12.2 Update/Edit Student Form

**File:** `apps/frontend/src/components/institute/StudentEditForm.tsx`

```typescript
const studentUpdateValidationSchema = Yup.object({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email format').required('Email is required'),
  phone: Yup.string(),
  // Password is optional for updates
  password: Yup.string().min(6, 'Password must be at least 6 characters'),
  branch_id: Yup.string().when('$userRole', {
    is: (role: string) => ['institute_admin', 'institute_staff'].includes(role),
    then: (schema) => schema.required('Branch selection is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  role_id: Yup.string().required('Role selection is required'),
  address: Yup.string(),
  dateOfBirth: Yup.date(),
  gender: Yup.string().oneOf(['male', 'female', 'other']),
  is_active: Yup.boolean()
})

interface StudentUpdateFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password?: string // Optional for updates
  branch_id?: string
  role_id: string
  address?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
  is_active: boolean
}

export function StudentEditForm({ student, isOpen, onClose, onSuccess }: StudentEditFormProps) {
  const { user } = useAuthStore()
  const {
    availableBranches,
    availableRoles,
    isUpdating,
    updateStudent,
    fetchAvailableBranches,
    fetchAvailableRoles,
    clearError
  } = useStudentStore()

  const initialValues: StudentUpdateFormData = {
    firstName: student?.firstName || '',
    lastName: student?.lastName || '',
    email: student?.email || '',
    phone: student?.phone || '',
    password: '', // Always empty for security
    branch_id: student?.branch_id || (user?.legacyRole === 'branch_manager' ? (user.branch_id || user.branch) : ''),
    role_id: student?.role_id || '',
    address: student?.address || '',
    dateOfBirth: student?.dateOfBirth ? new Date(student.dateOfBirth).toISOString().split('T')[0] : '',
    gender: student?.gender || '',
    is_active: student?.is_active ?? true
  }

  const handleSubmit = async (values: StudentUpdateFormData, { setSubmitting }: any) => {
    try {
      await updateStudent(student.id, values)
      onSuccess?.()
      onClose()
    } catch (error) {
      // Error handling is done in the store
    } finally {
      setSubmitting(false)
    }
  }

  // Rest of component implementation...
}
```

### 12.3 Role-Based Form Field Visibility

```typescript
const getVisibleFields = (userRole: string, operation: 'create' | 'update') => {
  const baseFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'dateOfBirth', 'gender']

  const fieldVisibility = {
    institute_admin: {
      create: [...baseFields, 'password', 'branch_id', 'role_id', 'is_active'],
      update: [...baseFields, 'password', 'branch_id', 'role_id', 'is_active']
    },
    institute_staff: {
      create: [...baseFields, 'password', 'branch_id', 'role_id'],
      update: [...baseFields, 'password', 'branch_id', 'role_id']
    },
    branch_manager: {
      create: [...baseFields, 'password', 'role_id'],
      update: [...baseFields, 'password', 'role_id']
    }
  }

  return fieldVisibility[userRole]?.[operation] || baseFields
}

const canEditField = (fieldName: string, userRole: string, operation: 'create' | 'update') => {
  const visibleFields = getVisibleFields(userRole, operation)
  return visibleFields.includes(fieldName)
}
```

## 13. Student Status Management

### 13.1 Status Toggle Component

**File:** `apps/frontend/src/components/institute/StudentStatusToggle.tsx`

```typescript
'use client'

import React, { useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { useStudentStore } from '@/stores/institute/useStudentStore'
import { Student } from '@/types/student'

interface StudentStatusToggleProps {
  student: Student
  onStatusChange?: (student: Student, newStatus: boolean) => void
}

export function StudentStatusToggle({ student, onStatusChange }: StudentStatusToggleProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [pendingStatus, setPendingStatus] = useState<boolean | null>(null)
  const { toggleStudentStatus, isUpdating } = useStudentStore()

  const handleStatusToggle = (newStatus: boolean) => {
    setPendingStatus(newStatus)
    setShowConfirmDialog(true)
  }

  const confirmStatusChange = async () => {
    if (pendingStatus === null) return

    try {
      await toggleStudentStatus(student.id, pendingStatus)
      onStatusChange?.(student, pendingStatus)
      setShowConfirmDialog(false)
      setPendingStatus(null)
    } catch (error) {
      // Error handling is done in the store
    }
  }

  const getConfirmationMessage = () => {
    const action = pendingStatus ? 'activate' : 'deactivate'
    const consequence = pendingStatus
      ? 'The student will be able to access courses and submit assignments.'
      : 'The student will lose access to courses and cannot submit assignments.'

    return {
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Student`,
      description: `Are you sure you want to ${action} ${student.firstName} ${student.lastName}? ${consequence}`
    }
  }

  const { title, description } = getConfirmationMessage()

  return (
    <>
      <div className="flex items-center space-x-2">
        <Switch
          checked={student.is_active}
          onCheckedChange={handleStatusToggle}
          disabled={isUpdating}
        />
        <span className={`text-sm ${student.is_active ? 'text-green-600' : 'text-red-600'}`}>
          {student.is_active ? 'Active' : 'Inactive'}
        </span>
      </div>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{title}</AlertDialogTitle>
            <AlertDialogDescription>{description}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowConfirmDialog(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmStatusChange} disabled={isUpdating}>
              {isUpdating ? 'Processing...' : 'Confirm'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
```

### 13.2 Status Change API Endpoint

**File:** `apps/api/src/endpoints/institute/students.ts`

```typescript
// Toggle student active status
export const toggleStudentStatusEndpoint: Endpoint = {
  path: '/institute/students/:id/status',
  method: 'patch',
  handler: async (req: any) => {
    const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'institute_staff'])
    const authResult = await authMiddleware(req)

    if (authResult) return authResult

    const user = req.user
    const studentId = req.params.id
    const { is_active, reason } = await req.json()

    try {
      // Verify student belongs to user's institute/branch
      const existingStudent = await req.payload.findByID({
        collection: 'users',
        id: studentId,
        depth: 2
      })

      if (!existingStudent || existingStudent.legacyRole !== 'student') {
        return Response.json({
          success: false,
          error: 'Student not found'
        }, { status: 404 })
      }

      // Validate access permissions
      if (!validateStudentAccess(user, existingStudent)) {
        return Response.json({
          success: false,
          error: 'Access denied'
        }, { status: 403 })
      }

      // Update student status
      const updatedStudent = await req.payload.update({
        collection: 'users',
        id: studentId,
        data: {
          is_active,
          updatedAt: new Date()
        }
      })

      // Create audit trail entry
      await createAuditTrail({
        userId: user.id,
        action: is_active ? 'STUDENT_ACTIVATED' : 'STUDENT_DEACTIVATED',
        targetId: studentId,
        targetType: 'student',
        reason,
        metadata: {
          studentName: `${existingStudent.firstName} ${existingStudent.lastName}`,
          previousStatus: existingStudent.is_active
        }
      })

      return Response.json({
        success: true,
        data: updatedStudent,
        message: `Student ${is_active ? 'activated' : 'deactivated'} successfully`
      })

    } catch (error) {
      console.error('Toggle student status error:', error)
      return Response.json({
        success: false,
        error: 'Failed to update student status'
      }, { status: 500 })
    }
  }
}
```

## 14. Role Management Integration

### 14.1 Role Selection Component

**File:** `apps/frontend/src/components/institute/RoleSelector.tsx`

```typescript
'use client'

import React, { useEffect } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { useRoleStore } from '@/stores/institute/useRoleStore'
import { AlertCircle } from 'lucide-react'

interface RoleSelectorProps {
  value: string
  onChange: (value: string) => void
  error?: string
  touched?: boolean
  disabled?: boolean
  required?: boolean
}

export function RoleSelector({ value, onChange, error, touched, disabled, required }: RoleSelectorProps) {
  const { studentRoles, isLoading, fetchStudentRoles } = useRoleStore()

  useEffect(() => {
    fetchStudentRoles()
  }, [fetchStudentRoles])

  return (
    <div className="space-y-2">
      <Label htmlFor="role_id">
        Student Role {required && '*'}
      </Label>
      <Select
        value={value}
        onValueChange={onChange}
        disabled={disabled || isLoading}
      >
        <SelectTrigger className={error && touched ? 'border-red-500' : ''}>
          <SelectValue placeholder={isLoading ? 'Loading roles...' : 'Select a role'} />
        </SelectTrigger>
        <SelectContent>
          {studentRoles.map((role) => (
            <SelectItem key={role.id} value={role.id}>
              <div className="flex flex-col">
                <span className="font-medium">{role.name}</span>
                <span className="text-xs text-gray-500">{role.description}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && touched && (
        <div className="text-sm text-red-500 flex items-center gap-1">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}
    </div>
  )
}
```

### 14.2 Role Store Integration

**File:** `apps/frontend/src/stores/institute/useRoleStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  isActive: boolean
  createdAt: Date
}

interface RoleStore {
  // State
  studentRoles: Role[]
  isLoading: boolean
  error: string | null

  // Actions
  fetchStudentRoles: () => Promise<void>
  clearError: () => void
}

export const useRoleStore = create<RoleStore>()(
  devtools((set, get) => ({
    // Initial state
    studentRoles: [],
    isLoading: false,
    error: null,

    // Fetch student-specific roles
    fetchStudentRoles: async () => {
      set({ isLoading: true, error: null })
      try {
        const response = await api.get('/api/institute/roles?type=student')
        if (response.success) {
          set({ studentRoles: response.data, isLoading: false })
        } else {
          throw new Error(response.error || 'Failed to fetch roles')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        set({ error: errorMessage, isLoading: false })
        toast.error('Failed to load student roles')
      }
    },

    clearError: () => set({ error: null })
  }))
)
```

### 14.3 Role-Based Permission Inheritance

```typescript
// Role permission inheritance logic
const getStudentPermissions = (role: Role, institutePermissions: string[]) => {
  const baseStudentPermissions = [
    'view_courses',
    'submit_assignments',
    'view_progress',
    'access_materials'
  ]

  // Combine base permissions with role-specific permissions
  const rolePermissions = role.permissions || []

  // Filter institute permissions that apply to students
  const applicableInstitutePermissions = institutePermissions.filter(permission =>
    permission.startsWith('student_') || permission.includes('course_access')
  )

  return [
    ...baseStudentPermissions,
    ...rolePermissions,
    ...applicableInstitutePermissions
  ].filter((permission, index, array) => array.indexOf(permission) === index) // Remove duplicates
}

// API endpoint for role-based permissions
export const getStudentRolesEndpoint: Endpoint = {
  path: '/institute/roles',
  method: 'get',
  handler: async (req: any) => {
    const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'institute_staff'])
    const authResult = await authMiddleware(req)

    if (authResult) return authResult

    const user = req.user
    const { type } = req.query

    try {
      const whereClause: any = {
        institute: { equals: user.institute },
        isActive: { equals: true }
      }

      if (type === 'student') {
        whereClause.applicableToStudents = { equals: true }
      }

      const roles = await req.payload.find({
        collection: 'roles',
        where: whereClause,
        depth: 1,
        sort: 'name'
      })

      return Response.json({
        success: true,
        data: roles.docs
      })

    } catch (error) {
      console.error('Fetch roles error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch roles'
      }, { status: 500 })
    }
  }
}
```

## 15. Enhanced Toast Notifications

### 15.1 Toast Message Templates

**File:** `apps/frontend/src/lib/toastTemplates.ts`

```typescript
import { toast } from 'sonner'

export const studentToastMessages = {
  // Create operations
  createSuccess: (studentName: string) =>
    toast.success('Student Created', {
      description: `${studentName} has been successfully added to the system.`,
      duration: 4000
    }),

  createError: (error: string) =>
    toast.error('Failed to Create Student', {
      description: error || 'An unexpected error occurred while creating the student.',
      duration: 5000
    }),

  // Update operations
  updateSuccess: (studentName: string) =>
    toast.success('Student Updated', {
      description: `${studentName}'s information has been successfully updated.`,
      duration: 4000
    }),

  updateError: (error: string) =>
    toast.error('Failed to Update Student', {
      description: error || 'An unexpected error occurred while updating the student.',
      duration: 5000
    }),

  // Status change operations
  activateSuccess: (studentName: string) =>
    toast.success('Student Activated', {
      description: `${studentName} has been activated and can now access courses.`,
      duration: 4000
    }),

  deactivateSuccess: (studentName: string) =>
    toast.success('Student Deactivated', {
      description: `${studentName} has been deactivated and cannot access courses.`,
      duration: 4000
    }),

  statusChangeError: (error: string) =>
    toast.error('Failed to Change Status', {
      description: error || 'An unexpected error occurred while changing student status.',
      duration: 5000
    }),

  // Delete operations
  deleteSuccess: (studentName: string) =>
    toast.success('Student Removed', {
      description: `${studentName} has been successfully removed from the system.`,
      duration: 4000
    }),

  deleteError: (error: string) =>
    toast.error('Failed to Remove Student', {
      description: error || 'An unexpected error occurred while removing the student.',
      duration: 5000
    }),

  // Bulk operations
  bulkActivateSuccess: (count: number) =>
    toast.success('Students Activated', {
      description: `${count} student${count > 1 ? 's' : ''} have been successfully activated.`,
      duration: 4000
    }),

  bulkDeactivateSuccess: (count: number) =>
    toast.success('Students Deactivated', {
      description: `${count} student${count > 1 ? 's' : ''} have been successfully deactivated.`,
      duration: 4000
    }),

  bulkOperationError: (operation: string, error: string) =>
    toast.error(`Failed to ${operation} Students`, {
      description: error || `An unexpected error occurred during the ${operation} operation.`,
      duration: 5000
    }),

  // Loading states
  createLoading: () =>
    toast.loading('Creating student...', {
      description: 'Please wait while we add the new student to the system.'
    }),

  updateLoading: () =>
    toast.loading('Updating student...', {
      description: 'Please wait while we save the changes.'
    }),

  statusChangeLoading: (action: string) =>
    toast.loading(`${action} student...`, {
      description: `Please wait while we ${action.toLowerCase()} the student.`
    }),

  deleteLoading: () =>
    toast.loading('Removing student...', {
      description: 'Please wait while we remove the student from the system.'
    }),

  // Validation errors
  validationError: (field: string, message: string) =>
    toast.error('Validation Error', {
      description: `${field}: ${message}`,
      duration: 4000
    }),

  // Permission errors
  permissionError: (action: string) =>
    toast.error('Permission Denied', {
      description: `You don't have permission to ${action} students.`,
      duration: 5000
    }),

  // Network errors
  networkError: () =>
    toast.error('Network Error', {
      description: 'Please check your internet connection and try again.',
      duration: 5000
    })
}

// Usage in components
export const useStudentToasts = () => {
  return studentToastMessages
}
```

### 15.2 Toast Integration in Store

**File:** `apps/frontend/src/stores/institute/useStudentStore.ts` (Enhanced)

```typescript
import { studentToastMessages } from '@/lib/toastTemplates'

export const useStudentStore = create<StudentStore>()(
  devtools((set, get) => ({
    // ... existing state

    // Enhanced create student with proper toast messages
    createStudent: async (data: StudentCreationData) => {
      set({ isCreating: true, error: null })

      const loadingToast = studentToastMessages.createLoading()

      try {
        const response = await api.post('/api/institute/students', data)

        if (response.success) {
          // Dismiss loading toast
          toast.dismiss(loadingToast)

          // Show success toast
          studentToastMessages.createSuccess(`${data.firstName} ${data.lastName}`)

          // Refresh student list
          await get().fetchStudents()

          set({ isCreating: false })
        } else {
          throw new Error(response.error || 'Failed to create student')
        }
      } catch (error) {
        // Dismiss loading toast
        toast.dismiss(loadingToast)

        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        set({ error: errorMessage, isCreating: false })

        // Show error toast with specific message
        if (errorMessage.includes('email')) {
          studentToastMessages.validationError('Email', 'This email address is already in use')
        } else if (errorMessage.includes('permission')) {
          studentToastMessages.permissionError('create')
        } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
          studentToastMessages.networkError()
        } else {
          studentToastMessages.createError(errorMessage)
        }

        throw error
      }
    },

    // Enhanced update student
    updateStudent: async (id: string, data: StudentUpdateFormData) => {
      set({ isUpdating: true, error: null })

      const loadingToast = studentToastMessages.updateLoading()

      try {
        const response = await api.put(`/api/institute/students/${id}`, data)

        if (response.success) {
          toast.dismiss(loadingToast)
          studentToastMessages.updateSuccess(`${data.firstName} ${data.lastName}`)

          // Update student in local state
          set(state => ({
            students: state.students.map(student =>
              student.id === id ? { ...student, ...response.data } : student
            ),
            isUpdating: false
          }))
        } else {
          throw new Error(response.error || 'Failed to update student')
        }
      } catch (error) {
        toast.dismiss(loadingToast)

        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        set({ error: errorMessage, isUpdating: false })

        studentToastMessages.updateError(errorMessage)
        throw error
      }
    },

    // Enhanced toggle student status
    toggleStudentStatus: async (id: string, is_active: boolean) => {
      set({ isUpdating: true, error: null })

      const action = is_active ? 'Activating' : 'Deactivating'
      const loadingToast = studentToastMessages.statusChangeLoading(action)

      try {
        const response = await api.patch(`/api/institute/students/${id}/status`, { is_active })

        if (response.success) {
          toast.dismiss(loadingToast)

          const student = get().students.find(s => s.id === id)
          const studentName = student ? `${student.firstName} ${student.lastName}` : 'Student'

          if (is_active) {
            studentToastMessages.activateSuccess(studentName)
          } else {
            studentToastMessages.deactivateSuccess(studentName)
          }

          // Update student in local state
          set(state => ({
            students: state.students.map(student =>
              student.id === id ? { ...student, is_active } : student
            ),
            isUpdating: false
          }))
        } else {
          throw new Error(response.error || 'Failed to update student status')
        }
      } catch (error) {
        toast.dismiss(loadingToast)

        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        set({ error: errorMessage, isUpdating: false })

        studentToastMessages.statusChangeError(errorMessage)
        throw error
      }
    }

    // ... rest of store implementation
  }))
)
```

## 16. Student List Management

### 16.1 Advanced Filtering System

**File:** `apps/frontend/src/components/institute/StudentFilters.tsx`

```typescript
'use client'

import React from 'react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DatePickerWithRange } from '@/components/ui/date-range-picker'
import { Search, Filter, X } from 'lucide-react'
import { useStudentStore } from '@/stores/institute/useStudentStore'

interface StudentFiltersProps {
  onFiltersChange: (filters: StudentFilters) => void
}

interface StudentFilters {
  search: string
  branch_id: string
  status: 'all' | 'active' | 'inactive'
  role_id: string
  dateRange: {
    from: Date | null
    to: Date | null
  }
}

export function StudentFilters({ onFiltersChange }: StudentFiltersProps) {
  const {
    availableBranches,
    availableRoles,
    filters,
    setFilters,
    clearFilters
  } = useStudentStore()

  const handleFilterChange = (key: keyof StudentFilters, value: any) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    onFiltersChange(newFilters)
  }

  const handleClearFilters = () => {
    clearFilters()
    onFiltersChange({
      search: '',
      branch_id: '',
      status: 'all',
      role_id: '',
      dateRange: { from: null, to: null }
    })
  }

  const hasActiveFilters = filters.search || filters.branch_id || filters.status !== 'all' ||
                          filters.role_id || filters.dateRange.from || filters.dateRange.to

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
          {hasActiveFilters && (
            <Button variant="outline" size="sm" onClick={handleClearFilters}>
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search by name, email, or phone..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Branch Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Branch</label>
            <Select
              value={filters.branch_id}
              onValueChange={(value) => handleFilterChange('branch_id', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All branches" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All branches</SelectItem>
                {availableBranches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.id}>
                    {branch.name} ({branch.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Status</label>
            <Select
              value={filters.status}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All students</SelectItem>
                <SelectItem value="active">Active only</SelectItem>
                <SelectItem value="inactive">Inactive only</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Role Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Role</label>
            <Select
              value={filters.role_id}
              onValueChange={(value) => handleFilterChange('role_id', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All roles" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All roles</SelectItem>
                {availableRoles.map((role) => (
                  <SelectItem key={role.id} value={role.id}>
                    {role.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Date Range Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Registration Date</label>
            <DatePickerWithRange
              value={filters.dateRange}
              onChange={(range) => handleFilterChange('dateRange', range)}
              placeholder="Select date range"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
```

### 16.2 Student List View Component

**File:** `apps/frontend/src/components/institute/StudentListView.tsx`

```typescript
'use client'

import React, { useState, useEffect } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { MoreHorizontal, Edit, Trash2, Eye, Grid, List, ChevronUp, ChevronDown } from 'lucide-react'
import { useStudentStore } from '@/stores/institute/useStudentStore'
import { StudentStatusToggle } from './StudentStatusToggle'
import { StudentCard } from './StudentCard'
import { Student } from '@/types/student'

type ViewMode = 'table' | 'card'
type SortField = 'name' | 'email' | 'branch' | 'status' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface StudentListViewProps {
  students: Student[]
  onEditStudent: (student: Student) => void
  onDeleteStudent: (student: Student) => void
  onViewStudent: (student: Student) => void
}

export function StudentListView({ students, onEditStudent, onDeleteStudent, onViewStudent }: StudentListViewProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [sortField, setSortField] = useState<SortField>('name')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  const { bulkUpdateStudentStatus } = useStudentStore()

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const sortedStudents = [...students].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortField) {
      case 'name':
        aValue = `${a.firstName} ${a.lastName}`.toLowerCase()
        bValue = `${b.firstName} ${b.lastName}`.toLowerCase()
        break
      case 'email':
        aValue = a.email.toLowerCase()
        bValue = b.email.toLowerCase()
        break
      case 'branch':
        aValue = a.branch?.name?.toLowerCase() || ''
        bValue = b.branch?.name?.toLowerCase() || ''
        break
      case 'status':
        aValue = a.is_active ? 'active' : 'inactive'
        bValue = b.is_active ? 'active' : 'inactive'
        break
      case 'createdAt':
        aValue = new Date(a.createdAt)
        bValue = new Date(b.createdAt)
        break
      default:
        return 0
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedStudents(students.map(student => student.id))
    } else {
      setSelectedStudents([])
    }
  }

  const handleSelectStudent = (studentId: string, checked: boolean) => {
    if (checked) {
      setSelectedStudents([...selectedStudents, studentId])
    } else {
      setSelectedStudents(selectedStudents.filter(id => id !== studentId))
    }
  }

  const handleBulkActivate = async () => {
    try {
      await bulkUpdateStudentStatus(selectedStudents, true)
      setSelectedStudents([])
    } catch (error) {
      // Error handling is done in the store
    }
  }

  const handleBulkDeactivate = async () => {
    try {
      await bulkUpdateStudentStatus(selectedStudents, false)
      setSelectedStudents([])
    } catch (error) {
      // Error handling is done in the store
    }
  }

  const SortableHeader = ({ field, children }: { field: SortField; children: React.ReactNode }) => (
    <TableHead
      className="cursor-pointer hover:bg-gray-50 select-none"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center gap-1">
        {children}
        {sortField === field && (
          sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
        )}
      </div>
    </TableHead>
  )

  if (viewMode === 'card') {
    return (
      <div className="space-y-4">
        {/* View Mode Toggle */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'table' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('table')}
            >
              <List className="h-4 w-4 mr-1" />
              Table
            </Button>
            <Button
              variant={viewMode === 'card' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('card')}
            >
              <Grid className="h-4 w-4 mr-1" />
              Cards
            </Button>
          </div>

          {selectedStudents.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {selectedStudents.length} selected
              </span>
              <Button size="sm" onClick={handleBulkActivate}>
                Activate Selected
              </Button>
              <Button size="sm" variant="outline" onClick={handleBulkDeactivate}>
                Deactivate Selected
              </Button>
            </div>
          )}
        </div>

        {/* Card Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {sortedStudents.map((student) => (
            <StudentCard
              key={student.id}
              student={student}
              isSelected={selectedStudents.includes(student.id)}
              onSelect={(checked) => handleSelectStudent(student.id, checked)}
              onEdit={() => onEditStudent(student)}
              onDelete={() => onDeleteStudent(student)}
              onView={() => onViewStudent(student)}
            />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* View Mode Toggle and Bulk Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'table' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('table')}
          >
            <List className="h-4 w-4 mr-1" />
            Table
          </Button>
          <Button
            variant={viewMode === 'card' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('card')}
          >
            <Grid className="h-4 w-4 mr-1" />
            Cards
          </Button>
        </div>

        {selectedStudents.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              {selectedStudents.length} selected
            </span>
            <Button size="sm" onClick={handleBulkActivate}>
              Activate Selected
            </Button>
            <Button size="sm" variant="outline" onClick={handleBulkDeactivate}>
              Deactivate Selected
            </Button>
          </div>
        )}
      </div>

      {/* Table View */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedStudents.length === students.length && students.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>Student</TableHead>
                <SortableHeader field="email">Email</SortableHeader>
                <SortableHeader field="branch">Branch</SortableHeader>
                <TableHead>Role</TableHead>
                <SortableHeader field="status">Status</SortableHeader>
                <SortableHeader field="createdAt">Joined</SortableHeader>
                <TableHead className="w-12">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedStudents.map((student) => (
                <TableRow key={student.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedStudents.includes(student.id)}
                      onCheckedChange={(checked) => handleSelectStudent(student.id, checked as boolean)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={student.avatar} />
                        <AvatarFallback>
                          {student.firstName[0]}{student.lastName[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">
                          {student.firstName} {student.lastName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {student.phone}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{student.email}</TableCell>
                  <TableCell>
                    {student.branch ? (
                      <Badge variant="outline">
                        {student.branch.name} ({student.branch.code})
                      </Badge>
                    ) : (
                      <span className="text-gray-400">No branch</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {student.role ? (
                      <Badge variant="secondary">{student.role.name}</Badge>
                    ) : (
                      <span className="text-gray-400">No role</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <StudentStatusToggle student={student} />
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {new Date(student.createdAt).toLocaleDateString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onViewStudent(student)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEditStudent(student)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Student
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => onDeleteStudent(student)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Remove Student
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
```

### 16.3 Student Card Component

**File:** `apps/frontend/src/components/institute/StudentCard.tsx`

```typescript
'use client'

import React from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Eye, Mail, Phone, MapPin, Calendar } from 'lucide-react'
import { StudentStatusToggle } from './StudentStatusToggle'
import { Student } from '@/types/student'

interface StudentCardProps {
  student: Student
  isSelected: boolean
  onSelect: (checked: boolean) => void
  onEdit: () => void
  onDelete: () => void
  onView: () => void
}

export function StudentCard({ student, isSelected, onSelect, onEdit, onDelete, onView }: StudentCardProps) {
  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${isSelected ? 'ring-2 ring-blue-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={isSelected}
              onCheckedChange={onSelect}
            />
            <Avatar className="h-12 w-12">
              <AvatarImage src={student.avatar} />
              <AvatarFallback className="bg-blue-100 text-blue-600">
                {student.firstName[0]}{student.lastName[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold text-lg">
                {student.firstName} {student.lastName}
              </h3>
              <p className="text-sm text-gray-500">
                Student ID: {student.id.slice(-8)}
              </p>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onView}>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Student
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onDelete} className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" />
                Remove Student
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Contact Information */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">{student.email}</span>
          </div>
          {student.phone && (
            <div className="flex items-center gap-2 text-sm">
              <Phone className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">{student.phone}</span>
            </div>
          )}
          {student.address && (
            <div className="flex items-center gap-2 text-sm">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600 truncate">{student.address}</span>
            </div>
          )}
        </div>

        {/* Branch and Role */}
        <div className="flex flex-wrap gap-2">
          {student.branch && (
            <Badge variant="outline" className="text-xs">
              {student.branch.name} ({student.branch.code})
            </Badge>
          )}
          {student.role && (
            <Badge variant="secondary" className="text-xs">
              {student.role.name}
            </Badge>
          )}
        </div>

        {/* Status and Stats */}
        <div className="flex items-center justify-between">
          <StudentStatusToggle student={student} />
          <div className="text-right">
            <div className="text-sm font-medium">
              {student.enrolledCourses || 0} Courses
            </div>
            <div className="text-xs text-gray-500">
              {student.totalProgress || 0}% Progress
            </div>
          </div>
        </div>

        {/* Join Date and Last Activity */}
        <div className="pt-2 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              Joined {new Date(student.createdAt).toLocaleDateString()}
            </div>
            {student.lastActivity && (
              <div>
                Last active: {new Date(student.lastActivity).toLocaleDateString()}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-2 pt-2">
          <Button size="sm" variant="outline" onClick={onView} className="flex-1">
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          <Button size="sm" variant="outline" onClick={onEdit} className="flex-1">
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
```

## 17. Complete API Endpoints

### 17.1 Enhanced Student CRUD Endpoints

**File:** `apps/api/src/endpoints/institute/students.ts`

```typescript
import { Endpoint } from 'payload/config'
import { requireAuth } from '../../middleware/auth'

// GET /api/institute/students - List students with filtering, pagination, sorting
export const getStudentsEndpoint: Endpoint = {
  path: '/institute/students',
  method: 'get',
  handler: async (req: any) => {
    const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'institute_staff'])
    const authResult = await authMiddleware(req)

    if (authResult) return authResult

    const user = req.user
    const {
      page = 1,
      limit = 20,
      search = '',
      branch_id = '',
      status = 'all',
      role_id = '',
      sort_field = 'createdAt',
      sort_direction = 'desc',
      date_from,
      date_to
    } = req.query

    try {
      // Build where clause based on user permissions and filters
      const whereClause: any = {
        institute: { equals: user.institute },
        legacyRole: { equals: 'student' }
      }

      // Role-based filtering
      if (user.legacyRole === 'branch_manager') {
        whereClause.branch_id = { equals: user.branch_id || user.branch }
      } else if (user.legacyRole === 'institute_staff') {
        // Filter by accessible branches based on user permissions
        const accessibleBranches = await getAccessibleBranches(user)
        if (accessibleBranches.length > 0) {
          whereClause.branch_id = { in: accessibleBranches }
        }
      }

      // Apply filters
      if (search) {
        whereClause.or = [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (branch_id && branch_id !== 'all') {
        whereClause.branch_id = { equals: branch_id }
      }

      if (status !== 'all') {
        whereClause.is_active = { equals: status === 'active' }
      }

      if (role_id) {
        whereClause.role_id = { equals: role_id }
      }

      if (date_from || date_to) {
        whereClause.createdAt = {}
        if (date_from) whereClause.createdAt.gte = new Date(date_from)
        if (date_to) whereClause.createdAt.lte = new Date(date_to)
      }

      // Build sort options
      const sortOptions: any = {}
      if (sort_field === 'name') {
        sortOptions.firstName = sort_direction
      } else {
        sortOptions[sort_field] = sort_direction
      }

      const students = await req.payload.find({
        collection: 'users',
        where: whereClause,
        page: parseInt(page),
        limit: parseInt(limit),
        sort: sortOptions,
        depth: 2 // Include branch and role relationships
      })

      return Response.json({
        success: true,
        data: students.docs,
        pagination: {
          page: students.page,
          limit: students.limit,
          totalPages: students.totalPages,
          totalDocs: students.totalDocs,
          hasNextPage: students.hasNextPage,
          hasPrevPage: students.hasPrevPage
        }
      })

    } catch (error) {
      console.error('Fetch students error:', error)
      return Response.json({
        success: false,
        error: 'Failed to fetch students'
      }, { status: 500 })
    }
  }
}

// PUT /api/institute/students/:id - Update student
export const updateStudentEndpoint: Endpoint = {
  path: '/institute/students/:id',
  method: 'put',
  handler: async (req: any) => {
    const authMiddleware = requireAuth(['institute_admin', 'branch_manager', 'institute_staff'])
    const authResult = await authMiddleware(req)

    if (authResult) return authResult

    const user = req.user
    const studentId = req.params.id
    const updateData = await req.json()

    try {
      // Verify student exists and user has access
      const existingStudent = await req.payload.findByID({
        collection: 'users',
        id: studentId
      })

      if (!existingStudent || existingStudent.legacyRole !== 'student') {
        return Response.json({
          success: false,
          error: 'Student not found'
        }, { status: 404 })
      }

      if (!validateStudentAccess(user, existingStudent)) {
        return Response.json({
          success: false,
          error: 'Access denied'
        }, { status: 403 })
      }

      // Validate and sanitize update data
      const allowedFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'dateOfBirth', 'gender', 'branch_id', 'role_id', 'is_active']
      const sanitizedData: any = {}

      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          sanitizedData[field] = updateData[field]
        }
      }

      // Role-based field restrictions
      if (user.legacyRole === 'branch_manager') {
        // Branch managers cannot change branch assignment
        delete sanitizedData.branch_id
        delete sanitizedData.is_active
      } else if (user.legacyRole === 'institute_staff') {
        // Staff cannot change active status
        delete sanitizedData.is_active
      }

      // Handle password update separately if provided
      if (updateData.password && updateData.password.trim()) {
        sanitizedData.password = updateData.password
      }

      // Update student
      const updatedStudent = await req.payload.update({
        collection: 'users',
        id: studentId,
        data: {
          ...sanitizedData,
          updatedAt: new Date()
        }
      })

      return Response.json({
        success: true,
        data: updatedStudent,
        message: 'Student updated successfully'
      })

    } catch (error) {
      console.error('Update student error:', error)
      return Response.json({
        success: false,
        error: 'Failed to update student'
      }, { status: 500 })
    }
  }
}

// DELETE /api/institute/students/:id - Soft delete student
export const deleteStudentEndpoint: Endpoint = {
  path: '/institute/students/:id',
  method: 'delete',
  handler: async (req: any) => {
    const authMiddleware = requireAuth(['institute_admin', 'branch_manager'])
    const authResult = await authMiddleware(req)

    if (authResult) return authResult

    const user = req.user
    const studentId = req.params.id
    const { reason } = await req.json()

    try {
      // Verify student exists and user has access
      const existingStudent = await req.payload.findByID({
        collection: 'users',
        id: studentId
      })

      if (!existingStudent || existingStudent.legacyRole !== 'student') {
        return Response.json({
          success: false,
          error: 'Student not found'
        }, { status: 404 })
      }

      if (!validateStudentAccess(user, existingStudent)) {
        return Response.json({
          success: false,
          error: 'Access denied'
        }, { status: 403 })
      }

      // Soft delete by deactivating and marking as deleted
      const deletedStudent = await req.payload.update({
        collection: 'users',
        id: studentId,
        data: {
          is_active: false,
          isDeleted: true,
          deletedAt: new Date(),
          deletedBy: user.id,
          deletionReason: reason
        }
      })

      // Create audit trail
      await createAuditTrail({
        userId: user.id,
        action: 'STUDENT_DELETED',
        targetId: studentId,
        targetType: 'student',
        reason,
        metadata: {
          studentName: `${existingStudent.firstName} ${existingStudent.lastName}`,
          studentEmail: existingStudent.email
        }
      })

      return Response.json({
        success: true,
        message: 'Student removed successfully'
      })

    } catch (error) {
      console.error('Delete student error:', error)
      return Response.json({
        success: false,
        error: 'Failed to remove student'
      }, { status: 500 })
    }
  }
}

// Helper functions
const validateStudentAccess = (user: any, student: any): boolean => {
  // Institute admin can access all students in their institute
  if (user.legacyRole === 'institute_admin') {
    return student.institute === user.institute
  }

  // Branch manager can only access students in their branch
  if (user.legacyRole === 'branch_manager') {
    return student.institute === user.institute &&
           student.branch_id === (user.branch_id || user.branch)
  }

  // Staff can access students in branches they have permission for
  if (user.legacyRole === 'institute_staff') {
    return student.institute === user.institute &&
           user.permissions?.includes(`branch_access_${student.branch_id}`)
  }

  return false
}

const getAccessibleBranches = async (user: any): Promise<string[]> => {
  // Extract branch IDs from user permissions
  const branchPermissions = user.permissions?.filter((p: string) =>
    p.startsWith('branch_access_')
  ) || []

  return branchPermissions.map((p: string) => p.replace('branch_access_', ''))
}

const createAuditTrail = async (data: any) => {
  // Implementation for audit trail creation
  // This would typically create a record in an audit_logs collection
  console.log('Audit trail:', data)
}
```

## 18. Updated Implementation Checklist

### 18.1 Database Schema Updates

- [ ] **Add `branch_id` column** to users table with foreign key constraint
- [ ] **Add `role_id` column** to users table for role management integration
- [ ] **Add `is_active` column** to users table for status management
- [ ] **Create audit_logs table** for tracking student management actions
- [ ] **Update indexes** for performance optimization
- [ ] **Create migration script** for existing data

### 18.2 Backend API Implementation

- [ ] **Enhanced student creation endpoint** with role and status support
- [ ] **Complete student update endpoint** with field-level permissions
- [ ] **Student status toggle endpoint** with audit trail
- [ ] **Student soft delete endpoint** with reason tracking
- [ ] **Advanced filtering endpoint** with pagination and sorting
- [ ] **Role management integration** endpoints
- [ ] **Bulk operations endpoints** for status changes
- [ ] **Audit trail system** implementation

### 18.3 Frontend Components

- [ ] **StudentCreateForm** with role selection and status management
- [ ] **StudentEditForm** with pre-populated fields and validation
- [ ] **StudentListView** with table/card toggle and sorting
- [ ] **StudentCard** component for card view display
- [ ] **StudentFilters** component with advanced filtering
- [ ] **StudentStatusToggle** with confirmation dialogs
- [ ] **RoleSelector** component for role management
- [ ] **Bulk operations UI** for multiple student management

### 18.4 Store Implementation

- [ ] **Enhanced useStudentStore** with complete CRUD operations
- [ ] **useRoleStore** for role management integration
- [ ] **Advanced filtering state management**
- [ ] **Bulk operations state handling**
- [ ] **Optimistic updates** for better UX
- [ ] **Error handling** with specific toast messages
- [ ] **Loading states** for all operations

### 18.5 Toast Notification System

- [ ] **Comprehensive toast templates** for all operations
- [ ] **Success messages** with student names and actions
- [ ] **Error messages** with specific validation details
- [ ] **Loading states** with descriptive messages
- [ ] **Bulk operation notifications**
- [ ] **Permission error handling**
- [ ] **Network error handling**

### 18.6 Testing Requirements

- [ ] **Unit tests** for all API endpoints
- [ ] **Component tests** for all React components
- [ ] **Integration tests** for complete workflows
- [ ] **Permission testing** for all user roles
- [ ] **Bulk operations testing**
- [ ] **Error handling testing**
- [ ] **Performance testing** with large datasets

## 19. File Structure Summary

```
apps/
├── api/src/
│   ├── endpoints/institute/
│   │   └── students.ts (Complete CRUD endpoints)
│   ├── middleware/
│   │   └── auth.ts (Enhanced with role permissions)
│   └── collections/
│       ├── Users.ts (Updated with new fields)
│       └── AuditLogs.ts (New collection)
├── frontend/src/
│   ├── stores/institute/
│   │   ├── useStudentStore.ts (Enhanced with CRUD)
│   │   └── useRoleStore.ts (New store)
│   ├── components/institute/
│   │   ├── StudentCreateForm.tsx
│   │   ├── StudentEditForm.tsx
│   │   ├── StudentListView.tsx
│   │   ├── StudentCard.tsx
│   │   ├── StudentFilters.tsx
│   │   ├── StudentStatusToggle.tsx
│   │   └── RoleSelector.tsx
│   ├── lib/
│   │   └── toastTemplates.ts (Toast message templates)
│   └── types/
│       └── student.ts (TypeScript interfaces)
└── docs/
    └── phase-11-student-creation-workflow.md (This document)
```

## 20. Summary

This comprehensive Phase 11 implementation guide provides:

### **Complete CRUD Operations:**
- Create, Read, Update, Delete functionality for students
- Role-based permissions and field visibility
- Advanced filtering, sorting, and pagination
- Bulk operations support

### **Enhanced User Experience:**
- Table and card view options
- Real-time status management
- Comprehensive toast notifications
- Optimistic updates and loading states

### **Role Management Integration:**
- Student role assignment and management
- Permission-based access control
- Role inheritance and validation

### **Robust Architecture:**
- Follows existing codebase patterns
- Uses existing users table structure
- Implements proper error handling
- Includes audit trail functionality

### **Production Ready Features:**
- Comprehensive validation and security
- Performance optimizations
- Scalable component architecture
- Complete testing coverage

The implementation maintains consistency with existing patterns while providing a complete, production-ready student management system with full CRUD capabilities, advanced filtering, role management, and comprehensive user experience enhancements.
```
```
```
```
