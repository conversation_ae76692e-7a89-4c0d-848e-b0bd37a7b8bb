# 📝 Phase 1: Code Templates & Implementation Examples

## 🎯 Overview
This document contains code templates and implementation examples for Phase 1 of the Groups Exam LMS SaaS platform development with integrated backend structure.

## 🏗️ Integrated Structure Notes
- **Backend API**: Located in `apps/api/` (Payload CMS)
- **Frontend App**: Single Next.js app in `apps/frontend/` serving all panels
- **Role-Based Routing**: `/super-admin/*`, `/admin/*`, and `/` routes
- **Shared Code**: Organized by user roles in `apps/frontend/src/components/`, `stores/`, `lib/`, `types/`, `hooks/`
- **API Calls**: All point to integrated backend at `process.env.NEXT_PUBLIC_API_URL`

## 🔔 Toast Notification Components

### **Toast Provider Component**

#### **components/shared/notifications/ToastProvider.tsx**
```typescript
'use client'

import React, { createContext, useContext, useState, useCallback } from 'react'
import { Toast } from './Toast'

export type ToastType = 'success' | 'error' | 'warning' | 'info'

export interface ToastMessage {
  id: string
  type: ToastType
  title: string
  description?: string
  duration?: number
}

interface ToastContextType {
  showToast: (toast: Omit<ToastMessage, 'id'>) => void
  hideToast: (id: string) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<ToastMessage[]>([])

  const showToast = useCallback((toast: Omit<ToastMessage, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast: ToastMessage = {
      ...toast,
      id,
      duration: toast.duration || 5000
    }

    setToasts(prev => [...prev, newToast])

    // Auto remove toast after duration
    setTimeout(() => {
      hideToast(id)
    }, newToast.duration)
  }, [])

  const hideToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }, [])

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {toasts.map(toast => (
          <Toast
            key={toast.id}
            {...toast}
            onClose={() => hideToast(toast.id)}
          />
        ))}
      </div>
    </ToastContext.Provider>
  )
}

export const useToast = () => {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}
```

### **Toast Component**

#### **components/shared/notifications/Toast.tsx**
```typescript
'use client'

import React, { useEffect, useState } from 'react'
import { X, CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react'
import { ToastType } from './ToastProvider'

interface ToastProps {
  id: string
  type: ToastType
  title: string
  description?: string
  duration?: number
  onClose: () => void
}

const toastStyles = {
  success: {
    container: 'bg-green-50 border-green-200 text-green-800',
    icon: CheckCircle,
    iconColor: 'text-green-500'
  },
  error: {
    container: 'bg-red-50 border-red-200 text-red-800',
    icon: XCircle,
    iconColor: 'text-red-500'
  },
  warning: {
    container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    icon: AlertTriangle,
    iconColor: 'text-yellow-500'
  },
  info: {
    container: 'bg-blue-50 border-blue-200 text-blue-800',
    icon: Info,
    iconColor: 'text-blue-500'
  }
}

export function Toast({ id, type, title, description, onClose }: ToastProps) {
  const [isVisible, setIsVisible] = useState(false)
  const style = toastStyles[type]
  const IconComponent = style.icon

  useEffect(() => {
    // Trigger animation
    setTimeout(() => setIsVisible(true), 10)
  }, [])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(onClose, 300) // Wait for animation to complete
  }

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out
        ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
        max-w-sm w-full bg-white border rounded-lg shadow-lg p-4
        ${style.container}
      `}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <IconComponent className={`h-5 w-5 ${style.iconColor}`} />
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium">{title}</p>
          {description && (
            <p className="mt-1 text-sm opacity-90">{description}</p>
          )}
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={handleClose}
            className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}
```

### **Toast Hook**

#### **components/shared/notifications/useToast.ts**
```typescript
import { useToast as useToastContext } from './ToastProvider'

// Pre-configured toast functions for common scenarios
export const useToast = () => {
  const { showToast } = useToastContext()

  return {
    // Authentication toasts
    loginSuccess: (message?: string) => showToast({
      type: 'success',
      title: 'Login Successful',
      description: message || 'Welcome back! You have been successfully logged in.'
    }),

    loginError: (message?: string) => showToast({
      type: 'error',
      title: 'Login Failed',
      description: message || 'Invalid email or password. Please try again.'
    }),

    registerSuccess: (message?: string) => showToast({
      type: 'success',
      title: 'Registration Successful',
      description: message || 'Your account has been created successfully.'
    }),

    registerError: (message?: string) => showToast({
      type: 'error',
      title: 'Registration Failed',
      description: message || 'Unable to create account. Please try again.'
    }),

    emailVerificationSent: () => showToast({
      type: 'info',
      title: 'Verification Email Sent',
      description: 'Please check your email and click the verification link.'
    }),

    emailVerified: () => showToast({
      type: 'success',
      title: 'Email Verified',
      description: 'Your email has been successfully verified.'
    }),

    passwordResetSent: () => showToast({
      type: 'info',
      title: 'Password Reset Email Sent',
      description: 'Check your email for password reset instructions.'
    }),

    passwordResetSuccess: () => showToast({
      type: 'success',
      title: 'Password Reset Successful',
      description: 'Your password has been updated successfully.'
    }),

    logoutSuccess: () => showToast({
      type: 'success',
      title: 'Logged Out',
      description: 'You have been successfully logged out.'
    }),

    // Generic toasts
    success: (title: string, description?: string) => showToast({
      type: 'success',
      title,
      description
    }),

    error: (title: string, description?: string) => showToast({
      type: 'error',
      title,
      description
    }),

    warning: (title: string, description?: string) => showToast({
      type: 'warning',
      title,
      description
    }),

    info: (title: string, description?: string) => showToast({
      type: 'info',
      title,
      description
    }),

    // CRUD operation toasts
    createSuccess: (entity: string) => showToast({
      type: 'success',
      title: 'Created Successfully',
      description: `${entity} has been created successfully.`
    }),

    updateSuccess: (entity: string) => showToast({
      type: 'success',
      title: 'Updated Successfully',
      description: `${entity} has been updated successfully.`
    }),

    deleteSuccess: (entity: string) => showToast({
      type: 'success',
      title: 'Deleted Successfully',
      description: `${entity} has been deleted successfully.`
    }),

    saveError: (entity: string) => showToast({
      type: 'error',
      title: 'Save Failed',
      description: `Unable to save ${entity}. Please try again.`
    }),

    // Network and loading toasts
    networkError: () => showToast({
      type: 'error',
      title: 'Network Error',
      description: 'Please check your internet connection and try again.'
    }),

    serverError: () => showToast({
      type: 'error',
      title: 'Server Error',
      description: 'Something went wrong on our end. Please try again later.'
    }),

    // Custom toast with all options
    custom: showToast
  }
}
```

## 🗂️ Zustand Store Templates

### **Authentication Store**

#### **useAuthStore.ts**
```typescript
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: string
  email: string
  name: string
  role: 'super_admin' | 'platform_staff' | 'institute_admin' | 'trainer' | 'student'
  instituteId?: string
  permissions?: string[]
}

interface AuthStore {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
  updateUser: (updates: Partial<User>) => void
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      
      login: async (email: string, password: string) => {
        set({ isLoading: true })
        try {
          // API calls to integrated backend at apps/api/
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ email, password })
          })
          
          if (response.ok) {
            const { user, token } = await response.json()
            set({ user, isAuthenticated: true, isLoading: false })
          } else {
            const error = await response.json()
            throw new Error(error.message || 'Login failed')
          }
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },
      
      logout: async () => {
        try {
          // Logout API call to integrated backend
          await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/logout`, {
            method: 'POST',
            credentials: 'include'
          })
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          set({ user: null, isAuthenticated: false })
        }
      },
      
      checkAuth: async () => {
        set({ isLoading: true })
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/me`, {
            credentials: 'include'
          })
          
          if (response.ok) {
            const user = await response.json()
            set({ user, isAuthenticated: true, isLoading: false })
          } else {
            set({ user: null, isAuthenticated: false, isLoading: false })
          }
        } catch (error) {
          set({ user: null, isAuthenticated: false, isLoading: false })
        }
      },
      
      updateUser: (updates: Partial<User>) => {
        const currentUser = get().user
        if (currentUser) {
          set({ user: { ...currentUser, ...updates } })
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        isAuthenticated: state.isAuthenticated 
      })
    }
  )
)
```

### **Institute Store (Super Admin)**

#### **useInstituteStore.ts**
```typescript
import { create } from 'zustand'

interface Institute {
  id: string
  name: string
  slug: string
  email: string
  phone: string
  status: 'setup' | 'active' | 'suspended'
  subscriptionPlan: string
  setupFeePaid: boolean
  commissionRate: number
  createdAt: string
}

interface InstituteStore {
  institutes: Institute[]
  selectedInstitute: Institute | null
  isLoading: boolean
  error: string | null
  
  // Actions
  fetchInstitutes: () => Promise<void>
  createInstitute: (data: Partial<Institute>) => Promise<void>
  updateInstitute: (id: string, data: Partial<Institute>) => Promise<void>
  deleteInstitute: (id: string) => Promise<void>
  setSelectedInstitute: (institute: Institute | null) => void
}

export const useInstituteStore = create<InstituteStore>((set, get) => ({
  institutes: [],
  selectedInstitute: null,
  isLoading: false,
  error: null,
  
  fetchInstitutes: async () => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/institutes`, {
        credentials: 'include'
      })
      
      if (response.ok) {
        const institutes = await response.json()
        set({ institutes, isLoading: false })
      } else {
        throw new Error('Failed to fetch institutes')
      }
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  createInstitute: async (data: Partial<Institute>) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/institutes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data)
      })
      
      if (response.ok) {
        const newInstitute = await response.json()
        set(state => ({
          institutes: [...state.institutes, newInstitute],
          isLoading: false
        }))
      } else {
        throw new Error('Failed to create institute')
      }
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  updateInstitute: async (id: string, data: Partial<Institute>) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/institutes/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data)
      })
      
      if (response.ok) {
        const updatedInstitute = await response.json()
        set(state => ({
          institutes: state.institutes.map(inst => 
            inst.id === id ? updatedInstitute : inst
          ),
          selectedInstitute: state.selectedInstitute?.id === id ? updatedInstitute : state.selectedInstitute,
          isLoading: false
        }))
      } else {
        throw new Error('Failed to update institute')
      }
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  deleteInstitute: async (id: string) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/institutes/${id}`, {
        method: 'DELETE',
        credentials: 'include'
      })
      
      if (response.ok) {
        set(state => ({
          institutes: state.institutes.filter(inst => inst.id !== id),
          selectedInstitute: state.selectedInstitute?.id === id ? null : state.selectedInstitute,
          isLoading: false
        }))
      } else {
        throw new Error('Failed to delete institute')
      }
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  setSelectedInstitute: (institute: Institute | null) => {
    set({ selectedInstitute: institute })
  }
}))
```

### **Course Store (Institute Admin)**

#### **useCourseStore.ts**
```typescript
import { create } from 'zustand'

interface Course {
  id: string
  title: string
  description: string
  price: number
  duration: number
  status: 'draft' | 'published' | 'archived'
  thumbnailUrl?: string
  instructorId: string
  categoryId: string
  totalLessons: number
  totalEnrollments: number
  createdAt: string
}

interface CourseStore {
  courses: Course[]
  selectedCourse: Course | null
  isLoading: boolean
  error: string | null
  filters: {
    status?: string
    category?: string
    instructor?: string
  }
  
  // Actions
  fetchCourses: () => Promise<void>
  createCourse: (data: Partial<Course>) => Promise<void>
  updateCourse: (id: string, data: Partial<Course>) => Promise<void>
  deleteCourse: (id: string) => Promise<void>
  setSelectedCourse: (course: Course | null) => void
  setFilters: (filters: Partial<CourseStore['filters']>) => void
}

export const useCourseStore = create<CourseStore>((set, get) => ({
  courses: [],
  selectedCourse: null,
  isLoading: false,
  error: null,
  filters: {},
  
  fetchCourses: async () => {
    set({ isLoading: true, error: null })
    try {
      const { filters } = get()
      const queryParams = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value)
      })
      
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/courses?${queryParams}`,
        { credentials: 'include' }
      )
      
      if (response.ok) {
        const courses = await response.json()
        set({ courses, isLoading: false })
      } else {
        throw new Error('Failed to fetch courses')
      }
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  createCourse: async (data: Partial<Course>) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/courses`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data)
      })
      
      if (response.ok) {
        const newCourse = await response.json()
        set(state => ({
          courses: [...state.courses, newCourse],
          isLoading: false
        }))
      } else {
        throw new Error('Failed to create course')
      }
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  updateCourse: async (id: string, data: Partial<Course>) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/courses/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data)
      })
      
      if (response.ok) {
        const updatedCourse = await response.json()
        set(state => ({
          courses: state.courses.map(course => 
            course.id === id ? updatedCourse : course
          ),
          selectedCourse: state.selectedCourse?.id === id ? updatedCourse : state.selectedCourse,
          isLoading: false
        }))
      } else {
        throw new Error('Failed to update course')
      }
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  deleteCourse: async (id: string) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/courses/${id}`, {
        method: 'DELETE',
        credentials: 'include'
      })
      
      if (response.ok) {
        set(state => ({
          courses: state.courses.filter(course => course.id !== id),
          selectedCourse: state.selectedCourse?.id === id ? null : state.selectedCourse,
          isLoading: false
        }))
      } else {
        throw new Error('Failed to delete course')
      }
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false })
    }
  },
  
  setSelectedCourse: (course: Course | null) => {
    set({ selectedCourse: course })
  },
  
  setFilters: (newFilters: Partial<CourseStore['filters']>) => {
    set(state => ({
      filters: { ...state.filters, ...newFilters }
    }))
  }
}))
```

## 📋 Formik Form Templates

### **Login Form Component**

#### **LoginForm.tsx**
```typescript
'use client'

import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuthStore } from '@/stores/useAuthStore'
import { useState } from 'react'
import { Eye, EyeOff, Loader2 } from 'lucide-react'

const loginSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required')
})

interface LoginFormValues {
  email: string
  password: string
}

interface LoginFormProps {
  title?: string
  subtitle?: string
  onSuccess?: () => void
}

export function LoginForm({ title = "Sign In", subtitle, onSuccess }: LoginFormProps) {
  const login = useAuthStore(state => state.login)
  const isLoading = useAuthStore(state => state.isLoading)
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)

  const handleSubmit = async (values: LoginFormValues, { setSubmitting }: any) => {
    try {
      setError(null)
      await login(values.email, values.password)
      onSuccess?.()
    } catch (error) {
      setError((error as Error).message || 'Invalid email or password')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold">{title}</CardTitle>
        {subtitle && <p className="text-muted-foreground">{subtitle}</p>}
      </CardHeader>
      <CardContent>
        <Formik
          initialValues={{ email: '', password: '' }}
          validationSchema={loginSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isSubmitting }) => (
            <Form className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Field
                  as={Input}
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className={errors.email && touched.email ? 'border-destructive' : ''}
                />
                {errors.email && touched.email && (
                  <p className="text-sm text-destructive">{errors.email}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Field
                    as={Input}
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="••••••••"
                    className={errors.password && touched.password ? 'border-destructive pr-10' : 'pr-10'}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.password && touched.password && (
                  <p className="text-sm text-destructive">{errors.password}</p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting || isLoading}
              >
                {isSubmitting || isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
```

### **Institute Creation Form**

#### **InstituteForm.tsx**
```typescript
'use client'

import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useInstituteStore } from '@/stores/useInstituteStore'
import { useState } from 'react'

const instituteSchema = Yup.object({
  name: Yup.string()
    .min(2, 'Institute name must be at least 2 characters')
    .max(100, 'Institute name must be less than 100 characters')
    .required('Institute name is required'),
  slug: Yup.string()
    .matches(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .min(3, 'Slug must be at least 3 characters')
    .max(50, 'Slug must be less than 50 characters')
    .required('Slug is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  phone: Yup.string()
    .matches(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/, 'Invalid phone number')
    .required('Phone number is required'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  subscriptionPlanId: Yup.string()
    .required('Subscription plan is required'),
  adminName: Yup.string()
    .min(2, 'Admin name must be at least 2 characters')
    .required('Admin name is required'),
  adminEmail: Yup.string()
    .email('Invalid email address')
    .required('Admin email is required'),
  adminPassword: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number')
    .required('Admin password is required')
})

interface InstituteFormValues {
  name: string
  slug: string
  email: string
  phone: string
  description: string
  subscriptionPlanId: string
  adminName: string
  adminEmail: string
  adminPassword: string
}

interface InstituteFormProps {
  onSuccess?: () => void
  onCancel?: () => void
}

const subscriptionPlans = [
  { id: 'starter', name: 'Starter Plan - $99 setup, 15% commission' },
  { id: 'growth', name: 'Growth Plan - $199 setup, 12% commission' },
  { id: 'professional', name: 'Professional Plan - $399 setup, 10% commission' },
  { id: 'enterprise', name: 'Enterprise Plan - $799 setup, 8% commission' }
]

export function InstituteForm({ onSuccess, onCancel }: InstituteFormProps) {
  const createInstitute = useInstituteStore(state => state.createInstitute)
  const isLoading = useInstituteStore(state => state.isLoading)
  const [error, setError] = useState<string | null>(null)

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleSubmit = async (values: InstituteFormValues, { setSubmitting }: any) => {
    try {
      setError(null)
      await createInstitute(values)
      onSuccess?.()
    } catch (error) {
      setError((error as Error).message || 'Failed to create institute')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Create New Institute</CardTitle>
      </CardHeader>
      <CardContent>
        <Formik
          initialValues={{
            name: '',
            slug: '',
            email: '',
            phone: '',
            description: '',
            subscriptionPlanId: '',
            adminName: '',
            adminEmail: '',
            adminPassword: ''
          }}
          validationSchema={instituteSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isSubmitting, values, setFieldValue }) => (
            <Form className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Institute Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Institute Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Institute Name</Label>
                    <Field
                      as={Input}
                      id="name"
                      name="name"
                      placeholder="ABC Learning Academy"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        setFieldValue('name', e.target.value)
                        if (!values.slug) {
                          setFieldValue('slug', generateSlug(e.target.value))
                        }
                      }}
                      className={errors.name && touched.name ? 'border-destructive' : ''}
                    />
                    {errors.name && touched.name && (
                      <p className="text-sm text-destructive">{errors.name}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="slug">URL Slug</Label>
                    <Field
                      as={Input}
                      id="slug"
                      name="slug"
                      placeholder="abc-learning-academy"
                      className={errors.slug && touched.slug ? 'border-destructive' : ''}
                    />
                    {errors.slug && touched.slug && (
                      <p className="text-sm text-destructive">{errors.slug}</p>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Will be used as: {values.slug}.groups-exam.com
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Institute Email</Label>
                    <Field
                      as={Input}
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      className={errors.email && touched.email ? 'border-destructive' : ''}
                    />
                    {errors.email && touched.email && (
                      <p className="text-sm text-destructive">{errors.email}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Field
                      as={Input}
                      id="phone"
                      name="phone"
                      placeholder="+****************"
                      className={errors.phone && touched.phone ? 'border-destructive' : ''}
                    />
                    {errors.phone && touched.phone && (
                      <p className="text-sm text-destructive">{errors.phone}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Field
                    as={Textarea}
                    id="description"
                    name="description"
                    placeholder="Brief description of the institute..."
                    rows={3}
                    className={errors.description && touched.description ? 'border-destructive' : ''}
                  />
                  {errors.description && touched.description && (
                    <p className="text-sm text-destructive">{errors.description}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subscriptionPlanId">Subscription Plan</Label>
                  <Field name="subscriptionPlanId">
                    {({ field }: any) => (
                      <Select onValueChange={(value) => setFieldValue('subscriptionPlanId', value)}>
                        <SelectTrigger className={errors.subscriptionPlanId && touched.subscriptionPlanId ? 'border-destructive' : ''}>
                          <SelectValue placeholder="Select a subscription plan" />
                        </SelectTrigger>
                        <SelectContent>
                          {subscriptionPlans.map((plan) => (
                            <SelectItem key={plan.id} value={plan.id}>
                              {plan.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </Field>
                  {errors.subscriptionPlanId && touched.subscriptionPlanId && (
                    <p className="text-sm text-destructive">{errors.subscriptionPlanId}</p>
                  )}
                </div>
              </div>

              {/* Admin Account */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Institute Admin Account</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="adminName">Admin Name</Label>
                    <Field
                      as={Input}
                      id="adminName"
                      name="adminName"
                      placeholder="John Doe"
                      className={errors.adminName && touched.adminName ? 'border-destructive' : ''}
                    />
                    {errors.adminName && touched.adminName && (
                      <p className="text-sm text-destructive">{errors.adminName}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="adminEmail">Admin Email</Label>
                    <Field
                      as={Input}
                      id="adminEmail"
                      name="adminEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      className={errors.adminEmail && touched.adminEmail ? 'border-destructive' : ''}
                    />
                    {errors.adminEmail && touched.adminEmail && (
                      <p className="text-sm text-destructive">{errors.adminEmail}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="adminPassword">Admin Password</Label>
                  <Field
                    as={Input}
                    id="adminPassword"
                    name="adminPassword"
                    type="password"
                    placeholder="••••••••"
                    className={errors.adminPassword && touched.adminPassword ? 'border-destructive' : ''}
                  />
                  {errors.adminPassword && touched.adminPassword && (
                    <p className="text-sm text-destructive">{errors.adminPassword}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Password must contain at least 8 characters with uppercase, lowercase, and number
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-4 pt-4">
                <Button
                  type="submit"
                  disabled={isSubmitting || isLoading}
                  className="flex-1"
                >
                  {isSubmitting || isLoading ? 'Creating...' : 'Create Institute'}
                </Button>
                {onCancel && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                )}
              </div>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
```
