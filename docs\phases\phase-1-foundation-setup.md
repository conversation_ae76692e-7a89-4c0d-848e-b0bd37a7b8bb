# 🚀 Phase 1: Foundation Setup - Complete Development Plan

## 📋 Overview
Phase 1 focuses on setting up the complete development environment, tech stack installation, and project structure for the Groups Exam LMS SaaS platform.

### 🎯 Objectives
- ✅ Install and configure complete tech stack
- ✅ Set up monorepo structure with proper folder organization
- ✅ Configure development environment
- ✅ Create organized folder structure for all user roles
- ✅ Establish foundation for future development phases

### ⏱️ Timeline
**Duration**: 2 weeks (10 working days)
**Team Size**: 2-3 developers

## 🛠️ Tech Stack

### **Backend**
- **Payload CMS**: Headless CMS with admin panel
- **PostgreSQL**: Primary database
- **Node.js**: Runtime environment
- **TypeScript**: Type safety

### **Frontend**
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type safety
- **TailwindCSS**: Utility-first CSS framework
- **Shadcn/ui + Radix**: Component library

### **State Management & Forms**
- **Zustand**: Lightweight state management
- **Formik**: Form handling
- **Yup**: Schema validation
- **React Query**: Server state management

### **Development Tools**
- **Turborepo**: Monorepo management
- **pnpm**: Package manager
- **ESLint**: Code linting
- **Prettier**: Code formatting

## 📁 Project Structure

```
groups-exam-lms/
├── 📁 apps/                             # All Applications (Frontend + Backend)
│   ├── 📁 api/                          # Payload CMS Backend (Integrated)
│   │   ├── 📁 src/
│   │   │   ├── 📁 collections/
│   │   │   │   ├── 📁 super-admin/      # Super Admin collections
│   │   │   │   ├── 📁 institute-admin/  # Institute Admin collections
│   │   │   │   ├── 📁 student/          # Student collections
│   │   │   │   └── 📁 shared/           # Shared collections
│   │   │   ├── 📁 globals/              # Global settings
│   │   │   ├── 📁 hooks/                # Payload hooks
│   │   │   ├── 📁 access/               # Access control
│   │   │   ├── 📁 endpoints/            # Custom API endpoints
│   │   │   └── 📁 utilities/            # Utility functions
│   │   ├── payload.config.ts
│   │   └── server.ts
│   └── 📁 frontend/                     # Single Next.js App for All Panels
│       ├── 📁 src/
│       │   ├── 📁 app/                  # Next.js App Router
│       │   │   ├── 📁 super-admin/      # Super Admin Routes
│       │   │   ├── 📁 admin/            # Institute Admin Routes
│       │   │   └── 📁 (student)/        # Student Routes (root level)
│       │   ├── 📁 components/           # Components by User Roles
│       │   │   ├── 📁 super-admin/      # Super Admin components
│       │   │   ├── 📁 institute-admin/  # Institute Admin components
│       │   │   └── 📁 student/          # Student components
│       │   ├── 📁 stores/               # State Management by User Roles
│       │   │   ├── 📁 super-admin/      # Super Admin stores
│       │   │   ├── 📁 institute-admin/  # Institute Admin stores
│       │   │   └── 📁 student/          # Student stores
│       │   ├── 📁 lib/                  # Utilities by User Roles
│       │   │   ├── 📁 super-admin/      # Super Admin utilities
│       │   │   ├── 📁 institute-admin/  # Institute Admin utilities
│       │   │   └── 📁 student/          # Student utilities
│       │   ├── 📁 types/                # TypeScript Types
│       │   │   ├── 📁 super-admin/
│       │   │   ├── 📁 institute-admin/
│       │   │   └── 📁 student/
│       │   └── 📁 hooks/                # Custom Hooks
│       │       ├── 📁 super-admin/
│       │       ├── 📁 institute-admin/
│       │       └── 📁 student/
│       ├── 📁 public/                   # Static Assets
│       │   ├── 📁 themes/               # Landing Page Themes
│       │   └── 📁 assets/               # Images, Icons
│       ├── next.config.js
│       ├── tailwind.config.js
│       └── package.json
│

│
├── 📁 packages/                         # Shared Packages
│   ├── 📁 ui/                           # Shared UI components
│   ├── 📁 utils/                        # Shared utilities
│   └── 📁 config/                       # Shared configuration
│
├── 📁 public/                           # Public assets
│   ├── 📁 themes/                       # Theme templates
│   └── 📁 assets/                       # Static assets
│
├── 📁 docs/                             # Documentation
│   ├── 📁 phases/                       # Phase documentation
│   └── 📁 api/                          # API documentation
│
├── package.json                         # Root package.json
├── pnpm-workspace.yaml                  # Monorepo config
├── turbo.json                           # Turborepo config
└── README.md
```

## 🔧 Installation Steps

### **Step 1: Project Initialization**
```bash
# Create main project directory
mkdir groups-exam-lms
cd groups-exam-lms

# Initialize as monorepo
npm init -y
npm install -g pnpm
```

### **Step 2: Integrated Backend Setup (Payload CMS)**
```bash
# Create apps directory first
mkdir apps
cd apps

# Create API directory for Payload CMS
mkdir api
cd api

# Initialize Payload CMS
npx create-payload-app@latest . --template blank --use-npm

# Install additional dependencies
npm install @payloadcms/plugin-cloud-storage
npm install @payloadcms/plugin-form-builder
npm install @payloadcms/plugin-seo
npm install bcryptjs jsonwebtoken cors helmet express-rate-limit

cd ../..
```

### **Step 3: Frontend Folder Structure Setup**
```bash
# Create main directories with user-role separation
mkdir -p {components,stores,lib,types,hooks}

# Complete apps directory (API already created in Step 2)
mkdir -p apps/{super-admin,institute-admin,student}

# Components directory (separated by user roles)
mkdir -p components/super-admin/{ui,forms,charts,tables,modals,layouts}
mkdir -p components/institute-admin/{ui,forms,course-builder,analytics,payment,layouts}
mkdir -p components/student/{ui,course-player,exam-interface,progress,layouts}

# Stores directory (separated by user roles)
mkdir -p stores/super-admin
mkdir -p stores/institute-admin
mkdir -p stores/student

# Lib directory (separated by user roles)
mkdir -p lib/super-admin
mkdir -p lib/institute-admin
mkdir -p lib/student

# Types directory (separated by user roles)
mkdir -p types/super-admin
mkdir -p types/institute-admin
mkdir -p types/student

# Hooks directory (separated by user roles)
mkdir -p hooks/super-admin
mkdir -p hooks/institute-admin
mkdir -p hooks/student

# Note: This creates organized folder structure by user roles
# Backend is integrated in apps/api/ directory
```

### **Step 4: Shared Packages Folder Structure**
```bash
# Create packages directory structure
mkdir -p packages/{types,ui,utils,config}

# Types package structure
mkdir -p packages/types/src/{api,database,forms,components}

# UI package structure
mkdir -p packages/ui/src/{components,hooks,utils}

# Utils package structure
mkdir -p packages/utils/src/{api,validation,formatting,constants}

# Config package structure
mkdir -p packages/config/src/{database,auth,payment,email}

# Note: Package dependencies will be installed when implementing actual packages
```

### **Step 5: UI Components Setup**
```bash
# Create shared UI components structure
mkdir -p components/shared/{ui,notifications,layouts}

# Toast notification components
mkdir -p components/shared/notifications

# Create toast component files
touch components/shared/notifications/Toast.tsx
touch components/shared/notifications/ToastProvider.tsx
touch components/shared/notifications/useToast.ts

# Shared UI components
mkdir -p components/shared/ui/{forms,buttons,modals,alerts}

echo "✅ UI components structure created!"
```

### **Step 6: Public Assets Structure**
```bash
# Create public directory structure
mkdir -p public/{themes,assets}

# Platform themes structure
mkdir -p public/themes/platform-themes/{saas-modern,saas-corporate,saas-startup}

# Institute themes structure
mkdir -p public/themes/institute-themes/{education-modern,education-classic,coaching-academy}

# Assets structure
mkdir -p public/assets/{images,icons,videos,documents}

# Create placeholder files for themes
touch public/themes/platform-themes/saas-modern/{preview.jpg,demo.jpg}
touch public/themes/platform-themes/saas-corporate/{preview.jpg,demo.jpg}
touch public/themes/platform-themes/saas-startup/{preview.jpg,demo.jpg}

touch public/themes/institute-themes/education-modern/{preview.jpg,demo.jpg}
touch public/themes/institute-themes/education-classic/{preview.jpg,demo.jpg}
touch public/themes/institute-themes/coaching-academy/{preview.jpg,demo.jpg}
```

## 🏗️ Detailed Folder Organization

### **User Role-Based Folder Structure**

#### **Components Structure (by User Roles)**
```
components/
├── 📁 super-admin/
│   ├── 📁 ui/                          # UI components
│   ├── 📁 forms/                       # Form components
│   ├── 📁 charts/                      # Analytics charts
│   ├── 📁 tables/                      # Data tables
│   ├── 📁 modals/                      # Modal dialogs
│   └── 📁 layouts/                     # Layout components
├── 📁 institute-admin/
│   ├── 📁 ui/                          # UI components
│   ├── 📁 forms/                       # Form components
│   ├── 📁 course-builder/              # Course creation UI
│   ├── 📁 analytics/                   # Analytics components
│   ├── 📁 payment/                     # Payment components
│   └── 📁 layouts/                     # Layout components
└── 📁 student/
    ├── 📁 ui/                          # UI components
    ├── 📁 course-player/               # Video player components
    ├── 📁 exam-interface/              # Exam UI components
    ├── 📁 progress/                    # Progress tracking
    └── 📁 layouts/                     # Layout components
```

#### **Stores Structure (by User Roles)**
```
stores/
├── 📁 super-admin/
│   ├── useAuthStore.ts
│   ├── useInstituteStore.ts
│   ├── useUserStore.ts
│   ├── useBillingStore.ts
│   └── useAnalyticsStore.ts
├── 📁 institute-admin/
│   ├── useAuthStore.ts
│   ├── useCourseStore.ts
│   ├── useUserStore.ts
│   ├── useAnalyticsStore.ts
│   ├── useBillingStore.ts
│   └── useBranchStore.ts
└── 📁 student/
    ├── useAuthStore.ts
    ├── useCourseStore.ts
    ├── useProgressStore.ts
    ├── useExamStore.ts
    └── useProfileStore.ts
```

#### **Lib Structure (by User Roles)**
```
lib/
├── 📁 super-admin/
│   ├── api.ts                          # API client
│   ├── utils.ts                        # Helper functions
│   ├── validations.ts                  # Validation schemas
│   └── constants.ts                    # Constants
├── 📁 institute-admin/
│   ├── api.ts                          # API client
│   ├── utils.ts                        # Helper functions
│   ├── validations.ts                  # Validation schemas
│   └── constants.ts                    # Constants
└── 📁 student/
    ├── api.ts                          # API client
    ├── utils.ts                        # Helper functions
    ├── validations.ts                  # Validation schemas
    └── constants.ts                    # Constants
```

#### **Types Structure (by User Roles)**
```
types/
├── 📁 super-admin/
│   ├── institute.ts                    # Institute types
│   ├── user.ts                         # User types
│   ├── billing.ts                      # Billing types
│   └── analytics.ts                    # Analytics types
├── 📁 institute-admin/
│   ├── course.ts                       # Course types
│   ├── user.ts                         # User types
│   ├── branch.ts                       # Branch types
│   └── analytics.ts                    # Analytics types
└── 📁 student/
    ├── course.ts                       # Course types
    ├── progress.ts                     # Progress types
    ├── exam.ts                         # Exam types
    └── profile.ts                      # Profile types
```

#### **Hooks Structure (by User Roles)**
```
hooks/
├── 📁 super-admin/
│   ├── useInstitutes.ts                # Institute management hooks
│   ├── useUsers.ts                     # User management hooks
│   ├── useBilling.ts                   # Billing hooks
│   └── useAnalytics.ts                 # Analytics hooks
├── 📁 institute-admin/
│   ├── useCourses.ts                   # Course management hooks
│   ├── useUsers.ts                     # User management hooks
│   ├── useBranches.ts                  # Branch management hooks
│   └── useAnalytics.ts                 # Analytics hooks
└── 📁 student/
    ├── useCourses.ts                   # Course access hooks
    ├── useProgress.ts                  # Progress tracking hooks
    ├── useExams.ts                     # Exam hooks
    └── useProfile.ts                   # Profile hooks
```

### **Step 7: Complete Folder Structure Creation**
```bash
# Create the complete organized folder structure
cat > create-structure.sh << 'EOF'
#!/bin/bash

echo "Creating complete LMS folder structure..."

# Backend structure (Payload CMS) - Now inside apps/api/
mkdir -p apps/api/src/{collections,globals,hooks,utilities,access,endpoints}
mkdir -p apps/api/src/collections/{super-admin,institute-admin,student,shared}
mkdir -p apps/api/src/globals/{settings,themes}
mkdir -p apps/api/src/endpoints/{super-admin,institute-admin,student}

# Documentation structure
mkdir -p docs/{phases,user-guides,api}

# Shared components structure
mkdir -p components/shared/{ui,notifications,layouts}

# Create placeholder files to maintain folder structure
touch components/super-admin/.gitkeep
touch components/institute-admin/.gitkeep
touch components/student/.gitkeep
touch components/shared/.gitkeep
touch stores/super-admin/.gitkeep
touch stores/institute-admin/.gitkeep
touch stores/student/.gitkeep
touch lib/super-admin/.gitkeep
touch lib/institute-admin/.gitkeep
touch lib/student/.gitkeep
touch types/super-admin/.gitkeep
touch types/institute-admin/.gitkeep
touch types/student/.gitkeep
touch hooks/super-admin/.gitkeep
touch hooks/institute-admin/.gitkeep
touch hooks/student/.gitkeep

echo "✅ Complete integrated folder structure with shared components created!"
EOF

chmod +x create-structure.sh
./create-structure.sh
```

## ⚙️ Configuration Files

### **Monorepo Configuration**

#### **pnpm-workspace.yaml**
```yaml
packages:
  - 'apps/*'
  - 'packages/*'
```

#### **turbo.json**
```json
{
  "$schema": "https://turbo.build/schema.json",
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "!.next/cache/**", "dist/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "lint": {},
    "type-check": {}
  }
}
```

#### **Root package.json**
```json
{
  "name": "groups-exam-lms",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "turbo run dev",
    "build": "turbo run build",
    "start": "turbo run start",
    "lint": "turbo run lint",
    "type-check": "turbo run type-check",
    "clean": "turbo run clean"
  },
  "devDependencies": {
    "turbo": "latest",
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0"
  },
  "packageManager": "pnpm@8.0.0"
}
```

### **Environment Configuration**

#### **API Backend (.env) - apps/api/.env**
```env
# Database
DATABASE_URI=postgresql://username:password@localhost:5432/groups_exam_lms
PAYLOAD_SECRET=your-secret-key-here
PAYLOAD_CONFIG_PATH=src/payload.config.ts
PORT=3001

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Storage (AWS S3)
AWS_S3_BUCKET=groups-exam-lms-storage
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# Payment Gateways
STRIPE_SECRET_KEY=sk_test_...
RAZORPAY_KEY_ID=rzp_test_...
RAZORPAY_KEY_SECRET=...

# Security
JWT_SECRET=your-jwt-secret
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
```

#### **Frontend Apps (.env.local)**
```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Authentication
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000

# Payment (Public Keys)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_...
```

## 🔨 Development Setup

### **Shared Packages Setup**

#### **packages/types/package.json**
```json
{
  "name": "@groups-exam/types",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch"
  },
  "dependencies": {
    "typescript": "^5.0.0"
  }
}
```

#### **packages/ui/package.json**
```json
{
  "name": "@groups-exam/ui",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch"
  },
  "dependencies": {
    "react": "^18.0.0",
    "@types/react": "^18.0.0",
    "typescript": "^5.0.0",
    "tailwindcss": "^3.0.0"
  }
}
```

### **Development Scripts**

#### **dev.sh** (Development Startup Script)
```bash
#!/bin/bash

echo "🚀 Starting Groups Exam LMS Development Environment..."

# Start all services in parallel
echo "Starting Payload CMS API..."
cd apps/api && npm run dev &

echo "Starting Frontend Application..."
cd apps/frontend && npm run dev -- --port 3000 &

echo "✅ All services started!"
echo "🔧 Payload CMS: http://localhost:3001/admin"
echo "📊 Super Admin: http://localhost:3000/super-admin"
echo "🏫 Institute Admin: http://localhost:3000/admin"
echo "🎓 Student Portal: http://localhost:3000"

wait
```

## 🔧 Phase 1 Backend Implementation

### **Payload CMS Basic Setup**
**File**: `apps/api/payload.config.ts`

```typescript
import { buildConfig } from 'payload/config'
import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { webpackBundler } from '@payloadcms/bundler-webpack'
import { slateEditor } from '@payloadcms/richtext-slate'
import path from 'path'

export default buildConfig({
  admin: {
    user: 'users', // Will be created in Phase 2
    bundler: webpackBundler(),
    webpack: (config) => ({
      ...config,
      resolve: {
        ...config.resolve,
        alias: {
          ...config.resolve.alias,
          '@': path.resolve(__dirname, '../..'),
        },
      },
    }),
  },
  editor: slateEditor({}),
  collections: [
    // Collections will be added in subsequent phases
    // Phase 2: Users, Institutes
    // Phase 3: Courses, Themes
    // Phase 4: Sessions, Settings, DomainRequests
  ],
  typescript: {
    outputFile: path.resolve(__dirname, 'payload-types.ts'),
  },
  graphQL: {
    schemaOutputFile: path.resolve(__dirname, 'generated-schema.graphql'),
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || 'mongodb://localhost:27017/groups-exam-lms',
  }),
  cors: [
    // Production cross-domain setup
    'https://admin.groups-exam.com',      // Super Admin Panel
    'https://groups-exam.com',            // Main Platform
    // Institute custom domains (dynamically loaded from database)
    'https://abc-institute.com',          // Example Institute Domain
    'https://xyz-academy.com',            // Example Institute Domain
    // Development domains
    'http://localhost:3000',              // Frontend App
    'http://localhost:3001',              // API
  ],
  csrf: [
    'https://admin.groups-exam.com',
    'https://institute.groups-exam.com',
    'https://student.groups-exam.com',
    'https://groups-exam.com',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
  ],
})
```

### **Database Connection Setup**
**File**: `apps/api/src/database/connection.ts`

```typescript
import mongoose from 'mongoose'

export const connectDatabase = async () => {
  try {
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    }

    await mongoose.connect(process.env.DATABASE_URI!, options)

    console.log('✅ Database connected successfully')
    console.log(`📊 Database: ${mongoose.connection.name}`)
    console.log(`🌐 Host: ${mongoose.connection.host}:${mongoose.connection.port}`)

  } catch (error) {
    console.error('❌ Database connection failed:', error)
    process.exit(1)
  }
}

// Handle connection events
mongoose.connection.on('connected', () => {
  console.log('📡 Mongoose connected to MongoDB')
})

mongoose.connection.on('error', (err) => {
  console.error('❌ Mongoose connection error:', err)
})

mongoose.connection.on('disconnected', () => {
  console.log('📡 Mongoose disconnected from MongoDB')
})

// Graceful shutdown
process.on('SIGINT', async () => {
  await mongoose.connection.close()
  console.log('📡 Mongoose connection closed through app termination')
  process.exit(0)
})
```

### **Environment Configuration**
**File**: `apps/api/.env.example`

```bash
# Database Configuration
DATABASE_URI=mongodb://localhost:27017/groups-exam-lms

# Payload CMS Configuration
PAYLOAD_SECRET=your-super-secret-payload-key-here
PAYLOAD_CONFIG_PATH=src/payload.config.ts
PORT=3001

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here

# Frontend URLs for CORS (Cross-Domain Setup)
# Production domains
SUPER_ADMIN_URL=https://admin.groups-exam.com
MAIN_PLATFORM_URL=https://groups-exam.com
# Institute custom domains (examples - loaded dynamically from database)
INSTITUTE_DOMAIN_1=https://abc-institute.com
INSTITUTE_DOMAIN_2=https://xyz-academy.com

# Development domains
DEV_FRONTEND_URL=http://localhost:3000
DEV_API_URL=http://localhost:3001

# Email Configuration (for future phases)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Storage Configuration (for future phases)
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# Security Configuration
BCRYPT_SALT_ROUNDS=12
SESSION_TIMEOUT=7200
MAX_LOGIN_ATTEMPTS=5

# Development Configuration
NODE_ENV=development
LOG_LEVEL=debug
```

### **Basic Server Setup**
**File**: `apps/api/src/server.ts`

```typescript
import express from 'express'
import payload from 'payload'
import { connectDatabase } from './database/connection'

require('dotenv').config()

const app = express()
const PORT = process.env.PORT || 3001

// Initialize Payload CMS
const start = async () => {
  try {
    // Connect to database
    await connectDatabase()

    // Initialize Payload
    await payload.init({
      secret: process.env.PAYLOAD_SECRET!,
      express: app,
      onInit: () => {
        payload.logger.info(`✅ Payload Admin URL: http://localhost:${PORT}${payload.getAdminURL()}`)
      },
    })

    // Add custom middleware here (will be expanded in future phases)

    // Health check endpoint
    app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
      })
    })

    // Start server
    app.listen(PORT, () => {
      console.log(`🚀 Server running on http://localhost:${PORT}`)
      console.log(`🔧 Admin panel: http://localhost:${PORT}/admin`)
      console.log(`📊 Health check: http://localhost:${PORT}/health`)
    })

  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

start()
```

### **Basic Middleware Setup**
**File**: `apps/api/src/middleware/index.ts`

```typescript
import { Request, Response, NextFunction } from 'express'
import cors from 'cors'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'

// CORS configuration for cross-domain authentication
export const corsMiddleware = cors({
  origin: [
    // Production cross-domain setup
    'https://admin.groups-exam.com',      // Super Admin Panel
    'https://groups-exam.com',            // Main Platform
    // Institute custom domains (dynamically loaded from database)
    'https://abc-institute.com',          // Example Institute Domain
    'https://xyz-academy.com',            // Example Institute Domain
    // Development domains
    'http://localhost:3000',              // Super Admin
    'http://localhost:3001',              // API
    'http://localhost:3002',              // Institute Testing
  ],
  credentials: true,  // Important for cross-domain authentication
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  exposedHeaders: ['Set-Cookie']  // For cross-domain cookie handling
})

// Security headers
export const securityMiddleware = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
})

// Rate limiting
export const rateLimitMiddleware = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
})

// Request logging middleware
export const loggingMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now()

  res.on('finish', () => {
    const duration = Date.now() - start
    console.log(`${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`)
  })

  next()
}

// Error handling middleware
export const errorMiddleware = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('❌ Error:', error)

  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
  })
}
```

### **Package.json for API**
**File**: `apps/api/package.json`

```json
{
  "name": "@groups-exam/api",
  "version": "1.0.0",
  "description": "Groups Exam LMS API with Payload CMS",
  "main": "dist/server.js",
  "scripts": {
    "dev": "cross-env NODE_ENV=development nodemon src/server.ts",
    "build": "tsc && copyfiles -u 1 \"src/**/*.{html,css,scss,ttf,woff,woff2,eot,svg,jpg,png}\" dist/",
    "start": "cross-env NODE_ENV=production node dist/server.js",
    "generate:types": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:types",
    "lint": "eslint src --ext .ts,.tsx",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "payload": "^2.0.0",
    "@payloadcms/db-mongodb": "^1.0.0",
    "@payloadcms/bundler-webpack": "^1.0.0",
    "@payloadcms/richtext-slate": "^1.0.0",
    "express": "^4.18.0",
    "mongoose": "^7.0.0",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "express-rate-limit": "^6.7.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "dotenv": "^16.0.0"
  },
  "devDependencies": {
    "@types/express": "^4.17.0",
    "@types/cors": "^2.8.0",
    "@types/bcryptjs": "^2.4.0",
    "@types/jsonwebtoken": "^9.0.0",
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0",
    "nodemon": "^3.0.0",
    "ts-node": "^10.9.0",
    "cross-env": "^7.0.0",
    "copyfiles": "^2.4.0",
    "eslint": "^8.0.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0"
  }
}
```

## 🎯 Phase 1 Success Criteria

### **Functional Requirements**
- [ ] ✅ All three applications start successfully
- [ ] ✅ Payload CMS admin panel is accessible
- [ ] ✅ Database connection is established
- [ ] ✅ Basic routing works in all apps
- [ ] ✅ Shared components structure is created
- [ ] ✅ Environment variables are loaded correctly

### **Technical Requirements**
- [ ] ✅ TypeScript compilation works without errors
- [ ] ✅ Payload CMS builds and runs successfully
- [ ] ✅ ESLint and Prettier are configured
- [ ] ✅ All dependencies are installed correctly
- [ ] ✅ Development servers run on correct ports
- [ ] ✅ Hot reload works in all applications

### **Backend Requirements**
- [ ] ✅ Payload CMS is configured and running
- [ ] ✅ MongoDB connection is established
- [ ] ✅ Admin panel is accessible at /admin
- [ ] ✅ TypeScript types are generated
- [ ] ✅ Basic middleware is configured
- [ ] ✅ Environment variables are loaded
- [ ] ✅ Health check endpoint responds
- [ ] ✅ CORS is properly configured

### **Infrastructure Requirements**
- [ ] ✅ Monorepo structure is properly set up
- [ ] ✅ User role-based folder organization
- [ ] ✅ Shared components structure created
- [ ] ✅ Development scripts are functional
- [ ] ✅ Package management works correctly

## 🔄 **Complete CRUD API Integration**

### **Institutes CRUD Endpoints**
**File**: `apps/api/src/endpoints/institutes/crud.ts`

```typescript
import { Endpoint } from 'payload/config'

const institutesCrudEndpoints: Endpoint[] = [
  // Get Institutes (with pagination and filters)
  {
    path: '/institutes',
    method: 'get',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const {
          page = 1,
          limit = 20,
          search = '',
          status = '',
          subscriptionPlan = '',
          isActive = ''
        } = req.query

        // Build where clause
        const where: any = {}

        // Search filter
        if (search) {
          where.or = [
            { name: { contains: search } },
            { email: { contains: search } },
            { phone: { contains: search } },
            { address: { contains: search } }
          ]
        }

        // Status filter
        if (status) {
          where.status = { equals: status }
        }

        // Subscription plan filter
        if (subscriptionPlan) {
          where.subscriptionPlan = { equals: subscriptionPlan }
        }

        // Active filter
        if (isActive !== '') {
          where.isActive = { equals: isActive === 'true' }
        }

        // Scope restrictions for institute admins
        if (currentUser.userType === 'institute_admin') {
          where.id = { equals: currentUser.institute }
        }

        const institutes = await req.payload.find({
          collection: 'institutes',
          where,
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          sort: '-createdAt',
          populate: ['country', 'state', 'district']
        })

        res.json({
          success: true,
          ...institutes
        })

      } catch (error) {
        console.error('Get institutes error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch institutes'
        })
      }
    }
  },

  // Create Institute
  {
    path: '/institutes',
    method: 'post',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser || currentUser.userType !== 'super_admin') {
          return res.status(403).json({
            error: 'Only super admins can create institutes'
          })
        }

        const {
          name,
          email,
          phone,
          address,
          country,
          state,
          district,
          pincode,
          website,
          subscriptionPlan,
          maxBranches,
          maxStudents,
          features,
          isActive
        } = req.body

        // Validate required fields
        if (!name || !email || !phone || !address) {
          return res.status(400).json({
            error: 'Name, email, phone, and address are required'
          })
        }

        // Check if institute already exists
        const existingInstitute = await req.payload.find({
          collection: 'institutes',
          where: {
            or: [
              { email: { equals: email } },
              { name: { equals: name } }
            ]
          },
          limit: 1
        })

        if (existingInstitute.totalDocs > 0) {
          return res.status(400).json({
            error: 'Institute with this name or email already exists'
          })
        }

        const instituteData = {
          name,
          email,
          phone,
          address,
          country,
          state,
          district,
          pincode,
          website,
          subscriptionPlan: subscriptionPlan || 'basic',
          maxBranches: maxBranches || 1,
          maxStudents: maxStudents || 100,
          features: features || [],
          status: 'pending',
          isActive: isActive !== undefined ? isActive : true,
          createdBy: currentUser.id
        }

        const institute = await req.payload.create({
          collection: 'institutes',
          data: instituteData
        })

        res.json({
          success: true,
          institute,
          message: 'Institute created successfully'
        })

      } catch (error) {
        console.error('Create institute error:', error)
        res.status(500).json({
          error: error.message || 'Failed to create institute'
        })
      }
    }
  }
]

export default institutesCrudEndpoints
```

### **Branches CRUD Endpoints**
**File**: `apps/api/src/endpoints/branches/crud.ts`

```typescript
import { Endpoint } from 'payload/config'

const branchesCrudEndpoints: Endpoint[] = [
  // Get Branches (with pagination and filters)
  {
    path: '/branches',
    method: 'get',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const {
          page = 1,
          limit = 20,
          search = '',
          institute = '',
          isActive = ''
        } = req.query

        // Build where clause
        const where: any = {}

        // Search filter
        if (search) {
          where.or = [
            { name: { contains: search } },
            { email: { contains: search } },
            { phone: { contains: search } },
            { address: { contains: search } }
          ]
        }

        // Institute filter
        if (institute) {
          where.institute = { equals: institute }
        }

        // Active filter
        if (isActive !== '') {
          where.isActive = { equals: isActive === 'true' }
        }

        // Scope restrictions
        if (currentUser.userType === 'institute_admin') {
          where.institute = { equals: currentUser.institute }
        } else if (currentUser.userType === 'branch_admin') {
          where.and = [
            { institute: { equals: currentUser.institute } },
            { id: { equals: currentUser.branch } }
          ]
        }

        const branches = await req.payload.find({
          collection: 'branches',
          where,
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          sort: '-createdAt',
          populate: ['institute', 'country', 'state', 'district']
        })

        res.json({
          success: true,
          ...branches
        })

      } catch (error) {
        console.error('Get branches error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch branches'
        })
      }
    }
  },

  // Create Branch
  {
    path: '/branches',
    method: 'post',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only super admins and institute admins can create branches'
          })
        }

        const {
          name,
          email,
          phone,
          address,
          institute,
          country,
          state,
          district,
          pincode,
          maxStudents,
          isActive
        } = req.body

        // Validate required fields
        if (!name || !email || !phone || !address || !institute) {
          return res.status(400).json({
            error: 'Name, email, phone, address, and institute are required'
          })
        }

        // Set institute for institute admins
        let branchInstitute = institute
        if (currentUser.userType === 'institute_admin') {
          branchInstitute = currentUser.institute
        }

        // Check if branch already exists
        const existingBranch = await req.payload.find({
          collection: 'branches',
          where: {
            and: [
              { institute: { equals: branchInstitute } },
              {
                or: [
                  { email: { equals: email } },
                  { name: { equals: name } }
                ]
              }
            ]
          },
          limit: 1
        })

        if (existingBranch.totalDocs > 0) {
          return res.status(400).json({
            error: 'Branch with this name or email already exists in this institute'
          })
        }

        // Check institute branch limit
        const instituteData = await req.payload.findByID({
          collection: 'institutes',
          id: branchInstitute
        })

        if (instituteData) {
          const existingBranches = await req.payload.find({
            collection: 'branches',
            where: { institute: { equals: branchInstitute } }
          })

          if (existingBranches.totalDocs >= instituteData.maxBranches) {
            return res.status(400).json({
              error: `Institute has reached maximum branch limit of ${instituteData.maxBranches}`
            })
          }
        }

        const branchData = {
          name,
          email,
          phone,
          address,
          institute: branchInstitute,
          country,
          state,
          district,
          pincode,
          maxStudents: maxStudents || 50,
          isActive: isActive !== undefined ? isActive : true,
          createdBy: currentUser.id
        }

        const branch = await req.payload.create({
          collection: 'branches',
          data: branchData
        })

        res.json({
          success: true,
          branch,
          message: 'Branch created successfully'
        })

      } catch (error) {
        console.error('Create branch error:', error)
        res.status(500).json({
          error: error.message || 'Failed to create branch'
        })
      }
    }
  },

  // Update Branch
  {
    path: '/branches/:id',
    method: 'patch',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        // Get existing branch
        const existingBranch = await req.payload.findByID({
          collection: 'branches',
          id
        })

        if (!existingBranch) {
          return res.status(404).json({
            error: 'Branch not found'
          })
        }

        // Check permissions
        const canEdit = currentUser.userType === 'super_admin' ||
          (currentUser.userType === 'institute_admin' && existingBranch.institute === currentUser.institute) ||
          (currentUser.userType === 'branch_admin' && currentUser.branch === id)

        if (!canEdit) {
          return res.status(403).json({
            error: 'Permission denied'
          })
        }

        const updateData = { ...req.body }

        // Remove sensitive fields for branch admin updates
        if (currentUser.userType === 'branch_admin') {
          delete updateData.institute
          delete updateData.maxStudents
        }

        const branch = await req.payload.update({
          collection: 'branches',
          id,
          data: updateData
        })

        res.json({
          success: true,
          branch,
          message: 'Branch updated successfully'
        })

      } catch (error) {
        console.error('Update branch error:', error)
        res.status(500).json({
          error: error.message || 'Failed to update branch'
        })
      }
    }
  },

  // Delete Branch
  {
    path: '/branches/:id',
    method: 'delete',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only super admins and institute admins can delete branches'
          })
        }

        // Get existing branch
        const existingBranch = await req.payload.findByID({
          collection: 'branches',
          id
        })

        if (!existingBranch) {
          return res.status(404).json({
            error: 'Branch not found'
          })
        }

        // Check permissions for institute admin
        if (currentUser.userType === 'institute_admin' && existingBranch.institute !== currentUser.institute) {
          return res.status(403).json({
            error: 'Can only delete branches from your institute'
          })
        }

        // Check if branch has users
        const users = await req.payload.find({
          collection: 'users',
          where: { branch: { equals: id } },
          limit: 1
        })

        if (users.totalDocs > 0) {
          return res.status(400).json({
            error: 'Cannot delete branch that has users. Remove users first.'
          })
        }

        await req.payload.delete({
          collection: 'branches',
          id
        })

        res.json({
          success: true,
          message: 'Branch deleted successfully'
        })

      } catch (error) {
        console.error('Delete branch error:', error)
        res.status(500).json({
          error: error.message || 'Failed to delete branch'
        })
      }
    }
  }
]

export default branchesCrudEndpoints
```

## 🗄️ **Zustand State Management**

### **Institutes Store**
**File**: `apps/super-admin/src/stores/useInstitutesStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface Institute {
  id: string
  name: string
  email: string
  phone: string
  address: string
  country?: any
  state?: any
  district?: any
  pincode?: string
  website?: string
  subscriptionPlan: 'basic' | 'standard' | 'premium' | 'enterprise'
  maxBranches: number
  maxStudents: number
  features: string[]
  status: 'pending' | 'active' | 'suspended' | 'cancelled'
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface InstitutesState {
  // Data
  institutes: Institute[]

  // UI State
  isLoading: boolean
  viewMode: 'card' | 'list'

  // Pagination
  pagination: PaginationInfo

  // Filters
  filters: {
    search: string
    status: string
    subscriptionPlan: string
    isActive: string
  }

  // Actions
  fetchInstitutes: (page?: number, filters?: any) => Promise<void>
  createInstitute: (data: any) => Promise<void>
  updateInstitute: (id: string, data: any) => Promise<void>
  deleteInstitute: (id: string) => Promise<void>

  setViewMode: (mode: 'card' | 'list') => void
  setFilters: (filters: any) => void
  clearFilters: () => void
}

export const useInstitutesStore = create<InstitutesState>()(
  devtools(
    (set, get) => ({
      // Initial state
      institutes: [],

      isLoading: false,
      viewMode: 'card',

      pagination: {
        page: 1,
        limit: 20,
        totalPages: 1,
        totalDocs: 0,
        hasNextPage: false,
        hasPrevPage: false
      },

      filters: {
        search: '',
        status: '',
        subscriptionPlan: '',
        isActive: 'true'
      },

      // Fetch institutes
      fetchInstitutes: async (page = 1, filters = {}) => {
        set({ isLoading: true })
        try {
          const currentFilters = { ...get().filters, ...filters }
          const params = new URLSearchParams({
            page: page.toString(),
            limit: get().pagination.limit.toString(),
            ...Object.fromEntries(
              Object.entries(currentFilters).filter(([_, value]) => value !== '')
            )
          })

          const response = await fetch(`/api/institutes?${params}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch institutes')
          }

          const data = await response.json()

          set({
            institutes: data.docs,
            pagination: {
              page: data.page,
              limit: data.limit,
              totalPages: data.totalPages,
              totalDocs: data.totalDocs,
              hasNextPage: data.hasNextPage,
              hasPrevPage: data.hasPrevPage
            }
          })

        } catch (error) {
          console.error('Fetch institutes error:', error)
          toast.error('Failed to fetch institutes')
        } finally {
          set({ isLoading: false })
        }
      },

      // Create institute
      createInstitute: async (data: any) => {
        try {
          const response = await fetch('/api/institutes', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to create institute')
          }

          const result = await response.json()

          // Refresh institutes list
          await get().fetchInstitutes()

          toast.success('Institute created successfully')
          return result

        } catch (error) {
          console.error('Create institute error:', error)
          toast.error(error.message || 'Failed to create institute')
          throw error
        }
      },

      // Update institute
      updateInstitute: async (id: string, data: any) => {
        try {
          const response = await fetch(`/api/institutes/${id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to update institute')
          }

          const result = await response.json()

          // Update institute in state
          set(state => ({
            institutes: state.institutes.map(institute =>
              institute.id === id ? { ...institute, ...result.institute } : institute
            )
          }))

          toast.success('Institute updated successfully')
          return result

        } catch (error) {
          console.error('Update institute error:', error)
          toast.error(error.message || 'Failed to update institute')
          throw error
        }
      },

      // Delete institute
      deleteInstitute: async (id: string) => {
        try {
          const response = await fetch(`/api/institutes/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to delete institute')
          }

          // Remove institute from state
          set(state => ({
            institutes: state.institutes.filter(institute => institute.id !== id)
          }))

          toast.success('Institute deleted successfully')

        } catch (error) {
          console.error('Delete institute error:', error)
          toast.error(error.message || 'Failed to delete institute')
          throw error
        }
      },

      // Set view mode
      setViewMode: (mode: 'card' | 'list') => {
        set({ viewMode: mode })
      },

      // Set filters
      setFilters: (filters: any) => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }))
      },

      // Clear filters
      clearFilters: () => {
        set({
          filters: {
            search: '',
            status: '',
            subscriptionPlan: '',
            isActive: 'true'
          }
        })
      }
    }),
    {
      name: 'institutes-store'
    }
  )
)
```

### **Branches Store**
**File**: `apps/super-admin/src/stores/useBranchesStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface Branch {
  id: string
  name: string
  email: string
  phone: string
  address: string
  institute: any
  country?: any
  state?: any
  district?: any
  pincode?: string
  maxStudents: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface BranchesState {
  // Data
  branches: Branch[]

  // UI State
  isLoading: boolean
  viewMode: 'card' | 'list'

  // Pagination
  pagination: PaginationInfo

  // Filters
  filters: {
    search: string
    institute: string
    isActive: string
  }

  // Actions
  fetchBranches: (page?: number, filters?: any) => Promise<void>
  createBranch: (data: any) => Promise<void>
  updateBranch: (id: string, data: any) => Promise<void>
  deleteBranch: (id: string) => Promise<void>

  setViewMode: (mode: 'card' | 'list') => void
  setFilters: (filters: any) => void
  clearFilters: () => void
}

export const useBranchesStore = create<BranchesState>()(
  devtools(
    (set, get) => ({
      // Initial state
      branches: [],

      isLoading: false,
      viewMode: 'card',

      pagination: {
        page: 1,
        limit: 20,
        totalPages: 1,
        totalDocs: 0,
        hasNextPage: false,
        hasPrevPage: false
      },

      filters: {
        search: '',
        institute: '',
        isActive: 'true'
      },

      // Fetch branches
      fetchBranches: async (page = 1, filters = {}) => {
        set({ isLoading: true })
        try {
          const currentFilters = { ...get().filters, ...filters }
          const params = new URLSearchParams({
            page: page.toString(),
            limit: get().pagination.limit.toString(),
            ...Object.fromEntries(
              Object.entries(currentFilters).filter(([_, value]) => value !== '')
            )
          })

          const response = await fetch(`/api/branches?${params}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch branches')
          }

          const data = await response.json()

          set({
            branches: data.docs,
            pagination: {
              page: data.page,
              limit: data.limit,
              totalPages: data.totalPages,
              totalDocs: data.totalDocs,
              hasNextPage: data.hasNextPage,
              hasPrevPage: data.hasPrevPage
            }
          })

        } catch (error) {
          console.error('Fetch branches error:', error)
          toast.error('Failed to fetch branches')
        } finally {
          set({ isLoading: false })
        }
      },

      // Create branch
      createBranch: async (data: any) => {
        try {
          const response = await fetch('/api/branches', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to create branch')
          }

          const result = await response.json()

          // Refresh branches list
          await get().fetchBranches()

          toast.success('Branch created successfully')
          return result

        } catch (error) {
          console.error('Create branch error:', error)
          toast.error(error.message || 'Failed to create branch')
          throw error
        }
      },

      // Update branch
      updateBranch: async (id: string, data: any) => {
        try {
          const response = await fetch(`/api/branches/${id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to update branch')
          }

          const result = await response.json()

          // Update branch in state
          set(state => ({
            branches: state.branches.map(branch =>
              branch.id === id ? { ...branch, ...result.branch } : branch
            )
          }))

          toast.success('Branch updated successfully')
          return result

        } catch (error) {
          console.error('Update branch error:', error)
          toast.error(error.message || 'Failed to update branch')
          throw error
        }
      },

      // Delete branch
      deleteBranch: async (id: string) => {
        try {
          const response = await fetch(`/api/branches/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to delete branch')
          }

          // Remove branch from state
          set(state => ({
            branches: state.branches.filter(branch => branch.id !== id)
          }))

          toast.success('Branch deleted successfully')

        } catch (error) {
          console.error('Delete branch error:', error)
          toast.error(error.message || 'Failed to delete branch')
          throw error
        }
      },

      // Set view mode
      setViewMode: (mode: 'card' | 'list') => {
        set({ viewMode: mode })
      },

      // Set filters
      setFilters: (filters: any) => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }))
      },

      // Clear filters
      clearFilters: () => {
        set({
          filters: {
            search: '',
            institute: '',
            isActive: 'true'
          }
        })
      }
    }),
    {
      name: 'branches-store'
    }
  )
)
```

## 📝 **Formik + Yup Forms**

### **Institute Form Component**
**File**: `apps/super-admin/src/components/institutes/InstituteForm.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { useInstitutesStore } from '@/stores/useInstitutesStore'
import { useLocationStore } from '@/stores/useLocationStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Loader2, Building, Save } from 'lucide-react'
import { toast } from 'sonner'

// Validation Schema
const instituteValidationSchema = Yup.object({
  name: Yup.string()
    .required('Institute name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  email: Yup.string()
    .required('Email is required')
    .email('Please enter a valid email address'),
  phone: Yup.string()
    .required('Phone number is required')
    .matches(/^[+]?[\d\s\-\(\)]+$/, 'Please enter a valid phone number'),
  address: Yup.string()
    .required('Address is required')
    .min(10, 'Address must be at least 10 characters')
    .max(500, 'Address must be less than 500 characters'),
  country: Yup.string()
    .required('Country is required'),
  state: Yup.string()
    .required('State is required'),
  district: Yup.string()
    .required('District is required'),
  pincode: Yup.string()
    .matches(/^\d{6}$/, 'Pincode must be 6 digits'),
  website: Yup.string()
    .url('Please enter a valid website URL'),
  subscriptionPlan: Yup.string()
    .required('Subscription plan is required')
    .oneOf(['basic', 'standard', 'premium', 'enterprise'], 'Invalid subscription plan'),
  maxBranches: Yup.number()
    .required('Maximum branches is required')
    .min(1, 'Must allow at least 1 branch')
    .max(100, 'Cannot exceed 100 branches'),
  maxStudents: Yup.number()
    .required('Maximum students is required')
    .min(10, 'Must allow at least 10 students')
    .max(10000, 'Cannot exceed 10,000 students'),
  features: Yup.array()
    .of(Yup.string()),
  isActive: Yup.boolean()
})

interface InstituteFormProps {
  initialData?: any
  onSubmit: (values: any) => Promise<void>
  onCancel: () => void
}

export function InstituteForm({ initialData, onSubmit, onCancel }: InstituteFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { countries, states, districts, fetchCountries, fetchStates, fetchDistricts } = useLocationStore()

  useEffect(() => {
    fetchCountries()
  }, [])

  const initialValues = {
    name: initialData?.name || '',
    email: initialData?.email || '',
    phone: initialData?.phone || '',
    address: initialData?.address || '',
    country: initialData?.country?.id || '',
    state: initialData?.state?.id || '',
    district: initialData?.district?.id || '',
    pincode: initialData?.pincode || '',
    website: initialData?.website || '',
    subscriptionPlan: initialData?.subscriptionPlan || 'basic',
    maxBranches: initialData?.maxBranches || 1,
    maxStudents: initialData?.maxStudents || 100,
    features: initialData?.features || [],
    isActive: initialData?.isActive ?? true
  }

  const handleSubmit = async (values: any) => {
    setIsSubmitting(true)
    try {
      await onSubmit(values)
      toast.success(initialData ? 'Institute updated successfully' : 'Institute created successfully')
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  const subscriptionPlans = [
    { value: 'basic', label: 'Basic Plan', description: 'Up to 1 branch, 100 students' },
    { value: 'standard', label: 'Standard Plan', description: 'Up to 5 branches, 500 students' },
    { value: 'premium', label: 'Premium Plan', description: 'Up to 20 branches, 2000 students' },
    { value: 'enterprise', label: 'Enterprise Plan', description: 'Unlimited branches and students' }
  ]

  const availableFeatures = [
    'online_classes',
    'exam_management',
    'fee_management',
    'library_management',
    'transport_management',
    'hostel_management',
    'sms_notifications',
    'email_notifications',
    'mobile_app',
    'analytics_reports'
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Building className="h-5 w-5 mr-2" />
          {initialData ? 'Edit Institute' : 'Create New Institute'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Formik
          initialValues={initialValues}
          validationSchema={instituteValidationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched }) => (
            <Form className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Institute Name *</Label>
                      <Field
                        as={Input}
                        id="name"
                        name="name"
                        placeholder="Enter institute name"
                        className={errors.name && touched.name ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="name" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address *</Label>
                      <Field
                        as={Input}
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Enter email address"
                        className={errors.email && touched.email ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="email" component="div" className="text-sm text-red-500" />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number *</Label>
                      <Field
                        as={Input}
                        id="phone"
                        name="phone"
                        placeholder="Enter phone number"
                        className={errors.phone && touched.phone ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="phone" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Field
                        as={Input}
                        id="website"
                        name="website"
                        placeholder="https://example.com"
                        className={errors.website && touched.website ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="website" component="div" className="text-sm text-red-500" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address">Address *</Label>
                    <Field
                      as={Textarea}
                      id="address"
                      name="address"
                      placeholder="Enter complete address"
                      rows={3}
                      className={errors.address && touched.address ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="address" component="div" className="text-sm text-red-500" />
                  </div>
                </CardContent>
              </Card>

              {/* Location Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Location Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="country">Country *</Label>
                      <Select
                        value={values.country}
                        onValueChange={(value) => {
                          setFieldValue('country', value)
                          setFieldValue('state', '')
                          setFieldValue('district', '')
                          fetchStates(value)
                        }}
                      >
                        <SelectTrigger className={errors.country && touched.country ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                        <SelectContent>
                          {countries.map((country) => (
                            <SelectItem key={country.id} value={country.id}>
                              {country.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <ErrorMessage name="country" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="state">State *</Label>
                      <Select
                        value={values.state}
                        onValueChange={(value) => {
                          setFieldValue('state', value)
                          setFieldValue('district', '')
                          fetchDistricts(value)
                        }}
                        disabled={!values.country}
                      >
                        <SelectTrigger className={errors.state && touched.state ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select state" />
                        </SelectTrigger>
                        <SelectContent>
                          {states.map((state) => (
                            <SelectItem key={state.id} value={state.id}>
                              {state.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <ErrorMessage name="state" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="district">District *</Label>
                      <Select
                        value={values.district}
                        onValueChange={(value) => setFieldValue('district', value)}
                        disabled={!values.state}
                      >
                        <SelectTrigger className={errors.district && touched.district ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select district" />
                        </SelectTrigger>
                        <SelectContent>
                          {districts.map((district) => (
                            <SelectItem key={district.id} value={district.id}>
                              {district.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <ErrorMessage name="district" component="div" className="text-sm text-red-500" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="pincode">Pincode</Label>
                    <Field
                      as={Input}
                      id="pincode"
                      name="pincode"
                      placeholder="Enter 6-digit pincode"
                      maxLength={6}
                      className={errors.pincode && touched.pincode ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="pincode" component="div" className="text-sm text-red-500" />
                  </div>
                </CardContent>
              </Card>

              {/* Subscription & Limits */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Subscription & Limits</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="subscriptionPlan">Subscription Plan *</Label>
                    <Select
                      value={values.subscriptionPlan}
                      onValueChange={(value) => setFieldValue('subscriptionPlan', value)}
                    >
                      <SelectTrigger className={errors.subscriptionPlan && touched.subscriptionPlan ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select subscription plan" />
                      </SelectTrigger>
                      <SelectContent>
                        {subscriptionPlans.map((plan) => (
                          <SelectItem key={plan.value} value={plan.value}>
                            <div>
                              <div className="font-medium">{plan.label}</div>
                              <div className="text-sm text-muted-foreground">{plan.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <ErrorMessage name="subscriptionPlan" component="div" className="text-sm text-red-500" />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="maxBranches">Maximum Branches *</Label>
                      <Field
                        as={Input}
                        id="maxBranches"
                        name="maxBranches"
                        type="number"
                        min="1"
                        max="100"
                        className={errors.maxBranches && touched.maxBranches ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="maxBranches" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="maxStudents">Maximum Students *</Label>
                      <Field
                        as={Input}
                        id="maxStudents"
                        name="maxStudents"
                        type="number"
                        min="10"
                        max="10000"
                        className={errors.maxStudents && touched.maxStudents ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="maxStudents" component="div" className="text-sm text-red-500" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Status */}
              <div className="flex items-center space-x-2">
                <Field
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  className="rounded"
                />
                <Label htmlFor="isActive">Active Institute</Label>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  <Save className="h-4 w-4 mr-2" />
                  {initialData ? 'Update Institute' : 'Create Institute'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
```

### **Branch Form Component**
**File**: `apps/super-admin/src/components/branches/BranchForm.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { useBranchesStore } from '@/stores/useBranchesStore'
import { useInstitutesStore } from '@/stores/useInstitutesStore'
import { useLocationStore } from '@/stores/useLocationStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Loader2, MapPin, Save } from 'lucide-react'
import { toast } from 'sonner'

// Validation Schema
const branchValidationSchema = Yup.object({
  name: Yup.string()
    .required('Branch name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  email: Yup.string()
    .required('Email is required')
    .email('Please enter a valid email address'),
  phone: Yup.string()
    .required('Phone number is required')
    .matches(/^[+]?[\d\s\-\(\)]+$/, 'Please enter a valid phone number'),
  address: Yup.string()
    .required('Address is required')
    .min(10, 'Address must be at least 10 characters')
    .max(500, 'Address must be less than 500 characters'),
  institute: Yup.string()
    .required('Institute is required'),
  country: Yup.string()
    .required('Country is required'),
  state: Yup.string()
    .required('State is required'),
  district: Yup.string()
    .required('District is required'),
  pincode: Yup.string()
    .matches(/^\d{6}$/, 'Pincode must be 6 digits'),
  maxStudents: Yup.number()
    .required('Maximum students is required')
    .min(10, 'Must allow at least 10 students')
    .max(1000, 'Cannot exceed 1,000 students'),
  isActive: Yup.boolean()
})

interface BranchFormProps {
  initialData?: any
  onSubmit: (values: any) => Promise<void>
  onCancel: () => void
}

export function BranchForm({ initialData, onSubmit, onCancel }: BranchFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { institutes, fetchInstitutes } = useInstitutesStore()
  const { countries, states, districts, fetchCountries, fetchStates, fetchDistricts } = useLocationStore()

  useEffect(() => {
    fetchInstitutes()
    fetchCountries()
  }, [])

  const initialValues = {
    name: initialData?.name || '',
    email: initialData?.email || '',
    phone: initialData?.phone || '',
    address: initialData?.address || '',
    institute: initialData?.institute?.id || '',
    country: initialData?.country?.id || '',
    state: initialData?.state?.id || '',
    district: initialData?.district?.id || '',
    pincode: initialData?.pincode || '',
    maxStudents: initialData?.maxStudents || 50,
    isActive: initialData?.isActive ?? true
  }

  const handleSubmit = async (values: any) => {
    setIsSubmitting(true)
    try {
      await onSubmit(values)
      toast.success(initialData ? 'Branch updated successfully' : 'Branch created successfully')
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MapPin className="h-5 w-5 mr-2" />
          {initialData ? 'Edit Branch' : 'Create New Branch'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Formik
          initialValues={initialValues}
          validationSchema={branchValidationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched }) => (
            <Form className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="institute">Institute *</Label>
                    <Select
                      value={values.institute}
                      onValueChange={(value) => setFieldValue('institute', value)}
                    >
                      <SelectTrigger className={errors.institute && touched.institute ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select institute" />
                      </SelectTrigger>
                      <SelectContent>
                        {institutes.map((institute) => (
                          <SelectItem key={institute.id} value={institute.id}>
                            {institute.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <ErrorMessage name="institute" component="div" className="text-sm text-red-500" />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Branch Name *</Label>
                      <Field
                        as={Input}
                        id="name"
                        name="name"
                        placeholder="Enter branch name"
                        className={errors.name && touched.name ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="name" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address *</Label>
                      <Field
                        as={Input}
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Enter email address"
                        className={errors.email && touched.email ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="email" component="div" className="text-sm text-red-500" />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number *</Label>
                      <Field
                        as={Input}
                        id="phone"
                        name="phone"
                        placeholder="Enter phone number"
                        className={errors.phone && touched.phone ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="phone" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="maxStudents">Maximum Students *</Label>
                      <Field
                        as={Input}
                        id="maxStudents"
                        name="maxStudents"
                        type="number"
                        min="10"
                        max="1000"
                        className={errors.maxStudents && touched.maxStudents ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="maxStudents" component="div" className="text-sm text-red-500" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address">Address *</Label>
                    <Field
                      as={Textarea}
                      id="address"
                      name="address"
                      placeholder="Enter complete address"
                      rows={3}
                      className={errors.address && touched.address ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="address" component="div" className="text-sm text-red-500" />
                  </div>
                </CardContent>
              </Card>

              {/* Location Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Location Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="country">Country *</Label>
                      <Select
                        value={values.country}
                        onValueChange={(value) => {
                          setFieldValue('country', value)
                          setFieldValue('state', '')
                          setFieldValue('district', '')
                          fetchStates(value)
                        }}
                      >
                        <SelectTrigger className={errors.country && touched.country ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                        <SelectContent>
                          {countries.map((country) => (
                            <SelectItem key={country.id} value={country.id}>
                              {country.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <ErrorMessage name="country" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="state">State *</Label>
                      <Select
                        value={values.state}
                        onValueChange={(value) => {
                          setFieldValue('state', value)
                          setFieldValue('district', '')
                          fetchDistricts(value)
                        }}
                        disabled={!values.country}
                      >
                        <SelectTrigger className={errors.state && touched.state ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select state" />
                        </SelectTrigger>
                        <SelectContent>
                          {states.map((state) => (
                            <SelectItem key={state.id} value={state.id}>
                              {state.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <ErrorMessage name="state" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="district">District *</Label>
                      <Select
                        value={values.district}
                        onValueChange={(value) => setFieldValue('district', value)}
                        disabled={!values.state}
                      >
                        <SelectTrigger className={errors.district && touched.district ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select district" />
                        </SelectTrigger>
                        <SelectContent>
                          {districts.map((district) => (
                            <SelectItem key={district.id} value={district.id}>
                              {district.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <ErrorMessage name="district" component="div" className="text-sm text-red-500" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="pincode">Pincode</Label>
                    <Field
                      as={Input}
                      id="pincode"
                      name="pincode"
                      placeholder="Enter 6-digit pincode"
                      maxLength={6}
                      className={errors.pincode && touched.pincode ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="pincode" component="div" className="text-sm text-red-500" />
                  </div>
                </CardContent>
              </Card>

              {/* Status */}
              <div className="flex items-center space-x-2">
                <Field
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  className="rounded"
                />
                <Label htmlFor="isActive">Active Branch</Label>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  <Save className="h-4 w-4 mr-2" />
                  {initialData ? 'Update Branch' : 'Create Branch'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
```

## 🎯 **Phase 1 Complete Integration Summary**

### **✅ CRUD Operations:**
```typescript
✅ Institutes Management API:
├── GET /api/institutes → List institutes with pagination & filters
├── POST /api/institutes → Create new institute (Super Admin only)
├── PATCH /api/institutes/:id → Update institute
├── DELETE /api/institutes/:id → Delete institute (with safety checks)
└── Scope-based access control for institute admins

✅ Branches Management API:
├── GET /api/branches → List branches with pagination & filters
├── POST /api/branches → Create new branch (Admin only)
├── PATCH /api/branches/:id → Update branch
├── DELETE /api/branches/:id → Delete branch (with safety checks)
└── Institute/branch scope restrictions
```

### **✅ Zustand State Management:**
```typescript
✅ Institutes Store:
├── 📊 fetchInstitutes() → Paginated institute list with filters
├── 📝 createInstitute() → Create institute with validation
├── ✏️ updateInstitute() → Update institute details
├── 🗑️ deleteInstitute() → Remove institute with safety checks
└── 🔍 Filter management (search, status, subscription plan)

✅ Branches Store:
├── 📊 fetchBranches() → Paginated branch list with filters
├── 📝 createBranch() → Create branch with institute validation
├── ✏️ updateBranch() → Update branch details
├── 🗑️ deleteBranch() → Remove branch with safety checks
└── 🔍 Filter management (search, institute, status)
```

### **✅ Formik + Yup Forms:**
```typescript
✅ Institute Form:
├── 📝 Name validation (2-100 characters)
├── 📧 Email validation with uniqueness check
├── 📱 Phone number format validation
├── 📍 Address validation (10-500 characters)
├── 🌍 Location cascade (country → state → district)
├── 🔢 Pincode validation (6 digits)
├── 🌐 Website URL validation
├── 📊 Subscription plan selection
├── 🔢 Limits validation (branches: 1-100, students: 10-10000)
└── ✅ Active status toggle

✅ Branch Form:
├── 🏢 Institute selection (required)
├── 📝 Name validation (2-100 characters)
├── 📧 Email validation with uniqueness per institute
├── 📱 Phone number format validation
├── 📍 Address validation (10-500 characters)
├── 🌍 Location cascade (country → state → district)
├── 🔢 Pincode validation (6 digits)
├── 👥 Max students validation (10-1000)
└── ✅ Active status toggle
```

### **✅ Enhanced Features:**
```typescript
✅ Security Features:
├── 🔐 Role-based access control
├── 🎯 Scope-based filtering (institute/branch)
├── 🚫 Safety checks before deletion
├── 📊 Subscription plan limits enforcement
├── 🔍 Uniqueness validation (name/email per scope)
└── 📝 Audit trail with createdBy tracking

✅ User Experience:
├── 🔔 Toast notifications for all actions
├── ⏳ Loading states with spinners
├── 🎨 Error styling with red borders
├── 📱 Responsive design for all devices
├── ♿ Accessibility with proper labels
├── 🔄 Real-time form validation
├── 🌍 Location cascade dropdowns
└── 📊 Subscription plan descriptions
```

**Perfect! Phase 1 Foundation Setup is now complete with full CRUD operations, Zustand state management, Formik + Yup forms, comprehensive validation, and security features! 🚀**
