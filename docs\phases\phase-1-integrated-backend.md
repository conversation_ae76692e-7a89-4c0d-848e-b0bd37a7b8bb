# 🔧 Phase 1: Integrated Backend Configuration

## 🎯 Overview
This document covers the integrated backend setup where Payload CMS is located within the `apps/api/` directory, providing a unified development experience.

## 📁 Integrated Backend Structure

### **apps/api/ Directory Structure**
```
apps/api/
├── 📁 src/
│   ├── 📁 collections/                  # Payload Collections
│   │   ├── 📁 super-admin/              # Super Admin Collections
│   │   │   ├── Institutes.ts            # Institute management
│   │   │   ├── SubscriptionPlans.ts     # Subscription plans
│   │   │   ├── PlatformSettings.ts      # Platform settings
│   │   │   └── Billing.ts               # Billing management
│   │   ├── 📁 institute-admin/          # Institute Admin Collections
│   │   │   ├── Courses.ts               # Course management
│   │   │   ├── Branches.ts              # Branch management
│   │   │   ├── Trainers.ts              # Trainer management
│   │   │   └── InstituteSettings.ts     # Institute settings
│   │   ├── 📁 student/                  # Student Collections
│   │   │   ├── Enrollments.ts           # Course enrollments
│   │   │   ├── Progress.ts              # Learning progress
│   │   │   ├── Exams.ts                 # Exam attempts
│   │   │   └── Certificates.ts          # Certificates
│   │   └── 📁 shared/                   # Shared Collections
│   │       ├── Users.ts                 # All user types
│   │       ├── Payments.ts              # Payment records
│   │       ├── Notifications.ts         # Notifications
│   │       └── Media.ts                 # File uploads
│   ├── 📁 globals/                      # Global Settings
│   │   ├── 📁 settings/
│   │   │   ├── PlatformSettings.ts      # Platform-wide settings
│   │   │   └── EmailSettings.ts         # Email configuration
│   │   └── 📁 themes/
│   │       ├── PlatformThemes.ts        # Platform themes
│   │       └── InstituteThemes.ts       # Institute themes
│   ├── 📁 hooks/                        # Payload Hooks
│   │   ├── auth.ts                      # Authentication hooks
│   │   ├── validation.ts                # Validation hooks
│   │   └── notifications.ts             # Notification hooks
│   ├── 📁 access/                       # Access Control
│   │   ├── superAdmin.ts                # Super admin access
│   │   ├── instituteAdmin.ts            # Institute admin access
│   │   ├── student.ts                   # Student access
│   │   └── shared.ts                    # Shared access rules
│   ├── 📁 endpoints/                    # Custom API Endpoints
│   │   ├── 📁 super-admin/
│   │   │   ├── analytics.ts             # Platform analytics
│   │   │   ├── billing.ts               # Billing endpoints
│   │   │   └── institutes.ts            # Institute management
│   │   ├── 📁 institute-admin/
│   │   │   ├── dashboard.ts             # Institute dashboard
│   │   │   ├── courses.ts               # Course management
│   │   │   └── analytics.ts             # Institute analytics
│   │   └── 📁 student/
│   │       ├── courses.ts               # Course access
│   │       ├── progress.ts              # Progress tracking
│   │       └── exams.ts                 # Exam endpoints
│   └── 📁 utilities/                    # Utility Functions
│       ├── auth.ts                      # Authentication utilities
│       ├── email.ts                     # Email utilities
│       ├── payment.ts                   # Payment utilities
│       └── validation.ts                # Validation utilities
├── payload.config.ts                    # Payload Configuration
├── server.ts                            # Express Server
├── package.json                         # API Dependencies
└── .env                                 # Environment Variables
```

## ⚙️ Payload Configuration

### **payload.config.ts**
```typescript
import { buildConfig } from 'payload/config'
import path from 'path'

// Import Collections
import { Users } from './src/collections/shared/Users'
import { Institutes } from './src/collections/super-admin/Institutes'
import { SubscriptionPlans } from './src/collections/super-admin/SubscriptionPlans'
import { Courses } from './src/collections/institute-admin/Courses'
import { Enrollments } from './src/collections/student/Enrollments'

// Import Globals
import { PlatformSettings } from './src/globals/settings/PlatformSettings'
import { PlatformThemes } from './src/globals/themes/PlatformThemes'

export default buildConfig({
  serverURL: process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3001',
  admin: {
    user: 'users',
    bundler: 'webpack',
    meta: {
      titleSuffix: '- Groups Exam LMS',
      favicon: '/assets/favicon.ico',
      ogImage: '/assets/og-image.jpg',
    },
  },
  collections: [
    // Shared Collections
    Users,
    
    // Super Admin Collections
    Institutes,
    SubscriptionPlans,
    
    // Institute Admin Collections
    Courses,
    
    // Student Collections
    Enrollments,
  ],
  globals: [
    PlatformSettings,
    PlatformThemes,
  ],
  typescript: {
    outputFile: path.resolve(__dirname, 'payload-types.ts'),
  },
  graphQL: {
    schemaOutputFile: path.resolve(__dirname, 'generated-schema.graphql'),
  },
  db: {
    type: 'postgres',
    url: process.env.DATABASE_URI,
  },
  upload: {
    limits: {
      fileSize: 5000000, // 5MB
    },
  },
  cors: [
    'http://localhost:3000', // Super Admin
    'http://localhost:3002', // Institute Admin
    'http://localhost:3003', // Student Portal
  ],
  csrf: [
    'http://localhost:3000',
    'http://localhost:3002', 
    'http://localhost:3003',
  ],
})
```

## 🔐 Environment Configuration

### **apps/api/.env**
```env
# Database Configuration
DATABASE_URI=postgresql://username:password@localhost:5432/groups_exam_lms
PAYLOAD_SECRET=your-payload-secret-key-here
PAYLOAD_CONFIG_PATH=src/payload.config.ts

# Server Configuration
PORT=3001
PAYLOAD_PUBLIC_SERVER_URL=http://localhost:3001

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Storage (AWS S3)
AWS_S3_BUCKET=groups-exam-lms-storage
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# Payment Gateways
STRIPE_SECRET_KEY=sk_test_...
RAZORPAY_KEY_ID=rzp_test_...
RAZORPAY_KEY_SECRET=...

# Security
JWT_SECRET=your-jwt-secret-key
CORS_ORIGIN=http://localhost:3000,http://localhost:3002,http://localhost:3003

# Development
NODE_ENV=development
```

## 🚀 Development Scripts

### **apps/api/package.json Scripts**
```json
{
  "name": "@groups-exam/api",
  "version": "1.0.0",
  "scripts": {
    "dev": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts nodemon",
    "build": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload build",
    "serve": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload serve",
    "generate:types": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:types",
    "generate:graphQLSchema": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:graphQLSchema"
  },
  "dependencies": {
    "payload": "^2.0.0",
    "express": "^4.18.0",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "@payloadcms/plugin-cloud-storage": "^1.0.0",
    "@payloadcms/plugin-form-builder": "^1.0.0",
    "@payloadcms/plugin-seo": "^1.0.0"
  },
  "devDependencies": {
    "@types/express": "^4.17.0",
    "@types/cors": "^2.8.0",
    "cross-env": "^7.0.3",
    "nodemon": "^3.0.0",
    "typescript": "^5.0.0"
  }
}
```

## 🔄 Integration with Frontend App

### **API Communication**
The single frontend application communicates with the integrated backend:

```typescript
// Frontend API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'

// Example API calls from frontend app
const apiClient = {
  // Super Admin API calls
  superAdmin: {
    getInstitutes: () => fetch(`${API_BASE_URL}/api/institutes`),
    createInstitute: (data) => fetch(`${API_BASE_URL}/api/institutes`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },
  
  // Institute Admin API calls
  instituteAdmin: {
    getCourses: () => fetch(`${API_BASE_URL}/api/courses`),
    createCourse: (data) => fetch(`${API_BASE_URL}/api/courses`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },
  
  // Student API calls
  student: {
    getEnrollments: () => fetch(`${API_BASE_URL}/api/enrollments`),
    enrollInCourse: (courseId) => fetch(`${API_BASE_URL}/api/enrollments`, {
      method: 'POST',
      body: JSON.stringify({ courseId })
    })
  }
}
```

## 🎯 Benefits of Integrated Structure

### **Development Benefits**
- ✅ **Single Repository**: Everything in one place
- ✅ **Shared Dependencies**: Common packages across all apps
- ✅ **Unified Build Process**: Single build command for all apps
- ✅ **Consistent Environment**: Same development setup

### **Deployment Benefits**
- ✅ **Simplified Deployment**: Deploy as single unit
- ✅ **Shared Resources**: Common database and storage
- ✅ **Environment Consistency**: Same environment variables
- ✅ **Easier Scaling**: Scale entire application together

### **Maintenance Benefits**
- ✅ **Code Reusability**: Shared utilities and types
- ✅ **Consistent Updates**: Update all apps together
- ✅ **Single Source of Truth**: One configuration for all
- ✅ **Easier Debugging**: All logs in one place

## 🔧 Development Workflow

### **Starting Development Environment**
```bash
# Start integrated backend
cd apps/api
npm run dev

# Start frontend application
cd apps/frontend && npm run dev -- --port 3000
```

### **Development URLs**
- 🔧 **API & Admin Panel**: http://localhost:3001/admin
- 📊 **Super Admin Dashboard**: http://localhost:3000
- 🏫 **Institute Admin Portal**: http://localhost:3002
- 🎓 **Student Portal**: http://localhost:3003

This integrated structure provides a clean, maintainable, and scalable foundation for the Groups Exam LMS SaaS platform! 🚀
