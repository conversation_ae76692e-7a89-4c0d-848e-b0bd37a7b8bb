# Phase 10: Institute Management

Comprehensive institute management system for Super Admin panel with institute creation, admin assignment, and dashboard activities.

## 📋 **Overview**

Phase 10 implements complete institute management functionality including:
- Institute CRUD operations with auto-slug generation
- Institute admin creation and assignment
- Custom domain management with verification
- Dashboard activities for both Super Admin and Institute Admin
- Soft delete functionality
- Address management with location integration

## 🗄️ **Database Schema**

### **Database Table Structure**

```sql
-- Institutes Table
CREATE TABLE institutes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(50),
  website VARCHAR(255),
  tagline TEXT,
  logo_id UUID REFERENCES media(id),
  address_street TEXT,
  city_id VARCHAR(255), -- Reference to location management
  state_id VARCHAR(255), -- Reference to location management
  country_id VARCHAR(255), -- Reference to location management
  district_id VARCHAR(255), -- Reference to location management
  zip_code VARCHAR(20),
  custom_domain VARCHAR(255),
  domain_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  deleted_at TIMESTAMP NULL -- For soft delete
);

-- Indexes for performance
CREATE INDEX idx_institutes_slug ON institutes(slug);
CREATE INDEX idx_institutes_active ON institutes(is_active);
CREATE INDEX idx_institutes_domain_verified ON institutes(domain_verified);
CREATE INDEX idx_institutes_deleted_at ON institutes(deleted_at);
CREATE INDEX idx_institutes_country_id ON institutes(country_id);
CREATE INDEX idx_institutes_state_id ON institutes(state_id);
CREATE INDEX idx_institutes_city_id ON institutes(city_id);
```

### **Institutes Collection**

```typescript
// apps/api/src/collections/Institutes.ts
import { CollectionConfig } from 'payload/types'

const Institutes: CollectionConfig = {
  slug: 'institutes',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'email', 'isActive', 'domainVerified', 'createdAt'],
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => user?.legacyRole === 'super_admin',
    update: ({ req: { user } }) => user?.legacyRole === 'super_admin' || user?.legacyRole === 'institute_admin',
    delete: ({ req: { user } }) => user?.legacyRole === 'super_admin',
  },
  fields: [
    // UUID Primary Key
    {
      name: 'id',
      type: 'text',
      defaultValue: () => {
        return require('crypto').randomUUID()
      },
      admin: {
        hidden: true,
      },
    },

    // Basic Information
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Institute Name',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'Slug',
      admin: {
        readOnly: true,
      },
      hooks: {
        beforeValidate: [
          ({ data }) => {
            if (data?.name && !data?.slug) {
              data.slug = data.name
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '')
            }
          },
        ],
      },
    },
    {
      name: 'email',
      type: 'email',
      label: 'Contact Email',
    },
    {
      name: 'phone',
      type: 'text',
      label: 'Contact Phone',
    },
    {
      name: 'website',
      type: 'text',
      label: 'Website URL',
    },
    {
      name: 'tagline',
      type: 'text',
      label: 'Tagline',
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      label: 'Institute Logo',
    },

    // Address Information with Location References
    {
      name: 'addressStreet',
      type: 'text',
      label: 'Street Address',
    },
    {
      name: 'cityId',
      type: 'text',
      label: 'City ID',
      admin: {
        description: 'Reference to city in location management system',
      },
    },
    {
      name: 'stateId',
      type: 'text',
      label: 'State ID',
      admin: {
        description: 'Reference to state in location management system',
      },
    },
    {
      name: 'countryId',
      type: 'text',
      label: 'Country ID',
      admin: {
        description: 'Reference to country in location management system',
      },
    },
    {
      name: 'districtId',
      type: 'text',
      label: 'District ID',
      admin: {
        description: 'Reference to district in location management system',
      },
    },
    {
      name: 'zipCode',
      type: 'text',
      label: 'ZIP/Postal Code',
    },

    // Domain Management
    {
      name: 'customDomain',
      type: 'text',
      label: 'Custom Domain',
    },
    {
      name: 'domainVerified',
      type: 'checkbox',
      label: 'Domain Verified',
      defaultValue: false,
    },

    // Status
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active',
      defaultValue: true,
    },

    // Soft Delete
    {
      name: 'deletedAt',
      type: 'date',
      admin: {
        hidden: true,
      },
    },
  ],
  timestamps: true,
}

export default Institutes
```

## 🔌 **API Endpoints**

### **Institute Management Endpoints**

```typescript
// apps/api/src/endpoints/institute-management.ts
import { Endpoint } from 'payload/config'
import { requireAuth, requireSuperAdmin } from '../middleware/auth'

// Helper function for authenticated endpoints
const createAuthenticatedEndpoint = (
  path: string,
  method: 'get' | 'post' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>,
  allowedRoles?: string[]
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = allowedRoles ? requireAuth(allowedRoles) : requireAuth()
      const authResult = await authMiddleware(req)
      
      if (authResult) {
        return authResult
      }
      
      return handler(req)
    }
  }
}

// Get institutes with filtering and pagination
export const getInstitutesEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes',
  'get',
  async (req: any) => {
    try {
      const { searchParams } = new URL(req.url!)
      const search = searchParams.get('search') || ''
      const isActive = searchParams.get('isActive') || 'true'
      const domainVerified = searchParams.get('domainVerified')
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')

      const where: any = {
        deletedAt: { exists: false } // Exclude soft deleted
      }

      // Active filter
      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      // Domain verified filter
      if (domainVerified !== null && domainVerified !== undefined) {
        where.domainVerified = { equals: domainVerified === 'true' }
      }

      // Search filter
      if (search) {
        where.or = [
          { name: { contains: search } },
          { email: { contains: search } },
          { slug: { contains: search } },
          { website: { contains: search } }
        ]
      }

      const institutes = await req.payload.find({
        collection: 'institutes',
        where,
        page,
        limit,
        sort: '-createdAt',
        populate: ['logo']
      })

      return Response.json({
        success: true,
        docs: institutes.docs,
        page: institutes.page,
        limit: institutes.limit,
        totalPages: institutes.totalPages,
        totalDocs: institutes.totalDocs,
        hasNextPage: institutes.hasNextPage,
        hasPrevPage: institutes.hasPrevPage
      })

    } catch (error) {
      console.error('Institutes fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
  ['super_admin'] // Only super admin can view all institutes
)

// Create institute with admin user
export const createInstituteEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes',
  'post',
  async (req: any) => {
    try {
      const {
        // Institute data
        name,
        email,
        phone,
        website,
        tagline,
        logo,
        addressStreet,
        cityId,
        stateId,
        countryId,
        districtId,
        zipCode,
        customDomain,
        // Admin user data
        adminFirstName,
        adminLastName,
        adminEmail,
        adminPassword
      } = await req.json()

      // Create institute
      const institute = await req.payload.create({
        collection: 'institutes',
        data: {
          name,
          email,
          phone,
          website,
          tagline,
          logo,
          addressStreet,
          cityId,
          stateId,
          countryId,
          districtId,
          zipCode,
          customDomain,
          domainVerified: false,
          isActive: true
        }
      })

      // Create institute admin user
      const adminUser = await req.payload.create({
        collection: 'users',
        data: {
          firstName: adminFirstName,
          lastName: adminLastName,
          email: adminEmail,
          password: adminPassword,
          legacyRole: 'institute_admin',
          institute: institute.id,
          isActive: true
        }
      })

      return Response.json({
        success: true,
        institute,
        adminUser: {
          id: adminUser.id,
          email: adminUser.email,
          firstName: adminUser.firstName,
          lastName: adminUser.lastName
        },
        message: 'Institute and admin user created successfully'
      })

    } catch (error) {
      console.error('Create institute error:', error)
      return Response.json(
        { success: false, error: error.message || 'Failed to create institute' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)

// Update institute
export const updateInstituteEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes/:id',
  'put',
  async (req: any) => {
    try {
      const { id } = req.params
      const updateData = await req.json()

      // Remove fields that shouldn't be updated directly
      delete updateData.slug // Slug is auto-generated
      delete updateData.createdAt
      delete updateData.deletedAt

      const institute = await req.payload.update({
        collection: 'institutes',
        id,
        data: updateData
      })

      return Response.json({
        success: true,
        institute,
        message: 'Institute updated successfully'
      })

    } catch (error) {
      console.error('Update institute error:', error)
      return Response.json(
        { success: false, error: error.message || 'Failed to update institute' },
        { status: 500 }
      )
    }
  },
  ['super_admin', 'institute_admin']
)

// Soft delete institute
export const deleteInstituteEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes/:id',
  'delete',
  async (req: any) => {
    try {
      const { id } = req.params

      // Soft delete by setting deletedAt timestamp
      const institute = await req.payload.update({
        collection: 'institutes',
        id,
        data: {
          deletedAt: new Date(),
          isActive: false
        }
      })

      return Response.json({
        success: true,
        message: 'Institute deleted successfully'
      })

    } catch (error) {
      console.error('Delete institute error:', error)
      return Response.json(
        { success: false, error: error.message || 'Failed to delete institute' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)

// Get institute statistics
export const getInstituteStatisticsEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/statistics',
  'get',
  async (req: any) => {
    try {
      // Total institutes
      const totalInstitutes = await req.payload.find({
        collection: 'institutes',
        where: { deletedAt: { exists: false } },
        limit: 0
      })

      // Active institutes
      const activeInstitutes = await req.payload.find({
        collection: 'institutes',
        where: {
          and: [
            { deletedAt: { exists: false } },
            { isActive: { equals: true } }
          ]
        },
        limit: 0
      })

      // Verified domains
      const verifiedDomains = await req.payload.find({
        collection: 'institutes',
        where: {
          and: [
            { deletedAt: { exists: false } },
            { domainVerified: { equals: true } }
          ]
        },
        limit: 0
      })

      // Recent institutes (last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const recentInstitutes = await req.payload.find({
        collection: 'institutes',
        where: {
          and: [
            { deletedAt: { exists: false } },
            { createdAt: { greater_than: thirtyDaysAgo } }
          ]
        },
        limit: 0
      })

      return Response.json({
        success: true,
        data: {
          total: totalInstitutes.totalDocs,
          active: activeInstitutes.totalDocs,
          inactive: totalInstitutes.totalDocs - activeInstitutes.totalDocs,
          verifiedDomains: verifiedDomains.totalDocs,
          recentlyCreated: recentInstitutes.totalDocs
        }
      })

    } catch (error) {
      console.error('Get institute statistics error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)

// Verify custom domain
export const verifyDomainEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes/:id/verify-domain',
  'post',
  async (req: any) => {
    try {
      const { id } = req.params

      // Get institute
      const institute = await req.payload.findByID({
        collection: 'institutes',
        id
      })

      if (!institute || !institute.customDomain) {
        return Response.json({
          success: false,
          error: 'Institute or custom domain not found'
        }, { status: 404 })
      }

      // TODO: Implement actual domain verification logic
      // For now, just mark as verified
      const updatedInstitute = await req.payload.update({
        collection: 'institutes',
        id,
        data: {
          domainVerified: true
        }
      })

      return Response.json({
        success: true,
        institute: updatedInstitute,
        message: 'Domain verified successfully'
      })

    } catch (error) {
      console.error('Verify domain error:', error)
      return Response.json(
        { success: false, error: 'Failed to verify domain' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)
```

## 🎨 **Frontend Implementation**

### **Zustand Store**

```typescript
// apps/frontend/src/stores/super-admin/useInstituteManagementStore.ts
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

// Types
export interface Institute {
  id: string // UUID
  name: string
  slug: string
  email?: string
  phone?: string
  website?: string
  tagline?: string
  logo?: any
  addressStreet?: string
  cityId?: string
  stateId?: string
  countryId?: string
  districtId?: string
  zipCode?: string
  customDomain?: string
  domainVerified: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string
}

interface InstituteFormData {
  // Institute data
  name: string
  email?: string
  phone?: string
  website?: string
  tagline?: string
  logo?: string
  addressStreet?: string
  cityId?: string
  stateId?: string
  countryId?: string
  districtId?: string
  zipCode?: string
  customDomain?: string
  // Admin user data
  adminFirstName: string
  adminLastName: string
  adminEmail: string
  adminPassword: string
}

interface Pagination {
  page: number
  limit: number
  totalDocs: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface Filters {
  search: string
  isActive?: boolean
  domainVerified?: boolean
}

interface Statistics {
  total: number
  active: number
  inactive: number
  verifiedDomains: number
  recentlyCreated: number
}

interface InstituteManagementStore {
  // State
  institutes: Institute[]
  selectedInstitute: Institute | null
  isLoading: boolean
  error: string | null
  pagination: Pagination
  filters: Filters
  statistics: Statistics | null

  // UI State
  showCreateForm: boolean
  showEditForm: boolean
  viewMode: 'list' | 'cards'

  // Actions
  setSelectedInstitute: (institute: Institute | null) => void
  setFilters: (filters: Partial<Filters>) => void
  setViewMode: (mode: 'list' | 'cards') => void
  setShowCreateForm: (show: boolean) => void
  setShowEditForm: (show: boolean) => void
  clearError: () => void

  // Data Actions
  fetchInstitutes: (page?: number, filters?: Partial<Filters>) => Promise<void>
  fetchStatistics: () => Promise<void>
  createInstitute: (data: InstituteFormData) => Promise<boolean>
  updateInstitute: (id: string, data: Partial<Institute>) => Promise<boolean>
  deleteInstitute: (id: string) => Promise<boolean>
  verifyDomain: (id: string) => Promise<boolean>
}

export const useInstituteManagementStore = create<InstituteManagementStore>()(
  devtools(
    (set, get) => ({
      // Initial State
      institutes: [],
      selectedInstitute: null,
      isLoading: false,
      error: null,
      pagination: {
        page: 1,
        limit: 20,
        totalDocs: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false
      },
      filters: {
        search: '',
        isActive: true,
        domainVerified: undefined
      },
      statistics: null,
      showCreateForm: false,
      showEditForm: false,
      viewMode: 'list',

      // UI Actions
      setSelectedInstitute: (institute) => set({ selectedInstitute: institute }),
      setFilters: (newFilters) => set(state => ({
        filters: { ...state.filters, ...newFilters }
      })),
      setViewMode: (mode) => set({ viewMode: mode }),
      setShowCreateForm: (show) => set({ showCreateForm: show }),
      setShowEditForm: (show) => set({ showEditForm: show }),
      clearError: () => set({ error: null }),

      // Fetch Institutes
      fetchInstitutes: async (page = 1, filters) => {
        set({ isLoading: true, error: null })
        try {
          const currentFilters = filters || get().filters
          const queryParams = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            ...(currentFilters.search && { search: currentFilters.search }),
            ...(currentFilters.isActive !== undefined && {
              isActive: currentFilters.isActive.toString()
            }),
            ...(currentFilters.domainVerified !== undefined && {
              domainVerified: currentFilters.domainVerified.toString()
            }),
          })

          const response = await api.get(`/api/institute-management/institutes?${queryParams}`)
          const data = response.data

          if (data.success) {
            set({
              institutes: data.docs,
              pagination: {
                page: data.page,
                limit: data.limit,
                totalDocs: data.totalDocs,
                totalPages: data.totalPages,
                hasNextPage: data.hasNextPage,
                hasPrevPage: data.hasPrevPage,
              },
              isLoading: false,
            })
          } else {
            throw new Error(data.message || 'Failed to fetch institutes')
          }
        } catch (error: any) {
          console.error('Error fetching institutes:', error)
          set({
            error: error.message || 'Failed to fetch institutes',
            isLoading: false
          })
          toast.error('Failed to fetch institutes')
        }
      },

      // Fetch Statistics
      fetchStatistics: async () => {
        try {
          const response = await api.get('/api/institute-management/statistics')
          const data = response.data

          if (data.success) {
            set({ statistics: data.data })
          }
        } catch (error: any) {
          console.error('Error fetching statistics:', error)
        }
      },

      // Create Institute
      createInstitute: async (instituteData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.post('/api/institute-management/institutes', instituteData)
          const data = response.data

          if (data.success) {
            await get().fetchInstitutes()
            await get().fetchStatistics()
            set({ showCreateForm: false })
            toast.success('Institute created successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to create institute')
          }
        } catch (error: any) {
          console.error('Error creating institute:', error)
          set({
            error: error.message || 'Failed to create institute',
            isLoading: false
          })
          toast.error('Failed to create institute')
          return false
        }
      },

      // Update Institute
      updateInstitute: async (id, updateData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.put(`/api/institute-management/institutes/${id}`, updateData)
          const data = response.data

          if (data.success) {
            await get().fetchInstitutes()
            await get().fetchStatistics()
            set({ showEditForm: false, selectedInstitute: null })
            toast.success('Institute updated successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to update institute')
          }
        } catch (error: any) {
          console.error('Error updating institute:', error)
          set({
            error: error.message || 'Failed to update institute',
            isLoading: false
          })
          toast.error('Failed to update institute')
          return false
        }
      },

      // Delete Institute
      deleteInstitute: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.delete(`/api/institute-management/institutes/${id}`)
          const data = response.data

          if (data.success) {
            await get().fetchInstitutes()
            await get().fetchStatistics()
            toast.success('Institute deleted successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to delete institute')
          }
        } catch (error: any) {
          console.error('Error deleting institute:', error)
          set({
            error: error.message || 'Failed to delete institute',
            isLoading: false
          })
          toast.error('Failed to delete institute')
          return false
        }
      },

      // Verify Domain
      verifyDomain: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.post(`/api/institute-management/institutes/${id}/verify-domain`)
          const data = response.data

          if (data.success) {
            await get().fetchInstitutes()
            toast.success('Domain verified successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to verify domain')
          }
        } catch (error: any) {
          console.error('Error verifying domain:', error)
          set({
            error: error.message || 'Failed to verify domain',
            isLoading: false
          })
          toast.error('Failed to verify domain')
          return false
        }
      },
    }),
    {
      name: 'institute-management-store',
    }
  )
)
```

### **React Components**

#### **Institute Management Page**

```typescript
// apps/frontend/src/app/super-admin/institute-management/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useInstituteManagementStore } from '@/stores/super-admin/useInstituteManagementStore'
import { InstituteFilters } from '@/components/institute-management/InstituteFilters'
import { InstitutesList } from '@/components/institute-management/InstitutesList'
import { InstituteCards } from '@/components/institute-management/InstituteCards'
import { InstituteForm } from '@/components/institute-management/InstituteForm'
import { InstituteStatistics } from '@/components/institute-management/InstituteStatistics'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Building2,
  Plus,
  List,
  Grid3X3,
  Download,
  Upload,
  Settings,
  AlertTriangle,
  Loader2
} from 'lucide-react'

export default function InstituteManagementPage() {
  const router = useRouter()
  const [authChecked, setAuthChecked] = useState(false)

  const { user, isAuthenticated, isLoading, initialize } = useAuthStore()
  const {
    institutes,
    isLoading: storeLoading,
    error,
    viewMode,
    showCreateForm,
    statistics,
    fetchInstitutes,
    fetchStatistics,
    setViewMode,
    setShowCreateForm,
    clearError
  } = useInstituteManagementStore()

  // Initialize auth
  useEffect(() => {
    const timer = setTimeout(() => {
      initialize()
    }, 100)
    return () => clearTimeout(timer)
  }, [initialize])

  // Check authentication
  useEffect(() => {
    if (!isLoading) {
      setAuthChecked(true)

      if (!isAuthenticated) {
        router.push('/auth/admin/login')
        return
      }

      if (!user || user.legacyRole !== 'super_admin') {
        router.push('/auth/admin/login')
        return
      }
    }
  }, [user, isAuthenticated, isLoading, router])

  // Load data
  useEffect(() => {
    if (authChecked && isAuthenticated && user) {
      fetchInstitutes()
      fetchStatistics()
    }
  }, [authChecked, isAuthenticated, user])

  if (isLoading || !authChecked) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!isAuthenticated || !user || user.legacyRole !== 'super_admin') {
    return null
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Building2 className="h-8 w-8" />
            Institute Management
          </h1>
          <p className="text-muted-foreground">
            Manage institutes, create admin accounts, and monitor activities
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode(viewMode === 'list' ? 'cards' : 'list')}
          >
            {viewMode === 'list' ? <Grid3X3 className="h-4 w-4" /> : <List className="h-4 w-4" />}
            {viewMode === 'list' ? 'Card View' : 'List View'}
          </Button>
          <Button onClick={() => setShowCreateForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Institute
          </Button>
        </div>
      </div>

      {/* Statistics */}
      {statistics && <InstituteStatistics statistics={statistics} />}

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <InstituteFilters />

      {/* Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Institutes ({institutes.length})</span>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Manage institute accounts and their administrators
          </CardDescription>
        </CardHeader>
        <CardContent>
          {storeLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading institutes...
            </div>
          ) : institutes.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No institutes found</h3>
              <p className="text-gray-500 mb-4">Get started by creating your first institute.</p>
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Institute
              </Button>
            </div>
          ) : viewMode === 'list' ? (
            <InstitutesList institutes={institutes} />
          ) : (
            <InstituteCards institutes={institutes} />
          )}
        </CardContent>
      </Card>

      {/* Create Form Modal */}
      {showCreateForm && (
        <InstituteForm
          isOpen={showCreateForm}
          onClose={() => setShowCreateForm(false)}
          mode="create"
        />
      )}
    </div>
  )
}
```

#### **Enhanced Institute Form Component**

```typescript
// apps/frontend/src/components/institute-management/InstituteForm.tsx
'use client'

import { useEffect, useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { useInstituteManagementStore, Institute } from '@/stores/super-admin/useInstituteManagementStore'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Loader2,
  Building2,
  User,
  Mail,
  Lock,
  Globe,
  MapPin,
  Phone,
  Link,
  Image,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'

interface InstituteFormProps {
  isOpen: boolean
  onClose: () => void
  mode: 'create' | 'edit'
  institute?: Institute
}

const validationSchema = Yup.object({
  // Institute fields
  name: Yup.string().required('Institute name is required'),
  email: Yup.string().email('Invalid email format'),
  phone: Yup.string(),
  website: Yup.string().url('Invalid URL format'),
  tagline: Yup.string(),
  customDomain: Yup.string(),

  // Address fields
  addressStreet: Yup.string(),
  cityId: Yup.string(),
  stateId: Yup.string(),
  countryId: Yup.string(),
  districtId: Yup.string(),
  zipCode: Yup.string(),

  // Admin fields (only for create mode)
  adminFirstName: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.required('Admin first name is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminLastName: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.required('Admin last name is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminEmail: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.email('Invalid email').required('Admin email is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminPassword: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.min(6, 'Password must be at least 6 characters').required('Admin password is required'),
    otherwise: (schema) => schema.notRequired()
  }),
})

export function InstituteForm({ isOpen, onClose, mode, institute }: InstituteFormProps) {
  const { createInstitute, updateInstitute, isLoading } = useInstituteManagementStore()
  const [currentStep, setCurrentStep] = useState(1)
  const [countries, setCountries] = useState([])
  const [states, setStates] = useState([])
  const [cities, setCities] = useState([])
  const [districts, setDistricts] = useState([])

  const totalSteps = mode === 'create' ? 2 : 1

  const formik = useFormik({
    initialValues: {
      // Institute fields
      name: institute?.name || '',
      email: institute?.email || '',
      phone: institute?.phone || '',
      website: institute?.website || '',
      tagline: institute?.tagline || '',
      customDomain: institute?.customDomain || '',

      // Address fields
      addressStreet: institute?.addressStreet || '',
      cityId: institute?.cityId || '',
      stateId: institute?.stateId || '',
      countryId: institute?.countryId || '',
      districtId: institute?.districtId || '',
      zipCode: institute?.zipCode || '',

      // Admin fields (only for create)
      adminFirstName: '',
      adminLastName: '',
      adminEmail: '',
      adminPassword: '',
    },
    validationSchema,
    validationContext: { mode },
    onSubmit: async (values) => {
      let success = false

      if (mode === 'create') {
        success = await createInstitute(values)
      } else if (mode === 'edit' && institute) {
        const { adminFirstName, adminLastName, adminEmail, adminPassword, ...updateData } = values
        success = await updateInstitute(institute.id, updateData)
      }

      if (success) {
        onClose()
      }
    },
  })

  // Reset form when institute changes
  useEffect(() => {
    if (institute && mode === 'edit') {
      formik.setValues({
        name: institute.name || '',
        email: institute.email || '',
        phone: institute.phone || '',
        website: institute.website || '',
        tagline: institute.tagline || '',
        customDomain: institute.customDomain || '',
        addressStreet: institute.addressStreet || '',
        cityId: institute.cityId || '',
        stateId: institute.stateId || '',
        countryId: institute.countryId || '',
        districtId: institute.districtId || '',
        zipCode: institute.zipCode || '',
        adminFirstName: '',
        adminLastName: '',
        adminEmail: '',
        adminPassword: '',
      })
    }
  }, [institute, mode])

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return 'Institute Information'
      case 2: return 'Institute Admin'
      default: return ''
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Building2 className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <DialogTitle className="text-xl">
                  {mode === 'create' ? 'Create New Institute' : 'Edit Institute'}
                </DialogTitle>
                <DialogDescription>
                  {mode === 'create'
                    ? 'Set up a new institute with admin account'
                    : 'Update institute information and settings'
                  }
                </DialogDescription>
              </div>
            </div>
            {mode === 'create' && (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  Step {currentStep} of {totalSteps}
                </Badge>
              </div>
            )}
          </div>

          {/* Progress Steps */}
          {mode === 'create' && (
            <div className="flex items-center justify-between mt-6">
              {[1, 2].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    step <= currentStep
                      ? 'bg-blue-600 border-blue-600 text-white'
                      : 'border-gray-300 text-gray-400'
                  }`}>
                    {step < currentStep ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <span className="text-sm font-medium">{step}</span>
                    )}
                  </div>
                  {step < 2 && (
                    <div className={`w-32 h-0.5 mx-2 ${
                      step < currentStep ? 'bg-blue-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          )}
        </DialogHeader>

        <div className="overflow-y-auto max-h-[calc(90vh-200px)]">
          <form onSubmit={formik.handleSubmit} className="space-y-6">
            {/* Step 1: Institute Information */}
            {(mode === 'edit' || currentStep === 1) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Institute Information
                  </CardTitle>
                  <CardDescription>
                    Complete institute details including contact and address information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Basic Information Section */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Basic Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name" className="flex items-center gap-2">
                          Institute Name
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="name"
                          name="name"
                          placeholder="Enter institute name"
                          value={formik.values.name}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.name && formik.errors.name}
                        />
                        {formik.values.name && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Slug: {formik.values.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="email" className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          Contact Email
                        </Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={formik.values.email}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.email && formik.errors.email}
                        />
                      </div>

                      <div>
                        <Label htmlFor="phone" className="flex items-center gap-2">
                          <Phone className="h-4 w-4" />
                          Contact Phone
                        </Label>
                        <Input
                          id="phone"
                          name="phone"
                          placeholder="+****************"
                          value={formik.values.phone}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.phone && formik.errors.phone}
                        />
                      </div>

                      <div>
                        <Label htmlFor="website" className="flex items-center gap-2">
                          <Globe className="h-4 w-4" />
                          Website URL
                        </Label>
                        <Input
                          id="website"
                          name="website"
                          placeholder="https://institute.com"
                          value={formik.values.website}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.website && formik.errors.website}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="tagline">Institute Tagline</Label>
                        <Input
                          id="tagline"
                          name="tagline"
                          placeholder="Empowering minds, shaping futures"
                          value={formik.values.tagline}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.tagline && formik.errors.tagline}
                        />
                      </div>

                      <div>
                        <Label htmlFor="customDomain" className="flex items-center gap-2">
                          <Link className="h-4 w-4" />
                          Custom Domain
                          <Badge variant="secondary" className="text-xs">Optional</Badge>
                        </Label>
                        <Input
                          id="customDomain"
                          name="customDomain"
                          placeholder="myinstitute.com"
                          value={formik.values.customDomain}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.customDomain && formik.errors.customDomain}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Students will access courses via this domain
                        </p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Address Information Section */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Address & Location
                    </h4>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="addressStreet">Street Address</Label>
                        <Input
                          id="addressStreet"
                          name="addressStreet"
                          placeholder="123 Education Street"
                          value={formik.values.addressStreet}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.addressStreet && formik.errors.addressStreet}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="countryId">Country</Label>
                          <Select
                            value={formik.values.countryId}
                            onValueChange={(value) => formik.setFieldValue('countryId', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select country" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">United States</SelectItem>
                              <SelectItem value="2">Canada</SelectItem>
                              <SelectItem value="3">United Kingdom</SelectItem>
                              <SelectItem value="4">Australia</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="stateId">State/Province</Label>
                          <Select
                            value={formik.values.stateId}
                            onValueChange={(value) => formik.setFieldValue('stateId', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select state" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">California</SelectItem>
                              <SelectItem value="2">New York</SelectItem>
                              <SelectItem value="3">Texas</SelectItem>
                              <SelectItem value="4">Florida</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="cityId">City</Label>
                          <Select
                            value={formik.values.cityId}
                            onValueChange={(value) => formik.setFieldValue('cityId', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select city" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">Los Angeles</SelectItem>
                              <SelectItem value="2">San Francisco</SelectItem>
                              <SelectItem value="3">San Diego</SelectItem>
                              <SelectItem value="4">Sacramento</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="districtId">District</Label>
                          <Select
                            value={formik.values.districtId}
                            onValueChange={(value) => formik.setFieldValue('districtId', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select district" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">Downtown</SelectItem>
                              <SelectItem value="2">Westside</SelectItem>
                              <SelectItem value="3">East District</SelectItem>
                              <SelectItem value="4">North District</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="zipCode">ZIP/Postal Code</Label>
                          <Input
                            id="zipCode"
                            name="zipCode"
                            placeholder="90210"
                            value={formik.values.zipCode}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            error={formik.touched.zipCode && formik.errors.zipCode}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          {/* Institute Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              <h3 className="text-lg font-medium">Institute Information</h3>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Institute Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.name && formik.errors.name}
                />
              </div>

              <div>
                <Label htmlFor="email">Contact Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.email && formik.errors.email}
                />
              </div>

              <div>
                <Label htmlFor="phone">Contact Phone</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formik.values.phone}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.phone && formik.errors.phone}
                />
              </div>

              <div>
                <Label htmlFor="website">Website URL</Label>
                <Input
                  id="website"
                  name="website"
                  value={formik.values.website}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.website && formik.errors.website}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="tagline">Tagline</Label>
              <Input
                id="tagline"
                name="tagline"
                value={formik.values.tagline}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.tagline && formik.errors.tagline}
              />
            </div>

            <div>
              <Label htmlFor="customDomain">Custom Domain</Label>
              <Input
                id="customDomain"
                name="customDomain"
                placeholder="example.com"
                value={formik.values.customDomain}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.customDomain && formik.errors.customDomain}
              />
            </div>
          </div>

          <Separator />

          {/* Address Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              <h3 className="text-lg font-medium">Address Information</h3>
            </div>

            <div>
              <Label htmlFor="addressStreet">Street Address</Label>
              <Input
                id="addressStreet"
                name="addressStreet"
                value={formik.values.addressStreet}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.addressStreet && formik.errors.addressStreet}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="cityId">City ID</Label>
                <Input
                  id="cityId"
                  name="cityId"
                  placeholder="City reference ID"
                  value={formik.values.cityId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.cityId && formik.errors.cityId}
                />
              </div>

              <div>
                <Label htmlFor="stateId">State ID</Label>
                <Input
                  id="stateId"
                  name="stateId"
                  placeholder="State reference ID"
                  value={formik.values.stateId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.stateId && formik.errors.stateId}
                />
              </div>

              <div>
                <Label htmlFor="countryId">Country ID</Label>
                <Input
                  id="countryId"
                  name="countryId"
                  placeholder="Country reference ID"
                  value={formik.values.countryId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.countryId && formik.errors.countryId}
                />
              </div>

              <div>
                <Label htmlFor="districtId">District ID</Label>
                <Input
                  id="districtId"
                  name="districtId"
                  placeholder="District reference ID"
                  value={formik.values.districtId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.districtId && formik.errors.districtId}
                />
              </div>

              <div>
                <Label htmlFor="zipCode">ZIP/Postal Code</Label>
                <Input
                  id="zipCode"
                  name="zipCode"
                  value={formik.values.zipCode}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.zipCode && formik.errors.zipCode}
                />
              </div>
            </div>
          </div>

          {/* Admin User Information (Create mode only) */}
          {mode === 'create' && (
            <>
              <Separator />
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <h3 className="text-lg font-medium">Institute Admin User</h3>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="adminFirstName">First Name *</Label>
                    <Input
                      id="adminFirstName"
                      name="adminFirstName"
                      value={formik.values.adminFirstName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.adminFirstName && formik.errors.adminFirstName}
                    />
                  </div>

                  <div>
                    <Label htmlFor="adminLastName">Last Name *</Label>
                    <Input
                      id="adminLastName"
                      name="adminLastName"
                      value={formik.values.adminLastName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.adminLastName && formik.errors.adminLastName}
                    />
                  </div>

                  <div>
                    <Label htmlFor="adminEmail">Email *</Label>
                    <Input
                      id="adminEmail"
                      name="adminEmail"
                      type="email"
                      value={formik.values.adminEmail}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.adminEmail && formik.errors.adminEmail}
                    />
                  </div>

                  <div>
                    <Label htmlFor="adminPassword">Password *</Label>
                    <Input
                      id="adminPassword"
                      name="adminPassword"
                      type="password"
                      value={formik.values.adminPassword}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.adminPassword && formik.errors.adminPassword}
                    />
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {mode === 'create' ? 'Create Institute' : 'Update Institute'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
```

#### **Institute View Modal Component**

```typescript
// apps/frontend/src/components/institute-management/InstituteViewModal.tsx
'use client'

import { useState, useEffect } from 'react'
import { useInstituteManagementStore, Institute } from '@/stores/super-admin/useInstituteManagementStore'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Building2,
  Mail,
  Phone,
  Globe,
  MapPin,
  Users,
  DollarSign,
  GraduationCap,
  Edit,
  CheckCircle,
  XCircle,
  Calendar,
  MoreHorizontal,
  Eye,
  Trash2
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface InstituteViewModalProps {
  isOpen: boolean
  onClose: () => void
  institute: Institute | null
  onEdit: (institute: Institute) => void
  onDelete: (institute: Institute) => void
}

interface InstituteStats {
  totalBills: number
  totalAmount: number
  pendingAmount: number
  totalStaff: number
  totalBranches: number
  totalStudents: number
  studentsByBranch: Array<{
    branchId: string
    branchName: string
    studentCount: number
  }>
}

interface StaffMember {
  id: string
  firstName: string
  lastName: string
  email: string
  role: string
  branch?: string
  isActive: boolean
  joinedAt: string
}

interface Branch {
  id: string
  name: string
  address: string
  manager: string
  studentCount: number
  isActive: boolean
  createdAt: string
}

export function InstituteViewModal({
  isOpen,
  onClose,
  institute,
  onEdit,
  onDelete
}: InstituteViewModalProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState<InstituteStats | null>(null)
  const [staff, setStaff] = useState<StaffMember[]>([])
  const [branches, setBranches] = useState<Branch[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Fetch institute details when modal opens
  useEffect(() => {
    if (isOpen && institute) {
      fetchInstituteDetails()
    }
  }, [isOpen, institute])

  const fetchInstituteDetails = async () => {
    if (!institute) return

    setIsLoading(true)
    try {
      // TODO: Replace with actual API calls
      // Mock data for demonstration
      const mockStats: InstituteStats = {
        totalBills: 24,
        totalAmount: 125000,
        pendingAmount: 15000,
        totalStaff: 18,
        totalBranches: 3,
        totalStudents: 450,
        studentsByBranch: [
          { branchId: '1', branchName: 'Main Campus', studentCount: 200 },
          { branchId: '2', branchName: 'North Branch', studentCount: 150 },
          { branchId: '3', branchName: 'South Branch', studentCount: 100 },
        ]
      }

      const mockStaff: StaffMember[] = [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'Institute Admin',
          isActive: true,
          joinedAt: '2024-01-15T00:00:00Z'
        },
        {
          id: '2',
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          role: 'Branch Manager',
          branch: 'Main Campus',
          isActive: true,
          joinedAt: '2024-02-01T00:00:00Z'
        },
        {
          id: '3',
          firstName: 'Mike',
          lastName: 'Johnson',
          email: '<EMAIL>',
          role: 'Instructor',
          branch: 'North Branch',
          isActive: true,
          joinedAt: '2024-03-10T00:00:00Z'
        }
      ]

      const mockBranches: Branch[] = [
        {
          id: '1',
          name: 'Main Campus',
          address: '123 Education St, City Center',
          manager: 'Jane Smith',
          studentCount: 200,
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          name: 'North Branch',
          address: '456 Learning Ave, North District',
          manager: 'Mike Johnson',
          studentCount: 150,
          isActive: true,
          createdAt: '2024-02-15T00:00:00Z'
        },
        {
          id: '3',
          name: 'South Branch',
          address: '789 Knowledge Blvd, South Area',
          manager: 'Sarah Wilson',
          studentCount: 100,
          isActive: true,
          createdAt: '2024-03-01T00:00:00Z'
        }
      ]

      setStats(mockStats)
      setStaff(mockStaff)
      setBranches(mockBranches)
    } catch (error) {
      console.error('Error fetching institute details:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!institute) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Building2 className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <DialogTitle className="text-xl">{institute.name}</DialogTitle>
                <DialogDescription className="flex items-center gap-2">
                  <span>@{institute.slug}</span>
                  {institute.domainVerified ? (
                    <Badge variant="default" className="text-xs">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Verified
                    </Badge>
                  ) : (
                    <Badge variant="secondary" className="text-xs">
                      <XCircle className="h-3 w-3 mr-1" />
                      Unverified
                    </Badge>
                  )}
                  <Badge variant={institute.isActive ? "default" : "destructive"} className="text-xs">
                    {institute.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </DialogDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => onEdit(institute)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" size="sm" onClick={() => onDelete(institute)}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 overflow-hidden">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="billing">Billing</TabsTrigger>
            <TabsTrigger value="staff">Staff</TabsTrigger>
            <TabsTrigger value="branches">Branches</TabsTrigger>
            <TabsTrigger value="students">Students</TabsTrigger>
          </TabsList>

          <div className="mt-4 overflow-y-auto max-h-[calc(90vh-200px)]">
            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              {/* Quick Stats */}
              {stats && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground">Total Bills</p>
                          <p className="text-2xl font-bold">{stats.totalBills}</p>
                        </div>
                        <DollarSign className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground">Total Staff</p>
                          <p className="text-2xl font-bold">{stats.totalStaff}</p>
                        </div>
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground">Branches</p>
                          <p className="text-2xl font-bold">{stats.totalBranches}</p>
                        </div>
                        <Building2 className="h-8 w-8 text-purple-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground">Students</p>
                          <p className="text-2xl font-bold">{stats.totalStudents}</p>
                        </div>
                        <GraduationCap className="h-8 w-8 text-orange-600" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Institute Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {institute.email && (
                      <div className="flex items-center gap-3">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span>{institute.email}</span>
                      </div>
                    )}
                    {institute.phone && (
                      <div className="flex items-center gap-3">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{institute.phone}</span>
                      </div>
                    )}
                    {institute.website && (
                      <div className="flex items-center gap-3">
                        <Globe className="h-4 w-4 text-muted-foreground" />
                        <a href={institute.website} target="_blank" rel="noopener noreferrer"
                           className="text-blue-600 hover:underline">
                          {institute.website}
                        </a>
                      </div>
                    )}
                    {institute.customDomain && (
                      <div className="flex items-center gap-3">
                        <Globe className="h-4 w-4 text-muted-foreground" />
                        <span>{institute.customDomain}</span>
                        {institute.domainVerified && (
                          <Badge variant="default" className="text-xs">Verified</Badge>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Address</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-start gap-3">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                      <div className="space-y-1">
                        {institute.addressStreet && <p>{institute.addressStreet}</p>}
                        <div className="text-sm text-muted-foreground">
                          {institute.cityId && <span>City ID: {institute.cityId}</span>}
                          {institute.stateId && <span>, State ID: {institute.stateId}</span>}
                          {institute.countryId && <span>, Country ID: {institute.countryId}</span>}
                          {institute.zipCode && <span> - {institute.zipCode}</span>}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Students by Branch */}
              {stats && (
                <Card>
                  <CardHeader>
                    <CardTitle>Students by Branch</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {stats.studentsByBranch.map((branch) => (
                        <div key={branch.branchId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{branch.branchName}</span>
                          </div>
                          <Badge variant="secondary">{branch.studentCount} students</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Billing Tab */}
            <TabsContent value="billing" className="space-y-6">
              {stats && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <p className="text-sm text-muted-foreground">Total Amount</p>
                          <p className="text-2xl font-bold text-green-600">
                            ${stats.totalAmount.toLocaleString()}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <p className="text-sm text-muted-foreground">Pending Amount</p>
                          <p className="text-2xl font-bold text-orange-600">
                            ${stats.pendingAmount.toLocaleString()}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <p className="text-sm text-muted-foreground">Total Bills</p>
                          <p className="text-2xl font-bold">{stats.totalBills}</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Bills</CardTitle>
                      <CardDescription>Latest billing information</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-8 text-muted-foreground">
                        <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Billing integration coming soon</p>
                        <p className="text-sm">Connect with billing system to view detailed bills</p>
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </TabsContent>

            {/* Staff Tab */}
            <TabsContent value="staff" className="space-y-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Staff Members</CardTitle>
                    <CardDescription>Manage institute staff and their roles</CardDescription>
                  </div>
                  <Button size="sm">
                    <Users className="h-4 w-4 mr-2" />
                    Add Staff
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {staff.map((member) => (
                      <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarFallback>
                              {member.firstName[0]}{member.lastName[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{member.firstName} {member.lastName}</p>
                            <p className="text-sm text-muted-foreground">{member.email}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">{member.role}</Badge>
                              {member.branch && (
                                <Badge variant="secondary" className="text-xs">{member.branch}</Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant={member.isActive ? "default" : "secondary"} className="text-xs">
                            {member.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                          <p className="text-xs text-muted-foreground mt-1">
                            Joined {formatDistanceToNow(new Date(member.joinedAt), { addSuffix: true })}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Branches Tab */}
            <TabsContent value="branches" className="space-y-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Branch Locations</CardTitle>
                    <CardDescription>Manage institute branches and locations</CardDescription>
                  </div>
                  <Button size="sm">
                    <Building2 className="h-4 w-4 mr-2" />
                    Add Branch
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {branches.map((branch) => (
                      <Card key={branch.id}>
                        <CardContent className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <h3 className="font-medium">{branch.name}</h3>
                              <Badge variant={branch.isActive ? "default" : "secondary"} className="text-xs">
                                {branch.isActive ? 'Active' : 'Inactive'}
                              </Badge>
                            </div>
                            <div className="space-y-2 text-sm text-muted-foreground">
                              <div className="flex items-center gap-2">
                                <MapPin className="h-3 w-3" />
                                <span>{branch.address}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Users className="h-3 w-3" />
                                <span>Manager: {branch.manager}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <GraduationCap className="h-3 w-3" />
                                <span>{branch.studentCount} students</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Calendar className="h-3 w-3" />
                                <span>Created {formatDistanceToNow(new Date(branch.createdAt), { addSuffix: true })}</span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Students Tab */}
            <TabsContent value="students" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Student Overview</CardTitle>
                  <CardDescription>Student enrollment and distribution</CardDescription>
                </CardHeader>
                <CardContent>
                  {stats && (
                    <div className="space-y-6">
                      <div className="text-center">
                        <p className="text-3xl font-bold text-blue-600">{stats.totalStudents}</p>
                        <p className="text-muted-foreground">Total Students Enrolled</p>
                      </div>

                      <Separator />

                      <div className="space-y-4">
                        <h4 className="font-medium">Distribution by Branch</h4>
                        {stats.studentsByBranch.map((branch) => (
                          <div key={branch.branchId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <Building2 className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{branch.branchName}</span>
                            </div>
                            <div className="text-right">
                              <p className="font-bold">{branch.studentCount}</p>
                              <p className="text-xs text-muted-foreground">
                                {((branch.studentCount / stats.totalStudents) * 100).toFixed(1)}%
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
```

#### **Institute Statistics Component**

```typescript
// apps/frontend/src/components/institute-management/InstituteStatistics.tsx
'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Building2, CheckCircle, XCircle, Globe, TrendingUp } from 'lucide-react'

interface Statistics {
  total: number
  active: number
  inactive: number
  verifiedDomains: number
  recentlyCreated: number
}

interface InstituteStatisticsProps {
  statistics: Statistics
}

export function InstituteStatistics({ statistics }: InstituteStatisticsProps) {
  const stats = [
    {
      title: 'Total Institutes',
      value: statistics.total,
      icon: Building2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Active Institutes',
      value: statistics.active,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Inactive Institutes',
      value: statistics.inactive,
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
    {
      title: 'Verified Domains',
      value: statistics.verifiedDomains,
      icon: Globe,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'Recently Created',
      value: statistics.recentlyCreated,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      subtitle: 'Last 30 days',
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      {stats.map((stat) => {
        const Icon = stat.icon
        return (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  {stat.subtitle && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {stat.subtitle}
                    </p>
                  )}
                </div>
                <div className={`p-2 rounded-full ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
```

## 📊 **Dashboard Activities**

### **Super Admin Dashboard Activities**

```typescript
// apps/frontend/src/components/dashboard/super-admin/RecentActivities.tsx
'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  Building2,
  UserPlus,
  Settings,
  Globe,
  CheckCircle,
  Clock,
  MoreHorizontal
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface Activity {
  id: string
  type: 'institute_created' | 'admin_assigned' | 'domain_verified' | 'institute_updated'
  title: string
  description: string
  timestamp: string
  user?: {
    name: string
    email: string
  }
  institute?: {
    name: string
    slug: string
  }
  metadata?: Record<string, any>
}

export function RecentActivities() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // TODO: Fetch actual activities from API
    // For now, using mock data
    const mockActivities: Activity[] = [
      {
        id: '1',
        type: 'institute_created',
        title: 'New Institute Created',
        description: 'Tech Academy was created with admin user assigned',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        user: {
          name: 'Super Admin',
          email: '<EMAIL>'
        },
        institute: {
          name: 'Tech Academy',
          slug: 'tech-academy'
        }
      },
      {
        id: '2',
        type: 'domain_verified',
        title: 'Domain Verified',
        description: 'Custom domain techacademy.com was verified',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        institute: {
          name: 'Tech Academy',
          slug: 'tech-academy'
        }
      },
      {
        id: '3',
        type: 'admin_assigned',
        title: 'Admin User Assigned',
        description: 'John Doe was assigned as institute admin',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
        user: {
          name: 'John Doe',
          email: '<EMAIL>'
        },
        institute: {
          name: 'Tech Academy',
          slug: 'tech-academy'
        }
      },
      {
        id: '4',
        type: 'institute_updated',
        title: 'Institute Updated',
        description: 'Business School profile information was updated',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
        institute: {
          name: 'Business School',
          slug: 'business-school'
        }
      }
    ]

    setTimeout(() => {
      setActivities(mockActivities)
      setIsLoading(false)
    }, 1000)
  }, [])

  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'institute_created':
        return <Building2 className="h-4 w-4" />
      case 'admin_assigned':
        return <UserPlus className="h-4 w-4" />
      case 'domain_verified':
        return <Globe className="h-4 w-4" />
      case 'institute_updated':
        return <Settings className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'institute_created':
        return 'text-blue-600 bg-blue-100'
      case 'admin_assigned':
        return 'text-green-600 bg-green-100'
      case 'domain_verified':
        return 'text-purple-600 bg-purple-100'
      case 'institute_updated':
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Recent Activities</CardTitle>
        <Button variant="outline" size="sm">
          View All
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-4">
              <div className={`p-2 rounded-full ${getActivityColor(activity.type)}`}>
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                  </p>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {activity.description}
                </p>
                <div className="flex items-center gap-2 mt-2">
                  {activity.institute && (
                    <Badge variant="outline" className="text-xs">
                      {activity.institute.name}
                    </Badge>
                  )}
                  {activity.user && (
                    <Badge variant="secondary" className="text-xs">
                      {activity.user.name}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
```

### **Institute Admin Dashboard Activities**

```typescript
// apps/frontend/src/components/dashboard/institute-admin/RecentActivities.tsx
'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  BookOpen,
  Users,
  GraduationCap,
  Settings,
  UserCheck,
  Clock
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface InstituteActivity {
  id: string
  type: 'course_created' | 'student_enrolled' | 'course_completed' | 'profile_updated' | 'user_registered'
  title: string
  description: string
  timestamp: string
  user?: {
    name: string
    email: string
  }
  course?: {
    name: string
    slug: string
  }
  metadata?: Record<string, any>
}

export function InstituteRecentActivities() {
  const [activities, setActivities] = useState<InstituteActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // TODO: Fetch actual activities from API
    // For now, using mock data
    const mockActivities: InstituteActivity[] = [
      {
        id: '1',
        type: 'student_enrolled',
        title: 'New Student Enrollment',
        description: 'Sarah Johnson enrolled in React Development Course',
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
        user: {
          name: 'Sarah Johnson',
          email: '<EMAIL>'
        },
        course: {
          name: 'React Development Course',
          slug: 'react-development'
        }
      },
      {
        id: '2',
        type: 'course_created',
        title: 'New Course Created',
        description: 'Advanced JavaScript course was published',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 1).toISOString(), // 1 hour ago
        course: {
          name: 'Advanced JavaScript',
          slug: 'advanced-javascript'
        }
      },
      {
        id: '3',
        type: 'course_completed',
        title: 'Course Completed',
        description: 'Mike Chen completed HTML & CSS Fundamentals',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
        user: {
          name: 'Mike Chen',
          email: '<EMAIL>'
        },
        course: {
          name: 'HTML & CSS Fundamentals',
          slug: 'html-css-fundamentals'
        }
      },
      {
        id: '4',
        type: 'user_registered',
        title: 'New User Registration',
        description: 'Alex Smith registered as a student',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
        user: {
          name: 'Alex Smith',
          email: '<EMAIL>'
        }
      }
    ]

    setTimeout(() => {
      setActivities(mockActivities)
      setIsLoading(false)
    }, 1000)
  }, [])

  const getActivityIcon = (type: InstituteActivity['type']) => {
    switch (type) {
      case 'course_created':
        return <BookOpen className="h-4 w-4" />
      case 'student_enrolled':
        return <UserCheck className="h-4 w-4" />
      case 'course_completed':
        return <GraduationCap className="h-4 w-4" />
      case 'profile_updated':
        return <Settings className="h-4 w-4" />
      case 'user_registered':
        return <Users className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getActivityColor = (type: InstituteActivity['type']) => {
    switch (type) {
      case 'course_created':
        return 'text-blue-600 bg-blue-100'
      case 'student_enrolled':
        return 'text-green-600 bg-green-100'
      case 'course_completed':
        return 'text-purple-600 bg-purple-100'
      case 'profile_updated':
        return 'text-orange-600 bg-orange-100'
      case 'user_registered':
        return 'text-indigo-600 bg-indigo-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Recent Activities</CardTitle>
        <Button variant="outline" size="sm">
          View All
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-4">
              <div className={`p-2 rounded-full ${getActivityColor(activity.type)}`}>
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                  </p>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {activity.description}
                </p>
                <div className="flex items-center gap-2 mt-2">
                  {activity.course && (
                    <Badge variant="outline" className="text-xs">
                      {activity.course.name}
                    </Badge>
                  )}
                  {activity.user && (
                    <Badge variant="secondary" className="text-xs">
                      {activity.user.name}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
```

## 📁 **Folder Structure**

```
apps/
├── api/
│   ├── src/
│   │   ├── collections/
│   │   │   └── Institutes.ts
│   │   ├── endpoints/
│   │   │   ├── institute-management.ts
│   │   │   └── institute-management-index.ts
│   │   └── middleware/
│   │       └── auth.ts
└── frontend/
    └── src/
        ├── app/
        │   └── super-admin/
        │       └── institute-management/
        │           └── page.tsx
        ├── components/
        │   ├── institute-management/
        │   │   ├── InstituteForm.tsx
        │   │   ├── InstitutesList.tsx
        │   │   ├── InstituteCards.tsx
        │   │   ├── InstituteFilters.tsx
        │   │   ├── InstituteStatistics.tsx
        │   │   └── InstituteActions.tsx
        │   └── dashboard/
        │       ├── super-admin/
        │       │   └── RecentActivities.tsx
        │       └── institute-admin/
        │           └── RecentActivities.tsx
        └── stores/
            └── super-admin/
                └── useInstituteManagementStore.ts
```

## 🚀 **Implementation Steps**

### **Phase 10.1: Database & API Setup**
1. **Create Institutes Collection**
   - Define collection schema with all required fields
   - Implement slug auto-generation hook
   - Add soft delete functionality
   - Set up proper access controls

2. **Create API Endpoints**
   - Implement CRUD operations for institutes
   - Add filtering, pagination, and search
   - Create statistics endpoint
   - Add domain verification endpoint

3. **Register Endpoints**
   - Add endpoints to Payload config
   - Test API functionality
   - Verify authentication middleware

### **Phase 10.2: Frontend Store & Components**
1. **Create Zustand Store**
   - Implement state management for institutes
   - Add CRUD operations
   - Include filtering and pagination
   - Add error handling and loading states

2. **Build Core Components**
   - Institute management page
   - Institute form (create/edit)
   - Institute list and card views
   - Filters and statistics components

3. **Add Navigation**
   - Update super admin sidebar
   - Add institute management menu item
   - Ensure proper routing

### **Phase 10.3: Dashboard Activities**
1. **Super Admin Dashboard**
   - Create recent activities component
   - Show institute-related activities
   - Include admin assignments and domain verifications

2. **Institute Admin Dashboard**
   - Create institute-specific activities
   - Show course and student activities
   - Filter activities by institute

### **Phase 10.4: Testing & Validation**
1. **API Testing**
   - Test all CRUD operations
   - Verify filtering and pagination
   - Test authentication and authorization

2. **Frontend Testing**
   - Test form validation
   - Verify state management
   - Test user interactions

3. **Integration Testing**
   - Test complete workflows
   - Verify data consistency
   - Test error scenarios

## 🔧 **Technical Implementation Notes**

### **Auto-Slug Generation**
```typescript
hooks: {
  beforeValidate: [
    ({ data }) => {
      if (data?.name && !data?.slug) {
        data.slug = data.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '')
      }
    },
  ],
},
```

### **Soft Delete Implementation**
```typescript
// In queries, always exclude soft deleted
where: {
  deletedAt: { exists: false }
}

// To soft delete
await payload.update({
  collection: 'institutes',
  id,
  data: {
    deletedAt: new Date(),
    isActive: false
  }
})
```

### **Location Integration**
```typescript
// Location fields reference existing location management system
// These should be populated from location dropdowns/selectors

// Example: Fetching location data for dropdowns
const fetchCountries = async () => {
  const response = await api.get('/api/location-management/countries')
  return response.data.docs
}

const fetchStates = async (countryId: string) => {
  const response = await api.get(`/api/location-management/states?countryId=${countryId}`)
  return response.data.docs
}

const fetchCities = async (stateId: string) => {
  const response = await api.get(`/api/location-management/cities?stateId=${stateId}`)
  return response.data.docs
}

const fetchDistricts = async (stateId: string) => {
  const response = await api.get(`/api/location-management/districts?stateId=${stateId}`)
  return response.data.docs
}

// In the form, these would be dropdown selectors instead of text inputs
// The form would store the selected IDs (countryId, stateId, cityId, districtId)
```

### **UUID Implementation**
```typescript
// Institute ID is auto-generated UUID
// Uses crypto.randomUUID() for unique identification
// Ensures global uniqueness across distributed systems

// Example UUID generation in collection
{
  name: 'id',
  type: 'text',
  defaultValue: () => {
    return require('crypto').randomUUID()
  },
  admin: {
    hidden: true,
  },
}
```

### **Domain Verification**
```typescript
// TODO: Implement actual domain verification
// - DNS TXT record verification
// - SSL certificate validation
// - Domain ownership confirmation
```

## 📋 **User Workflows**

### **Super Admin Workflow**
1. **Create Institute**
   - Fill institute information form
   - Add address details
   - Create admin user account
   - Set custom domain (optional)

2. **Manage Institutes**
   - View all institutes in list/card view
   - Filter by status, domain verification
   - Search by name, email, domain
   - Edit institute information
   - Verify custom domains
   - Soft delete institutes

3. **Monitor Activities**
   - View recent institute activities
   - Track admin assignments
   - Monitor domain verifications
   - Review institute updates

### **Institute Admin Workflow**
1. **Dashboard Overview**
   - View institute-specific statistics
   - Monitor recent activities
   - Track course and student activities

2. **Profile Management**
   - Update institute information
   - Manage contact details
   - Request domain verification

## 🎯 **Success Criteria**

### **Functional Requirements**
- ✅ Super Admin can create institutes with admin users
- ✅ Auto-slug generation from institute name
- ✅ Comprehensive filtering and search functionality
- ✅ Soft delete with proper data integrity
- ✅ Custom domain management with verification
- ✅ Dashboard activities for both user types
- ✅ Responsive design for all screen sizes

### **Technical Requirements**
- ✅ Follows existing project architectural patterns
- ✅ Uses Zustand for state management
- ✅ Implements Formik + Yup validation
- ✅ Includes proper error handling
- ✅ Toast notifications for user feedback
- ✅ Proper TypeScript typing
- ✅ Consistent UI/UX with existing components

### **Performance Requirements**
- ✅ Efficient pagination for large datasets
- ✅ Optimized API queries with proper indexing
- ✅ Lazy loading for components
- ✅ Debounced search functionality

## 🔄 **Integration Points**

### **With Existing Systems**
- **Location Management**: Address fields integration
- **Role Management**: Institute admin role assignment
- **User Management**: Admin user creation
- **Dashboard**: Activities integration

### **Future Phases**
- **Branch Management**: Institute-branch relationships
- **Course Management**: Institute-course associations
- **Student Management**: Institute-student enrollments
- **Billing System**: Institute-based billing

## 📝 **Notes**

- **Institute ID**: Uses UUID for global uniqueness and distributed system compatibility
- **Institute slugs**: Auto-generated from name and read-only to ensure consistency
- **Location Integration**: Uses reference IDs (countryId, stateId, cityId, districtId) to integrate with existing location management system
- **Address Fields**: Separated into individual fields for better data structure and location referencing
- **Soft delete**: Preserves data integrity by using deletedAt timestamp instead of hard deletion
- **Domain verification**: Placeholder implementation - requires DNS/SSL verification in production
- **Activities**: Currently mock data - implement real activity tracking system
- **Form validation**: Uses consistent Yup validation patterns across all forms
- **Error handling**: Follows project standards with proper error messages and toast notifications
- **Database indexes**: Added for performance on frequently queried fields (slug, active status, location IDs)
- **Location dropdowns**: Form should use dropdown selectors populated from location management API instead of text inputs

## 🎨 **UI/UX Design Specifications**

### **Institute View Modal Design**

#### **Layout Structure**
```
┌─────────────────────────────────────────────────────────────┐
│ Header: Institute Name + Status Badges + Action Buttons    │
├─────────────────────────────────────────────────────────────┤
│ Tab Navigation: Overview | Billing | Staff | Branches | Students │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Tab Content Area (Scrollable)                              │
│                                                             │
│ Overview Tab:                                               │
│ ┌─────────┬─────────┬─────────┬─────────┐                  │
│ │ Bills   │ Staff   │ Branches│ Students│ (Stats Cards)    │
│ └─────────┴─────────┴─────────┴─────────┘                  │
│                                                             │
│ ┌─────────────────┬─────────────────────┐                  │
│ │ Contact Info    │ Address Info        │                  │
│ └─────────────────┴─────────────────────┘                  │
│                                                             │
│ ┌─────────────────────────────────────────┐                │
│ │ Students by Branch Distribution         │                │
│ └─────────────────────────────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

#### **Color Scheme & Visual Hierarchy**
- **Primary**: Blue (#3B82F6) for institute branding
- **Success**: Green (#10B981) for verified/active states
- **Warning**: Orange (#F59E0B) for pending/unverified
- **Danger**: Red (#EF4444) for inactive/error states
- **Neutral**: Gray (#6B7280) for secondary information

#### **Typography Scale**
- **Modal Title**: 24px, font-bold
- **Tab Labels**: 14px, font-medium
- **Card Titles**: 18px, font-semibold
- **Stats Numbers**: 32px, font-bold
- **Body Text**: 14px, font-normal
- **Captions**: 12px, text-muted-foreground

### **Institute Form Modal Design**

#### **Multi-Step Form Layout (Create Mode)**
```
┌─────────────────────────────────────────────────────────────┐
│ Header: Title + Step Indicator (Step 1 of 3)               │
├─────────────────────────────────────────────────────────────┤
│ Progress Bar: ●━━━━━━━○━━━━━━━○                              │
│              Step 1   Step 2   Step 3                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Form Content (Card-based sections)                         │
│                                                             │
│ Step 1: Basic Information                                   │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📋 Basic Information                                    │ │
│ │ ┌─────────────┬─────────────┐                          │ │
│ │ │ Name*       │ Email       │                          │ │
│ │ └─────────────┴─────────────┘                          │ │
│ │ ┌─────────────┬─────────────┐                          │ │
│ │ │ Phone       │ Website     │                          │ │
│ │ └─────────────┴─────────────┘                          │ │
│ │ ┌─────────────────────────────┐                        │ │
│ │ │ Tagline                     │                        │ │
│ │ └─────────────────────────────┘                        │ │
│ │ ┌─────────────────────────────┐                        │ │
│ │ │ Custom Domain (Optional)    │                        │ │
│ │ └─────────────────────────────┘                        │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Actions: Cancel | Next: Address & Contact                  │
└─────────────────────────────────────────────────────────────┘
```

#### **Form Field Design Patterns**

**Input Field with Icon & Validation**
```
┌─────────────────────────────────────────────────────────────┐
│ 📧 Email Address *                                          │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ <EMAIL>                                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ✓ Valid email format                                        │
└─────────────────────────────────────────────────────────────┘
```

**Dropdown with Search**
```
┌─────────────────────────────────────────────────────────────┐
│ 🌍 Country                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ United States                               ▼           │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**Info Cards with Context**
```
┌─────────────────────────────────────────────────────────────┐
│ ℹ️  Admin Account Setup                                     │
│ This user will have full administrative access to manage   │
│ the institute, including courses, students, and settings.  │
└─────────────────────────────────────────────────────────────┘
```

### **Institute List/Card View Design**

#### **List View Layout**
```
┌─────────────────────────────────────────────────────────────┐
│ 🏢 Tech Academy                    [Active] [Verified]     │
│    @tech-academy                                            │
│    📧 <EMAIL>      📞 +1-555-0123         │
│    📍 San Francisco, CA            🌐 techacademy.com      │
│    👥 450 students • 3 branches • $125K revenue           │
│                                    [View] [Edit] [Delete]  │
├─────────────────────────────────────────────────────────────┤
│ 🏢 Business School                 [Active] [Unverified]   │
│    @business-school                                         │
│    📧 <EMAIL>          📞 +1-555-0456         │
│    📍 New York, NY                 🌐 bizschool.edu        │
│    👥 320 students • 2 branches • $89K revenue            │
│                                    [View] [Edit] [Delete]  │
└─────────────────────────────────────────────────────────────┘
```

#### **Card View Layout**
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ 🏢 Tech Academy     │ 🏢 Business School  │ 🏢 Art Institute    │
│ [Active] [Verified] │ [Active] [Pending]  │ [Inactive]          │
│                     │                     │                     │
│ 👥 450 students     │ 👥 320 students     │ 👥 180 students     │
│ 🏢 3 branches       │ 🏢 2 branches       │ 🏢 1 branch         │
│ 💰 $125K revenue    │ 💰 $89K revenue     │ 💰 $45K revenue     │
│                     │                     │                     │
│ 📧 contact@tech...  │ 📧 admin@biz...     │ 📧 info@art...      │
│ 🌐 techacademy.com  │ 🌐 bizschool.edu    │ 🌐 artinstitute.org │
│                     │                     │                     │
│ [View] [Edit] [⋮]   │ [View] [Edit] [⋮]   │ [View] [Edit] [⋮]   │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### **Interactive Elements**

#### **Status Badges**
- **Active**: Green background, white text, checkmark icon
- **Inactive**: Gray background, white text, X icon
- **Verified**: Blue background, white text, shield icon
- **Pending**: Orange background, white text, clock icon

#### **Action Buttons**
- **Primary**: Blue background, white text (Create, Save)
- **Secondary**: White background, gray border, gray text (Cancel, Edit)
- **Danger**: Red background, white text (Delete)
- **Icon Buttons**: Gray background, hover effects

#### **Loading States**
- **Skeleton Cards**: Animated gray placeholders
- **Spinner**: Blue rotating icon with "Loading..." text
- **Button Loading**: Disabled state with spinner icon

#### **Empty States**
- **No Institutes**: Building icon, "No institutes found" message, "Create Institute" CTA
- **No Search Results**: Search icon, "No results for 'query'" message, "Clear filters" option

### **Responsive Design Breakpoints**

#### **Desktop (1024px+)**
- Full modal width (max-width: 6xl)
- 4-column stats grid
- 2-column form layout
- Side-by-side cards

#### **Tablet (768px - 1023px)**
- Reduced modal width (max-width: 4xl)
- 2-column stats grid
- Single-column form layout
- Stacked cards

#### **Mobile (< 768px)**
- Full-screen modal
- Single-column stats grid
- Simplified form steps
- Single-column cards
- Bottom sheet for actions

### **Accessibility Features**

#### **Keyboard Navigation**
- Tab order: Header → Tabs → Content → Actions
- Enter/Space for button activation
- Arrow keys for tab navigation
- Escape to close modals

#### **Screen Reader Support**
- Proper ARIA labels for all interactive elements
- Role attributes for custom components
- Live regions for dynamic content updates
- Descriptive alt text for icons

#### **Color Contrast**
- WCAG AA compliance (4.5:1 ratio minimum)
- High contrast mode support
- Color-blind friendly palette
- Focus indicators for all interactive elements

### **Animation & Transitions**

#### **Modal Animations**
- Fade in/out: 200ms ease-in-out
- Scale transform: 0.95 to 1.0
- Backdrop blur effect

#### **Form Transitions**
- Step transitions: 300ms slide animation
- Field validation: 150ms color transition
- Loading states: Smooth spinner rotation

#### **Hover Effects**
- Button hover: 150ms background color transition
- Card hover: 200ms shadow elevation
- Icon hover: 100ms scale transform (1.05x)
