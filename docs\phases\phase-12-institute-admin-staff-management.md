# 📋 Phase 12: Institute Admin Staff Management

## 🎯 Overview
Simple staff management system for Institute Admins to manage staff members with role assignment from API-fetched roles. Clean CRUD operations with basic form handling and role-based filtering.

## 🏗️ Technical Stack
- **Frontend Framework**: Next.js with TypeScript
- **State Management**: Zustand for state management
- **Form Management**: Formik with simple form handling
- **Validation**: Yup with basic validation
- **UI Components**: Shadcn UI + Radix components
- **Styling**: TailwindCSS
- **Notifications**: Toast notifications (Sonner)
- **API Integration**: RESTful API endpoints with role fetching

## 🎭 Role-Based User Management

### Simple Approach
Single interface for managing staff members with roles fetched from API. No complex dynamic fields - just basic staff information with role assignment.

### Role Management
- **Roles Source**: Fetched from `/api/institute-admin/roles` endpoint
- **Dynamic Roles**: Support any roles returned from API
- **No Hardcoding**: No hardcoded role types or specific field requirements
- **Flexible**: Can handle new roles added through admin interface

### Basic Staff Fields
- **Personal Info**: firstName, lastName, email, phone
- **Authentication**: password (for creation)
- **Role Assignment**: legacyRole (selected from API roles)
- **Branch Assignment**: branch (selected from available branches)
- **Status**: isActive (active/inactive toggle)

## 🗂️ Simple File Structure

```
apps/frontend/src/
├── stores/institute/
│   └── useStaffStore.ts                    # Staff management store
├── components/institute-admin/staff-management/
│   ├── StaffList.tsx                       # Staff list with filtering
│   ├── StaffCreateForm.tsx                 # Simple staff creation form
│   ├── StaffEditForm.tsx                   # Staff edit form
│   ├── StaffCard.tsx                       # Staff card component
│   └── StaffFilters.tsx                    # Basic filters
├── app/admin/
│   └── staff/
│       └── page.tsx                        # Main staff management page
└── lib/validations/
    └── staffValidation.ts                  # Basic validation schemas
```

## 🔧 API Endpoints

### Staff Management Endpoints
```
GET    /api/institute-admin/staff                     # List staff with filtering
POST   /api/institute-admin/staff                     # Create staff member
GET    /api/institute-admin/staff/:id                 # Get staff member details
PATCH  /api/institute-admin/staff/:id                 # Update staff member
DELETE /api/institute-admin/staff/:id                 # Delete staff member
PATCH  /api/institute-admin/staff/:id/status          # Toggle status
```

### Role and Branch Endpoints
```
GET    /api/institute-admin/roles                     # Get available roles
GET    /api/institute-admin/branches                  # Get available branches
```

### Query Parameters
```
?role=any_role_from_api                               # Filter by any role from API
?branch_id=123                                        # Filter by branch
?status=active                                        # Filter by status (active/inactive)
?search=john                                          # Search by name/email
?page=1&limit=20                                      # Pagination
?sort=firstName&order=asc                             # Sorting
```

## 📊 Simple Data Model

### Staff Member Model
```typescript
interface StaffMember {
  // Basic fields only
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  legacyRole: string                  // Any role from API
  branch: {
    id: string
    name: string
  } | null
  isActive: boolean
  emailVerified: boolean
  lastLogin?: string
  createdAt: string
  updatedAt: string
}

### Role and Branch Types
```typescript
interface Role {
  id: string
  name: string
  description: string
  isActive: boolean
}

interface Branch {
  id: string
  name: string
  code: string
  isActive: boolean
}

interface StaffCreationData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password: string
  legacyRole: string
  branch_id?: string
  isActive: boolean
}
```

## 🎨 Simple UI Components

### Staff List Component (`StaffList.tsx`)
- **Role Filter Dropdown**: Filter by any role from API
- **Basic Table View**: Simple columns with essential data
- **Search**: Search by name/email
- **Basic Filters**: Role, branch, status filters
- **Pagination**: Standard pagination controls

### Simple Form Components

#### `StaffCreateForm.tsx`
- **Single Form**: Simple one-page form (no multi-step)
- **Role Dropdown**: Built-in dropdown populated from `/api/institute-admin/roles`
- **Basic Fields**: firstName, lastName, email, phone, password
- **Branch Selection**: Dropdown populated from `/api/institute-admin/branches`
- **Simple Validation**: Basic required field validation

```typescript
// Role dropdown implementation in create form
const StaffCreateForm = () => {
  const [roles, setRoles] = useState([])
  const [branches, setBranches] = useState([])

  useEffect(() => {
    // Fetch roles from API
    api.get('/api/institute-admin/roles').then(response => {
      setRoles(response.data)
    })

    // Fetch branches from API
    api.get('/api/institute-admin/branches').then(response => {
      setBranches(response.data)
    })
  }, [])

  return (
    <Form>
      {/* Basic fields */}
      <Input name="firstName" label="First Name" required />
      <Input name="lastName" label="Last Name" required />
      <Input name="email" label="Email" type="email" required />
      <Input name="phone" label="Phone" />
      <Input name="password" label="Password" type="password" required />

      {/* Role dropdown - no separate component needed */}
      <Select name="legacyRole" label="Role" required>
        {roles.map(role => (
          <SelectItem key={role.id} value={role.name}>
            {role.description || role.name}
          </SelectItem>
        ))}
      </Select>

      {/* Branch dropdown */}
      <Select name="branch_id" label="Branch">
        {branches.map(branch => (
          <SelectItem key={branch.id} value={branch.id}>
            {branch.name}
          </SelectItem>
        ))}
      </Select>

      <Button type="submit">Create Staff Member</Button>
    </Form>
  )
}
```

#### `StaffEditForm.tsx`
- **Role Display**: Show current role (editable with same dropdown)
- **Basic Fields**: Same fields as create form (password optional)
- **Simple Updates**: Update basic information only

## 🔄 Simple CRUD Operations

### Create Operations
1. **Basic Form**: Simple form with required fields
2. **API Call**: POST to `/api/institute-admin/staff`
3. **Success**: Toast notification and list refresh
4. **Error**: Display error message
5. **Loading**: Button loading state

### Read Operations
1. **List View**: Simple table with pagination
2. **Filtering**: Basic role and status filters
3. **Search**: Search by name/email
4. **API Call**: GET from `/api/institute-admin/staff`

### Update Operations
1. **Edit Form**: Same as create form with pre-filled data
2. **API Call**: PATCH to `/api/institute-admin/staff/:id`
3. **Success**: Toast notification and list refresh

### Delete Operations
1. **Soft Delete**: Set isActive to false
2. **Confirmation**: Simple confirmation dialog
3. **API Call**: DELETE to `/api/institute-admin/staff/:id`

## 🗃️ Simple Zustand Store Structure

### Staff Store (`useStaffStore.ts`)
```typescript
interface StaffStore {
  // State
  staffMembers: StaffMember[]
  selectedStaff: StaffMember | null
  availableBranches: Branch[]
  availableRoles: Role[]

  // Loading States
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean
  error: string | null

  // Pagination
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalDocs: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }

  // Basic Filters
  filters: {
    search: string
    role: string
    branch_id: string
    status: 'all' | 'active' | 'inactive'
  }

  // Actions
  fetchStaff: () => Promise<void>
  fetchAvailableRoles: () => Promise<void>
  fetchAvailableBranches: () => Promise<void>
  createStaff: (data: StaffCreationData) => Promise<void>
  updateStaff: (id: string, data: Partial<StaffCreationData>) => Promise<void>
  deleteStaff: (id: string) => Promise<void>
  toggleStaffStatus: (id: string) => Promise<void>
  setFilters: (filters: Partial<StaffStore['filters']>) => void
  clearError: () => void
}
```

### Key Store Functions
```typescript
// Fetch roles from API
const fetchAvailableRoles = async () => {
  try {
    const response = await api.get('/api/institute-admin/roles')
    set({ availableRoles: response.data })
  } catch (error) {
    console.error('Failed to fetch roles:', error)
  }
}

// Create staff with API role
const createStaff = async (data: StaffCreationData) => {
  set({ isCreating: true, error: null })
  try {
    const response = await api.post('/api/institute-admin/staff', data)
    if (response.success) {
      await fetchStaff() // Refresh list
      toast.success('Staff member created successfully')
    }
  } catch (error) {
    set({ error: error.message })
    toast.error('Failed to create staff member')
  } finally {
    set({ isCreating: false })
  }
}
```

## 📝 Simple Form Validation

### Basic Validation Schema
```typescript
const staffValidationSchema = Yup.object({
  firstName: Yup.string()
    .required('First name is required')
    .min(2, 'First name must be at least 2 characters'),
  lastName: Yup.string()
    .required('Last name is required')
    .min(2, 'Last name must be at least 2 characters'),
  email: Yup.string()
    .required('Email is required')
    .email('Please enter a valid email address'),
  phone: Yup.string()
    .matches(/^[+]?[\d\s\-\(\)]+$/, 'Please enter a valid phone number'),
  password: Yup.string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters'),
  legacyRole: Yup.string()
    .required('Role is required'),
  branch_id: Yup.string(),
  isActive: Yup.boolean().default(true)
})
```

## 🎯 Simple Implementation Phases

### Phase 1: Basic Setup (Week 1)
- [ ] Create staff API endpoints
- [ ] Create roles API endpoint (`/api/institute-admin/roles`)
- [ ] Create branches API endpoint (`/api/institute-admin/branches`)
- [ ] Set up basic staff store
- [ ] Implement basic CRUD operations

### Phase 2: Frontend Components (Week 2)
- [ ] Create simple staff list component
- [ ] Create basic staff creation form with role dropdown
- [ ] Create staff edit form
- [ ] Add role dropdown directly in forms (fetch from API)
- [ ] Add basic filtering and search

### Phase 3: Polish and Testing (Week 3)
- [ ] Add proper error handling
- [ ] Implement loading states
- [ ] Add toast notifications
- [ ] Test all CRUD operations
- [ ] Add basic validation

## 🔧 Technical Requirements

### Key Implementation Points
- [ ] **Role API Integration**: Fetch roles from `/api/institute-admin/roles`
- [ ] **No Hardcoded Roles**: Support any roles returned from API
- [ ] **Simple Form Design**: Single-page form without complex dynamic fields
- [ ] **Basic CRUD**: Create, read, update, delete with proper error handling
- [ ] **Role-based Filtering**: Filter staff list by any role from API
- [ ] **Branch Assignment**: Optional branch assignment for staff members
- [ ] **Status Management**: Active/inactive toggle functionality

### API Integration Pattern
```typescript
// Fetch roles directly in form component
const StaffForm = () => {
  const [roles, setRoles] = useState([])

  useEffect(() => {
    // Always fetch roles from API - no hardcoding
    api.get('/api/institute-admin/roles').then(response => {
      setRoles(response.data)
    })
  }, [])

  return (
    <Select name="legacyRole" label="Role" required>
      {roles.map(role => (
        <SelectItem key={role.id} value={role.name}>
          {role.description || role.name}
        </SelectItem>
      ))}
    </Select>
  )
}
```

### Success Criteria
- [ ] Staff can be created with any role from API
- [ ] Staff list shows all staff with role filtering
- [ ] Staff can be edited and deleted
- [ ] No hardcoded role dependencies
- [ ] Simple, maintainable codebase

---

*This simplified Phase 12 implementation provides a basic staff management system that fetches roles from API and supports simple CRUD operations without complex dynamic fields.*
