# Phase 13: Payment Gateway Management System

## 📋 Overview

This phase implements a comprehensive payment gateway management system that allows <PERSON> Admins to manage available payment gateways and Institute Admins to configure their own payment credentials. The system supports multiple payment gateways with flexible configuration and unified payment processing.

## 🎯 Objectives

1. **Super Admin Gateway Management**: Full CRUD operations for payment gateways
2. **Institute Admin Configuration**: Configure institute-specific payment credentials
3. **Unified Payment Processing**: Standardized payment service for all gateways
4. **Multi-Gateway Support**: Allow institutes to enable multiple gateways with selection logic
5. **Security & Compliance**: Secure credential storage and PCI compliance considerations

## 🏗️ System Architecture

### **Two-Tier Gateway Management**

```
Super Admin Level (Platform):
├── payment_gateways (Master gateway list)
│   ├── Gateway definitions and capabilities
│   ├── Supported currencies and countries
│   ├── Required/optional configuration fields
│   └── Documentation and integration details

Institute Admin Level (Institute-specific):
├── institute_payment_configs (Institute configurations)
│   ├── Institute-specific API credentials
│   ├── Gateway-specific settings
│   ├── Test/Live mode configurations
│   └── Enable/disable status per gateway
```

## 📊 Database Schema

### **Enhanced Payment Gateways Table**
The existing `payment_gateways` table will be enhanced to support the new requirements:

```sql
-- Enhanced payment_gateways table (Super Admin managed)
ALTER TABLE payment_gateways ADD COLUMN IF NOT EXISTS supported_currencies TEXT[] DEFAULT ARRAY['USD'];
ALTER TABLE payment_gateways ADD COLUMN IF NOT EXISTS supported_countries TEXT[] DEFAULT ARRAY['US'];
ALTER TABLE payment_gateways ADD COLUMN IF NOT EXISTS required_config_fields JSONB DEFAULT '{}';
ALTER TABLE payment_gateways ADD COLUMN IF NOT EXISTS optional_config_fields JSONB DEFAULT '{}';
ALTER TABLE payment_gateways ADD COLUMN IF NOT EXISTS documentation_url VARCHAR(500);
ALTER TABLE payment_gateways ADD COLUMN IF NOT EXISTS api_version VARCHAR(20);
ALTER TABLE payment_gateways ADD COLUMN IF NOT EXISTS webhook_support BOOLEAN DEFAULT true;
ALTER TABLE payment_gateways ADD COLUMN IF NOT EXISTS logo_url VARCHAR(500);
ALTER TABLE payment_gateways ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT false;
```

### **Institute Payment Configurations Table**
```sql
-- Institute Payment Configurations (already exists in schema)
CREATE TABLE IF NOT EXISTS institute_payment_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institute_id UUID REFERENCES institutes(id) ON DELETE CASCADE,
    payment_gateway_id UUID REFERENCES payment_gateways(id),
    
    -- Institute-specific configuration
    config_data JSONB NOT NULL, -- {"api_key": "sk_live_...", "secret_key": "...", "webhook_secret": "..."}
    
    -- Settings
    is_enabled BOOLEAN DEFAULT false,
    is_test_mode BOOLEAN DEFAULT true,
    priority_order INTEGER DEFAULT 0, -- For gateway selection priority
    
    -- Status
    status VARCHAR(20) DEFAULT 'inactive', -- 'inactive', 'testing', 'active', 'suspended'
    last_tested TIMESTAMP,
    test_result JSONB, -- Test transaction results
    
    -- Metadata
    configured_by UUID REFERENCES users(id), -- Institute Admin who configured
    configured_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(institute_id, payment_gateway_id)
);
```

## 🔧 Backend Implementation

### **1. Super Admin Gateway Management**

#### **Enhanced PaymentGateways Collection**
**File**: `apps/api/src/collections/PaymentGateways.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const PaymentGateways: CollectionConfig = {
  slug: 'payment-gateways',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'provider', 'supportedCurrencies', 'isActive'],
    group: 'Payment Management',
  },
  access: {
    read: () => true, // All users can read available gateways
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      admin: {
        description: 'Display name for the payment gateway'
      }
    },
    {
      name: 'provider',
      type: 'select',
      required: true,
      options: [
        { label: 'Razorpay', value: 'razorpay' },
        { label: 'Stripe', value: 'stripe' },
        { label: 'PayPal', value: 'paypal' },
        { label: 'Paytm', value: 'paytm' },
        { label: 'PhonePe', value: 'phonepe' },
        { label: 'Google Pay', value: 'googlepay' },
        { label: 'Bank Transfer', value: 'bank_transfer' },
      ],
      index: true,
    },
    {
      name: 'supportedCurrencies',
      type: 'array',
      required: true,
      minRows: 1,
      fields: [
        {
          name: 'currency',
          type: 'select',
          required: true,
          options: [
            { label: 'Indian Rupee (INR)', value: 'INR' },
            { label: 'US Dollar (USD)', value: 'USD' },
            { label: 'Euro (EUR)', value: 'EUR' },
            { label: 'British Pound (GBP)', value: 'GBP' },
          ]
        }
      ]
    },
    {
      name: 'supportedMethods',
      type: 'array',
      fields: [
        {
          name: 'method',
          type: 'select',
          required: true,
          options: [
            { label: 'Credit Card', value: 'credit_card' },
            { label: 'Debit Card', value: 'debit_card' },
            { label: 'UPI', value: 'upi' },
            { label: 'Net Banking', value: 'net_banking' },
            { label: 'Digital Wallet', value: 'wallet' },
            { label: 'Bank Transfer', value: 'bank_transfer' },
          ]
        }
      ]
    },
    {
      name: 'requiredConfigFields',
      type: 'json',
      admin: {
        description: 'JSON schema defining required configuration fields for institutes'
      }
    },
    {
      name: 'optionalConfigFields',
      type: 'json',
      admin: {
        description: 'JSON schema defining optional configuration fields for institutes'
      }
    },
    {
      name: 'fees',
      type: 'group',
      fields: [
        {
          name: 'transactionFeeType',
          type: 'select',
          required: true,
          options: [
            { label: 'Percentage', value: 'percentage' },
            { label: 'Fixed Amount', value: 'fixed' },
            { label: 'Both', value: 'both' },
          ]
        },
        {
          name: 'transactionFeePercentage',
          type: 'number',
          admin: {
            condition: (data) => data.transactionFeeType === 'percentage' || data.transactionFeeType === 'both'
          }
        },
        {
          name: 'transactionFeeFixed',
          type: 'number',
          admin: {
            condition: (data) => data.transactionFeeType === 'fixed' || data.transactionFeeType === 'both'
          }
        }
      ]
    },
    {
      name: 'documentationUrl',
      type: 'text',
      admin: {
        description: 'Link to gateway integration documentation'
      }
    },
    {
      name: 'logoUrl',
      type: 'text',
      admin: {
        description: 'URL to gateway logo image'
      }
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Show as featured gateway in institute selection'
      }
    },
    {
      name: 'displayOrder',
      type: 'number',
      admin: {
        description: 'Order in which gateway appears in selection (lower numbers first)'
      }
    }
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Set default required config fields based on provider
        if (!data.requiredConfigFields) {
          const defaultConfigs = {
            stripe: {
              publishable_key: 'string',
              secret_key: 'string',
              webhook_secret: 'string'
            },
            razorpay: {
              key_id: 'string',
              key_secret: 'string',
              webhook_secret: 'string'
            },
            paypal: {
              client_id: 'string',
              client_secret: 'string',
              webhook_id: 'string'
            }
          }
          data.requiredConfigFields = defaultConfigs[data.provider] || {}
        }
        return data
      }
    ]
  }
}

export default PaymentGateways
```

### **2. Institute Payment Configurations Collection**
**File**: `apps/api/src/collections/InstitutePaymentConfigs.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isInstituteAdmin, isAdmin } from '../access/index'

const InstitutePaymentConfigs: CollectionConfig = {
  slug: 'institute-payment-configs',
  admin: {
    useAsTitle: 'gateway.name',
    defaultColumns: ['gateway', 'institute', 'isEnabled', 'status'],
    group: 'Payment Management',
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.role === 'super_admin') return true
      if (user?.role === 'institute_admin') {
        return {
          institute: {
            equals: user.institute
          }
        }
      }
      return false
    },
    create: isInstituteAdmin,
    update: ({ req: { user } }) => {
      if (user?.role === 'super_admin') return true
      if (user?.role === 'institute_admin') {
        return {
          institute: {
            equals: user.institute
          }
        }
      }
      return false
    },
    delete: isAdmin,
  },
  fields: [
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      admin: {
        condition: (data, siblingData, { user }) => user?.role === 'super_admin'
      }
    },
    {
      name: 'gateway',
      type: 'relationship',
      relationTo: 'payment-gateways',
      required: true,
      filterOptions: {
        isActive: {
          equals: true
        }
      }
    },
    {
      name: 'configData',
      type: 'json',
      required: true,
      admin: {
        description: 'Gateway-specific configuration (API keys, secrets, etc.)'
      }
    },
    {
      name: 'isEnabled',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'isTestMode',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'priorityOrder',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Priority for gateway selection (lower numbers = higher priority)'
      }
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'inactive',
      options: [
        { label: 'Inactive', value: 'inactive' },
        { label: 'Testing', value: 'testing' },
        { label: 'Active', value: 'active' },
        { label: 'Suspended', value: 'suspended' },
      ]
    },
    {
      name: 'lastTested',
      type: 'date',
      admin: {
        readOnly: true
      }
    },
    {
      name: 'testResult',
      type: 'json',
      admin: {
        readOnly: true,
        description: 'Results from last gateway test'
      }
    },
    {
      name: 'configuredBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true
      }
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, req }) => {
        if (req.user?.role === 'institute_admin' && !data.institute) {
          data.institute = req.user.institute
        }
        if (!data.configuredBy) {
          data.configuredBy = req.user?.id
        }
        return data
      }
    ]
  }
}

export default InstitutePaymentConfigs
```

### **3. API Endpoints for Gateway Management**

#### **Super Admin Gateway Endpoints**
**File**: `apps/api/src/endpoints/super-admin/payment-gateways.ts`

```typescript
import { Endpoint } from 'payload/config'

const paymentGatewayEndpoints: Endpoint[] = [
  // Get all payment gateways (Super Admin)
  {
    path: '/super-admin/payment-gateways',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { user } = req

        if (user?.role !== 'super_admin') {
          return res.status(403).json({ error: 'Access denied' })
        }

        const gateways = await req.payload.find({
          collection: 'payment-gateways',
          sort: 'displayOrder',
          limit: 100
        })

        res.json({
          success: true,
          gateways: gateways.docs,
          total: gateways.totalDocs
        })
      } catch (error) {
        console.error('Gateway fetch error:', error)
        res.status(500).json({ error: 'Internal server error' })
      }
    }
  },

  // Create payment gateway (Super Admin)
  {
    path: '/super-admin/payment-gateways',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { user } = req

        if (user?.role !== 'super_admin') {
          return res.status(403).json({ error: 'Access denied' })
        }

        const gatewayData = req.body

        const gateway = await req.payload.create({
          collection: 'payment-gateways',
          data: gatewayData
        })

        res.json({
          success: true,
          gateway,
          message: 'Payment gateway created successfully'
        })
      } catch (error) {
        console.error('Gateway creation error:', error)
        res.status(500).json({ error: 'Failed to create payment gateway' })
      }
    }
  },

  // Update payment gateway (Super Admin)
  {
    path: '/super-admin/payment-gateways/:id',
    method: 'patch',
    handler: async (req, res) => {
      try {
        const { user } = req
        const { id } = req.params

        if (user?.role !== 'super_admin') {
          return res.status(403).json({ error: 'Access denied' })
        }

        const gateway = await req.payload.update({
          collection: 'payment-gateways',
          id,
          data: req.body
        })

        res.json({
          success: true,
          gateway,
          message: 'Payment gateway updated successfully'
        })
      } catch (error) {
        console.error('Gateway update error:', error)
        res.status(500).json({ error: 'Failed to update payment gateway' })
      }
    }
  },

  // Delete payment gateway (Super Admin)
  {
    path: '/super-admin/payment-gateways/:id',
    method: 'delete',
    handler: async (req, res) => {
      try {
        const { user } = req
        const { id } = req.params

        if (user?.role !== 'super_admin') {
          return res.status(403).json({ error: 'Access denied' })
        }

        // Check if gateway is being used by any institute
        const configs = await req.payload.find({
          collection: 'institute-payment-configs',
          where: {
            gateway: {
              equals: id
            }
          },
          limit: 1
        })

        if (configs.totalDocs > 0) {
          return res.status(400).json({
            error: 'Cannot delete gateway that is being used by institutes'
          })
        }

        await req.payload.delete({
          collection: 'payment-gateways',
          id
        })

        res.json({
          success: true,
          message: 'Payment gateway deleted successfully'
        })
      } catch (error) {
        console.error('Gateway deletion error:', error)
        res.status(500).json({ error: 'Failed to delete payment gateway' })
      }
    }
  }
]

export default paymentGatewayEndpoints
```

#### **Institute Admin Gateway Configuration Endpoints**
**File**: `apps/api/src/endpoints/institute-admin/payment-configs.ts`

```typescript
import { Endpoint } from 'payload/config'

const institutePaymentConfigEndpoints: Endpoint[] = [
  // Get available gateways for institute
  {
    path: '/institute-admin/payment-gateways/available',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { user } = req

        if (user?.role !== 'institute_admin') {
          return res.status(403).json({ error: 'Access denied' })
        }

        const gateways = await req.payload.find({
          collection: 'payment-gateways',
          where: {
            isActive: {
              equals: true
            }
          },
          sort: 'displayOrder'
        })

        res.json({
          success: true,
          gateways: gateways.docs
        })
      } catch (error) {
        console.error('Available gateways fetch error:', error)
        res.status(500).json({ error: 'Internal server error' })
      }
    }
  },

  // Get institute's configured gateways
  {
    path: '/institute-admin/payment-configs',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { user } = req

        if (user?.role !== 'institute_admin') {
          return res.status(403).json({ error: 'Access denied' })
        }

        const configs = await req.payload.find({
          collection: 'institute-payment-configs',
          where: {
            institute: {
              equals: user.institute
            }
          },
          populate: ['gateway']
        })

        res.json({
          success: true,
          configs: configs.docs
        })
      } catch (error) {
        console.error('Payment configs fetch error:', error)
        res.status(500).json({ error: 'Internal server error' })
      }
    }
  },

  // Configure payment gateway for institute
  {
    path: '/institute-admin/payment-configs',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { user } = req

        if (user?.role !== 'institute_admin') {
          return res.status(403).json({ error: 'Access denied' })
        }

        const { gatewayId, configData, isTestMode = true } = req.body

        // Check if gateway exists and is active
        const gateway = await req.payload.findByID({
          collection: 'payment-gateways',
          id: gatewayId
        })

        if (!gateway || !gateway.isActive) {
          return res.status(400).json({
            error: 'Invalid or inactive payment gateway'
          })
        }

        // Check if configuration already exists
        const existingConfig = await req.payload.find({
          collection: 'institute-payment-configs',
          where: {
            and: [
              {
                institute: {
                  equals: user.institute
                }
              },
              {
                gateway: {
                  equals: gatewayId
                }
              }
            ]
          }
        })

        let config
        if (existingConfig.totalDocs > 0) {
          // Update existing configuration
          config = await req.payload.update({
            collection: 'institute-payment-configs',
            id: existingConfig.docs[0].id,
            data: {
              configData,
              isTestMode,
              status: 'testing',
              configuredBy: user.id
            }
          })
        } else {
          // Create new configuration
          config = await req.payload.create({
            collection: 'institute-payment-configs',
            data: {
              institute: user.institute,
              gateway: gatewayId,
              configData,
              isTestMode,
              status: 'testing',
              configuredBy: user.id
            }
          })
        }

        res.json({
          success: true,
          config,
          message: 'Payment gateway configured successfully'
        })
      } catch (error) {
        console.error('Payment config creation error:', error)
        res.status(500).json({ error: 'Failed to configure payment gateway' })
      }
    }
  },

  // Test payment gateway configuration
  {
    path: '/institute-admin/payment-configs/:id/test',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { user } = req
        const { id } = req.params

        if (user?.role !== 'institute_admin') {
          return res.status(403).json({ error: 'Access denied' })
        }

        const config = await req.payload.findByID({
          collection: 'institute-payment-configs',
          id,
          populate: ['gateway']
        })

        if (!config || config.institute !== user.institute) {
          return res.status(404).json({ error: 'Configuration not found' })
        }

        // Perform gateway test (implementation depends on gateway)
        const testResult = await testGatewayConnection(config)

        // Update configuration with test results
        await req.payload.update({
          collection: 'institute-payment-configs',
          id,
          data: {
            lastTested: new Date(),
            testResult,
            status: testResult.success ? 'testing' : 'inactive'
          }
        })

        res.json({
          success: true,
          testResult,
          message: testResult.success ? 'Gateway test successful' : 'Gateway test failed'
        })
      } catch (error) {
        console.error('Gateway test error:', error)
        res.status(500).json({ error: 'Gateway test failed' })
      }
    }
  },

  // Enable/disable payment gateway
  {
    path: '/institute-admin/payment-configs/:id/toggle',
    method: 'patch',
    handler: async (req, res) => {
      try {
        const { user } = req
        const { id } = req.params
        const { isEnabled } = req.body

        if (user?.role !== 'institute_admin') {
          return res.status(403).json({ error: 'Access denied' })
        }

        const config = await req.payload.findByID({
          collection: 'institute-payment-configs',
          id
        })

        if (!config || config.institute !== user.institute) {
          return res.status(404).json({ error: 'Configuration not found' })
        }

        const updatedConfig = await req.payload.update({
          collection: 'institute-payment-configs',
          id,
          data: {
            isEnabled,
            status: isEnabled ? 'active' : 'inactive'
          }
        })

        res.json({
          success: true,
          config: updatedConfig,
          message: `Gateway ${isEnabled ? 'enabled' : 'disabled'} successfully`
        })
      } catch (error) {
        console.error('Gateway toggle error:', error)
        res.status(500).json({ error: 'Failed to update gateway status' })
      }
    }
  }
]

// Helper function to test gateway connection
const testGatewayConnection = async (config: any) => {
  try {
    // Implementation depends on specific gateway
    // This is a placeholder for actual gateway testing logic
    const { gateway, configData } = config

    switch (gateway.provider) {
      case 'stripe':
        return await testStripeConnection(configData)
      case 'razorpay':
        return await testRazorpayConnection(configData)
      case 'paypal':
        return await testPayPalConnection(configData)
      default:
        return {
          success: false,
          error: 'Gateway testing not implemented',
          timestamp: new Date()
        }
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date()
    }
  }
}

// Gateway-specific test functions (to be implemented)
const testStripeConnection = async (configData: any) => {
  // Implement Stripe connection test
  return { success: true, message: 'Stripe connection successful', timestamp: new Date() }
}

const testRazorpayConnection = async (configData: any) => {
  // Implement Razorpay connection test
  return { success: true, message: 'Razorpay connection successful', timestamp: new Date() }
}

const testPayPalConnection = async (configData: any) => {
  // Implement PayPal connection test
  return { success: true, message: 'PayPal connection successful', timestamp: new Date() }
}

export default institutePaymentConfigEndpoints
```

## 🔄 Unified Payment Service

### **Payment Service Architecture**
**File**: `apps/api/src/services/PaymentService.ts`

```typescript
import { PaymentGateway, InstitutePaymentConfig } from '../payload-types'

export interface PaymentIntent {
  id: string
  amount: number
  currency: string
  gatewayProvider: string
  gatewayPaymentId: string
  clientSecret?: string
  redirectUrl?: string
  metadata: Record<string, any>
}

export interface PaymentResult {
  success: boolean
  transactionId: string
  gatewayResponse: Record<string, any>
  amount: number
  currency: string
  paymentMethod: string
  error?: string
}

export class PaymentService {
  private gateways: Map<string, any> = new Map()

  constructor() {
    this.initializeGateways()
  }

  private initializeGateways() {
    // Initialize gateway SDKs
    // This would be done based on available gateways
  }

  /**
   * Create payment intent for a specific institute and gateway
   */
  async createPaymentIntent(
    instituteConfig: InstitutePaymentConfig,
    amount: number,
    currency: string,
    metadata: Record<string, any> = {}
  ): Promise<PaymentIntent> {
    const { gateway, configData } = instituteConfig

    switch (gateway.provider) {
      case 'stripe':
        return this.createStripePaymentIntent(configData, amount, currency, metadata)
      case 'razorpay':
        return this.createRazorpayPaymentIntent(configData, amount, currency, metadata)
      case 'paypal':
        return this.createPayPalPaymentIntent(configData, amount, currency, metadata)
      default:
        throw new Error(`Unsupported payment gateway: ${gateway.provider}`)
    }
  }

  /**
   * Process payment confirmation from webhook
   */
  async processPaymentConfirmation(
    gatewayProvider: string,
    webhookData: any,
    signature: string,
    configData: any
  ): Promise<PaymentResult> {
    switch (gatewayProvider) {
      case 'stripe':
        return this.processStripeWebhook(webhookData, signature, configData)
      case 'razorpay':
        return this.processRazorpayWebhook(webhookData, signature, configData)
      case 'paypal':
        return this.processPayPalWebhook(webhookData, signature, configData)
      default:
        throw new Error(`Unsupported payment gateway: ${gatewayProvider}`)
    }
  }

  /**
   * Get enabled gateways for an institute with selection priority
   */
  async getInstituteGateways(instituteId: string, currency?: string): Promise<InstitutePaymentConfig[]> {
    // This would be implemented to fetch from database
    // with proper filtering and sorting by priority
    return []
  }

  /**
   * Select best gateway for payment based on strategy
   */
  async selectPaymentGateway(
    instituteId: string,
    amount: number,
    currency: string,
    userPreference?: string
  ): Promise<InstitutePaymentConfig | null> {
    const availableGateways = await this.getInstituteGateways(instituteId, currency)

    if (availableGateways.length === 0) {
      return null
    }

    // Strategy 1: User preference (if provided and available)
    if (userPreference) {
      const preferredGateway = availableGateways.find(
        config => config.gateway.id === userPreference
      )
      if (preferredGateway) {
        return preferredGateway
      }
    }

    // Strategy 2: Priority order (lowest number = highest priority)
    const sortedByPriority = availableGateways.sort((a, b) =>
      (a.priorityOrder || 0) - (b.priorityOrder || 0)
    )

    return sortedByPriority[0]
  }

  // Gateway-specific implementations
  private async createStripePaymentIntent(
    configData: any,
    amount: number,
    currency: string,
    metadata: Record<string, any>
  ): Promise<PaymentIntent> {
    // Stripe implementation
    const stripe = require('stripe')(configData.secret_key)

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Stripe expects cents
      currency: currency.toLowerCase(),
      metadata,
      automatic_payment_methods: {
        enabled: true,
      },
    })

    return {
      id: paymentIntent.id,
      amount,
      currency,
      gatewayProvider: 'stripe',
      gatewayPaymentId: paymentIntent.id,
      clientSecret: paymentIntent.client_secret,
      metadata
    }
  }

  private async createRazorpayPaymentIntent(
    configData: any,
    amount: number,
    currency: string,
    metadata: Record<string, any>
  ): Promise<PaymentIntent> {
    // Razorpay implementation
    const Razorpay = require('razorpay')
    const razorpay = new Razorpay({
      key_id: configData.key_id,
      key_secret: configData.key_secret,
    })

    const order = await razorpay.orders.create({
      amount: Math.round(amount * 100), // Razorpay expects paise
      currency: currency.toUpperCase(),
      notes: metadata,
    })

    return {
      id: order.id,
      amount,
      currency,
      gatewayProvider: 'razorpay',
      gatewayPaymentId: order.id,
      metadata
    }
  }

  private async createPayPalPaymentIntent(
    configData: any,
    amount: number,
    currency: string,
    metadata: Record<string, any>
  ): Promise<PaymentIntent> {
    // PayPal implementation
    // This would use PayPal SDK to create payment
    return {
      id: `paypal_${Date.now()}`,
      amount,
      currency,
      gatewayProvider: 'paypal',
      gatewayPaymentId: `paypal_${Date.now()}`,
      redirectUrl: 'https://paypal.com/checkout/...',
      metadata
    }
  }

  private async processStripeWebhook(
    webhookData: any,
    signature: string,
    configData: any
  ): Promise<PaymentResult> {
    // Stripe webhook processing
    const stripe = require('stripe')(configData.secret_key)

    try {
      const event = stripe.webhooks.constructEvent(
        webhookData,
        signature,
        configData.webhook_secret
      )

      if (event.type === 'payment_intent.succeeded') {
        const paymentIntent = event.data.object
        return {
          success: true,
          transactionId: paymentIntent.id,
          gatewayResponse: paymentIntent,
          amount: paymentIntent.amount / 100,
          currency: paymentIntent.currency.toUpperCase(),
          paymentMethod: paymentIntent.payment_method_types[0] || 'card'
        }
      }

      return {
        success: false,
        transactionId: '',
        gatewayResponse: event,
        amount: 0,
        currency: '',
        paymentMethod: '',
        error: 'Unhandled webhook event'
      }
    } catch (error) {
      throw new Error(`Stripe webhook verification failed: ${error.message}`)
    }
  }

  private async processRazorpayWebhook(
    webhookData: any,
    signature: string,
    configData: any
  ): Promise<PaymentResult> {
    // Razorpay webhook processing
    const crypto = require('crypto')

    const expectedSignature = crypto
      .createHmac('sha256', configData.webhook_secret)
      .update(JSON.stringify(webhookData))
      .digest('hex')

    if (signature !== expectedSignature) {
      throw new Error('Razorpay webhook verification failed')
    }

    if (webhookData.event === 'payment.captured') {
      const payment = webhookData.payload.payment.entity
      return {
        success: true,
        transactionId: payment.id,
        gatewayResponse: payment,
        amount: payment.amount / 100,
        currency: payment.currency.toUpperCase(),
        paymentMethod: payment.method
      }
    }

    return {
      success: false,
      transactionId: '',
      gatewayResponse: webhookData,
      amount: 0,
      currency: '',
      paymentMethod: '',
      error: 'Unhandled webhook event'
    }
  }

  private async processPayPalWebhook(
    webhookData: any,
    signature: string,
    configData: any
  ): Promise<PaymentResult> {
    // PayPal webhook processing
    // Implementation would depend on PayPal SDK
    return {
      success: true,
      transactionId: webhookData.id || '',
      gatewayResponse: webhookData,
      amount: parseFloat(webhookData.amount?.value || '0'),
      currency: webhookData.amount?.currency_code || '',
      paymentMethod: 'paypal'
    }
  }
}

export const paymentService = new PaymentService()
```

## 🎨 Frontend Implementation

### **Gateway Selection Strategy**

Based on user experience best practices, I recommend the following **multi-gateway selection strategy**:

1. **Primary Strategy**: Allow institutes to enable multiple gateways simultaneously
2. **User Choice**: Present gateway options to users during checkout
3. **Smart Defaults**: Use priority ordering with fallback options
4. **Currency-based**: Filter gateways by supported currencies
5. **Failure Handling**: Automatic fallback to next available gateway

### **Benefits of Multi-Gateway Approach**:
- **Redundancy**: If one gateway fails, others are available
- **User Preference**: Users can choose their preferred payment method
- **Geographic Optimization**: Different gateways work better in different regions
- **Cost Optimization**: Institutes can route payments to gateways with better rates
- **Compliance**: Some regions require local payment processors

### **Super Admin Gateway Management Store**
**File**: `apps/frontend/src/stores/super-admin/usePaymentGatewayStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

export interface PaymentGateway {
  id: string
  name: string
  provider: string
  supportedCurrencies: Array<{ currency: string }>
  supportedMethods: Array<{ method: string }>
  requiredConfigFields: Record<string, string>
  optionalConfigFields: Record<string, string>
  fees: {
    transactionFeeType: 'percentage' | 'fixed' | 'both'
    transactionFeePercentage?: number
    transactionFeeFixed?: number
  }
  documentationUrl?: string
  logoUrl?: string
  isActive: boolean
  isFeatured: boolean
  displayOrder?: number
  createdAt: string
  updatedAt: string
}

interface PaymentGatewayState {
  gateways: PaymentGateway[]
  loading: boolean
  error: string | null

  // Actions
  fetchGateways: () => Promise<void>
  createGateway: (gatewayData: Partial<PaymentGateway>) => Promise<void>
  updateGateway: (id: string, gatewayData: Partial<PaymentGateway>) => Promise<void>
  deleteGateway: (id: string) => Promise<void>
  toggleGatewayStatus: (id: string, isActive: boolean) => Promise<void>
}

export const usePaymentGatewayStore = create<PaymentGatewayState>()(
  devtools(
    (set, get) => ({
      gateways: [],
      loading: false,
      error: null,

      fetchGateways: async () => {
        set({ loading: true, error: null })
        try {
          const response = await api.get('/super-admin/payment-gateways')
          set({
            gateways: response.data.gateways,
            loading: false
          })
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to fetch gateways',
            loading: false
          })
          toast.error('Failed to fetch payment gateways')
        }
      },

      createGateway: async (gatewayData) => {
        set({ loading: true, error: null })
        try {
          const response = await api.post('/super-admin/payment-gateways', gatewayData)

          set(state => ({
            gateways: [...state.gateways, response.data.gateway],
            loading: false
          }))

          toast.success('Payment gateway created successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to create gateway',
            loading: false
          })
          toast.error('Failed to create payment gateway')
          throw error
        }
      },

      updateGateway: async (id, gatewayData) => {
        set({ loading: true, error: null })
        try {
          const response = await api.patch(`/super-admin/payment-gateways/${id}`, gatewayData)

          set(state => ({
            gateways: state.gateways.map(gateway =>
              gateway.id === id ? response.data.gateway : gateway
            ),
            loading: false
          }))

          toast.success('Payment gateway updated successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to update gateway',
            loading: false
          })
          toast.error('Failed to update payment gateway')
          throw error
        }
      },

      deleteGateway: async (id) => {
        set({ loading: true, error: null })
        try {
          await api.delete(`/super-admin/payment-gateways/${id}`)

          set(state => ({
            gateways: state.gateways.filter(gateway => gateway.id !== id),
            loading: false
          }))

          toast.success('Payment gateway deleted successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to delete gateway',
            loading: false
          })
          toast.error('Failed to delete payment gateway')
          throw error
        }
      },

      toggleGatewayStatus: async (id, isActive) => {
        try {
          await get().updateGateway(id, { isActive })
        } catch (error) {
          // Error handling is done in updateGateway
        }
      }
    }),
    {
      name: 'payment-gateway-store'
    }
  )
)
```

### **Institute Admin Payment Configuration Store**
**File**: `apps/frontend/src/stores/institute-admin/usePaymentConfigStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

export interface AvailableGateway {
  id: string
  name: string
  provider: string
  supportedCurrencies: Array<{ currency: string }>
  supportedMethods: Array<{ method: string }>
  requiredConfigFields: Record<string, string>
  optionalConfigFields: Record<string, string>
  documentationUrl?: string
  logoUrl?: string
  isFeatured: boolean
}

export interface PaymentConfig {
  id: string
  gateway: AvailableGateway
  configData: Record<string, any>
  isEnabled: boolean
  isTestMode: boolean
  priorityOrder: number
  status: 'inactive' | 'testing' | 'active' | 'suspended'
  lastTested?: string
  testResult?: {
    success: boolean
    message: string
    timestamp: string
  }
  configuredBy: string
  createdAt: string
  updatedAt: string
}

interface PaymentConfigState {
  availableGateways: AvailableGateway[]
  configurations: PaymentConfig[]
  loading: boolean
  testingGateway: string | null
  error: string | null

  // Actions
  fetchAvailableGateways: () => Promise<void>
  fetchConfigurations: () => Promise<void>
  configureGateway: (gatewayId: string, configData: Record<string, any>, isTestMode?: boolean) => Promise<void>
  testGateway: (configId: string) => Promise<void>
  toggleGateway: (configId: string, isEnabled: boolean) => Promise<void>
  updatePriority: (configId: string, priorityOrder: number) => Promise<void>
}

export const usePaymentConfigStore = create<PaymentConfigState>()(
  devtools(
    (set, get) => ({
      availableGateways: [],
      configurations: [],
      loading: false,
      testingGateway: null,
      error: null,

      fetchAvailableGateways: async () => {
        set({ loading: true, error: null })
        try {
          const response = await api.get('/institute-admin/payment-gateways/available')
          set({
            availableGateways: response.data.gateways,
            loading: false
          })
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to fetch available gateways',
            loading: false
          })
          toast.error('Failed to fetch available payment gateways')
        }
      },

      fetchConfigurations: async () => {
        set({ loading: true, error: null })
        try {
          const response = await api.get('/institute-admin/payment-configs')
          set({
            configurations: response.data.configs,
            loading: false
          })
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to fetch configurations',
            loading: false
          })
          toast.error('Failed to fetch payment configurations')
        }
      },

      configureGateway: async (gatewayId, configData, isTestMode = true) => {
        set({ loading: true, error: null })
        try {
          const response = await api.post('/institute-admin/payment-configs', {
            gatewayId,
            configData,
            isTestMode
          })

          // Refresh configurations
          await get().fetchConfigurations()

          toast.success('Payment gateway configured successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to configure gateway',
            loading: false
          })
          toast.error('Failed to configure payment gateway')
          throw error
        }
      },

      testGateway: async (configId) => {
        set({ testingGateway: configId, error: null })
        try {
          const response = await api.post(`/institute-admin/payment-configs/${configId}/test`)

          // Update the specific configuration with test results
          set(state => ({
            configurations: state.configurations.map(config =>
              config.id === configId
                ? { ...config, testResult: response.data.testResult, lastTested: new Date().toISOString() }
                : config
            ),
            testingGateway: null
          }))

          if (response.data.testResult.success) {
            toast.success('Gateway test successful')
          } else {
            toast.error('Gateway test failed')
          }
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Gateway test failed',
            testingGateway: null
          })
          toast.error('Gateway test failed')
        }
      },

      toggleGateway: async (configId, isEnabled) => {
        set({ loading: true, error: null })
        try {
          const response = await api.patch(`/institute-admin/payment-configs/${configId}/toggle`, {
            isEnabled
          })

          set(state => ({
            configurations: state.configurations.map(config =>
              config.id === configId ? response.data.config : config
            ),
            loading: false
          }))

          toast.success(`Gateway ${isEnabled ? 'enabled' : 'disabled'} successfully`)
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to update gateway status',
            loading: false
          })
          toast.error('Failed to update gateway status')
        }
      },

      updatePriority: async (configId, priorityOrder) => {
        try {
          const response = await api.patch(`/institute-admin/payment-configs/${configId}`, {
            priorityOrder
          })

          set(state => ({
            configurations: state.configurations.map(config =>
              config.id === configId ? response.data.config : config
            )
          }))

          toast.success('Gateway priority updated successfully')
        } catch (error: any) {
          toast.error('Failed to update gateway priority')
        }
      }
    }),
    {
      name: 'payment-config-store'
    }
  )
)
```

### **Gateway Configuration Form Component**
**File**: `apps/frontend/src/components/institute-admin/payment/GatewayConfigForm.tsx`

```typescript
'use client'

import { useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { AvailableGateway } from '@/stores/institute-admin/usePaymentConfigStore'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ExternalLink, Eye, EyeOff } from 'lucide-react'

interface GatewayConfigFormProps {
  gateway: AvailableGateway
  isOpen: boolean
  onClose: () => void
  onSubmit: (configData: Record<string, any>, isTestMode: boolean) => Promise<void>
  existingConfig?: any
}

export default function GatewayConfigForm({
  gateway,
  isOpen,
  onClose,
  onSubmit,
  existingConfig
}: GatewayConfigFormProps) {
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Create validation schema based on required fields
  const validationSchema = Yup.object().shape(
    Object.keys(gateway.requiredConfigFields).reduce((schema, fieldKey) => {
      schema[fieldKey] = Yup.string().required(`${fieldKey} is required`)
      return schema
    }, {} as Record<string, any>)
  )

  // Create initial values
  const initialValues = {
    isTestMode: existingConfig?.isTestMode ?? true,
    ...Object.keys(gateway.requiredConfigFields).reduce((values, fieldKey) => {
      values[fieldKey] = existingConfig?.configData?.[fieldKey] || ''
      return values
    }, {} as Record<string, any>),
    ...Object.keys(gateway.optionalConfigFields || {}).reduce((values, fieldKey) => {
      values[fieldKey] = existingConfig?.configData?.[fieldKey] || ''
      return values
    }, {} as Record<string, any>)
  }

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      setIsSubmitting(true)
      try {
        const { isTestMode, ...configData } = values
        await onSubmit(configData, isTestMode)
        onClose()
      } catch (error) {
        // Error handled in store
      } finally {
        setIsSubmitting(false)
      }
    }
  })

  const toggleSecretVisibility = (fieldKey: string) => {
    setShowSecrets(prev => ({
      ...prev,
      [fieldKey]: !prev[fieldKey]
    }))
  }

  const isSecretField = (fieldKey: string) => {
    return fieldKey.toLowerCase().includes('secret') ||
           fieldKey.toLowerCase().includes('key') ||
           fieldKey.toLowerCase().includes('password')
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {gateway.logoUrl && (
              <img src={gateway.logoUrl} alt={gateway.name} className="w-6 h-6" />
            )}
            Configure {gateway.name}
          </DialogTitle>
          <DialogDescription>
            Set up your {gateway.name} payment gateway credentials and settings.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Gateway Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Gateway Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-muted-foreground">Provider</Label>
                  <p className="font-medium">{gateway.provider}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Supported Currencies</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {gateway.supportedCurrencies.map((curr, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {curr.currency}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {gateway.documentationUrl && (
                <div>
                  <a
                    href={gateway.documentationUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
                  >
                    <ExternalLink className="w-3 h-3" />
                    View Documentation
                  </a>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Configuration Fields */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Configuration</CardTitle>
              <CardDescription>
                Enter your {gateway.name} API credentials. These will be encrypted and stored securely.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Test Mode Toggle */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="isTestMode">Test Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Use sandbox/test environment for testing payments
                  </p>
                </div>
                <Switch
                  id="isTestMode"
                  checked={formik.values.isTestMode}
                  onCheckedChange={(checked) => formik.setFieldValue('isTestMode', checked)}
                />
              </div>

              {/* Required Fields */}
              <div className="space-y-4">
                <Label className="text-sm font-medium text-red-600">Required Fields</Label>
                {Object.entries(gateway.requiredConfigFields).map(([fieldKey, fieldType]) => (
                  <div key={fieldKey} className="space-y-2">
                    <Label htmlFor={fieldKey} className="capitalize">
                      {fieldKey.replace(/_/g, ' ')}
                    </Label>
                    <div className="relative">
                      <Input
                        id={fieldKey}
                        name={fieldKey}
                        type={isSecretField(fieldKey) && !showSecrets[fieldKey] ? 'password' : 'text'}
                        value={formik.values[fieldKey]}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder={`Enter your ${fieldKey.replace(/_/g, ' ')}`}
                        className={formik.errors[fieldKey] && formik.touched[fieldKey] ? 'border-red-500' : ''}
                      />
                      {isSecretField(fieldKey) && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => toggleSecretVisibility(fieldKey)}
                        >
                          {showSecrets[fieldKey] ? (
                            <EyeOff className="w-4 h-4" />
                          ) : (
                            <Eye className="w-4 h-4" />
                          )}
                        </Button>
                      )}
                    </div>
                    {formik.errors[fieldKey] && formik.touched[fieldKey] && (
                      <p className="text-sm text-red-600">{formik.errors[fieldKey]}</p>
                    )}
                  </div>
                ))}
              </div>

              {/* Optional Fields */}
              {Object.keys(gateway.optionalConfigFields || {}).length > 0 && (
                <div className="space-y-4">
                  <Label className="text-sm font-medium text-muted-foreground">Optional Fields</Label>
                  {Object.entries(gateway.optionalConfigFields || {}).map(([fieldKey, fieldType]) => (
                    <div key={fieldKey} className="space-y-2">
                      <Label htmlFor={fieldKey} className="capitalize">
                        {fieldKey.replace(/_/g, ' ')}
                      </Label>
                      <div className="relative">
                        <Input
                          id={fieldKey}
                          name={fieldKey}
                          type={isSecretField(fieldKey) && !showSecrets[fieldKey] ? 'password' : 'text'}
                          value={formik.values[fieldKey]}
                          onChange={formik.handleChange}
                          placeholder={`Enter your ${fieldKey.replace(/_/g, ' ')} (optional)`}
                        />
                        {isSecretField(fieldKey) && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => toggleSecretVisibility(fieldKey)}
                          >
                            {showSecrets[fieldKey] ? (
                              <EyeOff className="w-4 h-4" />
                            ) : (
                              <Eye className="w-4 h-4" />
                            )}
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Configuring...' : existingConfig ? 'Update Configuration' : 'Configure Gateway'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
```

## 🔒 Security & Compliance

### **Data Security Measures**

1. **Credential Encryption**
   ```typescript
   // Encrypt sensitive configuration data before storage
   const encryptConfigData = (configData: Record<string, any>) => {
     const crypto = require('crypto')
     const algorithm = 'aes-256-gcm'
     const secretKey = process.env.PAYMENT_CONFIG_ENCRYPTION_KEY

     const iv = crypto.randomBytes(16)
     const cipher = crypto.createCipher(algorithm, secretKey)

     let encrypted = cipher.update(JSON.stringify(configData), 'utf8', 'hex')
     encrypted += cipher.final('hex')

     const authTag = cipher.getAuthTag()

     return {
       encrypted,
       iv: iv.toString('hex'),
       authTag: authTag.toString('hex')
     }
   }
   ```

2. **Access Control**
   - Institute admins can only access their own gateway configurations
   - Super admins have read-only access to institute configurations for support
   - API keys are never returned in API responses (write-only)
   - Webhook endpoints use signature verification

3. **PCI Compliance Considerations**
   - Never store card details in the application
   - Use tokenization for recurring payments
   - Implement proper logging without sensitive data
   - Regular security audits and penetration testing

### **Environment Variables**
```bash
# Payment Gateway Encryption
PAYMENT_CONFIG_ENCRYPTION_KEY=your-32-character-encryption-key

# Gateway-specific settings (if needed for platform-level configs)
STRIPE_PLATFORM_CLIENT_ID=ca_xxx
RAZORPAY_PLATFORM_ACCOUNT_ID=acc_xxx
PAYPAL_PLATFORM_CLIENT_ID=xxx
```

## 📋 Implementation Guidelines

### **Phase Implementation Order**

1. **Phase 1: Backend Foundation** (Week 1)
   - Enhance PaymentGateways collection
   - Create InstitutePaymentConfigs collection
   - Implement basic API endpoints
   - Set up encryption for sensitive data

2. **Phase 2: Super Admin Management** (Week 2)
   - Super admin gateway CRUD operations
   - Gateway management UI components
   - Testing and validation

3. **Phase 3: Institute Admin Configuration** (Week 3)
   - Institute admin configuration endpoints
   - Gateway configuration UI
   - Testing functionality
   - Priority management

4. **Phase 4: Unified Payment Service** (Week 4)
   - Payment service implementation
   - Gateway-specific integrations
   - Webhook handling
   - Error handling and logging

5. **Phase 5: Frontend Integration** (Week 5)
   - Payment selection UI
   - Gateway testing interface
   - Status monitoring
   - User experience optimization

### **Gateway Integration Priority**

**Tier 1 (High Priority)**:
- **Razorpay**: Primary for Indian market, excellent UPI support
- **Stripe**: Global coverage, developer-friendly
- **PayPal**: International recognition, trust factor

**Tier 2 (Medium Priority)**:
- **Paytm**: Popular in India, good for local payments
- **PhonePe**: Growing UPI market share
- **Bank Transfer**: Manual verification option

**Tier 3 (Future)**:
- **Google Pay**: Integration complexity
- **Regional gateways**: Based on geographic expansion

### **Testing Strategy**

1. **Gateway Connection Testing**
   ```typescript
   // Test endpoint for each gateway
   POST /institute-admin/payment-configs/{id}/test

   // Test scenarios:
   - Valid credentials → Success response
   - Invalid credentials → Clear error message
   - Network issues → Timeout handling
   - Rate limiting → Retry logic
   ```

2. **Payment Flow Testing**
   ```typescript
   // Test payment creation
   - Small amount test transactions
   - Currency validation
   - Webhook delivery verification
   - Refund processing (if supported)
   ```

3. **Security Testing**
   ```typescript
   // Security validation
   - Webhook signature verification
   - Encrypted data storage
   - Access control enforcement
   - Input validation and sanitization
   ```

## 🚀 Deployment Checklist

### **Pre-deployment**
- [ ] Database migrations executed
- [ ] Environment variables configured
- [ ] Encryption keys generated and secured
- [ ] Gateway test accounts created
- [ ] Webhook endpoints configured

### **Post-deployment**
- [ ] Super admin can create/manage gateways
- [ ] Institute admins can configure gateways
- [ ] Gateway testing functionality works
- [ ] Payment processing flows correctly
- [ ] Webhook handling is functional
- [ ] Error logging is comprehensive

### **Monitoring & Alerts**
- [ ] Payment success/failure rates
- [ ] Gateway response times
- [ ] Webhook delivery status
- [ ] Configuration errors
- [ ] Security incidents

## 📊 Success Metrics

### **Technical Metrics**
- **Payment Success Rate**: >95% for all enabled gateways
- **Gateway Response Time**: <3 seconds average
- **Webhook Delivery**: >99% success rate
- **Configuration Errors**: <1% of setup attempts

### **Business Metrics**
- **Gateway Adoption**: % of institutes using multiple gateways
- **Payment Volume**: Increase in successful transactions
- **User Satisfaction**: Reduced payment-related support tickets
- **Revenue Impact**: Improved conversion rates

### **Security Metrics**
- **Zero Security Incidents**: No credential leaks or unauthorized access
- **Compliance Score**: 100% PCI DSS compliance where applicable
- **Audit Results**: Clean security audit reports

## 🔄 Future Enhancements

### **Phase 14 Considerations**
1. **Advanced Analytics**: Payment gateway performance dashboards
2. **Smart Routing**: AI-based gateway selection optimization
3. **Subscription Management**: Recurring payment handling
4. **Multi-currency**: Dynamic currency conversion
5. **Mobile Payments**: Enhanced mobile payment options
6. **Fraud Detection**: Advanced fraud prevention integration

### **Integration Opportunities**
- **Accounting Systems**: Automatic reconciliation
- **CRM Integration**: Payment history tracking
- **Marketing Tools**: Payment-based segmentation
- **Business Intelligence**: Advanced payment analytics

---

## 📝 Summary

This Phase 13 implementation provides a comprehensive payment gateway management system that:

✅ **Enables Super Admins** to manage platform-wide payment gateway availability
✅ **Empowers Institute Admins** to configure their own payment credentials securely
✅ **Supports Multiple Gateways** with intelligent selection and fallback mechanisms
✅ **Ensures Security** through encryption, access controls, and compliance measures
✅ **Provides Flexibility** for future gateway additions and feature enhancements
✅ **Optimizes User Experience** with smart defaults and user choice options

The multi-gateway approach with user choice and priority-based fallback provides the best balance of reliability, user experience, and business flexibility for the LMS platform.
```
