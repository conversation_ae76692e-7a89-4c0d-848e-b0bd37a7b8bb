# Phase 15: Super Admin Platform Blog Management System

## 📋 Overview

This phase implements a comprehensive platform-level blog management system for Super Admin and Super Admin Staff to create and manage LMS platform content, announcements, updates, and industry news. This system is separate from institute-level blogs and focuses on platform-wide content distribution.

## 🎯 Objectives

1. **Platform-Level Content**: Super Admin creates LMS updates, announcements, and platform news
2. **Separate from Institute Blogs**: Dedicated platform blog system independent of institute content
3. **Advanced Publishing**: Draft, scheduled, published states with priority levels
4. **Target Audience Control**: Content targeting (institutes, students, general public)
5. **Announcement System**: High-priority platform announcements
6. **List & Card Views**: Flexible content display options
7. **Cross-Platform Distribution**: Content visible across all institutes

## 🏗️ System Architecture

### **Platform Blog Hierarchy**
```
Platform Level (Super Admin):
├── Platform Blog Posts (LMS updates, announcements, news)
├── Platform Blog Categories (Platform, Updates, Industry, News)
├── Target Audience Management (Institutes, Students, Public)
├── Priority & Announcement System
├── Cross-Platform Analytics
└── Global Content Distribution

Content Types:
├── LMS Feature Updates
├── Platform Announcements  
├── Industry News & Trends
├── Best Practices & Guides
├── Success Stories
└── Policy Updates
```

## 📊 Database Schema

### **Platform Blog Posts Table**
```sql
CREATE TABLE platform_blog_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL UNIQUE,
    excerpt TEXT,
    content TEXT NOT NULL,
    featured_image_url VARCHAR(500),
    
    -- Publishing
    status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'scheduled', 'published', 'archived'
    published_at TIMESTAMP,
    scheduled_for TIMESTAMP,
    
    -- Platform-specific fields
    target_audience VARCHAR(50) DEFAULT 'all', -- 'institutes', 'students', 'staff', 'all'
    is_announcement BOOLEAN DEFAULT false,
    priority_level INTEGER DEFAULT 0, -- 0=normal, 1=important, 2=critical
    announcement_expires_at TIMESTAMP, -- For time-sensitive announcements
    
    -- Organization
    category_id UUID REFERENCES platform_blog_categories(id),
    tags TEXT[], -- Array of tag names
    
    -- SEO
    seo_title VARCHAR(150),
    seo_description VARCHAR(300),
    seo_keywords TEXT[],
    
    -- Engagement
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    
    -- Settings
    allow_comments BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    is_sticky BOOLEAN DEFAULT false, -- Pin to top
    show_on_dashboard BOOLEAN DEFAULT false, -- Show on institute dashboards
    reading_time INTEGER, -- Estimated reading time in minutes
    
    -- Authoring (Super Admin team only)
    author_id UUID REFERENCES users(id) NOT NULL,
    last_edited_by UUID REFERENCES users(id),
    
    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### **Platform Blog Categories Table**
```sql
CREATE TABLE platform_blog_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50), -- Icon class name
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    
    -- Category types for platform content
    category_type VARCHAR(50) DEFAULT 'general', -- 'updates', 'announcements', 'news', 'guides', 'general'
    
    -- SEO
    seo_title VARCHAR(150),
    seo_description VARCHAR(300),
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### **Platform Blog Analytics Table**
```sql
CREATE TABLE platform_blog_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID REFERENCES platform_blog_posts(id) ON DELETE CASCADE,
    
    -- Analytics data
    date DATE NOT NULL,
    views INTEGER DEFAULT 0,
    unique_views INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    comments INTEGER DEFAULT 0,
    shares INTEGER DEFAULT 0,
    
    -- Audience breakdown
    institute_admin_views INTEGER DEFAULT 0,
    student_views INTEGER DEFAULT 0,
    staff_views INTEGER DEFAULT 0,
    public_views INTEGER DEFAULT 0,
    
    -- Traffic sources
    direct_traffic INTEGER DEFAULT 0,
    search_traffic INTEGER DEFAULT 0,
    social_traffic INTEGER DEFAULT 0,
    referral_traffic INTEGER DEFAULT 0,
    dashboard_traffic INTEGER DEFAULT 0, -- Views from institute dashboards
    
    -- Engagement metrics
    avg_time_on_page INTEGER, -- in seconds
    bounce_rate DECIMAL(5,2), -- percentage
    
    created_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(post_id, date)
);
```

### **Platform Blog Comments Table**
```sql
CREATE TABLE platform_blog_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID REFERENCES platform_blog_posts(id) ON DELETE CASCADE,
    parent_comment_id UUID REFERENCES platform_blog_comments(id), -- For nested comments
    
    -- Comment content
    content TEXT NOT NULL,
    author_name VARCHAR(100) NOT NULL,
    author_email VARCHAR(255),
    author_id UUID REFERENCES users(id), -- If logged in user
    author_institute_id UUID REFERENCES institutes(id), -- Track which institute user is from
    
    -- Moderation
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'spam'
    moderated_by UUID REFERENCES users(id),
    moderated_at TIMESTAMP,
    
    -- Engagement
    like_count INTEGER DEFAULT 0,
    
    -- Metadata
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 Backend Implementation

### **Platform Blog Posts Collection**
**File**: `apps/api/src/collections/PlatformBlogPosts.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isSuperAdmin, isSuperAdminOrStaff } from '../access/index'

const PlatformBlogPosts: CollectionConfig = {
  slug: 'platform-blog-posts',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'status', 'targetAudience', 'isAnnouncement', 'author', 'publishedAt'],
    group: 'Platform Content',
  },
  access: {
    read: () => true, // Everyone can read published platform posts
    create: isSuperAdminOrStaff,
    update: isSuperAdminOrStaff,
    delete: isSuperAdmin,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      maxLength: 200,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'URL-friendly version of the title'
      }
    },
    {
      name: 'excerpt',
      type: 'textarea',
      maxLength: 300,
      admin: {
        description: 'Brief summary for previews and social sharing'
      }
    },
    {
      name: 'content',
      type: 'richText',
      required: true,
      admin: {
        elements: [
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
          'blockquote', 'ul', 'ol', 'li',
          'link', 'upload', 'indent'
        ],
        leaves: [
          'bold', 'italic', 'underline', 'strikethrough', 'code'
        ]
      }
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Main image for the blog post'
      }
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Scheduled', value: 'scheduled' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ]
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        condition: (data) => data.status === 'published'
      }
    },
    {
      name: 'scheduledFor',
      type: 'date',
      admin: {
        condition: (data) => data.status === 'scheduled'
      }
    },
    {
      name: 'targetAudience',
      type: 'select',
      required: true,
      defaultValue: 'all',
      options: [
        { label: 'All Users', value: 'all' },
        { label: 'Institute Admins', value: 'institutes' },
        { label: 'Students', value: 'students' },
        { label: 'Staff Members', value: 'staff' },
        { label: 'General Public', value: 'public' },
      ],
      admin: {
        description: 'Who should see this content'
      }
    },
    {
      name: 'isAnnouncement',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Mark as important platform announcement'
      }
    },
    {
      name: 'priorityLevel',
      type: 'select',
      defaultValue: 0,
      options: [
        { label: 'Normal', value: 0 },
        { label: 'Important', value: 1 },
        { label: 'Critical', value: 2 },
      ],
      admin: {
        condition: (data) => data.isAnnouncement,
        description: 'Priority level for announcements'
      }
    },
    {
      name: 'announcementExpiresAt',
      type: 'date',
      admin: {
        condition: (data) => data.isAnnouncement,
        description: 'When this announcement should stop being highlighted'
      }
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'platform-blog-categories',
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true
        }
      ]
    },
    {
      name: 'seo',
      type: 'group',
      fields: [
        {
          name: 'title',
          type: 'text',
          maxLength: 150,
          admin: {
            description: 'SEO title (max 150 characters)'
          }
        },
        {
          name: 'description',
          type: 'textarea',
          maxLength: 300,
          admin: {
            description: 'SEO description (max 300 characters)'
          }
        },
        {
          name: 'keywords',
          type: 'array',
          fields: [
            {
              name: 'keyword',
              type: 'text'
            }
          ]
        }
      ]
    },
    {
      name: 'settings',
      type: 'group',
      fields: [
        {
          name: 'allowComments',
          type: 'checkbox',
          defaultValue: true
        },
        {
          name: 'isFeatured',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Show in featured posts section'
          }
        },
        {
          name: 'isSticky',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Pin to top of blog list'
          }
        },
        {
          name: 'showOnDashboard',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Display on institute admin dashboards'
          }
        }
      ]
    },
    {
      name: 'analytics',
      type: 'group',
      admin: {
        readOnly: true
      },
      fields: [
        {
          name: 'viewCount',
          type: 'number',
          defaultValue: 0
        },
        {
          name: 'likeCount',
          type: 'number',
          defaultValue: 0
        },
        {
          name: 'commentCount',
          type: 'number',
          defaultValue: 0
        },
        {
          name: 'readingTime',
          type: 'number',
          admin: {
            description: 'Estimated reading time in minutes'
          }
        }
      ]
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        readOnly: true
      }
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, req }) => {
        // Set author from authenticated user
        if (!data.author) {
          data.author = req.user?.id
        }
        
        // Auto-generate slug from title if not provided
        if (!data.slug && data.title) {
          data.slug = data.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }
        
        // Calculate reading time
        if (data.content) {
          const wordCount = data.content.replace(/<[^>]*>/g, '').split(/\s+/).length
          data.analytics = {
            ...data.analytics,
            readingTime: Math.ceil(wordCount / 200) // Average reading speed
          }
        }
        
        // Set published date when status changes to published
        if (data.status === 'published' && !data.publishedAt) {
          data.publishedAt = new Date()
        }
        
        return data
      }
    ]
  }
}

export default PlatformBlogPosts
```

### **Platform Blog Categories Collection**
**File**: `apps/api/src/collections/PlatformBlogCategories.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isSuperAdminOrStaff } from '../access/index'

const PlatformBlogCategories: CollectionConfig = {
  slug: 'platform-blog-categories',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'categoryType', 'postCount', 'isActive'],
    group: 'Platform Content',
  },
  access: {
    read: () => true, // Everyone can read categories
    create: isSuperAdminOrStaff,
    update: isSuperAdminOrStaff,
    delete: isSuperAdminOrStaff,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'categoryType',
      type: 'select',
      required: true,
      defaultValue: 'general',
      options: [
        { label: 'Platform Updates', value: 'updates' },
        { label: 'Announcements', value: 'announcements' },
        { label: 'Industry News', value: 'news' },
        { label: 'Guides & Tutorials', value: 'guides' },
        { label: 'Success Stories', value: 'stories' },
        { label: 'General', value: 'general' },
      ],
      admin: {
        description: 'Type of content this category represents'
      }
    },
    {
      name: 'color',
      type: 'text',
      admin: {
        description: 'Hex color code for category display'
      }
    },
    {
      name: 'icon',
      type: 'text',
      admin: {
        description: 'Icon class name (e.g., lucide icon names)'
      }
    },
    {
      name: 'displayOrder',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Order for displaying categories (lower numbers first)'
      }
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'seo',
      type: 'group',
      fields: [
        {
          name: 'title',
          type: 'text',
          maxLength: 150,
        },
        {
          name: 'description',
          type: 'textarea',
          maxLength: 300,
        }
      ]
    }
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate slug from name if not provided
        if (!data.slug && data.name) {
          data.slug = data.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }

        return data
      }
    ]
  }
}

export default PlatformBlogCategories
```

### **Super Admin Platform Blog API Endpoints**
**File**: `apps/api/src/endpoints/super-admin/platform-blog.ts`

```typescript
import { Endpoint } from 'payload/config'

const platformBlogEndpoints: Endpoint[] = [
  // Get all platform blog posts
  {
    path: '/super-admin/platform-blog/posts',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { user } = req
        const { status, category, targetAudience, isAnnouncement, limit = 20, page = 1, view = 'list' } = req.query

        if (!user || (user.role !== 'super_admin' && user.legacyRole !== 'super_admin')) {
          return res.status(403).json({ error: 'Access denied' })
        }

        // Build query
        const query: any = {}

        if (status) {
          query.status = { equals: status }
        }

        if (category) {
          query.category = { equals: category }
        }

        if (targetAudience) {
          query.targetAudience = { equals: targetAudience }
        }

        if (isAnnouncement === 'true') {
          query.isAnnouncement = { equals: true }
        }

        const posts = await req.payload.find({
          collection: 'platform-blog-posts',
          where: query,
          limit: parseInt(limit as string),
          page: parseInt(page as string),
          sort: '-createdAt',
          populate: ['category', 'author']
        })

        res.json({
          success: true,
          posts: posts.docs,
          pagination: {
            page: posts.page,
            limit: posts.limit,
            totalPages: posts.totalPages,
            totalDocs: posts.totalDocs,
            hasNextPage: posts.hasNextPage,
            hasPrevPage: posts.hasPrevPage
          },
          view
        })
      } catch (error) {
        console.error('Platform blog posts fetch error:', error)
        res.status(500).json({ error: 'Failed to fetch platform blog posts' })
      }
    }
  },

  // Create platform blog post
  {
    path: '/super-admin/platform-blog/posts',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { user } = req

        if (!user || (user.role !== 'super_admin' && user.legacyRole !== 'super_admin')) {
          return res.status(403).json({ error: 'Access denied' })
        }

        const postData = {
          ...req.body,
          author: user.id
        }

        const post = await req.payload.create({
          collection: 'platform-blog-posts',
          data: postData
        })

        res.json({
          success: true,
          post,
          message: 'Platform blog post created successfully'
        })
      } catch (error) {
        console.error('Platform blog post creation error:', error)
        res.status(500).json({ error: 'Failed to create platform blog post' })
      }
    }
  },

  // Update platform blog post
  {
    path: '/super-admin/platform-blog/posts/:id',
    method: 'patch',
    handler: async (req, res) => {
      try {
        const { user } = req
        const { id } = req.params

        if (!user || (user.role !== 'super_admin' && user.legacyRole !== 'super_admin')) {
          return res.status(403).json({ error: 'Access denied' })
        }

        const post = await req.payload.update({
          collection: 'platform-blog-posts',
          id,
          data: {
            ...req.body,
            lastEditedBy: user.id
          }
        })

        res.json({
          success: true,
          post,
          message: 'Platform blog post updated successfully'
        })
      } catch (error) {
        console.error('Platform blog post update error:', error)
        res.status(500).json({ error: 'Failed to update platform blog post' })
      }
    }
  },

  // Delete platform blog post
  {
    path: '/super-admin/platform-blog/posts/:id',
    method: 'delete',
    handler: async (req, res) => {
      try {
        const { user } = req
        const { id } = req.params

        if (!user || user.role !== 'super_admin') {
          return res.status(403).json({ error: 'Access denied' })
        }

        await req.payload.delete({
          collection: 'platform-blog-posts',
          id
        })

        res.json({
          success: true,
          message: 'Platform blog post deleted successfully'
        })
      } catch (error) {
        console.error('Platform blog post deletion error:', error)
        res.status(500).json({ error: 'Failed to delete platform blog post' })
      }
    }
  },

  // Get platform blog categories
  {
    path: '/super-admin/platform-blog/categories',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { user } = req

        if (!user || (user.role !== 'super_admin' && user.legacyRole !== 'super_admin')) {
          return res.status(403).json({ error: 'Access denied' })
        }

        const categories = await req.payload.find({
          collection: 'platform-blog-categories',
          sort: 'displayOrder',
          limit: 100
        })

        res.json({
          success: true,
          categories: categories.docs
        })
      } catch (error) {
        console.error('Platform blog categories fetch error:', error)
        res.status(500).json({ error: 'Failed to fetch platform blog categories' })
      }
    }
  },

  // Platform blog analytics
  {
    path: '/super-admin/platform-blog/analytics',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { user } = req
        const { period = '30d', postId } = req.query

        if (!user || (user.role !== 'super_admin' && user.legacyRole !== 'super_admin')) {
          return res.status(403).json({ error: 'Access denied' })
        }

        // Calculate date range
        const now = new Date()
        const startDate = new Date()

        switch (period) {
          case '7d':
            startDate.setDate(now.getDate() - 7)
            break
          case '30d':
            startDate.setDate(now.getDate() - 30)
            break
          case '90d':
            startDate.setDate(now.getDate() - 90)
            break
          default:
            startDate.setDate(now.getDate() - 30)
        }

        let analyticsQuery: any = {
          date: {
            greater_than_equal: startDate.toISOString().split('T')[0]
          }
        }

        if (postId) {
          analyticsQuery.post = { equals: postId }
        }

        const analytics = await req.payload.find({
          collection: 'platform-blog-analytics',
          where: analyticsQuery,
          limit: 1000,
          sort: 'date'
        })

        // Aggregate data
        const aggregated = analytics.docs.reduce((acc, curr) => {
          acc.totalViews += curr.views || 0
          acc.totalUniqueViews += curr.uniqueViews || 0
          acc.totalLikes += curr.likes || 0
          acc.totalComments += curr.comments || 0
          acc.totalShares += curr.shares || 0
          acc.instituteAdminViews += curr.instituteAdminViews || 0
          acc.studentViews += curr.studentViews || 0
          acc.staffViews += curr.staffViews || 0
          acc.publicViews += curr.publicViews || 0
          return acc
        }, {
          totalViews: 0,
          totalUniqueViews: 0,
          totalLikes: 0,
          totalComments: 0,
          totalShares: 0,
          instituteAdminViews: 0,
          studentViews: 0,
          staffViews: 0,
          publicViews: 0
        })

        res.json({
          success: true,
          analytics: aggregated,
          dailyData: analytics.docs,
          period
        })
      } catch (error) {
        console.error('Platform blog analytics error:', error)
        res.status(500).json({ error: 'Failed to fetch platform blog analytics' })
      }
    }
  }
]

export default platformBlogEndpoints
```

## 🎨 Frontend Implementation

### **Platform Blog Management Zustand Store**
**File**: `apps/frontend/src/stores/super-admin/usePlatformBlogStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

export interface PlatformBlogPost {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: string
  featuredImage?: {
    url: string
    alt: string
  }
  status: 'draft' | 'scheduled' | 'published' | 'archived'
  publishedAt?: string
  scheduledFor?: string
  targetAudience: 'all' | 'institutes' | 'students' | 'staff' | 'public'
  isAnnouncement: boolean
  priorityLevel: 0 | 1 | 2
  announcementExpiresAt?: string
  category?: {
    id: string
    name: string
    slug: string
    categoryType: string
    color?: string
  }
  tags: Array<{ tag: string }>
  seo: {
    title?: string
    description?: string
    keywords?: Array<{ keyword: string }>
  }
  settings: {
    allowComments: boolean
    isFeatured: boolean
    isSticky: boolean
    showOnDashboard: boolean
  }
  analytics: {
    viewCount: number
    likeCount: number
    commentCount: number
    readingTime?: number
  }
  author: {
    id: string
    name: string
    email: string
  }
  createdAt: string
  updatedAt: string
}

export interface PlatformBlogCategory {
  id: string
  name: string
  slug: string
  description?: string
  categoryType: 'updates' | 'announcements' | 'news' | 'guides' | 'stories' | 'general'
  color?: string
  icon?: string
  displayOrder: number
  isActive: boolean
  postCount?: number
}

interface PlatformBlogState {
  // Posts
  posts: PlatformBlogPost[]
  currentPost: PlatformBlogPost | null
  postsLoading: boolean

  // Categories
  categories: PlatformBlogCategory[]
  categoriesLoading: boolean

  // Filters
  selectedStatus: string | null
  selectedCategory: string | null
  selectedTargetAudience: string | null
  showAnnouncementsOnly: boolean

  // View mode
  viewMode: 'list' | 'card'

  // Analytics
  analytics: {
    totalViews: number
    totalUniqueViews: number
    totalLikes: number
    totalComments: number
    totalShares: number
    instituteAdminViews: number
    studentViews: number
    staffViews: number
    publicViews: number
  }

  // UI State
  error: string | null

  // Actions
  fetchPosts: (params?: any) => Promise<void>
  fetchPost: (id: string) => Promise<void>
  createPost: (postData: Partial<PlatformBlogPost>) => Promise<void>
  updatePost: (id: string, postData: Partial<PlatformBlogPost>) => Promise<void>
  deletePost: (id: string) => Promise<void>
  publishPost: (id: string) => Promise<void>
  schedulePost: (id: string, scheduledFor: string) => Promise<void>

  // Categories
  fetchCategories: () => Promise<void>
  createCategory: (categoryData: Partial<PlatformBlogCategory>) => Promise<void>
  updateCategory: (id: string, categoryData: Partial<PlatformBlogCategory>) => Promise<void>
  deleteCategory: (id: string) => Promise<void>

  // Filters
  setFilters: (filters: any) => void
  clearFilters: () => void
  setViewMode: (mode: 'list' | 'card') => void

  // Analytics
  fetchAnalytics: (period?: string) => Promise<void>
}

export const usePlatformBlogStore = create<PlatformBlogState>()(
  devtools(
    (set, get) => ({
      // Initial state
      posts: [],
      currentPost: null,
      postsLoading: false,
      categories: [],
      categoriesLoading: false,
      selectedStatus: null,
      selectedCategory: null,
      selectedTargetAudience: null,
      showAnnouncementsOnly: false,
      viewMode: 'list',
      analytics: {
        totalViews: 0,
        totalUniqueViews: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0,
        instituteAdminViews: 0,
        studentViews: 0,
        staffViews: 0,
        publicViews: 0
      },
      error: null,

      // Posts actions
      fetchPosts: async (params = {}) => {
        set({ postsLoading: true, error: null })
        try {
          const state = get()
          const queryParams = {
            ...params,
            status: state.selectedStatus,
            category: state.selectedCategory,
            targetAudience: state.selectedTargetAudience,
            isAnnouncement: state.showAnnouncementsOnly ? 'true' : undefined,
            view: state.viewMode
          }

          // Remove null/undefined values
          Object.keys(queryParams).forEach(key => {
            if (queryParams[key] === null || queryParams[key] === undefined) {
              delete queryParams[key]
            }
          })

          const response = await api.get('/super-admin/platform-blog/posts', { params: queryParams })
          set({
            posts: response.data.posts,
            postsLoading: false
          })
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to fetch posts',
            postsLoading: false
          })
          toast.error('Failed to fetch platform blog posts')
        }
      },

      fetchPost: async (id) => {
        set({ postsLoading: true, error: null })
        try {
          const response = await api.get(`/super-admin/platform-blog/posts/${id}`)
          set({
            currentPost: response.data.post,
            postsLoading: false
          })
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to fetch post',
            postsLoading: false
          })
          toast.error('Failed to fetch platform blog post')
        }
      },

      createPost: async (postData) => {
        set({ postsLoading: true, error: null })
        try {
          const response = await api.post('/super-admin/platform-blog/posts', postData)

          set(state => ({
            posts: [response.data.post, ...state.posts],
            postsLoading: false
          }))

          toast.success('Platform blog post created successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to create post',
            postsLoading: false
          })
          toast.error('Failed to create platform blog post')
          throw error
        }
      },

      updatePost: async (id, postData) => {
        set({ postsLoading: true, error: null })
        try {
          const response = await api.patch(`/super-admin/platform-blog/posts/${id}`, postData)

          set(state => ({
            posts: state.posts.map(post =>
              post.id === id ? response.data.post : post
            ),
            currentPost: state.currentPost?.id === id ? response.data.post : state.currentPost,
            postsLoading: false
          }))

          toast.success('Platform blog post updated successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to update post',
            postsLoading: false
          })
          toast.error('Failed to update platform blog post')
          throw error
        }
      },

      deletePost: async (id) => {
        set({ postsLoading: true, error: null })
        try {
          await api.delete(`/super-admin/platform-blog/posts/${id}`)

          set(state => ({
            posts: state.posts.filter(post => post.id !== id),
            currentPost: state.currentPost?.id === id ? null : state.currentPost,
            postsLoading: false
          }))

          toast.success('Platform blog post deleted successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to delete post',
            postsLoading: false
          })
          toast.error('Failed to delete platform blog post')
        }
      },

      publishPost: async (id) => {
        try {
          await get().updatePost(id, {
            status: 'published',
            publishedAt: new Date().toISOString()
          })
          toast.success('Platform blog post published successfully')
        } catch (error) {
          // Error handling is done in updatePost
        }
      },

      schedulePost: async (id, scheduledFor) => {
        try {
          await get().updatePost(id, {
            status: 'scheduled',
            scheduledFor
          })
          toast.success('Platform blog post scheduled successfully')
        } catch (error) {
          // Error handling is done in updatePost
        }
      },

      // Categories actions
      fetchCategories: async () => {
        set({ categoriesLoading: true, error: null })
        try {
          const response = await api.get('/super-admin/platform-blog/categories')
          set({
            categories: response.data.categories,
            categoriesLoading: false
          })
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to fetch categories',
            categoriesLoading: false
          })
          toast.error('Failed to fetch platform blog categories')
        }
      },

      createCategory: async (categoryData) => {
        set({ categoriesLoading: true, error: null })
        try {
          const response = await api.post('/super-admin/platform-blog/categories', categoryData)

          set(state => ({
            categories: [...state.categories, response.data.category],
            categoriesLoading: false
          }))

          toast.success('Platform blog category created successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to create category',
            categoriesLoading: false
          })
          toast.error('Failed to create platform blog category')
          throw error
        }
      },

      updateCategory: async (id, categoryData) => {
        set({ categoriesLoading: true, error: null })
        try {
          const response = await api.patch(`/super-admin/platform-blog/categories/${id}`, categoryData)

          set(state => ({
            categories: state.categories.map(category =>
              category.id === id ? response.data.category : category
            ),
            categoriesLoading: false
          }))

          toast.success('Platform blog category updated successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to update category',
            categoriesLoading: false
          })
          toast.error('Failed to update platform blog category')
          throw error
        }
      },

      deleteCategory: async (id) => {
        set({ categoriesLoading: true, error: null })
        try {
          await api.delete(`/super-admin/platform-blog/categories/${id}`)

          set(state => ({
            categories: state.categories.filter(category => category.id !== id),
            categoriesLoading: false
          }))

          toast.success('Platform blog category deleted successfully')
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Failed to delete category',
            categoriesLoading: false
          })
          toast.error('Failed to delete platform blog category')
        }
      },

      // Filter actions
      setFilters: (filters) => {
        set({
          selectedStatus: filters.status || null,
          selectedCategory: filters.category || null,
          selectedTargetAudience: filters.targetAudience || null,
          showAnnouncementsOnly: filters.showAnnouncementsOnly || false
        })
      },

      clearFilters: () => {
        set({
          selectedStatus: null,
          selectedCategory: null,
          selectedTargetAudience: null,
          showAnnouncementsOnly: false
        })
      },

      setViewMode: (mode) => {
        set({ viewMode: mode })
      },

      // Analytics actions
      fetchAnalytics: async (period = '30d') => {
        try {
          const response = await api.get('/super-admin/platform-blog/analytics', {
            params: { period }
          })

          set({ analytics: response.data.analytics })
        } catch (error: any) {
          toast.error('Failed to fetch platform blog analytics')
        }
      }
    }),
    {
      name: 'platform-blog-store'
    }
  )
)
```

### **Platform Blog List View Component**
**File**: `apps/frontend/src/components/super-admin/platform-blog/PlatformBlogListView.tsx`

```typescript
'use client'

import { useState } from 'react'
import { usePlatformBlogStore, PlatformBlogPost } from '@/stores/super-admin/usePlatformBlogStore'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Calendar,
  Users,
  MessageSquare,
  Heart,
  AlertTriangle
} from 'lucide-react'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'

interface PlatformBlogListViewProps {
  posts: PlatformBlogPost[]
  loading: boolean
}

export default function PlatformBlogListView({ posts, loading }: PlatformBlogListViewProps) {
  const { deletePost, publishPost } = usePlatformBlogStore()
  const [selectedPosts, setSelectedPosts] = useState<string[]>([])

  const handleSelectPost = (postId: string, checked: boolean) => {
    if (checked) {
      setSelectedPosts([...selectedPosts, postId])
    } else {
      setSelectedPosts(selectedPosts.filter(id => id !== postId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPosts(posts.map(post => post.id))
    } else {
      setSelectedPosts([])
    }
  }

  const getStatusBadge = (status: string, isAnnouncement: boolean, priorityLevel: number) => {
    if (isAnnouncement) {
      const priorityColors = {
        0: 'bg-blue-100 text-blue-800',
        1: 'bg-orange-100 text-orange-800',
        2: 'bg-red-100 text-red-800'
      }
      return (
        <div className="flex items-center gap-1">
          <Badge className={priorityColors[priorityLevel as keyof typeof priorityColors]}>
            <AlertTriangle className="w-3 h-3 mr-1" />
            Announcement
          </Badge>
          <Badge variant={status === 'published' ? 'default' : 'secondary'}>
            {status}
          </Badge>
        </div>
      )
    }

    return (
      <Badge variant={status === 'published' ? 'default' : 'secondary'}>
        {status}
      </Badge>
    )
  }

  const getTargetAudienceBadge = (audience: string) => {
    const audienceConfig = {
      all: { label: 'All Users', color: 'bg-gray-100 text-gray-800' },
      institutes: { label: 'Institutes', color: 'bg-blue-100 text-blue-800' },
      students: { label: 'Students', color: 'bg-green-100 text-green-800' },
      staff: { label: 'Staff', color: 'bg-purple-100 text-purple-800' },
      public: { label: 'Public', color: 'bg-yellow-100 text-yellow-800' }
    }

    const config = audienceConfig[audience as keyof typeof audienceConfig] || audienceConfig.all

    return (
      <Badge className={config.color}>
        <Users className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 animate-pulse rounded" />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedPosts.length > 0 && (
        <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
          <span className="text-sm text-blue-700">
            {selectedPosts.length} post(s) selected
          </span>
          <Button size="sm" variant="outline">
            Bulk Publish
          </Button>
          <Button size="sm" variant="outline">
            Bulk Archive
          </Button>
          <Button size="sm" variant="destructive">
            Bulk Delete
          </Button>
        </div>
      )}

      {/* Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedPosts.length === posts.length && posts.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Target Audience</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Author</TableHead>
              <TableHead>Engagement</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {posts.map((post) => (
              <TableRow key={post.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedPosts.includes(post.id)}
                    onCheckedChange={(checked) => handleSelectPost(post.id, checked as boolean)}
                  />
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <Link
                      href={`/super-admin/platform-blog/posts/${post.id}`}
                      className="font-medium text-gray-900 hover:text-blue-600 line-clamp-1"
                    >
                      {post.title}
                    </Link>
                    {post.excerpt && (
                      <p className="text-sm text-gray-500 line-clamp-1">
                        {post.excerpt}
                      </p>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(post.status, post.isAnnouncement, post.priorityLevel)}
                </TableCell>
                <TableCell>
                  {getTargetAudienceBadge(post.targetAudience)}
                </TableCell>
                <TableCell>
                  {post.category && (
                    <Badge variant="outline" className="flex items-center gap-1 w-fit">
                      {post.category.color && (
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: post.category.color }}
                        />
                      )}
                      {post.category.name}
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarFallback className="text-xs">
                        {post.author.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm">{post.author.name}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-3 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      {post.analytics.viewCount}
                    </span>
                    <span className="flex items-center gap-1">
                      <Heart className="w-3 h-3" />
                      {post.analytics.likeCount}
                    </span>
                    <span className="flex items-center gap-1">
                      <MessageSquare className="w-3 h-3" />
                      {post.analytics.commentCount}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm text-gray-500">
                    {post.status === 'published' && post.publishedAt ? (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {formatDistanceToNow(new Date(post.publishedAt), { addSuffix: true })}
                      </div>
                    ) : post.status === 'scheduled' && post.scheduledFor ? (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        Scheduled for {new Date(post.scheduledFor).toLocaleDateString()}
                      </div>
                    ) : (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {formatDistanceToNow(new Date(post.createdAt), { addSuffix: true })}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/super-admin/platform-blog/posts/${post.id}`}>
                          <Eye className="w-4 h-4 mr-2" />
                          View
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/super-admin/platform-blog/posts/${post.id}/edit`}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      {post.status === 'draft' && (
                        <DropdownMenuItem onClick={() => publishPost(post.id)}>
                          <Calendar className="w-4 h-4 mr-2" />
                          Publish
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem
                        onClick={() => deletePost(post.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {posts.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No platform blog posts found.</p>
          <Button className="mt-4" asChild>
            <Link href="/super-admin/platform-blog/posts/new">
              Create Your First Post
            </Link>
          </Button>
        </div>
      )}
    </div>
  )
}
```

### **Platform Blog Card View Component**
**File**: `apps/frontend/src/components/super-admin/platform-blog/PlatformBlogCardView.tsx`

```typescript
'use client'

import { useState } from 'react'
import { usePlatformBlogStore, PlatformBlogPost } from '@/stores/super-admin/usePlatformBlogStore'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Calendar,
  Users,
  MessageSquare,
  Heart,
  AlertTriangle,
  Clock,
  Share2
} from 'lucide-react'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'

interface PlatformBlogCardViewProps {
  posts: PlatformBlogPost[]
  loading: boolean
}

export default function PlatformBlogCardView({ posts, loading }: PlatformBlogCardViewProps) {
  const { deletePost, publishPost } = usePlatformBlogStore()
  const [selectedPosts, setSelectedPosts] = useState<string[]>([])

  const handleSelectPost = (postId: string, checked: boolean) => {
    if (checked) {
      setSelectedPosts([...selectedPosts, postId])
    } else {
      setSelectedPosts(selectedPosts.filter(id => id !== postId))
    }
  }

  const getStatusBadge = (status: string, isAnnouncement: boolean, priorityLevel: number) => {
    if (isAnnouncement) {
      const priorityColors = {
        0: 'bg-blue-100 text-blue-800',
        1: 'bg-orange-100 text-orange-800',
        2: 'bg-red-100 text-red-800'
      }
      return (
        <div className="flex items-center gap-1">
          <Badge className={priorityColors[priorityLevel as keyof typeof priorityColors]}>
            <AlertTriangle className="w-3 h-3 mr-1" />
            Announcement
          </Badge>
        </div>
      )
    }

    return (
      <Badge variant={status === 'published' ? 'default' : 'secondary'}>
        {status}
      </Badge>
    )
  }

  const getTargetAudienceIcon = (audience: string) => {
    const audienceConfig = {
      all: { label: 'All Users', color: 'text-gray-600' },
      institutes: { label: 'Institutes', color: 'text-blue-600' },
      students: { label: 'Students', color: 'text-green-600' },
      staff: { label: 'Staff', color: 'text-purple-600' },
      public: { label: 'Public', color: 'text-yellow-600' }
    }

    const config = audienceConfig[audience as keyof typeof audienceConfig] || audienceConfig.all

    return (
      <div className={`flex items-center gap-1 text-sm ${config.color}`}>
        <Users className="w-3 h-3" />
        {config.label}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="h-80">
            <CardHeader className="space-y-2">
              <div className="h-4 bg-gray-200 animate-pulse rounded" />
              <div className="h-3 bg-gray-200 animate-pulse rounded w-3/4" />
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="h-20 bg-gray-200 animate-pulse rounded" />
              <div className="flex gap-2">
                <div className="h-6 bg-gray-200 animate-pulse rounded w-16" />
                <div className="h-6 bg-gray-200 animate-pulse rounded w-20" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Bulk Actions */}
      {selectedPosts.length > 0 && (
        <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
          <span className="text-sm text-blue-700">
            {selectedPosts.length} post(s) selected
          </span>
          <Button size="sm" variant="outline">
            Bulk Publish
          </Button>
          <Button size="sm" variant="outline">
            Bulk Archive
          </Button>
          <Button size="sm" variant="destructive">
            Bulk Delete
          </Button>
        </div>
      )}

      {/* Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {posts.map((post) => (
          <Card key={post.id} className="group hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="space-y-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedPosts.includes(post.id)}
                    onCheckedChange={(checked) => handleSelectPost(post.id, checked as boolean)}
                  />
                  {getStatusBadge(post.status, post.isAnnouncement, post.priorityLevel)}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/super-admin/platform-blog/posts/${post.id}`}>
                        <Eye className="w-4 h-4 mr-2" />
                        View
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/super-admin/platform-blog/posts/${post.id}/edit`}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </Link>
                    </DropdownMenuItem>
                    {post.status === 'draft' && (
                      <DropdownMenuItem onClick={() => publishPost(post.id)}>
                        <Calendar className="w-4 h-4 mr-2" />
                        Publish
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      onClick={() => deletePost(post.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <div>
                <CardTitle className="line-clamp-2 text-lg">
                  <Link
                    href={`/super-admin/platform-blog/posts/${post.id}`}
                    className="hover:text-blue-600 transition-colors"
                  >
                    {post.title}
                  </Link>
                </CardTitle>
                {post.excerpt && (
                  <CardDescription className="line-clamp-3 mt-2">
                    {post.excerpt}
                  </CardDescription>
                )}
              </div>

              {/* Featured Image */}
              {post.featuredImage && (
                <div className="aspect-video bg-gray-100 rounded-md overflow-hidden">
                  <img
                    src={post.featuredImage.url}
                    alt={post.featuredImage.alt || post.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Category and Target Audience */}
              <div className="flex items-center justify-between">
                {post.category && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    {post.category.color && (
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: post.category.color }}
                      />
                    )}
                    {post.category.name}
                  </Badge>
                )}
                {getTargetAudienceIcon(post.targetAudience)}
              </div>

              {/* Tags */}
              {post.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {post.tags.slice(0, 3).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      #{tag.tag}
                    </Badge>
                  ))}
                  {post.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{post.tags.length - 3} more
                    </Badge>
                  )}
                </div>
              )}

              {/* Engagement Stats */}
              <div className="flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center gap-3">
                  <span className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    {post.analytics.viewCount}
                  </span>
                  <span className="flex items-center gap-1">
                    <Heart className="w-3 h-3" />
                    {post.analytics.likeCount}
                  </span>
                  <span className="flex items-center gap-1">
                    <MessageSquare className="w-3 h-3" />
                    {post.analytics.commentCount}
                  </span>
                </div>
                {post.analytics.readingTime && (
                  <span className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {post.analytics.readingTime} min read
                  </span>
                )}
              </div>

              {/* Author and Date */}
              <div className="flex items-center justify-between pt-3 border-t">
                <div className="flex items-center gap-2">
                  <Avatar className="w-6 h-6">
                    <AvatarFallback className="text-xs">
                      {post.author.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm text-gray-600">{post.author.name}</span>
                </div>
                <div className="text-xs text-gray-500">
                  {post.status === 'published' && post.publishedAt ? (
                    formatDistanceToNow(new Date(post.publishedAt), { addSuffix: true })
                  ) : post.status === 'scheduled' && post.scheduledFor ? (
                    `Scheduled for ${new Date(post.scheduledFor).toLocaleDateString()}`
                  ) : (
                    formatDistanceToNow(new Date(post.createdAt), { addSuffix: true })
                  )}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="flex gap-2 pt-2">
                <Button size="sm" variant="outline" className="flex-1" asChild>
                  <Link href={`/super-admin/platform-blog/posts/${post.id}`}>
                    <Eye className="w-3 h-3 mr-1" />
                    View
                  </Link>
                </Button>
                <Button size="sm" variant="outline" className="flex-1" asChild>
                  <Link href={`/super-admin/platform-blog/posts/${post.id}/edit`}>
                    <Edit className="w-3 h-3 mr-1" />
                    Edit
                  </Link>
                </Button>
                {post.status === 'draft' && (
                  <Button
                    size="sm"
                    className="flex-1"
                    onClick={() => publishPost(post.id)}
                  >
                    <Share2 className="w-3 h-3 mr-1" />
                    Publish
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {posts.length === 0 && (
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MessageSquare className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No platform blog posts found</h3>
            <p className="text-gray-500 mb-4">
              Start creating platform content to share updates, announcements, and news with your community.
            </p>
            <Button asChild>
              <Link href="/super-admin/platform-blog/posts/new">
                Create Your First Post
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
```

### **Platform Blog Sidebar Navigation**
**File**: `apps/frontend/src/components/super-admin/platform-blog/PlatformBlogSidebar.tsx`

```typescript
'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { usePlatformBlogStore } from '@/stores/super-admin/usePlatformBlogStore'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  PenTool,
  FileText,
  FolderOpen,
  BarChart3,
  Settings,
  Plus,
  AlertTriangle,
  Calendar,
  Archive,
  Users,
  TrendingUp
} from 'lucide-react'

const platformBlogNavItems = [
  {
    title: 'Overview',
    href: '/super-admin/platform-blog',
    icon: BarChart3,
    description: 'Platform blog analytics and insights'
  },
  {
    title: 'All Posts',
    href: '/super-admin/platform-blog/posts',
    icon: FileText,
    description: 'Manage all platform posts'
  },
  {
    title: 'Create Post',
    href: '/super-admin/platform-blog/posts/new',
    icon: PenTool,
    description: 'Write a new platform post'
  },
  {
    title: 'Announcements',
    href: '/super-admin/platform-blog/announcements',
    icon: AlertTriangle,
    description: 'Platform announcements'
  },
  {
    title: 'Categories',
    href: '/super-admin/platform-blog/categories',
    icon: FolderOpen,
    description: 'Organize platform categories'
  },
  {
    title: 'Scheduled',
    href: '/super-admin/platform-blog/scheduled',
    icon: Calendar,
    description: 'Scheduled platform posts'
  },
  {
    title: 'Drafts',
    href: '/super-admin/platform-blog/drafts',
    icon: Archive,
    description: 'Draft platform posts'
  },
  {
    title: 'Analytics',
    href: '/super-admin/platform-blog/analytics',
    icon: TrendingUp,
    description: 'Detailed platform analytics'
  },
  {
    title: 'Settings',
    href: '/super-admin/platform-blog/settings',
    icon: Settings,
    description: 'Platform blog configuration'
  }
]

export default function PlatformBlogSidebar() {
  const pathname = usePathname()
  const { posts, categories } = usePlatformBlogStore()

  // Get post counts by status
  const postCounts = posts.reduce((acc, post) => {
    acc[post.status] = (acc[post.status] || 0) + 1
    if (post.isAnnouncement) acc.announcements = (acc.announcements || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-full overflow-y-auto">
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">Platform Blog</h2>
          <Button size="sm" asChild>
            <Link href="/super-admin/platform-blog/posts/new">
              <Plus className="w-4 h-4 mr-1" />
              New Post
            </Link>
          </Button>
        </div>

        {/* Navigation */}
        <nav className="space-y-1 mb-6">
          {platformBlogNavItems.map((item) => {
            const isActive = pathname === item.href
            const Icon = item.icon

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                  isActive
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                )}
              >
                <Icon className="w-4 h-4 mr-3" />
                <span className="flex-1">{item.title}</span>
                {item.title === 'Drafts' && postCounts.draft > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {postCounts.draft}
                  </Badge>
                )}
                {item.title === 'Scheduled' && postCounts.scheduled > 0 && (
                  <Badge variant="outline" className="ml-2">
                    {postCounts.scheduled}
                  </Badge>
                )}
                {item.title === 'Announcements' && postCounts.announcements > 0 && (
                  <Badge variant="destructive" className="ml-2">
                    {postCounts.announcements}
                  </Badge>
                )}
              </Link>
            )
          })}
        </nav>

        {/* Quick Stats */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Quick Stats</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Total Posts</span>
              <span className="font-medium">{posts.length}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Published</span>
              <span className="font-medium text-green-600">{postCounts.published || 0}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Drafts</span>
              <span className="font-medium text-gray-600">{postCounts.draft || 0}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Announcements</span>
              <span className="font-medium text-orange-600">{postCounts.announcements || 0}</span>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-900">Categories</h3>
            <Button size="sm" variant="ghost" asChild>
              <Link href="/super-admin/platform-blog/categories/new">
                <Plus className="w-3 h-3" />
              </Link>
            </Button>
          </div>
          <div className="space-y-1">
            {categories.slice(0, 5).map((category) => (
              <Link
                key={category.id}
                href={`/super-admin/platform-blog/posts?category=${category.id}`}
                className="flex items-center justify-between px-2 py-1 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded"
              >
                <div className="flex items-center">
                  {category.color && (
                    <div
                      className="w-2 h-2 rounded-full mr-2"
                      style={{ backgroundColor: category.color }}
                    />
                  )}
                  <span className="truncate">{category.name}</span>
                </div>
                {category.postCount && (
                  <Badge variant="secondary" className="text-xs">
                    {category.postCount}
                  </Badge>
                )}
              </Link>
            ))}
            {categories.length > 5 && (
              <Link
                href="/super-admin/platform-blog/categories"
                className="block px-2 py-1 text-xs text-blue-600 hover:text-blue-800"
              >
                View all categories →
              </Link>
            )}
          </div>
        </div>

        {/* Target Audiences */}
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-3">Target Audiences</h3>
          <div className="space-y-1">
            <Link
              href="/super-admin/platform-blog/posts?audience=institutes"
              className="flex items-center justify-between px-2 py-1 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded"
            >
              <div className="flex items-center">
                <Users className="w-3 h-3 mr-2 text-blue-500" />
                <span>Institutes</span>
              </div>
            </Link>
            <Link
              href="/super-admin/platform-blog/posts?audience=students"
              className="flex items-center justify-between px-2 py-1 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded"
            >
              <div className="flex items-center">
                <Users className="w-3 h-3 mr-2 text-green-500" />
                <span>Students</span>
              </div>
            </Link>
            <Link
              href="/super-admin/platform-blog/posts?audience=staff"
              className="flex items-center justify-between px-2 py-1 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded"
            >
              <div className="flex items-center">
                <Users className="w-3 h-3 mr-2 text-purple-500" />
                <span>Staff</span>
              </div>
            </Link>
            <Link
              href="/super-admin/platform-blog/posts?audience=public"
              className="flex items-center justify-between px-2 py-1 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded"
            >
              <div className="flex items-center">
                <Users className="w-3 h-3 mr-2 text-yellow-500" />
                <span>Public</span>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
```

### **Platform Blog Management Main Page**
**File**: `apps/frontend/src/app/super-admin/platform-blog/page.tsx`

```typescript
'use client'

import { useEffect, useState } from 'react'
import { usePlatformBlogStore } from '@/stores/super-admin/usePlatformBlogStore'
import PlatformBlogListView from '@/components/super-admin/platform-blog/PlatformBlogListView'
import PlatformBlogCardView from '@/components/super-admin/platform-blog/PlatformBlogCardView'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Plus,
  Search,
  Filter,
  List,
  Grid,
  Download,
  MoreHorizontal,
  AlertTriangle,
  Users,
  Eye,
  TrendingUp
} from 'lucide-react'
import Link from 'next/link'

export default function PlatformBlogManagementPage() {
  const {
    posts,
    categories,
    postsLoading,
    analytics,
    viewMode,
    selectedStatus,
    selectedCategory,
    selectedTargetAudience,
    showAnnouncementsOnly,
    fetchPosts,
    fetchCategories,
    fetchAnalytics,
    setFilters,
    clearFilters,
    setViewMode
  } = usePlatformBlogStore()

  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    fetchPosts()
    fetchCategories()
    fetchAnalytics()
  }, [fetchPosts, fetchCategories, fetchAnalytics])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Implement search functionality
    fetchPosts({ search: searchQuery })
  }

  const handleFilterChange = (key: string, value: string | boolean) => {
    const newFilters = {
      status: selectedStatus,
      category: selectedCategory,
      targetAudience: selectedTargetAudience,
      showAnnouncementsOnly,
      [key]: value === 'all' ? null : value
    }
    setFilters(newFilters)
    fetchPosts()
  }

  const handleClearFilters = () => {
    clearFilters()
    setSearchQuery('')
    fetchPosts()
  }

  // Get counts for different post types
  const postCounts = posts.reduce((acc, post) => {
    acc[post.status] = (acc[post.status] || 0) + 1
    if (post.isAnnouncement) acc.announcements = (acc.announcements || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const hasActiveFilters = selectedStatus || selectedCategory || selectedTargetAudience || showAnnouncementsOnly

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Platform Blog Management</h1>
          <p className="text-gray-600 mt-1">
            Create and manage platform-wide content, announcements, and updates
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button asChild>
            <Link href="/super-admin/platform-blog/posts/new">
              <Plus className="w-4 h-4 mr-2" />
              Create Post
            </Link>
          </Button>
        </div>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Views</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.totalViews.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Eye className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            +12% from last month
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Posts</p>
              <p className="text-2xl font-bold text-gray-900">{posts.length}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div className="flex gap-2 mt-2">
            <Badge variant="secondary" className="text-xs">
              {postCounts.published || 0} published
            </Badge>
            <Badge variant="outline" className="text-xs">
              {postCounts.draft || 0} drafts
            </Badge>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Announcements</p>
              <p className="text-2xl font-bold text-gray-900">{postCounts.announcements || 0}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-orange-600" />
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Active platform announcements
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Audience Reach</p>
              <p className="text-2xl font-bold text-gray-900">
                {(analytics.instituteAdminViews + analytics.studentViews + analytics.staffViews).toLocaleString()}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Cross-platform engagement
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-lg border space-y-4">
        <div className="flex items-center gap-4">
          {/* Search */}
          <form onSubmit={handleSearch} className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search platform posts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </form>

          {/* Filters */}
          <div className="flex items-center gap-2">
            <Select value={selectedStatus || 'all'} onValueChange={(value) => handleFilterChange('status', value)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedCategory || 'all'} onValueChange={(value) => handleFilterChange('category', value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedTargetAudience || 'all'} onValueChange={(value) => handleFilterChange('targetAudience', value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Audience" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Audiences</SelectItem>
                <SelectItem value="institutes">Institutes</SelectItem>
                <SelectItem value="students">Students</SelectItem>
                <SelectItem value="staff">Staff</SelectItem>
                <SelectItem value="public">Public</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant={showAnnouncementsOnly ? "default" : "outline"}
              size="sm"
              onClick={() => handleFilterChange('showAnnouncementsOnly', !showAnnouncementsOnly)}
            >
              <AlertTriangle className="w-4 h-4 mr-1" />
              Announcements
            </Button>
          </div>

          {/* View Toggle */}
          <div className="flex items-center gap-1 border rounded-md">
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'card' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('card')}
            >
              <Grid className="w-4 h-4" />
            </Button>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Filter className="w-4 h-4 mr-2" />
                Advanced Filters
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="w-4 h-4 mr-2" />
                Export Data
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Active Filters */}
        {hasActiveFilters && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Active filters:</span>
            {selectedStatus && (
              <Badge variant="secondary">Status: {selectedStatus}</Badge>
            )}
            {selectedCategory && (
              <Badge variant="secondary">
                Category: {categories.find(c => c.id === selectedCategory)?.name}
              </Badge>
            )}
            {selectedTargetAudience && (
              <Badge variant="secondary">Audience: {selectedTargetAudience}</Badge>
            )}
            {showAnnouncementsOnly && (
              <Badge variant="secondary">Announcements Only</Badge>
            )}
            <Button variant="ghost" size="sm" onClick={handleClearFilters}>
              Clear all
            </Button>
          </div>
        )}
      </div>

      {/* Content */}
      {viewMode === 'list' ? (
        <PlatformBlogListView posts={posts} loading={postsLoading} />
      ) : (
        <PlatformBlogCardView posts={posts} loading={postsLoading} />
      )}
    </div>
  )
}
```

### **Platform Blog Editor with Formik & Yup**
**File**: `apps/frontend/src/components/super-admin/platform-blog/PlatformBlogEditor.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { usePlatformBlogStore, PlatformBlogPost } from '@/stores/super-admin/usePlatformBlogStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Save,
  Eye,
  Send,
  Calendar,
  Image,
  Tag,
  Settings,
  Users,
  AlertTriangle,
  X
} from 'lucide-react'
import dynamic from 'next/dynamic'
import { toast } from 'sonner'

// Dynamic import for rich text editor
const RichTextEditor = dynamic(() => import('@/components/ui/rich-text-editor'), {
  ssr: false,
  loading: () => <div className="h-64 bg-gray-100 animate-pulse rounded" />
})

interface PlatformBlogEditorProps {
  post?: PlatformBlogPost
  mode: 'create' | 'edit'
}

const platformBlogPostSchema = Yup.object({
  title: Yup.string().required('Title is required').max(200, 'Title too long'),
  slug: Yup.string().required('Slug is required').max(200, 'Slug too long'),
  excerpt: Yup.string().max(300, 'Excerpt too long'),
  content: Yup.string().required('Content is required'),
  targetAudience: Yup.string().required('Target audience is required'),
  category: Yup.string(),
  seoTitle: Yup.string().max(150, 'SEO title too long'),
  seoDescription: Yup.string().max(300, 'SEO description too long'),
  announcementExpiresAt: Yup.date().when('isAnnouncement', {
    is: true,
    then: (schema) => schema.required('Expiry date required for announcements'),
    otherwise: (schema) => schema.nullable()
  })
})

export default function PlatformBlogEditor({ post, mode }: PlatformBlogEditorProps) {
  const router = useRouter()
  const {
    categories,
    createPost,
    updatePost,
    publishPost,
    schedulePost,
    fetchCategories
  } = usePlatformBlogStore()

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentTag, setCurrentTag] = useState('')
  const [tags, setTags] = useState<string[]>(post?.tags.map(t => t.tag) || [])
  const [scheduledDate, setScheduledDate] = useState('')

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  const formik = useFormik({
    initialValues: {
      title: post?.title || '',
      slug: post?.slug || '',
      excerpt: post?.excerpt || '',
      content: post?.content || '',
      targetAudience: post?.targetAudience || 'all',
      category: post?.category?.id || '',
      featuredImage: post?.featuredImage?.url || '',
      isAnnouncement: post?.isAnnouncement || false,
      priorityLevel: post?.priorityLevel || 0,
      announcementExpiresAt: post?.announcementExpiresAt || '',
      seoTitle: post?.seo.title || '',
      seoDescription: post?.seo.description || '',
      allowComments: post?.settings.allowComments ?? true,
      isFeatured: post?.settings.isFeatured ?? false,
      isSticky: post?.settings.isSticky ?? false,
      showOnDashboard: post?.settings.showOnDashboard ?? false
    },
    validationSchema: platformBlogPostSchema,
    onSubmit: async (values) => {
      // This will be handled by specific action buttons
    }
  })

  // Auto-generate slug from title
  useEffect(() => {
    if (formik.values.title && !formik.values.slug) {
      const slug = formik.values.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
      formik.setFieldValue('slug', slug)
    }
  }, [formik.values.title])

  const handleAddTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim())) {
      setTags([...tags, currentTag.trim()])
      setCurrentTag('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleSaveDraft = async () => {
    setIsSubmitting(true)
    try {
      const postData = {
        ...formik.values,
        tags: tags.map(tag => ({ tag })),
        status: 'draft',
        seo: {
          title: formik.values.seoTitle,
          description: formik.values.seoDescription
        },
        settings: {
          allowComments: formik.values.allowComments,
          isFeatured: formik.values.isFeatured,
          isSticky: formik.values.isSticky,
          showOnDashboard: formik.values.showOnDashboard
        }
      }

      if (mode === 'create') {
        await createPost(postData)
        router.push('/super-admin/platform-blog')
      } else {
        await updatePost(post!.id, postData)
      }
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePublish = async () => {
    setIsSubmitting(true)
    try {
      const postData = {
        ...formik.values,
        tags: tags.map(tag => ({ tag })),
        status: 'published',
        publishedAt: new Date().toISOString(),
        seo: {
          title: formik.values.seoTitle,
          description: formik.values.seoDescription
        },
        settings: {
          allowComments: formik.values.allowComments,
          isFeatured: formik.values.isFeatured,
          isSticky: formik.values.isSticky,
          showOnDashboard: formik.values.showOnDashboard
        }
      }

      if (mode === 'create') {
        await createPost(postData)
      } else {
        await updatePost(post!.id, postData)
      }

      router.push('/super-admin/platform-blog')
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSchedule = async () => {
    if (!scheduledDate) {
      toast.error('Please select a schedule date')
      return
    }

    setIsSubmitting(true)
    try {
      const postData = {
        ...formik.values,
        tags: tags.map(tag => ({ tag })),
        status: 'scheduled',
        scheduledFor: scheduledDate,
        seo: {
          title: formik.values.seoTitle,
          description: formik.values.seoDescription
        },
        settings: {
          allowComments: formik.values.allowComments,
          isFeatured: formik.values.isFeatured,
          isSticky: formik.values.isSticky,
          showOnDashboard: formik.values.showOnDashboard
        }
      }

      if (mode === 'create') {
        await createPost(postData)
      } else {
        await updatePost(post!.id, postData)
      }

      router.push('/super-admin/platform-blog')
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">
          {mode === 'create' ? 'Create Platform Post' : 'Edit Platform Post'}
        </h1>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button
            variant="outline"
            onClick={handleSaveDraft}
            disabled={isSubmitting}
          >
            <Save className="w-4 h-4 mr-2" />
            Save Draft
          </Button>
          <Button
            onClick={handlePublish}
            disabled={isSubmitting}
          >
            <Send className="w-4 h-4 mr-2" />
            Publish
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Post Content</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  name="title"
                  value={formik.values.title}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="Enter post title..."
                  className={formik.errors.title && formik.touched.title ? 'border-red-500' : ''}
                />
                {formik.errors.title && formik.touched.title && (
                  <p className="text-sm text-red-600 mt-1">{formik.errors.title}</p>
                )}
              </div>

              <div>
                <Label htmlFor="slug">URL Slug</Label>
                <Input
                  id="slug"
                  name="slug"
                  value={formik.values.slug}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="url-friendly-slug"
                  className={formik.errors.slug && formik.touched.slug ? 'border-red-500' : ''}
                />
                {formik.errors.slug && formik.touched.slug && (
                  <p className="text-sm text-red-600 mt-1">{formik.errors.slug}</p>
                )}
              </div>

              <div>
                <Label htmlFor="excerpt">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  name="excerpt"
                  value={formik.values.excerpt}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="Brief summary of the post..."
                  rows={3}
                  className={formik.errors.excerpt && formik.touched.excerpt ? 'border-red-500' : ''}
                />
                {formik.errors.excerpt && formik.touched.excerpt && (
                  <p className="text-sm text-red-600 mt-1">{formik.errors.excerpt}</p>
                )}
              </div>

              <div>
                <Label htmlFor="content">Content</Label>
                <RichTextEditor
                  value={formik.values.content}
                  onChange={(value) => formik.setFieldValue('content', value)}
                  placeholder="Write your platform post content..."
                />
                {formik.errors.content && formik.touched.content && (
                  <p className="text-sm text-red-600 mt-1">{formik.errors.content}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Publishing Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                Publishing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="scheduledDate">Schedule for later</Label>
                <Input
                  id="scheduledDate"
                  type="datetime-local"
                  value={scheduledDate}
                  onChange={(e) => setScheduledDate(e.target.value)}
                />
                {scheduledDate && (
                  <Button
                    className="w-full mt-2"
                    variant="outline"
                    onClick={handleSchedule}
                    disabled={isSubmitting}
                  >
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule Post
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Target Audience & Category */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-4 h-4 mr-2" />
                Audience & Organization
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="targetAudience">Target Audience</Label>
                <Select
                  value={formik.values.targetAudience}
                  onValueChange={(value) => formik.setFieldValue('targetAudience', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select target audience" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Users</SelectItem>
                    <SelectItem value="institutes">Institute Admins</SelectItem>
                    <SelectItem value="students">Students</SelectItem>
                    <SelectItem value="staff">Staff Members</SelectItem>
                    <SelectItem value="public">General Public</SelectItem>
                  </SelectContent>
                </Select>
                {formik.errors.targetAudience && formik.touched.targetAudience && (
                  <p className="text-sm text-red-600 mt-1">{formik.errors.targetAudience}</p>
                )}
              </div>

              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formik.values.category}
                  onValueChange={(value) => formik.setFieldValue('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Tags</Label>
                <div className="flex gap-2 mb-2">
                  <Input
                    value={currentTag}
                    onChange={(e) => setCurrentTag(e.target.value)}
                    placeholder="Add tag..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                  />
                  <Button type="button" size="sm" onClick={handleAddTag}>
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-1">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X
                        className="w-3 h-3 cursor-pointer"
                        onClick={() => handleRemoveTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Announcement Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="w-4 h-4 mr-2" />
                Announcement Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="isAnnouncement">Mark as Announcement</Label>
                <Switch
                  id="isAnnouncement"
                  checked={formik.values.isAnnouncement}
                  onCheckedChange={(checked) => formik.setFieldValue('isAnnouncement', checked)}
                />
              </div>

              {formik.values.isAnnouncement && (
                <>
                  <div>
                    <Label htmlFor="priorityLevel">Priority Level</Label>
                    <Select
                      value={formik.values.priorityLevel.toString()}
                      onValueChange={(value) => formik.setFieldValue('priorityLevel', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">Normal</SelectItem>
                        <SelectItem value="1">Important</SelectItem>
                        <SelectItem value="2">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="announcementExpiresAt">Expires At</Label>
                    <Input
                      id="announcementExpiresAt"
                      name="announcementExpiresAt"
                      type="datetime-local"
                      value={formik.values.announcementExpiresAt}
                      onChange={formik.handleChange}
                      className={formik.errors.announcementExpiresAt && formik.touched.announcementExpiresAt ? 'border-red-500' : ''}
                    />
                    {formik.errors.announcementExpiresAt && formik.touched.announcementExpiresAt && (
                      <p className="text-sm text-red-600 mt-1">{formik.errors.announcementExpiresAt}</p>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Post Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="w-4 h-4 mr-2" />
                Post Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="allowComments">Allow Comments</Label>
                <Switch
                  id="allowComments"
                  checked={formik.values.allowComments}
                  onCheckedChange={(checked) => formik.setFieldValue('allowComments', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="isFeatured">Featured Post</Label>
                <Switch
                  id="isFeatured"
                  checked={formik.values.isFeatured}
                  onCheckedChange={(checked) => formik.setFieldValue('isFeatured', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="isSticky">Sticky Post</Label>
                <Switch
                  id="isSticky"
                  checked={formik.values.isSticky}
                  onCheckedChange={(checked) => formik.setFieldValue('isSticky', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="showOnDashboard">Show on Dashboards</Label>
                <Switch
                  id="showOnDashboard"
                  checked={formik.values.showOnDashboard}
                  onCheckedChange={(checked) => formik.setFieldValue('showOnDashboard', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* SEO */}
          <Card>
            <CardHeader>
              <CardTitle>SEO Optimization</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="seoTitle">SEO Title</Label>
                <Input
                  id="seoTitle"
                  name="seoTitle"
                  value={formik.values.seoTitle}
                  onChange={formik.handleChange}
                  placeholder="SEO optimized title..."
                  maxLength={150}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {formik.values.seoTitle.length}/150 characters
                </p>
              </div>

              <div>
                <Label htmlFor="seoDescription">SEO Description</Label>
                <Textarea
                  id="seoDescription"
                  name="seoDescription"
                  value={formik.values.seoDescription}
                  onChange={formik.handleChange}
                  placeholder="SEO meta description..."
                  rows={3}
                  maxLength={300}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {formik.values.seoDescription.length}/300 characters
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
```

## 🚀 Implementation Guidelines

### **Phase Implementation Order**

**Week 1: Backend Foundation**
- Create platform blog database tables
- Implement Payload collections for platform posts and categories
- Set up API endpoints for CRUD operations
- Configure access control for Super Admin only

**Week 2: Core Frontend**
- Implement Zustand store for platform blog management
- Create basic list and card view components
- Build platform blog editor with Formik and Yup validation
- Set up routing and navigation

**Week 3: Advanced Features**
- Add announcement system with priority levels
- Implement target audience controls
- Create analytics dashboard
- Add bulk operations and filtering

**Week 4: Polish & Testing**
- Implement search functionality
- Add toast notifications and error handling
- Create responsive design optimizations
- Comprehensive testing and bug fixes

### **Key Differences from Institute Blog System**

| Feature | Institute Blog | Platform Blog |
|---------|---------------|---------------|
| **Access Control** | Institute staff only | Super Admin only |
| **Scope** | Institute-specific | Platform-wide |
| **Audience** | Institute students/staff | All users across platform |
| **Content Type** | Institute news/events | LMS updates/announcements |
| **Database** | `blog_posts` table | `platform_blog_posts` table |
| **Categories** | Institute categories | Platform categories |
| **Analytics** | Institute-level | Cross-platform metrics |

### **Content Strategy Recommendations**

**Platform Blog Content Types:**
1. **LMS Updates**: New features, improvements, bug fixes
2. **Platform Announcements**: Maintenance, policy changes, important notices
3. **Industry News**: EdTech trends, educational insights
4. **Best Practices**: How-to guides, tips for institutes
5. **Success Stories**: Institute achievements, case studies
6. **Policy Updates**: Terms of service, privacy policy changes

**Content Calendar Suggestions:**
- **Weekly**: Feature updates and improvements
- **Monthly**: Industry insights and best practices
- **Quarterly**: Major announcements and roadmap updates
- **As needed**: Critical announcements and maintenance notices

## 📊 Analytics & Reporting

### **Platform Blog Metrics**
```typescript
interface PlatformBlogAnalytics {
  // Overall engagement
  totalViews: number
  totalUniqueViews: number
  totalLikes: number
  totalComments: number
  totalShares: number

  // Audience breakdown
  instituteAdminViews: number
  studentViews: number
  staffViews: number
  publicViews: number

  // Content performance
  topPerformingPosts: PlatformBlogPost[]
  categoryPerformance: CategoryAnalytics[]
  announcementReach: AnnouncementMetrics[]

  // Engagement trends
  dailyEngagement: DailyMetrics[]
  audienceGrowth: AudienceGrowthMetrics[]
}
```

### **Success Metrics**
- **Reach**: Number of users viewing platform content
- **Engagement**: Comments, likes, shares per post
- **Announcement Effectiveness**: View rates for critical announcements
- **Content Quality**: Average time spent reading posts
- **Cross-Platform Impact**: Content driving institute engagement

## 🔒 Security & Access Control

### **Role-Based Permissions**
```typescript
// Platform blog access control
const platformBlogAccess = {
  read: () => true, // Everyone can read published posts
  create: (user) => user.role === 'super_admin' || user.legacyRole === 'super_admin',
  update: (user) => user.role === 'super_admin' || user.legacyRole === 'super_admin',
  delete: (user) => user.role === 'super_admin', // Only Super Admin can delete
  moderate: (user) => user.role === 'super_admin' || user.legacyRole === 'super_admin'
}
```

### **Content Moderation**
- **Auto-moderation**: Spam detection for comments
- **Manual review**: Super Admin approval for sensitive content
- **Content guidelines**: Platform-appropriate content standards
- **Audit trail**: Track all content changes and deletions

## 📱 Mobile Responsiveness

### **Responsive Design Considerations**
- **Mobile-first approach**: Optimized for mobile viewing
- **Touch-friendly interface**: Large buttons and touch targets
- **Readable typography**: Appropriate font sizes for mobile
- **Fast loading**: Optimized images and content delivery
- **Offline reading**: Cache popular posts for offline access

## 🔄 Integration Points

### **Cross-Platform Distribution**
```typescript
// Show platform posts on institute dashboards
const instituteDashboardContent = {
  platformAnnouncements: [], // Critical announcements
  featuredPosts: [], // Featured platform content
  relevantUpdates: [] // Audience-targeted content
}
```

### **Notification System**
- **Email notifications**: For critical announcements
- **In-app notifications**: For new platform updates
- **Dashboard alerts**: For time-sensitive information
- **RSS feeds**: For external content syndication

## 📋 Deployment Checklist

### **Database Setup**
- [ ] Create platform blog tables
- [ ] Set up proper indexes for performance
- [ ] Configure foreign key relationships
- [ ] Insert default platform categories

### **Backend Configuration**
- [ ] Deploy Payload collections
- [ ] Configure API endpoints
- [ ] Set up access control middleware
- [ ] Test CRUD operations

### **Frontend Deployment**
- [ ] Build and deploy React components
- [ ] Configure routing and navigation
- [ ] Test responsive design
- [ ] Verify form validation

### **Content Migration**
- [ ] Create initial platform categories
- [ ] Import any existing platform content
- [ ] Set up content templates
- [ ] Train Super Admin team on usage

## 🎯 Success Metrics & KPIs

### **Content Metrics**
- **Publishing Frequency**: Posts per week/month
- **Content Quality**: Average reading time and engagement
- **Announcement Reach**: Percentage of target audience reached
- **Content Variety**: Distribution across content types

### **User Engagement**
- **Cross-Platform Views**: Views from different user types
- **Comment Activity**: Community engagement levels
- **Social Sharing**: Content virality and reach
- **Return Readership**: Users returning to read new content

### **Business Impact**
- **Platform Adoption**: Increased user engagement with LMS
- **Support Reduction**: Fewer support tickets due to better communication
- **Feature Adoption**: Faster adoption of new LMS features
- **Community Building**: Stronger platform community engagement

---

## 📝 Summary

This Phase 15 Super Admin Platform Blog Management System provides:

✅ **Separate Platform Content**: Independent from institute blogs with dedicated tables and management
✅ **Super Admin Control**: Exclusive access for Super Admin and Super Admin Staff
✅ **Advanced Publishing**: WordPress-like features with announcements and priority levels
✅ **Target Audience Control**: Content targeting for different user types
✅ **List & Card Views**: Flexible content display options with Formik and Yup validation
✅ **Cross-Platform Distribution**: Content visible across all institutes and user types
✅ **Comprehensive Analytics**: Platform-wide engagement metrics and reporting
✅ **Mobile Responsive**: Optimized for all devices and screen sizes

### **Key Benefits:**
- **Centralized Communication**: Single source for platform-wide updates and announcements
- **Professional Publishing**: WordPress-like interface for high-quality content creation
- **Targeted Messaging**: Audience-specific content delivery
- **Analytics-Driven**: Data-driven content strategy and optimization
- **Scalable Architecture**: Designed to handle growth and increased content volume
- **SEO Optimized**: Built-in SEO features for better discoverability

**Recommended Implementation Timeline**: 4 weeks for full feature set
**Estimated Development Effort**: 2-3 developers working in parallel
**Go-Live Strategy**: Phased rollout starting with basic posting, then advanced features

இந்த system Super Admin-க்கு **complete platform-level blog management** provide பண்ணும் with all the advanced features you requested! 🚀
```
