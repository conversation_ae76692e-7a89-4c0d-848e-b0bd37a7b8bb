# 📚 Phase 16: Course Management System - Complete Implementation Guide

## 🎯 Overview
This document provides a comprehensive guide for implementing the Course Management system, with specific focus on Institute Admin operations, including course structure, lesson management, mock exams, question banks, assessment features, and daily operational procedures.

## 🏗️ Institute Admin Role & Permissions

### **Institute Admin Capabilities**
- **Course Management**: Create, edit, and manage all courses within the institute
- **Content Approval**: Approve/reject lessons and content created by trainers
- **Question Bank Management**: Oversee question creation and organization
- **Exam Scheduling**: Schedule and manage live exams and mock tests
- **Student Management**: Monitor student progress and performance
- **Analytics Access**: View detailed reports and analytics
- **Staff Management**: Manage trainers, course admins, and other staff

### **Access Restrictions**
- Cannot access other institutes' content
- Cannot modify super admin settings
- Cannot create new institutes or admin accounts
- Limited to institute-specific features and data

## 🏗️ Course Structure Hierarchy

### **1. Category and Exam Type Management**
The system uses a hierarchical structure for organizing courses:
- **Categories** → **Exam Types** → **Courses** → **Sub-Titles** → **Lessons**

#### **Category Examples:**
- Government Exams
- Banking & Finance
- Engineering Entrance
- Medical Entrance
- Teaching Exams
- Professional Courses

#### **Exam Type Examples (by Category):**
```
Government Exams:
├── TNPSC Group 1
├── TNPSC Group 2
├── TNUSRB
├── UPSC Civil Services
├── SSC CGL
└── Railway Group D

Banking & Finance:
├── Bank PO
├── Bank Clerk
├── IBPS
└── RBI Grade B

Engineering Entrance:
├── JEE Main
├── JEE Advanced
├── GATE
└── State Engineering Exams
```

### **2. Hierarchical Course Organization**
```
Institute
├── Categories (Government Exams, Banking, etc.)
│   ├── Exam Types (TNPSC Group 1, Bank PO, etc.)
│   │   ├── Courses (Indian Polity, Quantitative Aptitude, etc.)
│   │   │   ├── Sub-Titles (Constitutional Framework, Number Systems, etc.)
│   │   │   │   ├── Lessons (Fundamental Rights, Percentages, etc.)
│   │   │   │   │   ├── Content (Blog posts, study materials)
│   │   │   │   │   ├── Videos (Lecture videos, explanations)
│   │   │   │   │   ├── Documents (PDFs, study guides)
│   │   │   │   │   ├── Quizzes (Practice questions)
│   │   │   │   │   └── Mock Exams (Assessment tests)
```

### **3. Course Components**
Each course consists of:
- **A) Content** - Written materials and blog posts
- **B) Mock Exam** - Practice tests and assessments
- **C) Video Class** - Recorded video lessons
- **D) Live Class** - Real-time interactive sessions
- **E) Live Exam** - Scheduled live assessments

### **4. Course Information Structure**
```
Course
├── Category (Government Exams, Banking, etc.)
├── Exam Type (TNPSC Group 1, Bank PO, etc.)
├── Title (Indian Polity, Quantitative Aptitude, etc.)
├── Subtitle (Course subtitle)
├── Sub-Titles (e.g., "Constitutional Framework")
│   └── Lessons
│       ├── Title
│       ├── Content (Blog format)
│       ├── Videos (with on/off option)
│       ├── Documents (copy-paste only)
│       ├── Quiz (MCQ with solutions)
│       └── Mock Exam
└── Settings & Configuration
```

## 📖 Lesson Management

### **Lesson Structure**
Each lesson includes:
1. **Content (Blog)** - Detailed written material
2. **Videos** - Supplementary video explanations with toggle option
3. **Documents** - Downloadable study resources (strict copy-paste only)
4. **Mock Exam** - Practice tests for self-assessment
5. **Quiz** - Multiple-choice questions with solutions

### **Document Management**
- **E-documents** with watermark (institute logo) can be downloaded
- Only strict copy-paste allowed for document creation
- Watermarked PDF generation for offline access

### **Review & Approval System**
- All lessons require admin approval before going live
- Draft → Review → Published workflow
- Admin can approve/reject lessons with feedback

## 🎯 Mock Exams & Question Banks

### **Question Bank System Overview**

#### **Question Bank Creation Process**

**Option 1: Create Questions from Lessons**
```
Lesson → Quiz/Mock Exam → Questions → Question Bank
```
- When you create a lesson, you can add quiz questions
- These questions automatically go into your question bank
- You can reuse these questions in other exams

**Option 2: Direct Question Bank Creation**
```
Question Bank → Create Questions → Use in Any Exam/Test
```
- Create questions directly in the question bank
- Not tied to any specific lesson
- Can be used across multiple courses and exams

#### **Question Bank Structure**
```
Question Bank
├── Subject Categories (Indian Polity, Economy, Geography)
├── Difficulty Levels (Easy, Medium, Hard)
├── Question Types (MCQ, True/False, Aptitude)
├── Languages (Tamil, English, Malayalam)
└── Tags (Previous Year, Mock Test, Practice)
```

#### **Question Bank Sources**
- **Lesson Quizzes** → Auto-added to question bank
- **Direct Creation** → Manual question entry
- **Bulk Upload** → CSV/Excel import
- **Previous Year Papers** → Historical question import
- **Mock Exam Questions** → Reusable question pool

### **Live Test Question Generation**

#### **Automatic Question Selection**
```
Live Test Creation → Select Criteria → Auto-Generate Question Paper
```

**Selection Criteria:**
- Subject: Indian Polity (10 questions)
- Difficulty: Mixed (3 Easy + 5 Medium + 2 Hard)
- Question Type: MCQ only
- Language: Tamil + English
- Source: Question Bank + Previous Year

#### **Manual Question Selection**
```
Live Test Creation → Browse Question Bank → Pick Specific Questions
```

#### **Real-time Generation Process:**
1. **Test Configuration**
   - Set question count (e.g., 50 questions)
   - Choose subjects and weightage
   - Select difficulty distribution
   - Pick languages

2. **Dynamic Question Selection**
   - System picks questions from question bank based on criteria
   - Randomizes question order
   - Randomizes option order (if enabled)
   - Generates unique question paper for each test

3. **Live Test Display**
   - Questions appear one by one during live test
   - Timer runs for each question
   - Students can navigate between questions
   - Real-time answer submission

#### **Live Test Question Flow**
```
Live Test Start
    ↓
Question Bank Query (based on criteria)
    ↓
Generate Question Set (randomized)
    ↓
Display Questions (one by one or all at once)
    ↓
Collect Answers (real-time)
    ↓
Auto-Submit (when time ends)
    ↓
Generate Results (immediate or delayed)
```

#### **Practical Example: TNPSC Mock Test**
**Question Bank has:**
- 500 Indian Polity questions
- 300 Economy questions
- 200 Geography questions

**Live Test Setup:**
- Need 100 questions total
- 40 from Polity, 35 from Economy, 25 from Geography
- Mix of Easy (30%), Medium (50%), Hard (20%)

**System Auto-Generates:**
- Picks 40 random Polity questions (12 Easy + 20 Medium + 8 Hard)
- Picks 35 random Economy questions (10 Easy + 18 Medium + 7 Hard)
- Picks 25 random Geography questions (8 Easy + 12 Medium + 5 Hard)
- Randomizes the final order

### **Mock Exam Creation Process**
1. **User Access**: Admin/Teachers can create mock exams
2. **Unit System**: Create multiple units with questions
3. **Question Upload**: Bulk upload or individual question creation
4. **Preview & Review**: Generate preview before saving
5. **Edit & Republish**: Make corrections and re-preview
6. **Enable/Disable**: Toggle individual questions ON/OFF

### **Question Management**
- **Question Structure**:
  - Question text
  - Multiple options (configurable count)
  - Correct answer
  - Solution/Explanation
  - Hints/Suggestions (optional/mandatory)

### **Question Upload Methods**
- **Bulk Upload**: Structured format with columns:
  - Serial Number
  - Exam Details
  - Question
  - Options
  - Answer
  - Explanation
- **Individual Addition**: One-by-one question creation

### **Key Benefits**
✅ **Reusable Questions** - Create once, use multiple times
✅ **Dynamic Generation** - Different question sets each time
✅ **Smart Selection** - Based on difficulty, subject, etc.
✅ **Real-time Updates** - Add questions anytime, use immediately
✅ **Analytics** - Track which questions are most/least answered correctly

## ⚙️ Question Options & Settings

### **Option Count Configuration**
- Configurable number of options per question (2, 3, 4, or more)
- Validation ensures all option fields are filled
- Prevents saving incomplete questions

### **Option Display Settings**
Mentors can choose display format:
1. **Single Column View** - One option per line
2. **Two Columns View** - Two options per row
3. **Content-Based Layout** - Auto-adjust based on content length/type

### **Question Enhancement Features**
- **Suggestion Field (Hint)**: Optional guidance for students
- **Solution Display**: Shown after answer or test completion
- **Multilingual Support**: Up to 2 languages per question
- **Difficulty Levels**: Easy, Medium, Hard classification

## 🧠 Aptitude Tests

### **Mental Ability & Aptitude Features**
- Video solutions (maximum 5 minutes per question)
- Random question grouping option
- Offline PDF download with watermarked logo
- Specialized aptitude question types

### **OMR Sheet Format**
PDF generation includes:
1. **Page 1**: Institute details & contact number
2. **Page 2**: Logo with syllabus
3. **Page 3**: Instructions (English & native language)
4. **Page 4**: OMR sheet
5. **Page 5+**: Questions with options

## 🌐 Multi-Language Support

### **Supported Languages**
- Tamil
- Malayalam
- Kannada
- Telugu
- English
- Combined language options

### **Language Features**
- Questions support two languages
- Options displayed in grid format
- Language-based order and word length constraints
- Clear presentation for multilingual content

## ⏱️ Live Exam Features

### **Real-Time Exam Management**
- **Countdown Timer**: Shows remaining time (special alert for last 5 minutes)
- **Exam Navigation**: Save answers and move between questions
- **Results & Rankings**: Display results with rank table
- **Zoom Integration**: Questions displayed on Zoom screen
- **Polling System**: Students poll their answers
- **Top 10 Display**: Show top-ranked students

### **Exam Modes**
1. **Quick Result**: Instant feedback per question
2. **On-Submit Result**: Results after complete test submission

## 📊 Results & Analytics

### **Result Features**
- Downloadable answer key (one-page document)
- Each question shows correct answer and explanation
- Appreciation status based on marks:
  - Excellent
  - Very Good
  - Good
  - OK
  - Needs Practice

### **Configurable Appreciation Levels**
- Trainers can configure appreciation thresholds
- Custom messaging for different score ranges
- Motivational feedback system

## 📚 Previous Year Question Papers

### **Question Paper Organization Structure**
```
Previous Year Questions
├── Year (2023, 2022, 2021, 2020...)
├── Exam Type (TNPSC, TNUSRB, UPSC, Bank Exams)
├── Subject (Indian Polity, Economy, Geography, GK)
├── Lesson/Topic (Constitution, Fundamental Rights, etc.)
└── Question Details (Question, Options, Answer, Explanation)
```

### **Question Paper Update Process**

#### **Step-by-Step Selection:**
1. **Choose Year** → 2023 → 2022 → 2021 → 2020...
2. **Choose Exam Type** → TNPSC Group 1 → TNPSC Group 2 → TNUSRB → Bank PO
3. **Choose Subject** → Indian Polity → Economy → Geography → General Science
4. **Choose Lesson/Topic** → Constitution → Fundamental Rights → Parliament → Judiciary
5. **Update Questions** → Add/Edit/Delete questions for that specific combination

#### **Practical Example: Adding TNPSC 2023 Indian Polity Questions**
**Filter Selection:**
- Year: 2023
- Exam: TNPSC Group 1
- Subject: Indian Polity
- Lesson: Fundamental Rights

**Question Entry:**
```
Question: Which Article deals with Right to Education?
Options: A) Article 19  B) Article 21A  C) Article 32  D) Article 14
Answer: B) Article 21A
Explanation: Article 21A provides Right to Education for children aged 6-14
Year: 2023
Exam: TNPSC Group 1
Subject: Indian Polity
Lesson: Fundamental Rights
```

### **Bulk Upload Format**
```csv
Year,Exam,Subject,Lesson,Question,Option_A,Option_B,Option_C,Option_D,Answer,Explanation
2023,TNPSC Group 1,Indian Polity,Fundamental Rights,"Which Article deals with Right to Education?",Article 19,Article 21A,Article 32,Article 14,B,"Article 21A provides Right to Education"
2023,TNPSC Group 1,Economy,Banking,"RBI Governor term is",3 years,4 years,5 years,6 years,C,"RBI Governor serves for 5 years"
```

### **Advanced Search & Filter System**

#### **Search Options:**
- **By Year:** 2023, 2022, 2021...
- **By Exam:** TNPSC, TNUSRB, UPSC...
- **By Subject:** Polity, Economy, Geography...
- **By Lesson:** Constitution, Parliament...
- **By Keywords:** "Fundamental Rights", "RBI", "Constitution"

#### **Filter Combinations:**
- Year: 2023 + Subject: Indian Polity = All 2023 Polity questions
- Exam: TNPSC + Lesson: Constitution = All TNPSC Constitution questions
- Subject: Economy + Keywords: "Banking" = All Economy Banking questions

### **Question Analytics & Frequency Tracking**

#### **Heat Map Display:**
```
Question: "Who is the Father of Indian Constitution?"
├── 2023: Asked 2 times (TNPSC, TNUSRB)
├── 2022: Asked 1 time (TNPSC)
├── 2021: Asked 3 times (TNPSC, Bank PO, SSC)
├── 2020: Asked 1 time (UPSC)
└── Total Frequency: 7 times in 4 years
```

#### **Subject-wise Question Distribution Example (TNPSC 2023):**
```
Indian Polity: 25 questions
├── Constitution: 8 questions
├── Fundamental Rights: 6 questions
├── Parliament: 5 questions
├── Judiciary: 4 questions
└── Others: 2 questions

Economy: 20 questions
├── Banking: 7 questions
├── Budget: 5 questions
├── Economic Planning: 4 questions
└── Others: 4 questions
```

### **Update Workflow for Admin/Trainers**
1. **Login** → Institute Admin/Trainer Dashboard
2. **Navigate** → Previous Year Questions
3. **Filter** → Year + Exam + Subject + Lesson
4. **Action** → Add New / Edit Existing / Delete
5. **Bulk Upload** → CSV file with multiple questions
6. **Review** → Preview before saving
7. **Publish** → Make available to students

### **Student Access Features**
- **Browse by Year:** See all available years
- **Filter by Subject:** Focus on specific subjects
- **Search Questions:** Find specific topics
- **Practice Mode:** Take tests from previous years
- **Analytics:** See which questions appear frequently

### **Smart Recommendations & Heat Map Analytics**

#### **Suggested Similar Questions with Heat Map**
```typescript
// Heat map data structure
{
  questionId: string
  similarQuestions: Array<{
    questionId: string
    similarity: number // 0-100%
    topic: string
    difficulty: string
  }>
  heatMapData: {
    frequencyOverYears: Array<{
      year: number
      count: number
      examName: string
    }>
    yearWiseRepetition: Array<{
      year: number
      repetitionCount: number
      trend: 'increasing' | 'decreasing' | 'stable'
    }>
    difficultyTrends: Array<{
      year: number
      difficulty: 'easy' | 'medium' | 'hard'
      studentSuccessRate: number
    }>
  }
}
```

#### **Heat Map Visualization Features**
- **Question Frequency**: Visual representation of how often questions appear
- **Year-wise Trends**: Color-coded chart showing repetition patterns
- **Difficulty Analysis**: Track if questions become easier/harder over time
- **High-Weightage Identification**: Highlight frequently asked questions
- **Topic Importance**: Show which topics are gaining/losing relevance

#### **Global Search Options**
- **Cross-Exam Search**: Search across different exam types (TNPSC, TNUSRB, UPSC)
- **Content Type Search**: Search by course content, lessons, questions
- **Individual Search Options**:
  - Bundle-wise search
  - Course-wise search
  - Lesson-wise search
  - Question-wise search
- **Advanced Filters**: Combine multiple search criteria
- **Search History**: Save and reuse frequent searches

#### **Session Tracking for Trainer Uploads**
```typescript
// Session tracking structure
{
  uploadSessionId: string
  trainerId: string
  sessionTimestamp: Date
  questionsUploaded: Array<{
    questionId: string
    uploadTime: Date
    status: 'draft' | 'pending' | 'approved'
  }>
  sessionMetadata: {
    examType: string
    subject: string
    totalQuestions: number
    uploadMethod: 'individual' | 'bulk'
  }
}
```

### **Bulk List Display for Courses**
- **Course-Level Question Lists**: Display all questions for a course in bulk format
- **Easy Review Access**: Students can review multiple questions at once
- **Filtering Options**: Filter by difficulty, topic, previous year
- **Batch Operations**: Select multiple questions for practice tests
- **Export Options**: Download question lists for offline study

## 📈 Analytics & Reporting

### **Subject-wise Distribution**
Generate breakdown of questions by subject for specific exams:
```
Example Output:
- Indian Polity: 10 questions
- Economy: 15 questions
- Geography: 7 questions
- General Science: 13 questions
```

### **Performance Analytics**
- Question appearance frequency in previous years
- Subject-wise weightage analysis
- Student performance trends
- Difficulty level distribution

## 🔧 Assessment Configuration

### **Quiz Settings**
- Default: 10 questions per quiz (configurable by admin)
- Question count settings managed by mentor/admin
- Timer configuration for different question types
- Randomization options for question order

### **Exam Types**
- **Lesson Mock Exam**: Covers individual lessons
- **Full Mock Exam**: Covers entire sub-title
- **Previous Year Papers**: Historical question access
- **Live Exams**: Real-time scheduled assessments

## 🎮 Interactive Features

### **Student Engagement**
- Bookmark questions for later review
- Progress tracking across lessons
- Achievement badges and certificates
- Discussion forums for each lesson

### **Trainer Tools**
- Question analytics and performance insights
- Student progress monitoring
- Bulk operations for question management
- Export capabilities for offline use

## 🔒 Access Control & Permissions

### **Role-Based Access**
- **Institute Admin**: Full course management access
- **Trainers/Mentors**: Question creation and lesson management
- **Students**: View and attempt assigned content
- **Staff**: Limited access based on assigned permissions

### **Content Approval Workflow**
- Draft → Review → Published states
- Admin approval required for lesson publication
- Version control for content updates
- Audit trail for all changes

---

## 📋 Implementation Checklist

### **Phase 16.1: Core Course Structure**
- [ ] Exam type management
- [ ] Course creation and editing
- [ ] Sub-title and lesson hierarchy
- [ ] Basic content management

### **Phase 16.2: Question & Assessment System**
- [ ] Question bank creation
- [ ] Mock exam builder
- [ ] Quiz system implementation
- [ ] Answer validation and scoring

### **Phase 16.3: Advanced Features**
- [ ] Multi-language support
- [ ] Live exam functionality
- [ ] Analytics and reporting
- [ ] OMR sheet generation

### **Phase 16.4: Integration & Testing**
- [ ] Previous year question integration
- [ ] Search and recommendation system
- [ ] Performance optimization
- [ ] User acceptance testing

---

## 🗄️ Database Schema Requirements

### **Core Collections**

#### **Categories Collection**
```typescript
{
  id: string
  institute_id?: ObjectId // Reference to institutes (null for global categories)
  branch_id?: ObjectId // Reference to branches (null for institute-wide categories)
  name: string // "Government Exams", "Banking & Finance", "Engineering Entrance"
  description: string
  slug: string // "government-exams", "banking-finance"
  icon?: string // Icon class or URL for category display
  color?: string // Hex color code for category theming
  orderIndex: number // For custom ordering of categories

  // Metadata
  isActive: boolean
  isPublic: boolean // Whether visible to students
  isGlobal: boolean // Whether this is a global category (super admin created)

  // Sharing settings
  shareSettings: {
    shareWithBranches: boolean // Allow sharing with other branches
    shareWithMarketplace: boolean // Allow sharing in marketplace
    allowBranchCustomization: boolean // Allow branches to customize this category
  }

  // Branch sharing
  sharedBranches: ObjectId[] // Array of branch IDs this category is shared with
  originalBranch?: ObjectId // Original branch if this is a shared category
  isSharedCategory: boolean // Whether this is a shared category from another branch

  // Analytics
  courseCount: number // Auto-calculated number of courses
  studentCount: number // Auto-calculated number of enrolled students
  examTypeCount: number // Auto-calculated number of exam types

  // Audit fields
  createdBy: ObjectId
  createdAt: Date
  updatedAt: Date
}
```

#### **ExamTypes Collection**
```typescript
{
  id: string
  institute_id?: ObjectId // Reference to institutes (null for global exam types)
  branch_id?: ObjectId // Reference to branches (null for institute-wide exam types)
  category_id: ObjectId // Reference to categories
  name: string // "TNPSC Group 1", "Bank PO", "JEE Main"
  shortName: string // "TNPSC-G1", "Bank-PO", "JEE-M"
  description: string
  slug: string // "tnpsc-group-1", "bank-po"

  // Exam details
  examPattern?: string // "Prelims + Mains + Interview"
  duration?: string // "3 hours", "2 days"
  totalMarks?: number
  passingMarks?: number
  negativeMarking?: boolean

  // Exam schedule (if applicable)
  examFrequency?: string // "Annual", "Bi-annual", "Monthly"
  nextExamDate?: Date
  registrationDeadline?: Date

  // Content organization
  subjects: string[] // ["Polity", "Economy", "Geography"]
  languages: string[] // ["English", "Tamil", "Hindi"]

  // Metadata
  isActive: boolean
  isPublic: boolean
  isGlobal: boolean // Whether this is a global exam type (super admin created)
  difficulty: 'beginner' | 'intermediate' | 'advanced'

  // Sharing settings
  shareSettings: {
    shareWithBranches: boolean // Allow sharing with other branches
    shareWithMarketplace: boolean // Allow sharing in marketplace
    allowBranchCustomization: boolean // Allow branches to customize this exam type
  }

  // Branch sharing
  sharedBranches: ObjectId[] // Array of branch IDs this exam type is shared with
  originalBranch?: ObjectId // Original branch if this is a shared exam type
  isSharedExamType: boolean // Whether this is a shared exam type from another branch

  // Analytics
  courseCount: number // Auto-calculated
  studentCount: number // Auto-calculated
  successRate?: number // Overall success rate percentage

  // Audit fields
  createdBy: ObjectId
  createdAt: Date
  updatedAt: Date
}
```

#### **CourseSubtitles Collection**
```typescript
{
  id: string
  course: ObjectId // Reference to courses
  title: string
  description: string
  orderIndex: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}
```

#### **Lessons Collection**
```typescript
{
  id: string
  institute_id: ObjectId // Reference to institutes
  branch_id?: ObjectId // Reference to branches (optional for institute-wide lessons)
  course_id: ObjectId // Reference to courses
  subtitle_id: ObjectId // Reference to course_subtitles
  title: string
  description: string
  content: RichText // Blog content
  contentType: 'video' | 'document' | 'quiz' | 'mixed'

  // Video settings
  videoUrl?: string
  videoDuration?: number
  videoEnabled: boolean

  // Document settings
  documents: Array<{
    title: string
    content: string // Copy-paste content only
    downloadable: boolean
    watermarked: boolean
  }>

  // Quiz settings
  hasQuiz: boolean
  quizQuestionCount: number

  // Sharing settings
  shareSettings: {
    shareWithBranches: boolean // Allow sharing with other branches
    shareWithMarketplace: boolean // Allow sharing in marketplace
  }

  // Branch sharing
  sharedBranches: ObjectId[] // Array of branch IDs this lesson is shared with
  originalBranch?: ObjectId // Original branch if this is a shared lesson
  isSharedLesson: boolean // Whether this is a shared lesson from another branch

  // Status and approval
  status: 'draft' | 'pending_review' | 'approved' | 'published' | 'rejected'
  approvedBy?: ObjectId
  approvalNotes?: string

  orderIndex: number
  estimatedDuration: number // in minutes
  isActive: boolean
  createdBy: ObjectId
  createdAt: Date
  updatedAt: Date
}
```

#### **MockExams Collection**
```typescript
{
  id: string
  institute_id: ObjectId // Reference to institutes
  branch_id?: ObjectId // Reference to branches (optional for institute-wide exams)
  course_id?: ObjectId // Optional - can be standalone
  subtitle_id?: ObjectId // Optional - can be lesson-specific
  lesson_id?: ObjectId // Optional - can be lesson-specific

  title: string
  description: string
  examType: 'lesson_quiz' | 'subtitle_exam' | 'full_course' | 'previous_year' | 'live_exam'

  // Exam configuration
  questionCount: number
  duration: number // in minutes
  passingScore: number
  maxAttempts: number

  // Display settings
  resultMode: 'instant' | 'on_submit'
  showSolutions: boolean
  randomizeQuestions: boolean
  randomizeOptions: boolean

  // Scheduling (for live exams)
  isScheduled: boolean
  startTime?: Date
  endTime?: Date

  // Language support
  languages: string[] // ['en', 'ta', 'ml']
  defaultLanguage: string

  // Sharing settings
  shareSettings: {
    shareWithBranches: boolean // Allow sharing with other branches
    shareWithMarketplace: boolean // Allow sharing in marketplace
  }

  // Branch sharing
  sharedBranches: ObjectId[] // Array of branch IDs this exam is shared with
  originalBranch?: ObjectId // Original branch if this is a shared exam
  isSharedExam: boolean // Whether this is a shared exam from another branch

  // Appreciation levels
  appreciationLevels: Array<{
    minScore: number
    maxScore: number
    message: string
    level: 'excellent' | 'very_good' | 'good' | 'ok' | 'needs_practice'
  }>

  status: 'draft' | 'active' | 'archived'
  createdBy: ObjectId
  createdAt: Date
  updatedAt: Date
}
```

#### **Questions Collection**
```typescript
{
  id: string
  institute_id: ObjectId // Reference to institutes
  branch_id?: ObjectId // Reference to branches (optional for institute-wide questions)
  mockExam_id?: ObjectId // Optional - can be in question bank

  // Question content
  questionText: string
  questionTextSecondary?: string // For multilingual support
  questionType: 'multiple_choice' | 'true_false' | 'aptitude'

  // Options configuration
  optionCount: number // 2, 3, 4, or more
  options: Array<{
    text: string
    textSecondary?: string // For multilingual
    isCorrect: boolean
    orderIndex: number
  }>

  // Answer and explanation
  correctAnswer: string
  explanation: string
  explanationSecondary?: string
  hint?: string
  hintSecondary?: string

  // Video solution (for aptitude)
  videoSolutionUrl?: string
  videoSolutionDuration?: number // max 5 minutes

  // Metadata
  subject: string
  topic: string
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]

  // Previous year data
  isPreviousYear: boolean
  examYear?: number
  examName?: string
  frequency: number // How many times appeared

  // Sharing settings
  shareSettings: {
    shareWithBranches: boolean // Allow sharing with other branches
    shareWithMarketplace: boolean // Allow sharing in marketplace
  }

  // Branch sharing
  sharedBranches: ObjectId[] // Array of branch IDs this question is shared with
  originalBranch?: ObjectId // Original branch if this is a shared question
  isSharedQuestion: boolean // Whether this is a shared question from another branch

  // Display settings
  displayMode: 'single_column' | 'two_columns' | 'content_based'

  // Language and ordering
  languages: string[]
  orderIndex: number

  // Status
  isActive: boolean
  isEnabled: boolean // For ON/OFF toggle

  createdBy: ObjectId
  createdAt: Date
  updatedAt: Date
}
```

#### **ExamAttempts Collection**
```typescript
{
  id: string
  mockExam: ObjectId
  student: ObjectId

  // Attempt details
  attemptNumber: number
  startedAt: Date
  submittedAt?: Date
  duration: number // actual time taken

  // Responses
  responses: Array<{
    questionId: ObjectId
    selectedOption: string
    isCorrect: boolean
    timeSpent: number
    bookmarked: boolean
  }>

  // Scoring
  totalQuestions: number
  answeredQuestions: number
  correctAnswers: number
  score: number
  percentage: number

  // Result
  isPassed: boolean
  appreciationLevel: string
  appreciationMessage: string

  // Status
  status: 'in_progress' | 'submitted' | 'auto_submitted' | 'cancelled'

  createdAt: Date
  updatedAt: Date
}
```

### **Extended Collections**

#### **QuestionAnalytics Collection**
```typescript
{
  id: string
  question: ObjectId

  // Performance metrics
  totalAttempts: number
  correctAttempts: number
  averageTimeSpent: number
  difficultyRating: number

  // Frequency data
  yearlyFrequency: Array<{
    year: number
    count: number
    examName: string
  }>

  // Similar questions
  similarQuestions: ObjectId[]

  updatedAt: Date
}
```

#### **OMRSheets Collection**
```typescript
{
  id: string
  mockExam: ObjectId
  institute: ObjectId

  // PDF configuration
  includeInstructions: boolean
  languages: string[]
  watermarkLogo: ObjectId // Media reference

  // Generated PDF
  pdfUrl: string
  generatedAt: Date

  createdBy: ObjectId
  createdAt: Date
}
```

---

## 🔌 API Endpoints Specification

### **Course Management Endpoints**

#### **Categories Management**
- `GET /api/institute-admin/categories` - List all categories (institute + branch scoped)
- `GET /api/institute-admin/categories?branch_id=:branchId` - List categories for specific branch
- `POST /api/institute-admin/categories` - Create new category
- `PUT /api/institute-admin/categories/:id` - Update category
- `DELETE /api/institute-admin/categories/:id` - Delete category
- `GET /api/institute-admin/categories/:id/exam-types` - Get exam types for category
- `PUT /api/institute-admin/categories/reorder` - Reorder categories
- `POST /api/institute-admin/categories/:id/share-with-branches` - Share category with branches
- `GET /api/institute-admin/categories/shared` - List categories shared with current branch

#### **Exam Types Management**
- `GET /api/institute-admin/exam-types` - List all exam types (institute + branch scoped)
- `GET /api/institute-admin/exam-types?category_id=:categoryId` - List exam types by category
- `GET /api/institute-admin/exam-types?branch_id=:branchId` - List exam types for specific branch
- `POST /api/institute-admin/exam-types` - Create new exam type
- `PUT /api/institute-admin/exam-types/:id` - Update exam type
- `DELETE /api/institute-admin/exam-types/:id` - Delete exam type
- `GET /api/institute-admin/exam-types/:id/courses` - Get courses for exam type
- `POST /api/institute-admin/exam-types/:id/share-with-branches` - Share exam type with branches
- `GET /api/institute-admin/exam-types/shared` - List exam types shared with current branch

#### **Cascading Dropdown Endpoints**
- `GET /api/institute-admin/categories/dropdown` - Categories for dropdown (active only)
- `GET /api/institute-admin/exam-types/dropdown?category=:categoryId` - Exam types for dropdown
- `GET /api/institute-admin/cascading-data` - Complete hierarchy for forms

#### **API Implementation Examples**

**Categories Management:**
```typescript
// GET /api/institute-admin/categories
export async function GET(request: Request) {
  try {
    const categories = await Categories.find({
      institute: getInstituteId(request),
      isActive: true
    })
    .sort({ orderIndex: 1 })
    .populate('courseCount studentCount')

    return NextResponse.json({ categories })
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch categories' }, { status: 500 })
  }
}

// POST /api/institute-admin/categories
export async function POST(request: Request) {
  try {
    const data = await request.json()
    const instituteId = getInstituteId(request)

    // Generate slug from name
    const slug = data.name.toLowerCase().replace(/\s+/g, '-')

    // Get next order index
    const lastCategory = await Categories.findOne({ institute: instituteId })
      .sort({ orderIndex: -1 })
    const orderIndex = (lastCategory?.orderIndex || 0) + 1

    const category = await Categories.create({
      ...data,
      slug,
      orderIndex,
      institute: instituteId,
      createdBy: getUserId(request)
    })

    return NextResponse.json({ category }, { status: 201 })
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create category' }, { status: 500 })
  }
}
```

**Exam Types with Cascading:**
```typescript
// GET /api/institute-admin/exam-types?category=:categoryId
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('category')
    const instituteId = getInstituteId(request)

    const filter: any = {
      institute: instituteId,
      isActive: true
    }

    if (categoryId) {
      filter.category = categoryId
    }

    const examTypes = await ExamTypes.find(filter)
      .populate('category', 'name color icon')
      .sort({ name: 1 })

    return NextResponse.json({ examTypes })
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch exam types' }, { status: 500 })
  }
}

// GET /api/institute-admin/cascading-data
export async function GET(request: Request) {
  try {
    const instituteId = getInstituteId(request)

    const categories = await Categories.find({
      institute: instituteId,
      isActive: true
    }).sort({ orderIndex: 1 })

    const examTypes = await ExamTypes.find({
      institute: instituteId,
      isActive: true
    }).sort({ name: 1 })

    // Group exam types by category
    const examTypesByCategory = examTypes.reduce((acc, examType) => {
      const categoryId = examType.category.toString()
      if (!acc[categoryId]) {
        acc[categoryId] = []
      }
      acc[categoryId].push(examType)
      return acc
    }, {} as Record<string, ExamType[]>)

    return NextResponse.json({
      categories,
      examTypesByCategory
    })
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch cascading data' }, { status: 500 })
  }
}
```

#### **Course Subtitles**
- `GET /api/institute-admin/courses/:courseId/subtitles` - List subtitles
- `POST /api/institute-admin/courses/:courseId/subtitles` - Create subtitle
- `PUT /api/institute-admin/subtitles/:id` - Update subtitle
- `DELETE /api/institute-admin/subtitles/:id` - Delete subtitle
- `PUT /api/institute-admin/subtitles/:id/reorder` - Reorder subtitles

#### **Course Sharing Management**
- `GET /api/institute-admin/courses/:courseId/sharing` - Get course sharing settings
- `PUT /api/institute-admin/courses/:courseId/sharing` - Update course sharing settings
- `POST /api/institute-admin/courses/:courseId/share-with-branches` - Share course with branches
- `POST /api/institute-admin/courses/:courseId/share-with-marketplace` - Share course with marketplace
- `DELETE /api/institute-admin/courses/:courseId/unshare/:branchId` - Unshare course from branch
- `GET /api/institute-admin/shared-courses` - List courses shared with current branch
- `GET /api/institute-admin/marketplace-courses` - List courses available in marketplace

#### **Lesson Management**
- `GET /api/institute-admin/subtitles/:subtitleId/lessons` - List lessons
- `POST /api/institute-admin/subtitles/:subtitleId/lessons` - Create lesson
- `PUT /api/institute-admin/lessons/:id` - Update lesson
- `DELETE /api/institute-admin/lessons/:id` - Delete lesson
- `PUT /api/institute-admin/lessons/:id/approve` - Approve lesson
- `PUT /api/institute-admin/lessons/:id/reject` - Reject lesson
- `PUT /api/institute-admin/lessons/:id/publish` - Publish lesson

#### **Mock Exam Management**
- `GET /api/institute-admin/mock-exams` - List mock exams
- `POST /api/institute-admin/mock-exams` - Create mock exam
- `PUT /api/institute-admin/mock-exams/:id` - Update mock exam
- `DELETE /api/institute-admin/mock-exams/:id` - Delete mock exam
- `POST /api/institute-admin/mock-exams/:id/duplicate` - Duplicate exam

#### **Question Management**
- `GET /api/institute-admin/questions` - List questions with filters
- `POST /api/institute-admin/questions` - Create question
- `PUT /api/institute-admin/questions/:id` - Update question
- `DELETE /api/institute-admin/questions/:id` - Delete question
- `POST /api/institute-admin/questions/bulk-upload` - Bulk upload questions
- `PUT /api/institute-admin/questions/:id/toggle` - Enable/disable question
- `GET /api/institute-admin/questions/:id/analytics` - Question analytics

#### **Previous Year Questions**
- `GET /api/institute-admin/previous-year-questions` - Search previous year questions
- `POST /api/institute-admin/previous-year-questions/import` - Import questions
- `GET /api/institute-admin/previous-year-questions/analytics` - Subject-wise distribution

#### **OMR Sheet Generation**
- `POST /api/institute-admin/mock-exams/:id/generate-omr` - Generate OMR sheet
- `GET /api/institute-admin/omr-sheets/:id/download` - Download OMR PDF

### **Student Exam Endpoints**
- `GET /api/student/mock-exams/available` - Available exams for student
- `POST /api/student/mock-exams/:id/start` - Start exam attempt
- `PUT /api/student/exam-attempts/:id/answer` - Submit answer
- `POST /api/student/exam-attempts/:id/submit` - Submit complete exam
- `GET /api/student/exam-attempts/:id/result` - Get exam result

---

## 🎨 Frontend Component Structure

### **Main Course Management Components**

#### **Course Management Dashboard**
```
apps/frontend/src/components/institute/courses/
├── CourseManagement.tsx              # Main dashboard
├── CategoryManager.tsx               # Category management interface
├── ExamTypeManager.tsx               # Exam type management
├── CourseStructureView.tsx           # Hierarchical course view
├── SubtitleManager.tsx               # Subtitle management
├── LessonManager.tsx                 # Lesson CRUD operations
├── LessonApprovalQueue.tsx           # Admin approval interface
├── CascadingDropdown.tsx             # Category-ExamType dropdown component
├── CourseSharing.tsx                 # Course sharing management
├── BranchSharingModal.tsx            # Branch sharing interface
├── MarketplaceSharingModal.tsx       # Marketplace sharing interface
├── SharedCoursesView.tsx             # View shared courses from other branches
└── CourseAnalytics.tsx               # Course performance analytics
```

#### **Category & Exam Type Management**
```
apps/frontend/src/components/institute/categories/
├── CategoryList.tsx                  # Category listing with CRUD
├── CategoryForm.tsx                  # Category creation/edit form
├── CategoryCard.tsx                  # Individual category display
├── CategoryReorder.tsx               # Drag-and-drop reordering
└── CategoryAnalytics.tsx             # Category performance metrics

apps/frontend/src/components/institute/exam-types/
├── ExamTypeList.tsx                  # Exam type listing with filters
├── ExamTypeForm.tsx                  # Exam type creation/edit form
├── ExamTypeCard.tsx                  # Individual exam type display
├── ExamTypeDetails.tsx               # Detailed exam type view
└── ExamTypeAnalytics.tsx             # Exam type performance metrics
```

#### **Mock Exam Components**
```
apps/frontend/src/components/institute/exams/
├── MockExamManager.tsx               # Main exam management
├── ExamBuilder.tsx                   # Exam creation wizard
├── QuestionBank.tsx                  # Question repository
├── QuestionEditor.tsx                # Question creation/editing
├── BulkQuestionUpload.tsx            # CSV/Excel upload
├── ExamPreview.tsx                   # Preview before publishing
├── LiveExamController.tsx            # Live exam management
└── ExamAnalytics.tsx                 # Exam performance data
```

#### **Question Management Components**
```
apps/frontend/src/components/institute/questions/
├── QuestionManager.tsx               # Main question interface
├── QuestionForm.tsx                  # Question creation form
├── OptionBuilder.tsx                 # Dynamic option creation
├── MultiLanguageEditor.tsx           # Multilingual question support
├── QuestionPreview.tsx               # Question preview component
├── SimilarQuestions.tsx              # Related questions display
├── QuestionAnalytics.tsx             # Question performance metrics
└── PreviousYearQuestions.tsx         # Historical question bank
```

#### **Assessment Components**
```
apps/frontend/src/components/institute/assessments/
├── AssessmentDashboard.tsx           # Assessment overview
├── ResultAnalytics.tsx               # Result analysis
├── StudentPerformance.tsx            # Individual student metrics
├── SubjectWiseAnalytics.tsx          # Subject performance breakdown
├── HeatMapVisualization.tsx          # Question frequency heat map
└── OMRSheetGenerator.tsx             # OMR sheet creation
```

### **Shared Components**

#### **Form Components**
```
apps/frontend/src/components/shared/forms/
├── RichTextEditor.tsx                # Content creation
├── VideoUploader.tsx                 # Video file handling
├── DocumentUploader.tsx              # Document management
├── LanguageSelector.tsx              # Multi-language support
├── DifficultySelector.tsx            # Question difficulty
├── TagInput.tsx                      # Question tagging
└── TimerConfiguration.tsx            # Exam timing settings
```

#### **Display Components**
```
apps/frontend/src/components/shared/display/
├── QuestionRenderer.tsx              # Question display
├── OptionRenderer.tsx                # Option layout rendering
├── CountdownTimer.tsx                # Exam timer
├── ProgressIndicator.tsx             # Completion progress
├── ScoreDisplay.tsx                  # Result presentation
└── AppreciationBadge.tsx             # Achievement display
```

---

## 🔄 State Management (Zustand Stores)

### **Course Management Store**
```typescript
// apps/frontend/src/stores/courseManagementStore.ts
interface CourseManagementState {
  // Categories
  categories: Category[]
  selectedCategory: Category | null

  // Exam Types
  examTypes: ExamType[]
  selectedExamType: ExamType | null
  filteredExamTypes: ExamType[] // Filtered by selected category

  // Courses and Structure
  courses: Course[]
  selectedCourse: Course | null
  subtitles: Subtitle[]
  lessons: Lesson[]

  // Loading states
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  isFetchingExamTypes: boolean

  // Actions - Categories
  fetchCategories: () => Promise<void>
  createCategory: (data: CreateCategoryData) => Promise<void>
  updateCategory: (id: string, data: UpdateCategoryData) => Promise<void>
  deleteCategory: (id: string) => Promise<void>
  reorderCategories: (order: string[]) => Promise<void>

  // Actions - Exam Types
  fetchExamTypes: (categoryId?: string) => Promise<void>
  createExamType: (data: CreateExamTypeData) => Promise<void>
  updateExamType: (id: string, data: UpdateExamTypeData) => Promise<void>
  deleteExamType: (id: string) => Promise<void>

  // Actions - Cascading Selection
  selectCategory: (category: Category | null) => void
  selectExamType: (examType: ExamType | null) => void
  getCascadingData: () => Promise<CascadingData>

  // Actions - Course Structure
  fetchCourseStructure: (courseId: string) => Promise<void>
  createSubtitle: (courseId: string, data: CreateSubtitleData) => Promise<void>
  updateSubtitle: (id: string, data: UpdateSubtitleData) => Promise<void>
  deleteSubtitle: (id: string) => Promise<void>
  reorderSubtitles: (courseId: string, order: string[]) => Promise<void>

  createLesson: (subtitleId: string, data: CreateLessonData) => Promise<void>
  updateLesson: (id: string, data: UpdateLessonData) => Promise<void>
  deleteLesson: (id: string) => Promise<void>
  approveLesson: (id: string, notes?: string) => Promise<void>
  rejectLesson: (id: string, notes: string) => Promise<void>
  publishLesson: (id: string) => Promise<void>
}

// Type definitions
interface Category {
  id: string
  name: string
  description: string
  slug: string
  icon?: string
  color?: string
  orderIndex: number
  isActive: boolean
  courseCount: number
  studentCount: number
}

interface ExamType {
  id: string
  categoryId: string
  name: string
  shortName: string
  description: string
  slug: string
  examPattern?: string
  subjects: string[]
  languages: string[]
  isActive: boolean
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  courseCount: number
  studentCount: number
}

interface CascadingData {
  categories: Category[]
  examTypesByCategory: Record<string, ExamType[]>
}
```

### **Mock Exam Store**
```typescript
// apps/frontend/src/stores/mockExamStore.ts
interface MockExamState {
  // Exams
  mockExams: MockExam[]
  selectedExam: MockExam | null
  examQuestions: Question[]

  // Question Bank
  questionBank: Question[]
  questionFilters: QuestionFilters

  // Exam Attempts
  examAttempts: ExamAttempt[]
  currentAttempt: ExamAttempt | null

  // Loading states
  isLoading: boolean
  isCreatingExam: boolean
  isUploadingQuestions: boolean

  // Actions
  fetchMockExams: () => Promise<void>
  createMockExam: (data: CreateMockExamData) => Promise<void>
  updateMockExam: (id: string, data: UpdateMockExamData) => Promise<void>
  deleteMockExam: (id: string) => Promise<void>
  duplicateMockExam: (id: string) => Promise<void>

  fetchQuestionBank: (filters?: QuestionFilters) => Promise<void>
  createQuestion: (data: CreateQuestionData) => Promise<void>
  updateQuestion: (id: string, data: UpdateQuestionData) => Promise<void>
  deleteQuestion: (id: string) => Promise<void>
  toggleQuestion: (id: string, enabled: boolean) => Promise<void>
  bulkUploadQuestions: (file: File, examId: string) => Promise<void>

  fetchExamAttempts: (examId: string) => Promise<void>
  startExamAttempt: (examId: string) => Promise<ExamAttempt>
  submitAnswer: (attemptId: string, questionId: string, answer: string) => Promise<void>
  submitExam: (attemptId: string) => Promise<ExamResult>
}
```

### **Analytics Store**
```typescript
// apps/frontend/src/stores/analyticsStore.ts
interface AnalyticsState {
  // Course Analytics
  courseAnalytics: CourseAnalytics[]
  lessonAnalytics: LessonAnalytics[]

  // Question Analytics
  questionAnalytics: QuestionAnalytics[]
  subjectDistribution: SubjectDistribution[]

  // Performance Data
  studentPerformance: StudentPerformance[]
  examResults: ExamResult[]

  // Heat Map Data
  questionFrequency: QuestionFrequency[]
  yearlyTrends: YearlyTrend[]

  // Loading states
  isLoadingAnalytics: boolean

  // Actions
  fetchCourseAnalytics: (courseId?: string) => Promise<void>
  fetchQuestionAnalytics: (questionId: string) => Promise<void>
  fetchSubjectDistribution: (examType: string) => Promise<void>
  fetchStudentPerformance: (filters: PerformanceFilters) => Promise<void>
  fetchQuestionFrequency: (filters: FrequencyFilters) => Promise<void>
  generateOMRSheet: (examId: string, config: OMRConfig) => Promise<string>
}
```

---

## 🎯 Key Features Implementation

### **1. Category Management Component**
```typescript
// Category management interface
const CategoryManager = () => {
  const { categories, isLoading, fetchCategories, createCategory, updateCategory, deleteCategory, reorderCategories } = useCourseManagementStore()
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)

  useEffect(() => {
    fetchCategories()
  }, [])

  const handleCreateCategory = async (data: CreateCategoryData) => {
    await createCategory(data)
    setIsCreateModalOpen(false)
  }

  const handleUpdateCategory = async (id: string, data: UpdateCategoryData) => {
    await updateCategory(id, data)
    setEditingCategory(null)
  }

  const handleReorder = async (newOrder: string[]) => {
    await reorderCategories(newOrder)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Category Management</h2>
        <Button onClick={() => setIsCreateModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Category
        </Button>
      </div>

      <DragDropContext onDragEnd={handleReorder}>
        <Droppable droppableId="categories">
          {(provided) => (
            <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
              {categories.map((category, index) => (
                <Draggable key={category.id} draggableId={category.id} index={index}>
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      className="bg-white p-4 rounded-lg border shadow-sm"
                    >
                      <CategoryCard
                        category={category}
                        onEdit={() => setEditingCategory(category)}
                        onDelete={() => deleteCategory(category.id)}
                      />
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      <CategoryFormModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateCategory}
      />

      <CategoryFormModal
        isOpen={!!editingCategory}
        category={editingCategory}
        onClose={() => setEditingCategory(null)}
        onSubmit={(data) => handleUpdateCategory(editingCategory!.id, data)}
      />
    </div>
  )
}
```

### **2. Cascading Dropdown Component**
```typescript
// Cascading dropdown for category and exam type selection
const CascadingDropdown = ({ onSelectionChange, initialCategory, initialExamType }) => {
  const { categories, examTypes, filteredExamTypes, selectCategory, selectExamType, fetchExamTypes } = useCourseManagementStore()
  const [selectedCategory, setSelectedCategory] = useState(initialCategory)
  const [selectedExamType, setSelectedExamType] = useState(initialExamType)

  const handleCategoryChange = async (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId)
    setSelectedCategory(category)
    setSelectedExamType(null)

    selectCategory(category)
    await fetchExamTypes(categoryId)

    onSelectionChange({ category, examType: null })
  }

  const handleExamTypeChange = (examTypeId: string) => {
    const examType = filteredExamTypes.find(et => et.id === examTypeId)
    setSelectedExamType(examType)

    selectExamType(examType)
    onSelectionChange({ category: selectedCategory, examType })
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label className="block text-sm font-medium mb-2">Category</label>
        <Select
          value={selectedCategory?.id || ''}
          onValueChange={handleCategoryChange}
          placeholder="Select Category"
        >
          {categories.map(category => (
            <SelectItem key={category.id} value={category.id}>
              <div className="flex items-center space-x-2">
                {category.icon && <span className={category.icon} />}
                <span>{category.name}</span>
                <Badge variant="secondary">{category.courseCount} courses</Badge>
              </div>
            </SelectItem>
          ))}
        </Select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Exam Type</label>
        <Select
          value={selectedExamType?.id || ''}
          onValueChange={handleExamTypeChange}
          disabled={!selectedCategory}
          placeholder={selectedCategory ? "Select Exam Type" : "Select Category First"}
        >
          {filteredExamTypes.map(examType => (
            <SelectItem key={examType.id} value={examType.id}>
              <div className="flex items-center space-x-2">
                <span>{examType.name}</span>
                <Badge variant="outline">{examType.shortName}</Badge>
                <Badge
                  variant={examType.difficulty === 'beginner' ? 'success' :
                          examType.difficulty === 'intermediate' ? 'warning' : 'error'}
                >
                  {examType.difficulty}
                </Badge>
              </div>
            </SelectItem>
          ))}
        </Select>
      </div>
    </div>
  )
}
```

### **3. Course Sharing Management Component**
```typescript
// Course sharing management interface
const CourseSharing = ({ courseId }) => {
  const { branches, fetchBranches, updateCourseSharing, getCourseSharing } = useCourseManagementStore()
  const [sharingSettings, setSharingSettings] = useState(null)
  const [isBranchModalOpen, setIsBranchModalOpen] = useState(false)
  const [isMarketplaceModalOpen, setIsMarketplaceModalOpen] = useState(false)

  useEffect(() => {
    fetchBranches()
    loadSharingSettings()
  }, [courseId])

  const loadSharingSettings = async () => {
    const settings = await getCourseSharing(courseId)
    setSharingSettings(settings)
  }

  const handleBranchSharing = async (selectedBranches, permissions) => {
    await updateCourseSharing(courseId, {
      shareWithBranches: true,
      allowedBranches: selectedBranches,
      shareType: permissions.shareType,
      requireApproval: permissions.requireApproval
    })
    setIsBranchModalOpen(false)
    loadSharingSettings()
  }

  const handleMarketplaceSharing = async (marketplaceData) => {
    await updateCourseSharing(courseId, {
      shareWithMarketplace: true,
      marketplacePrice: marketplaceData.price,
      allowExternalPurchase: marketplaceData.allowPurchase,
      marketplaceCategory: marketplaceData.category,
      marketplaceTags: marketplaceData.tags
    })
    setIsMarketplaceModalOpen(false)
    loadSharingSettings()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold">Course Sharing Settings</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Branch Sharing Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building className="h-5 w-5" />
              <span>Share with Branches</span>
            </CardTitle>
            <CardDescription>
              Share this course with other branches in your institute
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Branch Sharing</span>
                <Switch
                  checked={sharingSettings?.shareWithBranches || false}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setIsBranchModalOpen(true)
                    } else {
                      handleBranchSharing([], { shareType: 'view_only', requireApproval: false })
                    }
                  }}
                />
              </div>

              {sharingSettings?.shareWithBranches && (
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">
                    Shared with {sharingSettings.sharedBranches?.length || 0} branches
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsBranchModalOpen(true)}
                  >
                    Manage Branch Sharing
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Marketplace Sharing Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5" />
              <span>Share with Marketplace</span>
            </CardTitle>
            <CardDescription>
              Make this course available in the public marketplace
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Marketplace Sharing</span>
                <Switch
                  checked={sharingSettings?.shareWithMarketplace || false}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setIsMarketplaceModalOpen(true)
                    } else {
                      handleMarketplaceSharing({
                        shareWithMarketplace: false,
                        price: 0,
                        allowPurchase: false
                      })
                    }
                  }}
                />
              </div>

              {sharingSettings?.shareWithMarketplace && (
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">
                    Status: {sharingSettings.marketplaceStatus || 'Pending Review'}
                  </p>
                  <p className="text-sm text-gray-600">
                    Price: ₹{sharingSettings.marketplacePrice || 0}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsMarketplaceModalOpen(true)}
                  >
                    Manage Marketplace Settings
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <BranchSharingModal
        isOpen={isBranchModalOpen}
        onClose={() => setIsBranchModalOpen(false)}
        branches={branches}
        currentSettings={sharingSettings}
        onSubmit={handleBranchSharing}
      />

      <MarketplaceSharingModal
        isOpen={isMarketplaceModalOpen}
        onClose={() => setIsMarketplaceModalOpen(false)}
        currentSettings={sharingSettings}
        onSubmit={handleMarketplaceSharing}
      />
    </div>
  )
}
```

### **4. Branch Sharing Modal Component**
```typescript
// Branch sharing modal for selecting branches and permissions
const BranchSharingModal = ({ isOpen, onClose, branches, currentSettings, onSubmit }) => {
  const [selectedBranches, setSelectedBranches] = useState([])
  const [shareType, setShareType] = useState('view_only')
  const [requireApproval, setRequireApproval] = useState(false)
  const [expiryDate, setExpiryDate] = useState(null)

  useEffect(() => {
    if (currentSettings) {
      setSelectedBranches(currentSettings.allowedBranches || [])
      setShareType(currentSettings.shareType || 'view_only')
      setRequireApproval(currentSettings.requireApproval || false)
      setExpiryDate(currentSettings.expiryDate || null)
    }
  }, [currentSettings])

  const handleSubmit = () => {
    onSubmit(selectedBranches, {
      shareType,
      requireApproval,
      expiryDate
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Share Course with Branches</DialogTitle>
          <DialogDescription>
            Select branches and configure sharing permissions
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Branch Selection */}
          <div>
            <label className="text-sm font-medium mb-2 block">Select Branches</label>
            <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
              {branches.map(branch => (
                <div key={branch.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={branch.id}
                    checked={selectedBranches.includes(branch.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedBranches([...selectedBranches, branch.id])
                      } else {
                        setSelectedBranches(selectedBranches.filter(id => id !== branch.id))
                      }
                    }}
                  />
                  <label htmlFor={branch.id} className="text-sm">
                    {branch.name}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Share Type */}
          <div>
            <label className="text-sm font-medium mb-2 block">Access Level</label>
            <Select value={shareType} onValueChange={setShareType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="view_only">View Only</SelectItem>
                <SelectItem value="full_access">Full Access</SelectItem>
                <SelectItem value="copy_and_edit">Copy and Edit</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Additional Options */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="requireApproval"
                checked={requireApproval}
                onCheckedChange={setRequireApproval}
              />
              <label htmlFor="requireApproval" className="text-sm">
                Require approval before access
              </label>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Expiry Date (Optional)</label>
              <Input
                type="date"
                value={expiryDate || ''}
                onChange={(e) => setExpiryDate(e.target.value)}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={selectedBranches.length === 0}>
            Share Course
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
```

### **2. Dynamic Option Builder**
```typescript
// Dynamic option creation component
const OptionBuilder = ({ optionCount, options, onChange }) => {
  const addOption = () => {
    const newOptions = [...options, { text: '', textSecondary: '', isCorrect: false }]
    onChange(newOptions)
  }

  const removeOption = (index) => {
    const newOptions = options.filter((_, i) => i !== index)
    onChange(newOptions)
  }

  const updateOption = (index, field, value) => {
    const newOptions = options.map((option, i) =>
      i === index ? { ...option, [field]: value } : option
    )
    onChange(newOptions)
  }

  return (
    <div className="space-y-3">
      {options.map((option, index) => (
        <div key={index} className="border rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium">Option {String.fromCharCode(65 + index)}</span>
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={option.isCorrect}
                onCheckedChange={(checked) => updateOption(index, 'isCorrect', checked)}
              />
              <label>Correct Answer</label>
              {options.length > 2 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeOption(index)}
                >
                  Remove
                </Button>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Input
              placeholder="Option text (Primary)"
              value={option.text}
              onChange={(e) => updateOption(index, 'text', e.target.value)}
            />
            <Input
              placeholder="Option text (Secondary)"
              value={option.textSecondary}
              onChange={(e) => updateOption(index, 'textSecondary', e.target.value)}
            />
          </div>
        </div>
      ))}

      {options.length < 6 && (
        <Button onClick={addOption} variant="outline" className="w-full">
          Add Option
        </Button>
      )}
    </div>
  )
}
```

### **3. Live Exam Controller**
```typescript
// Live exam management component
const LiveExamController = ({ examId }) => {
  const [isLive, setIsLive] = useState(false)
  const [participants, setParticipants] = useState([])
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [timeRemaining, setTimeRemaining] = useState(0)

  useEffect(() => {
    // WebSocket connection for real-time updates
    const ws = new WebSocket(`${process.env.NEXT_PUBLIC_WS_URL}/live-exam/${examId}`)

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      switch (data.type) {
        case 'participant_joined':
          setParticipants(prev => [...prev, data.participant])
          break
        case 'answer_submitted':
          updateParticipantAnswer(data.participantId, data.answer)
          break
        case 'time_update':
          setTimeRemaining(data.timeRemaining)
          break
      }
    }

    return () => ws.close()
  }, [examId])

  const startExam = () => {
    setIsLive(true)
    // Send start signal to all participants
  }

  const nextQuestion = () => {
    setCurrentQuestion(prev => prev + 1)
    // Broadcast next question to participants
  }

  const endExam = () => {
    setIsLive(false)
    // Calculate and display results
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Live Exam Controller</h2>
        <div className="flex items-center space-x-4">
          <CountdownTimer timeRemaining={timeRemaining} />
          <Button
            onClick={isLive ? endExam : startExam}
            variant={isLive ? "destructive" : "default"}
          >
            {isLive ? "End Exam" : "Start Exam"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <QuestionDisplay
            questionNumber={currentQuestion + 1}
            question={questions[currentQuestion]}
            isLive={isLive}
          />

          {isLive && (
            <div className="mt-4 flex justify-between">
              <Button
                onClick={() => setCurrentQuestion(prev => Math.max(0, prev - 1))}
                disabled={currentQuestion === 0}
              >
                Previous
              </Button>
              <Button
                onClick={nextQuestion}
                disabled={currentQuestion === questions.length - 1}
              >
                Next Question
              </Button>
            </div>
          )}
        </div>

        <div>
          <ParticipantsList
            participants={participants}
            currentQuestion={currentQuestion}
          />
        </div>
      </div>
    </div>
  )
}
```

---

## 🎥 **Video Management System**

### **Video Upload & Storage**
- **File Limits**: Max 2GB per video, 4 hours duration
- **Supported Formats**: MP4, AVI, MOV, WebM
- **Storage**: AWS S3 with CDN distribution
- **Quality Options**: Auto-transcoding to 720p, 1080p, 480p
- **Compression**: Automatic video compression for bandwidth optimization

### **Video Security Features**
- **DRM Protection**: Prevent video downloading and screen recording
- **Watermarking**: Institute logo overlay on videos
- **Access Control**: Time-based access, IP restrictions
- **Streaming**: Secure HLS streaming with token-based authentication

### **Video Enhancement**
- **Subtitles**: Multi-language subtitle support (Tamil, English, etc.)
- **Chapters**: Video chapters for easy navigation
- **Playback Speed**: 0.5x to 2x speed control
- **Quality Selection**: Manual quality selection by students
- **Resume Playback**: Continue from where student left off

---

## 📄 **Document Security & Watermarking**

### **Document Management**
- **Supported Formats**: PDF, Word, PowerPoint, Excel
- **File Size Limit**: Max 50MB per document
- **Version Control**: Track document versions and changes
- **Bulk Upload**: Multiple document upload with folder structure

### **Advanced Watermarking**
- **Customizable Watermarks**:
  - Institute logo and name
  - Student name and ID
  - Download timestamp
  - "Confidential" or custom text
- **Watermark Settings**:
  - Position: Center, corners, diagonal
  - Opacity: 10% to 50%
  - Size: Small, medium, large
  - Color: Grayscale or colored

### **Document Security**
- **Print Prevention**: Disable printing for sensitive documents
- **Copy Protection**: Prevent text selection and copying
- **Download Tracking**: Log who downloaded what and when
- **Expiry Dates**: Set document access expiry
- **View-Only Mode**: Documents that can only be viewed online

---

## 🎓 **Live Class Integration**

### **Zoom Integration**
```typescript
// Zoom meeting configuration
{
  meetingId: string
  password: string
  hostKey: string
  participantLimit: number
  recordingEnabled: boolean
  chatEnabled: boolean
  screenShareEnabled: boolean
  breakoutRoomsEnabled: boolean
}
```

### **Live Class Features**
- **Automatic Meeting Creation**: Generate Zoom meetings for scheduled classes
- **Recording Management**: Auto-record and save to course content
- **Attendance Tracking**: Automatic attendance based on join/leave times
- **Interactive Tools**:
  - Live chat with moderation
  - Polls and quizzes during class
  - Breakout rooms for group activities
  - Screen sharing for presentations
  - Whiteboard collaboration

### **Class Scheduling**
- **Recurring Classes**: Daily, weekly, monthly schedules
- **Calendar Integration**: Sync with Google Calendar, Outlook
- **Notifications**: Email and SMS reminders
- **Time Zone Support**: Multiple time zone handling

---

## 💰 **Payment & Access Control**

### **Content Monetization**
```typescript
// Content pricing structure
{
  contentType: 'lesson' | 'course' | 'exam' | 'document'
  accessType: 'free' | 'paid' | 'subscription'
  price: number
  currency: 'INR' | 'USD'
  validity: number // days
  discountPercentage?: number
  bulkPricing?: Array<{
    quantity: number
    discountPercentage: number
  }>
}
```

### **Payment Models**
- **One-time Payment**: Pay once, access forever
- **Subscription**: Monthly/yearly recurring payments
- **Pay-per-View**: Individual lesson/exam payments
- **Bundle Pricing**: Course packages with discounts
- **Bulk Discounts**: Group enrollments with reduced rates

### **Access Control**
- **Time-based Access**: 30 days, 6 months, 1 year
- **Attempt-based**: Limited number of exam attempts
- **Device Restrictions**: Limit access to specific devices
- **Concurrent Sessions**: Prevent multiple simultaneous logins
- **Geographic Restrictions**: IP-based access control

---

## 📱 **Mobile App Features**

### **Offline Capabilities**
- **Content Download**: Download lessons for offline study
- **Offline Exams**: Take downloaded exams without internet
- **Sync on Connect**: Auto-sync progress when online
- **Storage Management**: Manage downloaded content storage

### **Mobile-Optimized Interface**
- **Responsive Design**: Optimized for all screen sizes
- **Touch-friendly**: Large buttons, swipe gestures
- **Dark Mode**: Eye-friendly dark theme option
- **Font Scaling**: Adjustable text size for accessibility

### **Mobile Exam Features**
- **Portrait/Landscape**: Support both orientations
- **Touch Navigation**: Swipe between questions
- **Mobile Timer**: Prominent countdown display
- **Auto-save**: Frequent answer auto-saving
- **Network Recovery**: Handle connection drops gracefully

### **Push Notifications**
- **Exam Reminders**: 1 hour, 15 minutes before exam
- **New Content**: Notifications for new lessons/courses
- **Results Available**: Instant result notifications
- **Assignment Due**: Deadline reminders
- **Live Class**: Class starting notifications

---

## 📊 **Advanced Analytics**

### **Learning Path Analytics**
```typescript
// Learning analytics data structure
{
  studentId: string
  courseId: string
  learningPath: Array<{
    lessonId: string
    timeSpent: number
    completionRate: number
    attempts: number
    score: number
    timestamp: Date
  }>
  optimalPath: string[]
  deviations: Array<{
    expectedLesson: string
    actualLesson: string
    reason: string
  }>
}
```

### **Engagement Metrics**
- **Video Completion Rates**: Percentage of video watched
- **Time Spent**: Detailed time tracking per lesson/course
- **Interaction Patterns**: Click patterns, navigation behavior
- **Drop-off Points**: Where students typically stop
- **Peak Activity Times**: When students are most active

### **Predictive Analytics**
- **Performance Prediction**: Predict exam scores based on study patterns
- **At-risk Identification**: Identify students likely to fail
- **Recommendation Engine**: Suggest personalized learning paths
- **Completion Probability**: Predict course completion likelihood
- **Intervention Alerts**: Alert trainers when students need help

### **Comparative Analytics**
- **Batch Comparison**: Compare performance across different batches
- **Institute Benchmarking**: Compare with other institutes
- **Historical Trends**: Year-over-year performance analysis
- **Subject-wise Analysis**: Identify strong/weak subjects
- **Trainer Effectiveness**: Measure trainer impact on student performance

---

## 💬 **Communication Features**

### **Discussion Forums**
```typescript
// Forum structure
{
  courseId: string
  lessonId?: string
  threads: Array<{
    id: string
    title: string
    author: ObjectId
    posts: Array<{
      content: string
      author: ObjectId
      timestamp: Date
      likes: number
      replies: Array<Post>
    }>
    isPinned: boolean
    isLocked: boolean
    tags: string[]
  }>
}
```

### **Doubt Clearing System**
- **Q&A Platform**: Students ask questions, trainers answer
- **Upvoting System**: Community votes on best answers
- **Search Functionality**: Find previously answered questions
- **Category Tags**: Organize questions by subject/topic
- **Expert Answers**: Verified answers from subject experts

### **Messaging System**
- **Direct Messages**: One-on-one communication
- **Group Chats**: Batch-wise or course-wise groups
- **Broadcast Messages**: Institute-wide announcements
- **File Sharing**: Share documents, images in messages
- **Message Status**: Read receipts, delivery status

### **Announcement System**
- **Multi-level Announcements**:
  - Institute-wide
  - Course-specific
  - Batch-specific
  - Individual student
- **Rich Content**: Text, images, videos, documents
- **Scheduling**: Schedule announcements for future
- **Read Tracking**: Track who read announcements
- **Priority Levels**: Normal, important, urgent

---

## 🔒 **Backup & Data Management**

### **Automated Backup System**
```typescript
// Backup configuration
{
  frequency: 'daily' | 'weekly' | 'monthly'
  retentionPeriod: number // days
  backupTypes: Array<'database' | 'media' | 'documents' | 'analytics'>
  storageLocation: 'aws_s3' | 'google_cloud' | 'azure'
  encryption: boolean
  compression: boolean
}
```

### **Data Export Capabilities**
- **Student Data Export**: CSV, Excel, JSON formats
- **Results Export**: Detailed exam results and analytics
- **Content Export**: Backup of all course content
- **Analytics Export**: Performance data for external analysis
- **Bulk Operations**: Export data for multiple students/courses

### **Data Retention Policies**
- **Student Attempts**: Keep for 3 years
- **Video Analytics**: Keep for 1 year
- **Chat Messages**: Keep for 6 months
- **Login Logs**: Keep for 1 year
- **Payment Records**: Keep for 7 years (compliance)

### **GDPR Compliance**
- **Data Portability**: Export user data on request
- **Right to Deletion**: Complete data removal
- **Consent Management**: Track and manage user consents
- **Data Minimization**: Collect only necessary data
- **Privacy by Design**: Built-in privacy protection

---

## 🔗 **Integration with External Systems**

### **LTI (Learning Tools Interoperability) Integration**
```typescript
// LTI configuration
{
  version: '1.3'
  platformId: string
  clientId: string
  deploymentId: string
  targetLinkUri: string
  loginHint: string
  messageType: 'LtiResourceLinkRequest' | 'LtiDeepLinkingRequest'
}
```

### **API Access for Third-party Integrations**
- **RESTful APIs**: Complete CRUD operations
- **GraphQL Support**: Flexible data querying
- **Webhook Support**: Real-time event notifications
- **Rate Limiting**: API usage limits and throttling
- **API Documentation**: Swagger/OpenAPI documentation
- **SDK Support**: JavaScript, Python, PHP SDKs

### **Single Sign-On (SSO) Integration**
- **SAML 2.0**: Enterprise SSO integration
- **OAuth 2.0**: Social login (Google, Facebook, Microsoft)
- **LDAP/Active Directory**: Corporate directory integration
- **Custom SSO**: Institute-specific authentication systems
- **Multi-factor Authentication**: SMS, email, authenticator app

### **Calendar Integration**
- **Google Calendar**: Sync exam schedules and live classes
- **Microsoft Outlook**: Enterprise calendar integration
- **Apple Calendar**: iOS device synchronization
- **Custom Calendar**: Export to any calendar application
- **Timezone Handling**: Automatic timezone conversion

---

## 🎨 **Advanced Question Features**

### **Rich Media Questions**
```typescript
// Enhanced question structure
{
  questionType: 'text' | 'image' | 'audio' | 'video' | 'mathematical' | 'code'
  content: {
    text?: string
    imageUrl?: string
    audioUrl?: string
    videoUrl?: string
    mathEquation?: string // LaTeX format
    codeSnippet?: {
      language: string
      code: string
      highlightSyntax: boolean
    }
  }
  interactiveElements?: Array<{
    type: 'hotspot' | 'drag_drop' | 'matching' | 'ordering'
    configuration: object
  }>
}
```

### **Question Types**
- **Image-based Questions**: Diagrams, charts, maps with clickable hotspots
- **Audio Questions**: Listening comprehension with audio playback
- **Video Questions**: Video-based scenarios with embedded questions
- **Mathematical Questions**: LaTeX equation support with formula editor
- **Code Questions**: Programming questions with syntax highlighting
- **Interactive Questions**: Drag-and-drop, matching, ordering activities

### **Mathematical Equation Support**
- **LaTeX Rendering**: Full mathematical notation support
- **Equation Editor**: Visual equation builder
- **Formula Library**: Pre-built common formulas
- **Graph Plotting**: Interactive graphs and charts
- **Scientific Calculator**: Built-in calculator for complex calculations

### **Programming Question Features**
- **Code Editor**: Syntax highlighting for 20+ languages
- **Auto-completion**: Intelligent code suggestions
- **Code Execution**: Run and test code in sandbox environment
- **Test Cases**: Automated testing with multiple test cases
- **Plagiarism Detection**: Code similarity checking

---

## 🛡️ **Proctoring & Security**

### **Online Proctoring System**
```typescript
// Proctoring configuration
{
  cameraMonitoring: boolean
  screenRecording: boolean
  audioMonitoring: boolean
  faceDetection: boolean
  multiplePersonDetection: boolean
  suspiciousActivityDetection: boolean
  aiProctoring: boolean
  humanProctoring: boolean
}
```

### **Browser Security**
- **Browser Lockdown**: Prevent tab switching, new windows
- **Full-screen Mode**: Force full-screen during exams
- **Right-click Disable**: Prevent context menu access
- **Copy-paste Restriction**: Block clipboard operations
- **Developer Tools Block**: Prevent F12, inspect element
- **Print Screen Disable**: Block screenshot functionality

### **Advanced Security Measures**
- **Facial Recognition**: Verify student identity throughout exam
- **Keystroke Analysis**: Detect unusual typing patterns
- **Eye Tracking**: Monitor gaze patterns (with compatible hardware)
- **Background Monitoring**: Detect people entering/leaving
- **Noise Detection**: Alert on suspicious audio activity
- **Device Fingerprinting**: Ensure same device usage

### **Anti-cheating Features**
- **Question Randomization**: Different question sets per student
- **Option Shuffling**: Randomize answer option order
- **Time Pressure**: Limit time per question to prevent searching
- **Plagiarism Detection**: Compare answers for similarity
- **IP Monitoring**: Detect VPN usage or location changes
- **Multiple Session Detection**: Prevent simultaneous logins

---

## 📋 **Reporting & Certificates**

### **Custom Report Builder**
```typescript
// Report configuration
{
  reportType: 'performance' | 'attendance' | 'engagement' | 'financial'
  filters: {
    dateRange: { start: Date, end: Date }
    students?: string[]
    courses?: string[]
    exams?: string[]
    branches?: string[]
  }
  metrics: string[]
  groupBy: string[]
  sortBy: string
  format: 'pdf' | 'excel' | 'csv' | 'json'
  scheduling?: {
    frequency: 'daily' | 'weekly' | 'monthly'
    recipients: string[]
  }
}
```

### **Certificate Management**
- **Template Designer**: Drag-and-drop certificate builder
- **Dynamic Content**: Auto-populate student name, course, score
- **Digital Signatures**: Cryptographically signed certificates
- **Blockchain Verification**: Immutable certificate verification
- **QR Code Integration**: Quick verification via QR codes
- **Bulk Generation**: Generate certificates for multiple students

### **Academic Transcripts**
- **Comprehensive Records**: Complete academic history
- **Grade Calculations**: Automatic GPA/percentage calculations
- **Credit System**: Track course credits and requirements
- **Verification System**: Third-party transcript verification
- **Export Formats**: PDF, XML, JSON for different systems
- **Official Seals**: Digital watermarks and security features

### **Progress Reports**
- **Student Progress**: Detailed learning journey tracking
- **Parent Reports**: Simplified reports for parents/guardians
- **Trainer Reports**: Class performance and engagement metrics
- **Institute Reports**: Overall institutional performance
- **Comparative Analysis**: Benchmark against standards
- **Recommendation Engine**: Personalized improvement suggestions

---

## 🏢 **Multi-tenant Architecture**

### **Data Isolation**
```typescript
// Tenant isolation strategy
{
  isolationType: 'database' | 'schema' | 'row_level'
  tenantIdentifier: string
  dataEncryption: boolean
  backupIsolation: boolean
  customDomains: boolean
  brandingIsolation: boolean
}
```

### **Institute Customization**
- **Custom Branding**: Logo, colors, fonts per institute
- **Domain Mapping**: Custom domains (institute.example.com)
- **Theme Customization**: Complete UI/UX customization
- **Feature Toggles**: Enable/disable features per institute
- **Custom Fields**: Institute-specific data fields
- **Localization**: Language and cultural customization

### **Performance & Scalability**
- **Horizontal Scaling**: Auto-scaling based on load
- **CDN Integration**: Global content delivery
- **Database Sharding**: Distribute data across servers
- **Caching Strategy**: Redis/Memcached for performance
- **Load Balancing**: Distribute traffic across servers
- **Monitoring**: Real-time performance monitoring

---

## 📈 **Performance Requirements**

### **Concurrent User Support**
- **10,000+ Concurrent Users**: Support large-scale deployments
- **Live Exam Capacity**: 1,000+ simultaneous exam takers
- **Video Streaming**: 500+ concurrent video streams
- **Real-time Features**: WebSocket connections for live features
- **Database Performance**: Sub-second query response times
- **API Response Times**: <200ms for standard operations

### **Availability & Reliability**
- **99.9% Uptime**: High availability architecture
- **Disaster Recovery**: Multi-region backup and failover
- **Auto-scaling**: Dynamic resource allocation
- **Health Monitoring**: Proactive issue detection
- **Error Handling**: Graceful degradation of services
- **Maintenance Windows**: Scheduled maintenance with minimal downtime

---

## 🔧 **Implementation Phases**

### **🎯 Current Implementation Status (Updated: 2025-01-04)**

**Overall Progress: Phase 16.1 - 80% Complete** 🚀

#### **✅ Completed Components:**
- **Database Schema**: All collections implemented with proper relationships
- **API Endpoints**: Complete REST API for categories, exam types, and course sharing
- **Backend Logic**: Access control, validation, and business logic implemented
- **State Management**: Zustand store for frontend state management
- **Basic UI**: Category manager component with listing functionality

#### **🔄 In Progress:**
- **Frontend Components**: Category forms, exam type manager, cascading dropdown
- **UI Integration**: Connecting components with API endpoints

#### **⏳ Next Immediate Steps:**
1. Complete category and exam type form components
2. Implement cascading dropdown functionality
3. Test all API endpoints with frontend integration
4. Begin Phase 16.2 course sharing interface

#### **🏗️ Ready for Development:**
- Course sharing modals and interfaces
- Course creation integration with new structure
- Advanced sharing permissions and workflows

---

### **Phase 16.1: Foundation & Hierarchy (Months 1-2)** ✅ **80% COMPLETE**
- [x] **Category Management System** ✅ **COMPLETE**
  - [x] Categories collection and API endpoints
  - [x] Category CRUD interface for admins
  - [x] Category reordering and analytics
- [x] **Exam Type Management System** ✅ **COMPLETE**
  - [x] Exam types collection with category relationships
  - [x] Exam type CRUD interface for admins
  - [x] Cascading dropdown functionality
- [x] **Basic Course Structure** ✅ **COMPLETE**
  - [x] Updated course schema with exam type references and institute_id/branch_id
  - [x] Course creation with category-exam type selection
  - [x] Basic lesson management with branch scoping
- [x] **Course Sharing Foundation** ✅ **COMPLETE**
  - [x] Course sharing settings in database schema
  - [x] Basic branch-to-branch sharing functionality
  - [x] Marketplace sharing preparation
- [x] **Database Schema Implementation** ✅ **COMPLETE**
  - [x] Categories collection with institute_id, branch_id, sharing settings
  - [x] ExamTypes collection with category relationships and sharing
  - [x] Courses collection updates with sharing functionality
  - [x] Access control updates for new field structure
- [x] **API Endpoints Implementation** ✅ **COMPLETE**
  - [x] Categories CRUD endpoints with branch filtering
  - [x] Exam Types CRUD endpoints with category filtering
  - [x] Course sharing endpoints for branch and marketplace
  - [x] Cascading dropdown API for hierarchical data
- [/] **Frontend Components Implementation** 🔄 **IN PROGRESS**
  - [x] Course Management Store (Zustand state management)
  - [/] Category Manager Component (basic listing interface)
  - [ ] Category Form Components (creation/editing forms)
  - [ ] Exam Type Manager Component
  - [ ] Cascading Dropdown Component
- [ ] **Testing & Validation** ⏳ **PENDING**
  - [ ] API endpoint testing with authentication
  - [ ] Database migration testing
  - [ ] Frontend integration testing
- [ ] **Documentation Updates** ⏳ **PENDING**

### **Phase 16.2: Core Course Management (Months 3-4)** ⏳ **READY TO START**
- [ ] **Advanced Course Sharing Interface** ⏳ **NEXT PRIORITY**
  - [ ] Branch sharing modal with permission configuration
  - [ ] Marketplace sharing modal with submission workflow
  - [ ] Shared courses view and management dashboard
- [ ] **Course Creation Integration** ⏳ **NEXT PRIORITY**
  - [ ] Update course creation to use category-exam type structure
  - [ ] Integrate cascading dropdown in course forms
  - [ ] Course sharing settings in creation workflow
- [ ] **Question Bank & Mock Exams**
  - [ ] Question bank creation and organization with branch scoping
  - [ ] Mock exam creation with auto-generation
  - [ ] Previous year question management with sharing capabilities
- [ ] **Content Management**
  - [ ] Lesson creation and approval workflow
  - [ ] Rich text editor and multimedia support
  - [ ] Document management with watermarking

### **Phase 16.3: Assessment & Testing (Months 5-6)**
- [ ] **Live Exam System**
  - [ ] Live exam controller and real-time monitoring
  - [ ] Zoom integration and screen sharing
  - [ ] OMR sheet generation and management
- [ ] **Advanced Question Features**
  - [ ] Multi-language question support
  - [ ] Advanced question types (image, audio, math)
  - [ ] Question analytics and heat maps

### **Phase 16.4: Analytics & Monitoring (Months 7-8)**
- [ ] **Student Progress Tracking**
  - [ ] Individual and batch performance monitoring
  - [ ] Learning path analytics and recommendations
  - [ ] At-risk student identification
- [ ] **Advanced Analytics**
  - [ ] Course performance dashboards
  - [ ] Predictive analytics and insights
  - [ ] Revenue and enrollment tracking

### **Phase 16.5: Advanced Features (Months 9-10)**
- [ ] **Marketplace Integration**
  - [ ] Course marketplace listing and approval workflow
  - [ ] External institute course purchasing
  - [ ] Marketplace analytics and revenue tracking
  - [ ] Course preview and demo functionality
- [ ] **Security & Proctoring**
  - [ ] Online proctoring and anti-cheating measures
  - [ ] Content security and access control
  - [ ] Payment integration and monetization
- [ ] **Enterprise Features**
  - [ ] Multi-tenant architecture
  - [ ] External system integrations (SSO, LTI)
  - [ ] Custom branding and theming
  - [ ] Compliance and audit features

### **Phase 16.6: Mobile & Communication (Months 11-12)**
- [ ] **Mobile Application**
  - [ ] Responsive design and mobile optimization
  - [ ] Offline capabilities and sync
  - [ ] Push notifications and alerts
- [ ] **Communication Features**
  - [ ] Discussion forums and Q&A platform
  - [ ] Messaging and announcement system
  - [ ] Student blog and content creation

---

## 🏗️ **Software Structure & Organizational Hierarchy**

### **Super Admin Level**
```typescript
// Super Admin capabilities
{
  createAdminAccounts: boolean
  manageCompanyDetails: boolean
  trackStudentCounts: boolean
  monitorStorageUsage: boolean
  controlAccessRights: boolean
  enableDisableFeatures: boolean
  planManagement: boolean
}
```

#### **Super Admin Functions**
- **Multi-Admin Creation**: Create multiple Admin accounts for different institutes
- **Company Management**: Manage institute details, employees, staff, trainers, mentors
- **Usage Monitoring**: Track student counts and storage usage across all institutes
- **Access Control**: Enable/disable features based on assigned subscription plans
- **Plan Management**: Assign different feature sets based on pricing tiers

### **Admin Categories**

#### **Platform Only Admin**
- **Content Creation**: Create courses and content from scratch
- **No Predefined Content**: Build everything without templates
- **Full Creative Control**: Complete freedom in course design
- **Custom Branding**: Institute-specific customization

#### **Platform with Content Admin**
- **Content Selection**: Choose from pre-approved content library
- **Agreement-Based Access**: Content access based on licensing agreements
- **Template Usage**: Use predefined course templates
- **Faster Deployment**: Quick course setup with existing content

### **Employee Role Structure**

#### **Course Admin (Sub Admin)**
```typescript
// Course Admin permissions
{
  courseManagement: boolean
  contentApproval: boolean
  questionBankAccess: boolean
  examScheduling: boolean
  studentManagement: boolean
  reportGeneration: boolean
}
```

#### **Employee Roles**
- **Content Creator**: Creates lessons, videos, documents
- **Question Bank Manager**: Manages questions and mock exams
- **Exam Coordinator**: Schedules and manages live exams
- **Student Support**: Handles student queries and issues
- **Analytics Manager**: Generates reports and insights

### **Course Creation Workflow**

#### **Step 1: Course Initiation**
```
Employee/Admin Creates Course
    ↓
Course ID Generation (Admin Prefix + Number)
    Example: AEX101, TNPSC001, UPSC205
    ↓
Course Details Entry
    - Title, Description, Category
    - Exam Type, Subject, Duration
    - Pricing, Access Level
```

#### **Step 2: Verification Process**
```typescript
// Verification workflow
{
  courseId: string
  creatorId: string
  verificationSteps: Array<{
    stepNumber: number
    verifierEmployeeId: string
    verificationDate: Date
    status: 'pending' | 'approved' | 'rejected'
    comments: string
    verifierData: {
      name: string
      role: string
      department: string
    }
  }>
  finalApproval: {
    authorizedPersonId: string
    approvalDate: Date
    finalStatus: 'approved' | 'rejected'
    finalComments: string
  }
}
```

#### **Verification Steps (1 to N)**
1. **Content Review**: Check content quality and accuracy
2. **Technical Review**: Verify videos, documents, links work properly
3. **Language Review**: Check grammar, spelling, translations
4. **Subject Matter Expert Review**: Validate technical accuracy
5. **Final Approval**: Authorized person gives final approval

#### **Employee Data Storage**
- **Creator Information**: Store who created the course
- **Verification Trail**: Complete audit trail of all verification steps
- **Employee Restrictions**: Employees cannot download course contents
- **Access Logging**: Track all employee access to content

---

## 🛒 **Additional System Requirements**

### **Course Video Management**
```typescript
// Shared video system
{
  videoId: string
  videoUrl: string
  usedInCourses: string[] // Array of course IDs
  sharedAcrossInstitutes: boolean
  accessPermissions: Array<{
    instituteId: string
    accessLevel: 'view' | 'edit' | 'download'
  }>
}
```

#### **Cross-Course Video Usage**
- **Single Upload, Multiple Use**: One video can be used across multiple courses
- **Centralized Storage**: Videos stored in central repository
- **Permission Management**: Control which institutes can use which videos
- **Usage Tracking**: Track where each video is being used

### **Bundled Courses & Cart System**

#### **Course Bundling**
```typescript
// Bundle structure
{
  bundleId: string
  bundleName: string
  description: string
  courses: Array<{
    courseId: string
    originalPrice: number
    bundlePrice: number
  }>
  totalOriginalPrice: number
  bundlePrice: number
  discountPercentage: number
  validityPeriod: number // days
}
```

#### **Shopping Cart Features**
- **Bundle Selection**: Add complete bundles to cart
- **Individual Courses**: Mix bundles and individual courses
- **Price Calculation**: Automatic discount calculation
- **Save for Later**: Save cart items for future purchase
- **Bulk Discounts**: Additional discounts for multiple bundles

### **Migration Support System**

#### **Content Migration**
```typescript
// Migration configuration
{
  sourceFormat: 'excel' | 'word' | 'pdf' | 'csv' | 'json'
  targetFormat: 'lms_standard'
  migrationRules: Array<{
    sourceField: string
    targetField: string
    transformation?: string
  }>
  validationRules: Array<{
    field: string
    rule: string
    errorMessage: string
  }>
}
```

#### **Supported Migration Formats**
- **Excel Files**: Course content, question banks, student data
- **Word Documents**: Lesson content, course materials
- **PDF Files**: Previous year papers, study materials
- **CSV Files**: Bulk question uploads, student lists
- **Legacy LMS**: Import from other learning management systems

### **Bulk Upload Support**

#### **Quiz/Course File Upload**
- **Excel Format**: Structured spreadsheets with predefined columns
- **Word Document**: Rich text content with formatting
- **Template Provision**: Provide standard templates for uploads
- **Validation**: Check format compliance before processing
- **Error Reporting**: Detailed error reports for failed uploads

### **Student Authentication System**

#### **Login Validation Requirements**
```typescript
// Student registration validation
{
  email: {
    required: true
    validation: 'email_format'
    verification: 'email_otp'
    spamPrevention: true
  }
  mobileNumber: {
    required: true
    validation: 'phone_format'
    verification: 'sms_otp'
    countryCode: true
  }
  additionalValidation: {
    captcha: boolean
    termsAcceptance: boolean
    ageVerification: boolean
  }
}
```

#### **Anti-Spam Measures**
- **Email Verification**: Mandatory email OTP verification
- **Mobile Verification**: SMS OTP for mobile number validation
- **CAPTCHA**: Prevent automated registrations
- **Rate Limiting**: Limit registration attempts per IP
- **Blacklist Management**: Block known spam domains/numbers

---

## 🎯 **Live Exam (CBT Mode) Advanced Features**

### **Exam Scheduling & Countdown**
```typescript
// Live exam configuration
{
  examId: string
  scheduledStartTime: Date
  countdownConfiguration: {
    startCountdown: '1_hour' | '24_hours' | '48_hours'
    mentorConfigurable: boolean
    customCountdownTime?: number // in hours
  }
  brandingDisplay: {
    instituteLogoUrl: string
    examInstructions: string
    displayTiming: {
      thirtyMinutesBefore: {
        showLogo: boolean
        showGuidelines: boolean
      }
      fiveMinutesBefore: {
        showFinalInstructions: boolean
        showReminders: boolean
      }
    }
  }
}
```

### **Student Information Display**
- **Registration Number**: Student's unique registration ID
- **Date of Birth**: For identity verification
- **Question Booklet Instructions**: Detailed exam guidelines
- **Question Format Options**: MCQ, Descriptive, Mixed format
- **OMR Sheet Display**: Digital OMR sheet interface

### **Exam Advertisement System**
```typescript
// Exam page advertisements
{
  examId: string
  advertisements: {
    firstPage: {
      enabled: boolean
      content: string // HTML content
      imageUrl?: string
      linkUrl?: string
    }
    lastPage: {
      enabled: boolean
      content: string
      imageUrl?: string
      linkUrl?: string
      promotionalOffers?: Array<{
        title: string
        description: string
        validUntil: Date
        discountPercentage: number
      }>
    }
  }
}
```

### **Offline Exam Support**
- **Paper-Based Questions**: Download printable question papers
- **OMR Sheet Download**: Printable OMR sheets with institute branding
- **Result Timing**: Display when results will be available
- **Offline-Online Sync**: Sync offline exam data when online

---

## 🔄 **Hierarchical Sharing System**

### **Category & Exam Type Sharing**

#### **Category Sharing Structure**
```typescript
// Category sharing affects all child exam types and courses
{
  categoryId: string
  institute_id: ObjectId
  branch_id?: ObjectId // null for institute-wide categories
  shareSettings: {
    shareWithBranches: boolean
    allowBranchCustomization: boolean // Allow branches to modify shared categories
    inheritanceMode: 'cascade' | 'independent' // How sharing affects child exam types
  }
  sharedBranches: ObjectId[]
  childExamTypes: ObjectId[] // Auto-populated exam types under this category
  childCourses: ObjectId[] // Auto-populated courses under this category
}
```

#### **Exam Type Sharing Structure**
```typescript
// Exam type sharing affects all child courses
{
  examTypeId: string
  institute_id: ObjectId
  branch_id?: ObjectId
  category_id: ObjectId
  shareSettings: {
    shareWithBranches: boolean
    allowBranchCustomization: boolean
    inheritFromCategory: boolean // Whether to inherit sharing from parent category
  }
  sharedBranches: ObjectId[]
  childCourses: ObjectId[] // Auto-populated courses under this exam type
}
```

#### **Hierarchical Sharing Rules**
```
Category Sharing:
├── If category is shared → All child exam types are automatically shared
├── If category allows customization → Branches can modify exam types
└── If category is unshared → Child exam types become branch-specific

Exam Type Sharing:
├── If exam type is shared → All child courses are automatically shared
├── If exam type allows customization → Branches can modify courses
└── If exam type is unshared → Child courses become branch-specific

Course Sharing:
├── Independent of parent exam type sharing
├── Can be shared even if exam type is not shared
└── Can be unshared even if exam type is shared
```

### **Branch-to-Branch Content Sharing**

#### **Share Course with Other Branches**
```typescript
// Course sharing configuration
{
  courseId: string
  institute_id: ObjectId
  originalBranch_id: ObjectId
  shareSettings: {
    shareWithBranches: boolean
    allowedBranches: ObjectId[] // Specific branches or empty for all
    shareType: 'view_only' | 'full_access' | 'copy_and_edit'
    requireApproval: boolean
    expiryDate?: Date
  }
  sharedBranches: Array<{
    branch_id: ObjectId
    sharedAt: Date
    sharedBy: ObjectId
    accessLevel: 'view' | 'edit' | 'copy'
    isActive: boolean
  }>
}
```

#### **Hierarchical Sharing Example**
```
Institute: ABC Education
├── Branch A (Main Campus)
│   ├── Category: Government Exams (Created & Shared with all branches)
│   │   ├── Exam Type: TNPSC Group 1 (Auto-shared due to category sharing)
│   │   │   ├── Course: Indian Polity (Auto-shared due to exam type sharing)
│   │   │   └── Course: Economy (Branch A specific - not shared)
│   │   └── Exam Type: TNPSC Group 2 (Auto-shared due to category sharing)
│   └── Category: Banking (Branch A specific - not shared)
│
├── Branch B (City Campus)
│   ├── Category: Government Exams (Shared from Branch A)
│   │   ├── Exam Type: TNPSC Group 1 (Shared from Branch A)
│   │   │   ├── Course: Indian Polity (Shared from Branch A)
│   │   │   └── Course: Geography (Branch B specific)
│   │   └── Exam Type: TNPSC Group 2 (Shared from Branch A)
│   │       └── Course: General Studies (Branch B created)
│   └── Category: Engineering (Branch B specific)
│
└── Branch C (Online Campus)
    ├── Category: Government Exams (Shared from Branch A)
    │   └── Exam Type: TNPSC Group 1 (Shared from Branch A)
    │       └── Course: Indian Polity (Shared from Branch A)
    └── Category: Professional Courses (Branch C specific)
```

#### **Branch Sharing Workflow**
```
Original Branch Creates Content
    ↓
Enable "Share with Branches" option
    ↓
Select target branches or "All Branches"
    ↓
Set sharing permissions (view/edit/copy)
    ↓
Content appears in target branch lists
    ↓
Target branch can access based on permissions
    ↓
If hierarchical sharing enabled:
    - Category sharing → Auto-shares all child exam types and courses
    - Exam type sharing → Auto-shares all child courses
    - Course sharing → Independent sharing
```

### **Marketplace Course Sharing**

#### **Share Course to Marketplace**
```typescript
// Marketplace sharing configuration
{
  courseId: string
  institute_id: ObjectId
  branch_id: ObjectId
  marketplaceSettings: {
    shareWithMarketplace: boolean
    marketplacePrice: number
    allowExternalPurchase: boolean
    approvalRequired: boolean
    marketplaceCategory: string
    marketplaceTags: string[]
    previewContent: {
      allowPreview: boolean
      previewLessons: number
      previewDuration: number // minutes
    }
  }
  marketplaceStatus: 'private' | 'pending_review' | 'approved' | 'rejected' | 'published'
  externalPurchases: Array<{
    purchaser_institute_id: ObjectId
    purchasedAt: Date
    price: number
    accessLevel: 'view_only' | 'copy_and_edit'
    isActive: boolean
  }>
}
```

#### **Marketplace Sharing Workflow**
```
Institute Creates Course
    ↓
Enable "Share with Marketplace" option
    ↓
Set marketplace price and permissions
    ↓
Submit for marketplace review
    ↓
Admin approves/rejects marketplace listing
    ↓
Course appears in public marketplace
    ↓
Other institutes can purchase and use
```

## 🏪 **Marketplace & E-Commerce System**

### **Course Access Types**

#### **Internal (Within Institute)**
```typescript
// Internal course access
{
  accessType: 'internal'
  scope: 'institute' | 'branch' | 'shared_branches'
  restrictions: {
    enrolledStudentsOnly: boolean
    instituteSpecificContent: boolean
    downloadRestrictions: boolean
    copyProtection: boolean
  }
  contentSecurity: {
    watermarking: boolean
    viewOnlyMode: boolean
    sessionTracking: boolean
  }
}
```

#### **Marketplace (Public)**
```typescript
// Marketplace course access
{
  accessType: 'marketplace'
  visibility: 'public'
  permissions: {
    externalInstituteAccess: boolean
    previewMode: boolean
    purchaseRequired: boolean
  }
  pricing: {
    basePrice: number
    marketplacePrice: number
    bulkDiscounts: Array<{
      quantity: number
      discountPercentage: number
    }>
    institutionalPricing: boolean
  }
}
```

### **Admin Preview & Access Control**

#### **External Admin Access**
- **Read-Only Mode**: View content without editing capabilities
- **Copy Protection**: Prevent copying, downloading, or printing
- **PDF Display-Only**: Protected PDF viewing without download
- **Watermarked Previews**: All preview content includes watermarks

#### **Purchase Process Workflow**
```
Browse Marketplace
    ↓
View Course Details & Pricing
    ↓
Click "Request to Buy"
    ↓
Notification to Content Owner
    ↓
Owner Review & Decision
    ↓
Approval/Rejection Notification
    ↓
If Approved: Full Access Granted
If Rejected: Access Remains Restricted
    ↓
Usage Tracking & Analytics
```

### **Content Ownership & Editing Rights**
- **Original Content Protection**: Master content cannot be modified by buyers
- **Editable Copies**: Buyers get editable copies for their institute
- **Version Control**: Track original vs modified versions
- **Usage Analytics**: Track content usage and modifications

---

## ⏯️ **Pause & Resume Functionality**

### **Mock Exam/Quiz Pause & Resume**
```typescript
// Exam session management
{
  examAttemptId: string
  studentId: string
  examId: string
  sessionState: {
    isPaused: boolean
    pausedAt: Date
    resumedAt?: Date
    totalPauseDuration: number // in seconds
    currentQuestionIndex: number
    answeredQuestions: Array<{
      questionId: string
      selectedAnswer: string
      timeSpent: number
      isBookmarked: boolean
    }>
    remainingTime: number
    pauseHistory: Array<{
      pausedAt: Date
      resumedAt: Date
      duration: number
      reason?: string
    }>
  }
}
```

#### **Pause & Resume Features**
- **Flexible Pausing**: Pause at any point during exam/quiz
- **State Preservation**: Save current progress and answers
- **Time Management**: Track pause duration, adjust remaining time
- **Resume from Exact Point**: Continue from where student left off
- **Multiple Pause Sessions**: Allow multiple pause/resume cycles
- **Pause Limitations**: Optional limits on pause duration/frequency

### **Video Playback Control System**

#### **Advanced Video Controls**
```typescript
// Video playback state
{
  videoId: string
  studentId: string
  playbackState: {
    currentTime: number // seconds
    duration: number
    isPlaying: boolean
    isPaused: boolean
    playbackSpeed: number // 0.5x to 2x
    volume: number // 0 to 100
    quality: '480p' | '720p' | '1080p'
    subtitlesEnabled: boolean
    subtitleLanguage: string
  }
  watchHistory: Array<{
    sessionId: string
    startTime: number
    endTime: number
    watchDuration: number
    completionPercentage: number
    timestamp: Date
  }>
  bookmarks: Array<{
    time: number
    note: string
    createdAt: Date
  }>
}
```

#### **Video Control Features**
- **Play/Pause**: Standard video controls with keyboard shortcuts
- **Resume from Last Position**: Automatically resume from last watched point
- **Close & Resume**: Remember position even after closing video
- **Playback Speed Control**: 0.25x to 2x speed options
- **Quality Selection**: Auto or manual quality selection
- **Subtitle Support**: Multi-language subtitles with timing
- **Video Bookmarks**: Mark important sections for quick access
- **Watch Progress**: Track completion percentage and time spent

---

## 📝 **Previous Year Question Paper Management**

### **Upload & Editing System**
```typescript
// Previous year paper management
{
  paperId: string
  examType: string
  year: number
  subject: string
  uploadedBy: string
  uploadDate: Date
  questions: Array<{
    questionId: string
    questionText: string
    options: string[]
    correctAnswer: string
    explanation: string
    isVisible: boolean // Toggle visibility
    isEditable: boolean
    editHistory: Array<{
      editedBy: string
      editDate: Date
      changes: object
      reason: string
    }>
  }>
  paperStatus: 'draft' | 'review' | 'published'
  previewSettings: {
    allowPreview: boolean
    previewQuestions: number // Number of questions to show in preview
    watermarkPreview: boolean
  }
}
```

#### **Question Paper Features**
- **Bulk Upload**: Upload complete question papers in various formats
- **Individual Question Edit**: Edit specific questions within papers
- **Preview Mode**: Preview papers before publishing
- **Question Visibility Toggle**: Enable/disable specific questions
- **Edit History**: Track all changes made to questions
- **Version Control**: Maintain different versions of question papers
- **Approval Workflow**: Multi-step approval process for edits

### **Question Editing Interface**
- **Rich Text Editor**: Format questions with images, equations
- **Option Management**: Add/remove/reorder answer options
- **Explanation Editor**: Detailed explanations with multimedia
- **Difficulty Assignment**: Set difficulty levels for questions
- **Tag Management**: Add subject, topic, and custom tags
- **Bulk Operations**: Edit multiple questions simultaneously

---

## 📚 **Course Add-Ons System**

### **Add-On Management**
```typescript
// Course add-ons structure
{
  courseId: string
  addOns: Array<{
    addOnId: string
    type: 'book' | 'pdf' | 'document' | 'software' | 'tool' | 'resource'
    title: string
    description: string
    content: {
      fileUrl?: string
      downloadable: boolean
      viewOnline: boolean
      printable: boolean
      watermarked: boolean
    }
    pricing: {
      isFree: boolean
      price?: number
      bundledWithCourse: boolean
      separatePurchase: boolean
    }
    access: {
      availableAfter: 'immediate' | 'course_completion' | 'specific_lesson'
      prerequisiteLesson?: string
      timeBasedAccess?: {
        availableFrom: Date
        availableUntil: Date
      }
    }
    metadata: {
      fileSize?: number
      pageCount?: number
      format: string
      language: string
      author?: string
      publisher?: string
      isbn?: string
    }
  }>
}
```

#### **Add-On Types & Features**

**Books & Publications**
- **Digital Books**: PDF, EPUB formats with reader interface
- **Physical Books**: Integration with shipping and inventory
- **Interactive Books**: Embedded videos, quizzes, hyperlinks
- **Multi-language**: Same book in different languages

**PDF Resources**
- **Study Materials**: Supplementary reading materials
- **Reference Guides**: Quick reference sheets and cheat sheets
- **Practice Papers**: Additional question papers and worksheets
- **Solutions**: Detailed solution guides with step-by-step explanations

**Software & Tools**
- **Simulation Software**: Subject-specific simulation tools
- **Calculator Tools**: Scientific calculators, formula calculators
- **Mobile Apps**: Companion mobile applications
- **Browser Extensions**: Study aids and productivity tools

**Additional Resources**
- **Audio Files**: Pronunciation guides, lectures, podcasts
- **Video Libraries**: Supplementary video content
- **Image Galleries**: Diagrams, charts, infographics
- **Interactive Content**: Games, simulations, virtual labs

### **Add-On Access Control**
- **Conditional Access**: Unlock based on course progress
- **Time-based Release**: Release add-ons at specific times
- **Performance-based**: Unlock based on quiz/exam scores
- **Subscription Tiers**: Different add-ons for different subscription levels
- **Geographic Restrictions**: Region-specific add-ons

---

## 🔔 **Notifications & Push Notifications**

### **Advanced Notification System**
```typescript
// Notification with countdown and links
{
  notificationId: string
  recipientId: string
  type: 'exam_reminder' | 'course_update' | 'result_available' | 'deadline_warning'
  title: string
  message: string
  countdown: {
    enabled: boolean
    targetDate: Date
    displayFormat: 'days_hours_minutes' | 'hours_minutes' | 'minutes_seconds'
    updateInterval: number // seconds
  }
  actionLinks: Array<{
    text: string
    url: string
    type: 'primary' | 'secondary'
    openInNewTab: boolean
  }>
  scheduling: {
    sendAt: Date
    repeatInterval?: 'daily' | 'weekly' | 'monthly'
    stopAfter?: Date
  }
  delivery: {
    channels: Array<'push' | 'email' | 'sms' | 'in_app'>
    priority: 'low' | 'normal' | 'high' | 'urgent'
    requiresAcknowledgment: boolean
  }
}
```

#### **Notification Features**
- **Countdown Timers**: Real-time countdown to events
- **Clickable Links**: Direct links to relevant pages
- **Multi-channel Delivery**: Push, email, SMS, in-app
- **Smart Scheduling**: Send at optimal times for each user
- **Personalization**: Customize based on user preferences
- **Delivery Tracking**: Track open rates, click rates
- **A/B Testing**: Test different notification formats

### **Push Notification Categories**
- **Exam Alerts**: Countdown to exam start, last-minute reminders
- **Course Updates**: New lessons, content updates, announcements
- **Result Notifications**: Exam results, grade updates
- **Deadline Warnings**: Assignment due dates, registration deadlines
- **Social Notifications**: Forum replies, messages, mentions
- **System Alerts**: Maintenance notifications, feature updates

---

## 📊 **Student Blog & Content Creation**

### **Student Blog System**
```typescript
// Student blog management
{
  blogId: string
  authorId: string // Student ID
  instituteId: string
  title: string
  content: string // Rich text content
  category: string
  tags: string[]
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'published'
  approvalWorkflow: {
    submittedAt: Date
    reviewedBy?: string // Admin ID
    reviewedAt?: Date
    approvalComments?: string
    rejectionReason?: string
  }
  engagement: {
    views: number
    likes: number
    comments: Array<{
      commentId: string
      authorId: string
      content: string
      timestamp: Date
      isApproved: boolean
    }>
    shares: number
  }
  seoSettings: {
    metaTitle: string
    metaDescription: string
    slug: string
    featuredImage?: string
  }
}
```

#### **Student Blog Features**
- **Rich Content Editor**: Text, images, videos, links
- **Category Management**: Organize blogs by subjects/topics
- **Tag System**: Flexible tagging for better discovery
- **Admin Approval**: All blogs require admin approval before publishing
- **Comment System**: Allow comments with moderation
- **Social Features**: Like, share, bookmark functionality
- **SEO Optimization**: Meta tags, slugs, featured images

### **Content Moderation**
- **Automated Screening**: Check for inappropriate content
- **Manual Review**: Admin review before publishing
- **Community Guidelines**: Clear rules for student content
- **Reporting System**: Allow users to report inappropriate content
- **Appeal Process**: Students can appeal rejected content

---

## 🚀 **Daily Operations Guide for Institute Admins**

### **Morning Routine Checklist**
- [ ] **Review Pending Approvals**: Check lessons awaiting approval
- [ ] **Monitor System Health**: Verify all systems are operational
- [ ] **Check Student Activity**: Review overnight student engagement
- [ ] **Exam Schedules**: Confirm today's scheduled exams and live classes
- [ ] **Staff Updates**: Review trainer submissions and activities
- [ ] **Analytics Dashboard**: Check key performance indicators

### **Content Management Workflow**

#### **Daily Content Review Process**
```
1. Login to Institute Admin Dashboard
2. Navigate to "Pending Approvals" section
3. Review each lesson/content item:
   - Check content quality and accuracy
   - Verify multimedia elements work properly
   - Ensure proper formatting and structure
   - Validate quiz questions and answers
4. Make approval decision:
   - Approve: Content goes live immediately
   - Reject: Send detailed feedback to trainer
   - Request Changes: Specify required modifications
5. Update approval log with comments
```

#### **Weekly Content Audit**
- **Content Performance Review**: Analyze which content performs best
- **Student Feedback Analysis**: Review student comments and ratings
- **Trainer Performance**: Evaluate trainer content creation quality
- **Content Gap Analysis**: Identify missing topics or weak areas
- **Update Content Calendar**: Plan upcoming content creation

### **Exam Management Operations**

#### **Mock Exam Setup Procedure**
```
Step 1: Exam Planning
- Define exam objectives and scope
- Select question distribution by subject
- Set difficulty level percentages
- Choose exam duration and format

Step 2: Question Selection
- Access Question Bank
- Filter by: Subject, Difficulty, Previous Year, Topic
- Auto-select or manually choose questions
- Review selected questions for quality
- Set question randomization preferences

Step 3: Exam Configuration
- Set exam title and description
- Configure timing and scheduling
- Set access permissions and restrictions
- Configure result display settings
- Set appreciation levels and messages

Step 4: Preview & Testing
- Generate exam preview
- Test all functionality
- Verify question display and navigation
- Check timer and submission process
- Validate result calculation

Step 5: Publication
- Schedule exam for students
- Send notifications and reminders
- Monitor exam participation
- Provide real-time support during exam
```

#### **Live Exam Management**
```
Pre-Exam (24-48 hours before):
- Verify all technical requirements
- Test Zoom integration and screen sharing
- Prepare institute branding materials
- Send exam instructions to students
- Coordinate with technical support team

Day of Exam (1 hour before):
- Start countdown timer
- Display institute logo and guidelines
- Check student registration and attendance
- Verify question paper and OMR sheets
- Prepare for live monitoring

During Exam:
- Monitor student participation in real-time
- Display questions on shared screen
- Collect answers via polling system
- Track time and provide warnings
- Handle technical issues promptly

Post-Exam:
- Calculate and verify results
- Display top performers and rankings
- Send result notifications to students
- Generate detailed analytics reports
- Archive exam data for future reference
```

### **Student Support & Communication**

#### **Student Query Management**
```
Query Reception:
- Monitor discussion forums for new questions
- Check direct messages and support tickets
- Review email inquiries and phone calls
- Track query response times and resolution rates

Query Processing:
- Categorize queries by type and urgency
- Assign to appropriate staff members
- Set response time expectations
- Track query status and follow-up

Response Management:
- Provide accurate and helpful responses
- Include relevant links and resources
- Follow up to ensure query resolution
- Document common queries for FAQ updates
```

#### **Communication Strategies**
- **Regular Announcements**: Weekly updates on new content and exams
- **Performance Alerts**: Notify students about their progress and areas for improvement
- **Motivational Messages**: Send encouragement and achievement recognition
- **Technical Updates**: Inform about system updates and new features
- **Event Notifications**: Remind about upcoming exams, live classes, and deadlines

### **Analytics & Performance Monitoring**

#### **Key Metrics to Track Daily**
```
Student Engagement:
- Daily active users
- Lesson completion rates
- Video watch time
- Quiz attempt rates
- Forum participation

Content Performance:
- Most popular courses and lessons
- Content with highest completion rates
- Questions with highest difficulty
- Most accessed previous year papers

System Performance:
- Page load times
- Video streaming quality
- Exam system stability
- Mobile app performance
```

#### **Weekly Analytics Review**
- **Student Progress Reports**: Individual and batch performance analysis
- **Content Effectiveness**: Identify high and low performing content
- **Trainer Productivity**: Review content creation and approval rates
- **System Usage Patterns**: Analyze peak usage times and resource allocation
- **Revenue Analytics**: Track course sales and subscription metrics

### **Quality Assurance Procedures**

#### **Content Quality Standards**
```
Written Content:
- Grammar and spelling accuracy
- Proper formatting and structure
- Relevant and up-to-date information
- Clear explanations and examples
- Appropriate difficulty level

Video Content:
- Clear audio and video quality
- Proper lighting and framing
- Engaging presentation style
- Accurate information delivery
- Appropriate duration and pacing

Quiz Questions:
- Clear and unambiguous wording
- Correct answer validation
- Appropriate difficulty level
- Relevant explanations provided
- Proper option formatting
```

#### **Regular Quality Audits**
- **Monthly Content Review**: Comprehensive review of all published content
- **Student Feedback Analysis**: Regular review of student ratings and comments
- **Trainer Performance Evaluation**: Assess content creation quality and speed
- **System Performance Testing**: Regular testing of all system features
- **Security Audits**: Regular security checks and vulnerability assessments

### **Emergency Procedures**

#### **System Downtime Protocol**
```
Immediate Actions:
1. Verify the scope and cause of downtime
2. Notify technical support team
3. Communicate with affected students via alternative channels
4. Document the incident and timeline
5. Implement temporary workarounds if possible

Communication Plan:
- Send immediate notification to all users
- Provide estimated resolution time
- Update status regularly every 30 minutes
- Offer alternative access methods if available
- Send final resolution notification

Post-Incident:
- Conduct incident review and analysis
- Update emergency procedures if needed
- Compensate affected students if necessary
- Implement preventive measures
- Document lessons learned
```

#### **Exam Emergency Procedures**
- **Technical Issues**: Backup systems and manual processes
- **Student Connectivity Problems**: Alternative access methods and extensions
- **Content Errors**: Rapid correction and re-notification procedures
- **Security Breaches**: Immediate lockdown and investigation protocols
- **Natural Disasters**: Remote access and rescheduling procedures

### **Compliance & Documentation**

#### **Record Keeping Requirements**
- **Student Records**: Enrollment, progress, and performance data
- **Content Audit Trails**: All content changes and approval history
- **Exam Records**: Question papers, answer keys, and result data
- **Staff Activity Logs**: Content creation and approval activities
- **System Access Logs**: User login and activity tracking

#### **Regulatory Compliance**
- **Data Privacy**: GDPR compliance for student data protection
- **Educational Standards**: Adherence to curriculum and quality standards
- **Financial Records**: Payment processing and revenue tracking
- **Security Compliance**: Regular security audits and certifications
- **Accessibility Standards**: Ensure content accessibility for all students

---

## 📞 **Support & Resources**

### **Technical Support Contacts**
- **System Issues**: <EMAIL> | +91-XXXX-XXXX
- **Content Problems**: <EMAIL>
- **Payment Issues**: <EMAIL>
- **Emergency Hotline**: +91-XXXX-XXXX (24/7)

### **Training Resources**
- **Admin Training Videos**: Complete video tutorials for all features
- **User Manuals**: Detailed documentation for each system component
- **Best Practices Guide**: Proven strategies for effective course management
- **FAQ Database**: Answers to common questions and issues
- **Community Forum**: Connect with other institute admins for tips and advice

### **Regular Training Schedule**
- **Monthly Webinars**: New features and best practices
- **Quarterly Reviews**: System updates and performance optimization
- **Annual Conference**: Comprehensive training and networking
- **On-demand Support**: Personalized training sessions as needed

---

## 📋 **Best Practices for Institute Admins**

### **Content Quality Management**
1. **Regular Review**: Establish regular content review schedules
2. **Quality Standards**: Maintain consistent quality standards across all content
3. **Feedback Loop**: Provide constructive feedback to trainers
4. **Version Control**: Keep track of content versions and updates

### **Student Engagement**
1. **Regular Communication**: Send regular updates and announcements
2. **Performance Monitoring**: Regularly check student progress and intervene when needed
3. **Feedback Collection**: Gather student feedback on courses and content
4. **Recognition Programs**: Implement achievement recognition systems

### **Data Management**
1. **Regular Backups**: Ensure regular backup of all content and data
2. **Analytics Review**: Regularly review analytics to identify trends and issues
3. **Performance Optimization**: Monitor system performance and optimize as needed
4. **Security Monitoring**: Keep track of access logs and security events

---

*This comprehensive documentation provides complete technical specifications and practical operational guidance for implementing and managing the Course Management system with all required features, database schemas, API endpoints, frontend components, and daily operational procedures for Institute Admins.*
