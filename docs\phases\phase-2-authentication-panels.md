# 🔐 Phase 2: Authentication & Panel Development

## 📋 Overview
Phase 2 focuses on implementing complete authentication systems and core pages for all three user panels: Super Admin, Institute Admin, and Student Portal.

### 🎯 Objectives
- ✅ Implement authentication for all user types
- ✅ Create login/register pages for each panel
- ✅ Build core dashboard pages
- ✅ Implement role-based access control
- ✅ Set up protected routes and navigation

### ⏱️ Timeline
**Duration**: 4 weeks (20 working days)
**Team Size**: 3-4 developers

## 🏗️ Authentication System Architecture

### **Authentication Routes Structure**
```
Platform Level (groups-exam.com):
├── /auth/admin/login          # Super Admin & Platform Staff
├── /auth/register             # Institute Registration (one-time)
└── Public pages (marketing, pricing)

Institute Level (abc-institute.com):
├── /auth/login                # Institute Admin, Staff, Trainers
├── /auth/user-register        # Student Registration
├── /auth/user-login           # Student Login
└── Public pages (courses, about)
```

### **User Types & Access Patterns**
```
Super Admin Level:
├── Super Admin → /auth/admin/login
├── Platform Staff → /auth/admin/login
├── Platform Accountant → /auth/admin/login
└── Technical Admin → /auth/admin/login

Institute Level:
├── Institute Admin → /auth/login
├── Branch Manager → /auth/login
├── Trainers → /auth/login
├── Staff → /auth/login
└── Students → /auth/user-register → /auth/user-login
```

## 🎨 Panel Development Structure

### **1. Super Admin Panel (apps/super-admin/)**
```
apps/super-admin/src/app/
├── 📁 auth/
│   └── admin/
│       └── login/
│           └── page.tsx              # Super Admin Login
├── 📁 dashboard/
│   └── page.tsx                      # Main Dashboard
├── 📁 institutes/
│   ├── page.tsx                      # Institute List
│   ├── create/page.tsx               # Create Institute
│   └── [id]/
│       ├── page.tsx                  # Institute Details
│       └── edit/page.tsx             # Edit Institute
├── 📁 users/
│   ├── page.tsx                      # User Management
│   └── [id]/page.tsx                 # User Details
├── 📁 billing/
│   ├── page.tsx                      # Billing Dashboard
│   ├── plans/page.tsx                # Subscription Plans
│   └── commissions/page.tsx          # Commission Tracking
├── 📁 analytics/
│   └── page.tsx                      # Platform Analytics
├── 📁 settings/
│   └── page.tsx                      # Platform Settings
└── 📁 themes/
    └── page.tsx                      # Theme Management
```

### **2. Institute Admin Panel (apps/institute-admin/)**
```
apps/institute-admin/src/app/
├── 📁 auth/
│   ├── login/
│   │   └── page.tsx                  # Institute Admin Login
│   └── register/
│       └── page.tsx                  # Institute Registration
├── 📁 dashboard/
│   └── page.tsx                      # Institute Dashboard
├── 📁 courses/
│   ├── page.tsx                      # Course List
│   ├── create/page.tsx               # Create Course
│   └── [id]/
│       ├── page.tsx                  # Course Details
│       ├── edit/page.tsx             # Edit Course
│       └── lessons/
│           ├── page.tsx              # Lesson List
│           └── create/page.tsx       # Create Lesson
├── 📁 users/
│   ├── trainers/
│   │   ├── page.tsx                  # Trainer Management
│   │   └── create/page.tsx           # Add Trainer
│   ├── students/
│   │   ├── page.tsx                  # Student Management
│   │   └── import/page.tsx           # Import Students
│   └── staff/
│       ├── page.tsx                  # Staff Management
│       └── create/page.tsx           # Add Staff
├── 📁 branches/
│   ├── page.tsx                      # Branch List
│   └── create/page.tsx               # Create Branch
├── 📁 analytics/
│   └── page.tsx                      # Institute Analytics
├── 📁 billing/
│   ├── page.tsx                      # Billing Dashboard
│   └── payments/page.tsx             # Payment Gateway Setup
└── 📁 settings/
    ├── page.tsx                      # Institute Settings
    ├── profile/page.tsx              # Institute Profile
    └── domain/page.tsx               # Domain Configuration
```

### **3. Student Portal (apps/student/)**
```
apps/student/src/app/
├── 📁 auth/
│   ├── user-register/
│   │   └── page.tsx                  # Student Registration
│   ├── user-login/
│   │   └── page.tsx                  # Student Login
│   └── verify-email/
│       └── page.tsx                  # Email Verification
├── 📁 dashboard/
│   └── page.tsx                      # Student Dashboard
├── 📁 courses/
│   ├── page.tsx                      # Available Courses
│   ├── [id]/
│   │   ├── page.tsx                  # Course Details
│   │   ├── enroll/page.tsx           # Course Enrollment
│   │   └── learn/
│   │       ├── page.tsx              # Course Player
│   │       └── [lessonId]/page.tsx   # Lesson Player
│   └── my-courses/
│       └── page.tsx                  # Enrolled Courses
├── 📁 exams/
│   ├── page.tsx                      # Available Exams
│   ├── [id]/
│   │   ├── page.tsx                  # Exam Details
│   │   ├── instructions/page.tsx     # Exam Instructions
│   │   └── take/page.tsx             # Take Exam
│   └── results/
│       └── page.tsx                  # Exam Results
├── 📁 profile/
│   ├── page.tsx                      # Student Profile
│   ├── edit/page.tsx                 # Edit Profile
│   └── certificates/page.tsx         # Certificates
├── 📁 billing/
│   ├── page.tsx                      # Payment History
│   └── invoices/page.tsx             # Invoices
└── 📁 support/
    └── page.tsx                      # Help & Support
```

## 🔑 Authentication Implementation

### **JWT Token Structure**
```typescript
interface JWTPayload {
  userId: string
  email: string
  userType: 'super_admin' | 'platform_staff' | 'institute_admin' | 'trainer' | 'student'
  instituteId?: string
  branchId?: string
  permissions: string[]
  iat: number
  exp: number
}
```

### **Role-Based Access Control**
```typescript
// Access Control Matrix
const ACCESS_CONTROL = {
  super_admin: {
    routes: ['/dashboard', '/institutes', '/users', '/billing', '/analytics', '/settings', '/themes'],
    permissions: ['platform_management', 'institute_management', 'user_management', 'billing_management']
  },
  institute_admin: {
    routes: ['/dashboard', '/courses', '/users', '/branches', '/analytics', '/billing', '/settings'],
    permissions: ['institute_management', 'course_management', 'user_management', 'analytics_view']
  },
  trainer: {
    routes: ['/dashboard', '/courses', '/students', '/analytics'],
    permissions: ['course_creation', 'student_management', 'analytics_view']
  },
  student: {
    routes: ['/dashboard', '/courses', '/exams', '/profile', '/billing'],
    permissions: ['course_access', 'exam_access', 'profile_management']
  }
}
```

## 📱 Responsive Design Requirements

### **Mobile-First Approach**
- ✅ **Breakpoints**: Mobile (320px), Tablet (768px), Desktop (1024px)
- ✅ **Navigation**: Collapsible sidebar for mobile
- ✅ **Forms**: Touch-friendly inputs and buttons
- ✅ **Tables**: Horizontal scroll on mobile

### **Cross-Browser Compatibility**
- ✅ **Chrome**: Latest 2 versions
- ✅ **Firefox**: Latest 2 versions
- ✅ **Safari**: Latest 2 versions
- ✅ **Edge**: Latest 2 versions

## 🎯 Core Features Implementation

### **Authentication Features**
- ✅ **Login/Logout**: Secure JWT-based authentication
- ✅ **Registration**: Role-based registration flows
- ✅ **Password Reset**: Email-based password recovery
- ✅ **Email Verification**: Required for student accounts
- ✅ **Session Management**: Auto-logout on token expiry
- ✅ **Device Restrictions**: 1 mobile + 1 desktop per user

### **Dashboard Features**
- ✅ **Role-based Dashboards**: Customized for each user type
- ✅ **Quick Actions**: Common tasks accessible from dashboard
- ✅ **Analytics Widgets**: Key metrics and charts
- ✅ **Recent Activity**: Latest user actions and updates
- ✅ **Notifications**: System alerts and messages

### **Navigation Features**
- ✅ **Sidebar Navigation**: Collapsible with role-based menu items
- ✅ **Breadcrumbs**: Clear navigation path
- ✅ **Search**: Global search functionality
- ✅ **User Menu**: Profile, settings, logout options
- ✅ **Theme Toggle**: Light/dark mode support

## 🔧 Technical Implementation

### **Authentication Middleware**
```typescript
// apps/api/src/middleware/auth.ts
import jwt from 'jsonwebtoken'
import { Request, Response, NextFunction } from 'express'

interface AuthRequest extends Request {
  user?: {
    id: string
    email: string
    userType: string
    instituteId?: string
    permissions: string[]
  }
}

export const authenticateToken = (req: AuthRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ message: 'Access token required' })
  }

  jwt.verify(token, process.env.JWT_SECRET!, (err, decoded) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' })
    }

    req.user = decoded as any
    next()
  })
}

export const requireRole = (allowedRoles: string[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (!req.user || !allowedRoles.includes(req.user.userType)) {
      return res.status(403).json({ message: 'Insufficient permissions' })
    }
    next()
  }
}
```

### **Protected Route Component**
```typescript
// components/shared/ProtectedRoute.tsx
'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/useAuthStore'

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles: string[]
  redirectTo?: string
}

export function ProtectedRoute({
  children,
  allowedRoles,
  redirectTo = '/auth/login'
}: ProtectedRouteProps) {
  const { user, isAuthenticated, checkAuth } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  useEffect(() => {
    if (!isAuthenticated) {
      router.push(redirectTo)
      return
    }

    if (user && !allowedRoles.includes(user.userType)) {
      router.push('/unauthorized')
      return
    }
  }, [isAuthenticated, user, allowedRoles, redirectTo, router])

  if (!isAuthenticated || !user || !allowedRoles.includes(user.userType)) {
    return <div>Loading...</div>
  }

  return <>{children}</>
}
```

### **Layout Components**
```typescript
// components/super-admin/SuperAdminLayout.tsx
'use client'

import { Sidebar } from './Sidebar'
import { Header } from './Header'
import { ProtectedRoute } from '@/components/shared/ProtectedRoute'

interface SuperAdminLayoutProps {
  children: React.ReactNode
}

export function SuperAdminLayout({ children }: SuperAdminLayoutProps) {
  return (
    <ProtectedRoute allowedRoles={['super_admin', 'platform_staff']}>
      <div className="flex h-screen bg-gray-100">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Header />
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
            {children}
          </main>
        </div>
      </div>
    </ProtectedRoute>
  )
}
```

## 📋 Implementation Checklist

### **Phase 2.1: Authentication System (Week 1)**
- [ ] ✅ JWT authentication middleware in apps/api/
- [ ] ✅ User registration endpoints for all user types
- [ ] ✅ Login endpoints with role-based responses
- [ ] ✅ Password reset functionality
- [ ] ✅ Email verification system
- [ ] ✅ Session management and token refresh
- [ ] ✅ Device restriction implementation

### **Phase 2.2: Super Admin Panel (Week 2)**
- [ ] ✅ Super Admin login page (/auth/admin/login)
- [ ] ✅ Super Admin dashboard with key metrics
- [ ] ✅ Institute management (list, create, edit, delete)
- [ ] ✅ User management across all institutes
- [ ] ✅ Billing and commission tracking
- [ ] ✅ Platform analytics and reporting
- [ ] ✅ System settings and configuration

### **Phase 2.3: Institute Admin Panel (Week 3)**
- [ ] ✅ Institute registration page (/auth/register)
- [ ] ✅ Institute Admin login page (/auth/login)
- [ ] ✅ Institute dashboard with institute metrics
- [ ] ✅ Course management (create, edit, publish)
- [ ] ✅ User management (trainers, students, staff)
- [ ] ✅ Branch management
- [ ] ✅ Institute analytics and reports
- [ ] ✅ Billing and payment gateway setup

### **Phase 2.4: Student Portal (Week 4)**
- [ ] ✅ Student registration page (/auth/user-register)
- [ ] ✅ Student login page (/auth/user-login)
- [ ] ✅ Email verification flow
- [ ] ✅ Student dashboard with enrolled courses
- [ ] ✅ Course browsing and enrollment
- [ ] ✅ Course player interface
- [ ] ✅ Exam interface and results
- [ ] ✅ Profile management and certificates

## 🎨 UI/UX Design Guidelines

### **Design System**
- ✅ **Colors**: Primary, secondary, accent colors for each panel
- ✅ **Typography**: Consistent font hierarchy
- ✅ **Spacing**: 8px grid system
- ✅ **Components**: Reusable UI components
- ✅ **Icons**: Consistent icon library (Lucide React)

### **Panel-Specific Themes**
```typescript
// Super Admin Theme
const superAdminTheme = {
  primary: '#1e40af',      // Blue
  secondary: '#64748b',    // Slate
  accent: '#059669',       // Emerald
  background: '#f8fafc',   // Light gray
  sidebar: '#1e293b'       // Dark slate
}

// Institute Admin Theme
const instituteAdminTheme = {
  primary: '#7c3aed',      // Purple
  secondary: '#64748b',    // Slate
  accent: '#dc2626',       // Red
  background: '#fefefe',   // White
  sidebar: '#4c1d95'       // Dark purple
}

// Student Theme
const studentTheme = {
  primary: '#059669',      // Emerald
  secondary: '#6b7280',    // Gray
  accent: '#f59e0b',       // Amber
  background: '#f9fafb',   // Light gray
  sidebar: '#065f46'       // Dark emerald
}
```

## 🔧 Phase 2 Backend Implementation

### **Users Collection**
**File**: `apps/api/src/collections/Users.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin, isAdminOrSelf } from '../access/index'

const Users: CollectionConfig = {
  slug: 'users',
  auth: {
    tokenExpiration: 7200, // 2 hours
    verify: {
      generateEmailHTML: ({ token, user }) => {
        return `
          <h1>Verify your email</h1>
          <p>Hello ${user.firstName},</p>
          <p>Please click the link below to verify your email address:</p>
          <a href="${process.env.FRONTEND_URL}/verify-email?token=${token}">Verify Email</a>
        `
      },
    },
    forgotPassword: {
      generateEmailHTML: ({ token, user }) => {
        return `
          <h1>Reset your password</h1>
          <p>Hello ${user.firstName},</p>
          <p>Please click the link below to reset your password:</p>
          <a href="${process.env.FRONTEND_URL}/reset-password?token=${token}">Reset Password</a>
        `
      },
    },
  },
  admin: {
    useAsTitle: 'email',
    defaultColumns: ['firstName', 'lastName', 'email', 'userType', 'isActive'],
  },
  access: {
    read: isAdminOrSelf,
    create: () => true, // Allow registration
    update: isAdminOrSelf,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'firstName',
      type: 'text',
      required: true,
      maxLength: 50,
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
      maxLength: 50,
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true,
      index: true,
    },
    {
      name: 'userType',
      type: 'select',
      required: true,
      options: [
        { label: 'Super Admin', value: 'super_admin' },
        { label: 'Institute Admin', value: 'institute_admin' },
        { label: 'Trainer', value: 'trainer' },
        { label: 'Student', value: 'student' },
      ],
      index: true,
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: false,
      admin: {
        condition: (data) => data.userType !== 'super_admin',
      },
    },
    {
      name: 'profile',
      type: 'group',
      fields: [
        {
          name: 'avatar',
          type: 'upload',
          relationTo: 'media',
        },
        {
          name: 'phone',
          type: 'text',
          validate: (val) => {
            if (val && !/^[+]?[1-9][\d\s\-\(\)]{7,15}$/.test(val)) {
              return 'Invalid phone number format'
            }
            return true
          },
        },
        {
          name: 'dateOfBirth',
          type: 'date',
        },
        {
          name: 'bio',
          type: 'textarea',
          maxLength: 500,
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      access: {
        update: isAdmin,
      },
    },
    {
      name: 'lastLogin',
      type: 'date',
      admin: { readOnly: true },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          data.isActive = true
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default Users
```

### **Institutes Collection**
**File**: `apps/api/src/collections/Institutes.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin, isInstituteAdmin } from '../access/index'

const Institutes: CollectionConfig = {
  slug: 'institutes',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'email', 'status', 'subscriptionPlan', 'createdAt'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.userType === 'super_admin') return true
      if (user?.userType === 'institute_admin') {
        return { id: { equals: user.institute } }
      }
      return false
    },
    create: () => true, // Allow institute registration
    update: isInstituteAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      index: true,
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true,
      index: true,
    },
    {
      name: 'phone',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'address',
      type: 'group',
      fields: [
        { name: 'street', type: 'text', required: true },
        { name: 'city', type: 'text', required: true },
        { name: 'state', type: 'text', required: true },
        { name: 'country', type: 'text', required: true },
        { name: 'zipCode', type: 'text', required: true },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'pending',
      options: [
        { label: 'Pending Review', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Active', value: 'active' },
        { label: 'Suspended', value: 'suspended' },
        { label: 'Rejected', value: 'rejected' },
      ],
      index: true,
    },
    {
      name: 'subscriptionPlan',
      type: 'select',
      required: true,
      defaultValue: 'starter',
      options: [
        { label: 'Starter', value: 'starter' },
        { label: 'Growth', value: 'growth' },
        { label: 'Professional', value: 'professional' },
        { label: 'Enterprise', value: 'enterprise' },
      ],
      index: true,
    },
    {
      name: 'domain',
      type: 'group',
      fields: [
        {
          name: 'customDomain',
          type: 'text',
          required: true,
          unique: true,
          admin: {
            description: 'Institute\'s custom domain (e.g., abc-institute.com)'
          }
        },
        {
          name: 'customDomain',
          type: 'text',
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Generate slug from name
          if (!data.slug && data.name) {
            data.slug = data.name
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/(^-|-$)/g, '')
          }

          // Set default custom domain suggestion
          if (!data.domain?.customDomain && data.slug) {
            data.domain = {
              ...data.domain,
              customDomain: `${data.slug}.com`, // Suggestion only
            }
          }
        }
        return data
      },
    ],
  ],
  timestamps: true,
}

export default Institutes
```

### **Authentication Endpoints**
**File**: `apps/api/src/endpoints/auth/index.ts`

```typescript
import { Endpoint } from 'payload/config'
import jwt from 'jsonwebtoken'
import bcrypt from 'bcrypt'

const authEndpoints: Endpoint[] = [
  // Login endpoint
  {
    path: '/auth/login',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { email, password, userType } = req.body

        if (!email || !password) {
          return res.status(400).json({
            error: 'Email and password are required'
          })
        }

        // Find user
        const user = await req.payload.find({
          collection: 'users',
          where: {
            and: [
              { email: { equals: email.toLowerCase() } },
              { userType: { equals: userType } }
            ]
          }
        })

        if (!user.docs.length) {
          return res.status(401).json({
            error: 'Invalid credentials'
          })
        }

        const userDoc = user.docs[0]

        // Check if user is active
        if (!userDoc.isActive) {
          return res.status(401).json({
            error: 'Account is deactivated'
          })
        }

        // Check password
        const isValidPassword = await bcrypt.compare(password, userDoc.password)
        if (!isValidPassword) {
          return res.status(401).json({
            error: 'Invalid credentials'
          })
        }

        // Create JWT token
        const token = jwt.sign(
          {
            userId: userDoc.id,
            userType: userDoc.userType,
            institute: userDoc.institute
          },
          process.env.JWT_SECRET!,
          { expiresIn: '2h' }
        )

        // Update last login
        await req.payload.update({
          collection: 'users',
          id: userDoc.id,
          data: {
            lastLogin: new Date()
          }
        })

        // DON'T set cookies on API domain for cross-domain setup
        // Frontend will handle cookie storage on its own domain
        res.json({
          success: true,
          user: {
            id: userDoc.id,
            email: userDoc.email,
            firstName: userDoc.firstName,
            lastName: userDoc.lastName,
            userType: userDoc.userType,
            institute: userDoc.institute
          },
          token,
          expiresIn: 7200  // 2 hours - for frontend cookie setup
        })

      } catch (error) {
        console.error('Login error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  },

  // Register endpoint
  {
    path: '/auth/register',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { email, password, firstName, lastName, userType, institute } = req.body

        // Validation
        if (!email || !password || !firstName || !lastName || !userType) {
          return res.status(400).json({
            error: 'All required fields must be provided'
          })
        }

        // Check if user already exists
        const existingUser = await req.payload.find({
          collection: 'users',
          where: {
            email: { equals: email.toLowerCase() }
          }
        })

        if (existingUser.docs.length > 0) {
          return res.status(409).json({
            error: 'User with this email already exists'
          })
        }

        // Create user
        const newUser = await req.payload.create({
          collection: 'users',
          data: {
            email: email.toLowerCase(),
            password,
            firstName,
            lastName,
            userType,
            institute: userType !== 'super_admin' ? institute : undefined,
            isActive: true
          }
        })

        res.status(201).json({
          success: true,
          message: 'User registered successfully',
          user: {
            id: newUser.id,
            email: newUser.email,
            firstName: newUser.firstName,
            lastName: newUser.lastName,
            userType: newUser.userType
          }
        })

      } catch (error) {
        console.error('Registration error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  }
]

export default authEndpoints
```

### **Cross-Domain Authentication Setup**
**File**: `apps/api/src/middleware/cors.ts`

```typescript
// Enhanced CORS configuration for cross-domain authentication
export const corsConfig = {
  origin: [
    'https://admin.groups-exam.com',      // Super Admin Panel
    'https://groups-exam.com',            // Main Platform
    // Institute custom domains (dynamically added)
    'https://abc-institute.com',          // Example Institute Domain
    'https://xyz-academy.com',            // Example Institute Domain
    'http://localhost:3000',              // Development - Super Admin
    'http://localhost:3001',              // Development - API
    'http://localhost:3002',              // Development - Institute Testing
  ],
  credentials: true,  // Important for cross-domain requests
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  exposedHeaders: ['Set-Cookie']
}
```

### **Frontend Authentication Handler**
**File**: `components/shared/auth/AuthService.ts`

```typescript
// Cross-domain authentication service
import Cookies from 'js-cookie'

class AuthService {
  private apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api.groups-exam.com'

  async login(credentials: LoginCredentials) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',  // Important for CORS
        body: JSON.stringify(credentials)
      })

      const data = await response.json()

      if (data.success) {
        // Frontend handles cookie storage on its own domain
        Cookies.set('session', data.token, {
          domain: window.location.hostname,  // Works for institute's custom domain
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          expires: new Date(Date.now() + data.expiresIn * 1000)
        })

        // Also store user info
        Cookies.set('user', JSON.stringify(data.user), {
          domain: '.groups-exam.com',
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          expires: new Date(Date.now() + data.expiresIn * 1000)
        })

        return { success: true, user: data.user }
      } else {
        throw new Error(data.error || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  async logout() {
    try {
      // Call backend logout endpoint
      await fetch(`${this.apiBaseUrl}/auth/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Always clear frontend cookies
      Cookies.remove('session', { domain: '.groups-exam.com' })
      Cookies.remove('user', { domain: '.groups-exam.com' })
    }
  }

  getToken(): string | null {
    return Cookies.get('session') || null
  }

  getUser(): User | null {
    const userStr = Cookies.get('user')
    return userStr ? JSON.parse(userStr) : null
  }

  isAuthenticated(): boolean {
    return !!this.getToken()
  }

  // API call wrapper with automatic token inclusion
  async apiCall(endpoint: string, options: RequestInit = {}) {
    const token = this.getToken()

    return fetch(`${this.apiBaseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json',
        ...options.headers
      },
      credentials: 'include'
    })
  }
}

export const authService = new AuthService()
```

### **Updated Payload Config**
**File**: `apps/api/payload.config.ts`

```typescript
import { buildConfig } from 'payload/config'
import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { webpackBundler } from '@payloadcms/bundler-webpack'
import { slateEditor } from '@payloadcms/richtext-slate'
import path from 'path'

// Collections
import Users from './src/collections/Users'
import Institutes from './src/collections/Institutes'

// Endpoints
import authEndpoints from './src/endpoints/auth'

export default buildConfig({
  admin: {
    user: Users.slug,
    bundler: webpackBundler(),
  },
  editor: slateEditor({}),
  collections: [
    Users,
    Institutes,
  ],
  typescript: {
    outputFile: path.resolve(__dirname, 'payload-types.ts'),
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || 'mongodb://localhost:27017/groups-exam-lms',
  }),
  endpoints: [
    ...authEndpoints,
  ],
  cors: [
    // Production domains
    'https://admin.groups-exam.com',      // Super Admin Panel
    'https://groups-exam.com',            // Main Platform
    // Institute custom domains (dynamically loaded from database)
    'https://abc-institute.com',          // Example Institute Domain
    'https://xyz-academy.com',            // Example Institute Domain
    // Development domains
    'http://localhost:3000',              // Super Admin
    'http://localhost:3001',              // API
    'http://localhost:3002',              // Institute Testing
  ],
  csrf: [
    'https://admin.groups-exam.com',
    'https://groups-exam.com',
    // Institute custom domains (dynamically loaded)
    'https://abc-institute.com',
    'https://xyz-academy.com',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
  ],
})
```

## 🎯 Phase 2 Success Criteria

### **Functional Requirements**
- [ ] ✅ All authentication flows work correctly
- [ ] ✅ User registration and login are functional
- [ ] ✅ Role-based access control is implemented
- [ ] ✅ Institute registration and approval workflow works
- [ ] ✅ All three dashboards display correctly
- [ ] ✅ Navigation between pages works smoothly

### **Backend Requirements**
- [ ] ✅ Users collection is fully functional
- [ ] ✅ Institutes collection is implemented
- [ ] ✅ Authentication endpoints work correctly
- [ ] ✅ JWT token generation and validation
- [ ] ✅ Password hashing with bcrypt
- [ ] ✅ Access control functions implemented
- [ ] ✅ Database relationships are working

### **Security Requirements**
- [ ] ✅ Passwords are securely hashed
- [ ] ✅ JWT tokens are properly implemented
- [ ] ✅ Session management works correctly
- [ ] ✅ Input validation prevents malicious data
- [ ] ✅ CORS is properly configured
- [ ] ✅ Rate limiting is implemented

### **User Experience Requirements**
- [ ] ✅ Toast notifications work for all actions
- [ ] ✅ Form validation provides clear feedback
- [ ] ✅ Loading states are implemented
- [ ] ✅ Error handling is user-friendly
- [ ] ✅ Responsive design works on all devices

## 🔄 **Complete CRUD API Integration**

### **User Management CRUD Endpoints**
**File**: `apps/api/src/endpoints/users/crud.ts`

```typescript
import { Endpoint } from 'payload/config'

const usersCrudEndpoints: Endpoint[] = [
  // Get Users (with pagination and filters)
  {
    path: '/users',
    method: 'get',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const {
          page = 1,
          limit = 20,
          search = '',
          userType = '',
          isActive = '',
          institute = ''
        } = req.query

        // Build where clause
        const where: any = {}

        // Search filter
        if (search) {
          where.or = [
            { name: { contains: search } },
            { email: { contains: search } },
            { phone: { contains: search } }
          ]
        }

        // User type filter
        if (userType) {
          where.userType = { equals: userType }
        }

        // Active filter
        if (isActive !== '') {
          where.isActive = { equals: isActive === 'true' }
        }

        // Institute filter
        if (institute) {
          where.institute = { equals: institute }
        }

        // Scope restrictions
        if (currentUser.userType === 'institute_admin') {
          where.institute = { equals: currentUser.institute }
        } else if (currentUser.userType === 'branch_admin') {
          where.and = [
            { institute: { equals: currentUser.institute } },
            { branch: { equals: currentUser.branch } }
          ]
        }

        const users = await req.payload.find({
          collection: 'users',
          where,
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          sort: '-createdAt',
          populate: ['institute', 'branch']
        })

        res.json({
          success: true,
          ...users
        })

      } catch (error) {
        console.error('Get users error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch users'
        })
      }
    }
  },

  // Create User
  {
    path: '/users',
    method: 'post',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can create users'
          })
        }

        const { name, email, password, userType, institute, branch, phone, isActive } = req.body

        // Validate required fields
        if (!name || !email || !password || !userType) {
          return res.status(400).json({
            error: 'Name, email, password, and user type are required'
          })
        }

        // Check if user already exists
        const existingUser = await req.payload.find({
          collection: 'users',
          where: { email: { equals: email } },
          limit: 1
        })

        if (existingUser.totalDocs > 0) {
          return res.status(400).json({
            error: 'User with this email already exists'
          })
        }

        // Set institute/branch based on current user permissions
        let userData: any = {
          name,
          email,
          password,
          userType,
          phone,
          isActive: isActive !== undefined ? isActive : true
        }

        if (currentUser.userType === 'institute_admin') {
          userData.institute = currentUser.institute
          if (branch) userData.branch = branch
        } else if (currentUser.userType === 'super_admin') {
          if (institute) userData.institute = institute
          if (branch) userData.branch = branch
        }

        const user = await req.payload.create({
          collection: 'users',
          data: userData
        })

        // Remove password from response
        const { password: _, ...userResponse } = user

        res.json({
          success: true,
          user: userResponse,
          message: 'User created successfully'
        })

      } catch (error) {
        console.error('Create user error:', error)
        res.status(500).json({
          error: error.message || 'Failed to create user'
        })
      }
    }
  }
]

export default usersCrudEndpoints
```

## 🗄️ **Zustand State Management**

### **Authentication Store**
**File**: `apps/super-admin/src/stores/useAuthStore.ts`

```typescript
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { toast } from 'sonner'
import Cookies from 'js-cookie'

interface User {
  id: string
  name: string
  email: string
  userType: 'super_admin' | 'institute_admin' | 'branch_admin' | 'teacher' | 'student'
  institute?: string
  branch?: string
  isActive: boolean
  lastLogin?: string
}

interface AuthState {
  // Data
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean

  // Actions
  login: (credentials: { email: string; password: string }) => Promise<void>
  logout: () => Promise<void>
  register: (userData: any) => Promise<void>
  updateProfile: (data: any) => Promise<void>
  checkAuth: () => Promise<void>

  // Helpers
  setUser: (user: User | null) => void
  setToken: (token: string | null) => void
  clearAuth: () => void
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,

        // Login
        login: async (credentials) => {
          set({ isLoading: true })
          try {
            const response = await fetch('/api/auth/login', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              credentials: 'include',
              body: JSON.stringify(credentials)
            })

            if (!response.ok) {
              const error = await response.json()
              throw new Error(error.message || 'Login failed')
            }

            const data = await response.json()

            // Store token in cookie (handled by frontend)
            Cookies.set('payload-token', data.token, {
              expires: 7, // 7 days
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'lax'
            })

            set({
              user: data.user,
              token: data.token,
              isAuthenticated: true
            })

            toast.success('Login successful')

          } catch (error) {
            console.error('Login error:', error)
            toast.error(error.message || 'Login failed')
            throw error
          } finally {
            set({ isLoading: false })
          }
        },

        // Logout
        logout: async () => {
          try {
            await fetch('/api/auth/logout', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${get().token}`
              },
              credentials: 'include'
            })

            // Clear cookie
            Cookies.remove('payload-token')

            set({
              user: null,
              token: null,
              isAuthenticated: false
            })

            toast.success('Logged out successfully')

          } catch (error) {
            console.error('Logout error:', error)
            // Clear state anyway
            get().clearAuth()
          }
        },

        // Register
        register: async (userData) => {
          set({ isLoading: true })
          try {
            const response = await fetch('/api/auth/register', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(userData)
            })

            if (!response.ok) {
              const error = await response.json()
              throw new Error(error.message || 'Registration failed')
            }

            const data = await response.json()
            toast.success('Registration successful')

            return data

          } catch (error) {
            console.error('Registration error:', error)
            toast.error(error.message || 'Registration failed')
            throw error
          } finally {
            set({ isLoading: false })
          }
        },

        // Update profile
        updateProfile: async (data) => {
          const { user, token } = get()
          if (!user || !token) {
            throw new Error('Not authenticated')
          }

          try {
            const response = await fetch(`/api/users/${user.id}`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              credentials: 'include',
              body: JSON.stringify(data)
            })

            if (!response.ok) {
              const error = await response.json()
              throw new Error(error.message || 'Update failed')
            }

            const result = await response.json()

            set({
              user: { ...user, ...result.user }
            })

            toast.success('Profile updated successfully')

          } catch (error) {
            console.error('Update profile error:', error)
            toast.error(error.message || 'Update failed')
            throw error
          }
        },

        // Check authentication
        checkAuth: async () => {
          const token = Cookies.get('payload-token')

          if (!token) {
            get().clearAuth()
            return
          }

          try {
            const response = await fetch('/api/auth/me', {
              headers: {
                'Authorization': `Bearer ${token}`
              },
              credentials: 'include'
            })

            if (!response.ok) {
              throw new Error('Token invalid')
            }

            const data = await response.json()

            set({
              user: data.user,
              token,
              isAuthenticated: true
            })

          } catch (error) {
            console.error('Check auth error:', error)
            get().clearAuth()
          }
        },

        // Helpers
        setUser: (user) => set({ user, isAuthenticated: !!user }),
        setToken: (token) => set({ token }),
        clearAuth: () => {
          Cookies.remove('payload-token')
          set({
            user: null,
            token: null,
            isAuthenticated: false
          })
        }
      }),
      {
        name: 'auth-store',
        partialize: (state) => ({
          user: state.user,
          token: state.token,
          isAuthenticated: state.isAuthenticated
        })
      }
    ),
    {
      name: 'auth-store'
    }
  )
)
```

### **User Management Store**
**File**: `apps/super-admin/src/stores/useUsersStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface User {
  id: string
  name: string
  email: string
  userType: string
  institute?: any
  branch?: any
  phone?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface UsersState {
  // Data
  users: User[]

  // UI State
  isLoading: boolean
  viewMode: 'card' | 'list'

  // Pagination
  pagination: PaginationInfo

  // Filters
  filters: {
    search: string
    userType: string
    isActive: string
    institute: string
  }

  // Actions
  fetchUsers: (page?: number, filters?: any) => Promise<void>
  createUser: (data: any) => Promise<void>
  updateUser: (id: string, data: any) => Promise<void>
  deleteUser: (id: string) => Promise<void>

  setViewMode: (mode: 'card' | 'list') => void
  setFilters: (filters: any) => void
  clearFilters: () => void
}

export const useUsersStore = create<UsersState>()(
  devtools(
    (set, get) => ({
      // Initial state
      users: [],

      isLoading: false,
      viewMode: 'card',

      pagination: {
        page: 1,
        limit: 20,
        totalPages: 1,
        totalDocs: 0,
        hasNextPage: false,
        hasPrevPage: false
      },

      filters: {
        search: '',
        userType: '',
        isActive: 'true',
        institute: ''
      },

      // Fetch users
      fetchUsers: async (page = 1, filters = {}) => {
        set({ isLoading: true })
        try {
          const currentFilters = { ...get().filters, ...filters }
          const params = new URLSearchParams({
            page: page.toString(),
            limit: get().pagination.limit.toString(),
            ...Object.fromEntries(
              Object.entries(currentFilters).filter(([_, value]) => value !== '')
            )
          })

          const response = await fetch(`/api/users?${params}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch users')
          }

          const data = await response.json()

          set({
            users: data.docs,
            pagination: {
              page: data.page,
              limit: data.limit,
              totalPages: data.totalPages,
              totalDocs: data.totalDocs,
              hasNextPage: data.hasNextPage,
              hasPrevPage: data.hasPrevPage
            }
          })

        } catch (error) {
          console.error('Fetch users error:', error)
          toast.error('Failed to fetch users')
        } finally {
          set({ isLoading: false })
        }
      },

      // Create user
      createUser: async (data: any) => {
        try {
          const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to create user')
          }

          const result = await response.json()

          // Refresh users list
          await get().fetchUsers()

          toast.success('User created successfully')
          return result

        } catch (error) {
          console.error('Create user error:', error)
          toast.error(error.message || 'Failed to create user')
          throw error
        }
      },

      // Update user
      updateUser: async (id: string, data: any) => {
        try {
          const response = await fetch(`/api/users/${id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to update user')
          }

          const result = await response.json()

          // Update user in state
          set(state => ({
            users: state.users.map(user =>
              user.id === id ? { ...user, ...result.user } : user
            )
          }))

          toast.success('User updated successfully')
          return result

        } catch (error) {
          console.error('Update user error:', error)
          toast.error(error.message || 'Failed to update user')
          throw error
        }
      },

      // Delete user
      deleteUser: async (id: string) => {
        try {
          const response = await fetch(`/api/users/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to delete user')
          }

          // Remove user from state
          set(state => ({
            users: state.users.filter(user => user.id !== id)
          }))

          toast.success('User deleted successfully')

        } catch (error) {
          console.error('Delete user error:', error)
          toast.error(error.message || 'Failed to delete user')
          throw error
        }
      },

      // Set view mode
      setViewMode: (mode: 'card' | 'list') => {
        set({ viewMode: mode })
      },

      // Set filters
      setFilters: (filters: any) => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }))
      },

      // Clear filters
      clearFilters: () => {
        set({
          filters: {
            search: '',
            userType: '',
            isActive: 'true',
            institute: ''
          }
        })
      }
    }),
    {
      name: 'users-store'
    }
  )
)
```

## 📝 **Formik + Yup Forms**

### **Login Form Component**
**File**: `apps/super-admin/src/components/auth/LoginForm.tsx`

```typescript
'use client'

import { useState } from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { useAuthStore } from '@/stores/useAuthStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Loader2, Eye, EyeOff, LogIn } from 'lucide-react'
import { useRouter } from 'next/navigation'

// Validation Schema
const loginValidationSchema = Yup.object({
  email: Yup.string()
    .required('Email is required')
    .email('Please enter a valid email address'),
  password: Yup.string()
    .required('Password is required')
    .min(6, 'Password must be at least 6 characters')
})

export function LoginForm() {
  const [showPassword, setShowPassword] = useState(false)
  const { login, isLoading } = useAuthStore()
  const router = useRouter()

  const initialValues = {
    email: '',
    password: ''
  }

  const handleSubmit = async (values: any) => {
    try {
      await login(values)
      router.push('/dashboard')
    } catch (error) {
      // Error handled in store with toast
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-center">
          <LogIn className="h-5 w-5 mr-2" />
          Login to Your Account
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Formik
          initialValues={initialValues}
          validationSchema={loginValidationSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched }) => (
            <Form className="space-y-4">
              {/* Email Field */}
              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <Field
                  as={Input}
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  className={errors.email && touched.email ? 'border-red-500' : ''}
                />
                <ErrorMessage name="email" component="div" className="text-sm text-red-500" />
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <Label htmlFor="password">Password *</Label>
                <div className="relative">
                  <Field
                    as={Input}
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    className={`pr-10 ${errors.password && touched.password ? 'border-red-500' : ''}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <ErrorMessage name="password" component="div" className="text-sm text-red-500" />
              </div>

              {/* Submit Button */}
              <Button type="submit" disabled={isLoading} className="w-full">
                {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Sign In
              </Button>

              {/* Forgot Password Link */}
              <div className="text-center">
                <a href="/forgot-password" className="text-sm text-primary hover:underline">
                  Forgot your password?
                </a>
              </div>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
```

### **User Form Component**
**File**: `apps/super-admin/src/components/users/UserForm.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { useUsersStore } from '@/stores/useUsersStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Loader2, User, Eye, EyeOff } from 'lucide-react'
import { toast } from 'sonner'

// Validation Schema
const userValidationSchema = Yup.object({
  name: Yup.string()
    .required('Name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  email: Yup.string()
    .required('Email is required')
    .email('Please enter a valid email address'),
  password: Yup.string()
    .when('isEdit', {
      is: false,
      then: () => Yup.string()
        .required('Password is required')
        .min(6, 'Password must be at least 6 characters')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
      otherwise: () => Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number')
    }),
  userType: Yup.string()
    .required('User type is required')
    .oneOf(['super_admin', 'institute_admin', 'branch_admin', 'teacher', 'student'], 'Invalid user type'),
  phone: Yup.string()
    .matches(/^[+]?[\d\s\-\(\)]+$/, 'Please enter a valid phone number'),
  institute: Yup.string()
    .when('userType', {
      is: (userType: string) => ['institute_admin', 'branch_admin', 'teacher', 'student'].includes(userType),
      then: () => Yup.string().required('Institute is required for this user type'),
      otherwise: () => Yup.string()
    }),
  branch: Yup.string()
    .when('userType', {
      is: (userType: string) => ['branch_admin', 'teacher', 'student'].includes(userType),
      then: () => Yup.string().required('Branch is required for this user type'),
      otherwise: () => Yup.string()
    }),
  isActive: Yup.boolean()
})

interface UserFormProps {
  initialData?: any
  onSubmit: (values: any) => Promise<void>
  onCancel: () => void
}

export function UserForm({ initialData, onSubmit, onCancel }: UserFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const initialValues = {
    name: initialData?.name || '',
    email: initialData?.email || '',
    password: '',
    userType: initialData?.userType || 'student',
    phone: initialData?.phone || '',
    institute: initialData?.institute?.id || '',
    branch: initialData?.branch?.id || '',
    isActive: initialData?.isActive ?? true,
    isEdit: !!initialData
  }

  const handleSubmit = async (values: any) => {
    setIsSubmitting(true)
    try {
      // Remove empty password for edit
      if (values.isEdit && !values.password) {
        delete values.password
      }
      delete values.isEdit

      await onSubmit(values)
      toast.success(initialData ? 'User updated successfully' : 'User created successfully')
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  const userTypeOptions = [
    { value: 'super_admin', label: 'Super Admin' },
    { value: 'institute_admin', label: 'Institute Admin' },
    { value: 'branch_admin', label: 'Branch Admin' },
    { value: 'teacher', label: 'Teacher' },
    { value: 'student', label: 'Student' }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <User className="h-5 w-5 mr-2" />
          {initialData ? 'Edit User' : 'Create New User'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Formik
          initialValues={initialValues}
          validationSchema={userValidationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched }) => (
            <Form className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name *</Label>
                  <Field
                    as={Input}
                    id="name"
                    name="name"
                    placeholder="Enter full name"
                    className={errors.name && touched.name ? 'border-red-500' : ''}
                  />
                  <ErrorMessage name="name" component="div" className="text-sm text-red-500" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Field
                    as={Input}
                    id="email"
                    name="email"
                    type="email"
                    placeholder="Enter email address"
                    className={errors.email && touched.email ? 'border-red-500' : ''}
                  />
                  <ErrorMessage name="email" component="div" className="text-sm text-red-500" />
                </div>
              </div>

              {/* Password */}
              <div className="space-y-2">
                <Label htmlFor="password">
                  Password {!initialData && '*'}
                  {initialData && <span className="text-sm text-muted-foreground">(leave blank to keep current)</span>}
                </Label>
                <div className="relative">
                  <Field
                    as={Input}
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder={initialData ? 'Enter new password' : 'Enter password'}
                    className={`pr-10 ${errors.password && touched.password ? 'border-red-500' : ''}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                <ErrorMessage name="password" component="div" className="text-sm text-red-500" />
              </div>

              {/* User Type and Contact */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="userType">User Type *</Label>
                  <Select
                    value={values.userType}
                    onValueChange={(value) => setFieldValue('userType', value)}
                  >
                    <SelectTrigger className={errors.userType && touched.userType ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select user type" />
                    </SelectTrigger>
                    <SelectContent>
                      {userTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <ErrorMessage name="userType" component="div" className="text-sm text-red-500" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Field
                    as={Input}
                    id="phone"
                    name="phone"
                    placeholder="Enter phone number"
                    className={errors.phone && touched.phone ? 'border-red-500' : ''}
                  />
                  <ErrorMessage name="phone" component="div" className="text-sm text-red-500" />
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center space-x-2">
                <Field
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  className="rounded"
                />
                <Label htmlFor="isActive">Active User</Label>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  {initialData ? 'Update User' : 'Create User'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
```

## 🎯 **Phase 2 Complete Integration Summary**

### **✅ CRUD Operations:**
```typescript
✅ User Management API:
├── GET /api/users → List users with pagination & filters
├── POST /api/users → Create new user
├── PATCH /api/users/:id → Update user
├── DELETE /api/users/:id → Delete user
└── GET /api/auth/me → Get current user profile
```

### **✅ Zustand State Management:**
```typescript
✅ Authentication Store:
├── 🔐 login() → JWT authentication with cookie storage
├── 🚪 logout() → Clear session and cookies
├── 📝 register() → User registration
├── 👤 updateProfile() → Profile updates
└── 🔍 checkAuth() → Token validation

✅ Users Management Store:
├── 📊 fetchUsers() → Paginated user list with filters
├── 📝 createUser() → Create user with validation
├── ✏️ updateUser() → Update user details
├── 🗑️ deleteUser() → Remove user
└── 🔍 Filter management with real-time updates
```

### **✅ Formik + Yup Forms:**
```typescript
✅ Login Form:
├── 📧 Email validation with format checking
├── 🔒 Password validation with strength requirements
├── 👁️ Show/hide password toggle
├── 🔔 Toast notifications for success/error
└── 🎯 Automatic redirect after login

✅ User Form:
├── 📝 Full name validation (2-100 characters)
├── 📧 Email validation with uniqueness check
├── 🔒 Password validation (conditional for edit)
├── 📱 Phone number format validation
├── 👥 User type selection with role-based fields
├── 🏢 Institute/branch selection (conditional)
└── ✅ Active status toggle
```

### **✅ Enhanced Features:**
```typescript
✅ Security Features:
├── 🔐 JWT token authentication
├── 🍪 Secure cookie storage
├── 🎯 Role-based access control
├── 🔍 Scope-based filtering (institute/branch)
├── 🚫 Self-deletion prevention
└── 📝 Audit trail for user actions

✅ User Experience:
├── 🔔 Toast notifications for all actions
├── ⏳ Loading states with spinners
├── 🎨 Error styling with red borders
├── 📱 Responsive design for all devices
├── ♿ Accessibility with proper labels
└── 🔄 Real-time form validation
```

**Perfect! Phase 2 Authentication & User Management is now complete with full CRUD operations, Zustand state management, Formik + Yup forms, and comprehensive toast notifications! 🚀**
