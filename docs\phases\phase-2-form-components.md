# 📝 Phase 2: Form Components Implementation

## 🎯 Overview
This document provides detailed implementation for all form components used across the authentication and management pages.

## 🔐 Authentication Forms

### **1. Super Admin Login Form**
**Location**: `components/super-admin/forms/LoginForm.tsx`

```typescript
'use client'

import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuthStore } from '@/stores/super-admin/useAuthStore'
import { useToast } from '@/components/shared/notifications/useToast'
import { useState } from 'react'
import { Eye, EyeOff, Loader2 } from 'lucide-react'

const loginSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required')
})

interface LoginFormProps {
  userType: 'super_admin' | 'platform_staff'
  redirectTo: string
  apiEndpoint: string
}

export function LoginForm({ userType, redirectTo, apiEndpoint }: LoginFormProps) {
  const login = useAuthStore(state => state.login)
  const isLoading = useAuthStore(state => state.isLoading)
  const toast = useToast()
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)

  const handleSubmit = async (values: { email: string; password: string }) => {
    try {
      setError(null)
      await login(values.email, values.password, userType)

      // Show success toast
      toast.loginSuccess()

      // Redirect after short delay to show toast
      setTimeout(() => {
        window.location.href = redirectTo
      }, 1000)
    } catch (error) {
      const errorMessage = (error as Error).message || 'Login failed'
      setError(errorMessage)

      // Show error toast
      toast.loginError(errorMessage)
    }
  }

  return (
    <Formik
      initialValues={{ email: '', password: '' }}
      validationSchema={loginSchema}
      onSubmit={handleSubmit}
    >
      {({ errors, touched, isSubmitting }) => (
        <Form className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Field
              as={Input}
              id="email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
              className={errors.email && touched.email ? 'border-destructive' : ''}
            />
            {errors.email && touched.email && (
              <p className="text-sm text-destructive">{errors.email}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Field
                as={Input}
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="••••••••"
                className={errors.password && touched.password ? 'border-destructive pr-10' : 'pr-10'}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.password && touched.password && (
              <p className="text-sm text-destructive">{errors.password}</p>
            )}
          </div>
          
          <Button 
            type="submit" 
            className="w-full" 
            disabled={isSubmitting || isLoading}
          >
            {isSubmitting || isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Signing in...
              </>
            ) : (
              'Sign In'
            )}
          </Button>
        </Form>
      )}
    </Formik>
  )
}
```

### **2. Institute Registration Form**
**Location**: `components/institute-admin/forms/InstituteRegistrationForm.tsx`

```typescript
'use client'

import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/components/shared/notifications/useToast'
import { useState } from 'react'
import { Loader2 } from 'lucide-react'

const registrationSchema = Yup.object({
  // Institute Information
  instituteName: Yup.string()
    .min(2, 'Institute name must be at least 2 characters')
    .max(100, 'Institute name must be less than 100 characters')
    .required('Institute name is required'),
  instituteSlug: Yup.string()
    .matches(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .min(3, 'Slug must be at least 3 characters')
    .max(50, 'Slug must be less than 50 characters')
    .required('Slug is required'),
  instituteEmail: Yup.string()
    .email('Invalid email address')
    .required('Institute email is required'),
  institutePhone: Yup.string()
    .matches(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/, 'Invalid phone number')
    .required('Phone number is required'),
  instituteAddress: Yup.string()
    .max(500, 'Address must be less than 500 characters')
    .required('Address is required'),
  description: Yup.string()
    .max(1000, 'Description must be less than 1000 characters'),
  
  // Subscription Plan
  subscriptionPlan: Yup.string()
    .required('Subscription plan is required'),
  
  // Admin Account
  adminName: Yup.string()
    .min(2, 'Admin name must be at least 2 characters')
    .required('Admin name is required'),
  adminEmail: Yup.string()
    .email('Invalid email address')
    .required('Admin email is required'),
  adminPassword: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    )
    .required('Admin password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('adminPassword')], 'Passwords must match')
    .required('Please confirm your password'),
  
  // Terms and Conditions
  acceptTerms: Yup.boolean()
    .oneOf([true], 'You must accept the terms and conditions')
})

const subscriptionPlans = [
  { id: 'starter', name: 'Starter Plan', price: '$99 setup + 15% commission', features: ['100 students', 'Basic features'] },
  { id: 'growth', name: 'Growth Plan', price: '$199 setup + 12% commission', features: ['500 students', 'Advanced features'] },
  { id: 'professional', name: 'Professional Plan', price: '$399 setup + 10% commission', features: ['2,000 students', 'Premium features'] },
  { id: 'enterprise', name: 'Enterprise Plan', price: '$799 setup + 8% commission', features: ['Unlimited students', 'All features'] }
]

export function InstituteRegistrationForm() {
  const toast = useToast()
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleSubmit = async (values: any, { setSubmitting }: any) => {
    try {
      setError(null)

      const response = await fetch('/api/institutes/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values)
      })

      if (response.ok) {
        setSuccess(true)

        // Show success toast
        toast.registerSuccess('Your institute registration has been submitted for review.')
      } else {
        const errorData = await response.json()
        const errorMessage = errorData.message || 'Registration failed'
        throw new Error(errorMessage)
      }
    } catch (error) {
      const errorMessage = (error as Error).message || 'Registration failed'
      setError(errorMessage)

      // Show error toast
      toast.registerError(errorMessage)
    } finally {
      setSubmitting(false)
    }
  }

  if (success) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Registration Successful!</h2>
        <p className="text-gray-600 mb-4">
          Your institute registration has been submitted for review. You will receive an email confirmation shortly.
        </p>
        <Button onClick={() => window.location.href = '/auth/login'}>
          Continue to Login
        </Button>
      </div>
    )
  }

  return (
    <Formik
      initialValues={{
        instituteName: '',
        instituteSlug: '',
        instituteEmail: '',
        institutePhone: '',
        instituteAddress: '',
        description: '',
        subscriptionPlan: '',
        adminName: '',
        adminEmail: '',
        adminPassword: '',
        confirmPassword: '',
        acceptTerms: false
      }}
      validationSchema={registrationSchema}
      onSubmit={handleSubmit}
    >
      {({ errors, touched, isSubmitting, values, setFieldValue }) => (
        <Form className="space-y-8">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {/* Institute Information */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Institute Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="instituteName">Institute Name *</Label>
                <Field
                  as={Input}
                  id="instituteName"
                  name="instituteName"
                  placeholder="ABC Learning Academy"
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    setFieldValue('instituteName', e.target.value)
                    if (!values.instituteSlug) {
                      setFieldValue('instituteSlug', generateSlug(e.target.value))
                    }
                  }}
                  className={errors.instituteName && touched.instituteName ? 'border-destructive' : ''}
                />
                {errors.instituteName && touched.instituteName && (
                  <p className="text-sm text-destructive">{errors.instituteName}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="instituteSlug">URL Slug *</Label>
                <Field
                  as={Input}
                  id="instituteSlug"
                  name="instituteSlug"
                  placeholder="abc-learning-academy"
                  className={errors.instituteSlug && touched.instituteSlug ? 'border-destructive' : ''}
                />
                {errors.instituteSlug && touched.instituteSlug && (
                  <p className="text-sm text-destructive">{errors.instituteSlug}</p>
                )}
                <p className="text-xs text-gray-500">
                  Your institute will be available at: {values.instituteSlug}.groups-exam.com
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="instituteEmail">Institute Email *</Label>
                <Field
                  as={Input}
                  id="instituteEmail"
                  name="instituteEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  className={errors.instituteEmail && touched.instituteEmail ? 'border-destructive' : ''}
                />
                {errors.instituteEmail && touched.instituteEmail && (
                  <p className="text-sm text-destructive">{errors.instituteEmail}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="institutePhone">Phone Number *</Label>
                <Field
                  as={Input}
                  id="institutePhone"
                  name="institutePhone"
                  placeholder="+****************"
                  className={errors.institutePhone && touched.institutePhone ? 'border-destructive' : ''}
                />
                {errors.institutePhone && touched.institutePhone && (
                  <p className="text-sm text-destructive">{errors.institutePhone}</p>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="instituteAddress">Address *</Label>
              <Field
                as={Textarea}
                id="instituteAddress"
                name="instituteAddress"
                placeholder="Complete address of your institute"
                rows={3}
                className={errors.instituteAddress && touched.instituteAddress ? 'border-destructive' : ''}
              />
              {errors.instituteAddress && touched.instituteAddress && (
                <p className="text-sm text-destructive">{errors.instituteAddress}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Field
                as={Textarea}
                id="description"
                name="description"
                placeholder="Brief description of your institute and courses offered"
                rows={4}
                className={errors.description && touched.description ? 'border-destructive' : ''}
              />
              {errors.description && touched.description && (
                <p className="text-sm text-destructive">{errors.description}</p>
              )}
            </div>
          </div>
          
          {/* Subscription Plan */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Choose Your Plan</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {subscriptionPlans.map((plan) => (
                <div
                  key={plan.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    values.subscriptionPlan === plan.id
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setFieldValue('subscriptionPlan', plan.id)}
                >
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="subscriptionPlan"
                      value={plan.id}
                      checked={values.subscriptionPlan === plan.id}
                      onChange={() => setFieldValue('subscriptionPlan', plan.id)}
                      className="text-purple-600"
                    />
                    <div>
                      <h4 className="font-semibold text-gray-900">{plan.name}</h4>
                      <p className="text-sm text-gray-600">{plan.price}</p>
                      <ul className="text-xs text-gray-500 mt-1">
                        {plan.features.map((feature, index) => (
                          <li key={index}>• {feature}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {errors.subscriptionPlan && touched.subscriptionPlan && (
              <p className="text-sm text-destructive">{errors.subscriptionPlan}</p>
            )}
          </div>
          
          {/* Admin Account */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Admin Account</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="adminName">Admin Name *</Label>
                <Field
                  as={Input}
                  id="adminName"
                  name="adminName"
                  placeholder="John Doe"
                  className={errors.adminName && touched.adminName ? 'border-destructive' : ''}
                />
                {errors.adminName && touched.adminName && (
                  <p className="text-sm text-destructive">{errors.adminName}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="adminEmail">Admin Email *</Label>
                <Field
                  as={Input}
                  id="adminEmail"
                  name="adminEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  className={errors.adminEmail && touched.adminEmail ? 'border-destructive' : ''}
                />
                {errors.adminEmail && touched.adminEmail && (
                  <p className="text-sm text-destructive">{errors.adminEmail}</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="adminPassword">Password *</Label>
                <Field
                  as={Input}
                  id="adminPassword"
                  name="adminPassword"
                  type="password"
                  placeholder="••••••••"
                  className={errors.adminPassword && touched.adminPassword ? 'border-destructive' : ''}
                />
                {errors.adminPassword && touched.adminPassword && (
                  <p className="text-sm text-destructive">{errors.adminPassword}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password *</Label>
                <Field
                  as={Input}
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  placeholder="••••••••"
                  className={errors.confirmPassword && touched.confirmPassword ? 'border-destructive' : ''}
                />
                {errors.confirmPassword && touched.confirmPassword && (
                  <p className="text-sm text-destructive">{errors.confirmPassword}</p>
                )}
              </div>
            </div>
            
            <p className="text-xs text-gray-500">
              Password must contain at least 8 characters with uppercase, lowercase, and number
            </p>
          </div>
          
          {/* Terms and Conditions */}
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Field
                type="checkbox"
                name="acceptTerms"
                className="mt-1"
              />
              <div className="text-sm">
                <label htmlFor="acceptTerms" className="text-gray-700">
                  I agree to the{' '}
                  <a href="/terms" className="text-purple-600 hover:underline" target="_blank">
                    Terms of Service
                  </a>{' '}
                  and{' '}
                  <a href="/privacy" className="text-purple-600 hover:underline" target="_blank">
                    Privacy Policy
                  </a>
                </label>
              </div>
            </div>
            {errors.acceptTerms && touched.acceptTerms && (
              <p className="text-sm text-destructive">{errors.acceptTerms}</p>
            )}
          </div>
          
          {/* Submit Button */}
          <div className="pt-6">
            <Button 
              type="submit" 
              className="w-full" 
              disabled={isSubmitting}
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Institute...
                </>
              ) : (
                'Create Institute'
              )}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  )
}
```

### **3. Student Registration Form**
**Location**: `components/student/forms/StudentRegistrationForm.tsx`

```typescript
'use client'

import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/components/shared/notifications/useToast'
import { useState } from 'react'
import { Loader2, Eye, EyeOff } from 'lucide-react'

const studentRegistrationSchema = Yup.object({
  firstName: Yup.string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .required('First name is required'),
  lastName: Yup.string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .required('Last name is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  phone: Yup.string()
    .matches(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/, 'Invalid phone number')
    .required('Phone number is required'),
  dateOfBirth: Yup.date()
    .max(new Date(), 'Date of birth cannot be in the future')
    .required('Date of birth is required'),
  gender: Yup.string()
    .oneOf(['male', 'female', 'other'], 'Please select a valid gender')
    .required('Gender is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    )
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
  acceptTerms: Yup.boolean()
    .oneOf([true], 'You must accept the terms and conditions'),
  marketingEmails: Yup.boolean()
})

export function StudentRegistrationForm() {
  const toast = useToast()
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const handleSubmit = async (values: any, { setSubmitting }: any) => {
    try {
      setError(null)

      const response = await fetch('/api/students/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values)
      })

      if (response.ok) {
        setSuccess(true)

        // Show success toast
        toast.registerSuccess('Account created successfully! Please check your email to verify your account.')

        // Show email verification toast
        setTimeout(() => {
          toast.emailVerificationSent()
        }, 2000)
      } else {
        const errorData = await response.json()
        const errorMessage = errorData.message || 'Registration failed'
        throw new Error(errorMessage)
      }
    } catch (error) {
      const errorMessage = (error as Error).message || 'Registration failed'
      setError(errorMessage)

      // Show error toast
      toast.registerError(errorMessage)
    } finally {
      setSubmitting(false)
    }
  }

  if (success) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Registration Successful!</h2>
        <p className="text-gray-600 mb-4">
          Please check your email and click the verification link to activate your account.
        </p>
        <Button onClick={() => window.location.href = '/auth/user-login'}>
          Continue to Login
        </Button>
      </div>
    )
  }

  return (
    <Formik
      initialValues={{
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        dateOfBirth: '',
        gender: '',
        password: '',
        confirmPassword: '',
        acceptTerms: false,
        marketingEmails: false
      }}
      validationSchema={studentRegistrationSchema}
      onSubmit={handleSubmit}
    >
      {({ errors, touched, isSubmitting, values, setFieldValue }) => (
        <Form className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Personal Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name *</Label>
                <Field
                  as={Input}
                  id="firstName"
                  name="firstName"
                  placeholder="John"
                  className={errors.firstName && touched.firstName ? 'border-destructive' : ''}
                />
                {errors.firstName && touched.firstName && (
                  <p className="text-sm text-destructive">{errors.firstName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name *</Label>
                <Field
                  as={Input}
                  id="lastName"
                  name="lastName"
                  placeholder="Doe"
                  className={errors.lastName && touched.lastName ? 'border-destructive' : ''}
                />
                {errors.lastName && touched.lastName && (
                  <p className="text-sm text-destructive">{errors.lastName}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <Field
                as={Input}
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                className={errors.email && touched.email ? 'border-destructive' : ''}
              />
              {errors.email && touched.email && (
                <p className="text-sm text-destructive">{errors.email}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number *</Label>
                <Field
                  as={Input}
                  id="phone"
                  name="phone"
                  placeholder="+****************"
                  className={errors.phone && touched.phone ? 'border-destructive' : ''}
                />
                {errors.phone && touched.phone && (
                  <p className="text-sm text-destructive">{errors.phone}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="dateOfBirth">Date of Birth *</Label>
                <Field
                  as={Input}
                  id="dateOfBirth"
                  name="dateOfBirth"
                  type="date"
                  className={errors.dateOfBirth && touched.dateOfBirth ? 'border-destructive' : ''}
                />
                {errors.dateOfBirth && touched.dateOfBirth && (
                  <p className="text-sm text-destructive">{errors.dateOfBirth}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="gender">Gender *</Label>
              <Field name="gender">
                {({ field }: any) => (
                  <Select onValueChange={(value) => setFieldValue('gender', value)}>
                    <SelectTrigger className={errors.gender && touched.gender ? 'border-destructive' : ''}>
                      <SelectValue placeholder="Select your gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </Field>
              {errors.gender && touched.gender && (
                <p className="text-sm text-destructive">{errors.gender}</p>
              )}
            </div>
          </div>

          {/* Account Security */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Account Security</h3>

            <div className="space-y-2">
              <Label htmlFor="password">Password *</Label>
              <div className="relative">
                <Field
                  as={Input}
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="••••••••"
                  className={errors.password && touched.password ? 'border-destructive pr-10' : 'pr-10'}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.password && touched.password && (
                <p className="text-sm text-destructive">{errors.password}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password *</Label>
              <div className="relative">
                <Field
                  as={Input}
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="••••••••"
                  className={errors.confirmPassword && touched.confirmPassword ? 'border-destructive pr-10' : 'pr-10'}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.confirmPassword && touched.confirmPassword && (
                <p className="text-sm text-destructive">{errors.confirmPassword}</p>
              )}
            </div>

            <p className="text-xs text-gray-500">
              Password must contain at least 8 characters with uppercase, lowercase, and number
            </p>
          </div>

          {/* Terms and Preferences */}
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Field
                type="checkbox"
                name="acceptTerms"
                className="mt-1"
              />
              <div className="text-sm">
                <label htmlFor="acceptTerms" className="text-gray-700">
                  I agree to the{' '}
                  <a href="/terms" className="text-emerald-600 hover:underline" target="_blank">
                    Terms of Service
                  </a>{' '}
                  and{' '}
                  <a href="/privacy" className="text-emerald-600 hover:underline" target="_blank">
                    Privacy Policy
                  </a>
                </label>
              </div>
            </div>
            {errors.acceptTerms && touched.acceptTerms && (
              <p className="text-sm text-destructive">{errors.acceptTerms}</p>
            )}

            <div className="flex items-start space-x-3">
              <Field
                type="checkbox"
                name="marketingEmails"
                className="mt-1"
              />
              <div className="text-sm">
                <label htmlFor="marketingEmails" className="text-gray-700">
                  I would like to receive course updates and promotional emails
                </label>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="pt-4">
            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting}
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                'Create Account'
              )}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  )
}
```
