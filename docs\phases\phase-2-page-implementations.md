# 📄 Phase 2: Page Implementation Details

## 🎯 Overview
This document provides detailed implementation specifications for all pages across the three panels: Super Admin, Institute Admin, and Student Portal.

## 🔐 Authentication Pages

### **1. Super Admin Login Page**
**Route**: `apps/super-admin/src/app/auth/admin/login/page.tsx`

```typescript
'use client'

import { LoginForm } from '@/components/super-admin/forms/LoginForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function SuperAdminLoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Groups Exam LMS</h1>
          <p className="text-gray-600 mt-2">Super Admin Portal</p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Admin Login</CardTitle>
          </CardHeader>
          <CardContent>
            <LoginForm 
              userType="super_admin"
              redirectTo="/dashboard"
              apiEndpoint="/api/auth/admin/login"
            />
          </CardContent>
        </Card>
        
        <div className="text-center mt-6 text-sm text-gray-500">
          <p>Authorized personnel only</p>
        </div>
      </div>
    </div>
  )
}
```

### **2. Institute Registration Page**
**Route**: `apps/institute-admin/src/app/auth/register/page.tsx`

```typescript
'use client'

import { InstituteRegistrationForm } from '@/components/institute-admin/forms/InstituteRegistrationForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function InstituteRegistrationPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100 py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900">Join Groups Exam LMS</h1>
          <p className="text-gray-600 mt-2">Start your educational institute's digital journey</p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Institute Registration</CardTitle>
            </CardHeader>
            <CardContent>
              <InstituteRegistrationForm />
            </CardContent>
          </Card>
        </div>
        
        <div className="text-center mt-8">
          <p className="text-sm text-gray-600">
            Already have an account? 
            <a href="/auth/login" className="text-purple-600 hover:underline ml-1">
              Sign in here
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
```

### **3. Student Registration Page**
**Route**: `apps/student/src/app/auth/user-register/page.tsx`

```typescript
'use client'

import { StudentRegistrationForm } from '@/components/student/forms/StudentRegistrationForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function StudentRegistrationPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-teal-100 py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Create Your Account</h1>
          <p className="text-gray-600 mt-2">Join thousands of students learning online</p>
        </div>
        
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Student Registration</CardTitle>
            </CardHeader>
            <CardContent>
              <StudentRegistrationForm />
            </CardContent>
          </Card>
        </div>
        
        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            Already have an account? 
            <a href="/auth/user-login" className="text-emerald-600 hover:underline ml-1">
              Sign in here
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
```

## 📊 Dashboard Pages

### **1. Super Admin Dashboard**
**Route**: `apps/super-admin/src/app/dashboard/page.tsx`

```typescript
'use client'

import { SuperAdminLayout } from '@/components/super-admin/SuperAdminLayout'
import { StatsCards } from '@/components/super-admin/dashboard/StatsCards'
import { RevenueChart } from '@/components/super-admin/dashboard/RevenueChart'
import { RecentInstitutes } from '@/components/super-admin/dashboard/RecentInstitutes'
import { SystemHealth } from '@/components/super-admin/dashboard/SystemHealth'

export default function SuperAdminDashboard() {
  return (
    <SuperAdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Platform Overview</h1>
          <p className="text-gray-600">Monitor your LMS platform performance</p>
        </div>
        
        {/* Key Metrics */}
        <StatsCards />
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue Chart */}
          <RevenueChart />
          
          {/* System Health */}
          <SystemHealth />
        </div>
        
        {/* Recent Activity */}
        <RecentInstitutes />
      </div>
    </SuperAdminLayout>
  )
}
```

### **2. Institute Admin Dashboard**
**Route**: `apps/institute-admin/src/app/dashboard/page.tsx`

```typescript
'use client'

import { InstituteAdminLayout } from '@/components/institute-admin/InstituteAdminLayout'
import { InstituteStatsCards } from '@/components/institute-admin/dashboard/InstituteStatsCards'
import { EnrollmentChart } from '@/components/institute-admin/dashboard/EnrollmentChart'
import { RecentCourses } from '@/components/institute-admin/dashboard/RecentCourses'
import { QuickActions } from '@/components/institute-admin/dashboard/QuickActions'

export default function InstituteAdminDashboard() {
  return (
    <InstituteAdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Institute Dashboard</h1>
            <p className="text-gray-600">Manage your educational institute</p>
          </div>
          <QuickActions />
        </div>
        
        {/* Institute Metrics */}
        <InstituteStatsCards />
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Enrollment Trends */}
          <div className="lg:col-span-2">
            <EnrollmentChart />
          </div>
          
          {/* Recent Courses */}
          <div>
            <RecentCourses />
          </div>
        </div>
      </div>
    </InstituteAdminLayout>
  )
}
```

### **3. Student Dashboard**
**Route**: `apps/student/src/app/dashboard/page.tsx`

```typescript
'use client'

import { StudentLayout } from '@/components/student/StudentLayout'
import { StudentStatsCards } from '@/components/student/dashboard/StudentStatsCards'
import { ProgressChart } from '@/components/student/dashboard/ProgressChart'
import { EnrolledCourses } from '@/components/student/dashboard/EnrolledCourses'
import { UpcomingExams } from '@/components/student/dashboard/UpcomingExams'
import { Achievements } from '@/components/student/dashboard/Achievements'

export default function StudentDashboard() {
  return (
    <StudentLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Learning Dashboard</h1>
          <p className="text-gray-600">Track your progress and continue learning</p>
        </div>
        
        {/* Learning Stats */}
        <StudentStatsCards />
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Progress Overview */}
          <div className="lg:col-span-2">
            <ProgressChart />
          </div>
          
          {/* Achievements */}
          <div>
            <Achievements />
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Enrolled Courses */}
          <EnrolledCourses />
          
          {/* Upcoming Exams */}
          <UpcomingExams />
        </div>
      </div>
    </StudentLayout>
  )
}
```

## 📋 Management Pages

### **1. Institute Management (Super Admin)**
**Route**: `apps/super-admin/src/app/institutes/page.tsx`

```typescript
'use client'

import { SuperAdminLayout } from '@/components/super-admin/SuperAdminLayout'
import { InstituteTable } from '@/components/super-admin/institutes/InstituteTable'
import { InstituteFilters } from '@/components/super-admin/institutes/InstituteFilters'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import Link from 'next/link'

export default function InstitutesPage() {
  return (
    <SuperAdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Institute Management</h1>
            <p className="text-gray-600">Manage all educational institutes</p>
          </div>
          <Link href="/institutes/create">
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Institute
            </Button>
          </Link>
        </div>
        
        {/* Filters */}
        <InstituteFilters />
        
        {/* Institute Table */}
        <InstituteTable />
      </div>
    </SuperAdminLayout>
  )
}
```

### **2. Course Management (Institute Admin)**
**Route**: `apps/institute-admin/src/app/courses/page.tsx`

```typescript
'use client'

import { InstituteAdminLayout } from '@/components/institute-admin/InstituteAdminLayout'
import { CourseTable } from '@/components/institute-admin/courses/CourseTable'
import { CourseFilters } from '@/components/institute-admin/courses/CourseFilters'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import Link from 'next/link'

export default function CoursesPage() {
  return (
    <InstituteAdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Course Management</h1>
            <p className="text-gray-600">Create and manage your courses</p>
          </div>
          <Link href="/courses/create">
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Course
            </Button>
          </Link>
        </div>
        
        {/* Course Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border">
            <h3 className="text-sm font-medium text-gray-500">Total Courses</h3>
            <p className="text-2xl font-bold text-gray-900">24</p>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <h3 className="text-sm font-medium text-gray-500">Published</h3>
            <p className="text-2xl font-bold text-green-600">18</p>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <h3 className="text-sm font-medium text-gray-500">Draft</h3>
            <p className="text-2xl font-bold text-yellow-600">4</p>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <h3 className="text-sm font-medium text-gray-500">Archived</h3>
            <p className="text-2xl font-bold text-gray-600">2</p>
          </div>
        </div>
        
        {/* Filters */}
        <CourseFilters />
        
        {/* Course Table */}
        <CourseTable />
      </div>
    </InstituteAdminLayout>
  )
}
```

### **3. Course Browsing (Student)**
**Route**: `apps/student/src/app/courses/page.tsx`

```typescript
'use client'

import { StudentLayout } from '@/components/student/StudentLayout'
import { CourseGrid } from '@/components/student/courses/CourseGrid'
import { CourseFilters } from '@/components/student/courses/CourseFilters'
import { SearchBar } from '@/components/student/courses/SearchBar'

export default function CoursesPage() {
  return (
    <StudentLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Available Courses</h1>
          <p className="text-gray-600">Discover and enroll in courses</p>
        </div>
        
        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <SearchBar />
          </div>
          <CourseFilters />
        </div>
        
        {/* Course Grid */}
        <CourseGrid />
      </div>
    </StudentLayout>
  )
}
```
