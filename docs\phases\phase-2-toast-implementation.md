# 🔔 Phase 2: Toast Notification Implementation Guide

## 🎯 Overview
This document provides comprehensive implementation details for toast notifications across all authentication and CRUD operations in the Groups Exam LMS platform.

## 🏗️ Toast System Architecture

### **Toast Types and Use Cases**
```typescript
export type ToastType = 'success' | 'error' | 'warning' | 'info'

// Authentication Scenarios
- success: Login success, registration success, email verified
- error: Login failed, registration failed, validation errors
- warning: Session expiring, password strength warnings
- info: Email verification sent, password reset instructions sent
```

### **Toast Component Structure**
```
components/shared/notifications/
├── ToastProvider.tsx          # Context provider for toast management
├── Toast.tsx                  # Individual toast component
├── useToast.ts               # Hook with pre-configured toast functions
└── types.ts                  # TypeScript interfaces
```

## 🔧 Implementation Details

### **1. App-Level Toast Provider Setup**

#### **Super Admin App Layout**
```typescript
// apps/super-admin/src/app/layout.tsx
import { ToastProvider } from '@/components/shared/notifications/ToastProvider'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <ToastProvider>
          {children}
        </ToastProvider>
      </body>
    </html>
  )
}
```

#### **Institute Admin App Layout**
```typescript
// apps/institute-admin/src/app/layout.tsx
import { ToastProvider } from '@/components/shared/notifications/ToastProvider'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <ToastProvider>
          {children}
        </ToastProvider>
      </body>
    </html>
  )
}
```

#### **Student Portal App Layout**
```typescript
// apps/student/src/app/layout.tsx
import { ToastProvider } from '@/components/shared/notifications/ToastProvider'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <ToastProvider>
          {children}
        </ToastProvider>
      </body>
    </html>
  )
}
```

### **2. Authentication Toast Integration**

#### **Login Form with Toast Notifications**
```typescript
// Example: components/super-admin/forms/LoginForm.tsx
import { useToast } from '@/components/shared/notifications/useToast'

export function LoginForm() {
  const toast = useToast()
  const login = useAuthStore(state => state.login)

  const handleSubmit = async (values: LoginFormValues) => {
    try {
      await login(values.email, values.password)
      
      // Success toast
      toast.loginSuccess('Welcome back to the admin panel!')
      
      // Redirect after showing toast
      setTimeout(() => {
        router.push('/dashboard')
      }, 1500)
    } catch (error) {
      // Error toast with specific message
      toast.loginError(error.message || 'Invalid credentials')
    }
  }

  // ... rest of component
}
```

#### **Registration Form with Toast Notifications**
```typescript
// Example: components/institute-admin/forms/InstituteRegistrationForm.tsx
import { useToast } from '@/components/shared/notifications/useToast'

export function InstituteRegistrationForm() {
  const toast = useToast()

  const handleSubmit = async (values: RegistrationFormValues) => {
    try {
      const response = await fetch('/api/institutes/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values)
      })

      if (response.ok) {
        // Success toast
        toast.registerSuccess('Institute registration submitted successfully!')
        
        // Additional info toast
        setTimeout(() => {
          toast.info('Review Process', 'Your registration is under review. You will receive an email confirmation within 24 hours.')
        }, 2000)
        
        // Redirect to login
        setTimeout(() => {
          router.push('/auth/login')
        }, 4000)
      } else {
        const errorData = await response.json()
        throw new Error(errorData.message)
      }
    } catch (error) {
      // Error toast
      toast.registerError(error.message || 'Registration failed')
    }
  }

  // ... rest of component
}
```

### **3. CRUD Operations Toast Integration**

#### **Institute Management (Super Admin)**
```typescript
// Example: components/super-admin/institutes/InstituteActions.tsx
import { useToast } from '@/components/shared/notifications/useToast'

export function InstituteActions({ institute }: { institute: Institute }) {
  const toast = useToast()

  const handleApprove = async () => {
    try {
      await fetch(`/api/institutes/${institute.id}/approve`, {
        method: 'PATCH'
      })
      
      toast.success('Institute Approved', `${institute.name} has been approved and activated.`)
    } catch (error) {
      toast.error('Approval Failed', 'Unable to approve institute. Please try again.')
    }
  }

  const handleReject = async () => {
    try {
      await fetch(`/api/institutes/${institute.id}/reject`, {
        method: 'PATCH'
      })
      
      toast.warning('Institute Rejected', `${institute.name} registration has been rejected.`)
    } catch (error) {
      toast.error('Rejection Failed', 'Unable to reject institute. Please try again.')
    }
  }

  const handleDelete = async () => {
    try {
      await fetch(`/api/institutes/${institute.id}`, {
        method: 'DELETE'
      })
      
      toast.deleteSuccess('Institute')
    } catch (error) {
      toast.error('Delete Failed', 'Unable to delete institute. Please try again.')
    }
  }

  // ... rest of component
}
```

#### **Course Management (Institute Admin)**
```typescript
// Example: components/institute-admin/courses/CourseForm.tsx
import { useToast } from '@/components/shared/notifications/useToast'

export function CourseForm({ course, onSuccess }: CourseFormProps) {
  const toast = useToast()

  const handleSubmit = async (values: CourseFormValues) => {
    try {
      const isEditing = !!course?.id
      const url = isEditing ? `/api/courses/${course.id}` : '/api/courses'
      const method = isEditing ? 'PATCH' : 'POST'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values)
      })

      if (response.ok) {
        if (isEditing) {
          toast.updateSuccess('Course')
        } else {
          toast.createSuccess('Course')
          
          // Additional info for new course
          setTimeout(() => {
            toast.info('Next Steps', 'You can now add lessons and content to your course.')
          }, 2000)
        }
        
        onSuccess?.()
      } else {
        throw new Error('Save failed')
      }
    } catch (error) {
      toast.saveError('course')
    }
  }

  // ... rest of component
}
```

### **4. Network and Error Handling**

#### **API Client with Toast Integration**
```typescript
// lib/api-client.ts
import { useToast } from '@/components/shared/notifications/useToast'

class ApiClient {
  private toast = useToast()

  async request(url: string, options: RequestInit = {}) {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          this.toast.error('Session Expired', 'Please log in again.')
          // Redirect to login
          window.location.href = '/auth/login'
          return
        }

        if (response.status === 403) {
          this.toast.error('Access Denied', 'You do not have permission to perform this action.')
          return
        }

        if (response.status >= 500) {
          this.toast.serverError()
          return
        }

        // Other client errors
        const errorData = await response.json()
        throw new Error(errorData.message || 'Request failed')
      }

      return response.json()
    } catch (error) {
      if (error instanceof TypeError) {
        // Network error
        this.toast.networkError()
      } else {
        // Other errors
        this.toast.error('Request Failed', error.message)
      }
      throw error
    }
  }
}

export const apiClient = new ApiClient()
```

### **5. Email Verification Flow**

#### **Email Verification Component**
```typescript
// components/student/auth/EmailVerification.tsx
import { useToast } from '@/components/shared/notifications/useToast'

export function EmailVerification() {
  const toast = useToast()
  const [isVerifying, setIsVerifying] = useState(false)

  const handleResendVerification = async () => {
    try {
      await fetch('/api/auth/resend-verification', {
        method: 'POST'
      })
      
      toast.emailVerificationSent()
    } catch (error) {
      toast.error('Resend Failed', 'Unable to resend verification email.')
    }
  }

  const verifyEmail = async (token: string) => {
    setIsVerifying(true)
    try {
      const response = await fetch(`/api/auth/verify-email?token=${token}`, {
        method: 'POST'
      })

      if (response.ok) {
        toast.emailVerified()
        
        setTimeout(() => {
          toast.success('Account Activated', 'You can now access all features.')
          router.push('/dashboard')
        }, 2000)
      } else {
        throw new Error('Verification failed')
      }
    } catch (error) {
      toast.error('Verification Failed', 'Invalid or expired verification link.')
    } finally {
      setIsVerifying(false)
    }
  }

  // ... rest of component
}
```

### **6. Password Reset Flow**

#### **Password Reset Component**
```typescript
// components/shared/auth/PasswordReset.tsx
import { useToast } from '@/components/shared/notifications/useToast'

export function PasswordResetForm() {
  const toast = useToast()

  const handleRequestReset = async (email: string) => {
    try {
      await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      })
      
      toast.passwordResetSent()
    } catch (error) {
      toast.error('Reset Failed', 'Unable to send password reset email.')
    }
  }

  const handleResetPassword = async (token: string, newPassword: string) => {
    try {
      await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token, password: newPassword })
      })
      
      toast.passwordResetSuccess()
      
      setTimeout(() => {
        router.push('/auth/login')
      }, 2000)
    } catch (error) {
      toast.error('Reset Failed', 'Invalid or expired reset link.')
    }
  }

  // ... rest of component
}
```

## 🎨 Toast Styling and Positioning

### **Toast Container Positioning**
```css
/* Toast container is positioned in ToastProvider */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  max-width: 24rem;
  width: 100%;
}

@media (max-width: 640px) {
  .toast-container {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
    max-width: none;
  }
}
```

### **Toast Animation Classes**
```css
/* Toast animations */
.toast-enter {
  transform: translateX(100%);
  opacity: 0;
}

.toast-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 300ms ease-in-out;
}

.toast-exit {
  transform: translateX(0);
  opacity: 1;
}

.toast-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: all 300ms ease-in-out;
}
```

## 📱 Mobile Responsiveness

### **Mobile Toast Adaptations**
- Full-width toasts on mobile devices
- Adjusted positioning for mobile screens
- Touch-friendly close buttons
- Reduced animation duration for better performance

## 🧪 Testing Toast Notifications

### **Unit Tests for Toast Components**
```typescript
// __tests__/components/Toast.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Toast } from '@/components/shared/notifications/Toast'

describe('Toast Component', () => {
  it('renders success toast correctly', () => {
    const onClose = jest.fn()
    render(
      <Toast
        id="test"
        type="success"
        title="Success"
        description="Operation completed"
        onClose={onClose}
      />
    )

    expect(screen.getByText('Success')).toBeInTheDocument()
    expect(screen.getByText('Operation completed')).toBeInTheDocument()
  })

  it('calls onClose when close button is clicked', () => {
    const onClose = jest.fn()
    render(
      <Toast
        id="test"
        type="success"
        title="Success"
        onClose={onClose}
      />
    )

    fireEvent.click(screen.getByRole('button'))
    expect(onClose).toHaveBeenCalled()
  })
})
```

## 🎯 Best Practices

### **Toast Usage Guidelines**
1. **Use appropriate toast types** for different scenarios
2. **Keep messages concise** and actionable
3. **Provide specific error messages** when possible
4. **Use consistent timing** for auto-dismiss
5. **Avoid toast spam** - limit concurrent toasts
6. **Test on mobile devices** for proper positioning
7. **Include accessibility features** (ARIA labels, keyboard navigation)

### **Performance Considerations**
- Limit maximum number of concurrent toasts (max 3-5)
- Use efficient animations (transform/opacity only)
- Clean up toast timers properly
- Avoid memory leaks in toast provider

This comprehensive toast implementation provides a consistent, user-friendly notification system across all panels of the Groups Exam LMS platform! 🎉
