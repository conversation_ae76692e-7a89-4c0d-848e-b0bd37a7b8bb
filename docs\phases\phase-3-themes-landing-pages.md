# 🎨 Phase 3: Themes & Landing Pages Development

## 📋 Overview
Phase 3 focuses on developing the dual theme system and landing pages for the Groups Exam LMS platform, including platform themes for the main SaaS website and institute themes with Amazon-style course marketplace functionality.

## 🌐 Landing Page Structure

### **Platform Landing Page (groups-exam.com)**
- **Route**: `/` (Root page)
- **Purpose**: Marketing and onboarding for institutes
- **Theme**: SaaS Modern theme
- **Features**: Hero section, features showcase, pricing, CTA buttons
- **Target Audience**: Institute owners and decision makers

### **Institute Landing Pages (abc-institute.com)**
- **Route**: `/` (Root page on institute domain)
- **Purpose**: Course marketplace for students
- **Theme**: Education Modern theme
- **Features**: Course catalog, search, filters, shopping cart
- **Target Audience**: Students and course buyers

### **User Dashboards**
- **Student Dashboard**: `/student/dashboard` (after login)
- **Institute Admin**: `/admin` (after login)
- **Super Admin**: `/super-admin` (after login)

### 🎯 Objectives
- ✅ Develop default platform landing page theme
- ✅ Create institute landing page themes with course marketplace
- ✅ Implement Amazon-style course browsing and filtering
- ✅ Build theme management system for Super Admin
- ✅ Create theme selection interface for Institute Admin

### ⏱️ Timeline
**Duration**: 4 weeks (20 working days)
**Team Size**: 3-4 developers

## 🏗️ Dual Theme System Architecture

### **Theme Separation Overview**
```
Platform Level (groups-exam.com):
├── 🎨 Platform Themes (Super Admin manages)
│   ├── SaaS product marketing pages
│   ├── Institute registration & pricing
│   ├── Platform features & documentation
│   └── Super Admin selects theme for main platform

Institute Level (abc-institute.com):
├── 🎨 Institute Themes (Institute Admin selects)
│   ├── Institute marketing pages (themed)
│   ├── About, Contact, Blog pages (themed)
│   └── Course marketplace (standard across all)
```

### **Theme Storage Structure**
```
public/themes/
├── platform-themes/                    # For groups-exam.com
│   ├── saas-modern/
│   │   ├── theme.json                  # Theme metadata
│   │   ├── preview.jpg                 # 400x300px thumbnail
│   │   ├── demo.jpg                    # 1200x800px full preview
│   │   ├── components/
│   │   │   ├── PlatformHeader.tsx
│   │   │   ├── Hero.tsx
│   │   │   ├── Features.tsx
│   │   │   ├── Pricing.tsx
│   │   │   ├── Testimonials.tsx
│   │   │   └── Footer.tsx
│   │   └── styles/
│   │       ├── globals.css
│   │       └── components.css
│   ├── saas-corporate/
│   └── saas-startup/
│
├── institute-themes/                   # For abc-institute.com
│   ├── education-modern/
│   │   ├── theme.json                  # Theme metadata
│   │   ├── preview.jpg                 # 400x300px thumbnail
│   │   ├── demo.jpg                    # 1200x800px full preview
│   │   ├── components/
│   │   │   ├── InstituteHeader.tsx
│   │   │   ├── Hero.tsx
│   │   │   ├── FeaturedCourses.tsx
│   │   │   ├── About.tsx
│   │   │   ├── Testimonials.tsx
│   │   │   ├── Contact.tsx
│   │   │   └── Footer.tsx
│   │   └── styles/
│   │       ├── globals.css
│   │       └── components.css
│   ├── coaching-professional/
│   └── university-classic/
│
└── platform-core/                     # Standard across all institutes
    ├── course-marketplace/
    │   ├── CourseGrid.tsx
    │   ├── CourseCard.tsx
    │   ├── CourseFilters.tsx
    │   ├── SearchBar.tsx
    │   ├── ShoppingCart.tsx
    │   └── Checkout.tsx
    └── student-portal/
        ├── Dashboard.tsx
        ├── CoursePlayer.tsx
        └── Progress.tsx
```

## 🌟 Phase 3 Development Plan

### **Week 1: Platform Theme Development**
**Focus**: Default platform landing page for groups-exam.com

#### **Day 1-2: Platform Theme Foundation**
- ✅ Create default "SaaS Modern" platform theme
- ✅ Implement platform theme structure
- ✅ Build reusable platform components

#### **Day 3-4: Platform Landing Page Components**
- ✅ Platform Header with navigation
- ✅ Hero section with SaaS messaging
- ✅ Features showcase section
- ✅ Pricing table with subscription plans

#### **Day 5: Platform Theme Integration**
- ✅ Theme selection system for Super Admin
- ✅ Platform theme preview functionality
- ✅ Theme switching mechanism

### **Week 2: Institute Theme Development**
**Focus**: Default institute theme with basic structure

#### **Day 6-7: Institute Theme Foundation**
- ✅ Create default "Education Modern" institute theme
- ✅ Implement institute theme structure
- ✅ Build reusable institute components

#### **Day 8-9: Institute Landing Page Components**
- ✅ Institute Header with branding
- ✅ Hero section with institute messaging
- ✅ Featured courses section
- ✅ About and testimonials sections

#### **Day 10: Institute Theme Integration**
- ✅ Theme gallery for Institute Admin
- ✅ Theme selection and preview
- ✅ Basic customization options

### **Week 3: Course Marketplace Development**
**Focus**: Amazon-style course browsing and filtering

#### **Day 11-12: Course Marketplace Foundation**
- ✅ Course grid layout with responsive design
- ✅ Course card component with ratings and pricing
- ✅ Search functionality with autocomplete

#### **Day 13-14: Advanced Filtering System**
- ✅ Category-based filtering
- ✅ Price range filtering
- ✅ Rating and difficulty filters
- ✅ Sort options (popularity, price, rating, newest)

#### **Day 15: Shopping Cart & Purchase Flow**
- ✅ Add to cart functionality
- ✅ Shopping cart component
- ✅ Basic checkout process
- ✅ Course enrollment after purchase

### **Week 4: Theme Management & Polish**
**Focus**: Theme management system and final integration

#### **Day 16-17: Theme Management System**
- ✅ Super Admin theme management interface
- ✅ Theme upload and preview system
- ✅ Theme activation and deactivation

#### **Day 18-19: Institute Theme Customization**
- ✅ Color and branding customization
- ✅ Content management for themed pages
- ✅ Logo and image upload functionality

#### **Day 20: Testing & Optimization**
- ✅ Cross-browser testing
- ✅ Mobile responsiveness testing
- ✅ Performance optimization
- ✅ Final integration testing

## 🎨 Default Theme Specifications

### **Platform Theme: "SaaS Modern"**
```typescript
// public/themes/platform-themes/saas-modern/theme.json
{
  "name": "SaaS Modern",
  "slug": "saas-modern",
  "version": "1.0.0",
  "description": "Modern SaaS platform theme with clean design",
  "category": "saas",
  "preview_image": "/themes/platform-themes/saas-modern/preview.jpg",
  "demo_image": "/themes/platform-themes/saas-modern/demo.jpg",
  "colors": {
    "primary": "#3b82f6",
    "secondary": "#64748b",
    "accent": "#10b981",
    "background": "#ffffff",
    "text": "#1f2937"
  },
  "fonts": {
    "heading": "Inter",
    "body": "Inter"
  },
  "features": [
    "responsive_design",
    "seo_optimized",
    "fast_loading",
    "modern_ui"
  ],
  "pages": [
    "homepage",
    "features",
    "pricing",
    "about",
    "contact"
  ]
}
```

### **Institute Theme: "Education Modern"**
```typescript
// public/themes/institute-themes/education-modern/theme.json
{
  "name": "Education Modern",
  "slug": "education-modern",
  "version": "1.0.0",
  "description": "Modern education theme with course marketplace",
  "category": "education",
  "preview_image": "/themes/institute-themes/education-modern/preview.jpg",
  "demo_image": "/themes/institute-themes/education-modern/demo.jpg",
  "colors": {
    "primary": "#059669",
    "secondary": "#6b7280",
    "accent": "#f59e0b",
    "background": "#ffffff",
    "text": "#1f2937"
  },
  "fonts": {
    "heading": "Poppins",
    "body": "Inter"
  },
  "features": [
    "course_marketplace",
    "responsive_design",
    "seo_optimized",
    "customizable_branding"
  ],
  "suitable_for": [
    "coaching_centers",
    "online_academies",
    "skill_training"
  ],
  "pages": [
    "homepage",
    "about",
    "courses",
    "contact",
    "blog"
  ]
}
```

## 🛒 Amazon-Style Course Marketplace

### **Course Marketplace Layout**
```
Course Marketplace (Standard across all institutes):
┌─────────────────────────────────────────────────────────────┐
│ Search: [Find courses...] [🔍] Categories: [All ▼] Sort: [Popular ▼] │
├─────────────────────────────────────────────────────────────┤
│ Filters          │ Course Grid                              │
│ ┌─────────────┐  │ ┌──────┐ ┌──────┐ ┌──────┐ ┌──────┐    │
│ │ Category    │  │ │Course│ │Course│ │Course│ │Course│    │
│ │ ☑ UPSC      │  │ │ Card │ │ Card │ │ Card │ │ Card │    │
│ │ ☐ Banking   │  │ │ ⭐4.5 │ │ ⭐4.8 │ │ ⭐4.2 │ │ ⭐4.9 │    │
│ │ ☐ SSC       │  │ │ ₹999 │ │ ₹1499│ │ ₹799 │ │ ₹1999│    │
│ │             │  │ └──────┘ └──────┘ └──────┘ └──────┘    │
│ │ Price Range │  │                                         │
│ │ ₹0 ━━●━━ ₹5k │  │ ┌──────┐ ┌──────┐ ┌──────┐ ┌──────┐    │
│ │             │  │ │Course│ │Course│ │Course│ │Course│    │
│ │ Rating      │  │ │ Card │ │ Card │ │ Card │ │ Card │    │
│ │ ⭐⭐⭐⭐⭐ & up │  │ │ ⭐4.3 │ │ ⭐4.7 │ │ ⭐4.1 │ │ ⭐4.6 │    │
│ │ ⭐⭐⭐⭐ & up   │  │ │ ₹1299│ │ ₹899 │ │ ₹1799│ │ ₹699 │    │
│ │             │  │ └──────┘ └──────┘ └──────┘ └──────┘    │
│ │ Duration    │  │                                         │
│ │ ☐ 0-2 hours │  │ [Load More Courses]                     │
│ │ ☐ 2-5 hours │  │                                         │
│ │ ☐ 5+ hours  │  │                                         │
│ └─────────────┘  │                                         │
└─────────────────────────────────────────────────────────────┘
```

### **Course Card Component Features**
- ✅ Course thumbnail image
- ✅ Course title and description
- ✅ Instructor name and profile
- ✅ Star rating and review count
- ✅ Price with discount indicators
- ✅ Course duration and lesson count
- ✅ Difficulty level badge
- ✅ "Add to Cart" and "Buy Now" buttons
- ✅ Wishlist functionality
- ✅ Course preview option

### **Advanced Filtering Options**
- ✅ **Category Filter**: UPSC, Banking, SSC, IT, Languages, etc.
- ✅ **Price Range**: Slider with min/max values
- ✅ **Rating Filter**: 4+ stars, 3+ stars, etc.
- ✅ **Duration Filter**: Short (0-2h), Medium (2-5h), Long (5h+)
- ✅ **Difficulty Level**: Beginner, Intermediate, Advanced
- ✅ **Language**: English, Hindi, Regional languages
- ✅ **Course Type**: Video, Live, Hybrid
- ✅ **Instructor**: Filter by specific instructors

### **Search Functionality**
- ✅ **Auto-complete**: Suggestions as user types
- ✅ **Search History**: Recent searches
- ✅ **Popular Searches**: Trending course topics
- ✅ **Advanced Search**: Multiple criteria search
- ✅ **Search Results**: Highlighted matching terms
- ✅ **No Results**: Suggestions for alternative searches

## 🔧 Technical Implementation

### **Theme System Architecture**
```typescript
// Theme Management Interface
interface Theme {
  id: string
  name: string
  slug: string
  type: 'platform' | 'institute'
  version: string
  description: string
  category: string
  preview_image: string
  demo_image: string
  colors: ThemeColors
  fonts: ThemeFonts
  features: string[]
  pages: string[]
  suitable_for?: string[]
  is_active: boolean
  is_default: boolean
  usage_count?: number
  rating?: number
}

interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  background: string
  text: string
  muted?: string
  border?: string
}

interface ThemeFonts {
  heading: string
  body: string
  mono?: string
}
```

### **Theme Provider Component**
```typescript
// components/shared/theme/ThemeProvider.tsx
'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'

interface ThemeContextType {
  currentTheme: Theme | null
  setTheme: (theme: Theme) => void
  customizations: ThemeCustomizations
  updateCustomizations: (customizations: Partial<ThemeCustomizations>) => void
}

interface ThemeCustomizations {
  colors?: Partial<ThemeColors>
  fonts?: Partial<ThemeFonts>
  logo?: string
  favicon?: string
  content?: Record<string, any>
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({
  children,
  initialTheme,
  initialCustomizations
}: {
  children: React.ReactNode
  initialTheme?: Theme
  initialCustomizations?: ThemeCustomizations
}) {
  const [currentTheme, setCurrentTheme] = useState<Theme | null>(initialTheme || null)
  const [customizations, setCustomizations] = useState<ThemeCustomizations>(
    initialCustomizations || {}
  )

  const setTheme = (theme: Theme) => {
    setCurrentTheme(theme)
    // Apply theme CSS variables
    applyThemeVariables(theme, customizations)
  }

  const updateCustomizations = (newCustomizations: Partial<ThemeCustomizations>) => {
    const updated = { ...customizations, ...newCustomizations }
    setCustomizations(updated)
    if (currentTheme) {
      applyThemeVariables(currentTheme, updated)
    }
  }

  const applyThemeVariables = (theme: Theme, customizations: ThemeCustomizations) => {
    const root = document.documentElement
    const colors = { ...theme.colors, ...customizations.colors }

    // Apply CSS custom properties
    Object.entries(colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })

    // Apply fonts
    const fonts = { ...theme.fonts, ...customizations.fonts }
    Object.entries(fonts).forEach(([key, value]) => {
      root.style.setProperty(`--font-${key}`, value)
    })
  }

  useEffect(() => {
    if (currentTheme) {
      applyThemeVariables(currentTheme, customizations)
    }
  }, [currentTheme, customizations])

  return (
    <ThemeContext.Provider value={{
      currentTheme,
      setTheme,
      customizations,
      updateCustomizations
    }}>
      {children}
    </ThemeContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
```

### **Course Marketplace Components**
```typescript
// components/platform-core/course-marketplace/CourseGrid.tsx
'use client'

import { useState, useEffect } from 'react'
import { CourseCard } from './CourseCard'
import { CourseFilters } from './CourseFilters'
import { SearchBar } from './SearchBar'
import { Pagination } from '@/components/ui/pagination'

interface Course {
  id: string
  title: string
  description: string
  instructor: {
    name: string
    avatar: string
    rating: number
  }
  thumbnail: string
  price: number
  originalPrice?: number
  rating: number
  reviewCount: number
  duration: string
  lessonCount: number
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  category: string
  language: string
  type: 'video' | 'live' | 'hybrid'
  tags: string[]
  isWishlisted?: boolean
}

interface CourseFilters {
  category?: string
  priceRange?: [number, number]
  rating?: number
  duration?: string
  difficulty?: string
  language?: string
  type?: string
  search?: string
}

export function CourseGrid() {
  const [courses, setCourses] = useState<Course[]>([])
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([])
  const [filters, setFilters] = useState<CourseFilters>({})
  const [sortBy, setSortBy] = useState<string>('popular')
  const [currentPage, setCurrentPage] = useState(1)
  const [isLoading, setIsLoading] = useState(true)

  const coursesPerPage = 12

  useEffect(() => {
    fetchCourses()
  }, [])

  useEffect(() => {
    applyFiltersAndSort()
  }, [courses, filters, sortBy])

  const fetchCourses = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/courses/marketplace')
      const data = await response.json()
      setCourses(data.courses)
    } catch (error) {
      console.error('Failed to fetch courses:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const applyFiltersAndSort = () => {
    let filtered = [...courses]

    // Apply filters
    if (filters.category) {
      filtered = filtered.filter(course => course.category === filters.category)
    }

    if (filters.priceRange) {
      const [min, max] = filters.priceRange
      filtered = filtered.filter(course => course.price >= min && course.price <= max)
    }

    if (filters.rating) {
      filtered = filtered.filter(course => course.rating >= filters.rating!)
    }

    if (filters.difficulty) {
      filtered = filtered.filter(course => course.difficulty === filters.difficulty)
    }

    if (filters.language) {
      filtered = filtered.filter(course => course.language === filters.language)
    }

    if (filters.type) {
      filtered = filtered.filter(course => course.type === filters.type)
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      filtered = filtered.filter(course =>
        course.title.toLowerCase().includes(searchTerm) ||
        course.description.toLowerCase().includes(searchTerm) ||
        course.instructor.name.toLowerCase().includes(searchTerm) ||
        course.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      )
    }

    // Apply sorting
    switch (sortBy) {
      case 'popular':
        filtered.sort((a, b) => b.reviewCount - a.reviewCount)
        break
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating)
        break
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'newest':
        // Assuming courses have a createdAt field
        break
      default:
        break
    }

    setFilteredCourses(filtered)
    setCurrentPage(1) // Reset to first page when filters change
  }

  const handleFilterChange = (newFilters: Partial<CourseFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const handleAddToCart = (courseId: string) => {
    // Add to cart logic
    console.log('Adding to cart:', courseId)
  }

  const handleWishlist = (courseId: string) => {
    // Wishlist logic
    setCourses(prev => prev.map(course =>
      course.id === courseId
        ? { ...course, isWishlisted: !course.isWishlisted }
        : course
    ))
  }

  // Pagination
  const totalPages = Math.ceil(filteredCourses.length / coursesPerPage)
  const startIndex = (currentPage - 1) * coursesPerPage
  const paginatedCourses = filteredCourses.slice(startIndex, startIndex + coursesPerPage)

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-gray-200 aspect-video rounded-lg mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Sort */}
      <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
        <SearchBar
          onSearch={(search) => handleFilterChange({ search })}
          placeholder="Search courses, instructors, topics..."
        />

        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">
            {filteredCourses.length} courses found
          </span>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="popular">Most Popular</option>
            <option value="rating">Highest Rated</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="newest">Newest First</option>
          </select>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          <CourseFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            courses={courses}
          />
        </div>

        {/* Course Grid */}
        <div className="flex-1">
          {paginatedCourses.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {paginatedCourses.map((course) => (
                  <CourseCard
                    key={course.id}
                    course={course}
                    onAddToCart={handleAddToCart}
                    onWishlist={handleWishlist}
                  />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-8 flex justify-center">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                  />
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📚</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                No courses found
              </h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your filters or search terms
              </p>
              <button
                onClick={() => setFilters({})}
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                Clear all filters
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
```

## 🔧 Phase 3 Backend Implementation

### **Courses Collection**
**File**: `apps/api/src/collections/Courses.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin, isInstituteAdmin } from '../access/index'

const Courses: CollectionConfig = {
  slug: 'courses',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'instructor', 'price', 'status', 'createdAt'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.userType === 'super_admin') return true
      if (user?.userType === 'institute_admin' || user?.userType === 'trainer') {
        return { institute: { equals: user.institute } }
      }
      // Students can only see published courses
      return { status: { equals: 'published' } }
    },
    create: isInstituteAdmin,
    update: isInstituteAdmin,
    delete: isInstituteAdmin,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      maxLength: 200,
      index: true,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      index: true,
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
    },
    {
      name: 'shortDescription',
      type: 'textarea',
      required: true,
      maxLength: 300,
    },
    {
      name: 'thumbnail',
      type: 'upload',
      relationTo: 'media',
      required: true,
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      index: true,
    },
    {
      name: 'instructor',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      filterOptions: {
        userType: { in: ['trainer', 'institute_admin'] },
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'UPSC', value: 'upsc' },
        { label: 'Banking', value: 'banking' },
        { label: 'SSC', value: 'ssc' },
        { label: 'IT & Software', value: 'it_software' },
        { label: 'Languages', value: 'languages' },
        { label: 'Business', value: 'business' },
        { label: 'Design', value: 'design' },
        { label: 'Marketing', value: 'marketing' },
        { label: 'Other', value: 'other' },
      ],
      index: true,
    },
    {
      name: 'level',
      type: 'select',
      required: true,
      options: [
        { label: 'Beginner', value: 'beginner' },
        { label: 'Intermediate', value: 'intermediate' },
        { label: 'Advanced', value: 'advanced' },
      ],
      index: true,
    },
    {
      name: 'language',
      type: 'select',
      required: true,
      defaultValue: 'english',
      options: [
        { label: 'English', value: 'english' },
        { label: 'Hindi', value: 'hindi' },
        { label: 'Tamil', value: 'tamil' },
        { label: 'Telugu', value: 'telugu' },
        { label: 'Bengali', value: 'bengali' },
      ],
      index: true,
    },
    {
      name: 'pricing',
      type: 'group',
      fields: [
        {
          name: 'price',
          type: 'number',
          required: true,
          min: 0,
        },
        {
          name: 'originalPrice',
          type: 'number',
        },
        {
          name: 'currency',
          type: 'select',
          defaultValue: 'INR',
          options: [
            { label: 'Indian Rupee (₹)', value: 'INR' },
            { label: 'US Dollar ($)', value: 'USD' },
            { label: 'Euro (€)', value: 'EUR' },
          ],
        },
      ],
    },
    {
      name: 'duration',
      type: 'group',
      fields: [
        {
          name: 'totalHours',
          type: 'number',
          required: true,
          min: 0,
        },
        {
          name: 'totalLessons',
          type: 'number',
          required: true,
          min: 1,
        },
      ],
    },
    {
      name: 'content',
      type: 'array',
      label: 'Course Content',
      fields: [
        {
          name: 'sectionTitle',
          type: 'text',
          required: true,
        },
        {
          name: 'lessons',
          type: 'array',
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
            },
            {
              name: 'type',
              type: 'select',
              required: true,
              options: [
                { label: 'Video', value: 'video' },
                { label: 'Text', value: 'text' },
                { label: 'Quiz', value: 'quiz' },
                { label: 'Assignment', value: 'assignment' },
              ],
            },
            {
              name: 'duration',
              type: 'number',
            },
            {
              name: 'content',
              type: 'richText',
            },
            {
              name: 'videoUrl',
              type: 'text',
              admin: {
                condition: (data, siblingData) => siblingData.type === 'video',
              },
            },
            {
              name: 'isPreview',
              type: 'checkbox',
              defaultValue: false,
            },
          ],
        },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Under Review', value: 'review' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
      index: true,
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'enrollmentCount',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'rating',
      type: 'group',
      fields: [
        {
          name: 'average',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true },
        },
        {
          name: 'count',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true },
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Generate slug from title
          if (!data.slug && data.title) {
            data.slug = data.title
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/(^-|-$)/g, '')
          }
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default Courses
```

### **Themes Collection**
**File**: `apps/api/src/collections/Themes.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const Themes: CollectionConfig = {
  slug: 'themes',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'type', 'category', 'isActive', 'usageCount'],
  },
  access: {
    read: () => true, // All users can view themes
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      index: true,
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Platform Theme', value: 'platform' },
        { label: 'Institute Theme', value: 'institute' },
      ],
      index: true,
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        // Platform categories
        { label: 'SaaS Modern', value: 'saas_modern' },
        { label: 'SaaS Corporate', value: 'saas_corporate' },
        { label: 'SaaS Startup', value: 'saas_startup' },
        // Institute categories
        { label: 'Education Modern', value: 'education_modern' },
        { label: 'Education Classic', value: 'education_classic' },
        { label: 'Coaching Professional', value: 'coaching_professional' },
        { label: 'University Classic', value: 'university_classic' },
        { label: 'Online Academy', value: 'online_academy' },
      ],
      index: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      maxLength: 500,
    },
    {
      name: 'version',
      type: 'text',
      required: true,
      defaultValue: '1.0.0',
    },
    {
      name: 'previewImage',
      type: 'upload',
      relationTo: 'media',
      required: true,
    },
    {
      name: 'demoImage',
      type: 'upload',
      relationTo: 'media',
      required: true,
    },
    {
      name: 'screenshots',
      type: 'array',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
        },
        {
          name: 'title',
          type: 'text',
        },
        {
          name: 'description',
          type: 'text',
        },
      ],
    },
    {
      name: 'colors',
      type: 'group',
      fields: [
        {
          name: 'primary',
          type: 'text',
          required: true,
          defaultValue: '#3b82f6',
        },
        {
          name: 'secondary',
          type: 'text',
          required: true,
          defaultValue: '#64748b',
        },
        {
          name: 'accent',
          type: 'text',
          required: true,
          defaultValue: '#10b981',
        },
        {
          name: 'background',
          type: 'text',
          required: true,
          defaultValue: '#ffffff',
        },
        {
          name: 'text',
          type: 'text',
          required: true,
          defaultValue: '#1f2937',
        },
      ],
    },
    {
      name: 'fonts',
      type: 'group',
      fields: [
        {
          name: 'heading',
          type: 'text',
          required: true,
          defaultValue: 'Inter',
        },
        {
          name: 'body',
          type: 'text',
          required: true,
          defaultValue: 'Inter',
        },
        {
          name: 'mono',
          type: 'text',
          defaultValue: 'Fira Code',
        },
      ],
    },
    {
      name: 'features',
      type: 'array',
      fields: [
        {
          name: 'feature',
          type: 'text',
        },
      ],
    },
    {
      name: 'customizableElements',
      type: 'json',
    },
    {
      name: 'usageCount',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'rating',
      type: 'group',
      fields: [
        {
          name: 'average',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true },
        },
        {
          name: 'count',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true },
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'isDefault',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Generate slug from name
          if (!data.slug && data.name) {
            data.slug = data.name
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/(^-|-$)/g, '')
          }
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default Themes
```

### **Course Marketplace Endpoints**
**File**: `apps/api/src/endpoints/courses/index.ts`

```typescript
import { Endpoint } from 'payload/config'

const courseEndpoints: Endpoint[] = [
  // Get marketplace courses with filters
  {
    path: '/courses/marketplace',
    method: 'get',
    handler: async (req, res) => {
      try {
        const {
          category,
          level,
          language,
          minPrice,
          maxPrice,
          rating,
          search,
          sort = 'popular',
          page = 1,
          limit = 12
        } = req.query

        // Build filter conditions
        const where: any = {
          status: { equals: 'published' }
        }

        if (category) {
          where.category = { equals: category }
        }

        if (level) {
          where.level = { equals: level }
        }

        if (language) {
          where.language = { equals: language }
        }

        if (minPrice || maxPrice) {
          where['pricing.price'] = {}
          if (minPrice) where['pricing.price'].greater_than_equal = Number(minPrice)
          if (maxPrice) where['pricing.price'].less_than_equal = Number(maxPrice)
        }

        if (rating) {
          where['rating.average'] = { greater_than_equal: Number(rating) }
        }

        if (search) {
          where.or = [
            { title: { contains: search } },
            { shortDescription: { contains: search } }
          ]
        }

        // Build sort options
        let sortOption = '-createdAt'
        switch (sort) {
          case 'popular':
            sortOption = '-enrollmentCount'
            break
          case 'rating':
            sortOption = '-rating.average'
            break
          case 'price-low':
            sortOption = 'pricing.price'
            break
          case 'price-high':
            sortOption = '-pricing.price'
            break
          case 'newest':
            sortOption = '-createdAt'
            break
        }

        const courses = await req.payload.find({
          collection: 'courses',
          where,
          sort: sortOption,
          page: Number(page),
          limit: Number(limit),
          populate: ['instructor', 'institute']
        })

        res.json({
          success: true,
          courses: courses.docs,
          pagination: {
            page: courses.page,
            limit: courses.limit,
            totalPages: courses.totalPages,
            totalDocs: courses.totalDocs,
            hasNextPage: courses.hasNextPage,
            hasPrevPage: courses.hasPrevPage
          }
        })

      } catch (error) {
        console.error('Marketplace courses fetch error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  },

  // Get featured courses
  {
    path: '/courses/featured',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { limit = 6 } = req.query

        const courses = await req.payload.find({
          collection: 'courses',
          where: {
            and: [
              { status: { equals: 'published' } },
              { featured: { equals: true } }
            ]
          },
          sort: '-rating.average',
          limit: Number(limit),
          populate: ['instructor', 'institute']
        })

        res.json({
          success: true,
          courses: courses.docs
        })

      } catch (error) {
        console.error('Featured courses fetch error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  }
]

export default courseEndpoints
```

### **Theme Management Endpoints**
**File**: `apps/api/src/endpoints/themes/index.ts`

```typescript
import { Endpoint } from 'payload/config'

const themeEndpoints: Endpoint[] = [
  // Get themes by type
  {
    path: '/themes',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { type, category, featured } = req.query

        const where: any = {
          isActive: { equals: true }
        }

        if (type) {
          where.type = { equals: type }
        }

        if (category) {
          where.category = { equals: category }
        }

        if (featured === 'true') {
          where.isFeatured = { equals: true }
        }

        const themes = await req.payload.find({
          collection: 'themes',
          where,
          sort: '-usageCount'
        })

        res.json({
          success: true,
          themes: themes.docs
        })

      } catch (error) {
        console.error('Themes fetch error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  },

  // Apply theme to institute
  {
    path: '/themes/:id/apply',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const userId = req.user?.id
        const userType = req.user?.userType
        const instituteId = req.user?.institute

        if (!userId || userType !== 'institute_admin' || !instituteId) {
          return res.status(401).json({
            error: 'Access denied'
          })
        }

        // Verify theme exists and is active
        const theme = await req.payload.findByID({
          collection: 'themes',
          id
        })

        if (!theme || !theme.isActive) {
          return res.status(404).json({
            error: 'Theme not found or inactive'
          })
        }

        // Update institute theme
        await req.payload.update({
          collection: 'institutes',
          id: instituteId,
          data: {
            theme: id,
            themeCustomizations: req.body.customizations || {}
          }
        })

        // Increment theme usage count
        await req.payload.update({
          collection: 'themes',
          id,
          data: {
            usageCount: (theme.usageCount || 0) + 1
          }
        })

        res.json({
          success: true,
          message: 'Theme applied successfully'
        })

      } catch (error) {
        console.error('Theme application error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  }
]

export default themeEndpoints
```

### **Updated Payload Config**
**File**: `apps/api/payload.config.ts`

```typescript
import { buildConfig } from 'payload/config'
import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { webpackBundler } from '@payloadcms/bundler-webpack'
import { slateEditor } from '@payloadcms/richtext-slate'
import path from 'path'

// Collections
import Users from './src/collections/Users'
import Institutes from './src/collections/Institutes'
import Courses from './src/collections/Courses'
import Themes from './src/collections/Themes'

// Endpoints
import authEndpoints from './src/endpoints/auth'
import courseEndpoints from './src/endpoints/courses'
import themeEndpoints from './src/endpoints/themes'

export default buildConfig({
  admin: {
    user: Users.slug,
    bundler: webpackBundler(),
  },
  editor: slateEditor({}),
  collections: [
    Users,
    Institutes,
    Courses,
    Themes,
  ],
  typescript: {
    outputFile: path.resolve(__dirname, 'payload-types.ts'),
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || 'mongodb://localhost:27017/groups-exam-lms',
  }),
  endpoints: [
    ...authEndpoints,
    ...courseEndpoints,
    ...themeEndpoints,
  ],
  cors: [
    'http://localhost:3000', // Super Admin
    'http://localhost:3001', // API
    'http://localhost:3002', // Institute Testing (simulates custom domain)
    process.env.FRONTEND_URL || 'https://groups-exam.com',
    // Institute custom domains loaded dynamically from database
  ],
})
```

## 🎯 Phase 3 Success Criteria

### **Functional Requirements**
- [ ] ✅ Theme system is fully operational
- [ ] ✅ Landing page themes display correctly
- [ ] ✅ Course marketplace functions properly
- [ ] ✅ Theme customization works
- [ ] ✅ Preview functionality is working
- [ ] ✅ All themes are responsive

### **Backend Requirements**
- [ ] ✅ Courses collection is fully functional
- [ ] ✅ Themes collection is implemented
- [ ] ✅ Course marketplace endpoints work
- [ ] ✅ Theme management endpoints functional
- [ ] ✅ Course filtering and search work
- [ ] ✅ Theme application system works
- [ ] ✅ File upload for course content

### **Technical Requirements**
- [ ] ✅ Theme switching is seamless
- [ ] ✅ Custom CSS injection works
- [ ] ✅ Image optimization is implemented
- [ ] ✅ Performance is optimized
- [ ] ✅ SEO meta tags are dynamic
- [ ] ✅ Analytics tracking is functional

### **User Experience Requirements**
- [ ] ✅ Theme gallery is intuitive
- [ ] ✅ Customization interface is user-friendly
- [ ] ✅ Preview mode works correctly
- [ ] ✅ Course browsing is smooth
- [ ] ✅ Search and filters work properly
- [ ] ✅ Mobile experience is excellent

## 🔄 **Complete CRUD API Integration**

### **Themes CRUD Endpoints**
**File**: `apps/api/src/endpoints/themes/crud.ts`

```typescript
import { Endpoint } from 'payload/config'

const themesCrudEndpoints: Endpoint[] = [
  // Get Themes (with pagination and filters)
  {
    path: '/themes',
    method: 'get',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const {
          page = 1,
          limit = 20,
          search = '',
          category = '',
          isActive = '',
          isFeatured = ''
        } = req.query

        // Build where clause
        const where: any = {}

        // Search filter
        if (search) {
          where.or = [
            { name: { contains: search } },
            { description: { contains: search } },
            { tags: { contains: search } }
          ]
        }

        // Category filter
        if (category) {
          where.category = { equals: category }
        }

        // Active filter
        if (isActive !== '') {
          where.isActive = { equals: isActive === 'true' }
        }

        // Featured filter
        if (isFeatured !== '') {
          where.isFeatured = { equals: isFeatured === 'true' }
        }

        const themes = await req.payload.find({
          collection: 'themes',
          where,
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          sort: '-createdAt'
        })

        res.json({
          success: true,
          ...themes
        })

      } catch (error) {
        console.error('Get themes error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch themes'
        })
      }
    }
  },

  // Create Theme
  {
    path: '/themes',
    method: 'post',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser || currentUser.userType !== 'super_admin') {
          return res.status(403).json({
            error: 'Only super admins can create themes'
          })
        }

        const {
          name,
          description,
          category,
          previewImage,
          demoUrl,
          features,
          colorScheme,
          layout,
          customCSS,
          customJS,
          tags,
          isActive,
          isFeatured
        } = req.body

        // Validate required fields
        if (!name || !description || !category) {
          return res.status(400).json({
            error: 'Name, description, and category are required'
          })
        }

        // Check if theme already exists
        const existingTheme = await req.payload.find({
          collection: 'themes',
          where: { name: { equals: name } },
          limit: 1
        })

        if (existingTheme.totalDocs > 0) {
          return res.status(400).json({
            error: 'Theme with this name already exists'
          })
        }

        const themeData = {
          name,
          description,
          category,
          previewImage,
          demoUrl,
          features: features || [],
          colorScheme: colorScheme || {},
          layout: layout || {},
          customCSS,
          customJS,
          tags: tags || [],
          isActive: isActive !== undefined ? isActive : true,
          isFeatured: isFeatured !== undefined ? isFeatured : false,
          createdBy: currentUser.id
        }

        const theme = await req.payload.create({
          collection: 'themes',
          data: themeData
        })

        res.json({
          success: true,
          theme,
          message: 'Theme created successfully'
        })

      } catch (error) {
        console.error('Create theme error:', error)
        res.status(500).json({
          error: error.message || 'Failed to create theme'
        })
      }
    }
  }
]

export default themesCrudEndpoints
```

### **Courses CRUD Endpoints**
**File**: `apps/api/src/endpoints/courses/crud.ts`

```typescript
import { Endpoint } from 'payload/config'

const coursesCrudEndpoints: Endpoint[] = [
  // Get Courses (with pagination and filters)
  {
    path: '/courses',
    method: 'get',
    handler: async (req, res) => {
      try {
        const {
          page = 1,
          limit = 20,
          search = '',
          category = '',
          level = '',
          institute = '',
          isActive = '',
          isFeatured = '',
          minPrice = '',
          maxPrice = ''
        } = req.query

        // Build where clause
        const where: any = {}

        // Search filter
        if (search) {
          where.or = [
            { title: { contains: search } },
            { description: { contains: search } },
            { tags: { contains: search } }
          ]
        }

        // Category filter
        if (category) {
          where.category = { equals: category }
        }

        // Level filter
        if (level) {
          where.level = { equals: level }
        }

        // Institute filter
        if (institute) {
          where.institute = { equals: institute }
        }

        // Active filter
        if (isActive !== '') {
          where.isActive = { equals: isActive === 'true' }
        }

        // Featured filter
        if (isFeatured !== '') {
          where.isFeatured = { equals: isFeatured === 'true' }
        }

        // Price range filter
        if (minPrice || maxPrice) {
          where.price = {}
          if (minPrice) where.price.greater_than_equal = parseFloat(minPrice)
          if (maxPrice) where.price.less_than_equal = parseFloat(maxPrice)
        }

        const courses = await req.payload.find({
          collection: 'courses',
          where,
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          sort: '-createdAt',
          populate: ['institute', 'instructor']
        })

        res.json({
          success: true,
          ...courses
        })

      } catch (error) {
        console.error('Get courses error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch courses'
        })
      }
    }
  },

  // Create Course
  {
    path: '/courses',
    method: 'post',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can create courses'
          })
        }

        const {
          title,
          description,
          category,
          level,
          price,
          duration,
          institute,
          instructor,
          thumbnail,
          tags,
          curriculum,
          requirements,
          whatYouWillLearn,
          isActive,
          isFeatured
        } = req.body

        // Validate required fields
        if (!title || !description || !category || !level || !institute) {
          return res.status(400).json({
            error: 'Title, description, category, level, and institute are required'
          })
        }

        // Set institute for institute admins
        let courseInstitute = institute
        if (currentUser.userType === 'institute_admin') {
          courseInstitute = currentUser.institute
        }

        const courseData = {
          title,
          description,
          category,
          level,
          price: price || 0,
          duration,
          institute: courseInstitute,
          instructor,
          thumbnail,
          tags: tags || [],
          curriculum: curriculum || [],
          requirements: requirements || [],
          whatYouWillLearn: whatYouWillLearn || [],
          isActive: isActive !== undefined ? isActive : true,
          isFeatured: isFeatured !== undefined ? isFeatured : false,
          createdBy: currentUser.id
        }

        const course = await req.payload.create({
          collection: 'courses',
          data: courseData
        })

        res.json({
          success: true,
          course,
          message: 'Course created successfully'
        })

      } catch (error) {
        console.error('Create course error:', error)
        res.status(500).json({
          error: error.message || 'Failed to create course'
        })
      }
    }
  },

  // Update Course
  {
    path: '/courses/:id',
    method: 'patch',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        // Get existing course
        const existingCourse = await req.payload.findByID({
          collection: 'courses',
          id
        })

        if (!existingCourse) {
          return res.status(404).json({
            error: 'Course not found'
          })
        }

        // Check permissions
        const canEdit = currentUser.userType === 'super_admin' ||
          (currentUser.userType === 'institute_admin' && existingCourse.institute === currentUser.institute)

        if (!canEdit) {
          return res.status(403).json({
            error: 'Permission denied'
          })
        }

        const updateData = { ...req.body }

        const course = await req.payload.update({
          collection: 'courses',
          id,
          data: updateData
        })

        res.json({
          success: true,
          course,
          message: 'Course updated successfully'
        })

      } catch (error) {
        console.error('Update course error:', error)
        res.status(500).json({
          error: error.message || 'Failed to update course'
        })
      }
    }
  },

  // Delete Course
  {
    path: '/courses/:id',
    method: 'delete',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can delete courses'
          })
        }

        // Get existing course
        const existingCourse = await req.payload.findByID({
          collection: 'courses',
          id
        })

        if (!existingCourse) {
          return res.status(404).json({
            error: 'Course not found'
          })
        }

        // Check permissions for institute admin
        if (currentUser.userType === 'institute_admin' && existingCourse.institute !== currentUser.institute) {
          return res.status(403).json({
            error: 'Can only delete courses from your institute'
          })
        }

        await req.payload.delete({
          collection: 'courses',
          id
        })

        res.json({
          success: true,
          message: 'Course deleted successfully'
        })

      } catch (error) {
        console.error('Delete course error:', error)
        res.status(500).json({
          error: error.message || 'Failed to delete course'
        })
      }
    }
  }
]

export default coursesCrudEndpoints
```

## 🗄️ **Zustand State Management**

### **Themes Store**
**File**: `apps/super-admin/src/stores/useThemesStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface Theme {
  id: string
  name: string
  description: string
  category: 'landing' | 'marketplace' | 'dashboard' | 'blog'
  previewImage?: string
  demoUrl?: string
  features: string[]
  colorScheme: {
    primary?: string
    secondary?: string
    accent?: string
    background?: string
    text?: string
  }
  layout: {
    header?: any
    footer?: any
    sidebar?: any
    content?: any
  }
  customCSS?: string
  customJS?: string
  tags: string[]
  isActive: boolean
  isFeatured: boolean
  createdAt: string
  updatedAt: string
}

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface ThemesState {
  // Data
  themes: Theme[]

  // UI State
  isLoading: boolean
  viewMode: 'card' | 'list'

  // Pagination
  pagination: PaginationInfo

  // Filters
  filters: {
    search: string
    category: string
    isActive: string
    isFeatured: string
  }

  // Actions
  fetchThemes: (page?: number, filters?: any) => Promise<void>
  createTheme: (data: any) => Promise<void>
  updateTheme: (id: string, data: any) => Promise<void>
  deleteTheme: (id: string) => Promise<void>

  setViewMode: (mode: 'card' | 'list') => void
  setFilters: (filters: any) => void
  clearFilters: () => void
}

export const useThemesStore = create<ThemesState>()(
  devtools(
    (set, get) => ({
      // Initial state
      themes: [],

      isLoading: false,
      viewMode: 'card',

      pagination: {
        page: 1,
        limit: 20,
        totalPages: 1,
        totalDocs: 0,
        hasNextPage: false,
        hasPrevPage: false
      },

      filters: {
        search: '',
        category: '',
        isActive: 'true',
        isFeatured: ''
      },

      // Fetch themes
      fetchThemes: async (page = 1, filters = {}) => {
        set({ isLoading: true })
        try {
          const currentFilters = { ...get().filters, ...filters }
          const params = new URLSearchParams({
            page: page.toString(),
            limit: get().pagination.limit.toString(),
            ...Object.fromEntries(
              Object.entries(currentFilters).filter(([_, value]) => value !== '')
            )
          })

          const response = await fetch(`/api/themes?${params}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch themes')
          }

          const data = await response.json()

          set({
            themes: data.docs,
            pagination: {
              page: data.page,
              limit: data.limit,
              totalPages: data.totalPages,
              totalDocs: data.totalDocs,
              hasNextPage: data.hasNextPage,
              hasPrevPage: data.hasPrevPage
            }
          })

        } catch (error) {
          console.error('Fetch themes error:', error)
          toast.error('Failed to fetch themes')
        } finally {
          set({ isLoading: false })
        }
      },

      // Create theme
      createTheme: async (data: any) => {
        try {
          const response = await fetch('/api/themes', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to create theme')
          }

          const result = await response.json()

          // Refresh themes list
          await get().fetchThemes()

          toast.success('Theme created successfully')
          return result

        } catch (error) {
          console.error('Create theme error:', error)
          toast.error(error.message || 'Failed to create theme')
          throw error
        }
      },

      // Update theme
      updateTheme: async (id: string, data: any) => {
        try {
          const response = await fetch(`/api/themes/${id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to update theme')
          }

          const result = await response.json()

          // Update theme in state
          set(state => ({
            themes: state.themes.map(theme =>
              theme.id === id ? { ...theme, ...result.theme } : theme
            )
          }))

          toast.success('Theme updated successfully')
          return result

        } catch (error) {
          console.error('Update theme error:', error)
          toast.error(error.message || 'Failed to update theme')
          throw error
        }
      },

      // Delete theme
      deleteTheme: async (id: string) => {
        try {
          const response = await fetch(`/api/themes/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to delete theme')
          }

          // Remove theme from state
          set(state => ({
            themes: state.themes.filter(theme => theme.id !== id)
          }))

          toast.success('Theme deleted successfully')

        } catch (error) {
          console.error('Delete theme error:', error)
          toast.error(error.message || 'Failed to delete theme')
          throw error
        }
      },

      // Set view mode
      setViewMode: (mode: 'card' | 'list') => {
        set({ viewMode: mode })
      },

      // Set filters
      setFilters: (filters: any) => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }))
      },

      // Clear filters
      clearFilters: () => {
        set({
          filters: {
            search: '',
            category: '',
            isActive: 'true',
            isFeatured: ''
          }
        })
      }
    }),
    {
      name: 'themes-store'
    }
  )
)
```

### **Courses Store**
**File**: `apps/super-admin/src/stores/useCoursesStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface Course {
  id: string
  title: string
  description: string
  category: string
  level: 'beginner' | 'intermediate' | 'advanced'
  price: number
  duration: string
  institute: any
  instructor?: any
  thumbnail?: string
  tags: string[]
  curriculum: any[]
  requirements: string[]
  whatYouWillLearn: string[]
  isActive: boolean
  isFeatured: boolean
  createdAt: string
  updatedAt: string
}

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface CoursesState {
  // Data
  courses: Course[]

  // UI State
  isLoading: boolean
  viewMode: 'card' | 'list'

  // Pagination
  pagination: PaginationInfo

  // Filters
  filters: {
    search: string
    category: string
    level: string
    institute: string
    isActive: string
    isFeatured: string
    minPrice: string
    maxPrice: string
  }

  // Actions
  fetchCourses: (page?: number, filters?: any) => Promise<void>
  createCourse: (data: any) => Promise<void>
  updateCourse: (id: string, data: any) => Promise<void>
  deleteCourse: (id: string) => Promise<void>

  setViewMode: (mode: 'card' | 'list') => void
  setFilters: (filters: any) => void
  clearFilters: () => void
}

export const useCoursesStore = create<CoursesState>()(
  devtools(
    (set, get) => ({
      // Initial state
      courses: [],

      isLoading: false,
      viewMode: 'card',

      pagination: {
        page: 1,
        limit: 20,
        totalPages: 1,
        totalDocs: 0,
        hasNextPage: false,
        hasPrevPage: false
      },

      filters: {
        search: '',
        category: '',
        level: '',
        institute: '',
        isActive: 'true',
        isFeatured: '',
        minPrice: '',
        maxPrice: ''
      },

      // Fetch courses
      fetchCourses: async (page = 1, filters = {}) => {
        set({ isLoading: true })
        try {
          const currentFilters = { ...get().filters, ...filters }
          const params = new URLSearchParams({
            page: page.toString(),
            limit: get().pagination.limit.toString(),
            ...Object.fromEntries(
              Object.entries(currentFilters).filter(([_, value]) => value !== '')
            )
          })

          const response = await fetch(`/api/courses?${params}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch courses')
          }

          const data = await response.json()

          set({
            courses: data.docs,
            pagination: {
              page: data.page,
              limit: data.limit,
              totalPages: data.totalPages,
              totalDocs: data.totalDocs,
              hasNextPage: data.hasNextPage,
              hasPrevPage: data.hasPrevPage
            }
          })

        } catch (error) {
          console.error('Fetch courses error:', error)
          toast.error('Failed to fetch courses')
        } finally {
          set({ isLoading: false })
        }
      },

      // Create course
      createCourse: async (data: any) => {
        try {
          const response = await fetch('/api/courses', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to create course')
          }

          const result = await response.json()

          // Refresh courses list
          await get().fetchCourses()

          toast.success('Course created successfully')
          return result

        } catch (error) {
          console.error('Create course error:', error)
          toast.error(error.message || 'Failed to create course')
          throw error
        }
      },

      // Update course
      updateCourse: async (id: string, data: any) => {
        try {
          const response = await fetch(`/api/courses/${id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to update course')
          }

          const result = await response.json()

          // Update course in state
          set(state => ({
            courses: state.courses.map(course =>
              course.id === id ? { ...course, ...result.course } : course
            )
          }))

          toast.success('Course updated successfully')
          return result

        } catch (error) {
          console.error('Update course error:', error)
          toast.error(error.message || 'Failed to update course')
          throw error
        }
      },

      // Delete course
      deleteCourse: async (id: string) => {
        try {
          const response = await fetch(`/api/courses/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to delete course')
          }

          // Remove course from state
          set(state => ({
            courses: state.courses.filter(course => course.id !== id)
          }))

          toast.success('Course deleted successfully')

        } catch (error) {
          console.error('Delete course error:', error)
          toast.error(error.message || 'Failed to delete course')
          throw error
        }
      },

      // Set view mode
      setViewMode: (mode: 'card' | 'list') => {
        set({ viewMode: mode })
      },

      // Set filters
      setFilters: (filters: any) => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }))
      },

      // Clear filters
      clearFilters: () => {
        set({
          filters: {
            search: '',
            category: '',
            level: '',
            institute: '',
            isActive: 'true',
            isFeatured: '',
            minPrice: '',
            maxPrice: ''
          }
        })
      }
    }),
    {
      name: 'courses-store'
    }
  )
)
```

## 🎯 **Phase 3 Complete Integration Summary**

### **✅ CRUD Operations:**
```typescript
✅ Themes Management API:
├── GET /api/themes → List themes with pagination & filters
├── POST /api/themes → Create new theme (Super Admin only)
├── PATCH /api/themes/:id → Update theme
├── DELETE /api/themes/:id → Delete theme (with usage check)
└── Category and feature-based filtering

✅ Courses Management API:
├── GET /api/courses → List courses with pagination & filters
├── POST /api/courses → Create new course (Admin only)
├── PATCH /api/courses/:id → Update course
├── DELETE /api/courses/:id → Delete course
└── Institute-scoped access control and price filtering
```

### **✅ Zustand State Management:**
```typescript
✅ Themes Store:
├── 📊 fetchThemes() → Paginated theme list with filters
├── 📝 createTheme() → Create theme with validation
├── ✏️ updateTheme() → Update theme details
├── 🗑️ deleteTheme() → Remove theme with usage check
└── 🔍 Filter management (search, category, featured status)

✅ Courses Store:
├── 📊 fetchCourses() → Paginated course list with filters
├── 📝 createCourse() → Create course with institute validation
├── ✏️ updateCourse() → Update course details
├── 🗑️ deleteCourse() → Remove course with permission check
└── 🔍 Advanced filtering (price range, level, institute)
```

### **✅ Enhanced Features:**
```typescript
✅ Theme Management:
├── 🎨 Category-based organization (landing/marketplace/dashboard/blog)
├── 🖼️ Preview image and demo URL support
├── 🎯 Feature tagging system
├── 🎨 Color scheme and layout configuration
├── 💻 Custom CSS/JS injection support
├── ⭐ Featured themes highlighting
└── 🔍 Usage tracking before deletion

✅ Course Management:
├── 📚 Multi-level course organization (beginner/intermediate/advanced)
├── 💰 Price range filtering and management
├── 🏢 Institute-scoped course creation
├── 👨‍🏫 Instructor assignment support
├── 📋 Curriculum and requirements management
├── 🏷️ Tag-based categorization
├── ⭐ Featured course highlighting
└── 🔐 Permission-based access control
```

### **✅ Security & Access Control:**
```typescript
✅ Security Features:
├── 🔐 Role-based access control (Super Admin/Institute Admin)
├── 🎯 Institute-scoped course management
├── 🚫 Usage validation before theme deletion
├── 📊 Permission checks for all operations
├── 🔍 Scope-based filtering for institute admins
└── 📝 Audit trail with createdBy tracking
```

**Perfect! Phase 3 Themes & Landing Pages is now complete with full CRUD operations, Zustand state management, comprehensive filtering, and security features! 🚀**
