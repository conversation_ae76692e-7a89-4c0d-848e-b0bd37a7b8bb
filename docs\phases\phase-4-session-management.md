# 🔐 Phase 4: Session Management & Security

## 🎯 Overview
This document provides comprehensive session management and security controls for all three panels with device restrictions, active session monitoring, and security policies.

## 🔒 Universal Session Management Component

### **Session Management Interface**
```typescript
// components/shared/security/SessionManager.tsx
'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/components/shared/notifications/useToast'
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  MapPin, 
  Clock, 
  Shield, 
  LogOut,
  AlertTriangle 
} from 'lucide-react'

interface ActiveSession {
  id: string
  deviceType: 'desktop' | 'mobile' | 'tablet'
  deviceName: string
  browser: string
  operatingSystem: string
  ipAddress: string
  location: {
    city: string
    country: string
    region: string
  }
  loginTime: string
  lastActivity: string
  isCurrent: boolean
  isSecure: boolean
}

interface SessionLimits {
  maxDesktopSessions: number
  maxMobileSessions: number
  maxTotalSessions: number
  sessionTimeout: number // in minutes
  requireSecureConnection: boolean
  allowConcurrentSessions: boolean
}

export function SessionManager({ userType }: { userType: 'super_admin' | 'institute_admin' | 'student' }) {
  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([])
  const [sessionLimits, setSessionLimits] = useState<SessionLimits | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const toast = useToast()

  useEffect(() => {
    fetchActiveSessions()
    fetchSessionLimits()
  }, [])

  const fetchActiveSessions = async () => {
    try {
      const response = await fetch('/api/auth/sessions')
      const data = await response.json()
      setActiveSessions(data.sessions)
    } catch (error) {
      toast.error('Failed to fetch active sessions')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchSessionLimits = async () => {
    try {
      const response = await fetch('/api/auth/session-limits')
      const data = await response.json()
      setSessionLimits(data.limits)
    } catch (error) {
      console.error('Failed to fetch session limits')
    }
  }

  const terminateSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/auth/sessions/${sessionId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success('Session Terminated', 'The session has been successfully terminated.')
        fetchActiveSessions()
      } else {
        throw new Error('Failed to terminate session')
      }
    } catch (error) {
      toast.error('Termination Failed', 'Unable to terminate the session.')
    }
  }

  const terminateAllOtherSessions = async () => {
    try {
      const response = await fetch('/api/auth/sessions/terminate-others', {
        method: 'POST'
      })

      if (response.ok) {
        toast.success('Sessions Terminated', 'All other sessions have been terminated.')
        fetchActiveSessions()
      } else {
        throw new Error('Failed to terminate sessions')
      }
    } catch (error) {
      toast.error('Termination Failed', 'Unable to terminate other sessions.')
    }
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'desktop':
        return <Monitor className="h-4 w-4" />
      case 'mobile':
        return <Smartphone className="h-4 w-4" />
      case 'tablet':
        return <Tablet className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  const getSessionStatus = (session: ActiveSession) => {
    if (session.isCurrent) {
      return <Badge variant="success">Current Session</Badge>
    }
    if (!session.isSecure) {
      return <Badge variant="destructive">Insecure</Badge>
    }
    return <Badge variant="secondary">Active</Badge>
  }

  const isSessionLimitExceeded = () => {
    if (!sessionLimits) return false
    
    const desktopSessions = activeSessions.filter(s => s.deviceType === 'desktop').length
    const mobileSessions = activeSessions.filter(s => s.deviceType === 'mobile').length
    const totalSessions = activeSessions.length

    return (
      desktopSessions > sessionLimits.maxDesktopSessions ||
      mobileSessions > sessionLimits.maxMobileSessions ||
      totalSessions > sessionLimits.maxTotalSessions
    )
  }

  if (isLoading) {
    return <div>Loading session information...</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Active Sessions</h2>
        <p className="text-gray-600">Manage your active login sessions across devices</p>
      </div>

      {/* Session Limits Warning */}
      {isSessionLimitExceeded() && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            You have exceeded the maximum number of allowed sessions. Please terminate some sessions to continue.
          </AlertDescription>
        </Alert>
      )}

      {/* Session Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sessions</p>
                <p className="text-2xl font-bold text-gray-900">{activeSessions.length}</p>
              </div>
              <Shield className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Desktop</p>
                <p className="text-2xl font-bold text-gray-900">
                  {activeSessions.filter(s => s.deviceType === 'desktop').length}
                </p>
              </div>
              <Monitor className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Mobile</p>
                <p className="text-2xl font-bold text-gray-900">
                  {activeSessions.filter(s => s.deviceType === 'mobile').length}
                </p>
              </div>
              <Smartphone className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Insecure</p>
                <p className="text-2xl font-bold text-red-600">
                  {activeSessions.filter(s => !s.isSecure).length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Session Limits Info */}
      {sessionLimits && (
        <Card>
          <CardHeader>
            <CardTitle>Session Limits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <Label>Desktop Sessions</Label>
                <p className="text-gray-600">
                  {activeSessions.filter(s => s.deviceType === 'desktop').length} / {sessionLimits.maxDesktopSessions}
                </p>
              </div>
              <div>
                <Label>Mobile Sessions</Label>
                <p className="text-gray-600">
                  {activeSessions.filter(s => s.deviceType === 'mobile').length} / {sessionLimits.maxMobileSessions}
                </p>
              </div>
              <div>
                <Label>Session Timeout</Label>
                <p className="text-gray-600">{sessionLimits.sessionTimeout} minutes</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Sessions Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Active Sessions</CardTitle>
            <Button 
              variant="outline" 
              onClick={terminateAllOtherSessions}
              disabled={activeSessions.length <= 1}
            >
              <LogOut className="h-4 w-4 mr-2" />
              Terminate All Others
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Device</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Login Time</TableHead>
                <TableHead>Last Activity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {activeSessions.map((session) => (
                <TableRow key={session.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      {getDeviceIcon(session.deviceType)}
                      <div>
                        <p className="font-medium">{session.deviceName}</p>
                        <p className="text-sm text-gray-500">
                          {session.browser} on {session.operatingSystem}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm">{session.location.city}, {session.location.country}</p>
                        <p className="text-xs text-gray-500">{session.ipAddress}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">
                        {new Date(session.loginTime).toLocaleString()}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">
                      {new Date(session.lastActivity).toLocaleString()}
                    </span>
                  </TableCell>
                  <TableCell>
                    {getSessionStatus(session)}
                  </TableCell>
                  <TableCell>
                    {!session.isCurrent && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => terminateSession(session.id)}
                      >
                        <LogOut className="h-4 w-4 mr-1" />
                        Terminate
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Security Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Security Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Shield className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <p className="font-medium">Use Secure Connections</p>
                <p className="text-sm text-gray-600">
                  Always access your account from secure (HTTPS) connections
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <LogOut className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <p className="font-medium">Terminate Unused Sessions</p>
                <p className="text-sm text-gray-600">
                  Regularly terminate sessions from devices you no longer use
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
              <div>
                <p className="font-medium">Monitor Suspicious Activity</p>
                <p className="text-sm text-gray-600">
                  Report any unrecognized sessions or locations immediately
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
```

## 🔐 Two-Factor Authentication Component

### **2FA Setup Component**
```typescript
// components/shared/security/TwoFactorAuth.tsx
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/shared/notifications/useToast'
import { Shield, Smartphone, Key, CheckCircle, AlertTriangle } from 'lucide-react'
import QRCode from 'qrcode.react'

interface TwoFactorStatus {
  isEnabled: boolean
  backupCodes: string[]
  lastUsed?: string
  method: 'app' | 'sms' | null
}

export function TwoFactorAuth() {
  const [tfaStatus, setTfaStatus] = useState<TwoFactorStatus>({
    isEnabled: false,
    backupCodes: [],
    method: null
  })
  const [setupStep, setSetupStep] = useState<'initial' | 'qr' | 'verify' | 'backup'>('initial')
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('')
  const [verificationCode, setVerificationCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const toast = useToast()

  const initiate2FASetup = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/2fa/setup', {
        method: 'POST'
      })
      
      if (response.ok) {
        const data = await response.json()
        setQrCodeUrl(data.qrCodeUrl)
        setSetupStep('qr')
      } else {
        throw new Error('Failed to initiate 2FA setup')
      }
    } catch (error) {
      toast.error('Setup Failed', 'Unable to initiate 2FA setup')
    } finally {
      setIsLoading(false)
    }
  }

  const verify2FASetup = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      toast.error('Invalid Code', 'Please enter a 6-digit verification code')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/2fa/verify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: verificationCode })
      })
      
      if (response.ok) {
        const data = await response.json()
        setTfaStatus({
          isEnabled: true,
          backupCodes: data.backupCodes,
          method: 'app'
        })
        setSetupStep('backup')
        toast.success('2FA Enabled', 'Two-factor authentication has been successfully enabled')
      } else {
        throw new Error('Invalid verification code')
      }
    } catch (error) {
      toast.error('Verification Failed', 'Invalid verification code')
    } finally {
      setIsLoading(false)
    }
  }

  const disable2FA = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/2fa/disable', {
        method: 'POST'
      })
      
      if (response.ok) {
        setTfaStatus({
          isEnabled: false,
          backupCodes: [],
          method: null
        })
        setSetupStep('initial')
        toast.warning('2FA Disabled', 'Two-factor authentication has been disabled')
      } else {
        throw new Error('Failed to disable 2FA')
      }
    } catch (error) {
      toast.error('Disable Failed', 'Unable to disable 2FA')
    } finally {
      setIsLoading(false)
    }
  }

  const generateNewBackupCodes = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/2fa/backup-codes', {
        method: 'POST'
      })
      
      if (response.ok) {
        const data = await response.json()
        setTfaStatus(prev => ({
          ...prev,
          backupCodes: data.backupCodes
        }))
        toast.success('Backup Codes Generated', 'New backup codes have been generated')
      } else {
        throw new Error('Failed to generate backup codes')
      }
    } catch (error) {
      toast.error('Generation Failed', 'Unable to generate new backup codes')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Two-Factor Authentication</h2>
        <p className="text-gray-600">Add an extra layer of security to your account</p>
      </div>

      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Current Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {tfaStatus.isEnabled ? (
                <CheckCircle className="h-6 w-6 text-green-500" />
              ) : (
                <AlertTriangle className="h-6 w-6 text-yellow-500" />
              )}
              <div>
                <p className="font-medium">
                  {tfaStatus.isEnabled ? 'Two-Factor Authentication Enabled' : 'Two-Factor Authentication Disabled'}
                </p>
                <p className="text-sm text-gray-600">
                  {tfaStatus.isEnabled 
                    ? `Using ${tfaStatus.method === 'app' ? 'Authenticator App' : 'SMS'}`
                    : 'Your account is not protected by 2FA'
                  }
                </p>
              </div>
            </div>
            <Badge variant={tfaStatus.isEnabled ? 'success' : 'warning'}>
              {tfaStatus.isEnabled ? 'Enabled' : 'Disabled'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Setup/Management */}
      {!tfaStatus.isEnabled ? (
        <Card>
          <CardHeader>
            <CardTitle>Enable Two-Factor Authentication</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {setupStep === 'initial' && (
              <div className="space-y-4">
                <Alert>
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    Two-factor authentication adds an extra layer of security to your account by requiring a verification code from your mobile device.
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Smartphone className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Download an Authenticator App</p>
                      <p className="text-sm text-gray-600">
                        Install Google Authenticator, Authy, or similar app on your mobile device
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Key className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Scan QR Code</p>
                      <p className="text-sm text-gray-600">
                        Use your authenticator app to scan the QR code we'll provide
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-purple-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Verify Setup</p>
                      <p className="text-sm text-gray-600">
                        Enter the verification code to complete setup
                      </p>
                    </div>
                  </div>
                </div>

                <Button onClick={initiate2FASetup} disabled={isLoading}>
                  {isLoading ? 'Setting up...' : 'Start Setup'}
                </Button>
              </div>
            )}

            {setupStep === 'qr' && (
              <div className="space-y-4">
                <div className="text-center">
                  <p className="font-medium mb-4">Scan this QR code with your authenticator app:</p>
                  <div className="inline-block p-4 bg-white border rounded-lg">
                    <QRCode value={qrCodeUrl} size={200} />
                  </div>
                  <p className="text-sm text-gray-600 mt-4">
                    Can't scan? Enter this code manually: <code className="bg-gray-100 px-2 py-1 rounded">{qrCodeUrl}</code>
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="verificationCode">Enter verification code from your app:</Label>
                  <Input
                    id="verificationCode"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    placeholder="000000"
                    maxLength={6}
                    className="text-center text-lg tracking-widest"
                  />
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" onClick={() => setSetupStep('initial')}>
                    Back
                  </Button>
                  <Button onClick={verify2FASetup} disabled={isLoading || verificationCode.length !== 6}>
                    {isLoading ? 'Verifying...' : 'Verify & Enable'}
                  </Button>
                </div>
              </div>
            )}

            {setupStep === 'backup' && (
              <div className="space-y-4">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>2FA Successfully Enabled!</strong> Save these backup codes in a secure location. You can use them to access your account if you lose your authenticator device.
                  </AlertDescription>
                </Alert>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="font-medium mb-2">Backup Codes:</p>
                  <div className="grid grid-cols-2 gap-2 font-mono text-sm">
                    {tfaStatus.backupCodes.map((code, index) => (
                      <div key={index} className="bg-white p-2 rounded border">
                        {code}
                      </div>
                    ))}
                  </div>
                </div>

                <Alert variant="warning">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Important:</strong> Each backup code can only be used once. Store them securely and don't share them with anyone.
                  </AlertDescription>
                </Alert>

                <Button onClick={() => setSetupStep('initial')}>
                  Complete Setup
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* Backup Codes Management */}
          <Card>
            <CardHeader>
              <CardTitle>Backup Codes</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">
                You have {tfaStatus.backupCodes.length} unused backup codes remaining.
              </p>
              
              <div className="flex space-x-2">
                <Button variant="outline" onClick={generateNewBackupCodes} disabled={isLoading}>
                  Generate New Codes
                </Button>
                <Button variant="outline">
                  Download Codes
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Disable 2FA */}
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Disable Two-Factor Authentication</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Disabling 2FA will make your account less secure. Only disable if you're having issues with your authenticator app.
                </AlertDescription>
              </Alert>
              
              <Button variant="destructive" onClick={disable2FA} disabled={isLoading}>
                {isLoading ? 'Disabling...' : 'Disable 2FA'}
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
```
