# ⚙️ Phase 4: Settings & System Management

## 📋 Overview
Phase 4 focuses on comprehensive settings and system management across all three panels: Super Admin, Institute Admin, and Student Portal. This includes system settings, domain management, session controls, and user preferences.

### 🎯 Objectives
- ✅ Implement comprehensive settings for all panels
- ✅ Create domain name request system for institutes
- ✅ Build session management and security controls
- ✅ Develop system-wide configuration management
- ✅ Create user preference and customization options

### ⏱️ Timeline
**Duration**: 4 weeks (20 working days)
**Team Size**: 3-4 developers

## 🔧 Super Admin Settings Panel

### **System Settings Overview**
```
Super Admin Settings:
├── 🌐 Platform Settings
│   ├── General Configuration
│   ├── Email & SMTP Settings
│   ├── Storage & CDN Settings
│   └── API & Integration Settings
├── 🏢 Institute Management
│   ├── Registration Approval Settings
│   ├── Domain Request Management
│   ├── Commission & Billing Settings
│   └── Institute Limits & Quotas
├── 👥 User Management
│   ├── User Registration Settings
│   ├── Authentication Settings
│   ├── Session Management
│   └── Security Policies
├── 💰 Billing & Revenue
│   ├── Payment Gateway Configuration
│   ├── Commission Structure
│   ├── Billing Cycles
│   └── Revenue Analytics Settings
├── 🎨 Theme Management
│   ├── Platform Theme Settings
│   ├── Theme Upload & Approval
│   ├── Theme Marketplace
│   └── Custom CSS/JS Injection
└── 🔒 Security & Compliance
    ├── SSL Certificate Management
    ├── Backup & Recovery Settings
    ├── Audit Logs Configuration
    └── GDPR & Privacy Settings
```

### **1. Platform Settings**
**Route**: `apps/super-admin/src/app/settings/platform/page.tsx`

```typescript
'use client'

import { useState } from 'react'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/shared/notifications/useToast'

const platformSettingsSchema = Yup.object({
  platformName: Yup.string().required('Platform name is required'),
  platformUrl: Yup.string().url('Invalid URL').required('Platform URL is required'),
  supportEmail: Yup.string().email('Invalid email').required('Support email is required'),
  maintenanceMode: Yup.boolean(),
  allowRegistration: Yup.boolean(),
  requireEmailVerification: Yup.boolean(),
  maxInstitutesPerPlan: Yup.number().min(1).required(),
  sessionTimeout: Yup.number().min(5).max(1440).required(),
  maxLoginAttempts: Yup.number().min(3).max(10).required()
})

export default function PlatformSettingsPage() {
  const toast = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (values: any) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/admin/settings/platform', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values)
      })

      if (response.ok) {
        toast.updateSuccess('Platform Settings')
      } else {
        throw new Error('Failed to update settings')
      }
    } catch (error) {
      toast.saveError('platform settings')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Platform Settings</h1>
        <p className="text-gray-600">Configure global platform settings and policies</p>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="storage">Storage</TabsTrigger>
        </TabsList>

        <Formik
          initialValues={{
            platformName: 'Groups Exam LMS',
            platformUrl: 'https://groups-exam.com',
            supportEmail: '<EMAIL>',
            maintenanceMode: false,
            allowRegistration: true,
            requireEmailVerification: true,
            maxInstitutesPerPlan: 1000,
            sessionTimeout: 60,
            maxLoginAttempts: 5
          }}
          validationSchema={platformSettingsSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, values, setFieldValue }) => (
            <Form className="space-y-6">
              <TabsContent value="general" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>General Configuration</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="platformName">Platform Name</Label>
                        <Field
                          as={Input}
                          id="platformName"
                          name="platformName"
                          placeholder="Groups Exam LMS"
                        />
                        {errors.platformName && touched.platformName && (
                          <p className="text-sm text-destructive">{errors.platformName}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="platformUrl">Platform URL</Label>
                        <Field
                          as={Input}
                          id="platformUrl"
                          name="platformUrl"
                          placeholder="https://groups-exam.com"
                        />
                        {errors.platformUrl && touched.platformUrl && (
                          <p className="text-sm text-destructive">{errors.platformUrl}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="supportEmail">Support Email</Label>
                      <Field
                        as={Input}
                        id="supportEmail"
                        name="supportEmail"
                        type="email"
                        placeholder="<EMAIL>"
                      />
                      {errors.supportEmail && touched.supportEmail && (
                        <p className="text-sm text-destructive">{errors.supportEmail}</p>
                      )}
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Maintenance Mode</Label>
                          <p className="text-sm text-gray-500">
                            Enable to put the platform in maintenance mode
                          </p>
                        </div>
                        <Switch
                          checked={values.maintenanceMode}
                          onCheckedChange={(checked) => setFieldValue('maintenanceMode', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Allow Institute Registration</Label>
                          <p className="text-sm text-gray-500">
                            Allow new institutes to register on the platform
                          </p>
                        </div>
                        <Switch
                          checked={values.allowRegistration}
                          onCheckedChange={(checked) => setFieldValue('allowRegistration', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Require Email Verification</Label>
                          <p className="text-sm text-gray-500">
                            Require email verification for new user accounts
                          </p>
                        </div>
                        <Switch
                          checked={values.requireEmailVerification}
                          onCheckedChange={(checked) => setFieldValue('requireEmailVerification', checked)}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="security" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Security Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                        <Field
                          as={Input}
                          id="sessionTimeout"
                          name="sessionTimeout"
                          type="number"
                          min="5"
                          max="1440"
                        />
                        {errors.sessionTimeout && touched.sessionTimeout && (
                          <p className="text-sm text-destructive">{errors.sessionTimeout}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
                        <Field
                          as={Input}
                          id="maxLoginAttempts"
                          name="maxLoginAttempts"
                          type="number"
                          min="3"
                          max="10"
                        />
                        {errors.maxLoginAttempts && touched.maxLoginAttempts && (
                          <p className="text-sm text-destructive">{errors.maxLoginAttempts}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline">
                  Reset to Defaults
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? 'Saving...' : 'Save Settings'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </Tabs>
    </div>
  )
}
```

### **2. Domain Request Management**
**Route**: `apps/super-admin/src/app/settings/domains/page.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useToast } from '@/components/shared/notifications/useToast'
import { CheckCircle, XCircle, Clock, ExternalLink } from 'lucide-react'

interface DomainRequest {
  id: string
  instituteName: string
  requestedDomain: string
  currentDomain: string
  status: 'pending' | 'approved' | 'rejected' | 'active'
  requestedAt: string
  requestedBy: string
  notes?: string
  sslStatus?: 'pending' | 'active' | 'failed'
}

export default function DomainManagementPage() {
  const [domainRequests, setDomainRequests] = useState<DomainRequest[]>([])
  const [selectedRequest, setSelectedRequest] = useState<DomainRequest | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const toast = useToast()

  useEffect(() => {
    fetchDomainRequests()
  }, [])

  const fetchDomainRequests = async () => {
    try {
      const response = await fetch('/api/admin/domain-requests')
      const data = await response.json()
      setDomainRequests(data.requests)
    } catch (error) {
      toast.error('Failed to fetch domain requests')
    } finally {
      setIsLoading(false)
    }
  }

  const handleApproveRequest = async (requestId: string) => {
    try {
      const response = await fetch(`/api/admin/domain-requests/${requestId}/approve`, {
        method: 'POST'
      })

      if (response.ok) {
        toast.success('Domain Approved', 'Domain request has been approved and SSL setup initiated.')
        fetchDomainRequests()
      } else {
        throw new Error('Failed to approve domain')
      }
    } catch (error) {
      toast.error('Approval Failed', 'Unable to approve domain request.')
    }
  }

  const handleRejectRequest = async (requestId: string, reason: string) => {
    try {
      const response = await fetch(`/api/admin/domain-requests/${requestId}/reject`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reason })
      })

      if (response.ok) {
        toast.warning('Domain Rejected', 'Domain request has been rejected.')
        fetchDomainRequests()
      } else {
        throw new Error('Failed to reject domain')
      }
    } catch (error) {
      toast.error('Rejection Failed', 'Unable to reject domain request.')
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'warning',
      approved: 'success',
      rejected: 'destructive',
      active: 'success'
    }
    return <Badge variant={variants[status as keyof typeof variants]}>{status}</Badge>
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'approved':
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Domain Management</h1>
          <p className="text-gray-600">Manage custom domain requests from institutes</p>
        </div>
        <Button>
          <ExternalLink className="h-4 w-4 mr-2" />
          DNS Configuration Guide
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Requests</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {domainRequests.filter(r => r.status === 'pending').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Domains</p>
                <p className="text-2xl font-bold text-green-600">
                  {domainRequests.filter(r => r.status === 'active').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approved</p>
                <p className="text-2xl font-bold text-blue-600">
                  {domainRequests.filter(r => r.status === 'approved').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rejected</p>
                <p className="text-2xl font-bold text-red-600">
                  {domainRequests.filter(r => r.status === 'rejected').length}
                </p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Domain Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle>Domain Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Institute</TableHead>
                <TableHead>Requested Domain</TableHead>
                <TableHead>Current Domain</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>SSL Status</TableHead>
                <TableHead>Requested Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {domainRequests.map((request) => (
                <TableRow key={request.id}>
                  <TableCell className="font-medium">{request.instituteName}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span>{request.requestedDomain}</span>
                      {request.status === 'active' && (
                        <ExternalLink className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-gray-500">{request.currentDomain}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(request.status)}
                      {getStatusBadge(request.status)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {request.sslStatus && getStatusBadge(request.sslStatus)}
                  </TableCell>
                  <TableCell>{new Date(request.requestedAt).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {request.status === 'pending' && (
                        <>
                          <Button
                            size="sm"
                            onClick={() => handleApproveRequest(request.id)}
                          >
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedRequest(request)}
                          >
                            Reject
                          </Button>
                        </>
                      )}
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button size="sm" variant="ghost">
                            View Details
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Domain Request Details</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label>Institute Name</Label>
                              <p className="text-sm text-gray-600">{request.instituteName}</p>
                            </div>
                            <div>
                              <Label>Requested Domain</Label>
                              <p className="text-sm text-gray-600">{request.requestedDomain}</p>
                            </div>
                            <div>
                              <Label>Requested By</Label>
                              <p className="text-sm text-gray-600">{request.requestedBy}</p>
                            </div>
                            {request.notes && (
                              <div>
                                <Label>Notes</Label>
                                <p className="text-sm text-gray-600">{request.notes}</p>
                              </div>
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
```

## 🏫 Institute Admin Settings Panel

### **Institute Settings Overview**
```
Institute Admin Settings:
├── 🏢 Institute Profile
│   ├── Basic Information
│   ├── Branding & Logo
│   ├── Contact Information
│   └── Social Media Links
├── 🌐 Domain & Website
│   ├── Custom Domain Request
│   ├── Domain Verification Status
│   ├── SSL Certificate Status
│   └── Website Configuration
├── 👥 User Management
│   ├── Staff & Trainer Management
│   ├── Student Registration Settings
│   ├── Role & Permission Settings
│   └── Bulk User Import/Export
├── 💰 Billing & Payments
│   ├── Payment Gateway Setup
│   ├── Commission Settings View
│   ├── Billing History
│   └── Invoice Management
├── 📚 Course Settings
│   ├── Course Categories
│   ├── Pricing Templates
│   ├── Certificate Templates
│   └── Course Approval Workflow
├── 🎨 Theme & Branding
│   ├── Theme Selection
│   ├── Color Customization
│   ├── Logo & Images
│   └── Custom CSS
├── 📧 Communication
│   ├── Email Templates
│   ├── SMS Settings
│   ├── Notification Preferences
│   └── Announcement System
└── 🔒 Security & Privacy
    ├── Session Management
    ├── Two-Factor Authentication
    ├── Data Export/Import
    └── Privacy Settings
```

### **1. Domain Request Settings**
**Route**: `apps/institute-admin/src/app/settings/domain/page.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/components/shared/notifications/useToast'
import { CheckCircle, Clock, XCircle, ExternalLink, Copy } from 'lucide-react'

const domainRequestSchema = Yup.object({
  domainName: Yup.string()
    .matches(
      /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/,
      'Invalid domain format (e.g., myinstitute.com)'
    )
    .required('Domain name is required'),
  purpose: Yup.string().required('Purpose is required'),
  notes: Yup.string().max(500, 'Notes must be less than 500 characters')
})

interface DomainStatus {
  currentDomain: string
  customDomain?: string
  status: 'none' | 'pending' | 'approved' | 'active' | 'rejected'
  requestedAt?: string
  approvedAt?: string
  rejectionReason?: string
  sslStatus?: 'pending' | 'active' | 'failed'
  dnsRecords?: {
    type: string
    name: string
    value: string
    ttl: number
  }[]
}

export default function DomainSettingsPage() {
  const [domainStatus, setDomainStatus] = useState<DomainStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const toast = useToast()

  useEffect(() => {
    fetchDomainStatus()
  }, [])

  const fetchDomainStatus = async () => {
    try {
      const response = await fetch('/api/institute/domain-status')
      const data = await response.json()
      setDomainStatus(data.domain)
    } catch (error) {
      toast.error('Failed to fetch domain status')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDomainRequest = async (values: any) => {
    setIsSubmitting(true)
    try {
      const response = await fetch('/api/institute/domain-request', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values)
      })

      if (response.ok) {
        toast.success('Domain Request Submitted', 'Your custom domain request has been submitted for review.')
        fetchDomainStatus()
      } else {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to submit domain request')
      }
    } catch (error) {
      toast.error('Request Failed', (error as Error).message)
    } finally {
      setIsSubmitting(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied', 'DNS record copied to clipboard')
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      none: 'secondary',
      pending: 'warning',
      approved: 'info',
      active: 'success',
      rejected: 'destructive'
    }
    const labels = {
      none: 'No Custom Domain',
      pending: 'Pending Review',
      approved: 'Approved - Setup Required',
      active: 'Active',
      rejected: 'Rejected'
    }
    return <Badge variant={variants[status as keyof typeof variants]}>
      {labels[status as keyof typeof labels]}
    </Badge>
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-blue-500" />
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return null
    }
  }

  if (isLoading) {
    return <div>Loading domain settings...</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Domain & Website Settings</h1>
        <p className="text-gray-600">Manage your custom domain and website configuration</p>
      </div>

      {/* Current Domain Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>Current Domain Status</span>
            {domainStatus && getStatusIcon(domainStatus.status)}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>Current Domain</Label>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                  {domainStatus?.currentDomain}
                </span>
                <Button size="sm" variant="ghost">
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {domainStatus?.customDomain && (
              <div>
                <Label>Custom Domain</Label>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                    {domainStatus.customDomain}
                  </span>
                  {domainStatus.status === 'active' && (
                    <Button size="sm" variant="ghost">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>

          <div>
            <Label>Status</Label>
            <div className="flex items-center space-x-2">
              {domainStatus && getStatusBadge(domainStatus.status)}
              {domainStatus?.sslStatus && (
                <Badge variant={domainStatus.sslStatus === 'active' ? 'success' : 'warning'}>
                  SSL: {domainStatus.sslStatus}
                </Badge>
              )}
            </div>
          </div>

          {domainStatus?.status === 'rejected' && domainStatus.rejectionReason && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Rejection Reason:</strong> {domainStatus.rejectionReason}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* DNS Configuration (if approved) */}
      {domainStatus?.status === 'approved' && domainStatus.dnsRecords && (
        <Card>
          <CardHeader>
            <CardTitle>DNS Configuration Required</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Your domain has been approved! Please configure the following DNS records with your domain provider:
              </AlertDescription>
            </Alert>

            <div className="mt-4 space-y-3">
              {domainStatus.dnsRecords.map((record, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                    <div>
                      <Label className="text-xs">Type</Label>
                      <p className="font-mono text-sm">{record.type}</p>
                    </div>
                    <div>
                      <Label className="text-xs">Name</Label>
                      <p className="font-mono text-sm">{record.name}</p>
                    </div>
                    <div>
                      <Label className="text-xs">Value</Label>
                      <p className="font-mono text-sm break-all">{record.value}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div>
                        <Label className="text-xs">TTL</Label>
                        <p className="font-mono text-sm">{record.ttl}</p>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(record.value)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4">
              <Button onClick={fetchDomainStatus}>
                Check DNS Propagation
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Domain Request Form */}
      {(!domainStatus || domainStatus.status === 'none' || domainStatus.status === 'rejected') && (
        <Card>
          <CardHeader>
            <CardTitle>Request Custom Domain</CardTitle>
          </CardHeader>
          <CardContent>
            <Formik
              initialValues={{
                domainName: '',
                purpose: '',
                notes: ''
              }}
              validationSchema={domainRequestSchema}
              onSubmit={handleDomainRequest}
            >
              {({ errors, touched }) => (
                <Form className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="domainName">Domain Name</Label>
                    <Field
                      as={Input}
                      id="domainName"
                      name="domainName"
                      placeholder="myinstitute.com"
                      className={errors.domainName && touched.domainName ? 'border-destructive' : ''}
                    />
                    {errors.domainName && touched.domainName && (
                      <p className="text-sm text-destructive">{errors.domainName}</p>
                    )}
                    <p className="text-xs text-gray-500">
                      Enter your domain name without http:// or www (e.g., myinstitute.com)
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="purpose">Purpose</Label>
                    <Field
                      as={Input}
                      id="purpose"
                      name="purpose"
                      placeholder="Professional branding for our institute"
                      className={errors.purpose && touched.purpose ? 'border-destructive' : ''}
                    />
                    {errors.purpose && touched.purpose && (
                      <p className="text-sm text-destructive">{errors.purpose}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Additional Notes (Optional)</Label>
                    <Field
                      as={Textarea}
                      id="notes"
                      name="notes"
                      placeholder="Any additional information about your domain request"
                      rows={3}
                      className={errors.notes && touched.notes ? 'border-destructive' : ''}
                    />
                    {errors.notes && touched.notes && (
                      <p className="text-sm text-destructive">{errors.notes}</p>
                    )}
                  </div>

                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Requirements:</strong>
                      <ul className="list-disc list-inside mt-2 space-y-1">
                        <li>You must own the domain name</li>
                        <li>Domain must be properly registered and active</li>
                        <li>You'll need to configure DNS records after approval</li>
                        <li>SSL certificate will be automatically provisioned</li>
                      </ul>
                    </AlertDescription>
                  </Alert>

                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? 'Submitting Request...' : 'Submit Domain Request'}
                  </Button>
                </Form>
              )}
            </Formik>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
```

## 🎓 Student Settings Panel

### **Student Settings Overview**
```
Student Settings:
├── 👤 Profile Management
│   ├── Personal Information
│   ├── Profile Picture
│   ├── Contact Details
│   └── Emergency Contact
├── 🔒 Account Security
│   ├── Password Change
│   ├── Two-Factor Authentication
│   ├── Login History
│   └── Active Sessions
├── 🔔 Notification Preferences
│   ├── Email Notifications
│   ├── SMS Notifications
│   ├── Push Notifications
│   └── Course Updates
├── 📚 Learning Preferences
│   ├── Language Settings
│   ├── Timezone Settings
│   ├── Video Quality Preferences
│   └── Subtitle Preferences
├── 💳 Billing & Payments
│   ├── Payment Methods
│   ├── Purchase History
│   ├── Invoices & Receipts
│   └── Refund Requests
├── 📊 Privacy & Data
│   ├── Data Export
│   ├── Privacy Settings
│   ├── Cookie Preferences
│   └── Account Deletion
└── 🎯 Goals & Progress
    ├── Learning Goals
    ├── Study Schedule
    ├── Progress Tracking
    └── Achievement Badges
```

### **1. Profile Management**
**Route**: `apps/student/src/app/settings/profile/page.tsx`

```typescript
'use client'

import { useState, useRef } from 'react'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useToast } from '@/components/shared/notifications/useToast'
import { Camera, Upload } from 'lucide-react'

const profileSchema = Yup.object({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  phone: Yup.string().matches(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/, 'Invalid phone number'),
  dateOfBirth: Yup.date().max(new Date(), 'Date cannot be in future'),
  gender: Yup.string().oneOf(['male', 'female', 'other']),
  bio: Yup.string().max(500, 'Bio must be less than 500 characters'),
  address: Yup.string().max(200, 'Address must be less than 200 characters'),
  city: Yup.string(),
  state: Yup.string(),
  country: Yup.string(),
  zipCode: Yup.string()
})

export default function ProfileSettingsPage() {
  const [profileImage, setProfileImage] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const toast = useToast()

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (file.size > 5 * 1024 * 1024) {
      toast.error('File too large', 'Please select an image smaller than 5MB')
      return
    }

    setIsUploading(true)
    try {
      const formData = new FormData()
      formData.append('image', file)

      const response = await fetch('/api/student/profile/upload-image', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        setProfileImage(data.imageUrl)
        toast.success('Image uploaded successfully')
      } else {
        throw new Error('Failed to upload image')
      }
    } catch (error) {
      toast.error('Upload failed', 'Unable to upload profile image')
    } finally {
      setIsUploading(false)
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      const response = await fetch('/api/student/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...values, profileImage })
      })

      if (response.ok) {
        toast.updateSuccess('Profile')
      } else {
        throw new Error('Failed to update profile')
      }
    } catch (error) {
      toast.saveError('profile')
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Profile Settings</h1>
        <p className="text-gray-600">Manage your personal information and preferences</p>
      </div>

      <Formik
        initialValues={{
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          dateOfBirth: '',
          gender: '',
          bio: '',
          address: '',
          city: '',
          state: '',
          country: '',
          zipCode: ''
        }}
        validationSchema={profileSchema}
        onSubmit={handleSubmit}
      >
        {({ errors, touched, values, setFieldValue }) => (
          <Form className="space-y-6">
            {/* Profile Picture */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Picture</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-6">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src={profileImage || undefined} />
                    <AvatarFallback className="text-lg">
                      {values.firstName?.[0]}{values.lastName?.[0]}
                    </AvatarFallback>
                  </Avatar>

                  <div className="space-y-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isUploading}
                    >
                      <Camera className="h-4 w-4 mr-2" />
                      {isUploading ? 'Uploading...' : 'Change Picture'}
                    </Button>
                    <p className="text-xs text-gray-500">
                      JPG, PNG or GIF. Max size 5MB.
                    </p>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Field
                      as={Input}
                      id="firstName"
                      name="firstName"
                      className={errors.firstName && touched.firstName ? 'border-destructive' : ''}
                    />
                    {errors.firstName && touched.firstName && (
                      <p className="text-sm text-destructive">{errors.firstName}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Field
                      as={Input}
                      id="lastName"
                      name="lastName"
                      className={errors.lastName && touched.lastName ? 'border-destructive' : ''}
                    />
                    {errors.lastName && touched.lastName && (
                      <p className="text-sm text-destructive">{errors.lastName}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Field
                      as={Input}
                      id="email"
                      name="email"
                      type="email"
                      className={errors.email && touched.email ? 'border-destructive' : ''}
                    />
                    {errors.email && touched.email && (
                      <p className="text-sm text-destructive">{errors.email}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Field
                      as={Input}
                      id="phone"
                      name="phone"
                      className={errors.phone && touched.phone ? 'border-destructive' : ''}
                    />
                    {errors.phone && touched.phone && (
                      <p className="text-sm text-destructive">{errors.phone}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth">Date of Birth</Label>
                    <Field
                      as={Input}
                      id="dateOfBirth"
                      name="dateOfBirth"
                      type="date"
                      className={errors.dateOfBirth && touched.dateOfBirth ? 'border-destructive' : ''}
                    />
                    {errors.dateOfBirth && touched.dateOfBirth && (
                      <p className="text-sm text-destructive">{errors.dateOfBirth}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="gender">Gender</Label>
                    <Field name="gender">
                      {({ field }: any) => (
                        <Select onValueChange={(value) => setFieldValue('gender', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="male">Male</SelectItem>
                            <SelectItem value="female">Female</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </Field>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bio">Bio</Label>
                  <Field
                    as={Textarea}
                    id="bio"
                    name="bio"
                    placeholder="Tell us about yourself..."
                    rows={3}
                    className={errors.bio && touched.bio ? 'border-destructive' : ''}
                  />
                  {errors.bio && touched.bio && (
                    <p className="text-sm text-destructive">{errors.bio}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Address Information */}
            <Card>
              <CardHeader>
                <CardTitle>Address Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Field
                    as={Input}
                    id="address"
                    name="address"
                    placeholder="Street address"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Field as={Input} id="city" name="city" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="state">State/Province</Label>
                    <Field as={Input} id="state" name="state" />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="country">Country</Label>
                    <Field as={Input} id="country" name="country" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="zipCode">ZIP/Postal Code</Label>
                    <Field as={Input} id="zipCode" name="zipCode" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline">
                Cancel
              </Button>
              <Button type="submit">
                Save Changes
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  )
}
```

## 🔧 Phase 4 Backend Implementation

### **Sessions Collection**
**File**: `apps/api/src/collections/Sessions.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdminOrSelf } from '../access/index'

const Sessions: CollectionConfig = {
  slug: 'sessions',
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['user', 'deviceType', 'ipAddress', 'isActive', 'createdAt'],
  },
  access: {
    read: isAdminOrSelf,
    create: () => true, // Sessions are created automatically
    update: isAdminOrSelf,
    delete: isAdminOrSelf,
  },
  fields: [
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      index: true,
    },
    {
      name: 'sessionToken',
      type: 'text',
      required: true,
      unique: true,
      index: true,
      admin: { hidden: true },
    },
    {
      name: 'deviceInfo',
      type: 'group',
      fields: [
        {
          name: 'deviceType',
          type: 'select',
          required: true,
          options: [
            { label: 'Desktop', value: 'desktop' },
            { label: 'Mobile', value: 'mobile' },
            { label: 'Tablet', value: 'tablet' },
          ],
        },
        {
          name: 'deviceName',
          type: 'text',
          required: true,
        },
        {
          name: 'browser',
          type: 'text',
          required: true,
        },
        {
          name: 'operatingSystem',
          type: 'text',
          required: true,
        },
        {
          name: 'userAgent',
          type: 'text',
          admin: { hidden: true },
        },
      ],
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'ipAddress',
          type: 'text',
          required: true,
          index: true,
        },
        {
          name: 'city',
          type: 'text',
        },
        {
          name: 'region',
          type: 'text',
        },
        {
          name: 'country',
          type: 'text',
        },
        {
          name: 'latitude',
          type: 'number',
        },
        {
          name: 'longitude',
          type: 'number',
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'isSecure',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'lastActivity',
      type: 'date',
      required: true,
      index: true,
    },
    {
      name: 'expiresAt',
      type: 'date',
      required: true,
      index: true,
    },
    {
      name: 'loginMethod',
      type: 'select',
      options: [
        { label: 'Password', value: 'password' },
        { label: 'Two Factor', value: '2fa' },
        { label: 'Social Login', value: 'social' },
        { label: 'SSO', value: 'sso' },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Set expiration time (2 hours from now)
          const expirationTime = new Date()
          expirationTime.setHours(expirationTime.getHours() + 2)
          data.expiresAt = expirationTime
          data.lastActivity = new Date()
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default Sessions
```

### **Settings Collection**
**File**: `apps/api/src/collections/Settings.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const Settings: CollectionConfig = {
  slug: 'settings',
  admin: {
    useAsTitle: 'key',
    defaultColumns: ['key', 'category', 'updatedAt'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.userType === 'super_admin') return true
      // Institute admins can only read their own settings
      if (user?.userType === 'institute_admin') {
        return {
          or: [
            { category: { equals: 'public' } },
            { institute: { equals: user.institute } },
          ],
        }
      }
      return { category: { equals: 'public' } }
    },
    create: isAdmin,
    update: ({ req: { user } }) => {
      if (user?.userType === 'super_admin') return true
      if (user?.userType === 'institute_admin') {
        return { institute: { equals: user.institute } }
      }
      return false
    },
    delete: isAdmin,
  },
  fields: [
    {
      name: 'key',
      type: 'text',
      required: true,
      unique: true,
      index: true,
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Platform', value: 'platform' },
        { label: 'Institute', value: 'institute' },
        { label: 'Security', value: 'security' },
        { label: 'Email', value: 'email' },
        { label: 'Payment', value: 'payment' },
        { label: 'Theme', value: 'theme' },
        { label: 'Public', value: 'public' },
      ],
      index: true,
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      admin: {
        condition: (data) => data.category === 'institute',
      },
    },
    {
      name: 'value',
      type: 'json',
      required: true,
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'String', value: 'string' },
        { label: 'Number', value: 'number' },
        { label: 'Boolean', value: 'boolean' },
        { label: 'Object', value: 'object' },
        { label: 'Array', value: 'array' },
      ],
    },
    {
      name: 'description',
      type: 'textarea',
    },
    {
      name: 'isPublic',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'isEditable',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'validation',
      type: 'group',
      fields: [
        {
          name: 'required',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'minLength',
          type: 'number',
        },
        {
          name: 'maxLength',
          type: 'number',
        },
        {
          name: 'min',
          type: 'number',
        },
        {
          name: 'max',
          type: 'number',
        },
        {
          name: 'pattern',
          type: 'text',
        },
      ],
    },
  ],
  timestamps: true,
}

export default Settings
```

### **Domain Requests Collection**
**File**: `apps/api/src/collections/DomainRequests.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin, isInstituteAdmin } from '../access/index'

const DomainRequests: CollectionConfig = {
  slug: 'domain-requests',
  admin: {
    useAsTitle: 'requestedDomain',
    defaultColumns: ['requestedDomain', 'institute', 'status', 'createdAt'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.userType === 'super_admin') return true
      if (user?.userType === 'institute_admin') {
        return { institute: { equals: user.institute } }
      }
      return false
    },
    create: isInstituteAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      index: true,
    },
    {
      name: 'requestedBy',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'requestedDomain',
      type: 'text',
      required: true,
      index: true,
      validate: (val) => {
        if (!/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/.test(val)) {
          return 'Invalid domain format'
        }
        return true
      },
    },
    {
      name: 'currentDomain',
      type: 'text',
      required: true,
    },
    {
      name: 'purpose',
      type: 'text',
      required: true,
      maxLength: 200,
    },
    {
      name: 'notes',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'pending',
      options: [
        { label: 'Pending Review', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Active', value: 'active' },
        { label: 'Rejected', value: 'rejected' },
      ],
      index: true,
    },
    {
      name: 'reviewedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        condition: (data) => data.status !== 'pending',
      },
    },
    {
      name: 'reviewedAt',
      type: 'date',
      admin: {
        condition: (data) => data.status !== 'pending',
      },
    },
    {
      name: 'rejectionReason',
      type: 'textarea',
      admin: {
        condition: (data) => data.status === 'rejected',
      },
    },
    {
      name: 'dnsRecords',
      type: 'array',
      admin: {
        condition: (data) => data.status === 'approved',
      },
      fields: [
        {
          name: 'type',
          type: 'select',
          required: true,
          options: [
            { label: 'A', value: 'A' },
            { label: 'CNAME', value: 'CNAME' },
            { label: 'TXT', value: 'TXT' },
          ],
        },
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'value',
          type: 'text',
          required: true,
        },
        {
          name: 'ttl',
          type: 'number',
          defaultValue: 300,
        },
      ],
    },
    {
      name: 'sslStatus',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Active', value: 'active' },
        { label: 'Failed', value: 'failed' },
      ],
      admin: {
        condition: (data) => data.status === 'approved' || data.status === 'active',
      },
    },
    {
      name: 'activatedAt',
      type: 'date',
      admin: {
        condition: (data) => data.status === 'active',
      },
    },
  ],
  hooks: {
    afterChange: [
      ({ req, doc, operation }) => {
        if (operation === 'update' && doc.status === 'approved') {
          // Generate DNS records
          // Send approval notification
        }
        if (operation === 'update' && doc.status === 'rejected') {
          // Send rejection notification
        }
      },
    ],
  ],
  timestamps: true,
}

export default DomainRequests
```

### **Cross-Domain Session Management**
**File**: `apps/api/src/endpoints/sessions/index.ts`

```typescript
import { Endpoint } from 'payload/config'
import jwt from 'jsonwebtoken'

const sessionEndpoints: Endpoint[] = [
  // Get user sessions (cross-domain compatible)
  {
    path: '/sessions/user',
    method: 'get',
    handler: async (req, res) => {
      try {
        // Extract token from Authorization header (cross-domain)
        const authHeader = req.headers.authorization
        const token = authHeader?.replace('Bearer ', '')

        if (!token) {
          return res.status(401).json({
            error: 'Authentication token required'
          })
        }

        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
        const userId = decoded.userId

        // Get user sessions
        const sessions = await req.payload.find({
          collection: 'sessions',
          where: {
            and: [
              { user: { equals: userId } },
              { isActive: { equals: true } },
              { expiresAt: { greater_than: new Date() } }
            ]
          },
          sort: '-lastActivity'
        })

        res.json({
          success: true,
          sessions: sessions.docs.map(session => ({
            id: session.id,
            deviceInfo: session.deviceInfo,
            location: session.location,
            lastActivity: session.lastActivity,
            isSecure: session.isSecure,
            loginMethod: session.loginMethod,
            isCurrent: session.sessionToken === token
          }))
        })

      } catch (error) {
        console.error('Sessions fetch error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  },

  // Terminate session (cross-domain)
  {
    path: '/sessions/:id/terminate',
    method: 'delete',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const authHeader = req.headers.authorization
        const token = authHeader?.replace('Bearer ', '')

        if (!token) {
          return res.status(401).json({
            error: 'Authentication token required'
          })
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
        const userId = decoded.userId

        // Verify session belongs to user
        const session = await req.payload.findByID({
          collection: 'sessions',
          id
        })

        if (!session || session.user !== userId) {
          return res.status(403).json({
            error: 'Access denied'
          })
        }

        // Deactivate session
        await req.payload.update({
          collection: 'sessions',
          id,
          data: {
            isActive: false
          }
        })

        res.json({
          success: true,
          message: 'Session terminated successfully'
        })

      } catch (error) {
        console.error('Session termination error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  },

  // Logout from all devices (cross-domain)
  {
    path: '/sessions/logout-all',
    method: 'post',
    handler: async (req, res) => {
      try {
        const authHeader = req.headers.authorization
        const token = authHeader?.replace('Bearer ', '')

        if (!token) {
          return res.status(401).json({
            error: 'Authentication token required'
          })
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
        const userId = decoded.userId

        // Deactivate all user sessions
        const sessions = await req.payload.find({
          collection: 'sessions',
          where: {
            user: { equals: userId }
          }
        })

        const updatePromises = sessions.docs.map(session =>
          req.payload.update({
            collection: 'sessions',
            id: session.id,
            data: { isActive: false }
          })
        )

        await Promise.all(updatePromises)

        res.json({
          success: true,
          message: 'Logged out from all devices',
          terminatedSessions: sessions.docs.length
        })

      } catch (error) {
        console.error('Logout all error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  }
]

export default sessionEndpoints
```

### **Frontend Session Management**
**File**: `components/shared/session/SessionManager.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { authService } from '../auth/AuthService'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Monitor, Smartphone, Tablet, MapPin, Clock } from 'lucide-react'

interface Session {
  id: string
  deviceInfo: {
    deviceType: 'desktop' | 'mobile' | 'tablet'
    deviceName: string
    browser: string
    operatingSystem: string
  }
  location: {
    ipAddress: string
    city?: string
    region?: string
    country?: string
  }
  lastActivity: string
  isSecure: boolean
  loginMethod: string
  isCurrent: boolean
}

export function SessionManager() {
  const [sessions, setSessions] = useState<Session[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchSessions()
  }, [])

  const fetchSessions = async () => {
    try {
      setIsLoading(true)
      const response = await authService.apiCall('/sessions/user')
      const data = await response.json()

      if (data.success) {
        setSessions(data.sessions)
      }
    } catch (error) {
      console.error('Failed to fetch sessions:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const terminateSession = async (sessionId: string) => {
    try {
      const response = await authService.apiCall(`/sessions/${sessionId}/terminate`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setSessions(prev => prev.filter(s => s.id !== sessionId))
      }
    } catch (error) {
      console.error('Failed to terminate session:', error)
    }
  }

  const logoutAllDevices = async () => {
    try {
      const response = await authService.apiCall('/sessions/logout-all', {
        method: 'POST'
      })

      if (response.ok) {
        // Redirect to login as all sessions are terminated
        await authService.logout()
        window.location.href = '/auth/login'
      }
    } catch (error) {
      console.error('Failed to logout from all devices:', error)
    }
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile': return <Smartphone className="h-4 w-4" />
      case 'tablet': return <Tablet className="h-4 w-4" />
      default: return <Monitor className="h-4 w-4" />
    }
  }

  if (isLoading) {
    return <div>Loading sessions...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Active Sessions</h2>
        <Button
          variant="destructive"
          onClick={logoutAllDevices}
          disabled={sessions.length <= 1}
        >
          Logout All Devices
        </Button>
      </div>

      <div className="grid gap-4">
        {sessions.map((session) => (
          <Card key={session.id} className={session.isCurrent ? 'border-primary' : ''}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getDeviceIcon(session.deviceInfo.deviceType)}
                  <CardTitle className="text-lg">
                    {session.deviceInfo.deviceName}
                  </CardTitle>
                  {session.isCurrent && (
                    <Badge variant="default">Current Session</Badge>
                  )}
                </div>
                {!session.isCurrent && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => terminateSession(session.id)}
                  >
                    Terminate
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Monitor className="h-3 w-3" />
                <span>{session.deviceInfo.browser} on {session.deviceInfo.operatingSystem}</span>
              </div>

              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <span>
                  {session.location.city && session.location.country
                    ? `${session.location.city}, ${session.location.country}`
                    : session.location.ipAddress
                  }
                </span>
              </div>

              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>Last active: {new Date(session.lastActivity).toLocaleString()}</span>
              </div>

              <div className="flex space-x-2">
                <Badge variant={session.isSecure ? 'default' : 'secondary'}>
                  {session.isSecure ? 'Secure' : 'Standard'}
                </Badge>
                <Badge variant="outline">
                  {session.loginMethod}
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
```

## 🎯 Phase 4 Success Criteria

### **Functional Requirements**
- [ ] ✅ All settings pages are fully functional
- [ ] ✅ Domain request system works end-to-end
- [ ] ✅ Session management is operational
- [ ] ✅ Two-factor authentication works
- [ ] ✅ All forms validate properly
- [ ] ✅ File uploads work correctly
- [ ] ✅ Email notifications are sent

### **Backend Requirements**
- [ ] ✅ Sessions collection is fully functional
- [ ] ✅ Settings collection is implemented
- [ ] ✅ Domain requests collection works
- [ ] ✅ Settings management endpoints functional
- [ ] ✅ Session tracking and management
- [ ] ✅ Domain approval workflow works
- [ ] ✅ Two-factor authentication backend

### **Technical Requirements**
- [ ] ✅ All tests pass (unit, integration, E2E)
- [ ] ✅ Security vulnerabilities addressed
- [ ] ✅ Performance meets requirements
- [ ] ✅ Code follows established standards
- [ ] ✅ Documentation is complete
- [ ] ✅ API documentation updated

### **User Experience Requirements**
- [ ] ✅ Intuitive settings navigation
- [ ] ✅ Clear feedback for all actions
- [ ] ✅ Responsive design works well
- [ ] ✅ Accessibility standards met
- [ ] ✅ Error handling is user-friendly
- [ ] ✅ Help documentation available

## 🔄 **Complete CRUD API Integration**

### **Settings CRUD Endpoints**
**File**: `apps/api/src/endpoints/settings/crud.ts`

```typescript
import { Endpoint } from 'payload/config'

const settingsCrudEndpoints: Endpoint[] = [
  // Get Settings
  {
    path: '/settings',
    method: 'get',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const { category = '', scope = '' } = req.query

        // Build where clause
        const where: any = {}

        // Category filter
        if (category) {
          where.category = { equals: category }
        }

        // Scope-based filtering
        if (currentUser.userType === 'institute_admin') {
          where.or = [
            { scope: { equals: 'global' } },
            { scope: { equals: 'institute' } },
            { 'institute': { equals: currentUser.institute } }
          ]
        } else if (currentUser.userType === 'branch_admin') {
          where.or = [
            { scope: { equals: 'global' } },
            { scope: { equals: 'institute' } },
            { scope: { equals: 'branch' } },
            { 'institute': { equals: currentUser.institute } },
            { 'branch': { equals: currentUser.branch } }
          ]
        } else if (currentUser.userType === 'super_admin') {
          // Super admin can see all settings
          if (scope) {
            where.scope = { equals: scope }
          }
        }

        const settings = await req.payload.find({
          collection: 'settings',
          where,
          sort: 'category',
          populate: ['institute', 'branch']
        })

        res.json({
          success: true,
          settings: settings.docs
        })

      } catch (error) {
        console.error('Get settings error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch settings'
        })
      }
    }
  },

  // Create/Update Setting
  {
    path: '/settings',
    method: 'post',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const { key, value, category, scope, description, dataType, institute, branch } = req.body

        if (!key || value === undefined || !category || !scope) {
          return res.status(400).json({
            error: 'Key, value, category, and scope are required'
          })
        }

        // Check permissions
        if (scope === 'global' && currentUser.userType !== 'super_admin') {
          return res.status(403).json({
            error: 'Only super admins can manage global settings'
          })
        }

        // Check if setting already exists
        const existingWhere: any = { key: { equals: key } }

        if (scope === 'institute' && institute) {
          existingWhere.institute = { equals: institute }
        } else if (scope === 'branch' && branch) {
          existingWhere.branch = { equals: branch }
        }

        const existingSetting = await req.payload.find({
          collection: 'settings',
          where: existingWhere,
          limit: 1
        })

        const settingData = {
          key,
          value,
          category,
          scope,
          description,
          dataType: dataType || 'string',
          institute: scope === 'institute' ? institute : undefined,
          branch: scope === 'branch' ? branch : undefined
        }

        let result
        if (existingSetting.totalDocs > 0) {
          // Update existing setting
          result = await req.payload.update({
            collection: 'settings',
            id: existingSetting.docs[0].id,
            data: settingData
          })
        } else {
          // Create new setting
          result = await req.payload.create({
            collection: 'settings',
            data: settingData
          })
        }

        res.json({
          success: true,
          setting: result,
          message: existingSetting.totalDocs > 0 ? 'Setting updated successfully' : 'Setting created successfully'
        })

      } catch (error) {
        console.error('Create/update setting error:', error)
        res.status(500).json({
          error: error.message || 'Failed to save setting'
        })
      }
    }
  }
]

export default settingsCrudEndpoints
```

## 🗄️ **Zustand State Management**

### **Settings Store**
**File**: `apps/super-admin/src/stores/useSettingsStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface Setting {
  id: string
  key: string
  value: any
  category: string
  scope: 'global' | 'institute' | 'branch'
  description?: string
  dataType: 'string' | 'number' | 'boolean' | 'json' | 'array'
  institute?: any
  branch?: any
  createdAt: string
  updatedAt: string
}

interface SettingsState {
  // Data
  settings: Setting[]
  settingsByCategory: Record<string, Setting[]>

  // UI State
  isLoading: boolean

  // Filters
  filters: {
    category: string
    scope: string
  }

  // Actions
  fetchSettings: (filters?: any) => Promise<void>
  createSetting: (data: any) => Promise<void>
  updateSetting: (key: string, value: any, scope?: string, institute?: string, branch?: string) => Promise<void>
  deleteSetting: (id: string) => Promise<void>

  getSetting: (key: string, scope?: string, institute?: string, branch?: string) => Setting | null
  getSettingValue: (key: string, defaultValue?: any, scope?: string, institute?: string, branch?: string) => any

  setFilters: (filters: any) => void
  clearFilters: () => void
}

export const useSettingsStore = create<SettingsState>()(
  devtools(
    (set, get) => ({
      // Initial state
      settings: [],
      settingsByCategory: {},

      isLoading: false,

      filters: {
        category: '',
        scope: ''
      },

      // Fetch settings
      fetchSettings: async (filters = {}) => {
        set({ isLoading: true })
        try {
          const currentFilters = { ...get().filters, ...filters }
          const params = new URLSearchParams(
            Object.fromEntries(
              Object.entries(currentFilters).filter(([_, value]) => value !== '')
            )
          )

          const response = await fetch(`/api/settings?${params}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch settings')
          }

          const data = await response.json()

          // Group settings by category
          const settingsByCategory = data.settings.reduce((acc: Record<string, Setting[]>, setting: Setting) => {
            if (!acc[setting.category]) {
              acc[setting.category] = []
            }
            acc[setting.category].push(setting)
            return acc
          }, {})

          set({
            settings: data.settings,
            settingsByCategory
          })

        } catch (error) {
          console.error('Fetch settings error:', error)
          toast.error('Failed to fetch settings')
        } finally {
          set({ isLoading: false })
        }
      },

      // Create setting
      createSetting: async (data: any) => {
        try {
          const response = await fetch('/api/settings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to create setting')
          }

          const result = await response.json()

          // Refresh settings
          await get().fetchSettings()

          toast.success('Setting created successfully')
          return result

        } catch (error) {
          console.error('Create setting error:', error)
          toast.error(error.message || 'Failed to create setting')
          throw error
        }
      },

      // Update setting
      updateSetting: async (key: string, value: any, scope = 'global', institute?: string, branch?: string) => {
        try {
          const response = await fetch('/api/settings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
              key,
              value,
              scope,
              institute,
              branch
            })
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to update setting')
          }

          const result = await response.json()

          // Update setting in state
          set(state => ({
            settings: state.settings.map(setting =>
              setting.key === key &&
              setting.scope === scope &&
              setting.institute?.id === institute &&
              setting.branch?.id === branch
                ? { ...setting, value, ...result.setting }
                : setting
            )
          }))

          // Refresh grouped settings
          await get().fetchSettings()

          toast.success('Setting updated successfully')
          return result

        } catch (error) {
          console.error('Update setting error:', error)
          toast.error(error.message || 'Failed to update setting')
          throw error
        }
      },

      // Delete setting
      deleteSetting: async (id: string) => {
        try {
          const response = await fetch(`/api/settings/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to delete setting')
          }

          // Remove setting from state
          set(state => ({
            settings: state.settings.filter(setting => setting.id !== id)
          }))

          // Refresh grouped settings
          await get().fetchSettings()

          toast.success('Setting deleted successfully')

        } catch (error) {
          console.error('Delete setting error:', error)
          toast.error(error.message || 'Failed to delete setting')
          throw error
        }
      },

      // Get specific setting
      getSetting: (key: string, scope = 'global', institute?: string, branch?: string) => {
        const { settings } = get()
        return settings.find(setting =>
          setting.key === key &&
          setting.scope === scope &&
          (!institute || setting.institute?.id === institute) &&
          (!branch || setting.branch?.id === branch)
        ) || null
      },

      // Get setting value with fallback
      getSettingValue: (key: string, defaultValue: any = null, scope = 'global', institute?: string, branch?: string) => {
        const setting = get().getSetting(key, scope, institute, branch)
        if (setting) {
          // Parse value based on data type
          switch (setting.dataType) {
            case 'boolean':
              return setting.value === 'true' || setting.value === true
            case 'number':
              return Number(setting.value)
            case 'json':
              try {
                return JSON.parse(setting.value)
              } catch {
                return defaultValue
              }
            case 'array':
              return Array.isArray(setting.value) ? setting.value : []
            default:
              return setting.value
          }
        }
        return defaultValue
      },

      // Set filters
      setFilters: (filters: any) => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }))
      },

      // Clear filters
      clearFilters: () => {
        set({
          filters: {
            category: '',
            scope: ''
          }
        })
      }
    }),
    {
      name: 'settings-store'
    }
  )
)
```

## 📝 **Formik + Yup Forms**

### **Settings Form Component**
**File**: `apps/super-admin/src/components/settings/SettingsForm.tsx`

```typescript
'use client'

import { useState } from 'react'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { useSettingsStore } from '@/stores/useSettingsStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Loader2, Settings, Save } from 'lucide-react'
import { toast } from 'sonner'

// Validation Schema
const settingValidationSchema = Yup.object({
  key: Yup.string()
    .required('Setting key is required')
    .min(2, 'Key must be at least 2 characters')
    .max(100, 'Key must be less than 100 characters')
    .matches(/^[a-zA-Z0-9_.-]+$/, 'Key can only contain letters, numbers, underscores, dots, and hyphens'),
  value: Yup.mixed()
    .required('Setting value is required'),
  category: Yup.string()
    .required('Category is required')
    .oneOf(['system', 'email', 'payment', 'security', 'ui', 'api', 'notification'], 'Invalid category'),
  scope: Yup.string()
    .required('Scope is required')
    .oneOf(['global', 'institute', 'branch'], 'Invalid scope'),
  dataType: Yup.string()
    .required('Data type is required')
    .oneOf(['string', 'number', 'boolean', 'json', 'array'], 'Invalid data type'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  institute: Yup.string()
    .when('scope', {
      is: 'institute',
      then: () => Yup.string().required('Institute is required for institute scope'),
      otherwise: () => Yup.string()
    }),
  branch: Yup.string()
    .when('scope', {
      is: 'branch',
      then: () => Yup.string().required('Branch is required for branch scope'),
      otherwise: () => Yup.string()
    })
})

interface SettingsFormProps {
  initialData?: any
  onSubmit: (values: any) => Promise<void>
  onCancel: () => void
}

export function SettingsForm({ initialData, onSubmit, onCancel }: SettingsFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const initialValues = {
    key: initialData?.key || '',
    value: initialData?.value || '',
    category: initialData?.category || 'system',
    scope: initialData?.scope || 'global',
    dataType: initialData?.dataType || 'string',
    description: initialData?.description || '',
    institute: initialData?.institute?.id || '',
    branch: initialData?.branch?.id || ''
  }

  const handleSubmit = async (values: any) => {
    setIsSubmitting(true)
    try {
      // Process value based on data type
      let processedValue = values.value

      switch (values.dataType) {
        case 'number':
          processedValue = Number(values.value)
          break
        case 'boolean':
          processedValue = values.value === 'true' || values.value === true
          break
        case 'json':
          try {
            processedValue = JSON.parse(values.value)
          } catch (error) {
            throw new Error('Invalid JSON format')
          }
          break
        case 'array':
          if (typeof values.value === 'string') {
            processedValue = values.value.split(',').map(item => item.trim())
          }
          break
      }

      await onSubmit({
        ...values,
        value: processedValue
      })

      toast.success(initialData ? 'Setting updated successfully' : 'Setting created successfully')
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  const categoryOptions = [
    { value: 'system', label: 'System Settings' },
    { value: 'email', label: 'Email Configuration' },
    { value: 'payment', label: 'Payment Settings' },
    { value: 'security', label: 'Security Settings' },
    { value: 'ui', label: 'UI/UX Settings' },
    { value: 'api', label: 'API Configuration' },
    { value: 'notification', label: 'Notification Settings' }
  ]

  const scopeOptions = [
    { value: 'global', label: 'Global (Platform-wide)' },
    { value: 'institute', label: 'Institute Level' },
    { value: 'branch', label: 'Branch Level' }
  ]

  const dataTypeOptions = [
    { value: 'string', label: 'Text (String)' },
    { value: 'number', label: 'Number' },
    { value: 'boolean', label: 'True/False (Boolean)' },
    { value: 'json', label: 'JSON Object' },
    { value: 'array', label: 'Array (Comma-separated)' }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Settings className="h-5 w-5 mr-2" />
          {initialData ? 'Edit Setting' : 'Create New Setting'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Formik
          initialValues={initialValues}
          validationSchema={settingValidationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched }) => (
            <Form className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="key">Setting Key *</Label>
                  <Field
                    as={Input}
                    id="key"
                    name="key"
                    placeholder="e.g., max_file_size"
                    className={errors.key && touched.key ? 'border-red-500' : ''}
                  />
                  <p className="text-sm text-muted-foreground">
                    Unique identifier for this setting
                  </p>
                  <ErrorMessage name="key" component="div" className="text-sm text-red-500" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={values.category}
                    onValueChange={(value) => setFieldValue('category', value)}
                  >
                    <SelectTrigger className={errors.category && touched.category ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <ErrorMessage name="category" component="div" className="text-sm text-red-500" />
                </div>
              </div>

              {/* Scope and Data Type */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="scope">Scope *</Label>
                  <Select
                    value={values.scope}
                    onValueChange={(value) => setFieldValue('scope', value)}
                  >
                    <SelectTrigger className={errors.scope && touched.scope ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select scope" />
                    </SelectTrigger>
                    <SelectContent>
                      {scopeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <ErrorMessage name="scope" component="div" className="text-sm text-red-500" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dataType">Data Type *</Label>
                  <Select
                    value={values.dataType}
                    onValueChange={(value) => setFieldValue('dataType', value)}
                  >
                    <SelectTrigger className={errors.dataType && touched.dataType ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select data type" />
                    </SelectTrigger>
                    <SelectContent>
                      {dataTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <ErrorMessage name="dataType" component="div" className="text-sm text-red-500" />
                </div>
              </div>

              {/* Value Field */}
              <div className="space-y-2">
                <Label htmlFor="value">Setting Value *</Label>
                {values.dataType === 'boolean' ? (
                  <Select
                    value={values.value.toString()}
                    onValueChange={(value) => setFieldValue('value', value === 'true')}
                  >
                    <SelectTrigger className={errors.value && touched.value ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select value" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">True</SelectItem>
                      <SelectItem value="false">False</SelectItem>
                    </SelectContent>
                  </Select>
                ) : values.dataType === 'json' ? (
                  <Field
                    as={Textarea}
                    id="value"
                    name="value"
                    placeholder='{"key": "value"}'
                    rows={4}
                    className={errors.value && touched.value ? 'border-red-500' : ''}
                  />
                ) : (
                  <Field
                    as={Input}
                    id="value"
                    name="value"
                    type={values.dataType === 'number' ? 'number' : 'text'}
                    placeholder={
                      values.dataType === 'array'
                        ? 'item1, item2, item3'
                        : values.dataType === 'number'
                        ? '123'
                        : 'Enter value'
                    }
                    className={errors.value && touched.value ? 'border-red-500' : ''}
                  />
                )}
                {values.dataType === 'array' && (
                  <p className="text-sm text-muted-foreground">
                    Enter comma-separated values
                  </p>
                )}
                <ErrorMessage name="value" component="div" className="text-sm text-red-500" />
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Field
                  as={Textarea}
                  id="description"
                  name="description"
                  placeholder="Describe what this setting controls..."
                  rows={3}
                  className={errors.description && touched.description ? 'border-red-500' : ''}
                />
                <ErrorMessage name="description" component="div" className="text-sm text-red-500" />
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  <Save className="h-4 w-4 mr-2" />
                  {initialData ? 'Update Setting' : 'Create Setting'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
```

## 🎯 **Phase 4 Complete Integration Summary**

### **✅ CRUD Operations:**
```typescript
✅ Settings Management API:
├── GET /api/settings → List settings with scope filtering
├── POST /api/settings → Create/update setting
├── DELETE /api/settings/:id → Delete setting
└── Scope-based access control (global/institute/branch)
```

### **✅ Zustand State Management:**
```typescript
✅ Settings Store:
├── 📊 fetchSettings() → Load settings with category grouping
├── 📝 createSetting() → Create new setting
├── ✏️ updateSetting() → Update setting value
├── 🗑️ deleteSetting() → Remove setting
├── 🔍 getSetting() → Find specific setting
├── 💎 getSettingValue() → Get typed value with fallback
└── 🎯 Scope-aware filtering and access control
```

### **✅ Formik + Yup Forms:**
```typescript
✅ Settings Form:
├── 🔑 Key validation with format checking
├── 📊 Category selection with predefined options
├── 🎯 Scope selection (global/institute/branch)
├── 🔢 Data type selection with appropriate inputs
├── 💎 Value validation based on data type
├── 📝 Description field for documentation
└── 🔄 Dynamic form fields based on selections
```

**Perfect! Phase 4 Settings System is now complete with full CRUD operations, Zustand state management, Formik + Yup forms, and comprehensive scope-based access control! 🚀**
