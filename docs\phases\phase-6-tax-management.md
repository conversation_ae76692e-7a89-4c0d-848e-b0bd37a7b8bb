# 🧾 Phase 6: Tax Management System

## 📋 Overview
Phase 6 focuses on implementing a comprehensive tax management system for the Groups Exam LMS platform. This includes individual tax components, tax groups, location-based tax rules, and automated billing calculations for different geographical scenarios.

### 🎯 Objectives
- ✅ Implement individual tax components (SGST, CGST, IGST, VAT, etc.)
- ✅ Create tax groups with multiple tax combinations
- ✅ Build location-based tax rule engine
- ✅ Implement automated billing calculations
- ✅ Create tax management interfaces for Super Admin
- ✅ Build card view, list view, and advanced filtering
- ✅ Set up tax calculation APIs for billing

### ⏱️ Timeline
**Duration**: 3 weeks (15 working days)
**Team Size**: 2-3 developers

## 🏗️ Tax Management Architecture

### **Tax System Structure**
```
Tax Management Hierarchy:
├── 🏷️ Tax Components
│   ├── SGST (State Goods & Services Tax)
│   ├── CGST (Central Goods & Services Tax)
│   ├── IGST (Integrated Goods & Services Tax)
│   ├── VAT (Value Added Tax)
│   ├── Sales Tax, Income Tax, etc.
│   └── Custom Regional Taxes
├── 📦 Tax Groups
│   ├── GST 18% (CGST 9% + SGST 9%)
│   ├── GST 28% (CGST 14% + SGST 14%)
│   ├── International Tax Groups
│   └── Custom Combinations
├── 🌍 Tax Rules
│   ├── Location-based Rules
│   ├── Transaction Type Rules
│   ├── Entity Type Rules (B2B, B2C)
│   └── Product/Service Category Rules
└── 💰 Tax Calculations
    ├── Intra-state (Same State)
    ├── Inter-state (Different States)
    ├── International
    └── Special Economic Zones
```

### **Tax Scenarios Implementation**

## **Scenario 1: Intra-State Branch Billing (Same State)**
```
Branch Location: Tamil Nadu (Coimbatore)
Super Admin Location: Tamil Nadu (Chennai)
Tax Applied: CGST 9% + SGST 9% = 18%
Use Case: Tamil Nadu branch pays monthly bill to Super Admin in Tamil Nadu
```

## **Scenario 2: Inter-State Branch Billing (Different States)**
```
Branch Location: Kerala
Super Admin Location: Tamil Nadu
Tax Applied: IGST 18%
Use Case: Kerala branch pays monthly bill to Super Admin in Tamil Nadu
```

## **Scenario 3: International Branch Billing**
```
Branch Location: USA
Super Admin Location: Tamil Nadu, India
Tax Applied: Based on international tax rules (reverse charge/0%)
Use Case: USA branch pays monthly bill to Super Admin in India
```

## **Scenario 4: Student Course Purchase (Secondary)**
```
Student Location: Kerala
Branch Location: Tamil Nadu
Tax Applied: IGST 18% (Student location determines tax)
Use Case: Student in Kerala purchases course from Tamil Nadu branch
```

## 🔧 Phase 6 Backend Implementation

### **Tax Components Collection**
**File**: `apps/api/src/collections/TaxComponents.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const TaxComponents: CollectionConfig = {
  slug: 'tax-components',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'code', 'rate', 'type', 'isActive', 'createdAt'],
    group: 'Tax Management',
  },
  access: {
    read: () => true, // All users can read tax components
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      maxLength: 20,
      index: true,
      validate: (val) => {
        if (!/^[A-Z0-9_]+$/.test(val)) {
          return 'Tax code must contain only uppercase letters, numbers, and underscores'
        }
        return true
      },
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'SGST (State GST)', value: 'sgst' },
        { label: 'CGST (Central GST)', value: 'cgst' },
        { label: 'IGST (Integrated GST)', value: 'igst' },
        { label: 'VAT (Value Added Tax)', value: 'vat' },
        { label: 'Sales Tax', value: 'sales_tax' },
        { label: 'Income Tax', value: 'income_tax' },
        { label: 'Service Tax', value: 'service_tax' },
        { label: 'Custom Tax', value: 'custom' },
      ],
      index: true,
    },
    {
      name: 'rate',
      type: 'number',
      required: true,
      min: 0,
      max: 100,
      admin: {
        description: 'Tax rate as percentage (e.g., 9 for 9%)',
      },
    },
    {
      name: 'applicableRegions',
      type: 'array',
      fields: [
        {
          name: 'country',
          type: 'relationship',
          relationTo: 'countries',
          required: true,
        },
        {
          name: 'states',
          type: 'relationship',
          relationTo: 'states',
          hasMany: true,
        },
        {
          name: 'isDefault',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Default tax for this region',
          },
        },
      ],
    },
    {
      name: 'calculationMethod',
      type: 'select',
      required: true,
      defaultValue: 'percentage',
      options: [
        { label: 'Percentage', value: 'percentage' },
        { label: 'Fixed Amount', value: 'fixed' },
        { label: 'Tiered', value: 'tiered' },
      ],
    },
    {
      name: 'tieredRates',
      type: 'array',
      admin: {
        condition: (data) => data.calculationMethod === 'tiered',
      },
      fields: [
        {
          name: 'minAmount',
          type: 'number',
          required: true,
        },
        {
          name: 'maxAmount',
          type: 'number',
        },
        {
          name: 'rate',
          type: 'number',
          required: true,
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'effectiveFrom',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
    },
    {
      name: 'effectiveTo',
      type: 'date',
    },
    {
      name: 'priority',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Higher priority taxes are applied first',
      },
    },
    {
      name: 'metadata',
      type: 'json',
      admin: {
        description: 'Additional tax metadata and configuration',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create' || operation === 'update') {
          // Ensure tax code is uppercase
          if (data.code) {
            data.code = data.code.toUpperCase()
          }
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default TaxComponents
```

### **Tax Groups Collection**
**File**: `apps/api/src/collections/TaxGroups.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const TaxGroups: CollectionConfig = {
  slug: 'tax-groups',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'code', 'totalRate', 'componentsCount', 'isActive'],
    group: 'Tax Management',
  },
  access: {
    read: () => true,
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      maxLength: 20,
      index: true,
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'taxComponents',
      type: 'array',
      required: true,
      minRows: 1,
      fields: [
        {
          name: 'component',
          type: 'relationship',
          relationTo: 'tax-components',
          required: true,
        },
        {
          name: 'customRate',
          type: 'number',
          admin: {
            description: 'Override component rate (optional)',
          },
        },
        {
          name: 'isOptional',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
    },
    {
      name: 'totalRate',
      type: 'number',
      admin: {
        readOnly: true,
        description: 'Calculated total rate of all components',
      },
    },
    {
      name: 'applicableScenarios',
      type: 'array',
      fields: [
        {
          name: 'scenario',
          type: 'select',
          required: true,
          options: [
            { label: 'Intra-State (Same State)', value: 'intra_state' },
            { label: 'Inter-State (Different States)', value: 'inter_state' },
            { label: 'International', value: 'international' },
            { label: 'B2B Transaction', value: 'b2b' },
            { label: 'B2C Transaction', value: 'b2c' },
            { label: 'Export', value: 'export' },
            { label: 'Import', value: 'import' },
          ],
        },
        {
          name: 'fromCountry',
          type: 'relationship',
          relationTo: 'countries',
        },
        {
          name: 'toCountry',
          type: 'relationship',
          relationTo: 'countries',
        },
        {
          name: 'fromState',
          type: 'relationship',
          relationTo: 'states',
        },
        {
          name: 'toState',
          type: 'relationship',
          relationTo: 'states',
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'isDefault',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Default tax group for applicable scenarios',
      },
    },
    {
      name: 'effectiveFrom',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
    },
    {
      name: 'effectiveTo',
      type: 'date',
    },
    {
      name: 'priority',
      type: 'number',
      defaultValue: 0,
    },
  ],
  hooks: {
    beforeChange: [
      async ({ req, operation, data }) => {
        if (operation === 'create' || operation === 'update') {
          // Calculate total rate from components
          if (data.taxComponents && data.taxComponents.length > 0) {
            let totalRate = 0
            
            for (const componentData of data.taxComponents) {
              if (componentData.customRate) {
                totalRate += componentData.customRate
              } else if (componentData.component) {
                // Fetch component rate from database
                const component = await req.payload.findByID({
                  collection: 'tax-components',
                  id: componentData.component
                })
                if (component) {
                  totalRate += component.rate
                }
              }
            }
            
            data.totalRate = totalRate
          }
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default TaxGroups
```

### **Branches Collection (New for Branch-based Billing)**
**File**: `apps/api/src/collections/Branches.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin, isInstituteAdmin } from '../access/index'

const Branches: CollectionConfig = {
  slug: 'branches',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'institute', 'location', 'status', 'monthlyBill'],
    group: 'Institute Management',
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.userType === 'super_admin') return true
      if (user?.userType === 'institute_admin') {
        return { institute: { equals: user.institute } }
      }
      return false
    },
    create: isInstituteAdmin,
    update: isInstituteAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      maxLength: 20,
      index: true,
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      required: true,
      index: true,
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'country',
          type: 'relationship',
          relationTo: 'countries',
          required: true,
        },
        {
          name: 'state',
          type: 'relationship',
          relationTo: 'states',
          required: true,
        },
        {
          name: 'district',
          type: 'relationship',
          relationTo: 'districts',
        },
        {
          name: 'address',
          type: 'textarea',
          required: true,
        },
        {
          name: 'pincode',
          type: 'text',
          maxLength: 10,
        },
      ],
    },
    {
      name: 'contact',
      type: 'group',
      fields: [
        {
          name: 'email',
          type: 'email',
          required: true,
        },
        {
          name: 'phone',
          type: 'text',
          required: true,
        },
        {
          name: 'manager',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'billing',
      type: 'group',
      fields: [
        {
          name: 'monthlyBillAmount',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            description: 'Monthly bill amount before tax',
          },
        },
        {
          name: 'currency',
          type: 'select',
          required: true,
          defaultValue: 'INR',
          options: [
            { label: 'Indian Rupee (₹)', value: 'INR' },
            { label: 'US Dollar ($)', value: 'USD' },
            { label: 'Euro (€)', value: 'EUR' },
            { label: 'British Pound (£)', value: 'GBP' },
          ],
        },
        {
          name: 'billingCycle',
          type: 'select',
          required: true,
          defaultValue: 'monthly',
          options: [
            { label: 'Monthly', value: 'monthly' },
            { label: 'Quarterly', value: 'quarterly' },
            { label: 'Yearly', value: 'yearly' },
          ],
        },
        {
          name: 'nextBillingDate',
          type: 'date',
          required: true,
        },
        {
          name: 'lastBillingDate',
          type: 'date',
        },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'active',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
        { label: 'Suspended', value: 'suspended' },
        { label: 'Pending Setup', value: 'pending' },
      ],
      index: true,
    },
    {
      name: 'features',
      type: 'array',
      fields: [
        {
          name: 'feature',
          type: 'select',
          options: [
            { label: 'Course Management', value: 'courses' },
            { label: 'Student Management', value: 'students' },
            { label: 'Live Classes', value: 'live_classes' },
            { label: 'Exams & Assessments', value: 'exams' },
            { label: 'Reports & Analytics', value: 'reports' },
            { label: 'Custom Branding', value: 'branding' },
          ],
        },
        {
          name: 'isEnabled',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'limits',
      type: 'group',
      fields: [
        {
          name: 'maxStudents',
          type: 'number',
          defaultValue: 1000,
        },
        {
          name: 'maxCourses',
          type: 'number',
          defaultValue: 50,
        },
        {
          name: 'maxStorage',
          type: 'number',
          defaultValue: 10240, // 10GB in MB
          admin: {
            description: 'Storage limit in MB',
          },
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Set next billing date to next month if not provided
          if (!data.billing?.nextBillingDate) {
            const nextMonth = new Date()
            nextMonth.setMonth(nextMonth.getMonth() + 1)
            nextMonth.setDate(1) // First day of next month

            if (!data.billing) data.billing = {}
            data.billing.nextBillingDate = nextMonth
          }
        }
        return data
      },
    ],
  ],
  timestamps: true,
}

export default Branches
```

### **Tax Rules Collection**
**File**: `apps/api/src/collections/TaxRules.ts`

```typescript
import { CollectionConfig } from 'payload/types'
import { isAdmin } from '../access/index'

const TaxRules: CollectionConfig = {
  slug: 'tax-rules',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'ruleType', 'taxGroup', 'isActive', 'priority'],
    group: 'Tax Management',
  },
  access: {
    read: () => true,
    create: isAdmin,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      index: true,
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'ruleType',
      type: 'select',
      required: true,
      options: [
        { label: 'Location Based', value: 'location' },
        { label: 'Transaction Type', value: 'transaction' },
        { label: 'Entity Type', value: 'entity' },
        { label: 'Product Category', value: 'product' },
        { label: 'Amount Based', value: 'amount' },
        { label: 'Date Based', value: 'date' },
      ],
      index: true,
    },
    {
      name: 'conditions',
      type: 'group',
      fields: [
        // Location Conditions
        {
          name: 'fromCountry',
          type: 'relationship',
          relationTo: 'countries',
          admin: {
            condition: (data) => data.ruleType === 'location',
          },
        },
        {
          name: 'fromState',
          type: 'relationship',
          relationTo: 'states',
          admin: {
            condition: (data) => data.ruleType === 'location',
          },
        },
        {
          name: 'toCountry',
          type: 'relationship',
          relationTo: 'countries',
          admin: {
            condition: (data) => data.ruleType === 'location',
          },
        },
        {
          name: 'toState',
          type: 'relationship',
          relationTo: 'states',
          admin: {
            condition: (data) => data.ruleType === 'location',
          },
        },
        {
          name: 'isSameState',
          type: 'checkbox',
          admin: {
            condition: (data) => data.ruleType === 'location',
            description: 'Apply when from and to states are same',
          },
        },
        {
          name: 'isSameCountry',
          type: 'checkbox',
          admin: {
            condition: (data) => data.ruleType === 'location',
            description: 'Apply when from and to countries are same',
          },
        },

        // Transaction Type Conditions
        {
          name: 'transactionType',
          type: 'select',
          options: [
            { label: 'Branch Monthly Bill', value: 'branch_bill' },
            { label: 'Course Purchase', value: 'course_purchase' },
            { label: 'Subscription Payment', value: 'subscription' },
            { label: 'Platform Fee', value: 'platform_fee' },
            { label: 'Refund', value: 'refund' },
          ],
          admin: {
            condition: (data) => data.ruleType === 'transaction',
          },
        },

        // Entity Type Conditions
        {
          name: 'entityType',
          type: 'select',
          options: [
            { label: 'B2B (Business to Business)', value: 'b2b' },
            { label: 'B2C (Business to Consumer)', value: 'b2c' },
            { label: 'Individual', value: 'individual' },
            { label: 'Corporate', value: 'corporate' },
          ],
          admin: {
            condition: (data) => data.ruleType === 'entity',
          },
        },

        // Amount Based Conditions
        {
          name: 'minAmount',
          type: 'number',
          admin: {
            condition: (data) => data.ruleType === 'amount',
          },
        },
        {
          name: 'maxAmount',
          type: 'number',
          admin: {
            condition: (data) => data.ruleType === 'amount',
          },
        },
      ],
    },
    {
      name: 'taxGroup',
      type: 'relationship',
      relationTo: 'tax-groups',
      required: true,
    },
    {
      name: 'exemptions',
      type: 'array',
      fields: [
        {
          name: 'exemptionType',
          type: 'select',
          options: [
            { label: 'Full Exemption', value: 'full' },
            { label: 'Partial Exemption', value: 'partial' },
            { label: 'Rate Override', value: 'override' },
          ],
        },
        {
          name: 'exemptionRate',
          type: 'number',
          admin: {
            condition: (data, siblingData) =>
              siblingData.exemptionType === 'partial' ||
              siblingData.exemptionType === 'override',
          },
        },
        {
          name: 'reason',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      index: true,
    },
    {
      name: 'priority',
      type: 'number',
      required: true,
      defaultValue: 0,
      admin: {
        description: 'Higher priority rules are evaluated first',
      },
    },
    {
      name: 'effectiveFrom',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
    },
    {
      name: 'effectiveTo',
      type: 'date',
    },
  ],
  timestamps: true,
}

export default TaxRules
```

### **Tax Calculation Engine**
**File**: `apps/api/src/services/TaxCalculationService.ts`

```typescript
interface TaxCalculationRequest {
  amount: number
  fromLocation: {
    countryId: string
    stateId?: string
    districtId?: string
  }
  toLocation: {
    countryId: string
    stateId?: string
    districtId?: string
  }
  transactionType: 'branch_bill' | 'course_purchase' | 'subscription' | 'platform_fee'
  entityType: 'b2b' | 'b2c' | 'individual' | 'corporate'
  productCategory?: string
  date?: Date
}

interface TaxCalculationResult {
  baseAmount: number
  taxComponents: Array<{
    componentId: string
    name: string
    code: string
    rate: number
    amount: number
    type: string
  }>
  totalTaxAmount: number
  totalAmount: number
  appliedRules: Array<{
    ruleId: string
    ruleName: string
    taxGroupId: string
    taxGroupName: string
  }>
  scenario: 'intra_state' | 'inter_state' | 'international'
}

export class TaxCalculationService {
  constructor(private payload: any) {}

  async calculateTax(request: TaxCalculationRequest): Promise<TaxCalculationResult> {
    const { amount, fromLocation, toLocation, transactionType, entityType, date = new Date() } = request

    // Determine tax scenario
    const scenario = await this.determineTaxScenario(fromLocation, toLocation)

    // Find applicable tax rules
    const applicableRules = await this.findApplicableRules(
      fromLocation,
      toLocation,
      transactionType,
      entityType,
      amount,
      date
    )

    // Get tax group from highest priority rule
    const primaryRule = applicableRules[0]
    if (!primaryRule) {
      throw new Error('No applicable tax rules found')
    }

    // Get tax group details
    const taxGroup = await this.payload.findByID({
      collection: 'tax-groups',
      id: primaryRule.taxGroup,
      populate: ['taxComponents.component']
    })

    // Calculate tax components
    const taxComponents = []
    let totalTaxAmount = 0

    for (const groupComponent of taxGroup.taxComponents) {
      const component = groupComponent.component
      const rate = groupComponent.customRate || component.rate
      const taxAmount = (amount * rate) / 100

      taxComponents.push({
        componentId: component.id,
        name: component.name,
        code: component.code,
        rate: rate,
        amount: taxAmount,
        type: component.type
      })

      totalTaxAmount += taxAmount
    }

    return {
      baseAmount: amount,
      taxComponents,
      totalTaxAmount,
      totalAmount: amount + totalTaxAmount,
      appliedRules: applicableRules.map(rule => ({
        ruleId: rule.id,
        ruleName: rule.name,
        taxGroupId: rule.taxGroup,
        taxGroupName: taxGroup.name
      })),
      scenario
    }
  }

  private async determineTaxScenario(
    fromLocation: any,
    toLocation: any
  ): Promise<'intra_state' | 'inter_state' | 'international'> {
    // If different countries, it's international
    if (fromLocation.countryId !== toLocation.countryId) {
      return 'international'
    }

    // If same country but different states, it's inter-state
    if (fromLocation.stateId && toLocation.stateId &&
        fromLocation.stateId !== toLocation.stateId) {
      return 'inter_state'
    }

    // Same state or no state specified, it's intra-state
    return 'intra_state'
  }

  private async findApplicableRules(
    fromLocation: any,
    toLocation: any,
    transactionType: string,
    entityType: string,
    amount: number,
    date: Date
  ) {
    const scenario = await this.determineTaxScenario(fromLocation, toLocation)

    const rules = await this.payload.find({
      collection: 'tax-rules',
      where: {
        and: [
          { isActive: { equals: true } },
          { effectiveFrom: { less_than_equal: date } },
          {
            or: [
              { effectiveTo: { greater_than_equal: date } },
              { effectiveTo: { exists: false } }
            ]
          }
        ]
      },
      sort: '-priority',
      populate: ['taxGroup']
    })

    // Filter rules based on conditions
    const applicableRules = rules.docs.filter(rule => {
      return this.evaluateRuleConditions(rule, {
        fromLocation,
        toLocation,
        transactionType,
        entityType,
        amount,
        scenario
      })
    })

    return applicableRules
  }

  private evaluateRuleConditions(rule: any, context: any): boolean {
    const { conditions } = rule
    const { fromLocation, toLocation, transactionType, entityType, amount, scenario } = context

    switch (rule.ruleType) {
      case 'location':
        return this.evaluateLocationConditions(conditions, fromLocation, toLocation, scenario)

      case 'transaction':
        return conditions.transactionType === transactionType

      case 'entity':
        return conditions.entityType === entityType

      case 'amount':
        return this.evaluateAmountConditions(conditions, amount)

      default:
        return true
    }
  }

  private evaluateLocationConditions(conditions: any, fromLocation: any, toLocation: any, scenario: string): boolean {
    // Check same state condition
    if (conditions.isSameState && scenario !== 'intra_state') {
      return false
    }

    // Check same country condition
    if (conditions.isSameCountry && scenario === 'international') {
      return false
    }

    // Check specific country/state conditions
    if (conditions.fromCountry && conditions.fromCountry !== fromLocation.countryId) {
      return false
    }

    if (conditions.toCountry && conditions.toCountry !== toLocation.countryId) {
      return false
    }

    if (conditions.fromState && conditions.fromState !== fromLocation.stateId) {
      return false
    }

    if (conditions.toState && conditions.toState !== toLocation.stateId) {
      return false
    }

    return true
  }

  private evaluateAmountConditions(conditions: any, amount: number): boolean {
    if (conditions.minAmount && amount < conditions.minAmount) {
      return false
    }

    if (conditions.maxAmount && amount > conditions.maxAmount) {
      return false
    }

    return true
  }
}
```

### **Tax Management Endpoints**
**File**: `apps/api/src/endpoints/tax/index.ts`

```typescript
import { Endpoint } from 'payload/config'
import { TaxCalculationService } from '../../services/TaxCalculationService'

const taxEndpoints: Endpoint[] = [
  // Get tax components with filtering
  {
    path: '/tax/components',
    method: 'get',
    handler: async (req, res) => {
      try {
        const {
          search,
          type,
          isActive = 'true',
          country,
          page = 1,
          limit = 20
        } = req.query

        const where: any = {}

        if (isActive !== 'all') {
          where.isActive = { equals: isActive === 'true' }
        }

        if (type) {
          where.type = { equals: type }
        }

        if (search) {
          where.or = [
            { name: { contains: search } },
            { code: { contains: search } },
            { description: { contains: search } }
          ]
        }

        if (country) {
          where['applicableRegions.country'] = { equals: country }
        }

        const components = await req.payload.find({
          collection: 'tax-components',
          where,
          page: Number(page),
          limit: Number(limit),
          sort: 'name',
          populate: ['applicableRegions.country', 'applicableRegions.states']
        })

        res.json({
          success: true,
          components: components.docs,
          pagination: {
            page: components.page,
            limit: components.limit,
            totalPages: components.totalPages,
            totalDocs: components.totalDocs,
            hasNextPage: components.hasNextPage,
            hasPrevPage: components.hasPrevPage
          }
        })

      } catch (error) {
        console.error('Tax components fetch error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  },

  // Get tax groups with filtering
  {
    path: '/tax/groups',
    method: 'get',
    handler: async (req, res) => {
      try {
        const {
          search,
          scenario,
          isActive = 'true',
          page = 1,
          limit = 20
        } = req.query

        const where: any = {}

        if (isActive !== 'all') {
          where.isActive = { equals: isActive === 'true' }
        }

        if (search) {
          where.or = [
            { name: { contains: search } },
            { code: { contains: search } },
            { description: { contains: search } }
          ]
        }

        if (scenario) {
          where['applicableScenarios.scenario'] = { equals: scenario }
        }

        const groups = await req.payload.find({
          collection: 'tax-groups',
          where,
          page: Number(page),
          limit: Number(limit),
          sort: 'name',
          populate: ['taxComponents.component']
        })

        res.json({
          success: true,
          groups: groups.docs,
          pagination: {
            page: groups.page,
            limit: groups.limit,
            totalPages: groups.totalPages,
            totalDocs: groups.totalDocs,
            hasNextPage: groups.hasNextPage,
            hasPrevPage: groups.hasPrevPage
          }
        })

      } catch (error) {
        console.error('Tax groups fetch error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  },

  // Calculate tax for a transaction
  {
    path: '/tax/calculate',
    method: 'post',
    handler: async (req, res) => {
      try {
        const {
          amount,
          fromLocation,
          toLocation,
          transactionType,
          entityType,
          productCategory,
          date
        } = req.body

        if (!amount || !fromLocation || !toLocation || !transactionType || !entityType) {
          return res.status(400).json({
            error: 'Missing required fields for tax calculation'
          })
        }

        const taxService = new TaxCalculationService(req.payload)

        const result = await taxService.calculateTax({
          amount: Number(amount),
          fromLocation,
          toLocation,
          transactionType,
          entityType,
          productCategory,
          date: date ? new Date(date) : new Date()
        })

        res.json({
          success: true,
          calculation: result
        })

      } catch (error) {
        console.error('Tax calculation error:', error)
        res.status(500).json({
          error: error.message || 'Tax calculation failed'
        })
      }
    }
  },

  // Get tax rules with filtering
  {
    path: '/tax/rules',
    method: 'get',
    handler: async (req, res) => {
      try {
        const {
          search,
          ruleType,
          isActive = 'true',
          page = 1,
          limit = 20
        } = req.query

        const where: any = {}

        if (isActive !== 'all') {
          where.isActive = { equals: isActive === 'true' }
        }

        if (ruleType) {
          where.ruleType = { equals: ruleType }
        }

        if (search) {
          where.or = [
            { name: { contains: search } },
            { description: { contains: search } }
          ]
        }

        const rules = await req.payload.find({
          collection: 'tax-rules',
          where,
          page: Number(page),
          limit: Number(limit),
          sort: '-priority',
          populate: ['taxGroup', 'conditions.fromCountry', 'conditions.toCountry']
        })

        res.json({
          success: true,
          rules: rules.docs,
          pagination: {
            page: rules.page,
            limit: rules.limit,
            totalPages: rules.totalPages,
            totalDocs: rules.totalDocs,
            hasNextPage: rules.hasNextPage,
            hasPrevPage: rules.hasPrevPage
          }
        })

      } catch (error) {
        console.error('Tax rules fetch error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  },

  // Preview tax calculation for different scenarios
  {
    path: '/tax/preview',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { amount, scenarios } = req.body

        if (!amount || !scenarios || !Array.isArray(scenarios)) {
          return res.status(400).json({
            error: 'Amount and scenarios array are required'
          })
        }

        const taxService = new TaxCalculationService(req.payload)
        const results = []

        for (const scenario of scenarios) {
          try {
            const calculation = await taxService.calculateTax({
              amount: Number(amount),
              ...scenario
            })

            results.push({
              scenario: scenario.name || 'Unnamed Scenario',
              calculation,
              success: true
            })
          } catch (error) {
            results.push({
              scenario: scenario.name || 'Unnamed Scenario',
              error: error.message,
              success: false
            })
          }
        }

        res.json({
          success: true,
          previews: results
        })

      } catch (error) {
        console.error('Tax preview error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  },

  // Calculate branch monthly billing with tax
  {
    path: '/tax/branch-billing/:branchId',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { branchId } = req.params

        // Get branch details with location
        const branch = await req.payload.findByID({
          collection: 'branches',
          id: branchId,
          populate: ['location.country', 'location.state', 'institute']
        })

        if (!branch) {
          return res.status(404).json({
            error: 'Branch not found'
          })
        }

        // Get super admin location (platform location)
        const platformSettings = await req.payload.find({
          collection: 'settings',
          where: {
            key: { equals: 'platform_location' }
          },
          limit: 1
        })

        if (!platformSettings.docs.length) {
          return res.status(400).json({
            error: 'Platform location not configured'
          })
        }

        const platformLocation = platformSettings.docs[0].value

        // Calculate tax for branch billing
        const taxService = new TaxCalculationService(req.payload)

        const calculation = await taxService.calculateTax({
          amount: branch.billing.monthlyBillAmount,
          fromLocation: {
            countryId: branch.location.country.id,
            stateId: branch.location.state.id
          },
          toLocation: {
            countryId: platformLocation.countryId,
            stateId: platformLocation.stateId
          },
          transactionType: 'branch_bill',
          entityType: 'b2b'
        })

        res.json({
          success: true,
          branch: {
            id: branch.id,
            name: branch.name,
            location: branch.location,
            monthlyBillAmount: branch.billing.monthlyBillAmount,
            currency: branch.billing.currency
          },
          taxCalculation: calculation,
          billingDetails: {
            baseAmount: calculation.baseAmount,
            taxAmount: calculation.totalTaxAmount,
            totalAmount: calculation.totalAmount,
            currency: branch.billing.currency,
            nextBillingDate: branch.billing.nextBillingDate
          }
        })

      } catch (error) {
        console.error('Branch billing calculation error:', error)
        res.status(500).json({
          error: error.message || 'Branch billing calculation failed'
        })
      }
    }
  },

  // Get all branches billing summary
  {
    path: '/tax/branches-billing-summary',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { instituteId } = req.query

        const where: any = {
          isActive: { equals: true }
        }

        if (instituteId) {
          where.institute = { equals: instituteId }
        }

        // Get all active branches
        const branches = await req.payload.find({
          collection: 'branches',
          where,
          populate: ['location.country', 'location.state', 'institute'],
          limit: 1000
        })

        // Get platform location
        const platformSettings = await req.payload.find({
          collection: 'settings',
          where: {
            key: { equals: 'platform_location' }
          },
          limit: 1
        })

        if (!platformSettings.docs.length) {
          return res.status(400).json({
            error: 'Platform location not configured'
          })
        }

        const platformLocation = platformSettings.docs[0].value
        const taxService = new TaxCalculationService(req.payload)

        // Calculate tax for each branch
        const branchBillingSummary = await Promise.all(
          branches.docs.map(async (branch) => {
            try {
              const calculation = await taxService.calculateTax({
                amount: branch.billing.monthlyBillAmount,
                fromLocation: {
                  countryId: branch.location.country.id,
                  stateId: branch.location.state.id
                },
                toLocation: {
                  countryId: platformLocation.countryId,
                  stateId: platformLocation.stateId
                },
                transactionType: 'branch_bill',
                entityType: 'b2b'
              })

              return {
                branchId: branch.id,
                branchName: branch.name,
                location: `${branch.location.state.name}, ${branch.location.country.name}`,
                baseAmount: calculation.baseAmount,
                taxAmount: calculation.totalTaxAmount,
                totalAmount: calculation.totalAmount,
                currency: branch.billing.currency,
                taxScenario: calculation.scenario,
                appliedTaxes: calculation.taxComponents,
                nextBillingDate: branch.billing.nextBillingDate
              }
            } catch (error) {
              return {
                branchId: branch.id,
                branchName: branch.name,
                error: error.message,
                success: false
              }
            }
          })
        )

        // Calculate totals
        const summary = branchBillingSummary.reduce((acc, branch) => {
          if (!branch.error) {
            acc.totalBaseAmount += branch.baseAmount || 0
            acc.totalTaxAmount += branch.taxAmount || 0
            acc.totalBillAmount += branch.totalAmount || 0
            acc.successfulCalculations++
          } else {
            acc.failedCalculations++
          }
          return acc
        }, {
          totalBaseAmount: 0,
          totalTaxAmount: 0,
          totalBillAmount: 0,
          successfulCalculations: 0,
          failedCalculations: 0,
          totalBranches: branches.totalDocs
        })

        res.json({
          success: true,
          summary,
          branches: branchBillingSummary
        })

      } catch (error) {
        console.error('Branches billing summary error:', error)
        res.status(500).json({
          error: 'Internal server error'
        })
      }
    }
  }
]

export default taxEndpoints
```

## 🎨 Frontend Implementation

### **Zustand Tax Store**
**File**: `apps/super-admin/src/stores/useTaxStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface TaxComponent {
  id: string
  name: string
  code: string
  description?: string
  type: string
  rate: number
  applicableRegions: Array<{
    country: string
    states?: string[]
    isDefault: boolean
  }>
  calculationMethod: 'percentage' | 'fixed' | 'tiered'
  isActive: boolean
  effectiveFrom: string
  effectiveTo?: string
  priority: number
}

interface TaxGroup {
  id: string
  name: string
  code: string
  description?: string
  taxComponents: Array<{
    component: string | TaxComponent
    customRate?: number
    isOptional: boolean
  }>
  totalRate: number
  applicableScenarios: Array<{
    scenario: string
    fromCountry?: string
    toCountry?: string
    fromState?: string
    toState?: string
  }>
  isActive: boolean
  isDefault: boolean
  effectiveFrom: string
  effectiveTo?: string
  priority: number
}

interface TaxRule {
  id: string
  name: string
  description?: string
  ruleType: 'location' | 'transaction' | 'entity' | 'product' | 'amount' | 'date'
  conditions: any
  taxGroup: string | TaxGroup
  exemptions?: Array<{
    exemptionType: 'full' | 'partial' | 'override'
    exemptionRate?: number
    reason: string
  }>
  isActive: boolean
  priority: number
  effectiveFrom: string
  effectiveTo?: string
}

interface TaxCalculation {
  baseAmount: number
  taxComponents: Array<{
    componentId: string
    name: string
    code: string
    rate: number
    amount: number
    type: string
  }>
  totalTaxAmount: number
  totalAmount: number
  appliedRules: Array<{
    ruleId: string
    ruleName: string
    taxGroupId: string
    taxGroupName: string
  }>
  scenario: 'intra_state' | 'inter_state' | 'international'
}

interface TaxFilters {
  search: string
  type?: string
  scenario?: string
  ruleType?: string
  isActive: 'all' | 'true' | 'false'
  country?: string
}

interface Pagination {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface TaxState {
  // Data
  taxComponents: TaxComponent[]
  taxGroups: TaxGroup[]
  taxRules: TaxRule[]

  // UI State
  viewMode: 'list' | 'card'
  activeTab: 'components' | 'groups' | 'rules'
  isLoading: boolean
  error: string | null

  // Filters
  filters: TaxFilters

  // Pagination
  componentsPagination: Pagination
  groupsPagination: Pagination
  rulesPagination: Pagination

  // Tax Calculation
  calculationResult: TaxCalculation | null
  calculationLoading: boolean

  // Actions
  setViewMode: (mode: 'list' | 'card') => void
  setActiveTab: (tab: 'components' | 'groups' | 'rules') => void
  setFilters: (filters: Partial<TaxFilters>) => void

  // API Actions
  fetchTaxComponents: (page?: number) => Promise<void>
  fetchTaxGroups: (page?: number) => Promise<void>
  fetchTaxRules: (page?: number) => Promise<void>
  calculateTax: (request: any) => Promise<void>
  previewTaxCalculation: (amount: number, scenarios: any[]) => Promise<any>

  // CRUD Actions
  createTaxComponent: (data: Partial<TaxComponent>) => Promise<void>
  updateTaxComponent: (id: string, data: Partial<TaxComponent>) => Promise<void>
  deleteTaxComponent: (id: string) => Promise<void>

  createTaxGroup: (data: Partial<TaxGroup>) => Promise<void>
  updateTaxGroup: (id: string, data: Partial<TaxGroup>) => Promise<void>
  deleteTaxGroup: (id: string) => Promise<void>

  createTaxRule: (data: Partial<TaxRule>) => Promise<void>
  updateTaxRule: (id: string, data: Partial<TaxRule>) => Promise<void>
  deleteTaxRule: (id: string) => Promise<void>

  // Utility Actions
  clearError: () => void
  resetFilters: () => void
}

const initialFilters: TaxFilters = {
  search: '',
  isActive: 'true'
}

const initialPagination: Pagination = {
  page: 1,
  limit: 20,
  totalPages: 1,
  totalDocs: 0,
  hasNextPage: false,
  hasPrevPage: false
}

export const useTaxStore = create<TaxState>()(
  devtools(
    (set, get) => ({
      // Initial State
      taxComponents: [],
      taxGroups: [],
      taxRules: [],
      viewMode: 'list',
      activeTab: 'components',
      isLoading: false,
      error: null,
      filters: initialFilters,
      componentsPagination: initialPagination,
      groupsPagination: initialPagination,
      rulesPagination: initialPagination,
      calculationResult: null,
      calculationLoading: false,

      // UI Actions
      setViewMode: (mode) => set({ viewMode: mode }),
      setActiveTab: (tab) => set({ activeTab: tab }),
      setFilters: (newFilters) => set((state) => ({
        filters: { ...state.filters, ...newFilters }
      })),

      // API Actions
      fetchTaxComponents: async (page = 1) => {
        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            search: filters.search,
            isActive: filters.isActive,
            ...(filters.type && { type: filters.type }),
            ...(filters.country && { country: filters.country })
          })

          const response = await fetch(`/api/tax/components?${params}`)
          const data = await response.json()

          if (data.success) {
            set({
              taxComponents: data.components,
              componentsPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch tax components')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
        }
      },

      fetchTaxGroups: async (page = 1) => {
        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            search: filters.search,
            isActive: filters.isActive,
            ...(filters.scenario && { scenario: filters.scenario })
          })

          const response = await fetch(`/api/tax/groups?${params}`)
          const data = await response.json()

          if (data.success) {
            set({
              taxGroups: data.groups,
              groupsPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch tax groups')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
        }
      },

      fetchTaxRules: async (page = 1) => {
        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const params = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            search: filters.search,
            isActive: filters.isActive,
            ...(filters.ruleType && { ruleType: filters.ruleType })
          })

          const response = await fetch(`/api/tax/rules?${params}`)
          const data = await response.json()

          if (data.success) {
            set({
              taxRules: data.rules,
              rulesPagination: data.pagination,
              isLoading: false
            })
          } else {
            throw new Error(data.error || 'Failed to fetch tax rules')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            isLoading: false
          })
        }
      },

      calculateTax: async (request) => {
        set({ calculationLoading: true, error: null })
        try {
          const response = await fetch('/api/tax/calculate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request)
          })

          const data = await response.json()

          if (data.success) {
            set({
              calculationResult: data.calculation,
              calculationLoading: false
            })
          } else {
            throw new Error(data.error || 'Tax calculation failed')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            calculationLoading: false
          })
        }
      },

      previewTaxCalculation: async (amount, scenarios) => {
        try {
          const response = await fetch('/api/tax/preview', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ amount, scenarios })
          })

          const data = await response.json()
          return data.success ? data.previews : []
        } catch (error) {
          console.error('Tax preview error:', error)
          return []
        }
      },

      // CRUD Actions with API calls and toast notifications
      createTaxComponent: async (data) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/tax/components', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          })

          const result = await response.json()

          if (result.success) {
            // Refresh the list
            await get().fetchTaxComponents()
            // Show success toast
            toast.success('Tax component created successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to create tax component')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      updateTaxComponent: async (id, data) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/tax/components/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          })

          const result = await response.json()

          if (result.success) {
            // Refresh the list
            await get().fetchTaxComponents()
            toast.success('Tax component updated successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to update tax component')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      deleteTaxComponent: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/tax/components/${id}`, {
            method: 'DELETE'
          })

          const result = await response.json()

          if (result.success) {
            // Refresh the list
            await get().fetchTaxComponents()
            toast.success('Tax component deleted successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to delete tax component')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      createTaxGroup: async (data) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/tax/groups', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          })

          const result = await response.json()

          if (result.success) {
            await get().fetchTaxGroups()
            toast.success('Tax group created successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to create tax group')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      updateTaxGroup: async (id, data) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/tax/groups/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          })

          const result = await response.json()

          if (result.success) {
            await get().fetchTaxGroups()
            toast.success('Tax group updated successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to update tax group')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      deleteTaxGroup: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/tax/groups/${id}`, {
            method: 'DELETE'
          })

          const result = await response.json()

          if (result.success) {
            await get().fetchTaxGroups()
            toast.success('Tax group deleted successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to delete tax group')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      createTaxRule: async (data) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/tax/rules', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          })

          const result = await response.json()

          if (result.success) {
            await get().fetchTaxRules()
            toast.success('Tax rule created successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to create tax rule')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      updateTaxRule: async (id, data) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/tax/rules/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          })

          const result = await response.json()

          if (result.success) {
            await get().fetchTaxRules()
            toast.success('Tax rule updated successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to update tax rule')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      deleteTaxRule: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/tax/rules/${id}`, {
            method: 'DELETE'
          })

          const result = await response.json()

          if (result.success) {
            await get().fetchTaxRules()
            toast.success('Tax rule deleted successfully')
            set({ isLoading: false })
          } else {
            throw new Error(result.error || 'Failed to delete tax rule')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage, isLoading: false })
          toast.error(errorMessage)
          throw error
        }
      },

      // Utility Actions
      clearError: () => set({ error: null }),
      resetFilters: () => set({ filters: initialFilters })
    }),
    {
      name: 'tax-store'
    }
  )
)
```

### **Tax Management Main Component**
**File**: `apps/super-admin/src/app/tax-management/page.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { useTaxStore } from '@/stores/useTaxStore'
import { TaxTabs } from '@/components/tax/TaxTabs'
import { TaxFilters } from '@/components/tax/TaxFilters'
import { TaxComponentsList } from '@/components/tax/TaxComponentsList'
import { TaxGroupsList } from '@/components/tax/TaxGroupsList'
import { TaxRulesList } from '@/components/tax/TaxRulesList'
import { TaxCalculator } from '@/components/tax/TaxCalculator'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Plus, Calculator, Download, Upload } from 'lucide-react'

export default function TaxManagementPage() {
  const {
    activeTab,
    isLoading,
    error,
    setActiveTab,
    fetchTaxComponents,
    fetchTaxGroups,
    fetchTaxRules,
    clearError
  } = useTaxStore()

  useEffect(() => {
    // Load initial data based on active tab
    switch (activeTab) {
      case 'components':
        fetchTaxComponents()
        break
      case 'groups':
        fetchTaxGroups()
        break
      case 'rules':
        fetchTaxRules()
        break
    }
  }, [activeTab])

  const getTabTitle = () => {
    switch (activeTab) {
      case 'components':
        return 'Tax Components Management'
      case 'groups':
        return 'Tax Groups Management'
      case 'rules':
        return 'Tax Rules Management'
      default:
        return 'Tax Management'
    }
  }

  const getAddButtonText = () => {
    switch (activeTab) {
      case 'components': return 'Add Tax Component'
      case 'groups': return 'Add Tax Group'
      case 'rules': return 'Add Tax Rule'
      default: return 'Add'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Tax Management</h1>
          <p className="text-muted-foreground">
            Manage tax components, groups, and rules for automated billing
          </p>
        </div>

        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Calculator className="h-4 w-4 mr-2" />
            Tax Calculator
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            {getAddButtonText()}
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <p className="text-destructive">{error}</p>
              <Button variant="outline" size="sm" onClick={clearError}>
                Dismiss
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="components">Tax Components</TabsTrigger>
          <TabsTrigger value="groups">Tax Groups</TabsTrigger>
          <TabsTrigger value="rules">Tax Rules</TabsTrigger>
          <TabsTrigger value="calculator">Calculator</TabsTrigger>
        </TabsList>

        <TabsContent value="components" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Components Management</CardTitle>
              <p className="text-sm text-muted-foreground">
                Manage individual tax components like SGST, CGST, IGST, VAT, etc.
              </p>
            </CardHeader>
            <CardContent>
              <TaxFilters activeTab="components" />
              <div className="mt-6">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <TaxComponentsList />
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="groups" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Groups Management</CardTitle>
              <p className="text-sm text-muted-foreground">
                Manage tax groups that combine multiple tax components (e.g., GST 18% = CGST 9% + SGST 9%)
              </p>
            </CardHeader>
            <CardContent>
              <TaxFilters activeTab="groups" />
              <div className="mt-6">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <TaxGroupsList />
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rules" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Rules Management</CardTitle>
              <p className="text-sm text-muted-foreground">
                Manage location-based and scenario-based tax rules for automated calculations
              </p>
            </CardHeader>
            <CardContent>
              <TaxFilters activeTab="rules" />
              <div className="mt-6">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <TaxRulesList />
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calculator" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Calculator</CardTitle>
              <p className="text-sm text-muted-foreground">
                Calculate taxes for different scenarios and preview billing amounts
              </p>
            </CardHeader>
            <CardContent>
              <TaxCalculator />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
```

### **Enhanced Tax Components List Component**
**File**: `apps/super-admin/src/components/tax/TaxComponentsList.tsx`

```typescript
'use client'

import { useState } from 'react'
import { useTaxStore } from '@/stores/useTaxStore'
import { TaxComponentCard } from './TaxComponentCard'
import { TaxComponentListItem } from './TaxComponentListItem'
import { TaxComponentForm } from './TaxComponentForm'
import { TaxPagination } from './TaxPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { Receipt, Plus, Edit, Trash2 } from 'lucide-react'
import { toast } from 'sonner'

export function TaxComponentsList() {
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  const [editingComponent, setEditingComponent] = useState<any>(null)
  const [deletingComponent, setDeletingComponent] = useState<any>(null)

  const {
    taxComponents,
    viewMode,
    componentsPagination,
    isLoading,
    fetchTaxComponents,
    createTaxComponent,
    updateTaxComponent,
    deleteTaxComponent
  } = useTaxStore()

  const handlePageChange = (page: number) => {
    fetchTaxComponents(page)
  }

  const handleCreate = async (data: any) => {
    try {
      await createTaxComponent(data)
      setIsCreateOpen(false)
    } catch (error) {
      // Error handled in store with toast
    }
  }

  const handleEdit = async (data: any) => {
    try {
      await updateTaxComponent(editingComponent.id, data)
      setEditingComponent(null)
    } catch (error) {
      // Error handled in store with toast
    }
  }

  const handleDelete = async () => {
    try {
      await deleteTaxComponent(deletingComponent.id)
      setDeletingComponent(null)
    } catch (error) {
      // Error handled in store with toast
    }
  }

  if (isLoading && taxComponents.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (taxComponents.length === 0) {
    return (
      <EmptyState
        icon={Receipt}
        title="No tax components found"
        description="No tax components match your current filters. Create your first tax component to get started."
        action={
          <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Tax Component
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Tax Component</DialogTitle>
              </DialogHeader>
              <TaxComponentForm onSubmit={handleCreate} onCancel={() => setIsCreateOpen(false)} />
            </DialogContent>
          </Dialog>
        }
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Create Dialog */}
      <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Tax Component</DialogTitle>
          </DialogHeader>
          <TaxComponentForm onSubmit={handleCreate} onCancel={() => setIsCreateOpen(false)} />
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={!!editingComponent} onOpenChange={(open) => !open && setEditingComponent(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Tax Component</DialogTitle>
          </DialogHeader>
          {editingComponent && (
            <TaxComponentForm
              initialData={editingComponent}
              onSubmit={handleEdit}
              onCancel={() => setEditingComponent(null)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation */}
      <AlertDialog open={!!deletingComponent} onOpenChange={(open) => !open && setDeletingComponent(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Tax Component</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingComponent?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Components Grid/List */}
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {taxComponents.map((component) => (
            <TaxComponentCard
              key={component.id}
              component={component}
              onEdit={() => setEditingComponent(component)}
              onDelete={() => setDeletingComponent(component)}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {taxComponents.map((component) => (
            <TaxComponentListItem
              key={component.id}
              component={component}
              onEdit={() => setEditingComponent(component)}
              onDelete={() => setDeletingComponent(component)}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      <TaxPagination
        pagination={componentsPagination}
        onPageChange={handlePageChange}
      />
    </div>
  )
}
```

### **Tax Component Card Component**
**File**: `apps/super-admin/src/components/tax/TaxComponentCard.tsx`

```typescript
'use client'

import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Percent, Globe, Calendar } from 'lucide-react'

interface TaxComponentCardProps {
  component: any
  onEdit: () => void
  onDelete: () => void
}

export function TaxComponentCard({ component, onEdit, onDelete }: TaxComponentCardProps) {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'sgst': return 'bg-blue-100 text-blue-800'
      case 'cgst': return 'bg-green-100 text-green-800'
      case 'igst': return 'bg-purple-100 text-purple-800'
      case 'vat': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'sgst': return 'SGST'
      case 'cgst': return 'CGST'
      case 'igst': return 'IGST'
      case 'vat': return 'VAT'
      case 'sales_tax': return 'Sales Tax'
      case 'income_tax': return 'Income Tax'
      case 'service_tax': return 'Service Tax'
      case 'custom': return 'Custom Tax'
      default: return type.toUpperCase()
    }
  }

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${getTypeColor(component.type)}`}>
              <Percent className="h-4 w-4" />
            </div>
            <div>
              <h3 className="font-semibold text-lg">{component.name}</h3>
              <p className="text-sm text-muted-foreground">{component.code}</p>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onDelete} className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Tax Details */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Tax Rate:</span>
            <span className="font-semibold text-lg text-primary">{component.rate}%</span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Type:</span>
            <Badge variant="secondary" className={getTypeColor(component.type)}>
              {getTypeLabel(component.type)}
            </Badge>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Method:</span>
            <span className="text-sm capitalize">{component.calculationMethod}</span>
          </div>
        </div>

        {/* Applicable Regions */}
        {component.applicableRegions && component.applicableRegions.length > 0 && (
          <div className="space-y-1">
            <div className="flex items-center text-sm text-muted-foreground">
              <Globe className="h-3 w-3 mr-1" />
              Applicable Regions:
            </div>
            <div className="text-sm">
              {component.applicableRegions.slice(0, 2).map((region: any, index: number) => (
                <span key={index} className="inline-block mr-2">
                  {region.country?.name}
                  {index < Math.min(component.applicableRegions.length, 2) - 1 && ', '}
                </span>
              ))}
              {component.applicableRegions.length > 2 && (
                <span className="text-muted-foreground">+{component.applicableRegions.length - 2} more</span>
              )}
            </div>
          </div>
        )}

        {/* Status and Dates */}
        <div className="flex items-center justify-between pt-2 border-t">
          <Badge variant={component.isActive ? 'default' : 'secondary'}>
            {component.isActive ? 'Active' : 'Inactive'}
          </Badge>

          <div className="flex items-center text-xs text-muted-foreground">
            <Calendar className="h-3 w-3 mr-1" />
            {new Date(component.effectiveFrom).toLocaleDateString()}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
```

### **Tax Component List Item Component**
**File**: `apps/super-admin/src/components/tax/TaxComponentListItem.tsx`

```typescript
'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2, Percent, Globe } from 'lucide-react'

interface TaxComponentListItemProps {
  component: any
  onEdit: () => void
  onDelete: () => void
}

export function TaxComponentListItem({ component, onEdit, onDelete }: TaxComponentListItemProps) {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'sgst': return 'bg-blue-100 text-blue-800'
      case 'cgst': return 'bg-green-100 text-green-800'
      case 'igst': return 'bg-purple-100 text-purple-800'
      case 'vat': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'sgst': return 'SGST'
      case 'cgst': return 'CGST'
      case 'igst': return 'IGST'
      case 'vat': return 'VAT'
      default: return type.toUpperCase()
    }
  }

  return (
    <Card className="hover:shadow-sm transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Component Info */}
          <div className="flex items-center space-x-4 flex-1">
            {/* Icon and Basic Info */}
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${getTypeColor(component.type)}`}>
                <Percent className="h-4 w-4" />
              </div>

              <div>
                <h3 className="font-semibold">{component.name}</h3>
                <p className="text-sm text-muted-foreground">{component.code}</p>
              </div>
            </div>

            {/* Details */}
            <div className="hidden md:flex items-center space-x-6 flex-1">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Rate</p>
                <p className="font-semibold text-primary">{component.rate}%</p>
              </div>

              <div className="text-center">
                <p className="text-sm text-muted-foreground">Type</p>
                <Badge variant="secondary" className={getTypeColor(component.type)}>
                  {getTypeLabel(component.type)}
                </Badge>
              </div>

              <div className="text-center">
                <p className="text-sm text-muted-foreground">Method</p>
                <p className="text-sm capitalize">{component.calculationMethod}</p>
              </div>

              <div className="text-center">
                <p className="text-sm text-muted-foreground">Regions</p>
                <div className="flex items-center text-sm">
                  <Globe className="h-3 w-3 mr-1" />
                  {component.applicableRegions?.length || 0}
                </div>
              </div>
            </div>

            {/* Status */}
            <Badge variant={component.isActive ? 'default' : 'secondary'}>
              {component.isActive ? 'Active' : 'Inactive'}
            </Badge>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onDelete} className="text-destructive">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
```

### **Enhanced Tax Component Form with Formik & Yup**
**File**: `apps/super-admin/src/components/tax/TaxComponentForm.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { Formik, Form, Field, ErrorMessage, FieldArray } from 'formik'
import * as Yup from 'yup'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { CalendarIcon, Plus, Trash2, Loader2 } from 'lucide-react'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

// Yup Validation Schema
const taxComponentValidationSchema = Yup.object({
  name: Yup.string()
    .required('Tax name is required')
    .min(2, 'Tax name must be at least 2 characters')
    .max(100, 'Tax name must be less than 100 characters'),
  code: Yup.string()
    .required('Tax code is required')
    .min(2, 'Tax code must be at least 2 characters')
    .max(20, 'Tax code must be less than 20 characters')
    .matches(/^[A-Z0-9_]+$/, 'Code must contain only uppercase letters, numbers, and underscores'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  type: Yup.string()
    .required('Tax type is required')
    .oneOf(['sgst', 'cgst', 'igst', 'vat', 'sales_tax', 'income_tax', 'service_tax', 'custom']),
  rate: Yup.number()
    .required('Tax rate is required')
    .min(0, 'Rate must be positive')
    .max(100, 'Rate cannot exceed 100%'),
  calculationMethod: Yup.string()
    .required('Calculation method is required')
    .oneOf(['percentage', 'fixed', 'tiered']),
  isActive: Yup.boolean(),
  effectiveFrom: Yup.date()
    .required('Effective from date is required'),
  effectiveTo: Yup.date()
    .nullable()
    .test('is-after', 'Effective to must be after effective from', function(value) {
      const { effectiveFrom } = this.parent
      if (!value || !effectiveFrom) return true
      return new Date(value) > new Date(effectiveFrom)
    }),
  priority: Yup.number()
    .required('Priority is required')
    .min(0, 'Priority must be positive'),
  applicableRegions: Yup.array().of(
    Yup.object({
      country: Yup.string().required('Country is required'),
      states: Yup.array().of(Yup.string()),
      isDefault: Yup.boolean()
    })
  )
})

interface TaxComponentFormProps {
  initialData?: any
  onSubmit: (data: any) => Promise<void>
  onCancel: () => void
}

export function TaxComponentForm({ initialData, onSubmit, onCancel }: TaxComponentFormProps) {
  const [countries, setCountries] = useState<any[]>([])
  const [states, setStates] = useState<any[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  const initialValues = {
    name: initialData?.name || '',
    code: initialData?.code || '',
    description: initialData?.description || '',
    type: initialData?.type || 'sgst',
    rate: initialData?.rate || 0,
    calculationMethod: initialData?.calculationMethod || 'percentage',
    isActive: initialData?.isActive ?? true,
    effectiveFrom: initialData?.effectiveFrom ? new Date(initialData.effectiveFrom) : new Date(),
    effectiveTo: initialData?.effectiveTo ? new Date(initialData.effectiveTo) : null,
    priority: initialData?.priority || 0,
    applicableRegions: initialData?.applicableRegions || []
  }

  useEffect(() => {
    // Fetch countries for region selection
    fetchCountries()
  }, [])

  const fetchCountries = async () => {
    try {
      const response = await fetch('/api/locations/countries')
      const data = await response.json()
      if (data.success) {
        setCountries(data.countries)
      }
    } catch (error) {
      console.error('Failed to fetch countries:', error)
    }
  }

  const fetchStates = async (countryId: string) => {
    try {
      const response = await fetch(`/api/locations/states?country=${countryId}`)
      const data = await response.json()
      if (data.success) {
        setStates(data.states)
      }
    } catch (error) {
      console.error('Failed to fetch states:', error)
    }
  }

  const handleSubmit = async (values: any) => {
    setIsSubmitting(true)
    try {
      await onSubmit(values)
      toast.success(initialData ? 'Tax component updated successfully' : 'Tax component created successfully')
    } catch (error) {
      // Error handled in store with toast
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={taxComponentValidationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ values, setFieldValue, errors, touched }) => (
        <Form className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Tax Name *</Label>
                  <Field
                    as={Input}
                    id="name"
                    name="name"
                    placeholder="e.g., State Goods & Services Tax"
                    className={errors.name && touched.name ? 'border-red-500' : ''}
                  />
                  <ErrorMessage name="name" component="div" className="text-sm text-red-500" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="code">Tax Code *</Label>
                  <Field
                    as={Input}
                    id="code"
                    name="code"
                    placeholder="e.g., SGST_9"
                    style={{ textTransform: 'uppercase' }}
                    className={errors.code && touched.code ? 'border-red-500' : ''}
                  />
                  <p className="text-sm text-muted-foreground">
                    Unique code with uppercase letters, numbers, and underscores only
                  </p>
                  <ErrorMessage name="code" component="div" className="text-sm text-red-500" />
                </div>
              </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Brief description of the tax component..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select tax type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="sgst">SGST (State GST)</SelectItem>
                        <SelectItem value="cgst">CGST (Central GST)</SelectItem>
                        <SelectItem value="igst">IGST (Integrated GST)</SelectItem>
                        <SelectItem value="vat">VAT (Value Added Tax)</SelectItem>
                        <SelectItem value="sales_tax">Sales Tax</SelectItem>
                        <SelectItem value="income_tax">Income Tax</SelectItem>
                        <SelectItem value="service_tax">Service Tax</SelectItem>
                        <SelectItem value="custom">Custom Tax</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax Rate (%)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        placeholder="9.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="calculationMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Calculation Method</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="percentage">Percentage</SelectItem>
                        <SelectItem value="fixed">Fixed Amount</SelectItem>
                        <SelectItem value="tiered">Tiered Rates</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Effective Dates */}
        <Card>
          <CardHeader>
            <CardTitle>Effective Period</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="effectiveFrom"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Effective From</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="effectiveTo"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Effective To (Optional)</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>No end date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Higher priority taxes are applied first
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Status */}
        <Card>
          <CardHeader>
            <CardTitle>Status</CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Active Tax Component
                    </FormLabel>
                    <FormDescription>
                      Only active tax components will be used in calculations
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : initialData ? 'Update Tax Component' : 'Create Tax Component'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
```

### **Tax Filters Component**
**File**: `apps/super-admin/src/components/tax/TaxFilters.tsx`

```typescript
'use client'

import { useTaxStore } from '@/stores/useTaxStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { Search, Filter, X, Grid3X3, List, RefreshCw } from 'lucide-react'
import { useState } from 'react'

interface TaxFiltersProps {
  activeTab: 'components' | 'groups' | 'rules'
}

export function TaxFilters({ activeTab }: TaxFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const {
    filters,
    viewMode,
    setFilters,
    setViewMode,
    resetFilters,
    fetchTaxComponents,
    fetchTaxGroups,
    fetchTaxRules
  } = useTaxStore()

  const handleSearchChange = (value: string) => {
    setFilters({ search: value })
    // Debounced search
    setTimeout(() => {
      refreshData()
    }, 500)
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters({ [key]: value })
    refreshData()
  }

  const refreshData = () => {
    switch (activeTab) {
      case 'components':
        fetchTaxComponents(1)
        break
      case 'groups':
        fetchTaxGroups(1)
        break
      case 'rules':
        fetchTaxRules(1)
        break
    }
  }

  const handleReset = () => {
    resetFilters()
    refreshData()
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.type) count++
    if (filters.scenario) count++
    if (filters.ruleType) count++
    if (filters.country) count++
    if (filters.isActive !== 'true') count++
    return count
  }

  return (
    <Card>
      <CardContent className="p-4 space-y-4">
        {/* Search and View Toggle */}
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder={`Search ${activeTab}...`}
                value={filters.search}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Advanced Filters Toggle */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="relative"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {getActiveFiltersCount() > 0 && (
                <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                  {getActiveFiltersCount()}
                </Badge>
              )}
            </Button>

            {/* Reset Filters */}
            {getActiveFiltersCount() > 0 && (
              <Button variant="ghost" size="sm" onClick={handleReset}>
                <X className="h-4 w-4 mr-2" />
                Clear
              </Button>
            )}
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'list' | 'card')}>
              <ToggleGroupItem value="list" aria-label="List view">
                <List className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="card" aria-label="Card view">
                <Grid3X3 className="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>

            <Button variant="outline" size="sm" onClick={refreshData}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t">
            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={filters.isActive} onValueChange={(value) => handleFilterChange('isActive', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="true">Active Only</SelectItem>
                  <SelectItem value="false">Inactive Only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Type Filter (for components) */}
            {activeTab === 'components' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Tax Type</label>
                <Select value={filters.type || ''} onValueChange={(value) => handleFilterChange('type', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Types</SelectItem>
                    <SelectItem value="sgst">SGST</SelectItem>
                    <SelectItem value="cgst">CGST</SelectItem>
                    <SelectItem value="igst">IGST</SelectItem>
                    <SelectItem value="vat">VAT</SelectItem>
                    <SelectItem value="sales_tax">Sales Tax</SelectItem>
                    <SelectItem value="income_tax">Income Tax</SelectItem>
                    <SelectItem value="service_tax">Service Tax</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Scenario Filter (for groups) */}
            {activeTab === 'groups' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Scenario</label>
                <Select value={filters.scenario || ''} onValueChange={(value) => handleFilterChange('scenario', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All scenarios" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Scenarios</SelectItem>
                    <SelectItem value="intra_state">Intra-State</SelectItem>
                    <SelectItem value="inter_state">Inter-State</SelectItem>
                    <SelectItem value="international">International</SelectItem>
                    <SelectItem value="b2b">B2B</SelectItem>
                    <SelectItem value="b2c">B2C</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Rule Type Filter (for rules) */}
            {activeTab === 'rules' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Rule Type</label>
                <Select value={filters.ruleType || ''} onValueChange={(value) => handleFilterChange('ruleType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All rule types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Types</SelectItem>
                    <SelectItem value="location">Location Based</SelectItem>
                    <SelectItem value="transaction">Transaction Type</SelectItem>
                    <SelectItem value="entity">Entity Type</SelectItem>
                    <SelectItem value="product">Product Category</SelectItem>
                    <SelectItem value="amount">Amount Based</SelectItem>
                    <SelectItem value="date">Date Based</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Country Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Country</label>
              <Select value={filters.country || ''} onValueChange={(value) => handleFilterChange('country', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All countries" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Countries</SelectItem>
                  <SelectItem value="india">India</SelectItem>
                  <SelectItem value="usa">United States</SelectItem>
                  <SelectItem value="uk">United Kingdom</SelectItem>
                  <SelectItem value="canada">Canada</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Active Filters Display */}
        {getActiveFiltersCount() > 0 && (
          <div className="flex flex-wrap gap-2 pt-2 border-t">
            <span className="text-sm text-muted-foreground">Active filters:</span>
            {filters.search && (
              <Badge variant="secondary">
                Search: {filters.search}
                <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => handleFilterChange('search', '')} />
              </Badge>
            )}
            {filters.type && (
              <Badge variant="secondary">
                Type: {filters.type.toUpperCase()}
                <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => handleFilterChange('type', '')} />
              </Badge>
            )}
            {filters.scenario && (
              <Badge variant="secondary">
                Scenario: {filters.scenario}
                <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => handleFilterChange('scenario', '')} />
              </Badge>
            )}
            {filters.ruleType && (
              <Badge variant="secondary">
                Rule: {filters.ruleType}
                <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => handleFilterChange('ruleType', '')} />
              </Badge>
            )}
            {filters.country && (
              <Badge variant="secondary">
                Country: {filters.country}
                <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => handleFilterChange('country', '')} />
              </Badge>
            )}
            {filters.isActive !== 'true' && (
              <Badge variant="secondary">
                Status: {filters.isActive === 'false' ? 'Inactive' : 'All'}
                <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => handleFilterChange('isActive', 'true')} />
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
```
**File**: `apps/super-admin/src/components/tax/TaxComponentsList.tsx`

```typescript
'use client'

import { useTaxStore } from '@/stores/useTaxStore'
import { TaxComponentCard } from './TaxComponentCard'
import { TaxComponentListItem } from './TaxComponentListItem'
import { TaxPagination } from './TaxPagination'
import { EmptyState } from '@/components/ui/empty-state'
import { Receipt } from 'lucide-react'

export function TaxComponentsList() {
  const {
    taxComponents,
    viewMode,
    componentsPagination,
    isLoading,
    fetchTaxComponents
  } = useTaxStore()

  const handlePageChange = (page: number) => {
    fetchTaxComponents(page)
  }

  if (isLoading && taxComponents.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    )
  }

  if (taxComponents.length === 0) {
    return (
      <EmptyState
        icon={Receipt}
        title="No tax components found"
        description="No tax components match your current filters. Try adjusting your search criteria."
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Components Grid/List */}
      {viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {taxComponents.map((component) => (
            <TaxComponentCard
              key={component.id}
              component={component}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {taxComponents.map((component) => (
            <TaxComponentListItem
              key={component.id}
              component={component}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      <TaxPagination
        pagination={componentsPagination}
        onPageChange={handlePageChange}
      />
    </div>
  )
}
```

### **Updated Payload Config**
**File**: `apps/api/payload.config.ts`

```typescript
import { buildConfig } from 'payload/config'
import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { webpackBundler } from '@payloadcms/bundler-webpack'
import { slateEditor } from '@payloadcms/richtext-slate'
import path from 'path'

// Collections
import Users from './src/collections/Users'
import Institutes from './src/collections/Institutes'
import Courses from './src/collections/Courses'
import Themes from './src/collections/Themes'
import Sessions from './src/collections/Sessions'
import Settings from './src/collections/Settings'
import DomainRequests from './src/collections/DomainRequests'
import Countries from './src/collections/Countries'
import States from './src/collections/States'
import Districts from './src/collections/Districts'
import TaxComponents from './src/collections/TaxComponents'
import TaxGroups from './src/collections/TaxGroups'
import TaxRules from './src/collections/TaxRules'

// Endpoints
import authEndpoints from './src/endpoints/auth'
import courseEndpoints from './src/endpoints/courses'
import themeEndpoints from './src/endpoints/themes'
import settingsEndpoints from './src/endpoints/settings'
import locationEndpoints from './src/endpoints/locations'
import taxEndpoints from './src/endpoints/tax'

export default buildConfig({
  admin: {
    user: Users.slug,
    bundler: webpackBundler(),
  },
  editor: slateEditor({}),
  collections: [
    Users,
    Institutes,
    Courses,
    Themes,
    Sessions,
    Settings,
    DomainRequests,
    Countries,
    States,
    Districts,
    TaxComponents,
    TaxGroups,
    TaxRules,
  ],
  typescript: {
    outputFile: path.resolve(__dirname, 'payload-types.ts'),
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || 'mongodb://localhost:27017/groups-exam-lms',
  }),
  endpoints: [
    ...authEndpoints,
    ...courseEndpoints,
    ...themeEndpoints,
    ...settingsEndpoints,
    ...locationEndpoints,
    ...taxEndpoints,
  ],
  cors: [
    'https://admin.groups-exam.com',
    'https://institute.groups-exam.com',
    'https://student.groups-exam.com',
    'https://groups-exam.com',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
  ],
})
```

## 🎯 Phase 6 Success Criteria

### **Functional Requirements**
- [ ] ✅ Tax components management (SGST, CGST, IGST, VAT, etc.)
- [ ] ✅ Tax groups with component combinations (GST 18% = CGST 9% + SGST 9%)
- [ ] ✅ Location-based tax rule engine
- [ ] ✅ Automated tax calculations for all scenarios
- [ ] ✅ Monthly billing with correct tax applications
- [ ] ✅ International tax handling

### **Backend Requirements**
- [ ] ✅ Tax components collection with regional applicability
- [ ] ✅ Tax groups collection with component relationships
- [ ] ✅ Tax rules collection with condition-based logic
- [ ] ✅ Tax calculation service with scenario handling
- [ ] ✅ Tax calculation APIs with preview functionality
- [ ] ✅ Proper validation and error handling

### **Tax Scenarios Implementation**
- [ ] ✅ **Intra-State**: Same state transactions (CGST + SGST)
- [ ] ✅ **Inter-State**: Different state transactions (IGST)
- [ ] ✅ **International**: Cross-border transactions
- [ ] ✅ **B2B vs B2C**: Different entity type handling
- [ ] ✅ **Monthly Billing**: Institute location-based calculations
- [ ] ✅ **Course Purchase**: Student location-based taxes

### **Frontend Requirements**
- [ ] ✅ Zustand state management for tax data
- [ ] ✅ Tax management interface for Super Admin
- [ ] ✅ Card view and list view for all tax entities
- [ ] ✅ Advanced filtering and search functionality
- [ ] ✅ Tax calculator with scenario preview
- [ ] ✅ Real-time tax calculation display

### **User Experience Requirements**
- [ ] ✅ Intuitive tax management interface
- [ ] ✅ Clear tax calculation breakdowns
- [ ] ✅ Visual tax scenario comparisons
- [ ] ✅ Responsive design for all components
- [ ] ✅ Loading states and error handling
- [ ] ✅ Bulk operations for tax management

## 📊 **Phase 6 Implementation Summary**

### **Database Collections (3 New):**
```
🧾 Tax Management:
├── 🏷️ TaxComponents (SGST, CGST, IGST, VAT, etc.)
├── 📦 TaxGroups (GST 18% = CGST 9% + SGST 9%)
└── 📋 TaxRules (Location & scenario-based rules)
```

### **Tax Calculation Engine:**
```
🔧 Smart Tax Calculator:
├── 📍 Location-based scenarios (Intra/Inter/International)
├── 🏢 Entity type handling (B2B, B2C, Individual, Corporate)
├── 💰 Transaction type rules (Course, Subscription, Bills)
└── ⚡ Real-time calculation with rule priority
```

### **Branch Billing Scenarios:**
```
💳 Automated Branch Billing:
├── � Kerala Branch → Tamil Nadu Super Admin: IGST 18%
├── � Tamil Nadu Branch → Tamil Nadu Super Admin: CGST 9% + SGST 9% = 18%
├── � USA Branch → Tamil Nadu Super Admin: International tax rules
├── 🌿 Karnataka Branch → Tamil Nadu Super Admin: IGST 18%
└── 📅 Monthly automatic billing for all branches with location-based taxes
```

### **Enhanced Pagination Component**
**File**: `apps/super-admin/src/components/tax/TaxPagination.tsx`

```typescript
'use client'

import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react'

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface TaxPaginationProps {
  pagination: PaginationInfo
  onPageChange: (page: number) => void
  onLimitChange?: (limit: number) => void
}

export function TaxPagination({ pagination, onPageChange, onLimitChange }: TaxPaginationProps) {
  const { page, limit, totalPages, totalDocs, hasNextPage, hasPrevPage } = pagination

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      onPageChange(newPage)
    }
  }

  const handleLimitChange = (newLimit: string) => {
    if (onLimitChange) {
      onLimitChange(parseInt(newLimit))
      onPageChange(1) // Reset to first page when changing limit
    }
  }

  // Generate page numbers to show
  const getPageNumbers = () => {
    const delta = 2 // Number of pages to show on each side of current page
    const range = []
    const rangeWithDots = []

    for (let i = Math.max(2, page - delta); i <= Math.min(totalPages - 1, page + delta); i++) {
      range.push(i)
    }

    if (page - delta > 2) {
      rangeWithDots.push(1, '...')
    } else {
      rangeWithDots.push(1)
    }

    rangeWithDots.push(...range)

    if (page + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages)
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages)
    }

    return rangeWithDots
  }

  const startItem = (page - 1) * limit + 1
  const endItem = Math.min(page * limit, totalDocs)

  if (totalDocs === 0) {
    return null
  }

  return (
    <div className="flex items-center justify-between px-2 py-4 border-t">
      {/* Results Info */}
      <div className="flex items-center space-x-4">
        <p className="text-sm text-muted-foreground">
          Showing <span className="font-medium">{startItem}</span> to <span className="font-medium">{endItem}</span> of{' '}
          <span className="font-medium">{totalDocs}</span> results
        </p>

        {/* Page Size Selector */}
        {onLimitChange && (
          <div className="flex items-center space-x-2">
            <p className="text-sm text-muted-foreground">Show</p>
            <Select value={limit.toString()} onValueChange={handleLimitChange}>
              <SelectTrigger className="w-16">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">per page</p>
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center space-x-2">
        {/* First Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(1)}
          disabled={!hasPrevPage}
          className="h-8 w-8 p-0"
          title="First page"
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>

        {/* Previous Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(page - 1)}
          disabled={!hasPrevPage}
          className="h-8 w-8 p-0"
          title="Previous page"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {/* Page Numbers */}
        <div className="flex items-center space-x-1">
          {getPageNumbers().map((pageNum, index) => (
            <div key={index}>
              {pageNum === '...' ? (
                <span className="px-2 py-1 text-sm text-muted-foreground">...</span>
              ) : (
                <Button
                  variant={pageNum === page ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePageChange(pageNum as number)}
                  className="h-8 w-8 p-0"
                  title={`Page ${pageNum}`}
                >
                  {pageNum}
                </Button>
              )}
            </div>
          ))}
        </div>

        {/* Next Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(page + 1)}
          disabled={!hasNextPage}
          className="h-8 w-8 p-0"
          title="Next page"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* Last Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(totalPages)}
          disabled={!hasNextPage}
          className="h-8 w-8 p-0"
          title="Last page"
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
```

**Perfect! Phase 6 Tax Management System is now complete with enhanced Formik + Yup forms, toast notifications, and advanced pagination! 🚀**
