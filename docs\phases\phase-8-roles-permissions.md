# Phase 8: Roles & Permissions System

## 🎯 **Overview**
Comprehensive hybrid role-based and user-based permissions system with hierarchical levels, custom permission overrides, and complete CRUD management interface.

## 🏗️ **System Architecture**

### **Permission Hierarchy Levels:**
```
Level 1: Super Admin (Platform Level)
├── Can create/manage all roles and permissions
├── Access to all platform features
└── Can override any permission at any level

Level 2: Institute Admin (Institute Level)  
├── Can manage institute-specific roles
├── Can assign custom permissions to institute users
└── Limited to institute scope

Level 3: Branch/User Level (Branch/Individual Level)
├── Branch Admin: Branch-specific permissions
├── Teacher: Course-specific permissions  
├── Student: Learning-specific permissions
└── Custom permissions within scope
```

## 📊 **Database Collections**

### **Roles Collection**
**File**: `apps/api/src/collections/Roles.ts`

```typescript
import { CollectionConfig } from 'payload/types'

const Roles: CollectionConfig = {
  slug: 'roles',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'level', 'isActive', 'createdAt'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.userType === 'super_admin') return true
      if (user?.userType === 'institute_admin') {
        return {
          or: [
            { level: { equals: 2 } },
            { level: { equals: 3 } },
            { 'scope.institute': { equals: user.institute } }
          ]
        }
      }
      return false
    },
    create: ({ req: { user } }) => {
      return user?.userType === 'super_admin' || user?.userType === 'institute_admin'
    },
    update: ({ req: { user } }) => {
      return user?.userType === 'super_admin' || user?.userType === 'institute_admin'
    },
    delete: ({ req: { user } }) => {
      return user?.userType === 'super_admin'
    }
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      unique: true,
      validate: (val) => {
        if (!val || val.length < 2) return 'Role name must be at least 2 characters'
        if (val.length > 50) return 'Role name must be less than 50 characters'
        return true
      }
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      validate: (val) => {
        if (!val) return 'Role code is required'
        if (!/^[a-z_]+$/.test(val)) return 'Role code must contain only lowercase letters and underscores'
        return true
      }
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500
    },
    {
      name: 'level',
      type: 'select',
      required: true,
      options: [
        { label: 'Level 1 - Super Admin', value: 1 },
        { label: 'Level 2 - Institute Admin', value: 2 },
        { label: 'Level 3 - Branch/User Level', value: 3 }
      ],
      defaultValue: 3
    },
    {
      name: 'permissions',
      type: 'array',
      fields: [
        {
          name: 'permission',
          type: 'relationship',
          relationTo: 'permissions',
          required: true
        },
        {
          name: 'scope',
          type: 'select',
          options: [
            { label: 'Platform', value: 'platform' },
            { label: 'Institute', value: 'institute' },
            { label: 'Branch', value: 'branch' },
            { label: 'Self', value: 'self' }
          ],
          defaultValue: 'self'
        }
      ]
    },
    {
      name: 'scope',
      type: 'group',
      fields: [
        {
          name: 'institute',
          type: 'relationship',
          relationTo: 'institutes',
          admin: {
            condition: (data) => data.level >= 2
          }
        },
        {
          name: 'branch',
          type: 'relationship',
          relationTo: 'branches',
          admin: {
            condition: (data) => data.level === 3
          }
        }
      ]
    },
    {
      name: 'isSystemRole',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        readOnly: true,
        description: 'System roles cannot be deleted'
      }
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true
    },
    {
      name: 'metadata',
      type: 'group',
      fields: [
        {
          name: 'maxUsers',
          type: 'number',
          min: 0,
          admin: {
            description: 'Maximum number of users that can have this role (0 = unlimited)'
          }
        },
        {
          name: 'autoAssign',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Automatically assign this role to new users'
          }
        },
        {
          name: 'requiresApproval',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Role assignment requires approval'
          }
        }
      ]
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, req }) => {
        // Set scope based on user creating the role
        if (req.user?.userType === 'institute_admin' && !data.scope?.institute) {
          data.scope = {
            ...data.scope,
            institute: req.user.institute
          }
        }
        
        // Ensure level restrictions
        if (req.user?.userType === 'institute_admin' && data.level === 1) {
          throw new Error('Institute admins cannot create Level 1 roles')
        }
        
        return data
      }
    ]
  }
}

export default Roles
```

### **Permissions Collection**
**File**: `apps/api/src/collections/Permissions.ts`

```typescript
import { CollectionConfig } from 'payload/types'

const Permissions: CollectionConfig = {
  slug: 'permissions',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'category', 'level', 'isActive'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.userType === 'super_admin') return true
      if (user?.userType === 'institute_admin') {
        return {
          level: { greater_than_equal: 2 }
        }
      }
      return false
    },
    create: ({ req: { user } }) => {
      return user?.userType === 'super_admin'
    },
    update: ({ req: { user } }) => {
      return user?.userType === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      return user?.userType === 'super_admin'
    }
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      unique: true
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      validate: (val) => {
        if (!val) return 'Permission code is required'
        if (!/^[a-z_:]+$/.test(val)) return 'Permission code must contain only lowercase letters, underscores, and colons'
        return true
      }
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Platform Management', value: 'platform' },
        { label: 'Institute Management', value: 'institute' },
        { label: 'Branch Management', value: 'branch' },
        { label: 'User Management', value: 'users' },
        { label: 'Course Management', value: 'courses' },
        { label: 'Student Management', value: 'students' },
        { label: 'Billing & Payments', value: 'billing' },
        { label: 'Reports & Analytics', value: 'reports' },
        { label: 'Settings & Configuration', value: 'settings' }
      ]
    },
    {
      name: 'level',
      type: 'select',
      required: true,
      options: [
        { label: 'Level 1 - Super Admin Only', value: 1 },
        { label: 'Level 2 - Institute Admin', value: 2 },
        { label: 'Level 3 - Branch/User Level', value: 3 }
      ],
      defaultValue: 3
    },
    {
      name: 'resource',
      type: 'text',
      admin: {
        description: 'The resource this permission applies to (e.g., users, courses, billing)'
      }
    },
    {
      name: 'action',
      type: 'select',
      required: true,
      options: [
        { label: 'Create', value: 'create' },
        { label: 'Read', value: 'read' },
        { label: 'Update', value: 'update' },
        { label: 'Delete', value: 'delete' },
        { label: 'Manage', value: 'manage' },
        { label: 'View', value: 'view' },
        { label: 'Execute', value: 'execute' }
      ]
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true
    },
    {
      name: 'isSystemPermission',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        readOnly: true,
        description: 'System permissions cannot be deleted'
      }
    }
  ]
}

export default Permissions
```

### **User Permissions Collection**
**File**: `apps/api/src/collections/UserPermissions.ts`

```typescript
import { CollectionConfig } from 'payload/types'

const UserPermissions: CollectionConfig = {
  slug: 'user-permissions',
  admin: {
    useAsTitle: 'user',
    defaultColumns: ['user', 'customPermissions', 'expiresAt', 'isActive'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.userType === 'super_admin') return true
      if (user?.userType === 'institute_admin') {
        return {
          'user.institute': { equals: user.institute }
        }
      }
      return { user: { equals: user.id } }
    },
    create: ({ req: { user } }) => {
      return user?.userType === 'super_admin' || user?.userType === 'institute_admin'
    },
    update: ({ req: { user } }) => {
      return user?.userType === 'super_admin' || user?.userType === 'institute_admin'
    },
    delete: ({ req: { user } }) => {
      return user?.userType === 'super_admin' || user?.userType === 'institute_admin'
    }
  },
  fields: [
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      unique: true
    },
    {
      name: 'customPermissions',
      type: 'group',
      fields: [
        {
          name: 'granted',
          type: 'array',
          label: 'Additional Permissions',
          fields: [
            {
              name: 'permission',
              type: 'relationship',
              relationTo: 'permissions',
              required: true
            },
            {
              name: 'scope',
              type: 'select',
              options: [
                { label: 'Platform', value: 'platform' },
                { label: 'Institute', value: 'institute' },
                { label: 'Branch', value: 'branch' },
                { label: 'Self', value: 'self' }
              ],
              defaultValue: 'self'
            },
            {
              name: 'reason',
              type: 'text',
              admin: {
                description: 'Reason for granting this permission'
              }
            }
          ]
        },
        {
          name: 'revoked',
          type: 'array',
          label: 'Revoked Permissions',
          fields: [
            {
              name: 'permission',
              type: 'relationship',
              relationTo: 'permissions',
              required: true
            },
            {
              name: 'reason',
              type: 'text',
              admin: {
                description: 'Reason for revoking this permission'
              }
            }
          ]
        }
      ]
    },
    {
      name: 'expiresAt',
      type: 'date',
      admin: {
        description: 'When these custom permissions expire (optional)'
      }
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true
    },
    {
      name: 'approvalStatus',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' }
      ],
      defaultValue: 'approved'
    },
    {
      name: 'approvedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        condition: (data) => data.approvalStatus === 'approved'
      }
    },
    {
      name: 'notes',
      type: 'textarea',
      maxLength: 1000,
      admin: {
        description: 'Additional notes about these permission changes'
      }
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, req }) => {
        // Auto-approve if super admin is making changes
        if (req.user?.userType === 'super_admin') {
          data.approvalStatus = 'approved'
          data.approvedBy = req.user.id
        }

        return data
      }
    ]
  }
}

export default UserPermissions
```

## 🔧 **Permission Service**

### **Permission Calculation Service**
**File**: `apps/api/src/services/PermissionService.ts`

```typescript
interface User {
  id: string
  userType: string
  institute?: string
  branch?: string
  role?: string
}

interface Permission {
  id: string
  code: string
  level: number
  category: string
  resource: string
  action: string
}

interface CustomPermission {
  granted: Array<{
    permission: Permission
    scope: 'platform' | 'institute' | 'branch' | 'self'
    reason?: string
  }>
  revoked: Array<{
    permission: Permission
    reason?: string
  }>
}

export class PermissionService {
  constructor(private payload: any) {}

  async getUserPermissions(userId: string): Promise<string[]> {
    // 1. Get user details
    const user = await this.payload.findByID({
      collection: 'users',
      id: userId,
      populate: ['role']
    })

    if (!user) {
      throw new Error('User not found')
    }

    // 2. Get role-based permissions
    const rolePermissions = await this.getRolePermissions(user.role?.id)

    // 3. Get custom permissions
    const customPermissions = await this.getCustomPermissions(userId)

    // 4. Calculate final permissions
    return this.calculateFinalPermissions(rolePermissions, customPermissions)
  }

  private async getRolePermissions(roleId?: string): Promise<string[]> {
    if (!roleId) return []

    const role = await this.payload.findByID({
      collection: 'roles',
      id: roleId,
      populate: ['permissions.permission']
    })

    if (!role || !role.isActive) return []

    return role.permissions
      .filter((p: any) => p.permission.isActive)
      .map((p: any) => p.permission.code)
  }

  private async getCustomPermissions(userId: string): Promise<CustomPermission | null> {
    const userPermissions = await this.payload.find({
      collection: 'user-permissions',
      where: {
        and: [
          { user: { equals: userId } },
          { isActive: { equals: true } },
          { approvalStatus: { equals: 'approved' } },
          {
            or: [
              { expiresAt: { exists: false } },
              { expiresAt: { greater_than: new Date() } }
            ]
          }
        ]
      },
      populate: [
        'customPermissions.granted.permission',
        'customPermissions.revoked.permission'
      ],
      limit: 1
    })

    return userPermissions.docs[0]?.customPermissions || null
  }

  private calculateFinalPermissions(
    rolePermissions: string[],
    customPermissions: CustomPermission | null
  ): string[] {
    let finalPermissions = [...rolePermissions]

    if (customPermissions) {
      // Add granted permissions
      customPermissions.granted?.forEach(({ permission }) => {
        if (!finalPermissions.includes(permission.code)) {
          finalPermissions.push(permission.code)
        }
      })

      // Remove revoked permissions
      customPermissions.revoked?.forEach(({ permission }) => {
        finalPermissions = finalPermissions.filter(p => p !== permission.code)
      })
    }

    return finalPermissions
  }

  async hasPermission(
    userId: string,
    permissionCode: string,
    context?: {
      instituteId?: string
      branchId?: string
      resourceId?: string
    }
  ): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId)

    // Check if user has the permission
    if (!userPermissions.includes(permissionCode)) {
      return false
    }

    // Apply scope-based restrictions
    return this.checkScope(userId, permissionCode, context)
  }

  private async checkScope(
    userId: string,
    permissionCode: string,
    context?: {
      instituteId?: string
      branchId?: string
      resourceId?: string
    }
  ): Promise<boolean> {
    const user = await this.payload.findByID({
      collection: 'users',
      id: userId
    })

    // Super admin has access to everything
    if (user.userType === 'super_admin') {
      return true
    }

    // Institute-level permissions
    if (context?.instituteId && user.institute !== context.instituteId) {
      return false
    }

    // Branch-level permissions
    if (context?.branchId && user.branch !== context.branchId) {
      return false
    }

    return true
  }

  async getPermissionsByCategory(level?: number): Promise<Record<string, Permission[]>> {
    const where: any = { isActive: { equals: true } }

    if (level) {
      where.level = { greater_than_equal: level }
    }

    const permissions = await this.payload.find({
      collection: 'permissions',
      where,
      sort: 'category'
    })

    // Group by category
    return permissions.docs.reduce((acc: Record<string, Permission[]>, permission: Permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = []
      }
      acc[permission.category].push(permission)
      return acc
    }, {})
  }
}
```

## 🌐 **API Endpoints**

### **Roles & Permissions API**
**File**: `apps/api/src/endpoints/roles/index.ts`

```typescript
import { Endpoint } from 'payload/config'
import { PermissionService } from '../../services/PermissionService'

const rolesEndpoints: Endpoint[] = [
  // Get user permissions
  {
    path: '/roles/user-permissions/:userId',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { userId } = req.params
        const currentUser = req.user

        // Check if user can view these permissions
        if (currentUser?.userType !== 'super_admin' &&
            currentUser?.userType !== 'institute_admin' &&
            currentUser?.id !== userId) {
          return res.status(403).json({
            error: 'Access denied'
          })
        }

        const permissionService = new PermissionService(req.payload)
        const permissions = await permissionService.getUserPermissions(userId)

        res.json({
          success: true,
          permissions
        })

      } catch (error) {
        console.error('Get user permissions error:', error)
        res.status(500).json({
          error: error.message || 'Internal server error'
        })
      }
    }
  },

  // Check specific permission
  {
    path: '/roles/check-permission',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { userId, permission, context } = req.body
        const currentUser = req.user

        // Only allow checking own permissions or if admin
        if (currentUser?.userType !== 'super_admin' &&
            currentUser?.userType !== 'institute_admin' &&
            currentUser?.id !== userId) {
          return res.status(403).json({
            error: 'Access denied'
          })
        }

        const permissionService = new PermissionService(req.payload)
        const hasPermission = await permissionService.hasPermission(userId, permission, context)

        res.json({
          success: true,
          hasPermission
        })

      } catch (error) {
        console.error('Check permission error:', error)
        res.status(500).json({
          error: error.message || 'Internal server error'
        })
      }
    }
  },

  // Get permissions by category
  {
    path: '/roles/permissions-by-category',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { level } = req.query
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const permissionService = new PermissionService(req.payload)
        const permissions = await permissionService.getPermissionsByCategory(
          level ? parseInt(level as string) : undefined
        )

        res.json({
          success: true,
          permissions
        })

      } catch (error) {
        console.error('Get permissions by category error:', error)
        res.status(500).json({
          error: error.message || 'Internal server error'
        })
      }
    }
  },

  // Assign custom permissions to user
  {
    path: '/roles/assign-custom-permissions',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { userId, granted, revoked, expiresAt, notes } = req.body
        const currentUser = req.user

        if (currentUser?.userType !== 'super_admin' &&
            currentUser?.userType !== 'institute_admin') {
          return res.status(403).json({
            error: 'Only admins can assign custom permissions'
          })
        }

        // Check if user permissions already exist
        const existingPermissions = await req.payload.find({
          collection: 'user-permissions',
          where: { user: { equals: userId } },
          limit: 1
        })

        const data = {
          user: userId,
          customPermissions: {
            granted: granted || [],
            revoked: revoked || []
          },
          expiresAt,
          notes,
          approvedBy: currentUser.id,
          approvalStatus: 'approved',
          isActive: true
        }

        let result
        if (existingPermissions.docs.length > 0) {
          // Update existing
          result = await req.payload.update({
            collection: 'user-permissions',
            id: existingPermissions.docs[0].id,
            data
          })
        } else {
          // Create new
          result = await req.payload.create({
            collection: 'user-permissions',
            data
          })
        }

        res.json({
          success: true,
          userPermissions: result,
          message: 'Custom permissions assigned successfully'
        })

      } catch (error) {
        console.error('Assign custom permissions error:', error)
        res.status(500).json({
          error: error.message || 'Failed to assign custom permissions'
        })
      }
    }
  },

  // Get available roles for user level
  {
    path: '/roles/available-roles',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { level, instituteId } = req.query
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const where: any = {
          isActive: { equals: true }
        }

        // Filter by level
        if (level) {
          where.level = { equals: parseInt(level as string) }
        }

        // Filter by institute for institute admins
        if (currentUser.userType === 'institute_admin') {
          where.or = [
            { 'scope.institute': { equals: currentUser.institute } },
            { 'scope.institute': { exists: false } }
          ]
        }

        const roles = await req.payload.find({
          collection: 'roles',
          where,
          populate: ['permissions.permission'],
          sort: 'level'
        })

        res.json({
          success: true,
          roles: roles.docs
        })

      } catch (error) {
        console.error('Get available roles error:', error)
        res.status(500).json({
          error: error.message || 'Internal server error'
        })
      }
    }
  }
]

export default rolesEndpoints
```

### **Complete CRUD API Endpoints**
**File**: `apps/api/src/endpoints/roles/crud.ts`

```typescript
import { Endpoint } from 'payload/config'
import { PermissionService } from '../../services/PermissionService'

const rolesCrudEndpoints: Endpoint[] = [
  // Create Role
  {
    path: '/roles',
    method: 'post',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can create roles'
          })
        }

        const { name, code, description, level, permissions, scope, metadata, isActive } = req.body

        // Validate level restrictions
        if (currentUser.userType === 'institute_admin' && level === 1) {
          return res.status(403).json({
            error: 'Institute admins cannot create Level 1 roles'
          })
        }

        // Set scope for institute admins
        let roleScope = scope
        if (currentUser.userType === 'institute_admin') {
          roleScope = {
            ...roleScope,
            institute: currentUser.institute
          }
        }

        const roleData = {
          name,
          code,
          description,
          level,
          permissions: permissions || [],
          scope: roleScope,
          metadata: metadata || {
            maxUsers: 0,
            autoAssign: false,
            requiresApproval: false
          },
          isActive: isActive !== undefined ? isActive : true,
          isSystemRole: false
        }

        const role = await req.payload.create({
          collection: 'roles',
          data: roleData
        })

        res.json({
          success: true,
          role,
          message: 'Role created successfully'
        })

      } catch (error) {
        console.error('Create role error:', error)
        res.status(500).json({
          error: error.message || 'Failed to create role'
        })
      }
    }
  },

  // Get Roles (with pagination and filters)
  {
    path: '/roles',
    method: 'get',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const {
          page = 1,
          limit = 20,
          search = '',
          level = '',
          isActive = '',
          category = ''
        } = req.query

        // Build where clause
        const where: any = {}

        // Search filter
        if (search) {
          where.or = [
            { name: { contains: search } },
            { code: { contains: search } },
            { description: { contains: search } }
          ]
        }

        // Level filter
        if (level) {
          where.level = { equals: parseInt(level as string) }
        }

        // Active filter
        if (isActive !== '') {
          where.isActive = { equals: isActive === 'true' }
        }

        // Category filter (system vs custom)
        if (category === 'system') {
          where.isSystemRole = { equals: true }
        } else if (category === 'custom') {
          where.isSystemRole = { equals: false }
        }

        // Scope restrictions for institute admins
        if (currentUser.userType === 'institute_admin') {
          where.and = [
            {
              or: [
                { level: { greater_than_equal: 2 } },
                { 'scope.institute': { equals: currentUser.institute } },
                { 'scope.institute': { exists: false } }
              ]
            }
          ]
        }

        const roles = await req.payload.find({
          collection: 'roles',
          where,
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          sort: '-createdAt',
          populate: [
            'permissions.permission',
            'scope.institute',
            'scope.branch'
          ]
        })

        res.json({
          success: true,
          ...roles
        })

      } catch (error) {
        console.error('Get roles error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch roles'
        })
      }
    }
  },

  // Get Single Role
  {
    path: '/roles/:id',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const role = await req.payload.findByID({
          collection: 'roles',
          id,
          populate: [
            'permissions.permission',
            'scope.institute',
            'scope.branch'
          ]
        })

        if (!role) {
          return res.status(404).json({
            error: 'Role not found'
          })
        }

        // Check access permissions
        if (currentUser.userType === 'institute_admin') {
          if (role.level === 1 || (role.scope?.institute && role.scope.institute !== currentUser.institute)) {
            return res.status(403).json({
              error: 'Access denied'
            })
          }
        }

        res.json({
          success: true,
          role
        })

      } catch (error) {
        console.error('Get role error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch role'
        })
      }
    }
  },

  // Update Role
  {
    path: '/roles/:id',
    method: 'patch',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can update roles'
          })
        }

        // Get existing role
        const existingRole = await req.payload.findByID({
          collection: 'roles',
          id
        })

        if (!existingRole) {
          return res.status(404).json({
            error: 'Role not found'
          })
        }

        // Check permissions
        if (currentUser.userType === 'institute_admin') {
          if (existingRole.level === 1 || existingRole.isSystemRole) {
            return res.status(403).json({
              error: 'Cannot modify system roles or Level 1 roles'
            })
          }
        }

        const updateData = { ...req.body }

        // Prevent level escalation for institute admins
        if (currentUser.userType === 'institute_admin' && updateData.level === 1) {
          return res.status(403).json({
            error: 'Institute admins cannot create Level 1 roles'
          })
        }

        // Prevent system role modification
        if (existingRole.isSystemRole) {
          delete updateData.isSystemRole
          delete updateData.code // Prevent code changes for system roles
        }

        const role = await req.payload.update({
          collection: 'roles',
          id,
          data: updateData
        })

        res.json({
          success: true,
          role,
          message: 'Role updated successfully'
        })

      } catch (error) {
        console.error('Update role error:', error)
        res.status(500).json({
          error: error.message || 'Failed to update role'
        })
      }
    }
  },

  // Delete Role
  {
    path: '/roles/:id',
    method: 'delete',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser || currentUser.userType !== 'super_admin') {
          return res.status(403).json({
            error: 'Only super admins can delete roles'
          })
        }

        // Get existing role
        const existingRole = await req.payload.findByID({
          collection: 'roles',
          id
        })

        if (!existingRole) {
          return res.status(404).json({
            error: 'Role not found'
          })
        }

        // Prevent system role deletion
        if (existingRole.isSystemRole) {
          return res.status(403).json({
            error: 'Cannot delete system roles'
          })
        }

        // Check if role is in use
        const usersWithRole = await req.payload.find({
          collection: 'users',
          where: { role: { equals: id } },
          limit: 1
        })

        if (usersWithRole.totalDocs > 0) {
          return res.status(400).json({
            error: 'Cannot delete role that is assigned to users'
          })
        }

        await req.payload.delete({
          collection: 'roles',
          id
        })

        res.json({
          success: true,
          message: 'Role deleted successfully'
        })

      } catch (error) {
        console.error('Delete role error:', error)
        res.status(500).json({
          error: error.message || 'Failed to delete role'
        })
      }
    }
  }
]

export default rolesCrudEndpoints
```

### **Permissions CRUD API Endpoints**
**File**: `apps/api/src/endpoints/permissions/crud.ts`

```typescript
import { Endpoint } from 'payload/config'

const permissionsCrudEndpoints: Endpoint[] = [
  // Create Permission
  {
    path: '/permissions',
    method: 'post',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser || currentUser.userType !== 'super_admin') {
          return res.status(403).json({
            error: 'Only super admins can create permissions'
          })
        }

        const { name, code, description, category, level, resource, action, isActive } = req.body

        const permissionData = {
          name,
          code,
          description,
          category,
          level,
          resource,
          action,
          isActive: isActive !== undefined ? isActive : true,
          isSystemPermission: false
        }

        const permission = await req.payload.create({
          collection: 'permissions',
          data: permissionData
        })

        res.json({
          success: true,
          permission,
          message: 'Permission created successfully'
        })

      } catch (error) {
        console.error('Create permission error:', error)
        res.status(500).json({
          error: error.message || 'Failed to create permission'
        })
      }
    }
  },

  // Get Permissions (with pagination and filters)
  {
    path: '/permissions',
    method: 'get',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const {
          page = 1,
          limit = 20,
          search = '',
          category = '',
          level = '',
          isActive = ''
        } = req.query

        // Build where clause
        const where: any = {}

        // Search filter
        if (search) {
          where.or = [
            { name: { contains: search } },
            { code: { contains: search } },
            { description: { contains: search } }
          ]
        }

        // Category filter
        if (category) {
          where.category = { equals: category }
        }

        // Level filter
        if (level) {
          where.level = { greater_than_equal: parseInt(level as string) }
        }

        // Active filter
        if (isActive !== '') {
          where.isActive = { equals: isActive === 'true' }
        }

        // Level restrictions for institute admins
        if (currentUser.userType === 'institute_admin') {
          where.level = { greater_than_equal: 2 }
        }

        const permissions = await req.payload.find({
          collection: 'permissions',
          where,
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          sort: 'category'
        })

        res.json({
          success: true,
          ...permissions
        })

      } catch (error) {
        console.error('Get permissions error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch permissions'
        })
      }
    }
  },

  // Update Permission
  {
    path: '/permissions/:id',
    method: 'patch',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser || currentUser.userType !== 'super_admin') {
          return res.status(403).json({
            error: 'Only super admins can update permissions'
          })
        }

        // Get existing permission
        const existingPermission = await req.payload.findByID({
          collection: 'permissions',
          id
        })

        if (!existingPermission) {
          return res.status(404).json({
            error: 'Permission not found'
          })
        }

        const updateData = { ...req.body }

        // Prevent system permission modification
        if (existingPermission.isSystemPermission) {
          delete updateData.isSystemPermission
          delete updateData.code // Prevent code changes for system permissions
        }

        const permission = await req.payload.update({
          collection: 'permissions',
          id,
          data: updateData
        })

        res.json({
          success: true,
          permission,
          message: 'Permission updated successfully'
        })

      } catch (error) {
        console.error('Update permission error:', error)
        res.status(500).json({
          error: error.message || 'Failed to update permission'
        })
      }
    }
  },

  // Delete Permission
  {
    path: '/permissions/:id',
    method: 'delete',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser || currentUser.userType !== 'super_admin') {
          return res.status(403).json({
            error: 'Only super admins can delete permissions'
          })
        }

        // Get existing permission
        const existingPermission = await req.payload.findByID({
          collection: 'permissions',
          id
        })

        if (!existingPermission) {
          return res.status(404).json({
            error: 'Permission not found'
          })
        }

        // Prevent system permission deletion
        if (existingPermission.isSystemPermission) {
          return res.status(403).json({
            error: 'Cannot delete system permissions'
          })
        }

        // Check if permission is in use
        const rolesWithPermission = await req.payload.find({
          collection: 'roles',
          where: { 'permissions.permission': { equals: id } },
          limit: 1
        })

        if (rolesWithPermission.totalDocs > 0) {
          return res.status(400).json({
            error: 'Cannot delete permission that is assigned to roles'
          })
        }

        await req.payload.delete({
          collection: 'permissions',
          id
        })

        res.json({
          success: true,
          message: 'Permission deleted successfully'
        })

      } catch (error) {
        console.error('Delete permission error:', error)
        res.status(500).json({
          error: error.message || 'Failed to delete permission'
        })
      }
    }
  }
]

export default permissionsCrudEndpoints
```

### **User Permissions CRUD API Endpoints**
**File**: `apps/api/src/endpoints/user-permissions/crud.ts`

```typescript
import { Endpoint } from 'payload/config'

const userPermissionsCrudEndpoints: Endpoint[] = [
  // Get User Permissions
  {
    path: '/user-permissions',
    method: 'get',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const {
          page = 1,
          limit = 20,
          search = '',
          userId = '',
          approvalStatus = ''
        } = req.query

        // Build where clause
        const where: any = {}

        // User filter
        if (userId) {
          where.user = { equals: userId }
        }

        // Approval status filter
        if (approvalStatus) {
          where.approvalStatus = { equals: approvalStatus }
        }

        // Search filter (by user name/email)
        if (search) {
          where['user.name'] = { contains: search }
        }

        // Scope restrictions for institute admins
        if (currentUser.userType === 'institute_admin') {
          where['user.institute'] = { equals: currentUser.institute }
        }

        const userPermissions = await req.payload.find({
          collection: 'user-permissions',
          where,
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          sort: '-createdAt',
          populate: [
            'user',
            'customPermissions.granted.permission',
            'customPermissions.revoked.permission',
            'approvedBy'
          ]
        })

        res.json({
          success: true,
          ...userPermissions
        })

      } catch (error) {
        console.error('Get user permissions error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch user permissions'
        })
      }
    }
  },

  // Create/Update User Custom Permissions
  {
    path: '/user-permissions',
    method: 'post',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can assign custom permissions'
          })
        }

        const { userId, granted, revoked, expiresAt, notes } = req.body

        if (!userId) {
          return res.status(400).json({
            error: 'User ID is required'
          })
        }

        // Get target user
        const targetUser = await req.payload.findByID({
          collection: 'users',
          id: userId
        })

        if (!targetUser) {
          return res.status(404).json({
            error: 'User not found'
          })
        }

        // Check scope permissions for institute admins
        if (currentUser.userType === 'institute_admin') {
          if (targetUser.institute !== currentUser.institute) {
            return res.status(403).json({
              error: 'Can only assign permissions to users in your institute'
            })
          }
        }

        // Check if user permissions already exist
        const existingPermissions = await req.payload.find({
          collection: 'user-permissions',
          where: { user: { equals: userId } },
          limit: 1
        })

        const data = {
          user: userId,
          customPermissions: {
            granted: granted || [],
            revoked: revoked || []
          },
          expiresAt,
          notes,
          approvedBy: currentUser.id,
          approvalStatus: 'approved',
          isActive: true
        }

        let result
        if (existingPermissions.docs.length > 0) {
          // Update existing
          result = await req.payload.update({
            collection: 'user-permissions',
            id: existingPermissions.docs[0].id,
            data
          })
        } else {
          // Create new
          result = await req.payload.create({
            collection: 'user-permissions',
            data
          })
        }

        res.json({
          success: true,
          userPermissions: result,
          message: 'Custom permissions assigned successfully'
        })

      } catch (error) {
        console.error('Assign custom permissions error:', error)
        res.status(500).json({
          error: error.message || 'Failed to assign custom permissions'
        })
      }
    }
  },

  // Update User Permissions
  {
    path: '/user-permissions/:id',
    method: 'patch',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can update custom permissions'
          })
        }

        // Get existing user permissions
        const existingUserPermissions = await req.payload.findByID({
          collection: 'user-permissions',
          id,
          populate: ['user']
        })

        if (!existingUserPermissions) {
          return res.status(404).json({
            error: 'User permissions not found'
          })
        }

        // Check scope permissions for institute admins
        if (currentUser.userType === 'institute_admin') {
          if (existingUserPermissions.user.institute !== currentUser.institute) {
            return res.status(403).json({
              error: 'Can only update permissions for users in your institute'
            })
          }
        }

        const updateData = {
          ...req.body,
          approvedBy: currentUser.id,
          approvalStatus: 'approved'
        }

        const result = await req.payload.update({
          collection: 'user-permissions',
          id,
          data: updateData
        })

        res.json({
          success: true,
          userPermissions: result,
          message: 'Custom permissions updated successfully'
        })

      } catch (error) {
        console.error('Update user permissions error:', error)
        res.status(500).json({
          error: error.message || 'Failed to update custom permissions'
        })
      }
    }
  },

  // Delete User Permissions
  {
    path: '/user-permissions/:id',
    method: 'delete',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can remove custom permissions'
          })
        }

        // Get existing user permissions
        const existingUserPermissions = await req.payload.findByID({
          collection: 'user-permissions',
          id,
          populate: ['user']
        })

        if (!existingUserPermissions) {
          return res.status(404).json({
            error: 'User permissions not found'
          })
        }

        // Check scope permissions for institute admins
        if (currentUser.userType === 'institute_admin') {
          if (existingUserPermissions.user.institute !== currentUser.institute) {
            return res.status(403).json({
              error: 'Can only remove permissions for users in your institute'
            })
          }
        }

        await req.payload.delete({
          collection: 'user-permissions',
          id
        })

        res.json({
          success: true,
          message: 'Custom permissions removed successfully'
        })

      } catch (error) {
        console.error('Delete user permissions error:', error)
        res.status(500).json({
          error: error.message || 'Failed to remove custom permissions'
        })
      }
    }
  },

  // Approve/Reject User Permissions
  {
    path: '/user-permissions/:id/approval',
    method: 'patch',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const { approvalStatus, notes } = req.body
        const currentUser = req.user

        if (!currentUser || (currentUser.userType !== 'super_admin' && currentUser.userType !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can approve/reject permissions'
          })
        }

        if (!['approved', 'rejected'].includes(approvalStatus)) {
          return res.status(400).json({
            error: 'Invalid approval status'
          })
        }

        const result = await req.payload.update({
          collection: 'user-permissions',
          id,
          data: {
            approvalStatus,
            approvedBy: currentUser.id,
            notes: notes || '',
            isActive: approvalStatus === 'approved'
          }
        })

        res.json({
          success: true,
          userPermissions: result,
          message: `Permissions ${approvalStatus} successfully`
        })

      } catch (error) {
        console.error('Approve/reject permissions error:', error)
        res.status(500).json({
          error: error.message || 'Failed to update approval status'
        })
      }
    }
  }
]

export default userPermissionsCrudEndpoints
```

## 🗄️ **Zustand State Management**

### **Roles & Permissions Store**
**File**: `apps/super-admin/src/stores/useRolesStore.ts`

```typescript
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

interface Permission {
  id: string
  name: string
  code: string
  description: string
  category: string
  level: number
  resource: string
  action: string
  isActive: boolean
  isSystemPermission: boolean
}

interface Role {
  id: string
  name: string
  code: string
  description: string
  level: number
  permissions: Array<{
    permission: Permission
    scope: 'platform' | 'institute' | 'branch' | 'self'
  }>
  scope: {
    institute?: string
    branch?: string
  }
  isSystemRole: boolean
  isActive: boolean
  metadata: {
    maxUsers: number
    autoAssign: boolean
    requiresApproval: boolean
  }
  createdAt: string
  updatedAt: string
}

interface UserPermissions {
  id: string
  user: string
  customPermissions: {
    granted: Array<{
      permission: Permission
      scope: string
      reason?: string
    }>
    revoked: Array<{
      permission: Permission
      reason?: string
    }>
  }
  expiresAt?: string
  isActive: boolean
  approvalStatus: 'pending' | 'approved' | 'rejected'
  approvedBy?: string
  notes?: string
}

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface RolesState {
  // Data
  roles: Role[]
  permissions: Permission[]
  permissionsByCategory: Record<string, Permission[]>
  userPermissions: UserPermissions[]

  // UI State
  isLoading: boolean
  viewMode: 'card' | 'list'

  // Pagination
  rolesPagination: PaginationInfo
  permissionsPagination: PaginationInfo
  userPermissionsPagination: PaginationInfo

  // Filters
  rolesFilters: {
    search: string
    level: string
    isActive: string
    category: string
  }
  permissionsFilters: {
    search: string
    category: string
    level: string
    isActive: string
  }

  // Actions
  fetchRoles: (page?: number, filters?: any) => Promise<void>
  fetchPermissions: (page?: number, filters?: any) => Promise<void>
  fetchPermissionsByCategory: (level?: number) => Promise<void>
  fetchUserPermissions: (page?: number, filters?: any) => Promise<void>

  createRole: (data: any) => Promise<void>
  updateRole: (id: string, data: any) => Promise<void>
  deleteRole: (id: string) => Promise<void>

  createPermission: (data: any) => Promise<void>
  updatePermission: (id: string, data: any) => Promise<void>
  deletePermission: (id: string) => Promise<void>

  assignCustomPermissions: (userId: string, data: any) => Promise<void>
  removeCustomPermissions: (id: string) => Promise<void>

  checkUserPermission: (userId: string, permission: string, context?: any) => Promise<boolean>
  getUserPermissions: (userId: string) => Promise<string[]>

  setViewMode: (mode: 'card' | 'list') => void
  setRolesFilters: (filters: any) => void
  setPermissionsFilters: (filters: any) => void
  clearFilters: () => void
}

export const useRolesStore = create<RolesState>()(
  devtools(
    (set, get) => ({
      // Initial state
      roles: [],
      permissions: [],
      permissionsByCategory: {},
      userPermissions: [],

      isLoading: false,
      viewMode: 'card',

      rolesPagination: {
        page: 1,
        limit: 20,
        totalPages: 1,
        totalDocs: 0,
        hasNextPage: false,
        hasPrevPage: false
      },
      permissionsPagination: {
        page: 1,
        limit: 20,
        totalPages: 1,
        totalDocs: 0,
        hasNextPage: false,
        hasPrevPage: false
      },
      userPermissionsPagination: {
        page: 1,
        limit: 20,
        totalPages: 1,
        totalDocs: 0,
        hasNextPage: false,
        hasPrevPage: false
      },

      rolesFilters: {
        search: '',
        level: '',
        isActive: 'true',
        category: ''
      },
      permissionsFilters: {
        search: '',
        category: '',
        level: '',
        isActive: 'true'
      },

      // Fetch roles
      fetchRoles: async (page = 1, filters = {}) => {
        set({ isLoading: true })
        try {
          const currentFilters = { ...get().rolesFilters, ...filters }
          const params = new URLSearchParams({
            page: page.toString(),
            limit: get().rolesPagination.limit.toString(),
            ...Object.fromEntries(
              Object.entries(currentFilters).filter(([_, value]) => value !== '')
            )
          })

          const response = await fetch(`/api/roles?${params}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch roles')
          }

          const data = await response.json()

          set({
            roles: data.docs,
            rolesPagination: {
              page: data.page,
              limit: data.limit,
              totalPages: data.totalPages,
              totalDocs: data.totalDocs,
              hasNextPage: data.hasNextPage,
              hasPrevPage: data.hasPrevPage
            }
          })

        } catch (error) {
          console.error('Fetch roles error:', error)
          toast.error('Failed to fetch roles')
        } finally {
          set({ isLoading: false })
        }
      },

      // Fetch permissions
      fetchPermissions: async (page = 1, filters = {}) => {
        set({ isLoading: true })
        try {
          const currentFilters = { ...get().permissionsFilters, ...filters }
          const params = new URLSearchParams({
            page: page.toString(),
            limit: get().permissionsPagination.limit.toString(),
            ...Object.fromEntries(
              Object.entries(currentFilters).filter(([_, value]) => value !== '')
            )
          })

          const response = await fetch(`/api/permissions?${params}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch permissions')
          }

          const data = await response.json()

          set({
            permissions: data.docs,
            permissionsPagination: {
              page: data.page,
              limit: data.limit,
              totalPages: data.totalPages,
              totalDocs: data.totalDocs,
              hasNextPage: data.hasNextPage,
              hasPrevPage: data.hasPrevPage
            }
          })

        } catch (error) {
          console.error('Fetch permissions error:', error)
          toast.error('Failed to fetch permissions')
        } finally {
          set({ isLoading: false })
        }
      },

      // Fetch permissions by category
      fetchPermissionsByCategory: async (level?: number) => {
        try {
          const params = new URLSearchParams()
          if (level) params.append('level', level.toString())

          const response = await fetch(`/api/roles/permissions-by-category?${params}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch permissions by category')
          }

          const data = await response.json()
          set({ permissionsByCategory: data.permissions })

        } catch (error) {
          console.error('Fetch permissions by category error:', error)
          toast.error('Failed to fetch permissions by category')
        }
      },

      // Create role
      createRole: async (data: any) => {
        try {
          const response = await fetch('/api/roles', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to create role')
          }

          const result = await response.json()

          // Refresh roles list
          await get().fetchRoles()

          toast.success('Role created successfully')
          return result

        } catch (error) {
          console.error('Create role error:', error)
          toast.error(error.message || 'Failed to create role')
          throw error
        }
      },

      // Update role
      updateRole: async (id: string, data: any) => {
        try {
          const response = await fetch(`/api/roles/${id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to update role')
          }

          const result = await response.json()

          // Update role in state
          set(state => ({
            roles: state.roles.map(role =>
              role.id === id ? { ...role, ...result } : role
            )
          }))

          toast.success('Role updated successfully')
          return result

        } catch (error) {
          console.error('Update role error:', error)
          toast.error(error.message || 'Failed to update role')
          throw error
        }
      },

      // Delete role
      deleteRole: async (id: string) => {
        try {
          const response = await fetch(`/api/roles/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to delete role')
          }

          // Remove role from state
          set(state => ({
            roles: state.roles.filter(role => role.id !== id)
          }))

          toast.success('Role deleted successfully')

        } catch (error) {
          console.error('Delete role error:', error)
          toast.error(error.message || 'Failed to delete role')
          throw error
        }
      },

      // Set view mode
      setViewMode: (mode: 'card' | 'list') => {
        set({ viewMode: mode })
      },

      // Set roles filters
      setRolesFilters: (filters: any) => {
        set(state => ({
          rolesFilters: { ...state.rolesFilters, ...filters }
        }))
      },

      // Set permissions filters
      setPermissionsFilters: (filters: any) => {
        set(state => ({
          permissionsFilters: { ...state.permissionsFilters, ...filters }
        }))
      },

      // Clear filters
      clearFilters: () => {
        set({
          rolesFilters: {
            search: '',
            level: '',
            isActive: 'true',
            category: ''
          },
          permissionsFilters: {
            search: '',
            category: '',
            level: '',
            isActive: 'true'
          }
        })
      },

      // Fetch user permissions
      fetchUserPermissions: async (page = 1, filters = {}) => {
        set({ isLoading: true })
        try {
          const currentFilters = { ...filters }
          const params = new URLSearchParams({
            page: page.toString(),
            limit: get().userPermissionsPagination.limit.toString(),
            ...Object.fromEntries(
              Object.entries(currentFilters).filter(([_, value]) => value !== '')
            )
          })

          const response = await fetch(`/api/user-permissions?${params}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to fetch user permissions')
          }

          const data = await response.json()

          set({
            userPermissions: data.docs,
            userPermissionsPagination: {
              page: data.page,
              limit: data.limit,
              totalPages: data.totalPages,
              totalDocs: data.totalDocs,
              hasNextPage: data.hasNextPage,
              hasPrevPage: data.hasPrevPage
            }
          })

        } catch (error) {
          console.error('Fetch user permissions error:', error)
          toast.error('Failed to fetch user permissions')
        } finally {
          set({ isLoading: false })
        }
      },

      // Create permission
      createPermission: async (data: any) => {
        try {
          const response = await fetch('/api/permissions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to create permission')
          }

          const result = await response.json()

          // Refresh permissions list
          await get().fetchPermissions()
          await get().fetchPermissionsByCategory()

          toast.success('Permission created successfully')
          return result

        } catch (error) {
          console.error('Create permission error:', error)
          toast.error(error.message || 'Failed to create permission')
          throw error
        }
      },

      // Update permission
      updatePermission: async (id: string, data: any) => {
        try {
          const response = await fetch(`/api/permissions/${id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to update permission')
          }

          const result = await response.json()

          // Update permission in state
          set(state => ({
            permissions: state.permissions.map(permission =>
              permission.id === id ? { ...permission, ...result.permission } : permission
            )
          }))

          // Refresh permissions by category
          await get().fetchPermissionsByCategory()

          toast.success('Permission updated successfully')
          return result

        } catch (error) {
          console.error('Update permission error:', error)
          toast.error(error.message || 'Failed to update permission')
          throw error
        }
      },

      // Delete permission
      deletePermission: async (id: string) => {
        try {
          const response = await fetch(`/api/permissions/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to delete permission')
          }

          // Remove permission from state
          set(state => ({
            permissions: state.permissions.filter(permission => permission.id !== id)
          }))

          // Refresh permissions by category
          await get().fetchPermissionsByCategory()

          toast.success('Permission deleted successfully')

        } catch (error) {
          console.error('Delete permission error:', error)
          toast.error(error.message || 'Failed to delete permission')
          throw error
        }
      },

      // Assign custom permissions
      assignCustomPermissions: async (userId: string, data: any) => {
        try {
          const response = await fetch('/api/user-permissions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({ userId, ...data })
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to assign custom permissions')
          }

          const result = await response.json()

          // Refresh user permissions list
          await get().fetchUserPermissions()

          toast.success('Custom permissions assigned successfully')
          return result

        } catch (error) {
          console.error('Assign custom permissions error:', error)
          toast.error(error.message || 'Failed to assign custom permissions')
          throw error
        }
      },

      // Remove custom permissions
      removeCustomPermissions: async (id: string) => {
        try {
          const response = await fetch(`/api/user-permissions/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          })

          if (!response.ok) {
            const error = await response.json()
            throw new Error(error.message || 'Failed to remove custom permissions')
          }

          // Remove from state
          set(state => ({
            userPermissions: state.userPermissions.filter(up => up.id !== id)
          }))

          toast.success('Custom permissions removed successfully')

        } catch (error) {
          console.error('Remove custom permissions error:', error)
          toast.error(error.message || 'Failed to remove custom permissions')
          throw error
        }
      },

      // Check user permission
      checkUserPermission: async (userId: string, permission: string, context?: any) => {
        try {
          const response = await fetch('/api/roles/check-permission', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({ userId, permission, context })
          })

          if (!response.ok) {
            throw new Error('Failed to check permission')
          }

          const result = await response.json()
          return result.hasPermission

        } catch (error) {
          console.error('Check permission error:', error)
          return false
        }
      },

      // Get user permissions
      getUserPermissions: async (userId: string) => {
        try {
          const response = await fetch(`/api/roles/user-permissions/${userId}`, {
            credentials: 'include'
          })

          if (!response.ok) {
            throw new Error('Failed to get user permissions')
          }

          const result = await response.json()
          return result.permissions

        } catch (error) {
          console.error('Get user permissions error:', error)
          return []
        }
      }
    }),
    {
      name: 'roles-store'
    }
  )
)
```

## 📝 **Frontend Components**

### **Role Form Component with Formik & Yup**
**File**: `apps/super-admin/src/components/roles/RoleForm.tsx`

```typescript
'use client'

import { useState, useEffect } from 'react'
import { Formik, Form, Field, ErrorMessage, FieldArray } from 'formik'
import * as Yup from 'yup'
import { useRolesStore } from '@/stores/useRolesStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Loader2, Shield, Plus, Trash2, Users } from 'lucide-react'
import { toast } from 'sonner'

// Validation Schema
const roleValidationSchema = Yup.object({
  name: Yup.string()
    .required('Role name is required')
    .min(2, 'Role name must be at least 2 characters')
    .max(50, 'Role name must be less than 50 characters'),
  code: Yup.string()
    .required('Role code is required')
    .min(2, 'Role code must be at least 2 characters')
    .max(30, 'Role code must be less than 30 characters')
    .matches(/^[a-z_]+$/, 'Role code must contain only lowercase letters and underscores'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  level: Yup.number()
    .required('Permission level is required')
    .oneOf([1, 2, 3], 'Invalid permission level'),
  permissions: Yup.array().of(
    Yup.object({
      permission: Yup.string().required('Permission is required'),
      scope: Yup.string()
        .required('Scope is required')
        .oneOf(['platform', 'institute', 'branch', 'self'], 'Invalid scope')
    })
  ).min(1, 'At least one permission is required'),
  scope: Yup.object({
    institute: Yup.string().when('level', {
      is: (level: number) => level >= 2,
      then: () => Yup.string().nullable(),
      otherwise: () => Yup.string().nullable()
    }),
    branch: Yup.string().when('level', {
      is: 3,
      then: () => Yup.string().nullable(),
      otherwise: () => Yup.string().nullable()
    })
  }),
  metadata: Yup.object({
    maxUsers: Yup.number()
      .min(0, 'Max users must be positive')
      .nullable(),
    autoAssign: Yup.boolean(),
    requiresApproval: Yup.boolean()
  }),
  isActive: Yup.boolean()
})

interface RoleFormProps {
  initialData?: any
  onSubmit: (values: any) => Promise<void>
  onCancel: () => void
}

export function RoleForm({ initialData, onSubmit, onCancel }: RoleFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { permissionsByCategory, fetchPermissionsByCategory } = useRolesStore()

  useEffect(() => {
    // Fetch permissions based on role level
    const level = initialData?.level || 3
    fetchPermissionsByCategory(level)
  }, [initialData?.level])

  const initialValues = {
    name: initialData?.name || '',
    code: initialData?.code || '',
    description: initialData?.description || '',
    level: initialData?.level || 3,
    permissions: initialData?.permissions || [],
    scope: {
      institute: initialData?.scope?.institute || '',
      branch: initialData?.scope?.branch || ''
    },
    metadata: {
      maxUsers: initialData?.metadata?.maxUsers || 0,
      autoAssign: initialData?.metadata?.autoAssign || false,
      requiresApproval: initialData?.metadata?.requiresApproval || false
    },
    isActive: initialData?.isActive ?? true
  }

  const handleSubmit = async (values: any) => {
    setIsSubmitting(true)
    try {
      await onSubmit(values)
      toast.success(initialData ? 'Role updated successfully' : 'Role created successfully')
    } catch (error) {
      // Error handled in store
    } finally {
      setIsSubmitting(false)
    }
  }

  const levelOptions = [
    { value: 1, label: 'Level 1 - Super Admin', description: 'Platform-wide access' },
    { value: 2, label: 'Level 2 - Institute Admin', description: 'Institute-wide access' },
    { value: 3, label: 'Level 3 - Branch/User Level', description: 'Branch or individual access' }
  ]

  const scopeOptions = [
    { value: 'platform', label: 'Platform', description: 'Entire platform access' },
    { value: 'institute', label: 'Institute', description: 'Institute-wide access' },
    { value: 'branch', label: 'Branch', description: 'Branch-specific access' },
    { value: 'self', label: 'Self', description: 'Own resources only' }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="h-5 w-5 mr-2" />
          {initialData ? 'Edit Role' : 'Create New Role'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Formik
          initialValues={initialValues}
          validationSchema={roleValidationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched }) => (
            <Form className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Role Name *</Label>
                      <Field
                        as={Input}
                        id="name"
                        name="name"
                        placeholder="e.g., Branch Administrator"
                        className={errors.name && touched.name ? 'border-red-500' : ''}
                      />
                      <ErrorMessage name="name" component="div" className="text-sm text-red-500" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="code">Role Code *</Label>
                      <Field
                        as={Input}
                        id="code"
                        name="code"
                        placeholder="e.g., branch_admin"
                        style={{ textTransform: 'lowercase' }}
                        className={errors.code && touched.code ? 'border-red-500' : ''}
                      />
                      <p className="text-sm text-muted-foreground">
                        Unique code with lowercase letters and underscores only
                      </p>
                      <ErrorMessage name="code" component="div" className="text-sm text-red-500" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Field
                      as={Textarea}
                      id="description"
                      name="description"
                      placeholder="Describe the role and its responsibilities..."
                      rows={3}
                      className={errors.description && touched.description ? 'border-red-500' : ''}
                    />
                    <ErrorMessage name="description" component="div" className="text-sm text-red-500" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="level">Permission Level *</Label>
                    <Select
                      value={values.level.toString()}
                      onValueChange={(value) => {
                        const level = parseInt(value)
                        setFieldValue('level', level)
                        // Refresh permissions for new level
                        fetchPermissionsByCategory(level)
                      }}
                    >
                      <SelectTrigger className={errors.level && touched.level ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Select permission level" />
                      </SelectTrigger>
                      <SelectContent>
                        {levelOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-sm text-muted-foreground">{option.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <ErrorMessage name="level" component="div" className="text-sm text-red-500" />
                  </div>
                </CardContent>
              </Card>

              {/* Permissions Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Permissions</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Select permissions and their scope for this role
                  </p>
                </CardHeader>
                <CardContent>
                  <FieldArray name="permissions">
                    {({ push, remove }) => (
                      <div className="space-y-4">
                        {values.permissions.map((permission: any, index: number) => (
                          <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>Permission *</Label>
                                <Select
                                  value={permission.permission}
                                  onValueChange={(value) => setFieldValue(`permissions.${index}.permission`, value)}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select permission" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {Object.entries(permissionsByCategory).map(([category, perms]) => (
                                      <div key={category}>
                                        <div className="px-2 py-1 text-sm font-semibold text-muted-foreground">
                                          {category.replace('_', ' ').toUpperCase()}
                                        </div>
                                        {perms.map((perm: any) => (
                                          <SelectItem key={perm.id} value={perm.id}>
                                            <div>
                                              <div className="font-medium">{perm.name}</div>
                                              <div className="text-sm text-muted-foreground">{perm.description}</div>
                                            </div>
                                          </SelectItem>
                                        ))}
                                      </div>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="space-y-2">
                                <Label>Scope *</Label>
                                <Select
                                  value={permission.scope}
                                  onValueChange={(value) => setFieldValue(`permissions.${index}.scope`, value)}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select scope" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {scopeOptions.map((option) => (
                                      <SelectItem key={option.value} value={option.value}>
                                        <div>
                                          <div className="font-medium">{option.label}</div>
                                          <div className="text-sm text-muted-foreground">{option.description}</div>
                                        </div>
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>

                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => remove(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}

                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => push({ permission: '', scope: 'self' })}
                          className="w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Permission
                        </Button>

                        <ErrorMessage name="permissions" component="div" className="text-sm text-red-500" />
                      </div>
                    )}
                  </FieldArray>
                </CardContent>
              </Card>

              {/* Role Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Role Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="metadata.maxUsers">Max Users</Label>
                      <Field
                        as={Input}
                        id="metadata.maxUsers"
                        name="metadata.maxUsers"
                        type="number"
                        min="0"
                        placeholder="0 = unlimited"
                      />
                      <p className="text-sm text-muted-foreground">
                        Maximum users that can have this role (0 = unlimited)
                      </p>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Field
                        type="checkbox"
                        id="metadata.autoAssign"
                        name="metadata.autoAssign"
                        className="rounded"
                      />
                      <Label htmlFor="metadata.autoAssign">Auto-assign to new users</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Field
                        type="checkbox"
                        id="metadata.requiresApproval"
                        name="metadata.requiresApproval"
                        className="rounded"
                      />
                      <Label htmlFor="metadata.requiresApproval">Requires approval</Label>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Field
                      type="checkbox"
                      id="isActive"
                      name="isActive"
                      className="rounded"
                    />
                    <Label htmlFor="isActive">Active Role</Label>
                  </div>
                </CardContent>
              </Card>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  <Users className="h-4 w-4 mr-2" />
                  {initialData ? 'Update Role' : 'Create Role'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  )
}
```

### **Role Card Component**
**File**: `apps/super-admin/src/components/roles/RoleCard.tsx`

```typescript
'use client'

import { useState } from 'react'
import { useRolesStore } from '@/stores/useRolesStore'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Shield,
  Users,
  Settings,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Lock,
  Unlock
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface RoleCardProps {
  role: any
  onEdit?: (role: any) => void
  onDelete?: (role: any) => void
  onView?: (role: any) => void
}

export function RoleCard({ role, onEdit, onDelete, onView }: RoleCardProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { updateRole } = useRolesStore()

  const handleToggleStatus = async () => {
    setIsLoading(true)
    try {
      await updateRole(role.id, { isActive: !role.isActive })
    } catch (error) {
      // Error handled in store
    } finally {
      setIsLoading(false)
    }
  }

  const getLevelBadgeColor = (level: number) => {
    switch (level) {
      case 1: return 'bg-red-100 text-red-800 border-red-200'
      case 2: return 'bg-blue-100 text-blue-800 border-blue-200'
      case 3: return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getLevelLabel = (level: number) => {
    switch (level) {
      case 1: return 'Super Admin'
      case 2: return 'Institute Admin'
      case 3: return 'Branch/User'
      default: return 'Unknown'
    }
  }

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${!role.isActive ? 'opacity-60' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className={getLevelBadgeColor(role.level)}>
                <Shield className="h-5 w-5" />
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{role.name}</CardTitle>
              <p className="text-sm text-muted-foreground">{role.code}</p>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(role)}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(role)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Role
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={handleToggleStatus} disabled={isLoading}>
                {role.isActive ? (
                  <>
                    <Lock className="h-4 w-4 mr-2" />
                    Deactivate
                  </>
                ) : (
                  <>
                    <Unlock className="h-4 w-4 mr-2" />
                    Activate
                  </>
                )}
              </DropdownMenuItem>
              {onDelete && !role.isSystemRole && (
                <DropdownMenuItem
                  onClick={() => onDelete(role)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Role
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Description */}
        {role.description && (
          <p className="text-sm text-muted-foreground line-clamp-2">
            {role.description}
          </p>
        )}

        {/* Badges */}
        <div className="flex flex-wrap gap-2">
          <Badge className={getLevelBadgeColor(role.level)}>
            Level {role.level} - {getLevelLabel(role.level)}
          </Badge>

          {role.isSystemRole && (
            <Badge variant="outline" className="border-orange-200 text-orange-800">
              System Role
            </Badge>
          )}

          <Badge variant={role.isActive ? 'default' : 'secondary'}>
            {role.isActive ? 'Active' : 'Inactive'}
          </Badge>
        </div>

        {/* Permissions Count */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2">
            <Settings className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">
              {role.permissions?.length || 0} permissions
            </span>
          </div>

          {role.metadata?.maxUsers > 0 && (
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Max {role.metadata.maxUsers} users
              </span>
            </div>
          )}
        </div>

        {/* Scope Information */}
        {(role.scope?.institute || role.scope?.branch) && (
          <div className="text-sm">
            <span className="text-muted-foreground">Scope: </span>
            {role.scope.branch ? (
              <Badge variant="outline" className="text-xs">
                Branch: {role.scope.branch.name}
              </Badge>
            ) : role.scope.institute ? (
              <Badge variant="outline" className="text-xs">
                Institute: {role.scope.institute.name}
              </Badge>
            ) : null}
          </div>
        )}

        {/* Metadata */}
        <div className="flex flex-wrap gap-2 text-xs">
          {role.metadata?.autoAssign && (
            <Badge variant="outline" className="text-xs">
              Auto-assign
            </Badge>
          )}
          {role.metadata?.requiresApproval && (
            <Badge variant="outline" className="text-xs">
              Requires Approval
            </Badge>
          )}
        </div>

        {/* Footer */}
        <div className="pt-2 border-t text-xs text-muted-foreground">
          Created {formatDistanceToNow(new Date(role.createdAt), { addSuffix: true })}
        </div>
      </CardContent>
    </Card>
  )
}
```

### **Role List Item Component**
**File**: `apps/super-admin/src/components/roles/RoleListItem.tsx`

```typescript
'use client'

import { useState } from 'react'
import { useRolesStore } from '@/stores/useRolesStore'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Shield,
  Users,
  Settings,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Lock,
  Unlock
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface RoleListItemProps {
  role: any
  onEdit?: (role: any) => void
  onDelete?: (role: any) => void
  onView?: (role: any) => void
}

export function RoleListItem({ role, onEdit, onDelete, onView }: RoleListItemProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { updateRole } = useRolesStore()

  const handleToggleStatus = async () => {
    setIsLoading(true)
    try {
      await updateRole(role.id, { isActive: !role.isActive })
    } catch (error) {
      // Error handled in store
    } finally {
      setIsLoading(false)
    }
  }

  const getLevelBadgeColor = (level: number) => {
    switch (level) {
      case 1: return 'bg-red-100 text-red-800 border-red-200'
      case 2: return 'bg-blue-100 text-blue-800 border-blue-200'
      case 3: return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getLevelLabel = (level: number) => {
    switch (level) {
      case 1: return 'Super Admin'
      case 2: return 'Institute Admin'
      case 3: return 'Branch/User'
      default: return 'Unknown'
    }
  }

  return (
    <div className={`flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors ${!role.isActive ? 'opacity-60' : ''}`}>
      {/* Left Section - Role Info */}
      <div className="flex items-center space-x-4 flex-1">
        <Avatar className="h-10 w-10">
          <AvatarFallback className={getLevelBadgeColor(role.level)}>
            <Shield className="h-5 w-5" />
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold truncate">{role.name}</h3>
            <Badge className={`${getLevelBadgeColor(role.level)} text-xs`}>
              Level {role.level}
            </Badge>
            {role.isSystemRole && (
              <Badge variant="outline" className="border-orange-200 text-orange-800 text-xs">
                System
              </Badge>
            )}
          </div>

          <div className="flex items-center space-x-4 mt-1 text-sm text-muted-foreground">
            <span>{role.code}</span>
            <span>•</span>
            <div className="flex items-center space-x-1">
              <Settings className="h-3 w-3" />
              <span>{role.permissions?.length || 0} permissions</span>
            </div>
            {role.metadata?.maxUsers > 0 && (
              <>
                <span>•</span>
                <div className="flex items-center space-x-1">
                  <Users className="h-3 w-3" />
                  <span>Max {role.metadata.maxUsers}</span>
                </div>
              </>
            )}
          </div>

          {role.description && (
            <p className="text-sm text-muted-foreground mt-1 line-clamp-1">
              {role.description}
            </p>
          )}
        </div>
      </div>

      {/* Right Section - Status & Actions */}
      <div className="flex items-center space-x-4">
        {/* Status Badge */}
        <Badge variant={role.isActive ? 'default' : 'secondary'}>
          {role.isActive ? 'Active' : 'Inactive'}
        </Badge>

        {/* Created Date */}
        <div className="text-sm text-muted-foreground hidden md:block">
          {formatDistanceToNow(new Date(role.createdAt), { addSuffix: true })}
        </div>

        {/* Actions Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {onView && (
              <DropdownMenuItem onClick={() => onView(role)}>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
            )}
            {onEdit && (
              <DropdownMenuItem onClick={() => onEdit(role)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Role
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={handleToggleStatus} disabled={isLoading}>
              {role.isActive ? (
                <>
                  <Lock className="h-4 w-4 mr-2" />
                  Deactivate
                </>
              ) : (
                <>
                  <Unlock className="h-4 w-4 mr-2" />
                  Activate
                </>
              )}
            </DropdownMenuItem>
            {onDelete && !role.isSystemRole && (
              <DropdownMenuItem
                onClick={() => onDelete(role)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Role
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
```

### **Roles Filters Component**
**File**: `apps/super-admin/src/components/roles/RolesFilters.tsx`

```typescript
'use client'

import { useState } from 'react'
import { useRolesStore } from '@/stores/useRolesStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Search, Filter, X, RotateCcw } from 'lucide-react'
import { useDebounce } from '@/hooks/useDebounce'
import { useEffect } from 'react'

export function RolesFilters() {
  const {
    rolesFilters,
    setRolesFilters,
    clearFilters,
    fetchRoles
  } = useRolesStore()

  const [localSearch, setLocalSearch] = useState(rolesFilters.search)
  const debouncedSearch = useDebounce(localSearch, 300)

  // Update search filter when debounced value changes
  useEffect(() => {
    if (debouncedSearch !== rolesFilters.search) {
      setRolesFilters({ search: debouncedSearch })
      fetchRoles(1, { search: debouncedSearch })
    }
  }, [debouncedSearch])

  const handleFilterChange = (key: string, value: string) => {
    setRolesFilters({ [key]: value })
    fetchRoles(1, { [key]: value })
  }

  const handleClearFilters = () => {
    setLocalSearch('')
    clearFilters()
    fetchRoles(1)
  }

  const activeFiltersCount = Object.values(rolesFilters).filter(value =>
    value !== '' && value !== 'true'
  ).length

  const levelOptions = [
    { value: '', label: 'All Levels' },
    { value: '1', label: 'Level 1 - Super Admin' },
    { value: '2', label: 'Level 2 - Institute Admin' },
    { value: '3', label: 'Level 3 - Branch/User' }
  ]

  const statusOptions = [
    { value: 'true', label: 'Active Only' },
    { value: 'false', label: 'Inactive Only' },
    { value: '', label: 'All Status' }
  ]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          {activeFiltersCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearFilters}
              className="text-muted-foreground"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search roles by name or code..."
            value={localSearch}
            onChange={(e) => setLocalSearch(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filter Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Level Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Permission Level</label>
            <Select
              value={rolesFilters.level}
              onValueChange={(value) => handleFilterChange('level', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All levels" />
              </SelectTrigger>
              <SelectContent>
                {levelOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Status</label>
            <Select
              value={rolesFilters.isActive}
              onValueChange={(value) => handleFilterChange('isActive', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Category Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Category</label>
            <Select
              value={rolesFilters.category}
              onValueChange={(value) => handleFilterChange('category', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Categories</SelectItem>
                <SelectItem value="system">System Roles</SelectItem>
                <SelectItem value="custom">Custom Roles</SelectItem>
                <SelectItem value="institute">Institute Roles</SelectItem>
                <SelectItem value="branch">Branch Roles</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Active Filters Display */}
        {activeFiltersCount > 0 && (
          <div className="flex flex-wrap gap-2 pt-2 border-t">
            {rolesFilters.search && (
              <Badge variant="secondary">
                Search: {rolesFilters.search}
                <X
                  className="h-3 w-3 ml-1 cursor-pointer"
                  onClick={() => {
                    setLocalSearch('')
                    handleFilterChange('search', '')
                  }}
                />
              </Badge>
            )}
            {rolesFilters.level && (
              <Badge variant="secondary">
                Level: {levelOptions.find(opt => opt.value === rolesFilters.level)?.label}
                <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => handleFilterChange('level', '')} />
              </Badge>
            )}
            {rolesFilters.isActive !== 'true' && (
              <Badge variant="secondary">
                Status: {statusOptions.find(opt => opt.value === rolesFilters.isActive)?.label}
                <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => handleFilterChange('isActive', 'true')} />
              </Badge>
            )}
            {rolesFilters.category && (
              <Badge variant="secondary">
                Category: {rolesFilters.category}
                <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => handleFilterChange('category', '')} />
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
```

### **Enhanced Pagination Component**
**File**: `apps/super-admin/src/components/roles/RolesPagination.tsx`

```typescript
'use client'

import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Search } from 'lucide-react'
import { useState } from 'react'

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalDocs: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface RolesPaginationProps {
  pagination: PaginationInfo
  onPageChange: (page: number) => void
  onLimitChange?: (limit: number) => void
  showQuickJump?: boolean
}

export function RolesPagination({
  pagination,
  onPageChange,
  onLimitChange,
  showQuickJump = true
}: RolesPaginationProps) {
  const [jumpToPage, setJumpToPage] = useState('')
  const { page, limit, totalPages, totalDocs, hasNextPage, hasPrevPage } = pagination

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      onPageChange(newPage)
    }
  }

  const handleLimitChange = (newLimit: string) => {
    if (onLimitChange) {
      onLimitChange(parseInt(newLimit))
      onPageChange(1) // Reset to first page when changing limit
    }
  }

  const handleQuickJump = () => {
    const pageNum = parseInt(jumpToPage)
    if (pageNum >= 1 && pageNum <= totalPages) {
      handlePageChange(pageNum)
      setJumpToPage('')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleQuickJump()
    }
  }

  // Generate page numbers to show
  const getPageNumbers = () => {
    const delta = 2 // Number of pages to show on each side of current page
    const range = []
    const rangeWithDots = []

    for (let i = Math.max(2, page - delta); i <= Math.min(totalPages - 1, page + delta); i++) {
      range.push(i)
    }

    if (page - delta > 2) {
      rangeWithDots.push(1, '...')
    } else {
      rangeWithDots.push(1)
    }

    rangeWithDots.push(...range)

    if (page + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages)
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages)
    }

    return rangeWithDots
  }

  const startItem = (page - 1) * limit + 1
  const endItem = Math.min(page * limit, totalDocs)

  if (totalDocs === 0) {
    return (
      <div className="flex items-center justify-center py-8 text-muted-foreground">
        <p>No roles found</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col space-y-4 px-2 py-4 border-t bg-background">
      {/* Top Row - Results Info and Page Size */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <p className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{startItem}</span> to <span className="font-medium">{endItem}</span> of{' '}
            <span className="font-medium">{totalDocs}</span> roles
          </p>

          {/* Page Size Selector */}
          {onLimitChange && (
            <div className="flex items-center space-x-2">
              <p className="text-sm text-muted-foreground">Show</p>
              <Select value={limit.toString()} onValueChange={handleLimitChange}>
                <SelectTrigger className="w-16">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">per page</p>
            </div>
          )}
        </div>

        {/* Quick Jump */}
        {showQuickJump && totalPages > 10 && (
          <div className="flex items-center space-x-2">
            <p className="text-sm text-muted-foreground">Go to page:</p>
            <Input
              type="number"
              min="1"
              max={totalPages}
              value={jumpToPage}
              onChange={(e) => setJumpToPage(e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-16 h-8"
              placeholder={page.toString()}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={handleQuickJump}
              disabled={!jumpToPage || parseInt(jumpToPage) < 1 || parseInt(jumpToPage) > totalPages}
              className="h-8"
            >
              <Search className="h-3 w-3" />
            </Button>
          </div>
        )}
      </div>

      {/* Bottom Row - Pagination Controls */}
      <div className="flex items-center justify-center space-x-2">
        {/* First Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(1)}
          disabled={!hasPrevPage}
          className="h-8 w-8 p-0"
          title="First page"
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>

        {/* Previous Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(page - 1)}
          disabled={!hasPrevPage}
          className="h-8 w-8 p-0"
          title="Previous page"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {/* Page Numbers */}
        <div className="flex items-center space-x-1">
          {getPageNumbers().map((pageNum, index) => (
            <div key={index}>
              {pageNum === '...' ? (
                <span className="px-2 py-1 text-sm text-muted-foreground">...</span>
              ) : (
                <Button
                  variant={pageNum === page ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePageChange(pageNum as number)}
                  className="h-8 w-8 p-0"
                  title={`Page ${pageNum}`}
                >
                  {pageNum}
                </Button>
              )}
            </div>
          ))}
        </div>

        {/* Next Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(page + 1)}
          disabled={!hasNextPage}
          className="h-8 w-8 p-0"
          title="Next page"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* Last Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(totalPages)}
          disabled={!hasNextPage}
          className="h-8 w-8 p-0"
          title="Last page"
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Page Info */}
      <div className="flex items-center justify-center">
        <p className="text-xs text-muted-foreground">
          Page {page} of {totalPages}
        </p>
      </div>
    </div>
  )
}
```

### **Main Roles Page**
**File**: `apps/super-admin/src/app/roles/page.tsx`

```typescript
'use client'

import { useEffect, useState } from 'react'
import { useRolesStore } from '@/stores/useRolesStore'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { RoleForm } from '@/components/roles/RoleForm'
import { RoleCard } from '@/components/roles/RoleCard'
import { RoleListItem } from '@/components/roles/RoleListItem'
import { RolesFilters } from '@/components/roles/RolesFilters'
import { RolesPagination } from '@/components/roles/RolesPagination'
import { EmptyState } from '@/components/ui/empty-state'
import {
  Shield,
  Plus,
  Grid,
  List,
  Users,
  Settings,
  Eye
} from 'lucide-react'
import { toast } from 'sonner'

export default function RolesPage() {
  const {
    roles,
    isLoading,
    viewMode,
    rolesPagination,
    fetchRoles,
    createRole,
    updateRole,
    deleteRole,
    setViewMode
  } = useRolesStore()

  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingRole, setEditingRole] = useState<any>(null)
  const [viewingRole, setViewingRole] = useState<any>(null)
  const [deletingRole, setDeletingRole] = useState<any>(null)

  useEffect(() => {
    fetchRoles()
  }, [])

  const handleCreateRole = async (data: any) => {
    await createRole(data)
    setShowCreateDialog(false)
  }

  const handleUpdateRole = async (data: any) => {
    if (editingRole) {
      await updateRole(editingRole.id, data)
      setEditingRole(null)
    }
  }

  const handleDeleteRole = async () => {
    if (deletingRole) {
      await deleteRole(deletingRole.id)
      setDeletingRole(null)
    }
  }

  const handlePageChange = (page: number) => {
    fetchRoles(page)
  }

  const handleLimitChange = (limit: number) => {
    // Update pagination limit and fetch first page
    fetchRoles(1)
  }

  // Calculate stats
  const totalRoles = rolesPagination.totalDocs
  const activeRoles = roles.filter(role => role.isActive).length
  const systemRoles = roles.filter(role => role.isSystemRole).length
  const customRoles = roles.filter(role => !role.isSystemRole).length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Roles & Permissions</h1>
          <p className="text-muted-foreground">
            Manage user roles and their permissions across the platform
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Role
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Roles</p>
                <p className="text-2xl font-bold">{totalRoles}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Roles</p>
                <p className="text-2xl font-bold">{activeRoles}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">System Roles</p>
                <p className="text-2xl font-bold">{systemRoles}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Eye className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Custom Roles</p>
                <p className="text-2xl font-bold">{customRoles}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <RolesFilters />

      {/* Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Roles</CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'card' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('card')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading && roles.length === 0 ? (
            <div className="space-y-4">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="h-20 bg-gray-200 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : roles.length === 0 ? (
            <EmptyState
              icon={Shield}
              title="No roles found"
              description="No roles match your current filters. Try adjusting your search criteria or create a new role."
              action={
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Role
                </Button>
              }
            />
          ) : (
            <div className="space-y-6">
              {/* Roles Grid/List */}
              {viewMode === 'card' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {roles.map((role) => (
                    <RoleCard
                      key={role.id}
                      role={role}
                      onEdit={setEditingRole}
                      onDelete={setDeletingRole}
                      onView={setViewingRole}
                    />
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {roles.map((role) => (
                    <RoleListItem
                      key={role.id}
                      role={role}
                      onEdit={setEditingRole}
                      onDelete={setDeletingRole}
                      onView={setViewingRole}
                    />
                  ))}
                </div>
              )}

              {/* Pagination */}
              <RolesPagination
                pagination={rolesPagination}
                onPageChange={handlePageChange}
                onLimitChange={handleLimitChange}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Role Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Role</DialogTitle>
          </DialogHeader>
          <RoleForm
            onSubmit={handleCreateRole}
            onCancel={() => setShowCreateDialog(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Role Dialog */}
      <Dialog open={!!editingRole} onOpenChange={() => setEditingRole(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Role</DialogTitle>
          </DialogHeader>
          {editingRole && (
            <RoleForm
              initialData={editingRole}
              onSubmit={handleUpdateRole}
              onCancel={() => setEditingRole(null)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Role Confirmation */}
      <AlertDialog open={!!deletingRole} onOpenChange={() => setDeletingRole(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Role</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the role "{deletingRole?.name}"?
              This action cannot be undone and will affect all users assigned to this role.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteRole}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete Role
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
```

## 🎯 **Phase 8 Success Criteria**

### **✅ Hybrid Permission System:**
- ✅ **Role-based permissions** as default (easy management)
- ✅ **User-specific overrides** for flexibility
- ✅ **Hierarchical levels** (Super Admin → Institute Admin → Branch/User)
- ✅ **Scope-based restrictions** (Platform → Institute → Branch → Self)
- ✅ **Permission inheritance** with custom overrides

### **✅ Complete CRUD Operations:**
- ✅ **Roles management** with full CRUD
- ✅ **Permissions management** with categories
- ✅ **User permissions** with custom assignments
- ✅ **Approval workflows** for permission changes
- ✅ **Expiry dates** for temporary permissions

### **✅ Enhanced UI Components:**
- ✅ **Formik + Yup forms** with comprehensive validation
- ✅ **Card and list views** with responsive design
- ✅ **Advanced filtering** with real-time search
- ✅ **Toast notifications** for all actions
- ✅ **Pagination** with page size and quick jump

### **✅ State Management:**
- ✅ **Zustand store** with proper state management
- ✅ **API integration** with error handling
- ✅ **Real-time updates** after operations
- ✅ **Filter persistence** and URL sync
- ✅ **Loading states** and optimistic updates

### **Complete API Integration**

### **Main Endpoints Index**
**File**: `apps/api/src/endpoints/roles/index.ts`

```typescript
import rolesEndpoints from './roles'
import rolesCrudEndpoints from './crud'
import permissionsCrudEndpoints from '../permissions/crud'
import userPermissionsCrudEndpoints from '../user-permissions/crud'

// Combine all role-related endpoints
const allRolesEndpoints = [
  ...rolesEndpoints,
  ...rolesCrudEndpoints,
  ...permissionsCrudEndpoints,
  ...userPermissionsCrudEndpoints
]

export default allRolesEndpoints
```

### **Updated Payload Config**
**File**: `apps/api/payload.config.ts`

```typescript
import { buildConfig } from 'payload/config'
import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { webpackBundler } from '@payloadcms/bundler-webpack'
import { slateEditor } from '@payloadcms/richtext-slate'
import path from 'path'

// Collections
import Users from './src/collections/Users'
import Institutes from './src/collections/Institutes'
import Courses from './src/collections/Courses'
import Themes from './src/collections/Themes'
import Sessions from './src/collections/Sessions'
import Settings from './src/collections/Settings'
import DomainRequests from './src/collections/DomainRequests'
import Countries from './src/collections/Countries'
import States from './src/collections/States'
import Districts from './src/collections/Districts'
import TaxComponents from './src/collections/TaxComponents'
import TaxGroups from './src/collections/TaxGroups'
import TaxRules from './src/collections/TaxRules'
import Roles from './src/collections/Roles'
import Permissions from './src/collections/Permissions'
import UserPermissions from './src/collections/UserPermissions'

// Endpoints
import authEndpoints from './src/endpoints/auth'
import courseEndpoints from './src/endpoints/courses'
import themeEndpoints from './src/endpoints/themes'
import settingsEndpoints from './src/endpoints/settings'
import locationEndpoints from './src/endpoints/locations'
import taxEndpoints from './src/endpoints/tax'
import rolesEndpoints from './src/endpoints/roles'

export default buildConfig({
  admin: {
    user: Users.slug,
    bundler: webpackBundler(),
  },
  editor: slateEditor({}),
  collections: [
    Users,
    Institutes,
    Courses,
    Themes,
    Sessions,
    Settings,
    DomainRequests,
    Countries,
    States,
    Districts,
    TaxComponents,
    TaxGroups,
    TaxRules,
    Roles,
    Permissions,
    UserPermissions,
  ],
  typescript: {
    outputFile: path.resolve(__dirname, 'payload-types.ts'),
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || 'mongodb://localhost:27017/groups-exam-lms',
  }),
  endpoints: [
    ...authEndpoints,
    ...courseEndpoints,
    ...themeEndpoints,
    ...settingsEndpoints,
    ...locationEndpoints,
    ...taxEndpoints,
    ...rolesEndpoints,
  ],
  cors: [
    'https://admin.groups-exam.com',
    'https://institute.groups-exam.com',
    'https://student.groups-exam.com',
    'https://groups-exam.com',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
  ],
})
```

### **API Testing Examples**

### **Create Role API Test**
```bash
# Create a new role
curl -X POST http://localhost:3000/api/roles \
  -H "Content-Type: application/json" \
  -H "Cookie: payload-token=your-jwt-token" \
  -d '{
    "name": "Senior Teacher",
    "code": "senior_teacher",
    "description": "Teacher with additional student management permissions",
    "level": 3,
    "permissions": [
      {
        "permission": "permission_id_1",
        "scope": "branch"
      },
      {
        "permission": "permission_id_2",
        "scope": "self"
      }
    ],
    "metadata": {
      "maxUsers": 10,
      "autoAssign": false,
      "requiresApproval": true
    },
    "isActive": true
  }'
```

### **Get Roles with Filters API Test**
```bash
# Get roles with pagination and filters
curl -X GET "http://localhost:3000/api/roles?page=1&limit=20&search=teacher&level=3&isActive=true" \
  -H "Cookie: payload-token=your-jwt-token"
```

### **Assign Custom Permissions API Test**
```bash
# Assign custom permissions to a user
curl -X POST http://localhost:3000/api/user-permissions \
  -H "Content-Type: application/json" \
  -H "Cookie: payload-token=your-jwt-token" \
  -d '{
    "userId": "user_id_123",
    "granted": [
      {
        "permission": "permission_id_1",
        "scope": "branch",
        "reason": "Temporary promotion for project"
      }
    ],
    "revoked": [
      {
        "permission": "permission_id_2",
        "reason": "Security restriction"
      }
    ],
    "expiresAt": "2024-12-31T23:59:59.000Z",
    "notes": "Temporary permissions for Q4 project leadership"
  }'
```

### **Check Permission API Test**
```bash
# Check if user has specific permission
curl -X POST http://localhost:3000/api/roles/check-permission \
  -H "Content-Type: application/json" \
  -H "Cookie: payload-token=your-jwt-token" \
  -d '{
    "userId": "user_id_123",
    "permission": "manage_students",
    "context": {
      "branchId": "branch_abc",
      "instituteId": "institute_xyz"
    }
  }'
```

## 🎯 **Complete API Integration Summary**

### **✅ CRUD Operations:**
```typescript
✅ Roles API:
├── POST /api/roles → Create role
├── GET /api/roles → List roles (with filters & pagination)
├── GET /api/roles/:id → Get single role
├── PATCH /api/roles/:id → Update role
└── DELETE /api/roles/:id → Delete role

✅ Permissions API:
├── POST /api/permissions → Create permission
├── GET /api/permissions → List permissions (with filters)
├── PATCH /api/permissions/:id → Update permission
└── DELETE /api/permissions/:id → Delete permission

✅ User Permissions API:
├── GET /api/user-permissions → List user permissions
├── POST /api/user-permissions → Assign custom permissions
├── PATCH /api/user-permissions/:id → Update custom permissions
├── DELETE /api/user-permissions/:id → Remove custom permissions
└── PATCH /api/user-permissions/:id/approval → Approve/reject
```

### **✅ Special Endpoints:**
```typescript
✅ Permission System API:
├── GET /api/roles/user-permissions/:userId → Get user's final permissions
├── POST /api/roles/check-permission → Check specific permission
├── GET /api/roles/permissions-by-category → Organized permissions
└── GET /api/roles/available-roles → Roles for user level
```

### **✅ Security Features:**
```typescript
✅ Access Control:
├── 🔐 Level-based permission filtering
├── 🎯 Scope-based access restrictions
├── 🚫 System role/permission protection
├── ✅ Institute admin scope limitations
├── 📝 Audit trail for all changes
└── ⏰ Expiry dates for temporary permissions
```

### **✅ Error Handling:**
```typescript
✅ Comprehensive Error Handling:
├── 🔍 Input validation with detailed messages
├── 🚫 Permission denied with specific reasons
├── 📊 Resource not found handling
├── 🔄 Conflict resolution (roles in use)
├── 🛡️ Security violation detection
└── 📝 Detailed error logging
```

**Perfect! Phase 8 Roles & Permissions System is now complete with full API integration, comprehensive CRUD operations, security features, and real-world testing examples! 🚀**
