# 🏫 Institute Admin Guide - Groups Exam LMS SaaS

## 📋 Overview
As an Institute Admin, you manage your educational institute's presence on the Groups Exam platform. This guide covers everything from setting up your institute to managing courses, users, and analytics.

## 🎯 Institute Admin Responsibilities
- Manage institute settings and branding
- **Create and manage user accounts**: Add trainers, students, and other staff members
- **Oversee trainer activities**: Assign permissions, monitor performance, and manage content approval
- Approve and publish courses and content created by trainers
- Monitor institute performance and analytics
- Handle billing and subscription management
- Configure landing pages and public presence

---

## 🏁 Institute Registration to Custom Domain Access – Complete Workflow

### Overview of the Process
The journey from institute registration to having your own custom domain involves five key steps:
1. **Institute Registration** - Sign up via the main platform
2. **Initial Setup** - Configure your institute in preview mode
3. **Request Custom Domain** - Submit your desired domain
4. **Super Admin Approval** - Domain validation and activation
5. **Live Access** - Access your institute via custom domain

---

## 🧩 Step 1: Institute Registration (via LMS Landing Page)

### Registration Process
1. **Visit the Main Platform**
   - Navigate to the main platform (e.g., `https://groups-exam.com`)
   - Click on "Register Institute" or "Get Started"

2. **Complete Institute Signup Form**
   The registration process is now simplified into 2 easy steps:

   **Step 1: Institute Information**
   - **Institute Name** - Your educational institution's official name
   - **Institute Email** - Primary contact email for the institute
   - **Phone Number** - Main contact number

   **Step 2: Admin Account Setup**
   - **Admin Email** - Email address for the institute administrator
   - **Admin First Name** - Administrator's first name
   - **Admin Last Name** - Administrator's last name
   - **Password** - Secure password for admin account
   - **Terms Agreement** - Accept terms and conditions

3. **Submit Registration**
   - Review all information for accuracy
   - Complete both registration steps
   - Submit the registration form
   - **Status:** Institute created in **Pending Setup** status

### What Happens Next
- You'll receive a confirmation email with login credentials
- Your institute will be assigned a unique identifier (slug)
- You can access the admin panel to begin setup
- No subdomain configuration is required - the system handles this automatically

---

## 🧩 Step 2: Initial Setup (Preview Mode)

### Accessing Your Admin Panel
1. **Login to Admin Panel**
   - Navigate to the admin panel URL provided in your confirmation email
   - Use the admin credentials you created during registration
   - Your institute will have a unique identifier for internal access

### Required Setup Sections

#### **Institute Profile Configuration**
Complete your full institute profile:
- **Detailed Description** - Comprehensive about section
- **Mission & Vision** - Your educational philosophy
- **Accreditations** - Certifications and recognitions
- **Establishment Year** - When your institute was founded

#### **Contact & Social Information**
- **Complete Address** - Full physical address with postal code
- **Multiple Contact Methods** - Phone, email, fax
- **Social Media Links** - Facebook, Twitter, LinkedIn, Instagram
- **Operating Hours** - Business hours and availability

#### **Academic Setup**
- **Courses Configuration** - Add your course catalog
- **Trainer Profiles** - Set up instructor information
- **Branch Locations** - Multiple campus locations if applicable
- **Academic Calendar** - Terms, semesters, important dates

#### **Payment & Billing Configuration**
- **Payment Gateway Setup** - Configure payment methods
- **Fee Structure** - Course fees and payment plans
- **Billing Information** - Tax details and invoicing setup
- **Refund Policies** - Terms and conditions for refunds

#### **Landing Page Preview Setup**
- **Homepage Content** - Welcome message and key information
- **About Section** - Detailed institute information
- **Course Showcase** - Featured courses and programs
- **Contact Forms** - Inquiry and enrollment forms
- **Banners & Media** - Images and promotional content

### Preview Your Site
1. **Access Preview Mode**
   - Navigate to your institute's preview URL (provided in admin panel)
   - Review all public-facing content

2. **Review Key Sections**
   - **About Page** - Institute information and history
   - **Blog Section** - News and updates (if configured)
   - **Course Listings** - Available programs and courses
   - **Banners & Media** - Visual content and promotions
   - **Contact Forms** - Test form functionality

3. **Completeness Check**
   - System automatically checks for required information
   - Complete any missing sections highlighted by the system
   - Ensure all mandatory fields are filled

### Prerequisites for Domain Request
✅ **All required profile sections completed**
✅ **At least one course configured**
✅ **Contact information verified**
✅ **Landing page content reviewed**
✅ **Payment configuration completed**

---

## 🧩 Step 3: Request Custom Domain

### Accessing Domain Configuration
1. **Navigate to Domain Settings**
   - Go to: **Settings → Domain Configuration**
   - Or access via the main dashboard "Domain Setup" widget

### Domain Request Process
1. **Input Your Desired Domain**
   - Enter your custom domain (e.g., `learn.abcacademy.com`)
   - Ensure you own or have control over this domain

2. **DNS Pre-check**
   - System performs automatic DNS validation
   - Checks domain availability and configuration
   - Provides DNS setup instructions if needed

3. **Submit Domain Request**
   - Review domain information
   - Confirm ownership of the domain
   - Submit request to Super Admin

### DNS Setup Requirements
Before submitting your request, ensure:
- **Domain Ownership** - You have administrative access to the domain
- **DNS Access** - Ability to modify DNS records
- **CNAME or A Record** - Capability to add required DNS entries

### Request Status
After submission:
- **Status Changes to:** 🔐 **Waiting for Domain Activation**
- **Notification Sent** - Super Admin receives your domain request
- **Estimated Timeline** - 24-48 hours for review and activation

---

## 🧩 Step 4: Super Admin Approval Process

### What Happens During Approval
*Note: This section is for your understanding of the process*

1. **Super Admin Review**
   - Validates your institute setup completeness
   - Reviews domain request and DNS configuration
   - Checks for any policy compliance issues

2. **DNS Validation**
   - Verifies DNS records (CNAME or A record)
   - Ensures proper domain configuration
   - Tests domain accessibility and routing

3. **Technical Setup**
   - **SSL Certificate Provisioning** - Automatic SSL setup (Let's Encrypt / AWS ACM)
   - **DNS Binding** - Domain routing configuration (Cloudflare or manual)
   - **Platform Integration** - URL updates across the system

4. **Domain Activation**
   - Marks domain as **Active** in the system
   - Updates all internal references to use custom domain
   - Sends activation confirmation to institute

### During the Waiting Period
- Continue using your subdomain for access
- Monitor your email for updates from Super Admin
- Prepare any additional DNS changes if requested
- Ensure your domain's DNS settings remain accessible

---

## 🧩 Step 5: Institute Accesses via Custom Domain

### Activation Confirmation
You'll receive notification when your domain is activated:
- **Email Confirmation** - Domain activation notice
- **Status Update** - Dashboard shows "Domain Active"
- **Access Instructions** - Updated login and access information

### Accessing Your Live Institute
Once activated, you can access your institute via:

#### **Public Website**
- **URL:** `https://learn.abcacademy.com`
- **Features:** Full public website with all your content
- **Functionality:** Course browsing, enrollment, contact forms

#### **Admin Panel**
- **URL:** `https://learn.abcacademy.com/in-admin`
- **Access:** Same login credentials
- **Features:** Complete administrative control

#### **Student Portal**
- **URL:** `https://learn.abcacademy.com/student`
- **Access:** Student login and course access
- **Features:** Course materials, assignments, progress tracking

### Automatic Activation Features
🎉 **Upon domain verification, your institute automatically goes live with:**

#### **Instant Website Activation**
- ✅ **Custom Domain** - Professional web presence at your domain
- ✅ **Selected Theme Applied** - Your chosen landing page theme is live
- ✅ **Course Marketplace Active** - Amazon-style course browsing and purchase
- ✅ **Student Registration Portal** - Students can register and enroll
- ✅ **Institute Admin Login** - Admin access enabled (no registration needed)
- ✅ **All Courses Visible** - Your course catalog is publicly accessible
- ✅ **Payment Processing** - Students can purchase courses immediately
- ✅ **SSL Certificate** - Secure HTTPS connection automatically configured

#### **User Access Structure**
```
Your Live Website Structure:
https://abc-institute.com/
├── 🏠 Homepage (themed landing page)
├── 📚 Course Marketplace (Amazon-style)
│   ├── Course listing with filters
│   ├── Course detail pages
│   ├── Shopping cart & checkout
│   └── Student reviews & ratings
├── 👥 User Access
│   ├── Student Registration (open)
│   ├── Student Login (after registration)
│   └── Institute Admin Login (direct access, no registration)
├── 📄 Static Pages (themed)
│   ├── About Us
│   ├── Contact Us
│   └── Blog/News
└── 🔧 Functional Pages (platform standard)
    ├── Student Dashboard
    ├── Live Classes
    └── Exam Portal
```

### Login Access Structure
#### **For Students:**
```
Student Access Flow:
1. Visit: https://abc-institute.com
2. Browse courses (no login required)
3. Register: https://abc-institute.com/auth/user-register
4. Complete registration with email verification
5. Login: https://abc-institute.com/auth/user-login
6. Purchase courses and access content
7. Access student dashboard and enrolled courses
```

#### **For Institute Admin:**
```
Institute Admin Access:
1. Login URL: https://abc-institute.com/auth/login
2. Login with existing credentials (no registration)
3. Access full institute management dashboard
4. Manage courses, users, analytics, settings
5. No public registration - admin accounts created by Super Admin
```

#### **Complete Authentication Structure:**
```
Authentication Routes at Your Domain:

Public Access (No Login Required):
├── https://abc-institute.com/ (Homepage)
├── https://abc-institute.com/courses (Course browsing)
├── https://abc-institute.com/about (About page)
└── https://abc-institute.com/contact (Contact page)

Student Authentication:
├── https://abc-institute.com/auth/user-register (Registration)
├── https://abc-institute.com/auth/user-login (Login)
└── Email verification required after registration

Institute Staff Authentication:
├── https://abc-institute.com/auth/login (All institute staff)
├── Institute Admin (Full access)
├── Institute Staff (Limited access)
├── Tutors/Trainers (Course & student management)
└── Branch Managers (Branch-specific access)

Access Control:
✅ Students: Public registration allowed
🔒 Institute Staff: No registration - accounts created by admin
🔒 All staff accounts: Login-only access
📧 Email verification: Required for student accounts
```

### Post-Activation Steps
1. **Test All Functionality**
   - Verify all pages load correctly at your custom domain
   - Test student registration and enrollment process
   - Confirm payment processing works
   - Test admin login and dashboard access
   - Verify course marketplace functionality

2. **Update Marketing Materials**
   - Update business cards and brochures with new domain
   - Modify social media profiles with custom domain
   - Update email signatures and letterheads
   - Inform existing students about the new domain
   - Update any existing promotional content

3. **Inform Stakeholders**
   - Notify existing students of new domain
   - Update staff and faculty
   - Inform partners and affiliates

---

## 🚀 Getting Started

### Initial Setup
1. **Access Your Admin Dashboard**
   - Subdomain: `https://your-institute.groups-exam.com/auth/login`
   - Custom Domain: `https://your-domain.com/auth/login`
   - Login with credentials provided by Super Admin
   - No registration required - direct login access

2. **Complete Institute Profile**
   ```
   Institute Information:
   - Institute Name: "ABC Learning Academy"
   - Description: "Premier coaching for competitive exams"
   - Contact Information: Address, phone, email
   - Social Media Links: Facebook, Twitter, LinkedIn

   Academic Details:
   - Specialization: "UPSC, Banking, SSC"
   - Established Year: 2010
   - Student Capacity: 500
   - Faculty Count: 15
   ```

3. **Configure Basic Settings**
   - Set timezone and language preferences
   - Configure notification preferences
   - Set up payment gateway (if enabled)
   - Review subscription plan details

---

## 👥 User Management

**Note**: As an Institute Admin, you have full control over all users within your institute. Trainers are one of the key user types you manage, along with students and other staff members.

### Managing Trainers
Trainers are users within your institute who create content, conduct classes, and interact with students. You control their access, permissions, and activities.

1. **Adding New Trainers**
   - Navigate to "Users" → "Trainers"
   - Click "Add New Trainer"
   - Fill trainer information:
     ```
     Personal Details:
     - Full Name: "Dr. John Smith"
     - Email: "<EMAIL>"
     - Phone: "******-567-8900"
     - Qualification: "PhD in Mathematics"
     - Experience: "10 years"
     
     Platform Access:
     - Role: Trainer
     - Permissions: [Create Courses, Conduct Exams, Live Classes]
     - Subjects: ["Mathematics", "Reasoning", "General Knowledge"]
     - Referral Code: Auto-generated or custom
     ```

2. **Trainer Permissions (You Control These)**
   - **Course Creation**: Allow/restrict creating new courses
   - **Content Upload**: Grant/revoke permission to upload videos/documents
   - **Live Classes**: Enable/disable conducting live sessions
   - **Exam Creation**: Allow/restrict creating and managing exams
   - **Student Interaction**: Control access to student queries and discussions
   - **Branch Access**: Assign trainers to specific branches (if applicable)

3. **Trainer Analytics (Your Oversight)**
   - View individual trainer performance metrics
   - Monitor course creation and student enrollment stats
   - Track referral performance and commission reports
   - Review student feedback and ratings for each trainer
   - Assess trainer productivity and engagement levels

### Managing Students
Students are the learners in your institute who enroll in courses, attend classes, and take exams. You control their access and learning experience.

1. **Student Enrollment (Your Control)**
   - **Bulk Import**: Upload CSV file with student details
   - **Individual Addition**: Add students one by one
   - **Self Registration**: Enable/disable student self-registration
   - **Invitation System**: Send email invitations to prospective students
   - **Approval Process**: Review and approve student applications

2. **Student Organization (Your Management)**
   - Create student groups/batches for better organization
   - Assign students to specific courses and trainers
   - Set access permissions and content restrictions
   - Monitor student progress and engagement across all courses
   - Transfer students between branches (if applicable)

3. **Student Communication (Your Oversight)**
   - Send institute-wide announcements and notifications
   - Manage discussion forums and moderate content
   - Handle student queries and escalated support issues
   - Share important updates, schedules, and policy changes
   - Coordinate with trainers for student-specific issues

---

## 📚 Course Management

### Course Creation Workflow
1. **Course Planning**
   ```
   Course Structure:
   - Exam Type: UPSC/Banking/SSC/Custom
   - Course Title: "Complete UPSC Preparation"
   - Duration: 12 months
   - Target Audience: "UPSC aspirants"
   - Prerequisites: "12th grade completion"
   
   Content Organization:
   - Subjects: ["History", "Geography", "Polity"]
   - Chapters per Subject: 10-15
   - Lessons per Chapter: 5-8
   - Total Lessons: ~400
   ```

2. **Content Upload and Organization**
   - Upload video lectures (MP4 format recommended)
   - Add PDF notes and study materials
   - Create practice quizzes and assignments
   - Set lesson sequence and dependencies

3. **Course Approval Process**
   - **Draft**: Initial creation and content upload
   - **Review**: Internal quality check
   - **Verification**: Technical and content verification
   - **Approved**: Ready for student access
   - **Published**: Live and available to students

### Content Quality Control
1. **Review Guidelines**
   - Video quality standards (minimum 720p)
   - Audio clarity requirements
   - Content accuracy verification
   - Copyright compliance check

2. **Approval Workflow**
   - Trainer submits content for review
   - Admin reviews and provides feedback
   - Trainer makes necessary corrections
   - Final approval and publishing

---

## 🎓 Exam Management

### Creating Exams
1. **Exam Types**
   - **Mock Tests**: Practice exams with instant results
   - **Live Exams**: Scheduled exams with time limits
   - **Assignments**: Take-home assessments
   - **Quizzes**: Quick knowledge checks

2. **Exam Configuration**
   ```
   Exam Settings:
   - Title: "UPSC Prelims Mock Test 1"
   - Duration: 2 hours
   - Total Questions: 100
   - Marking Scheme: +2 for correct, -0.66 for wrong
   - Passing Marks: 60%
   - Attempts Allowed: 3
   - Result Display: Immediate/After exam ends
   ```

3. **Question Bank Management**
   - Import questions from Excel/CSV
   - Create question categories and tags
   - Set difficulty levels
   - Add explanations and references

### Exam Analytics
1. **Performance Metrics**
   - Average scores and pass rates
   - Question-wise analysis
   - Time spent per question
   - Student ranking and percentiles

2. **Improvement Insights**
   - Identify weak topics
   - Suggest remedial content
   - Track progress over time
   - Compare with peer performance

---

## 🎥 Live Classes

### Scheduling Live Sessions
1. **Class Setup**
   ```
   Live Class Details:
   - Title: "Current Affairs Discussion"
   - Subject: General Studies
   - Trainer: Dr. John Smith
   - Date & Time: 2024-12-25 10:00 AM
   - Duration: 2 hours
   - Platform: Zoom/YouTube Live
   - Max Participants: 100
   ```

2. **Pre-Class Preparation**
   - Send notifications to enrolled students
   - Share class materials and agenda
   - Test technical setup
   - Prepare interactive elements (polls, Q&A)

3. **Class Management**
   - Monitor attendance
   - Manage participant interactions
   - Record sessions for later access
   - Handle technical issues

### Live Class Analytics
- Attendance rates and patterns
- Student engagement metrics
- Recording view statistics
- Feedback and ratings

---

## 💰 Commission-Based Billing & Revenue Management

### New Billing Model Overview
**Revolutionary Approach**: No monthly fees! Pay setup fee once, then share revenue through commissions.

```
Your Billing Journey:
┌─────────────────────────────────────────────────────────────┐
│ Month 1: Setup Fee Payment                                  │
│ ├── Choose your plan (Starter/Growth/Professional/Enterprise)│
│ ├── Pay one-time setup fee ($99-$799)                      │
│ ├── SSL certificate & domain activation included            │
│ └── Website goes live with all plan features               │
│                                                             │
│ Month 2+: Commission-Based Revenue Sharing                 │
│ ├── Students purchase courses on your website              │
│ ├── Platform automatically deducts commission (8-15%)      │
│ ├── You keep the remaining revenue (85-92%)                │
│ └── No monthly subscription fees ever!                     │
└─────────────────────────────────────────────────────────────┘
```

### Plan Selection & Management
1. **Available Plans Overview**
   ```
   Choose Your Plan Based on Your Needs:
   ┌─────────────────────────────────────────────────────────────┐
   │ Plan         │ Setup Fee │ Commission │ Students │ Features  │
   ├─────────────────────────────────────────────────────────────┤
   │ Starter      │ $99       │ 15%        │ 100      │ Basic     │
   │ Growth       │ $199      │ 12%        │ 500      │ Standard  │
   │ Professional │ $399      │ 10%        │ 2,000    │ Advanced  │
   │ Enterprise   │ $799      │ 8%         │ Unlimited│ Premium   │
   └─────────────────────────────────────────────────────────────┘
   ```

2. **Plan Details & Limits**
   ```
   Current Plan: Growth Plan
   ┌─────────────────────────────────────────────────────────────┐
   │ Plan Details                                                │
   ├─────────────────────────────────────────────────────────────┤
   │ Setup Fee Paid: ✅ $199 (January 15, 2024)                 │
   │ Commission Rate: 12% of student course purchases           │
   │ Status: ✅ Active & Commission-based billing enabled       │
   │                                                             │
   │ Current Usage vs Limits:                                   │
   │ ├── Students: 234/500 (47% used)                          │
   │ ├── Trainers: 8/15 (53% used)                             │
   │ ├── Branches: 2/3 (67% used)                              │
   │ ├── Courses: 23/50 (46% used)                             │
   │ └── Storage: 12GB/25GB (48% used)                         │
   │                                                             │
   │ Enabled Features:                                          │
   │ ✅ Course Management  ✅ Marketplace  ✅ Live Classes      │
   │ ✅ Online Exams      ✅ Blog         ✅ Custom Domain      │
   │ ✅ Advanced Analytics ✅ Student Portal                    │
   └─────────────────────────────────────────────────────────────┘
   ```

3. **Plan Upgrade Options**
   ```
   Upgrade to Professional Plan:
   ┌─────────────────────────────────────────────────────────────┐
   │ Upgrade Benefits                                            │
   ├─────────────────────────────────────────────────────────────┤
   │ Commission Rate: 12% → 10% (2% savings on all sales)       │
   │ Max Students: 500 → 2,000 (4x capacity)                   │
   │ Max Trainers: 15 → 50 (3x capacity)                       │
   │ Max Branches: 3 → 10 (3x capacity)                        │
   │ Max Courses: 50 → 200 (4x capacity)                       │
   │ Storage: 25GB → 100GB (4x storage)                        │
   │                                                             │
   │ New Features:                                              │
   │ ✅ API Access        ✅ White Label Branding              │
   │ ✅ Premium Analytics ✅ Priority Support                   │
   │                                                             │
   │ Upgrade Cost: $200 (difference between plans)             │
   │ Effective Immediately: Lower commission rate starts now    │
   └─────────────────────────────────────────────────────────────┘
   ```

2. **Payment Gateway Configuration**
   **Your Role**: Select from available gateways and configure your own API keys

   ```
   Payment Gateway Setup Process:
   1. View Available Gateways (provided by Super Admin)
   2. Select Gateway(s) for your institute
   3. Configure your own API keys and settings
   4. Test payment processing
   5. Enable live payment processing
   ```

   **Available Payment Gateways:**
   ```
   Gateway Selection Dashboard:
   ┌─────────────────────────────────────────────────────────────┐
   │ Gateway Name    │ Countries │ Currencies │ Status │ Action │
   ├─────────────────────────────────────────────────────────────┤
   │ Stripe          │ Global    │ 135+       │ ⚙️ Setup│ [Configure] │
   │ Razorpay        │ India     │ INR        │ ✅ Active│ [Manage]    │
   │ PayPal          │ Global    │ 25+        │ ❌ Disabled│ [Configure] │
   │ PhonePe         │ India     │ INR        │ ⚙️ Setup│ [Configure] │
   │ Paytm           │ India     │ INR        │ ❌ Disabled│ [Configure] │
   └─────────────────────────────────────────────────────────────┘
   ```

   **Gateway Configuration Examples:**

   **Stripe Configuration:**
   ```
   Stripe Payment Gateway Setup:

   Required Information:
   - Publishable Key: pk_live_51H... (from your Stripe dashboard)
   - Secret Key: sk_live_51H... (from your Stripe dashboard)
   - Webhook Secret: whsec_... (for payment confirmations)

   Optional Settings:
   - Statement Descriptor: "ABC LEARNING" (appears on customer's bank statement)
   - Capture Method: "Automatic" (immediate payment capture)

   Test Configuration:
   ☐ Test payment with ₹1 transaction
   ☐ Verify webhook delivery
   ☐ Check payment confirmation emails
   ```

   **Razorpay Configuration:**
   ```
   Razorpay Payment Gateway Setup:

   Required Information:
   - Key ID: rzp_live_... (from your Razorpay dashboard)
   - Key Secret: ... (from your Razorpay dashboard)
   - Webhook Secret: ... (for payment notifications)

   Optional Settings:
   - Theme Color: #3399cc (matches your institute branding)
   - Company Name: "ABC Learning Institute"

   Test Configuration:
   ☐ Test payment with ₹1 transaction
   ☐ Verify UPI and card payments
   ☐ Check payment success notifications
   ```

3. **Course Pricing and Revenue**
   - Set course pricing and discounts
   - Manage referral codes and commissions
   - Handle refunds and billing disputes
   - Monitor payment success rates

### Commission-Based Revenue Analytics
1. **Revenue Dashboard**
   ```
   Your Revenue Performance (Last 3 Months):
   ┌─────────────────────────────────────────────────────────────┐
   │ Month        │ Course Sales │ Commission │ Your Revenue     │
   ├─────────────────────────────────────────────────────────────┤
   │ January 2024 │ $12,500      │ $1,500     │ $11,000 (88%)   │
   │ February 2024│ $15,200      │ $1,824     │ $13,376 (88%)   │
   │ March 2024   │ $18,750      │ $2,250     │ $16,500 (88%)   │
   ├─────────────────────────────────────────────────────────────┤
   │ Total        │ $46,450      │ $5,574     │ $40,876 (88%)   │
   │ Growth       │ +50%         │ +50%       │ +50%            │
   └─────────────────────────────────────────────────────────────┘

   Key Insights:
   - Your commission rate: 12% (Growth Plan)
   - You keep: 88% of all course sales
   - Monthly growth: 50% increase
   - Average course price: ₹2,450
   ```

2. **Course-wise Revenue Breakdown**
   ```
   Top Performing Courses (This Month):
   ┌─────────────────────────────────────────────────────────────┐
   │ Course Name              │ Sales │ Commission │ Your Revenue │
   ├─────────────────────────────────────────────────────────────┤
   │ Complete UPSC Preparation│ $5,200│ $624       │ $4,576      │
   │ Banking Exam Mastery     │ $3,800│ $456       │ $3,344      │
   │ SSC CGL Complete Course  │ $2,900│ $348       │ $2,552      │
   │ English Grammar Basics   │ $2,100│ $252       │ $1,848      │
   │ Mathematics for Banking  │ $1,750│ $210       │ $1,540      │
   └─────────────────────────────────────────────────────────────┘
   ```

3. **Commission Savings Calculator**
   ```
   Plan Upgrade Impact Calculator:

   Current Plan (Growth - 12% commission):
   Monthly Sales: $18,750 → Commission: $2,250 → You Keep: $16,500

   If upgraded to Professional (10% commission):
   Monthly Sales: $18,750 → Commission: $1,875 → You Keep: $16,875
   Monthly Savings: $375 (2% of $18,750)
   Annual Savings: $4,500

   Upgrade Cost: $200 (one-time)
   Break-even: 0.5 months
   ```

4. **Referral System Revenue**
   ```
   Trainer Referral Performance:
   ┌─────────────────────────────────────────────────────────────┐
   │ Trainer Name    │ Referrals │ Conversions │ Revenue Generated│
   ├─────────────────────────────────────────────────────────────┤
   │ Dr. Sarah Kumar │ 45        │ 38 (84%)    │ $3,200          │
   │ Prof. Raj Patel │ 32        │ 28 (88%)    │ $2,800          │
   │ Ms. Priya Singh │ 28        │ 22 (79%)    │ $2,100          │
   └─────────────────────────────────────────────────────────────┘

   Total Referral Revenue: $8,100 this month
   Your Share (after trainer commission): $6,480
   Platform Commission (12%): $777
   Your Net Revenue: $5,703
   ```

### Monthly Commission Bills & Payments
**Important**: You receive monthly bills for commission payments to the platform

1. **Monthly Billing Process**
   ```
   How Monthly Billing Works:
   ┌─────────────────────────────────────────────────────────────┐
   │ Throughout the Month:                                       │
   │ ├── Students purchase courses on your website              │
   │ ├── You receive 88% of payment immediately                 │
   │ ├── Platform commission (12%) is tracked                   │
   │ └── Commission accumulates for monthly billing             │
   │                                                             │
   │ 1st of Next Month:                                         │
   │ ├── System generates your commission bill automatically    │
   │ ├── Detailed bill sent via email with PDF                 │
   │ ├── 30-day payment terms (due by month end)               │
   │ └── Multiple payment options available                     │
   └─────────────────────────────────────────────────────────────┘
   ```

2. **Sample Commission Bill**
   ```
   Your Commission Bill: INV-2024-03-ABC001
   ┌─────────────────────────────────────────────────────────────┐
   │ ABC Learning Academy                                        │
   │ Commission Bill - March 2024                               │
   ├─────────────────────────────────────────────────────────────┤
   │ Bill Date: April 1, 2024                                   │
   │ Due Date: April 30, 2024                                   │
   │ Your Plan: Growth Plan (12% commission)                    │
   │                                                             │
   │ March 2024 Summary:                                        │
   │ ├── Total Course Sales: $18,750                           │
   │ ├── Your Revenue (88%): $16,500 (already received)        │
   │ ├── Platform Commission (12%): $2,250 (due now)           │
   │ └── Total Transactions: 45 course purchases               │
   │                                                             │
   │ Detailed Breakdown:                                        │
   │ ├── UPSC Complete Course: 15 sales × $500 = $7,500       │
   │ │   Commission: $900                                       │
   │ ├── Banking Exam Prep: 12 sales × $400 = $4,800          │
   │ │   Commission: $576                                       │
   │ ├── SSC CGL Course: 10 sales × $350 = $3,500             │
   │ │   Commission: $420                                       │
   │ └── English Grammar: 8 sales × $200 = $1,600             │
   │     Commission: $192                                       │
   │                                                             │
   │ Payment Due: $2,250                                        │
   │ Late Fee (if overdue): 2% after due date                  │
   └─────────────────────────────────────────────────────────────┘
   ```

3. **Bills Dashboard**
   ```
   Your Commission Bills:
   ┌─────────────────────────────────────────────────────────────┐
   │ Bill Period │ Course Sales │ Commission │ Status    │ Due Date │
   ├─────────────────────────────────────────────────────────────┤
   │ Apr 2024    │ $22,100      │ $2,652     │ ⏳ Pending│ May 30   │
   │ Mar 2024    │ $18,750      │ $2,250     │ ✅ Paid   │ Apr 30   │
   │ Feb 2024    │ $15,200      │ $1,824     │ ✅ Paid   │ Mar 30   │
   │ Jan 2024    │ $12,500      │ $1,500     │ ✅ Paid   │ Feb 29   │
   └─────────────────────────────────────────────────────────────┘

   Actions Available:
   - [📄 View Bill] [💳 Pay Now] [📧 Email Support] [📊 Analytics]
   ```

4. **Payment Options**
   ```
   How to Pay Your Commission Bill:
   ┌─────────────────────────────────────────────────────────────┐
   │ Online Payment (Recommended):                              │
   │ ├── Credit/Debit Card payment                              │
   │ ├── Net Banking                                            │
   │ ├── UPI Payment                                            │
   │ └── Digital Wallet                                         │
   │                                                             │
   │ Bank Transfer:                                             │
   │ ├── Account: Groups Exam Platform                         │
   │ ├── Account Number: XXXX-XXXX-1234                        │
   │ ├── IFSC: HDFC0001234                                     │
   │ └── Reference: Your Bill Number                           │
   │                                                             │
   │ Cheque Payment:                                            │
   │ ├── Payable to: "Groups Exam LMS Platform"                │
   │ ├── Mail to: Billing Department                           │
   │ └── Include bill number on cheque                         │
   └─────────────────────────────────────────────────────────────┘
   ```

5. **Payment Reminders & Late Fees**
   ```
   Payment Schedule & Penalties:
   ┌─────────────────────────────────────────────────────────────┐
   │ Timeline:                                                   │
   │ ├── Bill Generated: 1st of month                          │
   │ ├── Payment Due: 30th of month                            │
   │ ├── Grace Period: 3 days                                  │
   │ └── Late Fee Applied: After grace period                  │
   │                                                             │
   │ Late Fee Structure:                                        │
   │ ├── 1-15 days overdue: 2% of bill amount                  │
   │ ├── 16-30 days overdue: 5% of bill amount                 │
   │ └── 30+ days overdue: Account suspension warning          │
   │                                                             │
   │ Notifications:                                             │
   │ ├── 📧 Bill generated notification                        │
   │ ├── 📧 Payment reminder (7 days before due)               │
   │ ├── 📧 Due date reminder                                  │
   │ └── 📧 Overdue notice                                     │
   └─────────────────────────────────────────────────────────────┘
   ```

6. **Commission Bill Analytics**
   ```
   Your Billing Trends:
   ┌─────────────────────────────────────────────────────────────┐
   │ Last 6 Months Commission Bills:                            │
   │ ├── Average Monthly Commission: $2,045                     │
   │ ├── Highest Month: $2,652 (April 2024)                    │
   │ ├── Lowest Month: $1,200 (November 2023)                  │
   │ ├── Growth Rate: +18% month-over-month                     │
   │ └── Payment History: 100% on-time payments                │
   │                                                             │
   │ Commission Savings Opportunity:                            │
   │ Current Plan: Growth (12% commission)                      │
   │ Upgrade to Professional (10% commission):                  │
   │ Monthly Savings: $409 (based on current volume)           │
   │ Annual Savings: $4,908                                     │
   │ Upgrade Cost: $200 (pays for itself in 2 weeks!)         │
   └─────────────────────────────────────────────────────────────┘
   ```
   - Monitor trainer commissions
   - Analyze referral conversion rates
   - Manage payout schedules

---

## 🏢 Multi-Branch Management System

### Overview of Branch Concept
The Groups Exam LMS supports **multiple branches** for institutes that operate in different locations or want to organize their operations by departments, courses, or regions. Each branch functions as a semi-independent unit within your institute.

### Branch Benefits
- **Localized Management**: Each branch can have its own manager and staff
- **Targeted Marketing**: Branch-specific landing pages with institute branding
- **Regional Analytics**: Track performance by location or department
- **Scalable Growth**: Easily expand to new locations
- **Flexible Organization**: Organize by geography, subjects, or student types

### Creating and Managing Branches
1. **Adding New Branches**
   - Navigate to "Institute" → "Branches"
   - Click "Create New Branch"
   - Fill comprehensive branch information:
     ```
     Branch Details:
     - Branch Name: "Downtown Learning Center"
     - Branch Code: "DLC" (internal reference)
     - Slug: "downtown" (for URL: institute.com/downtown)
     - Description: "Our flagship downtown location specializing in UPSC preparation"
     - Branch Type: "Physical Location" / "Virtual Department" / "Subject-Specific"

     Location Information:
     - Address: "123 Main Street, Downtown, City - 123456"
     - Phone: "******-567-8901"
     - Email: "<EMAIL>"
     - Operating Hours: "9:00 AM - 9:00 PM (Mon-Sat)"
     - Capacity: "200 students"

     Branch Manager Assignment:
     - Assign existing trainer as branch manager
     - Set branch-specific permissions and access levels
     - Configure reporting hierarchy
     - Define decision-making authority
     ```

2. **Advanced Branch Configuration**
   ```
   URL and Access Settings:
   - Primary URL: institute.com/downtown
   - Alternative URL: downtown.institute.com (if subdomain enabled)
   - Mobile App Branch Code: "DLC"
   - QR Code Access: Generate branch-specific QR codes

   Branding Customization:
   - Institute Logo: Uses main institute logo (consistent across all branches)
   - Color Scheme: Customize primary/secondary colors for branch identity
   - Contact Information: Branch-specific phone, email, address
   - Social Media: Branch-specific social media links
   - Local Language: Set regional language preferences

   Operational Settings:
   - Timezone: Set local timezone for the branch
   - Currency: Local currency for payments (if different)
   - Academic Calendar: Branch-specific holidays and schedules
   - Notification Preferences: Local communication channels
   ```

3. **Course and Content Management**
   - **Course Assignment**: Assign specific courses to branches
   - **Content Localization**: Branch-specific study materials
   - **Exam Scheduling**: Branch-wise exam calendars
   - **Live Class Coordination**: Branch-specific class schedules
   - **Resource Allocation**: Assign trainers and resources per branch

4. **Comprehensive Branch Analytics**
   ```
   Performance Metrics:
   - Student enrollment and retention by branch
   - Course completion rates per branch
   - Revenue generation and profitability
   - Trainer performance and utilization
   - Local market penetration analysis

   Comparative Analysis:
   - Cross-branch performance comparison
   - Best practices identification
   - Resource optimization opportunities
   - Growth trend analysis
   - ROI assessment per branch
   ```

### Advanced Branch User Management
1. **Strategic User Assignment**
   ```
   Student Management:
   - Bulk assign students to specific branches based on location
   - Transfer students between branches (with academic record preservation)
   - Set cross-branch access for students taking multiple courses
   - Manage branch-specific student groups and batches
   - Handle branch transfers and academic continuity

   Trainer Assignment:
   - Assign trainers to primary and secondary branches
   - Set cross-branch teaching permissions
   - Manage trainer workload distribution
   - Configure branch-specific trainer roles
   - Handle trainer mobility between branches
   ```

2. **Branch Management Hierarchy**
   ```
   Branch Manager Role:
   - Limited institute access with branch-specific permissions
   - User management within assigned branch only
   - Course and content approval for branch
   - Local analytics and reporting access
   - Student support and query resolution

   Branch Staff Roles:
   - Branch Coordinator: Administrative support
   - Branch Trainer: Teaching and content creation
   - Branch Support: Student assistance and technical help
   - Branch Accountant: Local billing and payment management
   ```

3. **Cross-Branch Operations**
   - **Shared Resources**: Allow trainers to teach across multiple branches
   - **Student Mobility**: Enable students to attend classes at different branches
   - **Content Sharing**: Share courses and materials between branches
   - **Unified Reporting**: Consolidated analytics across all branches
   - **Resource Optimization**: Balance workload and capacity across branches

### Branch Implementation Examples

#### Example 1: Geographic Branches
```
ABC Coaching Institute - Multi-City Operations:

Main Institute: "ABC Coaching Institute"
├── Branch 1: "ABC Downtown" (downtown.abc-coaching.com)
│   ├── Focus: UPSC & State PSC
│   ├── Capacity: 300 students
│   └── Manager: Dr. Smith
├── Branch 2: "ABC Suburbs" (suburbs.abc-coaching.com)
│   ├── Focus: Banking & SSC
│   ├── Capacity: 200 students
│   └── Manager: Prof. Johnson
└── Branch 3: "ABC Online" (online.abc-coaching.com)
    ├── Focus: All courses (virtual)
    ├── Capacity: Unlimited
    └── Manager: Ms. Davis
```

#### Example 2: Subject-Specific Branches
```
Excellence Academy - Department-Based Organization:

Main Institute: "Excellence Academy"
├── Branch 1: "Engineering Division" (engineering.excellence.com)
│   ├── Courses: JEE, GATE, ESE
│   └── Specialized trainers and labs
├── Branch 2: "Medical Division" (medical.excellence.com)
│   ├── Courses: NEET, AIIMS, JIPMER
│   └── Medical faculty and resources
└── Branch 3: "Management Division" (management.excellence.com)
    ├── Courses: CAT, XAT, GMAT
    └── Industry expert trainers
```

#### Example 3: Hybrid Model
```
Smart Learning Hub - Mixed Approach:

Main Institute: "Smart Learning Hub"
├── Branch 1: "North Campus" (Physical + Online)
├── Branch 2: "South Campus" (Physical + Online)
├── Branch 3: "Corporate Training" (B2B Services)
└── Branch 4: "International Students" (Global Online)
```

---

## 🌐 Institute Website Management

### Understanding Your Website Structure
Your institute gets a complete website with two distinct parts:

1. **Landing Pages (Theme-based)** - You can customize these
   - Homepage, About, Contact, Blog pages
   - Choose from Super Admin's theme gallery
   - Customize colors, content, and branding

2. **Course Marketplace (Platform Standard)** - Consistent across all institutes
   - Amazon-style course browsing and filtering
   - Course detail pages and purchase flow
   - Student dashboard and progress tracking
   - Same UI/UX for all institutes on the platform

### Landing Page Theme Selection
1. **Choosing a Theme from Gallery**
   ```
   Theme Selection Process:
   1. Navigate to "Settings" → "Landing Page" → "Theme Gallery"
   2. Browse available institute themes with preview images
   3. Click on theme cards to see detailed previews
   4. View live demo with sample content
   5. Check theme features and customization options
   6. Click "Select This Theme" to apply to landing pages only
   ```

2. **Enhanced Theme Gallery Interface**
   ```
   Theme Display with Multiple Previews:
   ┌─────────────────────────────────────────────────────────────┐
   │                    Modern Academy Theme                     │
   │ ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
   │ │ Landing Page│ Marketplace │Course Detail│  Checkout   │   │
   │ │[Preview Img]│[Preview Img]│[Preview Img]│[Preview Img]│   │
   │ └─────────────┴─────────────┴─────────────┴─────────────┘   │
   │ Education Category | ⭐⭐⭐⭐⭐ (4.8/5) | 156 institutes    │
   │ Features: Course Marketplace, Filters, Cart, Reviews       │
   │ [👁️ Live Demo] [📱 Mobile Preview] [✅ Select Theme]        │
   └─────────────────────────────────────────────────────────────┘

   ┌─────────────────────────────────────────────────────────────┐
   │                   Coaching Pro Theme                        │
   │ ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
   │ │ Landing Page│ Marketplace │Course Detail│  Dashboard  │   │
   │ │[Preview Img]│[Preview Img]│[Preview Img]│[Preview Img]│   │
   │ └─────────────┴─────────────┴─────────────┴─────────────┘   │
   │ Professional Category | ⭐⭐⭐⭐⭐ (4.9/5) | 89 institutes  │
   │ Features: Advanced Filters, Bulk Purchase, Analytics       │
   │ [👁️ Live Demo] [📱 Mobile Preview] [✅ Select Theme]        │
   └─────────────────────────────────────────────────────────────┘

   Enhanced Features:
   - Multiple preview images for each page type
   - Live demo functionality for complete user journey
   - Mobile preview option
   - Feature list showing marketplace capabilities
   - Usage statistics from other institutes
   - Filter by marketplace features (filters, cart, reviews, etc.)
   ```

3. **Theme Application Scope**
   ```
   What Gets Themed:
   ✅ Homepage (institute marketing)
   ✅ About Us page
   ✅ Contact page
   ✅ Blog/News page
   ✅ Branch-specific pages

   What Stays Standard:
   🔒 Course marketplace (Amazon-style listing)
   🔒 Course detail pages
   🔒 Shopping cart and checkout
   🔒 Student dashboard
   🔒 Live classes interface
   🔒 Exam and assessment pages
   ```

4. **Theme Application Process**
   - Apply theme to landing pages only (main institute and all branches)
   - Automatic backup of current theme before switching
   - Preview changes before making them live
   - Rollback option if needed
   - Course marketplace remains unchanged

4. **Theme Customization After Selection**
   ```
   Branding Customization:
   - Upload institute logo and favicon (used across all branches)
   - Set primary and secondary colors (can be customized per branch)
   - Choose fonts and typography (consistent across institute)
   - Add custom CSS for advanced styling
   - Customize theme-specific elements (buttons, cards, sections)

   Content Management:
   - Hero section with compelling headline
   - About us section with institute story
   - Course catalog with search and filters
   - Testimonials and success stories
   - Contact information and inquiry forms
   - Branch-specific content sections
   ```

5. **Theme Preview and Publishing**
   ```
   Preview Process:
   1. Make customizations to selected theme
   2. Use "Preview" mode to see changes
   3. Test on different devices (mobile, tablet, desktop)
   4. Review all pages and sections
   5. Click "Publish" to make changes live
   6. Monitor performance and user feedback
   ```

3. **Course Discovery Integration**
   - Configure course catalog display
   - Set up advanced search functionality
   - Enable filtering by subject, price, difficulty
   - Add course preview capabilities
   - Integrate direct enrollment buttons

### SEO and Performance Optimization
1. **SEO Settings**
   - Meta titles and descriptions
   - Open Graph and Twitter Card settings
   - Structured data for course listings
   - Sitemap generation and submission
   - Google Analytics integration

2. **Performance Monitoring**
   - Page load speed optimization
   - Mobile responsiveness testing
   - Conversion rate tracking
   - User behavior analytics
   - A/B testing for different layouts

### Blog Management
1. **Content Creation**
   - Write educational blog posts
   - Share exam tips and strategies
   - Announce important updates
   - Feature student success stories

2. **Content Scheduling**
   - Plan content calendar
   - Schedule posts in advance
   - Manage categories and tags
   - Monitor engagement metrics

---

## 📊 Analytics & Reports

### Institute Dashboard
1. **Key Metrics**
   - Total students and active users
   - Course completion rates
   - Exam performance averages
   - Revenue and growth trends

2. **Usage Analytics**
   - Most popular courses and content
   - Peak usage times and patterns
   - Device and platform preferences
   - Geographic distribution of students

### Custom Reports
1. **Academic Reports**
   - Student progress reports
   - Course effectiveness analysis
   - Trainer performance metrics
   - Exam result summaries

2. **Business Reports**
   - Revenue and financial analysis
   - Marketing campaign effectiveness
   - Student acquisition costs
   - Retention and churn analysis

---

## 🔔 Communication & Notifications

### Notification Management
1. **Notification Channels**
   - Email notifications
   - WhatsApp messages
   - SMS alerts
   - Push notifications (mobile app)

2. **Message Templates**
   - Welcome messages for new students
   - Class reminders and schedules
   - Exam notifications and results
   - Payment confirmations and receipts

### Community Management
1. **Discussion Forums**
   - Create subject-wise discussion groups
   - Moderate conversations and content
   - Encourage peer-to-peer learning
   - Handle disputes and inappropriate content

2. **Student Engagement**
   - Organize virtual events and competitions
   - Share motivational content
   - Celebrate student achievements
   - Gather feedback and suggestions

---

## 🛠️ Technical Settings

### Payment Gateway Management

#### **Gateway Selection and Setup**
1. **Viewing Available Gateways**
   ```
   Navigate to: Settings → Payment Gateways

   Available Gateways List:
   ┌─────────────────────────────────────────────────────────────┐
   │ Gateway Details                                             │
   ├─────────────────────────────────────────────────────────────┤
   │ 🌍 Stripe                                                   │
   │ Countries: Global (135+ countries)                         │
   │ Currencies: USD, EUR, GBP, INR, AUD, CAD, etc.            │
   │ Features: Cards, Digital Wallets, Bank Transfers          │
   │ Documentation: stripe.com/docs                             │
   │ [📋 View Requirements] [⚙️ Configure]                      │
   ├─────────────────────────────────────────────────────────────┤
   │ 🇮🇳 Razorpay                                                │
   │ Countries: India                                           │
   │ Currencies: INR                                            │
   │ Features: UPI, Cards, Net Banking, Wallets                │
   │ Documentation: razorpay.com/docs                          │
   │ [📋 View Requirements] [⚙️ Configure]                      │
   └─────────────────────────────────────────────────────────────┘
   ```

2. **Gateway Configuration Process**
   ```
   Step-by-Step Configuration:

   Step 1: Select Gateway
   - Click "Configure" on desired gateway
   - Review requirements and documentation

   Step 2: Gather API Credentials
   - Create account with payment provider
   - Generate API keys from their dashboard
   - Note down required credentials

   Step 3: Configure in Platform
   - Enter API credentials securely
   - Set optional preferences
   - Configure webhook URLs

   Step 4: Test Configuration
   - Run test transactions
   - Verify webhook delivery
   - Check payment confirmations

   Step 5: Go Live
   - Switch from test mode to live mode
   - Enable gateway for student payments
   - Monitor transaction success rates
   ```

#### **Detailed Gateway Setup Guides**

**Stripe Setup Guide:**
```
1. Create Stripe Account:
   - Visit stripe.com and create business account
   - Complete business verification
   - Activate your account

2. Get API Keys:
   - Go to Stripe Dashboard → Developers → API Keys
   - Copy Publishable Key: pk_live_51H...
   - Copy Secret Key: sk_live_51H...
   - Create Webhook Endpoint for payment confirmations

3. Platform Configuration:
   Required Fields:
   ✓ Publishable Key: pk_live_51H...
   ✓ Secret Key: sk_live_51H...
   ✓ Webhook Secret: whsec_...

   Optional Fields:
   ✓ Statement Descriptor: "ABC LEARNING"
   ✓ Capture Method: "Automatic"

4. Test Payment:
   - Use test card: ************** 4242
   - Verify payment success
   - Check webhook delivery
```

**Razorpay Setup Guide:**
```
1. Create Razorpay Account:
   - Visit razorpay.com and create business account
   - Complete KYC verification
   - Activate your account

2. Get API Keys:
   - Go to Razorpay Dashboard → Settings → API Keys
   - Copy Key ID: rzp_live_...
   - Copy Key Secret: ...
   - Set up webhook for payment notifications

3. Platform Configuration:
   Required Fields:
   ✓ Key ID: rzp_live_...
   ✓ Key Secret: ...
   ✓ Webhook Secret: ...

   Optional Fields:
   ✓ Theme Color: #3399cc
   ✓ Company Name: "ABC Learning Institute"

4. Test Payment:
   - Use test UPI ID: success@razorpay
   - Verify payment success
   - Check notification delivery
```

#### **Gateway Management Dashboard**
```
Payment Gateway Status:
┌─────────────────────────────────────────────────────────────┐
│ Gateway Name    │ Status │ Mode │ Last Transaction │ Success Rate │
├─────────────────────────────────────────────────────────────┤
│ Stripe          │ ✅ Live │ Live │ 2 hours ago     │ 98.5%       │
│ Razorpay        │ 🧪 Test │ Test │ 1 day ago       │ 100%        │
│ PayPal          │ ❌ Off  │ -    │ Never           │ -           │
└─────────────────────────────────────────────────────────────┘

Actions Available:
- [⚙️ Configure] [🧪 Test] [📊 Analytics] [❌ Disable]
```

#### **Payment Analytics and Monitoring**
```
Payment Performance Dashboard:
- Total Transactions: 1,234 this month
- Success Rate: 98.5% (industry average: 95%)
- Failed Payments: 18 (reasons: insufficient funds, expired cards)
- Average Transaction Value: ₹2,450
- Peak Payment Hours: 6 PM - 9 PM
- Popular Payment Methods: UPI (45%), Cards (35%), Net Banking (20%)
```

### Integration Management
1. **Third-Party Services**
   - Zoom integration for live classes
   - Email service provider setup
   - WhatsApp Business API
   - CDN and storage services

2. **API Access**
   - Generate API keys (if available)
   - Configure webhook endpoints
   - Monitor API usage and limits
   - Integrate with external systems

### Security Settings
1. **Access Control**
   - Two-factor authentication
   - Password policies
   - Session management
   - IP restrictions (if needed)

2. **Data Protection**
   - Student data privacy settings
   - Content protection and watermarking
   - Backup and recovery options
   - GDPR compliance tools

---

## 📋 Best Practices

### Daily Tasks
- [ ] Review new student registrations
- [ ] Check course approval queue
- [ ] Monitor live class schedules
- [ ] Respond to student queries
- [ ] Review platform notifications

### Weekly Tasks
- [ ] Analyze student engagement metrics
- [ ] Review trainer performance
- [ ] Update course content and schedules
- [ ] Plan marketing campaigns
- [ ] Conduct team meetings

### Monthly Tasks
- [ ] Generate comprehensive reports
- [ ] Review financial performance
- [ ] Plan new course offerings
- [ ] Evaluate platform usage
- [ ] Update institute policies

---

## 🆘 Support and Troubleshooting

### Common Issues
1. **Login Problems**: Password reset, account lockouts
2. **Content Upload**: File format issues, size limits
3. **Payment Issues**: Failed transactions, refund requests
4. **Technical Problems**: Video playback, mobile app issues

### Getting Help
- **Platform Support**: <EMAIL>
- **Technical Issues**: <EMAIL>
- **Billing Questions**: <EMAIL>
- **Knowledge Base**: help.groups-exam.com

---

# 📜 Roadmap: Institute Admin Panel - Groups Exam LMS SaaS

## 📌 Phase 1: Core Institute Setup

**Objective**: Enable Institute Admins to onboard, configure settings, and prepare the institute.

### Tasks:
- Institute Admin Login (via subdomain or custom domain)
- Institute Profile Management
- Branding: logo, color, and contact details
- Subscription Plan View + Billing History
- Domain Configuration (via Super Admin approval)
- Institute-specific Dashboard

---

## 📌 Phase 2: User Role Management (Tutors & Students)

**Objective**: Full control over user onboarding and role access.

### Tasks:
- Trainer Creation (roles: Trainer, Mentor, Educator)
- Trainer-specific permissions: [Create Course, Conduct Exam, Live Class, Analytics]
- Student Management: Add, import, group, or invite students
- Role-based access control (RBAC)
- Device restriction login (1 mobile + 1 desktop)

### Roles & Permissions

| Role                | Permissions                                                                    | Managed By          |
| ------------------- | ------------------------------------------------------------------------------ | ------------------- |
| **Institute Admin** | Full access to manage courses, users, billing, branches, and domain            | Super Admin         |
| **Trainer**         | Create courses, conduct live classes, manage quizzes and mock exams            | **Institute Admin** |
| **Mentor**          | Assist trainers, interact with students, moderate discussions                  | **Institute Admin** |
| **Educator**        | Upload educational material, participate in live classes                       | **Institute Admin** |
| **Student**         | Access assigned courses, take exams, track progress                            | **Institute Admin** |
| **Branch Manager**  | Manage users and content within a specific branch                              | **Institute Admin** |
| **Sub-admin Roles** | Custom permissions (e.g., Billing Admin, Support Admin) defined by Super Admin | Super Admin         |

> Permissions follow CRUD structure and are scoped per role/module.

---

## 📌 Phase 3: Branch Management

**Objective**: Institute can manage multiple branches with decentralization and analytics.

### Tasks:
- Add/Edit/Delete Branches
- Assign Trainers and Students to branches
- Branch-level customization (subdomain, colors, contact info - uses institute logo)
- Branch Manager Role with limited permissions
- Branch-specific analytics and reports
- Course access limitation by branch

---

## 📌 Phase 4: Course Creation & Verification Flow

**Objective**: Build scalable and quality-controlled course management.

### Tasks:
- Course Builder: Create title, structure (exam > subject > subtitle > lesson)
- Content Types: Video, Document, Quiz, Mock Exam
- 3-Level Content Verification Flow: Draft > Verify > Approve > Publish
- Lock/Unlock content based on lesson progression
- Multi-language and bulk upload support

---

## 📌 Phase 5: Custom Domain & Landing Page

**Objective**: Allow each institute to operate under its own brand.

### Tasks:
- Request custom domain (ex: `learn.abcacademy.com`)
- Super Admin approval flow
- SSL provisioning
- Institute Landing Page Builder:
  - Theme selector
  - Section-based editor: Home, About, Contact, Blog
  - SEO fields + Analytics + Mobile responsive

---

## 📌 Phase 6: Live Class & Exam Management

**Objective**: Enable interactive and scheduled learning.

### Tasks:
- Zoom & YouTube Live integration
- Schedule & Notify for Live Classes
- Live Class Poll + Leaderboard + Attendance
- Mock Exams, Quizzes, Descriptive Paper
- Multi-language questions with OMR-style PDF
- Live Exam with countdown, branding, and analytics

---

## 📌 Phase 7: Marketplace & Course Monetization

**Objective**: Let institute admins monetize content or purchase shared content.

### Tasks:
- Internal vs Marketplace Courses toggle
- Purchase content from other institutes (with edit-lock)
- Revenue Share configuration (duration-based)
- Referral system for trainers
- E-book sales and Add-on items in course

---

## 📌 Phase 8: Analytics & Reporting

**Objective**: Provide performance insights for data-driven growth.

### Tasks:
- Institute-wide Dashboard
- Trainer Performance Report
- Student Progress Report
- Exam Analytics
- Revenue Dashboard
- Branch-wise Report
- Referral Analytics

---

## 📌 Phase 9: Communication & Engagement

**Objective**: Strengthen institute-student communication.

### Tasks:
- Announcement System (Email, WhatsApp, SMS, Push)
- Community Forum (subject-wise)
- Notification Templates (payment, exam, class)
- Blog/Article management (student/trainer-generated)
- Feedback and review system

---

## 📌 Phase 10: Advanced Configuration & Security

**Objective**: Ensure privacy, integrity, and scalability.

### Tasks:
- Two-Factor Authentication
- Watermarking content
- IP/device/session restrictions
- GDPR & data privacy settings
- API & Webhook access
- Backup and restore tools

---

## 🚀 Tech Stack Alignment

- **Frontend**: Next.js + ShadCN + TailwindCSS
- **Backend**: Payload CMS
- **Video/Live**: Zoom API, YouTube Live
- **Payments**: Razorpay, Stripe, UPI, QR
- **Notifications**: WhatsApp Business API, SMTP, Firebase Push
- **Custom Domain Handling**: Cloudflare/Direct DNS with Let's Encrypt or AWS ACM

---

## 🏢 Institute Registration & Domain Activation Flow

### 🛠️ Step 1: Institute Registration (via LMS Landing Page)

- Institute visits the main platform (e.g., `https://groups-exam.com`)
- Fills out **Institute Signup Form**:
  - Institute Name, Email, Phone
  - Requested Subdomain (e.g., `abc.groups-exam.com`)
  - Basic branding info (logo, tagline, categories)
- Institute created in **`Pending Setup`** status

### 🛠️ Step 2: Initial Setup (Preview Mode)

- Admin logs in via default subdomain: `https://abc.groups-exam.com/in-admin`
- Fills full profile, branding, courses, trainers, branches
- Sets up landing page preview
- Previews entire setup using system-provided subdomain

### 🛠️ Step 3: Request Custom Domain

- Navigate to **Settings → Domain Configuration**
- Enter desired domain (e.g., `learn.abcacademy.com`)
- System checks DNS and flags readiness
- Super Admin receives request and institute status is **`Waiting for Domain Activation`**

### 🛠️ Step 4: Super Admin Approval

- Super Admin verifies DNS and binds the domain
- SSL certificate setup (Let's Encrypt / AWS ACM)
- Domain marked as **Active**

### 🛠️ Step 5: Institute Access via Custom Domain

- Public: `https://learn.abcacademy.com`
- Admin Panel: `https://learn.abcacademy.com/in-admin`
- All landing content and institute settings go live

> 🎉 Institute is now live with custom domain and production access.

---

## 📆 Institute Admin Operational Workflows

### 🗂️ Daily Operations

**Morning Routine (9:00 AM - 10:00 AM)**
- [ ] **System Health Check**
  - Review overnight notifications and alerts
  - Check server status and platform performance
  - Verify payment gateway and integration status

- [ ] **Student Management**
  - Review new student registrations from previous day
  - Approve pending student applications
  - Process student queries and support tickets
  - Check student attendance for scheduled classes

- [ ] **Content Review**
  - Review trainer-submitted content awaiting approval
  - Check course materials for quality and compliance
  - Approve or request revisions for pending content
  - Monitor live class schedules for the day

**Midday Operations (12:00 PM - 1:00 PM)**
- [ ] **Live Class Monitoring**
  - Ensure scheduled live classes are running smoothly
  - Monitor attendance and engagement metrics
  - Handle technical issues or student complaints
  - Coordinate with trainers for any last-minute changes

- [ ] **Communication Management**
  - Send daily announcements to students
  - Respond to urgent emails and messages
  - Update social media and communication channels
  - Handle parent/guardian inquiries

**Evening Review (6:00 PM - 7:00 PM)**
- [ ] **Performance Analytics**
  - Review daily engagement metrics
  - Check course completion rates
  - Monitor exam performance and results
  - Analyze student feedback and ratings

- [ ] **Administrative Tasks**
  - Update course schedules and announcements
  - Process refund requests and billing issues
  - Review trainer performance reports
  - Plan next day's activities and priorities

### 📅 Weekly Operations

**Monday - Planning & Strategy**
- [ ] **Weekly Planning Session**
  - Review previous week's performance metrics
  - Set goals and targets for the current week
  - Plan marketing campaigns and promotional activities
  - Schedule trainer meetings and training sessions

- [ ] **Content Strategy**
  - Plan new course launches and content updates
  - Review content calendar and publishing schedule
  - Coordinate with trainers for content creation
  - Update landing page and marketing materials

**Wednesday - Mid-Week Review**
- [ ] **Performance Analysis**
  - Analyze student engagement and retention metrics
  - Review course performance and completion rates
  - Check financial performance and revenue trends
  - Assess trainer productivity and effectiveness

- [ ] **System Maintenance**
  - Review platform performance and optimization
  - Update software and security patches
  - Backup important data and configurations
  - Test disaster recovery procedures

**Friday - Week Wrap-up**
- [ ] **Weekly Reports**
  - Generate comprehensive performance reports
  - Analyze student feedback and satisfaction scores
  - Review financial statements and billing reports
  - Prepare weekly summary for stakeholders

- [ ] **Team Communication**
  - Conduct weekly team meetings with trainers
  - Share performance insights and feedback
  - Plan weekend activities and special events
  - Address any concerns or issues raised

### 📆 Monthly Operations

**First Week - Strategic Planning**
- [ ] **Monthly Business Review**
  - Analyze monthly performance against targets
  - Review student acquisition and retention rates
  - Assess revenue growth and profitability
  - Evaluate market trends and competition

- [ ] **Content and Curriculum Review**
  - Review and update course curriculum
  - Plan new course offerings based on demand
  - Evaluate trainer performance and feedback
  - Update pricing strategies and promotional offers

**Second Week - Marketing and Outreach**
- [ ] **Marketing Campaign Analysis**
  - Review effectiveness of current marketing campaigns
  - Plan new marketing initiatives and strategies
  - Update website content and SEO optimization
  - Engage with community and social media followers

- [ ] **Partnership and Collaboration**
  - Explore new partnership opportunities
  - Review existing partnerships and collaborations
  - Plan joint ventures and co-marketing activities
  - Network with industry professionals and peers

**Third Week - Operations Optimization**
- [ ] **Process Improvement**
  - Review and optimize operational processes
  - Implement new tools and technologies
  - Train staff on new procedures and systems
  - Update policies and standard operating procedures

- [ ] **Quality Assurance**
  - Conduct quality audits of courses and content
  - Review student feedback and satisfaction surveys
  - Implement improvements based on feedback
  - Ensure compliance with educational standards

**Fourth Week - Financial and Administrative**
- [ ] **Financial Review**
  - Process monthly financial statements
  - Review budget performance and variances
  - Plan for upcoming expenses and investments
  - Process trainer commissions and payouts

- [ ] **Administrative Tasks**
  - Update institute policies and procedures
  - Review and renew contracts and agreements
  - Plan for upcoming events and activities
  - Prepare monthly reports for stakeholders

---

## 🎯 Key Performance Indicators (KPIs) for Institute Admins

### 📊 Student Metrics
- **Student Enrollment Rate**: New students per month
- **Student Retention Rate**: Percentage of students completing courses
- **Student Satisfaction Score**: Average rating from student feedback
- **Course Completion Rate**: Percentage of students finishing courses
- **Student Engagement**: Average time spent on platform per student

### 💰 Financial Metrics
- **Monthly Recurring Revenue (MRR)**: Predictable monthly income
- **Average Revenue Per User (ARPU)**: Revenue per student
- **Customer Acquisition Cost (CAC)**: Cost to acquire new students
- **Lifetime Value (LTV)**: Total revenue from a student over time
- **Profit Margin**: Percentage of revenue retained as profit

### 🎓 Academic Metrics
- **Course Quality Score**: Average rating of courses
- **Trainer Performance**: Student ratings and feedback for trainers
- **Exam Pass Rate**: Percentage of students passing exams
- **Content Engagement**: Views, downloads, and interaction rates
- **Live Class Attendance**: Average attendance for live sessions

### 🚀 Growth Metrics
- **User Growth Rate**: Month-over-month user growth
- **Course Catalog Growth**: New courses added per month
- **Market Share**: Position in local/regional education market
- **Brand Recognition**: Social media followers and engagement
- **Referral Rate**: New students from existing student referrals

---

## 📋 Institute Admin Checklist Templates

### 🌅 Daily Checklist
```
Date: ___________

Morning (9:00 AM - 10:00 AM)
□ Check overnight notifications and alerts
□ Review new student registrations
□ Approve pending student applications
□ Check live class schedules for today
□ Review trainer-submitted content

Midday (12:00 PM - 1:00 PM)
□ Monitor ongoing live classes
□ Respond to urgent student queries
□ Send daily announcements
□ Handle technical support issues

Evening (6:00 PM - 7:00 PM)
□ Review daily performance metrics
□ Check course completion rates
□ Process billing and payment issues
□ Plan tomorrow's priorities

Notes:
_________________________________
_________________________________
```

### 📅 Weekly Checklist
```
Week of: ___________

Monday - Planning
□ Weekly planning session
□ Set weekly goals and targets
□ Plan marketing campaigns
□ Schedule trainer meetings

Wednesday - Review
□ Analyze mid-week performance
□ Review student engagement
□ Check financial metrics
□ System maintenance tasks

Friday - Wrap-up
□ Generate weekly reports
□ Conduct team meetings
□ Plan weekend activities
□ Address concerns and issues

Weekly Summary:
_________________________________
_________________________________
```

### 📆 Monthly Checklist
```
Month: ___________

Week 1 - Strategic Planning
□ Monthly business review
□ Curriculum and content review
□ Performance analysis
□ Goal setting for next month

Week 2 - Marketing & Outreach
□ Marketing campaign analysis
□ Partnership opportunities
□ Community engagement
□ SEO and content updates

Week 3 - Operations Optimization
□ Process improvement initiatives
□ Quality assurance audits
□ Staff training and development
□ Policy updates

Week 4 - Financial & Administrative
□ Financial statements review
□ Budget analysis
□ Commission processing
□ Monthly reporting

Monthly Achievements:
_________________________________
_________________________________

Areas for Improvement:
_________________________________
_________________________________
```

---

> This comprehensive guide provides complete coverage of Institute Admin functions, from daily operations to strategic planning. For specific technical issues or advanced configurations, refer to the technical documentation or contact platform support.
