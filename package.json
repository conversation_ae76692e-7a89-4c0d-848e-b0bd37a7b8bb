{"name": "groups-exam-lms", "version": "1.0.0", "description": "Groups Exam LMS - Multi-tenant Learning Management System", "private": true, "directories": {"doc": "docs"}, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev:clean": "cleanup-ports.bat && turbo run dev", "start": "turbo run start", "lint": "turbo run lint", "clean": "turbo run clean", "cleanup-ports": "cleanup-ports.bat", "api:dev": "cd apps/api && npm run dev", "api:build": "cd apps/api && npm run build", "api:start": "cd apps/api && npm run start", "frontend:dev": "cd apps/frontend && npm run dev", "frontend:build": "cd apps/frontend && npm run build", "frontend:start": "cd apps/frontend && npm run start", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:api": "playwright test tests/api/", "test:e2e": "playwright test tests/e2e/", "test:course-builder": "playwright test tests/e2e/course-builder.spec.ts", "test:lesson-management": "playwright test tests/e2e/lesson-management.spec.ts", "test:mobile": "playwright test --project=mobile-chrome", "test:firefox": "playwright test --project=firefox", "test:webkit": "playwright test --project=webkit", "test:report": "playwright show-report", "test:install": "playwright install", "test:install-deps": "playwright install-deps"}, "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "task-master-ai": "^0.19.0", "turbo": "^2.0.0", "typescript": "^5.0.0"}, "packageManager": "pnpm@8.15.0", "workspaces": ["apps/*", "packages/*", "components", "stores", "lib", "types", "hooks"], "keywords": ["lms", "learning-management-system", "multi-tenant", "saas", "education", "payload-cms", "nextjs", "react"], "author": "Groups Exam Team", "license": "MIT"}