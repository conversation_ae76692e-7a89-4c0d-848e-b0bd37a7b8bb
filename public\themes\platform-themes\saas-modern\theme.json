{"name": "SaaS Modern", "slug": "saas-modern", "version": "1.0.0", "description": "Modern SaaS platform theme with clean design and professional layout", "category": "saas_modern", "type": "platform", "preview_image": "/themes/platform-themes/saas-modern/preview.jpg", "demo_image": "/themes/platform-themes/saas-modern/demo.jpg", "colors": {"primary": "#3b82f6", "secondary": "#64748b", "accent": "#10b981", "background": "#ffffff", "text": "#1f2937", "muted": "#6b7280", "border": "#e5e7eb", "success": "#10b981", "warning": "#f59e0b", "error": "#ef4444"}, "fonts": {"heading": "Inter, sans-serif", "body": "Inter, sans-serif", "mono": "Fira Code, monospace"}, "features": ["responsive_design", "seo_optimized", "fast_loading", "modern_ui", "accessibility_ready", "dark_mode_support"], "pages": ["homepage", "features", "pricing", "about", "contact", "blog", "documentation"], "components": ["PlatformHeader", "Hero", "Features", "Pricing", "Testimonials", "Footer", "Contact"], "customizable_elements": {"colors": {"primary": true, "secondary": true, "accent": true}, "fonts": {"heading": true, "body": true}, "logo": true, "hero_content": true, "contact_info": true}, "requirements": {"nextjs": ">=14.0.0", "react": ">=18.0.0", "tailwindcss": ">=3.0.0"}, "author": {"name": "Groups Exam LMS", "email": "<EMAIL>", "website": "https://groupsexam.com"}, "license": "MIT", "tags": ["saas", "modern", "professional", "clean", "responsive", "business"]}