// Script to set up hello.local:3000 institute for testing

const API_URL = 'http://localhost:3001'

async function setupHelloInstitute() {
  try {
    console.log('🏫 Setting up hello.local:3000 institute...')

    // Get the first institute from the list
    console.log('📋 Getting institutes...')
    const institutesResponse = await fetch(`${API_URL}/api/institutes`)
    const institutesResult = await institutesResponse.json()
    
    if (!institutesResponse.ok || !institutesResult.docs || institutesResult.docs.length === 0) {
      throw new Error('No institutes found')
    }

    // Take the first institute
    const institute = institutesResult.docs[0]
    console.log('✅ Found institute:', institute.name, '(ID:', institute.id + ')')

    // Update the institute directly in the database using Payload admin API
    console.log('🔄 Updating institute with custom domain...')
    
    // Use Payload's admin API to update the institute
    const updateData = {
      customDomain: 'hello.local:3000',
      domainVerified: true,
      isActive: true
    }

    console.log('Update data:', updateData)
    
    // Since we can't authenticate easily, let's use the Payload admin interface
    console.log('📝 Please manually update the institute in Payload admin:')
    console.log('1. Go to: http://localhost:3001/admin/collections/institutes')
    console.log('2. Edit the first institute:', institute.name)
    console.log('3. Set Custom Domain to: hello.local:3000')
    console.log('4. Set Domain Verified to: true')
    console.log('5. Set Is Active to: true')
    console.log('6. Save the changes')
    
    // Test the domain resolution after manual update
    console.log('\n🔍 After manual update, test domain resolution:')
    console.log('URL: http://localhost:3001/api/institutes/by-domain?domain=hello.local:3000')
    
    console.log('\n🌐 Then test the institute landing page:')
    console.log('URL: http://hello.local:3000/')

  } catch (error) {
    console.error('❌ Error setting up institute:', error)
  }
}

// Run the script
setupHelloInstitute()
