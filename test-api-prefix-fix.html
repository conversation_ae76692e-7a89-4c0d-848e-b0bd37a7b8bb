<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 API Prefix Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-box {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid;
        }
        .comparison-box.before {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .comparison-box.after {
            border-color: #28a745;
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API Prefix Fix Test</h1>
        <p>Test that the `/api` prefix issue in upload URLs is fixed.</p>
        
        <div class="success">
            <strong>✅ Fixed:</strong> Removed `/api` prefix from frontend upload calls<br>
            - Changed `/api/upload` → `/upload`<br>
            - Changed `/api/upload/my-files` → `/upload/my-files`<br>
            - Changed `/api/upload/{id}` → `/upload/{id}`<br>
            - Should prevent Next.js rewrite from adding `/api` to response URLs
        </div>
    </div>

    <div class="container">
        <h3>🔍 Before vs After Comparison</h3>
        <div class="comparison">
            <div class="comparison-box before">
                <h4>❌ Before Fix</h4>
                <p><strong>Frontend calls:</strong><br>
                <code>/api/upload</code></p>
                <p><strong>Next.js rewrite:</strong><br>
                Adds `/api` to response URLs</p>
                <p><strong>Response URLs:</strong><br>
                <code>/api/media/files/filename.jpg</code></p>
            </div>
            <div class="comparison-box after">
                <h4>✅ After Fix</h4>
                <p><strong>Frontend calls:</strong><br>
                <code>/upload</code></p>
                <p><strong>Direct backend:</strong><br>
                No rewrite interference</p>
                <p><strong>Response URLs:</strong><br>
                <code>/media/folder/filename.jpg</code></p>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>📁 Test File Upload</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select a file to test the fixed upload URLs</p>
            <p style="color: #666; font-size: 14px;">Should return clean URLs without `/api` prefix</p>
            <input type="file" id="fileInput" class="hidden">
        </div>

        <button class="btn success" onclick="testFixedUpload()" id="uploadBtn" disabled>Test Fixed Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🧪 Test Different Upload Types</h3>
        <p>Test various upload types to ensure all return clean URLs:</p>
        
        <button class="btn" onclick="testUploadType('avatar')">Test Avatar Upload</button>
        <button class="btn" onclick="testUploadType('document')">Test Document Upload</button>
        <button class="btn" onclick="testUploadType('course_thumbnail')">Test Course Thumbnail</button>
        <button class="btn" onclick="testUploadType('general')">Test General Upload</button>
        
        <div id="typeTestResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testFixedUpload() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, 'document', 'Fixed Upload Test');
        }

        async function testUploadType(uploadType) {
            if (!selectedFile) {
                showTypeTestResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, uploadType, `${uploadType} Upload Test`, true);
        }

        async function testUploadWithFile(file, uploadType, testName, useTypeResult = false) {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                const showResultFunc = useTypeResult ? showTypeTestResult : showResult;
                showResultFunc('info', `Testing ${testName}...`);
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('uploadType', uploadType);

                console.log(`🚀 Testing ${testName}:`, {
                    fileName: file.name,
                    uploadType: uploadType,
                    endpoint: 'http://localhost:3001/upload' // Note: no /api prefix
                });

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    const mediaUrl = data.media?.url;
                    
                    if (!mediaUrl) {
                        showResultFunc('error', `No URL in response for ${testName}`);
                        return;
                    }

                    // Analyze the URL
                    const hasApiPrefix = mediaUrl.includes('/api/');
                    const hasFilesPath = mediaUrl.includes('/files/');
                    const startsWithMedia = mediaUrl.startsWith('/media/');
                    
                    // Determine expected folder
                    const folderMap = {
                        'avatar': 'avatars',
                        'course_thumbnail': 'courses',
                        'institute_logo': 'institutes',
                        'document': 'documents',
                        'general': 'uploads'
                    };
                    const expectedFolder = folderMap[uploadType] || 'uploads';
                    const hasCorrectFolder = mediaUrl.includes(`/media/${expectedFolder}/`);

                    let resultText = `🎉 ${testName} Results:\n\n`;
                    resultText += `📋 Response Analysis:\n`;
                    resultText += `  - Returned URL: ${mediaUrl}\n`;
                    resultText += `  - Upload Type: ${uploadType}\n`;
                    resultText += `  - Expected Folder: ${expectedFolder}\n\n`;
                    
                    resultText += `✅ URL Quality Checks:\n`;
                    resultText += `  - No /api/ prefix: ${!hasApiPrefix ? 'PASS ✅' : 'FAIL ❌'}\n`;
                    resultText += `  - No /files/ path: ${!hasFilesPath ? 'PASS ✅' : 'FAIL ❌'}\n`;
                    resultText += `  - Starts with /media/: ${startsWithMedia ? 'PASS ✅' : 'FAIL ❌'}\n`;
                    resultText += `  - Correct folder: ${hasCorrectFolder ? 'PASS ✅' : 'FAIL ❌'}\n\n`;
                    
                    const allChecksPass = !hasApiPrefix && !hasFilesPath && startsWithMedia && hasCorrectFolder;
                    
                    if (allChecksPass) {
                        resultText += `🎯 PERFECT! All URL checks passed!\n`;
                        resultText += `✅ The /api prefix issue is completely fixed!`;
                        showResultFunc('success', resultText);
                    } else {
                        resultText += `⚠️ Some issues remain:\n`;
                        if (hasApiPrefix) resultText += `  - Still has /api/ prefix\n`;
                        if (hasFilesPath) resultText += `  - Still has /files/ path\n`;
                        if (!startsWithMedia) resultText += `  - Doesn't start with /media/\n`;
                        if (!hasCorrectFolder) resultText += `  - Wrong folder path\n`;
                        showResultFunc('error', resultText);
                    }
                } else {
                    showResultFunc('error', `${testName} failed: ${data.message}`);
                }

            } catch (error) {
                console.error(`❌ ${testName} error:`, error);
                const showResultFunc = useTypeResult ? showTypeTestResult : showResult;
                showResultFunc('error', `${testName} error: ${error.message}`);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showTypeTestResult(type, message) {
            const element = document.getElementById('typeTestResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 API Prefix Fix Test loaded');
            console.log('🎯 Testing that /api prefix is removed from upload URLs');
            console.log('📋 Frontend now calls /upload instead of /api/upload');
            console.log('✅ Should prevent Next.js rewrite from adding /api to response URLs');
            
            showResult('info', 'Ready to test API prefix fix. Select a file and click "Test Fixed Upload".');
        });
    </script>
</body>
</html>
