<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 API Prefix Only Removal Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-box {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid;
        }
        .comparison-box.before {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .comparison-box.after {
            border-color: #28a745;
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API Prefix Only Removal Test</h1>
        <p>Test that only the `/api/` prefix is removed, keeping `/media/file/` structure.</p>
        
        <div class="success">
            <strong>✅ Updated Logic:</strong> Remove only /api/ prefix<br>
            - Before: `/api/media/file/filename.jpg`<br>
            - After: `/media/file/filename.jpg`<br>
            - Keeps the `/media/file/` structure intact<br>
            - Only removes the `/api/` part
        </div>
    </div>

    <div class="container">
        <h3>🔍 Before vs After Comparison</h3>
        <div class="comparison">
            <div class="comparison-box before">
                <h4>❌ Before Fix</h4>
                <p><strong>Main URL:</strong><br>
                <code>/api/media/file/filename.jpg</code></p>
                <p><strong>Size URLs:</strong><br>
                <code>/api/media/file/filename-80x80.webp</code></p>
                <p><strong>Problem:</strong><br>
                Contains unwanted `/api/` prefix</p>
            </div>
            <div class="comparison-box after">
                <h4>✅ After Fix</h4>
                <p><strong>Main URL:</strong><br>
                <code>/media/file/filename.jpg</code></p>
                <p><strong>Size URLs:</strong><br>
                <code>/media/file/filename-80x80.webp</code></p>
                <p><strong>Result:</strong><br>
                Clean URLs without `/api/` prefix</p>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>📁 Test Upload</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test API prefix removal</p>
            <p style="color: #666; font-size: 14px;">Should return URLs with `/media/file/` structure</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testApiPrefixRemoval()" id="uploadBtn" disabled>Test API Prefix Removal</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🧪 Test Different Upload Types</h3>
        <p>Test various upload types to ensure all get correct URL structure:</p>
        
        <button class="btn" onclick="testUploadType('avatar')">Test Avatar Upload</button>
        <button class="btn" onclick="testUploadType('course_thumbnail')">Test Course Thumbnail</button>
        <button class="btn" onclick="testUploadType('document')">Test Document Upload</button>
        
        <div id="typeResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testApiPrefixRemoval() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, 'avatar', 'API Prefix Removal Test');
        }

        async function testUploadType(uploadType) {
            if (!selectedFile) {
                showTypeResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, uploadType, `${uploadType} API Prefix Test`, true);
        }

        async function testUploadWithFile(file, uploadType, testName, useTypeResult = false) {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                const showResultFunc = useTypeResult ? showTypeResult : showResult;
                showResultFunc('info', `Testing ${testName}...`);
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('uploadType', uploadType);

                console.log(`🚀 Testing ${testName}:`, {
                    fileName: file.name,
                    uploadType: uploadType
                });

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeApiPrefixRemoval(data, testName, showResultFunc);
                } else {
                    showResultFunc('error', `${testName} failed: ${data.message}`);
                }

            } catch (error) {
                console.error(`❌ ${testName} error:`, error);
                const showResultFunc = useTypeResult ? showTypeResult : showResult;
                showResultFunc('error', `${testName} error: ${error.message}`);
            }
        }

        function analyzeApiPrefixRemoval(data, testName, showResultFunc) {
            const media = data.media;
            
            if (!media) {
                showResultFunc('error', `No media object in ${testName} response`);
                return;
            }

            let resultText = `🔧 ${testName} API Prefix Analysis:\n\n`;
            
            // Analyze main URL
            const mainUrl = media.url;
            const noApiPrefix = !mainUrl.includes('/api/');
            const hasMediaFile = mainUrl.includes('/media/file/');
            const correctStructure = noApiPrefix && hasMediaFile;
            
            resultText += `📋 Main URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - No /api/ prefix: ${noApiPrefix ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Has /media/file/: ${hasMediaFile ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Correct structure: ${correctStructure ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            // Analyze thumbnail URL if present
            let thumbnailCorrect = true;
            if (media.thumbnailURL) {
                const thumbNoApi = !media.thumbnailURL.includes('/api/');
                const thumbHasMediaFile = media.thumbnailURL.includes('/media/file/');
                thumbnailCorrect = thumbNoApi && thumbHasMediaFile;
                
                resultText += `🖼️ Thumbnail URL Analysis:\n`;
                resultText += `  - URL: ${media.thumbnailURL}\n`;
                resultText += `  - No /api/ prefix: ${thumbNoApi ? 'PASS ✅' : 'FAIL ❌'}\n`;
                resultText += `  - Has /media/file/: ${thumbHasMediaFile ? 'PASS ✅' : 'FAIL ❌'}\n`;
                resultText += `  - Correct structure: ${thumbnailCorrect ? 'YES ✅' : 'NO ❌'}\n\n`;
            }
            
            // Analyze sizes
            let allSizesCorrect = true;
            let sizesAnalysis = '';
            
            if (media.sizes && Object.keys(media.sizes).length > 0) {
                sizesAnalysis += `📐 Size URLs Analysis:\n`;
                
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeNoApi = !sizeData.url.includes('/api/');
                        const sizeHasMediaFile = sizeData.url.includes('/media/file/');
                        const sizeCorrect = sizeNoApi && sizeHasMediaFile;
                        
                        if (!sizeCorrect) allSizesCorrect = false;
                        
                        sizesAnalysis += `  - ${sizeName}: ${sizeData.url}\n`;
                        sizesAnalysis += `    No /api/: ${sizeNoApi ? '✅' : '❌'} | Has /media/file/: ${sizeHasMediaFile ? '✅' : '❌'}\n`;
                    }
                });
                sizesAnalysis += `\n`;
            } else {
                sizesAnalysis += `📐 No size URLs generated\n\n`;
            }
            
            resultText += sizesAnalysis;
            
            // Overall assessment
            const allUrlsCorrect = correctStructure && thumbnailCorrect && allSizesCorrect;
            
            resultText += `🎯 Overall Assessment:\n`;
            resultText += `  - Main URL correct: ${correctStructure ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Thumbnail URL correct: ${thumbnailCorrect ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - All size URLs correct: ${allSizesCorrect ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - API prefix removal working: ${allUrlsCorrect ? 'PERFECT ✅' : 'INCOMPLETE ❌'}\n\n`;
            
            if (allUrlsCorrect) {
                resultText += `🎉 API PREFIX REMOVAL SUCCESS!\n`;
                resultText += `✅ All URLs have correct /media/file/ structure!\n`;
                resultText += `✅ No /api/ prefixes anywhere!\n`;
                resultText += `🎯 URLs stored as: /media/file/filename.jpg`;
                showResultFunc('success', resultText);
            } else {
                resultText += `⚠️ API prefix removal incomplete:\n`;
                if (!correctStructure) resultText += `  - Main URL structure incorrect\n`;
                if (!thumbnailCorrect) resultText += `  - Thumbnail URL structure incorrect\n`;
                if (!allSizesCorrect) resultText += `  - Some size URLs structure incorrect\n`;
                resultText += `❌ Check the URL replacement logic`;
                showResultFunc('error', resultText);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showTypeResult(type, message) {
            const element = document.getElementById('typeResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 API Prefix Only Removal Test loaded');
            console.log('🎯 Testing that only /api/ prefix is removed');
            console.log('📋 Should keep /media/file/ structure intact');
            
            showResult('info', 'Ready to test API prefix removal. Select an image and click "Test API Prefix Removal".');
        });
    </script>
</body>
</html>
