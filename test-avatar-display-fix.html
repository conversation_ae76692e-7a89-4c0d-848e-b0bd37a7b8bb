<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖼️ Avatar Display Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-data {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .avatar-preview {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .avatar-preview img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #ddd;
        }
        .avatar-info {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Avatar Display Fix Test</h1>
        <p>Test that the "[object Object]" issue in profile settings is fixed.</p>
        
        <div class="success">
            <strong>✅ Fixed Issues:</strong><br>
            - Added proper avatar type handling in ProfileSettingsModal<br>
            - Created getAvatarUrl helper function<br>
            - Handles string URLs, media objects, and media IDs<br>
            - No more "[object Object]" in URLs
        </div>
    </div>

    <div class="container">
        <h3>🧪 Test Current User Avatar</h3>
        <p>Get current user data and test avatar display:</p>
        
        <button class="btn success" onclick="getCurrentUser()">Get Current User</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="userResult"></div>
        <div id="avatarPreview"></div>
    </div>

    <div class="container">
        <h3>📋 Avatar Format Examples</h3>
        
        <div class="test-data">
            <strong>❌ Problematic Format (causes [object Object]):</strong><br>
            user.avatar = {<br>
            &nbsp;&nbsp;id: 123,<br>
            &nbsp;&nbsp;url: "/media/avatars/filename.jpg",<br>
            &nbsp;&nbsp;sizes: { avatar_medium: { url: "/media/avatars/filename-medium.jpg" } }<br>
            }
        </div>
        
        <div class="test-data">
            <strong>✅ Fixed Handling:</strong><br>
            getAvatarUrl(user.avatar) → "http://localhost:3001/media/avatars/filename-medium.jpg"
        </div>
        
        <button class="btn" onclick="testAvatarFormats()">Test Avatar Format Handling</button>
        <div id="formatTestResult"></div>
    </div>

    <div class="container">
        <h3>🔍 Debug Avatar URL Generation</h3>
        <p>Test the avatar URL generation logic:</p>
        
        <button class="btn" onclick="debugAvatarLogic()">Debug Avatar Logic</button>
        <div id="debugResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';

        // Simulate the getFileUrl function from frontend
        function getFileUrl(path) {
            if (!path) return null;
            if (path.startsWith('http')) return path;
            const backendUrl = 'http://localhost:3001';
            return `${backendUrl}${path}`;
        }

        // Simulate the getAvatarUrl helper function
        function getAvatarUrl(avatar) {
            if (!avatar) return null;
            
            if (typeof avatar === 'string') {
                // If avatar is already a URL string, use it directly
                return avatar;
            } else if (typeof avatar === 'object' && avatar && 'url' in avatar) {
                // If avatar is a media object, extract the URL
                const avatarUrl = avatar.sizes?.avatar_medium?.url || avatar.url;
                return getFileUrl(avatarUrl);
            } else {
                // If avatar is something else (like a media ID), we can't display it directly
                console.log('🖼️ Avatar format not supported for display:', avatar);
                return null;
            }
        }

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showUserResult('success', '✅ Token set successfully');
        }

        async function getCurrentUser() {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showUserResult('info', 'Getting current user data...');
                
                const response = await fetch('http://localhost:3001/users/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('👤 User data:', data);

                if (data.success || response.ok) {
                    const user = data.user || data;
                    
                    let resultText = '👤 Current User Data:\n\n';
                    resultText += `Name: ${user.firstName} ${user.lastName}\n`;
                    resultText += `Email: ${user.email}\n`;
                    resultText += `Phone: ${user.phone || 'N/A'}\n\n`;
                    
                    resultText += `🖼️ Avatar Analysis:\n`;
                    resultText += `Raw avatar data: ${JSON.stringify(user.avatar, null, 2)}\n`;
                    resultText += `Avatar type: ${typeof user.avatar}\n`;
                    
                    if (user.avatar) {
                        const avatarUrl = getAvatarUrl(user.avatar);
                        resultText += `Generated URL: ${avatarUrl}\n`;
                        
                        if (avatarUrl && avatarUrl !== '[object Object]') {
                            resultText += `✅ Avatar URL generation: SUCCESS\n`;
                            showAvatarPreview(avatarUrl, user);
                        } else {
                            resultText += `❌ Avatar URL generation: FAILED\n`;
                        }
                    } else {
                        resultText += `No avatar set\n`;
                    }
                    
                    showUserResult('success', resultText);
                } else {
                    showUserResult('error', `❌ Failed to get user: ${data.message || 'Unknown error'}`);
                }

            } catch (error) {
                console.error('❌ Get user error:', error);
                showUserResult('error', `❌ Get user error: ${error.message}`);
            }
        }

        function testAvatarFormats() {
            try {
                showFormatTestResult('info', 'Testing different avatar formats...\n\n');
                
                // Test different avatar formats
                const testCases = [
                    {
                        name: 'String URL',
                        avatar: '/media/avatars/test.jpg',
                        expected: 'http://localhost:3001/media/avatars/test.jpg'
                    },
                    {
                        name: 'Full URL',
                        avatar: 'http://localhost:3001/media/avatars/test.jpg',
                        expected: 'http://localhost:3001/media/avatars/test.jpg'
                    },
                    {
                        name: 'Media Object',
                        avatar: {
                            id: 123,
                            url: '/media/avatars/test.jpg',
                            sizes: {
                                avatar_medium: { url: '/media/avatars/test-medium.jpg' }
                            }
                        },
                        expected: 'http://localhost:3001/media/avatars/test-medium.jpg'
                    },
                    {
                        name: 'Media Object (no sizes)',
                        avatar: {
                            id: 123,
                            url: '/media/avatars/test.jpg'
                        },
                        expected: 'http://localhost:3001/media/avatars/test.jpg'
                    },
                    {
                        name: 'Media ID (number)',
                        avatar: 123,
                        expected: null
                    },
                    {
                        name: 'Null',
                        avatar: null,
                        expected: null
                    }
                ];
                
                let resultText = '📊 Avatar Format Test Results:\n\n';
                
                testCases.forEach((testCase, index) => {
                    const result = getAvatarUrl(testCase.avatar);
                    const success = result === testCase.expected;
                    
                    resultText += `${index + 1}. ${testCase.name}:\n`;
                    resultText += `   Input: ${JSON.stringify(testCase.avatar)}\n`;
                    resultText += `   Expected: ${testCase.expected}\n`;
                    resultText += `   Result: ${result}\n`;
                    resultText += `   Status: ${success ? '✅ PASS' : '❌ FAIL'}\n\n`;
                });
                
                showFormatTestResult('success', resultText);
                
            } catch (error) {
                console.error('❌ Format test error:', error);
                showFormatTestResult('error', `❌ Format test error: ${error.message}`);
            }
        }

        function debugAvatarLogic() {
            try {
                showDebugResult('info', 'Debugging avatar URL generation logic...\n\n');
                
                // Simulate the problematic case that caused [object Object]
                const problematicAvatar = {
                    id: 123,
                    url: '/media/avatars/Screenshot 2023-06-10 122948-1752214072391-839c54e2-f74f-44fa-86d6-87b9b45758fe.png',
                    filename: 'Screenshot 2023-06-10 122948-1752214072391-839c54e2-f74f-44fa-86d6-87b9b45758fe.png',
                    mimeType: 'image/png',
                    filesize: 123456,
                    sizes: {
                        avatar_small: { url: '/media/avatars/Screenshot-small.png', width: 50, height: 50 },
                        avatar_medium: { url: '/media/avatars/Screenshot-medium.png', width: 150, height: 150 },
                        avatar_large: { url: '/media/avatars/Screenshot-large.png', width: 300, height: 300 }
                    }
                };
                
                let debugText = '🔍 Debug Analysis:\n\n';
                
                debugText += `Input avatar object:\n${JSON.stringify(problematicAvatar, null, 2)}\n\n`;
                
                debugText += `Step-by-step processing:\n`;
                debugText += `1. Check if avatar exists: ${!!problematicAvatar}\n`;
                debugText += `2. Check avatar type: ${typeof problematicAvatar}\n`;
                debugText += `3. Has 'url' property: ${'url' in problematicAvatar}\n`;
                debugText += `4. Has sizes: ${!!problematicAvatar.sizes}\n`;
                debugText += `5. Has avatar_medium: ${!!problematicAvatar.sizes?.avatar_medium}\n`;
                
                const selectedUrl = problematicAvatar.sizes?.avatar_medium?.url || problematicAvatar.url;
                debugText += `6. Selected URL: ${selectedUrl}\n`;
                
                const finalUrl = getFileUrl(selectedUrl);
                debugText += `7. Final URL: ${finalUrl}\n\n`;
                
                debugText += `✅ Result: ${finalUrl}\n`;
                debugText += `❌ Old behavior would have been: [object Object]\n\n`;
                
                debugText += `🎯 The fix prevents the object from being converted to string!`;
                
                showDebugResult('success', debugText);
                
            } catch (error) {
                console.error('❌ Debug error:', error);
                showDebugResult('error', `❌ Debug error: ${error.message}`);
            }
        }

        function showAvatarPreview(avatarUrl, user) {
            const previewDiv = document.getElementById('avatarPreview');
            previewDiv.innerHTML = `
                <div class="avatar-preview">
                    <img src="${avatarUrl}" alt="Avatar" 
                         onload="console.log('✅ Avatar loaded successfully')" 
                         onerror="console.error('❌ Avatar failed to load')">
                    <div class="avatar-info">
                        <strong>${user.firstName} ${user.lastName}</strong><br>
                        <small>Avatar URL: ${avatarUrl}</small><br>
                        <small>Status: ${avatarUrl.includes('[object Object]') ? '❌ Contains [object Object]' : '✅ Valid URL'}</small>
                    </div>
                </div>
            `;
        }

        function showUserResult(type, message) {
            const element = document.getElementById('userResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showFormatTestResult(type, message) {
            const element = document.getElementById('formatTestResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showDebugResult(type, message) {
            const element = document.getElementById('debugResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🖼️ Avatar Display Fix Test loaded');
            console.log('🎯 Testing avatar display fix for [object Object] issue');
            console.log('📋 The ProfileSettingsModal should now display avatars correctly');
            
            showUserResult('info', 'Ready to test avatar display fix. Click "Get Current User" to start.');
        });
    </script>
</body>
</html>
