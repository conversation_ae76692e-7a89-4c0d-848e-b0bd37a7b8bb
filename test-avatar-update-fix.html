<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖼️ Avatar Update Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.danger {
            background-color: #dc3545;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .test-data {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Avatar Update Fix Test</h1>
        <p>Test that the avatar update validation error is fixed.</p>
        
        <div class="success">
            <strong>✅ Fixed Issues:</strong><br>
            - Backend now handles avatar URLs and converts them to media IDs<br>
            - Frontend components send correct data types<br>
            - Validation error should be resolved
        </div>
    </div>

    <div class="container">
        <h3>🧪 Test Avatar Upload & Update</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image file to test avatar upload and update</p>
            <p style="color: #666; font-size: 14px;">This will test the complete flow</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="uploadAndUpdateProfile()" id="uploadBtn" disabled>Upload & Update Profile</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        <button class="btn danger" onclick="testWithUrl()">Test with URL (Should Work Now)</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>📋 Test Data Examples</h3>
        
        <div class="test-data">
            <strong>❌ Old Problematic Data (caused validation error):</strong><br>
            {<br>
            &nbsp;&nbsp;"avatar": "http://localhost:3002/media/avatars/filename.png",<br>
            &nbsp;&nbsp;"firstName": "User",<br>
            &nbsp;&nbsp;"lastName": "LMS"<br>
            }
        </div>
        
        <div class="test-data">
            <strong>✅ New Fixed Data (should work):</strong><br>
            {<br>
            &nbsp;&nbsp;"avatar": 123, // Media ID<br>
            &nbsp;&nbsp;"firstName": "User",<br>
            &nbsp;&nbsp;"lastName": "LMS"<br>
            }
        </div>
        
        <button class="btn" onclick="testBothDataTypes()">Test Both Data Types</button>
        <div id="dataTestResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function uploadAndUpdateProfile() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showResult('info', 'Step 1: Uploading avatar...');
                
                // Step 1: Upload avatar
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'avatar');
                formData.append('updateUserField', 'avatar');

                const uploadResponse = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                const uploadData = await uploadResponse.json();
                console.log('📦 Upload response:', uploadData);

                if (!uploadData.success) {
                    showResult('error', `Upload failed: ${uploadData.message}`);
                    return;
                }

                showResult('info', 
                    `Step 1 Complete: Avatar uploaded!\n` +
                    `Media ID: ${uploadData.media.id}\n` +
                    `URL: ${uploadData.media.url}\n\n` +
                    `Step 2: Testing profile update...`
                );

                // Step 2: Test profile update with media ID (correct way)
                const profileData = {
                    firstName: 'Test',
                    lastName: 'User',
                    email: '<EMAIL>',
                    phone: '9629090020',
                    avatar: uploadData.media.id // ✅ Use media ID
                };

                const updateResponse = await fetch('http://localhost:3001/super-admin/user/me', {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(profileData),
                });

                const updateData = await updateResponse.json();
                console.log('📦 Update response:', updateData);

                if (updateData.success || updateResponse.ok) {
                    showResult('success', 
                        `🎉 COMPLETE SUCCESS!\n\n` +
                        `✅ Step 1: Avatar uploaded successfully\n` +
                        `  - Media ID: ${uploadData.media.id}\n` +
                        `  - URL: ${uploadData.media.url}\n\n` +
                        `✅ Step 2: Profile updated successfully\n` +
                        `  - No validation errors!\n` +
                        `  - Avatar field accepted media ID\n\n` +
                        `🎯 The validation error is fixed!`
                    );
                } else {
                    showResult('error', 
                        `❌ Profile update failed: ${updateData.message || 'Unknown error'}\n\n` +
                        `Response: ${JSON.stringify(updateData, null, 2)}`
                    );
                }

            } catch (error) {
                console.error('❌ Test error:', error);
                showResult('error', `❌ Test error: ${error.message}`);
            }
        }

        async function testWithUrl() {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showResult('info', 'Testing profile update with avatar URL (should work now)...');
                
                // Test with URL (the problematic case that should now work)
                const profileData = {
                    firstName: 'Test',
                    lastName: 'User',
                    email: '<EMAIL>',
                    phone: '9629090020',
                    avatar: 'http://localhost:3002/media/avatars/Screenshot%202023-06-10%20122948-1752213461924-3220e694-363d-4edc-8a71-a03871c20d26.png'
                };

                console.log('🧪 Testing with URL data:', profileData);

                const response = await fetch('http://localhost:3001/super-admin/user/me', {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(profileData),
                });

                const data = await response.json();
                console.log('📦 URL test response:', data);

                if (data.success || response.ok) {
                    showResult('success', 
                        `✅ URL TEST SUCCESS!\n\n` +
                        `The backend now correctly handles avatar URLs:\n` +
                        `  - Extracts filename from URL\n` +
                        `  - Finds corresponding media record\n` +
                        `  - Uses media ID for update\n\n` +
                        `🎯 Validation error is fixed!`
                    );
                } else {
                    showResult('error', 
                        `❌ URL test failed: ${data.message || 'Unknown error'}\n\n` +
                        `Response: ${JSON.stringify(data, null, 2)}`
                    );
                }

            } catch (error) {
                console.error('❌ URL test error:', error);
                showResult('error', `❌ URL test error: ${error.message}`);
            }
        }

        async function testBothDataTypes() {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showDataTestResult('info', 'Testing both data types...\n\n');
                
                // Test 1: With URL (should work now)
                const urlData = {
                    firstName: 'Test',
                    lastName: 'User',
                    email: '<EMAIL>',
                    phone: '9629090020',
                    avatar: 'http://localhost:3001/media/avatars/Screenshot%202023-06-10%20122948-1752213461924-3220e694-363d-4edc-8a71-a03871c20d26.png'
                };

                console.log('🧪 Test 1: URL data');
                const urlResponse = await fetch('http://localhost:3001/super-admin/user/me', {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(urlData),
                });

                const urlResult = await urlResponse.json();
                
                // Test 2: With media ID (should definitely work)
                const idData = {
                    firstName: 'Test',
                    lastName: 'User',
                    email: '<EMAIL>',
                    phone: '9629090020',
                    avatar: 123 // Example media ID
                };

                console.log('🧪 Test 2: ID data');
                const idResponse = await fetch('http://localhost:3001/super-admin/user/me', {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(idData),
                });

                const idResult = await idResponse.json();

                // Show results
                let resultText = '📊 Data Type Test Results:\n\n';
                
                resultText += `🔗 Test 1: Avatar URL\n`;
                resultText += `  Status: ${urlResponse.status}\n`;
                resultText += `  Success: ${urlResult.success || urlResponse.ok ? 'YES' : 'NO'}\n`;
                resultText += `  Message: ${urlResult.message || 'OK'}\n\n`;
                
                resultText += `🆔 Test 2: Avatar ID\n`;
                resultText += `  Status: ${idResponse.status}\n`;
                resultText += `  Success: ${idResult.success || idResponse.ok ? 'YES' : 'NO'}\n`;
                resultText += `  Message: ${idResult.message || 'OK'}\n\n`;
                
                const bothWork = (urlResult.success || urlResponse.ok) && (idResult.success || idResponse.ok);
                
                if (bothWork) {
                    resultText += `🎉 BOTH DATA TYPES WORK!\n`;
                    resultText += `✅ The validation error is completely fixed!`;
                    showDataTestResult('success', resultText);
                } else {
                    resultText += `⚠️ Some issues remain - check the individual results above.`;
                    showDataTestResult('error', resultText);
                }

            } catch (error) {
                console.error('❌ Data type test error:', error);
                showDataTestResult('error', `❌ Data type test error: ${error.message}`);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showDataTestResult(type, message) {
            const element = document.getElementById('dataTestResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🖼️ Avatar Update Fix Test loaded');
            console.log('🎯 Testing avatar validation error fix');
            console.log('📋 The backend should now handle both URLs and media IDs');
            
            showResult('info', 'Ready to test avatar update fix. Select an image or test with existing data.');
        });
    </script>
</body>
</html>
