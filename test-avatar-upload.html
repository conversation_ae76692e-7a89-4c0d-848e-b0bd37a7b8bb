<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Admin Avatar Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .avatar-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .avatar-size {
            text-align: center;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
        }
        .avatar-size img {
            border-radius: 50%;
            border: 2px solid #ddd;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .hidden {
            display: none;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Super Admin Avatar Upload Test</h1>
        
        <div class="info">
            <strong>Token:</strong> eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg
        </div>

        <div class="upload-area" id="uploadArea">
            <p>📁 Drag & drop an image here or click to select</p>
            <p style="color: #666; font-size: 14px;">Supported: JPG, PNG, GIF, WEBP (Max 5MB)</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <div>
            <button class="btn" onclick="uploadAvatar()" id="uploadBtn" disabled>
                <span id="uploadText">Upload Avatar</span>
                <span id="uploadSpinner" class="loading hidden"></span>
            </button>
            <button class="btn" onclick="getCurrentAvatar()" id="getAvatarBtn">Get Current Avatar</button>
            <button class="btn" onclick="removeAvatar()" id="removeAvatarBtn">Remove Avatar</button>
        </div>

        <div id="result"></div>
        <div id="avatarPreview" class="avatar-preview"></div>
    </div>

    <script>
        const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const uploadBtn = document.getElementById('uploadBtn');
        const result = document.getElementById('result');
        const avatarPreview = document.getElementById('avatarPreview');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            if (!file.type.startsWith('image/')) {
                showResult('error', 'Please select an image file');
                return;
            }
            if (file.size > 5 * 1024 * 1024) {
                showResult('error', 'File size must be less than 5MB');
                return;
            }
            
            selectedFile = file;
            uploadBtn.disabled = false;
            showResult('info', `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
        }

        async function uploadAvatar() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            setLoading(true);
            
            try {
                const formData = new FormData();
                formData.append('file', selectedFile);

                console.log('🚀 Uploading avatar...');
                
                const response = await fetch('http://localhost:3001/api/super-admin/avatar/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                    body: formData,
                });

                const data = await response.json();
                console.log('📦 Upload response:', data);

                if (data.success) {
                    showResult('success', `✅ Avatar uploaded successfully! Media ID: ${data.media.id}`);
                    displayAvatarSizes(data.media);
                } else {
                    showResult('error', `❌ Upload failed: ${data.message}`);
                }
            } catch (error) {
                console.error('Upload error:', error);
                showResult('error', `❌ Upload error: ${error.message}`);
            } finally {
                setLoading(false);
            }
        }

        async function getCurrentAvatar() {
            try {
                console.log('🔍 Getting current avatar...');
                
                const response = await fetch('http://localhost:3001/api/super-admin/avatar/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('📦 Get avatar response:', data);

                if (data.success) {
                    if (data.avatar) {
                        showResult('success', `✅ Current avatar found! ID: ${data.avatar.id}`);
                        displayAvatarSizes(data.avatar);
                    } else {
                        showResult('info', '📷 No avatar set');
                        avatarPreview.innerHTML = '';
                    }
                } else {
                    showResult('error', `❌ Failed to get avatar: ${data.message}`);
                }
            } catch (error) {
                console.error('Get avatar error:', error);
                showResult('error', `❌ Get avatar error: ${error.message}`);
            }
        }

        async function removeAvatar() {
            if (!confirm('Are you sure you want to remove your avatar?')) {
                return;
            }

            try {
                console.log('🗑️ Removing avatar...');
                
                const response = await fetch('http://localhost:3001/api/super-admin/avatar/me', {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('📦 Remove avatar response:', data);

                if (data.success) {
                    showResult('success', '✅ Avatar removed successfully!');
                    avatarPreview.innerHTML = '';
                } else {
                    showResult('error', `❌ Failed to remove avatar: ${data.message}`);
                }
            } catch (error) {
                console.error('Remove avatar error:', error);
                showResult('error', `❌ Remove avatar error: ${error.message}`);
            }
        }

        function displayAvatarSizes(media) {
            const baseUrl = 'http://localhost:3001';
            const sizes = media.sizes || {};
            
            let html = '<h3>🖼️ Generated Avatar Sizes:</h3>';
            
            // Original image
            html += `
                <div class="avatar-size">
                    <h4>Original</h4>
                    <img src="${baseUrl}${media.url}" alt="Original" style="max-width: 150px; max-height: 150px;">
                    <p>${media.width || 'Unknown'}x${media.height || 'Unknown'}</p>
                </div>
            `;
            
            // Generated sizes
            const sizeLabels = {
                avatar_small: 'Small (40x40)',
                avatar_medium: 'Medium (80x80)', 
                avatar_large: 'Large (150x150)',
                profile: 'Profile (300x300)',
                thumbnail: 'Thumbnail (400x300)'
            };
            
            Object.entries(sizes).forEach(([sizeName, sizeData]) => {
                if (sizeLabels[sizeName]) {
                    html += `
                        <div class="avatar-size">
                            <h4>${sizeLabels[sizeName]}</h4>
                            <img src="${baseUrl}${sizeData.url}" alt="${sizeName}">
                            <p>${sizeData.width}x${sizeData.height}</p>
                        </div>
                    `;
                }
            });
            
            avatarPreview.innerHTML = html;
        }

        function showResult(type, message) {
            result.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function setLoading(loading) {
            const uploadText = document.getElementById('uploadText');
            const uploadSpinner = document.getElementById('uploadSpinner');
            
            if (loading) {
                uploadText.textContent = 'Uploading...';
                uploadSpinner.classList.remove('hidden');
                uploadBtn.disabled = true;
            } else {
                uploadText.textContent = 'Upload Avatar';
                uploadSpinner.classList.add('hidden');
                uploadBtn.disabled = !selectedFile;
            }
        }

        // Load current avatar on page load
        window.addEventListener('load', getCurrentAvatar);
    </script>
</body>
</html>
