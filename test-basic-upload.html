<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Basic File Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .hidden {
            display: none;
        }
        select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Basic File Upload Test</h1>
        <p>Test the common file upload API with local storage (no image processing for now).</p>
        
        <div class="info">
            <strong>Token:</strong> eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg
        </div>
    </div>

    <div class="container">
        <h3>📁 File Upload</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Drag & drop a file here or click to select</p>
            <p style="color: #666; font-size: 14px;">Any file type, max 25MB</p>
            <input type="file" id="fileInput" class="hidden">
        </div>

        <div>
            <label>Upload Type:</label>
            <select id="uploadType">
                <option value="avatar">Avatar (updates user.avatar)</option>
                <option value="course_thumbnail">Course Thumbnail</option>
                <option value="institute_logo">Institute Logo</option>
                <option value="document">Document</option>
                <option value="general">General File</option>
            </select>
        </div>

        <button class="btn" onclick="uploadFile()" id="uploadBtn" disabled>Upload File</button>
        <button class="btn" onclick="getMyFiles()">Get My Files</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>📂 My Files</h3>
        <div id="filesList"></div>
    </div>

    <script>
        const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'white';
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'white';
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            selectedFile = file;
            document.getElementById('uploadBtn').disabled = false;
            showResult('info', `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
        }

        async function uploadFile() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            const uploadType = document.getElementById('uploadType').value;
            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('uploadType', uploadType);
            
            // If avatar, update user field
            if (uploadType === 'avatar') {
                formData.append('updateUserField', 'avatar');
            }

            try {
                showResult('info', 'Uploading file...');
                
                const response = await fetch('http://localhost:3001/api/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                    body: formData,
                });

                const data = await response.json();
                console.log('📦 Upload response:', data);

                if (data.success) {
                    showResult('success', `✅ ${data.message}\n\nMedia ID: ${data.media.id}\nURL: ${data.media.url}\nType: ${data.media.mediaType}`);
                    
                    if (data.user) {
                        console.log('👤 User updated:', data.user);
                    }
                    
                    // Refresh file list
                    getMyFiles();
                } else {
                    showResult('error', `❌ Upload failed: ${data.message}`);
                }
            } catch (error) {
                console.error('Upload error:', error);
                showResult('error', `❌ Upload error: ${error.message}`);
            }
        }

        async function getMyFiles() {
            try {
                const response = await fetch('http://localhost:3001/api/upload/my-files', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('📂 My files:', data);

                if (data.success) {
                    displayFilesList(data.files);
                } else {
                    showResult('error', `Failed to get files: ${data.message}`);
                }
            } catch (error) {
                console.error('Get files error:', error);
                showResult('error', `Error: ${error.message}`);
            }
        }

        async function deleteFile(fileId, fileName) {
            if (!confirm(`Are you sure you want to delete "${fileName}"?`)) {
                return;
            }

            try {
                const response = await fetch(`http://localhost:3001/api/upload/${fileId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('🗑️ Delete result:', data);

                if (data.success) {
                    showResult('success', `File "${fileName}" deleted successfully`);
                    getMyFiles(); // Refresh file list
                } else {
                    showResult('error', `Failed to delete file: ${data.message}`);
                }
            } catch (error) {
                console.error('Delete error:', error);
                showResult('error', `Error: ${error.message}`);
            }
        }

        function displayFilesList(files) {
            const filesList = document.getElementById('filesList');
            
            if (files.length === 0) {
                filesList.innerHTML = '<p>No files uploaded yet.</p>';
                return;
            }

            let html = '<h4>📁 Uploaded Files:</h4>';
            files.forEach(file => {
                const fileSize = (file.filesize / 1024 / 1024).toFixed(2);
                const uploadDate = new Date(file.createdAt).toLocaleDateString();
                
                html += `
                    <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong>${file.filename}</strong><br>
                                <small>Type: ${file.mediaType} | Size: ${fileSize} MB | Uploaded: ${uploadDate}</small><br>
                                <small>URL: ${file.url}</small>
                            </div>
                            <div>
                                <button class="btn" onclick="window.open('http://localhost:3001${file.url}', '_blank')" style="margin: 2px;">View</button>
                                <button class="btn" onclick="deleteFile('${file.id}', '${file.filename}')" style="background-color: #dc3545; margin: 2px;">Delete</button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            filesList.innerHTML = html;
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 Basic File Upload Test loaded');
            getMyFiles();
        });
    </script>
</body>
</html>
