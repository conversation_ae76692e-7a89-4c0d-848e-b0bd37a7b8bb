<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Body Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .hidden {
            display: none;
        }
        select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Body Fix Test</h1>
        <p>Test that the "Body has already been read" error is fixed in the file upload API.</p>
        
        <div class="info">
            <strong>Fixed Issue:</strong> "Body is unusable: Body has already been read"<br>
            <strong>Solution:</strong> Pass already parsed file to middleware to avoid double parsing
        </div>
    </div>

    <div class="container">
        <h3>📁 File Upload Test</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Drag & drop a file here or click to select</p>
            <p style="color: #666; font-size: 14px;">Test the fixed upload endpoint</p>
            <input type="file" id="fileInput" class="hidden">
        </div>

        <div>
            <label>Upload Type:</label>
            <select id="uploadType">
                <option value="avatar">Avatar (updates user.avatar)</option>
                <option value="course_thumbnail">Course Thumbnail</option>
                <option value="institute_logo">Institute Logo</option>
                <option value="document">Document</option>
                <option value="general">General File</option>
            </select>
        </div>

        <button class="btn" onclick="uploadFile()" id="uploadBtn" disabled>Test Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'white';
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'white';
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            selectedFile = file;
            document.getElementById('uploadBtn').disabled = false;
            showResult('info', `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
        }

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function uploadFile() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            const uploadType = document.getElementById('uploadType').value;
            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('uploadType', uploadType);
            
            // If avatar, update user field
            if (uploadType === 'avatar') {
                formData.append('updateUserField', 'avatar');
            }

            try {
                showResult('info', 'Testing upload with fixed endpoint...');
                
                console.log('🚀 Testing upload to fixed endpoint...');
                console.log('📋 Upload details:', {
                    fileName: selectedFile.name,
                    fileSize: selectedFile.size,
                    fileType: selectedFile.type,
                    uploadType: uploadType
                });

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                console.log('📦 Response headers:', Object.fromEntries(response.headers.entries()));

                const data = await response.json();
                console.log('📦 Upload response:', data);

                if (data.success) {
                    showResult('success', `✅ Upload successful! No "Body already read" error!\n\nDetails:\n- Media ID: ${data.media.id}\n- URL: ${data.media.url}\n- Type: ${data.media.mediaType}\n- Upload Type: ${data.uploadType}`);
                    
                    if (data.user) {
                        console.log('👤 User updated:', data.user);
                    }
                } else {
                    showResult('error', `❌ Upload failed: ${data.message}`);
                }
            } catch (error) {
                console.error('❌ Upload error:', error);
                showResult('error', `❌ Upload error: ${error.message}`);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Body Fix Test loaded');
            console.log('🎯 Testing endpoint: http://localhost:3001/upload');
            showResult('info', 'Ready to test the fixed upload endpoint. Select a file and click "Test Upload".');
        });
    </script>
</body>
</html>
