<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Settings Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Bulk Settings API Test</h1>
    
    <div class="test-section">
        <h2>1. Authentication</h2>
        <button onclick="testAuth()">Login</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Bulk Settings with Empty Values</h2>
        <button onclick="testBulkWithEmpty()">Test Bulk Update (Empty Values)</button>
        <div id="bulk-empty-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Bulk Settings with Null Values</h2>
        <button onclick="testBulkWithNull()">Test Bulk Update (Null Values)</button>
        <div id="bulk-null-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Check Current Settings</h2>
        <button onclick="checkSettings()">Check Settings</button>
        <div id="settings-result"></div>
    </div>

    <script>
        let testToken = null;

        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function testAuth() {
            try {
                showResult('auth-result', 'info', 'Authenticating...');
                
                const response = await fetch('http://localhost:3001/api/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '123456'
                    }),
                });

                const data = await response.json();
                
                if (data.token) {
                    testToken = data.token;
                    showResult('auth-result', 'success', `✅ Authentication successful!`);
                } else {
                    showResult('auth-result', 'error', `❌ Authentication failed: ${data.message}`);
                }
            } catch (error) {
                showResult('auth-result', 'error', `❌ Auth error: ${error.message}`);
            }
        }

        async function testBulkWithEmpty() {
            if (!testToken) {
                showResult('bulk-empty-result', 'error', '❌ Please authenticate first');
                return;
            }

            try {
                showResult('bulk-empty-result', 'info', '🔄 Testing bulk update with empty values...');
                
                const settings = [
                    {
                        key: 'platform_logo',
                        value: '',
                        category: 'platform',
                        type: 'url',
                        description: 'Platform logo URL',
                        is_public: true
                    },
                    {
                        key: 'platform_favicon',
                        value: '',
                        category: 'platform',
                        type: 'url',
                        description: 'Platform favicon URL',
                        is_public: true
                    }
                ];

                console.log('📤 Sending bulk request with empty values:', settings);

                const response = await fetch('http://localhost:3001/api/platform/settings/bulk', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ settings }),
                });

                const result = await response.json();
                console.log('📦 Bulk response:', result);
                
                if (result.success) {
                    showResult('bulk-empty-result', 'success', `✅ Bulk update successful!<br>
                        <strong>Updated:</strong> ${result.updated}<br>
                        <strong>Failed:</strong> ${result.failed}<br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>`);
                } else {
                    showResult('bulk-empty-result', 'error', `❌ Bulk update failed: ${result.message}<br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>`);
                }
            } catch (error) {
                showResult('bulk-empty-result', 'error', `❌ Error: ${error.message}`);
            }
        }

        async function testBulkWithNull() {
            if (!testToken) {
                showResult('bulk-null-result', 'error', '❌ Please authenticate first');
                return;
            }

            try {
                showResult('bulk-null-result', 'info', '🔄 Testing bulk update with null values...');
                
                const settings = [
                    {
                        key: 'platform_logo',
                        value: null,
                        category: 'platform',
                        type: 'url',
                        description: 'Platform logo URL',
                        is_public: true
                    },
                    {
                        key: 'platform_favicon',
                        value: null,
                        category: 'platform',
                        type: 'url',
                        description: 'Platform favicon URL',
                        is_public: true
                    }
                ];

                console.log('📤 Sending bulk request with null values:', settings);

                const response = await fetch('http://localhost:3001/api/platform/settings/bulk', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ settings }),
                });

                const result = await response.json();
                console.log('📦 Bulk response:', result);
                
                if (result.success) {
                    showResult('bulk-null-result', 'success', `✅ Bulk update successful!<br>
                        <strong>Updated:</strong> ${result.updated}<br>
                        <strong>Failed:</strong> ${result.failed}<br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>`);
                } else {
                    showResult('bulk-null-result', 'error', `❌ Bulk update failed: ${result.message}<br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>`);
                }
            } catch (error) {
                showResult('bulk-null-result', 'error', `❌ Error: ${error.message}`);
            }
        }

        async function checkSettings() {
            if (!testToken) {
                showResult('settings-result', 'error', '❌ Please authenticate first');
                return;
            }

            try {
                showResult('settings-result', 'info', '🔍 Checking current settings...');
                
                const response = await fetch('http://localhost:3001/api/platform/settings?category=platform', {
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                });

                const result = await response.json();
                
                if (result.success) {
                    const logoSetting = result.settings.find(s => s.key === 'platform_logo');
                    const faviconSetting = result.settings.find(s => s.key === 'platform_favicon');
                    
                    showResult('settings-result', 'success', `✅ Settings retrieved!<br>
                        <strong>Platform Logo:</strong> "${logoSetting ? logoSetting.value : 'Not found'}"<br>
                        <strong>Platform Favicon:</strong> "${faviconSetting ? faviconSetting.value : 'Not found'}"<br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>`);
                } else {
                    showResult('settings-result', 'error', `❌ Failed to get settings: ${result.message}`);
                }
            } catch (error) {
                showResult('settings-result', 'error', `❌ Error: ${error.message}`);
            }
        }

        // Auto-authenticate on page load
        window.onload = function() {
            testAuth();
        };
    </script>
</body>
</html>
