<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Double Hook Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .hook-flow {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Double Hook Fix Test</h1>
        <p>Test that both `beforeChange` and `afterChange` hooks are working to fix media URLs.</p>
        
        <div class="success">
            <strong>✅ Enhanced Fix:</strong> Added both beforeChange and afterChange hooks<br>
            - beforeChange: Fixes URLs before saving to database<br>
            - afterChange: Fixes URLs that Payload generates after saving<br>
            - Double protection ensures all URLs are clean<br>
            - Handles both create and update operations
        </div>
    </div>

    <div class="container">
        <h3>🔄 Hook Flow Explanation</h3>
        <div class="hook-flow">
            <strong>1. beforeChange Hook:</strong><br>
            - Runs before data is saved to database<br>
            - Fixes URLs in the incoming data<br>
            - Prevents /api/media/file/ from being stored initially<br><br>
            
            <strong>2. afterChange Hook:</strong><br>
            - Runs after record is saved to database<br>
            - Catches URLs that Payload generates automatically<br>
            - Updates the record again with clean URLs<br>
            - Ensures even auto-generated URLs are fixed
        </div>
    </div>

    <div class="container">
        <h3>📁 Test Upload with Double Hook</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test the double hook fix</p>
            <p style="color: #666; font-size: 14px;">Should fix URLs in both beforeChange and afterChange</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testDoubleHookUpload()" id="uploadBtn" disabled>Test Double Hook Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🧪 Test Different Scenarios</h3>
        <p>Test various scenarios to ensure hooks work in all cases:</p>
        
        <button class="btn" onclick="testScenario('avatar')">Test Avatar (beforeChange + afterChange)</button>
        <button class="btn" onclick="testScenario('course_thumbnail')">Test Course Thumbnail</button>
        <button class="btn" onclick="testScenario('document')">Test Document Upload</button>
        
        <div id="scenarioResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testDoubleHookUpload() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, 'avatar', 'Double Hook Test');
        }

        async function testScenario(uploadType) {
            if (!selectedFile) {
                showScenarioResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, uploadType, `${uploadType} Double Hook Test`, true);
        }

        async function testUploadWithFile(file, uploadType, testName, useScenarioResult = false) {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                const showResultFunc = useScenarioResult ? showScenarioResult : showResult;
                showResultFunc('info', `Testing ${testName}...`);
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('uploadType', uploadType);

                console.log(`🚀 Testing ${testName}:`, {
                    fileName: file.name,
                    uploadType: uploadType
                });

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeDoubleHookResult(data, testName, showResultFunc);
                } else {
                    showResultFunc('error', `${testName} failed: ${data.message}`);
                }

            } catch (error) {
                console.error(`❌ ${testName} error:`, error);
                const showResultFunc = useScenarioResult ? showScenarioResult : showResult;
                showResultFunc('error', `${testName} error: ${error.message}`);
            }
        }

        function analyzeDoubleHookResult(data, testName, showResultFunc) {
            const media = data.media;
            
            if (!media) {
                showResultFunc('error', `No media object in ${testName} response`);
                return;
            }

            let resultText = `🔧 ${testName} Hook Analysis:\n\n`;
            
            // Analyze main URL
            const mainUrl = media.url;
            const mainUrlFixed = !mainUrl.includes('/api/media/file/');
            const mainUrlStartsWithMedia = mainUrl.startsWith('/media/');
            
            resultText += `📋 Main URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - beforeChange hook worked: ${mainUrlFixed ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Starts with /media/: ${mainUrlStartsWithMedia ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            // Analyze sizes (this is where afterChange hook is most important)
            let allSizesFixed = true;
            let sizesAnalysis = '';
            
            if (media.sizes && Object.keys(media.sizes).length > 0) {
                sizesAnalysis += `📐 Size URLs Analysis (afterChange hook critical here):\n`;
                
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeUrlFixed = !sizeData.url.includes('/api/media/file/');
                        const sizeUrlStartsWithMedia = sizeData.url.startsWith('/media/');
                        const sizeUrlValid = sizeUrlFixed && sizeUrlStartsWithMedia;
                        
                        if (!sizeUrlValid) allSizesFixed = false;
                        
                        sizesAnalysis += `  - ${sizeName}: ${sizeData.url}\n`;
                        sizesAnalysis += `    afterChange hook worked: ${sizeUrlFixed ? 'YES ✅' : 'NO ❌'}\n`;
                        sizesAnalysis += `    Starts /media/: ${sizeUrlStartsWithMedia ? 'YES ✅' : 'NO ❌'}\n`;
                    }
                });
                sizesAnalysis += `\n`;
            } else {
                sizesAnalysis += `📐 No size URLs generated (may be normal for some file types)\n\n`;
            }
            
            resultText += sizesAnalysis;
            
            // Hook effectiveness analysis
            resultText += `🔍 Hook Effectiveness Analysis:\n`;
            resultText += `  - beforeChange hook: ${mainUrlFixed ? 'WORKING ✅' : 'FAILED ❌'}\n`;
            resultText += `  - afterChange hook: ${allSizesFixed ? 'WORKING ✅' : 'FAILED ❌'}\n`;
            resultText += `  - Overall protection: ${mainUrlFixed && allSizesFixed ? 'COMPLETE ✅' : 'INCOMPLETE ❌'}\n\n`;
            
            // Overall assessment
            const doubleHookWorking = mainUrlFixed && allSizesFixed;
            
            if (doubleHookWorking) {
                resultText += `🎉 DOUBLE HOOK SUCCESS!\n`;
                resultText += `✅ Both beforeChange and afterChange hooks are working!\n`;
                resultText += `✅ All URLs are now clean without /api/media/file/ prefix!\n`;
                resultText += `🎯 Database stores clean URLs that work with localhost:3001/media/`;
                showResultFunc('success', resultText);
            } else {
                resultText += `⚠️ Double hook protection incomplete:\n`;
                if (!mainUrlFixed) resultText += `  - beforeChange hook may not be working\n`;
                if (!allSizesFixed) resultText += `  - afterChange hook may not be working\n`;
                resultText += `❌ Some URLs still contain /api/media/file/ prefix`;
                showResultFunc('error', resultText);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showScenarioResult(type, message) {
            const element = document.getElementById('scenarioResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Double Hook Fix Test loaded');
            console.log('🎯 Testing both beforeChange and afterChange hooks');
            console.log('📋 Should fix URLs at both creation and after auto-generation');
            
            showResult('info', 'Ready to test double hook fix. Select an image and click "Test Double Hook Upload".');
        });
    </script>
</body>
</html>
