<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Enhanced URL Clean Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .cleanup-flow {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Enhanced URL Clean Test</h1>
        <p>Test the enhanced URL cleaning that applies multiple layers of protection.</p>
        
        <div class="success">
            <strong>✅ Enhanced Protection:</strong> Multiple layers of URL cleaning<br>
            - Layer 1: Storage service generates clean URLs<br>
            - Layer 2: Upload middleware cleans response URLs<br>
            - Layer 3: Final cleanup catches any remaining /api/ URLs<br>
            - Layer 4: Database hooks (if working) clean stored URLs
        </div>
    </div>

    <div class="container">
        <h3>🔄 Enhanced Cleanup Flow</h3>
        <div class="cleanup-flow">
            <strong>Multi-Layer URL Cleaning Process:</strong><br><br>
            
            <strong>1. Storage Service:</strong> Generates /media/folder/filename.jpg<br>
            <strong>2. Upload Middleware:</strong> Cleans uploadResult.sizes URLs<br>
            <strong>3. Final Cleanup:</strong> Scans entire response for /api/media/file/<br>
            <strong>4. Response Guarantee:</strong> NO /api/ URLs in final response<br><br>
            
            <em>Even if Payload generates /api/media/file/ URLs, they get cleaned!</em>
        </div>
    </div>

    <div class="container">
        <h3>📁 Test Enhanced Upload</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test enhanced URL cleaning</p>
            <p style="color: #666; font-size: 14px;">Should return completely clean URLs in response</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testEnhancedUpload()" id="uploadBtn" disabled>Test Enhanced Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🧪 Test Multiple File Types</h3>
        <p>Test various file types to ensure all get clean URLs:</p>
        
        <button class="btn" onclick="testFileType('avatar')">Test Avatar (Image Sizes)</button>
        <button class="btn" onclick="testFileType('course_thumbnail')">Test Course Thumbnail</button>
        <button class="btn" onclick="testFileType('document')">Test Document</button>
        
        <div id="typeResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testEnhancedUpload() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, 'avatar', 'Enhanced URL Clean Test');
        }

        async function testFileType(uploadType) {
            if (!selectedFile) {
                showTypeResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, uploadType, `${uploadType} Enhanced Test`, true);
        }

        async function testUploadWithFile(file, uploadType, testName, useTypeResult = false) {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                const showResultFunc = useTypeResult ? showTypeResult : showResult;
                showResultFunc('info', `Testing ${testName}...`);
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('uploadType', uploadType);

                console.log(`🚀 Testing ${testName}:`, {
                    fileName: file.name,
                    uploadType: uploadType
                });

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeEnhancedCleanup(data, testName, showResultFunc);
                } else {
                    showResultFunc('error', `${testName} failed: ${data.message}`);
                }

            } catch (error) {
                console.error(`❌ ${testName} error:`, error);
                const showResultFunc = useTypeResult ? showTypeResult : showResult;
                showResultFunc('error', `${testName} error: ${error.message}`);
            }
        }

        function analyzeEnhancedCleanup(data, testName, showResultFunc) {
            const media = data.media;
            
            if (!media) {
                showResultFunc('error', `No media object in ${testName} response`);
                return;
            }

            let resultText = `🔧 ${testName} Enhanced Cleanup Analysis:\n\n`;
            
            // Analyze main URL
            const mainUrl = media.url;
            const mainUrlClean = !mainUrl.includes('/api/media/file/') && !mainUrl.includes('/api/');
            const mainUrlStartsWithMedia = mainUrl.startsWith('/media/');
            
            resultText += `📋 Main URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - No /api/ anywhere: ${mainUrlClean ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Starts with /media/: ${mainUrlStartsWithMedia ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Enhanced cleanup worked: ${mainUrlClean && mainUrlStartsWithMedia ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            // Analyze thumbnail URL if present
            let thumbnailClean = true;
            if (media.thumbnailURL) {
                thumbnailClean = !media.thumbnailURL.includes('/api/media/file/') && !media.thumbnailURL.includes('/api/');
                const thumbnailStartsWithMedia = media.thumbnailURL.startsWith('/media/');
                
                resultText += `🖼️ Thumbnail URL Analysis:\n`;
                resultText += `  - URL: ${media.thumbnailURL}\n`;
                resultText += `  - No /api/ anywhere: ${thumbnailClean ? 'PASS ✅' : 'FAIL ❌'}\n`;
                resultText += `  - Starts with /media/: ${thumbnailStartsWithMedia ? 'PASS ✅' : 'FAIL ❌'}\n\n`;
                
                thumbnailClean = thumbnailClean && thumbnailStartsWithMedia;
            }
            
            // Analyze sizes (most critical test)
            let allSizesClean = true;
            let sizesAnalysis = '';
            
            if (media.sizes && Object.keys(media.sizes).length > 0) {
                sizesAnalysis += `📐 Size URLs Analysis (Critical Test):\n`;
                
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeUrlClean = !sizeData.url.includes('/api/media/file/') && !sizeData.url.includes('/api/');
                        const sizeUrlStartsWithMedia = sizeData.url.startsWith('/media/');
                        const sizeUrlValid = sizeUrlClean && sizeUrlStartsWithMedia;
                        
                        if (!sizeUrlValid) allSizesClean = false;
                        
                        sizesAnalysis += `  - ${sizeName}: ${sizeData.url}\n`;
                        sizesAnalysis += `    Enhanced cleanup: ${sizeUrlClean ? 'PASS ✅' : 'FAIL ❌'}\n`;
                        sizesAnalysis += `    Starts /media/: ${sizeUrlStartsWithMedia ? 'PASS ✅' : 'FAIL ❌'}\n`;
                    }
                });
                sizesAnalysis += `\n`;
            } else {
                sizesAnalysis += `📐 No size URLs generated\n\n`;
            }
            
            resultText += sizesAnalysis;
            
            // Enhanced cleanup effectiveness
            resultText += `🔍 Enhanced Cleanup Effectiveness:\n`;
            resultText += `  - Main URL cleaned: ${mainUrlClean && mainUrlStartsWithMedia ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Thumbnail URL cleaned: ${thumbnailClean ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - All size URLs cleaned: ${allSizesClean ? 'YES ✅' : 'NO ❌'}\n`;
            
            // Overall assessment
            const enhancedCleanupWorking = mainUrlClean && mainUrlStartsWithMedia && thumbnailClean && allSizesClean;
            
            resultText += `  - Overall enhanced cleanup: ${enhancedCleanupWorking ? 'PERFECT ✅' : 'INCOMPLETE ❌'}\n\n`;
            
            if (enhancedCleanupWorking) {
                resultText += `🎉 ENHANCED CLEANUP SUCCESS!\n`;
                resultText += `✅ ALL URLs are completely clean!\n`;
                resultText += `✅ No /api/media/file/ or /api/ anywhere!\n`;
                resultText += `🎯 Multi-layer protection is working perfectly!\n`;
                resultText += `🌐 All URLs work with localhost:3001/media/`;
                showResultFunc('success', resultText);
            } else {
                resultText += `⚠️ Enhanced cleanup incomplete:\n`;
                if (!mainUrlClean || !mainUrlStartsWithMedia) resultText += `  - Main URL still has issues\n`;
                if (!thumbnailClean) resultText += `  - Thumbnail URL still has issues\n`;
                if (!allSizesClean) resultText += `  - Some size URLs still have issues\n`;
                resultText += `❌ Some URLs still contain /api/ prefix`;
                showResultFunc('error', resultText);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showTypeResult(type, message) {
            const element = document.getElementById('typeResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Enhanced URL Clean Test loaded');
            console.log('🎯 Testing multi-layer URL cleaning protection');
            console.log('📋 Should eliminate ALL /api/ prefixes from response');
            
            showResult('info', 'Ready to test enhanced URL cleaning. Select an image and click "Test Enhanced Upload".');
        });
    </script>
</body>
</html>
