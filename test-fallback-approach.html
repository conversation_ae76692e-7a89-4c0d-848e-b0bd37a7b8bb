<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Fallback Approach Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Fallback Approach Test</h1>
        <p>Test the fallback approach that handles Payload CMS upload conflicts.</p>
        
        <div class="info">
            <strong>🔍 New Approach:</strong><br>
            1. ✅ Save file to correct folder: media/avatars/<br>
            2. 🔄 Try to create media record in Payload<br>
            3. 🔄 If Payload conflicts, use fallback approach<br>
            4. ✅ File is saved regardless of database record issues
        </div>
    </div>

    <div class="container">
        <h3>📁 Avatar Upload Test</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image file to test avatar upload</p>
            <p style="color: #666; font-size: 14px;">Test the fallback approach for Payload conflicts</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn" onclick="uploadAvatar()" id="uploadBtn" disabled>Test Avatar Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🔍 Expected Outcomes</h3>
        <div class="info">
            <strong>Possible Results:</strong><br>
            <strong>✅ Best Case:</strong> Payload record creation works, full success<br>
            <strong>⚠️ Fallback Case:</strong> File saved successfully, database record skipped<br>
            <strong>❌ Worst Case:</strong> Still getting ENOENT errors
        </div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function uploadAvatar() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('uploadType', 'avatar');
            formData.append('updateUserField', 'avatar');

            try {
                showResult('info', 'Testing fallback approach for avatar upload...');
                
                console.log('🔄 Testing fallback approach...');
                console.log('📋 Upload details:', {
                    fileName: selectedFile.name,
                    fileSize: selectedFile.size,
                    fileType: selectedFile.type,
                    uploadType: 'avatar',
                    expectedFolder: 'avatars'
                });

                const startTime = Date.now();
                
                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                const endTime = Date.now();
                const uploadTime = endTime - startTime;

                console.log('📦 Response status:', response.status);
                console.log('⏱️ Upload time:', uploadTime + 'ms');

                const data = await response.json();
                console.log('📦 Upload response:', data);

                if (data.success) {
                    const actualUrl = data.media.url;
                    const isCorrectFolder = actualUrl.includes('/avatars/');
                    const hasMediaId = !!data.media.id;
                    const hasUserUpdate = !!data.user;
                    
                    showResult('success', 
                        `🎉 SUCCESS! Fallback approach working!\n\n` +
                        `✅ File Processing:\n` +
                        `  - File saved to correct folder: ${isCorrectFolder ? 'YES' : 'NO'}\n` +
                        `  - Actual URL: ${actualUrl}\n` +
                        `  - Upload time: ${uploadTime}ms\n\n` +
                        `✅ Database Records:\n` +
                        `  - Media ID created: ${hasMediaId ? 'YES' : 'NO'}\n` +
                        `  - Media ID: ${data.media.id || 'N/A'}\n` +
                        `  - Media Type: ${data.media.mediaType}\n\n` +
                        `✅ User Update:\n` +
                        `  - User avatar updated: ${hasUserUpdate ? 'YES' : 'NO'}\n` +
                        `  - User ID: ${data.user?.id || 'N/A'}\n\n` +
                        `✅ Response Data:\n` +
                        `  - Upload Type: ${data.uploadType}\n` +
                        `  - Message: ${data.message}\n\n` +
                        `🎯 Fallback approach successful!`
                    );
                    
                    console.log('🎉 SUCCESS WITH FALLBACK APPROACH!');
                    console.log('✅ File saved to correct folder:', isCorrectFolder);
                    console.log('✅ Media record handling:', hasMediaId ? 'Database record created' : 'Fallback object used');
                    console.log('✅ User avatar updated:', hasUserUpdate);
                    
                } else {
                    showResult('error', `❌ Upload still failed: ${data.message}`);
                    
                    console.error('❌ UPLOAD STILL FAILING');
                    console.error('Error message:', data.message);
                    
                    // Analyze the error
                    if (data.message.includes('ENOENT')) {
                        console.error('🔍 ENOENT Error Analysis:');
                        
                        const pathMatch = data.message.match(/open '([^']+)'/);
                        if (pathMatch) {
                            const attemptedPath = pathMatch[1];
                            console.error('Attempted path:', attemptedPath);
                            
                            const hasAvatarsFolder = attemptedPath.includes('\\avatars\\') || attemptedPath.includes('/avatars/');
                            console.error('Has avatars folder:', hasAvatarsFolder);
                            
                            showResult('error',
                                `❌ ENOENT Error Analysis:\n\n` +
                                `Attempted path: ${attemptedPath}\n` +
                                `Has avatars folder: ${hasAvatarsFolder ? 'YES' : 'NO'}\n\n` +
                                `${hasAvatarsFolder ? 
                                    '⚠️ Path is correct but file access failed. This might be a timing or permission issue.' : 
                                    '❌ Path is wrong! The file is still being saved to the wrong location.'
                                }\n\n` +
                                `🔍 This suggests the error is happening before our fallback approach can take effect.`
                            );
                        }
                    } else {
                        showResult('error', `❌ Non-ENOENT error: ${data.message}`);
                    }
                }
            } catch (error) {
                console.error('❌ Upload error:', error);
                showResult('error', `❌ Upload error: ${error.message}`);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔄 Fallback Approach Test loaded');
            console.log('🎯 Testing endpoint: http://localhost:3001/upload');
            console.log('📋 Expected behavior:');
            console.log('  1. File saved to media/avatars/ (should work)');
            console.log('  2. Try to create Payload media record');
            console.log('  3. If Payload conflicts, use fallback approach');
            console.log('  4. Return success regardless of database record issues');
            
            showResult('info', 'Ready to test the fallback approach. Select an image and click "Test Avatar Upload".');
        });
    </script>
</body>
</html>
