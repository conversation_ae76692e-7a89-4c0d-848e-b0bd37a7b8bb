<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Folder Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Folder Debug Test</h1>
        <p>Debug why files are not being saved to the correct folder.</p>
        
        <div class="error">
            <strong>❌ Current Issue:</strong> Files are being saved to root media/ instead of subfolders like media/avatars/
        </div>
    </div>

    <div class="container">
        <h3>📁 Test Avatar Upload</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image file to test avatar upload</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn" onclick="uploadAvatar()" id="uploadBtn" disabled>Test Avatar Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🔍 Debug Information</h3>
        <p>Check the browser console for detailed logging from the storage service.</p>
        <div id="debugInfo" class="info">
            Expected behavior:<br>
            1. uploadType: 'avatar' → folder: 'avatars'<br>
            2. File should be saved to: media/avatars/filename.jpg<br>
            3. URL should be: /media/avatars/filename.jpg
        </div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function uploadAvatar() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('uploadType', 'avatar'); // This should trigger folder: 'avatars'
            formData.append('updateUserField', 'avatar');

            try {
                showResult('info', 'Testing avatar upload with folder debugging...');
                
                console.log('🚀 Starting avatar upload test...');
                console.log('📋 Upload details:', {
                    fileName: selectedFile.name,
                    fileSize: selectedFile.size,
                    fileType: selectedFile.type,
                    uploadType: 'avatar',
                    expectedFolder: 'avatars',
                    expectedPath: 'media/avatars/' + selectedFile.name
                });

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);

                const data = await response.json();
                console.log('📦 Upload response:', data);

                if (data.success) {
                    const actualPath = data.media.url;
                    const expectedPath = '/media/avatars/';
                    const isCorrectFolder = actualPath.includes('/avatars/');
                    
                    showResult(
                        isCorrectFolder ? 'success' : 'error', 
                        `${isCorrectFolder ? '✅' : '❌'} Upload ${isCorrectFolder ? 'successful' : 'failed'}!\n\n` +
                        `Expected folder: /media/avatars/\n` +
                        `Actual URL: ${actualPath}\n` +
                        `Correct folder: ${isCorrectFolder ? 'YES' : 'NO'}\n\n` +
                        `Media ID: ${data.media.id}\n` +
                        `Type: ${data.media.mediaType}\n` +
                        `Upload Type: ${data.uploadType}`
                    );
                    
                    if (!isCorrectFolder) {
                        console.error('❌ FOLDER ISSUE: File was not saved to the correct folder!');
                        console.error('Expected: /media/avatars/');
                        console.error('Actual:', actualPath);
                    }
                } else {
                    showResult('error', `❌ Upload failed: ${data.message}`);
                    
                    // Check if it's the directory error
                    if (data.message.includes('ENOENT') && data.message.includes('no such file or directory')) {
                        console.error('❌ DIRECTORY ERROR: The file path in the error shows where it tried to save:');
                        console.error(data.message);
                        
                        // Extract the path from the error message
                        const pathMatch = data.message.match(/open '([^']+)'/);
                        if (pathMatch) {
                            const attemptedPath = pathMatch[1];
                            console.error('Attempted path:', attemptedPath);
                            
                            // Check if it includes the folder
                            const hasAvatarsFolder = attemptedPath.includes('\\avatars\\') || attemptedPath.includes('/avatars/');
                            console.error('Includes avatars folder:', hasAvatarsFolder);
                            
                            showResult('error', 
                                `❌ Directory Error Analysis:\n\n` +
                                `Attempted path: ${attemptedPath}\n` +
                                `Includes avatars folder: ${hasAvatarsFolder ? 'YES' : 'NO'}\n\n` +
                                `${hasAvatarsFolder ? 
                                    'The folder path is correct, but the directory might not exist.' : 
                                    'The folder path is WRONG! File is being saved to root media/ instead of media/avatars/'
                                }`
                            );
                        }
                    }
                }
            } catch (error) {
                console.error('❌ Upload error:', error);
                showResult('error', `❌ Upload error: ${error.message}`);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔍 Folder Debug Test loaded');
            console.log('🎯 Testing endpoint: http://localhost:3001/upload');
            console.log('📋 Expected behavior:');
            console.log('  1. uploadType: "avatar" should map to folder: "avatars"');
            console.log('  2. File should be saved to: media/avatars/filename.jpg');
            console.log('  3. URL should be: /media/avatars/filename.jpg');
            console.log('');
            console.log('🔍 Watch the console for detailed logging from the storage service');
            
            showResult('info', 'Ready to test avatar upload folder handling. Select an image and click "Test Avatar Upload".');
        });
    </script>
</body>
</html>
