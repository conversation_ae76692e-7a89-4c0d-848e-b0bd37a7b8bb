<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Full URL Cleaning Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .url-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Full URL Cleaning Test</h1>
        <p>Test the enhanced URL cleaning that handles full URLs from Payload.</p>
        
        <div class="success">
            <strong>✅ Enhanced Cleaning:</strong> Now handles full URLs<br>
            - Detects: http://localhost:3001/api/media/file/filename.jpg<br>
            - Converts to: /media/file/filename.jpg<br>
            - Also handles: /api/media/file/filename.jpg → /media/file/filename.jpg<br>
            - Works for main URL, thumbnail URL, and all size URLs
        </div>
    </div>

    <div class="container">
        <h3>🔍 URL Cleaning Examples</h3>
        <div class="url-example">
            <strong>Full URL Cleaning:</strong><br>
            Input: <code>http://localhost:3001/api/media/file/avatar.jpg</code><br>
            Output: <code>/media/file/avatar.jpg</code><br><br>
            
            <strong>Relative URL Cleaning:</strong><br>
            Input: <code>/api/media/file/avatar.jpg</code><br>
            Output: <code>/media/file/avatar.jpg</code><br><br>
            
            <strong>Already Clean URLs:</strong><br>
            Input: <code>/media/file/avatar.jpg</code><br>
            Output: <code>/media/file/avatar.jpg</code> (unchanged)
        </div>
    </div>

    <div class="container">
        <h3>📁 Test Upload with Full URL Cleaning</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test full URL cleaning</p>
            <p style="color: #666; font-size: 14px;">Should clean all URLs regardless of format</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testFullUrlCleaning()" id="uploadBtn" disabled>Test Full URL Cleaning</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testFullUrlCleaning() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showResult('info', 'Testing full URL cleaning...');
                
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'avatar');

                console.log('🚀 Testing full URL cleaning');

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeFullUrlCleaning(data);
                } else {
                    showResult('error', `Upload failed: ${data.message}`);
                }

            } catch (error) {
                console.error('❌ Upload error:', error);
                showResult('error', `Upload error: ${error.message}`);
            }
        }

        function analyzeFullUrlCleaning(data) {
            const media = data.media;
            
            if (!media) {
                showResult('error', 'No media object in response');
                return;
            }

            let resultText = `🔧 Full URL Cleaning Analysis:\n\n`;
            
            // Analyze main URL
            const mainUrl = media.url;
            const mainUrlClean = !mainUrl.includes('://') && !mainUrl.includes('/api/');
            const mainUrlCorrect = mainUrl.startsWith('/media/file/');
            
            resultText += `📋 Main URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - No domain: ${!mainUrl.includes('://') ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - No /api/ prefix: ${!mainUrl.includes('/api/') ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Correct format: ${mainUrlCorrect ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Cleaning worked: ${mainUrlClean && mainUrlCorrect ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            // Analyze thumbnail URL if present
            let thumbnailClean = true;
            if (media.thumbnailURL) {
                const thumbClean = !media.thumbnailURL.includes('://') && !media.thumbnailURL.includes('/api/');
                const thumbCorrect = media.thumbnailURL.startsWith('/media/file/');
                thumbnailClean = thumbClean && thumbCorrect;
                
                resultText += `🖼️ Thumbnail URL Analysis:\n`;
                resultText += `  - URL: ${media.thumbnailURL}\n`;
                resultText += `  - No domain: ${!media.thumbnailURL.includes('://') ? 'PASS ✅' : 'FAIL ❌'}\n`;
                resultText += `  - No /api/ prefix: ${!media.thumbnailURL.includes('/api/') ? 'PASS ✅' : 'FAIL ❌'}\n`;
                resultText += `  - Correct format: ${thumbCorrect ? 'PASS ✅' : 'FAIL ❌'}\n\n`;
            }
            
            // Analyze sizes (most important test)
            let allSizesClean = true;
            let sizesAnalysis = '';
            
            if (media.sizes && Object.keys(media.sizes).length > 0) {
                sizesAnalysis += `📐 Size URLs Analysis (Critical Test):\n`;
                
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeClean = !sizeData.url.includes('://') && !sizeData.url.includes('/api/');
                        const sizeCorrect = sizeData.url.startsWith('/media/file/');
                        const sizeValid = sizeClean && sizeCorrect;
                        
                        if (!sizeValid) allSizesClean = false;
                        
                        sizesAnalysis += `  - ${sizeName}: ${sizeData.url}\n`;
                        sizesAnalysis += `    No domain: ${!sizeData.url.includes('://') ? '✅' : '❌'}\n`;
                        sizesAnalysis += `    No /api/: ${!sizeData.url.includes('/api/') ? '✅' : '❌'}\n`;
                        sizesAnalysis += `    Correct format: ${sizeCorrect ? '✅' : '❌'}\n`;
                    }
                });
                sizesAnalysis += `\n`;
            } else {
                sizesAnalysis += `📐 No size URLs generated\n\n`;
            }
            
            resultText += sizesAnalysis;
            
            // Overall assessment
            const fullCleaningWorking = mainUrlClean && mainUrlCorrect && thumbnailClean && allSizesClean;
            
            resultText += `🎯 Overall Assessment:\n`;
            resultText += `  - Main URL cleaned: ${mainUrlClean && mainUrlCorrect ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Thumbnail URL cleaned: ${thumbnailClean ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - All size URLs cleaned: ${allSizesClean ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Full URL cleaning working: ${fullCleaningWorking ? 'PERFECT ✅' : 'INCOMPLETE ❌'}\n\n`;
            
            if (fullCleaningWorking) {
                resultText += `🎉 FULL URL CLEANING SUCCESS!\n`;
                resultText += `✅ All URLs are completely clean!\n`;
                resultText += `✅ No domains or /api/ prefixes anywhere!\n`;
                resultText += `🎯 Enhanced cleaning handles all URL formats perfectly!\n`;
                resultText += `🌐 URLs ready for /media/file/ route serving!`;
                showResult('success', resultText);
            } else {
                resultText += `⚠️ Full URL cleaning incomplete:\n`;
                if (!mainUrlClean || !mainUrlCorrect) resultText += `  - Main URL still has issues\n`;
                if (!thumbnailClean) resultText += `  - Thumbnail URL still has issues\n`;
                if (!allSizesClean) resultText += `  - Some size URLs still have issues\n`;
                resultText += `❌ Enhanced cleaning may need more work`;
                showResult('error', resultText);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Full URL Cleaning Test loaded');
            console.log('🎯 Testing enhanced URL cleaning for full URLs');
            console.log('📋 Should handle http://localhost:3001/api/media/file/ URLs');
            
            showResult('info', 'Ready to test full URL cleaning. Select an image and click "Test Full URL Cleaning".');
        });
    </script>
</body>
</html>
