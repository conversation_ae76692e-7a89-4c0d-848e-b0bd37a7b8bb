<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Full URL Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-box {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid;
        }
        .comparison-box.before {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .comparison-box.after {
            border-color: #28a745;
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Full URL Fix Test</h1>
        <p>Test that the enhanced hooks can handle full URLs that Payload generates.</p>
        
        <div class="success">
            <strong>✅ Enhanced Hooks:</strong> Now handle full URLs<br>
            - Detects: http://localhost:3001/api/media/file/filename.jpg<br>
            - Converts to: /media/file/filename.jpg<br>
            - Handles both full URLs and relative URLs<br>
            - Works in both beforeChange and afterChange hooks
        </div>
    </div>

    <div class="container">
        <h3>🔍 Before vs After Comparison</h3>
        <div class="comparison">
            <div class="comparison-box before">
                <h4>❌ Before Enhanced Fix</h4>
                <p><strong>Payload generates:</strong><br>
                <code>http://localhost:3001/api/media/file/filename.jpg</code></p>
                <p><strong>Hooks couldn't handle:</strong><br>
                Full URLs with domain</p>
                <p><strong>Result:</strong><br>
                URLs still had /api/ prefix</p>
            </div>
            <div class="comparison-box after">
                <h4>✅ After Enhanced Fix</h4>
                <p><strong>Payload generates:</strong><br>
                <code>http://localhost:3001/api/media/file/filename.jpg</code></p>
                <p><strong>Enhanced hooks convert to:</strong><br>
                <code>/media/file/filename.jpg</code></p>
                <p><strong>Result:</strong><br>
                Clean URLs without domain or /api/</p>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>📁 Test Enhanced Upload</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test enhanced URL fixing</p>
            <p style="color: #666; font-size: 14px;">Should handle full URLs and convert them to clean paths</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testEnhancedUrlFix()" id="uploadBtn" disabled>Test Enhanced URL Fix</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🧪 Test URL Patterns</h3>
        <p>Test various URL patterns to ensure all are handled correctly:</p>
        
        <button class="btn" onclick="testUrlPattern('avatar')">Test Avatar URLs</button>
        <button class="btn" onclick="testUrlPattern('course_thumbnail')">Test Course URLs</button>
        <button class="btn" onclick="testUrlPattern('document')">Test Document URLs</button>
        
        <div id="patternResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testEnhancedUrlFix() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, 'avatar', 'Enhanced URL Fix Test');
        }

        async function testUrlPattern(uploadType) {
            if (!selectedFile) {
                showPatternResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, uploadType, `${uploadType} URL Pattern Test`, true);
        }

        async function testUploadWithFile(file, uploadType, testName, usePatternResult = false) {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                const showResultFunc = usePatternResult ? showPatternResult : showResult;
                showResultFunc('info', `Testing ${testName}...`);
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('uploadType', uploadType);

                console.log(`🚀 Testing ${testName}:`, {
                    fileName: file.name,
                    uploadType: uploadType
                });

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeEnhancedUrlFix(data, testName, showResultFunc);
                } else {
                    showResultFunc('error', `${testName} failed: ${data.message}`);
                }

            } catch (error) {
                console.error(`❌ ${testName} error:`, error);
                const showResultFunc = usePatternResult ? showPatternResult : showResult;
                showResultFunc('error', `${testName} error: ${error.message}`);
            }
        }

        function analyzeEnhancedUrlFix(data, testName, showResultFunc) {
            const media = data.media;
            
            if (!media) {
                showResultFunc('error', `No media object in ${testName} response`);
                return;
            }

            let resultText = `🔧 ${testName} Enhanced URL Analysis:\n\n`;
            
            // Analyze main URL
            const mainUrl = media.url;
            const isCleanUrl = !mainUrl.includes('://') && !mainUrl.includes('/api/');
            const hasCorrectFormat = mainUrl.startsWith('/media/');
            
            resultText += `📋 Main URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - No domain: ${!mainUrl.includes('://') ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - No /api/ prefix: ${!mainUrl.includes('/api/') ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Correct format: ${hasCorrectFormat ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Enhanced hooks working: ${isCleanUrl && hasCorrectFormat ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            // Analyze thumbnail URL if present
            let thumbnailFixed = true;
            if (media.thumbnailURL) {
                const thumbClean = !media.thumbnailURL.includes('://') && !media.thumbnailURL.includes('/api/');
                const thumbCorrectFormat = media.thumbnailURL.startsWith('/media/');
                thumbnailFixed = thumbClean && thumbCorrectFormat;
                
                resultText += `🖼️ Thumbnail URL Analysis:\n`;
                resultText += `  - URL: ${media.thumbnailURL}\n`;
                resultText += `  - No domain: ${!media.thumbnailURL.includes('://') ? 'PASS ✅' : 'FAIL ❌'}\n`;
                resultText += `  - No /api/ prefix: ${!media.thumbnailURL.includes('/api/') ? 'PASS ✅' : 'FAIL ❌'}\n`;
                resultText += `  - Correct format: ${thumbCorrectFormat ? 'PASS ✅' : 'FAIL ❌'}\n\n`;
            }
            
            // Analyze sizes
            let allSizesFixed = true;
            let sizesAnalysis = '';
            
            if (media.sizes && Object.keys(media.sizes).length > 0) {
                sizesAnalysis += `📐 Size URLs Analysis:\n`;
                
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeClean = !sizeData.url.includes('://') && !sizeData.url.includes('/api/');
                        const sizeCorrectFormat = sizeData.url.startsWith('/media/');
                        const sizeFixed = sizeClean && sizeCorrectFormat;
                        
                        if (!sizeFixed) allSizesFixed = false;
                        
                        sizesAnalysis += `  - ${sizeName}: ${sizeData.url}\n`;
                        sizesAnalysis += `    No domain: ${!sizeData.url.includes('://') ? '✅' : '❌'}\n`;
                        sizesAnalysis += `    No /api/: ${!sizeData.url.includes('/api/') ? '✅' : '❌'}\n`;
                        sizesAnalysis += `    Correct format: ${sizeCorrectFormat ? '✅' : '❌'}\n`;
                    }
                });
                sizesAnalysis += `\n`;
            } else {
                sizesAnalysis += `📐 No size URLs generated\n\n`;
            }
            
            resultText += sizesAnalysis;
            
            // Overall assessment
            const enhancedFixWorking = isCleanUrl && hasCorrectFormat && thumbnailFixed && allSizesFixed;
            
            resultText += `🎯 Overall Assessment:\n`;
            resultText += `  - Main URL enhanced: ${isCleanUrl && hasCorrectFormat ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Thumbnail URL enhanced: ${thumbnailFixed ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - All size URLs enhanced: ${allSizesFixed ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Enhanced hooks working: ${enhancedFixWorking ? 'PERFECT ✅' : 'INCOMPLETE ❌'}\n\n`;
            
            if (enhancedFixWorking) {
                resultText += `🎉 ENHANCED URL FIX SUCCESS!\n`;
                resultText += `✅ All URLs are clean and properly formatted!\n`;
                resultText += `✅ No domains or /api/ prefixes anywhere!\n`;
                resultText += `🎯 Enhanced hooks handle all URL patterns perfectly!`;
                showResultFunc('success', resultText);
            } else {
                resultText += `⚠️ Enhanced URL fix incomplete:\n`;
                if (!isCleanUrl || !hasCorrectFormat) resultText += `  - Main URL still has issues\n`;
                if (!thumbnailFixed) resultText += `  - Thumbnail URL still has issues\n`;
                if (!allSizesFixed) resultText += `  - Some size URLs still have issues\n`;
                resultText += `❌ Enhanced hooks may need more work`;
                showResultFunc('error', resultText);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showPatternResult(type, message) {
            const element = document.getElementById('patternResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Full URL Fix Test loaded');
            console.log('🎯 Testing enhanced hooks that handle full URLs');
            console.log('📋 Should convert http://localhost:3001/api/media/file/ to /media/file/');
            
            showResult('info', 'Ready to test enhanced URL fix. Select an image and click "Test Enhanced URL Fix".');
        });
    </script>
</body>
</html>
