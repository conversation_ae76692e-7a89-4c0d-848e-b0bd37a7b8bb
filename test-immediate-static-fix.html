<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Immediate Static Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.warning {
            background-color: #ffc107;
            color: #212529;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .test-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .image-preview {
            margin: 20px 0;
            text-align: center;
        }
        .image-preview img {
            max-width: 300px;
            max-height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Immediate Static Fix Test</h1>
        <p>Test the immediate static file serving fix that works without server restart.</p>
        
        <div class="success">
            <strong>✅ Immediate Fix:</strong> Created custom endpoint to serve static files<br>
            - Works immediately without server restart<br>
            - Handles all file types with proper MIME types<br>
            - Includes security checks and caching headers
        </div>
    </div>

    <div class="container">
        <h3>🔍 Debug Media Directory</h3>
        <p>First, let's check what files exist in the media directory:</p>
        
        <button class="btn success" onclick="debugMediaDirectory()">Debug Media Directory</button>
        <div id="debugResult"></div>
    </div>

    <div class="container">
        <h3>📁 Test Specific URL</h3>
        <p>Test the exact URL that was giving 404 error:</p>
        
        <div class="test-url">
            <strong>Test URL:</strong> http://localhost:3001/media/avatars/Screenshot%202023-06-10%20122948-1752209816761-a3db1a39-6938-4461-966b-f9de9c31e80a.png
        </div>
        
        <button class="btn" onclick="testSpecificUrl()">Test This URL</button>
        <button class="btn warning" onclick="testUrlInNewTab()">Open in New Tab</button>
        
        <div id="urlTestResult"></div>
        <div id="imagePreview" class="image-preview"></div>
    </div>

    <div class="container">
        <h3>📂 Test All Found Files</h3>
        <p>Test static serving for all files found in the media directory:</p>
        
        <button class="btn success" onclick="testAllFiles()">Test All Files</button>
        <div id="allFilesResult"></div>
    </div>

    <script>
        async function debugMediaDirectory() {
            try {
                showDebugResult('info', 'Checking media directory structure...');
                
                const response = await fetch('http://localhost:3001/debug/media', {
                    method: 'GET',
                });

                console.log('🔍 Debug response status:', response.status);

                const data = await response.json();
                console.log('🔍 Debug response:', data);

                if (data.success) {
                    let resultText = `✅ Media Directory Debug Results:\n\n`;
                    resultText += `📁 Media Directory: ${data.mediaDir}\n`;
                    resultText += `📊 Total Files: ${data.totalFiles}\n\n`;
                    
                    if (data.files && data.files.length > 0) {
                        resultText += `📋 Files Found:\n`;
                        data.files.forEach((file, index) => {
                            resultText += `${index + 1}. ${file.name}\n`;
                            resultText += `   Path: ${file.path}\n`;
                            resultText += `   URL: ${file.url}\n`;
                            resultText += `   Size: ${(file.size / 1024).toFixed(1)} KB\n`;
                            resultText += `   Modified: ${new Date(file.modified).toLocaleString()}\n\n`;
                        });
                        
                        if (data.totalFiles > data.files.length) {
                            resultText += `... and ${data.totalFiles - data.files.length} more files\n`;
                        }
                    } else {
                        resultText += `⚠️ No files found in media directory\n`;
                    }
                    
                    showDebugResult('success', resultText);
                    
                    // Store files for testing
                    window.mediaFiles = data.files || [];
                } else {
                    showDebugResult('error', `❌ Debug failed: ${data.message}`);
                }
            } catch (error) {
                console.error('❌ Debug error:', error);
                showDebugResult('error', `❌ Debug error: ${error.message}`);
            }
        }

        async function testSpecificUrl() {
            const testUrl = 'http://localhost:3001/media/avatars/Screenshot%202023-06-10%20122948-1752209816761-a3db1a39-6938-4461-966b-f9de9c31e80a.png';
            
            try {
                showUrlTestResult('info', 'Testing specific URL with new endpoint...');
                
                console.log('🔍 Testing URL:', testUrl);
                
                const response = await fetch(testUrl, {
                    method: 'GET',
                });

                console.log('📦 Response:', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                });

                if (response.ok) {
                    const contentType = response.headers.get('content-type') || 'unknown';
                    const contentLength = response.headers.get('content-length') || 'unknown';
                    
                    showUrlTestResult('success', 
                        `🎉 SUCCESS! URL now works!\n\n` +
                        `✅ Response Details:\n` +
                        `  - Status: ${response.status} ${response.statusText}\n` +
                        `  - Content-Type: ${contentType}\n` +
                        `  - Content-Length: ${contentLength} bytes\n` +
                        `  - Cache-Control: ${response.headers.get('cache-control') || 'N/A'}\n\n` +
                        `🎯 The immediate static fix is working!`
                    );
                    
                    // Show image preview if it's an image
                    if (contentType.startsWith('image/')) {
                        showImagePreview(testUrl, 'Screenshot');
                    }
                } else {
                    showUrlTestResult('error', 
                        `❌ URL still not working!\n\n` +
                        `Status: ${response.status} ${response.statusText}\n\n` +
                        `This means the endpoint might not be loaded yet or there's another issue.`
                    );
                }
            } catch (error) {
                console.error('❌ URL test error:', error);
                showUrlTestResult('error', `❌ URL test error: ${error.message}`);
            }
        }

        function testUrlInNewTab() {
            const testUrl = 'http://localhost:3001/media/avatars/Screenshot%202023-06-10%20122948-1752209816761-a3db1a39-6938-4461-966b-f9de9c31e80a.png';
            window.open(testUrl, '_blank');
            showUrlTestResult('info', 'Opened URL in new tab. Check if it loads or shows 404.');
        }

        async function testAllFiles() {
            if (!window.mediaFiles || window.mediaFiles.length === 0) {
                showAllFilesResult('warning', 'No files found. Run "Debug Media Directory" first.');
                return;
            }
            
            try {
                showAllFilesResult('info', `Testing static serving for ${window.mediaFiles.length} files...`);
                
                const results = [];
                
                for (const file of window.mediaFiles.slice(0, 5)) { // Test first 5 files
                    const testUrl = `http://localhost:3001${file.url}`;
                    
                    try {
                        const response = await fetch(testUrl, { method: 'HEAD' });
                        results.push({
                            file: file.name,
                            url: file.url,
                            status: response.status,
                            success: response.ok
                        });
                    } catch (error) {
                        results.push({
                            file: file.name,
                            url: file.url,
                            status: 'Error',
                            success: false,
                            error: error.message
                        });
                    }
                }
                
                let resultText = `📊 Static Serving Test Results:\n\n`;
                let successCount = 0;
                
                results.forEach((result, index) => {
                    const status = result.success ? '✅' : '❌';
                    resultText += `${index + 1}. ${status} ${result.file}\n`;
                    resultText += `   URL: ${result.url}\n`;
                    resultText += `   Status: ${result.status}\n`;
                    if (result.error) {
                        resultText += `   Error: ${result.error}\n`;
                    }
                    resultText += `\n`;
                    
                    if (result.success) successCount++;
                });
                
                resultText += `\n📈 Summary: ${successCount}/${results.length} files accessible\n`;
                
                if (successCount === results.length) {
                    resultText += `\n🎉 All files are now accessible via static serving!`;
                    showAllFilesResult('success', resultText);
                } else if (successCount > 0) {
                    resultText += `\n⚠️ Some files are accessible, but not all.`;
                    showAllFilesResult('warning', resultText);
                } else {
                    resultText += `\n❌ No files are accessible. The static endpoint might not be working.`;
                    showAllFilesResult('error', resultText);
                }
                
            } catch (error) {
                console.error('❌ Test all files error:', error);
                showAllFilesResult('error', `❌ Test error: ${error.message}`);
            }
        }

        function showImagePreview(imageUrl, filename) {
            const previewDiv = document.getElementById('imagePreview');
            previewDiv.innerHTML = `
                <h4>🖼️ Image Preview: ${filename}</h4>
                <img src="${imageUrl}" alt="${filename}" 
                     onload="console.log('✅ Image loaded successfully')" 
                     onerror="console.error('❌ Image failed to load')">
                <p><a href="${imageUrl}" target="_blank">Open in new tab</a></p>
            `;
        }

        function showDebugResult(type, message) {
            const element = document.getElementById('debugResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showUrlTestResult(type, message) {
            const element = document.getElementById('urlTestResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showAllFilesResult(type, message) {
            const element = document.getElementById('allFilesResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 Immediate Static Fix Test loaded');
            console.log('🎯 Testing immediate static file serving fix');
            console.log('📋 This fix works without server restart');
            
            showDebugResult('info', 'Ready to test immediate static file serving fix. Click "Debug Media Directory" to start.');
        });
    </script>
</body>
</html>
