<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Media File Route Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .route-explanation {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Media File Route Test</h1>
        <p>Test the new `/media/file/` route that serves files with Payload's generated URLs.</p>
        
        <div class="success">
            <strong>✅ Smart Solution:</strong> Created /media/file/ route handler<br>
            - No need to change Payload's URL generation<br>
            - Route serves files at /media/file/filename.jpg<br>
            - Works with existing /api/media/file/ URLs (removes /api/)<br>
            - Handles all file types and sizes automatically
        </div>
    </div>

    <div class="container">
        <h3>🔄 How It Works</h3>
        <div class="route-explanation">
            <strong>Smart Route Solution:</strong><br><br>
            
            <strong>1. Payload generates:</strong> `/api/media/file/filename.jpg`<br>
            <strong>2. Frontend removes /api/:</strong> `/media/file/filename.jpg`<br>
            <strong>3. Route handler serves:</strong> File from media/filename.jpg<br>
            <strong>4. Result:</strong> URLs work perfectly!<br><br>
            
            <em>No complex URL replacement needed - just smart routing!</em>
        </div>
    </div>

    <div class="container">
        <h3>📁 Test Upload & Route</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test upload and route serving</p>
            <p style="color: #666; font-size: 14px;">Will test both upload and file serving via route</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testUploadAndRoute()" id="uploadBtn" disabled>Test Upload & Route</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🧪 Test Route Access</h3>
        <p>Test direct access to files via the new route:</p>
        
        <button class="btn" onclick="testRouteAccess()">Test Route Access</button>
        <button class="btn" onclick="testSizeUrls()">Test Size URLs</button>
        
        <div id="routeResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;
        let lastUploadedUrls = [];

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testUploadAndRoute() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showResult('info', 'Testing upload and route serving...');
                
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'avatar');

                console.log('🚀 Testing upload and route');

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    await analyzeUploadAndRoute(data);
                } else {
                    showResult('error', `Upload failed: ${data.message}`);
                }

            } catch (error) {
                console.error('❌ Upload error:', error);
                showResult('error', `Upload error: ${error.message}`);
            }
        }

        async function analyzeUploadAndRoute(data) {
            const media = data.media;
            
            if (!media) {
                showResult('error', 'No media object in response');
                return;
            }

            let resultText = `🎉 Upload & Route Test Results:\n\n`;
            
            // Collect all URLs to test
            const urlsToTest = [];
            
            if (media.url) {
                urlsToTest.push({ type: 'Main URL', url: media.url });
            }
            
            if (media.thumbnailURL) {
                urlsToTest.push({ type: 'Thumbnail URL', url: media.thumbnailURL });
            }
            
            if (media.sizes) {
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        urlsToTest.push({ type: `Size ${sizeName}`, url: sizeData.url });
                    }
                });
            }
            
            lastUploadedUrls = urlsToTest;
            
            resultText += `📋 Found ${urlsToTest.length} URLs to test:\n`;
            urlsToTest.forEach(item => {
                resultText += `  - ${item.type}: ${item.url}\n`;
            });
            resultText += `\n`;
            
            // Test each URL
            let allWorking = true;
            resultText += `🔍 Testing Route Access:\n`;
            
            for (const item of urlsToTest) {
                try {
                    // Convert /api/media/file/ to /media/file/ for testing
                    const testUrl = item.url.replace(/^\/api\//, '/');
                    const fullUrl = `http://localhost:3001${testUrl}`;
                    
                    console.log(`🔍 Testing: ${fullUrl}`);
                    
                    const testResponse = await fetch(fullUrl, { method: 'HEAD' });
                    const working = testResponse.ok;
                    
                    if (!working) allWorking = false;
                    
                    resultText += `  - ${item.type}: ${working ? 'WORKS ✅' : 'FAILS ❌'} (${testResponse.status})\n`;
                    resultText += `    URL: ${fullUrl}\n`;
                } catch (error) {
                    allWorking = false;
                    resultText += `  - ${item.type}: ERROR ❌ (${error.message})\n`;
                }
            }
            
            resultText += `\n🎯 Overall Assessment:\n`;
            resultText += `  - Upload successful: YES ✅\n`;
            resultText += `  - Route serving files: ${allWorking ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Solution working: ${allWorking ? 'PERFECT ✅' : 'NEEDS WORK ❌'}\n\n`;
            
            if (allWorking) {
                resultText += `🎉 SUCCESS! The /media/file/ route is working!\n`;
                resultText += `✅ All URLs are accessible via the route handler!\n`;
                resultText += `🎯 No URL modification needed - smart routing works!`;
                showResult('success', resultText);
            } else {
                resultText += `⚠️ Some URLs are not accessible via the route.\n`;
                resultText += `❌ Check the route implementation or file locations.`;
                showResult('error', resultText);
            }
        }

        async function testRouteAccess() {
            if (lastUploadedUrls.length === 0) {
                showRouteResult('error', 'Please upload a file first to test route access');
                return;
            }

            try {
                showRouteResult('info', 'Testing route access for uploaded files...');
                
                let resultText = `🔍 Route Access Test Results:\n\n`;
                
                for (const item of lastUploadedUrls) {
                    // Test the route URL (remove /api/ prefix)
                    const routeUrl = item.url.replace(/^\/api\//, '/');
                    const fullUrl = `http://localhost:3001${routeUrl}`;
                    
                    try {
                        const response = await fetch(fullUrl, { method: 'HEAD' });
                        const contentType = response.headers.get('content-type') || 'unknown';
                        
                        resultText += `📁 ${item.type}:\n`;
                        resultText += `  - Original: ${item.url}\n`;
                        resultText += `  - Route URL: ${routeUrl}\n`;
                        resultText += `  - Status: ${response.status} ${response.statusText}\n`;
                        resultText += `  - Content-Type: ${contentType}\n`;
                        resultText += `  - Working: ${response.ok ? 'YES ✅' : 'NO ❌'}\n\n`;
                    } catch (error) {
                        resultText += `📁 ${item.type}:\n`;
                        resultText += `  - Route URL: ${routeUrl}\n`;
                        resultText += `  - Error: ${error.message}\n`;
                        resultText += `  - Working: NO ❌\n\n`;
                    }
                }
                
                showRouteResult('info', resultText);
                
            } catch (error) {
                showRouteResult('error', `Route access test error: ${error.message}`);
            }
        }

        async function testSizeUrls() {
            if (lastUploadedUrls.length === 0) {
                showRouteResult('error', 'Please upload a file first to test size URLs');
                return;
            }

            try {
                showRouteResult('info', 'Testing size URLs specifically...');
                
                const sizeUrls = lastUploadedUrls.filter(item => item.type.includes('Size'));
                
                if (sizeUrls.length === 0) {
                    showRouteResult('info', 'No size URLs found in last upload');
                    return;
                }
                
                let resultText = `📐 Size URLs Test Results:\n\n`;
                
                for (const item of sizeUrls) {
                    const routeUrl = item.url.replace(/^\/api\//, '/');
                    const fullUrl = `http://localhost:3001${routeUrl}`;
                    
                    try {
                        const response = await fetch(fullUrl, { method: 'HEAD' });
                        
                        resultText += `🖼️ ${item.type}:\n`;
                        resultText += `  - URL: ${routeUrl}\n`;
                        resultText += `  - Status: ${response.status}\n`;
                        resultText += `  - Working: ${response.ok ? 'YES ✅' : 'NO ❌'}\n\n`;
                    } catch (error) {
                        resultText += `🖼️ ${item.type}:\n`;
                        resultText += `  - URL: ${routeUrl}\n`;
                        resultText += `  - Error: ${error.message}\n`;
                        resultText += `  - Working: NO ❌\n\n`;
                    }
                }
                
                showRouteResult('info', resultText);
                
            } catch (error) {
                showRouteResult('error', `Size URLs test error: ${error.message}`);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showRouteResult(type, message) {
            const element = document.getElementById('routeResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Media File Route Test loaded');
            console.log('🎯 Testing /media/file/ route handler');
            console.log('📋 Smart solution: route serves files without URL modification');
            
            showResult('info', 'Ready to test media file route. Select an image and click "Test Upload & Route".');
        });
    </script>
</body>
</html>
