<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Media Route Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.danger {
            background-color: #dc3545;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .image-preview {
            margin: 20px 0;
            text-align: center;
        }
        .image-preview img {
            max-width: 300px;
            max-height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Media Route Fix Test</h1>
        <p>Test that the "stats is not defined" error is fixed in the Next.js media route.</p>
        
        <div class="success">
            <strong>✅ Fixed:</strong> Moved stats variable declaration outside try block<br>
            - Variable scope issue resolved<br>
            - ETag and Last-Modified headers should work now<br>
            - Internal server error should be gone
        </div>
    </div>

    <div class="container">
        <h3>🧪 Test Specific File</h3>
        <p>Test the exact file that was giving "stats is not defined" error:</p>
        
        <div class="test-url">
            <strong>Problematic URL:</strong> http://localhost:3001/media/avatars/Screenshot%202023-06-10%20122948-1752214072391-839c54e2-f74f-44fa-86d6-87b9b45758fe.png
        </div>
        
        <button class="btn success" onclick="testFixedUrl()">Test Fixed URL</button>
        <button class="btn" onclick="openInNewTab()">Open in New Tab</button>
        
        <div id="testResult"></div>
        <div id="imagePreview" class="image-preview"></div>
    </div>

    <div class="container">
        <h3>🔍 Debug Information</h3>
        <p>Check what's happening with the media route:</p>
        
        <button class="btn" onclick="debugMediaRoute()">Debug Media Route</button>
        <button class="btn danger" onclick="testNonExistentFile()">Test Non-Existent File</button>
        
        <div id="debugResult"></div>
    </div>

    <div class="container">
        <h3>📊 Error Comparison</h3>
        <div class="info">
            <strong>Before Fix:</strong><br>
            <code>Internal server error: Media route error: stats is not defined</code><br><br>
            
            <strong>After Fix:</strong><br>
            <code>Should return 200 OK with proper headers or 404 if file doesn't exist</code>
        </div>
    </div>

    <script>
        const testUrl = 'http://localhost:3001/media/avatars/Screenshot%202023-06-10%20122948-1752214072391-839c54e2-f74f-44fa-86d6-87b9b45758fe.png';

        async function testFixedUrl() {
            try {
                showTestResult('info', 'Testing fixed URL...');
                
                console.log('🔧 Testing fixed URL:', testUrl);
                
                const response = await fetch(testUrl, {
                    method: 'HEAD',
                });

                console.log('📦 Response:', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                });

                if (response.ok) {
                    const contentType = response.headers.get('content-type') || 'unknown';
                    const contentLength = response.headers.get('content-length') || 'unknown';
                    const etag = response.headers.get('etag') || 'none';
                    const lastModified = response.headers.get('last-modified') || 'none';
                    
                    showTestResult('success', 
                        `🎉 FIXED! URL now works!\n\n` +
                        `✅ Response Details:\n` +
                        `  - Status: ${response.status} ${response.statusText}\n` +
                        `  - Content-Type: ${contentType}\n` +
                        `  - Content-Length: ${contentLength} bytes\n` +
                        `  - ETag: ${etag}\n` +
                        `  - Last-Modified: ${lastModified}\n\n` +
                        `🎯 The "stats is not defined" error is fixed!`
                    );
                    
                    // Show image preview if it's an image
                    if (contentType.includes('image')) {
                        showImagePreview(testUrl, 'Fixed Image');
                    }
                } else if (response.status === 404) {
                    showTestResult('info', 
                        `📁 File not found (404)\n\n` +
                        `This is expected if the file doesn't exist.\n` +
                        `The important thing is that we're not getting the\n` +
                        `"stats is not defined" internal server error anymore!\n\n` +
                        `✅ The route handler is working correctly.`
                    );
                } else {
                    showTestResult('error', 
                        `❌ Unexpected response!\n\n` +
                        `Status: ${response.status} ${response.statusText}\n\n` +
                        `Expected either 200 OK or 404 Not Found.`
                    );
                }
            } catch (error) {
                console.error('❌ Test error:', error);
                showTestResult('error', `❌ Test error: ${error.message}`);
            }
        }

        function openInNewTab() {
            window.open(testUrl, '_blank');
            showTestResult('info', 'Opened URL in new tab. Check if it loads or shows a proper error (not internal server error).');
        }

        async function debugMediaRoute() {
            try {
                showDebugResult('info', 'Debugging media route...');
                
                // Test a simple path to see if the route handler is working
                const debugUrl = 'http://localhost:3001/media/test.txt';
                
                console.log('🔍 Testing debug URL:', debugUrl);
                
                const response = await fetch(debugUrl, {
                    method: 'HEAD',
                });

                console.log('🔍 Debug response:', {
                    status: response.status,
                    statusText: response.statusText
                });

                let resultText = '🔍 Media Route Debug Results:\n\n';
                resultText += `Test URL: ${debugUrl}\n`;
                resultText += `Status: ${response.status} ${response.statusText}\n\n`;
                
                if (response.status === 404) {
                    resultText += `✅ Route handler is working!\n`;
                    resultText += `  - 404 is expected for non-existent files\n`;
                    resultText += `  - No internal server error\n`;
                    resultText += `  - Route is properly handling requests\n\n`;
                    resultText += `🎯 The "stats is not defined" error is fixed!`;
                    showDebugResult('success', resultText);
                } else if (response.status === 500) {
                    resultText += `❌ Still getting internal server error\n`;
                    resultText += `  - The fix might not be complete\n`;
                    resultText += `  - Check server logs for details`;
                    showDebugResult('error', resultText);
                } else {
                    resultText += `🤔 Unexpected status: ${response.status}\n`;
                    resultText += `  - This might indicate another issue`;
                    showDebugResult('info', resultText);
                }
                
            } catch (error) {
                console.error('❌ Debug error:', error);
                showDebugResult('error', `❌ Debug error: ${error.message}`);
            }
        }

        async function testNonExistentFile() {
            try {
                showDebugResult('info', 'Testing non-existent file...');
                
                const nonExistentUrl = 'http://localhost:3001/media/avatars/non-existent-file.jpg';
                
                const response = await fetch(nonExistentUrl, {
                    method: 'HEAD',
                });

                console.log('🔍 Non-existent file test:', {
                    url: nonExistentUrl,
                    status: response.status,
                    statusText: response.statusText
                });

                if (response.status === 404) {
                    showDebugResult('success', 
                        `✅ Non-existent file test passed!\n\n` +
                        `URL: ${nonExistentUrl}\n` +
                        `Status: ${response.status} ${response.statusText}\n\n` +
                        `🎯 Route handler correctly returns 404 for missing files\n` +
                        `✅ No internal server error - the fix is working!`
                    );
                } else if (response.status === 500) {
                    showDebugResult('error', 
                        `❌ Still getting internal server error!\n\n` +
                        `URL: ${nonExistentUrl}\n` +
                        `Status: ${response.status} ${response.statusText}\n\n` +
                        `The fix might not be complete or there's another issue.`
                    );
                } else {
                    showDebugResult('info', 
                        `🤔 Unexpected response for non-existent file:\n\n` +
                        `URL: ${nonExistentUrl}\n` +
                        `Status: ${response.status} ${response.statusText}`
                    );
                }
                
            } catch (error) {
                console.error('❌ Non-existent file test error:', error);
                showDebugResult('error', `❌ Test error: ${error.message}`);
            }
        }

        function showImagePreview(imageUrl, description) {
            const previewDiv = document.getElementById('imagePreview');
            previewDiv.innerHTML = `
                <h4>🖼️ Image Preview: ${description}</h4>
                <img src="${imageUrl}" alt="${description}" 
                     onload="console.log('✅ Image loaded successfully')" 
                     onerror="console.error('❌ Image failed to load')">
                <p><a href="${imageUrl}" target="_blank">Open in new tab</a></p>
            `;
        }

        function showTestResult(type, message) {
            const element = document.getElementById('testResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showDebugResult(type, message) {
            const element = document.getElementById('debugResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Media Route Fix Test loaded');
            console.log('🎯 Testing that "stats is not defined" error is fixed');
            console.log('📋 The route should now work properly or return 404');
            
            showTestResult('info', 'Ready to test the media route fix. Click "Test Fixed URL" to verify the error is resolved.');
            
            // Auto-test after a short delay
            setTimeout(testFixedUrl, 1000);
        });
    </script>
</body>
</html>
