<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐍 Media Server Port 3002 Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .image-preview {
            margin: 20px 0;
            text-align: center;
        }
        .image-preview img {
            max-width: 300px;
            max-height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .file-list {
            margin: 20px 0;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐍 Media Server Port 3002 Test</h1>
        <p>Test the Python media server running on port 3002.</p>
        
        <div class="success">
            <strong>✅ Python Media Server:</strong> Running on http://localhost:3002<br>
            - Serves files from media/ directory and subfolders<br>
            - Includes CORS headers for cross-origin requests<br>
            - Should work immediately without Payload conflicts
        </div>
    </div>

    <div class="container">
        <h3>🔍 Server Status Check</h3>
        <button class="btn success" onclick="checkServerStatus()">Check Server Status</button>
        <button class="btn" onclick="getFileList()">Get File List</button>
        <div id="statusResult"></div>
    </div>

    <div class="container">
        <h3>📁 Test Specific File</h3>
        <p>Test your specific file that was giving 404 errors:</p>
        
        <div class="test-url">
            <strong>Test URL:</strong> http://localhost:3002/media/avatars/Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png
        </div>
        
        <button class="btn" onclick="testSpecificFile()">Test This File</button>
        <button class="btn" onclick="openInNewTab()">Open in New Tab</button>
        
        <div id="fileTestResult"></div>
        <div id="imagePreview" class="image-preview"></div>
    </div>

    <div class="container">
        <h3>📂 Available Files</h3>
        <div id="filesList" class="file-list"></div>
    </div>

    <script>
        const serverUrl = 'http://localhost:3002';
        const testFileUrl = `${serverUrl}/media/avatars/Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png`;

        async function checkServerStatus() {
            try {
                showStatusResult('info', 'Checking server status...');
                
                const response = await fetch(`${serverUrl}/health`, {
                    method: 'GET',
                });

                console.log('🔍 Health check response:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('🔍 Health check data:', data);
                    
                    showStatusResult('success', 
                        `✅ Server is running!\n\n` +
                        `Status: ${data.status}\n` +
                        `Message: ${data.message}\n` +
                        `Media Directory: ${data.mediaDir}\n` +
                        `Port: ${data.port}\n` +
                        `Timestamp: ${data.timestamp}\n\n` +
                        `🎯 Python media server is working correctly!`
                    );
                } else {
                    showStatusResult('error', 
                        `❌ Server health check failed!\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `The server might not be running properly.`
                    );
                }
            } catch (error) {
                console.error('❌ Health check error:', error);
                showStatusResult('error', 
                    `❌ Cannot connect to server!\n` +
                    `Error: ${error.message}\n\n` +
                    `Make sure the Python media server is running on port 3002.`
                );
            }
        }

        async function getFileList() {
            try {
                showStatusResult('info', 'Getting file list...');
                
                const response = await fetch(`${serverUrl}/debug/media`, {
                    method: 'GET',
                });

                console.log('📂 File list response:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('📂 File list data:', data);
                    
                    if (data.success && data.files) {
                        displayFilesList(data.files);
                        showStatusResult('success', 
                            `✅ Found ${data.totalFiles} files!\n\n` +
                            `Media Directory: ${data.mediaDir}\n` +
                            `Files shown: ${data.files.length}\n\n` +
                            `Check the "Available Files" section below.`
                        );
                    } else {
                        showStatusResult('error', `❌ Failed to get file list: ${data.message}`);
                    }
                } else {
                    showStatusResult('error', 
                        `❌ File list request failed!\n` +
                        `Status: ${response.status} ${response.statusText}`
                    );
                }
            } catch (error) {
                console.error('❌ File list error:', error);
                showStatusResult('error', `❌ File list error: ${error.message}`);
            }
        }

        async function testSpecificFile() {
            try {
                showFileTestResult('info', 'Testing specific file...');
                
                console.log('🔍 Testing file:', testFileUrl);
                
                const response = await fetch(testFileUrl, {
                    method: 'HEAD',
                });

                console.log('📦 File test response:', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                });

                if (response.ok) {
                    const contentType = response.headers.get('content-type') || 'unknown';
                    const contentLength = response.headers.get('content-length') || 'unknown';
                    
                    showFileTestResult('success', 
                        `🎉 FILE IS ACCESSIBLE!\n\n` +
                        `✅ File Details:\n` +
                        `  - URL: ${testFileUrl}\n` +
                        `  - Status: ${response.status} ${response.statusText}\n` +
                        `  - Content-Type: ${contentType}\n` +
                        `  - Content-Length: ${contentLength} bytes\n\n` +
                        `🎯 The 404 error is fixed! File is now accessible via port 3002.`
                    );
                    
                    // Show image preview
                    if (contentType.includes('image')) {
                        showImagePreview(testFileUrl, 'Your Avatar');
                    }
                } else {
                    showFileTestResult('error', 
                        `❌ File still not accessible!\n\n` +
                        `URL: ${testFileUrl}\n` +
                        `Status: ${response.status} ${response.statusText}\n\n` +
                        `There might be an issue with the file path or server configuration.`
                    );
                }
            } catch (error) {
                console.error('❌ File test error:', error);
                showFileTestResult('error', `❌ File test error: ${error.message}`);
            }
        }

        function openInNewTab() {
            window.open(testFileUrl, '_blank');
            showFileTestResult('info', 'Opened file in new tab. Check if it loads or shows an error.');
        }

        function displayFilesList(files) {
            const filesList = document.getElementById('filesList');
            
            if (files.length === 0) {
                filesList.innerHTML = '<p>No files found.</p>';
                return;
            }

            let html = `<h4>📁 ${files.length} Files Found:</h4>`;
            files.forEach(file => {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                const isTargetFile = file.name.includes('Screenshot 2023-06-10 123201-1752211977516');
                
                html += `
                    <div class="file-item" ${isTargetFile ? 'style="border: 2px solid #28a745; background: #d4edda;"' : ''}>
                        <div>
                            <strong>${file.name}</strong> ${isTargetFile ? '← YOUR FILE' : ''}<br>
                            <small>Path: ${file.path} | Size: ${fileSize} MB</small><br>
                            <small>URL: ${file.url}</small>
                        </div>
                        <div>
                            <button class="btn" onclick="testFileUrl('${serverUrl}${file.url}', '${file.name}')">Test</button>
                        </div>
                    </div>
                `;
            });
            
            filesList.innerHTML = html;
        }

        async function testFileUrl(url, filename) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                if (response.ok) {
                    console.log(`✅ ${filename} is accessible`);
                    if (response.headers.get('content-type')?.includes('image')) {
                        showImagePreview(url, filename);
                    }
                } else {
                    console.error(`❌ ${filename} returned ${response.status}`);
                }
            } catch (error) {
                console.error(`❌ ${filename} test error:`, error);
            }
        }

        function showImagePreview(imageUrl, filename) {
            const previewDiv = document.getElementById('imagePreview');
            previewDiv.innerHTML = `
                <h4>🖼️ Image Preview: ${filename}</h4>
                <img src="${imageUrl}" alt="${filename}" 
                     onload="console.log('✅ Image loaded successfully')" 
                     onerror="console.error('❌ Image failed to load')">
                <p><a href="${imageUrl}" target="_blank">Open in new tab</a></p>
            `;
        }

        function showStatusResult(type, message) {
            const element = document.getElementById('statusResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showFileTestResult(type, message) {
            const element = document.getElementById('fileTestResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🐍 Media Server Port 3002 Test loaded');
            console.log('🎯 Testing Python media server on port 3002');
            console.log('📋 This should bypass all Payload CMS issues');
            
            showStatusResult('info', 'Ready to test Python media server. Click "Check Server Status" to start.');
            
            // Auto-check server status
            setTimeout(checkServerStatus, 1000);
        });
    </script>
</body>
</html>
