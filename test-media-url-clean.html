<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧹 Media URL Clean Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-box {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid;
        }
        .comparison-box.before {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .comparison-box.after {
            border-color: #28a745;
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Media URL Clean Test</h1>
        <p>Test that uploaded media URLs are stored without `/api/` prefix.</p>
        
        <div class="success">
            <strong>✅ Fixed:</strong> Media URLs now stored with clean `/media/` prefix<br>
            - Database updated to store clean URLs<br>
            - Response returns clean URLs<br>
            - Size URLs also cleaned<br>
            - No more `/api/media/file/` prefix
        </div>
    </div>

    <div class="container">
        <h3>🔍 Before vs After Comparison</h3>
        <div class="comparison">
            <div class="comparison-box before">
                <h4>❌ Before Fix</h4>
                <p><strong>Stored URL:</strong><br>
                <code>/api/media/file/filename.jpg</code></p>
                <p><strong>Size URLs:</strong><br>
                <code>/api/media/file/filename-80x80.webp</code></p>
                <p><strong>Problem:</strong><br>
                Contains unwanted `/api/` prefix</p>
            </div>
            <div class="comparison-box after">
                <h4>✅ After Fix</h4>
                <p><strong>Stored URL:</strong><br>
                <code>/media/avatars/filename.jpg</code></p>
                <p><strong>Size URLs:</strong><br>
                <code>/media/avatars/filename-80x80.webp</code></p>
                <p><strong>Result:</strong><br>
                Clean URLs without `/api/` prefix</p>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>📁 Test File Upload</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test clean URL storage</p>
            <p style="color: #666; font-size: 14px;">Should store URLs with `/media/` prefix only</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testCleanUpload()" id="uploadBtn" disabled>Test Clean URL Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🧪 Test Different Upload Types</h3>
        <p>Test various upload types to ensure all store clean URLs:</p>
        
        <button class="btn" onclick="testUploadType('avatar')">Test Avatar Upload</button>
        <button class="btn" onclick="testUploadType('course_thumbnail')">Test Course Thumbnail</button>
        <button class="btn" onclick="testUploadType('institute_logo')">Test Institute Logo</button>
        
        <div id="typeTestResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testCleanUpload() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, 'avatar', 'Clean URL Test');
        }

        async function testUploadType(uploadType) {
            if (!selectedFile) {
                showTypeTestResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, uploadType, `${uploadType} Clean URL Test`, true);
        }

        async function testUploadWithFile(file, uploadType, testName, useTypeResult = false) {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                const showResultFunc = useTypeResult ? showTypeTestResult : showResult;
                showResultFunc('info', `Testing ${testName}...`);
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('uploadType', uploadType);

                console.log(`🚀 Testing ${testName}:`, {
                    fileName: file.name,
                    uploadType: uploadType
                });

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeCleanUrls(data, testName, showResultFunc);
                } else {
                    showResultFunc('error', `${testName} failed: ${data.message}`);
                }

            } catch (error) {
                console.error(`❌ ${testName} error:`, error);
                const showResultFunc = useTypeResult ? showTypeTestResult : showResult;
                showResultFunc('error', `${testName} error: ${error.message}`);
            }
        }

        function analyzeCleanUrls(data, testName, showResultFunc) {
            const media = data.media;
            
            if (!media) {
                showResultFunc('error', `No media object in ${testName} response`);
                return;
            }

            let resultText = `🧹 ${testName} URL Analysis:\n\n`;
            
            // Analyze main URL
            const mainUrl = media.url;
            const mainUrlClean = !mainUrl.includes('/api/');
            const mainUrlStartsWithMedia = mainUrl.startsWith('/media/');
            
            resultText += `📋 Main URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - No /api/ prefix: ${mainUrlClean ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Starts with /media/: ${mainUrlStartsWithMedia ? 'PASS ✅' : 'FAIL ❌'}\n\n`;
            
            // Analyze size URLs
            let allSizesClean = true;
            let sizeAnalysis = '';
            
            if (media.sizes) {
                sizeAnalysis += `📐 Size URLs Analysis:\n`;
                
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeUrlClean = !sizeData.url.includes('/api/');
                        const sizeUrlStartsWithMedia = sizeData.url.startsWith('/media/');
                        const sizeUrlValid = sizeUrlClean && sizeUrlStartsWithMedia;
                        
                        if (!sizeUrlValid) allSizesClean = false;
                        
                        sizeAnalysis += `  - ${sizeName}: ${sizeData.url}\n`;
                        sizeAnalysis += `    No /api/: ${sizeUrlClean ? '✅' : '❌'} | Starts /media/: ${sizeUrlStartsWithMedia ? '✅' : '❌'}\n`;
                    }
                });
                sizeAnalysis += `\n`;
            } else {
                sizeAnalysis += `📐 No size URLs generated\n\n`;
            }
            
            resultText += sizeAnalysis;
            
            // Overall assessment
            const allUrlsClean = mainUrlClean && mainUrlStartsWithMedia && allSizesClean;
            
            resultText += `🎯 Overall Assessment:\n`;
            resultText += `  - Main URL clean: ${mainUrlClean && mainUrlStartsWithMedia ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - All size URLs clean: ${allSizesClean ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Overall result: ${allUrlsClean ? 'PERFECT ✅' : 'NEEDS WORK ❌'}\n\n`;
            
            if (allUrlsClean) {
                resultText += `🎉 SUCCESS! All URLs are clean without /api/ prefix!\n`;
                resultText += `✅ Media URLs are now stored correctly.`;
                showResultFunc('success', resultText);
            } else {
                resultText += `⚠️ Some URLs still contain /api/ prefix.\n`;
                resultText += `❌ The fix may need additional work.`;
                showResultFunc('error', resultText);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showTypeTestResult(type, message) {
            const element = document.getElementById('typeTestResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🧹 Media URL Clean Test loaded');
            console.log('🎯 Testing that media URLs are stored without /api/ prefix');
            console.log('📋 Should store URLs as /media/folder/filename.jpg');
            
            showResult('info', 'Ready to test clean URL storage. Select an image and click "Test Clean URL Upload".');
        });
    </script>
</body>
</html>
