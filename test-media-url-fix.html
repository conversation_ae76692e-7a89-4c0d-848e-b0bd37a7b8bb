<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Media URL Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-box {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid;
        }
        .comparison-box.before {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .comparison-box.after {
            border-color: #28a745;
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Media URL Fix Test</h1>
        <p>Test that media URLs are automatically fixed in the database to use `/media/` instead of `/api/media/file/`.</p>
        
        <div class="success">
            <strong>✅ Fixed:</strong> Added beforeChange hook to Media collection<br>
            - Automatically fixes URLs when media records are created/updated<br>
            - Converts `/api/media/file/` to `/media/`<br>
            - Fixes main URL, thumbnailURL, and all size URLs<br>
            - Now `localhost:3001/media/avatars/filename.jpg` should work
        </div>
    </div>

    <div class="container">
        <h3>🔍 Before vs After Comparison</h3>
        <div class="comparison">
            <div class="comparison-box before">
                <h4>❌ Before Fix</h4>
                <p><strong>Database URL:</strong><br>
                <code>/api/media/file/filename.jpg</code></p>
                <p><strong>Access URL:</strong><br>
                <code>localhost:3001/api/media/file/filename.jpg</code></p>
                <p><strong>Problem:</strong><br>
                Can't access via `/media/avatars/`</p>
            </div>
            <div class="comparison-box after">
                <h4>✅ After Fix</h4>
                <p><strong>Database URL:</strong><br>
                <code>/media/avatars/filename.jpg</code></p>
                <p><strong>Access URL:</strong><br>
                <code>localhost:3001/media/avatars/filename.jpg</code></p>
                <p><strong>Result:</strong><br>
                Direct access works perfectly!</p>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>📁 Test New Upload</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test the URL fix</p>
            <p style="color: #666; font-size: 14px;">New uploads should have clean URLs stored in database</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testNewUpload()" id="uploadBtn" disabled>Test New Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🧪 Test URL Access</h3>
        <p>Test if you can access media files via the clean URLs:</p>
        
        <button class="btn" onclick="testMediaAccess()">Test Media Access</button>
        <button class="btn" onclick="testAvatarAccess()">Test Avatar Access</button>
        
        <div id="accessResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;
        let lastUploadedUrl = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testNewUpload() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showResult('info', 'Testing new upload with URL fix...');
                
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'avatar');

                console.log('🚀 Testing new upload with URL fix');

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeUploadUrls(data);
                } else {
                    showResult('error', `Upload failed: ${data.message}`);
                }

            } catch (error) {
                console.error('❌ Upload error:', error);
                showResult('error', `Upload error: ${error.message}`);
            }
        }

        function analyzeUploadUrls(data) {
            const media = data.media;
            
            if (!media) {
                showResult('error', 'No media object in response');
                return;
            }

            lastUploadedUrl = media.url;

            let resultText = `🎉 Upload URL Analysis:\n\n`;
            
            // Analyze main URL
            const mainUrl = media.url;
            const urlFixed = !mainUrl.includes('/api/media/file/');
            const urlStartsWithMedia = mainUrl.startsWith('/media/');
            
            resultText += `📋 Main URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - No /api/media/file/: ${urlFixed ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Starts with /media/: ${urlStartsWithMedia ? 'PASS ✅' : 'FAIL ❌'}\n\n`;
            
            // Analyze sizes if available
            let allSizesFixed = true;
            if (media.sizes && Object.keys(media.sizes).length > 0) {
                resultText += `📐 Size URLs Analysis:\n`;
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeUrlFixed = !sizeData.url.includes('/api/media/file/');
                        const sizeUrlStartsWithMedia = sizeData.url.startsWith('/media/');
                        
                        if (!sizeUrlFixed || !sizeUrlStartsWithMedia) allSizesFixed = false;
                        
                        resultText += `  - ${sizeName}: ${sizeData.url}\n`;
                        resultText += `    Fixed: ${sizeUrlFixed ? '✅' : '❌'} | Starts /media/: ${sizeUrlStartsWithMedia ? '✅' : '❌'}\n`;
                    }
                });
                resultText += `\n`;
            }
            
            // Overall assessment
            const allUrlsFixed = urlFixed && urlStartsWithMedia && allSizesFixed;
            
            resultText += `🎯 Overall Assessment:\n`;
            resultText += `  - Main URL fixed: ${urlFixed && urlStartsWithMedia ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - All size URLs fixed: ${allSizesFixed ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Database hook working: ${allUrlsFixed ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            if (allUrlsFixed) {
                resultText += `🎉 PERFECT! URL fix is working!\n`;
                resultText += `✅ Database now stores clean URLs.\n`;
                resultText += `🌐 You can now access files via localhost:3001/media/avatars/`;
                showResult('success', resultText);
            } else {
                resultText += `⚠️ URL fix may not be working completely.\n`;
                resultText += `❌ Check the hook implementation.`;
                showResult('error', resultText);
            }
        }

        async function testMediaAccess() {
            if (!lastUploadedUrl) {
                showAccessResult('error', 'Please upload a file first to test access');
                return;
            }

            try {
                showAccessResult('info', 'Testing media file access...');
                
                // Test the clean URL
                const cleanUrl = `http://localhost:3001${lastUploadedUrl}`;
                
                console.log('🔍 Testing access to:', cleanUrl);
                
                const response = await fetch(cleanUrl, {
                    method: 'HEAD',
                });

                console.log('📦 Access response:', response.status);

                if (response.ok) {
                    const contentType = response.headers.get('content-type') || 'unknown';
                    
                    showAccessResult('success', 
                        `✅ Media access works!\n\n` +
                        `URL: ${cleanUrl}\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `Content-Type: ${contentType}\n\n` +
                        `🎉 You can now access media files directly!`
                    );
                } else {
                    showAccessResult('error', 
                        `❌ Media access failed!\n\n` +
                        `URL: ${cleanUrl}\n` +
                        `Status: ${response.status} ${response.statusText}\n\n` +
                        `The URL fix may need more work.`
                    );
                }

            } catch (error) {
                console.error('❌ Access test error:', error);
                showAccessResult('error', `Access test error: ${error.message}`);
            }
        }

        async function testAvatarAccess() {
            try {
                showAccessResult('info', 'Testing avatar folder access...');
                
                // Test a known avatar URL pattern
                const testUrl = 'http://localhost:3001/media/avatars/';
                
                const response = await fetch(testUrl, {
                    method: 'HEAD',
                });

                console.log('📦 Avatar folder response:', response.status);

                if (response.ok || response.status === 403) {
                    showAccessResult('success', 
                        `✅ Avatar folder accessible!\n\n` +
                        `URL: ${testUrl}\n` +
                        `Status: ${response.status} ${response.statusText}\n\n` +
                        `🎯 The /media/avatars/ path is working!`
                    );
                } else {
                    showAccessResult('error', 
                        `❌ Avatar folder not accessible!\n\n` +
                        `URL: ${testUrl}\n` +
                        `Status: ${response.status} ${response.statusText}`
                    );
                }

            } catch (error) {
                console.error('❌ Avatar access test error:', error);
                showAccessResult('error', `Avatar access test error: ${error.message}`);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showAccessResult(type, message) {
            const element = document.getElementById('accessResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Media URL Fix Test loaded');
            console.log('🎯 Testing that media URLs are fixed in database');
            console.log('📋 Should store URLs as /media/avatars/filename.jpg');
            
            showResult('info', 'Ready to test media URL fix. Select an image and click "Test New Upload".');
        });
    </script>
</body>
</html>
