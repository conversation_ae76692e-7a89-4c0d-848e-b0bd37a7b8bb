<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ Next.js Media Route Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.warning {
            background-color: #ffc107;
            color: #212529;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .image-preview {
            margin: 20px 0;
            text-align: center;
        }
        .image-preview img {
            max-width: 300px;
            max-height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-box {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid;
        }
        .comparison-box.port3001 {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .comparison-box.port3002 {
            border-color: #ffc107;
            background-color: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ Next.js Media Route Test</h1>
        <p>Test the Next.js media route handler on port 3001 vs Python server on port 3002.</p>
        
        <div class="success">
            <strong>✅ Next.js Route Handler:</strong> /media/[...path]/route.ts<br>
            - Serves files directly from Next.js app on port 3001<br>
            - No need for separate Python server<br>
            - Integrated with your main application
        </div>
    </div>

    <div class="container">
        <h3>🔍 Port Comparison</h3>
        <div class="comparison">
            <div class="comparison-box port3001">
                <h4>✅ Port 3001 (Next.js Route)</h4>
                <div class="test-url">
                    http://localhost:3001/media/avatars/Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png
                </div>
                <p><small>Uses Next.js route handler - integrated solution</small></p>
            </div>
            <div class="comparison-box port3002">
                <h4>⚠️ Port 3002 (Python Server)</h4>
                <div class="test-url">
                    http://localhost:3002/media/avatars/Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png
                </div>
                <p><small>Separate Python server - temporary solution</small></p>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>🧪 Test Both Ports</h3>
        <p>Test the same file on both ports to verify the Next.js route handler is working:</p>
        
        <button class="btn success" onclick="testPort3001()">Test Port 3001 (Next.js)</button>
        <button class="btn warning" onclick="testPort3002()">Test Port 3002 (Python)</button>
        <button class="btn" onclick="testBothPorts()">Test Both Ports</button>
        <button class="btn" onclick="openBothInTabs()">Open Both in New Tabs</button>
        
        <div id="testResult"></div>
        <div id="imagePreview" class="image-preview"></div>
    </div>

    <div class="container">
        <h3>📱 Frontend Integration</h3>
        <p>The frontend getFileUrl function now points to port 3001:</p>
        
        <div class="info">
            <strong>Updated Frontend Code:</strong><br>
            <code>const backendUrl = 'http://localhost:3001'</code><br>
            <code>return `${backendUrl}${path}` // Uses Next.js route handler</code>
        </div>
        
        <button class="btn success" onclick="testFrontendIntegration()">Test Frontend Integration</button>
        <div id="frontendResult"></div>
    </div>

    <script>
        const testFileName = 'Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png';
        const port3001Url = `http://localhost:3001/media/avatars/${testFileName}`;
        const port3002Url = `http://localhost:3002/media/avatars/${testFileName}`;

        async function testPort3001() {
            await testUrl(port3001Url, 'Port 3001 (Next.js Route)', 'success');
        }

        async function testPort3002() {
            await testUrl(port3002Url, 'Port 3002 (Python Server)', 'warning');
        }

        async function testBothPorts() {
            showTestResult('info', 'Testing both ports...\n\n');
            
            const result3001 = await testUrlSilent(port3001Url);
            const result3002 = await testUrlSilent(port3002Url);
            
            let resultText = '📊 Port Comparison Results:\n\n';
            
            resultText += `✅ Port 3001 (Next.js Route):\n`;
            resultText += `   URL: ${port3001Url}\n`;
            resultText += `   Status: ${result3001.status} ${result3001.statusText}\n`;
            resultText += `   Working: ${result3001.success ? 'YES' : 'NO'}\n\n`;
            
            resultText += `⚠️ Port 3002 (Python Server):\n`;
            resultText += `   URL: ${port3002Url}\n`;
            resultText += `   Status: ${result3002.status} ${result3002.statusText}\n`;
            resultText += `   Working: ${result3002.success ? 'YES' : 'NO'}\n\n`;
            
            if (result3001.success && result3002.success) {
                resultText += `🎉 BOTH PORTS WORKING!\n`;
                resultText += `   - Port 3001: Next.js route handler ✅\n`;
                resultText += `   - Port 3002: Python server ✅\n\n`;
                resultText += `✅ You can now use port 3001 and stop the Python server if desired.`;
                showTestResult('success', resultText);
                
                // Show image preview from port 3001
                showImagePreview(port3001Url, 'Port 3001 (Next.js)');
            } else if (result3001.success && !result3002.success) {
                resultText += `✅ Next.js route handler working, Python server not needed!\n`;
                resultText += `🎯 Port 3001 is the preferred solution.`;
                showTestResult('success', resultText);
                
                showImagePreview(port3001Url, 'Port 3001 (Next.js)');
            } else if (!result3001.success && result3002.success) {
                resultText += `⚠️ Only Python server working.\n`;
                resultText += `❌ Next.js route handler needs debugging.`;
                showTestResult('warning', resultText);
                
                showImagePreview(port3002Url, 'Port 3002 (Python)');
            } else {
                resultText += `❌ Neither port working - there might be another issue.`;
                showTestResult('error', resultText);
            }
        }

        function openBothInTabs() {
            window.open(port3001Url, '_blank');
            window.open(port3002Url, '_blank');
            showTestResult('info', 'Opened both URLs in new tabs. Check which one loads correctly.');
        }

        async function testUrl(url, description, expectedType) {
            try {
                showTestResult('info', `Testing ${description}...\n\nURL: ${url}`);
                
                const response = await fetch(url, {
                    method: 'HEAD',
                });

                console.log(`🔍 ${description} test:`, {
                    url,
                    status: response.status,
                    statusText: response.statusText
                });

                if (response.ok) {
                    const contentType = response.headers.get('content-type') || 'unknown';
                    const contentLength = response.headers.get('content-length') || 'unknown';
                    
                    showTestResult('success', 
                        `✅ ${description} works!\n\n` +
                        `URL: ${url}\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `Content-Type: ${contentType}\n` +
                        `Content-Length: ${contentLength} bytes\n\n` +
                        `🎉 Media file is accessible!`
                    );
                    
                    // Show image preview
                    if (contentType.includes('image')) {
                        showImagePreview(url, description);
                    }
                } else {
                    showTestResult('error', 
                        `❌ ${description} failed!\n\n` +
                        `URL: ${url}\n` +
                        `Status: ${response.status} ${response.statusText}\n\n` +
                        `The route handler might not be working properly.`
                    );
                }
            } catch (error) {
                console.error(`❌ ${description} test error:`, error);
                showTestResult('error', `❌ ${description} test error: ${error.message}`);
            }
        }

        async function testUrlSilent(url) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                return {
                    success: response.ok,
                    status: response.status,
                    statusText: response.statusText,
                    contentType: response.headers.get('content-type')
                };
            } catch (error) {
                return {
                    success: false,
                    status: 'Error',
                    statusText: error.message,
                    contentType: null
                };
            }
        }

        function testFrontendIntegration() {
            // Simulate the frontend getFileUrl function
            const testPath = '/media/avatars/Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png';
            
            // Current frontend behavior
            const backendUrl = 'http://localhost:3001'; // process.env.NEXT_PUBLIC_API_URL
            const generatedUrl = `${backendUrl}${testPath}`;
            
            let resultText = '📱 Frontend Integration Test:\n\n';
            resultText += `Input path: ${testPath}\n\n`;
            resultText += `Frontend getFileUrl() behavior:\n`;
            resultText += `  - Backend URL: ${backendUrl}\n`;
            resultText += `  - Generated URL: ${generatedUrl}\n`;
            resultText += `  - Uses: Next.js route handler on port 3001\n\n`;
            resultText += `✅ This should now work without any /api prefix issues!\n`;
            resultText += `🎯 The URL goes directly to the Next.js media route handler.`;
            
            showFrontendResult('success', resultText);
        }

        function showImagePreview(imageUrl, description) {
            const previewDiv = document.getElementById('imagePreview');
            previewDiv.innerHTML = `
                <h4>🖼️ Image Preview: ${description}</h4>
                <img src="${imageUrl}" alt="${description}" 
                     onload="console.log('✅ Image loaded successfully from: ${imageUrl}')" 
                     onerror="console.error('❌ Image failed to load from: ${imageUrl}')">
                <p><a href="${imageUrl}" target="_blank">Open in new tab</a></p>
            `;
        }

        function showTestResult(type, message) {
            const element = document.getElementById('testResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showFrontendResult(type, message) {
            const element = document.getElementById('frontendResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('⚡ Next.js Media Route Test loaded');
            console.log('🎯 Testing Next.js route handler vs Python server');
            console.log('📋 Port 3001 should now work with the new route handler');
            
            showTestResult('info', 'Ready to test Next.js media route handler. Click "Test Both Ports" to compare.');
            
            // Auto-test both ports after a short delay
            setTimeout(testBothPorts, 1000);
        });
    </script>
</body>
</html>
