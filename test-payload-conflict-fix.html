<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Payload Conflict Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Payload Conflict Fix Test</h1>
        <p>Test that the Payload CMS upload conflict is resolved.</p>
        
        <div class="info">
            <strong>🔍 Issue Analysis:</strong><br>
            ✅ File uploads to correct folder: media/avatars/<br>
            ❌ But Payload CMS conflicts when creating media record<br>
            ✅ Fixed: Create media record without triggering Payload's upload handling
        </div>
    </div>

    <div class="container">
        <h3>📁 Avatar Upload Test</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image file to test avatar upload</p>
            <p style="color: #666; font-size: 14px;">Test the complete upload flow with Payload conflict fix</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn" onclick="uploadAvatar()" id="uploadBtn" disabled>Test Avatar Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🔍 Expected Flow</h3>
        <div class="info">
            <strong>Complete Upload Process:</strong><br>
            1. ✅ Parse form data (file, uploadType: 'avatar')<br>
            2. ✅ Determine folder: 'avatars'<br>
            3. ✅ Save file to: media/avatars/filename.jpg<br>
            4. ✅ Create media record in database (without file conflict)<br>
            5. ✅ Update user.avatar field<br>
            6. ✅ Return success response
        </div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function uploadAvatar() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('uploadType', 'avatar');
            formData.append('updateUserField', 'avatar');

            try {
                showResult('info', 'Testing complete avatar upload flow...');
                
                console.log('🚀 Testing complete avatar upload flow...');
                console.log('📋 Upload details:', {
                    fileName: selectedFile.name,
                    fileSize: selectedFile.size,
                    fileType: selectedFile.type,
                    uploadType: 'avatar',
                    expectedFolder: 'avatars',
                    expectedPath: '/media/avatars/' + selectedFile.name
                });

                const startTime = Date.now();
                
                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                const endTime = Date.now();
                const uploadTime = endTime - startTime;

                console.log('📦 Response status:', response.status);
                console.log('⏱️ Upload time:', uploadTime + 'ms');

                const data = await response.json();
                console.log('📦 Upload response:', data);

                if (data.success) {
                    const actualUrl = data.media.url;
                    const isCorrectFolder = actualUrl.includes('/avatars/');
                    
                    showResult('success', 
                        `🎉 COMPLETE SUCCESS! Avatar upload working perfectly!\n\n` +
                        `✅ File Processing:\n` +
                        `  - File saved to correct folder: ${isCorrectFolder ? 'YES' : 'NO'}\n` +
                        `  - Actual URL: ${actualUrl}\n` +
                        `  - Upload time: ${uploadTime}ms\n\n` +
                        `✅ Database Records:\n` +
                        `  - Media ID: ${data.media.id}\n` +
                        `  - Media Type: ${data.media.mediaType}\n` +
                        `  - Alt Text: ${data.media.alt}\n\n` +
                        `✅ User Update:\n` +
                        `  - User avatar field updated: ${data.user ? 'YES' : 'NO'}\n` +
                        `  - User ID: ${data.user?.id || 'N/A'}\n\n` +
                        `✅ Response Data:\n` +
                        `  - Upload Type: ${data.uploadType}\n` +
                        `  - Message: ${data.message}\n\n` +
                        `🎯 All systems working correctly!`
                    );
                    
                    console.log('🎉 COMPLETE SUCCESS!');
                    console.log('✅ File saved to correct folder:', isCorrectFolder);
                    console.log('✅ Media record created:', data.media.id);
                    console.log('✅ User avatar updated:', !!data.user);
                    
                    if (data.user) {
                        console.log('👤 Updated user:', {
                            id: data.user.id,
                            firstName: data.user.firstName,
                            lastName: data.user.lastName,
                            avatar: data.user.avatar
                        });
                    }
                } else {
                    showResult('error', `❌ Upload failed: ${data.message}`);
                    
                    console.error('❌ UPLOAD FAILED');
                    console.error('Error message:', data.message);
                    
                    // Analyze the error
                    if (data.message.includes('ENOENT')) {
                        console.error('🔍 ENOENT Error Analysis:');
                        
                        const pathMatch = data.message.match(/open '([^']+)'/);
                        if (pathMatch) {
                            const attemptedPath = pathMatch[1];
                            console.error('Attempted path:', attemptedPath);
                            
                            const hasAvatarsFolder = attemptedPath.includes('\\avatars\\') || attemptedPath.includes('/avatars/');
                            console.error('Has avatars folder:', hasAvatarsFolder);
                            
                            if (hasAvatarsFolder) {
                                console.error('✅ Folder path is correct - this might be a permission or timing issue');
                            } else {
                                console.error('❌ Folder path is wrong - the fix did not work');
                            }
                        }
                    } else if (data.message.includes('Payload')) {
                        console.error('🔍 Payload Conflict Analysis:');
                        console.error('This might be a Payload CMS upload handling conflict');
                    }
                }
            } catch (error) {
                console.error('❌ Upload error:', error);
                showResult('error', `❌ Upload error: ${error.message}`);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Payload Conflict Fix Test loaded');
            console.log('🎯 Testing endpoint: http://localhost:3001/upload');
            console.log('📋 Expected complete flow:');
            console.log('  1. File saved to media/avatars/');
            console.log('  2. Media record created in database');
            console.log('  3. User avatar field updated');
            console.log('  4. Success response returned');
            
            showResult('info', 'Ready to test the complete avatar upload flow. Select an image and click "Test Avatar Upload".');
        });
    </script>
</body>
</html>
