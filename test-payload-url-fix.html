<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Payload URL Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-box {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid;
        }
        .comparison-box.before {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .comparison-box.after {
            border-color: #28a745;
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Payload URL Fix Test</h1>
        <p>Test that Payload now generates correct URLs without /api/ prefix after serverURL configuration.</p>
        
        <div class="success">
            <strong>✅ Fixed:</strong> Added serverURL to Payload config<br>
            - serverURL: 'http://localhost:3001'<br>
            - Should generate URLs without /api/ prefix<br>
            - URLs should be: /media/file/filename.jpg<br>
            - Not: /api/media/file/filename.jpg
        </div>
    </div>

    <div class="container">
        <h3>🔍 Before vs After Comparison</h3>
        <div class="comparison">
            <div class="comparison-box before">
                <h4>❌ Before Fix</h4>
                <p><strong>Payload generates:</strong><br>
                <code>/api/media/file/filename.jpg</code></p>
                <p><strong>Problem:</strong><br>
                Includes unwanted /api/ prefix</p>
                <p><strong>Cause:</strong><br>
                No serverURL configured</p>
            </div>
            <div class="comparison-box after">
                <h4>✅ After Fix</h4>
                <p><strong>Payload generates:</strong><br>
                <code>/media/file/filename.jpg</code></p>
                <p><strong>Result:</strong><br>
                Clean URLs without /api/ prefix</p>
                <p><strong>Solution:</strong><br>
                serverURL configured properly</p>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>📁 Test Upload with Fixed URLs</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test Payload URL generation</p>
            <p style="color: #666; font-size: 14px;">Should generate URLs without /api/ prefix</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testPayloadUrlFix()" id="uploadBtn" disabled>Test Payload URL Fix</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🧪 Test URL Generation</h3>
        <p>Test various upload types to ensure all generate correct URLs:</p>
        
        <button class="btn" onclick="testUrlGeneration('avatar')">Test Avatar URLs</button>
        <button class="btn" onclick="testUrlGeneration('course_thumbnail')">Test Course URLs</button>
        <button class="btn" onclick="testUrlGeneration('document')">Test Document URLs</button>
        
        <div id="urlResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testPayloadUrlFix() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, 'avatar', 'Payload URL Fix Test');
        }

        async function testUrlGeneration(uploadType) {
            if (!selectedFile) {
                showUrlResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, uploadType, `${uploadType} URL Generation Test`, true);
        }

        async function testUploadWithFile(file, uploadType, testName, useUrlResult = false) {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                const showResultFunc = useUrlResult ? showUrlResult : showResult;
                showResultFunc('info', `Testing ${testName}...`);
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('uploadType', uploadType);

                console.log(`🚀 Testing ${testName}:`, {
                    fileName: file.name,
                    uploadType: uploadType
                });

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzePayloadUrls(data, testName, showResultFunc);
                } else {
                    showResultFunc('error', `${testName} failed: ${data.message}`);
                }

            } catch (error) {
                console.error(`❌ ${testName} error:`, error);
                const showResultFunc = useUrlResult ? showUrlResult : showResult;
                showResultFunc('error', `${testName} error: ${error.message}`);
            }
        }

        function analyzePayloadUrls(data, testName, showResultFunc) {
            const media = data.media;
            
            if (!media) {
                showResultFunc('error', `No media object in ${testName} response`);
                return;
            }

            let resultText = `🔧 ${testName} URL Analysis:\n\n`;
            
            // Analyze main URL
            const mainUrl = media.url;
            const noApiPrefix = !mainUrl.includes('/api/');
            const hasCorrectFormat = mainUrl.startsWith('/media/') || mainUrl.startsWith('/media/file/');
            
            resultText += `📋 Main URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - No /api/ prefix: ${noApiPrefix ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Correct format: ${hasCorrectFormat ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Payload fix working: ${noApiPrefix && hasCorrectFormat ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            // Analyze thumbnail URL if present
            let thumbnailFixed = true;
            if (media.thumbnailURL) {
                const thumbNoApi = !media.thumbnailURL.includes('/api/');
                const thumbCorrectFormat = media.thumbnailURL.startsWith('/media/');
                thumbnailFixed = thumbNoApi && thumbCorrectFormat;
                
                resultText += `🖼️ Thumbnail URL Analysis:\n`;
                resultText += `  - URL: ${media.thumbnailURL}\n`;
                resultText += `  - No /api/ prefix: ${thumbNoApi ? 'PASS ✅' : 'FAIL ❌'}\n`;
                resultText += `  - Correct format: ${thumbCorrectFormat ? 'PASS ✅' : 'FAIL ❌'}\n\n`;
            }
            
            // Analyze sizes
            let allSizesFixed = true;
            let sizesAnalysis = '';
            
            if (media.sizes && Object.keys(media.sizes).length > 0) {
                sizesAnalysis += `📐 Size URLs Analysis:\n`;
                
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeNoApi = !sizeData.url.includes('/api/');
                        const sizeCorrectFormat = sizeData.url.startsWith('/media/');
                        const sizeFixed = sizeNoApi && sizeCorrectFormat;
                        
                        if (!sizeFixed) allSizesFixed = false;
                        
                        sizesAnalysis += `  - ${sizeName}: ${sizeData.url}\n`;
                        sizesAnalysis += `    No /api/: ${sizeNoApi ? '✅' : '❌'} | Correct format: ${sizeCorrectFormat ? '✅' : '❌'}\n`;
                    }
                });
                sizesAnalysis += `\n`;
            } else {
                sizesAnalysis += `📐 No size URLs generated\n\n`;
            }
            
            resultText += sizesAnalysis;
            
            // Overall assessment
            const payloadFixed = noApiPrefix && hasCorrectFormat && thumbnailFixed && allSizesFixed;
            
            resultText += `🎯 Overall Assessment:\n`;
            resultText += `  - Main URL fixed: ${noApiPrefix && hasCorrectFormat ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Thumbnail URL fixed: ${thumbnailFixed ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - All size URLs fixed: ${allSizesFixed ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Payload serverURL fix working: ${payloadFixed ? 'PERFECT ✅' : 'INCOMPLETE ❌'}\n\n`;
            
            if (payloadFixed) {
                resultText += `🎉 PAYLOAD URL FIX SUCCESS!\n`;
                resultText += `✅ Payload now generates clean URLs!\n`;
                resultText += `✅ No /api/ prefixes in generated URLs!\n`;
                resultText += `🎯 serverURL configuration is working perfectly!`;
                showResultFunc('success', resultText);
            } else {
                resultText += `⚠️ Payload URL fix incomplete:\n`;
                if (!noApiPrefix || !hasCorrectFormat) resultText += `  - Main URL still has issues\n`;
                if (!thumbnailFixed) resultText += `  - Thumbnail URL still has issues\n`;
                if (!allSizesFixed) resultText += `  - Some size URLs still have issues\n`;
                resultText += `❌ May need to restart server for config changes to take effect`;
                showResultFunc('error', resultText);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showUrlResult(type, message) {
            const element = document.getElementById('urlResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Payload URL Fix Test loaded');
            console.log('🎯 Testing Payload serverURL configuration');
            console.log('📋 Should generate URLs without /api/ prefix');
            
            showResult('info', 'Ready to test Payload URL fix. Select an image and click "Test Payload URL Fix".');
            
            // Show restart reminder
            setTimeout(() => {
                showResult('info', 
                    'Ready to test Payload URL fix. Select an image and click "Test Payload URL Fix".\n\n' +
                    '⚠️ Note: If you just made the serverURL config change, you may need to restart the server for it to take effect.'
                );
            }, 2000);
        });
    </script>
</body>
</html>
