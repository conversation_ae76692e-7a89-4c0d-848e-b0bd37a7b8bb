// Comprehensive test script for Phase 16 Course Management API endpoints
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:3001/api';
let authToken = null;

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, passed, details = '') {
  testResults.tests.push({ name, passed, details });
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}: ${details}`);
  }
}

// Helper function to make authenticated API calls
async function apiCall(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
    ...options.headers
  };

  try {
    const response = await fetch(url, {
      ...options,
      headers
    });

    const data = await response.text();
    let jsonData = null;
    
    try {
      jsonData = JSON.parse(data);
    } catch (e) {
      // Response is not JSON
    }

    return {
      ok: response.ok,
      status: response.status,
      data: jsonData,
      rawData: data
    };
  } catch (error) {
    return {
      ok: false,
      status: 0,
      error: error.message
    };
  }
}

// Test authentication
async function testAuthentication() {
  console.log('\n🔐 Testing Authentication...');
  
  const response = await apiCall('/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'password123'
    })
  });

  if (response.ok && response.data?.token) {
    authToken = response.data.token;
    logTest('Authentication', true);
    return true;
  } else {
    logTest('Authentication', false, `Status: ${response.status}`);
    return false;
  }
}

// Test Categories API
async function testCategoriesAPI() {
  console.log('\n📂 Testing Categories API...');
  
  // Test GET categories
  let response = await apiCall('/institute-admin/categories');
  logTest('GET Categories', response.ok, response.ok ? '' : `Status: ${response.status}`);

  // Test GET categories dropdown
  response = await apiCall('/institute-admin/categories/dropdown');
  logTest('GET Categories Dropdown', response.ok, response.ok ? '' : `Status: ${response.status}`);

  // Test POST category (create)
  const categoryData = {
    name: 'Test Government Exams',
    description: 'Test category for government competitive exams',
    icon: 'fas fa-university',
    color: '#3b82f6',
    isActive: true,
    isPublic: true
  };

  response = await apiCall('/institute-admin/categories', {
    method: 'POST',
    body: JSON.stringify(categoryData)
  });

  let createdCategoryId = null;
  if (response.ok && response.data?.category) {
    createdCategoryId = response.data.category.id;
    logTest('POST Category (Create)', true);
  } else {
    logTest('POST Category (Create)', false, `Status: ${response.status}`);
  }

  // Test PUT category (update)
  if (createdCategoryId) {
    const updateData = {
      name: 'Updated Test Government Exams',
      description: 'Updated test category description'
    };

    response = await apiCall(`/institute-admin/categories/${createdCategoryId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });

    logTest('PUT Category (Update)', response.ok, response.ok ? '' : `Status: ${response.status}`);
  }

  return createdCategoryId;
}

// Test Exam Types API
async function testExamTypesAPI(categoryId) {
  console.log('\n📝 Testing Exam Types API...');
  
  // Test GET exam types
  let response = await apiCall('/institute-admin/exam-types');
  logTest('GET Exam Types', response.ok, response.ok ? '' : `Status: ${response.status}`);

  // Test GET exam types with category filter
  if (categoryId) {
    response = await apiCall(`/institute-admin/exam-types?category_id=${categoryId}`);
    logTest('GET Exam Types (Filtered)', response.ok, response.ok ? '' : `Status: ${response.status}`);
  }

  // Test GET exam types dropdown
  response = await apiCall('/institute-admin/exam-types/dropdown');
  logTest('GET Exam Types Dropdown', response.ok, response.ok ? '' : `Status: ${response.status}`);

  // Test POST exam type (create)
  if (categoryId) {
    const examTypeData = {
      category_id: categoryId,
      name: 'Test TNPSC Group 1',
      shortName: 'TNPSC-G1',
      description: 'Test exam type for TNPSC Group 1 examination',
      difficulty: 'intermediate',
      subjects: [
        { subject: 'General Studies' },
        { subject: 'Current Affairs' }
      ],
      languages: [
        { language: 'en' },
        { language: 'ta' }
      ],
      isActive: true,
      isPublic: true
    };

    response = await apiCall('/institute-admin/exam-types', {
      method: 'POST',
      body: JSON.stringify(examTypeData)
    });

    let createdExamTypeId = null;
    if (response.ok && response.data?.examType) {
      createdExamTypeId = response.data.examType.id;
      logTest('POST Exam Type (Create)', true);
    } else {
      logTest('POST Exam Type (Create)', false, `Status: ${response.status}`);
    }

    return createdExamTypeId;
  }

  return null;
}

// Test Cascading Data API
async function testCascadingDataAPI() {
  console.log('\n🔗 Testing Cascading Data API...');
  
  const response = await apiCall('/institute-admin/cascading-data');
  
  if (response.ok && response.data) {
    const { categories, examTypesByCategory } = response.data;
    const hasCategories = Array.isArray(categories) && categories.length >= 0;
    const hasExamTypesByCategory = typeof examTypesByCategory === 'object';
    
    logTest('GET Cascading Data', hasCategories && hasExamTypesByCategory, 
      hasCategories && hasExamTypesByCategory ? '' : 'Invalid data structure');
  } else {
    logTest('GET Cascading Data', false, `Status: ${response.status}`);
  }
}

// Test Course Sharing API
async function testCourseSharingAPI() {
  console.log('\n🔄 Testing Course Sharing API...');
  
  // First, try to get existing courses
  const coursesResponse = await apiCall('/institute-admin/courses');
  
  if (coursesResponse.ok && coursesResponse.data?.docs?.length > 0) {
    const courseId = coursesResponse.data.docs[0].id;
    
    // Test GET course sharing settings
    let response = await apiCall(`/institute-admin/courses/${courseId}/sharing`);
    logTest('GET Course Sharing Settings', response.ok, response.ok ? '' : `Status: ${response.status}`);

    // Test PUT course sharing settings
    const sharingData = {
      shareSettings: {
        shareWithBranches: true,
        shareWithMarketplace: false,
        allowExternalPurchase: false,
        approvalRequired: true
      }
    };

    response = await apiCall(`/institute-admin/courses/${courseId}/sharing`, {
      method: 'PUT',
      body: JSON.stringify(sharingData)
    });

    logTest('PUT Course Sharing Settings', response.ok, response.ok ? '' : `Status: ${response.status}`);

    // Test GET shared courses
    response = await apiCall('/institute-admin/shared-courses');
    logTest('GET Shared Courses', response.ok, response.ok ? '' : `Status: ${response.status}`);
  } else {
    logTest('Course Sharing Tests', false, 'No courses available for testing');
  }
}

// Test error handling
async function testErrorHandling() {
  console.log('\n⚠️ Testing Error Handling...');
  
  // Test unauthorized access (without token)
  const originalToken = authToken;
  authToken = null;
  
  let response = await apiCall('/institute-admin/categories');
  logTest('Unauthorized Access Handling', !response.ok && response.status === 401, 
    response.ok ? 'Should have failed with 401' : '');
  
  authToken = originalToken;

  // Test invalid category ID
  response = await apiCall('/institute-admin/categories/invalid-id', {
    method: 'PUT',
    body: JSON.stringify({ name: 'Test' })
  });
  logTest('Invalid ID Handling', !response.ok, response.ok ? 'Should have failed' : '');

  // Test invalid data
  response = await apiCall('/institute-admin/categories', {
    method: 'POST',
    body: JSON.stringify({ name: '' }) // Empty name should fail validation
  });
  logTest('Invalid Data Handling', !response.ok, response.ok ? 'Should have failed validation' : '');
}

// Test data validation
async function testDataValidation() {
  console.log('\n✅ Testing Data Validation...');
  
  // Test category name validation
  const invalidCategoryData = {
    name: 'A', // Too short
    description: 'Test'
  };

  let response = await apiCall('/institute-admin/categories', {
    method: 'POST',
    body: JSON.stringify(invalidCategoryData)
  });

  logTest('Category Name Length Validation', !response.ok, 
    response.ok ? 'Should reject short names' : '');

  // Test missing required fields
  response = await apiCall('/institute-admin/categories', {
    method: 'POST',
    body: JSON.stringify({}) // Missing required fields
  });

  logTest('Required Fields Validation', !response.ok, 
    response.ok ? 'Should require name and description' : '');
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Phase 16 API Tests\n');
  console.log('=' .repeat(60));
  
  // Test authentication first
  const authSuccess = await testAuthentication();
  if (!authSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }

  // Run all API tests
  const categoryId = await testCategoriesAPI();
  const examTypeId = await testExamTypesAPI(categoryId);
  await testCascadingDataAPI();
  await testCourseSharingAPI();
  await testErrorHandling();
  await testDataValidation();

  // Cleanup - delete test data
  if (examTypeId) {
    await apiCall(`/institute-admin/exam-types/${examTypeId}`, { method: 'DELETE' });
  }
  if (categoryId) {
    await apiCall(`/institute-admin/categories/${categoryId}`, { method: 'DELETE' });
  }

  // Print summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(60));
  console.log(`Total Tests: ${testResults.passed + testResults.failed}`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(test => !test.passed)
      .forEach(test => console.log(`   - ${test.name}: ${test.details}`));
  }

  if (testResults.failed === 0) {
    console.log('\n🎉 All tests passed! Phase 16 API implementation is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the implementation.');
  }
}

// Run the tests
runAllTests().catch(console.error);
