# PowerShell script to test Phase 16 Course Management API endpoints
$API_BASE = "http://localhost:3001/api"
$authToken = $null

# Test results tracking
$testResults = @{
    passed = 0
    failed = 0
    tests = @()
}

function Log-Test {
    param(
        [string]$name,
        [bool]$passed,
        [string]$details = ""
    )
    
    $testResults.tests += @{ name = $name; passed = $passed; details = $details }
    if ($passed) {
        $testResults.passed++
        Write-Host "✅ $name" -ForegroundColor Green
    } else {
        $testResults.failed++
        Write-Host "❌ $name`: $details" -ForegroundColor Red
    }
}

function Invoke-ApiCall {
    param(
        [string]$endpoint,
        [string]$method = "GET",
        [hashtable]$body = $null,
        [hashtable]$headers = @{}
    )
    
    $url = "$API_BASE$endpoint"
    $requestHeaders = @{
        "Content-Type" = "application/json"
    }
    
    if ($authToken) {
        $requestHeaders["Authorization"] = "Bearer $authToken"
    }
    
    foreach ($key in $headers.Keys) {
        $requestHeaders[$key] = $headers[$key]
    }
    
    try {
        $params = @{
            Uri = $url
            Method = $method
            Headers = $requestHeaders
            UseBasicParsing = $true
        }
        
        if ($body) {
            $params.Body = ($body | ConvertTo-Json -Depth 10)
        }
        
        $response = Invoke-WebRequest @params
        
        $data = $null
        if ($response.Content) {
            try {
                $data = $response.Content | ConvertFrom-Json
            } catch {
                # Response is not JSON
            }
        }
        
        return @{
            ok = $response.StatusCode -ge 200 -and $response.StatusCode -lt 300
            status = $response.StatusCode
            data = $data
        }
    } catch {
        return @{
            ok = $false
            status = if ($_.Exception.Response) { $_.Exception.Response.StatusCode } else { 0 }
            error = $_.Exception.Message
        }
    }
}

function Test-Authentication {
    Write-Host "`n🔐 Testing Authentication..." -ForegroundColor Cyan
    
    $loginData = @{
        email = "<EMAIL>"
        password = "password123"
    }
    
    $response = Invoke-ApiCall -endpoint "/auth/login" -method "POST" -body $loginData
    
    if ($response.ok -and $response.data.token) {
        $script:authToken = $response.data.token
        Log-Test "Authentication" $true
        return $true
    } else {
        Log-Test "Authentication" $false "Status: $($response.status)"
        return $false
    }
}

function Test-CategoriesAPI {
    Write-Host "`n📂 Testing Categories API..." -ForegroundColor Cyan
    
    # Test GET categories
    $response = Invoke-ApiCall -endpoint "/institute-admin/categories"
    Log-Test "GET Categories" $response.ok $(if (-not $response.ok) { "Status: $($response.status)" } else { "" })
    
    # Test GET categories dropdown
    $response = Invoke-ApiCall -endpoint "/institute-admin/categories/dropdown"
    Log-Test "GET Categories Dropdown" $response.ok $(if (-not $response.ok) { "Status: $($response.status)" } else { "" })
    
    # Test POST category (create)
    $categoryData = @{
        name = "Test Government Exams"
        description = "Test category for government competitive exams"
        icon = "fas fa-university"
        color = "#3b82f6"
        isActive = $true
        isPublic = $true
    }
    
    $response = Invoke-ApiCall -endpoint "/institute-admin/categories" -method "POST" -body $categoryData
    
    $createdCategoryId = $null
    if ($response.ok -and $response.data.category) {
        $createdCategoryId = $response.data.category.id
        Log-Test "POST Category (Create)" $true
    } else {
        Log-Test "POST Category (Create)" $false "Status: $($response.status)"
    }
    
    return $createdCategoryId
}

function Test-ExamTypesAPI {
    param([string]$categoryId)
    
    Write-Host "`n📝 Testing Exam Types API..." -ForegroundColor Cyan
    
    # Test GET exam types
    $response = Invoke-ApiCall -endpoint "/institute-admin/exam-types"
    Log-Test "GET Exam Types" $response.ok $(if (-not $response.ok) { "Status: $($response.status)" } else { "" })
    
    # Test GET exam types dropdown
    $response = Invoke-ApiCall -endpoint "/institute-admin/exam-types/dropdown"
    Log-Test "GET Exam Types Dropdown" $response.ok $(if (-not $response.ok) { "Status: $($response.status)" } else { "" })
    
    if ($categoryId) {
        # Test GET exam types with category filter
        $response = Invoke-ApiCall -endpoint "/institute-admin/exam-types?category_id=$categoryId"
        Log-Test "GET Exam Types (Filtered)" $response.ok $(if (-not $response.ok) { "Status: $($response.status)" } else { "" })
    }
}

function Test-CascadingDataAPI {
    Write-Host "`n🔗 Testing Cascading Data API..." -ForegroundColor Cyan
    
    $response = Invoke-ApiCall -endpoint "/institute-admin/cascading-data"
    
    if ($response.ok -and $response.data) {
        $hasCategories = $response.data.categories -is [array]
        $hasExamTypesByCategory = $response.data.examTypesByCategory -is [hashtable] -or $response.data.examTypesByCategory -is [PSCustomObject]
        
        Log-Test "GET Cascading Data" ($hasCategories -and $hasExamTypesByCategory) $(if (-not ($hasCategories -and $hasExamTypesByCategory)) { "Invalid data structure" } else { "" })
    } else {
        Log-Test "GET Cascading Data" $false "Status: $($response.status)"
    }
}

function Test-ErrorHandling {
    Write-Host "`n⚠️ Testing Error Handling..." -ForegroundColor Cyan
    
    # Test unauthorized access (without token)
    $originalToken = $script:authToken
    $script:authToken = $null
    
    $response = Invoke-ApiCall -endpoint "/institute-admin/categories"
    Log-Test "Unauthorized Access Handling" (-not $response.ok -and $response.status -eq 401) $(if ($response.ok) { "Should have failed with 401" } else { "" })
    
    $script:authToken = $originalToken
    
    # Test invalid data
    $invalidData = @{ name = "" }
    $response = Invoke-ApiCall -endpoint "/institute-admin/categories" -method "POST" -body $invalidData
    Log-Test "Invalid Data Handling" (-not $response.ok) $(if ($response.ok) { "Should have failed validation" } else { "" })
}

function Show-Summary {
    Write-Host "`n$('=' * 60)" -ForegroundColor Yellow
    Write-Host "📊 TEST SUMMARY" -ForegroundColor Yellow
    Write-Host "$('=' * 60)" -ForegroundColor Yellow
    Write-Host "Total Tests: $($testResults.passed + $testResults.failed)"
    Write-Host "✅ Passed: $($testResults.passed)" -ForegroundColor Green
    Write-Host "❌ Failed: $($testResults.failed)" -ForegroundColor Red
    
    $successRate = if (($testResults.passed + $testResults.failed) -gt 0) {
        [math]::Round(($testResults.passed / ($testResults.passed + $testResults.failed)) * 100, 1)
    } else { 0 }
    Write-Host "Success Rate: $successRate%"
    
    if ($testResults.failed -gt 0) {
        Write-Host "`n❌ Failed Tests:" -ForegroundColor Red
        $testResults.tests | Where-Object { -not $_.passed } | ForEach-Object {
            Write-Host "   - $($_.name): $($_.details)" -ForegroundColor Red
        }
    }
    
    if ($testResults.failed -eq 0) {
        Write-Host "`n🎉 All tests passed! Phase 16 API implementation is working correctly." -ForegroundColor Green
    } else {
        Write-Host "`n⚠️ Some tests failed. Please check the implementation." -ForegroundColor Yellow
    }
}

# Main execution
Write-Host "🚀 Starting Phase 16 API Tests" -ForegroundColor Cyan
Write-Host "$('=' * 60)" -ForegroundColor Cyan

# Test authentication first
$authSuccess = Test-Authentication
if (-not $authSuccess) {
    Write-Host "`n❌ Cannot proceed without authentication" -ForegroundColor Red
    return
}

# Run all API tests
$categoryId = Test-CategoriesAPI
Test-ExamTypesAPI -categoryId $categoryId
Test-CascadingDataAPI
Test-ErrorHandling

# Show summary
Show-Summary
