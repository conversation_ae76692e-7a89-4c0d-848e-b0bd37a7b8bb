@echo off
echo 🚀 Testing Phase 16 Course Management API Endpoints
echo ============================================================

echo.
echo 🔐 Testing API Server Status...
curl -s -o nul -w "HTTP Status: %%{http_code}" http://localhost:3001/api/health
if %errorlevel% neq 0 (
    echo ❌ API Server is not responding
    pause
    exit /b 1
) else (
    echo ✅ API Server is running
)

echo.
echo 📂 Testing Categories Endpoint...
curl -s -o nul -w "HTTP Status: %%{http_code}" http://localhost:3001/api/institute-admin/categories
if %errorlevel% neq 0 (
    echo ❌ Categories endpoint failed
) else (
    echo ✅ Categories endpoint accessible
)

echo.
echo 📝 Testing Exam Types Endpoint...
curl -s -o nul -w "HTTP Status: %%{http_code}" http://localhost:3001/api/institute-admin/exam-types
if %errorlevel% neq 0 (
    echo ❌ Exam Types endpoint failed
) else (
    echo ✅ Exam Types endpoint accessible
)

echo.
echo 🔗 Testing Cascading Data Endpoint...
curl -s -o nul -w "HTTP Status: %%{http_code}" http://localhost:3001/api/institute-admin/cascading-data
if %errorlevel% neq 0 (
    echo ❌ Cascading Data endpoint failed
) else (
    echo ✅ Cascading Data endpoint accessible
)

echo.
echo 📊 Testing Complete!
echo ============================================================
echo.
echo ✅ Phase 16 API endpoints are accessible
echo 🎯 Ready for frontend integration testing
echo.
pause
