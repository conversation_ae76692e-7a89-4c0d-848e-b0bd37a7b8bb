// Test script for Phase 16 Course Management implementation
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:3001/api';

// Test authentication and get token
async function testAuthentication() {
  try {
    console.log('🔐 Testing authentication...');
    
    const response = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>', // Replace with actual admin email
        password: 'password123' // Replace with actual password
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Authentication successful');
      return data.token;
    } else {
      console.log('❌ Authentication failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Authentication error:', error.message);
    return null;
  }
}

// Test categories endpoint
async function testCategories(token) {
  try {
    console.log('\n📂 Testing categories endpoint...');
    
    const response = await fetch(`${API_BASE}/institute-admin/categories`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Categories endpoint working');
      console.log(`   Found ${data.categories?.length || 0} categories`);
      return true;
    } else {
      console.log('❌ Categories endpoint failed:', response.status);
      const errorData = await response.text();
      console.log('   Error:', errorData);
      return false;
    }
  } catch (error) {
    console.log('❌ Categories endpoint error:', error.message);
    return false;
  }
}

// Test exam types endpoint
async function testExamTypes(token) {
  try {
    console.log('\n📝 Testing exam types endpoint...');
    
    const response = await fetch(`${API_BASE}/institute-admin/exam-types`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Exam types endpoint working');
      console.log(`   Found ${data.examTypes?.length || 0} exam types`);
      return true;
    } else {
      console.log('❌ Exam types endpoint failed:', response.status);
      const errorData = await response.text();
      console.log('   Error:', errorData);
      return false;
    }
  } catch (error) {
    console.log('❌ Exam types endpoint error:', error.message);
    return false;
  }
}

// Test cascading data endpoint
async function testCascadingData(token) {
  try {
    console.log('\n🔗 Testing cascading data endpoint...');
    
    const response = await fetch(`${API_BASE}/institute-admin/cascading-data`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Cascading data endpoint working');
      console.log(`   Categories: ${data.categories?.length || 0}`);
      console.log(`   Exam types by category: ${Object.keys(data.examTypesByCategory || {}).length} categories`);
      return true;
    } else {
      console.log('❌ Cascading data endpoint failed:', response.status);
      const errorData = await response.text();
      console.log('   Error:', errorData);
      return false;
    }
  } catch (error) {
    console.log('❌ Cascading data endpoint error:', error.message);
    return false;
  }
}

// Test creating a category
async function testCreateCategory(token) {
  try {
    console.log('\n➕ Testing category creation...');
    
    const categoryData = {
      name: 'Test Government Exams',
      description: 'Test category for government competitive exams',
      icon: 'fas fa-university',
      color: '#3b82f6',
      isActive: true,
      isPublic: true
    };

    const response = await fetch(`${API_BASE}/institute-admin/categories`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(categoryData)
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Category creation successful');
      console.log(`   Created category: ${data.category?.name}`);
      return data.category?.id;
    } else {
      console.log('❌ Category creation failed:', response.status);
      const errorData = await response.text();
      console.log('   Error:', errorData);
      return null;
    }
  } catch (error) {
    console.log('❌ Category creation error:', error.message);
    return null;
  }
}

// Test creating an exam type
async function testCreateExamType(token, categoryId) {
  if (!categoryId) {
    console.log('\n⏭️  Skipping exam type creation (no category ID)');
    return null;
  }

  try {
    console.log('\n➕ Testing exam type creation...');
    
    const examTypeData = {
      category_id: categoryId,
      name: 'Test TNPSC Group 1',
      shortName: 'TNPSC-G1',
      description: 'Test exam type for TNPSC Group 1 examination',
      difficulty: 'intermediate',
      subjects: [
        { subject: 'General Studies' },
        { subject: 'Current Affairs' },
        { subject: 'Tamil Language' }
      ],
      languages: [
        { language: 'en' },
        { language: 'ta' }
      ],
      isActive: true,
      isPublic: true
    };

    const response = await fetch(`${API_BASE}/institute-admin/exam-types`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(examTypeData)
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Exam type creation successful');
      console.log(`   Created exam type: ${data.examType?.name}`);
      return data.examType?.id;
    } else {
      console.log('❌ Exam type creation failed:', response.status);
      const errorData = await response.text();
      console.log('   Error:', errorData);
      return null;
    }
  } catch (error) {
    console.log('❌ Exam type creation error:', error.message);
    return null;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Phase 16 Course Management Tests\n');
  
  // Test authentication
  const token = await testAuthentication();
  if (!token) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }

  // Test endpoints
  const categoriesWorking = await testCategories(token);
  const examTypesWorking = await testExamTypes(token);
  const cascadingWorking = await testCascadingData(token);

  // Test creation if endpoints are working
  let categoryId = null;
  let examTypeId = null;

  if (categoriesWorking) {
    categoryId = await testCreateCategory(token);
  }

  if (examTypesWorking && categoryId) {
    examTypeId = await testCreateExamType(token, categoryId);
  }

  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`   Authentication: ${token ? '✅' : '❌'}`);
  console.log(`   Categories API: ${categoriesWorking ? '✅' : '❌'}`);
  console.log(`   Exam Types API: ${examTypesWorking ? '✅' : '❌'}`);
  console.log(`   Cascading Data API: ${cascadingWorking ? '✅' : '❌'}`);
  console.log(`   Category Creation: ${categoryId ? '✅' : '❌'}`);
  console.log(`   Exam Type Creation: ${examTypeId ? '✅' : '❌'}`);

  if (categoriesWorking && examTypesWorking && cascadingWorking) {
    console.log('\n🎉 Phase 16 Course Management implementation is working!');
  } else {
    console.log('\n⚠️  Some issues found. Check the errors above.');
  }
}

// Run the tests
runTests().catch(console.error);
