<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Platform Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        input[type="file"] { margin: 10px 0; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Platform Logo/Favicon Upload Test</h1>
    
    <div class="test-section">
        <h2>1. Authentication Test</h2>
        <button onclick="testAuth()">Test Authentication</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Platform Logo Upload Test</h2>
        <input type="file" id="logoFile" accept="image/*">
        <button onclick="testLogoUpload()" id="logoUploadBtn">Upload Platform Logo</button>
        <div id="logo-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Platform Favicon Upload Test</h2>
        <input type="file" id="faviconFile" accept="image/*,.ico">
        <button onclick="testFaviconUpload()" id="faviconUploadBtn">Upload Platform Favicon</button>
        <div id="favicon-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Bulk Settings Update Test</h2>
        <button onclick="testBulkUpdate()">Test Bulk Settings Update</button>
        <div id="bulk-result"></div>
    </div>

    <div class="test-section">
        <h2>5. Settings Verification</h2>
        <button onclick="checkSettings()">Check Platform Settings</button>
        <div id="settings-result"></div>
    </div>

    <script>
        let testToken = null;

        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function testAuth() {
            try {
                showResult('auth-result', 'info', 'Testing authentication...');
                
                const response = await fetch('http://localhost:3001/api/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '123456'
                    }),
                });

                const data = await response.json();
                
                if (data.token) {
                    testToken = data.token;
                    showResult('auth-result', 'success', `✅ Authentication successful! Token: ${data.token.substring(0, 20)}...`);
                } else {
                    showResult('auth-result', 'error', `❌ Authentication failed: ${data.message}`);
                }
            } catch (error) {
                showResult('auth-result', 'error', `❌ Auth error: ${error.message}`);
            }
        }

        async function testLogoUpload() {
            const fileInput = document.getElementById('logoFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('logo-result', 'error', '❌ Please select a logo file first');
                return;
            }

            if (!testToken) {
                showResult('logo-result', 'error', '❌ Please authenticate first');
                return;
            }

            try {
                showResult('logo-result', 'info', '📤 Uploading platform logo...');
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('uploadType', 'platform_logo');
                formData.append('folder', 'platform');

                const response = await fetch('http://localhost:3001/api/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('logo-result', 'success', `✅ Logo uploaded successfully!<br>
                        <strong>URL:</strong> ${result.media.url}<br>
                        <strong>Filename:</strong> ${result.media.filename}<br>
                        <strong>Media Type:</strong> ${result.media.mediaType}<br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>`);
                } else {
                    showResult('logo-result', 'error', `❌ Upload failed: ${result.message}`);
                }
            } catch (error) {
                showResult('logo-result', 'error', `❌ Upload error: ${error.message}`);
            }
        }

        async function testFaviconUpload() {
            const fileInput = document.getElementById('faviconFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('favicon-result', 'error', '❌ Please select a favicon file first');
                return;
            }

            if (!testToken) {
                showResult('favicon-result', 'error', '❌ Please authenticate first');
                return;
            }

            try {
                showResult('favicon-result', 'info', '📤 Uploading platform favicon...');
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('uploadType', 'platform_favicon');
                formData.append('folder', 'platform');

                const response = await fetch('http://localhost:3001/api/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('favicon-result', 'success', `✅ Favicon uploaded successfully!<br>
                        <strong>URL:</strong> ${result.media.url}<br>
                        <strong>Filename:</strong> ${result.media.filename}<br>
                        <strong>Media Type:</strong> ${result.media.mediaType}<br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>`);
                } else {
                    showResult('favicon-result', 'error', `❌ Upload failed: ${result.message}`);
                }
            } catch (error) {
                showResult('favicon-result', 'error', `❌ Upload error: ${error.message}`);
            }
        }

        async function initializeSettings() {
            if (!testToken) {
                showResult('init-result', 'error', '❌ Please authenticate first');
                return;
            }

            try {
                showResult('init-result', 'info', '🔧 Initializing platform media settings...');

                // Use the new initialization endpoint
                const response = await fetch('http://localhost:3001/api/platform/settings/init-media', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                const result = await response.json();

                if (result.success) {
                    const logoResult = result.results.find(r => r.key === 'platform_logo');
                    const faviconResult = result.results.find(r => r.key === 'platform_favicon');

                    showResult('init-result', 'success', `✅ Platform media settings initialized!<br>
                        <strong>Logo Setting:</strong> ${logoResult ? logoResult.operation : 'Not found'}<br>
                        <strong>Favicon Setting:</strong> ${faviconResult ? faviconResult.operation : 'Not found'}<br>
                        <strong>Initialized:</strong> ${result.initialized}<br>
                        <strong>Failed:</strong> ${result.failed}<br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>`);
                } else {
                    showResult('init-result', 'error', `❌ Initialization failed: ${result.message}`);
                }

            } catch (error) {
                showResult('init-result', 'error', `❌ Initialization error: ${error.message}`);
            }
        }

        async function checkSettings() {
            if (!testToken) {
                showResult('settings-result', 'error', '❌ Please authenticate first');
                return;
            }

            try {
                showResult('settings-result', 'info', '🔍 Checking platform settings...');

                const response = await fetch('http://localhost:3001/api/platform/settings?category=platform', {
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                });

                const result = await response.json();

                if (result.success) {
                    const logoSetting = result.settings.find(s => s.key === 'platform_logo');
                    const faviconSetting = result.settings.find(s => s.key === 'platform_favicon');

                    showResult('settings-result', 'success', `✅ Settings retrieved successfully!<br>
                        <strong>Platform Logo:</strong> ${logoSetting ? logoSetting.value || 'Empty' : 'Not found'}<br>
                        <strong>Platform Favicon:</strong> ${faviconSetting ? faviconSetting.value || 'Empty' : 'Not found'}<br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>`);
                } else {
                    showResult('settings-result', 'error', `❌ Failed to get settings: ${result.message}`);
                }
            } catch (error) {
                showResult('settings-result', 'error', `❌ Settings error: ${error.message}`);
            }
        }

        // Auto-authenticate and initialize on page load
        window.onload = async function() {
            await testAuth();
            // Wait a bit for auth to complete, then initialize settings
            setTimeout(async () => {
                if (testToken) {
                    await initializeSettings();
                }
            }, 1000);
        };
    </script>
</body>
</html>
