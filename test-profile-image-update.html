<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖼️ Profile Image Update Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .upload-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.danger {
            background-color: #dc3545;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .hidden {
            display: none;
        }
        .avatar-preview {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .avatar-size {
            text-align: center;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .avatar-size img {
            border-radius: 50%;
            border: 2px solid #ddd;
            display: block;
            margin: 0 auto 5px;
        }
        .avatar-size h4 {
            margin: 5px 0;
            font-size: 12px;
            color: #666;
        }
        .file-list {
            margin: 20px 0;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
        .file-info {
            flex: 1;
        }
        .file-actions {
            display: flex;
            gap: 5px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Profile Image Update Test</h1>
        <p>Test the common file upload API for profile image updates with automatic avatar sizing.</p>
        
        <div class="info">
            <strong>Token:</strong> eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg
        </div>
    </div>

    <!-- Profile Avatar Upload -->
    <div class="container">
        <div class="upload-section">
            <h3>👤 Profile Avatar Upload</h3>
            <div class="upload-area" id="avatarUploadArea">
                <p>📁 Drag & drop your profile image here or click to select</p>
                <p style="color: #666; font-size: 14px;">Max 5MB, Images only (JPG, PNG, GIF, WEBP)</p>
                <input type="file" id="avatarFileInput" accept="image/*" class="hidden">
            </div>
            <button class="btn" onclick="uploadProfileImage()" id="uploadAvatarBtn" disabled>
                <span id="avatarUploadText">Upload Profile Image</span>
                <span id="avatarUploadSpinner" class="loading hidden"></span>
            </button>
            <button class="btn success" onclick="getCurrentUser()">Get Current Profile</button>
            <div id="avatarResult"></div>
            <div id="avatarPreview" class="avatar-preview"></div>
        </div>
    </div>

    <!-- Other File Types -->
    <div class="container">
        <div class="upload-section">
            <h3>📄 Other File Uploads</h3>
            <div class="upload-area" id="generalUploadArea">
                <p>📁 Upload any file (documents, images, etc.)</p>
                <p style="color: #666; font-size: 14px;">Max 25MB</p>
                <input type="file" id="generalFileInput" class="hidden">
            </div>
            <select id="uploadTypeSelect">
                <option value="general">General File</option>
                <option value="document">Document</option>
                <option value="course_thumbnail">Course Thumbnail</option>
                <option value="institute_logo">Institute Logo</option>
            </select>
            <button class="btn" onclick="uploadGeneralFile()" id="uploadGeneralBtn" disabled>Upload File</button>
            <div id="generalResult"></div>
        </div>
    </div>

    <!-- My Files -->
    <div class="container">
        <div class="upload-section">
            <h3>📂 My Uploaded Files</h3>
            <button class="btn" onclick="getMyFiles()">Refresh My Files</button>
            <button class="btn" onclick="getMyFiles('user_avatar')">My Avatars Only</button>
            <div id="filesResult"></div>
            <div id="filesList" class="file-list"></div>
        </div>
    </div>

    <script>
        const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedAvatarFile = null;
        let selectedGeneralFile = null;

        // Avatar file handling
        const avatarFileInput = document.getElementById('avatarFileInput');
        const avatarUploadArea = document.getElementById('avatarUploadArea');
        
        avatarUploadArea.addEventListener('click', () => avatarFileInput.click());
        avatarUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            avatarUploadArea.classList.add('dragover');
        });
        avatarUploadArea.addEventListener('dragleave', () => {
            avatarUploadArea.classList.remove('dragover');
        });
        avatarUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            avatarUploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleAvatarFileSelect(files[0]);
            }
        });

        avatarFileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleAvatarFileSelect(e.target.files[0]);
            }
        });

        function handleAvatarFileSelect(file) {
            if (!file.type.startsWith('image/')) {
                showResult('avatarResult', 'error', 'Please select an image file');
                return;
            }
            if (file.size > 5 * 1024 * 1024) {
                showResult('avatarResult', 'error', 'File size must be less than 5MB');
                return;
            }
            
            selectedAvatarFile = file;
            document.getElementById('uploadAvatarBtn').disabled = false;
            showResult('avatarResult', 'info', `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
        }

        // General file handling
        const generalFileInput = document.getElementById('generalFileInput');
        const generalUploadArea = document.getElementById('generalUploadArea');
        
        generalUploadArea.addEventListener('click', () => generalFileInput.click());
        generalFileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedGeneralFile = e.target.files[0];
                document.getElementById('uploadGeneralBtn').disabled = false;
                showResult('generalResult', 'info', `Selected: ${selectedGeneralFile.name}`);
            }
        });

        // Upload profile image using common API
        async function uploadProfileImage() {
            if (!selectedAvatarFile) {
                showResult('avatarResult', 'error', 'Please select a file first');
                return;
            }

            setAvatarLoading(true);
            
            try {
                const formData = new FormData();
                formData.append('file', selectedAvatarFile);
                formData.append('uploadType', 'avatar');
                formData.append('updateUserField', 'avatar'); // This will update user.avatar field
                formData.append('folder', 'avatars');

                console.log('🚀 Uploading profile image...');
                
                const response = await fetch('http://localhost:3001/api/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                    body: formData,
                });

                const data = await response.json();
                console.log('📦 Upload response:', data);

                if (data.success) {
                    showResult('avatarResult', 'success', `✅ Profile image updated successfully!`);
                    displayAvatarSizes(data.media);
                    
                    // Show updated user info
                    if (data.user) {
                        console.log('👤 User profile updated:', data.user);
                    }
                } else {
                    showResult('avatarResult', 'error', `❌ Upload failed: ${data.message}`);
                }
            } catch (error) {
                console.error('Upload error:', error);
                showResult('avatarResult', 'error', `❌ Upload error: ${error.message}`);
            } finally {
                setAvatarLoading(false);
            }
        }

        // Upload general file
        async function uploadGeneralFile() {
            if (!selectedGeneralFile) {
                showResult('generalResult', 'error', 'Please select a file first');
                return;
            }

            try {
                const uploadType = document.getElementById('uploadTypeSelect').value;
                const formData = new FormData();
                formData.append('file', selectedGeneralFile);
                formData.append('uploadType', uploadType);

                console.log('🚀 Uploading file...', { type: uploadType, name: selectedGeneralFile.name });
                
                const response = await fetch('http://localhost:3001/api/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                    body: formData,
                });

                const data = await response.json();
                console.log('📦 Upload response:', data);

                if (data.success) {
                    showResult('generalResult', 'success', `✅ ${data.message}`);
                    getMyFiles(); // Refresh file list
                } else {
                    showResult('generalResult', 'error', `❌ Upload failed: ${data.message}`);
                }
            } catch (error) {
                console.error('Upload error:', error);
                showResult('generalResult', 'error', `❌ Upload error: ${error.message}`);
            }
        }

        // Get current user profile
        async function getCurrentUser() {
            try {
                const response = await fetch('http://localhost:3001/api/super-admin/user/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('👤 Current user:', data);

                if (data.success && data.user) {
                    showResult('avatarResult', 'success', `Current user: ${data.user.firstName} ${data.user.lastName}`);
                    
                    // Display current avatar if exists
                    if (data.user.avatar) {
                        console.log('🖼️ User has avatar:', data.user.avatar);
                        // You might want to fetch the full avatar details here
                    } else {
                        showResult('avatarResult', 'info', 'No avatar set for current user');
                    }
                } else {
                    showResult('avatarResult', 'error', `Failed to get user: ${data.message}`);
                }
            } catch (error) {
                console.error('Get user error:', error);
                showResult('avatarResult', 'error', `Error: ${error.message}`);
            }
        }

        // Get my uploaded files
        async function getMyFiles(mediaType = null) {
            try {
                let url = 'http://localhost:3001/api/upload/my-files';
                if (mediaType) {
                    url += `?mediaType=${mediaType}`;
                }

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('📂 My files:', data);

                if (data.success) {
                    showResult('filesResult', 'success', `Found ${data.files.length} files`);
                    displayFilesList(data.files);
                } else {
                    showResult('filesResult', 'error', `Failed to get files: ${data.message}`);
                }
            } catch (error) {
                console.error('Get files error:', error);
                showResult('filesResult', 'error', `Error: ${error.message}`);
            }
        }

        // Delete file
        async function deleteFile(fileId, fileName) {
            if (!confirm(`Are you sure you want to delete "${fileName}"?`)) {
                return;
            }

            try {
                const response = await fetch(`http://localhost:3001/api/upload/${fileId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('🗑️ Delete result:', data);

                if (data.success) {
                    showResult('filesResult', 'success', `File "${fileName}" deleted successfully`);
                    getMyFiles(); // Refresh file list
                } else {
                    showResult('filesResult', 'error', `Failed to delete file: ${data.message}`);
                }
            } catch (error) {
                console.error('Delete error:', error);
                showResult('filesResult', 'error', `Error: ${error.message}`);
            }
        }

        // Display avatar sizes
        function displayAvatarSizes(media) {
            const baseUrl = 'http://localhost:3001';
            const sizes = media.sizes || {};
            
            let html = '<h4>🖼️ Generated Avatar Sizes:</h4>';
            
            // Original image
            html += `
                <div class="avatar-size">
                    <h4>Original</h4>
                    <img src="${baseUrl}${media.url}" alt="Original" style="max-width: 100px; max-height: 100px;">
                    <p>${media.width || 'Unknown'}x${media.height || 'Unknown'}</p>
                </div>
            `;
            
            // Generated sizes
            const sizeLabels = {
                avatar_small: 'Small (40x40)',
                avatar_medium: 'Medium (80x80)', 
                avatar_large: 'Large (150x150)',
                profile: 'Profile (300x300)'
            };
            
            Object.entries(sizes).forEach(([sizeName, sizeData]) => {
                if (sizeLabels[sizeName]) {
                    html += `
                        <div class="avatar-size">
                            <h4>${sizeLabels[sizeName]}</h4>
                            <img src="${baseUrl}${sizeData.url}" alt="${sizeName}">
                            <p>${sizeData.width}x${sizeData.height}</p>
                        </div>
                    `;
                }
            });
            
            document.getElementById('avatarPreview').innerHTML = html;
        }

        // Display files list
        function displayFilesList(files) {
            const filesList = document.getElementById('filesList');
            
            if (files.length === 0) {
                filesList.innerHTML = '<p>No files uploaded yet.</p>';
                return;
            }

            let html = '';
            files.forEach(file => {
                const fileSize = (file.filesize / 1024 / 1024).toFixed(2);
                const uploadDate = new Date(file.createdAt).toLocaleDateString();
                
                html += `
                    <div class="file-item">
                        <div class="file-info">
                            <strong>${file.filename}</strong><br>
                            <small>Type: ${file.mediaType} | Size: ${fileSize} MB | Uploaded: ${uploadDate}</small>
                        </div>
                        <div class="file-actions">
                            <button class="btn" onclick="window.open('http://localhost:3001${file.url}', '_blank')">View</button>
                            <button class="btn danger" onclick="deleteFile('${file.id}', '${file.filename}')">Delete</button>
                        </div>
                    </div>
                `;
            });
            
            filesList.innerHTML = html;
        }

        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function setAvatarLoading(loading) {
            const uploadText = document.getElementById('avatarUploadText');
            const uploadSpinner = document.getElementById('avatarUploadSpinner');
            const uploadBtn = document.getElementById('uploadAvatarBtn');
            
            if (loading) {
                uploadText.textContent = 'Uploading...';
                uploadSpinner.classList.remove('hidden');
                uploadBtn.disabled = true;
            } else {
                uploadText.textContent = 'Upload Profile Image';
                uploadSpinner.classList.add('hidden');
                uploadBtn.disabled = !selectedAvatarFile;
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 Profile Image Update Test loaded');
            getCurrentUser();
            getMyFiles();
        });
    </script>
</body>
</html>
