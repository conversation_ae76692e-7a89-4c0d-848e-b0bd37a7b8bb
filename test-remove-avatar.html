<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗑️ Remove Avatar Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.danger {
            background-color: #dc3545;
        }
        .btn.danger:hover {
            background-color: #c82333;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .input-group {
            margin: 15px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .checkbox-group {
            margin: 10px 0;
        }
        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗑️ Remove Avatar Test</h1>
        <p>Test the avatar removal API that deletes files and cleans up database records.</p>
        
        <div class="warning">
            <strong>⚠️ Warning:</strong> This will permanently delete avatar files and database records!<br>
            - Deletes original file and all size variants<br>
            - Removes database record from media collection<br>
            - Optionally removes avatar reference from user profile<br>
            - Cannot be undone!
        </div>
    </div>

    <div class="container">
        <h3>🔧 Remove Avatar Options</h3>
        
        <div class="input-group">
            <label for="mediaId">Media ID (optional - will auto-detect from user if not provided):</label>
            <input type="text" id="mediaId" placeholder="e.g., 123">
        </div>
        
        <div class="input-group">
            <label for="userId">User ID (optional - defaults to current user):</label>
            <input type="text" id="userId" placeholder="e.g., 456">
        </div>
        
        <div class="checkbox-group">
            <label>
                <input type="checkbox" id="removeFromUser" checked>
                Remove avatar reference from user profile
            </label>
        </div>

        <button class="btn danger" onclick="removeAvatar()">🗑️ Remove Avatar</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>📋 API Usage Examples</h3>
        
        <div class="info">
            <strong>🔗 API Endpoint:</strong> DELETE /remove-avatar<br><br>
            
            <strong>📝 Request Body Examples:</strong><br><br>
            
            <strong>1. Remove current user's avatar (auto-detect):</strong><br>
            <code>{ "removeFromUser": true }</code><br><br>
            
            <strong>2. Remove specific media by ID:</strong><br>
            <code>{ "mediaId": "123", "removeFromUser": true }</code><br><br>
            
            <strong>3. Remove another user's avatar (admin only):</strong><br>
            <code>{ "userId": "456", "removeFromUser": true }</code><br><br>
            
            <strong>4. Delete media file only (keep user reference):</strong><br>
            <code>{ "mediaId": "123", "removeFromUser": false }</code>
        </div>
    </div>

    <div class="container">
        <h3>🔍 Test Results</h3>
        <div id="testResults"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function removeAvatar() {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            const mediaId = document.getElementById('mediaId').value.trim();
            const userId = document.getElementById('userId').value.trim();
            const removeFromUser = document.getElementById('removeFromUser').checked;

            // Build request body
            const requestBody = {
                removeFromUser
            };

            if (mediaId) {
                requestBody.mediaId = mediaId;
            }

            if (userId) {
                requestBody.userId = userId;
            }

            console.log('🗑️ Remove avatar request:', requestBody);

            try {
                showResult('info', 'Removing avatar...');
                
                const response = await fetch('http://localhost:3001/remove-avatar', {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeRemovalResult(data);
                } else {
                    showResult('error', `Avatar removal failed: ${data.message}`);
                }

            } catch (error) {
                console.error('❌ Remove avatar error:', error);
                showResult('error', `Remove avatar error: ${error.message}`);
            }
        }

        function analyzeRemovalResult(data) {
            let resultText = `🎉 Avatar Removal Success!\n\n`;
            
            resultText += `📋 Removal Details:\n`;
            resultText += `  - Success: ${data.success ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Message: ${data.message}\n\n`;
            
            if (data.deletedMedia) {
                resultText += `🗑️ Deleted Media:\n`;
                resultText += `  - ID: ${data.deletedMedia.id}\n`;
                resultText += `  - Filename: ${data.deletedMedia.filename}\n`;
                resultText += `  - URL: ${data.deletedMedia.url}\n\n`;
            }
            
            resultText += `👤 User Profile Updated: ${data.userUpdated ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            resultText += `✅ Files Deleted:\n`;
            resultText += `  - Original file removed from disk\n`;
            resultText += `  - All size variants removed\n`;
            resultText += `  - Database record deleted\n`;
            if (data.userUpdated) {
                resultText += `  - User avatar reference cleared\n`;
            }
            
            showResult('success', resultText);
            
            // Show test results
            showTestResults({
                operation: 'Remove Avatar',
                success: true,
                deletedFiles: data.deletedMedia ? 1 : 0,
                userUpdated: data.userUpdated,
                timestamp: new Date().toLocaleString()
            });
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showTestResults(result) {
            const element = document.getElementById('testResults');
            
            let resultText = `📊 Test Results:\n\n`;
            resultText += `🔧 Operation: ${result.operation}\n`;
            resultText += `✅ Success: ${result.success ? 'YES' : 'NO'}\n`;
            resultText += `🗑️ Files Deleted: ${result.deletedFiles}\n`;
            resultText += `👤 User Updated: ${result.userUpdated ? 'YES' : 'NO'}\n`;
            resultText += `⏰ Timestamp: ${result.timestamp}\n\n`;
            
            if (result.success) {
                resultText += `🎯 Avatar removal completed successfully!\n`;
                resultText += `All files and database records have been cleaned up.`;
            } else {
                resultText += `❌ Avatar removal failed.\n`;
                resultText += `Check the error message above for details.`;
            }
            
            element.innerHTML = `<div class="${result.success ? 'success' : 'error'}">${resultText}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🗑️ Remove Avatar Test loaded');
            console.log('🎯 Testing avatar removal API');
            console.log('📋 Will delete files and clean up database records');
            
            showResult('info', 'Ready to test avatar removal. Configure options and click "Remove Avatar".');
        });
    </script>
</body>
</html>
