<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Simple Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .hidden {
            display: none;
        }
        select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .file-list {
            margin: 20px 0;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Simple Upload Test</h1>
        <p>Test the simple upload endpoint that bypasses all Payload CMS complexity.</p>
        
        <div class="success">
            <strong>✅ Simple Approach:</strong><br>
            - No Payload Media collection conflicts<br>
            - Direct file system operations<br>
            - Minimal dependencies<br>
            - Should work reliably
        </div>
    </div>

    <div class="container">
        <h3>📁 File Upload Test</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Drag & drop a file here or click to select</p>
            <p style="color: #666; font-size: 14px;">Test the simple upload approach</p>
            <input type="file" id="fileInput" class="hidden">
        </div>

        <div>
            <label>Upload Type:</label>
            <select id="uploadType">
                <option value="avatar">Avatar (saves to avatars/)</option>
                <option value="course_thumbnail">Course Thumbnail (saves to courses/)</option>
                <option value="institute_logo">Institute Logo (saves to institutes/)</option>
                <option value="document">Document (saves to documents/)</option>
                <option value="general">General File (saves to uploads/)</option>
            </select>
        </div>

        <button class="btn" onclick="uploadFile()" id="uploadBtn" disabled>Test Simple Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        <button class="btn success" onclick="getFiles()">Get Uploaded Files</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>📂 Uploaded Files</h3>
        <div id="filesList" class="file-list"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'white';
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'white';
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            selectedFile = file;
            document.getElementById('uploadBtn').disabled = false;
            showResult('info', `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
        }

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function uploadFile() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            const uploadType = document.getElementById('uploadType').value;
            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('uploadType', uploadType);

            try {
                showResult('info', 'Testing simple upload approach...');
                
                console.log('🚀 Testing simple upload approach...');
                console.log('📋 Upload details:', {
                    fileName: selectedFile.name,
                    fileSize: selectedFile.size,
                    fileType: selectedFile.type,
                    uploadType: uploadType
                });

                const startTime = Date.now();
                
                const response = await fetch('http://localhost:3001/simple-upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                const endTime = Date.now();
                const uploadTime = endTime - startTime;

                console.log('📦 Response status:', response.status);
                console.log('⏱️ Upload time:', uploadTime + 'ms');

                const data = await response.json();
                console.log('📦 Upload response:', data);

                if (data.success) {
                    const file = data.file;
                    const isCorrectFolder = file.url.includes(`/${file.folder}/`);
                    
                    showResult('success', 
                        `🎉 SIMPLE UPLOAD SUCCESS!\n\n` +
                        `✅ File Details:\n` +
                        `  - Original Name: ${file.originalName}\n` +
                        `  - Generated Name: ${file.filename}\n` +
                        `  - Size: ${(file.size / 1024 / 1024).toFixed(2)} MB\n` +
                        `  - Type: ${file.type}\n` +
                        `  - Upload Time: ${uploadTime}ms\n\n` +
                        `✅ Storage Details:\n` +
                        `  - Folder: ${file.folder}/\n` +
                        `  - URL: ${file.url}\n` +
                        `  - Correct Folder: ${isCorrectFolder ? 'YES' : 'NO'}\n` +
                        `  - Upload Type: ${file.uploadType}\n\n` +
                        `✅ File ID: ${file.id}\n\n` +
                        `🎯 Simple upload working perfectly!`
                    );
                    
                    console.log('🎉 SIMPLE UPLOAD SUCCESS!');
                    console.log('✅ File saved to correct folder:', isCorrectFolder);
                    console.log('✅ No Payload conflicts');
                    console.log('✅ Direct file system operations working');
                    
                    // Refresh file list
                    getFiles();
                    
                } else {
                    showResult('error', `❌ Simple upload failed: ${data.message}`);
                    console.error('❌ SIMPLE UPLOAD FAILED');
                    console.error('Error:', data.message);
                    console.error('Full response:', data);
                }
            } catch (error) {
                console.error('❌ Upload error:', error);
                showResult('error', `❌ Upload error: ${error.message}`);
            }
        }

        async function getFiles() {
            try {
                const response = await fetch('http://localhost:3001/simple-upload/files', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('📂 Files response:', data);

                if (data.success) {
                    displayFilesList(data.files);
                } else {
                    showResult('error', `Failed to get files: ${data.message}`);
                }
            } catch (error) {
                console.error('Get files error:', error);
                showResult('error', `Error: ${error.message}`);
            }
        }

        function displayFilesList(files) {
            const filesList = document.getElementById('filesList');
            
            if (files.length === 0) {
                filesList.innerHTML = '<p>No files uploaded yet.</p>';
                return;
            }

            let html = `<h4>📁 ${files.length} Uploaded Files:</h4>`;
            files.forEach(file => {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                const uploadDate = new Date(file.createdAt).toLocaleDateString();
                
                html += `
                    <div class="file-item">
                        <div>
                            <strong>${file.filename}</strong><br>
                            <small>Folder: ${file.folder} | Size: ${fileSize} MB | Uploaded: ${uploadDate}</small><br>
                            <small>URL: ${file.url}</small>
                        </div>
                        <div>
                            <button class="btn" onclick="window.open('http://localhost:3001${file.url}', '_blank')">View</button>
                        </div>
                    </div>
                `;
            });
            
            filesList.innerHTML = html;
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 Simple Upload Test loaded');
            console.log('🎯 Testing endpoint: http://localhost:3001/simple-upload');
            console.log('📋 Simple approach benefits:');
            console.log('  - No Payload Media collection conflicts');
            console.log('  - Direct file system operations');
            console.log('  - Minimal dependencies');
            console.log('  - Should work reliably');
            
            showResult('info', 'Ready to test the simple upload approach. Select a file and click "Test Simple Upload".');
            getFiles(); // Load existing files
        });
    </script>
</body>
</html>
