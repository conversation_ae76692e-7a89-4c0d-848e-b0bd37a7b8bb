<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📁 Static File Serving Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .hidden {
            display: none;
        }
        .image-preview {
            margin: 20px 0;
            text-align: center;
        }
        .image-preview img {
            max-width: 300px;
            max-height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📁 Static File Serving Test</h1>
        <p>Test that Payload CMS can now serve files from subfolders like /media/avatars/</p>
        
        <div class="success">
            <strong>✅ Fixed:</strong> Added Express static middleware to Payload config<br>
            - Serves files from media/ directory and all subfolders<br>
            - Proper MIME types and caching headers<br>
            - CORS support for cross-origin requests
        </div>
    </div>

    <div class="container">
        <h3>📁 Upload & Test Static Serving</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image file to upload and test static serving</p>
            <p style="color: #666; font-size: 14px;">Upload using simple endpoint, then test if URL works</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn" onclick="uploadAndTest()" id="uploadBtn" disabled>Upload & Test Static Serving</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        <button class="btn success" onclick="testExistingFiles()">Test Existing Files</button>
        
        <div id="result"></div>
        <div id="imagePreview" class="image-preview"></div>
    </div>

    <div class="container">
        <h3>🔍 Manual URL Tests</h3>
        <p>Test these URLs manually to verify static serving:</p>
        <div class="test-url">
            <strong>Test URL:</strong> http://localhost:3001/media/avatars/Screenshot%202023-06-10%20122948-1752209816761-a3db1a39-6938-4461-966b-f9de9c31e80a.png
        </div>
        <button class="btn" onclick="testSpecificUrl()">Test This Specific URL</button>
        <div id="urlTestResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function uploadAndTest() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showResult('info', 'Step 1: Uploading file using simple upload...');
                
                // Step 1: Upload file
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'avatar');

                const uploadResponse = await fetch('http://localhost:3001/simple-upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                const uploadData = await uploadResponse.json();
                console.log('📦 Upload response:', uploadData);

                if (!uploadData.success) {
                    showResult('error', `Upload failed: ${uploadData.message}`);
                    return;
                }

                const fileUrl = uploadData.file.url;
                showResult('info', `Step 2: File uploaded successfully!\nURL: ${fileUrl}\n\nTesting static serving...`);

                // Step 2: Test if the file can be accessed via static serving
                await testFileAccess(fileUrl, uploadData.file);

            } catch (error) {
                console.error('❌ Upload and test error:', error);
                showResult('error', `Error: ${error.message}`);
            }
        }

        async function testFileAccess(fileUrl, fileInfo) {
            try {
                console.log('🔍 Testing file access:', fileUrl);
                
                const fullUrl = `http://localhost:3001${fileUrl}`;
                
                // Test if file is accessible
                const response = await fetch(fullUrl, {
                    method: 'HEAD', // Use HEAD to check if file exists without downloading
                });

                console.log('📦 Static serving test response:', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                });

                if (response.ok) {
                    showResult('success', 
                        `🎉 STATIC SERVING SUCCESS!\n\n` +
                        `✅ File Upload:\n` +
                        `  - File: ${fileInfo.filename}\n` +
                        `  - Folder: ${fileInfo.folder}/\n` +
                        `  - Size: ${(fileInfo.size / 1024 / 1024).toFixed(2)} MB\n\n` +
                        `✅ Static Serving:\n` +
                        `  - URL: ${fileUrl}\n` +
                        `  - Full URL: ${fullUrl}\n` +
                        `  - Status: ${response.status} ${response.statusText}\n` +
                        `  - Content-Type: ${response.headers.get('content-type') || 'N/A'}\n` +
                        `  - Content-Length: ${response.headers.get('content-length') || 'N/A'}\n\n` +
                        `🎯 File is accessible via static serving!`
                    );

                    // Show image preview if it's an image
                    if (fileInfo.type.startsWith('image/')) {
                        showImagePreview(fullUrl, fileInfo.filename);
                    }
                } else {
                    showResult('error', 
                        `❌ STATIC SERVING FAILED!\n\n` +
                        `File uploaded successfully but not accessible via static serving:\n` +
                        `  - URL: ${fileUrl}\n` +
                        `  - Full URL: ${fullUrl}\n` +
                        `  - Status: ${response.status} ${response.statusText}\n\n` +
                        `This means the Express static middleware is not working correctly.`
                    );
                }
            } catch (error) {
                console.error('❌ Static serving test error:', error);
                showResult('error', `Static serving test failed: ${error.message}`);
            }
        }

        async function testSpecificUrl() {
            const testUrl = 'http://localhost:3001/media/avatars/Screenshot%202023-06-10%20122948-1752209816761-a3db1a39-6938-4461-966b-f9de9c31e80a.png';
            
            try {
                showUrlTestResult('info', 'Testing specific URL...');
                
                const response = await fetch(testUrl, {
                    method: 'HEAD',
                });

                console.log('🔍 Specific URL test:', {
                    url: testUrl,
                    status: response.status,
                    statusText: response.statusText
                });

                if (response.ok) {
                    showUrlTestResult('success', 
                        `✅ Specific URL works!\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `Content-Type: ${response.headers.get('content-type') || 'N/A'}`
                    );
                    
                    // Show image preview
                    showImagePreview(testUrl, 'Existing Screenshot');
                } else {
                    showUrlTestResult('error', 
                        `❌ Specific URL failed!\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `The file might not exist or static serving is not working.`
                    );
                }
            } catch (error) {
                showUrlTestResult('error', `Error testing URL: ${error.message}`);
            }
        }

        async function testExistingFiles() {
            try {
                showResult('info', 'Getting list of existing files...');
                
                const response = await fetch('http://localhost:3001/simple-upload/files', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                
                if (data.success && data.files.length > 0) {
                    showResult('info', `Found ${data.files.length} files. Testing static serving for each...`);
                    
                    // Test the first few files
                    const filesToTest = data.files.slice(0, 3);
                    let results = [];
                    
                    for (const file of filesToTest) {
                        const fullUrl = `http://localhost:3001${file.url}`;
                        
                        try {
                            const testResponse = await fetch(fullUrl, { method: 'HEAD' });
                            results.push(`${file.filename}: ${testResponse.status} ${testResponse.statusText}`);
                        } catch (error) {
                            results.push(`${file.filename}: Error - ${error.message}`);
                        }
                    }
                    
                    showResult('info', 
                        `Static serving test results:\n\n` +
                        results.join('\n') + '\n\n' +
                        `Files tested: ${filesToTest.length} of ${data.files.length} total`
                    );
                } else {
                    showResult('info', 'No existing files found to test.');
                }
            } catch (error) {
                showResult('error', `Error testing existing files: ${error.message}`);
            }
        }

        function showImagePreview(imageUrl, filename) {
            const previewDiv = document.getElementById('imagePreview');
            previewDiv.innerHTML = `
                <h4>🖼️ Image Preview: ${filename}</h4>
                <img src="${imageUrl}" alt="${filename}" onload="console.log('✅ Image loaded successfully')" onerror="console.error('❌ Image failed to load')">
                <p><a href="${imageUrl}" target="_blank">Open in new tab</a></p>
            `;
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showUrlTestResult(type, message) {
            const element = document.getElementById('urlTestResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('📁 Static File Serving Test loaded');
            console.log('🎯 Testing static serving from: http://localhost:3001/media/');
            console.log('📋 Expected behavior:');
            console.log('  1. Upload file to subfolder (e.g., avatars/)');
            console.log('  2. File should be accessible via /media/avatars/filename.jpg');
            console.log('  3. Express static middleware should serve the file');
            
            showResult('info', 'Ready to test static file serving. Upload an image or test existing files.');
        });
    </script>
</body>
</html>
