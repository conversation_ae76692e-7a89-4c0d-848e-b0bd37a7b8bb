<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Storage Service URLs Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .fix-explanation {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Storage Service URLs Test</h1>
        <p>Test that we're using clean URLs from storage service instead of Payload's URLs.</p>
        
        <div class="success">
            <strong>✅ Fixed Approach:</strong> Use storage service URLs directly<br>
            - Storage service generates: /media/file/filename.jpg<br>
            - Payload generates: http://localhost:3001/api/media/file/filename.jpg<br>
            - Solution: Use storage URLs, ignore Payload URLs<br>
            - Result: Clean URLs without domain or /api/ prefix
        </div>
    </div>

    <div class="container">
        <h3>🔄 How the Fix Works</h3>
        <div class="fix-explanation">
            <strong>Smart URL Source Selection:</strong><br><br>
            
            <strong>1. Storage Service:</strong> Generates clean URLs like /media/file/filename.jpg<br>
            <strong>2. Payload Database:</strong> Stores URLs like http://localhost:3001/api/media/file/filename.jpg<br>
            <strong>3. Our Fix:</strong> Use storage service URLs, ignore Payload URLs<br>
            <strong>4. Result:</strong> Response contains only clean URLs<br><br>
            
            <em>By using the right source, we avoid URL cleaning complexity!</em>
        </div>
    </div>

    <div class="container">
        <h3>📁 Test Storage Service URLs</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test storage service URL usage</p>
            <p style="color: #666; font-size: 14px;">Should return clean URLs from storage service</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testStorageServiceUrls()" id="uploadBtn" disabled>Test Storage Service URLs</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testStorageServiceUrls() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showResult('info', 'Testing storage service URL usage...');
                
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'avatar');

                console.log('🚀 Testing storage service URLs');

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeStorageServiceUrls(data);
                } else {
                    showResult('error', `Upload failed: ${data.message}`);
                }

            } catch (error) {
                console.error('❌ Upload error:', error);
                showResult('error', `Upload error: ${error.message}`);
            }
        }

        function analyzeStorageServiceUrls(data) {
            const media = data.media;
            
            if (!media) {
                showResult('error', 'No media object in response');
                return;
            }

            let resultText = `🔧 Storage Service URLs Analysis:\n\n`;
            
            // Analyze main URL
            const mainUrl = media.url;
            const isStorageUrl = !mainUrl.includes('://') && !mainUrl.includes('/api/');
            const hasCorrectFormat = mainUrl.startsWith('/media/file/');
            
            resultText += `📋 Main URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - No domain: ${!mainUrl.includes('://') ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - No /api/ prefix: ${!mainUrl.includes('/api/') ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Storage format: ${hasCorrectFormat ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Using storage URLs: ${isStorageUrl && hasCorrectFormat ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            // Analyze thumbnail URL if present
            let thumbnailIsStorage = true;
            if (media.thumbnailURL) {
                const thumbIsStorage = !media.thumbnailURL.includes('://') && !media.thumbnailURL.includes('/api/');
                const thumbCorrectFormat = media.thumbnailURL.startsWith('/media/file/');
                thumbnailIsStorage = thumbIsStorage && thumbCorrectFormat;
                
                resultText += `🖼️ Thumbnail URL Analysis:\n`;
                resultText += `  - URL: ${media.thumbnailURL}\n`;
                resultText += `  - No domain: ${!media.thumbnailURL.includes('://') ? 'PASS ✅' : 'FAIL ❌'}\n`;
                resultText += `  - No /api/ prefix: ${!media.thumbnailURL.includes('/api/') ? 'PASS ✅' : 'FAIL ❌'}\n`;
                resultText += `  - Storage format: ${thumbCorrectFormat ? 'PASS ✅' : 'FAIL ❌'}\n\n`;
            }
            
            // Analyze sizes
            let allSizesFromStorage = true;
            let sizesAnalysis = '';
            
            if (media.sizes && Object.keys(media.sizes).length > 0) {
                sizesAnalysis += `📐 Size URLs Analysis:\n`;
                
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeIsStorage = !sizeData.url.includes('://') && !sizeData.url.includes('/api/');
                        const sizeCorrectFormat = sizeData.url.startsWith('/media/file/');
                        const sizeFromStorage = sizeIsStorage && sizeCorrectFormat;
                        
                        if (!sizeFromStorage) allSizesFromStorage = false;
                        
                        sizesAnalysis += `  - ${sizeName}: ${sizeData.url}\n`;
                        sizesAnalysis += `    No domain: ${!sizeData.url.includes('://') ? '✅' : '❌'}\n`;
                        sizesAnalysis += `    No /api/: ${!sizeData.url.includes('/api/') ? '✅' : '❌'}\n`;
                        sizesAnalysis += `    Storage format: ${sizeCorrectFormat ? '✅' : '❌'}\n`;
                    }
                });
                sizesAnalysis += `\n`;
            } else {
                sizesAnalysis += `📐 No size URLs generated\n\n`;
            }
            
            resultText += sizesAnalysis;
            
            // Overall assessment
            const usingStorageUrls = isStorageUrl && hasCorrectFormat && thumbnailIsStorage && allSizesFromStorage;
            
            resultText += `🎯 Overall Assessment:\n`;
            resultText += `  - Main URL from storage: ${isStorageUrl && hasCorrectFormat ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Thumbnail URL from storage: ${thumbnailIsStorage ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - All size URLs from storage: ${allSizesFromStorage ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Storage service URLs working: ${usingStorageUrls ? 'PERFECT ✅' : 'INCOMPLETE ❌'}\n\n`;
            
            if (usingStorageUrls) {
                resultText += `🎉 STORAGE SERVICE URLs SUCCESS!\n`;
                resultText += `✅ All URLs are from storage service!\n`;
                resultText += `✅ No Payload URLs with domains!\n`;
                resultText += `🎯 Clean /media/file/ URLs throughout!\n`;
                resultText += `🌐 Ready for direct file serving!`;
                showResult('success', resultText);
            } else {
                resultText += `⚠️ Storage service URLs incomplete:\n`;
                if (!isStorageUrl || !hasCorrectFormat) resultText += `  - Main URL not from storage service\n`;
                if (!thumbnailIsStorage) resultText += `  - Thumbnail URL not from storage service\n`;
                if (!allSizesFromStorage) resultText += `  - Some size URLs not from storage service\n`;
                resultText += `❌ Still using Payload URLs instead of storage URLs`;
                showResult('error', resultText);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Storage Service URLs Test loaded');
            console.log('🎯 Testing that we use storage service URLs instead of Payload URLs');
            console.log('📋 Should return /media/file/ URLs from storage service');
            
            showResult('info', 'Ready to test storage service URLs. Select an image and click "Test Storage Service URLs".');
        });
    </script>
</body>
</html>
