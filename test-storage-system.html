<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Flexible Storage System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.danger {
            background-color: #dc3545;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Flexible Storage System Test</h1>
        <p>Test the new file upload system that can switch between local storage and S3 based on configuration.</p>
        
        <div class="info">
            <strong>Token:</strong> eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg
        </div>
    </div>

    <div class="grid">
        <!-- Storage Configuration Section -->
        <div class="container">
            <div class="section">
                <h3>📋 Storage Configuration</h3>
                <div class="form-group">
                    <label>Storage Provider:</label>
                    <select id="storageProvider">
                        <option value="local">Local Storage</option>
                        <option value="s3">Amazon S3</option>
                    </select>
                </div>
                <div id="s3Config" class="hidden">
                    <div class="form-group">
                        <label>S3 Bucket:</label>
                        <input type="text" id="s3Bucket" placeholder="my-bucket-name">
                    </div>
                    <div class="form-group">
                        <label>S3 Access Key:</label>
                        <input type="text" id="s3AccessKey" placeholder="AKIAIOSFODNN7EXAMPLE">
                    </div>
                    <div class="form-group">
                        <label>S3 Secret Key:</label>
                        <input type="password" id="s3SecretKey" placeholder="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY">
                    </div>
                    <div class="form-group">
                        <label>S3 Region:</label>
                        <input type="text" id="s3Region" value="us-east-1">
                    </div>
                </div>
                <button class="btn" onclick="updateStorageConfig()">Update Storage Config</button>
                <button class="btn" onclick="getStorageConfig()">Get Current Config</button>
                <div id="configResult"></div>
            </div>
        </div>

        <!-- Avatar Upload Section -->
        <div class="container">
            <div class="section">
                <h3>👤 Avatar Upload Test</h3>
                <div class="upload-area" id="avatarUploadArea">
                    <p>📁 Drag & drop avatar image here or click to select</p>
                    <p style="color: #666; font-size: 14px;">Max 5MB, Images only</p>
                    <input type="file" id="avatarFileInput" accept="image/*" class="hidden">
                </div>
                <button class="btn" onclick="uploadAvatar()" id="uploadAvatarBtn" disabled>Upload Avatar</button>
                <button class="btn" onclick="getCurrentAvatar()">Get Current Avatar</button>
                <button class="btn danger" onclick="removeAvatar()">Remove Avatar</button>
                <div id="avatarResult"></div>
            </div>
        </div>
    </div>

    <!-- Settings Management Section -->
    <div class="container">
        <div class="section">
            <h3>⚙️ Settings Management</h3>
            <button class="btn" onclick="getStorageSettings()">Get Storage Settings</button>
            <button class="btn" onclick="createStorageSettings()">Create Default Storage Settings</button>
            <button class="btn success" onclick="getAllSettings()">Get All Settings</button>
            <div id="settingsResult"></div>
        </div>
    </div>

    <script>
        const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedAvatarFile = null;

        // Storage provider change handler
        document.getElementById('storageProvider').addEventListener('change', function() {
            const s3Config = document.getElementById('s3Config');
            if (this.value === 's3') {
                s3Config.classList.remove('hidden');
            } else {
                s3Config.classList.add('hidden');
            }
        });

        // Avatar file selection
        const avatarFileInput = document.getElementById('avatarFileInput');
        const avatarUploadArea = document.getElementById('avatarUploadArea');
        
        avatarUploadArea.addEventListener('click', () => avatarFileInput.click());
        avatarFileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedAvatarFile = e.target.files[0];
                document.getElementById('uploadAvatarBtn').disabled = false;
                showResult('avatarResult', 'info', `Selected: ${selectedAvatarFile.name}`);
            }
        });

        // Storage configuration functions
        async function updateStorageConfig() {
            const provider = document.getElementById('storageProvider').value;
            const settings = { storage_provider: provider };
            
            if (provider === 's3') {
                settings.s3_bucket = document.getElementById('s3Bucket').value;
                settings.s3_access_key = document.getElementById('s3AccessKey').value;
                settings.s3_secret_key = document.getElementById('s3SecretKey').value;
                settings.s3_region = document.getElementById('s3Region').value;
            }

            try {
                // Update storage_provider setting
                const response = await fetch(`http://localhost:3001/platform/settings/storage_provider`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        value: provider,
                        category: 'storage',
                        type: 'string'
                    }),
                });

                const result = await response.json();
                showResult('configResult', result.success ? 'success' : 'error', 
                    `Storage provider updated: ${JSON.stringify(result, null, 2)}`);

                // Update S3 settings if needed
                if (provider === 's3') {
                    for (const [key, value] of Object.entries(settings)) {
                        if (key !== 'storage_provider' && value) {
                            await updateSetting(key, value, 'storage');
                        }
                    }
                }
            } catch (error) {
                showResult('configResult', 'error', `Error: ${error.message}`);
            }
        }

        async function updateSetting(key, value, category) {
            try {
                const response = await fetch(`http://localhost:3001/platform/settings/${key}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        value: value,
                        category: category,
                        type: 'string'
                    }),
                });
                return await response.json();
            } catch (error) {
                console.error(`Failed to update ${key}:`, error);
            }
        }

        async function getStorageConfig() {
            try {
                const response = await fetch(`http://localhost:3001/platform/settings/category/storage`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const result = await response.json();
                showResult('configResult', result.success ? 'success' : 'error', 
                    `Current storage config: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                showResult('configResult', 'error', `Error: ${error.message}`);
            }
        }

        // Avatar upload functions
        async function uploadAvatar() {
            if (!selectedAvatarFile) {
                showResult('avatarResult', 'error', 'Please select a file first');
                return;
            }

            const formData = new FormData();
            formData.append('file', selectedAvatarFile);

            try {
                const response = await fetch('http://localhost:3001/api/super-admin/avatar/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                    body: formData,
                });

                const result = await response.json();
                showResult('avatarResult', result.success ? 'success' : 'error', 
                    `Avatar upload result: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                showResult('avatarResult', 'error', `Error: ${error.message}`);
            }
        }

        async function getCurrentAvatar() {
            try {
                const response = await fetch('http://localhost:3001/api/super-admin/avatar/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const result = await response.json();
                showResult('avatarResult', result.success ? 'success' : 'error', 
                    `Current avatar: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                showResult('avatarResult', 'error', `Error: ${error.message}`);
            }
        }

        async function removeAvatar() {
            try {
                const response = await fetch('http://localhost:3001/api/super-admin/avatar/me', {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const result = await response.json();
                showResult('avatarResult', result.success ? 'success' : 'error', 
                    `Remove avatar result: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                showResult('avatarResult', 'error', `Error: ${error.message}`);
            }
        }

        // Settings management functions
        async function getStorageSettings() {
            try {
                const response = await fetch('http://localhost:3001/platform/settings/category/storage', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const result = await response.json();
                showResult('settingsResult', result.success ? 'success' : 'error', 
                    `Storage settings: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                showResult('settingsResult', 'error', `Error: ${error.message}`);
            }
        }

        async function createStorageSettings() {
            const defaultSettings = [
                { key: 'storage_provider', value: 'local', category: 'storage', type: 'string', description: 'Storage provider (local or s3)' },
                { key: 's3_bucket', value: '', category: 'storage', type: 'string', description: 'S3 bucket name' },
                { key: 's3_access_key', value: '', category: 'storage', type: 'string', description: 'S3 access key' },
                { key: 's3_secret_key', value: '', category: 'storage', type: 'string', description: 'S3 secret key' },
                { key: 's3_region', value: 'us-east-1', category: 'storage', type: 'string', description: 'S3 region' },
            ];

            let results = [];
            for (const setting of defaultSettings) {
                try {
                    const response = await fetch('http://localhost:3001/platform/settings', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            ...setting,
                            is_public: false,
                            is_required: setting.key === 'storage_provider'
                        }),
                    });

                    const result = await response.json();
                    results.push(`${setting.key}: ${result.success ? 'Created' : result.message}`);
                } catch (error) {
                    results.push(`${setting.key}: Error - ${error.message}`);
                }
            }

            showResult('settingsResult', 'info', `Default settings creation:\n${results.join('\n')}`);
        }

        async function getAllSettings() {
            try {
                const response = await fetch('http://localhost:3001/platform/settings', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                const result = await response.json();
                showResult('settingsResult', result.success ? 'success' : 'error', 
                    `All settings: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                showResult('settingsResult', 'error', `Error: ${error.message}`);
            }
        }

        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 Storage System Test loaded');
            getStorageConfig();
        });
    </script>
</body>
</html>
