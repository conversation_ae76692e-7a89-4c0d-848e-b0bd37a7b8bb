<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔑 Token Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 Token Fix Test</h1>
        <p>Test that the file upload API can properly get the authentication token.</p>
        
        <div class="info">
            <strong>Test Token:</strong> eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg
        </div>
    </div>

    <div class="container">
        <h3>🔧 Token Management</h3>
        <button class="btn" onclick="setToken()">Set Test Token</button>
        <button class="btn" onclick="checkToken()">Check Token</button>
        <button class="btn" onclick="clearToken()">Clear Token</button>
        <div id="tokenResult"></div>
    </div>

    <div class="container">
        <h3>📁 File Upload Test</h3>
        <div class="upload-area" id="uploadArea">
            <p>📁 Select a file to test upload with token</p>
            <input type="file" id="fileInput" class="hidden">
        </div>
        <button class="btn" onclick="testUpload()" id="uploadBtn" disabled>Test Upload</button>
        <div id="uploadResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('uploadResult', 'info', `Selected: ${selectedFile.name}`);
            }
        });

        // Token management functions
        function setToken() {
            try {
                // Set token in localStorage (both ways for compatibility)
                localStorage.setItem('auth_token', testToken);
                
                // Also set in Zustand format
                const authStorage = {
                    state: {
                        token: testToken,
                        user: { id: 6, email: '<EMAIL>' }
                    },
                    version: 0
                };
                localStorage.setItem('auth-storage', JSON.stringify(authStorage));
                
                showResult('tokenResult', 'success', '✅ Test token set successfully');
                console.log('🔑 Token set:', {
                    directToken: localStorage.getItem('auth_token'),
                    zustandToken: localStorage.getItem('auth-storage')
                });
            } catch (error) {
                showResult('tokenResult', 'error', `❌ Failed to set token: ${error.message}`);
            }
        }

        function checkToken() {
            try {
                // Check direct token
                const directToken = localStorage.getItem('auth_token');
                
                // Check Zustand token
                let zustandToken = null;
                try {
                    const authStorage = localStorage.getItem('auth-storage');
                    if (authStorage) {
                        const parsed = JSON.parse(authStorage);
                        zustandToken = parsed?.state?.token || null;
                    }
                } catch (error) {
                    console.error('Failed to parse auth storage:', error);
                }

                const result = {
                    directToken: directToken ? `${directToken.substring(0, 20)}...` : 'Not found',
                    zustandToken: zustandToken ? `${zustandToken.substring(0, 20)}...` : 'Not found',
                    hasDirectToken: !!directToken,
                    hasZustandToken: !!zustandToken
                };

                showResult('tokenResult', 'info', `Token Status:\n${JSON.stringify(result, null, 2)}`);
                console.log('🔍 Token check:', result);
            } catch (error) {
                showResult('tokenResult', 'error', `❌ Failed to check token: ${error.message}`);
            }
        }

        function clearToken() {
            try {
                localStorage.removeItem('auth_token');
                localStorage.removeItem('auth-storage');
                showResult('tokenResult', 'info', '🗑️ Tokens cleared');
            } catch (error) {
                showResult('tokenResult', 'error', `❌ Failed to clear token: ${error.message}`);
            }
        }

        // Test upload function
        async function testUpload() {
            if (!selectedFile) {
                showResult('uploadResult', 'error', 'Please select a file first');
                return;
            }

            try {
                showResult('uploadResult', 'info', 'Testing upload with token...');

                // Test the token retrieval function (same as in file-upload.ts)
                const getAuthToken = () => {
                    if (typeof window !== 'undefined') {
                        // First try direct auth_token
                        let token = localStorage.getItem('auth_token');

                        // If not found, try Zustand auth storage
                        if (!token) {
                            try {
                                const authStorage = localStorage.getItem('auth-storage');
                                if (authStorage) {
                                    const parsed = JSON.parse(authStorage);
                                    token = parsed?.state?.token || null;
                                }
                            } catch (error) {
                                console.error('Failed to parse auth storage:', error);
                            }
                        }

                        return token;
                    }
                    return null;
                };

                const token = getAuthToken();
                if (!token) {
                    throw new Error('No authentication token found. Please set token first.');
                }

                console.log('🔑 Using token:', `${token.substring(0, 20)}...`);

                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'avatar');
                formData.append('updateUserField', 'avatar');

                const response = await fetch('http://localhost:3001/api/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                    body: formData,
                });

                const data = await response.json();
                console.log('📦 Upload response:', data);

                if (data.success) {
                    showResult('uploadResult', 'success', `✅ Upload successful!\n\nMedia ID: ${data.media.id}\nURL: ${data.media.url}\nType: ${data.media.mediaType}`);
                } else {
                    showResult('uploadResult', 'error', `❌ Upload failed: ${data.message}`);
                }
            } catch (error) {
                console.error('Upload error:', error);
                showResult('uploadResult', 'error', `❌ Upload error: ${error.message}`);
            }
        }

        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🚀 Token Fix Test loaded');
            checkToken();
        });
    </script>
</body>
</html>
