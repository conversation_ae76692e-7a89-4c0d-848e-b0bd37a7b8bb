<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Upload Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.debug {
            background-color: #dc3545;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .debug-info {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Upload Debug Test</h1>
        <p>Debug test to see if our upload middleware is working correctly.</p>
        
        <div class="debug-info">
            <strong>🔍 Debug Purpose:</strong><br>
            - Test if upload middleware is being called<br>
            - Check what URLs are being generated<br>
            - Verify if the issue is in middleware or elsewhere<br>
            - Compare regular upload vs debug upload
        </div>
    </div>

    <div class="container">
        <h3>📁 Upload Tests</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image for debug testing</p>
            <p style="color: #666; font-size: 14px;">Will test both regular and debug endpoints</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testRegularUpload()" id="regularBtn" disabled>Test Regular Upload (/upload)</button>
        <button class="btn debug" onclick="testDebugUpload()" id="debugBtn" disabled>Test Debug Upload (/test-upload-debug)</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🔍 Debug Analysis</h3>
        <p>Compare the results from both endpoints:</p>
        
        <div id="comparison"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;
        let regularResult = null;
        let debugResult = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('regularBtn').disabled = false;
                document.getElementById('debugBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testRegularUpload() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showResult('info', 'Testing regular upload endpoint...');
                
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'avatar');

                console.log('🚀 Testing regular upload endpoint: /upload');

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Regular upload response status:', response.status);
                const data = await response.json();
                console.log('📦 Regular upload response data:', data);

                regularResult = data;
                analyzeUploadResult(data, 'Regular Upload (/upload)');
                updateComparison();

            } catch (error) {
                console.error('❌ Regular upload error:', error);
                showResult('error', `Regular upload error: ${error.message}`);
            }
        }

        async function testDebugUpload() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showResult('info', 'Testing debug upload endpoint...');
                
                const formData = new FormData();
                formData.append('file', selectedFile);

                console.log('🚀 Testing debug upload endpoint: /test-upload-debug');

                const response = await fetch('http://localhost:3001/test-upload-debug', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Debug upload response status:', response.status);
                const data = await response.json();
                console.log('📦 Debug upload response data:', data);

                debugResult = data;
                analyzeUploadResult(data, 'Debug Upload (/test-upload-debug)');
                updateComparison();

            } catch (error) {
                console.error('❌ Debug upload error:', error);
                showResult('error', `Debug upload error: ${error.message}`);
            }
        }

        function analyzeUploadResult(data, testName) {
            if (!data.success) {
                showResult('error', `${testName} failed: ${data.message}`);
                return;
            }

            const media = data.media;
            if (!media) {
                showResult('error', `${testName}: No media object in response`);
                return;
            }

            let resultText = `🔧 ${testName} Analysis:\n\n`;
            
            // Analyze main URL
            const mainUrl = media.url;
            const hasApiPrefix = mainUrl?.includes('/api/');
            const hasDomain = mainUrl?.includes('://');
            const isClean = !hasApiPrefix && !hasDomain;
            
            resultText += `📋 Main URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - Has domain: ${hasDomain ? 'YES ❌' : 'NO ✅'}\n`;
            resultText += `  - Has /api/ prefix: ${hasApiPrefix ? 'YES ❌' : 'NO ✅'}\n`;
            resultText += `  - Is clean: ${isClean ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            // Analyze sizes
            let sizesAnalysis = '';
            if (media.sizes && Object.keys(media.sizes).length > 0) {
                sizesAnalysis += `📐 Size URLs Analysis:\n`;
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeHasApi = sizeData.url.includes('/api/');
                        const sizeHasDomain = sizeData.url.includes('://');
                        sizesAnalysis += `  - ${sizeName}: ${sizeData.url}\n`;
                        sizesAnalysis += `    Has domain: ${sizeHasDomain ? 'YES ❌' : 'NO ✅'}\n`;
                        sizesAnalysis += `    Has /api/: ${sizeHasApi ? 'YES ❌' : 'NO ✅'}\n`;
                    }
                });
                sizesAnalysis += `\n`;
            }
            
            resultText += sizesAnalysis;
            
            // Debug info if available
            if (data.analysis) {
                resultText += `🔍 Debug Analysis:\n`;
                resultText += `  - URL has domain: ${data.analysis.urlHasDomain ? 'YES ❌' : 'NO ✅'}\n`;
                resultText += `  - URL has /api/: ${data.analysis.urlHasApiPrefix ? 'YES ❌' : 'NO ✅'}\n`;
                resultText += `  - Is clean URL: ${data.analysis.isCleanUrl ? 'YES ✅' : 'NO ❌'}\n`;
                resultText += `  - Sizes count: ${data.analysis.sizesCount}\n\n`;
            }
            
            if (isClean) {
                resultText += `🎉 ${testName} SUCCESS!\n`;
                resultText += `✅ URLs are clean and working correctly!`;
                showResult('success', resultText);
            } else {
                resultText += `⚠️ ${testName} has URL issues:\n`;
                if (hasDomain) resultText += `  - URLs contain domain\n`;
                if (hasApiPrefix) resultText += `  - URLs contain /api/ prefix\n`;
                resultText += `❌ Middleware may not be working correctly`;
                showResult('error', resultText);
            }
        }

        function updateComparison() {
            if (!regularResult || !debugResult) return;

            const comparisonElement = document.getElementById('comparison');
            
            let comparisonText = `🔍 Comparison Results:\n\n`;
            
            comparisonText += `📊 Regular Upload (/upload):\n`;
            comparisonText += `  - URL: ${regularResult.media?.url}\n`;
            comparisonText += `  - Has domain: ${regularResult.media?.url?.includes('://') ? 'YES ❌' : 'NO ✅'}\n`;
            comparisonText += `  - Has /api/: ${regularResult.media?.url?.includes('/api/') ? 'YES ❌' : 'NO ✅'}\n\n`;
            
            comparisonText += `📊 Debug Upload (/test-upload-debug):\n`;
            comparisonText += `  - URL: ${debugResult.media?.url}\n`;
            comparisonText += `  - Has domain: ${debugResult.media?.url?.includes('://') ? 'YES ❌' : 'NO ✅'}\n`;
            comparisonText += `  - Has /api/: ${debugResult.media?.url?.includes('/api/') ? 'YES ❌' : 'NO ✅'}\n\n`;
            
            const regularClean = !regularResult.media?.url?.includes('://') && !regularResult.media?.url?.includes('/api/');
            const debugClean = !debugResult.media?.url?.includes('://') && !debugResult.media?.url?.includes('/api/');
            
            comparisonText += `🎯 Conclusion:\n`;
            if (regularClean && debugClean) {
                comparisonText += `✅ Both endpoints return clean URLs - middleware is working!`;
            } else if (!regularClean && !debugClean) {
                comparisonText += `❌ Both endpoints return dirty URLs - middleware issue!`;
            } else if (regularClean && !debugClean) {
                comparisonText += `⚠️ Regular works, debug doesn't - debug endpoint issue!`;
            } else {
                comparisonText += `⚠️ Debug works, regular doesn't - regular endpoint issue!`;
            }

            comparisonElement.innerHTML = `<div class="info">${comparisonText}</div>`;
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Upload Debug Test loaded');
            console.log('🎯 Testing both regular and debug upload endpoints');
            console.log('📋 Will help identify where the URL issue is coming from');
            
            showResult('info', 'Ready to debug upload. Select an image and test both endpoints.');
        });
    </script>
</body>
</html>
