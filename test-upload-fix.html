<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Upload Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Upload Fix Test</h1>
        <p>Test that the upload issues are fixed and files upload successfully.</p>
        
        <div class="success">
            <strong>✅ Fixed Issues:</strong><br>
            - Simplified media record creation (using fallback approach)<br>
            - Prevented database update errors<br>
            - File upload still works correctly<br>
            - Clean URLs returned without /api/ prefix
        </div>
    </div>

    <div class="container">
        <h3>📁 Test File Upload</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select an image to test the fixed upload</p>
            <p style="color: #666; font-size: 14px;">Should upload successfully without database errors</p>
            <input type="file" id="fileInput" accept="image/*" class="hidden">
        </div>

        <button class="btn success" onclick="testUpload()" id="uploadBtn" disabled>Test Upload</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🧪 Test Different Upload Types</h3>
        <p>Test various upload types to ensure all work correctly:</p>
        
        <button class="btn" onclick="testUploadType('avatar')">Test Avatar Upload</button>
        <button class="btn" onclick="testUploadType('course_thumbnail')">Test Course Thumbnail</button>
        <button class="btn" onclick="testUploadType('document')">Test Document Upload</button>
        
        <div id="typeTestResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function testUpload() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, 'avatar', 'Upload Fix Test');
        }

        async function testUploadType(uploadType) {
            if (!selectedFile) {
                showTypeTestResult('error', 'Please select a file first');
                return;
            }

            await testUploadWithFile(selectedFile, uploadType, `${uploadType} Upload Test`, true);
        }

        async function testUploadWithFile(file, uploadType, testName, useTypeResult = false) {
            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                const showResultFunc = useTypeResult ? showTypeTestResult : showResult;
                showResultFunc('info', `Testing ${testName}...`);
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('uploadType', uploadType);

                console.log(`🚀 Testing ${testName}:`, {
                    fileName: file.name,
                    uploadType: uploadType
                });

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeUploadResult(data, testName, showResultFunc);
                } else {
                    showResultFunc('error', `${testName} failed: ${data.message}`);
                }

            } catch (error) {
                console.error(`❌ ${testName} error:`, error);
                const showResultFunc = useTypeResult ? showTypeTestResult : showResult;
                showResultFunc('error', `${testName} error: ${error.message}`);
            }
        }

        function analyzeUploadResult(data, testName, showResultFunc) {
            const media = data.media;
            
            if (!media) {
                showResultFunc('error', `No media object in ${testName} response`);
                return;
            }

            let resultText = `🎉 ${testName} SUCCESS!\n\n`;
            
            // Analyze response
            resultText += `📋 Upload Results:\n`;
            resultText += `  - Success: ${data.success ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - Message: ${data.message}\n`;
            resultText += `  - Media ID: ${media.id}\n`;
            resultText += `  - Filename: ${media.filename}\n`;
            resultText += `  - File Size: ${(media.filesize / 1024 / 1024).toFixed(2)} MB\n`;
            resultText += `  - MIME Type: ${media.mimeType}\n\n`;
            
            // Analyze URL
            const mainUrl = media.url;
            const urlClean = !mainUrl.includes('/api/');
            const urlStartsWithMedia = mainUrl.startsWith('/media/');
            
            resultText += `🔗 URL Analysis:\n`;
            resultText += `  - URL: ${mainUrl}\n`;
            resultText += `  - No /api/ prefix: ${urlClean ? 'PASS ✅' : 'FAIL ❌'}\n`;
            resultText += `  - Starts with /media/: ${urlStartsWithMedia ? 'PASS ✅' : 'FAIL ❌'}\n\n`;
            
            // Analyze sizes if available
            if (media.sizes && Object.keys(media.sizes).length > 0) {
                resultText += `📐 Generated Sizes:\n`;
                Object.entries(media.sizes).forEach(([sizeName, sizeData]) => {
                    if (sizeData && sizeData.url) {
                        const sizeUrlClean = !sizeData.url.includes('/api/');
                        resultText += `  - ${sizeName}: ${sizeData.url} ${sizeUrlClean ? '✅' : '❌'}\n`;
                    }
                });
                resultText += `\n`;
            }
            
            // Overall assessment
            const uploadWorking = data.success && media.id && media.filename;
            const urlsClean = urlClean && urlStartsWithMedia;
            
            resultText += `🎯 Overall Assessment:\n`;
            resultText += `  - Upload working: ${uploadWorking ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - URLs clean: ${urlsClean ? 'YES ✅' : 'NO ❌'}\n`;
            resultText += `  - No database errors: ${data.success ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            if (uploadWorking && urlsClean) {
                resultText += `🎉 PERFECT! Upload is working correctly!\n`;
                resultText += `✅ All issues have been resolved.`;
                showResultFunc('success', resultText);
            } else {
                resultText += `⚠️ Some issues may remain.\n`;
                resultText += `❌ Check the details above.`;
                showResultFunc('error', resultText);
            }
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showTypeTestResult(type, message) {
            const element = document.getElementById('typeTestResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔧 Upload Fix Test loaded');
            console.log('🎯 Testing that upload issues are fixed');
            console.log('📋 Should upload files successfully without database errors');
            
            showResult('info', 'Ready to test upload fix. Select an image and click "Test Upload".');
        });
    </script>
</body>
</html>
