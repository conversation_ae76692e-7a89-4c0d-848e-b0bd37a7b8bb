<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 URL Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.danger {
            background-color: #dc3545;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .url-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .url-box {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid;
        }
        .url-box.wrong {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .url-box.correct {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .image-preview {
            margin: 20px 0;
            text-align: center;
        }
        .image-preview img {
            max-width: 300px;
            max-height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 URL Fix Test</h1>
        <p>Test that the frontend now generates correct media URLs without the extra /api prefix.</p>
        
        <div class="success">
            <strong>✅ Fixed:</strong> Updated getFileUrl function to use direct backend URL<br>
            - Bypasses Next.js rewrite rules that add /api<br>
            - Generates correct media URLs for images<br>
            - Should work with existing uploaded files
        </div>
    </div>

    <div class="container">
        <h3>🔍 URL Comparison</h3>
        <div class="url-comparison">
            <div class="url-box wrong">
                <h4>❌ Wrong URL (with /api)</h4>
                <div class="test-url">
                    http://localhost:3001/api/media/avatars/Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png
                </div>
                <p><small>This URL has extra /api and returns 404</small></p>
            </div>
            <div class="url-box correct">
                <h4>✅ Correct URL (without /api)</h4>
                <div class="test-url">
                    http://localhost:3001/media/avatars/Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png
                </div>
                <p><small>This URL should work correctly</small></p>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>🧪 Test Both URLs</h3>
        <p>Test both the wrong and correct URLs to verify the fix:</p>
        
        <button class="btn danger" onclick="testWrongUrl()">Test Wrong URL (with /api)</button>
        <button class="btn success" onclick="testCorrectUrl()">Test Correct URL (without /api)</button>
        <button class="btn" onclick="testBothUrls()">Test Both URLs</button>
        
        <div id="testResult"></div>
        <div id="imagePreview" class="image-preview"></div>
    </div>

    <div class="container">
        <h3>📱 Frontend Integration Test</h3>
        <p>Test how the frontend getFileUrl function works now:</p>
        
        <button class="btn success" onclick="testGetFileUrl()">Test getFileUrl Function</button>
        <div id="frontendResult"></div>
    </div>

    <script>
        const wrongUrl = 'http://localhost:3001/api/media/avatars/Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png';
        const correctUrl = 'http://localhost:3001/media/avatars/Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png';

        async function testWrongUrl() {
            await testUrl(wrongUrl, 'Wrong URL (with /api)', 'error');
        }

        async function testCorrectUrl() {
            await testUrl(correctUrl, 'Correct URL (without /api)', 'success');
        }

        async function testBothUrls() {
            showTestResult('info', 'Testing both URLs...\n\n');
            
            const wrongResult = await testUrlSilent(wrongUrl);
            const correctResult = await testUrlSilent(correctUrl);
            
            let resultText = '📊 URL Comparison Results:\n\n';
            
            resultText += `❌ Wrong URL (with /api):\n`;
            resultText += `   URL: ${wrongUrl}\n`;
            resultText += `   Status: ${wrongResult.status} ${wrongResult.statusText}\n`;
            resultText += `   Working: ${wrongResult.success ? 'YES' : 'NO'}\n\n`;
            
            resultText += `✅ Correct URL (without /api):\n`;
            resultText += `   URL: ${correctUrl}\n`;
            resultText += `   Status: ${correctResult.status} ${correctResult.statusText}\n`;
            resultText += `   Working: ${correctResult.success ? 'YES' : 'NO'}\n\n`;
            
            if (correctResult.success && !wrongResult.success) {
                resultText += `🎉 SUCCESS! The fix is working:\n`;
                resultText += `   - Wrong URL returns 404 (as expected)\n`;
                resultText += `   - Correct URL returns 200 (fixed!)\n`;
                showTestResult('success', resultText);
                
                // Show image preview
                showImagePreview(correctUrl, 'Fixed URL');
            } else if (wrongResult.success && correctResult.success) {
                resultText += `⚠️ Both URLs work - this is unexpected\n`;
                showTestResult('info', resultText);
            } else if (!wrongResult.success && !correctResult.success) {
                resultText += `❌ Neither URL works - there might be another issue\n`;
                showTestResult('error', resultText);
            } else {
                resultText += `🤔 Unexpected result pattern\n`;
                showTestResult('info', resultText);
            }
        }

        async function testUrl(url, description, expectedType) {
            try {
                showTestResult('info', `Testing ${description}...\n\nURL: ${url}`);
                
                const response = await fetch(url, {
                    method: 'HEAD',
                });

                console.log(`🔍 ${description} test:`, {
                    url,
                    status: response.status,
                    statusText: response.statusText
                });

                if (response.ok) {
                    const contentType = response.headers.get('content-type') || 'unknown';
                    const contentLength = response.headers.get('content-length') || 'unknown';
                    
                    showTestResult('success', 
                        `✅ ${description} works!\n\n` +
                        `URL: ${url}\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `Content-Type: ${contentType}\n` +
                        `Content-Length: ${contentLength} bytes\n\n` +
                        `${expectedType === 'success' ? '🎉 This is the expected result!' : '⚠️ This URL should not work!'}`
                    );
                    
                    // Show image preview if it's an image and we expect it to work
                    if (contentType.startsWith('image/') && expectedType === 'success') {
                        showImagePreview(url, description);
                    }
                } else {
                    showTestResult(expectedType === 'error' ? 'success' : 'error', 
                        `${expectedType === 'error' ? '✅' : '❌'} ${description} ${expectedType === 'error' ? 'correctly' : 'unexpectedly'} failed!\n\n` +
                        `URL: ${url}\n` +
                        `Status: ${response.status} ${response.statusText}\n\n` +
                        `${expectedType === 'error' ? '🎯 This is expected - the wrong URL should return 404!' : '❌ This URL should work but it doesn\'t!'}`
                    );
                }
            } catch (error) {
                console.error(`❌ ${description} test error:`, error);
                showTestResult('error', `❌ ${description} test error: ${error.message}`);
            }
        }

        async function testUrlSilent(url) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                return {
                    success: response.ok,
                    status: response.status,
                    statusText: response.statusText,
                    contentType: response.headers.get('content-type')
                };
            } catch (error) {
                return {
                    success: false,
                    status: 'Error',
                    statusText: error.message,
                    contentType: null
                };
            }
        }

        function testGetFileUrl() {
            // Simulate the frontend getFileUrl function
            const testPath = '/media/avatars/Screenshot%202023-06-10%20123201-1752211977516-15bc4678-6865-47b7-b3b6-2de8e82db206.png';
            
            // Old behavior (wrong)
            const oldBaseUrl = 'http://localhost:3001';
            const oldUrl = `${oldBaseUrl}${testPath}`;
            
            // New behavior (fixed)
            const backendUrl = 'http://localhost:3001'; // process.env.NEXT_PUBLIC_API_URL
            const newUrl = `${backendUrl}${testPath}`;
            
            let resultText = '📱 Frontend getFileUrl Function Test:\n\n';
            resultText += `Input path: ${testPath}\n\n`;
            resultText += `Old behavior (before fix):\n`;
            resultText += `  - Would go through Next.js rewrites\n`;
            resultText += `  - Result: ${oldUrl} → gets rewritten to /api/media/...\n`;
            resultText += `  - Final URL: http://localhost:3001/api${testPath} ❌\n\n`;
            resultText += `New behavior (after fix):\n`;
            resultText += `  - Direct backend URL construction\n`;
            resultText += `  - Result: ${newUrl}\n`;
            resultText += `  - No rewrite interference ✅\n\n`;
            
            if (oldUrl === newUrl) {
                resultText += `✅ URLs are the same - the fix maintains correct behavior\n`;
                resultText += `🎯 The key difference is that this bypasses Next.js rewrites`;
            } else {
                resultText += `⚠️ URLs are different - this might indicate an issue`;
            }
            
            showFrontendResult('success', resultText);
        }

        function showImagePreview(imageUrl, description) {
            const previewDiv = document.getElementById('imagePreview');
            previewDiv.innerHTML = `
                <h4>🖼️ Image Preview: ${description}</h4>
                <img src="${imageUrl}" alt="${description}" 
                     onload="console.log('✅ Image loaded successfully')" 
                     onerror="console.error('❌ Image failed to load')">
                <p><a href="${imageUrl}" target="_blank">Open in new tab</a></p>
            `;
        }

        function showTestResult(type, message) {
            const element = document.getElementById('testResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showFrontendResult(type, message) {
            const element = document.getElementById('frontendResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔗 URL Fix Test loaded');
            console.log('🎯 Testing URL generation fix');
            console.log('📋 The fix should prevent /api from being added to media URLs');
            
            showTestResult('info', 'Ready to test URL fix. Click "Test Both URLs" to verify the fix is working.');
        });
    </script>
</body>
</html>
