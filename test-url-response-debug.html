<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 URL Response Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .btn.success {
            background-color: #28a745;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .url-analysis {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 URL Response Debug</h1>
        <p>Debug the URL response issue where `/api/media/files/` is being returned instead of `/media/`.</p>
        
        <div class="info">
            <strong>🔍 Issue Analysis:</strong><br>
            - Backend should return: `/media/folder/filename.jpg`<br>
            - But getting: `/api/media/files/filename.jpg`<br>
            - Need to find where `/api` prefix is being added
        </div>
    </div>

    <div class="container">
        <h3>📁 Test File Upload</h3>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Select a file to test upload and check URL response</p>
            <p style="color: #666; font-size: 14px;">This will show the exact URL returned by the backend</p>
            <input type="file" id="fileInput" class="hidden">
        </div>

        <button class="btn success" onclick="uploadAndDebugUrl()" id="uploadBtn" disabled>Upload & Debug URL</button>
        <button class="btn" onclick="setToken()">Set Token</button>
        
        <div id="result"></div>
    </div>

    <div class="container">
        <h3>🔍 Compare Upload Endpoints</h3>
        <p>Test both upload endpoints to see which one has the URL issue:</p>
        
        <button class="btn" onclick="testMainUpload()">Test Main Upload (/upload)</button>
        <button class="btn" onclick="testSimpleUpload()">Test Simple Upload (/simple-upload)</button>
        
        <div id="compareResult"></div>
    </div>

    <script>
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.TWB-BMdlEUfAlAfVKcmVtcyg6Bak-PCfXly4xc4ALsg';
        let selectedFile = null;

        // File input handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                showResult('info', `Selected: ${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        function setToken() {
            localStorage.setItem('auth_token', testToken);
            showResult('success', '✅ Token set successfully');
        }

        async function uploadAndDebugUrl() {
            if (!selectedFile) {
                showResult('error', 'Please select a file first');
                return;
            }

            // Set token if not already set
            if (!localStorage.getItem('auth_token')) {
                setToken();
            }

            try {
                showResult('info', 'Uploading file and debugging URL response...');
                
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'document'); // Use document type to test non-avatar uploads

                console.log('🚀 Uploading file:', selectedFile.name);

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                console.log('📦 Response status:', response.status);
                console.log('📦 Response headers:', Object.fromEntries(response.headers.entries()));

                const data = await response.json();
                console.log('📦 Response data:', data);

                if (data.success) {
                    analyzeUrlResponse(data, 'Main Upload Endpoint');
                } else {
                    showResult('error', `Upload failed: ${data.message}`);
                }

            } catch (error) {
                console.error('❌ Upload error:', error);
                showResult('error', `Upload error: ${error.message}`);
            }
        }

        async function testMainUpload() {
            if (!selectedFile) {
                showCompareResult('error', 'Please select a file first');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'document');

                const response = await fetch('http://localhost:3001/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                const data = await response.json();
                
                if (data.success) {
                    showCompareResult('info', 
                        `📋 Main Upload Endpoint (/upload):\n\n` +
                        `Response URL: ${data.media?.url || 'N/A'}\n` +
                        `Expected: /media/documents/filename.jpg\n` +
                        `Has /api prefix: ${data.media?.url?.includes('/api/') ? 'YES ❌' : 'NO ✅'}\n` +
                        `Has /files/ path: ${data.media?.url?.includes('/files/') ? 'YES ❌' : 'NO ✅'}\n\n` +
                        `Full response: ${JSON.stringify(data, null, 2)}`
                    );
                } else {
                    showCompareResult('error', `Main upload failed: ${data.message}`);
                }

            } catch (error) {
                showCompareResult('error', `Main upload error: ${error.message}`);
            }
        }

        async function testSimpleUpload() {
            if (!selectedFile) {
                showCompareResult('error', 'Please select a file first');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('uploadType', 'document');

                const response = await fetch('http://localhost:3001/simple-upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                    },
                    body: formData,
                });

                const data = await response.json();
                
                if (data.success) {
                    showCompareResult('info', 
                        `📋 Simple Upload Endpoint (/simple-upload):\n\n` +
                        `Response URL: ${data.file?.url || 'N/A'}\n` +
                        `Expected: /media/documents/filename.jpg\n` +
                        `Has /api prefix: ${data.file?.url?.includes('/api/') ? 'YES ❌' : 'NO ✅'}\n` +
                        `Has /files/ path: ${data.file?.url?.includes('/files/') ? 'YES ❌' : 'NO ✅'}\n\n` +
                        `Full response: ${JSON.stringify(data, null, 2)}`
                    );
                } else {
                    showCompareResult('error', `Simple upload failed: ${data.message}`);
                }

            } catch (error) {
                showCompareResult('error', `Simple upload error: ${error.message}`);
            }
        }

        function analyzeUrlResponse(data, endpointName) {
            const mediaUrl = data.media?.url || data.file?.url;
            
            if (!mediaUrl) {
                showResult('error', 'No URL found in response');
                return;
            }

            let analysisText = `🔍 URL Analysis for ${endpointName}:\n\n`;
            
            analysisText += `📋 Response Details:\n`;
            analysisText += `  - Returned URL: ${mediaUrl}\n`;
            analysisText += `  - URL Type: ${typeof mediaUrl}\n`;
            analysisText += `  - URL Length: ${mediaUrl.length}\n\n`;
            
            analysisText += `🔍 URL Structure Analysis:\n`;
            analysisText += `  - Starts with /api/: ${mediaUrl.startsWith('/api/') ? 'YES ❌' : 'NO ✅'}\n`;
            analysisText += `  - Contains /files/: ${mediaUrl.includes('/files/') ? 'YES ❌' : 'NO ✅'}\n`;
            analysisText += `  - Starts with /media/: ${mediaUrl.startsWith('/media/') ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            // Break down the URL path
            const urlParts = mediaUrl.split('/').filter(part => part);
            analysisText += `🔗 URL Path Breakdown:\n`;
            urlParts.forEach((part, index) => {
                analysisText += `  ${index + 1}. "${part}"\n`;
            });
            analysisText += `\n`;
            
            // Expected vs actual
            const expectedPattern = '/media/documents/';
            const actualPattern = mediaUrl.substring(0, mediaUrl.lastIndexOf('/') + 1);
            
            analysisText += `📊 Pattern Comparison:\n`;
            analysisText += `  - Expected pattern: ${expectedPattern}\n`;
            analysisText += `  - Actual pattern: ${actualPattern}\n`;
            analysisText += `  - Patterns match: ${actualPattern === expectedPattern ? 'YES ✅' : 'NO ❌'}\n\n`;
            
            if (mediaUrl.includes('/api/')) {
                analysisText += `❌ ISSUE FOUND: URL contains /api/ prefix\n`;
                analysisText += `🔧 This suggests the frontend is adding /api/ somewhere\n`;
            } else if (mediaUrl.includes('/files/')) {
                analysisText += `❌ ISSUE FOUND: URL contains /files/ instead of folder name\n`;
                analysisText += `🔧 This suggests the backend is using wrong folder logic\n`;
            } else if (mediaUrl.startsWith('/media/')) {
                analysisText += `✅ URL FORMAT CORRECT: Starts with /media/\n`;
                analysisText += `🎯 The backend is generating correct URLs\n`;
            } else {
                analysisText += `❓ UNEXPECTED FORMAT: URL doesn't match expected patterns\n`;
            }
            
            analysisText += `\n📋 Full Response Data:\n${JSON.stringify(data, null, 2)}`;
            
            const resultType = mediaUrl.includes('/api/') || mediaUrl.includes('/files/') ? 'error' : 'success';
            showResult(resultType, analysisText);
        }

        function showResult(type, message) {
            const element = document.getElementById('result');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showCompareResult(type, message) {
            const element = document.getElementById('compareResult');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🔍 URL Response Debug Test loaded');
            console.log('🎯 Testing URL response to find where /api prefix is added');
            console.log('📋 Expected: /media/folder/filename.jpg');
            console.log('❌ Getting: /api/media/files/filename.jpg');
            
            showResult('info', 'Ready to debug URL response. Select a file and click "Upload & Debug URL".');
        });
    </script>
</body>
</html>
