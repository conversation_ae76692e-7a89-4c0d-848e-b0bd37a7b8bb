# Course Builder System - Playwright Testing

This directory contains comprehensive Playwright tests for the Course Builder System, covering both frontend E2E tests and backend API tests.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- Frontend running on `localhost:3000`
- API running on `localhost:3001`

### Installation
```bash
# Install Playwright
npm run test:install

# Install browser dependencies (if needed)
npm run test:install-deps
```

### Running Tests
```bash
# Run all tests
npm test

# Run with UI mode (recommended for development)
npm run test:ui

# Run in headed mode (see browser)
npm run test:headed

# Run specific test suites
npm run test:e2e          # Frontend E2E tests only
npm run test:api          # API tests only

# Run specific test files
npm run test:course-builder    # Course builder tests
npm run test:lesson-management # Lesson management tests

# Run on specific browsers
npm run test:firefox      # Firefox only
npm run test:webkit       # Safari/WebKit only
npm run test:mobile       # Mobile Chrome

# Debug mode (step through tests)
npm run test:debug

# View test report
npm run test:report
```

## 📁 Test Structure

```
tests/
├── e2e/                     # Frontend E2E tests
│   ├── course-builder.spec.ts
│   └── lesson-management.spec.ts
├── api/                     # Backend API tests
│   ├── courses.api.spec.ts
│   └── lessons.api.spec.ts
├── utils/                   # Test utilities and helpers
│   └── test-helpers.ts
├── fixtures/                # Test data and files
│   ├── test-data.json
│   ├── test-thumbnail.jpg
│   ├── test-document.pdf
│   └── test-video.mp4
├── .auth/                   # Authentication state files
├── auth.setup.ts           # Authentication setup
├── global-setup.ts         # Global test setup
├── global-teardown.ts      # Global test cleanup
└── README.md               # This file
```

## 🧪 Test Categories

### Frontend E2E Tests (`tests/e2e/`)

#### Course Builder Tests
- ✅ Course creation wizard flow
- ✅ Form validation and error handling
- ✅ File upload functionality
- ✅ Course editing and duplication
- ✅ Search and filtering
- ✅ Responsive design testing

#### Lesson Management Tests
- ✅ Lesson CRUD operations
- ✅ Drag-and-drop reordering
- ✅ Bulk operations
- ✅ Content management (video, documents, text)
- ✅ Lesson settings configuration
- ✅ Search and filtering

### Backend API Tests (`tests/api/`)

#### Courses API Tests
- ✅ CRUD operations
- ✅ Validation and error handling
- ✅ Pagination and filtering
- ✅ Status transitions
- ✅ Duplicate slug handling
- ✅ Search functionality

#### Lessons API Tests
- ✅ CRUD operations with course association
- ✅ Lesson type validation
- ✅ Reordering functionality
- ✅ Content-specific fields (video, assignment, live session)
- ✅ Bulk operations
- ✅ Duplication

## 🔧 Test Configuration

### Browser Support
- ✅ Chromium (Chrome/Edge)
- ✅ Firefox
- ✅ WebKit (Safari)
- ✅ Mobile Chrome
- ✅ Mobile Safari

### Test Data Management
- **Setup**: Automatic test data creation in `global-setup.ts`
- **Cleanup**: Automatic cleanup in `global-teardown.ts`
- **Isolation**: Each test runs with fresh data
- **Authentication**: Persistent login state for faster tests

### Environment Configuration
```typescript
// Frontend URL
baseURL: 'http://localhost:3000'

// API URL  
apiURL: 'http://localhost:3001'

// Test timeouts
timeout: 30000ms
expect.timeout: 10000ms
```

## 🛠️ Test Utilities

### TestHelpers Class
Provides reusable methods for common test operations:

```typescript
const helpers = new TestHelpers(page)

// Authentication
await helpers.loginAsAdmin()
await helpers.loginAsInstituteAdmin()

// Navigation
await helpers.navigateToCourseBuilder()
await helpers.navigateToLessonManagement(courseId)

// Course operations
await helpers.createTestCourse(courseData)
await helpers.deleteTestCourse(courseTitle)

// Lesson operations
await helpers.createTestLesson(lessonData)
await helpers.dragAndDropLesson(fromIndex, toIndex)

// File uploads
await helpers.uploadFile(selector, filePath)

// Assertions
await helpers.expectCourseToExist(courseTitle)
await helpers.waitForLoadingToComplete()
```

### APIHelpers Class
Provides methods for API testing:

```typescript
const apiHelpers = new APIHelpers()

// Authenticated requests
await apiHelpers.makeAuthenticatedRequest('/api/courses')

// CRUD operations
await apiHelpers.createTestCourse(courseData)
await apiHelpers.deleteTestCourse(courseId)
```

## 📊 Test Reports

### HTML Report
```bash
npm run test:report
```
- Interactive test results
- Screenshots and videos on failure
- Trace viewer for debugging

### CI/CD Integration
- JSON and JUnit reports generated
- Artifacts saved in `test-results/`
- Retry logic for flaky tests

## 🐛 Debugging Tests

### Debug Mode
```bash
npm run test:debug
```
- Step through tests line by line
- Inspect page state
- Modify test data on the fly

### UI Mode
```bash
npm run test:ui
```
- Visual test runner
- Watch mode for development
- Time travel debugging

### Screenshots and Videos
- Automatic screenshots on failure
- Video recording for failed tests
- Trace files for detailed debugging

## 🔍 Test Data Selectors

All UI elements use `data-testid` attributes for reliable selection:

```typescript
// Login form
'[data-testid="email-input"]'
'[data-testid="password-input"]'
'[data-testid="login-button"]'

// Course builder
'[data-testid="create-course-button"]'
'[data-testid="course-title"]'
'[data-testid="course-description"]'

// Lesson management
'[data-testid="add-lesson-button"]'
'[data-testid="lesson-item"]'
'[data-testid="lesson-menu"]'
```

## 🚨 Common Issues & Solutions

### Port Conflicts
```bash
# Kill processes on ports 3000 and 3001
npm run cleanup-ports
```

### Authentication Issues
```bash
# Clear auth state and re-run setup
rm -rf tests/.auth/
npm test
```

### Browser Installation
```bash
# Install missing browsers
npm run test:install
```

### Test Data Conflicts
```bash
# Manual cleanup if needed
npm run test -- --grep "cleanup"
```

## 📈 Performance Testing

### Load Testing
- Multiple concurrent users
- API rate limiting tests
- Database performance under load

### Accessibility Testing
- WCAG 2.1 AA compliance
- Keyboard navigation
- Screen reader compatibility

## 🔄 Continuous Integration

### GitHub Actions Example
```yaml
- name: Install Playwright
  run: npm run test:install

- name: Run Playwright tests
  run: npm test

- name: Upload test results
  uses: actions/upload-artifact@v3
  with:
    name: playwright-report
    path: test-results/
```

## 📝 Writing New Tests

### Test Structure
```typescript
import { test, expect } from '@playwright/test'
import { TestHelpers } from '../utils/test-helpers'

test.describe('Feature Name', () => {
  let helpers: TestHelpers

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page)
    await helpers.loginAsAdmin()
  })

  test('should do something', async ({ page }) => {
    // Test implementation
  })
})
```

### Best Practices
- ✅ Use descriptive test names
- ✅ Keep tests independent
- ✅ Use page object patterns
- ✅ Add proper cleanup
- ✅ Use data-testid selectors
- ✅ Test both happy and error paths

## 🎯 Coverage Goals

- **Frontend E2E**: 90%+ critical user journeys
- **API Coverage**: 100% endpoint coverage
- **Cross-browser**: All major browsers
- **Mobile**: Responsive design validation
- **Accessibility**: WCAG 2.1 AA compliance

---

**Happy Testing! 🎉**

For questions or issues, please check the test logs or contact the development team.
