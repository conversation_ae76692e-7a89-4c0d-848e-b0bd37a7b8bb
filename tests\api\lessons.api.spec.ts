import { test, expect } from '@playwright/test'
import { APIHelpers } from '../utils/test-helpers'

test.describe('Lessons API', () => {
  let apiHelpers: APIHelpers
  let testCourseId: string
  let testLessonId: string

  test.beforeAll(async () => {
    apiHelpers = new APIHelpers()
    
    // Create a test course for lessons
    const courseData = {
      title: 'Test Course for Lessons API',
      slug: 'test-course-for-lessons-api',
      description: 'Course for testing lesson APIs',
      category: 'test-category',
      difficulty: 'beginner',
      language: 'en',
      estimated_duration: 20
    }

    const courseResponse = await apiHelpers.createTestCourse(courseData)
    const course = await courseResponse.json()
    testCourseId = course.doc.id
  })

  test.afterAll(async () => {
    // Cleanup test course
    if (testCourseId) {
      await apiHelpers.deleteTestCourse(testCourseId)
    }
  })

  test.afterEach(async () => {
    // Cleanup test lesson if created
    if (testLessonId) {
      await apiHelpers.deleteTestLesson(testLessonId)
      testLessonId = ''
    }
  })

  test('should create a new lesson', async ({ request }) => {
    const lessonData = {
      title: 'API Test Lesson',
      slug: 'api-test-lesson',
      type: 'text',
      description: 'Lesson created via API testing',
      content: 'This is the lesson content',
      course: testCourseId,
      order: 1,
      duration: 15,
      is_preview: false,
      is_mandatory: true,
      status: 'draft'
    }

    const response = await apiHelpers.createTestLesson(lessonData)
    expect(response.status).toBe(201)

    const responseData = await response.json()
    expect(responseData.doc.title).toBe(lessonData.title)
    expect(responseData.doc.type).toBe(lessonData.type)
    expect(responseData.doc.course).toBe(testCourseId)

    testLessonId = responseData.doc.id
  })

  test('should get lesson by ID', async ({ request }) => {
    // First create a lesson
    const lessonData = {
      title: 'Get Lesson Test',
      slug: 'get-lesson-test',
      type: 'video',
      description: 'Lesson for GET testing',
      course: testCourseId,
      order: 1,
      duration: 20
    }

    const createResponse = await apiHelpers.createTestLesson(lessonData)
    const createdLesson = await createResponse.json()
    testLessonId = createdLesson.doc.id

    // Get the lesson
    const getResponse = await apiHelpers.makeAuthenticatedRequest(`/api/lessons/${testLessonId}`)
    expect(getResponse.status).toBe(200)

    const lessonResponse = await getResponse.json()
    expect(lessonResponse.title).toBe(lessonData.title)
    expect(lessonResponse.type).toBe(lessonData.type)
  })

  test('should update lesson', async ({ request }) => {
    // Create a lesson
    const lessonData = {
      title: 'Update Test Lesson',
      slug: 'update-test-lesson',
      type: 'text',
      description: 'Original description',
      course: testCourseId,
      order: 1,
      duration: 10
    }

    const createResponse = await apiHelpers.createTestLesson(lessonData)
    const createdLesson = await createResponse.json()
    testLessonId = createdLesson.doc.id

    // Update the lesson
    const updateData = {
      title: 'Updated Lesson Title',
      description: 'Updated description',
      type: 'video',
      duration: 25
    }

    const updateResponse = await apiHelpers.makeAuthenticatedRequest(`/api/lessons/${testLessonId}`, {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    })

    expect(updateResponse.status).toBe(200)

    const updatedLesson = await updateResponse.json()
    expect(updatedLesson.doc.title).toBe(updateData.title)
    expect(updatedLesson.doc.description).toBe(updateData.description)
    expect(updatedLesson.doc.type).toBe(updateData.type)
  })

  test('should list lessons for a course', async ({ request }) => {
    // Create multiple lessons for the course
    const lessons = []
    for (let i = 1; i <= 3; i++) {
      const lessonData = {
        title: `Course Lesson ${i}`,
        slug: `course-lesson-${i}`,
        type: 'text',
        description: `Lesson ${i} for course`,
        course: testCourseId,
        order: i,
        duration: 10
      }

      const response = await apiHelpers.createTestLesson(lessonData)
      const lesson = await response.json()
      lessons.push(lesson.doc.id)
    }

    // Get lessons for the course
    const listResponse = await apiHelpers.makeAuthenticatedRequest(`/api/courses/${testCourseId}/lessons`)
    expect(listResponse.status).toBe(200)

    const listData = await listResponse.json()
    expect(listData.docs).toHaveLength(3)
    expect(listData.docs[0].course).toBe(testCourseId)

    // Cleanup
    for (const lessonId of lessons) {
      await apiHelpers.deleteTestLesson(lessonId)
    }
  })

  test('should reorder lessons', async ({ request }) => {
    // Create multiple lessons
    const lessons = []
    for (let i = 1; i <= 3; i++) {
      const lessonData = {
        title: `Reorder Lesson ${i}`,
        slug: `reorder-lesson-${i}`,
        type: 'text',
        course: testCourseId,
        order: i,
        duration: 5
      }

      const response = await apiHelpers.createTestLesson(lessonData)
      const lesson = await response.json()
      lessons.push(lesson.doc)
    }

    // Reorder lessons (move lesson 3 to position 1)
    const reorderData = {
      lessons: [
        { id: lessons[2].id, order: 1 },
        { id: lessons[0].id, order: 2 },
        { id: lessons[1].id, order: 3 }
      ]
    }

    const reorderResponse = await apiHelpers.makeAuthenticatedRequest(`/api/courses/${testCourseId}/lessons/reorder`, {
      method: 'POST',
      body: JSON.stringify(reorderData)
    })

    expect(reorderResponse.status).toBe(200)

    // Verify new order
    const listResponse = await apiHelpers.makeAuthenticatedRequest(`/api/courses/${testCourseId}/lessons`)
    const orderedLessons = await listResponse.json()
    
    expect(orderedLessons.docs[0].title).toBe('Reorder Lesson 3')
    expect(orderedLessons.docs[1].title).toBe('Reorder Lesson 1')
    expect(orderedLessons.docs[2].title).toBe('Reorder Lesson 2')

    // Cleanup
    for (const lesson of lessons) {
      await apiHelpers.deleteTestLesson(lesson.id)
    }
  })

  test('should validate lesson types', async ({ request }) => {
    const validTypes = ['video', 'text', 'quiz', 'assignment', 'live', 'document', 'interactive']
    
    for (const type of validTypes) {
      const lessonData = {
        title: `${type} Lesson Test`,
        slug: `${type}-lesson-test`,
        type: type,
        course: testCourseId,
        order: 1
      }

      const response = await apiHelpers.createTestLesson(lessonData)
      expect(response.status).toBe(201)

      const lesson = await response.json()
      expect(lesson.doc.type).toBe(type)

      // Cleanup
      await apiHelpers.deleteTestLesson(lesson.doc.id)
    }
  })

  test('should handle invalid lesson type', async ({ request }) => {
    const lessonData = {
      title: 'Invalid Type Lesson',
      slug: 'invalid-type-lesson',
      type: 'invalid-type',
      course: testCourseId,
      order: 1
    }

    const response = await apiHelpers.createTestLesson(lessonData)
    expect(response.status).toBe(400)

    const errorData = await response.json()
    expect(errorData.errors).toBeDefined()
    expect(errorData.errors.some((error: any) => error.field === 'type')).toBe(true)
  })

  test('should duplicate lesson', async ({ request }) => {
    // Create a lesson
    const lessonData = {
      title: 'Lesson to Duplicate',
      slug: 'lesson-to-duplicate',
      type: 'text',
      description: 'Original lesson for duplication',
      content: 'Original content',
      course: testCourseId,
      order: 1,
      duration: 15
    }

    const createResponse = await apiHelpers.createTestLesson(lessonData)
    const createdLesson = await createResponse.json()
    const originalLessonId = createdLesson.doc.id

    // Duplicate the lesson
    const duplicateResponse = await apiHelpers.makeAuthenticatedRequest(`/api/lessons/${originalLessonId}/duplicate`, {
      method: 'POST'
    })

    expect(duplicateResponse.status).toBe(201)

    const duplicatedLesson = await duplicateResponse.json()
    expect(duplicatedLesson.doc.title).toBe('Lesson to Duplicate (Copy)')
    expect(duplicatedLesson.doc.content).toBe(lessonData.content)
    expect(duplicatedLesson.doc.course).toBe(testCourseId)

    // Cleanup
    await apiHelpers.deleteTestLesson(originalLessonId)
    await apiHelpers.deleteTestLesson(duplicatedLesson.doc.id)
  })

  test('should handle lesson with video content', async ({ request }) => {
    const lessonData = {
      title: 'Video Lesson Test',
      slug: 'video-lesson-test',
      type: 'video',
      course: testCourseId,
      order: 1,
      video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      duration: 30
    }

    const response = await apiHelpers.createTestLesson(lessonData)
    expect(response.status).toBe(201)

    const lesson = await response.json()
    expect(lesson.doc.video_url).toBe(lessonData.video_url)
    expect(lesson.doc.type).toBe('video')

    testLessonId = lesson.doc.id
  })

  test('should handle lesson with assignment content', async ({ request }) => {
    const lessonData = {
      title: 'Assignment Lesson Test',
      slug: 'assignment-lesson-test',
      type: 'assignment',
      course: testCourseId,
      order: 1,
      assignment: {
        instructions: 'Complete the coding exercise',
        due_date: '2024-12-31T23:59:59.000Z',
        max_score: 100,
        submission_type: 'file'
      }
    }

    const response = await apiHelpers.createTestLesson(lessonData)
    expect(response.status).toBe(201)

    const lesson = await response.json()
    expect(lesson.doc.assignment.instructions).toBe(lessonData.assignment.instructions)
    expect(lesson.doc.assignment.max_score).toBe(lessonData.assignment.max_score)

    testLessonId = lesson.doc.id
  })

  test('should handle lesson with live session content', async ({ request }) => {
    const lessonData = {
      title: 'Live Session Test',
      slug: 'live-session-test',
      type: 'live',
      course: testCourseId,
      order: 1,
      live_session: {
        scheduled_at: '2024-12-25T10:00:00.000Z',
        duration_minutes: 60,
        meeting_url: 'https://zoom.us/j/123456789',
        meeting_id: '123456789',
        meeting_password: 'password123',
        max_participants: 50
      }
    }

    const response = await apiHelpers.createTestLesson(lessonData)
    expect(response.status).toBe(201)

    const lesson = await response.json()
    expect(lesson.doc.live_session.meeting_url).toBe(lessonData.live_session.meeting_url)
    expect(lesson.doc.live_session.duration_minutes).toBe(lessonData.live_session.duration_minutes)

    testLessonId = lesson.doc.id
  })

  test('should validate required fields', async ({ request }) => {
    const invalidLessonData = {
      description: 'Lesson without title',
      course: testCourseId
    }

    const response = await apiHelpers.makeAuthenticatedRequest('/api/lessons', {
      method: 'POST',
      body: JSON.stringify(invalidLessonData)
    })

    expect(response.status).toBe(400)

    const errorData = await response.json()
    expect(errorData.errors).toBeDefined()
    expect(errorData.errors.some((error: any) => error.field === 'title')).toBe(true)
    expect(errorData.errors.some((error: any) => error.field === 'type')).toBe(true)
  })

  test('should delete lesson', async ({ request }) => {
    // Create a lesson
    const lessonData = {
      title: 'Lesson to Delete',
      slug: 'lesson-to-delete',
      type: 'text',
      course: testCourseId,
      order: 1
    }

    const createResponse = await apiHelpers.createTestLesson(lessonData)
    const createdLesson = await createResponse.json()
    const lessonId = createdLesson.doc.id

    // Delete the lesson
    const deleteResponse = await apiHelpers.deleteTestLesson(lessonId)
    expect(deleteResponse.status).toBe(200)

    // Verify lesson is deleted
    const getResponse = await apiHelpers.makeAuthenticatedRequest(`/api/lessons/${lessonId}`)
    expect(getResponse.status).toBe(404)
  })

  test('should handle bulk operations', async ({ request }) => {
    // Create multiple lessons
    const lessons = []
    for (let i = 1; i <= 3; i++) {
      const lessonData = {
        title: `Bulk Test Lesson ${i}`,
        slug: `bulk-test-lesson-${i}`,
        type: 'text',
        course: testCourseId,
        order: i,
        status: 'draft'
      }

      const response = await apiHelpers.createTestLesson(lessonData)
      const lesson = await response.json()
      lessons.push(lesson.doc.id)
    }

    // Bulk publish lessons
    const bulkData = {
      action: 'publish',
      lessonIds: lessons
    }

    const bulkResponse = await apiHelpers.makeAuthenticatedRequest('/api/lessons/bulk', {
      method: 'POST',
      body: JSON.stringify(bulkData)
    })

    expect(bulkResponse.status).toBe(200)

    // Verify lessons are published
    for (const lessonId of lessons) {
      const getResponse = await apiHelpers.makeAuthenticatedRequest(`/api/lessons/${lessonId}`)
      const lesson = await getResponse.json()
      expect(lesson.status).toBe('published')
    }

    // Cleanup
    for (const lessonId of lessons) {
      await apiHelpers.deleteTestLesson(lessonId)
    }
  })
})
