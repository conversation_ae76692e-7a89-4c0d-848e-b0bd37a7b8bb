import { test as setup, expect } from '@playwright/test'

const authFile = 'tests/.auth/user.json'

setup('authenticate as admin', async ({ page }) => {
  console.log('🔐 Setting up authentication...')
  
  // Navigate to login page
  await page.goto('/admin/login')
  
  // Fill login form
  await page.fill('[data-testid="email-input"]', '<EMAIL>')
  await page.fill('[data-testid="password-input"]', 'test123456')
  
  // Click login button
  await page.click('[data-testid="login-button"]')
  
  // Wait for successful login redirect
  await page.waitForURL('/admin/dashboard')
  
  // Verify we're logged in
  await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()
  
  // Save authentication state
  await page.context().storageState({ path: authFile })
  
  console.log('✅ Authentication setup completed')
})

setup('authenticate as institute admin', async ({ page }) => {
  console.log('🔐 Setting up institute admin authentication...')
  
  // Navigate to login page
  await page.goto('/admin/login')
  
  // Fill login form
  await page.fill('[data-testid="email-input"]', '<EMAIL>')
  await page.fill('[data-testid="password-input"]', 'test123456')
  
  // Click login button
  await page.click('[data-testid="login-button"]')
  
  // Wait for successful login redirect
  await page.waitForURL('/admin/dashboard')
  
  // Verify we're logged in
  await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()
  
  // Save authentication state
  await page.context().storageState({ path: 'tests/.auth/institute-admin.json' })
  
  console.log('✅ Institute admin authentication setup completed')
})
