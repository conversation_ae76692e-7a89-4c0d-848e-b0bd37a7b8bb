import { test, expect } from '@playwright/test'
import { TestHelpers } from '../utils/test-helpers'

test.describe('Course Builder', () => {
  let helpers: TestHelpers

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page)
    await helpers.loginAsAdmin()
    await helpers.navigateToCourseBuilder()
  })

  test('should display course builder page', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Course Builder')
    await expect(page.locator('[data-testid="create-course-button"]')).toBeVisible()
  })

  test('should create a new course successfully', async ({ page }) => {
    const courseData = {
      title: 'Test Course - Playwright',
      description: 'This is a test course created by Playwright automation',
      category: 'test-category',
      difficulty: 'beginner',
      duration: 10,
      price: 99.99
    }

    await helpers.createTestCourse(courseData)
    
    // Verify course appears in the list
    await helpers.expectCourseToExist(courseData.title)
    
    // Cleanup
    await helpers.deleteTestCourse(courseData.title)
  })

  test('should validate required fields in course creation', async ({ page }) => {
    // Click create course button
    await page.click('[data-testid="create-course-button"]')
    
    // Try to submit without filling required fields
    await page.click('[data-testid="create-course-submit"]')
    
    // Check for validation errors
    await helpers.expectValidationError('[data-testid="course-title"]', 'Course title is required')
    await helpers.expectValidationError('[data-testid="course-description"]', 'Course description is required')
    await helpers.expectValidationError('[data-testid="course-category"]', 'Course category is required')
  })

  test('should navigate through course creation wizard', async ({ page }) => {
    // Click create course button
    await page.click('[data-testid="create-course-button"]')
    
    // Step 1: Basic Information
    await expect(page.locator('[data-testid="wizard-step-1"]')).toHaveClass(/active/)
    
    await page.fill('[data-testid="course-title"]', 'Wizard Test Course')
    await page.fill('[data-testid="course-description"]', 'Testing the wizard flow')
    await page.selectOption('[data-testid="course-category"]', 'test-category')
    await page.selectOption('[data-testid="course-difficulty"]', 'intermediate')
    await page.fill('[data-testid="course-duration"]', '15')
    
    // Go to next step
    await page.click('[data-testid="wizard-next-button"]')
    
    // Step 2: Settings & Pricing
    await expect(page.locator('[data-testid="wizard-step-2"]')).toHaveClass(/active/)
    
    await page.click('[data-testid="paid-course-toggle"]')
    await page.fill('[data-testid="course-price"]', '149.99')
    
    // Go to next step
    await page.click('[data-testid="wizard-next-button"]')
    
    // Step 3: Content & Objectives
    await expect(page.locator('[data-testid="wizard-step-3"]')).toHaveClass(/active/)
    
    await page.fill('[data-testid="learning-objective-input"]', 'Learn Playwright testing')
    await page.click('[data-testid="add-objective-button"]')
    
    // Go to next step
    await page.click('[data-testid="wizard-next-button"]')
    
    // Step 4: Enrollment Settings
    await expect(page.locator('[data-testid="wizard-step-4"]')).toHaveClass(/active/)
    
    // Go to review step
    await page.click('[data-testid="wizard-next-button"]')
    
    // Step 5: Review
    await expect(page.locator('[data-testid="wizard-step-5"]')).toHaveClass(/active/)
    await expect(page.locator('[data-testid="course-summary"]')).toContainText('Wizard Test Course')
    
    // Submit course
    await page.click('[data-testid="create-course-submit"]')
    
    // Verify success
    await helpers.waitForToastMessage('Course created successfully')
    
    // Cleanup
    await helpers.deleteTestCourse('Wizard Test Course')
  })

  test('should handle course creation with file uploads', async ({ page }) => {
    // Click create course button
    await page.click('[data-testid="create-course-button"]')
    
    // Fill basic information
    await page.fill('[data-testid="course-title"]', 'Course with Media')
    await page.fill('[data-testid="course-description"]', 'Testing file uploads')
    await page.selectOption('[data-testid="course-category"]', 'test-category')
    await page.selectOption('[data-testid="course-difficulty"]', 'beginner')
    await page.fill('[data-testid="course-duration"]', '8')
    
    // Navigate to media step
    await page.click('[data-testid="wizard-next-button"]') // Settings
    await page.click('[data-testid="wizard-next-button"]') // Content
    await page.click('[data-testid="wizard-next-button"]') // Enrollment
    
    // Upload thumbnail (mock file)
    const thumbnailPath = 'tests/fixtures/test-thumbnail.jpg'
    await helpers.uploadFile('[data-testid="thumbnail-upload"]', thumbnailPath)
    
    // Verify upload success
    await expect(page.locator('[data-testid="thumbnail-preview"]')).toBeVisible()
    
    // Submit course
    await page.click('[data-testid="wizard-next-button"]') // Review
    await page.click('[data-testid="create-course-submit"]')
    
    // Verify success
    await helpers.waitForToastMessage('Course created successfully')
    
    // Cleanup
    await helpers.deleteTestCourse('Course with Media')
  })

  test('should edit existing course', async ({ page }) => {
    // First create a course
    const courseData = {
      title: 'Course to Edit',
      description: 'Original description',
      category: 'test-category',
      difficulty: 'beginner',
      duration: 5
    }
    
    await helpers.createTestCourse(courseData)
    
    // Find and edit the course
    const courseCard = page.locator(`[data-testid="course-card"][data-title="${courseData.title}"]`)
    await courseCard.locator('[data-testid="course-menu"]').click()
    await page.click('[data-testid="edit-course"]')
    
    // Modify course details
    await page.fill('[data-testid="course-title"]', 'Edited Course Title')
    await page.fill('[data-testid="course-description"]', 'Updated description')
    
    // Save changes
    await page.click('[data-testid="save-course-button"]')
    
    // Verify changes
    await helpers.waitForToastMessage('Course updated successfully')
    await helpers.expectCourseToExist('Edited Course Title')
    
    // Cleanup
    await helpers.deleteTestCourse('Edited Course Title')
  })

  test('should duplicate course', async ({ page }) => {
    // First create a course
    const courseData = {
      title: 'Course to Duplicate',
      description: 'Original course for duplication',
      category: 'test-category',
      difficulty: 'intermediate',
      duration: 12
    }
    
    await helpers.createTestCourse(courseData)
    
    // Duplicate the course
    const courseCard = page.locator(`[data-testid="course-card"][data-title="${courseData.title}"]`)
    await courseCard.locator('[data-testid="course-menu"]').click()
    await page.click('[data-testid="duplicate-course"]')
    
    // Verify duplicate appears
    await helpers.waitForToastMessage('Course duplicated successfully')
    await helpers.expectCourseToExist('Course to Duplicate (Copy)')
    
    // Cleanup
    await helpers.deleteTestCourse('Course to Duplicate')
    await helpers.deleteTestCourse('Course to Duplicate (Copy)')
  })

  test('should filter and search courses', async ({ page }) => {
    // Create multiple test courses
    const courses = [
      { title: 'JavaScript Basics', category: 'programming', difficulty: 'beginner' },
      { title: 'Advanced React', category: 'programming', difficulty: 'advanced' },
      { title: 'Design Fundamentals', category: 'design', difficulty: 'beginner' }
    ]
    
    for (const course of courses) {
      await helpers.createTestCourse({
        ...course,
        description: `Test course: ${course.title}`,
        duration: 10
      })
    }
    
    // Test search functionality
    await page.fill('[data-testid="course-search"]', 'JavaScript')
    await helpers.waitForLoadingToComplete()
    
    await helpers.expectCourseToExist('JavaScript Basics')
    await expect(page.locator('[data-testid="course-card"][data-title="Advanced React"]')).not.toBeVisible()
    
    // Clear search
    await page.fill('[data-testid="course-search"]', '')
    await helpers.waitForLoadingToComplete()
    
    // Test category filter
    await page.selectOption('[data-testid="category-filter"]', 'design')
    await helpers.waitForLoadingToComplete()
    
    await helpers.expectCourseToExist('Design Fundamentals')
    await expect(page.locator('[data-testid="course-card"][data-title="JavaScript Basics"]')).not.toBeVisible()
    
    // Test difficulty filter
    await page.selectOption('[data-testid="category-filter"]', 'all')
    await page.selectOption('[data-testid="difficulty-filter"]', 'advanced')
    await helpers.waitForLoadingToComplete()
    
    await helpers.expectCourseToExist('Advanced React')
    await expect(page.locator('[data-testid="course-card"][data-title="JavaScript Basics"]')).not.toBeVisible()
    
    // Cleanup
    for (const course of courses) {
      await helpers.deleteTestCourse(course.title)
    }
  })
})
