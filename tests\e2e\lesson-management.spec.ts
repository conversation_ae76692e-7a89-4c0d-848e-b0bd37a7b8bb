import { test, expect } from '@playwright/test'
import { TestHelpers } from '../utils/test-helpers'

test.describe('Lesson Management', () => {
  let helpers: TestHelpers
  let testCourseId: string

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page)
    await helpers.loginAsAdmin()
    
    // Create a test course first
    const courseData = {
      title: 'Test Course for Lessons',
      description: 'Course for testing lesson management',
      category: 'test-category',
      difficulty: 'beginner',
      duration: 20
    }
    
    await helpers.createTestCourse(courseData)
    
    // Get course ID and navigate to lesson management
    const courseCard = page.locator(`[data-testid="course-card"][data-title="${courseData.title}"]`)
    testCourseId = await courseCard.getAttribute('data-course-id') || 'test-course-id'
    
    await helpers.navigateToLessonManagement(testCourseId)
  })

  test.afterEach(async ({ page }) => {
    // Cleanup test course
    await helpers.navigateToCourseBuilder()
    await helpers.deleteTestCourse('Test Course for Lessons')
  })

  test('should display lesson management interface', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Lesson Management')
    await expect(page.locator('[data-testid="add-lesson-button"]')).toBeVisible()
    await expect(page.locator('[data-testid="lesson-list"]')).toBeVisible()
  })

  test('should create different types of lessons', async ({ page }) => {
    const lessonTypes = [
      { type: 'video', title: 'Video Lesson Test' },
      { type: 'text', title: 'Text Lesson Test' },
      { type: 'quiz', title: 'Quiz Lesson Test' },
      { type: 'assignment', title: 'Assignment Lesson Test' }
    ]

    for (const lesson of lessonTypes) {
      await helpers.createTestLesson({
        title: lesson.title,
        type: lesson.type,
        description: `Test ${lesson.type} lesson`,
        duration: 15
      })
      
      // Verify lesson appears in list
      await helpers.expectLessonToExist(lesson.title)
    }
  })

  test('should validate lesson form fields', async ({ page }) => {
    // Click add lesson button
    await page.click('[data-testid="add-lesson-button"]')
    
    // Try to submit without required fields
    await page.click('[data-testid="save-lesson-button"]')
    
    // Check for validation errors
    await helpers.expectValidationError('[data-testid="lesson-title"]', 'Lesson title is required')
    await helpers.expectValidationError('[data-testid="lesson-type"]', 'Lesson type is required')
  })

  test('should reorder lessons with drag and drop', async ({ page }) => {
    // Create multiple lessons
    const lessons = [
      { title: 'Lesson 1', type: 'text' },
      { title: 'Lesson 2', type: 'video' },
      { title: 'Lesson 3', type: 'quiz' }
    ]

    for (const lesson of lessons) {
      await helpers.createTestLesson(lesson)
    }

    // Verify initial order
    const lessonItems = page.locator('[data-testid="lesson-item"]')
    await expect(lessonItems.nth(0)).toContainText('Lesson 1')
    await expect(lessonItems.nth(1)).toContainText('Lesson 2')
    await expect(lessonItems.nth(2)).toContainText('Lesson 3')

    // Drag lesson 3 to position 1
    await helpers.dragAndDropLesson(2, 0)

    // Verify new order
    await expect(lessonItems.nth(0)).toContainText('Lesson 3')
    await expect(lessonItems.nth(1)).toContainText('Lesson 1')
    await expect(lessonItems.nth(2)).toContainText('Lesson 2')
  })

  test('should perform bulk operations on lessons', async ({ page }) => {
    // Create multiple lessons
    const lessons = [
      { title: 'Bulk Test 1', type: 'text' },
      { title: 'Bulk Test 2', type: 'video' },
      { title: 'Bulk Test 3', type: 'quiz' }
    ]

    for (const lesson of lessons) {
      await helpers.createTestLesson(lesson)
    }

    // Select multiple lessons
    await helpers.selectLessons([0, 1])

    // Perform bulk publish
    await helpers.performBulkAction('publish')

    // Verify lessons are published
    const publishedLessons = page.locator('[data-testid="lesson-item"][data-status="published"]')
    await expect(publishedLessons).toHaveCount(2)
  })

  test('should edit lesson content', async ({ page }) => {
    // Create a text lesson
    await helpers.createTestLesson({
      title: 'Lesson to Edit',
      type: 'text',
      description: 'Original description'
    })

    // Click edit button
    const lessonItem = page.locator('[data-testid="lesson-item"][data-title="Lesson to Edit"]')
    await lessonItem.locator('[data-testid="lesson-menu"]').click()
    await page.click('[data-testid="edit-lesson"]')

    // Modify lesson content
    await page.fill('[data-testid="lesson-title"]', 'Edited Lesson Title')
    await page.fill('[data-testid="lesson-description"]', 'Updated description')

    // Add rich text content
    const richTextEditor = page.locator('[data-testid="rich-text-editor"]')
    await richTextEditor.fill('This is the updated lesson content with rich text.')

    // Save changes
    await page.click('[data-testid="save-lesson-button"]')

    // Verify changes
    await helpers.waitForToastMessage('Lesson updated successfully')
    await helpers.expectLessonToExist('Edited Lesson Title')
  })

  test('should add video content to lesson', async ({ page }) => {
    // Create a video lesson
    await helpers.createTestLesson({
      title: 'Video Content Test',
      type: 'video'
    })

    // Edit the lesson
    const lessonItem = page.locator('[data-testid="lesson-item"][data-title="Video Content Test"]')
    await lessonItem.locator('[data-testid="lesson-menu"]').click()
    await page.click('[data-testid="edit-lesson"]')

    // Add video URL
    await helpers.addVideoContent('https://www.youtube.com/watch?v=dQw4w9WgXcQ')

    // Verify video content is added
    await expect(page.locator('[data-testid="video-preview"]')).toBeVisible()

    // Save lesson
    await page.click('[data-testid="save-lesson-button"]')
    await helpers.waitForToastMessage('Lesson updated successfully')
  })

  test('should add document content to lesson', async ({ page }) => {
    // Create a document lesson
    await helpers.createTestLesson({
      title: 'Document Content Test',
      type: 'document'
    })

    // Edit the lesson
    const lessonItem = page.locator('[data-testid="lesson-item"][data-title="Document Content Test"]')
    await lessonItem.locator('[data-testid="lesson-menu"]').click()
    await page.click('[data-testid="edit-lesson"]')

    // Upload document
    const documentPath = 'tests/fixtures/test-document.pdf'
    await helpers.addDocumentContent(documentPath)

    // Verify document is uploaded
    await expect(page.locator('[data-testid="document-preview"]')).toBeVisible()

    // Save lesson
    await page.click('[data-testid="save-lesson-button"]')
    await helpers.waitForToastMessage('Lesson updated successfully')
  })

  test('should configure lesson settings', async ({ page }) => {
    // Create a lesson
    await helpers.createTestLesson({
      title: 'Settings Test Lesson',
      type: 'text'
    })

    // Edit the lesson
    const lessonItem = page.locator('[data-testid="lesson-item"][data-title="Settings Test Lesson"]')
    await lessonItem.locator('[data-testid="lesson-menu"]').click()
    await page.click('[data-testid="edit-lesson"]')

    // Navigate to settings tab
    await page.click('[data-testid="lesson-settings-tab"]')

    // Configure settings
    await page.check('[data-testid="is-preview-checkbox"]')
    await page.check('[data-testid="is-mandatory-checkbox"]')
    await page.selectOption('[data-testid="lesson-status"]', 'published')

    // Save lesson
    await page.click('[data-testid="save-lesson-button"]')
    await helpers.waitForToastMessage('Lesson updated successfully')

    // Verify settings are applied
    const updatedLesson = page.locator('[data-testid="lesson-item"][data-title="Settings Test Lesson"]')
    await expect(updatedLesson.locator('[data-testid="preview-badge"]')).toBeVisible()
    await expect(updatedLesson.locator('[data-testid="mandatory-badge"]')).toBeVisible()
    await expect(updatedLesson.locator('[data-testid="status-badge"]')).toContainText('Published')
  })

  test('should duplicate lesson', async ({ page }) => {
    // Create a lesson
    await helpers.createTestLesson({
      title: 'Lesson to Duplicate',
      type: 'text',
      description: 'Original lesson for duplication'
    })

    // Duplicate the lesson
    const lessonItem = page.locator('[data-testid="lesson-item"][data-title="Lesson to Duplicate"]')
    await lessonItem.locator('[data-testid="lesson-menu"]').click()
    await page.click('[data-testid="duplicate-lesson"]')

    // Verify duplicate appears
    await helpers.waitForToastMessage('Lesson duplicated successfully')
    await helpers.expectLessonToExist('Lesson to Duplicate (Copy)')
  })

  test('should delete lesson', async ({ page }) => {
    // Create a lesson
    await helpers.createTestLesson({
      title: 'Lesson to Delete',
      type: 'text'
    })

    // Delete the lesson
    await helpers.deleteTestLesson('Lesson to Delete')

    // Verify lesson is removed
    await expect(page.locator('[data-testid="lesson-item"][data-title="Lesson to Delete"]')).not.toBeVisible()
  })

  test('should search and filter lessons', async ({ page }) => {
    // Create lessons of different types
    const lessons = [
      { title: 'JavaScript Basics', type: 'video' },
      { title: 'CSS Fundamentals', type: 'text' },
      { title: 'HTML Quiz', type: 'quiz' },
      { title: 'JavaScript Assignment', type: 'assignment' }
    ]

    for (const lesson of lessons) {
      await helpers.createTestLesson(lesson)
    }

    // Test search functionality
    await page.fill('[data-testid="lesson-search"]', 'JavaScript')
    await helpers.waitForLoadingToComplete()

    await helpers.expectLessonToExist('JavaScript Basics')
    await helpers.expectLessonToExist('JavaScript Assignment')
    await expect(page.locator('[data-testid="lesson-item"][data-title="CSS Fundamentals"]')).not.toBeVisible()

    // Clear search
    await page.fill('[data-testid="lesson-search"]', '')
    await helpers.waitForLoadingToComplete()

    // Test type filter
    await page.selectOption('[data-testid="lesson-type-filter"]', 'quiz')
    await helpers.waitForLoadingToComplete()

    await helpers.expectLessonToExist('HTML Quiz')
    await expect(page.locator('[data-testid="lesson-item"][data-title="JavaScript Basics"]')).not.toBeVisible()
  })
})
