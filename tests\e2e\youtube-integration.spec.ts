import { test, expect } from '@playwright/test'

/**
 * YouTube Integration E2E Tests
 * 
 * Tests the YouTube Integration component functionality including:
 * - URL validation
 * - Video metadata extraction
 * - Playlist support
 * - Video selection workflow
 */

test.describe('YouTube Integration', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to content management page
    await page.goto('/admin/content-management')
    
    // Wait for page to load
    await page.waitForLoadState('networkidle')
  })

  test('should validate YouTube video URLs correctly', async ({ page }) => {
    // Test valid video URL
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    
    // Should show valid status
    await expect(page.locator('text=Valid YouTube video URL')).toBeVisible()
    
    // Test invalid URL
    await page.fill('[data-testid="youtube-url"]', 'https://invalid-url.com')
    
    // Should show invalid status
    await expect(page.locator('text=Invalid YouTube URL format')).toBeVisible()
    
    // Test YouTube short URL
    await page.fill('[data-testid="youtube-url"]', 'https://youtu.be/dQw4w9WgXcQ')
    
    // Should show valid status
    await expect(page.locator('text=Valid YouTube video URL')).toBeVisible()
  })

  test('should validate YouTube playlist URLs correctly', async ({ page }) => {
    // Test valid playlist URL
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/playlist?list=PLExample123')
    
    // Should show valid playlist status
    await expect(page.locator('text=Valid YouTube playlist URL')).toBeVisible()
    await expect(page.locator('[data-testid="playlist-badge"]')).toContainText('playlist')
  })

  test('should extract video metadata when URL is entered', async ({ page }) => {
    // Enable auto-extract metadata
    await page.check('[data-testid="auto-extract-toggle"]')
    
    // Enter valid YouTube URL
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    
    // Wait for metadata extraction
    await page.waitForSelector('[data-testid="video-title"]', { timeout: 10000 })
    
    // Verify video metadata is displayed
    await expect(page.locator('[data-testid="video-title"]')).toBeVisible()
    await expect(page.locator('[data-testid="video-duration"]')).toBeVisible()
    
    // Verify video preview iframe is loaded
    await expect(page.locator('iframe[title*="Video"]')).toBeVisible()
  })

  test('should manually extract metadata when validate button is clicked', async ({ page }) => {
    // Disable auto-extract
    await page.uncheck('[data-testid="auto-extract-toggle"]')
    
    // Enter valid YouTube URL
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    
    // Click validate button
    await page.click('[data-testid="validate-url"]')
    
    // Wait for loading to complete
    await page.waitForSelector('[data-testid="video-title"]', { timeout: 10000 })
    
    // Verify metadata is extracted
    await expect(page.locator('[data-testid="video-title"]')).toBeVisible()
  })

  test('should handle playlist metadata extraction', async ({ page }) => {
    // Enter playlist URL
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/playlist?list=PLExample123')
    
    // Click validate button
    await page.click('[data-testid="validate-url"]')
    
    // Wait for playlist metadata
    await page.waitForSelector('[data-testid="playlist-title"]', { timeout: 10000 })
    
    // Verify playlist information is displayed
    await expect(page.locator('[data-testid="playlist-title"]')).toBeVisible()
    await expect(page.locator('text=videos')).toBeVisible()
    
    // Verify playlist videos are listed
    await expect(page.locator('[data-testid="playlist-video-item"]').first()).toBeVisible()
  })

  test('should allow video selection', async ({ page }) => {
    // Extract video metadata
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    await page.click('[data-testid="validate-url"]')
    
    // Wait for video preview
    await page.waitForSelector('[data-testid="select-video"]', { timeout: 10000 })
    
    // Click select video button
    await page.click('[data-testid="select-video"]')
    
    // Verify success toast appears
    await expect(page.locator('text=Video selected')).toBeVisible()
  })

  test('should allow playlist import', async ({ page }) => {
    // Extract playlist metadata
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/playlist?list=PLExample123')
    await page.click('[data-testid="validate-url"]')
    
    // Wait for playlist preview
    await page.waitForSelector('[data-testid="select-playlist"]', { timeout: 10000 })
    
    // Click import playlist button
    await page.click('[data-testid="select-playlist"]')
    
    // Verify success toast appears
    await expect(page.locator('text=Playlist selected')).toBeVisible()
  })

  test('should copy video URL to clipboard', async ({ page }) => {
    // Grant clipboard permissions
    await page.context().grantPermissions(['clipboard-read', 'clipboard-write'])
    
    // Extract video metadata
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    await page.click('[data-testid="validate-url"]')
    
    // Wait for video preview
    await page.waitForSelector('[data-testid="copy-url"]', { timeout: 10000 })
    
    // Click copy URL button
    await page.click('[data-testid="copy-url"]')
    
    // Verify URL is copied to clipboard
    const clipboardText = await page.evaluate(() => navigator.clipboard.readText())
    expect(clipboardText).toContain('youtube.com')
  })

  test('should open video in new tab', async ({ page }) => {
    // Extract video metadata
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    await page.click('[data-testid="validate-url"]')
    
    // Wait for video preview
    await page.waitForSelector('[data-testid="view-on-youtube"]', { timeout: 10000 })
    
    // Verify external link has correct attributes
    const link = page.locator('[data-testid="view-on-youtube"]')
    await expect(link).toHaveAttribute('target', '_blank')
    await expect(link).toHaveAttribute('rel', 'noopener noreferrer')
  })

  test('should handle loading states correctly', async ({ page }) => {
    // Enter URL
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    
    // Click validate button
    await page.click('[data-testid="validate-url"]')
    
    // Verify loading spinner appears
    await expect(page.locator('[data-testid="validate-url"] .animate-spin')).toBeVisible()
    
    // Wait for loading to complete
    await page.waitForSelector('[data-testid="video-title"]', { timeout: 10000 })
    
    // Verify loading spinner disappears
    await expect(page.locator('[data-testid="validate-url"] .animate-spin')).not.toBeVisible()
  })

  test('should handle error states gracefully', async ({ page }) => {
    // Mock network error
    await page.route('**/api/youtube/**', route => {
      route.abort('failed')
    })
    
    // Enter URL and try to validate
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    await page.click('[data-testid="validate-url"]')
    
    // Verify error toast appears
    await expect(page.locator('text=Extraction failed')).toBeVisible()
  })

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Verify component is responsive
    await expect(page.locator('[data-testid="youtube-url"]')).toBeVisible()
    
    // Test URL input on mobile
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    await page.click('[data-testid="validate-url"]')
    
    // Verify mobile layout works
    await page.waitForSelector('[data-testid="video-title"]', { timeout: 10000 })
    await expect(page.locator('[data-testid="video-title"]')).toBeVisible()
  })

  test('should maintain accessibility standards', async ({ page }) => {
    // Check for proper labels
    await expect(page.locator('label[for="youtube-url"]')).toBeVisible()
    await expect(page.locator('label[for="auto-extract"]')).toBeVisible()
    
    // Test keyboard navigation
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="youtube-url"]')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="validate-url"]')).toBeFocused()
    
    // Test ARIA attributes
    await expect(page.locator('[data-testid="youtube-url"]')).toHaveAttribute('aria-describedby')
  })

  test('should integrate with existing video player', async ({ page }) => {
    // Extract video metadata
    await page.fill('[data-testid="youtube-url"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    await page.click('[data-testid="validate-url"]')
    
    // Wait for video preview
    await page.waitForSelector('[data-testid="select-video"]', { timeout: 10000 })
    
    // Select video
    await page.click('[data-testid="select-video"]')
    
    // Verify integration with video player component
    await expect(page.locator('.video-player-container')).toBeVisible()
  })
})

// Helper functions for testing
export const youTubeTestHelpers = {
  async fillYouTubeUrl(page: any, url: string) {
    await page.fill('[data-testid="youtube-url"]', url)
  },

  async validateUrl(page: any) {
    await page.click('[data-testid="validate-url"]')
  },

  async waitForVideoMetadata(page: any) {
    await page.waitForSelector('[data-testid="video-title"]', { timeout: 10000 })
  },

  async selectVideo(page: any) {
    await page.click('[data-testid="select-video"]')
  },

  async selectPlaylist(page: any) {
    await page.click('[data-testid="select-playlist"]')
  }
}
