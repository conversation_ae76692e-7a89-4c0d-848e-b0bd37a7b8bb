{"users": {"admin": {"email": "<EMAIL>", "password": "test123456", "firstName": "Test", "lastName": "Admin", "role": "super_admin"}, "instituteAdmin": {"email": "<EMAIL>", "password": "test123456", "firstName": "Institute", "lastName": "Admin", "role": "institute_admin"}, "instructor": {"email": "<EMAIL>", "password": "test123456", "firstName": "Test", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "role": "instructor"}}, "categories": [{"name": "Programming", "slug": "programming", "description": "Programming and software development courses"}, {"name": "Design", "slug": "design", "description": "UI/UX and graphic design courses"}, {"name": "Business", "slug": "business", "description": "Business and entrepreneurship courses"}, {"name": "Test Category", "slug": "test-category", "description": "Category for testing purposes"}], "institutes": [{"name": "Test Institute", "slug": "test-institute", "email": "<EMAIL>", "phone": "+1234567890", "website": "https://testinstitute.com", "tagline": "Excellence in Education", "is_active": true, "address": {"street": "123 Test Street", "city": "Test City", "state": "Test State", "country": "Test Country", "zipCode": "12345"}}], "courses": [{"title": "JavaScript Fundamentals", "slug": "javascript-fundamentals", "description": "Learn the basics of JavaScript programming language", "category": "programming", "difficulty": "beginner", "language": "en", "estimated_duration": 20, "price": 99.99, "currency": "USD", "status": "published", "is_featured": true, "tags": ["javascript", "programming", "web development"]}, {"title": "React for Be<PERSON>ners", "slug": "react-for-beginners", "description": "Introduction to React.js library for building user interfaces", "category": "programming", "difficulty": "intermediate", "language": "en", "estimated_duration": 30, "price": 149.99, "currency": "USD", "status": "published", "prerequisites": ["javascript-fundamentals"], "tags": ["react", "javascript", "frontend"]}, {"title": "UI/UX Design Principles", "slug": "ui-ux-design-principles", "description": "Learn the fundamental principles of user interface and user experience design", "category": "design", "difficulty": "beginner", "language": "en", "estimated_duration": 25, "price": 79.99, "currency": "USD", "status": "published", "tags": ["design", "ui", "ux", "principles"]}], "lessons": [{"title": "Introduction to JavaScript", "slug": "introduction-to-javascript", "type": "video", "description": "Overview of JavaScript and its role in web development", "order": 1, "duration": 15, "is_preview": true, "is_mandatory": true, "status": "published", "video_url": "https://www.youtube.com/watch?v=W6NZfCO5SIk"}, {"title": "Variables and Data Types", "slug": "variables-and-data-types", "type": "text", "description": "Understanding JavaScript variables and different data types", "order": 2, "duration": 20, "is_mandatory": true, "status": "published", "content": "<h2>Variables in JavaScript</h2><p>Variables are containers for storing data values...</p>"}, {"title": "JavaScript Quiz 1", "slug": "javascript-quiz-1", "type": "quiz", "description": "Test your knowledge of JavaScript basics", "order": 3, "duration": 10, "is_mandatory": true, "status": "published", "quiz": {"questions": [{"question": "What is JavaScript?", "type": "multiple_choice", "options": ["A programming language", "A markup language", "A styling language", "A database"], "correct_answer": 0}], "passing_score": 70}}, {"title": "Build a Simple Calculator", "slug": "build-simple-calculator", "type": "assignment", "description": "Create a basic calculator using JavaScript", "order": 4, "duration": 60, "is_mandatory": true, "status": "published", "assignment": {"instructions": "Build a calculator that can perform basic arithmetic operations", "due_date": "2024-12-31T23:59:59.000Z", "max_score": 100, "submission_type": "file"}}], "testFiles": {"thumbnail": "tests/fixtures/test-thumbnail.jpg", "document": "tests/fixtures/test-document.pdf", "video": "tests/fixtures/test-video.mp4", "image": "tests/fixtures/test-image.png"}, "apiEndpoints": {"frontend": "http://localhost:3000", "api": "http://localhost:3001", "health": "/api/health", "login": "/api/users/login", "courses": "/api/courses", "lessons": "/api/lessons", "categories": "/api/categories", "institutes": "/api/institutes", "users": "/api/users"}, "testSelectors": {"loginForm": {"emailInput": "[data-testid='email-input']", "passwordInput": "[data-testid='password-input']", "loginButton": "[data-testid='login-button']"}, "courseBuilder": {"createButton": "[data-testid='create-course-button']", "courseTitle": "[data-testid='course-title']", "courseDescription": "[data-testid='course-description']", "courseCategory": "[data-testid='course-category']", "courseDifficulty": "[data-testid='course-difficulty']", "submitButton": "[data-testid='create-course-submit']"}, "lessonManagement": {"addLessonButton": "[data-testid='add-lesson-button']", "lessonTitle": "[data-testid='lesson-title']", "lessonType": "[data-testid='lesson-type']", "lessonDescription": "[data-testid='lesson-description']", "saveLessonButton": "[data-testid='save-lesson-button']", "lessonList": "[data-testid='lesson-list']", "lessonItem": "[data-testid='lesson-item']"}, "common": {"loadingSpinner": "[data-testid='loading-spinner']", "successToast": "[data-testid='success-toast']", "errorToast": "[data-testid='error-toast']", "confirmButton": "[data-testid='confirm-button']", "cancelButton": "[data-testid='cancel-button']"}}, "testConfig": {"timeouts": {"short": 5000, "medium": 10000, "long": 30000}, "retries": {"ci": 2, "local": 0}, "browsers": ["chromium", "firefox", "webkit"], "viewports": {"mobile": {"width": 375, "height": 667}, "tablet": {"width": 768, "height": 1024}, "desktop": {"width": 1920, "height": 1080}}}}