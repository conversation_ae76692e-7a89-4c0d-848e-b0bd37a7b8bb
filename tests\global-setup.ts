import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...')
  
  // Launch browser for setup
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // Wait for frontend to be ready
    console.log('⏳ Waiting for frontend server...')
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' })
    console.log('✅ Frontend server is ready')

    // Wait for API to be ready
    console.log('⏳ Waiting for API server...')
    const apiResponse = await page.request.get('http://localhost:3001/api/health')
    if (apiResponse.ok()) {
      console.log('✅ API server is ready')
    } else {
      console.log('⚠️ API server health check failed, but continuing...')
    }

    // Setup test data
    await setupTestData(page)
    
    // Create admin user session
    await setupAdminSession(page)

  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }

  console.log('✅ Global test setup completed')
}

async function setupTestData(page: any) {
  console.log('📊 Setting up test data...')
  
  try {
    // Create test categories
    await page.request.post('http://localhost:3001/api/categories', {
      data: {
        name: 'Test Category',
        slug: 'test-category',
        description: 'Category for testing purposes'
      }
    })

    // Create test institute
    await page.request.post('http://localhost:3001/api/institutes', {
      data: {
        name: 'Test Institute',
        slug: 'test-institute',
        email: '<EMAIL>',
        is_active: true
      }
    })

    console.log('✅ Test data setup completed')
  } catch (error) {
    console.log('⚠️ Test data setup failed, but continuing...', error)
  }
}

async function setupAdminSession(page: any) {
  console.log('👤 Setting up admin session...')
  
  try {
    // Login as admin user
    const loginResponse = await page.request.post('http://localhost:3001/api/users/login', {
      data: {
        email: '<EMAIL>',
        password: 'test123456'
      }
    })

    if (loginResponse.ok()) {
      const loginData = await loginResponse.json()
      
      // Store auth token for tests
      process.env.TEST_AUTH_TOKEN = loginData.token
      console.log('✅ Admin session created')
    } else {
      console.log('⚠️ Admin login failed, tests may need to handle authentication')
    }
  } catch (error) {
    console.log('⚠️ Admin session setup failed:', error)
  }
}

export default globalSetup
