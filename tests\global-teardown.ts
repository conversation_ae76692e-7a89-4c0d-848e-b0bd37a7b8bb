import { chromium } from '@playwright/test'

async function globalTeardown() {
  console.log('🧹 Starting global test teardown...')
  
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // Clean up test data
    await cleanupTestData(page)
    
    console.log('✅ Global test teardown completed')
  } catch (error) {
    console.error('❌ Global teardown failed:', error)
  } finally {
    await browser.close()
  }
}

async function cleanupTestData(page: any) {
  console.log('🗑️ Cleaning up test data...')
  
  try {
    // Set auth header if available
    const authToken = process.env.TEST_AUTH_TOKEN
    const headers = authToken ? { 'Authorization': `Bearer ${authToken}` } : {}

    // Clean up test courses
    const coursesResponse = await page.request.get('http://localhost:3001/api/courses', {
      headers
    })
    
    if (coursesResponse.ok()) {
      const courses = await coursesResponse.json()
      for (const course of courses.docs || []) {
        if (course.title?.includes('Test') || course.slug?.includes('test')) {
          await page.request.delete(`http://localhost:3001/api/courses/${course.id}`, {
            headers
          })
        }
      }
    }

    // Clean up test lessons
    const lessonsResponse = await page.request.get('http://localhost:3001/api/lessons', {
      headers
    })
    
    if (lessonsResponse.ok()) {
      const lessons = await lessonsResponse.json()
      for (const lesson of lessons.docs || []) {
        if (lesson.title?.includes('Test') || lesson.slug?.includes('test')) {
          await page.request.delete(`http://localhost:3001/api/lessons/${lesson.id}`, {
            headers
          })
        }
      }
    }

    // Clean up test categories
    const categoriesResponse = await page.request.get('http://localhost:3001/api/categories', {
      headers
    })
    
    if (categoriesResponse.ok()) {
      const categories = await categoriesResponse.json()
      for (const category of categories.docs || []) {
        if (category.name?.includes('Test') || category.slug?.includes('test')) {
          await page.request.delete(`http://localhost:3001/api/categories/${category.id}`, {
            headers
          })
        }
      }
    }

    console.log('✅ Test data cleanup completed')
  } catch (error) {
    console.log('⚠️ Test data cleanup failed:', error)
  }
}

export default globalTeardown
