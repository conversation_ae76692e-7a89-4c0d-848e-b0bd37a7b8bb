import { test, expect } from '@playwright/test'

/**
 * Course Builder MCP Integration Test
 * 
 * This test demonstrates how to use <PERSON><PERSON> with MCP for testing
 * the Course Builder System. Run this test using:
 * 
 * npx playwright test tests/mcp/course-builder-mcp.spec.ts --headed
 */

test.describe('Course Builder MCP Integration Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('http://localhost:3000')
    
    // Handle any initial dialogs
    page.on('dialog', async dialog => {
      console.log(`Dialog: ${dialog.message()}`)
      await dialog.accept()
    })
  })

  test('should navigate to course builder and take screenshot', async ({ page }) => {
    // Navigate to course builder
    await page.goto('http://localhost:3000/admin/course-builder')
    
    // Wait for page to load
    await page.waitForLoadState('networkidle')
    
    // Take screenshot for documentation
    await page.screenshot({ 
      path: 'test-results/course-builder-page.png',
      fullPage: true 
    })
    
    // Verify page title
    await expect(page).toHaveTitle(/Course Builder/)
  })

  test('should test course creation workflow', async ({ page }) => {
    await page.goto('http://localhost:3000/admin/course-builder')
    
    // Click create course button
    const createButton = page.locator('[data-testid="create-course-button"]')
    if (await createButton.isVisible()) {
      await createButton.click()
    } else {
      // Fallback: look for any button with "Create" text
      await page.click('button:has-text("Create")')
    }
    
    // Fill course form
    await page.fill('[data-testid="course-title"]', 'MCP Test Course')
    await page.fill('[data-testid="course-description"]', 'Course created via MCP Playwright integration')
    
    // Select category if available
    const categorySelect = page.locator('[data-testid="course-category"]')
    if (await categorySelect.isVisible()) {
      await categorySelect.selectOption('programming')
    }
    
    // Select difficulty
    const difficultySelect = page.locator('[data-testid="course-difficulty"]')
    if (await difficultySelect.isVisible()) {
      await difficultySelect.selectOption('beginner')
    }
    
    // Set duration
    await page.fill('[data-testid="course-duration"]', '10')
    
    // Take screenshot of filled form
    await page.screenshot({ 
      path: 'test-results/course-form-filled.png' 
    })
    
    // Submit form
    const submitButton = page.locator('[data-testid="create-course-submit"]')
    if (await submitButton.isVisible()) {
      await submitButton.click()
    }
    
    // Wait for success message or navigation
    await page.waitForTimeout(2000)
    
    // Take final screenshot
    await page.screenshot({ 
      path: 'test-results/course-created.png' 
    })
  })

  test('should test lesson management interface', async ({ page }) => {
    // Navigate to lesson management (mock course ID)
    await page.goto('http://localhost:3000/admin/courses/test-course/lessons')
    
    // Take screenshot of lesson management
    await page.screenshot({ 
      path: 'test-results/lesson-management.png' 
    })
    
    // Try to add a lesson
    const addLessonButton = page.locator('[data-testid="add-lesson-button"]')
    if (await addLessonButton.isVisible()) {
      await addLessonButton.click()
      
      // Fill lesson form
      await page.fill('[data-testid="lesson-title"]', 'MCP Test Lesson')
      
      // Select lesson type
      const typeSelect = page.locator('[data-testid="lesson-type"]')
      if (await typeSelect.isVisible()) {
        await typeSelect.selectOption('video')
      }
      
      // Take screenshot of lesson form
      await page.screenshot({ 
        path: 'test-results/lesson-form.png' 
      })
    }
  })

  test('should test responsive design', async ({ page }) => {
    await page.goto('http://localhost:3000/admin/course-builder')
    
    // Test desktop view
    await page.setViewportSize({ width: 1920, height: 1080 })
    await page.screenshot({ 
      path: 'test-results/desktop-view.png' 
    })
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.screenshot({ 
      path: 'test-results/tablet-view.png' 
    })
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 })
    await page.screenshot({ 
      path: 'test-results/mobile-view.png' 
    })
  })

  test('should test file upload functionality', async ({ page }) => {
    await page.goto('http://localhost:3000/admin/course-builder')
    
    // Look for file upload elements
    const fileInputs = page.locator('input[type="file"]')
    const count = await fileInputs.count()
    
    console.log(`Found ${count} file upload inputs`)
    
    // Take screenshot showing upload areas
    await page.screenshot({ 
      path: 'test-results/file-upload-areas.png' 
    })
    
    // Test drag and drop areas
    const dropZones = page.locator('[data-testid*="drop-zone"], .dropzone')
    const dropCount = await dropZones.count()
    
    console.log(`Found ${dropCount} drop zones`)
  })

  test('should test navigation and accessibility', async ({ page }) => {
    await page.goto('http://localhost:3000/admin/course-builder')
    
    // Test keyboard navigation
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    
    // Take screenshot showing focus states
    await page.screenshot({ 
      path: 'test-results/keyboard-navigation.png' 
    })
    
    // Check for accessibility violations (basic)
    const headings = page.locator('h1, h2, h3, h4, h5, h6')
    const headingCount = await headings.count()
    console.log(`Found ${headingCount} headings`)
    
    // Check for alt text on images
    const images = page.locator('img')
    const imageCount = await images.count()
    console.log(`Found ${imageCount} images`)
  })

  test('should monitor network requests', async ({ page }) => {
    // Monitor network requests
    const requests: string[] = []
    
    page.on('request', request => {
      requests.push(`${request.method()} ${request.url()}`)
    })
    
    await page.goto('http://localhost:3000/admin/course-builder')
    
    // Wait for all network activity to complete
    await page.waitForLoadState('networkidle')
    
    // Log all requests
    console.log('Network Requests:')
    requests.forEach(request => console.log(request))
    
    // Check for API calls
    const apiRequests = requests.filter(req => req.includes('/api/'))
    console.log(`Found ${apiRequests.length} API requests`)
  })

  test('should test error handling', async ({ page }) => {
    await page.goto('http://localhost:3000/admin/course-builder')
    
    // Monitor console errors
    const errors: string[] = []
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })
    
    // Try to submit empty form to trigger validation
    const submitButton = page.locator('button[type="submit"]')
    if (await submitButton.isVisible()) {
      await submitButton.click()
    }
    
    // Wait for potential errors
    await page.waitForTimeout(2000)
    
    // Log any console errors
    if (errors.length > 0) {
      console.log('Console Errors:')
      errors.forEach(error => console.log(error))
    }
    
    // Take screenshot of error state
    await page.screenshot({ 
      path: 'test-results/error-state.png' 
    })
  })

  test('should generate test report', async ({ page }) => {
    const testResults = {
      timestamp: new Date().toISOString(),
      tests: [],
      screenshots: [
        'course-builder-page.png',
        'course-form-filled.png',
        'course-created.png',
        'lesson-management.png',
        'desktop-view.png',
        'tablet-view.png',
        'mobile-view.png',
        'file-upload-areas.png',
        'keyboard-navigation.png',
        'error-state.png'
      ],
      summary: {
        total: 8,
        passed: 0,
        failed: 0,
        skipped: 0
      }
    }
    
    // Save test report
    const fs = require('fs')
    fs.writeFileSync(
      'test-results/mcp-test-report.json', 
      JSON.stringify(testResults, null, 2)
    )
    
    console.log('Test report generated: test-results/mcp-test-report.json')
  })
})

/**
 * MCP Integration Commands
 * 
 * Use these commands in Augment's Playwright MCP:
 * 
 * 1. browser_navigate_Playwright({ url: "http://localhost:3000/admin/course-builder" })
 * 2. browser_snapshot_Playwright()
 * 3. browser_take_screenshot_Playwright({ filename: "course-builder.png" })
 * 4. browser_click_Playwright({ element: "Create Course", ref: "create-btn" })
 * 5. browser_type_Playwright({ element: "Title", ref: "title", text: "Test Course" })
 * 6. browser_file_upload_Playwright({ paths: ["test-file.jpg"] })
 * 7. browser_drag_Playwright({ startElement: "Lesson 1", startRef: "lesson-1", endElement: "Lesson 2", endRef: "lesson-2" })
 * 8. browser_resize_Playwright({ width: 375, height: 667 })
 * 9. browser_network_requests_Playwright()
 * 10. browser_console_messages_Playwright()
 */
