# Course Builder MCP Testing Commands

This file contains step-by-step Playwright MCP commands to test your Course Builder System in Augment.

## 🎭 Prerequisites
- Frontend running on `http://localhost:3000`
- API running on `http://localhost:3001`
- Playwright MCP available in Augment

## 🚀 Test Sequence

### 1. Initial Navigation and Setup
```typescript
// Navigate to course builder
browser_navigate_Playwright({ 
  url: "http://localhost:3000/admin/course-builder" 
})

// Take initial snapshot
browser_snapshot_Playwright()

// Take screenshot for documentation
browser_take_screenshot_Playwright({ 
  filename: "course-builder-initial.png" 
})
```

### 2. Test Course Creation Form
```typescript
// Click create course button
browser_click_Playwright({ 
  element: "Create New Course button", 
  ref: "create-course-btn" 
})

// Fill course title
browser_type_Playwright({ 
  element: "Course title input", 
  ref: "course-title", 
  text: "MCP Test Course - JavaScript Fundamentals" 
})

// Fill course description
browser_type_Playwright({ 
  element: "Course description textarea", 
  ref: "course-description", 
  text: "This course teaches JavaScript fundamentals through hands-on examples and projects. Created via MCP testing." 
})

// Select category
browser_select_option_Playwright({ 
  element: "Category dropdown", 
  ref: "course-category", 
  values: ["programming"] 
})

// Select difficulty
browser_select_option_Playwright({ 
  element: "Difficulty dropdown", 
  ref: "course-difficulty", 
  values: ["beginner"] 
})

// Set duration
browser_type_Playwright({ 
  element: "Duration input", 
  ref: "course-duration", 
  text: "20" 
})

// Take screenshot of filled form
browser_take_screenshot_Playwright({ 
  filename: "course-form-filled.png" 
})
```

### 3. Test File Upload
```typescript
// Upload course thumbnail
browser_file_upload_Playwright({ 
  paths: ["tests/fixtures/test-thumbnail.jpg"] 
})

// Wait for upload to complete
browser_wait_for_Playwright({ 
  text: "Upload successful" 
})

// Take screenshot showing uploaded file
browser_take_screenshot_Playwright({ 
  filename: "course-thumbnail-uploaded.png" 
})
```

### 4. Test Form Submission
```typescript
// Submit the course creation form
browser_click_Playwright({ 
  element: "Create Course submit button", 
  ref: "submit-course" 
})

// Wait for success message
browser_wait_for_Playwright({ 
  text: "Course created successfully" 
})

// Take screenshot of success state
browser_take_screenshot_Playwright({ 
  filename: "course-created-success.png" 
})
```

### 5. Test Lesson Management
```typescript
// Navigate to lesson management
browser_navigate_Playwright({ 
  url: "http://localhost:3000/admin/courses/test-course-id/lessons" 
})

// Take snapshot of lesson management interface
browser_snapshot_Playwright()

// Click add lesson button
browser_click_Playwright({ 
  element: "Add Lesson button", 
  ref: "add-lesson-btn" 
})

// Fill lesson details
browser_type_Playwright({ 
  element: "Lesson title", 
  ref: "lesson-title", 
  text: "Introduction to Variables" 
})

// Select lesson type
browser_select_option_Playwright({ 
  element: "Lesson type dropdown", 
  ref: "lesson-type", 
  values: ["video"] 
})

// Add lesson description
browser_type_Playwright({ 
  element: "Lesson description", 
  ref: "lesson-description", 
  text: "Learn about JavaScript variables and data types" 
})

// Save lesson
browser_click_Playwright({ 
  element: "Save Lesson button", 
  ref: "save-lesson" 
})
```

### 6. Test Drag and Drop Functionality
```typescript
// Create multiple lessons first, then test reordering
browser_click_Playwright({ 
  element: "Add Lesson button", 
  ref: "add-lesson-btn" 
})

browser_type_Playwright({ 
  element: "Lesson title", 
  ref: "lesson-title", 
  text: "Functions and Methods" 
})

browser_click_Playwright({ 
  element: "Save Lesson button", 
  ref: "save-lesson" 
})

// Test drag and drop reordering
browser_drag_Playwright({
  startElement: "First lesson in list",
  startRef: "lesson-item-1",
  endElement: "Second lesson position",
  endRef: "lesson-item-2"
})

// Take screenshot of reordered lessons
browser_take_screenshot_Playwright({ 
  filename: "lessons-reordered.png" 
})
```

### 7. Test Responsive Design
```typescript
// Test mobile view
browser_resize_Playwright({ 
  width: 375, 
  height: 667 
})

browser_take_screenshot_Playwright({ 
  filename: "mobile-view.png" 
})

// Test tablet view
browser_resize_Playwright({ 
  width: 768, 
  height: 1024 
})

browser_take_screenshot_Playwright({ 
  filename: "tablet-view.png" 
})

// Test desktop view
browser_resize_Playwright({ 
  width: 1920, 
  height: 1080 
})

browser_take_screenshot_Playwright({ 
  filename: "desktop-view.png" 
})
```

### 8. Test Content Management
```typescript
// Navigate to content management
browser_navigate_Playwright({ 
  url: "http://localhost:3000/admin/content-management" 
})

// Test video player
browser_click_Playwright({ 
  element: "Video content tab", 
  ref: "video-tab" 
})

// Add video URL
browser_type_Playwright({ 
  element: "Video URL input", 
  ref: "video-url", 
  text: "https://www.youtube.com/watch?v=dQw4w9WgXcQ" 
})

// Test document viewer
browser_click_Playwright({ 
  element: "Document content tab", 
  ref: "document-tab" 
})

// Upload document
browser_file_upload_Playwright({ 
  paths: ["tests/fixtures/test-document.pdf"] 
})
```

### 9. Monitor Network and Console
```typescript
// Check network requests
browser_network_requests_Playwright()

// Check console messages for errors
browser_console_messages_Playwright()
```

### 10. Test Error Handling
```typescript
// Test form validation by submitting empty form
browser_navigate_Playwright({ 
  url: "http://localhost:3000/admin/course-builder" 
})

browser_click_Playwright({ 
  element: "Create Course button", 
  ref: "create-course-btn" 
})

// Try to submit without filling required fields
browser_click_Playwright({ 
  element: "Submit button", 
  ref: "submit-btn" 
})

// Take screenshot of validation errors
browser_take_screenshot_Playwright({ 
  filename: "validation-errors.png" 
})
```

## 📊 Running the Complete Test Suite

### Option 1: Manual MCP Commands
Run each command above step by step in Augment's Playwright MCP interface.

### Option 2: Automated Test Suite
```bash
# Run the complete test file
npx playwright test tests/mcp/course-builder-mcp.spec.ts --headed

# Run with UI mode
npx playwright test tests/mcp/course-builder-mcp.spec.ts --ui

# Run specific test
npx playwright test tests/mcp/course-builder-mcp.spec.ts -g "should test course creation"
```

### Option 3: Use Existing Test Scripts
```bash
# Run course builder tests
npm run test:course-builder

# Run lesson management tests
npm run test:lesson-management

# Run all E2E tests
npm run test:e2e

# Run with visual UI
npm run test:ui
```

## 🎯 Expected Results

After running these tests, you should have:

1. **Screenshots** documenting the UI at each step
2. **Network request logs** showing API interactions
3. **Console logs** revealing any JavaScript errors
4. **Responsive design validation** across different screen sizes
5. **File upload verification** for media content
6. **Form validation testing** for error handling
7. **Drag-and-drop functionality** confirmation
8. **Cross-browser compatibility** results

## 🔍 Debugging Tips

1. **Use snapshots** to understand page structure
2. **Take screenshots** at each major step
3. **Monitor network requests** to debug API issues
4. **Check console messages** for JavaScript errors
5. **Test responsive design** on different screen sizes
6. **Verify file uploads** work correctly
7. **Test error scenarios** to ensure proper handling

## 📈 Test Coverage

This MCP test suite covers:
- ✅ Course creation workflow
- ✅ Lesson management interface
- ✅ Content management system
- ✅ File upload functionality
- ✅ Drag-and-drop interactions
- ✅ Responsive design
- ✅ Form validation
- ✅ Error handling
- ✅ Network monitoring
- ✅ Accessibility basics

## 🚀 Next Steps

1. Run the MCP commands step by step
2. Review generated screenshots
3. Analyze network requests and console logs
4. Fix any issues discovered
5. Run the automated test suite
6. Generate comprehensive test reports

Happy testing with Playwright MCP! 🎭✨
