import { test, expect } from '@playwright/test'

// Test API endpoints for settings
test.describe('Settings API Endpoints', () => {
  const baseURL = 'http://localhost:3001'
  
  test.beforeEach(async ({ page }) => {
    // Set up authentication headers if needed
    await page.setExtraHTTPHeaders({
      'Content-Type': 'application/json',
      // Add auth headers here if required
    })
  })

  test('GET /api/settings - should return all settings', async ({ request }) => {
    const response = await request.get(`${baseURL}/api/settings`)
    
    expect(response.status()).toBe(200)
    const data = await response.json()
    expect(data).toHaveProperty('settings')
    expect(Array.isArray(data.settings)).toBe(true)
  })

  test('GET /api/settings/category/platform - should return platform settings', async ({ request }) => {
    const response = await request.get(`${baseURL}/api/settings/category/platform`)
    
    expect(response.status()).toBe(200)
    const data = await response.json()
    expect(data).toHaveProperty('settings')
    expect(Array.isArray(data.settings)).toBe(true)
    
    // Check if all returned settings are platform category
    data.settings.forEach((setting: any) => {
      expect(setting.category).toBe('platform')
    })
  })

  test('POST /api/settings - should create a new setting', async ({ request }) => {
    const newSetting = {
      key: 'test_setting',
      value: 'test_value',
      category: 'platform',
      type: 'string',
      is_public: false,
      description: 'Test setting for Playwright'
    }

    const response = await request.post(`${baseURL}/api/settings`, {
      data: newSetting
    })
    
    expect(response.status()).toBe(201)
    const data = await response.json()
    expect(data).toHaveProperty('setting')
    expect(data.setting.key).toBe(newSetting.key)
    expect(data.setting.value).toBe(newSetting.value)
  })

  test('PUT /api/settings/:key - should update existing setting', async ({ request }) => {
    const settingKey = 'platform_name'
    const updatedValue = 'Updated Platform Name'

    const response = await request.put(`${baseURL}/api/settings/${settingKey}`, {
      data: {
        value: updatedValue,
        description: 'Updated platform name for testing'
      }
    })
    
    expect(response.status()).toBe(200)
    const data = await response.json()
    expect(data).toHaveProperty('setting')
    expect(data.setting.value).toBe(updatedValue)
  })

  test('POST /api/settings/bulk - should update multiple settings', async ({ request }) => {
    const bulkSettings = [
      {
        key: 'platform_name',
        value: 'Bulk Updated Platform',
        category: 'platform',
        type: 'string',
        is_public: true
      },
      {
        key: 'support_email',
        value: '<EMAIL>',
        category: 'platform',
        type: 'email',
        is_public: true
      }
    ]

    const response = await request.post(`${baseURL}/api/settings/bulk`, {
      data: { settings: bulkSettings }
    })
    
    expect(response.status()).toBe(200)
    const data = await response.json()
    expect(data).toHaveProperty('updated')
    expect(data.updated).toBeGreaterThan(0)
  })

  test('GET /api/settings/:key - should return specific setting', async ({ request }) => {
    const settingKey = 'platform_name'
    
    const response = await request.get(`${baseURL}/api/settings/${settingKey}`)
    
    expect(response.status()).toBe(200)
    const data = await response.json()
    expect(data).toHaveProperty('setting')
    expect(data.setting.key).toBe(settingKey)
  })

  test('DELETE /api/settings/:key - should delete setting', async ({ request }) => {
    // First create a test setting
    const testSetting = {
      key: 'test_delete_setting',
      value: 'to_be_deleted',
      category: 'platform',
      type: 'string',
      is_public: false
    }

    await request.post(`${baseURL}/api/settings`, { data: testSetting })

    // Then delete it
    const response = await request.delete(`${baseURL}/api/settings/${testSetting.key}`)
    
    expect(response.status()).toBe(200)
    const data = await response.json()
    expect(data).toHaveProperty('message')
    expect(data.message).toContain('deleted')
  })

  test('GET /api/settings with filters - should filter settings', async ({ request }) => {
    const response = await request.get(`${baseURL}/api/settings?category=security&type=boolean`)
    
    expect(response.status()).toBe(200)
    const data = await response.json()
    expect(data).toHaveProperty('settings')
    
    // Check if all returned settings match filters
    data.settings.forEach((setting: any) => {
      expect(setting.category).toBe('security')
      expect(setting.type).toBe('boolean')
    })
  })

  test('API error handling - should return 404 for non-existent setting', async ({ request }) => {
    const response = await request.get(`${baseURL}/api/settings/non_existent_setting`)
    
    expect(response.status()).toBe(404)
    const data = await response.json()
    expect(data).toHaveProperty('error')
  })

  test('API validation - should return 400 for invalid setting data', async ({ request }) => {
    const invalidSetting = {
      // Missing required fields
      value: 'test_value'
    }

    const response = await request.post(`${baseURL}/api/settings`, {
      data: invalidSetting
    })
    
    expect(response.status()).toBe(400)
    const data = await response.json()
    expect(data).toHaveProperty('error')
  })
})

// Test settings by category
test.describe('Settings API by Category', () => {
  const baseURL = 'http://localhost:3001'
  const categories = ['platform', 'security', 'storage', 'email']

  categories.forEach(category => {
    test(`GET /api/settings/category/${category} - should return ${category} settings`, async ({ request }) => {
      const response = await request.get(`${baseURL}/api/settings/category/${category}`)
      
      expect(response.status()).toBe(200)
      const data = await response.json()
      expect(data).toHaveProperty('settings')
      expect(Array.isArray(data.settings)).toBe(true)
      
      // Verify all settings belong to the correct category
      data.settings.forEach((setting: any) => {
        expect(setting.category).toBe(category)
      })
    })
  })
})

// Test settings validation
test.describe('Settings Validation', () => {
  const baseURL = 'http://localhost:3001'

  test('should validate email type settings', async ({ request }) => {
    const emailSetting = {
      key: 'test_email',
      value: 'invalid-email',
      category: 'platform',
      type: 'email',
      is_public: false
    }

    const response = await request.post(`${baseURL}/api/settings`, {
      data: emailSetting
    })
    
    // Should fail validation for invalid email
    expect(response.status()).toBe(400)
  })

  test('should validate URL type settings', async ({ request }) => {
    const urlSetting = {
      key: 'test_url',
      value: 'not-a-valid-url',
      category: 'platform',
      type: 'url',
      is_public: false
    }

    const response = await request.post(`${baseURL}/api/settings`, {
      data: urlSetting
    })
    
    // Should fail validation for invalid URL
    expect(response.status()).toBe(400)
  })

  test('should validate number type settings', async ({ request }) => {
    const numberSetting = {
      key: 'test_number',
      value: 'not-a-number',
      category: 'platform',
      type: 'number',
      is_public: false
    }

    const response = await request.post(`${baseURL}/api/settings`, {
      data: numberSetting
    })
    
    // Should fail validation for invalid number
    expect(response.status()).toBe(400)
  })

  test('should validate boolean type settings', async ({ request }) => {
    const booleanSetting = {
      key: 'test_boolean',
      value: 'maybe',
      category: 'platform',
      type: 'boolean',
      is_public: false
    }

    const response = await request.post(`${baseURL}/api/settings`, {
      data: booleanSetting
    })
    
    // Should fail validation for invalid boolean
    expect(response.status()).toBe(400)
  })
})
