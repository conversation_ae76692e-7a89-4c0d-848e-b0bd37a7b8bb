import { Page, expect } from '@playwright/test'

export class TestHelpers {
  constructor(private page: Page) {}

  // Authentication helpers
  async loginAsAdmin() {
    await this.page.goto('/admin/login')
    await this.page.fill('[data-testid="email-input"]', '<EMAIL>')
    await this.page.fill('[data-testid="password-input"]', 'test123456')
    await this.page.click('[data-testid="login-button"]')
    await this.page.waitForURL('/admin/dashboard')
  }

  async loginAsInstituteAdmin() {
    await this.page.goto('/admin/login')
    await this.page.fill('[data-testid="email-input"]', '<EMAIL>')
    await this.page.fill('[data-testid="password-input"]', 'test123456')
    await this.page.click('[data-testid="login-button"]')
    await this.page.waitForURL('/admin/dashboard')
  }

  // Navigation helpers
  async navigateToCourseBuilder() {
    await this.page.goto('/admin/course-builder')
    await this.page.waitForLoadState('networkidle')
  }

  async navigateToLessonManagement(courseId: string) {
    await this.page.goto(`/admin/courses/${courseId}/lessons`)
    await this.page.waitForLoadState('networkidle')
  }

  // Course creation helpers
  async createTestCourse(courseData: {
    title: string
    description: string
    category: string
    difficulty: string
    duration: number
    price?: number
  }) {
    await this.navigateToCourseBuilder()
    
    // Click create course button
    await this.page.click('[data-testid="create-course-button"]')
    
    // Fill course form
    await this.page.fill('[data-testid="course-title"]', courseData.title)
    await this.page.fill('[data-testid="course-description"]', courseData.description)
    await this.page.selectOption('[data-testid="course-category"]', courseData.category)
    await this.page.selectOption('[data-testid="course-difficulty"]', courseData.difficulty)
    await this.page.fill('[data-testid="course-duration"]', courseData.duration.toString())
    
    if (courseData.price !== undefined) {
      await this.page.click('[data-testid="paid-course-toggle"]')
      await this.page.fill('[data-testid="course-price"]', courseData.price.toString())
    }
    
    // Submit form
    await this.page.click('[data-testid="create-course-submit"]')
    
    // Wait for success message
    await expect(this.page.locator('[data-testid="success-toast"]')).toBeVisible()
    
    return courseData
  }

  // Lesson creation helpers
  async createTestLesson(lessonData: {
    title: string
    type: string
    description?: string
    duration?: number
  }) {
    // Click add lesson button
    await this.page.click('[data-testid="add-lesson-button"]')
    
    // Fill lesson form
    await this.page.fill('[data-testid="lesson-title"]', lessonData.title)
    await this.page.selectOption('[data-testid="lesson-type"]', lessonData.type)
    
    if (lessonData.description) {
      await this.page.fill('[data-testid="lesson-description"]', lessonData.description)
    }
    
    if (lessonData.duration) {
      await this.page.fill('[data-testid="lesson-duration"]', lessonData.duration.toString())
    }
    
    // Submit form
    await this.page.click('[data-testid="save-lesson-button"]')
    
    // Wait for success message
    await expect(this.page.locator('[data-testid="success-toast"]')).toBeVisible()
    
    return lessonData
  }

  // File upload helpers
  async uploadFile(inputSelector: string, filePath: string) {
    const fileInput = this.page.locator(inputSelector)
    await fileInput.setInputFiles(filePath)
    
    // Wait for upload to complete
    await expect(this.page.locator('[data-testid="upload-success"]')).toBeVisible()
  }

  // Form validation helpers
  async expectValidationError(fieldSelector: string, errorMessage: string) {
    const errorElement = this.page.locator(`${fieldSelector} + .error-message`)
    await expect(errorElement).toContainText(errorMessage)
  }

  async expectFormToBeValid() {
    const submitButton = this.page.locator('[data-testid="submit-button"]')
    await expect(submitButton).not.toBeDisabled()
  }

  // Wait helpers
  async waitForLoadingToComplete() {
    await this.page.waitForSelector('[data-testid="loading-spinner"]', { state: 'hidden' })
  }

  async waitForToastMessage(message: string) {
    await expect(this.page.locator('[data-testid="toast"]')).toContainText(message)
  }

  // Drag and drop helpers
  async dragAndDropLesson(fromIndex: number, toIndex: number) {
    const lessons = this.page.locator('[data-testid="lesson-item"]')
    const fromLesson = lessons.nth(fromIndex)
    const toLesson = lessons.nth(toIndex)
    
    await fromLesson.dragTo(toLesson)
    
    // Wait for reorder to complete
    await this.waitForLoadingToComplete()
  }

  // Bulk operations helpers
  async selectLessons(indices: number[]) {
    for (const index of indices) {
      await this.page.locator(`[data-testid="lesson-checkbox-${index}"]`).check()
    }
  }

  async performBulkAction(action: string) {
    await this.page.selectOption('[data-testid="bulk-action-select"]', action)
    await this.page.click('[data-testid="bulk-action-button"]')
    
    // Confirm action if needed
    const confirmButton = this.page.locator('[data-testid="confirm-bulk-action"]')
    if (await confirmButton.isVisible()) {
      await confirmButton.click()
    }
    
    await this.waitForLoadingToComplete()
  }

  // Content management helpers
  async addVideoContent(videoUrl: string) {
    await this.page.click('[data-testid="add-video-content"]')
    await this.page.fill('[data-testid="video-url-input"]', videoUrl)
    await this.page.click('[data-testid="save-video-content"]')
    await this.waitForLoadingToComplete()
  }

  async addDocumentContent(filePath: string) {
    await this.page.click('[data-testid="add-document-content"]')
    await this.uploadFile('[data-testid="document-upload"]', filePath)
    await this.page.click('[data-testid="save-document-content"]')
    await this.waitForLoadingToComplete()
  }

  // Assertion helpers
  async expectCourseToExist(courseTitle: string) {
    await expect(this.page.locator(`[data-testid="course-card"][data-title="${courseTitle}"]`)).toBeVisible()
  }

  async expectLessonToExist(lessonTitle: string) {
    await expect(this.page.locator(`[data-testid="lesson-item"][data-title="${lessonTitle}"]`)).toBeVisible()
  }

  async expectElementToBeVisible(selector: string) {
    await expect(this.page.locator(selector)).toBeVisible()
  }

  async expectElementToContainText(selector: string, text: string) {
    await expect(this.page.locator(selector)).toContainText(text)
  }

  // Cleanup helpers
  async deleteTestCourse(courseTitle: string) {
    const courseCard = this.page.locator(`[data-testid="course-card"][data-title="${courseTitle}"]`)
    await courseCard.locator('[data-testid="course-menu"]').click()
    await this.page.click('[data-testid="delete-course"]')
    await this.page.click('[data-testid="confirm-delete"]')
    await this.waitForLoadingToComplete()
  }

  async deleteTestLesson(lessonTitle: string) {
    const lessonItem = this.page.locator(`[data-testid="lesson-item"][data-title="${lessonTitle}"]`)
    await lessonItem.locator('[data-testid="lesson-menu"]').click()
    await this.page.click('[data-testid="delete-lesson"]')
    await this.page.click('[data-testid="confirm-delete"]')
    await this.waitForLoadingToComplete()
  }
}

// API helpers
export class APIHelpers {
  constructor(private baseURL: string = 'http://localhost:3001') {}

  async makeAuthenticatedRequest(endpoint: string, options: any = {}) {
    const authToken = process.env.TEST_AUTH_TOKEN
    return fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authToken ? `Bearer ${authToken}` : '',
        ...options.headers
      }
    })
  }

  async createTestCourse(courseData: any) {
    return this.makeAuthenticatedRequest('/api/courses', {
      method: 'POST',
      body: JSON.stringify(courseData)
    })
  }

  async createTestLesson(lessonData: any) {
    return this.makeAuthenticatedRequest('/api/lessons', {
      method: 'POST',
      body: JSON.stringify(lessonData)
    })
  }

  async deleteTestCourse(courseId: string) {
    return this.makeAuthenticatedRequest(`/api/courses/${courseId}`, {
      method: 'DELETE'
    })
  }

  async deleteTestLesson(lessonId: string) {
    return this.makeAuthenticatedRequest(`/api/lessons/${lessonId}`, {
      method: 'DELETE'
    })
  }
}
