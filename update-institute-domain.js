// Script to update an existing institute with custom domain hello.local:3000

const API_URL = 'http://localhost:3001'

async function updateInstituteWithCustomDomain() {
  try {
    console.log('🏫 Updating institute with custom domain hello.local:3000...')

    // First, get the list of institutes
    console.log('📋 Getting list of institutes...')
    const institutesResponse = await fetch(`${API_URL}/api/institutes`)
    const institutesResult = await institutesResponse.json()
    
    if (!institutesResponse.ok || !institutesResult.docs || institutesResult.docs.length === 0) {
      throw new Error('No institutes found')
    }

    // Take the first institute
    const institute = institutesResult.docs[0]
    console.log('✅ Found institute:', institute.name, '(ID:', institute.id + ')')

    // Update the institute with custom domain using Payload's update endpoint
    console.log('🔄 Updating institute with custom domain...')
    const updateResponse = await fetch(`${API_URL}/api/institutes/${institute.id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        customDomain: 'hello.local:3000',
        domainVerified: true // Mark as verified for testing
      })
    })

    const updateResult = await updateResponse.json()
    
    if (updateResponse.ok) {
      console.log('✅ Institute updated successfully!')
      console.log('Institute Name:', updateResult.name)
      console.log('Custom Domain:', updateResult.customDomain)
      console.log('Domain Verified:', updateResult.domainVerified)
      
      // Test the domain resolution
      console.log('🔍 Testing domain resolution...')
      const domainTestResponse = await fetch(`${API_URL}/api/institutes/by-domain?domain=hello.local:3000`)
      const domainTestResult = await domainTestResponse.json()
      
      if (domainTestResponse.ok && domainTestResult.success) {
        console.log('✅ Domain resolution working!')
        console.log('Resolved institute:', domainTestResult.institute.name)
      } else {
        console.log('⚠️ Domain resolution test failed:', domainTestResult.error)
      }
      
    } else {
      console.error('❌ Failed to update institute:', updateResult.errors || updateResult.message)
      console.log('Response status:', updateResponse.status)
      console.log('Full response:', updateResult)
    }

  } catch (error) {
    console.error('❌ Error updating institute:', error)
  }
}

// Run the script
updateInstituteWithCustomDomain()
